#pragma once
#include "CrossBase/Threading/Task.h"

#include "Resource/Texture/TextureFormatConvert.h"
#include "ResourceAsset_generated.h"
#include "Resource/resourceforward.h"
#include "Resource/TextureResourceInfo.h"
#include "stduuid/gsl/span"



struct StreamingData
{
    gsl::span<UInt8> mData;
    cross::TextureResourceInfo mResourceInfo;
    std::vector<cross::NGICopyBufferTexture>* mUpdates;
    cross::StreamingScratchBuffer::Range mRange;
};

enum StreamingStatus
{
    Loading,
    Loaded
};

class Resource_API StreamableTextureResource : public std::enable_shared_from_this<StreamableTextureResource>
{
public:
    typedef std::function<void(UInt32, StreamingData &)> Succecor;
    typedef std::function<void(UInt32)> Failure;

    typedef std::tuple<StreamingData, UInt32, Succecor, Failure> StreamRequest;

    virtual ~StreamableTextureResource()
    {
    };

    std::shared_ptr<StreamableTextureResource> GetSharedPtr() { return this->shared_from_this(); }

     
    bool RequestSubResource(UInt32 SubResourceID, Succecor success, Failure failure = [](UInt32) {})
    {
        {
            std::shared_lock<std::shared_mutex> flightLock(mInflightEventMutex);
            if (mInflightEvents.empty())
            {
                // the lock, to prevent possible multi thread confilct
                // but if the request and the succecor/failure run in same thread. it would be not neccessary
                std::shared_lock<std::shared_mutex> lock{ mRequestMutex };
                if (mResourceStatus.count(SubResourceID))
                {
                    if (mResourceStatus[SubResourceID] == Loading)
                        return false;
                    else
                    {
                        success(SubResourceID, mDataCaches[SubResourceID]);
                        lock.unlock();
                        EraseSubResource(SubResourceID);
                        return true;
                    }
                }
            }
            else
            {
                return false;
            }
        }

        std::unique_lock<std::shared_mutex> lock{ mRequestMutex };
        mResourceStatus[SubResourceID] = Loaded;

        AddEvent(cross::threading::Async<StreamRequest>([SubResourceID, handle = this->GetSharedPtr(), success, failure](auto) { return std::make_tuple(handle->RequestSubResourceImp(SubResourceID), SubResourceID, success, failure); }));
        return false;
    }

    void AddEvent(cross::threading::TaskEventPtr event);

    void ProcessPendingEvents();

    void EraseSubResource(int subresource)
    {
        std::unique_lock<std::shared_mutex> lock{ mRequestMutex };
        mResourceStatus.erase(subresource);
        mDataCaches.erase(subresource);
    }

    virtual StreamingData RequestSubResourceImp(UInt32 subresouceID)
    {
        return StreamingData();
    }
    // preventing game postUpdate and render request conflicts
    std::shared_mutex mInflightEventMutex;
    std::vector<cross::threading::TaskEventPtr> mInflightEvents;
    std::atomic_uint64_t mEventCounter;

    std::shared_mutex mRequestMutex;
    std::map<int, StreamingStatus> mResourceStatus;
    std::map<int, StreamingData> mDataCaches;
};



// A 2D/3D/Cube Texture
class Resource_API TextureResource : public StreamableTextureResource
{
    using ImageList = std::vector<CrossSchema::TextureAssetImage>;
public:

    std::shared_ptr<TextureResource> GetSharedPtr() { return std::static_pointer_cast<TextureResource>(this->shared_from_this()); }
    bool Deserialize(cross::FBSerializer const& s, bool UseStreaming);
    inline UInt32 GetFaceNum() const{ return IsCubeType(mResourceInfo.Dimension) ? 6 : 1; };
    UInt32 GetImageIndex(UInt32 mipLevel, UInt32 faceIndex, UInt32 arryIndex) const;
    void GetImageLevels(UInt32 imageIndex, UInt32& mipLevel, UInt32& faceIndex, UInt32& arryIndex) const;

    const UInt8* GetImageData(UInt32 imageIndex) const;
    const UInt8* GetImageData(UInt32 mipLevel, UInt32 faceIndex, UInt32 arryIndex) const;

    UInt8* GetImageData(UInt32 imageIndex);
    UInt8* GetImageData(UInt32 mipLevel, UInt32 faceIndex, UInt32 arryIndex);

    bool AddArrayImageData(const ImageList& images, const std::vector<UInt8>& data);
    bool DelArrayImageData(UInt32 arrayIndex);

    UInt8* GetFallbackColor() {
        return reinterpret_cast<UInt8*>(&mFallbackColor);
    };

    UInt8 ProcessMipBias(CrossSchema::TextureGroup group, UInt32 mipbias, UInt32& mipCount, ImageList& images, std::vector<UInt8>& data);
    CrossSchema::TextureAssetImage* GetImage(UInt32 imageIndex);
    CrossSchema::TextureAssetImage* GetImage(UInt32 mipLevel, UInt32 faceIndex, UInt32 arryIndex);
    uint32_t CreateTexAssetT();
    void Transcode();
    void Clear();
    ~TextureResource()
    {
        mData.clear();
    }

public:
    cross::TextureResourceInfo  mResourceInfo;
    UInt32                      mFlags = 0;
    ImageList                   mImages;
    std::vector<UInt8>          mData;
    CrossSchema::TextureAssetT  mTexAssetT;   //= nullptr;
    bool                        mVTStreaming = false;
    UInt32                      mFallbackColor = 0;
};

class Resource_API TextureResource_SpanData
{
public:
    bool Deserialize(cross::FBSerializer const& s, bool UseStreaming);
    UInt32 GetImageIndex(UInt32 mipLevel, UInt32 faceIndex) const;
    void GetImageLevels(UInt32 imageIndex, UInt32& mipLevel, UInt32& faceIndex) const;
    bool Deserialize(cross::FBSerializer const& s);
    inline UInt32 GetFaceNum() const{ return IsCubeType(mResourceInfo.Dimension) ? 6 : 1; };
    UInt32 GetImageIndex(UInt32 mipLevel, UInt32 faceIndex, UInt32 arryIndex) const;
    void GetImageLevels(UInt32 imageIndex, UInt32& mipLevel, UInt32& faceIndex, UInt32& arryIndex) const;

    const UInt8* GetImageData(UInt32 imageIndex) const;
    const UInt8* GetImageData(UInt32 mipLevel, UInt32 faceIndex, UInt32 arryIndex) const;

    UInt8* GetImageData(UInt32 imageIndex);
    UInt8* GetImageData(UInt32 mipLevel, UInt32 faceIndex, UInt32 arryIndex);

    CrossSchema::TextureAssetImage* GetImage(UInt32 imageIndex);
    CrossSchema::TextureAssetImage* GetImage(UInt32 mipLevel, UInt32 faceIndex, UInt32 arryIndex);

    cross::TextureResourceInfo mResourceInfo;
    std::vector<CrossSchema::TextureAssetImage> mImages;
    gsl::span<UInt8> mSpanData;
};