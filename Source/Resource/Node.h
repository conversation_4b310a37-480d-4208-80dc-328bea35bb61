#pragma once
#include "ResourceAsset_generated.h"

namespace cross
{
    // control of visitor function
    enum class NodeVisitMode
    {
        Break,
        Continue,
        Recurse,
    };

    class Node;

    class NodeVisitor
    {
        friend class Node;

        template <typename Func, typename Type = NodeVisitMode>
        using TEnableIfNodeVisitor = std::enable_if_t<
            std::is_invocable_r_v<NodeVisitMode, TRemoveRCVT<Func>, Node const*, Node const*>, Type>;

    public:
        template <typename Func> 
        auto OnBeginNode(Func&& func) -> std::enable_if_t<
            std::is_invocable_r_v<NodeVisitMode, TRemoveRCVT<Func>, Node const*, Node const*>,
            NodeVisitor&>
        {
            mBeginNode = std::forward<Func>(func);
            return *this;
        }

        template <typename Func>
        auto OnEndNode(Func&& func) -> std::enable_if_t<
            std::is_invocable_v<TRemoveRCVT<Func>, Node const*, NodeVisitMode>,
            NodeVisitor&>
        {
            mEndNode = std::forward<Func>(func);
            return *this;
        }

    private:
        TFunction<NodeVisitMode(Node const*, Node const*)> mBeginNode;
        TFunction<void(Node const*, NodeVisitMode)> mEndNode;
    };

    class Node
    {
    public:
        static auto Visit(Node const* root, NodeVisitor const& visitor)
        {
            return Visit(nullptr, root, visitor);
        }

        static auto Visit(Node const& root, NodeVisitor const& visitor)
        {
            return Visit(std::addressof(root), visitor);
        }

        static NodeVisitMode Visit(Node const* parent, Node const* current, NodeVisitor const& visitor)
        {
            if (!visitor.mBeginNode)
            {
                //NodeVisitMode::Break;
            }

            auto currentVisitResult = visitor.mBeginNode(parent, current);
            if (currentVisitResult == NodeVisitMode::Recurse)
            {
                for (auto const& node : current->Children)
                {
                    if (Visit(current, std::addressof(node), visitor) == NodeVisitMode::Break)
                    {
                        currentVisitResult = NodeVisitMode::Break;
                        break;
                    }
                }
            }

            if (visitor.mEndNode)
            {
                visitor.mEndNode(current, currentVisitResult);
            }

            return currentVisitResult;
        }
		static void buildnode(cross::Node& root, CrossSchema::ImportNodeT& fbnode)
		{
			if (fbnode.localtransform.size())
				root.LocalTransform = Matrix4x4f(fbnode.localtransform.data());
			else
				root.LocalTransform = Matrix4x4f::Identity();
			root.BoneIndexInImportMesh = fbnode.boneindexinimportmesh;
			root.IsRootInImportMesh = fbnode.isrootinimportmesh;
			root.MeshIndex = fbnode.meshindex;
			root.Name = fbnode.name;
			if (!fbnode.children.size())
				return;
			for (auto &s : fbnode.children)
			{
				root.Children.push_back(Node());
				buildnode(root.Children.back(), *s);
			}

		}
		static void UnPackNode(FBSerializer const & s, CrossSchema::ImportNodeT& root, const CrossSchema::ImportNode* rootnode)
		{
			
			s.Read(rootnode, root.localtransform, CrossSchema::ImportNode::VT_LOCALTRANSFORM);
			s.Read(rootnode, root.meshindex, CrossSchema::ImportNode::VT_MESHINDEX);
			s.Read(rootnode, root.name, CrossSchema::ImportNode::VT_NAME);
			if (rootnode->children())
			{
				for (flatbuffers::uoffset_t _i = 0; _i < rootnode->children()->size(); _i++)
				{
					root.children.push_back(std::unique_ptr<CrossSchema::ImportNodeT>(new CrossSchema::ImportNodeT));
					UnPackNode(s, *root.children[_i], rootnode->children()->Get(_i));
				}
			}
			s.Read(rootnode, root.isrootinimportmesh, CrossSchema::ImportNode::VT_ISROOTINIMPORTMESH);
			s.Read(rootnode, root.boneindexinimportmesh, CrossSchema::ImportNode::VT_BONEINDEXINIMPORTMESH);
		}
    public:
        Matrix4x4f              LocalTransform;
        int                     MeshIndex;
        std::string             Name;
        std::vector<Node>       Children;
		bool					IsRootInImportMesh = false;
		int						BoneIndexInImportMesh = -1;
    };
}
