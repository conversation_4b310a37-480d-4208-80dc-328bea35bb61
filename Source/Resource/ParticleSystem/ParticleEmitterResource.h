#pragma once
#include "Resource/ResourceManager.h"
#include "Resource/ParticleSystem/ParticleEmitterInfo.h"

namespace cross::fx
{
class Resource_API CEMeta(Script) ParticleEmitterResource : public Resource
{
public:
    FRIEND_WITH_REFLECTION_MODULE;
    friend class cross::ResourceManager;

protected:
    ParticleEmitterResource() = default;

public:
    virtual ~ParticleEmitterResource() = default;

    static int GetClassIDStatic() { return ClassID(ParticleEmitterResource); }

    bool Serialize(SerializeNode&& node, const std::string& path) override;

    bool Deserialize(DeserializeNode const& node) override;

    const char* GetEmitterPath() const;

    const char* GetMaterialPath() const;



public:
    inline void SetEmitterState(const EmitterStateInfo& info) { mEmitterState = info; }
    inline void SetParticleSpawn(const ParticleSpawnInfo& info) { mParticleSpawn = info; }
    inline void SetLocationShape(const LocationShapeInfo& info) { mLocationShape = info; }
    inline void SetParticleInit(const ParticleInitInfo& info) { mParticleInit = info; }

    inline void SetSizeScale(const SizeScaleInfo& scale) { mSizeScale = scale; }
    inline void SetColorScale(const ColorScaleInfo& scale) { mColorScale = scale; }
    inline void SetSpriteRotationRate(const SpriteRotationRateInfo& rate) { mSpriteRotationRate = rate; }
    inline void SetVelocity(const VelocityInfo& vel) { mVelocity = vel; }
    inline void SetVectorNoise(const VectorNoiseInfo& noise) { mVectorNoise = noise; }
    inline void SetGravity(const GravityInfo& gravity) { mGravity = gravity; }
    inline void SetForce(const ForceInfo& force) { mForce = force; }
    inline void SetVortex(const VortexForceInfo& vortex) { mVortex = vortex; }
    inline void SetPointAttraction(const PointAttractionForceInfo& attraction) { mPointAttraction = attraction; }
    inline void SetSolveForceVelocity(const SolveForceVelocityInfo& solve) { mSolveForceVelocity = solve; }
    inline void SetSubUV(const SubUVInfo& uv) { mSubUV = uv; }
    inline void SetParticleState(const ParticleStateInfo& particleState) { mParticleState = particleState; }
    inline void SetEventGenerator(const EventGeneratorInfo& generator) { mEventGenerator = generator; }

    inline void SetRendererType(const ParticleRendererType info) { mRendererType = info; }
    inline void SetSpriteRenderer(const ParticleSpriteRendererInfo& info) { mSpriteRenderer = info; }
    inline void SetMeshRenderer(const ParticleMeshRendererInfo& info) { mMeshRenderer = info; }

    inline EmitterStateInfo&           GetEmitterState() { return mEmitterState; }
    inline const EmitterStateInfo&     GetEmitterState() const { return mEmitterState; }
    inline ParticleSpawnInfo&          GetParticleSpawn() { return mParticleSpawn; }
    inline const ParticleSpawnInfo&    GetParticleSpawn() const { return mParticleSpawn; }
    inline LocationShapeInfo&          GetLocationShape() { return mLocationShape; }
    inline const LocationShapeInfo&    GetLocationShape() const  { return mLocationShape; }
    inline ParticleInitInfo&           GetParticleInit() { return mParticleInit; }
    inline const ParticleInitInfo&     GetParticleInit() const { return mParticleInit; }
    inline ParticleSpriteRendererInfo& GetSpriteRenderer() { return mSpriteRenderer; }
    inline ParticleMeshRendererInfo&   GetMeshRenderer() { return mMeshRenderer; }

    inline const SizeScaleInfo& GetSizeScale() const { return mSizeScale; }
    inline const ColorScaleInfo& GetColorScale() const { return mColorScale; }
    inline const SpriteRotationRateInfo& GetSpriteRotationRate() const { return mSpriteRotationRate; }
    inline const VelocityInfo& GetVelocity() const { return mVelocity; }
    inline const VectorNoiseInfo& GetVectorNoise() const { return mVectorNoise; }
    inline const GravityInfo& GetGravity() const { return mGravity; }
    inline const ForceInfo& GetForce() const { return mForce; }
    inline const VortexForceInfo& GetVortexForce() const { return mVortex; }
    inline const PointAttractionForceInfo& GetPointAttractionForce() const { return mPointAttraction; }
    inline const SolveForceVelocityInfo& GetSolveForceVelocity() const { return mSolveForceVelocity; }
    inline const SubUVInfo& GetSubUV() const { return mSubUV; }
    inline const ParticleStateInfo& GetParticleState() const { return mParticleState; }
    inline const EventGeneratorInfo& GetEventGenerator() const { return mEventGenerator; }

    inline const ParticleRendererType GetRendererType() const { return mRendererType;}
    inline const ParticleSpriteRendererInfo& GetSpriteRenderer() const { return mSpriteRenderer; }
    inline const ParticleMeshRendererInfo& GetMeshRenderer() const { return mMeshRenderer; }

public:
    CEMeta(Serialize)
    EmitterStateInfo  mEmitterState;

    CEMeta(Serialize)
    ParticleSpawnInfo mParticleSpawn;

    CEMeta(Serialize)
    LocationShapeInfo mLocationShape;

    CEMeta(Serialize)
    ParticleInitInfo  mParticleInit;

    CEMeta(Serialize)
    SizeScaleInfo mSizeScale;

    CEMeta(Serialize)
    ColorScaleInfo mColorScale;

    CEMeta(Serialize)
    SpriteRotationRateInfo mSpriteRotationRate;

    CEMeta(Serialize)
    VelocityInfo mVelocity;

    CEMeta(Serialize)
    VectorNoiseInfo mVectorNoise;

    CEMeta(Serialize)
    GravityInfo mGravity;

    CEMeta(Serialize)
    ForceInfo mForce;

    CEMeta(Serialize)
    VortexForceInfo mVortex;

    CEMeta(Serialize)
    PointAttractionForceInfo mPointAttraction;

    CEMeta(Serialize)
    SolveForceVelocityInfo mSolveForceVelocity;

    CEMeta(Serialize)
    SubUVInfo mSubUV;

    CEMeta(Serialize)
    ParticleStateInfo mParticleState;

    CEMeta(Serialize)
    EventGeneratorInfo mEventGenerator;

    CEMeta(Serialize)
    ParticleRendererType mRendererType{ParticleRendererType::Sprite};

    CEMeta(Serialize)
    ParticleSpriteRendererInfo mSpriteRenderer;

    CEMeta(Serialize)
    ParticleMeshRendererInfo mMeshRenderer;
};

}   // namespace cross::fx