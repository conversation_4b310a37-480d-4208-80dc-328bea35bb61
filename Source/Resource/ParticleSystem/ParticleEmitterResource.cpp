#include "EnginePrefix.h"
#include "ParticleEmitterResource.h"
#include "Resource/resourceasset.h"
#include "CrossFX/ParticleSystem/ParticleAttributeNames.h"
#include "Runtime/GameWorld/GameWorld.h"
namespace cross::fx
{

bool ParticleEmitterResource::Serialize(SerializeNode&& node, const std::string& path)
{
    if (!Resource::HasAsset())
    {
        CreateAsset(path);
    }
    Resource::ClearReference();

    SerializeContext context;
    node["EmitterState"] = std::move(mEmitterState.Serialize(context));
    node["LocationShape"] = std::move(mLocationShape.Serialize(context));
    node["ParticleSpawn"] = std::move(mParticleSpawn.Serialize(context));
    node["ParticleInit"] = std::move(mParticleInit.Serialize(context));
    node["SizeScale"] = std::move(mSizeScale.Serialize(context));
    node["ColorScale"] = std::move(mColorScale.Serialize(context));
    node["SpriteRotationRate"] = std::move(mSpriteRotationRate.Serialize(context));
    node["Velocity"] = std::move(mVelocity.Serialize(context));
    node["VectorNoise"] = std::move(mVectorNoise.Serialize(context));
    node["Gravity"] = std::move(mGravity.Serialize(context));
    node["Force"] = std::move(mForce.Serialize(context));
    node["Vortex"] = std::move(mVortex.Serialize(context));
    node["PointAttraction"] = std::move(mPointAttraction.Serialize(context));
    node["SolveForceVelocity"] = std::move(mSolveForceVelocity.Serialize(context));
    node["SubUV"] = std::move(mSubUV.Serialize(context));
    node["ParticleState"] = std::move(mParticleState.Serialize(context));

    if (mSpriteRenderer.Enabled)
    {
        node["RendererType"] = ParticleRendererType::Sprite;
        node["SpriteRenderer"] = std::move(mSpriteRenderer.Serialize(context));
        AddReferenceResource(mSpriteRenderer.MaterialPath);
    }
    else if (mMeshRenderer.Enabled)
    {
        node["RendererType"] = ParticleRendererType::Mesh;
        node["MeshRenderer"] = std::move(mMeshRenderer.Serialize(context));
        AddReferenceResource(mMeshRenderer.ModelPath);
        AddReferenceResource(mMeshRenderer.MaterialPath);
    }

    return Resource::Serialize(std::move(node), path);
}
bool ParticleEmitterResource::Deserialize(DeserializeNode const& node)
    {
    SerializeContext context;
    DESERIALIZE_MODULE(node, context, mEmitterState, "EmitterState");
    DESERIALIZE_MODULE(node, context, mLocationShape, "LocationShape");
    DESERIALIZE_MODULE(node, context, mParticleSpawn, "ParticleSpawn");
    DESERIALIZE_MODULE(node, context, mParticleInit, "ParticleInit");
    DESERIALIZE_MODULE(node, context, mSizeScale, "SizeScale");
    DESERIALIZE_MODULE(node, context, mColorScale, "ColorScale");
    DESERIALIZE_MODULE(node, context, mSpriteRotationRate, "SpriteRotationRate");
    DESERIALIZE_MODULE(node, context, mVelocity, "Velocity");
    DESERIALIZE_MODULE(node, context, mVectorNoise, "VectorNoise");
    DESERIALIZE_MODULE(node, context, mGravity, "Gravity");
    DESERIALIZE_MODULE(node, context, mForce, "Force");
    DESERIALIZE_MODULE(node, context, mVortex, "Vortex");
    DESERIALIZE_MODULE(node, context, mPointAttraction, "PointAttraction");
    DESERIALIZE_MODULE(node, context, mSolveForceVelocity, "SolveForceVelocity");
    DESERIALIZE_MODULE(node, context, mSubUV, "SubUV");
    DESERIALIZE_MODULE(node, context, mParticleState, "ParticleState");
    DESERIALIZE_MODULE(node, context, mSpriteRenderer, "SpriteRenderer");
    DESERIALIZE_MODULE(node, context, mMeshRenderer, "MeshRenderer");
    if (node.HasMember("RendererType"))
    {
        mRendererType = static_cast<ParticleRendererType>(node["RendererType"].AsInt32());
    }

    return true;
}

const char* ParticleEmitterResource::GetEmitterPath() const
{
    return GetAsset() ? GetAsset()->GetName() : nullptr;
}

const char* ParticleEmitterResource::GetMaterialPath() const
{
    // TODO(jihuixu) Maybe other type renderer.
    return mSpriteRenderer.MaterialPath.c_str();
}

}   // namespace cross::fx
