#pragma once
#include "CECommon/Animation/Curve/FloatCurve.h"
#include "CECommon/Utilities/Random.h"
#include "CrossBase/Math/CrossMath.h"
#include "CrossBase/Serialization/SerializeNode.h"
#include "CrossFX/ParticleSystem/ParticleCurvePool.h"

namespace cross
{
struct ColorCurve
{
    ColorCurve()
    {
        FloatCurveKey key;
        key.Value = 1.0;

        key.Time = 0.0;
        RCurve.Keys.emplace_back(key);
        key.Time = 1.0;
        RCurve.Keys.emplace_back(key);

        key.Time = 0.0;
        GCurve.Keys.emplace_back(key);
        key.Time = 1.0;
        GCurve.Keys.emplace_back(key);

        key.Time = 0.0;
        BCurve.Keys.emplace_back(key);
        key.Time = 1.0;
        BCurve.Keys.emplace_back(key);

        key.Time = 0.0;
        ACurve.Keys.emplace_back(key);
        key.Time = 1.0;
        ACurve.Keys.emplace_back(key);
    }

    ColorCurve& operator=(const ColorCurve& other)
    {
        Scaler = other.Scaler;
        RCurve = other.RCurve;
        GCurve = other.GCurve;
        BCurve = other.BCurve;
        ACurve = other.ACurve;
        mLerpColor = other.mLerpColor;

        return *this;
    }

    const ColorRGBAf& Evaluate(float time) const
    {
        time = std::max(0.0f, std::min(1.0f, time));
        mLerpColor = {RCurve.Eval(time, 0.0f) * Scaler.x, GCurve.Eval(time, 0.0f) * Scaler.y, BCurve.Eval(time, 0.0f) * Scaler.z, ACurve.Eval(time, 1.0f) * Scaler.w};
        return mLerpColor;
    }

    size_t Transfer(std::vector<UInt8>& output, size_t offset) const
    {
        output.resize(output.size() + sizeof(Float4) + sizeof(UInt4));
        size_t stride = 0;
        UInt8* dataPtr = output.data() + offset;
        std::memcpy(dataPtr + stride, &Scaler, sizeof(Float4));
        stride += sizeof(Float4);
        std::memcpy(dataPtr + stride, &CurveID, sizeof(UInt4));
        stride += sizeof(UInt4);
        return stride;
    }

    size_t UpdateInplace(std::vector<UInt8>& output, size_t offset) const
    {
        size_t stride = 0;
        UInt8* dataPtr = output.data() + offset;
        std::memcpy(dataPtr + stride, &Scaler, sizeof(Float4));
        stride += sizeof(Float4);
        std::memcpy(dataPtr + stride, &CurveID, sizeof(UInt4));
        stride += sizeof(UInt4);
        return stride;
    }

    void RecordCurveID(const UInt4& curveID) const
    {
        CurveID = curveID;
    }

    UInt4 GetCurveID() const
    {
        return CurveID;
    }

    CEFunction(PrecheckDeserializeNode)
    bool CheckValid(const DeserializeNode& in) { return in.IsObject(); }

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "FloatCurveTrack", ToolTips = "red curve"))
    FloatCurveTrack RCurve;

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "FloatCurveTrack", ToolTips = "green curve"))
    FloatCurveTrack GCurve;

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "FloatCurveTrack", ToolTips = "blue curve"))
    FloatCurveTrack BCurve;

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "FloatCurveTrack", ToolTips = "alpha curve"))
    FloatCurveTrack ACurve;

    CEMeta(Serialize, Editor, Reflect, Script)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "The scaler of color curve"))
    Float4 Scaler{1.0, 1.0, 1.0, 1.0};

    mutable ColorRGBAf mLerpColor;
    mutable UInt4 CurveID = {fx::INVALID_CURVE_ID, fx::INVALID_CURVE_ID, fx::INVALID_CURVE_ID, fx::INVALID_CURVE_ID};

    CE_Serialize_Deserialize;
};
}
