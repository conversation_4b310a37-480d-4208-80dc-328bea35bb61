#pragma once
#include "CrossBase/Math/CrossMath.h"
#include "CrossBase/Math/Color.h"
#include "MinMaxCurve.h"
#include "MinMaxGradient.h"
#include "ColorCurve.h"

namespace cross {

template<typename E>
constexpr auto enum_cast(E e) noexcept
{
    return static_cast<std::underlying_type_t<E>>(e);
}

template<typename E>
constexpr std::enable_if_t<std::is_enum<E>::value, E> operator&(E lhs, E rhs) noexcept
{
    return E(enum_cast(lhs) & enum_cast(rhs));
}

enum class CEMeta(Editor) ParticleAnimationMode
{
    InfinityLoop,
    WholeSheet,
    SingleRow
};

enum class CEMeta(Editor) GridRowMode
{
    Random,
    Custom
};

enum class CEMeta(Editor) ForceSpace
{
    Local,
    World
};

enum class CEMeta(Editor) KillTrigger
{
    None,
    Inside,
    Outside
};

enum class CEMeta(Editor) ScaleColorChannel : UInt32
{
    None               = 0,
    SCALE_CHANNEL_A    = 1 << 0,
    SCALE_CHANNEL_B    = 1 << 1,
    SCALE_CHANNEL_G    = 1 << 2,
    SCALE_CHANNEL_R    = 1 << 3,
    SCALE_CHANNEL_RG   = SCALE_CHANNEL_R | SCALE_CHANNEL_G,
    SCALE_CHANNEL_RB   = SCALE_CHANNEL_R | SCALE_CHANNEL_B,
    SCALE_CHANNEL_RA   = SCALE_CHANNEL_R | SCALE_CHANNEL_A,
    SCALE_CHANNEL_GB   = SCALE_CHANNEL_G | SCALE_CHANNEL_B,
    SCALE_CHANNEL_GA   = SCALE_CHANNEL_G | SCALE_CHANNEL_A,
    SCALE_CHANNEL_BA   = SCALE_CHANNEL_B | SCALE_CHANNEL_A,
    SCALE_CHANNEL_RGB  = SCALE_CHANNEL_R | SCALE_CHANNEL_G | SCALE_CHANNEL_B,
    SCALE_CHANNEL_RGBA = SCALE_CHANNEL_R | SCALE_CHANNEL_G | SCALE_CHANNEL_B | SCALE_CHANNEL_A,
};

struct DynamicVector2
{
    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve"))
    MinMaxCurve X{0.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve"))
    MinMaxCurve Y{0.0f};

    CE_Serialize_DeserializeEditor;
};

struct DynamicVector3
{
    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve"))
    MinMaxCurve X{0.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve"))
    MinMaxCurve Y{0.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve"))
    MinMaxCurve Z{0.0f};

    CE_Serialize_DeserializeEditor;
};

struct ModuleInfo
{
    ModuleInfo() = default;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", bHide = true))
    bool Enabled{false};

    virtual bool IsEnabled() const { return Enabled; };

    CE_Serialize_DeserializeEditor;
};
using ModuleInfoPtr = std::shared_ptr<ModuleInfo>;

struct SpriteRotationRateInfo : public ModuleInfo
{
    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve", ToolTips = "Sprite Rotation Rate (Degree)"))
    MinMaxCurve SpriteRotationRate;

    CE_Serialize_DeserializeEditor;
};

struct SizeScaleInfo : public ModuleInfo
{
    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve", ToolTips = "Particle's Size Scale"))
    MinMaxCurve SizeScale;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "If True, use XY separate value instead of SizeScale."))
    bool SeparateAxis{false};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve", ToolTips = "Particle's Size Scale For X Axis"))
    MinMaxCurve SizeScaleX;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve", ToolTips = "Particle's Size Scale For Y Axis"))
    MinMaxCurve SizeScaleY;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve", ToolTips = "Particle's Size Scale For Z Axis"))
    MinMaxCurve SizeScaleZ;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Whether scale particle depend on the distance between camera"))
    bool ScaleWithCamera{false};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "When ScaleWithCamera is enabled, xy means input scale range, yw means output scale range"))
    Float4 CameraRemapScale;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Whether scale particle depend on the velocity"))
    bool ScaleWithVelocity{false};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "How small a particle can be scaled base on its velocity"))
    Float3 MinScaleFactor{1.0f, 1.0f, 1.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "How large a particle can be scaled base on its velocity"))
    Float3 MaxScaleFactor{2.0f, 2.0f, 2.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "The speed threshold which defines the min and max scale factor, values above threshold clamp to max"))
    float VelocityThreshold{1000.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "If you decide to look up into the scale factor with a curve, you can vary how the scale is applied over the velocity variation"))
    bool SampleScaleFactorByCurve{false};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve", ToolTips = "Scale Factor Curve"))
    MinMaxCurve ScaleFactorCurve{MinMaxCurveMode::CURVE};

    CE_Serialize_DeserializeEditor;
};

struct ColorScaleInfo : public ModuleInfo
{
    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxGradient", ToolTips = "Particle's Color Scale"))
    MinMaxGradient ColorScale{MinMaxGradientMode::GRADIENT};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Whether scale particle's color depend on the distance between camera"))
    bool ScaleWithCamera{false};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Whether scale particle's color depend on the distance between camera"))
    ScaleColorChannel ScaleChannel{ScaleColorChannel::None};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "When ScaleWithCamera is enabled, xy means input scale range, yw means output scale range"))
    Float4 CameraRemapScale{0.0f, 1.0f, 0.0f, 1.0f};

    CE_Serialize_DeserializeEditor;
};

struct VelocityInfo : public ModuleInfo
{
    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "Velocity vector"))
    DynamicVector3 Vector;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "Velocity vector offset"))
    DynamicVector3 Offset;

    CE_Serialize_DeserializeEditor;
};

struct VectorNoiseInfo : public ModuleInfo
{
    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "Low values create soft, smooth noise."))
    DynamicVector3 Frequency{0.01f, 0.01f, 0.01f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Multiply scaler for the force"))
    float Scaler {1.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "Noise strength"))
    DynamicVector3 Strength{100.f, 100.f, 100.f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve", ToolTips = "Overlay this offset after noise compute for Z axes."))
    MinMaxCurve ScrollOffset;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "Offset apply on input particle's position"))
    DynamicVector3 PositionOffset{0.0f, 0.0f, 0.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "When enabled, use three axis value instead of X axis"))
    bool SeparateAxis{false};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "When enabled, strength is proportional to frequency."))
    bool Damping{true};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Specify how many layers of overlapping noise are combined to produce the final noise values."))
    UInt32 OctaveCount{0};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "For each additional noise layer, adjust the frequency by this multiplier."))
    float OctaveScale{0.5f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "For each additional noise layer, reduce the strength by this proportion."))
    float OctaveMultiplier{1.0f};

    CE_Serialize_DeserializeEditor;
};

struct GravityInfo : public ModuleInfo
{
    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "Gravity force vector"))
    DynamicVector3 GravityVector{0.0f, -980.0f, 0.0f};

    CE_Serialize_DeserializeEditor;
};

struct ForceInfo : public ModuleInfo
{
    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "Custom force vector"))
    DynamicVector3 ForceVector;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Multiply scaler for the force"))
    ForceSpace Space {ForceSpace::Local};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Multiply scaler for the force"))
    float Scaler {1.0f};

    CE_Serialize_DeserializeEditor;
};

struct VortexForceInfo : public ModuleInfo
{
    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "Custom force vector"))
    DynamicVector3 VortexAxis;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "Offset for VortexOriginOffset"))
    DynamicVector3 VortexOriginOffset;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve", ToolTips = "Amount of velocity to add to the force"))
    MinMaxCurve VortexForceAmount{0.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve", ToolTips = "An optional pull towards the origin origin"))
    MinMaxCurve OriginPullAmount{0.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Multiply scaler for the force"))
    ForceSpace Space {ForceSpace::Local};

    CE_Serialize_DeserializeEditor;
};

struct PointAttractionForceInfo : public ModuleInfo
{
    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve", ToolTips = "Attraction Strength"))
    MinMaxCurve AttractionStrength{0.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve", ToolTips = "Attraction Radius"))
    MinMaxCurve AttractionRadius{0.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Enabled Make FalloffExponent Useful"))
    bool UseFalloff{false};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Exponent to apply to the falloff."))
    float FalloffExponent{0.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve", ToolTips = "If within around the Attractor Position, kill particle."))
    MinMaxCurve KillRadius{0.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "In order to catch extremely high speed particles that would otherwise overshoot the kill radius on a single frame."))
    float KillRadiusOvershootCorrection{1.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Attractor Position"))
    Float3 AttractorPosition;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Enable AttractorOffset"))
    bool UseAttractorOffset{false};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "Offset apply on attractor's position"))
    DynamicVector3 AttractorOffset{0.0f, 0.0f, 0.0f};

    CE_Serialize_DeserializeEditor;
};

struct SolveForceVelocityInfo : public ModuleInfo
{
    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve", ToolTips = "Speed Scale"))
    MinMaxCurve SpeedScale{1.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Enable SpeedLimit"))
    bool EnableSpeedLimit{false};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve", ToolTips = "Speed Limit"))
    MinMaxCurve SpeedLimit{1000.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Drag force control switch"))
    bool DragEnable{false};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve", ToolTips = "Apply a Drag force to particle velocity when DragEnable is True"))
    MinMaxCurve Drag{0.0f};

    CE_Serialize_DeserializeEditor;
};

struct SubUVInfo : public ModuleInfo
{
    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "The number of tiles X"))
    UInt32 TileX{1u};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "The number of tiles X"))
    UInt32 TileY{1u};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "The Animation mode can be set to Whole Sheet or Single Row."))
    ParticleAnimationMode Animation{ParticleAnimationMode::InfinityLoop};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve", ToolTips = "Allows you to specify which frame the particle animation should start on."))
    MinMaxCurve StartFrame;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "MinMaxCurve", ToolTips = "A curve that specifies how the frame of animation increases as time progresses."))
    MinMaxCurve Frame;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "A curve that specifies how the frame of animation increases as time progresses."))
    SInt32 Cycles{1};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "While ParticleAnimationMode is InfinityLoop, auto accumulate for indexf per frame."))
    float PlayRate{1.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "When Animation is SingleRow mode, the way for select row."))
    GridRowMode RowMode{GridRowMode::Custom};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Which row to be selected."))
    UInt32 RowIndex{0u};

    CE_Serialize_DeserializeEditor;
};

struct ParticleStateInfo : public ModuleInfo
{
    CEMeta(Serialize, Editor, Reflect) 
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "The trigger when kill particles"))
    KillTrigger KillTrigger {KillTrigger::None};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Volume influence particle's state"))
    Float3 Volume;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Volume's position"))
    Double3 VolumePosition;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Volume's rotation"))
    Float3 VolumeRotation;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Volume's position space"))
    ForceSpace Space;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Remove distant particles from the camera"))
    bool KillDistantFromCamera = false;

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Particle distance from the camera"))
    float CameraDistance = 1000;

    CE_Serialize_DeserializeEditor;
};

struct EventGeneratorInfo : public ModuleInfo
{
    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Whether to generate SpawnEvent"))
    bool GenSpawnEvent{false};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Whether to generate DeathEvent"))
    bool GenDeathEvent{false};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Whether to generate CollisionEvent"))
    bool GenCollisionEvent{false};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Whether to generate CustomEvent"))
    bool GenCustomEvent{false};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "The probability of event trigger"))
    float EventProbability{1.0f};

    CEMeta(Serialize, Editor, Reflect)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Delay to send event"))
    float SendDelay{0.0f};

    CE_Serialize_DeserializeEditor;
};

}