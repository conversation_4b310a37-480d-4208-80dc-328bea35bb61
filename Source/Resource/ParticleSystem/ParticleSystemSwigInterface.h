#pragma once
#include "Resource/Resource.h"
#include "ParticleSystemInfo.h"
#include "ParticleEmitterInfo.h"

namespace cross
{
    CE<PERSON><PERSON>(Editor) 
    ParticleSystemInfo GetParticleSystemInfo(cross::Resource* resource);

    CEMeta(Editor)
    void* GetParticleEmitterInfo(cross::Resource* resource);

    CEMeta(Editor)
    void AddParticleEmitter(cross::Resource* system, cross::Resource* emitter, const ParticleSystemInfo& info);

    CEMeta(Editor)
    void UpdateParticleSystem(cross::Resource* system, const ParticleSystemInfo& info);
    
    CEMeta(Editor)
    void UpdateParticleEmitter(cross::Resource* emitter, const ParticleEmitterInfo& info);
}   // namespace cross