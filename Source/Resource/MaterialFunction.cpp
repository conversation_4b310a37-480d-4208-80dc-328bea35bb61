#include "MaterialFunction.h"
#include "ResourceManager.h"

namespace cross::resource {
bool MaterialFunction::Serialize(SerializeNode&& s, const std::string& path)
{
    if (!Resource::HasAsset())
    {
        CreateAsset(path);
    }

    Resource::ClearReference();

    if (!mExpressionsString.empty())
    {
        s["expression"] = SerializeNode::ParseFrom<PERSON>son(mExpressionsString);
    }

    {
        SerializeContext context{};
        s["defines"] = mDefines.Serialize(context);
    }

    return Resource::Serialize(std::move(s), path);
}

bool MaterialFunction::Deserialize(DeserializeNode const& s)
{
    if (s.<PERSON>("expression"))
    {
        mExpressionsString = s["expression"].FormatToJson();
    }
    else
    {
        mExpressionsString.clear();
    }

    if (s.<PERSON>("defines"))
    {
        SerializeContext context{};
        mDefines.Deserialize(s["defines"], context);
    }

    return true;
}

bool MaterialFunction::ResetResource()
{
    mExpressionsString.clear();
    mDefines = MaterialFunctionDefines{};
    return Resource::ResetResource();
}

MaterialFunction* MaterialFunction::MaterialFunction_CreateMaterialFunction()
{
    auto materialFunctionPtr = gResourceMgr.CreateResourceAs<cross::resource::MaterialFunction>();
    materialFunctionPtr->IncreaseRefCount();
    return materialFunctionPtr.get();
}
void MaterialFunction::CopyFrom(const MaterialFunction& function)
{
    mExpressionsString = function.mExpressionsString;
    mDefines = function.mDefines;
}
}   // namespace cross::resource