#pragma once

//#include "NativeGraphicsInterface/RHI/DeviceTypes.h"
//#include "Runtime/RenderSystem/GPUContext/GPUResource/Public/TextureFormatConvert.h"
//
///* Important note about endianess.
//Endianess needs to be swapped for the following formats:
//TexFormatARGBFloat, TexFormatRGB565, TexFormatARGB4444, (assuming for this too: TexFormatRGBA4444)
//*/
//
//UInt32 GetBytesFromTextureFormat(TextureFormat inFormat);
//UInt32 GetMaxBytesPerPixel(TextureFormat inFormat);
//int GetRowBytesFromWidthAndFormat(int width, TextureFormat format);
//bool IsValidTextureFormat(TextureFormat format);
//
//inline bool IsCompressedBC45TextureFormat(TextureFormat format)
//{
//	return (format == TexFormatBC4) || (format == TexFormatBC5);
//}
//
//inline bool IsCompressedBC67TextureFormat(TextureFormat format)
//{
//	return (format == TexFormatBC6H) || (format == TexFormatBC7);
//}
//
//inline bool IsISPCCompatibleTextureFormat(TextureFormat format)
//{
//	return (format == TexFormatBC6H) || (format == TexFormatBC7) || (format == TexFormatDXT1) || (format == TexFormatDXT5);
//}
//
//inline bool IsCompressedDXTTextureFormat(TextureFormat format)
//{
//	return format >= TexFormatDXT1 && format <= TexFormatDXT5;
//}
//
//inline bool IsCompressedCrunchTextureFormat(TextureFormat format)
//{
//	return format == TexFormatDXT1Crunched || format == TexFormatDXT5Crunched || format == TexFormatETC_RGB4Crunched || format == TexFormatETC2_RGBA8Crunched;
//}
//
//inline bool IsCompressedPVRTCTextureFormat(TextureFormat format)
//{
//	return format >= TexFormatPVRTC_RGB2 && format <= TexFormatPVRTC_RGBA4;
//}
//
//inline bool IsCompressedETCTextureFormat(TextureFormat format)
//{
//	return format == TexFormatETC_RGB4;
//}
//
//inline bool IsCompressedEACTextureFormat(TextureFormat format)
//{
//	return format >= TexFormatEAC_R && format <= TexFormatEAC_RG_SIGNED;
//}
//
//inline bool IsCompressedETC2TextureFormat(TextureFormat format)
//{
//	return format >= TexFormatETC2_RGB && format <= TexFormatETC2_RGBA8;
//}
//
//inline bool Is16BitTextureFormat(TextureFormat format)
//{
//	return format == TexFormatARGB4444 || format == TexFormatRGBA4444 || format == TexFormatRGB565;
//}
//
//inline bool IsCompressedASTCTextureFormat(TextureFormat format)
//{
//	return (format >= TexFormatASTC_4x4 && format <= TexFormatASTC_12x12) || (format >= TexFormatASTC_HDR_4x4 && format <= TexFormatASTC_HDR_12x12);
//}
//
//inline bool IsAnyCompressedTextureFormat(TextureFormat format)
//{
//	return IsCompressedDXTTextureFormat(format) || IsCompressedPVRTCTextureFormat(format)
//		|| IsCompressedETCTextureFormat(format) || IsCompressedEACTextureFormat(format)
//		|| IsCompressedETC2TextureFormat(format) || IsCompressedASTCTextureFormat(format);
//}
//
//bool IsAlphaOnlyTextureFormat(TextureFormat format);
//
//int GetTextureSizeAllowedMultiple(TextureFormat format);
//int GetMinimumTextureMipSizeForFormat(TextureFormat format);
//bool IsAlphaOnlyTextureFormat(TextureFormat format);
//
//TextureFormat ConvertToAlphaTextureFormat(TextureFormat format);
//
//bool HasAlphaTextureFormat(TextureFormat format);
//
////bool IsDepthRTFormat(RenderTextureFormat format);
////bool IsHalfRTFormat(RenderTextureFormat format);
//
//const char* GetCompressionTypeString(TextureFormat format);
//const char* GetTextureFormatString(TextureFormat format);
//const char* GetTextureColorSpaceString(TextureColorSpace colorSpace);
//
//TextureColorSpace ColorSpaceToTextureColorSpace(ColorSpace colorSpace);
