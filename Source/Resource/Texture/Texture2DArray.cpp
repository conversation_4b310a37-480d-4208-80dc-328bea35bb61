#include "EnginePrefix.h"
#include "Resource/Texture/Texture2DArray.h"
#include "CrossBase/Threading/RenderingThread.h"
#include "Resource/AssetStreaming.h"
#include "Resource/Texture/Texture2D.h"
#include "Resource/Texture/TextureUDIM.h"
#include "Resource/ResourceManager.h"

namespace cross { namespace resource {
    void Texture2DArray::DestroyRenderData(cross::FrameParam* frameParam)
    {
        mTextures.clear();
        DispatchRenderingCommandWithToken([&] {
            if (mGPUTex)
                mGPUTex.reset();
            if (mTextureSource)
                mTextureSource.reset();
        });
    }

    bool Texture2DArray::CreateGPUResource()
    {
        if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() != AppStartUpTypeHeadless)
        {
            if (mTextures.size() <= 0)
                return false;

            cross::IGPUTexture* oldGpuTex = nullptr;
            if (mGPUTex.get() == nullptr || mGPUTex->GetType() != GetGPUTextureTypeByInfo(mEnableMergeVT))
            {
                mGPUTex = gResourceMgr.mCreateRenderObjectMgr->GetGpuTex(mTextureInfo);
            }
            Assert(mGPUTex);

            IncreaseRefCount();
            // DispatchRenderingCommandWithToken([this, oldGpuTex]
            {
                QUICK_SCOPED_CPU_TIMING("Texture2DArray::CreateGPUResource");
                if (!mEnableVT)
                {
                    std::vector<cross::IGPUTexture*> gpuTexs;
                    for (auto tex : mTextures)
                    {
                        if (auto renderTex = tex->GetTextureR())
                        {
                            gpuTexs.emplace_back(renderTex);
                        }
                    }
                    if (gpuTexs.size() == mTextures.size())
                    {
                        mGPUTex->Initialize(mTextureInfo, gpuTexs, GetName().c_str());
                    }
                }
                else
                {
                    std::vector<std::shared_ptr<TextureDataProvider>> texDatas;
                    for (auto tex : mTextures)
                        texDatas.emplace_back(tex->GetTextureDataPtr());
                    mGPUTex->Initialize(texDatas, mEnableMergeVT);
                    mGPUTex->GetNGITexture()->SetDebugName(GetName().c_str());
                }
                
                DecreaseRefCount();
            }
            //);
        }
        return true;
    }

    bool Texture2DArray::ResetResource() {
        Resource::ResetResource();
        Clear();
        return true;
    }

    Texture2DArray::Texture2DArray(TextureFormat format, ColorSpace colorSpace, UInt32 wide, UInt32 high, UInt32 depth, UInt32 mipCount, UInt32 arraySize)
        : Texture({TextureDimension::Tex2DArray, format, colorSpace, wide, high, depth, mipCount, arraySize})
    {}


    bool Texture2DArray::Serialize()
    {
        ClearReference();
        SerializeNode node;
        node["EnableVT"] = mEnableVT;
        node["EnableMergeVT"] = mEnableMergeVT;
        SerializeNode textureListNode = SerializeNode::EmptyArray();

        for (const auto& texture : mTextures)
        {
            textureListNode.PushBack(texture->GetGuid_Str());
            if (!mEnableVT)
            {
                AddReferenceResource(texture->GetGuid_Str());
            }
        }
        node["TextureList"] = std::move(textureListNode);
        Resource::Serialize(std::move(node), GetName());
        // 
        if (mOriginEnableVT != mEnableVT)
        {
            for (auto textRes : mTextures)
            {
                textRes->Serialize();
            }
            mOriginEnableVT = mEnableVT;
        }
        return true;
    }

    bool Texture2DArray::Serialize(const CrossSchema::TextureAssetT& inTextureAsset)
    {
        return false;
    }


    bool Texture2DArray::Deserialize(DeserializeNode const& s)
    {
        if (auto enableVTNode = s.HasMember("EnableVT"); enableVTNode)
        {
            mEnableVT = enableVTNode->AsBoolean();

            mOriginEnableVT = mEnableVT;
 
        }
        if (auto enableMergeVTNode = s.HasMember("EnableMergeVT"); enableMergeVTNode)
        {
            mEnableMergeVT = enableMergeVTNode->AsBoolean();
        }
        if (auto textureListNode = s.HasMember("TextureList"); textureListNode && textureListNode->IsArray())
        {
            for (int idx = 0; idx < textureListNode->Size(); idx++)
            {
                auto texture = textureListNode->At(idx).AsString();
                AddTexture(texture);
            }
        }
        //CreateGPUResource();
        return true;
    }

    bool Texture2DArray::Deserialize(cross::FBSerializer const& s)
    {
        if (mTextureSource == nullptr)
        {
            mTextureSource = std::make_shared<TextureResourceDataProvider>(GetName_CStr(), GetAsset()->GetHeader());
        }
        else
        {
            mTextureSource->Reload(GetAsset()->GetHeader());
        }

        if (mTextureSource->Deserialize(s))
        {
            mTextureInfo = mTextureSource->GetTextureInfo();
            //CreateGPUResource();
            return true;
        }
        return false;
    }

    void Texture2DArray::DeserializeCustomNode(const DeserializeNode& json)
    {
    }

    bool Texture2DArray::PostDeserialize()
    {
        return CreateGPUResource();
    }

    int  Texture2DArray::GetTextureIndex(const std::string& path) const
    {
        auto guid = gResourceMgr.ConvertPathToGuid(path);
        for (UInt32 i = 0; i < mTextures.size(); i++)
        {
            if (guid == mTextures[i]->GetGuid_Str())
                return i;
        }
        return -1;
    }

    std::string Texture2DArray::GetTexture(int index) const {
        if (index >= GetTextureCount())
            return "";
        return mTextures[index]->GetGuid_Str();
    }

    Texture2DPtr Texture2DArray::GetTextureResource(int index) const
    {
        if (index >= GetTextureCount())
            return Texture2DPtr(nullptr);
        return mTextures[index];
    }

    int  Texture2DArray::AddTextures(const std::vector<std::string>& paths)
    {
        int successNum = 0;
        for (const auto& path : paths)
        {
            if (AddTexture(path))
                successNum += 1;
        }
        return successNum;
    }

    bool Texture2DArray::AddTexture(const std::string& path)
    {
        //if (HasTexture(path))
        //    return false;
        const auto& filepath = gResourceMgr.ConvertGuidToPath(path);
        if (!EngineGlobal::GetFileSystem()->HaveFile(filepath))
            return false;
        auto srcRes = gAssetStreamingManager->GetResource(filepath.c_str(), true, !mEnableVT);
        if (srcRes->GetClassID() != ClassID(Texture2D))
            return false;
        return AddTexture(TypeCast<Texture>(srcRes));
    }

    bool Texture2DArray::ReplaceTexture(int index, const std::string& path)
    {
        const auto& filepath = gResourceMgr.ConvertGuidToPath(path);
        if (!EngineGlobal::GetFileSystem()->HaveFile(filepath))
            return false;
        auto srcRes = gAssetStreamingManager->GetResource(filepath.c_str(), true, !mEnableVT);
        if (srcRes->GetClassID() != ClassID(Texture2D))
            return false;
        return ReplaceTexture(index, TypeCast<Texture> (srcRes));
    }

    bool Texture2DArray::AddTexture(TexturePtr textRes)
    {
        if (textRes->GetClassID() != ClassID(Texture2D))
            return false;
        auto text2DRes = TypeCast<Texture2D>(textRes);
        InitByTexture(text2DRes);
        return AddTexture2D(text2DRes);
    }

    bool Texture2DArray::ReplaceTexture(int index, TexturePtr textRes)
    {
        if (textRes->GetClassID() != ClassID(Texture2D))
            return false;
        auto text2DRes = TypeCast<Texture2D>(textRes);
        if (!CheckTexture2D(text2DRes))
            return false;
        mTextures[index] = text2DRes;
        return true;
    }

    bool Texture2DArray::DelTexture(int index, bool transcode)
    {
        if (index >= mTextures.size())
            return false;
        mTextureInfo.ArraySize -= mTextures[index]->GetTextureData()->GetFaceNum();

        mTextures.erase(mTextures.begin() + index);

        if (mTextureInfo.ArraySize <= 0)
            Clear();
        return true;
    }

    void Texture2DArray::SetEnableVTStreaming(bool enable)
    {
        if (mEnableVT == enable)
            return;
        mEnableVT = enable;
        mTextureInfo.EnableVirtualTextureStreaming = enable;
        for (auto tex : mTextures)
        {
            tex->SetEnableVTStreaming(enable);
        }
    }
    
    std::vector<std::string> Texture2DArray::GetTextures() const {
        std::vector<std::string> texs;
        for (auto tex : mTextures)
        {
            texs.emplace_back(tex->GetGuid_Str());
        }
        return texs;
    }

    bool Texture2DArray::SetTextures(const std::vector<std::string>& paths)
    {
        bool isChanged = false;
        SInt32 newTexNum = static_cast<UInt32>(paths.size());
        std::vector<std::string> newTexs(newTexNum);
        for (SInt32 i = 0; i < newTexNum; i++)
        {
            newTexs[i] = gResourceMgr.ConvertPathToGuid(paths[i]);
        }
        std::vector<SInt32> deleteTextures;
        for (SInt32 j = 0; j < mTextures.size();)
        {
            if (j >= newTexNum)
            {
                isChanged = true;
                DelTexture(j, false);
            }
            else
            {
                if (mTextures[j]->GetGuid_Str() != newTexs[j])
                {
                    isChanged = true;
                    ReplaceTexture(j, newTexs[j]);
                }
                j++;
            }
        }
        SInt32 addTexNum = newTexNum - static_cast<SInt32>(mTextures.size());
        for (SInt32 k = addTexNum; k > 0; k--)
        {
            isChanged = true;
            AddTexture(newTexs[newTexNum - k]);
        }
        return isChanged;
    }

    bool Texture2DArray::FinishAddTexture()
    {
        return CreateGPUResource();
    }

    void Texture2DArray::Clear()
    {
        mTextures.clear();

        if (mTextureSource)
            mTextureSource->Clear();
        mTextureSource = nullptr;
        mIsInit = false;
    }

    bool Texture2DArray::CheckTexture2D(Texture2DPtr textRes)
    {
        if (!mIsInit)
            return false;
        const auto& textInfo = textRes->GetTextureInfo();
        return textInfo.Format == mTextureInfo.Format 
            && textInfo.ColorSpace == mTextureInfo.ColorSpace 
            && textInfo.Width == mTextureInfo.Width 
            && textInfo.Height == mTextureInfo.Height 
            && textInfo.MipCount == mTextureInfo.MipCount 
            && textInfo.EnableVirtualTextureStreaming == mTextureInfo.EnableVirtualTextureStreaming;
    }

    bool Texture2DArray::AddTexture2D(Texture2DPtr textRes)
    {
        if (!CheckTexture2D(textRes))
            return false;
        mTextureInfo.ArraySize += textRes->GetTextureData()->GetFaceNum();
        mTextures.emplace_back(textRes);
        return true;
    }

    void Texture2DArray::InitByTexture(Texture2DPtr textRes)
    {
        if (mIsInit)
            return;
        const auto& textInfo = textRes->GetTextureInfo();

        mWide                   = textInfo.Width;
        mHigh                   = textInfo.Height;
        mDepth                  = textInfo.Depth;
        mTextureInfo            = textInfo;
        mTextureInfo.MinMips    = textInfo.MinMips;
        mTextureInfo.MaxMips    = textInfo.MaxMips;
        mTextureInfo.MipCount   = textInfo.MipCount;
        mTextureInfo.Dimension  = TextureDimension::Tex2DArray;
        mTextureInfo.ArraySize  = 0;
        mTextureInfo.EnableVirtualTextureStreaming = mEnableVT;
        mIsInit = true;
    }

    void Texture2DArray::InitTextureResource(const TextureInfo& textureInfo) {
        if (mTextureSource == nullptr)
        {
            mTextureSource = std::make_shared<TextureResourceDataProvider>(GetName_CStr(), GetAsset()->GetHeader());
        }
        else
        {
            mTextureSource->Reload(GetAsset()->GetHeader());
        }

        mTextureSource->mResourceInfo.Dimension     = textureInfo.Dimension;
        mTextureSource->mResourceInfo.Format        = textureInfo.Format;
        mTextureSource->mResourceInfo.ColorSpace    = textureInfo.ColorSpace;
        mTextureSource->mResourceInfo.Width         = textureInfo.Width;
        mTextureSource->mResourceInfo.Height        = textureInfo.Height;
        mTextureSource->mResourceInfo.MipCount      = textureInfo.MipCount;
        mTextureSource->mResourceInfo.ArraySize     = textureInfo.ArraySize;
        mTextureSource->mResourceInfo.VirtualTextureStreaming = mTextureInfo.EnableVirtualTextureStreaming;
    }
}}   // namespace cross::resource
