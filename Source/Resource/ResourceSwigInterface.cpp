#include "EnginePrefix.h"
#include "ResourceSwigInterface.h"
#include "Resource/MeshAssetDataResource.h"
#if CROSSENGINE_WIN
#    include "cmft/image.h"
#include "MeshletGenerator/D3D12MeshletGenerator.h"
#include "CECommon/Utilities/Random.h"
#endif
#include <CrossImage/image.h>
#include <Resource/AssetStreaming.h>
#include "Resource/Material.h"
#include "Math/QTangents.h"

#define MIN_int32 static_cast<int>(0x80000000)
#define MAX_int32 static_cast<int>(0x7fffffff)
#define DECODE_TEST 0

namespace cross {

class BitReader
{
public:
    BitReader(std::vector<UInt8>& buffer)
        : mBuffer(buffer)
        , mByteOffset(0)
        , mBitOffset(0)
    {}

    void MarkOffset(UInt32 offset)
    {
        UInt32 byteOffset = offset / 8;
        UInt32 bitOffset = offset % 8;
        MarkOffset(byteOffset, bitOffset);
    }

    void MarkOffset(UInt32 byteOffset, UInt32 bitOffset)
    {
        mByteOffset = byteOffset;
        mBitOffset = bitOffset;
        if (mBitOffset > 8) 
        {
            mByteOffset += mBitOffset / 8;
            mBitOffset = mBitOffset % 8;
        }
    }

    UInt32 ReadBits(UInt32 numBits)
    {
        UInt32 result = *(reinterpret_cast<UInt32*>(mBuffer.data() + mByteOffset));
        result >>= mBitOffset;
        result &= (1 << numBits) - 1;
        MarkOffset(mByteOffset, mBitOffset + numBits);
        return result;
    }

    void Flush() 
    {
        if (mBitOffset > 0) 
        {
            mByteOffset += 1;
            mBitOffset = 0;
        }
    }

private:
    std::vector<UInt8>& mBuffer;
    UInt32 mByteOffset;
    UInt32 mBitOffset;
};

struct RGAValue
{
    UInt32 R;
    UInt32 G;
    UInt32 B;
};

TerrainInfo GetTerrainInfoFromResource(cross::Resource* resource)
{
    TerrainInfo terrainInfo;
    if (resource->GetClassID() == ClassID(TerrainResource)) {
        auto terrainRes = TYPE_CAST(resource::TerrainResource*, resource);
        terrainInfo = terrainRes->mTerrainInfo;
    }
    return terrainInfo;
}

static Float3 OctahedronDecode(int X, int Y, int QuantizationBits)
{
    const int QuantizationMaxValue = (1 << QuantizationBits) - 1;
    float fx = X * (2.0f / QuantizationMaxValue) - 1.0f;
    float fy = Y * (2.0f / QuantizationMaxValue) - 1.0f;
    float fz = 1.0f - std::abs(fx) - std::abs(fy);
    float t = std::clamp(-fz, 0.0f, 1.0f);
    fx += (fx >= 0.0f ? -t : t);
    fy += (fy >= 0.0f ? -t : t);

    return Float3(fx, fy, fz).Normalized();
}

static Float3 OctahedronDecode(UInt32 Packed, int QuantizationBits)
{
    int X, Y;
    UInt32 mask = (1 << 9) - 1;
    X = Packed & mask;
    Y = (Packed >> 9) & mask;
    return OctahedronDecode(X, Y, QuantizationBits);
}

static Float2 OctahedronEncode(Float3 N)
{
    N /= (std::abs(N.x) + std::abs(N.y) + std::abs(N.z));

    if (N.z < 0.0)
    {
        Float3 absN(std::abs(N.x), std::abs(N.y), std::abs(N.z));
        N.x = (N.x >= 0.0f) ? (1.0f - absN.y) : (absN.y - 1.0f);
        N.y = (N.y >= 0.0f) ? (1.0f - absN.x) : (absN.x - 1.0f);
    }

    return Float2(N.x, N.y);
}

static void OctahedronEncodePrecise(Float3 N, int& X, int& Y, int QuantizationBits)
{
    const int QuantizationMaxValue = (1 << QuantizationBits) - 1;
    Float2 Coord = OctahedronEncode(N);

    const float Scale = 0.5f * QuantizationMaxValue;
    const float Bias = 0.5f * QuantizationMaxValue;
    int NX = std::clamp(static_cast<int>(Coord.x * Scale + Bias), 0, QuantizationMaxValue);
    int NY = std::clamp(static_cast<int>(Coord.y * Scale + Bias), 0, QuantizationMaxValue);

    float MinError = 1.0f;
    int BestNX = 0;
    int BestNY = 0;
    for (int OffsetY = 0; OffsetY < 2; OffsetY++)
    {
        for (int OffsetX = 0; OffsetX < 2; OffsetX++)
        {
            int TX = NX + OffsetX;
            int TY = NY + OffsetY;
            if (TX <= QuantizationMaxValue && TY <= QuantizationMaxValue)
            {
                Float3 RN = OctahedronDecode(TX, TY, QuantizationBits);
                float Error = std::abs(1.0f - RN.Dot(N));
                if (Error < MinError)
                {
                    MinError = Error;
                    BestNX = TX;
                    BestNY = TY;
                }
            }
        }
    }

    X = BestNX;
    Y = BestNY;
}

MeshAssetDataInfo GetMeshInfoFromResource(cross::Resource* resource)
{
    MeshAssetDataInfo meshinfo;
    if (resource->GetClassID() == ClassID(MeshAssetDataResource))
    {
        auto meshres = TYPE_CAST(resource::MeshAssetDataResource*, resource);

        const MeshAssetData* meshAssetData = meshres->GetAssetData();
        const resource::MeshAssetLODSetting* LodSettings = meshres->GetLODSetting();
        const auto& lodIndices = meshAssetData->GetLodStartIndex();
        const UInt8 lodCount = std::min<UInt8>(static_cast<UInt8>(LodSettings->mLevelSettings.size()), meshAssetData->GetLodCount());

        meshinfo.VertexCounts.resize(lodCount);
        meshinfo.TrianglesCounts.resize(lodCount);
        meshinfo.LodGroups.resize(lodCount);

        UInt32 meshPartCount = 0;
        // find the largest mesh parts;
        for (int i = 0; i < lodCount; i++)
        {
            meshPartCount = std::max(meshPartCount, meshAssetData->GetMeshPartCount(i));
        }
        meshinfo.MaterialSlots.resize(meshPartCount);
        for (auto lodIndex = 0u; lodIndex < lodCount; ++lodIndex)
        {
            const UInt32 lodMeshPartCount = meshAssetData->GetMeshPartCount(lodIndex);
            const UInt32 lodOffset = lodIndices[lodIndex];
            for (auto meshPartIndex = 0u; meshPartIndex < meshPartCount; ++meshPartIndex)
            {
                if (meshPartIndex + lodOffset < meshAssetData->GetAllLodMeshPartCount() && meshPartIndex < lodMeshPartCount)
                {
                    const auto& meshPartInfo = meshAssetData->GetMeshPartInfo(meshPartIndex + lodOffset);
                    meshinfo.VertexCounts[lodIndex] += meshPartInfo.mVertexCount;
                    meshinfo.TrianglesCounts[lodIndex] += meshPartInfo.mIndexCount / 3;
                }

                auto& MaterialSlot = meshinfo.MaterialSlots[meshPartIndex].LodMaterialSlots.emplace_back();
                MaterialSlot.LodIndex = lodIndex;
                MaterialSlot.MeshPartIndex = meshPartIndex;
            }

            const auto& levelSetting = LodSettings->mLevelSettings[lodIndex];
            meshinfo.LodGroups[lodIndex].FadeTrasition = levelSetting.mFadeTransitionWidth;
            meshinfo.LodGroups[lodIndex].ScreenHeight = levelSetting.mScreenReleativeTransitionHeight;
        }
        meshinfo.CulledHeight = LodSettings->mCulledHeight;
        meshinfo.IsStreamable = LodSettings->mIsStreamable;

        meshinfo.UVCount = meshAssetData->GetUVChannelCount();
        meshinfo.MeshChannelsInformations = meshAssetData->GartherVertexChannelInfos();
        meshinfo.MeshVertexFormats = meshAssetData->GartherVertexFormatInfos();
        meshinfo.IsSkinedMesh = meshAssetData->IsSkinValid();
        //auto* simplify = meshres->GetSimplify();
        //meshinfo.Simplification.TriangleRate = simplify->triangleRate;
        meshinfo.WGS84CurvationCorrection.MeshCurvation = meshAssetData->HaveVersionFlag(MeshAssetVersionFlags::VersionMeshCurvation);
        meshinfo.WGS84CurvationCorrection.PhysicsCurvation = meshAssetData->HaveVersionFlag(MeshAssetVersionFlags::VersionPhysicsCurvation);
        const auto& bbox = meshAssetData->GetBoundingBox();
        bbox.GetCenter(&meshinfo.MeshCenter);
        bbox.GetExtent(&meshinfo.MeshSize);
    }
    return meshinfo;
}

void UpdateMeshLodSetting(cross::Resource* resource, const MeshAssetDataInfo& mesh)
{
    if (resource->GetClassID() == ClassID(MeshAssetDataResource))
    {
        auto meshres = TYPE_CAST(resource::MeshAssetDataResource*, resource);
        cross::resource::MeshAssetLODSetting* rawLODSetting = meshres->GetRawLODSetting();
        auto& lodContent = rawLODSetting->mLevelSettings;
        for (int lodIndex = 0; lodIndex < lodContent.size(); ++lodIndex)
        {
            if (lodIndex >= mesh.LodGroups.size())
            {
                break;
            }
            auto& lodItem = lodContent[lodIndex];
            lodItem.mFadeTransitionWidth = mesh.LodGroups[lodIndex].FadeTrasition;
            lodItem.mScreenReleativeTransitionHeight = mesh.LodGroups[lodIndex].ScreenHeight;
        }
        rawLODSetting->mCulledHeight = mesh.CulledHeight;
        meshres->SetMeshAssetStreamable(mesh.IsStreamable);

        GenerateMeshClusters(resource, mesh);
    }
}

#if CROSSENGINE_WIN
template<typename IndexType>
void GenerateMeshClustersImp(const DirectX::XMFLOAT3* position, UInt32 vertexCount, IndexType* indices, UInt32 indexCount, MeshAssetData* meshAsset)
{
    
}
#endif

void GenerateMeshClusters(cross::Resource* resource, const MeshAssetDataInfo& mesh) 
{
#if CROSSENGINE_WIN
    
#endif
}

void UpdateMeshTriangleRate(cross::Resource* resource, const MeshAssetDataInfo& mesh)
{
    if (resource->GetClassID() == ClassID(MeshAssetDataResource))
    {
          //auto meshres = TYPE_CAST(resource::MeshAssetDataResource*, resource);
        //auto* simplify = meshres->GetSimplify();
        //simplify->triangleRate = static_cast<int>(mesh.Simplification.TriangleRate);
    }
}

cross::Resource* GetTexture2DMipmap(cross::Resource* resource, uint32_t mipmap, const std::string& NdaAbsoPath)
{
#if CROSSENGINE_WIN
    std::string assetPath = const_cast<std::string&>(NdaAbsoPath);
    if (resource->GetClassID() == ClassID(Texture2D))
    {
        auto textures = TYPE_CAST(resource::Texture*, resource);
        TextureResourceInfo info = textures->GetTextureData()->mResourceInfo;
        std::vector<CrossSchema::TextureAssetImage> images = textures->GetTextureData()->mImages;
        std::vector<UInt8> data = textures->GetTextureData()->mData;
        CrossSchema::TextureAssetT texAssetT = textures->GetTextureData()->mTexAssetT;

        uint32_t mip_pitch = 0;
        uint32_t mip_width = 0;
        uint32_t mip_height = 0;
        uint32_t mipMapSize = 0;

        if (info.Format == TextureFormat::RGBA32)
        {
            // generate Image
            cmft::Image base_image;
            cmft::imageCreate(base_image, images[0].width(), images[0].height(), 0x303030ff, 1, 1, cmft::TextureFormat::RGBA8);
            base_image.m_dataSize = images[0].databytesize();
            base_image.m_format = cmft::TextureFormat::RGBA8;   // corresponding CE RGBA32
            memcpy(base_image.m_data, data.data() + images[0].dataoffset(), images[0].databytesize());
            cmft::imageGenerateMipMapChain(base_image);

            uint32_t offsets[CUBE_FACE_NUM][MAX_MIP_NUM];
            cmft::imageGetMipOffsets(offsets, base_image);
            const uint32_t bytes_per_pixel = cmft::getImageDataInfo(base_image.m_format).m_bytesPerPixel;

            std::vector<CrossSchema::TextureAssetImage> texImages;

            uint32_t currentMipmap = (mipmap < static_cast<uint32_t>(base_image.m_numMips - 1) ? mipmap : (base_image.m_numMips - 2));
            mipMapSize = offsets[0][currentMipmap + 1] - offsets[0][currentMipmap];
            texAssetT.data.resize(mipMapSize);
            memcpy(texAssetT.data.data(), (const uint8_t*)base_image.m_data + offsets[0][currentMipmap], mipMapSize);
            using namespace std;
            const uint32_t mip_size = std::max(UINT32_C(1), base_image.m_width >> currentMipmap);
            mip_pitch = mip_size * bytes_per_pixel;
            mip_width = std::max(UINT32_C(1), base_image.m_width >> currentMipmap);
            mip_height = std::max(UINT32_C(1), base_image.m_height >> currentMipmap);
        }
        else if (info.Format == TextureFormat::BC3 || info.Format == TextureFormat::BC6H || info.Format == TextureFormat::BC7)
        {
            if (mipmap > images.size())
            {
                mipmap = 0;
            }
            texAssetT.data.resize(images[mipmap].databytesize());
            memcpy(texAssetT.data.data(), (const uint8_t*)data.data() + images[mipmap].dataoffset(), images[mipmap].databytesize());
            mip_height = images[mipmap].height();
            mip_width = images[mipmap].width();
            mip_pitch = images[mipmap].rowpitch();
            mipMapSize = images[mipmap].databytesize();
        }
        else
        {}
        // generate new Resource
        TextureInfo textureInfo;
        textureInfo.Dimension = info.Dimension;
        textureInfo.ColorSpace = info.ColorSpace;
        textureInfo.Format = info.Format;
        textureInfo.Depth = info.Depth;
        textureInfo.Height = mip_height;
        textureInfo.Width = mip_width;
        textureInfo.MipCount = 1;
        textureInfo.MinMips = 1;
        textureInfo.MaxMips = 1;
        textureInfo.EnableStreaming = info.EnableStreaming;
        textureInfo.RequestedMips = false;
        textureInfo.ArraySize = info.ArraySize;

        resource::Texture* newTexture = new resource::Texture(textureInfo);
        std::vector<UInt8> mData = newTexture->GetTextureData()->mData;
        newTexture->GetTextureData()->mData.resize(texAssetT.data.size());
        memcpy(newTexture->GetTextureData()->mData.data(), texAssetT.data.data(), texAssetT.data.size());
        newTexture->GetTextureData()->mImages.resize(1);
        CrossSchema::TextureAssetImage newAssetImage;
        newAssetImage.mutate_width(mip_width);
        newAssetImage.mutate_height(mip_height);
        newAssetImage.mutate_depth(1);
        newAssetImage.mutate_rowpitch(mip_pitch);
        newAssetImage.mutate_dataoffset(0);
        newAssetImage.mutate_databytesize(mipMapSize);
        newTexture->GetTextureData()->mImages.push_back(newAssetImage);

        cross::Resource* test = TYPE_CAST(cross::Resource*, ResourceBase::Produce(resource->GetClassID()));

        auto testT = TYPE_CAST(resource::Texture*, test);
        testT->CreateAsset(NdaAbsoPath);
        testT->SetWidth(mip_width);
        testT->SetHeight(mip_height);
        testT->SetDepth(info.Depth);
        testT->SetTextureInfo(textureInfo);

        cross::resource::TextureResourceDataProvider* textureSource = new cross::resource::TextureResourceDataProvider(NdaAbsoPath.c_str(), {} , 0);
        textureSource->mData.resize(texAssetT.data.size());
        memcpy(textureSource->mData.data(), texAssetT.data.data(), texAssetT.data.size());
        textureSource->mImages.clear();
        textureSource->mImages.push_back(newAssetImage);

        textureSource->mResourceInfo.Width = mip_width;
        textureSource->mResourceInfo.Height = mip_height;
        textureSource->mResourceInfo.Depth = info.Depth;
        textureSource->mResourceInfo.Dimension = info.Dimension;
        textureSource->mResourceInfo.ColorSpace = info.ColorSpace;
        textureSource->mResourceInfo.Format = info.Format;
        textureSource->mResourceInfo.ArraySize = info.ArraySize;
        textureSource->mResourceInfo.EnableStreaming = info.EnableStreaming;
        textureSource->mResourceInfo.MipCount = 1;

        testT->SetTextureData(textureSource);
        testT->CreateGPUResource();

        return testT;
    }
    return new Resource();
#else
    Assert(0);
    return nullptr;
#endif
}
cross::Resource* GetTexture2DRGBA(cross::Resource* resource, uint32_t index, const std::string& NdaAbsoPath)
{
#if CROSSENGINE_WIN
    std::string assetPath = const_cast<std::string&>(NdaAbsoPath);
    if (resource->GetClassID() == ClassID(Texture2D))
    {
        int channel_1 = 0;
        int channel_2 = 0;
        std::string display_channel("");
        switch (static_cast<int>(index))
        {
        case 0:
            channel_1 = 0;
            channel_2 = 3;
            display_channel = "R";
            break;
        case 1:
            channel_1 = 1;
            channel_2 = 3;
            display_channel = "G";
            break;
        case 2:
            channel_1 = 2;
            channel_2 = 3;
            display_channel = "B";
            break;
        case 3:
            channel_2 = 3;
            display_channel = "A";
            break;
        default:
            break;
        }

        auto textures = TYPE_CAST(resource::Texture*, resource);
        TextureResourceInfo info = textures->GetTextureData()->mResourceInfo;
        std::vector<CrossSchema::TextureAssetImage> images = textures->GetTextureData()->mImages;
        std::vector<UInt8> data = textures->GetTextureData()->mData;
        CrossSchema::TextureAssetT texAssetT = textures->GetTextureData()->mTexAssetT;

        std::vector<UINT8> Rdata(images[0].databytesize());
        if (static_cast<int>(index) == 3)
        {
            for (size_t i = 0; i < images[0].databytesize(); i += 4)
            {
                Rdata[i + 0] = 255;
                Rdata[i + 1] = 255;
                Rdata[i + 2] = 255;
                Rdata[i + channel_2] = data[i + channel_2];
            }
        }
        else
        {
            for (size_t i = 0; i < images[0].databytesize(); i += 4)
            {
                Rdata[i + channel_1] = data[i + channel_1];
                Rdata[i + channel_2] = data[i + channel_2];
            }
        }
        texAssetT.data.resize(images[0].databytesize());
        memcpy(texAssetT.data.data(), (const uint8_t*)Rdata.data(), images[0].databytesize());

        // generate new Resource
        TextureInfo textureInfo;
        textureInfo.Dimension = info.Dimension;
        textureInfo.ColorSpace = info.ColorSpace;
        textureInfo.Format = info.Format;
        textureInfo.Depth = info.Depth;
        textureInfo.Height = images[0].width();
        textureInfo.Width = images[0].height();
        textureInfo.MipCount = 1;
        textureInfo.MinMips = 1;
        textureInfo.MaxMips = 1;
        textureInfo.EnableStreaming = info.EnableStreaming;
        textureInfo.RequestedMips = false;
        textureInfo.ArraySize = info.ArraySize;

        resource::Texture* newTexture = new resource::Texture(textureInfo);
        std::vector<UInt8> mData = newTexture->GetTextureData()->mData;
        newTexture->GetTextureData()->mData.resize(texAssetT.data.size());
        memcpy(newTexture->GetTextureData()->mData.data(), texAssetT.data.data(), texAssetT.data.size());
        newTexture->GetTextureData()->mImages.resize(1);
        CrossSchema::TextureAssetImage newAssetImage;
        newAssetImage.mutate_width(images[0].width());
        newAssetImage.mutate_height(images[0].height());
        newAssetImage.mutate_depth(1);
        newAssetImage.mutate_rowpitch(images[0].rowpitch());
        newAssetImage.mutate_dataoffset(0);
        newAssetImage.mutate_databytesize(images[0].databytesize());
        newTexture->GetTextureData()->mImages.push_back(newAssetImage);

        cross::Resource* test = TYPE_CAST(cross::Resource*, ResourceBase::Produce(resource->GetClassID()));

        auto testT = TYPE_CAST(resource::Texture*, test);
        testT->CreateAsset(NdaAbsoPath);
        testT->SetWidth(images[0].width());
        testT->SetHeight(images[0].height());
        testT->SetDepth(info.Depth);
        testT->SetTextureInfo(textureInfo);

        cross::resource::TextureResourceDataProvider* textureSource = new cross::resource::TextureResourceDataProvider(NdaAbsoPath.c_str(), {} , 0);
        textureSource->mData.resize(texAssetT.data.size());
        memcpy(textureSource->mData.data(), texAssetT.data.data(), texAssetT.data.size());
        textureSource->mImages.clear();
        textureSource->mImages.push_back(newAssetImage);

        textureSource->mResourceInfo.Width = images[0].width();
        textureSource->mResourceInfo.Height = images[0].height();
        textureSource->mResourceInfo.Depth = info.Depth;
        textureSource->mResourceInfo.Dimension = info.Dimension;
        textureSource->mResourceInfo.ColorSpace = info.ColorSpace;
        textureSource->mResourceInfo.Format = info.Format;
        textureSource->mResourceInfo.ArraySize = info.ArraySize;
        textureSource->mResourceInfo.EnableStreaming = info.EnableStreaming;
        textureSource->mResourceInfo.MipCount = 1;

        testT->SetTextureData(textureSource);
        testT->CreateGPUResource();

        return testT;
    }
    return new Resource();
#else
    Assert(0);
    return nullptr;
#endif
}
cross::Resource* GetTexture2DZoomIn(cross::Resource* resource, uint32_t width, uint32_t height, uint32_t x, uint32_t y, const std::string& NdaAbsoPath)
{
#if CROSSENGINE_WIN
    std::string assetPath = const_cast<std::string&>(NdaAbsoPath);
    if (resource->GetClassID() == ClassID(Texture2D))
    {
        auto textures = TYPE_CAST(resource::Texture*, resource);
        TextureResourceInfo info = textures->GetTextureData()->mResourceInfo;
        std::vector<CrossSchema::TextureAssetImage> images = textures->GetTextureData()->mImages;
        std::vector<UInt8> data = textures->GetTextureData()->mData;
        CrossSchema::TextureAssetT texAssetT = textures->GetTextureData()->mTexAssetT;

        UINT32 index = 128;
        std::vector<UINT8> Rdata(index * index * 4);
        x = x == 0 ? 1 : x;
        y = y == 0 ? 1 : y;
        for (size_t i = 0; i < height; i += 1)
        {
            memcpy(&Rdata[i * index * 4], &data[((y + i - 1) * info.Width * 4) + ((x - 1) * 4)], width * 4);
        }

        texAssetT.data.resize(index * index * 4);
        memcpy(texAssetT.data.data(), (const uint8_t*)Rdata.data(), index * index * 4);

        // generate new Resource
        TextureInfo textureInfo;
        textureInfo.Dimension = info.Dimension;
        textureInfo.ColorSpace = info.ColorSpace;
        textureInfo.Format = info.Format;
        textureInfo.Depth = info.Depth;
        textureInfo.Height = index;
        textureInfo.Width = index;
        textureInfo.MipCount = 1;
        textureInfo.MinMips = 1;
        textureInfo.MaxMips = 1;
        textureInfo.EnableStreaming = info.EnableStreaming;
        textureInfo.RequestedMips = false;
        textureInfo.ArraySize = info.ArraySize;

        resource::Texture* newTexture = new resource::Texture(textureInfo);
        std::vector<UInt8> mData = newTexture->GetTextureData()->mData;
        newTexture->GetTextureData()->mData.resize(texAssetT.data.size());
        memcpy(newTexture->GetTextureData()->mData.data(), texAssetT.data.data(), texAssetT.data.size());
        newTexture->GetTextureData()->mImages.resize(1);
        CrossSchema::TextureAssetImage newAssetImage;
        newAssetImage.mutate_width(index);
        newAssetImage.mutate_height(index);
        newAssetImage.mutate_depth(1);
        newAssetImage.mutate_rowpitch(((index - 1) / 1 + 1) * 4);
        newAssetImage.mutate_dataoffset(0);
        newAssetImage.mutate_databytesize(index * index * 4);
        newTexture->GetTextureData()->mImages.push_back(newAssetImage);

        cross::Resource* test = TYPE_CAST(cross::Resource*, ResourceBase::Produce(resource->GetClassID()));

        auto testT = TYPE_CAST(resource::Texture*, test);
        testT->CreateAsset(NdaAbsoPath);
        testT->SetWidth(images[0].width());
        testT->SetHeight(images[0].height());
        testT->SetDepth(info.Depth);
        testT->SetTextureInfo(textureInfo);

        cross::resource::TextureResourceDataProvider* textureSource = new cross::resource::TextureResourceDataProvider(NdaAbsoPath.c_str(), {} , 0);
        textureSource->mData.resize(texAssetT.data.size());
        memcpy(textureSource->mData.data(), texAssetT.data.data(), texAssetT.data.size());
        textureSource->mImages.clear();
        textureSource->mImages.push_back(newAssetImage);

        textureSource->mResourceInfo.Width = index;
        textureSource->mResourceInfo.Height = index;
        textureSource->mResourceInfo.Depth = info.Depth;
        textureSource->mResourceInfo.Dimension = info.Dimension;
        textureSource->mResourceInfo.ColorSpace = info.ColorSpace;
        textureSource->mResourceInfo.Format = info.Format;
        textureSource->mResourceInfo.ArraySize = info.ArraySize;
        textureSource->mResourceInfo.EnableStreaming = info.EnableStreaming;
        textureSource->mResourceInfo.MipCount = 1;

        testT->SetTextureData(textureSource);
        testT->CreateGPUResource();

        return testT;
    }
    return new Resource();
#else
    Assert(0);
    return nullptr;
#endif
}

std::vector<uint32_t> GetTexture2DRGBAValue(cross::Resource* resource, uint32_t x, uint32_t y, const std::string& NdaAbsoPath)
{
    std::string assetPath = const_cast<std::string&>(NdaAbsoPath);
    if (resource->GetClassID() == ClassID(Texture2D))
    {
        auto textures = TYPE_CAST(resource::Texture*, resource);
        TextureResourceInfo info = textures->GetTextureData()->mResourceInfo;
        std::vector<CrossSchema::TextureAssetImage> images = textures->GetTextureData()->mImages;
        std::vector<UInt8> data = textures->GetTextureData()->mData;
        CrossSchema::TextureAssetT texAssetT = textures->GetTextureData()->mTexAssetT;
        std::vector<uint32_t> rgb_value;
        rgb_value.push_back(data[((y - 1) * info.Width * 4) + ((x - 1) * 4)]);
        rgb_value.push_back(data[((y - 1) * info.Width * 4) + ((x - 1) * 4 + 1)]);
        rgb_value.push_back(data[((y - 1) * info.Width * 4) + ((x - 1) * 4 + 2)]);
        return rgb_value;
    }
    return std::vector<uint32_t>(3);
}

resource::MaterialParameterInfo GetMaterialParameterInfo(cross::Resource* resource)
{
    resource::MaterialParameterInfo result;
    if (resource->GetClassID() == ClassID(MaterialParameterCollection))
    {
        if (auto mpc = TYPE_CAST(resource::MaterialParameterCollection*, resource))
        {
            result = mpc->GetParameterInfo();
        }
    }
    return result;
}

resource::ParameterCollectionUsages GetMaterialParameterUsages(cross::Resource* resource)
{
    if (resource->GetClassID() == ClassID(Material))
    {
        if (auto material = TYPE_CAST(resource::Material*, resource))
        {
            return material->GetAllParameterUsages();
        }
    }
    return resource::ParameterCollectionUsages();
}

void AddParameterUsage(cross::Resource* resource, const std::string& item)
{
    if (resource->GetClassID() != ClassID(Material))
    {
        return;
    }
    if (auto material = TYPE_CAST(resource::Material*, resource))
    {
        material->AddParameterUsage(item);
    }
}

void DeleteParameterUsage(cross::Resource* resource, UInt32 index)
{
    if (resource->GetClassID() != ClassID(Material))
    {
        return;
    }
    if (auto material = TYPE_CAST(resource::Material*, resource))
    {
        material->DeleteParameterUsage(index);
    }
}

void ClearParameterUsage(cross::Resource* resource) 
{
    if (resource->GetClassID() != ClassID(Material))
    {
        return;
    }
    if (auto material = TYPE_CAST(resource::Material*, resource))
    {
        material->ClearParameterUsage();
    }
}

void SetParameterUsage(cross::Resource* resource, const std::vector<std::string>& usages)
{
    if (resource->GetClassID() != ClassID(Material))
    {
        return;
    }
    if (auto material = TYPE_CAST(resource::Material*, resource))
    {
        material->SetAllParameterUsages(usages);
    }
}

}   // namespace cross
