#pragma once

// Include
#include "EnginePrefix.h"
#include "ECS/Develop/Framework/Types.h"
#include "Resource/Resource.h"
#include "Resource/Prefab/PrefabData.h"
#include "CEMetaMacros.h"
namespace cross::resource {
class Resource_API CEMeta(Script) PrefabResource : public Resource
{
public:
    //@brief GetClassIDStatic
    static int GetClassIDStatic()
    {
        return ClassID(PrefabResource);
    }
    //@brief Constructor
    PrefabResource() {};
    //@brief Constructor
    PrefabResource(std::string path){};
    //@brief Destructor
    virtual ~PrefabResource();

public:
    //@brief GetRefCount
    virtual int32_t GetRefCount();
    //@brief GetRefCount
    virtual int32_t GetRefCount() const;
    //@brief Serialize
    bool Serialize();
    //@brief Serialize
    SerializeNode Serialize(PrefabDataPtr data);
    //@brief Serialize
    bool Serialize(SerializeNode&& s, const std::string& path) override;
    //@brief Deserialize
    bool Deserialize(DeserializeNode const& s) override;
    //@brief PostDeserialize
    bool PostDeserialize() override;
    //@brief GetPrefabData
    PrefabDataPtr GetPrefabData() { return mPrefabData; }
    //@brief GetJsonData
    SerializeNode GetJsonData();
    //@brief RebuildPrefabDepend
    bool RebuildPrefabDepend();
public:
    static bool RebuildPrefabDepend(const std::string& prefabId);
    static bool RebuildPrefab(PrefabDataPtr data, PrefabEntityDataPtr entityData);
    static bool RebuildInheritPrefab(PrefabDataPtr data, PrefabEntityDataPtr entityData);
    static bool RebuildInheritPrefabRecursive(PrefabDataPtr data, PrefabEntityDataPtr entityData);
    static PrefabResourcePtr GetPrefabResource(const std::string& prefabId);
    static PrefabDataPtr GetPrefabData(const std::string& prefabId);
    static PrefabEntityDataPtr GetPrefabEntityData(const std::string& prefabId, CrossUUID prefabEuid);
    static bool CheckPrefabChild(const std::string& prefabId, CrossUUID prefabEuid, CrossUUID checkEuid);
    static bool CheckPrefabEntityData(PrefabEntityDataPtr entityData);

protected:
    void SerializeEntity(SerializeNode& entitiesJson, PrefabDataPtr data, CrossUUID euid);
    
    PrefabDataPtr               mPrefabData = new PrefabData();
    bool                        mIsReload = false;
};
}   // namespace cross::resource
