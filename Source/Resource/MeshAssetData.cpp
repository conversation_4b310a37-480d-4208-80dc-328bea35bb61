
#include "EnginePrefix.h"
#include "MeshAssetData.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/SettingsManager.h"
#include "CECommon/Utilities/NDACommon.h"
#include "CrossBase/Math/CrossMath.h"
#include "CrossBase/Math/QTangents.h"
#include "ImportMesh_generated.h"
#include "ImportMeshAssetData_generated.h"
#include "ResourceAsset_generated.h"
#include "ImportSkeleton_generated.h"
#include "ImportAnimation_generated.h"
#include "CECommon/Common/EngineGlobal.h"
#include "PhysicsEngine/PhysicsEngine.h"
#include "PhysicsEngine/PhysicsCooker.h"
#include <queue>

namespace cross
{
//////////////////////////////////////////////////////////////////////////
// BlendShapeInfo
//
//////////////////////////////////////////////////////////////////////////
bool BlendShapeInfo::Deserialize(const CrossSchema::ImportBlendShapeInfo& blendShape)
{
    VertexChannelSemanticMask = blendShape.fvertexchannelsemanticmask();

    // Blend shape vertex channel data
    const auto vertexChannelNum = static_cast<UInt32>(blendShape.fvertexchanneldata()->size());
    for (UInt32 channelIdx = 0; channelIdx < vertexChannelNum; ++channelIdx)
    {
        const auto fbVertexChannelData = (*blendShape.fvertexchanneldata())[channelIdx];
        if (!fbVertexChannelData->fdata())
            continue;

        VertexChannelAssetData& vertexChannel = VertexChannelData.emplace_back();

        vertexChannel.mStride = fbVertexChannelData->fstride();
        vertexChannel.mVertexChannel = static_cast<VertexChannel>(fbVertexChannelData->fvertexchannel());
        vertexChannel.mDataFormat = static_cast<VertexFormat>(fbVertexChannelData->fdataformat());
        vertexChannel.mData.resize(fbVertexChannelData->fdata()->size());
        std::copy(fbVertexChannelData->fdata()->begin(), fbVertexChannelData->fdata()->end(), vertexChannel.mData.begin());
    }

    // Blend shape channel data
    const auto blendShapeChannelNum = static_cast<UInt32>(blendShape.fchannelinfos()->size());
    ChannelShapeData.resize(blendShapeChannelNum);
    for (UInt32 channelIdx = 0; channelIdx < blendShapeChannelNum; ++channelIdx)
    {
        const auto fbBlendShapeChannel = (*blendShape.fchannelinfos())[channelIdx];
        auto& curChannel = ChannelShapeData[channelIdx];

        HashString channelName = HashString(fbBlendShapeChannel->fname()->c_str());
        ChannelNameToIndexMap.insert({channelName, channelIdx});
        ChannelNameData.emplace_back(channelName);

        const auto shapeNum = static_cast<UInt32>(fbBlendShapeChannel->fnormalizedfullweights()->size());
        curChannel.NormalizedFullWeights.resize(shapeNum);
        std::copy(fbBlendShapeChannel->fnormalizedfullweights()->begin(), fbBlendShapeChannel->fnormalizedfullweights()->end(), curChannel.NormalizedFullWeights.begin());

        curChannel.DeltaShapes.resize(shapeNum);
        for (UInt32 shapeIdx = 0; shapeIdx < shapeNum; ++shapeIdx)
        {
            curChannel.DeltaShapes[shapeIdx].mVertexStart = (*fbBlendShapeChannel->fdeltashapes())[shapeIdx]->fvertexstart();
            curChannel.DeltaShapes[shapeIdx].mVertexCount = (*fbBlendShapeChannel->fdeltashapes())[shapeIdx]->fvertexcount();
        }
    }

    return true;
}


//////////////////////////////////////////////////////////////////////////
// MeshAssetData
//
//////////////////////////////////////////////////////////////////////////

MeshAssetData::MeshAssetData()
{
    mRenderMesh = gResourceMgr.mCreateRenderObjectMgr->GetMeshR(this);
}


MeshAssetData::MeshAssetData(bool is16BitIndex)
{
    mIndexStream.mIs16BitIndex = is16BitIndex;
    mRenderMesh = gResourceMgr.mCreateRenderObjectMgr->GetMeshR(this);
}

MeshAssetData::GetRawVertexAndIndexDataOutput MeshAssetData::GetRawVertexAndIndexData(UInt32 lod, VertexChannel dataChannel) const
{
    GetRawVertexAndIndexDataOutput output;
    UInt32 lodCount = GetLodCount();
    Assert(lodCount > 0 && lod < lodCount);

    const VertexChannelAssetData* vertexChannel = GetVertexChannelData(dataChannel);
    const IndexStreamAssetData& indexStream = GetIndexStream();

    UInt32 lodMeshPartStartIndex = 0;
    UInt32 lodMeshCount = 0;
    GetMeshLodInfo(lod, lodMeshPartStartIndex, lodMeshCount);
    Assert(lodMeshCount > 0);

    UInt32 vertexStart = GetMeshPartInfo(lodMeshPartStartIndex).mVertexStart;
    output.vertexData = vertexChannel->mData.data() + vertexStart * vertexChannel->mStride;
    output.vertexCount = 0;
    output.vertexStride = vertexChannel->mStride;
    output.vertexDataFormat = vertexChannel->mDataFormat;

    UInt32 indexStart = GetMeshPartInfo(lodMeshPartStartIndex).mIndexStart;
    output.indexData = indexStream.mData.data() + indexStart * (indexStream.mIs16BitIndex ? 2 : 4);
    output.indexCount = 0;
    output.indexStride = indexStream.mIs16BitIndex ? 2 : 4;
    output.is16BitIndex = indexStream.mIs16BitIndex;

    for (UInt32 meshPart = lodMeshPartStartIndex; meshPart < lodMeshPartStartIndex + lodMeshCount; ++meshPart)
    {
        const MeshPartAssetInfo& part = GetMeshPartInfo(meshPart);
        output.vertexCount += part.mVertexCount;
        output.indexCount += part.mIndexCount;
    }
    return output;
}
MeshAssetData::GetRawVertexAndIndexDataOutput MeshAssetData::GetMeshPartData(UInt32 lod, UInt32 meshPart) const {
    GetRawVertexAndIndexDataOutput output;
    UInt32 lodCount = GetLodCount();
    Assert(lodCount > 0 && lod < lodCount);

    const VertexChannelAssetData* vertexChannel = GetVertexChannelData(VertexChannel::Position0);
    const IndexStreamAssetData& indexStream = GetIndexStream();

    UInt32 lodMeshPartStartIndex = 0;
    UInt32 lodMeshCount = 0;
    GetMeshLodInfo(lod, lodMeshPartStartIndex, lodMeshCount);
    Assert(lodMeshCount > 0);
    const MeshPartAssetInfo& meshPartInfo = GetMeshPartInfo(lodMeshPartStartIndex+meshPart);
    UInt32 vertexStart = meshPartInfo.mVertexStart;
    output.vertexData = vertexChannel->mData.data() + vertexStart * vertexChannel->mStride;
    output.vertexCount = meshPartInfo.mVertexCount;
    output.vertexStride = vertexChannel->mStride;
    output.vertexDataFormat = vertexChannel->mDataFormat;

    UInt32 indexStart = meshPartInfo.mIndexStart;
    output.indexData = indexStream.mData.data() + indexStart * (indexStream.mIs16BitIndex ? 2 : 4);
    output.indexCount = meshPartInfo.mIndexCount;
    output.indexStride = indexStream.mIs16BitIndex ? 2 : 4;
    output.is16BitIndex = indexStream.mIs16BitIndex;

    return output;
}

void MeshAssetData::GetIndexData(UInt32 lod, std::vector<UInt32>& outputIndices) const
{
    UInt32 lodMeshPartStartIndex = 0;
    UInt32 lodMeshCount = 0;
    GetMeshLodInfo(lod, lodMeshPartStartIndex, lodMeshCount);

    UInt16 indexStride = mIndexStream.mIs16BitIndex ? sizeof(UInt16) : sizeof(UInt32);
    UInt8 const* indexDataStart = mIndexStream.mData.data();

    for (UInt32 meshPart = lodMeshPartStartIndex; meshPart < lodMeshPartStartIndex + lodMeshCount; ++meshPart)
    {
        const MeshPartAssetInfo& meshPartInfo = GetMeshPartInfo(meshPart);

        for (UInt32 i = meshPartInfo.mIndexStart; i < meshPartInfo.mIndexStart + meshPartInfo.mIndexCount; i++)
        {
            if (mIndexStream.mIs16BitIndex)
            {
                outputIndices.push_back(*reinterpret_cast<const UInt16*>(indexDataStart + i * indexStride));
            }
            else
            {
                outputIndices.push_back(*reinterpret_cast<const UInt32*>(indexDataStart + i * indexStride));
            }
        }
    }
}

void UpdateBound(cross::Float3& maxBound, cross::Float3& minBound, cross::Float3 const& vertex)
{
    maxBound.x = (std::max)(vertex.x, maxBound.x);
    maxBound.y = (std::max)(vertex.y, maxBound.y);
    maxBound.z = (std::max)(vertex.z, maxBound.z);

    minBound.x = (std::min)(vertex.x, minBound.x);
    minBound.y = (std::min)(vertex.y, minBound.y);
    minBound.z = (std::min)(vertex.z, minBound.z);
}

template <class T1, class T2, class Func>
void ConvertVertexAttribute(const CrossSchema::VertexChannelAssetData * fbVertexChannelData, VertexChannelAssetData& vertexChannel, VertexFormat format, Func func)
{
    int num_elements = fbVertexChannelData->fdata()->size() / fbVertexChannelData->fstride();
    const T2* data = (const T2*)fbVertexChannelData->fdata()->Data();
    std::vector<T1> normalized_data(num_elements);
    int dims = T1::Dim();
    for (int i = 0; i < num_elements; i++)
    {
        for (int j = 0; j < dims; j++)
        {
            func(data[i].data()[j], normalized_data[i].data()[j]);
        }
    }

    vertexChannel.mDataFormat = format;
    vertexChannel.mStride = GetByteSize(vertexChannel.mDataFormat);
    vertexChannel.mData.resize(normalized_data.size() * sizeof(T1));
    std::copy(static_cast<UInt8*>(normalized_data.data()), static_cast<UInt8*>(normalized_data.data()) + normalized_data.size() * sizeof(T1), vertexChannel.mData.begin());
}

IMeshR* MeshAssetData::GetRenderMesh() const
{
    return mRenderMesh.get();
}

EditMeshLodError MeshAssetData::UpdateLodMeshData(UInt8 lod, const MeshAssetData* newMeshData)
{
    UInt32 lodCount = GetLodCount();
    // 1 check lod
    if (lod > lodCount) {
        LOG_WARN("Update lod({0}) large lodCount{1}", lod, lodCount);
        return EditMeshLodError::ERR_EDITOR_LOD_OVERFLOW;
    }
    // 2 check mesh part count
    if (GetMeshPartCount(lod) != newMeshData->GetMeshPartCount(0))
    {
        LOG_WARN("Update lod failed: mesh part count not same {} {}", GetMeshPartCount(lod), newMeshData->GetMeshPartCount(0));
        return EditMeshLodError::ERR_EDITOR_MESH_PART_COUNT;
    }

    UInt32 newVertexCount = 0;
    UInt32 newIndexCount = 0;
    UInt32 newLodMeshPartStartIndex = 0;
    UInt32 newLodMeshCount = 0;
    newMeshData->GetMeshLodInfo(0, newLodMeshPartStartIndex, newLodMeshCount);
    for (UInt32 meshPart = newLodMeshPartStartIndex; meshPart < newLodMeshPartStartIndex + newLodMeshCount; ++meshPart)
    {
        const MeshPartAssetInfo& part = newMeshData->GetMeshPartInfo(meshPart);
        newVertexCount += part.mVertexCount;
        newIndexCount += part.mIndexCount;
    }

    UInt32 oldVertexCount = 0;
    UInt32 oldIndexCount = 0;
    UInt32 oldLodMeshPartStartIndex = 0;
    UInt32 oldLodMeshCount = 0;
    GetMeshLodInfo(lod, oldLodMeshPartStartIndex, oldLodMeshCount);
    for (UInt32 meshPart = oldLodMeshPartStartIndex; meshPart < oldLodMeshPartStartIndex + oldLodMeshCount; ++meshPart)
    {
        const MeshPartAssetInfo& part = GetMeshPartInfo(meshPart);
        oldVertexCount += part.mVertexCount;
        oldIndexCount += part.mIndexCount;
    }
    mVertexCount += newVertexCount - oldVertexCount;
    // update vertex  data
    auto oldVertexStart = GetMeshPartInfo(oldLodMeshPartStartIndex).mVertexStart;
    auto newVertexStart = newMeshData->GetMeshPartInfo(newLodMeshPartStartIndex).mVertexStart;
    for (auto& oldVertexChannel : mVertexChannelData)
    {
        auto vertexStride = oldVertexChannel.mStride;
        const auto* newVertexChannel = newMeshData->GetVertexChannelData(oldVertexChannel.mVertexChannel);
        if (newVertexCount > oldVertexCount) {
            oldVertexChannel.mData.insert(oldVertexChannel.mData.begin() + (oldVertexStart + oldVertexCount) * vertexStride, (newVertexCount - oldVertexCount) * vertexStride, 0);
        }    
        else if (newVertexCount < oldVertexCount)
        {
            oldVertexChannel.mData.erase(oldVertexChannel.mData.begin() + (oldVertexStart + newVertexCount) * vertexStride, oldVertexChannel.mData.begin() + (oldVertexStart + oldVertexCount) * vertexStride);
            oldVertexChannel.mData.shrink_to_fit();
        }
        if (newVertexChannel)
        {
            auto newVertexStride = newVertexChannel->mStride;
            if (newVertexStride > vertexStride)
            {
                for (UInt32 idx = 0; idx < newVertexCount; idx++) {
                    memcpy(oldVertexChannel.mData.data() + (oldVertexStart + idx) * vertexStride, newVertexChannel->mData.data() + (newVertexStart + idx) * vertexStride + newVertexStride - vertexStride, vertexStride);
                }
            }
            else if (newVertexStride <  vertexStride)
            {
                for (UInt32 idx = 0; idx < newVertexCount; idx++)
                {
                    memset(oldVertexChannel.mData.data() + (oldVertexStart + idx) * vertexStride, 0, vertexStride - newVertexStride);
                    memcpy(oldVertexChannel.mData.data() + (oldVertexStart + idx) * vertexStride + vertexStride - newVertexStride, newVertexChannel->mData.data() + (newVertexStart + idx) * vertexStride, newVertexStride);
                }
            }
            else
            {
                memcpy(oldVertexChannel.mData.data() + oldVertexStart * vertexStride, newVertexChannel->mData.data() + newVertexStart * vertexStride, newVertexCount * vertexStride);
            }
        }
        else
        {
            memset(oldVertexChannel.mData.data() + oldVertexStart * vertexStride, 0, newVertexCount * vertexStride);
        }
    }
    // update index data
    auto oldIndexStart = GetMeshPartInfo(oldLodMeshPartStartIndex).mIndexStart;
    auto& oldIndexSream = GetIndexStream();
    auto newIndexStart = newMeshData->GetMeshPartInfo(newLodMeshPartStartIndex).mIndexStart;
    const auto& newIndexStream = newMeshData->GetIndexStream();
    auto indexStride = oldIndexSream.mIs16BitIndex ? 2 : 4;
    if (newIndexCount > oldIndexCount)
    {
        oldIndexSream.mData.insert(oldIndexSream.mData.begin() + (oldIndexStart + oldIndexCount) * indexStride, (newIndexCount - oldIndexCount) * indexStride, 0);
    }
    else if (newIndexCount < oldIndexCount)
    {
        oldIndexSream.mData.erase(oldIndexSream.mData.begin() + (oldIndexStart + newIndexCount) * indexStride, oldIndexSream.mData.begin() + (oldIndexStart + oldIndexCount) * indexStride);
        oldIndexSream.mData.shrink_to_fit();
    }
    auto newIndexStride = newIndexStream.mIs16BitIndex ? 2 : 4;
    if (newIndexStride > indexStride)
    {
        for (UInt32 idx = 0; idx < newIndexCount; idx++)
        {
            memcpy(oldIndexSream.mData.data() + (oldIndexStart + idx) * indexStride, newIndexStream.mData.data() + (newIndexStart + idx) * indexStride + newIndexStride - indexStride, indexStride);
        }
    }
    else if (newIndexStride < indexStride)
    {
        for (UInt32 idx = 0; idx < newIndexCount; idx++)
        {
            memset(oldIndexSream.mData.data() + (oldIndexStart + idx) * indexStride, 0, indexStride - newIndexStride);
            memcpy(oldIndexSream.mData.data() + (oldIndexStart + idx) * indexStride + indexStride - newIndexStride, newIndexStream.mData.data() + (newIndexStart + idx) * indexStride, newIndexStride);
        }
    }
    else
    {
        memcpy(oldIndexSream.mData.data() + oldIndexStart * indexStride, newIndexStream.mData.data() + newIndexStart * indexStride, newIndexCount * indexStride);
    }
    oldIndexSream.mCount += newIndexCount - oldIndexCount;
    // update mesh part data
    UInt32 tmpVertexCount = 0;
    UInt32 tmpIndexCount = 0;
    UInt32 oldPrimitiveCount = 0;
    UInt32 newPrimitiveCount = 0;
    UInt32 minLodMeshCount = std::min(newLodMeshCount, oldLodMeshCount);
    for (UInt32 index = 0; index < minLodMeshCount; ++index)
    {
        MeshPartAssetInfo& oldMeshPart = mMeshPartInfo[oldLodMeshPartStartIndex + index];
        const MeshPartAssetInfo&  newMeshPart = newMeshData->GetMeshPartInfo(newLodMeshPartStartIndex + index);
        oldPrimitiveCount += oldMeshPart.mPrimitiveCount;
        oldMeshPart.mVertexStart = oldVertexStart + tmpVertexCount;
        oldMeshPart.mVertexCount = newMeshPart.mVertexCount;
        oldMeshPart.mIndexStart = oldIndexStart + tmpIndexCount;
        oldMeshPart.mIndexCount = newMeshPart.mIndexCount;
        oldMeshPart.mPrimitiveCount = newMeshPart.mPrimitiveCount;
        oldMeshPart.mMeshBound = newMeshPart.mMeshBound;
        oldMeshPart.mMiscFlag = newMeshPart.mMiscFlag;
        oldMeshPart.mShadowBias = newMeshPart.mShadowBias;
        oldMeshPart.mShadowNormalBias = newMeshPart.mShadowNormalBias;
        oldMeshPart.mPrimitiveType = newMeshPart.mPrimitiveType;
        oldMeshPart.mCollisionTree = newMeshPart.mCollisionTree;
        oldMeshPart.mBlendShape = newMeshPart.mBlendShape;

        tmpVertexCount += newMeshPart.mVertexCount;
        tmpIndexCount += newMeshPart.mIndexCount;
        newPrimitiveCount += newMeshPart.mPrimitiveCount;
    }
    if (newLodMeshCount > oldLodMeshCount)
    {
        for (UInt32 index = 0; index < newLodMeshCount - oldLodMeshCount; index++)
        {
            const auto& newMeshPart = newMeshData->GetMeshPartInfo(newLodMeshPartStartIndex + oldLodMeshCount + index);
            mMeshPartInfo.insert(mMeshPartInfo.begin() + oldLodMeshCount + index, newMeshPart);
            mMeshPartInfo[oldLodMeshCount + index].mVertexStart = oldVertexStart + tmpVertexCount;
            mMeshPartInfo[oldLodMeshCount + index].mIndexStart = oldIndexStart + tmpIndexCount;
            tmpVertexCount += newMeshPart.mVertexCount;
            tmpIndexCount += newMeshPart.mIndexCount;
            newPrimitiveCount += newMeshPart.mPrimitiveCount;
        }
    }
    else if (newLodMeshCount < oldLodMeshCount)
    {
        for (UInt32 index = minLodMeshCount; index < oldLodMeshCount; index ++)
        {
            oldPrimitiveCount += mMeshPartInfo[oldLodMeshPartStartIndex + index].mPrimitiveCount;
        }
        mMeshPartInfo.erase(mMeshPartInfo.begin() + oldLodMeshPartStartIndex + newLodMeshCount, mMeshPartInfo.begin() + oldLodMeshPartStartIndex + oldLodMeshCount);
    }
    for (UInt32 index = oldLodMeshPartStartIndex + newLodMeshCount; index < mMeshPartInfo.size(); index++)
    {
        mMeshPartInfo[index].mVertexStart   = oldVertexStart + tmpVertexCount;
        mMeshPartInfo[index].mIndexStart    = oldIndexStart + tmpIndexCount;
        tmpVertexCount  += mMeshPartInfo[index].mVertexCount;
        tmpIndexCount   += mMeshPartInfo[index].mIndexCount;
    }    
    mPrimitiveCount += newPrimitiveCount - oldPrimitiveCount;
    // update lod mesh part index
    UInt32 lodMeshPartCountInc = newLodMeshCount - oldLodMeshCount;
    for (UInt32 index = lod + 1; index < lodCount; index++)
    {
        mMeshPartLodStartIndex[index] += lodMeshPartCountInc;
    }
    return EditMeshLodError::ERR_EDITOR_SUCCESS;
}

EditMeshLodError MeshAssetData::AddLodMeshData(const MeshAssetData* newMeshData)
{
    // 1 check mesh part count
    //auto lodCount = GetLodCount();
    //if (GetMeshPartCount(lodCount - 1) != newMeshData->GetMeshPartCount(0))
    //{
    //    LOG_WARN("Add lod failed: mesh part count not same {} {}", GetMeshPartCount(lodCount - 1), newMeshData->GetMeshPartCount(0));
    //    return EditMeshLodError::ERR_EDITOR_MESH_PART_COUNT;
    //}
    // prepare
    UInt32 newVertexCount = 0;
    UInt32 newIndexCount = 0;
    UInt32 newLodMeshPartStartIndex = 0;
    UInt32 newLodMeshCount = 0;
    newMeshData->GetMeshLodInfo(0, newLodMeshPartStartIndex, newLodMeshCount);
    for (UInt32 meshPart = newLodMeshPartStartIndex; meshPart < newLodMeshPartStartIndex + newLodMeshCount; ++meshPart)
    {
        const MeshPartAssetInfo& part = newMeshData->GetMeshPartInfo(meshPart);
        newVertexCount += part.mVertexCount;
        newIndexCount += part.mIndexCount;
    }
    // add vertex data
    UInt32 oldLodMeshPartCount = static_cast<UInt32>(mMeshPartInfo.size());
    UInt32 oldVertexDataCount = mMeshPartInfo[oldLodMeshPartCount - 1].mVertexStart + mMeshPartInfo[oldLodMeshPartCount - 1].mVertexCount;
    UInt32 oldVertexStart = oldVertexDataCount;
    UInt32 oldIndexDataCount = mMeshPartInfo[oldLodMeshPartCount - 1].mIndexStart + mMeshPartInfo[oldLodMeshPartCount - 1].mIndexCount;
    auto newVertexStart = newMeshData->GetMeshPartInfo(newLodMeshPartStartIndex).mVertexStart;
    for (auto& oldVertexChannel : mVertexChannelData)
    {
        auto vertexStride = oldVertexChannel.mStride;
        const auto* newVertexChannel = newMeshData->GetVertexChannelData(oldVertexChannel.mVertexChannel);
        //oldVertexChannel.mData.resize(oldVertexChannel.mData.size() + newVertexCount * vertexStride);
        oldVertexChannel.mData.resize((oldVertexDataCount + newVertexCount) * vertexStride);
        if (newVertexChannel)
        {
            auto newVertexStride = newVertexChannel->mStride;
            if (newVertexStride > vertexStride)
            {
                for (UInt32 idx = 0; idx < newVertexCount; idx++)
                {
                    memcpy(oldVertexChannel.mData.data() + (oldVertexStart + idx) * vertexStride, newVertexChannel->mData.data() + (newVertexStart + idx) * vertexStride + newVertexStride - vertexStride, vertexStride);
                }
            }
            else if (newVertexStride < vertexStride)
            {
                for (UInt32 idx = 0; idx < newVertexCount; idx++)
                {
                    memset(oldVertexChannel.mData.data() + (oldVertexStart + idx) * vertexStride, 0, vertexStride - newVertexStride);
                    memcpy(oldVertexChannel.mData.data() + (oldVertexStart + idx) * vertexStride + vertexStride - newVertexStride, newVertexChannel->mData.data() + (newVertexStart + idx) * vertexStride, newVertexStride);
                }
            }
            else
            {
                memcpy(oldVertexChannel.mData.data() + oldVertexStart * vertexStride, newVertexChannel->mData.data() + newVertexStart * vertexStride, newVertexCount * vertexStride);
            }
        }
        else
        {
            memset(oldVertexChannel.mData.data() + oldVertexStart * vertexStride, 0, newVertexCount * vertexStride);
        }
    }
    // add index data
    auto& oldIndexSream = GetIndexStream();
    UInt32 oldIndexCount = mMeshPartInfo[oldLodMeshPartCount - 1].mIndexStart + mMeshPartInfo[oldLodMeshPartCount - 1].mIndexCount;
    auto oldIndexStart = oldIndexCount; // oldIndexSream.mCount;
    auto indexStride = oldIndexSream.mIs16BitIndex ? 2 : 4;

    auto newIndexStart = newMeshData->GetMeshPartInfo(newLodMeshPartStartIndex).mIndexStart;
    const auto& newIndexStream = newMeshData->GetIndexStream();
    auto newIndexStride = newIndexStream.mIs16BitIndex ? 2 : 4;
    //oldIndexSream.mData.resize(oldIndexSream.mData.size() + newIndexCount * indexStride);
    oldIndexSream.mData.resize((oldIndexDataCount + newIndexCount) * indexStride);
    auto srcData = oldIndexSream.mData.data();
    auto dstData = newIndexStream.mData.data();
    if (newIndexStride > indexStride)
    {
        for (UInt32 idx = 0; idx < newIndexCount; idx++)
        {
            memcpy(srcData + (oldIndexStart + idx) * indexStride, dstData + (newIndexStart + idx) * indexStride + newIndexStride - indexStride, indexStride);
        }
    }
    else if (newIndexStride < indexStride)
    {
        for (UInt32 idx = 0; idx < newIndexCount; idx++)
        {
            memset(srcData + (oldIndexStart + idx) * indexStride + indexStride - newIndexStride, 0, indexStride - newIndexStride);
            memcpy(srcData + (oldIndexStart + idx) * indexStride, dstData + (newIndexStart + idx) * newIndexStride, newIndexStride);
        }
    }
    else
    {
        memcpy(srcData + oldIndexStart * indexStride, dstData + newIndexStart * indexStride, newIndexCount * indexStride);
    }
    //oldIndexSream.mCount += newIndexCount;
    oldIndexSream.mCount = oldIndexDataCount + newIndexCount;
    // add mesh part info
    UInt32 tmpVertexCount = 0;
    UInt32 tmpIndexCount = 0;
    UInt32 tmpPrimitiveCount = 0;
    mMeshPartInfo.resize(mMeshPartInfo.size() + newLodMeshCount);
    for (UInt32 index = 0; index < newLodMeshCount; index++)
    {
        const auto& newMeshPart = newMeshData->GetMeshPartInfo(newLodMeshPartStartIndex + index);
        mMeshPartInfo[oldLodMeshPartCount + index] = newMeshPart;
        mMeshPartInfo[oldLodMeshPartCount + index].mVertexStart = oldVertexDataCount + tmpVertexCount;
        mMeshPartInfo[oldLodMeshPartCount + index].mIndexStart = oldIndexDataCount + tmpIndexCount;
        tmpVertexCount += newMeshPart.mVertexCount;
        tmpIndexCount += newMeshPart.mIndexCount;
        tmpPrimitiveCount += newMeshPart.mPrimitiveCount;
    }
    //mVertexCount += tmpVertexCount;
    //mPrimitiveCount += tmpPrimitiveCount;
    mVertexCount = oldVertexDataCount + newVertexCount;
    mPrimitiveCount = (oldIndexDataCount + newIndexCount) / 3;
    // add lod start index
    mMeshPartLodStartIndex.emplace_back(oldLodMeshPartCount);
    return EditMeshLodError::ERR_EDITOR_SUCCESS;
}

EditMeshLodError MeshAssetData::DelLodMeshData(UInt8 lod)
{
    // 1 check mesh part count
    auto lodCount = GetLodCount();
    if (lod >= lodCount)
    {
        LOG_WARN("Del lod({0}) large lodCount{1}", lod, lodCount);
        return EditMeshLodError::ERR_EDITOR_LOD_OVERFLOW;
    }
    // prepare
    UInt32 vertexCount = 0;
    UInt32 indexCount = 0;
    UInt32 primitiveCount = 0;
    UInt32 lodMeshPartStartIndex = 0;
    UInt32 lodMeshCount = 0;
    GetMeshLodInfo(lod, lodMeshPartStartIndex, lodMeshCount);
    for (UInt32 meshPart = lodMeshPartStartIndex; meshPart < lodMeshPartStartIndex + lodMeshCount; ++meshPart)
    {
        const MeshPartAssetInfo& part = GetMeshPartInfo(meshPart);
        vertexCount     += part.mVertexCount;
        indexCount      += part.mIndexCount;
        primitiveCount  += part.mPrimitiveCount;
    }
    //  delete mesh part info
    mMeshPartInfo.erase(mMeshPartInfo.begin() + lodMeshPartStartIndex, mMeshPartInfo.begin() + lodMeshPartStartIndex + lodMeshCount);
    for (UInt32 meshPart = lodMeshPartStartIndex; meshPart < mMeshPartInfo.size(); ++meshPart)
    {
        mMeshPartInfo[meshPart].mVertexStart -= vertexCount;
        mMeshPartInfo[meshPart].mIndexStart -= indexCount;
    }
    // delete mesh  part lod start index
    mMeshPartLodStartIndex.erase(mMeshPartLodStartIndex.begin() + lod);
    for (UInt8 lodIndex = lod; lodIndex < lodCount - 1; lodIndex++)
    {
        mMeshPartLodStartIndex[lodIndex] -= lodMeshCount;
    }
    UInt32 lodMeshPartCount = static_cast<UInt32>(mMeshPartInfo.size());
    UInt32 vertexDataCount = mMeshPartInfo[lodMeshPartCount - 1].mVertexStart + mMeshPartInfo[lodMeshPartCount - 1].mVertexCount;
    //UInt32 vertexStart = vertexDataCount;
    UInt32 indexDataCount = mMeshPartInfo[lodMeshPartCount - 1].mIndexStart + mMeshPartInfo[lodMeshPartCount - 1].mIndexCount;
    // delete vertex data
    auto vertexStart = GetMeshPartInfo(lodMeshPartStartIndex).mVertexStart;
    for (auto& vertexChannel : mVertexChannelData)
    {
        auto vertexStride = vertexChannel.mStride;
        UInt32 endPos = (vertexStart + vertexCount) * vertexStride;
        if (endPos <= vertexChannel.mData.size())
        {
            vertexChannel.mData.erase(vertexChannel.mData.begin() + vertexStart * vertexStride, vertexChannel.mData.begin() + (vertexStart + vertexCount) * vertexStride);
            vertexChannel.mData.shrink_to_fit();
            vertexChannel.mData.resize(vertexDataCount * vertexStride);
        }
        else
        {
            AssertMsg(false, "Channel Data Error Format: {} DataSize {} EndPos {}", vertexChannel.mDataFormat, vertexChannel.mData.size(), endPos);
        }
    }
    // delete index data
    auto indexStart = GetMeshPartInfo(lodMeshPartStartIndex).mIndexStart;
    auto indexStride = mIndexStream.mIs16BitIndex ? 2 : 4;
    mIndexStream.mData.erase(mIndexStream.mData.begin() + indexStart * indexStride, mIndexStream.mData.begin() + (indexStart + indexCount) * indexStride);
    mIndexStream.mData.resize(indexDataCount * indexStride);
    mIndexStream.mCount = indexDataCount;
    mVertexCount = vertexDataCount;
    mPrimitiveCount = indexDataCount / 3;
    return EditMeshLodError::ERR_EDITOR_SUCCESS;
}

cross::BoundingBox MeshAssetData::GetMeshPartBoundingBox(UInt32 meshPart)
{
    if (meshPart >= 0 && meshPart < mMeshPartInfo.size())
    {
        const auto& meshBound = mMeshPartInfo[meshPart].mMeshBound;
        Float3 extent((meshBound.Max.x - meshBound.Min.x) / 2, (meshBound.Max.y - meshBound.Min.y) / 2, (meshBound.Max.z - meshBound.Min.z) / 2);
        Float3 center(extent.x + meshBound.Min.x, extent.y + meshBound.Min.y, extent.z + meshBound.Min.z);
        BoundingBox boundingBox(center, extent);
        return boundingBox;
    }
    else
    {
        return GetBoundingBox();
    }
}

int MeshAssetData::Combine(const std::vector<MeshAssetData*>& inMeshs, const std::vector<Float4x4>& trans, const std::vector<std::vector<std::array<int, 4>>>& texIndexs, MeshAssetData& outMesh)
{
    if (inMeshs.size() <= 0)
        return 1;
    // check
    for (UInt32 meshIndex = 1; meshIndex < inMeshs.size(); meshIndex++)
    {
        const auto& mesh = *inMeshs[meshIndex];
        if (mesh.mVertexChannelSemanticMask != inMeshs[0]->mVertexChannelSemanticMask)
            return 2;
        for (const auto& vertexChannel : mesh.mVertexChannelData)
        {
            auto standerVertexChannel = inMeshs[0]->GetVertexChannelData(vertexChannel.mVertexChannel);
            if (standerVertexChannel && (standerVertexChannel->mDataFormat != vertexChannel.mDataFormat || standerVertexChannel->mStride != vertexChannel.mStride))
                return 2;
        }
        //if (mesh.mIndexStream.mIs16BitIndex != inMeshs[0]->mIndexStream.mIs16BitIndex)
        //    return 3;
        if (mesh.mMeshPartLodStartIndex != inMeshs[0]->mMeshPartLodStartIndex || mesh.mMeshPartInfo.size() != inMeshs[0]->mMeshPartInfo.size())
            return 4;
    }
    //
    outMesh.mIndexStream.mIs16BitIndex = false;
    outMesh.mMeshPartLodStartIndex = inMeshs[0]->mMeshPartLodStartIndex;
    // 
    UInt32 meshPartCount = static_cast<UInt32>(inMeshs[0]->mMeshPartInfo.size());
    for (UInt32 meshPartIndex = 0; meshPartIndex < meshPartCount; meshPartIndex++)
    {
        for (UInt32 meshIndex = 0; meshIndex < inMeshs.size(); meshIndex++)
        {
            const auto& meshPart = inMeshs[meshIndex]->mMeshPartInfo[meshPartIndex];
            if (outMesh.mMeshPartInfo.size() <= meshPartIndex)
            {
                outMesh.mMeshPartNames.emplace_back("part" + std::to_string(meshPartIndex));
                auto& outMeshPart = outMesh.mMeshPartInfo.emplace_back();
                outMeshPart.mNameIndex = static_cast<SInt16>(meshPartIndex);
                outMeshPart.mMaterialIndex = 0;
                outMeshPart.mPrimitiveType = meshPart.mPrimitiveType;
                if (meshPartIndex > 0)
                {
                    auto& preMeshPart = outMesh.mMeshPartInfo[meshPartIndex - 1];
                    outMeshPart.mIndexStart     = preMeshPart.mIndexStart + preMeshPart.mIndexCount;
                    outMeshPart.mVertexStart    = preMeshPart.mVertexStart + preMeshPart.mVertexCount;
                }
            }
            auto& outMeshPart = outMesh.mMeshPartInfo[meshPartIndex];
            outMeshPart.mVertexCount    += meshPart.mVertexCount;
            outMeshPart.mIndexCount     += meshPart.mIndexCount;
            outMeshPart.mPrimitiveCount += meshPart.mPrimitiveCount;
            outMeshPart.mMeshBound.Encapsulate(meshPart.mMeshBound);
        }
    }
    Float4 uvIds;
    FloatToHalfConverter converter;
    for (UInt32 meshIndex = 0; meshIndex < inMeshs.size(); meshIndex++)
    {
        UInt32 startIndex = outMesh.mVertexCount;
        bool addUVIdx = false;
        for (const auto& channel : inMeshs[meshIndex]->mVertexChannelData)
        {
            if (!inMeshs[0]->HasVertexChannel(channel.mVertexChannel))
                continue;

            if (!trans.empty() && channel.mVertexChannel == VertexChannel::Position0) {
                if (channel.mDataFormat == VertexFormat::Float3)
                {
                    for (UInt32 vertexIndex = 0; vertexIndex < inMeshs[meshIndex]->mVertexCount; vertexIndex++)
                    {
                        Float3 position;
                        ConvertToFloat(channel.mData.data() + vertexIndex * channel.mStride, channel.mDataFormat, position);
                        Float4 newPositon = Float4(position, 1.0f) * trans[meshIndex];
                        position = Float3(newPositon.x, newPositon.y, newPositon.z);
                        outMesh.AddChannelData(channel.mVertexChannel, channel.mDataFormat, channel.mStride, reinterpret_cast<UInt8*>(&position), 1);
                    }
                    continue;
                }
            }
            if (texIndexs.size() && channel.mVertexChannel == VertexChannel::Normal2)
            {
                if (channel.mDataFormat == VertexFormat::Float4)
                {
                    for (UInt32 vertexIndex = 0; vertexIndex < inMeshs[meshIndex]->mVertexCount; vertexIndex++)
                    {
                        ConvertToFloat(channel.mData.data() + vertexIndex * channel.mStride, channel.mDataFormat, uvIds);
                        uvIds.x = texIndexs[meshIndex][static_cast<int>(uvIds.x < 0 ? 0 : uvIds.x)][0] * 1.0f;
                        uvIds.y = texIndexs[meshIndex][static_cast<int>(uvIds.y < 0 ? 0 : uvIds.y)][1] * 1.0f;
                        uvIds.z = texIndexs[meshIndex][static_cast<int>(uvIds.z < 0 ? 0 : uvIds.z)][2] * 1.0f;
                        uvIds.w = texIndexs[meshIndex][static_cast<int>(uvIds.w < 0 ? 0 : uvIds.w)][3] * 1.0f;
                        outMesh.AddChannelData(channel.mVertexChannel, channel.mDataFormat, channel.mStride, reinterpret_cast<UInt8*>(&uvIds), 1);
                    }
                    addUVIdx = true;
                }
                continue;
            }
            outMesh.AddChannelData(channel.mVertexChannel, channel.mDataFormat, channel.mStride, channel.mData.data(), inMeshs[meshIndex]->mVertexCount);         
        }
        if (texIndexs.size() && !addUVIdx)
        {
            uvIds.x = static_cast<float>(texIndexs[meshIndex][0][0]);
            uvIds.y = static_cast<float>(texIndexs[meshIndex][0][0]);
            uvIds.z = static_cast<float>(texIndexs[meshIndex][0][2]);
            uvIds.w = static_cast<float>(texIndexs[meshIndex][0][3]);
            for (UInt32 vertexIndex = 0; vertexIndex < inMeshs[meshIndex]->mVertexCount; vertexIndex++)
            {
                outMesh.AddChannelData(VertexChannel::Normal2, VertexFormat::Float4, sizeof(Float4), reinterpret_cast<UInt8*>(uvIds.data()), 1);
            }
        }

        outMesh.mVertexCount    += inMeshs[meshIndex]->mVertexCount;
        outMesh.mPrimitiveCount += inMeshs[meshIndex]->mPrimitiveCount;

        const auto& indexStream = inMeshs[meshIndex]->mIndexStream;
        for (UInt32 index = 0; index < indexStream.mCount; index++)
        {
            UInt32 indexValue;
            if (indexStream.mIs16BitIndex)
            {
                const UInt16* mData16 = reinterpret_cast<const UInt16*>(indexStream.mData.data());
                indexValue = static_cast<UInt32>(mData16[index]);
            }
            else
            {
                const UInt32* mData32 = reinterpret_cast<const UInt32*>(indexStream.mData.data());
                indexValue = static_cast<UInt32>(mData32[index]);
            }
            indexValue += startIndex;
            outMesh.AddIndexData(outMesh.mIndexStream.mIs16BitIndex ? static_cast<UInt16>(indexValue) : indexValue);
        }
    }
    outMesh.CalculateWholeAABB();
    int collisonTp = inMeshs[0]->mPhysicsCollision.mMeshGeometry.size() > 0 ? 1 : (inMeshs[0]->mPhysicsCollision.mBoxGeometry.size() > 0 ? 0 : -1);
    outMesh.AddCollisionMesh(collisonTp);
    return 0;
}

void MeshAssetData::AddCollisionMesh(int type)
{
    if (type == 0) // simple collision mesh
    {   
        if (mPhysicsCollision.mBoxGeometry.size() <= 0)
        {
            Float3 center = (mAABB.Min + mAABB.Max) / 2.f;
            Float3 halfExtents = (mAABB.Max - mAABB.Min) / 2.0f;
            PhysicsGeometryBox box = {center, Quaternion::Identity(), halfExtents};
            mPhysicsCollision.mBoxGeometry.emplace_back(std::move(box));
        }
        return;
    }
    if (type == 1) // complex collision
    {
        if (mPhysicsCollision.mMeshGeometry.size() <= 0)
        {
            auto posChannel = GetVertexChannelData(VertexChannel::Position0);
            PhysicsEngine* physicsEngine = EngineGlobal::GetPhysicsEngine();
            for (const auto& meshPart : mMeshPartInfo) 
            {
                if (posChannel && meshPart.mVertexCount > 0)
                {
                    auto tran = physicsEngine->GetCooker()->BuildTriangleMesh(
                        posChannel->mData.data() + meshPart.mVertexStart, meshPart.mVertexCount, posChannel->mStride, mIndexStream.mData.data() + meshPart.mIndexStart, meshPart.mIndexCount, mIndexStream.mIs16BitIndex ? 2 : 4);
                    PhysicsGeometryMesh coll = {Float3::Zero(), Quaternion::Identity(), tran};
                    mPhysicsCollision.mMeshGeometry.emplace_back(std::move(coll));
                }
            }
        }
        return;
    }
}

cross::Float3 MeshAssetData::ChangeMeshCenterToPointsCenter()
{
    auto* vertexChannelPos0Data = GetVertexChannelData(VertexChannel::Position0);
    auto* VBPos = reinterpret_cast<Float3*>(vertexChannelPos0Data->mData.data());
    auto vertexCnt = GetVertexCount();
    Double3 center = Double3::Zero();
    for (UInt32 i = 0; i < vertexCnt; ++i)
    {
        Double3 pos = {VBPos[i].x, VBPos[i].y, VBPos[i].z};
        center += pos;
    }
    center /= vertexCnt * 1.0;
    for (UInt32 i = 0; i < vertexCnt; ++i)
    {
        VBPos[i].x -= static_cast<float>(center.x);
        VBPos[i].y -= static_cast<float>(center.y);
        VBPos[i].z -= static_cast<float>(center.z);
    }
    int collisonTp = mPhysicsCollision.mMeshGeometry.size() > 0 ? 1 : (mPhysicsCollision.mBoxGeometry.size() > 0 ? 0 : -1);
    if (collisonTp >= 0)
    {
        mPhysicsCollision.mMeshGeometry.clear();
        mPhysicsCollision.mBoxGeometry.clear();
        AddCollisionMesh(collisonTp);
    }
    CalculateWholeAABB();
    return center.ToFloat3();
}

int MeshAssetData::SeparateTo(Float2 point, Float2 blockSize, std::vector<MeshAssetData>& outMeshs)
{
    UInt32 rightRow = static_cast<UInt32>(std::max(std::ceil((mAABB.Max.x - point.x) / blockSize.x), 0.0f));
    UInt32 leftRow = static_cast<UInt32>(std::max(std::ceil((point.x - mAABB.Min.x) / blockSize.x), 0.0f));
    UInt32 upLine = static_cast<UInt32>(std::max(std::ceil((mAABB.Max.z - point.y) / blockSize.y), 0.0f));
    UInt32 downLine = static_cast<UInt32>(std::max(std::ceil((point.y - mAABB.Min.z) / blockSize.y), 0.0f));

    UInt32 blockCount = (rightRow + leftRow) * (upLine + downLine);
    if (blockCount <= 1)
        return -1;
    std::vector<Double2> blockCenters(blockCount);
    std::vector<std::unordered_map<UInt32, UInt32>> indexMaps(blockCount);
    outMeshs.resize(blockCount);

    
    double xBegin = point.x - leftRow * blockSize.x;
    double yBgine = point.y - downLine * blockSize.y;
    for (UInt32 i = 0; i < rightRow + leftRow; i++)
    {
        for (UInt32 j = 0; j < upLine + downLine; j++)
        {
            UInt32 index = i * (upLine + downLine) + j;
            blockCenters[index].x = xBegin + (i + 0.5) * blockSize.x;
            blockCenters[index].y = yBgine + (j + 0.5) * blockSize.y;
        }
    }
    // pre mesh
    for (UInt32 idx = 0; idx < outMeshs.size(); idx++)
    {
        outMeshs[idx].mIndexStream.mIs16BitIndex = mIndexStream.mIs16BitIndex;
        outMeshs[idx].mName = GetName() + ".part" + std::to_string(idx);
        outMeshs[idx].mMaterialNames = mMaterialNames;
        outMeshs[idx].mMeshPartNames = mMeshPartNames;
        outMeshs[idx].mMeshPartInfo.resize(mMeshPartInfo.size());
        outMeshs[idx].mMeshPartLodStartIndex = mMeshPartLodStartIndex;
    }
    // gen mesh
    for (UInt8 lod = 0; lod < GetLodCount(); lod++)
    {
        SeparateTo(lod, blockCenters, outMeshs, indexMaps);
    }
    int collisonTp = mPhysicsCollision.mMeshGeometry.size() > 0 ? 1 : (mPhysicsCollision.mBoxGeometry.size() > 0 ? 0 : -1);
    if (collisonTp >= 0)
    {
        for (UInt32 idx = 0; idx < outMeshs.size(); idx++)
        {
            outMeshs[idx].AddCollisionMesh(collisonTp);
        }
    }
    return 0;
}

int MeshAssetData::SeparateTo(Float2 point, Float2 blockSize, MeshAssetData& outMesh) {
    std::vector<MeshAssetData> tempMeshes;
    int ret = SeparateTo(point, blockSize, tempMeshes);
    if (ret == 0)
    {
        auto lodCount = GetLodCount();
        auto tempMeshCount = tempMeshes.size();
        // init out mesh
        outMesh.mIndexStream.mIs16BitIndex = mIndexStream.mIs16BitIndex;
        outMesh.mName = GetName() + ".split";
        outMesh.mMaterialNames = mMaterialNames;
        outMesh.mMeshPartNames = mMeshPartNames;
        // init count
        for (size_t i = 0; i < tempMeshCount; i++)
        {
            outMesh.mVertexCount += tempMeshes[i].mVertexCount;
            outMesh.mPrimitiveCount += tempMeshes[i].mPrimitiveCount;
            outMesh.mIndexStream.mCount += tempMeshes[i].mIndexStream.mCount;
        }
        // init vertex data
        outMesh.mVertexChannelData.clear();
        for (const auto& channelData : mVertexChannelData)
        {
            VertexChannelAssetData& vertexChannel = outMesh.mVertexChannelData.emplace_back();
            vertexChannel.mStride = channelData.mStride;
            vertexChannel.mVertexChannel = channelData.mVertexChannel;
            vertexChannel.mDataFormat = channelData.mDataFormat;
            outMesh.mVertexChannelSemanticMask |= static_cast<UInt32>(GetSemantic(channelData.mVertexChannel));
            vertexChannel.mData.resize(outMesh.mVertexCount * channelData.mStride);
        }
        // init index data
        auto indexStride = mIndexStream.mIs16BitIndex ? 2 : 4;
        outMesh.mIndexStream.mData.resize(indexStride * outMesh.mIndexStream.mCount);

        UInt32 vertexStart = 0;
        UInt32 indexStart = 0;
        UInt32 meshPartCount = 0;
        UInt32 tmepLodMeshPartStartIndex = 0;
        UInt32 tempLodMeshPartCount = 0;
        for (UInt8 lod = 0; lod < lodCount; lod++)
        {
            outMesh.mMeshPartLodStartIndex.push_back(meshPartCount);
            for (UInt64 tempMeshIdx = 0; tempMeshIdx < tempMeshCount; tempMeshIdx++)
            {
                auto& tmepMesh = tempMeshes[tempMeshIdx];
                tmepMesh.GetMeshLodInfo(lod, tmepLodMeshPartStartIndex, tempLodMeshPartCount);

                for (UInt32 meshPartIdx = tmepLodMeshPartStartIndex; meshPartIdx < tmepLodMeshPartStartIndex + tempLodMeshPartCount; meshPartIdx++)
                {
                    auto& tempMeshPartInfo = tmepMesh.mMeshPartInfo[meshPartIdx];
                    if (tempMeshPartInfo.mVertexCount <= 0)
                        continue;
                    auto& outMeshPartInfo = outMesh.mMeshPartInfo.emplace_back(tempMeshPartInfo);
                    outMeshPartInfo.mVertexStart = vertexStart;
                    outMeshPartInfo.mIndexStart = indexStart;

                    // vertex data
                    for (size_t i = 0; i < tmepMesh.mVertexChannelData.size(); i++)
                    {
                        auto& tempChannelData = tmepMesh.mVertexChannelData[i];
                        auto& channelData = outMesh.mVertexChannelData[i];
                        memmove(channelData.mData.data() + vertexStart * channelData.mStride, tempChannelData.mData.data() + tempMeshPartInfo.mVertexStart * channelData.mStride, tempMeshPartInfo.mVertexCount * channelData.mStride);
                    }
                    // index data
                    memmove(outMesh.mIndexStream.mData.data() + indexStart * indexStride, tmepMesh.mIndexStream.mData.data() + tempMeshPartInfo.mIndexStart * indexStride, tempMeshPartInfo.mIndexCount * indexStride);
                    // 
                    vertexStart += tempMeshPartInfo.mVertexCount;
                    indexStart += tempMeshPartInfo.mIndexCount;
                    meshPartCount++;
                }
            }
            // AddCollisionMesh
            int collisonTp = mPhysicsCollision.mMeshGeometry.size() > 0 ? 1 : (mPhysicsCollision.mBoxGeometry.size() > 0 ? 0 : -1);
            outMesh.AddCollisionMesh(collisonTp);
        }
    }
    return ret;
}

void MeshAssetData::AddChannelData(VertexChannel channel, VertexFormat format, UInt16 stride, const UInt8* inData, UInt32 inSize) {
    if (!HasVertexChannel(channel))
    {
        VertexChannelAssetData& vertexChannel = mVertexChannelData.emplace_back();
        vertexChannel.mStride = stride;
        vertexChannel.mVertexChannel = channel;
        vertexChannel.mDataFormat = format;
        mVertexChannelSemanticMask |= static_cast<UInt32>(GetSemantic(channel));
        vertexChannel.mData.clear();
    }

    VertexChannelAssetData* vertexChannel = GetVertexChannelData(channel);
    auto orgSize = static_cast<UInt32>(vertexChannel->mData.size());
    vertexChannel->mData.resize(orgSize + inSize * stride);
    memcpy(vertexChannel->mData.data() + orgSize, inData, inSize * stride);
}

bool MeshAssetData::IsClusterMeshEnabled() const
{
    return mClusterMeshEnabled;
}

void MeshAssetData::CollectBlendShapeChannelInfo(UInt32 meshPartIndex)
{
    const auto& meshPart = mMeshPartInfo[meshPartIndex];
    if (meshPart.HasBlendShape())
    {
        UInt32 lodIndex = 0;
        const auto& lodStartIdxArray = GetLodStartIndex();
        const UInt32 lodCount = static_cast<UInt32>(lodStartIdxArray.size());
        for (UInt32 idx = 0; idx < lodCount; ++idx)
        {
            if ((idx == lodCount - 1U) && meshPartIndex >= lodStartIdxArray[idx])
            {
                lodIndex = idx;
            }
            else if (meshPartIndex >= lodStartIdxArray[idx] && meshPartIndex < lodStartIdxArray[idx + 1])
            {
                lodIndex = idx;
                break;
            }
        }

        auto& blendShapeChannelMap = mBlendShapeLODChannelMap[lodIndex];

        const auto& ChannelNameIndexMap = meshPart.mBlendShape.ChannelNameToIndexMap;
        for (auto& channelPair : ChannelNameIndexMap)
        {
            auto result = blendShapeChannelMap.find(channelPair.first);
            if (result != blendShapeChannelMap.end())
            {
                // Record channel index in current submodel for cur channel
                result->second.emplace_back(meshPartIndex - lodStartIdxArray[lodIndex], channelPair.second);
            }
            else
            {
                auto pair = blendShapeChannelMap.insert({channelPair.first, ChannelCollectInfo()});
                // Record channel index in current submodel for cur channel
                pair.first->second.emplace_back(meshPartIndex - lodStartIdxArray[lodIndex], channelPair.second);
            }
        }
    }
}

void MeshAssetData::SeparateTo(UInt8 lod, std::vector<Double2>& blockCenters, std::vector<MeshAssetData>& outMeshs, std::vector<std::unordered_map<UInt32, UInt32>>& outIndexMaps)
{
    // prepare
    UInt32 meshPartStartIndex = 0;
    UInt32 meshPartCount = 0;
    GetMeshLodInfo(lod, meshPartStartIndex, meshPartCount);

    UInt32 primitiveVertexCount = mIndexStream.mCount / mPrimitiveCount;
    std::vector<UInt32> vertexIndexs(primitiveVertexCount, 0);

    const VertexChannelAssetData& vertexPos = GetChannelAssetData(VertexChannel::Position0);
    for (UInt32 meshPartIndex = meshPartStartIndex; meshPartIndex < meshPartStartIndex + meshPartCount; ++meshPartIndex)
    {
        const MeshPartAssetInfo& part = GetMeshPartInfo(meshPartIndex);
        for (auto& mesh : outMeshs)
        {
            auto& meshPart = mesh.mMeshPartInfo[meshPartIndex];
            meshPart.mNameIndex = part.mNameIndex;
            meshPart.mMaterialIndex = part.mMaterialIndex;
            meshPart.mPrimitiveType = part.mPrimitiveType;
            if (meshPartIndex > 0)
            {
                auto& preMeshPart = mesh.mMeshPartInfo[meshPartIndex - 1];
                meshPart.mIndexStart = preMeshPart.mIndexStart + preMeshPart.mIndexCount;
                meshPart.mVertexStart = preMeshPart.mVertexStart + preMeshPart.mVertexCount;
            }
        }
        for (UInt32 index = 0; index < part.mIndexCount; index += primitiveVertexCount)
        {
            MeshBound faceBound;
            Double2 faceCenter(0.0, 0.0);
            
            for (UInt32 pmvIndex = 0; pmvIndex < primitiveVertexCount; pmvIndex++)
            {
                if (mIndexStream.mIs16BitIndex)
                {
                    const UInt16* mData16 = reinterpret_cast<const UInt16*>(mIndexStream.mData.data());
                    vertexIndexs[pmvIndex] = static_cast<UInt32>(mData16[index + pmvIndex + part.mIndexStart]) + part.mVertexStart;
                }
                else
                {
                    const UInt32* mData32 = reinterpret_cast<const UInt32*>(mIndexStream.mData.data());
                    vertexIndexs[pmvIndex] = static_cast<UInt32>(mData32[index + pmvIndex + part.mIndexStart]) + part.mVertexStart;
                }
                Float3 vertex;
                ConvertToFloat(vertexPos.mData.data() + vertexIndexs[pmvIndex] * vertexPos.mStride, vertexPos.mDataFormat, vertex);
                faceBound.Encapsulate(vertex);
                faceCenter.x += vertex.x;
                faceCenter.y += vertex.z;
            }
            faceCenter = faceCenter * (1.0 / primitiveVertexCount);

            double minDis = HUGE_VAL;
            UInt32 minBlockIndex = 0;
            for (UInt32 blockIndex = 0; blockIndex < blockCenters.size(); blockIndex++)
            {
                double dis = Double2::Distance(faceCenter, blockCenters[blockIndex]);
                if (dis < minDis)
                {
                    minDis = dis;
                    minBlockIndex = blockIndex;
                }
            }
            auto& meshPart = outMeshs[minBlockIndex].GetMeshPartInfo(meshPartIndex);
            auto& indexMap = outIndexMaps[minBlockIndex];
            auto& outMesh = outMeshs[minBlockIndex];
            meshPart.mMeshBound.Encapsulate(faceBound);
            outMesh.mAABB.Encapsulate(meshPart.mMeshBound);
            for (UInt32 vertexIndex : vertexIndexs)
            {
                if (indexMap.find(vertexIndex) == indexMap.end())
                {
                    for (const auto& channel : mVertexChannelData)
                    {
                        outMesh.AddChannelData(channel.mVertexChannel, channel.mDataFormat, channel.mStride, channel.mData.data() + vertexIndex * channel.mStride, 1);
                    }
                    meshPart.mVertexCount++;
                    outMesh.mVertexCount++;
                    indexMap.try_emplace(vertexIndex, outMesh.mVertexCount - 1);
                }
                meshPart.mIndexCount++;
                UInt32 newVertexIndex = indexMap[vertexIndex] - meshPart.mVertexStart;
                outMesh.AddIndexData(mIndexStream.mIs16BitIndex ? static_cast<UInt16>(newVertexIndex) : newVertexIndex);
            }
            meshPart.mPrimitiveCount++;
            outMesh.mPrimitiveCount++;
        }
    }
}
void MeshAssetData::OnDataUploaded() 
{
    if (GetBindPoseInvMat().empty()) // Only clear Static Mesh Data
    {
        //Clear VB IB In memory
        GetIndexStream().mData.clear();
        std::vector<UInt8, ce_stl_allocator<UInt8>>().swap(GetIndexStream().mData);
        ClearVertexData();
    }
}
bool MeshAssetData::DeserializeFromCrossSchema(const CrossSchema::ImportMeshAssetData& importMeshAsset)
    {
    QUICK_SCOPED_CPU_TIMING("MeshAssetData::DeserializeFromCrossSchema");
    // temporary code
    //static bool USEFULLPRECISION_UV = false;
    //static bool USEFULLPRECISION_NORMAL = false;
    static bool GenerateQtangentOnTheFly = true;
    static bool DumpNormalAndTangent = false;

    mVersion = static_cast<MeshAssetVersionFlags>(importMeshAsset.fversion());
    mName.append(importMeshAsset.fname()->c_str());
    mVertexCount = importMeshAsset.fvertexcount();
    mPrimitiveCount = importMeshAsset.fprimitivecount();
    mVertexChannelSemanticMask = importMeshAsset.fvertexchannelsemanticmask();

    mIndexStream.mCount = importMeshAsset.findexstream()->fcount();
    mIndexStream.mData.resize(importMeshAsset.findexstream()->fdata()->size());
    //std::copy(importMeshAsset.findexstream()->fdata()->begin(), importMeshAsset.findexstream()->fdata()->end(), mIndexStream.mData.begin());
    memcpy(mIndexStream.mData.data(), importMeshAsset.findexstream()->fdata()->data(), importMeshAsset.findexstream()->fdata()->size());
    mIndexStream.mIs16BitIndex = importMeshAsset.findexstream()->fis16bitindex();

    FloatToHalfConverter converter;
    auto FloatToHalf = [&converter](const float& src, UInt16& out)
    {
        converter.Convert(src, out);
    };

    auto ToSNORM = [](const float& src, auto& out)
    {
        FloatToSNORM(src, out);
    };

    // Vertex channel data
    for (int i = 0; i < (int)importMeshAsset.fvertexchanneldata()->size(); i++)
    {
        QUICK_SCOPED_CPU_TIMING("MeshAssetData::VertexData");
        auto fbVertexChannelData = (*importMeshAsset.fvertexchanneldata())[i];
        if (!fbVertexChannelData->fdata())
            continue;

        VertexChannelAssetData& vertexChannel = mVertexChannelData.emplace_back();
        vertexChannel.mVertexChannel = (VertexChannel)fbVertexChannelData->fvertexchannel();

        // Convert all Byte3 UByte3 Channels to Byte4 UByte4, since vulkan doesn't support R8G8B8_(S/U)Norm format texel buffer
        UInt32 convertedSize = 0;
        switch ((VertexFormat)fbVertexChannelData->fdataformat())
        {
        case VertexFormat::Byte3_Norm:
            convertedSize = fbVertexChannelData->fdata()->size() / 3 * 4;
            vertexChannel.mData.resize(convertedSize);
            
            for (UInt32 index = 0; index < convertedSize / 4; index++)
            {
                memcpy(vertexChannel.mData.data() + index * 4, fbVertexChannelData->fdata()->data() + index * 3, 3);
                memset(vertexChannel.mData.data() + index * 4 + 3, 0, 1);  // set w to 0
            }
        
            vertexChannel.mDataFormat = VertexFormat::Byte4_Norm;
            vertexChannel.mStride = 4;
            break;
        case VertexFormat::UByte3_Norm:
            convertedSize = fbVertexChannelData->fdata()->size() / 3 * 4;
            vertexChannel.mData.resize(convertedSize);
        
            for (UInt32 index = 0; index < convertedSize / 4; index++)
            {
                memcpy(vertexChannel.mData.data() + index * 4, fbVertexChannelData->fdata()->data() + index * 3, 3);
                memset(vertexChannel.mData.data() + index * 4 + 3, 0, 1);  // set w to 0
            }
        
            vertexChannel.mDataFormat = VertexFormat::UByte4_Norm;
            vertexChannel.mStride = 4;
            break;
        default:
            vertexChannel.mData.resize(fbVertexChannelData->fdata()->size());
            memcpy(vertexChannel.mData.data(), fbVertexChannelData->fdata()->data(), fbVertexChannelData->fdata()->size());
            vertexChannel.mDataFormat = (VertexFormat)fbVertexChannelData->fdataformat();
            vertexChannel.mStride = fbVertexChannelData->fstride();
            break;
        }
    }

    // Runtime Add QuatTangent (a hack to avoid reimport assets)
    if (EngineGlobal::GetSettingMgr()->GetEnableTangents())
    {
        // only generate when both normal and tangent present and no quat tan
        if (HasVertexChannel(VertexChannel::Normal0) && HasVertexChannel(VertexChannel::Tangent0) && !HasVertexChannel(VertexChannel::QUATTAN0))
        {
            // emplace at first to avoid get Vertexchannel failed.
            VertexChannelAssetData& vertexChannel = mVertexChannelData.emplace_back();

            auto tangent_vertex_data = GetVertexChannelData(VertexChannel::Tangent0);
            auto normal_vertex_data = GetVertexChannelData(VertexChannel::Normal0);

            mVertexChannelSemanticMask |= static_cast<UInt32>(VertexSemantic::SemanticQuatTan);
 
            vertexChannel.mVertexChannel = VertexChannel::QUATTAN0;
            vertexChannel.mDataFormat = tangent_vertex_data->mDataFormat;
            vertexChannel.mStride = tangent_vertex_data->mStride;
            vertexChannel.mData.resize(tangent_vertex_data->mData.size());

            for (size_t i = 0; i < mVertexCount; i++)
            {
                Float3 normal;
                Float4 tangent;

                auto vertex_normal_address = normal_vertex_data->mData.data() + i * normal_vertex_data->mStride;
                auto tangent_normal_address = tangent_vertex_data->mData.data() + i * tangent_vertex_data->mStride;

                auto qtangent_address = vertexChannel.mData.data() + i * vertexChannel.mStride;

                ConvertToFloat(vertex_normal_address, normal_vertex_data->mDataFormat, normal, true);
                ConvertToFloat(tangent_normal_address, tangent_vertex_data->mDataFormat, tangent);

                auto quat = tangent_space_to_quat(normal, tangent, 8 * GetByteSize(tangent_vertex_data->mDataFormat) / 4);


                if (vertexChannel.mDataFormat == VertexFormat::Short4_Norm)
                {
                    UShort4 normalized_data;
                    FloatToSNORM(quat.x, normalized_data.x);
                    FloatToSNORM(quat.y, normalized_data.y);
                    FloatToSNORM(quat.z, normalized_data.z);
                    FloatToSNORM(quat.w, normalized_data.w);

                    *reinterpret_cast<UShort4*>(qtangent_address) = normalized_data;
                }
                else if (vertexChannel.mDataFormat == VertexFormat::Byte4_Norm)
                {
                    UChar4 normalized_data;
                    FloatToSNORM(quat.x, normalized_data.x);
                    FloatToSNORM(quat.y, normalized_data.y);
                    FloatToSNORM(quat.z, normalized_data.z);
                    FloatToSNORM(quat.w, normalized_data.w);

                    *reinterpret_cast<UChar4*>(qtangent_address) = normalized_data;
                }
                else if (vertexChannel.mDataFormat == VertexFormat::Float4)
                {
                    *reinterpret_cast<Quaternion*>(qtangent_address) = quat;
                }
                else 
                {
                    LOG_ERROR("Qtangents does not support format {}", vertexChannel.mDataFormat);
                }
            }
        }

        if (DumpNormalAndTangent)
        {
            for (auto itr = mVertexChannelData.begin(); itr != mVertexChannelData.end();)
            {
                if (itr->mVertexChannel == VertexChannel::Normal0)
                {
                    mVertexChannelSemanticMask &= ~static_cast<UInt32>(VertexSemantic::SemanticNormal);
                    itr = mVertexChannelData.erase(itr);
                    continue;
                }
                else if (itr->mVertexChannel == VertexChannel::Tangent0)
                {
                    mVertexChannelSemanticMask &= ~static_cast<UInt32>(VertexSemantic::SemanticTangent);
                    itr = mVertexChannelData.erase(itr);
                    continue;
                }
                itr++;
            }
        }
    }

    // mMeshPartLodStartIndex
    if (importMeshAsset.fmeshpartlodstartindex())
    {

        mMeshPartLodStartIndex.resize(importMeshAsset.fmeshpartlodstartindex()->size());
        std::copy(importMeshAsset.fmeshpartlodstartindex()->begin(), importMeshAsset.fmeshpartlodstartindex()->end(), mMeshPartLodStartIndex.begin());
    }
    else
    {
        // temp code
        mMeshPartLodStartIndex.emplace_back(0);
    }

    // MeshPart info
    mBlendShapeLODChannelMap.resize(GetLodCount());
    for (int i = 0; i < (int)importMeshAsset.fmeshpartinfo()->size(); i++)
    {
        QUICK_SCOPED_CPU_TIMING("MeshAssetData::fmeshpartinfo");
        MeshPartAssetInfo& meshInfo = mMeshPartInfo.emplace_back();
        auto fbMeshPart = (*importMeshAsset.fmeshpartinfo())[i];

        meshInfo.mMeshBound.Max = Float3(fbMeshPart->fbindinginfo()->fmax()->x(), fbMeshPart->fbindinginfo()->fmax()->y(), fbMeshPart->fbindinginfo()->fmax()->z());
        meshInfo.mMeshBound.Min = Float3(fbMeshPart->fbindinginfo()->fmin()->x(), fbMeshPart->fbindinginfo()->fmin()->y(), fbMeshPart->fbindinginfo()->fmin()->z());

        if (importMeshAsset.fcustomattributedata())
        {
            meshInfo.mCustomAttributeData.resize(importMeshAsset.fcustomattributedata()->size());
            std::copy(importMeshAsset.fcustomattributedata()->begin(), importMeshAsset.fcustomattributedata()->end(), meshInfo.mCustomAttributeData.end());

            meshInfo.mCustomAttributeInfo.mDataFlag = fbMeshPart->fcustomattributeinfo()->fdataflag();
            meshInfo.mCustomAttributeInfo.mDataOffset = fbMeshPart->fcustomattributeinfo()->fdataoffset();
            meshInfo.mCustomAttributeInfo.mDataSizeInByte = fbMeshPart->fcustomattributeinfo()->fdatasizeinbyte();
            meshInfo.mCustomAttributeInfo.mKeyNameHash = fbMeshPart->fcustomattributeinfo()->fkeynamehash();
        }

        meshInfo.mIndexCount = fbMeshPart->findexcount();
        meshInfo.mIndexStart = fbMeshPart->findexstart();
        meshInfo.mMaterialIndex = fbMeshPart->fmaterialindex();
        meshInfo.mMiscFlag = fbMeshPart->fmiscflag();
        meshInfo.mNameIndex = fbMeshPart->fnameindex();
        meshInfo.mPrimitiveCount = fbMeshPart->fprimitivecount();
        meshInfo.mPrimitiveType = (PrimitiveTopology)fbMeshPart->fprimitivetype();
        meshInfo.mRenderPriority = fbMeshPart->frenderpriority();
        meshInfo.mShadowBias = fbMeshPart->fshadowbias();
        meshInfo.mShadowNormalBias = fbMeshPart->fshadownormalbias();
        meshInfo.mVertexCount = fbMeshPart->fvertexcount();
        meshInfo.mVertexStart = fbMeshPart->fvertexstart();

        // MeshPart collision tree
        if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() == AppStartUpTypeCrossEditor) {
            if (fbMeshPart->fcollisiontree())
            {
                auto treeVec = fbMeshPart->fcollisiontree();
                const UInt32 treeNodeNum = static_cast<UInt32>(treeVec->size());
                meshInfo.mCollisionTree.resize(treeNodeNum);
                for (UInt32 j = 0; j < treeNodeNum; j++)
                {
                    auto cNode = (*treeVec)[j];

                    meshInfo.mCollisionTree[j].Index = cNode->index();
                    meshInfo.mCollisionTree[j].LeftIndex = cNode->leftindex();
                    meshInfo.mCollisionTree[j].RightIndex = cNode->rightindex();
                    meshInfo.mCollisionTree[j].Bound.Min = {(*cNode->minpos())[0], (*cNode->minpos())[1], (*cNode->minpos())[2]};
                    meshInfo.mCollisionTree[j].Bound.Max = {(*cNode->maxpos())[0], (*cNode->maxpos())[1], (*cNode->maxpos())[2]};
                    if (cNode->trianglelist())
                    {
                        meshInfo.mCollisionTree[j].TriangleList.resize(cNode->trianglelist()->size());
                        memcpy(reinterpret_cast<void*>(meshInfo.mCollisionTree[j].TriangleList.data()), cNode->trianglelist()->Data(), cNode->trianglelist()->size() * sizeof(int32_t));
                    }
                }
            }
        }

        // MeshPart blend shape info
        if (fbMeshPart->fblendshape())
        {
            if (!meshInfo.mBlendShape.Deserialize(*fbMeshPart->fblendshape()))
            {
                AssertMsg(false, "BlendShape deserialize failed for mesh part: [{}]", mMeshPartNames[meshInfo.mNameIndex]);
                return false;
            }

            CollectBlendShapeChannelInfo(i);
        }
    }

    // mMaterialNames
    if (importMeshAsset.fmaterialnames())
    {
        mMaterialNames.resize(importMeshAsset.fmaterialnames()->size());
        for (int i = 0; i < (int)importMeshAsset.fmaterialnames()->size(); i++)
        {
            auto vec = importMeshAsset.fmaterialnames();
            mMaterialNames[i].append((*vec)[i]->c_str());
        }
    }

    // mMeshPartNames
    if (importMeshAsset.fmeshpartnames())
    {
        mMeshPartNames.resize(importMeshAsset.fmeshpartnames()->size());
        for (int i = 0; i < (int)importMeshAsset.fmeshpartnames()->size(); i++)
        {
            auto vec = importMeshAsset.fmeshpartnames();
            mMeshPartNames[i].append((*vec)[i]->c_str());
        }
    }

    // mAABB
    mAABB.Max = Float3(importMeshAsset.faabb()->fmax()->x(), importMeshAsset.faabb()->fmax()->y(), importMeshAsset.faabb()->fmax()->z());
    mAABB.Min = Float3(importMeshAsset.faabb()->fmin()->x(), importMeshAsset.faabb()->fmin()->y(), importMeshAsset.faabb()->fmin()->z());

    if (mAABB.Max.x == -431602080.0f || mAABB.Max.x == -INFINITY)
    {
        QUICK_SCOPED_CPU_TIMING("MeshAssetData::CalculateWholeAABB");
        mAABB.Max = {-INFINITY, -INFINITY, -INFINITY};
        mAABB.Min = {INFINITY, INFINITY, INFINITY};
        CalculateWholeAABB();
    }

    // Physics collision
    if (importMeshAsset.fphysicscollision())
    {
        QUICK_SCOPED_CPU_TIMING("MeshAssetData::Deserialize collision");
        PhysicsEngine* physicsEngine = EngineGlobal::GetPhysicsEngine();
        if (importMeshAsset.fphysicscollision()->boxcollision())
        {
            for (const CrossSchema::PhysicsBoxCollision* coll : *importMeshAsset.fphysicscollision()->boxcollision())
            {
                mPhysicsCollision.mBoxGeometry.emplace_back(Float3{coll->position()->x(), coll->position()->y(), coll->position()->z()},
                                                            Quaternion{coll->rotate()->x(), coll->rotate()->y(), coll->rotate()->z(), coll->rotate()->w()},
                                                            Float3{coll->halfextents()->x(), coll->halfextents()->y(), coll->halfextents()->z()});
            }
        }
        if (importMeshAsset.fphysicscollision()->spherecollision())
        {
            for (const CrossSchema::PhysicsSphereCollision* coll : *importMeshAsset.fphysicscollision()->spherecollision())
            {
                mPhysicsCollision.mSphereGeometry.emplace_back(Float3{coll->position()->x(), coll->position()->y(), coll->position()->z()}, coll->radius());
            }
        }
        if (importMeshAsset.fphysicscollision()->capsulecollision())
        {
            for (const CrossSchema::PhysicsCapsuleCollision* coll : *importMeshAsset.fphysicscollision()->capsulecollision())
            {
                mPhysicsCollision.mCapsuleGeometry.emplace_back(
                    Float3{coll->position()->x(), coll->position()->y(), coll->position()->z()}, Quaternion{coll->rotate()->x(), coll->rotate()->y(), coll->rotate()->z(), coll->rotate()->w()}, coll->radius(), coll->halfHeight());
            }
        }
        if (importMeshAsset.fphysicscollision()->convexcollision())
        {
            for (const CrossSchema::PhysicsConvexCollision* coll : *importMeshAsset.fphysicscollision()->convexcollision())
            {
                std::shared_ptr<PhysicsConvexMesh> convex = physicsEngine->GetCooker()->DeserializeConvexMesh(coll->data()->data(), coll->data()->size());
                mPhysicsCollision.mConvexGeometry.emplace_back(
                    Float3{coll->position()->x(), coll->position()->y(), coll->position()->z()}, Quaternion{coll->rotate()->x(), coll->rotate()->y(), coll->rotate()->z(), coll->rotate()->w()}, std::move(convex));
            }
        }
        if (importMeshAsset.fphysicscollision()->meshcollision())
        {
            for (auto coll : *importMeshAsset.fphysicscollision()->meshcollision())
            {
                std::shared_ptr<PhysicsTriangleMesh> mesh = physicsEngine->GetCooker()->DeserializeTriangleMesh(coll->data()->data(), coll->data()->size());
                mPhysicsCollision.mMeshGeometry.emplace_back(
                    Float3{coll->position()->x(), coll->position()->y(), coll->position()->z()}, Quaternion{coll->rotate()->x(), coll->rotate()->y(), coll->rotate()->z(), coll->rotate()->w()}, std::move(mesh));
            }
        }
    }

    if (importMeshAsset.frefskeleton() == nullptr || importMeshAsset.fbindposeinv() == nullptr)
    {
        return true;
    }

    // Skinning information - RefSkeleton
    bool deserializeRefSkRe = mMeshRefSkelt.Deserialize(importMeshAsset.frefskeleton());

    // Skinning information - BindPoseInvMatrices
    size_t BoneNum = importMeshAsset.fbindposeinv()->size();
    Assert(BoneNum == mMeshRefSkelt.GetRawBoneNum());
    mBindPoseInvMat.clear();

    for (auto iBone = 0; iBone < BoneNum; iBone++)
    {
        auto invmat = (*importMeshAsset.fbindposeinv())[iBone]->val();

        Float4x4A mat(invmat->data());
        mBindPoseInvMat.push_back(SIMD::LoadFloat4x4A(&mat));
    }

    return deserializeRefSkRe;
}

bool MeshAssetData::IsSkinValid() const
{
    bool isChannelValid = GetVertexChannelData(VertexChannel::BlendIndex0) != nullptr && GetVertexChannelData(VertexChannel::BlendWeight0) != nullptr;
    bool isRefSkeltValid = mMeshRefSkelt.GetRawBoneNum() > 0;
    return isChannelValid && isRefSkeltValid;
}

bool MeshAssetData::SerializeToCrossSchema(std::vector<char>& fbBufVec, std::string ndaFilePath)
{
    using namespace CrossSchema;   // safe

    ImportMeshAssetDataT meshAssetDataT;
    bool ret = ToImportMeshAssetDataT(meshAssetDataT);
    if (!ret)
        return false;

    auto classID = ClassID(NullType);
    CrossSchema::ResourceHeader header(ASSET_MAGIC_NUMBER, 0, classID, 0, 0);
    flatbuffers::FlatBufferBuilder builder(4096);
    auto mloc = CrossSchema::CreateImportMeshAssetData(builder, &meshAssetDataT);
    auto name = PathHelper::GetBaseFileName(ndaFilePath);
    auto mloc2 = CrossSchema::CreateResourceAsset(builder, &header, builder.CreateString(name), CrossSchema::ResourceType::ImportMeshAssetData, mloc.Union());
    FinishResourceAssetBuffer(builder, mloc2);

    fbBufVec.clear();
    char* buff = (char*)builder.GetBufferPointer();

    fbBufVec.insert(fbBufVec.end(), (char*)buff, (char*)buff + builder.GetSize());
    return true;
}

bool MeshAssetData::ToImportMeshAssetDataT(CrossSchema::ImportMeshAssetDataT& meshAssetDataT)
{
    // mAABB
    std::unique_ptr<CrossSchema::MeshBoundT> aabb = std::make_unique<CrossSchema::MeshBoundT>();
    aabb->fmax = std::make_unique<CrossSchema::float3>(mAABB.Max.x, mAABB.Max.y, mAABB.Max.z);
    aabb->fmin = std::make_unique<CrossSchema::float3>(mAABB.Min.x, mAABB.Min.y, mAABB.Min.z);
    meshAssetDataT.faabb = std::move(aabb);
    meshAssetDataT.fversion = static_cast<UInt32>(mVersion);
    // mCustomAttribute
    meshAssetDataT.fcustomattributedata.resize(mCustomAttribute.size());
    std::copy(mCustomAttribute.begin(), mCustomAttribute.end(), meshAssetDataT.fcustomattributedata.begin());

    // mCustomAttributeInfo
    meshAssetDataT.fcustomattributeinfo = std::make_unique<CrossSchema::CustomAttributeInfoT>();
    meshAssetDataT.fcustomattributeinfo->fdataflag = mCustomAttributeInfo.mDataFlag;
    meshAssetDataT.fcustomattributeinfo->fdataoffset = mCustomAttributeInfo.mDataOffset;
    meshAssetDataT.fcustomattributeinfo->fdatasizeinbyte = mCustomAttributeInfo.mDataSizeInByte;
    meshAssetDataT.fcustomattributeinfo->fkeynamehash = mCustomAttributeInfo.mKeyNameHash;

    // mCustomAttributeVersion
    meshAssetDataT.fcustomattributeversion = mCustomAttributeVersion;

    // mCustomAttributeVersionFlag
    meshAssetDataT.fcustomattributeversionflag = mCustomAttributeVersionFlag;

    // mIndexStream
    meshAssetDataT.findexstream = std::make_unique<CrossSchema::IndexStreamAssetDataT>();
    meshAssetDataT.findexstream->fcount = mIndexStream.mCount;
    meshAssetDataT.findexstream->fdata.resize(mIndexStream.mData.size());
    std::copy(mIndexStream.mData.begin(), mIndexStream.mData.end(), meshAssetDataT.findexstream->fdata.begin());
    meshAssetDataT.findexstream->fis16bitindex = mIndexStream.mIs16BitIndex;

    // mMaterialNames
    for (int i = 0; i < mMaterialNames.size(); i++)
    {
        meshAssetDataT.fmaterialnames.push_back(mMaterialNames[i]);
    }

    // mMeshPartInfo
    meshAssetDataT.fmeshpartinfo.clear();
    meshAssetDataT.fmeshpartinfo.resize(0);
    for (int i = 0; i < mMeshPartInfo.size(); i++)
    {
        std::unique_ptr<CrossSchema::ImportMeshPartAssetInfoT> partAssetInfoT = std::make_unique<CrossSchema::ImportMeshPartAssetInfoT>();
        partAssetInfoT->fbindinginfo = std::make_unique<CrossSchema::MeshBoundT>();
        partAssetInfoT->fbindinginfo->fmax = std::make_unique<CrossSchema::float3>(mMeshPartInfo[i].mMeshBound.Max.x, mMeshPartInfo[i].mMeshBound.Max.y, mMeshPartInfo[i].mMeshBound.Max.z);
        partAssetInfoT->fbindinginfo->fmin = std::make_unique<CrossSchema::float3>(mMeshPartInfo[i].mMeshBound.Min.x, mMeshPartInfo[i].mMeshBound.Min.y, mMeshPartInfo[i].mMeshBound.Min.z);
        partAssetInfoT->fcustomattributedata.resize(mMeshPartInfo[i].mCustomAttributeData.size());
        std::copy(mMeshPartInfo[i].mCustomAttributeData.begin(), mMeshPartInfo[i].mCustomAttributeData.end(), partAssetInfoT->fcustomattributedata.begin());
        partAssetInfoT->fcustomattributeinfo = std::make_unique<CrossSchema::CustomAttributeInfoT>();
        partAssetInfoT->fcustomattributeinfo->fdataflag = mMeshPartInfo[i].mCustomAttributeInfo.mDataFlag;
        partAssetInfoT->fcustomattributeinfo->fdataoffset = mMeshPartInfo[i].mCustomAttributeInfo.mDataOffset;
        partAssetInfoT->fcustomattributeinfo->fdatasizeinbyte = mMeshPartInfo[i].mCustomAttributeInfo.mDataSizeInByte;
        partAssetInfoT->fcustomattributeinfo->fkeynamehash = mMeshPartInfo[i].mCustomAttributeInfo.mKeyNameHash;

        partAssetInfoT->findexcount = mMeshPartInfo[i].mIndexCount;
        partAssetInfoT->findexstart = mMeshPartInfo[i].mIndexStart;
        partAssetInfoT->fmaterialindex = mMeshPartInfo[i].mMaterialIndex;
        partAssetInfoT->fmiscflag = mMeshPartInfo[i].mMiscFlag;
        partAssetInfoT->fnameindex = mMeshPartInfo[i].mNameIndex;
        partAssetInfoT->fprimitivecount = mMeshPartInfo[i].mPrimitiveCount;
        partAssetInfoT->fprimitivetype = (uint32_t)mMeshPartInfo[i].mPrimitiveType;
        partAssetInfoT->frenderpriority = mMeshPartInfo[i].mRenderPriority;
        partAssetInfoT->fshadowbias = mMeshPartInfo[i].mShadowBias;
        partAssetInfoT->fshadownormalbias = mMeshPartInfo[i].mShadowNormalBias;
        partAssetInfoT->fvertexcount = mMeshPartInfo[i].mVertexCount;
        partAssetInfoT->fvertexstart = mMeshPartInfo[i].mVertexStart;

        // Mesh collision tree
        if (mMeshPartInfo[i].mCollisionTree.size() > 0)
        {
            for (int j = 0; j < mMeshPartInfo[i].mCollisionTree.size(); j++)
            {
                std::unique_ptr<CrossSchema::CollisionNodeT> c = std::make_unique<CrossSchema::CollisionNodeT>();
                c->index = mMeshPartInfo[i].mCollisionTree[j].Index;
                c->leftindex = mMeshPartInfo[i].mCollisionTree[j].LeftIndex;
                c->rightindex = mMeshPartInfo[i].mCollisionTree[j].RightIndex;
                c->maxpos.clear();
                c->maxpos.push_back(mMeshPartInfo[i].mCollisionTree[j].Bound.Max.x);
                c->maxpos.push_back(mMeshPartInfo[i].mCollisionTree[j].Bound.Max.y);
                c->maxpos.push_back(mMeshPartInfo[i].mCollisionTree[j].Bound.Max.z);
                c->minpos.clear();
                c->minpos.push_back(mMeshPartInfo[i].mCollisionTree[j].Bound.Min.x);
                c->minpos.push_back(mMeshPartInfo[i].mCollisionTree[j].Bound.Min.y);
                c->minpos.push_back(mMeshPartInfo[i].mCollisionTree[j].Bound.Min.z);

                c->trianglelist.resize(mMeshPartInfo[i].mCollisionTree[j].TriangleList.size());
                std::copy(mMeshPartInfo[i].mCollisionTree[j].TriangleList.begin(), mMeshPartInfo[i].mCollisionTree[j].TriangleList.end(), c->trianglelist.begin());

                partAssetInfoT->fcollisiontree.push_back(std::move(c));
            }
        }

        // Mesh blend shape
        const auto& meshBlendShape = mMeshPartInfo[i].mBlendShape;
        if (meshBlendShape.HasBlendShape())
        {
            std::unique_ptr<CrossSchema::ImportBlendShapeInfoT> fbBlendShape = std::make_unique<CrossSchema::ImportBlendShapeInfoT>();
            fbBlendShape->fvertexchannelsemanticmask = meshBlendShape.VertexChannelSemanticMask;

            fbBlendShape->fvertexchanneldata.reserve(meshBlendShape.VertexChannelData.size());
            for (const auto& curVertexChannel : meshBlendShape.VertexChannelData)
            {
                auto& fbVertexChannel = fbBlendShape->fvertexchanneldata.emplace_back(std::make_unique<CrossSchema::VertexChannelAssetDataT>());

                fbVertexChannel->fdata.resize(curVertexChannel.mData.size());
                std::copy(curVertexChannel.mData.begin(), curVertexChannel.mData.end(), fbVertexChannel->fdata.begin());

                fbVertexChannel->fdataformat = static_cast<UInt32>(curVertexChannel.mDataFormat);
                fbVertexChannel->ffrequency = curVertexChannel.mFrequency;
                fbVertexChannel->fmiscflag = static_cast<UInt16>(curVertexChannel.mMiscFlag);
                fbVertexChannel->freserve0 = static_cast<UInt16>(curVertexChannel.mReserve0);
                fbVertexChannel->freserve1 = static_cast<UInt16>(curVertexChannel.mReserve1);
                fbVertexChannel->fstream = curVertexChannel.mStream;
                fbVertexChannel->fstreamoffset = curVertexChannel.mStreamOffset;
                fbVertexChannel->fstride = curVertexChannel.mStride;
                fbVertexChannel->fvertexchannel = static_cast<UInt32>(curVertexChannel.mVertexChannel);
            }

            fbBlendShape->fchannelinfos.reserve(meshBlendShape.ChannelShapeData.size());
            for (auto channelIdx = 0; channelIdx < meshBlendShape.ChannelShapeData.size(); ++channelIdx)
            {
                const auto& curChannelInfo = meshBlendShape.ChannelShapeData[channelIdx];
                auto& fbBlendShapeChannel = fbBlendShape->fchannelinfos.emplace_back(std::make_unique<CrossSchema::ImportBlendShapeChannelInfoT>());

                fbBlendShapeChannel->fname = std::string(meshBlendShape.ChannelNameData[channelIdx].GetCString());
                fbBlendShapeChannel->fnormalizedfullweights.resize(curChannelInfo.DeltaShapes.size());
                std::copy(curChannelInfo.NormalizedFullWeights.begin(), curChannelInfo.NormalizedFullWeights.end(), fbBlendShapeChannel->fnormalizedfullweights.begin());

                fbBlendShapeChannel->fdeltashapes.reserve(curChannelInfo.DeltaShapes.size());
                for (auto shapeIdx = 0; shapeIdx < curChannelInfo.DeltaShapes.size(); ++shapeIdx)
                {
                    const auto& curDeltaShape = curChannelInfo.DeltaShapes[shapeIdx];
                    auto fbDeltaShape = std::make_unique<CrossSchema::ImportDeltaShapeInfoT>();

                    fbDeltaShape->fvertexstart = curDeltaShape.mVertexStart;
                    fbDeltaShape->fvertexcount = curDeltaShape.mVertexCount;

                    fbBlendShapeChannel->fdeltashapes.emplace_back(std::move(fbDeltaShape));
                }
            }

            partAssetInfoT->fblendshape = std::move(fbBlendShape);
        }

        meshAssetDataT.fmeshpartinfo.push_back(std::move(partAssetInfoT));
    }

    // mMeshPartLodStartIndex
    meshAssetDataT.fmeshpartlodstartindex.resize(mMeshPartLodStartIndex.size());
    std::copy(mMeshPartLodStartIndex.begin(), mMeshPartLodStartIndex.end(), meshAssetDataT.fmeshpartlodstartindex.begin());

    // mMeshPartNames
    for (int i = 0; i < mMeshPartNames.size(); i++)
    {
        meshAssetDataT.fmeshpartnames.push_back(mMeshPartNames[i]);
    }

    // mName
    meshAssetDataT.fname = mName.length() <= 0 ? "unknow" : mName;

    // mPrimitiveCount
    meshAssetDataT.fprimitivecount = mPrimitiveCount;

    // mVersion
    meshAssetDataT.fversion = static_cast<UInt32>(mVersion);

    // mVertexChannelData
    for (int i = 0; i < mVertexChannelData.size(); i++)
    {
        std::unique_ptr<CrossSchema::VertexChannelAssetDataT> v = std::make_unique<CrossSchema::VertexChannelAssetDataT>();
        v->fdata.resize(mVertexChannelData[i].mData.size());
        std::copy(mVertexChannelData[i].mData.begin(), mVertexChannelData[i].mData.end(), v->fdata.begin());

        v->fdataformat = (uint32_t)mVertexChannelData[i].mDataFormat;
        v->ffrequency = mVertexChannelData[i].mFrequency;
        v->fmiscflag = (uint16_t)mVertexChannelData[i].mMiscFlag;
        v->freserve0 = (uint16_t)mVertexChannelData[i].mReserve0;
        v->freserve1 = (uint16_t)mVertexChannelData[i].mReserve1;
        v->fstream = mVertexChannelData[i].mStream;
        v->fstreamoffset = mVertexChannelData[i].mStreamOffset;
        v->fvertexchannel = (uint32_t)mVertexChannelData[i].mVertexChannel;
        v->fstride = (uint16_t)mVertexChannelData[i].mStride;
        meshAssetDataT.fvertexchanneldata.push_back(std::move(v));
    }

    // mVertexChannelSemanticMask
    meshAssetDataT.fvertexchannelsemanticmask = mVertexChannelSemanticMask;

    // mVertexCount
    meshAssetDataT.fvertexcount = mVertexCount;

    // PhysicsCollision
    meshAssetDataT.fphysicscollision = std::make_unique<CrossSchema::PhysicsCollisionT>();
    for (const auto& boxCollision : mPhysicsCollision.mBoxGeometry)
    {
        auto boxPtr = std::make_unique<CrossSchema::PhysicsBoxCollisionT>();
        boxPtr->position = std::make_unique<CrossSchema::float3>(boxCollision.position.x, boxCollision.position.y, boxCollision.position.z);
        boxPtr->rotate = std::make_unique<CrossSchema::float4>(boxCollision.rotate.x, boxCollision.rotate.y, boxCollision.rotate.z, boxCollision.rotate.w);
        boxPtr->halfextents = std::make_unique<CrossSchema::float3>(boxCollision.halfExtents.x, boxCollision.halfExtents.y, boxCollision.halfExtents.z);
        meshAssetDataT.fphysicscollision->boxcollision.push_back(std::move(boxPtr));
    }
    for (const auto& sphereCollision : mPhysicsCollision.mSphereGeometry)
    {
        auto spherePtr = std::make_unique<CrossSchema::PhysicsSphereCollisionT>();
        spherePtr->position = std::make_unique<CrossSchema::float3>(sphereCollision.position.x, sphereCollision.position.y, sphereCollision.position.z);
        spherePtr->radius = sphereCollision.radius;
        meshAssetDataT.fphysicscollision->spherecollision.push_back(std::move(spherePtr));
    }
    for (const auto& capsuleCollision : mPhysicsCollision.mCapsuleGeometry)
    {
        auto capsulePtr = std::make_unique<CrossSchema::PhysicsCapsuleCollisionT>();
        capsulePtr->position = std::make_unique<CrossSchema::float3>(capsuleCollision.position.x, capsuleCollision.position.y, capsuleCollision.position.z);
        capsulePtr->rotate = std::make_unique<CrossSchema::float4>(capsuleCollision.rotate.x, capsuleCollision.rotate.y, capsuleCollision.rotate.z, capsuleCollision.rotate.w);
        capsulePtr->radius = capsuleCollision.radius;
        capsulePtr->halfHeight = capsuleCollision.halfHeight;
        meshAssetDataT.fphysicscollision->capsulecollision.push_back(std::move(capsulePtr));
    }
    for (const auto& convexCollision : mPhysicsCollision.mConvexGeometry)
    {
        auto convexPtr = std::make_unique<CrossSchema::PhysicsConvexCollisionT>();
        convexPtr->position = std::make_unique<CrossSchema::float3>(convexCollision.position.x, convexCollision.position.y, convexCollision.position.z);
        convexPtr->rotate = std::make_unique<CrossSchema::float4>(convexCollision.rotate.x, convexCollision.rotate.y, convexCollision.rotate.z, convexCollision.rotate.w);
        PhysicsEngine* physicsEngine = EngineGlobal::GetPhysicsEngine();
        convexPtr->data = physicsEngine->GetCooker()->SerializeConvexMesh(convexCollision.mesh.get());
        meshAssetDataT.fphysicscollision->convexcollision.push_back(std::move(convexPtr));
    }
    for (const auto& meshCollision : mPhysicsCollision.mMeshGeometry)
    {
        auto meshPtr = std::make_unique<CrossSchema::PhysicsMeshCollisionT>();
        meshPtr->position = std::make_unique<CrossSchema::float3>(meshCollision.position.x, meshCollision.position.y, meshCollision.position.z);
        meshPtr->rotate = std::make_unique<CrossSchema::float4>(meshCollision.rotate.x, meshCollision.rotate.y, meshCollision.rotate.z, meshCollision.rotate.w);
        PhysicsEngine* physicsEngine = EngineGlobal::GetPhysicsEngine();
        meshPtr->data = physicsEngine->GetCooker()->SerializeTriangleMesh(meshCollision.mesh.get());
        meshAssetDataT.fphysicscollision->meshcollision.push_back(std::move(meshPtr));
    }

    // Reference Skeleton
    meshAssetDataT.frefskeleton = std::make_unique<CrossSchema::ImportRefSkeletonT>();
    meshAssetDataT.frefskeleton->name = fmt::format("{}_SK" , GetName());
    meshAssetDataT.frefskeleton->skelteon.reserve(mMeshRefSkelt.GetRawBoneNum());
    meshAssetDataT.fbindposeinv.reserve(mMeshRefSkelt.GetRawBoneNum());
    const auto& bones = mMeshRefSkelt.GetRefBoneNodes();
    for (int index = 0; index < mMeshRefSkelt.GetRawBoneNum(); index++)
    {
        const auto& bone = bones[index];
        auto& importNode = meshAssetDataT.frefskeleton->skelteon.emplace_back(std::make_unique<CrossSchema::ImportBoneNodeT>());
        importNode->name = bone.Name.GetCString();
        importNode->boneid = index;
        importNode->parentid = bone.ParentIndex;

        // bind pose inverse matrix should not be stored in Skeleton but in MeshAssetData
        Float4x4 bindPosInvMat;
        SIMD::StoreFloat4x4(&bindPosInvMat, mBindPoseInvMat[index]);
        importNode->bindposeinv = std::vector<float>(bindPosInvMat.data(), bindPosInvMat.data() + 16);

        const auto& rawRefBonePose = mMeshRefSkelt.GetRawRefBonePoseWorldMatirx()[index];
        const auto& rawRefBoneBind = mMeshRefSkelt.GetRawRefBoneSymWorldMatrix()[index];
        importNode->worldmatrix = std::vector<float>(rawRefBonePose.data(), rawRefBonePose.data() + 16);
        importNode->bindposedef = std::vector<float>(rawRefBoneBind.data(), rawRefBoneBind.data() + 16);

        // Bind Pose Inverse Matrix
        auto& invmat = meshAssetDataT.fbindposeinv.emplace_back(std::make_unique<CrossSchema::invmatrixT>());
        invmat->val = std::vector<float>(bindPosInvMat.data(), bindPosInvMat.data() + 16);
    }

    return true;
}

cross::BoundingSphere MeshAssetData::CalculateBoundingSphere(UInt32 lod) 
{
    UInt32 meshPartStartIndex, subSectionCount;
    GetMeshLodInfo(lod, meshPartStartIndex, subSectionCount);
    BoundingSphere mergeSphere;
    for (UInt32 i = 0; i < subSectionCount; ++i)
    {
        auto boundingSphere = CalculateBoundingSphere(lod, i);
        if (i == 0)
        {
            mergeSphere = boundingSphere;
        }
        else
        {
            BoundingSphere::CreateMerged(mergeSphere, boundingSphere, mergeSphere);
        }
    }
    return mergeSphere;
}

cross::BoundingSphere MeshAssetData::CalculateBoundingSphere(UInt32 lod, UInt32 meshPartIndex) 
{
    UInt32 meshPartStartIndex, subSectionCount;
    GetMeshLodInfo(lod, meshPartStartIndex, subSectionCount);
    auto& meshPartInfo = GetMeshPartInfo(meshPartStartIndex + meshPartIndex);

    VertexChannelAssetData& vertpos = GetChannelAssetData(VertexChannel::Position0);
    // UInt32 count = static_cast<UInt32>(vertpos.mData.size()) / static_cast<UInt32>(vertpos.mStride);
    UInt32 count = meshPartInfo.mVertexCount;

    // Find the min & max points indices along each axis.
    uint32_t minAxis[3] = {0, 0, 0};
    uint32_t maxAxis[3] = {0, 0, 0};
    Float3 minPoint, maxPoint;
    memcpy(minPoint.data(), vertpos.mData.data(), vertpos.mStride);
    memcpy(maxPoint.data(), vertpos.mData.data(), vertpos.mStride);
    auto vertexData = vertpos.mData.data();

    for (UInt32 i = meshPartInfo.mVertexStart; i < meshPartInfo.mVertexStart + count; i++)
    {
        float* point = reinterpret_cast<float*>(vertexData + i * vertpos.mStride);

        for (UInt32 j = 0; j < 3; ++j)
        {
            float* min = reinterpret_cast<float*>(vertexData + minAxis[j] * vertpos.mStride);
            float* max = reinterpret_cast<float*>(vertexData + maxAxis[j] * vertpos.mStride);

            minAxis[j] = point[j] < min[j] ? i : minAxis[j];
            maxAxis[j] = point[j] > max[j] ? i : maxAxis[j];
        }
    }

    // Find axis with maximum span.
    float distSqMax = 0;
    UInt32 axis = 0;

    for (UInt32 i = 0; i < 3u; ++i)
    {
        Float3 min;
        Float3 max;
        memcpy(min.data(), vertexData + minAxis[i] * vertpos.mStride, vertpos.mStride);
        memcpy(max.data(), vertexData + maxAxis[i] * vertpos.mStride, vertpos.mStride);

        float distSq = (max - min).LengthSquared();
        if (distSq > distSqMax)
        {
            distSqMax = distSq;
            axis = i;
        }
    }

    // Calculate an initial starting center point & radius.
    Float3 p1;
    Float3 p2;
    memcpy(p1.data(), vertexData + minAxis[axis] * vertpos.mStride, vertpos.mStride);
    memcpy(p2.data(), vertexData + maxAxis[axis] * vertpos.mStride, vertpos.mStride);

    Float3 center = (p1 + p2) * 0.5f;
    float radius = (p2 - p1).Length() * 0.5f;
    float radiusSq = radius * radius;
    // Add all our points to bounding sphere expanding radius & recalculating center point as necessary.
    for (uint32_t i = meshPartInfo.mVertexStart; i < meshPartInfo.mVertexStart + count; ++i)
    {
        Float3 point;
        memcpy(point.data(), vertexData + i * vertpos.mStride, vertpos.mStride);
        float distSq = (point - center).LengthSquared();

        if (distSq > radiusSq)
        {
            float dist = sqrt(distSq);
            float k = (radius / dist) * 0.5f + 0.5f;

            center = center * k + point * (1 - k);
            radius = (radius + dist) * 0.5f;
        }
    }

    // Populate a single XMVECTOR with center & radius data.
    BoundingSphere shpere(center, radius);
    return shpere;
}

}
