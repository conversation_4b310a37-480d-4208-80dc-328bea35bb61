#include "LevelSeqAnimationTrack.h"

#include "Resource/AssetStreaming.h"
#include "Resource/ResourceManager.h"
#include "CrossBase/Serialization/SerializeTemplateBase.h"

namespace cross {
using namespace anim;

LevelSeqAnimationTrack::LevelSeqAnimationTrack()
{
    static SInt32 InstanceID;
    mInstanceID = InstanceID++;
}

void* LevelSeqAnimationTrack::AddNewAnimSection(float startTime, const std::string& animSequencePath)
{
    auto animSeqResPtr = TypeCast<AnimSequenceRes>(gAssetStreamingManager->LoadSynchronously(animSequencePath));
    if (animSeqResPtr)
    {
        auto pSection = AnimationSections.emplace_back(new LevelSeqAnimationSection());
        pSection->SectionName = PathHelper::GetBaseFileName(animSeqResPtr->GetName());
        pSection->SectionStart = startTime;
        pSection->SequenceLength = animSeqResPtr->GetStreamingAnimation()->DurationInSec;
        pSection->SectionEnd = startTime + pSection->SequenceLength;
        pSection->AnimSequencePath = gResourceMgr.ConvertPathToGuid(animSequencePath);
        return pSection.get();
    }
    return nullptr;
}

bool LevelSeqAnimationTrack::RemoveAnimSectionByID(SInt32 sectionID)
{
    auto it = std::find_if(AnimationSections.begin(), AnimationSections.end(), [sectionID](const std::shared_ptr<LevelSeqAnimationSection>& section) { return section->GetSectionID() == sectionID; });

    if (it != AnimationSections.end())
    {
        AnimationSections.erase(it);
        return true;
    }
    else
    {
        return false;
    }
}

void LevelSeqAnimationTrack::Clear()
{
    AnimationSections.clear();
}

UInt32 LevelSeqAnimationTrack::AnimSectionCount() const
{
    return static_cast<UInt32>(AnimationSections.size());
}

void* LevelSeqAnimationTrack::GetAnimSectionAt(UInt32 index) const
{
    if (index < AnimSectionCount())
    {
        return AnimationSections[index].get();
    }
    return nullptr;
}

void* LevelSeqAnimationTrack::GetAnimSectionByID(SInt32 sectionID) const
{
    auto it = std::find_if(AnimationSections.begin(), AnimationSections.end(), [sectionID](const std::shared_ptr<LevelSeqAnimationSection>& section) { return section->GetSectionID() == sectionID; });
    if (it != AnimationSections.end())
    {
        return it->get();
    }
    return nullptr;
}

// void* LevelSeqAnimationTrackList::AddNewTrack()
// {
//     auto pTrack = TrackList.emplace_back(new LevelSeqAnimationTrack());
//     return pTrack.get();
// }
//
// void* LevelSeqAnimationTrackList::GetTrackAt(UInt32 index)
// {
//     if (index < TrackList.size())
//     {
//         return TrackList[index].get();
//     }
//     return nullptr;
// }
//
// void* LevelSeqAnimationTrackList::GetTrackByID(SInt32 trackID)
// {
//     auto it = std::find_if(TrackList.begin(), TrackList.end(), [trackID](const std::shared_ptr<LevelSeqAnimationTrack>& track) { return track->GetTrackID() == trackID; });
//     if (it != TrackList.end())
//     {
//         return it->get();
//     }
//     return nullptr;
// }
//
// bool LevelSeqAnimationTrackList::RemoveTrackByID(SInt32 trackID)
// {
//     auto it = std::find_if(TrackList.begin(), TrackList.end(), [trackID](const std::shared_ptr<LevelSeqAnimationTrack>& track) { return track->GetTrackID() == trackID; });
//     if (it != TrackList.end())
//     {
//         TrackList.erase(it);
//         return true;
//     }
//     return false;
// }
//
// void LevelSeqAnimationTrackList::Clear()
// {
//     TrackList.clear();
// }
//
// UInt32 LevelSeqAnimationTrackList::TrackCount() const
// {
//     return static_cast<UInt32>(TrackList.size());
// }
}   // namespace cross