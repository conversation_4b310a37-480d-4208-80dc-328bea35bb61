#pragma once
#include "CrossBase/Platform/PlatformTypes.h"
#include <memory>
#include <vector>

namespace cross::Stellar<PERSON>esh
{
    class GraphPartitioner final
    {
    public:
        // Represent a graph using metis library compressed storage format(CSR)
        struct Graph final
        {
            SInt32 mNum{0};
            std::vector<SInt32> mAdjacencyIndex;   // size of mNum + 1
            std::vector<SInt32> mAdjacency;        // size of nEdge * 2
            // std::vector<int> mAdjacencyCost;    // size of nEdge * 2
        };

        struct Range
        {
            UInt32 mBegin{~0u};
            UInt32 mEnd{0u};

            bool operator<(Range const& r) const
            {
                return mBegin < r.mBegin;
            }
        };

        GraphPartitioner(Graph graph);
        void PartitionGraph(SInt32 minPartitionSize, SInt32 maxPartitionSize);

        std::vector<Range>  mRanges;
        std::vector<UInt32> mIndexes;

    private:
        struct Impl;
        std::unique_ptr<Impl> pImpl;
    };
}

