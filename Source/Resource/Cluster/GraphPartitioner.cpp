#include "GraphPartitioner.h"
#include "metis.h"
#include "CrossBase/Math/CrossMath.h"

namespace cross::StellarMesh
{

struct GraphPartitioner::Impl
{
    Graph mGraph{};
    GraphPartitioner* mData{};

    std::vector<SInt32> mPartitionIDs{};
    std::vector<SInt32> mSwappedWith{};

    UInt32 mNumTriangles{};
    SInt32 mMaxPartitionSize{};
    SInt32 mMinPartitionSize{};

    Impl(Graph g, GraphPartitioner* data)
        : mGraph(std::move(g))
        , mData(data)
    {}

    void ClearGraph(Graph& graph)
    {
        std::vector<SInt32>().swap(graph.mAdjacencyIndex);
        std::vector<SInt32>().swap(graph.mAdjacency);
        graph.mNum = 0;
    }

    void BisectGraph(Graph& graph, std::array<Graph, 2>& childGraphs, SInt32 begin)
    {
        auto addPartition = [&] (UInt32 begin, UInt32 num) {
            mData->mRanges.emplace_back(begin, begin + num);
        };

        if (graph.mNum <= mMaxPartitionSize)
        {
            addPartition(begin, graph.mNum);
            return;
        }

        const SInt32 targetPartitionSize = (mMaxPartitionSize + mMinPartitionSize) / 2;
        const SInt32 targetNumPartitions = std::max(2, math::DivideAndRoundNearest(graph.mNum, targetPartitionSize));

        SInt32 numConstraints = 1;
        SInt32 numParts = 2;
        SInt32 edgeCuts = 0;

        float partitionWeight[] = {
                float(targetNumPartitions / 2) / targetNumPartitions,
                1.0f - float(targetNumPartitions / 2) / targetNumPartitions
            };

        idx_t Options[ METIS_NOPTIONS ];
	    METIS_SetDefaultOptions( Options );

	    bool bLoose = targetNumPartitions >= 128 || mMaxPartitionSize / mMinPartitionSize > 1;
        bool bSlow = graph.mNum < 4096;

        Options[ METIS_OPTION_UFACTOR ] = bLoose ? 200 : 1;
        //Options[ METIS_OPTION_NCUTS ] = graph.mNum < 1024 ? 8 : (graph.mNum < 4096 ? 4 : 1);
        //Options[ METIS_OPTION_NCUTS ] = bSlow ? 4 : 1;
        //Options[ METIS_OPTION_NITER ] = bSlow ? 20 : 10;
        //Options[ METIS_OPTION_IPTYPE ] = METIS_IPTYPE_RANDOM;
        //Options[ METIS_OPTION_MINCONN ] = 1;

        {
            // TODO: METIS is not thread-safe, so we need to lock it.
            // but why UE can use it in parallel?
            static std::mutex metisMutex{};
            std::lock_guard<std::mutex> lock(metisMutex);

            int res = METIS_PartGraphRecursive(
                &graph.mNum,
                &numConstraints,
                graph.mAdjacencyIndex.data(),
                graph.mAdjacency.data(),
                nullptr,
                nullptr,
                nullptr,
                &numParts,
                partitionWeight,
                nullptr,
                Options,
                &edgeCuts,
                mPartitionIDs.data() + begin
            );

            Assert(res == METIS_OK);
        }

        // Swap indexes array to complete the partition.
        // Make sure that triangles with partition id 0 is on the front side, triangles with partition id 1 is on the back side.
        // mSwappedWith array is used to record swap ops
        SInt32 l = begin, r = begin + graph.mNum - 1;
        while(l <= r)
        {
            while (l <= r && mPartitionIDs[l] == 0)
            {
                mSwappedWith[l] = l;
                l++;
            }
            while (l <= r && mPartitionIDs[r] == 1)
            {
                mSwappedWith[r] = r;
                r--;
            }

            if (l < r)
            {
                std::swap(mData->mIndexes[l], mData->mIndexes[r]);

                mSwappedWith[l] = r;
                mSwappedWith[r] = l;
                l++;
                r--;
            }
        }

        SInt32 split = l;

        SInt32 num[2];
        num[0] = split - begin;
        num[1] = graph.mNum - num[0];

        Assert(num[0] > 1 && num[1] > 1);

        if (num[0] <= mMaxPartitionSize && num[1] <= mMaxPartitionSize)
        {
            addPartition(begin, num[0]);
            addPartition(split, num[1]);
        }
        else
        {
            for (SInt32 i = 0; i < 2; i++)
            {
                childGraphs[i].mAdjacency.reserve(graph.mAdjacency.size() >> 1ull);
                childGraphs[i].mAdjacencyIndex.reserve(num[i] + 1);
                childGraphs[i].mNum = num[i];
            }

            for (SInt32 i = 0; i < graph.mNum; i++)
            {
                auto& childGraph = childGraphs[i >= childGraphs[0].mNum];
                SInt32 childBegin = i >= childGraphs[0].mNum ? split : begin;

                childGraph.mAdjacencyIndex.emplace_back(static_cast<SInt32>(childGraph.mAdjacency.size()));
                SInt32 orgIndex = mSwappedWith[begin + i] - begin;
                for (SInt32 adjIndex = graph.mAdjacencyIndex[orgIndex]; adjIndex < graph.mAdjacencyIndex[orgIndex + 1]; adjIndex++)
                {
                    SInt32 adj = graph.mAdjacency[adjIndex];

                    // remap to child
                    adj = mSwappedWith[adj + begin] - childBegin;

                    if (0 <= adj && adj < childGraph.mNum)
                    {
                        childGraph.mAdjacency.emplace_back(adj);
                    }
                }
            }
            childGraphs[0].mAdjacencyIndex.emplace_back(static_cast<SInt32>(childGraphs[0].mAdjacency.size()));
            childGraphs[1].mAdjacencyIndex.emplace_back(static_cast<SInt32>(childGraphs[1].mAdjacency.size()));
        }
    }

    void RecursiveBisectGraph(Graph graph, SInt32 begin)
    {
        std::array<Graph, 2> childGraphs{};
        BisectGraph(graph, childGraphs, begin);
        ClearGraph(graph);

        if (childGraphs[0].mNum > 0 && childGraphs[1].mNum > 0)
        {
            auto num0 = childGraphs[0].mNum;
            RecursiveBisectGraph(std::move(childGraphs[0]), begin);
            RecursiveBisectGraph(std::move(childGraphs[1]), begin + num0);
        }
        else if (childGraphs[0].mNum > 0 || childGraphs[1].mNum > 0)
        {
            Assert(false);
        }
    }
};

GraphPartitioner::GraphPartitioner(Graph graph)
    : pImpl(std::make_unique<Impl>(std::move(graph), this))
{
    pImpl->mNumTriangles = graph.mNum;

    mIndexes.resize(pImpl->mNumTriangles);
    for (UInt32 i = 0; i < pImpl->mNumTriangles; i++)
    {
        mIndexes[i] = i;
    }
}

void GraphPartitioner::PartitionGraph(SInt32 minPartitionSize, SInt32 maxPartitionSize)
{
    pImpl->mMinPartitionSize = minPartitionSize;
    pImpl->mMaxPartitionSize = maxPartitionSize;
    pImpl->mPartitionIDs.resize(pImpl->mNumTriangles);
    pImpl->mSwappedWith.resize(pImpl->mNumTriangles);
    SInt32 NumPartitionsExpected = (pImpl->mGraph.mNum + minPartitionSize - 1) / minPartitionSize;
    mRanges.reserve(NumPartitionsExpected);

    pImpl->RecursiveBisectGraph(std::move(pImpl->mGraph), 0);

    pImpl->mPartitionIDs.clear();
    pImpl->mSwappedWith.clear();

    std::sort(mRanges.begin(), mRanges.end());
}

}

