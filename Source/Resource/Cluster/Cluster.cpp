#include "Cluster.h"

#include <unordered_map>

namespace
{
constexpr cross::Float3 upVector{0.0f, 1.0f, 0.0f};
constexpr cross::Float3 forwardVector{1.0f, 0.0f, 0.0f};
constexpr float fltThreshold = 1e12f;

void SanitizeFloat(float& x, float minValue, float maxValue, float defaultValue)
{
    if (x >= minValue && x <= maxValue)
        ;
    else if (x < minValue)
        x = minValue;
    else if (x > maxValue)
        x = maxValue;
    else
        x = defaultValue;
}

void SanitizeVector(cross::Float3& v, float maxValue, cross::Float3 defaultValue)
{
    if (!(v.x >= -maxValue && v.x <= maxValue &&
          v.y >= -maxValue && v.y <= maxValue &&
          v.z >= -maxValue && v.z <= maxValue))
    {
        v = defaultValue;
    }
}

}


namespace cross::StellarMesh
{
Cluster::Cluster(MeshStreamData const& inMesh, std::vector<SInt32> const& inMaterialIndexed, std::span<UInt32> triangleIndexes, Adjacency const& adjacency)
{
    auto const& inVerts = inMesh.mVertexAttribute;
    auto const& inIndexes = inMesh.mIndexes;

	mHasTangents = !inVerts.mTangent.empty();
    mHasColors   = !inVerts.mColor.empty();

	mNumTris = static_cast<UInt32>(triangleIndexes.size());

    mNumTexCoords = inVerts.mUV0.empty() ? 0 : (inVerts.mUV1.empty() ? 1 : 2);

    std::unordered_map<UInt32, UInt32> oldToNewIndex;

    for (UInt32 i = 0; i < mNumTris; ++i)
    {
        UInt32 triIndex = triangleIndexes[i];

        for (UInt32 j = 0; j < 3; ++j)
        {
            UInt32 oldIndex = inIndexes[triIndex * 3 + j];
            auto newIndexIt = oldToNewIndex.find(oldIndex);
            UInt32 newIndex = newIndexIt == oldToNewIndex.end() ? ~0u : newIndexIt->second;

            if (newIndex == ~0u)
            {
                newIndex = mNumVerts++;
                oldToNewIndex[oldIndex] = newIndex;

                mMeshData.mVertexAttribute.mPosition.emplace_back(inVerts.mPosition[oldIndex]);
                mMeshData.mVertexAttribute.mNormal.emplace_back(inVerts.mNormal[oldIndex]);

                if (mHasTangents)
                {
                    mMeshData.mVertexAttribute.mTangent.emplace_back(inVerts.mTangent[oldIndex]);
                    // TODO: BiNormal
                }

                if (mHasColors)
                {
                    mMeshData.mVertexAttribute.mColor.emplace_back(inVerts.mColor[oldIndex]);
                }

                switch (mNumTexCoords)
                {
                case 2:
                    mMeshData.mVertexAttribute.mUV1.emplace_back(inVerts.mUV1[oldIndex]);
                    [[fallthrough]];
                case 1:
                    mMeshData.mVertexAttribute.mUV0.emplace_back(inVerts.mUV0[oldIndex]);
                    [[fallthrough]];
                default:
                    break;
                }

                //if (mNumTexCoords)
                //{
                //    //EmplaceFloat2ToVerts(inVerts.mUv[oldIndex]);
                //}
            }

            mMeshData.mIndexes.emplace_back(newIndex);
            
            // TODO: external edges
            //SInt32 edgeIndex = triIndex * 3 + j;
            //SInt32 adjCount = 0;

            //adjacency.ForEach(edgeIndex, [&](SInt32 edgeIndex0, SInt32 adjIndex) {
            //    UInt32 adjTriIndex = graphPartitioner.mSortedTo[adjIndex / 3];
            //    if (adjTriIndex < triBegin && adjTriIndex >= triEnd)
            //    {
            //        adjCount++;
            //    }
            //});

            //mExternalEdges.emplace_back(adjCount);
            //mNumExternalEdges += adjCount != 0;
        }

        mMaterialIndexes.emplace_back(inMaterialIndexed[triIndex]);
    }

    SanitizeVertexData();
}
void Cluster::SanitizeVertexData()
{
    for (auto& position : mMeshData.mVertexAttribute.mPosition)
    {
        SanitizeFloat(position.x, -fltThreshold, fltThreshold, 0.0f);
        SanitizeFloat(position.y, -fltThreshold, fltThreshold, 0.0f);
        SanitizeFloat(position.z, -fltThreshold, fltThreshold, 0.0f);
    }

    for (auto& normal : mMeshData.mVertexAttribute.mNormal)
    {
        SanitizeVector(normal, fltThreshold, upVector);
    }

    for (auto& tangent : mMeshData.mVertexAttribute.mTangent)
    {
        auto& realTanget = *reinterpret_cast<cross::Float3*>(&tangent);
        SanitizeVector(realTanget, fltThreshold, forwardVector);
    }

    for (auto& color : mMeshData.mVertexAttribute.mColor)
    {
        SanitizeFloat(color.x, 0.0f, 1.0f, 1.0f);
        SanitizeFloat(color.y, 0.0f, 1.0f, 1.0f);
        SanitizeFloat(color.z, 0.0f, 1.0f, 1.0f);
        SanitizeFloat(color.w, 0.0f, 1.0f, 1.0f);
    }

    for (auto& uv0 : mMeshData.mVertexAttribute.mUV0)
    {
        SanitizeFloat(uv0.x, -fltThreshold, fltThreshold, 0.0f);
        SanitizeFloat(uv0.y, -fltThreshold, fltThreshold, 0.0f);
    }

    for (auto& uv1 : mMeshData.mVertexAttribute.mUV1)
    {
        SanitizeFloat(uv1.x, -fltThreshold, fltThreshold, 0.0f);
        SanitizeFloat(uv1.y, -fltThreshold, fltThreshold, 0.0f);
    }
}
}   // namespace cross::StellarMesh