#pragma once
#include "GraphPartitioner.h"
#include "Util.h"

#ifndef _MANAGED
#include <span>
#endif

namespace cross::StellarMesh
{

struct InputMeshData
{
    MeshStreamData mMeshData{};
    //std::vector<UInt32> mIndexes{};
    UInt32 mTriangleNum{};

    // each triangle has a material index
    std::vector<SInt32> mMaterialIndices{};
};

struct ClusterPartitionSetting
{
    static constexpr UInt32 maxTrianglesInCluster = 128;
    static constexpr UInt32 maxVerticesInCluster = 256;
    static constexpr UInt32 minClusterGroupSize = 8;
    static constexpr UInt32 maxClusterGroupSize = 32;
};

struct GPUCluster
{
    alignas(4) UInt32 mVertexOffset;     // Index offset of this cluster's vertex in the whole mesh
    alignas(4) UInt32 mVertexCount;      // Numbers of vertices in this cluster
    alignas(4) UInt32 mTriangleOffset;   // Index offset of this cluster's triangle in the whole mesh
    alignas(4) UInt32 mTriangleCount;    // Numbers of triangles in this cluster

};

struct MaterialTriangle
{
    UInt32 Index0;
    UInt32 Index1;
    UInt32 Index2;
    UInt32 MaterialIndex;
    UInt32 RangeCount;
};

struct MaterialRange
{
    UInt32 mBegin{0};
    UInt32 mCount{0};
    UInt32 mMaterialIndex{};
};

struct Cluster
{
    Cluster() {}
#ifndef _MANAGED
    Cluster(MeshStreamData const& inMesh, std::vector<SInt32> const& inMaterialIndexed, std::span<UInt32> triangleIndexes, Adjacency const& adjacency);
#endif
    void SanitizeVertexData();

    UInt32 mNumTris = 0;
    UInt32 mNumVerts = 0;
    UInt32 mNumTexCoords = 0;
    bool mHasTangents = false;
    bool mHasColors = false;

    // [position, normal, <tangent>, <color>, <uv>] = [3 * float, 3 * float, 4 * float, 4 * float, 2 * float]
    //std::vector<float>  mVerts;
    //std::vector<UInt32> mIndexes;
    MeshStreamData mMeshData;
    std::vector<SInt32> mMaterialIndexes;
    std::vector<MaterialRange> mMaterialRanges;
    std::vector<SInt8>  mExternalEdges;
    UInt32              mNumExternalEdges = 0;

    SInt32 lodLevel = 0;
};

class ClusterGroup
{
public:
};
}   // namespace cross
