// include
#include "Resource/DataCompressor.h"
// include lz4
#include "lz4.h"
#include "lz4hc.h"


namespace cross {
// LZ4 compress
static constexpr char LZ4Header[] = {'L', 'Z', '4', '#'};
BinaryArchive* LZ4Compressor::Compress(const char* srcData, int srcSize) const
{
    int desSize = LZ4_compressBound(srcSize);
    char* compressedBuff = new char[desSize];
    int compressedSize;
    if (gDataCompressor->IsLZ4HC())
        compressedSize = LZ4_compress_HC(srcData, compressedBuff, srcSize, desSize, LZ4HC_CLEVEL_MAX);
    else
        compressedSize = LZ4_compress_fast(srcData, compressedBuff, srcSize, desSize, gDataCompressor->GetLZ4AccelerationFactor());
    if (compressedSize <= 0 || compressedSize >= srcSize)
    {
        delete[] compressedBuff;
        return nullptr;
    }
    UInt8* buff = new UInt8[(UInt64)compressedSize + 8];
    *reinterpret_cast<UInt32*>(buff) = *reinterpret_cast<const UInt32*>(LZ4Header);
    *reinterpret_cast<UInt32*>(buff + 4) = static_cast<UInt32>(srcSize);
    memcpy(buff + 8, compressedBuff, compressedSize);
    delete[] compressedBuff;
    return new BinaryArchive(buff, (UInt64) compressedSize + 8);
}

BinaryArchive* LZ4Compressor::DeCompress(const char* srcData, int srcSize) const
{
    if (!srcData || srcSize < 8)
        return nullptr;
    srcData += 4;
    UInt32 desSize = *reinterpret_cast<const UInt32*>(srcData);
    srcData += 4;
    char* buff = new char[desSize];
    int decompressedSize = LZ4_decompress_safe(srcData, buff, srcSize - 8, static_cast<int>(desSize));
    if (decompressedSize <= 0)
    {
        delete[] buff;
        return nullptr;
    }
    return new BinaryArchive(reinterpret_cast<UInt8*>(buff), (UInt64)desSize);
}

bool LZ4Compressor::IsCompressed(const char* srcData, int srcSize) const
{
    return (srcSize > 8) && std::memcmp(srcData, LZ4Header, 4) == 0;
}

DataCompressor::DataCompressor() {
    Register(CompressorType::LZ4, new LZ4Compressor());
}

DataCompressor::~DataCompressor() {
    mCompressMap.clear();
}

DataCompressor* DataCompressor::GetInstance()
{
    static DataCompressor instance;
    return &instance;
}

BinaryArchive* DataCompressor::TryCompress(CompressorType compressor, const UInt8* srcData, UInt64 srcSize) const
{
    if (IsCompressed(srcData, srcSize))
        return nullptr;

    auto it = mCompressMap.find(compressor);
    if (it != mCompressMap.end())
    {
        return it->second->Compress((const char*)srcData, static_cast<int>(srcSize));
    }
    return nullptr;
}

BinaryArchive* DataCompressor::TryCompress(const UInt8* srcData, UInt64 srcSize) const
{
    auto it = mCompressMap.find(mDefault);
    if (it != mCompressMap.end())
    {
        return it->second->Compress((const char*)srcData, static_cast<int>(srcSize));
    }
    return nullptr;
}

BinaryArchive* DataCompressor::TryDeCompress(const UInt8* srcData, UInt64 srcSize) const
{
    auto srcTempData = (const char*)srcData;
    auto srcTempSize = static_cast<int>(srcSize);
    for (auto& it : mCompressMap)
    {
        if (it.second->IsCompressed(srcTempData, srcTempSize))
        {
            return it.second->DeCompress(srcTempData, srcTempSize);
        }
    }
    return nullptr;
}

bool DataCompressor::IsCompressed(const UInt8* srcData, UInt64 srcSize) const
{
    auto srcTempData = (const char*)srcData;
    auto srcTempSize = static_cast<int>(srcSize);
    for (auto& it : mCompressMap)
    {
        if (it.second->IsCompressed(srcTempData, srcTempSize))
        {
            return true;
        }
    }
    return false;
}
}   // namespace cross
