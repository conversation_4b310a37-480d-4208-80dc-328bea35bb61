#pragma once

// Include
#include "EnginePrefix.h"
#include "ECS/Develop/Framework/Types.h"
#include "Resource/Resource.h"
#include "CECommon/Common/MeshDefines.h"
#include "CEMetaMacros.h"
namespace cross {
using TagMap  = std::unordered_map<std::string, std::string>;

enum class CEMeta(Editor) PCGType : UInt8
{
    Building = 0,
    Highway,
    Road,
    Bridge,
    Water,
    Foliage,
    Unkown
};


enum class CEMeta(Editor, Cli, Puerts) PCGBuildingType : UInt8
{
    CBD = 0,
    Ordinary,
    PoorHouse,
    WareHouse,
    MaxCount
};

class Resource_API CoordinateTransfer
{
public:
    CoordinateTransfer(){};

    void    SetLatLon(const Float3& lon, const Float3& lat);
    Double2 GCJ02_To_WGS84(double lon, double lat);
    Double2 WGS84_To_GCJ02(double lon, double lat);
    Double2 WGS84_To_2D(double lon, double lat);
    Double2 GCJ02_To_2D(double lon, double lat);

private:
    double  LonToX(double lon);
    double  LatToY(double lat);
    double  ToDoubleNumber(const Float3& value);
    double  TransformLat(double x, double y);
    double  TransformLon(double x, double y);

private:
    double  mLongitude = 0.0;
    double  mLatitude = 0.0;
    double  mOriginX = 0.0;
    double  mOriginY = 0.0;
    double  mScaleFactor = 0.0;
};

struct PCGBound
{
    Double2 mMin{std::numeric_limits<double>::infinity(), std::numeric_limits<double>::infinity()};
    Double2 mMax{-std::numeric_limits<double>::infinity(), -std::numeric_limits<double>::infinity()};

    PCGBound(){};
    
    PCGBound(const Double2& center, const Double2& extand)
    {
        mMin.x = center.x - extand.x;
        mMin.y = center.y - extand.y;
        mMax.x = center.x + extand.x;
        mMax.y = center.y + extand.y;
    };

    PCGBound(const Float4& bound) {
        mMin.x = bound.x;
        mMin.y = bound.y;
        mMax.x = bound.z;
        mMax.y = bound.w;
    }

    inline void Encapsulate(const Double2& point) noexcept {
        mMin.x = std::min(mMin.x, point.x);
        mMin.y = std::min(mMin.y, point.y);

        mMax.x = std::max(mMax.x, point.x);
        mMax.y = std::max(mMax.y, point.y);
    }

    inline void Encapsulate(const PCGBound& other) noexcept
    {
        mMin.x = std::min(mMin.x, other.mMin.x);
        mMin.y = std::min(mMin.y, other.mMin.y);

        mMax.x = std::max(mMax.x, other.mMax.x);
        mMax.y = std::max(mMax.y, other.mMax.y);
    }

    inline bool Contains(const Double2& point) const {
        return point.x >= mMin.x && point.y >= mMin.y && point.x <= mMax.x && point.y <= mMax.y;
    }

    inline bool Contains(const PCGBound& other) const
    {
        return other.mMin.x >= mMin.x && other.mMin.y >= mMin.y && other.mMax.x <= mMax.x && other.mMax.y <= mMax.y;
    }

    inline PCGBound CreateIntersection(const PCGBound& other) const
    {
        PCGBound ret;
        ret.mMin.x = std::max(mMin.x, other.mMin.x);
        ret.mMin.y = std::max(mMin.y, other.mMin.y);
        ret.mMax.x = std::min(mMax.x, other.mMax.x);
        ret.mMax.y = std::min(mMax.y, other.mMax.y);
        return ret;
    }

    inline Double2 GetCenter() const
    {
        return Double2((mMin.x + mMax.x) / 2, (mMin.y + mMax.y) / 2);
    }

    inline bool IsValid() const
    {
        return mMax.x >= mMin.x && mMax.y >= mMin.y;
    }

    inline double Area() const
    {
        if (!IsValid())
            return 0.0;
        return (mMax.x - mMin.x) * (mMax.y - mMin.y);
    }
};

struct PCGNode
{
    SInt64  mID;
    Double2 mPos;

    inline bool operator>(const PCGNode& other)
    {
        if (mPos.x == other.mPos.x)
            return mPos.y > other.mPos.y;
        return mPos.x > other.mPos.x;
    }

    inline bool operator==(const PCGNode& other) {
        return mPos == other.mPos;
    }

    inline bool operator!=(const PCGNode& other)
    {
        return mPos != other.mPos;
    }
};

struct PCGWay
{
    SInt64              mID;
    std::vector<SInt64> mRefNodes;
    TagMap              mTags;
    PCGBound            mBound;
};

struct PCGGroup
{
    SInt64              mID;
    std::vector<SInt64> mRefWays;
    TagMap              mTags;
    PCGBound            mBound;
};

struct PCGBlock
{
    PCGBound            mBound;
    std::vector<SInt64> mRefGroups;
};

struct Resource_API PCGMap
{
    PCGType                 mType = PCGType::Unkown;
    PCGBound                mBound;
    Double2                 mBlockSize;
    std::vector<PCGBlock*>  mBlocks;
    SInt32                  mLineNum{0};
    SInt32                  mRowNum{0};
    Double2                 mBlockOrigin{0.0, 0.0};
    std::unordered_map<SInt64, PCGNode*>    mNodeMap;
    std::unordered_map<SInt64, PCGWay*>     mWayMap;
    std::unordered_map<SInt64, PCGGroup*>   mGroupMap;
#ifdef CROSSENGINE_EDITOR
    SInt64                  mMaxNodeId{0};
    SInt64                  mMaxWayId{0};
    SInt64                  mMaxGroupId{0};
#endif

    PCGMap() {}
    PCGMap(PCGType tp) : mType(tp) {}
    ~PCGMap();

    void      BuildMapBlock();
    SInt32    GetBlockIndexByPos(const Double2& pos) const;
    void      TraverseBlockByPos(const Double2& pos, UInt8 layer, std::function<void(SInt32 index)> func) const;
    void      TraverseWayByBound(const PCGBound& bound, std::function<void(SInt32 blockIdx, SInt64 groupId, SInt64 wayId)> func) const;
    PCGBlock* GetBlockByPos(const Double2& pos) const;
#ifdef CROSSENGINE_EDITOR
    void      DeleteGroup(SInt64 groupId);
    void      DeleteWay(SInt64 groupId, SInt64 wayId);
    PCGNode*  AddNode();
    PCGWay*   AddWay();
    PCGGroup* AddGroup();
    SInt32    AddGroupToBlock(PCGGroup* group);
#endif
};

#ifdef CROSSENGINE_EDITOR
struct PCGMapRebuilderConfigure
{
    PCGBound    mRange;
    bool        mRedumplicate = false;
    float       mSimplifyEpsilon = 0.f;
    double      mDistanceThreshold = 100.0;

    PCGMapRebuilderConfigure(const Float4& range, bool redumplicate, float simplifyEpsilon, double distanceThreshold)
        : mRange(range)
        , mRedumplicate(redumplicate)
        , mSimplifyEpsilon(simplifyEpsilon)
        , mDistanceThreshold(distanceThreshold)
    {
    }
};


class Resource_API PCGMapRebuilder {
public:
    static void Rebuild(PCGMap& map, const PCGMapRebuilderConfigure& config);

    PCGMapRebuilder(const PCGMapRebuilderConfigure& config);
    ~PCGMapRebuilder(){};

protected:
    void SimplifyMapWay(PCGMap& map, float simplifyEpsilon);
    void RebuildBuildingMap(PCGMap& map);
    void RebuildRoadMap(PCGMap& map);

private:
    void ReBuildBuildingBlock(PCGMap& map);
    bool RebuildBudingGroupWay(PCGMap& map, PCGGroup* group, UInt32 wayIdx);
    void RebuildBuildingWayNode(PCGMap& map, PCGGroup* group, UInt32 wayIdx);
    void RebuildBuildingWayArea(PCGMap& map);
    void RebuildBuildingWayType(PCGMap& map, PCGGroup* group, UInt32 wayIdx);
    bool ReMergeBuildingGroup(PCGMap& map);

private:
    void ReBuildRoadBlock(PCGMap& map);

private:
    bool IsClockwise(PCGMap& map, PCGWay* way);
    double CalculatePolygonArea(PCGMap& map, PCGWay* way);
    void RamerDouglasPeucker(PCGMap& map, const std::vector<SInt64>& inWayNodes, std::vector<SInt64>& outWayNodes, float epsilon);
    double PerpendicularDistance(PCGMap& map, const std::vector<SInt64>& wayNodes, UInt64 pIdx, UInt64 lineIdx1, UInt64 lineIdx2);
    bool CheckMergeGroup(PCGMap& map, PCGGroup* group1, PCGGroup* group2, double distance);
    bool CheckBuildingDistance(PCGMap& map, PCGWay* way1, PCGWay* way2, double distance);

protected:
    PCGMapRebuilderConfigure mConfig;
};
#endif

namespace resource {
    class Resource_API CEMeta(Script) PCGResource : public Resource
    {
    public:
        //@brief GetClassIDStatic
        static int GetClassIDStatic() { return ClassID(PCGResource); }
        //@brief Constructor
        PCGResource(){};
        //@brief Constructor
        PCGResource(std::string path){};
        //@brief Destructor
        virtual ~PCGResource(){};

    public:
        void  Serialize(const PCGMap& mapData);
        bool  Deserialize(cross::SimpleSerializer const& s) override;
        const PCGMap& GetMapData() const { return mMapData; };
        PCGMap& GetMapData() { return mMapData; };

    protected:
        PCGMap   mMapData;

    public:
        static constexpr float sBuildingLevelHeight = 3.f;
    };
}   // namespace resource
}   // namespace cross
