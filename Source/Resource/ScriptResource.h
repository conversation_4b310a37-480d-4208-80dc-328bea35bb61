#pragma once
#include "Resource/Resource.h"
#if SUPPORT_LUA
#include "ScriptEngine/ScriptEngine.h"
#endif
#include "Resource/resourceforward.h"
#include "CECommon/Resource/IRuntimeObject.h"

namespace cross
{
    namespace resource
    {
        class ScriptResource : public Resource
        {
            FRIEND_WITH_REFLECTION_MODULE;
        protected:
            ScriptResource() = default;
        
        public:
            using PropertyContainer = std::vector<ScriptProperty>;

        public:
            virtual ~ScriptResource() = default;

            bool PostDeserialize() override;

            static int GetClassIDStatic() { return ClassID(ScriptResource); }
            void SetContent(std::string fileContent, std::string filePath/*for script debug info*/) { mScriptString = std::move(fileContent); mScriptPath = std::move(filePath); }
            std::string const& GetContent() const { return mScriptString; }
            bool LoadScript();
#if SUPPORT_LUA
            script::Local<script::Value> GetClass() { return mClass.get(); }            
#endif
            const PropertyContainer& GetPropeties() const;

        private:
            void ParaseProperty();

        private:
            std::string mScriptPath;
            std::string mScriptString;
#if SUPPORT_LUA
            script::Global<script::Value> mClass;
#endif

        };
    }//namespace resource
}//namespace cross

