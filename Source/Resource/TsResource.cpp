#include "EnginePrefix.h"

#include "Resource/TsResource.h"
#include "CrossBase/String/StringHelper.h"
#include "ResourceManager.h"
namespace cross { namespace resource 
{
    bool TsResource::PostDeserialize()
    {
        return true;
    }
    
    bool TsResource::HotReloadScript()
    {
        auto scriptPath = GetName();
        if (scriptPath.empty())
            return false;
        if (StringHelper::EndsWith(scriptPath, ".mts"))
        {
            scriptPath = cross::StringHelper::Replace(scriptPath, "Contents/TypeScript/", "");
            scriptPath = cross::StringHelper::Replace(scriptPath, ".mts", ".mjs");

            gResourceMgr.mTypeScriptHotReloadMgr->ReloadScriptModule(cross::StringHelper::Replace(scriptPath, "Contents/TypeScript/", ""), mScriptString);
        }
        else
        {
            gResourceMgr.mTypeScriptHotReloadMgr->ReloadScriptModule(cross::StringHelper::Replace(scriptPath, "Contents/JavaScript/", ""), mScriptString);
        }
        return true;
    }
}}   // namespace cross::resource
