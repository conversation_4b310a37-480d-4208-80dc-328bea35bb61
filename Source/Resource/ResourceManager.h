#pragma once

#include "CECommon/Utilities/NDACommon.h"
#include "CECommon/Common/RenderingCommandBatcher.h"
#include "Resource/Resource.h"
#include "Resource/resourceasset.h"
#include "CrossBase/Threading/RenderingThread.h"
#include "CrossBase/Serialization/ResourceMetaHeader.h"
#include "Threading/Task.h"
#include "resourceasset.h"
#include "Resource/BaseClasses/ClassIDs.h"
#include "Resource/AssetStreaming.h"
#include "Runtime/Editor/EditorCallback.h"
#include <shared_mutex>
#include "Resource/IResourceInterface.h"

namespace cross
{
struct CEMeta(Cli) ImageInfo
{
    CEMeta(Cli)
    int Width = 0;
    CEMeta(Cli)
    int Height = 0;
    CEMeta(Cli)
    unsigned int* Data = nullptr;
};
    static constexpr const char* Defaultshaderpath = "EngineResource/Shader/ClusterRed.shader.nda";
    static constexpr const char* Defaulttexturepath = "EngineResource/Texture/DefaultTexture.nda";
    static constexpr const char* Defaulttexturecubepath = "EngineResource/Texture/DefaultTextureCube.nda";
    static constexpr const char* Defaulttexturearraypath = "EngineResource/Texture/DefaultTexture2DArray.nda";
    static constexpr const char* Defaultmodelpath = "EngineResource/Model/Sphere.nda";
    inline bool IsScriptFile(const std::string& inStrFileName)
    {
        if (auto pos = inStrFileName.rfind("."); pos >= 0)
        {
            std::string ext = inStrFileName.substr(pos + 1, inStrFileName.size());
            return ext == "lua" || ext == "js" || ext == "ts" || ext == "mjs" || ext == "mts";
        }
        return false;
    }

    inline bool IsTSFile(const std::string& inStrFileName)
    {
        if (auto pos = inStrFileName.rfind("."); pos >= 0)
        {
            std::string ext = inStrFileName.substr(pos + 1, inStrFileName.size());
            return ext == "js" || ext == "ts" || ext == "mjs" || ext == "mts";
        }
        return false;
    }
    inline bool IsNDAFile(const std::string& inStrFileName)
    {
        if (auto pos = inStrFileName.rfind("."); pos >= 0)
        {
            std::string ext = inStrFileName.substr(pos + 1, inStrFileName.size());
            return ext == "compute.nda" || ext == "nda" || ext == "prefab" || ext == "model" || ext == "block" || ext == "system" || ext == "emitter" || ext == "mpc" || ext == "materialfunction" || ext == "flow" || ext == "iam";
        }
        return false;
    }

    struct ResourceReloadEventData
    {
        std::string mPath;
        ClassIDType ClassID;
    };
    using ResourceReloadEvent = SystemEvent<ResourceReloadEventData>;

    struct CEMeta(Cli) ResourceAABB
    {
    public:
        CEMeta(Cli)
        cross::Double3 min = cross::Double3::Zero();
        CEMeta(Cli)
        cross::Double3 max = cross::Double3::Zero();
    };
    class Resource_API CEMeta(Cli) ResourceUtil  
    {
    public:
        CEMeta(Cli)
        static void ReleaseResource(Resource* resource);

        // This function is only used for the editor.
        // If you want to use it in the cpp, must use "ResourcePtr{ResourceGet(...)}" to hold this.
        // TODO: Make ResourcePtr usable in editor and return ResourcePtr instead of Resource*.
        CEMeta(Cli)
        static Resource* ResourceGet(char const* path);

        CEMeta(Cli)
        static int ResourceGetClassID(char const* path);

        CEMeta(Cli)
        static void ResourceSaveToFile(Resource * resource, char const* path);
        CEMeta(Cli)
        static bool ResourceCheckDependency(Resource * resource, char const* path, bool recursive);
        CEMeta(Cli)
        static bool CompareTexture(const char* path, const char* path1, const char* path2, double& MSE, double& PSNR, double& SSIM);
        CEMeta(Cli)
        static void Editor_SaveImage(const char* filename, int width, int height, unsigned int* data);
        CEMeta(Cli)
        static ImageInfo Editor_LoadImage(const char* filename);
        CEMeta(Cli)
        static unsigned int* Editor_CreateImageBuffer(int width, int height);
        CEMeta(Cli)
        static void Editor_FreeImage(unsigned int* image);
        CEMeta(Cli)
        static unsigned int* Editor_CreateImage(const char* filename, int width, int height, unsigned int color);
        CEMeta(Cli)
        static void CrossEngine_DeleteResource(const char* filename);
        CEMeta(Cli)
        static void CrossEngine_ReloadResource(const char* filename);
    };



    class Resource_API ResourceManager : public SystemEventManager<ResourceReloadEvent>
    {
        friend class AssetStreamingManager;
        friend class AssetStreamingContext;
        friend class AssetStreamingHandle;
        friend class ResourceManagerScript;
        //using ResourceContainer = std::unordered_map<std::string, ResourcePtr>;
        //using ResourceContainer = CEHashMap<std::string, ResourcePtr>;
        using ResourceContainer = CEHashMap<HashString, ResourcePtr>;
        using NDAFileContainer = CEHashMap<HashString, std::pair<cross::resource::LoadNDAFileInfo*, Archive*>>;
               
    public:
        static constexpr UInt64 HeadMapSize = 1024 * 16;
        ~ResourceManager() = default;
        ICreateRenderObjectMgr* mCreateRenderObjectMgr = nullptr;
        ITypeScriptHotReloadMgr* mTypeScriptHotReloadMgr = nullptr;
        IStreamingMgr* mStreamingMgr = nullptr;

        CEFunction(Editor)
        static ResourceManager& Instance();
        bool ReloadResource(ResourcePtr res);

        CEFunction(Editor)
        void TryReloadResource(std::string FilePath, EditorGeneralCallBack CallBack);
        CEFunction(Editor)
        void TryReloadResource(std::string FilePath);
        CEFunction(Editor) 
        void ImmediateReloadResource(std::string FilePath);
        CEFunction(Editor) 
        bool IsNDAFile(const std::string& FilePath) { return cross::IsNDAFile(FilePath); };
        CEFunction(Editor) 
        bool IsScriptFile(const std::string& FilePath) { return cross::IsScriptFile(FilePath); };

        template <class T, typename... Args>
        ResourcePtr CreateResource(Args... args)
        {
            using namespace gbf::reflection;
            ResourceFuturePtr<T> pRes = ResourceFuturePtr<T>(new T(std::forward<Args>(args)...));
            if constexpr (std::is_base_of_v<RttiBase, T>) {
                pRes->__bind_rtti_info(__type_of<T>(), StorageType::StorageRemote, next_reflection_obj_id());
            }
            const void * address = static_cast<const void*>(pRes.get());
            std::stringstream ss;
            ss << address;
            std::string resPtrAddress = ss.str();
            
            std::string runtimeResPath("runtimeResPath_");
            runtimeResPath += resPtrAddress;

            return AddResource(runtimeResPath.c_str(), ResourcePtr(pRes), true);
        }

        template<class T, typename... Args>
        CE_REAL_PTR_IMPLMENT<T> CreateNamedResource(const std::string& name, Args... args)
        {
            using namespace gbf::reflection;
            ResourceFuturePtr<T> pRes = ResourceFuturePtr<T>(new T(std::forward<Args>(args)...));
            if constexpr (std::is_base_of_v<RttiBase, T>)
            {
                pRes->__bind_rtti_info(__type_of<T>(), StorageType::StorageRemote, next_reflection_obj_id());
            }
            

            return TypeCast<T>(AddResource(name.c_str(), ResourcePtr(pRes), true));
        }

        template <class T, typename... Args>
        CE_REAL_PTR_IMPLMENT<T> CreateResourceAs(Args... args)
        {
            auto res = CreateResource<T>(std::forward<Args>(args)...);
            return TypeCast<T>(res);
        }
        
        ResourcePtr CreateResourceByClassID(uint64_t classID, const std::string filepath);

        template<class T, typename... Args>
        CE_REAL_PTR_IMPLMENT<T> CreateCompletedResourceAs(const std::string& filepath, Args... args)
        {
            using namespace gbf::reflection;
            ResourceFuturePtr<T> pRes = ResourceFuturePtr<T>(new T(std::forward<Args>(args)...));
            if constexpr (std::is_base_of_v<RttiBase, T>)
            {
                pRes->__bind_rtti_info(__type_of<T>(), StorageType::StorageRemote, next_reflection_obj_id());
            }
            pRes->CreateAsset(filepath);
            pRes->GetAsset()->EnsureGuid();
            AddResource(filepath.c_str(), pRes);
            return pRes;
        }

        // Only for serialized without runtime loading process.
        template <class T, typename... Args>
        CE_REAL_PTR_IMPLMENT<T> CreateTempResourceAs(Args... args)
        {
            using namespace gbf::reflection;
            ResourceFuturePtr<T> pRes = ResourceFuturePtr<T>(new T(std::forward<Args>(args)...));
            if constexpr (std::is_base_of_v<RttiBase, T>) {
                pRes->__bind_rtti_info(__type_of<T>(), StorageType::StorageRemote, next_reflection_obj_id());
            }
            return pRes;
        }

        void SetCollectGrabge(bool flag)
        {
            mCollectGrabge = flag;
        }
    private:
        ResourceManager();  
        UInt32 GetMemoryUsage()const;
        void Destroy();

        bool mCollectGrabge{true};
    public:
        void Initialize();

        void CheckLeak();
        void GarbageCollect(bool forcegc = false, bool disCardInterval = false);

        void BuildGuidMap();

        void InitDefaultResource();

        template<typename T>
        void LoadDefaultRes(const char* path)
        {
            size_t classID = typeid(T).hash_code();
            if (mDefaultResources.find(classID) == mDefaultResources.end())
            {
                mDefaultResources.emplace(classID, cross::AssetStreamingManager::Get()->LoadSynchronously(path));
            }
        }
        template<typename T>
        ResourcePtr GetDefaultRes()
        {
            size_t classID = typeid(T).hash_code();
            if (mDefaultResources.find(classID) == mDefaultResources.end())
            {
                return ResourcePtr(nullptr);
            }
            return mDefaultResources.find(classID)->second;
        }
        // find both deserialized and postdeserialized mesh
        cross::ResourcePtr Find(const char* fileName);

        cross::ResourcePtr FindPostDeserizalized(const char* fileName);

        cross::ResourcePtr FindDeserialized(const char* fileName);

        cross::ResourcePtr FindAnywhere(const char* fileName);

        void AddResourceDependencies(const DeserializeNode& node, ResourcePtr resource);

        void AddResourceDependencies(const DeserializeNode& node, std::set<std::string>& outDenpencies);

        CEFunction(Editor) void MarkResourceChanged(const std::string& path);

        CEFunction(Editor) void RemoveResourceThumbnail(const std::string& path);

        CEFunction(Editor)
        void BuildResourceList();

        CEFunction(Editor) 
        void ReLoadResourceList(const std::string& rootPath);

        CEFunction(Editor)
        std::string ConvertGuidToPath(const std::string& guid);

        CEFunction(Editor) 
        bool HasGuid(const std::string& guid);

        CEFunction(Editor)
        std::string GetGuidByPath(const std::string& path);

        CEFunction(Editor)
        std::string ConvertPathToGuid(const std::string& path);

        CEFunction(Editor)
        void DeleteFilePaths(std::vector<std::string> paths);

        CEFunction(Editor) 
        void ChangeFilePaths(std::vector<std::string> oldPaths, std::vector<std::string> newPaths);

        CEFunction(Editor) 
        void AddNewFiles(std::vector<std::string> paths, bool newGuid = true);

        void AddFileByHeader(const std::string& path, ResourceMetaHeader& metaHeader);

        bool CheckFileByGuid(const std::string& guid);

        bool CheckEngineFileVaile(const std::string& filename, bool checkExist = true);

        std::string GenFileGuid(const std::string& path);

        ResourcePtr GetResource(const char* input, bool inNeedReset = false, std::function<void(ResourcePtr inRes)> inCallback = nullptr);

        CEFunction(Editor)
        bool   CreateResource(ResourceMetaHeader& metaHeader, const std::string& contentStr, const std::string& path);

        void DeleteResource(const char* name);

        const auto& GetLastFrameResettedResources() const { return mLastFrameResettedResources; }

    private:
        inline bool IsRenderRelatedResource(ClassIDType ClassID);

        bool GetResourceMetaHeader(const std::string& path, ResourceMetaHeader& header);

        std::pair<cross::resource::LoadNDAFileInfo*, Archive*> GetOrCreateNDAFileInfo(const std::string& file);

        std::pair<cross::resource::LoadNDAFileInfo*, Archive*> ReadNDAFileAndCacheImp(const std::string& file);

        void RemoveCachedNDAFile(const std::string& file);

        ResourcePtr AddResource(const char* fileName, ResourcePtr resource, bool isRuntimeRes = false);

        ResourcePtr AddDerserializedResource(const char* fileName, ResourcePtr resource);

        // only call when addResource;
        void RemoveDeserializedResource(const char* filename, ResourcePtr resource);



    public:
        void TraverseWorkDirToAddHeader();

    private:
        bool ResourceLoaded(const char* name);

    public:
        AssetHeader GetResourceHeader(char const* name);
        bool ReloadResource(const char* name, std::function<void(ResourcePtr inRes)>);
        CEFunction(Editor)
        bool GetResourceDependencies(const char* resourcePath, std::vector<std::string>& outDependencyPaths, bool recursive = false);
        void AddChangedResource(ResourcePtr res);
        bool CheckChangedResByID(int resId){ return mChangedResourceMap.find(resId) != mChangedResourceMap.end(); };
        const std::list<ResourcePtr>* GetChangedResListById(int resId);

        ResourcePtr LoadRawResouceForEditorDebug(const char* resoucePath);


    protected:

    private:
        bool NeedReloadRelatedResources(const char* fileName);
        bool ReloadRelatedResources(const char* fileName);


    public:
        resource::ResourceLoadError GetResourecHeaderInfo(const char* fileName, uint32_t& outResClassId, int32_t& outResVersion);
        void EnableAsyncLoading(bool enable) { mIsAsyncLoading = enable; }
        bool IsAsyncLoading() { return mIsAsyncLoading ;}
        void UpdateAsync();
        void Tick();
        
    private:
        void OutputAsyncTimeStatue();
        void AddToResetResouraceList(ResourcePtr inResPtr);
        bool mIsAsyncLoading = false;
        bool mAsyncLoadReCreateDependceFile = false;
        //AsyncTimeState mAsyncTimeState;
        //friend class AsyncTimeState;
        
    public:
        auto GetFileSystem() const
        {
            return mFileSystem;
        }
        const char* GetDefaultTexturePath();
        const char* GetBlackTexturePath();
        bool        IsStreamMode() { return mUseStreamMode; }
        size_t GetResourceCount()
        {
            return mResources.size();
        }

        bool ParrallelDeleteEnabled() const {
            return mEnableParallelDelete;
        }

        void EnqueueMaterialRenderingCommand(std::function<void()>&& cmd)
        {
            // MaterialParameterCollection::FlushMaterials  dispatches a multi-threaded for loop to update materials in parallel,
            // which could lead to write-write access conflicts
            mMaterialRenderingCommandBatcher.Enqueue(std::move(cmd));
        }

        void DispatchMaterialRenderingCommands();
    private:
        ResourcePtr               mNullResource{ nullptr };
        ResourceContainer         mDeserializedResources;
        ResourceContainer         mResources; // fully loaded resource;
        ResourceContainer         mForceDeleteResource; // resource in this list is not in ResourceManager, and shou be gced as soon as possible;
  
        NDAFileContainer          mCachedNDAFiles;

        using ResetCallback = std::function<void(ResourcePtr inRes)>;
        std::map<ResourcePtr, ResetCallback>  mNeedResetResources;
        std::map<ResourcePtr, ResetCallback>  mLastFrameResettedResources;

        std::shared_mutex  mMutex;
        std::shared_mutex  mNDAFileMutex;
        std::shared_mutex  mDeserializedMutex;

        ResourcePtr mDefaultTex;
        ResourcePtr mBlackTex;

        filesystem::FileSystem* mFileSystem    = nullptr;
        bool                    mUseStreamMode = false;

        std::unordered_map<int, std::list<ResourcePtr>> mChangedResourceMap;
        cross::RenderingCommandBatcher mMaterialRenderingCommandBatcher;


        CEHashMap<size_t, ResourcePtr> mDefaultResources;
        float  mLastCollectTime = 0;
        UInt32 mLastCollectFrameId = 0;
        UInt32 mGarbageCollectFrameInterval = 0;
        bool   mEnableParallelDelete = true;
        UInt32 mReleasePerFrame = UINT32_MAX;
        UInt32 mNeedToRelease = 0;

        friend class ResourceAssetManager;
        friend class ResourecTest;
        friend class GetResourecHeaderInfoTest;
        
        friend void Resource::RequestRefresh();
    };
    template<typename To, typename From>
    ResourceFuturePtr<To> TypeCast(ResourceFuturePtr<From> f)
    {
        To* to = TYPE_CAST(To*, f.get());
        if (!to)
        {
            to = TYPE_CAST(To*, cross::ResourceManager::Instance().GetDefaultRes<To>().get());
            LOG_ERROR("Fatal: Load or Get Resource Failed, Use a default resource");
        }
        return ResourceFuturePtr<To>(to);
    }
    }


#define gResourceMgr cross::ResourceManager::Instance()
