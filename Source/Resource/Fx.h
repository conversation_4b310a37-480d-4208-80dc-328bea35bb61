#pragma once
#include "Resource/Resource.h"
#include "Resource/Shader.h"
#include "CrossBase/String/NameID.h"
#include "NativeGraphicsInterface/NGI.h"
#include "CrossBase/Serialization/SerializeNode.h"
#include "FxDefines.h"
#include "MaterialInterface.h"
#include "MaterialDefines.h"

namespace cross { namespace resource {

    struct CEMeta(Serialize) ParameterInfos
    {
        CE_Serialize_Deserialize

        CEProperty()
        std::map<NameID, MaterialParameterScalar> mScalarParams;
        CEProperty()
        std::map<NameID, MaterialParameterVector> mVectorParams;
        CEProperty()
        std::map<NameID, MaterialParameterBool> mBoolParams;
        CEProperty()
        std::map<NameID, MaterialParameterTexture> mTextureParams;
    };

    class Resource_API CEMeta(Cli, Script) Fx : public MaterialInterface
    {
    public:
        FRIEND_WITH_REFLECTION_MODULE;
        friend class cross::ResourceManager;
        using PropertyType = MaterialInterface::PropertyType;
        using StateType = MaterialInterface::StateType;
        using PropertyAccesser = MaterialInterface::PropertyAccesser;

        enum PropertyFlags : UInt8
        {
            Shader_Resource = 0x10,
            Shader_CbMember = 0x20,
            Shader_SpMember = 0x40,
            Shader_Macro = 0x80
        };

        struct Property
        {
            struct Range
            {
                float mMin{NAN};
                float mMax{NAN};

                Range() {}
                Range(float min, float max)
                    : mMin(min)
                    , mMax(max)
                {}
                inline bool IsValid() const { return mMin != NAN && mMax != NAN && mMin < mMax; }
            };

            NameID mName;
            NameID mGroupName{"Default Group"};

            PropertyType mValue;
            bool mUsage{false};
            bool mVisible{true};
            bool mIsColor{false};
            Range mRange;

            Property() {}
            Property(const char* name, PropertyType value)
                : mName(name)
                , mValue(value)
            {}
            Property(const char* group, const char* name)
                : mGroupName(group)
                , mName(name)
            {}
        };

        struct Pass
        {
            bool mEnable{true};
            NameID name;
            ShaderPtr mShaderPtr;
            std::string mShaderCode;
            std::unordered_set<NameID> mMacros;
            StateType mState{
                GetDefaultBlendState(),
                GetDefaultDepthStencilState(),
                GetDefaultRasterizationState(),
                GetDefaultDynamicState(),
                2000,
            };

            Pass() = default;
            Pass(const ShaderPtr& shader, const NameID& key);
        };

        Fx() = default;
        virtual ~Fx() = default;

        virtual bool RefreshRenderData() override;

        StatementDefaultResource(Fx)

        static int GetClassIDStatic()
        {
            return ClassID(Fx);
        }

        virtual bool ResetResource() override;

        void CopyFrom(const Fx& fx);

    private:
        virtual void PostDestroy() override;
        virtual void DestroyRenderData(FrameParam* frameParam) override;

        void SetToRender(const NameID& propID, const PropertyType& propVal);
        void SetToRender(const NameID& passID, const StateType& states);
        void SetToRender(const NameID& passID, const UInt32 group);
        void SetToRender(const NameID& passID, const bool enable);

    public:
        static void SerializePropertyValue(SerializeNode& s, const PropertyType& value);
        static std::optional<PropertyType> DeserializePropertyValue(const DeserializeNode& s);

        bool Serialize(SerializeNode&& s, const std::string& path) override;
        void Serialize(SerializeNode& s);
        bool Deserialize(DeserializeNode const& s) override;
        bool PostDeserialize() override;

    private:
        void FillPropertyFlag(const NameID& name, UInt8 flag);
        void FillShaderMacro(const NameID& name);
        void FillShaderBuffer(const NGIVariableDesc& member);
        void FillShaderResource(const ShaderResourceDesc& member);
        void FillPropertyByShader(Pass& pass);

        void RefreshProperties();

        void RefreshChildMaterial();

    public:
        Shader* GetShader(const NameID& passID) override;

        const Pass& GetPass(const NameID& passID) const;
        Pass& GetPass(const NameID& passID);

        const Pass& GetDefaultPass() const;
        inline const auto& GetAllPass() const { return mPasses; }
        inline auto& GetAllPass() { return mPasses; }
        inline auto& GetAllProperties() { return mProperties; }

        inline bool HasPass(const NameID& passID) const { return mPasses.find(passID) != mPasses.end(); }
        FxRenderState GetRenderState(const NameID& passID) const;
        
        virtual PropertyType const* GetProperty(NameID const& name) const override;
        bool const* GetPropertyUsage(NameID const& name) const;
        virtual bool const* GetPropertyVisible(NameID const& name) const override;
       
        virtual bool const* GetPropertyIsColor(NameID const& name) const override;

        virtual bool IsPassEnable(const NameID& passID) const override;
        virtual const StateType* GetState(const NameID& passID) const override;
        virtual UInt32 GetRenderGroup(NameID const& passID) override;
        virtual void VisitProperty(PropertyAccesser func) const override;
        virtual bool HasAncestor(const std::string& name) override;

        virtual void Refresh(bool resetResource = false) override;
        virtual void RefreshMaterialTree(bool resetResource = false, bool skipSelf = false) override;

        Property::Range const* GetPropertyMinMax(NameID const& name) const;
        UInt8 GetPropertyFlag(const NameID& name) const;

        inline const auto& GetExposedPropertyName() const { return mPropertyNames; }
        inline auto& GetExposedPropertyName() { return mPropertyNames; }
        inline const auto& GetExposedPassName() const { return mPassNames; }
        inline auto& GetExposedPassName() { return mPassNames; }
        inline const auto& GetExposedGroupName() const { return mGroupNames; }
        inline auto& GetExposedGroupName() { return mGroupNames; }
        inline const auto& GetProperties() const { return mProperties; }

    public:
        CEMeta(Cli)
        virtual void SetBool(const NameID& name, bool value) override;
        virtual void SetInt(const NameID& name, int value) override;
        virtual void SetFloat(const NameID& name, float value) override;
        virtual void SetFloat2(const NameID& name, const float value[2]) override;
        virtual void SetFloat3(const NameID& name, const float value[3]) override;
        virtual void SetFloat4(const NameID& name, const float value[4]) override;
        virtual void SetFloatArray(const NameID& name, int length, const float* pValue = nullptr) override;
        virtual void SetTexture(const NameID& name, TexturePtr texture) override;
        virtual void SetTextureProp(const NameID& name, TexturePtr texture) override;
        virtual void SetSamplerState(const NameID& name, const SamplerState& samplerState) override;
        virtual void SetProp(const NameID& name, const PropertyType& propVal) override;
        CEMeta(Cli)
        static Fx* FxCreateFx();
        CEMeta(Cli)
        static Fx* FxCreateFxNew();
        CEMeta(Cli)
        void SetPropertyGroup(const char* name, const char* group);
        CEMeta(Cli)
        void SetPropertyVisible(const NameID& name, bool visible);
        CEMeta(Cli)
        void SetPropertyIsColor(const NameID& name, bool isColor);
        CEMeta(Cli)
        void SetPropertyMin(const NameID& name, float min);
        CEMeta(Cli)
        void SetPropertyMax(const NameID& name, float max);
        CEMeta(Cli)
        int EditorGetPropertyType(const NameID& name);
        CEMeta(Cli)
        bool EditorGetPropertyIsColor(const NameID& name);

        CEMeta(Cli)
        bool EditorGetPropertyUsage(const NameID& name);
        CEMeta(Cli)
        bool EditorGetPropertyVisible(const NameID& name);
        CEMeta(Cli)
        float EditorGetPropertyFloat(const NameID& name);
        CEMeta(Cli)
        Float2 EditorGetPropertyMinMax(const NameID& name);
        CEMeta(Cli)
        Float2 EditorGetPropertyFloat2(const NameID& name);
        CEMeta(Cli)
        Float3 EditorGetPropertyFloat3(const NameID& name);
        CEMeta(Cli)
        Float4 EditorGetPropertyFloat4(const NameID& name);
        CEMeta(Cli)
        bool EditorGetPropertyBool(const NameID& name);
        CEMeta(Cli)
        std::string EditorGetPropertyString(const NameID& name);
        CEMeta(Cli)
        std::string GetGroupNameList();
        CEMeta(Cli)
        std::string GetPropertyNameList();
        CEMeta(Cli)
        void RemoveUnusedProps();
        CEMeta(Cli)
        void ChangePropertyPosition(const char* name, const char* prev, const char* next);
        CEMeta(Cli)
        void ChangePassPosition(const char* name, const char* next);
        CEMeta(Cli)
        void ChangeGroupPosition(const char* name, const char* next);
        CEMeta(Cli)
        void EditorSetFloat(const NameID& name, float value);
        CEMeta(Cli)
        void EditorSetFloat2(const NameID& name, Float2 value);
        CEMeta(Cli)
        void EditorSetFloat3(const NameID& name, Float3 value);
        CEMeta(Cli)
        void EditorSetFloat4(const NameID& name, Float4 value);
        CEMeta(Cli)
        void EditorSetTexture(const NameID& name, const char* value);
        CEMeta(Cli)
        void EditorSetShader(const NameID& name, const char* value);

        CEMeta(Cli)
        std::string EditorGetPassNameList();

        CEMeta(Cli)
        void CreatePass(const NameID& passID);
        CEMeta(Cli)
        void DeletePass(const NameID& passID);
        CEMeta(Cli)
        void RenamePass(const NameID& oldName, const NameID& newName);
        CEMeta(Cli)
        void CreateGroup(const NameID& name);
        CEMeta(Cli)
        void DeleteGroup(const NameID& name);
        CEMeta(Cli)
        void RenameGroup(const NameID& oldName, const NameID& newName);
        void SetShader(const NameID& passID, ShaderPtr shader);
        CEMeta(Cli)
        void SetRenderState(const NameID& passID, FxRenderState renderState);
        void SetRenderState(const NameID& passID, const StateType& renderState);
        void SetTargetBlendState(const NameID& passID, const NGITargetBlendStateDesc& state, int index);

        virtual void SetPassEnable(const NameID& passID, bool enable) override;
        virtual void SetBlendState(const NameID& passID, const NGIBlendStateDesc& state) override;
        virtual void SetBlendStateProp(const NameID& passID, const NGIBlendStateDesc& blendDesc) override;
        virtual void SetDepthStencilState(const NameID& passID, const NGIDepthStencilStateDesc& state) override;
        virtual void SetDepthStencilStateProp(const NameID& passID, const NGIDepthStencilStateDesc& depthStencilDesc) override;
        virtual void SetRasterizerState(const NameID& passID, const NGIRasterizationStateDesc& state) override;
        virtual void SetDynamicState(const NameID& passID, const NGIDynamicStateDesc& state) override;
        CEMeta(Cli)
        virtual void SetRenderGroup(const NameID& passID, UInt32 renderGroup) override;
        virtual void SetRenderGroupProp(const NameID& passID, UInt32 val) override;

    public:
        virtual FxPtr GetFx() const override;
        virtual MaterialInterfacePtr CreateInstance() override;
        virtual MaterialPtr CreateTempInstance() override;
        virtual MaterialRenderState EditorGetRenderState(const NameID& passID) const override;
        virtual bool IsDependent(const std::string& materialGuid) override;

        virtual void EditorVisitProperty(PropertyAccesser func) override;
        virtual std::string EditorGetPropertyString() override;
        virtual std::string EditorGetPassString() override;
        virtual void EditorSetRenderState(const NameID& passID, MaterialRenderState renderState) override;
        CEMeta(Cli)
        int EditorGetPassRenderGroup(const NameID& passID);
        CEMeta(Cli)
        std::string EditorGetPassShaderPath(const NameID& passID);
        CEMeta(Cli)
        FxRenderState EditorFxGetRenderState(const NameID& passID);
        CEMeta(Cli)
        BlendStateDescForEditor EditorGetBlendState(const NameID& passID) const;
        CEMeta(Cli)
        DepthStencilStateDesc EditorGetDepthStencilState(const NameID& passID) const;
        CEMeta(Cli)
        RasterizationStateDesc EditorGetRasterizationState(const NameID& passID) const;
        CEMeta(Cli)
        DynamicStateDesc EditorGetDynamicState(const NameID& passID) const;
        CEMeta(Cli)
        std::string EditorGetPropertyGroupName(const char* name);


        CEMeta(Cli)
        void EditorSetDepthStencilState(const NameID& passID,  DepthStencilStateDesc state);
        CEMeta(Cli)
        void EditorSetBlendState(const NameID& passID,  BlendStateDescForEditor state);
        CEMeta(Cli)
        void EditorSetRasterizerState(const NameID& passID,  RasterizationStateDesc state);
        CEMeta(Cli)
        void EditorSetDynamicState(const NameID& passID,  DynamicStateDesc state);


        virtual bool EditorGetBlendEnable(const NameID& passID) override;
        virtual bool EditorIsPropertyOverrided(const NameID& propID) override;
        virtual bool EditorIsRenderGroupOverrided(const NameID& passID) override;
        virtual bool EditorIsRenderStateOverrided(const NameID& passID) override;
        virtual void EditorNotitfySubMaterial() override;
        virtual void AfterAssetDelete() override;

        void NotifyChange();
        virtual void NotifyChangeRecursively() override;

        virtual void FillParameterCollectionValue(MPCPtr mpc) override;
        virtual void FillAllParameterCollectionValue() override;
        virtual bool HasMpcError() override;
        virtual void RegistAllMpc() override;
        virtual void UnregistAllMpc() override;

    public:
        const std::string& GetExpressionsString() const { return mExpressionsString; }
        void SetExpressionString(std::string_view str) { mExpressionsString = std::string(str); }
        void SetMaterialParameterCollectionUsage(const MaterialParameterCollectionUsageInfo& mpcUsage);

        auto& GetMaterialDefines() { return mDefines; }
        auto& GetMaterialParameterCollectionInfo() { return mMpcs; }

        enum class MaterialParamType
        {
            Scalar,
            Vector,
            ShaderConst,
            Texture,
        };

        NameID GetParameterRemappedName(MaterialParamType paramType, NameID name);

        auto& GetParameterInfos()
        {
            return mParameterInfos;
        }

    private:
        std::vector<NameID> mPropertyNames;
        std::vector<NameID> mPassNames;
        std::vector<NameID> mGroupNames;
        std::unordered_map<NameID, Property> mProperties;
        std::unordered_map<NameID, Pass> mPasses;
        std::unordered_map<NameID, UInt8> mPropertyFlags;

        std::vector<MaterialParameterCollectionInfo> mMpcs;

        // material editor related data
        std::string mExpressionsString;
        MaterialDefines mDefines{};
        ParameterInfos mParameterInfos{};
    };
}}   // namespace cross::resource
