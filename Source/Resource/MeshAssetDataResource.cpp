#include "EnginePrefix.h"
#include "Resource/MeshAssetDataResource.h"
#include "CECommon/Utilities/NDACommon.h"
#include "CECommon/Common/SettingsManager.h"
#include "Resource/AssetFileHeader.h"
#include "Resource/resourceasset.h"
#include "Resource/IResourceInterface.h"

namespace cross
{
    namespace resource
    {
        MeshAssetDataResource::MeshAssetDataResource()
            : Resource()
        {
            INIT_RTTI_IN_CONSTRUCTOR;
        }

        MeshAssetDataResource::~MeshAssetDataResource()
        {
            mStreamableProxy.reset();
            if (mSerializer && mArchive)
            {
                mArchive->GetMMapedFile()->Close();
                mArchive.reset();
                mSerializer.reset();
            }
        }
        
        bool MeshAssetDataResource::CrossSchemaToMeshAssetData(CrossSchema::ImportMeshAssetData& importMeshAsset)
        {
            return true;
        }

        bool MeshAssetDataResource::Serialize(const CrossSchema::ImportMeshAssetDataT& inMeshAssetAsset)
        {
            ClassIDType classID = ClassID(MeshAssetDataResource);
            
            const std::string ndaFilePath = mAsset->GetName();
            
            AssetFileHeader h;
            h.SetMagicNumber(ASSET_MAGIC_NUMBER_JMETA);
            h.SetVersion(Resource::gResourceJsonHeaderVersion);
            h.SetGUID(0x0001, 0x0002);
            h.SetClassID(classID);
            h.SetContenType((UInt32)CONTENT_TYPE::CONTENT_TYPE_FLATBUFFER);
            //h.SetIsStreamFile(mIsStreamFile);

            //cross::FileArchive archive{file};
            //cross::SimpleSerializer serializer{archive};
            CrossSchema::ResourceHeader header(ASSET_MAGIC_NUMBER, 0, classID, 0, 0);
            flatbuffers::FlatBufferBuilder meshBuilder(4096);

            auto mloc = CrossSchema::CreateImportMeshAssetData(meshBuilder, &inMeshAssetAsset);

            auto name = PathHelper::GetBaseFileName(ndaFilePath);

            auto mloc2 = CrossSchema::CreateResourceAsset(meshBuilder, &header, meshBuilder.CreateString(name), CrossSchema::ResourceType::ImportMeshAssetData, mloc.Union());
            FinishResourceAssetBuffer(meshBuilder, mloc2);
            CreateAsset(ndaFilePath);
            Assert(this->GetClassID() == classID);
            
            const int bufferSize = sizeof(uint8_t) * meshBuilder.GetSize();
            std::vector<char> fbBufVec;
            fbBufVec.resize(bufferSize);
            std::transform(meshBuilder.GetBufferPointer(), meshBuilder.GetBufferPointer() + meshBuilder.GetSize(), fbBufVec.begin(), [](char v) {return static_cast<uint8_t>(v);});

            //add custom stuff
            SerializeNode meshAssetLodSettingNode;
            meshAssetLodSettingNode[MeshAssetLODSetting_KEY] = GetLODSetting()->Serialize();
            meshAssetLodSettingNode[SimplifySetting_KEY] = GetSimplify()->Serialize();

            //AddReferenceResource("linsh1");
            //AddReferenceResource("linsh2");
            //AddReferenceResource("linsh3");

           return Resource::Serialize(fbBufVec, ndaFilePath, std::move(meshAssetLodSettingNode));
        }


        bool MeshAssetDataResource::Deserialize(FBSerializer const & s)
        {
            mOffset = static_cast<UInt32>(s.InitialOffset());
            auto fbmesh = CrossSchema::GetResourceAsset(s.GetArchive().Data() + s.InitialOffset())->resource_as_ImportMeshAssetData();

            mMeshAssetData.Clear();
            mMeshAssetData.DeserializeFromCrossSchema(*fbmesh);
            mMeshAssetData.SetAssetGUID(mAsset->GetGuid());
            if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() != AppStartUpTypeHeadless)
            {
                mMeshAssetData.GetRenderMesh()->BuildStaticMesh();
            }

            if (gResourceMgr.mStreamingMgr->IsStreamingEnabled() && mMeshAssetData.IsMeshStreamable() && !mStreamableProxy)
            {
                // We create GPU buffers later, current data should stay in CPU for a while
                ResidentInCpu = true;
                mStreamableProxy = gResourceMgr.mCreateRenderObjectMgr->GetStreamable(ResourcePtr(this));
            }

            //const IMeshBuilderUtils* meshBuilderUtils = GetMeshBuilderUtils();
            //Assert(meshBuilderUtils);
            //mMeshAssetData.SetSharedRenderGeometry(meshBuilderUtils->BuildStaticMesh(&mMeshAssetData));
            //need optimize
            if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() == AppStartUpTypeCrossEditor)
            {
                BuildCollisionMesh();
            }
            OnDataUploaded();
            return true;
        }

        int MeshAssetDataResource::Mesh_UpdateLodMesh(int lod, const char* lodPath)
        {
            auto lodResPtr = gAssetStreamingManager->LoadSynchronously(lodPath);
            if (!lodResPtr || lodResPtr->GetClassID() != ClassID(MeshAssetDataResource))
                return static_cast<int>(cross::EditMeshLodError::ERR_EDITOR_RES_TYPE);
            return static_cast<int>(UpdateLodMesh(static_cast<UInt8>(lod), TYPE_CAST(cross::resource::MeshAssetDataResource*, lodResPtr.get())));
        }

        int MeshAssetDataResource::Mesh_AddLodMesh(const char* lodPath)
        {
            auto lodResPtr = gAssetStreamingManager->LoadSynchronously(lodPath);
            if (!lodResPtr || lodResPtr->GetClassID() != ClassID(MeshAssetDataResource))
                return static_cast<int>(cross::EditMeshLodError::ERR_EDITOR_RES_TYPE);
            return static_cast<int>(AddLodMesh(TYPE_CAST(cross::resource::MeshAssetDataResource*, lodResPtr.get())));
        }

        int MeshAssetDataResource::Mesh_DelLodMesh(int lod)
        {
            return static_cast<int>(DelLodMesh(static_cast<UInt8>(lod)));
        }

        bool MeshAssetDataResource::Mesh_HasCluster()
        {
            return false;
        }
        void MeshAssetDataResource::OnDataUploaded() 
        {
            if (!ResidentInCpu && EngineGlobal::GetSettingMgr()->GetAppStartUpType() == AppStartUpTypeStandAlone)
            {
                mMeshAssetData.OnDataUploaded();
            }
        }

        void MeshAssetDataResource::ReloadMesh()
        {
            if (!mSerializer)
            {
                mArchive = std::unique_ptr<MemoryMappingArchive>(reinterpret_cast<MemoryMappingArchive*>(ResourceAssetManager::Instance().MemoryMapFile(mAsset->GetName(), filesystem::MapRange::WholeFile)));
                mSerializer = std::make_unique<cross::FBSerializer>(*mArchive, mOffset);
            }
            auto fbmesh = CrossSchema::GetResourceAsset(mSerializer->GetArchive().Data() + mSerializer->InitialOffset())->resource_as_ImportMeshAssetData();
            mMeshAssetData.Clear();
            mMeshAssetData.DeserializeFromCrossSchema(*fbmesh);

            if (gResourceMgr.mStreamingMgr->IsStreamingEnabled() && mMeshAssetData.IsMeshStreamable() && !mStreamableProxy)
            {
                mStreamableProxy = gResourceMgr.mCreateRenderObjectMgr->GetStreamable(ResourcePtr(this));
            }

            mMeshAssetData.GetRenderMesh()->BuildStaticMesh();

            //const IMeshBuilderUtils* meshBuilderUtils = GetMeshBuilderUtils();
            //mMeshAssetData.SetSharedRenderGeometry(meshBuilderUtils->BuildStaticMesh(&mMeshAssetData));
            mArchive->GetMMapedFile()->Close();
        }

        EditMeshLodError MeshAssetDataResource::UpdateLodMesh(UInt8 lod, MeshAssetDataResource* meshResource)
        {
            auto ret = GetAssetData()->UpdateLodMeshData(lod, meshResource->GetAssetData());
            if (ret == EditMeshLodError::ERR_EDITOR_SUCCESS)
            {
                if (!Serialize(PathHelper::GetAbsolutePath(GetName())))
                    ret = EditMeshLodError::ERR_EDITOR_SERIALIZE;
            }
            return ret;
        }

        EditMeshLodError MeshAssetDataResource::AddLodMesh(MeshAssetDataResource* meshResource)
        {
            auto ret = GetAssetData()->AddLodMeshData(meshResource->GetAssetData());
            if (ret == EditMeshLodError::ERR_EDITOR_SUCCESS)
            {
                if (!Serialize(PathHelper::GetAbsolutePath(GetName())))
                    ret = EditMeshLodError::ERR_EDITOR_SERIALIZE;
            }
            return ret;
        }

        EditMeshLodError MeshAssetDataResource::DelLodMesh(UInt8 lod)
        {
            auto ret = GetAssetData()->DelLodMeshData(lod);
            if (ret == EditMeshLodError::ERR_EDITOR_SUCCESS)
            {
                if (!Serialize(PathHelper::GetAbsolutePath(GetName())))
                    ret = EditMeshLodError::ERR_EDITOR_SERIALIZE;
            }
            return ret;
        }

        int MeshAssetDataResource::Combine(const std::vector<MeshAssetDataResourcePtr>& inMeshAsset, const std::vector<Float4x4>& trans, const std::vector<std::vector<std::array<int, 4>>>& texIndexs, MeshAssetDataResourcePtr outMeshAsset)
        {
            std::vector<MeshAssetData*> inMeshDatas;
            for (const auto& meshAsset : inMeshAsset)
            {
                inMeshDatas.emplace_back(&(meshAsset->mMeshAssetData));
            }
            return MeshAssetData::Combine(inMeshDatas, trans, texIndexs, outMeshAsset->mMeshAssetData);
        }

        int MeshAssetDataResource::SeparateTo(Float2 point, Float2 blockSize, std::vector<MeshAssetDataResourcePtr>& outMeshAssets, bool splitToPart) 
        {
            int ret = 0;
            if (splitToPart)
            {
                MeshAssetData meshData;
                ret = mMeshAssetData.SeparateTo(point, blockSize, meshData);
                if (ret >= 0)
                {
                    if (meshData.GetVertexCount() > 0)
                    {
                        std::string resPath = GetName();
                        auto last_pos = resPath.rfind('/') + 1;
                        std::string filename = resPath.substr(last_pos, resPath.rfind('.') - last_pos);
                        resPath.replace(last_pos, resPath.length(), filename + ".instance.nda");
                        MeshAssetDataResource* res = new MeshAssetDataResource();
                        //res->mClassID = ClassID(MeshAssetDataResource);
                        res->mMeshAssetData = std::move(meshData);
                        res->mLODSetting = mLODSetting;
                        res->CreateAsset(resPath);
                        outMeshAssets.emplace_back(MeshAssetDataResourcePtr(res));
                    }
                }   
            }
            else
            {
                std::vector<MeshAssetData> meshDatas;
                ret = mMeshAssetData.SeparateTo(point, blockSize, meshDatas);
                for (UInt32 idx = 0; idx < meshDatas.size(); idx++)
                {
                    const auto& meshData = meshDatas[idx];
                    if (meshData.GetVertexCount() <= 0)
                        continue;
                    std::string resPath = GetName();
                    auto last_pos = resPath.rfind('/') + 1;
                    std::string filename = resPath.substr(last_pos, resPath.rfind('.') - last_pos);
                    resPath.replace(last_pos, resPath.length(), filename + "_parts/" + filename + ".part" + std::to_string(idx) + ".nda");
                    MeshAssetDataResource* res = new MeshAssetDataResource();
                    //res->mClassID = ClassID(MeshAssetDataResource);
                    res->mMeshAssetData = std::move(meshDatas[idx]);
                    res->mLODSetting = mLODSetting;
                    res->CreateAsset(resPath);
                    outMeshAssets.emplace_back(MeshAssetDataResourcePtr(res));
                }
            }           
            return ret;
        }

        void MeshAssetDataResource::BuildCollisionMesh()
        {
            const int allSubMeshCount = mMeshAssetData.GetAllLodMeshPartCount();
            mCollisionMeshVec.resize(allSubMeshCount);
            for (int i = 0; i < allSubMeshCount; i++)
            {
                mCollisionMeshVec[i].InitCollisionMesh(mMeshAssetData, i);
            }
        }

        bool MeshAssetDataResource::Serialize(std::string ndapath)
        {
            bool ret = false;
            std::vector<char> fbBufVec;
            ret = mMeshAssetData.SerializeToCrossSchema(fbBufVec, ndapath);
            if (!ret)
                return false;

            std::string absolutePath = PathHelper::GetAssetAbsolutePath(ndapath);
            Assert(GetClassID() == ClassID(MeshAssetDataResource));
            
            //add custom stuff
            SerializeNode meshAssetLodSettingNode;
            meshAssetLodSettingNode[MeshAssetLODSetting_KEY] = GetLODSetting()->Serialize();
            meshAssetLodSettingNode[SimplifySetting_KEY] = GetSimplify()->Serialize();
            Resource::Serialize(fbBufVec, absolutePath,  std::move(meshAssetLodSettingNode));
            return true;
        }

        bool MeshAssetDataResource::Serialize(SerializeNode&& s, const std::string& path)
        {
            if (!Resource::HasAsset())
            {
                CreateAsset(path);
            }

            Resource::ClearReference();

           return Serialize(path);
        }
    
        void MeshAssetDataResource::DeserializeCustomNode(const DeserializeNode& customNode)
        {
            auto settingsNodeOption = customNode.HasMember(Resource::MeshAssetLODSetting_KEY);
            if (settingsNodeOption)
            {
                GetRawLODSetting()->Deserialize(settingsNodeOption.value());
                mMeshAssetData.SetMeshStreamable(GetRawLODSetting()->mIsStreamable);
            }

            auto settingsTriangleRate = customNode.HasMember(Resource::SimplifySetting_KEY);
            if (settingsTriangleRate) 
            {
                GetSimplify()->Deserialize(customNode);
            }
        }

        bool MeshAssetDataResource::ResetResource()
        {
            Resource::ResetResource();
            mMeshAssetData.Clear();
            mCollisionMeshVec.clear();
            return true;
        }

        bool MeshAssetDataResource::IsMeshAssetStreamable() const
        {
            return mLODSetting.mIsStreamable;
        }

        void MeshAssetDataResource::SetMeshAssetStreamable(bool enabled)
        {
            mLODSetting.mIsStreamable = enabled;
            if (mAsset)
            {
                Serialize(mAsset->GetName());
                mStreamableProxy.reset();
                gResourceMgr.ReloadResource(ResourcePtr(this));
            }
        }

        SerializeNode MeshAssetLODSetting::Serialize() const
        {
            SerializeNode result;
            SerializeNode settingsNode;
            for (const auto& levelSetting : mLevelSettings)
            {
                SerializeNode settingNode;
                settingNode["fadeTransitionWidth"] = levelSetting.mFadeTransitionWidth;
                settingNode["screenRelativeTransitionHeight"] = levelSetting.mScreenReleativeTransitionHeight;
                settingsNode.PushBack(std::move(settingNode));
            }
            result["lodLevelSetting"] = std::move(settingsNode);
            result["culledHeight"] = mCulledHeight;
            result["isStreamable"] = mIsStreamable;
            
            return result;
        }

        void MeshAssetLODSetting::Deserialize(const DeserializeNode& json)
        {
            if (!json.IsArray())
            {
                if (auto t = json.HasMember("lodLevelSetting"))
                {
                    const auto& lodLevelSettingJson = *t;
                    AssertMsg(lodLevelSettingJson.IsArray(), "Lod level setting is serialized as array and will be deserialized as array.");
                    for (auto i = 0; i < lodLevelSettingJson.Size(); ++i)
                    {
                        if (lodLevelSettingJson[i].HasMember("fadeTransitionWidth"))
                        {
                            mLevelSettings[i].mFadeTransitionWidth = lodLevelSettingJson[i]["fadeTransitionWidth"].AsFloat();
                            mLevelSettings[i].mScreenReleativeTransitionHeight = lodLevelSettingJson[i]["screenRelativeTransitionHeight"].AsFloat();
                        }
                    }
                }
                if (auto t = json.HasMember("culledHeight"))
                {
                    mCulledHeight = t->AsFloat();
                }
                if (auto t = json.HasMember("isStreamable"))
                {
                    mIsStreamable = t->AsBoolean();
                }
            }
            // for compatibility
            else
            {
                for (auto i = 0; i < json.Size(); ++i)
                {
                    if (json[i].HasMember("fadeTransitionWidth"))
                    {
                        mLevelSettings[i].mFadeTransitionWidth = json[i]["fadeTransitionWidth"].AsFloat();
                        mLevelSettings[i].mScreenReleativeTransitionHeight = json[i]["screenRelativeTransitionHeight"].AsFloat();
                    }
                }
            }
        }

        SerializeNode Simplify::Serialize() const
        {
            SerializeNode settingsNode;
            settingsNode["triangleRate"] = triangleRate;
            return settingsNode;
        }

        void Simplify::Deserialize(const DeserializeNode& json)
        {
            if (json.HasMember("triangleRate"))
            {
                triangleRate = json["triangleRate"].AsInt32();
            }
        }
    }//namespace resource
}//namespace cross
