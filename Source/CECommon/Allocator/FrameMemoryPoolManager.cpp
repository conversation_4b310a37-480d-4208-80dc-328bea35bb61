#include "PlatformPrefix.h"
#include <new>
#include <stdlib.h>
#include "FrameMemoryPoolManager.h"
#include "Log.h"
#include "memory/Module.h"
#include "CrossBase/Profiling/Profiling.h"
#include "tracy/Tracy.hpp"
#include "CrossBase/Template/TypeTraits.hpp"
namespace cross
{
MemoryBlock::MemoryBlock(const UInt32 size, const MemoryAlignmentSize align, void* buffer, UInt8 lifetime)
    : mSize(size)
    , mAlign(align)
    , mBuffer(buffer)
    , mLifeTime(lifetime)
{
}

MemoryBlock::~MemoryBlock()
{
    if (mBuffer)
    {
        mBuffer = nullptr;
    }
}

bool MemoryBlock::Disable()
{
    return true;
}

bool MemoryBlock::Enable()
{
    return true;
}

MemoryBlock* FrameMemoryPoolManager::GetFrameMemoryBlock_threadsafe(const UInt32 frameParamIdx, const UInt8 times, const UInt8 lifetime)
{
    return nullptr;
}
FrameMemoryPoolManager::FrameMemoryPoolManager() {}

bool FrameMemoryPoolManager::ReturnFrameMemoryBlock_threadsafe(MemoryBlock* block, const UInt32 frameParamIdx){
    return true;
}

void FrameMemoryPoolManager::ReleaseUnusedBlocks_threadsafe_force()
{
    for (UInt32 i = 0; i < MAX_FRAME_PARAM_COUNT; ++i)
    {
        ReleaseUnusedBlocks_threadsafe_force(i);
    }
}

void FrameMemoryPoolManager::ReleaseUnusedBlocks_threadsafe_force(const UInt32 frameParamId)
{
    {
        std::lock_guard<std::mutex> lock(mMtx);
        for (auto& [times, reservedBlocks] : mAvailableFrameBlocks)
        {
            for (MemoryBlock* block : reservedBlocks)
            {
                delete block;
            }
            reservedBlocks.clear();
        }
    }
}

void FrameMemoryPoolManager::ReleaseUnusedBlocks_threadsafe_soft(const UInt32 frameParamId)
{
    {
        std::lock_guard<std::mutex> lock(mMtx);
        for (auto& [times, reservedBlocks] : mAvailableFrameBlocks)
        {
            SInt32 i = ToSInt32(reservedBlocks.size()) - 1;
            for (; i >= 0; --i)
            {
                MemoryBlock* block = reservedBlocks[i];
                block->RecordUnusedFrames();
                if (block->GetUnusedFrames() >= FRAME_MEMORY_MAINTAIN_TIME)
                {
                    QUICK_SCOPED_CPU_TIMING("FrameAllocatorFreeBlocks");
                    RealFreeCountFrame++;
                    delete block;
                    reservedBlocks[i] = nullptr;
                }
            }
            reservedBlocks.erase(std::remove(reservedBlocks.begin(), reservedBlocks.end(), nullptr), reservedBlocks.end());
        }
    }
}

void FrameMemoryPoolManager::EndFrame(const UInt32 frameParamId)
{
}

FrameMemoryPoolManager::~FrameMemoryPoolManager() {}

std::vector<MemoryBlock*>& FrameMemoryPoolManager::GetAvailableBlocks(const UInt32 frameParamId, const UInt8 times)
{
    return mAvailableFrameBlocks[times];
}

}
