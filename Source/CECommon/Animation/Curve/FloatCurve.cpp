#include "EnginePrefix.h"
#include "CECommon/Animation/Curve/FloatCurve.h"

namespace cross {
#define Is<PERSON><PERSON>(x) ((x) > -MathUtils::MathEps && (x) < MathUtils::MathEps)
static int SolveLinear(double c[2], double& s)
{
    /* linear form: c[1] * x + c[0] = 0 */
    s = -c[0] / c[1];
    return 1;
}

/*
 *   Utility functions to find cubic and quartic roots
 *   https://github.com/erich666/GraphicsGems/blob/master/gems/Roots3And4.c
 */
static int SolveQuadratic(double c[3], double s[2])
{
    double p, q, D;

    /* normal form: x^2 + px + q = 0 */

    p = c[1] / (2 * c[2]);
    q = c[0] / c[2];

    D = p * p - q;

    if (IsZero(D))
    {
        s[0] = -p;
        return 1;
    }
    else if (D < 0)
    {
        return 0;
    }
    else /* if (D > 0) */
    {
        double sqrt_D = sqrt(D);

        s[0] = sqrt_D - p;
        s[1] = -sqrt_D - p;
        return 2;
    }
}

static int SolveCubic(double c[4], double s[3])
{
    int i, num;
    double sub;
    double A, B, C;
    double sq_A, p, q;
    double cb_p, D;

    /* normal form: x^3 + Ax^2 + Bx + C = 0 */

    A = c[2] / c[3];
    B = c[1] / c[3];
    C = c[0] / c[3];

    /*  substitute x = y - A/3 to eliminate quadric term:
    x^3 +px + q = 0 */

    sq_A = A * A;
    p = 1.0 / 3 * (-1.0 / 3 * sq_A + B);
    q = 1.0 / 2 * (2.0 / 27 * A * sq_A - 1.0 / 3 * A * B + C);

    /* use Cardano's formula */

    cb_p = p * p * p;
    D = q * q + cb_p;

    if (IsZero(D))
    {
        if (IsZero(q)) /* one triple solution */
        {
            s[0] = 0;
            num = 1;
        }
        else /* one single and one double solution */
        {
            double u = cbrt(-q);
            s[0] = 2 * u;
            s[1] = -u;
            num = 2;
        }
    }
    else if (D < 0) /* Casus irreducibilis: three real solutions */
    {
        double phi = 1.0 / 3 * acos(-q / sqrt(-cb_p));
        double t = 2 * sqrt(-p);

        s[0] = t * cos(phi);
        s[1] = -t * cos(phi + MathUtils::MathPi / 3);
        s[2] = -t * cos(phi - MathUtils::MathPi / 3);
        num = 3;
    }
    else /* one real solution */
    {
        double sqrt_D = sqrt(D);
        double u = cbrt(sqrt_D - q);
        double v = -cbrt(sqrt_D + q);

        s[0] = u + v;
        num = 1;
    }

    /* resubstitute */

    sub = 1.0 / 3 * A;

    for (i = 0; i < num; ++i)
        s[i] -= sub;

    return num;
}

// calculate a BezierCurve with 2 end points and 2 control points
/*
         o(p1)        o(p2)
        /               \
       /                 \
      /                   \
     o(p0)                 o(p3)
*/

static double LerpStable(const double& A, const double& B, double Alpha)
{
    return static_cast<double>((A * (1.0f - Alpha)) + (B * Alpha));
}

static double BezierCurveInterp(double p0, double p1, double p2, double p3, float alpha)
{
    const double dp01 = LerpStable(p0, p1, alpha);
    const double dp12 = LerpStable(p1, p2, alpha);
    const double dp23 = LerpStable(p2, p3, alpha);
    const double dp012 = LerpStable(dp01, dp12, alpha);
    const double dp123 = LerpStable(dp12, dp23, alpha);

    double result = LerpStable(dp012, dp123, alpha);
    return result;
}

static double EvalCubicCurve(double leftX, double leftY, double leftTangent, double leftWeight, double rightX, double rightY, double rightTangent, double rightWeight, float inTime)
{
    const double diffX = rightX - leftX;

    // A * x ** 3 + B * x ** 2 + C * x + D = 0
    double coeff[4], solutions[3];

    coeff[0] = leftX - inTime;
    coeff[1] = 3.0f * leftWeight * diffX;
    coeff[2] = 3.0f * (rightX - rightWeight * diffX - leftX - leftWeight * diffX) - coeff[1];
    coeff[3] = diffX - coeff[1] - coeff[2];

    int NumSolutions;
    float resultAlpha = -1.0f;

    if (IsZero(coeff[3]))
    {
        if (IsZero(coeff[2]))
        {
            SolveLinear(coeff, solutions[0]);
            NumSolutions = 1;
        }
        else
        {
            NumSolutions = SolveQuadratic(coeff, solutions);
        }
    }
    else
    {
        NumSolutions = SolveCubic(coeff, solutions);
    }

    if (NumSolutions == 1)
    {
        resultAlpha = static_cast<float>(solutions[0]);
    }
    else
    {
        for (int i = 0; i < NumSolutions; i++)
        {
            float result = static_cast<float>(solutions[i]);
            if (result >= 0.0f && result <= 1.0f && (result > resultAlpha || resultAlpha < 0.0f))
            {
                resultAlpha = result;
            }
        }
        resultAlpha = resultAlpha == -1.0f ? 0.0f : resultAlpha;
    }

    const double p0 = leftY;
    const double p3 = rightY;
    const double p1 = p0 + leftTangent * diffX * leftWeight;
    const double p2 = p3 - rightTangent * diffX * rightWeight;

    return BezierCurveInterp(p0, p1, p2, p3, resultAlpha);
}

static bool Compare2Keys(const FloatCurveKey& key1, const FloatCurveKey& key2)
{
    return key1.Time < key2.Time;
}

static double EvalFor2Keys(const FloatCurveKey& key1, const FloatCurveKey& key2, float inTime)
{
    Assert(inTime >= key1.Time);
    Assert(inTime <= key2.Time);

    const float deltaTime = key2.Time - key1.Time;
    if (deltaTime <= 0.f || key1.InterpType == KIT_CONSTANT)
    {
        return key1.QueryValue<double>();
    }
    else
    {
        const float alpha = (inTime - key1.Time) / deltaTime;

        if (key1.InterpType == KIT_LINEAR)
        {
            return MathUtils::LerpStable(key1.QueryValue<double>(), key2.QueryValue<double>(), alpha);
        }
        else   // key1.InterpType == KIT_SMOOTH
        {
            // Do not need to solve cubic func for 1/3 points
            if (key1.AutoWeighted && key2.AutoWeighted)
            {
                double p0 = key1.QueryValue<double>();

                double p3 = key2.QueryValue<double>();
                const double p1 = p0 + key1.LeaveTangent * deltaTime / 3.0f;
                const double p2 = p3 - key2.ArriveTangent * deltaTime / 3.0f;
                return BezierCurveInterp(p0, p1, p2, p3, alpha);
            }
            else
            {
                return EvalCubicCurve(key1.Time, key1.QueryValue<double>(), key1.LeaveTangent, key1.LeaveWeight, key2.Time, key2.QueryValue<double>(), key2.ArriveTangent, key2.ArriveWeight, inTime);
            }
        }
    }
}

void FloatCurveTrack::RemoveDuplicateKeys()
{
    for (int i = GetNumKeys() - 1; i > 0; i--)
    {
        if (Keys[i] == Keys[i - 1])
        {
            Keys.erase(Keys.begin() + i);
        }
    }
}

float FloatCurveTrack::Eval(float inTime, float DefaultValue) const
{
    // clamp time
    FloatCurveKey keyLeft, keyRight;
    float time;
    UInt32 code = Query2Keys(inTime, keyLeft, keyRight, time);

    if (code == 0)
    {
        return DefaultValue;
    }
    else if (code == 1)
    {
        return keyLeft.Value;
    }
    else if (code == 2)
    {
        return keyRight.Value;
    }
    else if (code == 3)
    {
        return static_cast<float>(EvalFor2Keys(keyLeft, keyRight, time));
    }

    return DefaultValue;
}

double FloatCurveTrack::EvalDouble(float inTime, double DefaultValue) const
{
    //std::cout << "Time : " << inTime << std::endl;
    // clamp time
    FloatCurveKey keyLeft, keyRight;
    float time;
    UInt32 code = Query2Keys(inTime, keyLeft, keyRight, time);

    if (code == 0)
    {
        return DefaultValue;
    }
    else if (code == 1)
    {
        return keyLeft.QueryValue<double>();
    }
    else if (code == 2)
    {
        return keyRight.QueryValue<double>();
    }
    else if (code == 3)
    {
        return EvalFor2Keys(keyLeft, keyRight, time);
    }

    return DefaultValue;
}

UInt32 FloatCurveTrack::Query2Keys(float inTime, FloatCurveKey& keyLeft, FloatCurveKey& keyRight, float& outTime) const
{
    // clamp time
    float clampedTime = ClampTime(inTime);
    const auto NumKeys = GetNumKeys();

    if (NumKeys == 0)
    {
        return 0;
    }
    else if (NumKeys == 1 || clampedTime <= Keys[0].Time)
    {
        keyLeft = Keys[0];
        outTime = Keys[0].Time;
        return 1;
    }
    else if (clampedTime >= Keys[NumKeys - 1].Time)
    {
        keyRight = Keys[NumKeys - 1];
        outTime = Keys[NumKeys - 1].Time;
        return 2;
    }
    else
    {
        FloatCurveKey clampedTimeKey;
        clampedTimeKey.Time = clampedTime;
        auto itr = std::upper_bound(Keys.begin(), Keys.end(), clampedTimeKey, Compare2Keys);
        Assert(itr != Keys.end());
        Assert(itr != Keys.begin());
        keyLeft = *(itr - 1);
        keyRight = *itr;

        outTime = clampedTime;
        return 3;
    }
}

bool FloatCurveTrack::Validate() const
{
    for (auto const& key : Keys)
    {
        bool ret = !(isnan(key.Time) || isnan(key.ArriveTangent) || isnan(key.ArriveWeight) || isnan(key.LeaveTangent) || isnan(key.LeaveWeight) || isnan(key.Time) || isnan(key.QueryValue<double>()) ||

                     isinf(key.Time) || isinf(key.ArriveTangent) || isinf(key.ArriveWeight) || isinf(key.LeaveTangent) || isinf(key.LeaveWeight) || isinf(key.Time) || isinf(key.QueryValue<double>()));
        if (ret)
        {
            continue;
        }
        else
        {
            return false;
        }
    }
    return true;
}

float CycleTime(float inTime, float minTime, float maxTime)
{
    float durationTime = maxTime - minTime;
    Assert(durationTime > 0.f);

    if (inTime < minTime)
    {
        inTime += std::ceilf((minTime - inTime) / durationTime) * durationTime;
    }
    else if (inTime >= maxTime)
    {
        inTime -= (std::floorf((inTime - maxTime) / durationTime) + 1) * durationTime;
    }

    return inTime;
}

float FloatCurveTrack::ClampTime(float inTime) const
{
    const auto NumKeys = Keys.size();

    if (NumKeys <= 1)
    {
        return inTime;
    }

    // mKeys.size >= 2
    if (inTime < Keys[0].Time)
    {
        /*
         * ---inTime--Key[0]------Key[i]------Key[N-1]----
         */
        if (EnterType == CRT_CYCLE)
        {
            return CycleTime(inTime, Keys[0].Time, Keys[NumKeys - 1].Time);
        }
        else
        {
            return Keys[0].Time;
        }
    }
    else if (inTime >= Keys[NumKeys - 1].Time)
    {
        /*
         * -----Key[0]------Key[i]------Key[N-1]--inTime--
         */
        if (LeaveType == CRT_CYCLE)
        {
            return CycleTime(inTime, Keys[0].Time, Keys[NumKeys - 1].Time);
        }
        else
        {
            return Keys[NumKeys - 1].Time;
        }
    }
    else
    {
        /*
         * -----Key[0]---inTime---Key[i]------Key[N-1]----
         */
        return inTime;
    }
}

bool FloatCurveListModifier::AddTrack(const FloatCurveTrack& inTrack)
{
    if (!HasTrack(inTrack.Name))
    {
        mCurveList.mTracks.emplace_back(inTrack);

#if CROSSENGINE_EDITOR
        mCurveList.CacheRankedKeys();
#endif

        return true;
    }
    return false;
}

bool FloatCurveListModifier::RemoveTrack(const UniqueString& inName)
{
    SInt32 index = FindTrackIndex(inName);
    if (index < 0)
    {
        return false;
    }
    else
    {
        mCurveList.mTracks.erase(mCurveList.mTracks.begin() + index);

#if CROSSENGINE_EDITOR
        mCurveList.CacheRankedKeys();
#endif

        return true;
    }
}

bool FloatCurveListModifier::SetTrack(const FloatCurveTrack& inTrack)
{
    SInt32 index = FindTrackIndex(inTrack.Name);
    if (index < 0)
    {
        return false;
    }
    else
    {
        mCurveList.mTracks[index] = inTrack;

#if CROSSENGINE_EDITOR
        mCurveList.CacheRankedKeys();
#endif
    }
    return true;
}

void FloatCurveListModifier::ApplyFloatCurveListInfo(const FloatCurveListInfo& inInfoData)
{
    auto NumTracks = inInfoData.Items.size();
    //temp cancel
    //Assert(mCurveList.mTracks.size() == NumTracks);
    for (size_t iTrack = 0; iTrack < mCurveList.mTracks.size(); iTrack++)
    {
        mCurveList.mTracks[iTrack].Name = inInfoData.Items[iTrack].CurveName;
    }
    if (mCurveList.mTracks.size() != NumTracks) 
    {
        FloatCurveTrack curveTrack;
        mCurveList.mTracks.push_back(curveTrack);
        mCurveList.mTracks[mCurveList.mTracks.size() - 1].Name = "t"; 
    }
}

bool FloatCurveList::HasTrack(const UniqueString& inName) const
{
    return FindTrackIndex(inName) >= 0;
}

SInt32 FloatCurveList::FindTrackIndex(const UniqueString& inName) const
{
    for (SInt32 i = 0; i < static_cast<SInt32>(mTracks.size()); i++)
    {
        if (mTracks[i].Name == inName)
        {
            return i;
        }
    }
    return -1;
}

void FloatCurveList::Serialize(std::vector<UInt8>& curveSet) const
{
    SerializeContext context;
    SerializeNode node = Serialize(context);
    auto const& binData = node.FormatToBin();
    curveSet.insert(curveSet.end(), binData.begin(), binData.end());
}

bool FloatCurveList::Deserialize(const flatbuffers::Vector<uint8_t>* curveData)
{
    SerializeContext context;
    const auto node = DeserializeNode::ParseFromBin(curveData->data(), curveData->size());
    Deserialize(node, context);

#if CROSSENGINE_EDITOR
    CacheRankedKeys();
#endif

    return true;
}

Float1Curve::Float1Curve(const UniqueString& inName /*= "x"*/)
{
    mTracks.push_back({inName});
}

float Float1Curve::Eval(float inTime, float DefaultValue /*= 0.0f*/) const
{
    return mTracks[0].Eval(inTime, DefaultValue);
}

Float2Curve::Float2Curve(const UniqueString& inNameX /*= "x"*/, const UniqueString& inNameY /*= "y"*/)
{
    mTracks.push_back({inNameX});
    mTracks.push_back({inNameY});
}

Float2 Float2Curve::Eval(float inTime, Float2 DefaultValue /*= {0.0f, 0.0f}*/) const
{
    Float2 result = DefaultValue;

    result.x = mTracks[0].Eval(inTime, DefaultValue.x);
    result.y = mTracks[1].Eval(inTime, DefaultValue.y);
    return result;
}

Float3Curve::Float3Curve(const UniqueString& inNameX /*= "x"*/, const UniqueString& inNameY /*= "y"*/, const UniqueString& inNameZ /*= "z"*/)
{
    mTracks.push_back({inNameX});
    mTracks.push_back({inNameY});
    mTracks.push_back({inNameZ});
}

Float3 Float3Curve::Eval(float inTime, Float3 DefaultValue /*= {0.0f, 0.0f, 0.0f}*/) const
{
    Float3 result = DefaultValue;

    result.x = mTracks[0].Eval(inTime, DefaultValue.x);
    result.y = mTracks[1].Eval(inTime, DefaultValue.y);
    result.z = mTracks[2].Eval(inTime, DefaultValue.z);
    return result;
}

Double3Curve::Double3Curve(const UniqueString& inNameX /*= "x"*/, const UniqueString& inNameY /*= "y"*/, const UniqueString& inNameZ /*= "z"*/)
{
    mTracks.push_back({inNameX});
    mTracks.push_back({inNameY});
    mTracks.push_back({inNameZ});
}

Double3 Double3Curve::Eval(float inTime, Double3 DefaultValue /*= {0.0f, 0.0f, 0.0f}*/) const
{
    Double3 result = DefaultValue;
    result.x = mTracks[0].EvalDouble(inTime, DefaultValue.x);
    result.y = mTracks[1].EvalDouble(inTime, DefaultValue.y);
    result.z = mTracks[2].EvalDouble(inTime, DefaultValue.z);
    return result;
}

Float4Curve::Float4Curve(const UniqueString& inNameX /*= "x"*/, const UniqueString& inNameY /*= "y"*/, const UniqueString& inNameZ /*= "z"*/, const UniqueString& inNameW /*= "w"*/)
{
    mTracks.push_back({inNameX});
    mTracks.push_back({inNameY});
    mTracks.push_back({inNameZ});
    mTracks.push_back({inNameW});
}
Float4 Float4Curve::Eval(float inTime, Float4 DefaultValue /*= {0.0f, 0.0f, 0.0f, 0.0f}*/) const
{
    Float4 result = DefaultValue;

    result.x = mTracks[0].Eval(inTime, DefaultValue.x);
    result.y = mTracks[1].Eval(inTime, DefaultValue.y);
    result.z = mTracks[2].Eval(inTime, DefaultValue.z);
    result.w = mTracks[3].Eval(inTime, DefaultValue.w);
    return result;
}

RotEulerCurve::RotEulerCurve(const UniqueString& inNameX, const UniqueString& inNameY, const UniqueString& inNameZ, const UniqueString& inNameW, const UniqueString& inNameT)
{
    mTracks.push_back({ inNameX });
    mTracks.push_back({ inNameY });
    mTracks.push_back({ inNameZ });
    mTracks.push_back({ inNameW });
    mTracks.push_back({ inNameT });
}

Quaternion RotEulerCurve::Eval(float inTime, Quaternion DefaultValue /*= {0.0f, 0.0f, 0.0f}*/) const
{
    Quaternion result = DefaultValue;
    Assert(mTracks[0].GetNumKeys() == mTracks[1].GetNumKeys());
    Assert(mTracks[1].GetNumKeys() == mTracks[2].GetNumKeys());
    Assert(mTracks[2].GetNumKeys() == mTracks[3].GetNumKeys());

    FloatCurveKey xL, xR;
    FloatCurveKey yL, yR;
    FloatCurveKey zL, zR;
    FloatCurveKey wL, wR;
    FloatCurveKey tL, tR;
    float timeX, timeY, timeZ, timeW, timeT;
    UInt32 codeX, codeY, codeZ, codeW, codeT;

    codeX = mTracks[0].Query2Keys(inTime, xL, xR, timeX);
    codeY = mTracks[1].Query2Keys(inTime, yL, yR, timeY);
    codeZ = mTracks[2].Query2Keys(inTime, zL, zR, timeZ);
    codeW = mTracks[3].Query2Keys(inTime, wL, wR, timeW);
    codeT = mTracks[4].Query2Keys(inTime, tL, tR, timeT);

    float tValue = mTracks[4].Eval(inTime);

    Assert(codeX == codeY);
    Assert(codeY == codeZ);
    Assert(codeZ == codeW);

    if (codeX == 0)
    {
        return DefaultValue;
    }
    else if (codeX == 1)
    {
        return Quaternion{xL.Value, yL.Value, zL.Value, wL.Value};
    }
    else if (codeX == 2)
    {
        return Quaternion{xR.Value, yR.Value, zR.Value, wR.Value};
    }
    else if (codeX == 3)
    {
        Quaternion l = Quaternion{xL.Value, yL.Value, zL.Value, wL.Value};
        Quaternion r = Quaternion{xR.Value, yR.Value, zR.Value, wR.Value};

        if (Quaternion::Dot(l, r) < 0.0f)
        {
            r = -r;
        }

        float alpha = 0.0f;
        if (tR.Value - tL.Value > MathUtils::MathEps)
        {
            alpha = (tValue - tL.Value) / (tR.Value - tL.Value);
        }
        return Quaternion::Slerp(l, r, alpha);
    }

    return DefaultValue;
}

TransformCurve::TransformCurve(const UniqueString& inNameRY /*= "Rotation.Yaw"*/, const UniqueString& inNameRP /*= "Rotation.Pitch"*/, const UniqueString& inNameRR /*= "Rotation.Roll"*/, const UniqueString& inNameTX /*= "Translation.X"*/,
                               const UniqueString& inNameTY /*= "Translation.Y"*/, const UniqueString& inNameTZ /*= "Translation.Z"*/, const UniqueString& inNameSX /*= "Scale.X"*/, const UniqueString& inNameSY /*= "Scale.Y"*/,
                               const UniqueString& inNameSZ /*= "Scale.Z" */)
{
    mTracks.reserve(9);
    mTracks.push_back({inNameRY});
    mTracks.push_back({inNameRP});
    mTracks.push_back({inNameRR});

    mTracks.push_back({inNameTX});
    mTracks.push_back({inNameTY});
    mTracks.push_back({inNameTZ});

    mTracks.push_back({inNameSX});
    mTracks.push_back({inNameSY});
    mTracks.push_back({inNameSZ});
}

EventCurve::EventCurve(const UniqueString& inName)
{
    mTracks.push_back({inName});
}



 std::string EventCurve::Eval(float inTime, float deltaTime) const
{
    // std::cout << "Keys :  " << mTracks[0].Keys.size() << std::endl;
    for (size_t i = 0; i < mTracks[0].Keys.size(); i++)
    {
        if (inTime - deltaTime <= mTracks[0].Keys[i].Time && mTracks[0].Keys[i].Time <= inTime)
        {
            return mTracks[0].Keys[i].EventString;
        }
    }
    return "";
}
}   // namespace cross
#undef IsZero
