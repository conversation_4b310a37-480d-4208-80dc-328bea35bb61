#pragma once
#include "CrossBase/Math/CrossMath.h"
#include "Plane.h"

namespace cross {
class CECOMMON_API ConvexVolumn
{
public:
    ConvexVolumn() {}
    ConvexVolumn(const std::vector<Plane>& planes);

    void Init();

    bool IntersectBox(const Float3& origin, const Float3& extent) const;
    bool IntersectBox(const Float3& origin, const Float3& extent, bool& outIsFullyInside) const;
    void IntersectBox(const Float3& origin, const Float3& extent, bool& outIsPartiallyInside, bool& outIsPartiallyOutside) const;

    bool IntersectSphere(const Float3& origin, float radius) const;
    bool IntersectSphere(const Float3& origin, float radius, bool& outIsFullyInside) const;

    bool IntersectPoint(const Float3& point) const
    {
        return IntersectSphere(point, 0.0f);
    }

public:
    std::vector<Plane> planes;
    std::vector<Plane> permutedPlanes;
};
}   // namespace cross