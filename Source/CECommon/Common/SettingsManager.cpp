#include "EnginePrefix.h"
#include <functional>
#include <stdio.h>
#include <ctime>
#include "SettingsManager.h"
#include "CmdSettings.h"
#include "IFileSystemInterface.h"
#include "meta/reflection/meta/meta_class.hpp"
#include "meta/reflection/runtime/cxx_runtime.hpp"
#if CROSSENGINE_WIN
#    include <dxgi.h>
#endif
namespace cross {

    SettingsManager::SettingsManager(IFileSystemInfrastructure* fs)
        : mFileSystem(fs)
        , mEngineConfigFileName("EngineConfig.json")
        , mProjectConfigFileName("ProjectConfig.json")
        , mLayerConfigFileName("LayerConfig.json")
        , mLaunchScene()
        , mSettingSuccessfullyLoaded(false)
        , mLayerSetting(this)
    {
        InitPlatFormType();

        DEBUG_ASSERT(mFileSystem != nullptr);

        // default device type
#if CROSSENGINE_OSX
        mRenderMode = NGIPlatform::Metal;
#elif CROSSENGINE_IOS
        mRenderMode = NGIPlatform::Metal;
#elif CROSSENGINE_WIN
        mRenderMode = NGIPlatform::Vulkan;
#elif CROSSENGINE_ANDROID
        mRenderMode = NGIPlatform::OpenGLES3;
#else
        mRenderMode = NGIPlatform::Vulkan;
#endif
    }



    SettingsManager::~SettingsManager()
    {
        SerializeEngineConfigToFile();
    }

    void SettingsManager::InitPlatFormType()
    {
        //platform type
        {
#if CROSSENGINE_OSX
            mPlatform = PLATFORMTYPE_MACOS;
#elif CROSSENGINE_IOS
            mPlatform = PLATFORMTYPE_IOS;
#elif CROSSENGINE_WIN
            mPlatform = PLATFORMTYPE_WIN;
#elif CROSSENGINE_ANDROID
            mPlatform = PLATFORMTYPE_ANDROID;
#else
            Assert(false);
            mPlatform = PLATFORMTYPE_CONSOLE;
#endif
        }
    }





    std::optional<cross::SerializeNode> SettingsManager::HasSettingOption(const std::string& inName)
    {
        std::optional<cross::SerializeNode> result;
        if (mProjectConfig->HasMember(inName))
        {
            result = mProjectConfig->At(inName)->Clone();
            return result;
        }
        else if (mEngineConfig->HasMember(inName))
        {
            result = mEngineConfig->At(inName)->Clone();
            return result;
        }

        return {};
    }


    std::optional<cross::SerializeNode> SettingsManager::GetSettingOption(const std::string& inName)
    {
        return HasSettingOption(inName);
    }

    void SettingsManager::LoadDecalSetting() 
    {
        GetValue("DecalSetting.enable", mDecalSetting.Enable);
    }

    void SettingsManager::ExtractValue(std::string PrevKey, const DeserializeNode& node)
    {
        for (auto it = node.begin(); it != node.end(); it++)
        {
            std::string key = it.Key().data();
            key = key.substr(0, it.Key().size());
            auto value = it.Value();
            
            key = PrevKey.empty() ? key : PrevKey + "." + key;

            if (value.IsObject())
            {
                ExtractValue(key, value);
                continue;
            }

            if (value.IsArray() && value.Size() > 0)
            {
                if (value[0].IsNumber())
                {
                    std::vector<double> v;
                    for (int i = 0; i < value.Size(); i++)
                    {
                        v.push_back(static_cast<double>(value[i].AsDouble()));
                    }
                    mContext[key] = v;
                }
            }
            else if (value.IsBoolean())
            {
                mContext[key] = value.AsBoolean();
            }
            else if (value.IsString())
            {
                mContext[key] = value.AsString();
            }
            else if (value.IsFloatingPoint())
            {
                mContext[key] = value.AsFloat();
            }
            else if (value.IsIntegral())
            {
                mContext[key] = value.AsInt32();
            }
        }
    }

    void SettingsManager::ExtractLayers(const DeserializeNode& node) 
    {
        for (auto it = node.begin(); it != node.end(); it++)
        {
            std::string key = it.Key().data();
            key = key.substr(0, it.Key().size());
            auto value = it.Value();

            if (key == "Layers" && value.IsObject()) 
            {
                for (auto layerIter = value.begin(); layerIter != value.end(); layerIter++) 
                {
                    std::string layerKey = layerIter.Key().data();
                    layerKey = layerKey.substr(0, layerIter.Key().size());
                    auto layerValue = layerIter.Value();

                    UInt32 layerIndex = stoi(layerKey);
                    auto layerName = layerValue.AsString();

                    if (layerName.size() != 0) 
                    {
                        mLayerSetting.SetLayer(layerIndex, layerName);
                    }
                }

                break;
            }
        }
    }

    auto FindNodeByPath(SerializeNode& node, std::string path) -> std::optional<SerializeNode>
    {
        size_t pos = 0;
        std::string token;
        const std::string delimiter = ".";

        if ((pos = path.find(delimiter)) != std::string::npos)
        {
            token = path.substr(0, pos);
            path.erase(0, pos + delimiter.length());
            if (auto child = node.HasMember(token))
            {
                return FindNodeByPath(child.value(), path);
            }
            else
                return nullptr;
        }
        else
        {
            return node.HasMember(path);
        }
    }

    void SettingsManager::OverrideProjectConfig(ScalabilityLevel level, const DeserializeNode& node)
    {
        auto renderPipelineSetting = mProjectConfig->HasMember("RenderPipelineSetting");
        if (!renderPipelineSetting)
            return;

        for (auto it = node.begin(); it != node.end(); it++)
        {
            std::string item = it.Key().data();
            auto array = it.Value();
            if (array.Size() <= static_cast<UInt32>(level))
                continue;
            auto pairs = array[static_cast<UInt32>(level)];

            for (auto setting = pairs.begin(); setting != pairs.end(); setting++)
            {
                auto key = setting.Key();
                auto path = std::string(key);
                auto result = FindNodeByPath(renderPipelineSetting.value(), path);
                if (result)
                    result.value() = std::move(SerializeNode::CreateFromDeserializeNode(setting.Value()));
            }
        }
    }

    void SettingsManager::LoadAndApplyScalabilityConfig()
    {
        std::string scalabilityConfigFileName;
        if (mContext.GetValue("Scalability.filename", scalabilityConfigFileName))
        {
            auto scalabilityConfig = LoadConfigFile(PathHelper::GetAssetAbsolutePath(scalabilityConfigFileName));
            if (!scalabilityConfig)
            {
                scalabilityConfig = LoadConfigFile(PathHelper::GetEngineResourceAbsolutePath(scalabilityConfigFileName));
            }

            if (scalabilityConfig)
            {
                int level = SCALABILITY_LEVEL_INVALID;
                mContext.GetValue("Scalability.level", level);
                if (level != SCALABILITY_LEVEL_INVALID)
                {
                    OverrideProjectConfig(static_cast<ScalabilityLevel>(level), scalabilityConfig.value());
                }
            }
        }
    }

    bool SettingsManager::LoadFromFile()
    {
        mSettingSuccessfullyLoaded = false;

#if CROSSENGINE_IOS || CROSSENGINE_ANDROID
        const std::string& documentPath = PathHelper::GetDocumentDirectoryPath();
        mEngineConfig = LoadConfigFile(documentPath + "/" + mEngineConfigFileName);
        mProjectConfig = LoadConfigFile(documentPath + "/" + mProjectConfigFileName);
        mLayerConfig = LoadConfigFile(documentPath + "/" + mLayerConfigFileName);
        ExtractValue("", mEngineConfig.value());
        ExtractValue("", mProjectConfig.value());
        if (mLayerConfig.has_value())
        {
            ExtractLayers(mLayerConfig.value());
        }

        LoadAndApplyScalabilityConfig();

#endif


#if !CROSSENGINE_UNITTEST
        if (!mEngineConfig)
        { 
            mEngineConfig = LoadConfigFile(PathHelper::GetEngineResourceAbsolutePath(mEngineConfigFileName));
            ExtractValue("", mEngineConfig.value());
        }

        if (!mProjectConfig)
        {
            mProjectConfig = LoadConfigFile(PathHelper::GetAssetAbsolutePath(mProjectConfigFileName));
            ExtractValue("", mProjectConfig.value());

            LoadAndApplyScalabilityConfig();
        }
        
        if (!mLayerConfig)
        {
            mLayerConfig = LoadConfigFile(PathHelper::GetAssetAbsolutePath(mLayerConfigFileName));
            if (mLayerConfig.has_value())
            {
                ExtractLayers(mLayerConfig.value());
            }
        }

        ///////////////////////////////////////////////////////////////////////////////
        Assert(mEngineConfig && mProjectConfig);

        std::optional<cross::SerializeNode> result;
        // This is a project-config-only property
        if (!GetValue<std::string>("Launch", mLaunchScene))
        {
            LOG_ERROR("Startup scene is not specified.");
            Assert(0);
            return false;
        }

        // This is a engine-config-only property
        if (result = mEngineConfig->HasMember("EnginePreloadResources"); result && result->IsArray())
        {
            for (int32_t i = 0; i != result->Size(); i++)
            {
                Assert(result->At(i).IsString());
                mEnginePreloadResources.push_back(result->At(i).AsString());
            }
        }
        else
        {
            LOG_INFO("Preloaded engine resources are not specified.");
        }


        GetValue("UseSeparateRenderingThread", mUseSeparateRenderingThread);
        GetValue("UseSeparatePresentThread", mUseSeperatePresentThread);
        GetValue("NumTaskThreads", mNumTaskThreads);
        GetValue("MaxTickRates", mMaxTickRates);
        GetValue("EnableDLSS", mEnableDLSS);
        GetValue("EnableDLSSG", mEnableDLSSG);
        GetValue<std::string>("ClientName", mClientName);
        GetValue("UseFFXFrameInterpolation", mFFXFrameInterpolation);
        if (mEnableDLSSG)
        {
            CmdSettings::Inst().gMaxQueuedFrame = 4;
        }
        GetValue("ShowDLSSDebugInfo", mShowDLSSDebugInfo);
        do
        {
            if (HasKey<bool>("AsNGIBackendQA")) {
                if (GetKeyValue<bool>("AsNGIBackendQA"))
                {
                    static constexpr NGIPlatform gSupportedRendererModes[]
                    {
                    #if NGI_SUPPORTS_VULKAN
                        NGIPlatform::Vulkan,
                    #endif
                    #if NGI_SUPPORTS_METAL
                        NGIPlatform::Metal,
                    #endif
                    #if NGI_SUPPORTS_OPENGLES30
                        NGIPlatform::OpenGLES3,
                    #endif
                    };

                    std::time_t t = std::time(nullptr);
                    std::tm* now = std::localtime(&t);
                    mRenderMode = gSupportedRendererModes[now->tm_mday % std::extent_v<decltype(gSupportedRendererModes)>];
                    break;
                }
            }

            // Project config can override enigne config.
            int  value = static_cast<int>(mRenderMode);
            GetValue("RendererMode", value);
            mRenderMode = static_cast<NGIPlatform>(value);
        } while (false);

        LoadRenderPipelineSetting();



        // Read when you need add configs:
        // think carefully before you add a line
        // Many of these setting should implemented and queried at their own system implememtation
        // And many of these should be deprecated!
        
        // Project config can override engine config.
        // Get global configs value
        CmdSettings::Inst().ParseValueContainer(mContext);

        GetValue("Renderdoc", mRenderdoc);
       
        GetValue("UseMultithreadedRendering", mUseMultithreadedRendering);
        GetValue("UseAsyncLoading", mUseAsyncLoading);
        GetValue("UseGPUSkin", mUseGPUSkin);

        GetValue("UseCulling", mEnableCulling);
        GetValue("EnableFoliagePicking", mEnableFoliagePicking);
        GetValue("FoliageLoDBias", mFoliageLoDBias);
        GetValue("FoliageInstanceLoadFactor", mFoliageInstanceLoadFactor);
        GetValue("TerrainLoDPixelError", mTerrainLoDPixelError);
        GetValue("TerrainStreamingDistance", mTerrainStreamingDistance);
        GetValue("DisableTerrainCulling", mDisableTerrainCulling);
        GetValue("RGPThreadingMultiplexingFactor", mRGPThreadingMultiplexingFactor);

        GetValue("BlackScreenSetting", mBlackScreenSetting);
        GetValue("MonitorIndex", mMonitorSetting.MonitorIndex);
        GetValue("ClientScreenWidth", mClientScreenWidth);
        GetValue("ClientScreenHeight", mClientScreenHeight);
        GetValue("ClientScreenScale", mScreenScale);
        GetValue("DpiAware", mDpiAware);
        GetValue("EnableQTangents", mEnableQTangents);
        GetValue("FrustumCulling.BlockSize", mFrustumCullingBlockSize);
        GetValue("EditorIconScale", mEditorIconScale);
        GetValue("ShowRegenerateFxMsgBoxBeforeOpenProject", mShowRegenerateFxMsgBoxBeforeOpenProject);
        LoadDecalSetting();

        int value = static_cast<int>(mMonitorSetting.DisplayMode);
        GetValue("FullScreen", value);
        mMonitorSetting.DisplayMode = static_cast<FullScreenMode>(value);

        

        result = HasSettingOption("filesystems");
        if (result)
        {
            std::string workDir = cross::PathHelper::GetCurrentDirectoryPath();
            std::string docDir = workDir + "/Documents";

            std::string engineDir = cross::PathHelper::GetEngineResourceDirectoryPath();

            filesystem::FileSystemConfig resFilesystemConfig;
            filesystem::FileLoaderConfigList& resFileLoaderConfigList = resFilesystemConfig.mFileLoaderConfigs;

            cross::SerializeNode& fileSystemNode = *result;

            std::optional<cross::SerializeNode> resNode = fileSystemNode.HasMember("res");
            Assert(resNode && resNode->IsObject());

            for (auto itr = resNode->begin(); itr != resNode->end(); ++itr)
            {
                std::string_view key = itr.Key();

                std::string strFileLoaderName = std::string(key);

                if (strFileLoaderName.find(filesystem::DISCRETE_KEY) != std::string::npos)
                {
                    strFileLoaderName = filesystem::DISCRETE_KEY;
                }

                cross::SerializeNode valueNode = itr.Value();

                auto os = std::move(valueNode.HasMember(filesystem::OPENER_KEY));
                std::string strOpener = os->AsString();

                auto root = std::move(valueNode.HasMember(filesystem::ROOT_KEY));
                std::string strRoot = root->AsString();
                
                filesystem::FileLoaderConfig fileLoaderConfig;
                fileLoaderConfig.mFileLoader = strFileLoaderName;
                fileLoaderConfig.mOpener = strOpener;
                fileLoaderConfig.mRootPath = strRoot;

                mFileSystem->ReplaceString(fileLoaderConfig.mRootPath, "%WORK_DIR%", workDir);

#if CROSSENGINE_EDITOR
                mFileSystem->ReplaceString(fileLoaderConfig.mRootPath, "%DOC_DIR%", workDir);
#elif (CROSSENGINE_IOS || CROSSENGINE_ANDROID)
                mFileSystem->ReplaceString(fileLoaderConfig.mRootPath, "%DOC_DIR%", documentPath);
#else
                mFileSystem->ReplaceString(fileLoaderConfig.mRootPath, "%DOC_DIR%", docDir);
#endif
                mFileSystem->ReplaceString(fileLoaderConfig.mRootPath, "%ENGINE_RES_DIR%", engineDir);

                PathHelper::Normalize(fileLoaderConfig.mRootPath);

                resFileLoaderConfigList.push_back(fileLoaderConfig);
            }

            mFileSystem->Init(resFilesystemConfig);
        }

        result = HasSettingOption("qualification");
        if (result)
        {
            cross::SerializeNode& qualificationNode = *result;

            {
                std::optional<cross::SerializeNode> resourcePathNode = qualificationNode.HasMember("ResourcePath");
                if (resourcePathNode)
                {
                    Assert(resourcePathNode->IsString());
                    mQualificationResourcePath = resourcePathNode->AsString();
                }
            }
        }

        result = HasSettingOption("MPCDI");
        if (result)
        {
            cross::SerializeNode& mpcdiNode = *result;

            {
                std::optional<cross::SerializeNode> enabledNode = mpcdiNode.HasMember("enabled");
                if (enabledNode)
                {
                    Assert(enabledNode->IsBoolean());
                    mMPCDI.enabled = enabledNode->AsBoolean();
                }
            }

            {
                std::optional<cross::SerializeNode> yawNode = mpcdiNode.HasMember("yaw");
                if (yawNode)
                {
                    Assert(yawNode->IsFloatingPoint() || yawNode->IsNumber());
                    mMPCDI.yaw = yawNode->AsDouble();
                }
            }

            {
                std::optional<cross::SerializeNode> pitchNode = mpcdiNode.HasMember("pitch");
                if (pitchNode)
                {
                    Assert(pitchNode->IsFloatingPoint() || pitchNode->IsNumber());
                    mMPCDI.pitch = pitchNode->AsDouble();
                }
            }

            {
                std::optional<cross::SerializeNode> rollNode = mpcdiNode.HasMember("roll");
                if (rollNode)
                {
                    Assert(rollNode->IsFloatingPoint() || rollNode->IsNumber());
                    mMPCDI.roll = rollNode->AsDouble();
                }
            }

            {
                std::optional<cross::SerializeNode> leftNode = mpcdiNode.HasMember("left");
                if (leftNode)
                {
                    Assert(leftNode->IsFloatingPoint() || leftNode->IsNumber());
                    mMPCDI.left = leftNode->AsDouble();
                }
            }

            {
                std::optional<cross::SerializeNode> rightNode = mpcdiNode.HasMember("right");
                if (rightNode)
                {
                    Assert(rightNode->IsFloatingPoint() || rightNode->IsNumber());
                    mMPCDI.right = rightNode->AsDouble();
                }
            }

            {
                std::optional<cross::SerializeNode> topNode = mpcdiNode.HasMember("top");
                if (topNode)
                {
                    Assert(topNode->IsFloatingPoint() || topNode->IsNumber());
                    mMPCDI.top = topNode->AsDouble();
                }
            }

            {
                std::optional<cross::SerializeNode> bottomNode = mpcdiNode.HasMember("bottom");
                if (bottomNode)
                {
                    Assert(bottomNode->IsFloatingPoint() || bottomNode->IsNumber());
                    mMPCDI.bottom = bottomNode->AsDouble();
                }
            }
  
            {
                std::optional<cross::SerializeNode> colorTableSizeNode = mpcdiNode.HasMember("ColorTableSize");
                std::optional<cross::SerializeNode> gridSizeNode = mpcdiNode.HasMember("GridSize");
                if (colorTableSizeNode && gridSizeNode)
                {
                    Assert(colorTableSizeNode->IsArray());
                    Assert(colorTableSizeNode->Size() == 3);
                    SerializeNode srNode = colorTableSizeNode->At(0);
                    Assert(srNode.IsNumber());
                    SerializeNode sgNode = colorTableSizeNode->At(1);
                    Assert( sgNode.IsNumber());
                    SerializeNode sbNode = colorTableSizeNode->At(2);
                    Assert(sbNode.IsNumber());
                    mMPCDI.color_table_size = Int3(srNode.AsInt32(), sgNode.AsInt32(), sbNode.AsInt32());
              
                    Assert(gridSizeNode->IsArray());
                    Assert(gridSizeNode->Size() == 2);
                    SerializeNode sxNode = gridSizeNode->At(0);
                    Assert(sxNode.IsNumber());
                    SerializeNode syNode = gridSizeNode->At(1);
                    Assert(syNode.IsNumber());
                    mMPCDI.grid_size = Int2(sxNode.AsInt32(), syNode.AsInt32());
                }
                mMPCDI.color_table_pixel_size = Float2(1.0f / static_cast<float>((mMPCDI.color_table_size.x + 1) * (mMPCDI.color_table_size.z + 1) * mMPCDI.grid_size.x)
                    , 1.0f / static_cast<float>((mMPCDI.color_table_size.y + 1) * mMPCDI.grid_size.y));
            }
        }
#else
        std::string workDir = cross::PathHelper::GetCurrentDirectoryPath();
        std::string docDir = workDir + "/Documents";
        filesystem::FileLoaderConfig fileLoaderConfig;
        fileLoaderConfig.mFileLoader = "discrete";
        fileLoaderConfig.mOpener = "os";
        fileLoaderConfig.mRootPath = "%ENGINE_RES_DIR%";
        filesystem::FileSystemConfig resFilesystemConfig;
        filesystem::FileLoaderConfigList& resFileLoaderConfigList = resFilesystemConfig.mFileLoaderConfigs;
        mFileSystem->ReplaceString(fileLoaderConfig.mRootPath, "%WORK_DIR%", workDir);
        mFileSystem->ReplaceString(fileLoaderConfig.mRootPath, "%DOC_DIR%", docDir);
        std::string engineDir = cross::PathHelper::GetEngineResourceDirectoryPath();
        mFileSystem->ReplaceString(fileLoaderConfig.mRootPath, "%ENGINE_RES_DIR%", engineDir);
        resFileLoaderConfigList.push_back(fileLoaderConfig);
        mFileSystem->Init(resFilesystemConfig);
#endif
        mSettingSuccessfullyLoaded = true;
        return mSettingSuccessfullyLoaded;
    }

    void SettingsManager::SerializeEngineConfigToFile() {}

    void SettingsManager::SerializeProjectConfigToFile() {
        mProjectConfig.value()["ClientScreenWidth"] = mClientScreenWidth;
        mProjectConfig.value()["ClientScreenHeight"] = mClientScreenHeight;
        mProjectConfig.value()["FullScreen"] = mMonitorSetting.DisplayMode;
        SaveConfigFile(mProjectConfig.value(), PathHelper::GetAssetAbsolutePath(mProjectConfigFileName));
    }

    void SettingsManager::SerializeLayerConfigToFile()
    {
        const auto& layerInfos = mLayerSetting.GetAllLayerInfos();

        SerializeNode layersNode;
        for (UInt32 layerIndex = 0; layerIndex < LayerSetting::LayerCountMax; layerIndex++)
        {
            layersNode[std::to_string(layerIndex)] = layerInfos[layerIndex].LayerName;
        }

        SerializeNode rootNode;
        rootNode["Layers"] = std::move(layersNode);

        SaveConfigFile(rootNode, PathHelper::GetAssetAbsolutePath(mLayerConfigFileName));
    }

    std::optional<SerializeNode> SettingsManager::LoadConfigFile(const std::string& fileName)
    {
        std::string fileContent;

        FILE* fp = fopen(fileName.c_str(), "rb");
        if (fp == nullptr)
        {
            LOG_ERROR("Fail to load config file path: {}", fileName);
            //Assert(0);
            return {};
        }

        int32_t fileLength = 0;
        fseek(fp, 0, SEEK_END);
        fileLength = static_cast<int32_t>(ftell(fp));
        Assert(fileLength > 0);
        fseek(fp, 0, SEEK_SET);

        fileContent.resize(fileLength);
        fread(fileContent.data(), fileLength, 1, fp);

        fclose(fp);

        bool success = false;
        SerializeNode node = SerializeNode::ParseFromJson(fileContent, &success);
        if (!success)
        {
            LOG_ERROR("Parse config file failed. Path: \"{}\".", fileName);
            return {};
        }

        return node;
    }

    void SettingsManager::SaveConfigFile(SerializeNode& node, const std::string& fileName)
    {
        FILE* fp = fopen(fileName.c_str(), "w");
        if (fp == nullptr)
        {
            LOG_ERROR("Fail to save config file path: {}", fileName);
        }

        std::string binaryData = node.FormatToJson();
        fwrite(binaryData.data(), 1, binaryData.size(), fp);

        fclose(fp);
    }

    bool SettingsManager::GetIsAsyncLoading() const
    {
        return mIsAsyncLoading;
    }

    void SettingsManager::SetUseSeparateRenderingThread(bool enable)
    {
        mUseSeparateRenderingThread = enable;
    }
            
    CECOMMON_API void SettingsManager::SetMessageKeepOnInit(bool enable)
    {
        mMessageKeep = enable;
    }

    CECOMMON_API void SettingsManager::SetNumTaskThreads(SInt32 numTaskThreads)
    {
        mNumTaskThreads = numTaskThreads;
    }


    void SettingsManager::SetLaunchScene(std::string const& scenePath)
    {
        mLaunchScene = scenePath;
        SerializeEngineConfigToFile();
    }

    void SettingsManager::NotifyRenderPipelineSettingChanged()
    {
        mRenderPipelineSetting->Initialize();
        RenderPipelineSettingEvent e{ mRenderPipelineSetting };
        DispatchImmediateEvent(e);
    }

    void SettingsManager::LoadRenderPipelineSetting()
    {
        // Project config can override enigne config.
        auto result = HasSettingOption("RenderPipelineSetting");
        if (result)
        {
            if (auto type = result->HasMember("UseRenderPipeline"); type)
            {
                if (type.value().AsString() == "UseBuiltinRP")
                {
                    auto metaclass = gbf::reflection::query_meta_class_by_name("cross::BuiltinRenderPipelineSetting");
                    mRenderPipelineSettingHolder = gbf::reflection::cxx::CreateShared(*metaclass);
                }
                else if (type.value().AsString() == "UseHDRP")
                {
                    auto metaclass = gbf::reflection::query_meta_class_by_name("cross::HDRenderPipelineSetting");
                    mRenderPipelineSettingHolder = gbf::reflection::cxx::CreateShared(*metaclass);
                }
                else if (type.value().AsString() == "UseUnrealRP")
                {
                    auto metaclass = gbf::reflection::query_meta_class_by_name("cross::UnrealRenderPipelineSetting");
                    mRenderPipelineSettingHolder = gbf::reflection::cxx::CreateShared(*metaclass);
                }
                else if (type.value().AsString() == "UseFFSRP")
                {
                    auto metaclass = gbf::reflection::query_meta_class_by_name("cross::FFSRenderPipelineSetting");
                    mRenderPipelineSettingHolder = gbf::reflection::cxx::CreateShared(*metaclass);
                }
                else
                {
                    Assert(false);
                }
            }
            else
            {
                auto metaclass = gbf::reflection::query_meta_class_by_name("cross::FFSRenderPipelineSetting");
                mRenderPipelineSettingHolder = gbf::reflection::cxx::CreateShared(*metaclass);
            }
            mRenderPipelineSetting = &mRenderPipelineSettingHolder.Get<RenderPipelineSetting>();

            SerializeContext context;
            mRenderPipelineSetting->Deserialize(*result, context);
        }
        else
        {
            auto metaclass = gbf::reflection::query_meta_class_by_name("cross::FFSRenderPipelineSetting");
            mRenderPipelineSettingHolder = gbf::reflection::cxx::CreateShared(*metaclass);
         
            mRenderPipelineSetting = &mRenderPipelineSettingHolder.Get<RenderPipelineSetting>();
        }
    }

    void SettingsManager::ReloadRenderPipelineSetting()
    {
        mEngineConfig = LoadConfigFile(PathHelper::GetEngineResourceAbsolutePath(mEngineConfigFileName));
        mProjectConfig = LoadConfigFile(PathHelper::GetAssetAbsolutePath(mProjectConfigFileName));
        ExtractValue("", mEngineConfig.value());
        ExtractValue("", mProjectConfig.value());

        LoadAndApplyScalabilityConfig();

        LoadRenderPipelineSetting();
        NotifyRenderPipelineSettingChanged();
    }

    void SettingsManager::SetMaxTickRates(SInt32 maxTickRates)
    {
        mMaxTickRates = maxTickRates;
    }

    void SettingsManager::InitDisplayModeList() 
    {
#if CROSSENGINE_WIN
        IDXGIFactory* factory;
        if (FAILED(CreateDXGIFactory(__uuidof(IDXGIFactory), reinterpret_cast<void**>(&factory))))
        {
            LOG_ERROR("Fail to create dxgi factory");
            return;
        }

        std::vector<IDXGIAdapter*> adapters;

        IDXGIAdapter* adapter =nullptr;
        factory->EnumAdapters(0, &adapter);
        for (UINT i = 0; factory->EnumAdapters(i, &adapter) != DXGI_ERROR_NOT_FOUND; ++i)
        {
            adapters.emplace_back(std::move(adapter));
        }

        if (adapters.empty())
        {
            LOG_ERROR("Fail to find GPU");
            return;
        }

        std::sort(adapters.begin(), adapters.end(), [](auto& adp1, auto& adp2) {
            DXGI_ADAPTER_DESC desc1;
            adp1->GetDesc(&desc1);
            DXGI_ADAPTER_DESC desc2;
            adp2->GetDesc(&desc2);
            return desc1.DedicatedVideoMemory > desc2.DedicatedVideoMemory;
        });

        adapter = adapters.front();
        IDXGIOutput* output;

        if (FAILED(adapter->EnumOutputs(0, &output)))
        {
            DXGI_ADAPTER_DESC desc;
            adapter->GetDesc(&desc);
            std::string descStr;
            if (auto len = WideCharToMultiByte(CP_ACP, 0, desc.Description, -1, nullptr, 0, nullptr, nullptr); len != 0)
            {
                descStr.resize(len);
                WideCharToMultiByte(CP_ACP, 0, desc.Description, -1, descStr.data(), len, nullptr, nullptr);
            }

            LOG_ERROR("Fail to find monitor on GPU: {}", descStr);
            return;
        }
        UINT numModes = 0;
        if (FAILED(output->GetDisplayModeList(DXGI_FORMAT_R8G8B8A8_UNORM, DXGI_ENUM_MODES_INTERLACED, &numModes, NULL)))
        {
            LOG_ERROR("Failed to get display mode count.");
            return;
        }
        DXGI_MODE_DESC* displayModes = new DXGI_MODE_DESC[numModes];
        if (FAILED(output->GetDisplayModeList(DXGI_FORMAT_R8G8B8A8_UNORM, DXGI_ENUM_MODES_INTERLACED, &numModes, displayModes)))
        {
            LOG_ERROR("Failed to get display mode list.");
            delete[] displayModes;
            return;
        }

        auto& resolutionList = mMonitorSetting.ResolutionList;
        resolutionList.clear();
        for (int i = numModes-1; i>=0; --i)
        {
            // display mode contains resolution, refresh rate, format etc, so there exists repetitive resolutions.
            // so far, we only reserve the resolutions which width > height and width larger than 1200.
            if (displayModes[i].Width > 1200u && displayModes[i].Width > displayModes[i].Height)
            {
                if (resolutionList.empty() || (displayModes[i].Width != resolutionList.back().first || displayModes[i].Height != resolutionList.back().second))
                {
                    resolutionList.emplace_back(std::pair<UInt32, UInt32>{displayModes[i].Width, displayModes[i].Height});
                    // check weather there is a resolution same as resolution in config.
                    if (mClientScreenWidth == displayModes[i].Width && mClientScreenHeight == displayModes[i].Height)
                    {
                        mMonitorSetting.CurrentResolutionIndex = (int)resolutionList.size() - 1;
                    }
                }
            }               
        }
        // if monitor do not support display mode in project config, use window mode
        if (mMonitorSetting.CurrentResolutionIndex == -1)
        {
            resolutionList.emplace_back(std::pair<UInt32, UInt32>{mClientScreenWidth, mClientScreenHeight});
            mMonitorSetting.CurrentResolutionIndex = (int)resolutionList.size() - 1;
            mMonitorSetting.DisplayMode = FullScreenMode::Windowed;
        }
        // if monitor do not support display mode in project config, use origin monitor display resolution and full screen.
        //if (mMonitorSetting.CurrentResolutionIndex == -1)
        //{
        //    DXGI_OUTPUT_DESC outputDesc;
        //    output->GetDesc(&outputDesc);
        //    HMONITOR hMonitor = outputDesc.Monitor;
        //    MONITORINFOEX monitorInfo;
        //    monitorInfo.cbSize = sizeof(MONITORINFOEX);
        //    GetMonitorInfo(hMonitor, &monitorInfo);
        //    DEVMODE devMode;
        //    devMode.dmSize = sizeof(DEVMODE);
        //    devMode.dmDriverExtra = 0;
        //    EnumDisplaySettings(monitorInfo.szDevice, ENUM_CURRENT_SETTINGS, &devMode);
        //    for (int i = 0; i < resolutionList.size(); ++i)
        //    {
        //        if (resolutionList[i].first == devMode.dmPelsWidth && resolutionList[i].second == devMode.dmPelsHeight)
        //        {
        //            mClientScreenWidth = devMode.dmPelsWidth;
        //            mClientScreenHeight = devMode.dmPelsHeight;
        //            mMonitorSetting.CurrentResolutionIndex = i; 
        //            break;
        //        }
        //    }

        //    // if origin display mode is not normalized one, use the monitor's native display resolution anyway.
        //    if (mMonitorSetting.CurrentResolutionIndex == -1)
        //    {
        //        mClientScreenWidth = resolutionList[0].first;
        //        mClientScreenHeight = resolutionList[0].second;
        //        mMonitorSetting.CurrentResolutionIndex = 0;
        //    }
        //    mMonitorSetting.DisplayMode = FullScreenMode::Exclusive;
        //}

        
            
        factory->Release();
        for (auto* ptr : adapters)
        {
            ptr->Release();
        }
        output->Release();
        delete[] displayModes;
#endif
    }
    std::string SettingsManager::GetDisplaySettings() const
    {
        SerializeNode res;
        // resolution settings
        res["resolution"];
        auto& resolutionList = mMonitorSetting.ResolutionList;

        for (int i = 0; i < resolutionList.size(); ++i)
        {
            SerializeNode node;
            node["width"] = resolutionList[i].first;
            node["height"] = resolutionList[i].second;
            res["resolution"]["list"].PushBack(std::move(node));
        }
        res["resolution"]["current"] = mMonitorSetting.CurrentResolutionIndex;
        // displaymode settings
        res["displaymode"] = mMonitorSetting.DisplayMode;
        return res.FormatToJson();
    }

    void SettingsManager::SetDisplaySettings(unsigned resolutionIndex, unsigned mode)
    {
        if ((int)resolutionIndex == mMonitorSetting.CurrentResolutionIndex && (FullScreenMode)mode == mMonitorSetting.DisplayMode)
        {
            return;
        }
        if (auto cvar = EngineGlobal::Inst().GetTerminalManager()->GetTerminalVar("Engine.monitor"))
        {
            mClientScreenWidth = mMonitorSetting.ResolutionList[resolutionIndex].first;
            mClientScreenHeight = mMonitorSetting.ResolutionList[resolutionIndex].second;

            mMonitorSetting.CurrentResolutionIndex = resolutionIndex;
            mMonitorSetting.DisplayMode = (FullScreenMode)mode;
            cvar->OnChange(std::to_string(mMonitorSetting.ResolutionList[resolutionIndex].first) + "x" + std::to_string(mMonitorSetting.ResolutionList[resolutionIndex].second) + "(" + std::to_string(mode) + ")");
            SerializeProjectConfigToFile();
        }
    }

    LayerSetting::LayerSetting(SettingsManager* settingManager)
        : mSettingManager(settingManager)
        , mEmptyLayerInfo()
    {
        for (UInt32 layerIndex = 0; layerIndex < LayerCountMax; layerIndex++)
        {
            mLayers[layerIndex].LayerIndex = layerIndex;
        }

        for (UInt32 layerIndex = 0; layerIndex < 8; layerIndex++)
        {
            mLayers[layerIndex].IsBuiltInLayer = true;
        }

        mLayers[0].LayerName = "Default";
        mLayers[4].LayerName = "Preview";
    }

    bool LayerSetting::SetLayer(UInt32 index, const std::string& name) 
    {
        if (index < LayerCountMax)
        {
            LayerInfo& layerInfo = mLayers[index];

            if (!layerInfo.IsBuiltInLayer)
            {
                mLayers[index].LayerName = name;

                return true;
            }
        }

        return false;
    }

    const LayerInfo& LayerSetting::GetLayerInfo(UInt32 index) const
    {
        if (index < LayerCountMax)
        {
            return mLayers[index];
        }

        return mEmptyLayerInfo;
    }

    bool LayerSetting::IsLayerAllocated(UInt32 index) const
    {
        return index < LayerCountMax && !mLayers[index].LayerName.empty();
    }

    SInt32 LayerSetting::GetLayerIndex(const std::string& name) const
    {
        if (!name.empty())
        {
            for (UInt32 layerIndex = 0; layerIndex < LayerCountMax; layerIndex++)
            {
                if (mLayers[layerIndex].LayerName == name)
                {
                    return layerIndex;
                }
            }
        }

        return -1;
    }

    const std::array<LayerInfo, LayerSetting::LayerCountMax>& LayerSetting::GetAllLayerInfos() const
    {
        return mLayers;
    }

    std::vector<LayerInfo> LayerSetting::GetAllocatedLayerInfos() const
    {
        std::vector<LayerInfo> result;

        for (UInt32 layerIndex = 0; layerIndex < LayerCountMax; layerIndex++)
        {
            if (!mLayers[layerIndex].LayerName.empty())
            {
                result.push_back(mLayers[layerIndex]);
            }
        }

        return result;
    }

    void LayerSetting::SerializeConfigToFile()
    {
        mSettingManager->SerializeLayerConfigToFile();
    }
}   // namespace cross
