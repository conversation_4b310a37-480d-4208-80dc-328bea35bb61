#include "PlatformPrefix.h"
#include "FrameCounter.h"
#include <atomic>

namespace cross::frame
{
    static std::atomic_uint64_t gGameFrameNumber;
    static std::atomic_uint64_t gRenderingFrameNumber;
    static std::atomic_uint64_t gGPUFrameNumber;

    uint64_t GetGameFrameNumber()
    {
        return gGameFrameNumber.load(std::memory_order_relaxed);
    }

    uint64_t GetRenderingFrameNumber()
    {
        return gRenderingFrameNumber.load(std::memory_order_relaxed);
    }

    uint64_t GetGPUFrameNumber()
    {
        return gGPUFrameNumber.load(std::memory_order_relaxed);
    }

    void AdvanceGameFrame()
    {
        gGameFrameNumber.fetch_add(1, std::memory_order_relaxed);
    }

    void AdvanceRenderingFrame()
    {
        gRenderingFrameNumber.fetch_add(1, std::memory_order_relaxed);
    }

    void AdvanceGPUFrame()
    {
        gGPUFrameNumber.fetch_add(1, std::memory_order_relaxed);
    }
}
