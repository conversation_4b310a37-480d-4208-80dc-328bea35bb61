#include "EnginePrefix.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/GlobalSystemDesc.h"
#include "CECommon/Common/SystemEvent.h"
#include "InGameTerminalManager.h"
#include "CrossBase/Log.h"
#include <regex>

namespace cross {


void InGameTerminalManager::RegisterTerminalCommand(const char* commandname, WorldInternalSystem* receiver)
{
    if (mCommands.find(StringID(commandname)) != mCommands.end())
    {
        mCommands.find(StringID(commandname))->second->SubscribeEvent<TerminalCommandEvent>(receiver, 0);
    }
    else
    {
        auto newCommand = std::make_unique<TerminalCommand>(commandname, true);
        newCommand->SubscribeEvent<TerminalCommandEvent>(receiver, 0);
        mCommands.emplace(StringID(commandname), std::move(newCommand));
        mCommandsAndVars.push_back(commandname);
    }
}

void InGameTerminalManager::UnregisterTerminalCommand(const char* commandname, WorldInternalSystem* receiver)
{
    auto Cmd = mCommands.find(StringID(commandname));
    if (Cmd != mCommands.end())
    {
        Cmd->second->Unsubscribe<TerminalCommandEvent>(receiver);
    }
}

void InGameTerminalManager::UnregisterReceiver(WorldInternalSystem* receiver)
{
    for (auto& Cmd : mCommands)
    {
        Cmd.second->Unsubscribe<TerminalCommandEvent>(receiver);
    }
}


void InGameTerminalManager::TriggerCommand(const char* commandname)
{
    // if (mCommands.find(Commandname) == mCommands.end())
    //{
    //     bool regexRet = false;
    //     std::smatch m;
    //     regexRet = std::regex_search(commandName.cbegin(), commandName.cend(), m, std::regex("(light)(.*?r.*?)(\\w+)(.*?t.*?)(\\d+)(.*?i.*?)(\\w+)(.*?n.*?)(\\d+)"));
    //     if (regexRet)
    //     {
    //         // LOG_ERROR("{} match rw = {}, lightType = {}, intensity = {}, index ={}", commandName.c_str(), m[3].str().c_str(), m[5].str().c_str(), m[7].str().c_str(), m[9].str().c_str());
    //         UniqueString tempCommendname("light");
    //         mCommands.find(tempCommendname)->second->OnTrigger(m[3].str(), static_cast<UInt16>(std::stoi(m[5].str())), static_cast<SInt16>(std::stoi(m[7].str())), static_cast<UInt16>(std::stoi(m[9].str())));
    //         return;
    //     }
    //     else
    //     {
    //         LOG_ERROR("{} not exist", Commandname.GetCString());
    //         return;
    //     }
    // }

    auto lowerCmdStr = StringHelper::ToLowerChar(commandname);
    auto lowerCmdName = lowerCmdStr.c_str();

    if (mCommands.find(StringID(lowerCmdName)) != mCommands.end())
    {
        mCommands.find(StringID(lowerCmdName))->second->OnTrigger();
    }
}
TerminalVarCommand* InGameTerminalManager::GetTerminalVar(const char* cvarname)
{
    auto CVar = mCVars.find(StringID(cvarname));
    if (CVar != mCVars.end())
    {
        return CVar->second.get();
    }
    return nullptr;
}
bool InGameTerminalManager::TriggerTerminalVar(const char* cvarname)
{
    std::string Command(cvarname);
    std::smatch m;
    auto regexRet = std::regex_match(Command.cbegin(), Command.cend(), m, std::regex(R"x([a-zA-Z_/][a-zA-Z_0-9./]*=[a-zA-Z_0-9./]*)x"));
    if (regexRet)
    {
        std::smatch varnamem;
        std::regex_match(Command.cbegin(), Command.cend(), varnamem, std::regex("(.*)=(.*)"));
        std::string varname = varnamem[1].str();
        std::string value = varnamem[2].str();
        auto cvar = GetTerminalVar(varname.c_str());
        if (cvar)
        {
            if (cvar->GetType() == 0)
            {
                bool bvalue = std::atoi(value.c_str());
                cvar->OnChange(bvalue);
            }
            else if (cvar->GetType() == 1)
            {
                UInt32 i = std::atoi(value.c_str());
                cvar->OnChange(i);
            }
            else if (cvar->GetType() == 2)
            {
                float f = static_cast<float>(std::atof(value.c_str()));
                cvar->OnChange(f);
            }
            else if (cvar->GetType() == 3)
            {
                cvar->OnChange(value);
            }
            else
            {
                float f = static_cast<float>(std::atof(value.c_str()));
                cvar->OnChange(f);
            }

            return true;
        }
    }
    return false;
}
InGameTerminalManager::~InGameTerminalManager()
{
    Unsubscribe<cross::TerminalVarEvent>(&mTerminalVarProcessor);
}

void InGameTerminalManager::InitManager() 
{
    SubscribeEvent<cross::TerminalVarEvent>(&mTerminalVarProcessor, 0);
}

void TerminalVarProcessor::NotifyEvent(const SystemEventBase& event, UInt32& flag)
{
    if (event.mEventType == TerminalVarEvent::sEventType)
    {
        const TerminalVarEvent* e = TYPE_CAST(const TerminalVarEvent*, &event);
        if (e->mData.mValueRef.index() == e->mData.mSetValue.index())
        {
            switch (e->mData.mValueRef.index())
            {
            case 0:
            {
                bool* vref = std::get<0>(e->mData.mValueRef);
                *vref = std::get<0>(e->mData.mSetValue);
            }
            break;
            case 1:
            {
                UInt32* vref = std::get<1>(e->mData.mValueRef);
                *vref = std::get<1>(e->mData.mSetValue);
            }
            break;
            case 2:
            {
                float* fref = std::get<2>(e->mData.mValueRef);
                *fref = std::get<2>(e->mData.mSetValue);
            }
            break;
            case 3:
            {
                std::string* sref = std::get<3>(e->mData.mValueRef);
                *sref = std::get<3>(e->mData.mSetValue);
            }
            break;
            default:
                break;
            }
            e->mData.mCallBack(e->mData.mSetValue);
        }
        else
        {
            e->mData.mCallBack(e->mData.mSetValue);
        }
    }
}

}   // namespace cross