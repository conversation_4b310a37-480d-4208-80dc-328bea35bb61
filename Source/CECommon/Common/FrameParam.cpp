
#include "PlatformPrefix.h"
#include "FrameParam.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Allocator/FrameAllocator.h"
#include "CECommon/Allocator/FrameMemoryPoolManager.h"
#include "CrossBase/TimeDef.h"
#include "Threading/RenderingThread.h"
#include "Threading/TaskSystem.h"
#include <cassert>

namespace cross {
constexpr UInt32 TrimInterval = 300;
FrameParam::FrameParam()
{
    UInt8 lifetime = 1;
    UInt32 instanceCount = sInstanceCount()++;
    mUniqueID = instanceCount;
    mDefaultFrameAllocator.reset(new FrameAllocator(lifetime, instanceCount));
}

FrameParam::~FrameParam() {}

std::atomic<UInt32>& FrameParam::sInstanceCount()
{
    static std::atomic<UInt32> sInstanceCountVal{0};
    return sInstanceCountVal;
}

void FrameParam::SetFrameID(UInt32 frameCount)
{
    assert(mFrameCount == 0);
    mFrameCount = frameCount;
}

void FrameParam::BeginFrameStage(FrameStage stage)
{
    assert(stage > mRunningStage);
    if (stage == FRAME_STAGE_GAME)
    {
        mGameFrameStartTime = ClockType::now();
        mDefaultFrameAllocator->BeginGameStage();
        std::for_each(mMultiFrameAllocators.begin(), mMultiFrameAllocators.end(), [](auto& alloc) { alloc.second->BeginGameStage(); });
        assert(mRunningStage == FRAME_STAGE_INVALID);
        mRunningStage = FRAME_STAGE_GAME;
    }
    else if (stage == FRAME_STAGE_RENDER)
    {
        mRenderFrameStartTime = ClockType::now();
        mDefaultFrameAllocator->BeginRenderStage();
        std::for_each(mMultiFrameAllocators.begin(), mMultiFrameAllocators.end(), [](auto& alloc) { alloc.second->BeginRenderStage(); });
        assert(mRunningStage == FRAME_STAGE_GAME);
        mRunningStage = FRAME_STAGE_RENDER;
    }
    else
    {
        // never reach here
        assert(false);
    }
}

void FrameParam::EndFrameStage(FrameStage stage, bool doTrim)
{
    assert(stage <= mRunningStage);
    if (stage == FRAME_STAGE_GAME)
    {
        mDefaultFrameAllocator->EndGameStage();
        std::for_each(mMultiFrameAllocators.begin(), mMultiFrameAllocators.end(), [](auto& alloc) { alloc.second->EndGameStage(); });
        assert(mFinishedStage == FRAME_STAGE_INVALID);
        mFinishedStage = FRAME_STAGE_RENDER;
        mGameFrameEndTime = ClockType::now();
    }
    else if (stage == FRAME_STAGE_RENDER)
    {
        mDefaultFrameAllocator->EndRenderStage(doTrim);
        std::for_each(mMultiFrameAllocators.begin(), mMultiFrameAllocators.end(), [doTrim = doTrim](auto& alloc) { alloc.second->EndRenderStage(doTrim); });
        assert(mFinishedStage == FRAME_STAGE_RENDER);
        mFinishedStage = FRAME_STAGE_INVALID;
        mRenderFrameEndTime = ClockType::now();
    }
    else
    {
        // never reach here
        assert(false);
    }
}
void FrameParam::Reset()
    {
    mFrameCount = 0;
    mRunningStage = FRAME_STAGE_INVALID;
    mFinishedStage = FRAME_STAGE_INVALID;
    mGameFrameStartTime = TimePoint();
    mGameFrameEndTime = TimePoint();
    mRenderFrameStartTime = TimePoint();
    mRenderFrameEndTime = TimePoint();
}

FrameAllocator* FrameParam::GetFrameAllocator() const
{
    return mDefaultFrameAllocator.get();
}
CECOMMON_API FrameAllocator* FrameParam::GetFrameAllocatorWithLifeTime(UInt8 lifetime)
{
    if (mMultiFrameAllocators.count(lifetime) == 0)
    {
        mMultiFrameAllocators.try_emplace(lifetime, std::unique_ptr<FrameAllocator>(new FrameAllocator{lifetime, mUniqueID}));
    }
    return mMultiFrameAllocators[lifetime].get();
}
float FrameParam::GetRenderDeltaTime() const
{
    return mRenderDeltaTime;
}
float FrameParam::GetGameDeltaTime() const
{
    return mGameDeltaTime;
}
void FrameParam::SetRenderEndTime()
{
    mRenderEndTime = ClockType::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::microseconds>(mRenderEndTime - mRenderStartTime);
    mRenderDeltaTime = static_cast<float>(elapsed.count()) / 1000.0f;
}

void FrameParam::SetGameStartTime() 
{
    mGameStartTime = ClockType::now();
}
void FrameParam::SetGameEndTime() 
{
    mGameEndTime = ClockType::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::microseconds>(mGameEndTime - mGameStartTime);
    mGameDeltaTime = static_cast<float>(elapsed.count()) / 1000.0f;
}
    //////////////////////////////////////////////////////////////////////////
// FrameParamManager
FrameParamManager::FrameParamManager(UInt32 frameFreq) {}

FrameParamManager::~FrameParamManager() {}

void FrameParamManager::BeginGameFrame(float time, float deltaTime)
{
    mFrameCount++;
    mGameParamIndex++;
    mGameParamIndex %= MAX_FRAME_PARAM_COUNT;
    FrameMemoryPoolManager::Instance().RealFreeCountFrame = 0;
    FrameMemoryPoolManager::Instance().RealMallocCountFrame = 0;
    auto& gameFrameParam = mAllFrameParams[mGameParamIndex];
    gameFrameParam.SetFrameID(mFrameCount);
    gameFrameParam.BeginFrameStage(FRAME_STAGE_GAME);
    gameFrameParam.mTime = time;
    gameFrameParam.mDeltaTime = deltaTime;
}

void FrameParamManager::EndGameFrame()
{
    mAllFrameParams[mGameParamIndex].EndFrameStage(FRAME_STAGE_GAME, mFrameCount);
}

void FrameParamManager::BeginRenderFrame()
{
    DispatchRenderingCommandWithToken([this] {
        mFrameCountRender++;
        mRenderParamIndex++;
        mRenderParamIndex %= MAX_FRAME_PARAM_COUNT;
        mAllFrameParams[mRenderParamIndex].BeginFrameStage(FRAME_STAGE_RENDER);
    });
}

void FrameParamManager::EndRenderFrame()
{
    DispatchRenderingCommandWithToken([this] {
        if ((mFrameCountRender - mRenderParamIndex) % TrimInterval == 0 && mFrameCountRender >= mRenderParamIndex)
        {
            mAllFrameParams[mRenderParamIndex].EndFrameStage(FRAME_STAGE_RENDER, true);
        }
        else
        {
            mAllFrameParams[mRenderParamIndex].EndFrameStage(FRAME_STAGE_RENDER);
        }
        mAllFrameParams[mRenderParamIndex].Reset(); 
    });
}


FrameParam* FrameParamManager::GetCurrentGameFrameParam()
{
    return &mAllFrameParams[mGameParamIndex];
}

 FrameParam* FrameParamManager::GetCurrentRenderFrameParam()
{
    return &mAllFrameParams[mRenderParamIndex];
}
}   // namespace cross