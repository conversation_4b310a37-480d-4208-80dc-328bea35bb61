#pragma once

#include <mutex>
#include <unordered_map>
#include "CrossBase/String/HashString.h"
#include "CECommon/Common/GameSystemBase.h"
#include "CECommon/Common/RenderSystemBase.h"
#include "CECommon/GameWorld/IGameWorld.h"

namespace cross {
namespace ecs {
    struct Prototype;
}

class HashString;

struct SystemDesc
{
    const SInt32 mID;
    const HashString mName;

    ~SystemDesc() {}

    inline UInt32 GetDeserializePriority() const
    {
        return mDeserializePriority;
    }

    inline bool IsGameSystem() const
    {
        return mIsGameSystem;
    }

    inline bool IsRenderSystem() const
    {
        return !mIsGameSystem;
    }

protected:
    UInt32 mDeserializePriority{0};
    bool mIsGameSystem{true};

    SystemDesc(SInt32 systemID, const char* name, bool isGameSystem)
        : mID(systemID)
        , mName(name)
        , mIsGameSystem(isGameSystem)
    {}

    void SetDeserializePriority(UInt32 priority)
    {
        mDeserializePriority = priority;
    }

    friend class ComponentSystemDescSystem;
};
using TypeIndex = std::type_index;
class ComponentSystemDescSystem
{
public:
    inline UInt32 GetGameSystemDescCount() const
    {
        return mGameSystemDescCount;
    }

    inline UInt32 GetRenderSystemDescCount() const
    {
        return mRenderSystemDescCount;
    }


    CECOMMON_API SystemDesc* GetGameSystemDescByName(const char* Name);

    CECOMMON_API SystemDesc* GetRenderSystemDescByName(const char* Name);

    template<typename System>
    inline SystemDesc& CreateOrGetSystemDesc();

    void SetSystemDeserializeDependency(SystemDesc* desc, const char* systemTypeName);

protected:
    UInt32 mGameSystemDescCount{0};
    UInt32 mRenderSystemDescCount{0};

    CEHashMap<StringHash32, SystemDesc*> mGameSystemDescMap;
    CEHashMap<StringHash32, SystemDesc*> mRenderSystemDescMap;
    CEHashMap<TypeIndex, SystemDesc*> mSystemTypeMap;
    std::mutex mSystemDescMtx;
};

template<typename T>
TypeIndex GetTypeIndex()
{
    return std::type_index(typeid(T));
}
/// Attention of static variable initializer!!! makesure regist it and use it
/// in the same dll module
template<typename System>
inline SystemDesc& ComponentSystemDescSystem::CreateOrGetSystemDesc()
{
    if (mSystemTypeMap.find(GetTypeIndex<System>()) != mSystemTypeMap.end()) 
    {
        return *mSystemTypeMap.find(GetTypeIndex<System>())->second;
    }
    else
    {
        std::lock_guard<std::mutex> lock(mSystemDescMtx);
        if constexpr (std::is_base_of_v<cross::GameSystemBase, System>) 
        {
            std::string systemName(NameDetailPretty<System>());
            auto sGSystemDesc = new SystemDesc(mGameSystemDescCount++, systemName.c_str(), true);
            mGameSystemDescMap.emplace(sGSystemDesc->mName.GetHash32(), sGSystemDesc);
            mSystemTypeMap.emplace(GetTypeIndex<System>(), sGSystemDesc);
            return *sGSystemDesc;
        }
        else if constexpr (std::is_base_of_v<cross::RenderSystemBase, System>)
        {
            std::string systemName(NameDetailPretty<System>());
            auto sRSystemDesc = new SystemDesc(mRenderSystemDescCount++, systemName.c_str(), false);
            mRenderSystemDescMap.emplace(sRSystemDesc->mName.GetHash32(), sRSystemDesc);
            mSystemTypeMap.emplace(GetTypeIndex<System>(), sRSystemDesc);
            return *sRSystemDesc;
        }
    }
    static SystemDesc* sNullSystemDesc{nullptr};
    return *sNullSystemDesc;
}
}   // namespace cross