#pragma once

#include "CECommon/Common/CECommonForward.h"

#include <string>

namespace cross {

class EngineVersion
{
public:
    static const std::string GitBranch;
    static const std::string GitCommitSha1;
    static const std::string GitCommitAuthor;
    static const std::string GitCommitEmail;
    static const std::string GitCommitDate;
    // static const std::string GitCommitSubject;
    static const std::string BuildTimestamp;

    CECOMMON_API static const std::string GetGitBranch();
    CECOMMON_API static const std::string GetGitCommitSha1();
    CECOMMON_API static const std::string GetGitCommitAuthor();
    CECOMMON_API static const std::string GetGitCommitEmail();
    CECOMMON_API static const std::string GetGitCommitDate();
    // CECOMMON_API static const std::string GetGitCommitSubject();
    CECOMMON_API static const std::string GetBuildTimestamp();
};

}
