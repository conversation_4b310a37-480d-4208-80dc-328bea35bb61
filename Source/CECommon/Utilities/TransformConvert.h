#pragma once

#include "CECommon/Common/CECommonForward.h"
#include "CrossBase/Math/CrossMath.h"

namespace cross {
class CECOMMON_API TransformConvert
{
public:
    CEFunction(Editor, Script) static Double3 CE2UE_Scale(const Double3& From)
    {
        return Double3{From.z, From.x, From.y};
    }

    CEFunction(Editor, Script) static Double3 CE2UE_Translation(const Double3& From)
    {
        return Double3{From.x, -From.z, From.y};
    }

    CEFunction(Editor, Script) static Quaternion64 CE2UE_Quaternion(const Quaternion64& From);

    CEFunction(Editor, Script) static Double3 CE2UE_Rotator(const Double3& From)
    {
        return Double3{From.z, From.x, From.y};
    }

    CEFunction(Editor, Script) static Double3 UE2CE_Scale(const Double3& From)
    {
        return Double3{From.y, From.z, From.x};
    }

    CEFunction(Editor, Script) static Double3 UE2CE_Translation(const Double3& From)
    {
        return Double3(From.x, From.z, -From.y);
    }

    CEFunction(Editor, Script) static Quaternion64 UE2CE_Quaternion(const Quaternion64& From);

    CEFunction(Editor, Script) static Double3 UE2CE_Rotator(const Double3& From)
    {
        return Double3{From.y, From.z, From.x};
    }

    CEFunction(Editor, Script) static Double3 UE_QuatToRotator(const Quaternion64& UE_Q);

    CEFunction(Editor, Script) static Quaternion64 UE_RotatorToQuat(const Double3& UE_R);

    static double ClampAxis(double Angle)
    {
        Angle = std::fmod(Angle, 360.0);

        if (Angle < 0.0)
        {
            // shift to [0,360) range
            Angle += 360.0;
        }

        return Angle;
    }

    static double NormalizeAxis(double Angle)
    {
        Angle = ClampAxis(Angle);

        if (Angle > 180.0)
        {
            // shift to (-180,180]
            Angle -= 360.0;
        }

        return Angle;
    }

    static void SinCos(double& ScalarSin, double& ScalarCos, double Value);
};
}   // namespace cross