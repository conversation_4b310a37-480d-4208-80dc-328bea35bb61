#pragma once

#include "CECommon/Common/CECommonForward.h"
#include "CrossBase/Template/TypeDemangle.hpp"
#include "CrossBase/Template/Functional.hpp"
#include "CrossBase/String/UniqueString.h"
#include "CrossBase/String/HashString.h"


namespace cross
{
    using SettingKey = HashString;

    template<typename Value>
    class CECOMMON_API ValueContainer
    {
    public:
        bool <PERSON>alid<PERSON>ey(const SettingKey& key)
        {
            return mContext.find(key) != mContext.end();
        }

        template<typename T>
        bool <PERSON><PERSON>ey(const SettingKey& key)
        {
            if (mContext.find(key) == mContext.end())
            {
                return false;
            }
            return std::visit([](auto const& val) {
                if constexpr (std::is_convertible_v<decltype(val), T>)
                {
                    return true;
                }
                else
                {
                    return false;
                }
                }, mContext[key]);
        }

        // note: need use <PERSON><PERSON><PERSON><T> to check the key and value type match
        template<typename T>
        T GetKeyValue(const SettingKey& key)
        {
            return std::get<T>(mContext[key]);
        }

        void SetKeyValue(const SettingKey& key, const Value & value)
        {
            mContext[key] = value;
        }

         Value& operator [] (const SettingKey& key)
        {
            return mContext[key];
        }

        template<typename T>
        bool GetValue(const SettingKey& key, T& OutValue) const
        {
            auto it = mContext.find(key);
            if (it == mContext.end())
            {
                return false;
            }

            return std::visit([&OutValue](auto const& val) {
                if constexpr (std::is_convertible_v<decltype(val), T>)
                {
                    OutValue = T(val);
                    return true;
                }
                else
                {
                    return false;
                }
                }, it->second);
        }

        std::unordered_map<HashString, Value> mContext;
    };
}

