#pragma once

#if DEBUGMODE

char const* GetName() { return ""; };

#define ABORT_INVALID_FLOAT(value,varname,classname) \
if (!IsFinite (value)) \
{ \
	ErrorStringObject(Format("{}.{} assign attempt for '{}' is not valid. Input {} is { {} }.", #classname, #varname, GetName(), #varname, FloatToString(value).c_str()), this); \
	return; \
}

#define ABORT_INVALID_ARG_FLOAT(value,varname,methodname,classname) \
if (!IsFinite (value)) \
{ \
	ErrorStringObject(Format("{}.{}({}) assign attempt for '{}' is not valid. Input {} is { {} }.", #classname, #methodname, #varname, GetName(), #varname, FloatToString(value).c_str()), this); \
	return; \
}

#define RETURN_INVALID_ARG_FLOAT(value,varname,methodname,classname,returnvalue) \
if (!IsFinite (value)) \
{ \
	ErrorStringObject(Format("{}.{}({}) assign attempt for '{}' is not valid. Input {} is { {} }.", #classname, #methodname, #varname, GetName(), #varname, FloatToString(value).c_str()), this); \
	return returnvalue; \
}


#define ABORT_INVALID_VECTOR2(value,varname,classname) \
if (!IsFinite (value)) \
{ \
	ErrorStringObject(Format("{}.{} assign attempt for '{}' is not valid. Input {} is { {}, {} }.", #classname, #varname, GetName(), #varname, FloatToString(value.x).c_str(), FloatToString(value.y).c_str()), this); \
	return; \
}

#define ABORT_INVALID_ARG_VECTOR2(value,varname,methodname,classname) \
if (!IsFinite (value)) \
{ \
	ErrorStringObject(Format("{}.{}({}) assign attempt for '{}' is not valid. Input {} is { {}, {} }.", #classname, #methodname, #varname, GetName(), #varname, FloatToString(value.x).c_str(), FloatToString(value.y).c_str()), this); \
	return; \
}

#define RETURN_INVALID_ARG_VECTOR2(value,varname,methodname,classname,returnvalue) \
if (!IsFinite (value)) \
{ \
	ErrorStringObject(Format("{}.{}({}) assign attempt for '{}' is not valid. Input {} is { {}, {} }.", #classname, #methodname, #varname, GetName(), #varname, FloatToString(value.x).c_str(), FloatToString(value.y).c_str()), this); \
	return returnvalue; \
}


#define ABORT_INVALID_VECTOR3(value,varname,classname) \
if (!IsFinite (value)) \
{ \
	ErrorStringObject(Format("{}.{} assign attempt for '{}' is not valid. Input {} is { {}, {}, {} }.", #classname, #varname, GetName(), #varname, FloatToString(value.x).c_str(), FloatToString(value.y).c_str(), FloatToString(value.z).c_str()), this); \
	return; \
}

#define ABORT_INVALID_ARG_VECTOR3(value,varname,methodname,classname) \
if (!IsFinite (value)) \
{ \
	ErrorStringObject(Format("{}.{}({}) assign attempt for '{}' is not valid. Input {} is { {}, {}, {} }.", #classname, #methodname, #varname, GetName(), #varname, FloatToString(value.x).c_str(), FloatToString(value.y).c_str(), FloatToString(value.z).c_str()), this); \
	return; \
}

#define RETURN_INVALID_ARG_VECTOR3(value,varname,methodname,classname,returnvalue) \
if (!IsFinite (value)) \
{ \
	ErrorStringObject(Format("{}.{}({}) assign attempt for '{}' is not valid. Input {} is { {}, {}, {} }.", #classname, #methodname, #varname, GetName(), #varname, FloatToString(value.x).c_str(), FloatToString(value.y).c_str(), FloatToString(value.z).c_str()), this); \
	return returnvalue; \
}


#define ABORT_INVALID_QUATERNION(value,varname,classname)	\
if (!IsFinite(value)) \
{ \
	ErrorStringObject(Format("{}.{} assign attempt for '{}' is not valid. Input rotation is { {}, {}, {}, {} }.", #classname, #varname, GetName(), FloatToString(value.x).c_str(), FloatToString(value.y).c_str(), FloatToString(value.z).c_str(), FloatToString(value.w).c_str()), this); \
	return; \
}

#define ABORT_INVALID_ARG_QUATERNION(value,varname,methodname,classname)	\
if (!IsFinite(value)) \
{ \
	ErrorStringObject(Format("{}.{}({}) assign attempt for '{}' is not valid. Input rotation is { {}, {}, {}, {} }.", #classname, #methodname, #varname, GetName(), FloatToString(value.x).c_str(), FloatToString(value.y).c_str(), FloatToString(value.z).c_str(), FloatToString(value.w).c_str()), this); \
	return; \
}

#define RETURN_INVALID_ARG_QUATERNION(value,varname,methodname,classname,returnvalue)	\
if (!IsFinite(value)) \
{ \
	ErrorStringObject(Format("{}.{}({}) assign attempt for '{}' is not valid. Input rotation is { {}, {}, {}, {} }.", #classname, #methodname, #varname, GetName(), FloatToString(value.x).c_str(), FloatToString(value.y).c_str(), FloatToString(value.z).c_str(), FloatToString(value.w).c_str()), this); \
	return returnvalue; \
}


#else

#define ABORT_INVALID_FLOAT(value,varname,classname)
#define ABORT_INVALID_VECTOR2(value,varname,classname)
#define ABORT_INVALID_VECTOR3(value,varname,classname)
#define ABORT_INVALID_QUATERNION(value,varname,classname)

#define ABORT_INVALID_ARG_FLOAT(value,varname,methodname,classname)
#define ABORT_INVALID_ARG_VECTOR2(value,varname,methodname,classname)
#define ABORT_INVALID_ARG_VECTOR3(value,varname,methodname,classname)
#define ABORT_INVALID_ARG_QUATERNION(value,varname,methodname,classname)

#define RETURN_INVALID_ARG_FLOAT(value,varname,methodname,classname,returnvalue)
#define RETURN_INVALID_ARG_VECTOR2(value,varname,methodname,classname,returnvalue)
#define RETURN_INVALID_ARG_VECTOR3(value,varname,methodname,classname,returnvalue)
#define RETURN_INVALID_ARG_QUATERNION(value,varname,methodname,classname,returnvalue)

#endif
