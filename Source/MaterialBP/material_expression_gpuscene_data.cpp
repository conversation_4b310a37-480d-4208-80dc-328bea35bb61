#include "EnginePrefix.h"
#include "material_expression_gpuscene_data.h"
#include "material_compiler.h"

#define DefineMaterialExpressionGPUSceneData(Type)                                                                 \
int32_t cross::MaterialExpressionGPUSceneData##Type::Compile(MaterialCompiler& compiler, ExpressionOutput* output) \
{                                                                                                                  \
    return compiler.GPUSceneData(m_DataLevel, MaterialValueType::MCT_##Type, m_Name);                              \
}

DefineMaterialExpressionGPUSceneData(Float1);
DefineMaterialExpressionGPUSceneData(Float2);
DefineMaterialExpressionGPUSceneData(Float3);
DefineMaterialExpressionGPUSceneData(Float4);
DefineMaterialExpressionGPUSceneData(UInt1);
DefineMaterialExpressionGPUSceneData(UInt2);
DefineMaterialExpressionGPUSceneData(UInt3);
DefineMaterialExpressionGPUSceneData(UInt4);
DefineMaterialExpressionGPUSceneData(SInt1);
DefineMaterialExpressionGPUSceneData(SInt2);
DefineMaterialExpressionGPUSceneData(SInt3);
DefineMaterialExpressionGPUSceneData(SInt4);
DefineMaterialExpressionGPUSceneData(Bool);