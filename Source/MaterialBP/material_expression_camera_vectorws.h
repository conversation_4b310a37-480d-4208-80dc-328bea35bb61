#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) MaterialExpressionCameraVectorWS : public MaterialExpression {
 public:
  virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

  virtual std::string GetCaption() const override { return "CameraVectorWS"; }

public:
  CEMeta(Reflect)
  ExpressionOutput m_Result;
};
}  // namespace cross
