#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) MaterialExpressionComponentMask : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override
    {
        return fmt::format("Mask({}{}{}{})", m_R ? "R" : "", m_G ? "G" : "", m_B ? "B" : "", m_A ? "A" : "");
    }

    virtual std::string GetMenuName() const 
    {
        return "Component Mask";
    }

public:
    CEProperty(Reflect)
    ExpressionInput m_Input;

    CEMeta(Reflect)
    ExpressionOutput m_Result;

    CEProperty(Reflect)
    bool m_R = false;

    CEProperty(Reflect)
    bool m_G = false;

    CEProperty(Reflect)
    bool m_B = false;

    CEProperty(Reflect)
    bool m_A = false;
};
}   // namespace cross