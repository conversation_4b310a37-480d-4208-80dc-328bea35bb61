#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) Material_API MaterialExpressionParameter : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual std::string GetCaption() const override
    {
        return m_Name;
    }

    virtual void OnPropertyChange(IMaterialEditor * editor) override;

    virtual bool IsTypeMaching(const MaterialParameter* param) const = 0;

    virtual std::shared_ptr<MaterialParameter> ToParameter() const = 0;

    virtual void CopyFrom(const MaterialParameter* src)
    {
        m_ParameterName = src->ParameterName;
        m_Name = src->DisplayName;
        m_Group = src->GroupName;
        m_SortPriority = src->SortPriority;
    }

    virtual void CopyTo(MaterialParameter* dst) const
    {
        dst->ParameterName = m_ParameterName;
        dst->DisplayName = m_Name;
        dst->GroupName = m_Group;
        dst->SortPriority = m_SortPriority;
    }

public:
    CEProperty(Reflect, EditorPropertyInfo(bHide = true))
    std::string m_ParameterName;

    CEProperty(Reflect, EditorPropertyInfo(bHide = true))
    bool m_IsVisibleInMaterialInstanceEditor{true};

    CEProperty(Reflect)
    std::string m_Name;

    CEProperty(Reflect)
    std::string m_Group = "Default";

    CEProperty(Reflect)
    float m_SortPriority = 1.0f;

};
}   // namespace cross