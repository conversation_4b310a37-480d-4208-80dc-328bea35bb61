#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) MaterialExpressionExponential2 : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override
    {
        return "Exponential2";
    }

public:
    CEProperty(Reflect)
    ExpressionInput m_Input;

    CEMeta(Reflect)
    ExpressionOutput m_Result;

    CEProperty(Reflect, meta(OverrideInputProperty = m_Input))
    float m_ConstA;
};
}   // namespace cross