#include "material_shared.h"

namespace cross {
MaterialCustomInterpolatorInfo* MaterialCustomInterpolatorSet::FindInterpolator(std::string_view name)
{
    for (int i = 0; i < m_CustomInterpolators.size(); i++)
    {
        auto& interpolator = m_CustomInterpolators[i];
        if (interpolator.Name == name)
        {
            return &interpolator;
        }
    }

    return nullptr;
}

void MaterialCustomInterpolatorSet::AddInterpolator(MaterialCustomInterpolatorType type, std::string_view name, int32_t code)
{
    m_CustomInterpolators.push_back(MaterialCustomInterpolatorInfo{type, std::string(name), "CustomInterpolator" + std::to_string(m_CIIndex++), code});
}

bool MaterialCustomInterpolatorSet::IsValid() const
{
    for (const auto& interpolator : m_CustomInterpolators)
    {
        if (interpolator.CodeIndex == CODE_CHUNK_INDEX_NONE)
        {
            return false;
        }
    }
    return true;
}

int32_t UniformExpressionSet::FindOrAddParameter(MaterialParameterType type, std::string_view name, MaterialParameterValue value)
{
    for (int i = 0; i < m_UniformParameters.size(); i++)
    {
        auto& parameter = m_UniformParameters[i];
        if (parameter.m_Type == type && parameter.m_ParameterName == name)
        {
            return i;
        }
    }

    m_UniformParameters.push_back(MaterialParameterInfo{type, std::string(name), value});
    return static_cast<int32_t>(m_UniformParameters.size() - 1);
}

MaterialFunctionCompileState::~MaterialFunctionCompileState()
{
    for (auto [key, functionState] : m_SharedFunctionStates)
    {
        delete functionState;
    }
}

MaterialFunctionCompileState* MaterialFunctionCompileState::FindOrAddSharedFunctionState(MaterialExpressionKey expressionKey, MaterialExpressionFunctionCall* functionCall)
{
    if (auto iter = m_SharedFunctionStates.find(expressionKey); iter != m_SharedFunctionStates.end())
    {
        return iter->second;
    }
    else
    {
        return m_SharedFunctionStates[expressionKey] = new MaterialFunctionCompileState(functionCall);
    }
}

int32_t MaterialCompilerShaderConstSet::FindOrAddShaderConst(MaterialShaderConstType type, std::string_view name, std::string_view displayName, MaterialShaderConstValue value)
{
    for (int i = 0; i < m_ShaderConsts.size(); i++)
    {
        auto& shaderConst = m_ShaderConsts[i];
        if (shaderConst.Type == type && shaderConst.ShaderConstName == name)
        {
            return i;
        }
    }

    m_ShaderConsts.push_back(MaterialShaderConstInfo{type, std::string(name), std::string(displayName), value});
    return static_cast<int32_t>(m_ShaderConsts.size() - 1);
}

void MaterialCompileMacroSet::AddMacro(const std::string& key, const std::string& value)
{
    m_Macros[key] = value;
}
}   // namespace cross