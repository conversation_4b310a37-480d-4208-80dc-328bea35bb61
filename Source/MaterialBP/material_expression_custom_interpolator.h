#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) MaterialExpressionCustomInterpolator : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * outputPin) override;

    virtual std::string GetCaption() const override
    {
        if (m_Input.m_LinkedExpressionOutput)
        {
            return "CustomInterpolator";
        }
        return "CustomInterpolator: " + m_Name;
    }

    virtual std::string GetMenuName() const override
    {
        return "CustomInterpolator";
    }

public:
    CEProperty(Reflect)
    std::string m_Name{""};

    CEProperty(Reflect, meta(DisplayName = "VS"))
    ExpressionInput m_Input;

    CEMeta(Reflect, meta(DisplayName = "PS"))
    ExpressionOutput m_Result;
};
}   // namespace cross