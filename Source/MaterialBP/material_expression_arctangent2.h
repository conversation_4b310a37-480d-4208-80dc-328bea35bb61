#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) MaterialExpressionArctangent2 : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override
    {
        return "Arctangent2";
    }

public:
    CEProperty(Reflect)
    ExpressionInput m_Y;

    CEProperty(Reflect)
    ExpressionInput m_X;

    CEMeta(Reflect)
    ExpressionOutput m_Result;
};
}   // namespace cross