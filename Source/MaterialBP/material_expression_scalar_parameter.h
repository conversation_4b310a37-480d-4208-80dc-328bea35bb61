#pragma once

#include "material_expression_parameter.h"

namespace cross {
class CEMeta(Cli) Material_API MaterialExpressionScalarParameter : public MaterialExpressionParameter
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetMenuName() const override
    {
        return "ScalarParam";
    }

    bool IsTypeMaching(const MaterialParameter* param) const override { return dynamic_cast<const MaterialParameterScalar*>(param); }

    std::shared_ptr<MaterialParameter> ToParameter() const override
    {
        return gbf::reflection::make_shared_with_rtti<MaterialParameterScalar>();
    }

    void CopyFrom(const MaterialParameter* src) override
    {
        MaterialExpressionParameter::CopyFrom(src);
        auto _src = dynamic_cast<const MaterialParameterScalar*>(src);
        m_DefaultValue = _src->Value;
        m_SliderMin = _src->SliderMin;
        m_SliderMax = _src->SliderMax;
    }

    void CopyTo(MaterialParameter* dst) const override
    {
        MaterialExpressionParameter::CopyTo(dst);
        auto _dst = dynamic_cast<MaterialParameterScalar*>(dst);
        _dst->Value = m_DefaultValue;
        _dst->SliderMin = m_SliderMin;
        _dst->SliderMax = m_SliderMax;
    }

    CEMeta(Reflect)
    ExpressionOutput m_Result;

    CEProperty(Reflect)
    float m_DefaultValue;

    CEMeta(Serialize, Reflect)
    float m_SliderMin;

    CEMeta(Serialize, Reflect)
    float m_SliderMax;
};
}   // namespace cross