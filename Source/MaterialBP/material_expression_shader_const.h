#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) Material_API MaterialExpressionShaderConst : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual std::string GetCaption() const override
    {
        return m_Name;
    }

    virtual void OnPropertyChange(IMaterialEditor * editor) override;

public:
    CEProperty(Reflect, EditorPropertyInfo(bHide = true))
    std::string m_ParameterName;

    CEProperty(Reflect)
    std::string m_Name;

    CEMeta(Reflect)
    ExpressionOutput m_Result;
};
}   // namespace cross