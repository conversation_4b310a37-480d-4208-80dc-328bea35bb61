#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) MaterialExpressionCrossProduct : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override
    {
        return "Cross Product";
    }

public:
    CEProperty(Reflect)
    ExpressionInput m_A;

    CEProperty(Reflect)
    ExpressionInput m_B;

    CEMeta(Reflect)
    ExpressionOutput m_Result;
};
}   // namespace cross