#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) MaterialExpressionSkyAtmosphereViewLuminance : public MaterialExpression
{
public:
    int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    std::string GetCaption() const override
    {
        return fmt::format("SkyAtmosphereViewLuminance");
    }

    CEMeta(Reflect)
    ExpressionOutput m_Result;
};
}   // namespace cross