#pragma once

#include "material_expression.h"
#include "material_shared.h"

namespace cross {

class CEMeta(Cli) MaterialExpressionViewProperty : public MaterialExpression
{

public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override
    {
        return "ViewProperty";
    }

public:
    CEProperty(Reflect, EditorPropertyInfo(PropertyType = "Auto"))
    ExposedViewProperty m_Property;

    CEMeta(Reflect, DisplayName="Property")
    ExpressionOutput m_PropertyOutput;

    CEMeta(Reflect, DisplayName="InvProperty")
    ExpressionOutput m_InvPropertyOutput;
};
}   // namespace cross