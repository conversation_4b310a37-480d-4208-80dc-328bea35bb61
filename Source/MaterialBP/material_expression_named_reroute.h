#pragma once

#include "material_expression.h"

namespace cross {

class CEMeta(Cli) Material_API MaterialExpressionNamedRerouteDeclaration : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    MaterialExpressionNamedRerouteDeclaration()
    {
        ImColor color = MaterialExpression::GetTitleColor();
        m_NodeColor = {color.Value.x, color.Value.y, color.Value.z, 1.0};
    }

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override
    {
        return m_Name;
    }

    virtual ImColor GetTitleColor() const override
    {
        return ImColor(m_NodeColor.x, m_NodeColor.y, m_NodeColor.z, 1.0);
    }

    virtual std::string GetMenuName() const override
    {
        return "Reroute Declaration";
    }

    virtual void OnPropertyChange(IMaterialEditor * editor) override;

public:
    CEProperty(Reflect)
    ExpressionInput m_Input;

    CEMeta(Reflect)
    ExpressionOutput m_Output;

    CEProperty(Reflect)
    std::string m_Name{"Name"};

    CEProperty(Reflect, EditorPropertyInfo(PropertyType = "Float4AsColor"))
    cross::Float4 m_NodeColor;

    CEProperty(Reflect, EditorPropertyInfo(bHide = true))
    std::string m_VariableGuid{""};
};


class CEMeta(Cli) Material_API MaterialExpressionNamedRerouteUsage : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;
    MaterialExpressionNamedRerouteUsage() = default;
    MaterialExpressionNamedRerouteUsage(MaterialExpressionNamedRerouteDeclaration * declaration)
        : m_Declaration(declaration)
    {
        m_DeclarationGuid = declaration->m_VariableGuid;
    }

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override
    {
        return m_Declaration ? m_Declaration->m_Name : GetMenuName();
    }

    virtual ImColor GetTitleColor() const override
    {
        if (m_Declaration)
            return ImColor(m_Declaration->m_NodeColor.x, m_Declaration->m_NodeColor.y, m_Declaration->m_NodeColor.z, 1.0);
        return MaterialExpression::GetTitleColor();
    }

    virtual std::string GetMenuName() const override
    {
        return "Named Reroute Usage";
    }

public:
    CEProperty(Reflect, EditorPropertyInfo(bHide = true))
    std::string m_DeclarationGuid{""};

    CEMeta(Reflect)
    ExpressionOutput m_Result;

    MaterialExpressionNamedRerouteDeclaration* m_Declaration{nullptr};
};

}   // namespace cross