#pragma once

#include "material_expression_parameter.h"

namespace cross {
class CEMeta(Cli) Material_API MaterialExpressionVectorParameter : public MaterialExpressionParameter
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetMenuName() const override
    {
        return "VectorParam";
    }

    bool IsTypeMaching(const MaterialParameter* param) const override { return dynamic_cast<const MaterialParameterVector*>(param); }


    std::shared_ptr<MaterialParameter> ToParameter() const override
    {
        return  gbf::reflection::make_shared_with_rtti<MaterialParameterVector>();
    }

    void CopyFrom(const MaterialParameter* src) override
    {
        MaterialExpressionParameter::CopyFrom(src);
        auto _src = dynamic_cast<const MaterialParameterVector*>(src);
        m_DefaultValue = _src->Value;
    }

    void CopyTo(MaterialParameter* dst) const override
    {
        MaterialExpressionParameter::CopyTo(dst);
        auto _dst = dynamic_cast<MaterialParameterVector*>(dst);
        _dst->Value = m_DefaultValue;
    }

public:
    CEMeta(Reflect)
    ExpressionOutput m_Result;

    CEProperty(Reflect, EditorPropertyInfo(PropertyType = "Float4AsColor"))
    cross::Float4 m_DefaultValue;

    CEMeta(Reflect, meta(RedirectTo = m_Result, ColorMask = R))
    ExpressionOutput m_R;

    CEMeta(Reflect, meta(RedirectTo = m_Result, ColorMask = G))
    ExpressionOutput m_G;

    CEMeta(Reflect, meta(RedirectTo = m_Result, ColorMask = B))
    ExpressionOutput m_B;

    CEMeta(Reflect, meta(RedirectTo = m_Result, ColorMask = A))
    ExpressionOutput m_A;
};
}   // namespace cross