#include "CrossBase/Log.h"
#include "AssetPipeline/Import/AssetImporterManager.h"
#include "AssetPipeline/Interface/AssetPipeline.h"
#include "GamePlayBaseFramework/base/core/core_global.hpp"
#include "material_compiler.h"
#include "material_expression.h"
#include "material_expression_custom.h"
#include "material_expression_function_call.h"
#include "material_expression_surface_shader.h"
#include "material_expression_vertex_shader.h"
#include "material_expression_parameter.h"
#include "material_expression_custom_interpolator.h"
#include "material_expression_named_reroute.h"
#include <assert.h>
#include <fmt/format.h>
#include <fstream>
#include <stdarg.h>
#include "shader_template.h"
#include "Resource/Fx.h"
#include "Runtime/Animation/Animator/Parameter/exprtk_expression.hpp"
#pragma warning(push)
#pragma warning(disable : 4701)
#pragma warning(disable : 4703)
#ifndef LINE_TERMINATOR
#    define LINE_TERMINATOR "\n"
#endif

namespace cross {

static inline MaterialValueType GetVectorType(uint32_t numComponents)
{
    switch (numComponents)
    {
    case 1:
        return MCT_Float;
    case 2:
        return MCT_Float2;
    case 3:
        return MCT_Float3;
    case 4:
        return MCT_Float4;
    default:
        return MCT_Unknown;
    };
}

MaterialCompiler::MaterialCompiler(InputParameters parameters, bool isCompileFunctionPreview)
    : m_InputParams(parameters)
    , m_IsCompileExpressionPreview(isCompileFunctionPreview)
{
    if (m_InputParams.clearMessage)
    {
        m_InputParams.clearMessage();
    }
    ResetCompileState();
}

MaterialCompiler::~MaterialCompiler()
{
    for (int i = 0; i < SF_NumFrequencies; i++)
    {
        for (auto* functionState : m_FunctionStacks[i])
        {
            delete functionState;
        }
    }
}

bool MaterialCompiler::Translate(CompiledShaderCodes& codes)
{
    switch (m_InputParams.defines.Domain)
    {
    case MaterialDomain::Surface:
        m_IsCompilePostProcess = false;
        codes.NeedCompileForward = true;
        codes.NeedCompileGPass = true;
        codes.NeedCompileVSMDepth = true;
        codes.NeedCompileShadow = true;
        codes.NeedCompileMeshDecal = false;
        return Translate_Surface(codes);
    case MaterialDomain::MeshDecal:
        m_IsCompilePostProcess = false;
        codes.NeedCompileForward = false;
        codes.NeedCompileGPass = false;
        codes.NeedCompileVSMDepth = false;
        codes.NeedCompileShadow = false;
        codes.NeedCompileMeshDecal = true;
        return Translate_MeshDecal(codes);
    case MaterialDomain::Foliage:
        m_IsCompilePostProcess = false;
        codes.NeedCompileForward = false;
        codes.NeedCompileGPass = true;
        codes.NeedCompileVSMDepth = true;
        codes.NeedCompileShadow = false;
        codes.NeedCompileMeshDecal = false;
        return Translate_Foliage(codes);
    case MaterialDomain::PostProcess:
        m_IsCompilePostProcess = true;
        codes.NeedCompileForward = true;
        codes.NeedCompileGPass = false;
        codes.NeedCompileVSMDepth = false;
        codes.NeedCompileShadow = false;
        codes.NeedCompileMeshDecal = false;
        return Translate_PostProcess(codes);
    default:
        Assert(false);
    }
    return false;
}
void MaterialCompiler::ClearVtIndex()
{
    if (m_InputParams.VTs)
    {
        m_InputParams.VTs->clear();
    }
    if (m_InputParams.overflowVTs)
    {
        m_InputParams.overflowVTs->clear();
    }
}
bool MaterialCompiler::Translate_Foliage(CompiledShaderCodes& codes)
{
    return TranslateDefaultImpl(codes,
                                
                                MaterialTemplateFileLocations{
                                    "Shader\\Material\\Lit\\SurfaceForward.template",
                                    "Shader\\Material\\Lit\\FoliageGPass.template",
                                    "Shader\\Material\\Lit\\SurfaceShadow.template",
                                    "Shader\\Material\\Lit\\FoliageVSMDepth.template",
                                    "",
                                });
}

bool MaterialCompiler::Translate_Surface(CompiledShaderCodes& codes)
{
    return TranslateDefaultImpl(
        codes,
        
        MaterialTemplateFileLocations {
            "Shader\\Material\\Lit\\SurfaceForward.template",
            "Shader\\Material\\Lit\\SurfaceGPass.template",
            "Shader\\Material\\Lit\\SurfaceShadow.template",
            "Shader\\Material\\Lit\\SurfaceVSMDepth.template",
            "",
        }
    );
}

bool MaterialCompiler::Translate_PostProcess(CompiledShaderCodes& codes)
{
    bool compileSuccess = true;

    ClearVtIndex();

    TranslateParams defaultParams{
        .usages = {{.Usage = MaterialUsage::USED_WITH_DEFAULT}},
    };

    auto& materialDefines = m_InputParams.defines;

    if (materialDefines.EnableDebugSymbol)
    {
        defaultParams.enables.emplace_back(material_literal::debug_symbol);
    }

    if (codes.NeedCompileForward)
    {
        auto params = defaultParams;
        codes.ForwardShaderCode.ShaderCode = TranslatePass_PostProcess(ForwardPassName, "Shader\\Material\\Lit\\PostProcessForward.template", params);
        compileSuccess &= !codes.ForwardShaderCode.ShaderCode.empty();
    }

    return compileSuccess;
}

bool MaterialCompiler::Translate_MeshDecal(CompiledShaderCodes& codes)
{
    return TranslateDefaultImpl(codes,
                                
                                MaterialTemplateFileLocations{
                                    "",
                                    "",
                                    "",
                                    "",
                                    "Shader\\Material\\Lit\\MeshDecal.template",
                                });
}

bool MaterialCompiler::TranslateDefaultImpl(CompiledShaderCodes& codes, MaterialTemplateFileLocations const& templateFileLocation)
{
    bool compileSuccess = true;

    ClearVtIndex();

    TranslateParams defaultParams{
        .usages = {{.Usage = MaterialUsage::USED_WITH_DEFAULT}},
    };

    auto& materialDefines = m_InputParams.defines;

    if (materialDefines.EnableDebugSymbol)
    {
        defaultParams.enables.emplace_back(material_literal::debug_symbol);
    }

    if (materialDefines.UsedWithLocalSpaceParticle || materialDefines.UsedWithGlobalSpaceParticle)
    {
        defaultParams.defines.emplace_back("PARTICLE_PASS");
    }

    auto HandleParticleUsage = [&](TranslateParams& params, bool enableMeshParticle, bool enableSpriteParticle) {
        auto AddSubKeyword = [&](auto& usage) {
            if (enableMeshParticle)
            {
                usage.Keywords.emplace_back("ENABLE_MESH_PARTICLE");
            }
            if (enableSpriteParticle)
            {
                usage.Keywords.emplace_back("ENABLE_SPRITE_PARTICLE");
            }
        };

        if (m_InputParams.defines.UsedWithLocalSpaceParticle)
        {
            AddSubKeyword(params.usages.emplace_back(MaterialUsage::USED_WITH_LOCAL_SPACE_PARTICLE));
        }

        if (m_InputParams.defines.UsedWithGlobalSpaceParticle)
        {
            AddSubKeyword(params.usages.emplace_back(MaterialUsage::USED_WITH_GLOBAL_SPACE_PARTICLE));
        }
    };

    if (m_InputParams.defines.UsedWithTerrain)
    {
        defaultParams.usages.emplace_back(MaterialUsage::USED_WITH_TERRAIN);
    }

    auto surfaceShaderExpression = m_InputParams.surfaceShaderExpression;
    m_CompilationOutput.mBaseColorConnencted     = surfaceShaderExpression && surfaceShaderExpression->m_BaseColor.m_LinkedExpressionOutput     != nullptr;
    m_CompilationOutput.mMetallicConnencted      = surfaceShaderExpression && surfaceShaderExpression->m_Metallic.m_LinkedExpressionOutput      != nullptr;
    m_CompilationOutput.mSpecularConnencted      = surfaceShaderExpression && surfaceShaderExpression->m_Specular.m_LinkedExpressionOutput      != nullptr;
    m_CompilationOutput.mRoughnessConnencted     = surfaceShaderExpression && surfaceShaderExpression->m_Roughness.m_LinkedExpressionOutput     != nullptr;
    m_CompilationOutput.mOpacityConnencted       = surfaceShaderExpression && surfaceShaderExpression->m_Opacity.m_LinkedExpressionOutput       != nullptr;
    m_CompilationOutput.mEmissiveColorConnencted = surfaceShaderExpression && surfaceShaderExpression->m_EmissiveColor.m_LinkedExpressionOutput != nullptr;
    m_CompilationOutput.mNormalConnencted        = surfaceShaderExpression && surfaceShaderExpression->m_Normal.m_LinkedExpressionOutput        != nullptr;

    if (codes.NeedCompileForward)
    {
        auto params = defaultParams;
        HandleParticleUsage(params, true, true);
        codes.ForwardShaderCode.ShaderCode = TranslatePass_Surface(ForwardPassName, templateFileLocation.Forward, params);
        compileSuccess &= !codes.ForwardShaderCode.ShaderCode.empty();
    }

    if (codes.NeedCompileGPass)
    {
        auto params = defaultParams;
        HandleParticleUsage(params, true, false);
        if (m_InputParams.defines.UsedWithSkeletalMesh)
        {
            params.usages.emplace_back(MaterialUsage::USED_WITH_SKELETAL_MESH);
        }

        codes.GPassShaderCode.ShaderCode = TranslatePass_Surface(GPassName, templateFileLocation.GPass, params);
        compileSuccess &= !codes.GPassShaderCode.ShaderCode.empty();
    }

    if (codes.NeedCompileShadow)
    {
        auto params = defaultParams;
        HandleParticleUsage(params, true, false);
        codes.ShadowShaderCode.ShaderCode = TranslatePass_Surface(ShadowPassName, templateFileLocation.Shadow, params);
        compileSuccess &= !codes.ShadowShaderCode.ShaderCode.empty();
    }

    if (codes.NeedCompileVSMDepth)
    {
        auto params = defaultParams;
        HandleParticleUsage(params, true, false);
        codes.VSMDepthShaderCode.ShaderCode = TranslatePass_Surface(VSMPassName, templateFileLocation.VSMDepth, params);
        compileSuccess &= !codes.VSMDepthShaderCode.ShaderCode.empty();
    }

    if (codes.NeedCompileMeshDecal)
    {
        auto params = defaultParams;
        HandleParticleUsage(params, true, true);
        codes.MeshDecalShaderCode.ShaderCode = TranslatePass_Surface(MeshDecalPassName, templateFileLocation.MeshDecal, params);
        compileSuccess &= !codes.MeshDecalShaderCode.ShaderCode.empty();
    }

    return compileSuccess;
}

void MaterialCompiler::ResetCompileState()
{
    m_NextSymbolIndex = 0;

    for (int i = 0; i < SF_NumFrequencies; i++)
    {
        m_SharedPropertyCodeChunks[i].clear();

        for (auto* functionState : m_FunctionStacks[i])
        {
            delete functionState;
        }

        m_FunctionStacks[i].clear();
        m_FunctionStacks[i].push_back(new MaterialFunctionCompileState(nullptr));
    }

    m_ShaderConstSet.Clear();
    m_ExpressionCustomEntries.clear();
    m_Samplers.clear();
    m_MacroSet.Clear();
    m_CustomInterpolatorSet.Clear();
    m_ExternalCustomInterpolatorCodeIndices.clear();
    m_NumVirtualTextureFeedbackRequests = 0;
}

std::string MaterialCompiler::TranslatePass_Surface(std::string_view passName, std::string_view templateFileName, const TranslateParams& params)
{
    // Reset compile state
    ResetCompileState();

    // Add common macros
    if (passName == ForwardPassName || passName == GPassName || passName == MeshDecalPassName)
    {
        m_MacroSet.AddMacro(Macro_VertexNeedNormal);
        m_MacroSet.AddMacro(Macro_VertexNeedTangent);
    }
    else if (passName == ShadowPassName)
    {
        // ShadowMap need normal to do depth bias
        m_MacroSet.AddMacro(Macro_VertexNeedNormal);
    }
    else if (passName == VSMPassName)
    {
        m_MacroSet.AddMacro(Macro_VertexNeedClipDistance);
    }

    for (auto& define : params.defines)
    {
        m_MacroSet.AddMacro(std::string(define));
    }

    // when translate shadow pass, we will only compile OpacityMask and WorldPositionOffset
    m_IsCompileShadowPass = passName == ShadowPassName || passName == VSMPassName;

    int32_t result = CODE_CHUNK_INDEX_NONE;

    MaterialExpressionVertexShader* vertexShaderExpression = m_InputParams.vertexShaderExpression;
    VertexShaderCompileResult* vertexShaderCompileResult;
    MaterialExpressionSurfaceShader* surfaceShaderExpression = m_InputParams.surfaceShaderExpression;
    SurfaceShaderCompileResult* surfaceShaderCompileResult;

    MaterialExpression* previewExpression = m_InputParams.previewExpression;

    if (!previewExpression)
    {
        vertexShaderExpression->m_CompileResult.Clear();
        surfaceShaderExpression->m_CompileResult.Clear();
        CallRootExpression(vertexShaderExpression);
        GatherCustomVertexInterpolators(surfaceShaderExpression);
        CallRootExpression(surfaceShaderExpression);

        vertexShaderCompileResult = &vertexShaderExpression->m_CompileResult;
        surfaceShaderCompileResult = &surfaceShaderExpression->m_CompileResult;

        if (!surfaceShaderCompileResult->IsValid() || !vertexShaderCompileResult->IsValid() || !m_CustomInterpolatorSet.IsValid())
        {
            return "";
        }
    }
    else
    {
        AssignShaderFrequencyScope(SF_Surface);

        Assert(!previewExpression->GetOutputPins().empty());

        result = CallExpression(previewExpression, previewExpression->GetOutputPins()[0]);

        if (result == CODE_CHUNK_INDEX_NONE)
        {
            result = Constant3(0.0, 0.0, 0.0);
        }
    }

    if (HasVT())
    {
        m_MacroSet.AddMacro("OPEN_VT");
        m_MacroSet.AddMacro("NUM_VIRTUALTEXTURE_SAMPLES", std::to_string(m_NumVirtualTextureFeedbackRequests));
    }

    if (m_InputParams.defines.UsedWithTerrain)
    {
        m_MacroSet.AddMacro(Macro_NumMaterialTexcoords, "1");
        m_MacroSet.AddMacro(Macro_VertexNeedNormal);
        m_MacroSet.AddMacro(Macro_VertexNeedTangent);
        m_MacroSet.AddMacro(GetTerrainMacro(m_InputParams.defines.TerrainMode));
    }

    // Assemble hlsl code
    {
        ShaderTemplate shaderTemplate;

        // Keywords and enables
        {
            std::stringstream ss;
            for (auto& keyword : params.keywords)
            {
                ss << fmt::format("#pragma keyword {}\n", keyword);
            }
            for (auto& usage : params.usages)
            {
                ss << fmt::format("#pragma usage {}\n", usage.Usage);
                for (auto& keyword : usage.Keywords)
                {
                    ss << fmt::format("#pragma usage_keyword {}\n", keyword);
                }
            }
            for (auto& enable : params.enables)
            {
                ss << fmt::format("#pragma enable {}\n", enable);
            }

            shaderTemplate.PushCode("KeywordDefinitions", ss.str());
        }

        // Macro
        {
            std::stringstream ss;
            for (const auto& [key, value] : m_MacroSet.GetMacros())
            {
                ss << fmt::format("#define {} {}\n", key, value);
            }

            shaderTemplate.PushCode("MacroDefinitions", ss.str());
        }

        // ShaderConst
        {
            std::stringstream ss;
            for (auto& shaderConst : m_ShaderConstSet.GetShaderConsts())
            {
                ss << fmt::format("SHADER_CONST({}, {}, {});\n", HLSLTypeString(GetMaterialValueType(shaderConst.Type)), shaderConst.ShaderConstName, 1);
            }

            shaderTemplate.PushCode("ShaderConstDefinitions", ss.str());
        }

        // ObjectSceneData
        {
            std::stringstream ss;
            for (auto& [name, type] : m_GPUSceneObjectDataSet)
            {
                ss << fmt::format("{} {}; \\\n", HLSLTypeString(type), name);
            }

            shaderTemplate.PushCode("ObjectSceneDataDefinitions", ss.str());
        }

        // PrimitiveSceneData
        {
            std::stringstream ss;
            for (auto& [name, type] : m_GPUScenePrimitiveDataSet)
            {
                ss << fmt::format("{} {}; \\\n", HLSLTypeString(type), name);
            }

            shaderTemplate.PushCode("PrimitiveSceneDataDefinitions", ss.str());
        }

        // NumericParameter
        {
            std::stringstream ss;
            for (auto& parameter : m_AllUniformExpressionSet.GetParamters())
            {
                if (!IsTexture(parameter.m_Type))
                {
                    ss << HLSLTypeString(GetMaterialValueType(parameter.m_Type)) << " " << parameter.m_ParameterName << ";\\\n";
                }
            }

            shaderTemplate.PushCode("NumericParameters", ss.str());
        }

        // TextureParameter
        {
            std::stringstream ss;

            for (auto& [sceneTextureId, lookUp] : m_SceneTextures)
            {
                if (lookUp)
                {
                    ss << fmt::format("Texture2D<float4> ce_{} : register(space0);\n", sceneTextureId);
                }
            }

            if (HasVT())
            {
                ss << "#include \"Features/VirtualTexture/VirtualTextureCommon.hlsl\"\n";
            }
            if (m_InputParams.defines.UsedWithTerrain)
            {
                ss << "#include \"Material/Lit/SurfaceData_MaterialEditor/Terrain.hlsl\"\n";
            }

            for (auto& parameter : m_AllUniformExpressionSet.GetParamters())
            {
                if (IsTexture(parameter.m_Type))
                {
                    if (int index = FindVtIndex(parameter.m_ParameterName); index != -1)
                    {
                        // Virtual texture
                        ss << fmt::format("DECLARVT(Texture2D<float4>, {}, {});\n", parameter.m_ParameterName, index);
                    }
                    else
                    {
                        // Not virtual texture
                        ss << HLSLTypeString(GetMaterialValueType(parameter.m_Type)) << " " << parameter.m_ParameterName << " : register(space1);\n";
                    }
                }
            }

            shaderTemplate.PushCode("TextureParameters", ss.str());
        }

        // Sampler
        {
            std::stringstream ss;

            for (auto& sampler : m_Samplers)
            {
                ss << fmt::format("SamplerState {} : register(space1);\n", sampler.Name);
            }

            shaderTemplate.PushCode("SamplerParameters", ss.str());
        }

        // CustomFunction
        {
            std::stringstream ss;
            for (auto& expressionCustomEntry : m_ExpressionCustomEntries)
            {
                ss << expressionCustomEntry.Implementation << "\n";
            }

            shaderTemplate.PushCode("CustomFunctions", ss.str());
        }

        // CustomVSOutput
        {
            std::stringstream ss;
            int index = 1;
            for (const auto& [type, _, innerName, code] : m_CustomInterpolatorSet.GetCustomInterpolators())
            {
                ss << fmt::format("{} {} : COLOR{}; \\\n", HLSLTypeString(GetMaterialValueType(type)), innerName, std::to_string(index++));
            }

            shaderTemplate.PushCode("CustomVSOutput", ss.str());
        }

        // CustomPSInput
        {
            std::stringstream ss;
            for (const auto& [type, _, innerName, code] : m_CustomInterpolatorSet.GetCustomInterpolators())
            {
                ss << fmt::format("{} {}; \\\n", HLSLTypeString(GetMaterialValueType(type)), innerName);
            }

            shaderTemplate.PushCode("CustomPSInput", ss.str());
        }

        // CustomSurfaceInput
        {
            std::stringstream ss;
            for (const auto& [type, _, innerName, code] : m_CustomInterpolatorSet.GetCustomInterpolators())
            {
                ss << fmt::format("{} {}; \\\n", HLSLTypeString(GetMaterialValueType(type)), innerName);
            }

            shaderTemplate.PushCode("CustomSurfaceInput", ss.str());
        }

        // WorldPositionOffset
        {
            // switch ShaderFrequency to make GetParameterCode() to get correct result
            AssignShaderFrequencyScope(SF_WorldPositionOffset);

            std::stringstream ss;

            if (HasVT())
            {
                ss << "FVirtualTextureFeedbackParams vtFeedbackParams = (FVirtualTextureFeedbackParams)0;\n";
                ss << "InitializeVirtualTextureFeedback(vtFeedbackParams, input.positionNDC);\n";
                ss << "#if ENABLE_STOCHASTIC_FILTERING\n";
                ss << "float4 randParams;\n";
                ss << "InitializeRandParams(randParams, input.positionNDC.xy);\n";
                ss << "#endif\n";
            }

            for (auto& codeChunk : m_SharedPropertyCodeChunks[SF_WorldPositionOffset])
            {
                if (!codeChunk.m_IsInlined)
                {
                    ss << codeChunk.m_Definition;
                }
            }

            if (!m_InputParams.previewExpression)
            {
                if (vertexShaderCompileResult->WorldPositionOffset)
                {
                    ss << "return positionWS + " << GetParameterCode(*vertexShaderCompileResult->WorldPositionOffset) << ";\n";
                }
                else
                {
                    ss << "return positionWS;\n";
                }
            }
            else
            {
                ss << "return positionWS;\n";
            }

            shaderTemplate.PushCode("WorldPositionOffset", ss.str());
        }

        // Generate CustomInterpolators
        {
            // switch ShaderFrequency to make GetParameterCode() to get correct result
            AssignShaderFrequencyScope(SF_GenerateCustomInterpolators);

            std::stringstream ss;

            if (HasVT())
            {
                ss << "FVirtualTextureFeedbackParams vtFeedbackParams = (FVirtualTextureFeedbackParams)0;\n";
                ss << "InitializeVirtualTextureFeedback(vtFeedbackParams, input.positionNDC);\n";
                ss << "#if ENABLE_STOCHASTIC_FILTERING\n";
                ss << "float4 randParams;\n";
                ss << "InitializeRandParams(randParams, input.positionNDC.xy);\n";
                ss << "#endif\n";
            }

            for (auto& codeChunk : m_SharedPropertyCodeChunks[SF_GenerateCustomInterpolators])
            {
                if (!codeChunk.m_IsInlined)
                {
                    ss << codeChunk.m_Definition;
                }
            }

            const auto& customInnerInterpolators = m_CustomInterpolatorSet.GetCustomInterpolators();
            for (int i = 0; i < customInnerInterpolators.size(); i++)
            {
                ss << fmt::format("input.{} = {};\n", customInnerInterpolators[i].InnerName, GetParameterCode(customInnerInterpolators[i].CodeIndex).data());
            }
            
            shaderTemplate.PushCode("GenerateCustomInterpolators", ss.str());
        }

        // SetCustomPSInput
        {
            std::stringstream ss;
            for (const auto& [type, _, innerName, code] : m_CustomInterpolatorSet.GetCustomInterpolators())
            {
                ss << fmt::format("output.{} = input.{}; \\\n", innerName, innerName);
            }

            shaderTemplate.PushCode("SetCustomPSInput", ss.str());
        }

        // SetCustomPSInput
        {
            std::stringstream ss;
            for (const auto& [type, _, innerName, code] : m_CustomInterpolatorSet.GetCustomInterpolators())
            {
                ss << fmt::format("surfaceInput.{} = psInput.{}; \\\n", innerName, innerName);
            }

            shaderTemplate.PushCode("SetCustomSurfaceInput", ss.str());
        }

        // SurfaceData
        {
            // switch ShaderFrequency to make GetParameterCode() to get correct result
            AssignShaderFrequencyScope(SF_Surface);

            std::stringstream ss;

            if (HasVT())
            {
                ss << "FVirtualTextureFeedbackParams vtFeedbackParams = (FVirtualTextureFeedbackParams)0;\n";
                ss << "InitializeVirtualTextureFeedback(vtFeedbackParams, input.positionNDC);\n";
                ss << "#if ENABLE_STOCHASTIC_FILTERING\n";
                ss << "float4 randParams;\n";
                ss << "InitializeRandParams(randParams, input.positionNDC.xy);\n";
                ss << "#endif\n";
            }

            for (auto& codeChunk : m_SharedPropertyCodeChunks[SF_Surface])
            {
                if (!codeChunk.m_IsInlined)
                {
                    ss << codeChunk.m_Definition;
                }
            }

            ss << "SurfaceData surfaceData = (SurfaceData)0;\n";

            if (!m_InputParams.previewExpression)
            {
                if (surfaceShaderCompileResult->baseColor)
                {
                    ss << "surfaceData.baseColor = " + GetParameterCode(*surfaceShaderCompileResult->baseColor) << ";\n";
                }

                if (surfaceShaderCompileResult->metallic)
                {
                    ss << "surfaceData.metallic = " + GetParameterCode(*surfaceShaderCompileResult->metallic) << ";\n";
                }

                if (surfaceShaderCompileResult->specular)
                {
                    ss << "surfaceData.specular = " + GetParameterCode(*surfaceShaderCompileResult->specular) << ";\n";
                }

                if (surfaceShaderCompileResult->roughness)
                {
                    ss << "surfaceData.roughness = " + GetParameterCode(*surfaceShaderCompileResult->roughness) << ";\n";
                }

                if (surfaceShaderCompileResult->opacity)
                {
                    ss << "surfaceData.opacity = " + GetParameterCode(*surfaceShaderCompileResult->opacity) << ";\n";
                }

                if (surfaceShaderCompileResult->opacityMask)
                {
                    ss << "surfaceData.opacityMask = " + GetParameterCode(*surfaceShaderCompileResult->opacityMask) << ";\n";
                }

                ss << "surfaceData.opacityMaskClip = 0.3333;\n";

                if (surfaceShaderCompileResult->normal)
                {
                    ss << "surfaceData.normalTS = " + GetParameterCode(*surfaceShaderCompileResult->normal) << ";\n";
                }

                if (surfaceShaderCompileResult->ambientOcclusion)
                {
                    ss << "surfaceData.ambientOcclusion = " + GetParameterCode(*surfaceShaderCompileResult->ambientOcclusion) << ";\n";
                }

                if (surfaceShaderCompileResult->emissiveColor)
                {
                    ss << "surfaceData.emissiveColor = " + GetParameterCode(*surfaceShaderCompileResult->emissiveColor) << ";\n";
                }

                if (surfaceShaderCompileResult->subsurfaceColor)
                {
                    ss << "surfaceData.subsurfaceColor = " + GetParameterCode(*surfaceShaderCompileResult->subsurfaceColor) << ";\n";
                }

                if (surfaceShaderCompileResult->debugColor)
                {
                    ss << "surfaceData.debugColor = " + GetParameterCode(*surfaceShaderCompileResult->debugColor) << ";\n";
                }

                if (surfaceShaderCompileResult->temporalReactive)
                {
                    ss << "surfaceData.temporalReactive = " + GetParameterCode(*surfaceShaderCompileResult->temporalReactive) << ";\n";
                }

                if (HasVT())
                {
                    UInt32 FrameNumber = 1;
		            float Opacity = 1.0;
                    ss << "FinalizeVirtualTextureFeedback(vtFeedbackParams, input.positionNDC, " << Opacity << ", " << FrameNumber << ", VTFeedbackBuffer);\n";
                }

                ss << "surfaceData.materialType = MATERIAL_TYPE;\n";
            }
            else
            {
                ss << "surfaceData.opacity = 1.0f;\n";
                ss << "surfaceData.opacityMask = 1.0f;\n";
                ss << "surfaceData.opacityMaskClip = 0.333;\n";
                ss << "surfaceData.normalTS = float3(0, 0, 1);\n";
                ss << "surfaceData.emissiveColor = " + GetParameterCode(result) << ";\n";
                ss << "surfaceData.materialType = MaterialType_Unlit;\n";
            }

            ss << "return surfaceData;";

            shaderTemplate.PushCode("SurfaceData", ss.str());
        }

        return shaderTemplate.OutputGeneratedCode(templateFileName);
    }
}

std::string MaterialCompiler::TranslatePass_PostProcess(const std::string& passName, const std::string& templateFileName, const TranslateParams& params)
{
    // Reset compile state
    ResetCompileState();

    m_MacroSet.AddMacro(Macro_NumMaterialTexcoords, "1");

    for (auto& define : params.defines)
    {
        m_MacroSet.AddMacro(std::string(define));
    }

    int32_t result = CODE_CHUNK_INDEX_NONE;

    /*MaterialExpressionVertexShader* vertexShaderExpression = m_MaterialEditor->GetVertexShaderExpression();
    VertexShaderCompileResult* vertexShaderCompileResult;*/
    MaterialExpressionSurfaceShader* surfaceShaderExpression = m_InputParams.surfaceShaderExpression;
    SurfaceShaderCompileResult* surfaceShaderCompileResult;

    MaterialExpression* previewExpression = m_InputParams.previewExpression;

    if (!m_InputParams.previewExpression)
    {
        //vertexShaderExpression->m_CompileResult.Clear();
        surfaceShaderExpression->m_CompileResult.Clear();

        //CallRootExpression(vertexShaderExpression);
        CallRootExpression(surfaceShaderExpression);

        //vertexShaderCompileResult = &vertexShaderExpression->m_CompileResult;
        surfaceShaderCompileResult = &surfaceShaderExpression->m_CompileResult;

        if (!surfaceShaderCompileResult->IsValid())// || !vertexShaderCompileResult->IsValid())
        {
            return "";
        }
    }
    else
    {
        AssignShaderFrequencyScope(SF_Surface);

        if (previewExpression)
        {
            assert(!previewExpression->GetOutputPins().empty());

            result = CallExpression(previewExpression, previewExpression->GetOutputPins()[0]);
        }

        if (result == CODE_CHUNK_INDEX_NONE)
        {
            result = Constant3(0.0, 0.0, 0.0);
        }
    }

    if (HasVT())
    {
        m_MacroSet.AddMacro("OPEN_VT");
    }

    // Assemble hlsl code
    {
        ShaderTemplate shaderTemplate;

        // Keywords and enables
        {
            std::stringstream ss;
            for (auto& keyword : params.keywords)
            {
                ss << fmt::format("#pragma keyword {}\n", keyword);
            }
            for (auto& usage : params.usages)
            {
                ss << fmt::format("#pragma usage {}\n", usage.Usage);
                for (auto& keyword : usage.Keywords)
                {
                    ss << fmt::format("#pragma usage_keyword {}\n", keyword);
                }
            }
            for (auto& enable : params.enables)
            {
                ss << fmt::format("#pragma enable {}\n", enable);
            }

            shaderTemplate.PushCode("KeywordDefinitions", ss.str());
        }

        // Macro
        {
            std::stringstream ss;
            for (const auto& [key, value] : m_MacroSet.GetMacros())
            {
                ss << fmt::format("#define {} {}\n", key, value);
            }

            shaderTemplate.PushCode("MacroDefinitions", ss.str());
        }

        // ShaderConst
        {
            std::stringstream ss;
            for (auto& shaderConst : m_ShaderConstSet.GetShaderConsts())
            {
                ss << fmt::format("SHADER_CONST({}, {}, {});\n", HLSLTypeString(GetMaterialValueType(shaderConst.Type)), shaderConst.ShaderConstName, 1);
            }

            shaderTemplate.PushCode("ShaderConstDefinitions", ss.str());
        }

        // NumericParameter
        {
            std::stringstream ss;
            for (auto& parameter : m_AllUniformExpressionSet.GetParamters())
            {
                if (!IsTexture(parameter.m_Type))
                {
                    ss << HLSLTypeString(GetMaterialValueType(parameter.m_Type)) << " " << parameter.m_ParameterName << ";\\\n";
                }
            }

            shaderTemplate.PushCode("NumericParameters", ss.str());
        }

        // TextureParameter
        {
            std::stringstream ss;

            for (auto& [sceneTextureId, lookUp] : m_SceneTextures)
            {
                if (lookUp)
                {
                    ss << fmt::format("Texture2D<float4> ce_{} : register(space0);\n", sceneTextureId);
                }
            }

            if (HasVT())
            {
                ss << "#include \"Features/VirtualTexture/VirtualTextureCommon.hlsl\"\n";
            }
            if (m_InputParams.defines.UsedWithTerrain)
            {
                ss << "#include \"Material/Lit/SurfaceData_MaterialEditor/Terrain.hlsl\"\n";
            }

            for (auto& parameter : m_AllUniformExpressionSet.GetParamters())
            {
                if (IsTexture(parameter.m_Type))
                {
                    if (int index = FindVtIndex(parameter.m_ParameterName); index != -1)
                    {
                        // Virtual texture
                        ss << fmt::format("DECLARVT(Texture2D<float4>, {}, {});\n", parameter.m_ParameterName, index);
                    }
                    else
                    {
                        // Not virtual texture
                        ss << HLSLTypeString(GetMaterialValueType(parameter.m_Type)) << " " << parameter.m_ParameterName << " : register(space1);\n";
                    }
                }
            }

            shaderTemplate.PushCode("TextureParameters", ss.str());
        }

        // Sampler
        {
            std::stringstream ss;

            for (auto& sampler : m_Samplers)
            {
                ss << fmt::format("SamplerState {} : register(space1);\n", sampler.Name);
            }

            shaderTemplate.PushCode("SamplerParameters", ss.str());
        }

        // CustomFunction
        {
            std::stringstream ss;
            for (auto& expressionCustomEntry : m_ExpressionCustomEntries)
            {
                ss << expressionCustomEntry.Implementation << "\n";
            }

            shaderTemplate.PushCode("CustomFunctions", ss.str());
        }

        // PostProcessData
        {
            // switch ShaderFrequency to make GetParameterCode() to get correct result
            AssignShaderFrequencyScope(SF_Surface);

            std::stringstream ss;

            if (HasVT())
            {
                ss << "FVirtualTextureFeedbackParams FeedbackParams = (FVirtualTextureFeedbackParams)0;\n";
                ss << "InitializeVirtualTextureFeedback(FeedbackParams, input.positionNDC);\n";
                ss << "#if ENABLE_STOCHASTIC_FILTERING\n";
                ss << "float4 randParams;\n";
                ss << "InitializeRandParams(randParams, input.positionNDC.xy);\n";
                ss << "#endif\n";
            }

            for (auto& codeChunk : m_SharedPropertyCodeChunks[SF_Surface])
            {
                if (!codeChunk.m_IsInlined)
                {
                    ss << codeChunk.m_Definition;
                }
            }

            ss << "float3 emissiveColor = " + GetParameterCode(*surfaceShaderCompileResult->emissiveColor) << ";\n";
            ss << "float opacity = " + (m_InputParams.defines.PostProcessEnableOutputAlpha ? GetParameterCode(*surfaceShaderCompileResult->opacity) : "1.f") << ";\n";
            ss << "return float4(emissiveColor.xyz, opacity);";

            shaderTemplate.PushCode("PostProcessData", ss.str());
        }

        return shaderTemplate.OutputGeneratedCode(templateFileName);
    }
}

void MaterialCompiler::CallExpressionSurfaceData(MaterialExpressionSurfaceShader* surfaceDataExpression) {}

void MaterialCompiler::CallExpression(MaterialExpression* expression)
{
    CallExpression(expression, nullptr);
}

const char* MaterialCompiler::GetTerrainMacro(TerrainMode terrainMode)
{
    switch (terrainMode)
    {
    case TerrainMode::Land:
        return Macro_TerrainLand;
        break;
    case TerrainMode::Ocean:
        return Macro_TerrainOcean;
        break;
    case TerrainMode::WeightBlend:
        return Macro_TerrainWeightBlend;
        break;
    default:
        Assert(false);
        return Macro_TerrainLand;
    }
}

void MaterialCompiler::AssignShaderFrequencyScope(ShaderFrequency shaderFrequency)
{
    m_CurrentScopeChunks = &m_SharedPropertyCodeChunks[shaderFrequency];
    m_ShaderFrequency = shaderFrequency;
}

void MaterialCompiler::BeginCompileShaderFrequency(MaterialExpression* expression, ShaderFrequency shaderFrequency)
{
    AssignShaderFrequencyScope(shaderFrequency);

    m_FunctionStacks[m_ShaderFrequency].back()->m_ExpressionStack.push_back({expression, nullptr});
}

void MaterialCompiler::EndCompileShaderFrequency()
{
    m_FunctionStacks[m_ShaderFrequency].back()->m_ExpressionStack.pop_back();
}

int32_t MaterialCompiler::CallExpression(MaterialExpression* expression, ExpressionOutput* output)
{
    EMaterialProperty currentProperty = this->GetMaterialAttribute();
    MaterialExpressionKey expressionKey{expression, output, currentProperty};

    auto& currentFunctionStack = m_FunctionStacks[m_ShaderFrequency];
    MaterialFunctionCompileState* currentFuncitonState = currentFunctionStack.back();

    bool expressionCompiled = false;
    int32_t existingCodeIndex = CODE_CHUNK_INDEX_NONE;
    if (auto iter = currentFuncitonState->m_ExpressionCodeMap.find(expressionKey); iter != currentFuncitonState->m_ExpressionCodeMap.end())
    {
        expressionCompiled = true;
        existingCodeIndex = iter->second;
    }

    if (expressionCompiled)
    {
        return existingCodeIndex;
    }
    else
    {
        // Disallow reentrance.
        if (std::find(currentFuncitonState->m_ExpressionStack.begin(), currentFuncitonState->m_ExpressionStack.end(), expressionKey) != currentFuncitonState->m_ExpressionStack.end())
        {
            return Error("Reentrant expression");
        }

        // The first time this expression is called, translate it.
        currentFuncitonState->m_ExpressionStack.push_back(expressionKey);

        // Attempt to share function states between function calls
        MaterialExpressionFunctionCall* functionCall = dynamic_cast<MaterialExpressionFunctionCall*>(expression);
        if (functionCall)
        {
            MaterialExpressionKey reuseCompileStateExpressionKey = expressionKey;
            expressionKey.output = nullptr;

            MaterialFunctionCompileState* sharedFunctionState = currentFuncitonState->FindOrAddSharedFunctionState(reuseCompileStateExpressionKey, functionCall);
            functionCall->SetSharedCompileState(sharedFunctionState);
        }

        int32_t result = expression->Compile(*this, output);

        if (auto* expressionParam = dynamic_cast<MaterialExpressionParameter*>(expression))
        {
            expressionParam->m_IsVisibleInMaterialInstanceEditor = true;
        }

        // Restore state
        if (functionCall)
        {
            functionCall->SetSharedCompileState(nullptr);
        }

        MaterialExpressionKey poppedExpressionKey = currentFuncitonState->m_ExpressionStack.back();
        currentFuncitonState->m_ExpressionStack.pop_back();

        // Cache the translation
        currentFuncitonState->m_ExpressionCodeMap[expressionKey] = result;

        return result;
    }
}

int32_t MaterialCompiler::CallRootExpression(MaterialExpression* expression)
{
    return expression->Compile(*this, nullptr);
}

void MaterialCompiler::CallMaterialFunctionOutputs(const std::vector<MaterialExpression*>& functionOutputs)
{
    AssignShaderFrequencyScope(SF_Surface);

    for (auto* functionOutput : functionOutputs)
    {
        CallExpression(functionOutput);
    }
}

void MaterialCompiler::PushFunction(MaterialFunctionCompileState* functionState)
{
    auto& currentFunctionStack = m_FunctionStacks[m_ShaderFrequency];
    currentFunctionStack.push_back(functionState);
}

MaterialFunctionCompileState* MaterialCompiler::PopFunction()
{
    auto& currentFunctionStack = m_FunctionStacks[m_ShaderFrequency];
    auto* functionState = currentFunctionStack.back();
    currentFunctionStack.pop_back();
    return functionState;
}

void MaterialCompiler::AddCustomInterpolator(MaterialCustomInterpolatorType type, std::string_view name, int32_t code)
{
    m_CustomInterpolatorSet.AddInterpolator(type, name, code);
}

int32_t MaterialCompiler::FindCustomInterpolator(std::string_view name)
{
    if (!IsShaderFrequency(SF_Surface))
    {
        return Errorf("Current shader state don't support CustomInterpolator");
    }

    MaterialCustomInterpolatorInfo* interpolatorInfo = interpolatorInfo = m_CustomInterpolatorSet.FindInterpolator(name);

    if (!interpolatorInfo)
    {
        return Errorf("CustomInterpolator:{} doesn't exist", name);
    }

    return AddInlinedCodeChunk(GetMaterialValueType(interpolatorInfo->Type), "input.{}", interpolatorInfo->InnerName);
}

size_t MaterialCompiler::GetCustomInterpolatorsSize()
{
    return m_CustomInterpolatorSet.GetCustomInterpolators().size();
}

void MaterialCompiler::GatherCustomVertexInterpolators(MaterialExpression* surfaceExpression)
{
    std::function<void(MaterialExpression*, std::unordered_set<MaterialExpression*>&, std::vector<MaterialExpression*>&)> GetAllExpressionsImpl;
    std::function<void(std::vector<MaterialExpression*>)> PreCompileVSInterpolators;

    GetAllExpressionsImpl = [&](MaterialExpression* expr, std::unordered_set<MaterialExpression*>& visited, std::vector<MaterialExpression*>& result)
    {
        if (!expr || visited.count(expr))
        {
            return;
        }
        visited.insert(expr);
        result.push_back(expr);
        for (auto* input : expr->m_Inputs)
        {
            if (input && input->m_LinkedExpressionOutput)
            {
                GetAllExpressionsImpl(input->m_LinkedExpressionOutput->m_ParentExpression, visited, result);
            }
        }
        if (auto expressionRerouteUsage = dynamic_cast<MaterialExpressionNamedRerouteUsage*>(expr))
        {
            GetAllExpressionsImpl(expressionRerouteUsage->m_Declaration, visited, result);
        }
    };

    auto GetAllExpressions = [&](MaterialExpression* rootExpression) 
    {
        std::unordered_set<MaterialExpression*> visited;
        std::vector<MaterialExpression*> result;
        GetAllExpressionsImpl(rootExpression, visited, result);
        return result;
    };

    auto GetFunctionCallExpressions = [&](MaterialExpressionFunctionCall* functionCall)
    {
        std::vector<MaterialExpression*> expressions;
        for (auto& expression : functionCall->m_MaterialFunctionEditorData.Expressions)
        {
            expressions.push_back(expression.get());
        }
        return expressions;
    };

    PreCompileVSInterpolators = [&](std::vector<MaterialExpression*> expressions) {
        for (MaterialExpression* expression : expressions)
        {
            if (auto* customInterpolator = dynamic_cast<MaterialExpressionCustomInterpolator*>(expression))
            {
                if (!customInterpolator->m_Input.m_LinkedExpressionOutput)
                {
                    continue;
                }
                int32_t Ret = customInterpolator->m_Input.Compile(*this);
                customInterpolator->m_Name = std::format("CustomVertexInterpolatorExternal{}", static_cast<int32_t>(GetCustomInterpolatorsSize()));
                static std::map<MaterialValueType, MaterialCustomInterpolatorType> interpolatorTypeMap = {
                    {MaterialValueType::MCT_Float, MaterialCustomInterpolatorType::Float4},
                    {MaterialValueType::MCT_Float1, MaterialCustomInterpolatorType::Float1},
                    {MaterialValueType::MCT_Float2, MaterialCustomInterpolatorType::Float2},
                    {MaterialValueType::MCT_Float3, MaterialCustomInterpolatorType::Float3},
                    {MaterialValueType::MCT_Float4, MaterialCustomInterpolatorType::Float4},
                    {MaterialValueType::MCT_UInt1, MaterialCustomInterpolatorType::UInt1},
                    {MaterialValueType::MCT_UInt2, MaterialCustomInterpolatorType::UInt2},
                    {MaterialValueType::MCT_UInt3, MaterialCustomInterpolatorType::UInt3},
                    {MaterialValueType::MCT_UInt4, MaterialCustomInterpolatorType::UInt4},
                };
                AddCustomInterpolator(interpolatorTypeMap.find(GetParameterType(Ret))->second, customInterpolator->m_Name, Ret);
            }
            else if (auto* functionCall = dynamic_cast<MaterialExpressionFunctionCall*>(expression))
            {
                auto& currentFunctionStack = m_FunctionStacks[SF_GenerateCustomInterpolators].back();
                if (!functionCall->m_MaterialFunction.empty())
                {
                    MaterialExpressionKey expressionKey{expression, nullptr, EMaterialProperty::None};
                    MaterialFunctionCompileState* sharedFunctionState = currentFunctionStack->FindOrAddSharedFunctionState(expressionKey, functionCall);
                    functionCall->SetSharedCompileState(sharedFunctionState);
                    PushFunction(sharedFunctionState);
                    PreCompileVSInterpolators(GetFunctionCallExpressions(functionCall));
                    PopFunction();
                    functionCall->SetSharedCompileState(nullptr);
                }
            }
        }
    };

    BeginCompileShaderFrequency(surfaceExpression, SF_GenerateCustomInterpolators);
    PreCompileVSInterpolators(GetAllExpressions(surfaceExpression));
    EndCompileShaderFrequency();
}

int32_t MaterialCompiler::ScalarParameter(std::string_view parameterName, float value)
{
    m_AllUniformExpressionSet.FindOrAddParameter(MaterialParameterType::Scalar, parameterName, value);

    return AddCodeChunk(MCT_Float, "{}", parameterName);
}

int32_t MaterialCompiler::VectorParameter(std::string_view parameterName, Float4 value)
{
    m_AllUniformExpressionSet.FindOrAddParameter(MaterialParameterType::Vector, parameterName, value);

    return AddCodeChunk(MCT_Float4, "{}", parameterName);
}

int32_t MaterialCompiler::TextureParameter(std::string_view parameterName, MaterialValueType type, std::string_view textureGuid)
{
    m_AllUniformExpressionSet.FindOrAddParameter(ToParameterType(type), parameterName, std::string(textureGuid));

    return AddInlinedCodeChunk(type, "{}", parameterName);
}

int32_t MaterialCompiler::ShaderConstBool(std::string_view shaderConstName, std::string_view shaderConstDisplayName, bool value)
{
    m_ShaderConstSet.FindOrAddShaderConst(MaterialShaderConstType::Bool, shaderConstName, shaderConstDisplayName, value);
    m_AllShaderConstSet.FindOrAddShaderConst(MaterialShaderConstType::Bool, shaderConstName, shaderConstDisplayName, value);

    return AddInlinedCodeChunk(MCT_Bool, "{}", shaderConstName);
}

int32_t MaterialCompiler::ShaderConstFloat(std::string_view shaderConstName, std::string_view shaderConstDisplayName, float value)
{
    m_ShaderConstSet.FindOrAddShaderConst(MaterialShaderConstType::Float, shaderConstName, shaderConstDisplayName, value);
    m_AllShaderConstSet.FindOrAddShaderConst(MaterialShaderConstType::Float, shaderConstName, shaderConstDisplayName, value);

    return AddInlinedCodeChunk(MCT_Float, "{}", shaderConstName);
}

int32_t MaterialCompiler::GPUSceneData(GPUSceneDataLevel level, MaterialValueType type, std::string_view GPUSceneDataName)
{
    switch (level)
    {
    case GPUSceneDataLevel::Object:
        m_GPUSceneObjectDataSet.emplace(GPUSceneDataName, type);
        return AddCodeChunk(type, "objectData.{}", GPUSceneDataName);
    case GPUSceneDataLevel::Primitive:
        m_GPUScenePrimitiveDataSet.emplace(GPUSceneDataName, type);
        return AddCodeChunk(type, "primitiveData.{}", GPUSceneDataName);
    default:
        Assert(false);
        return CODE_CHUNK_INDEX_NONE;
    }
}

int32_t MaterialCompiler::ObjectLocalBounds(BoundsOutputIndex outputIndex)
{
    uint32_t boundsCenter = AddCodeChunk(MCT_Float3, "primitiveData.ce_LocalBoundsCenter");
    uint32_t halfBoundsExtent = AddCodeChunk(MCT_Float3, "primitiveData.ce_LocalBoundsExtent");
    switch (outputIndex)
    {
    case BoundsOutputIndex::BoundsHalfExtent:
        return halfBoundsExtent;
    case BoundsOutputIndex::BoundsExtent:
        return Mul(halfBoundsExtent, Constant(2.f));
    case BoundsOutputIndex::BoundsMin:
        return Sub(boundsCenter, halfBoundsExtent);
    case BoundsOutputIndex::BoundsMax:
        return Add(boundsCenter, halfBoundsExtent);
    default:
        Assert(false);
        return CODE_CHUNK_INDEX_NONE;
    }
}

void MaterialCompiler::PushMaterialAttribute(EMaterialProperty attribute)
{
    m_MaterialAttributesStack.push_back(attribute);
}

void MaterialCompiler::PopMaterialAttribute()
{
    m_MaterialAttributesStack.pop_back();
}

EMaterialProperty MaterialCompiler::GetMaterialAttribute()
{
    if (m_MaterialAttributesStack.empty())
    {
        return EMaterialProperty::None;
    }
    return m_MaterialAttributesStack.back();
}

int32_t MaterialCompiler::ParticleColor()
{
    return AddCodeChunk(MaterialValueType::MCT_Float4, "GetParticleColor(objectData)");
}

int32_t cross::MaterialCompiler::ParticleUVScale()
{
    return AddCodeChunk(MaterialValueType::MCT_Float4, "GetParticleUVScale(objectData)");
}

int32_t cross::MaterialCompiler::ParticleAnimatedVelocity()
{
    return AddCodeChunk(MaterialValueType::MCT_Float3, "GetParticleAnimatedVelocity(objectData)");
}

int32_t cross::MaterialCompiler::ParticlePosition()
{
    return AddCodeChunk(MaterialValueType::MCT_Float3, "GetParticlePosition(objectData)");
}

int32_t cross::MaterialCompiler::ParticleRotation()
{
    return AddCodeChunk(MaterialValueType::MCT_Float3, "GetParticleRotation(objectData)");
}

int32_t cross::MaterialCompiler::ParticleSizeScale()
{
    return AddCodeChunk(MaterialValueType::MCT_Float3, "GetParticleSizeScale(objectData)");
}

int32_t MaterialCompiler::Constant(float x)
{
    return AddCodeChunk(MCT_Float, "{}", x);
}

int32_t MaterialCompiler::Constant2(float x, float y)
{
    return AddCodeChunk(MCT_Float2, "float2({}, {})", x, y);
}

int32_t MaterialCompiler::Constant3(float x, float y, float z)
{
    return AddCodeChunk(MCT_Float3, "float3({}, {}, {})", x, y, z);
}

int32_t MaterialCompiler::Constant4(float x, float y, float z, float w)
{
    return AddCodeChunk(MCT_Float4, "float4({}, {}, {}, {})", x, y, z, w);
}

int32_t MaterialCompiler::Sine(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(GetParameterType(x), "sin({})", GetParameterCode(x).data());
}

int32_t MaterialCompiler::Cosine(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(GetParameterType(x), "cos({})", GetParameterCode(x).data());
}

int32_t MaterialCompiler::Tangent(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(GetParameterType(x), "tan({})", GetParameterCode(x).data());
}

int32_t MaterialCompiler::Arcsine(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(GetParameterType(x), "asin({})", GetParameterCode(x).data());
}

int32_t MaterialCompiler::Arccosine(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(GetParameterType(x), "acos({})", GetParameterCode(x).data());
}

int32_t MaterialCompiler::Arctangent(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(GetParameterType(x), "atan({})", GetParameterCode(x).data());
}

int32_t MaterialCompiler::Arctangent2(int32_t y, int32_t x)
{
    if (y == CODE_CHUNK_INDEX_NONE || x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(GetParameterType(y), "atan2({}, {})", GetParameterCode(y).data(), GetParameterCode(x).data());
}

int32_t MaterialCompiler::Floor(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(GetParameterType(x), "floor({})", GetParameterCode(x).data());
}

int32_t MaterialCompiler::Ceil(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(GetParameterType(x), "ceil({})", GetParameterCode(x).data());
}

int32_t MaterialCompiler::Round(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(GetParameterType(x), "round({})", GetParameterCode(x).data());
}

int32_t MaterialCompiler::Truncate(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(GetParameterType(x), "trunc({})", GetParameterCode(x).data());
}

int32_t MaterialCompiler::Sign(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(GetParameterType(x), "sign({})", GetParameterCode(x).data());
}

int32_t MaterialCompiler::Frac(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(GetParameterType(x), "frac({})", GetParameterCode(x).data());
}

int32_t MaterialCompiler::Fmod(int32_t a, int32_t b)
{
    if (a == CODE_CHUNK_INDEX_NONE || b == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(GetParameterType(a), "fmod({}, {})", GetParameterCode(a).data(), GetParameterCode(b).data());
}

int32_t MaterialCompiler::ViewProperty(ExposedViewProperty Property, bool InvProperty)
{
    struct MaterialExposedViewPropertyMeta
    {
        ExposedViewProperty EnumValue;
        MaterialValueType Type;
        const char* PropertyCode;
        const char* InvPropertyCode;
    };

    static const MaterialExposedViewPropertyMeta ViewPropertyMetaArray[] = {
        {ExposedViewProperty::WorldSpaceCameraPosition, MCT_Float3, ("ce_CameraPos"), nullptr},
        {ExposedViewProperty::TileCameraPosition,       MCT_Float3, ("ce_CameraTilePosition"), nullptr},
        {ExposedViewProperty::CameraVectorWS,           MCT_Float3, ("normalize(ce_CameraPos.xyz - input.positionWS)"), nullptr},
        {ExposedViewProperty::TanHalfVieldOfView,       MCT_Float2, ("float2(ce_InvProjection[0][0], ce_InvProjection[1][1])"), nullptr},
        {ExposedViewProperty::ViewSize,                 MCT_Float2, ("ce_ScreenParams.xy"), ("ce_ScreenParams.zw")},
    };
    auto& PropertyMeta = ViewPropertyMetaArray[Property];
    return AddCodeChunk(PropertyMeta.Type, "{}", PropertyMeta.PropertyCode);
}

int32_t MaterialCompiler::Exponential(int32_t x)
{
    return AddCodeChunk(GetParameterType(x), "exp({})", GetParameterCode(x).data());
}

int32_t MaterialCompiler::Exponential2(int32_t x)
{
    return AddCodeChunk(GetParameterType(x), "exp2({})", GetParameterCode(x).data());
}

int32_t MaterialCompiler::DynamicBranch(int32_t condition, int32_t a, int32_t b)
{
    if (condition == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    if (b == CODE_CHUNK_INDEX_NONE)
    {
        return a;
    }

    if (a == CODE_CHUNK_INDEX_NONE)
    {
        return b;
    }

    if (GetParameterType(condition) != MCT_Bool)
    {
        return Errorf("Value is not type Bool");
    }

    MaterialValueType typeA = GetParameterType(a);
    MaterialValueType typeB = GetParameterType(b);
    MaterialValueType resultType = MCT_Unknown;
    if (typeA == typeB)
    {
        resultType = typeA;
    }
    else if (!IsFloatNumericType(typeA) || !IsFloatNumericType(typeB))
    {
        return Errorf("Cannot branch on non float numeric Types if they are not equal: {} {}", HLSLTypeString(typeA), HLSLTypeString(typeB));
    }
    else
    {
        resultType = GetNumComponents(typeA) > GetNumComponents(typeB) ? typeA : typeB;
    }

    a = ForceCast(a, resultType, MaterialCastFlags::ReplicateScalar);
    b = ForceCast(b, resultType, MaterialCastFlags::ReplicateScalar);
    std::string symbolName = CreateSymbolName("static");

    AddCodeChunk(MCT_VoidStatement, "{} {};", HLSLTypeString(resultType), symbolName);
    AddCodeChunk(MCT_VoidStatement, "switch (int({})){{ default: {} = {}; break; case 0: {} = {}; break;}}", GetParameterCode(condition).data(), symbolName.data(), GetParameterCode(a).data(), symbolName.data(), GetParameterCode(b).data());
    return AddCodeChunk(resultType, "{}", symbolName);
}

int32_t MaterialCompiler::If(int32_t a, int32_t b, int32_t aGreaterThanB, int32_t aEqualsB, int32_t aLessThanB)
{
    if (a == CODE_CHUNK_INDEX_NONE || b == CODE_CHUNK_INDEX_NONE || aGreaterThanB == CODE_CHUNK_INDEX_NONE || aLessThanB == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    if (!IsFloatNumericType(GetParameterType(a)))
    {
        return Errorf("If input A must be of type float");
    }

    if (!IsFloatNumericType(GetParameterType(b)))
    {
        return Errorf("If input B must be of type float");
    }

    if (aEqualsB != CODE_CHUNK_INDEX_NONE)
    {
        MaterialValueType resultType = GetArithmeticResultType(GetParameterType(aGreaterThanB), GetArithmeticResultType(GetParameterType(aEqualsB), GetParameterType(aLessThanB)));

        int32_t coercedAGreaterThanB = ForceCast(aGreaterThanB, resultType);
        int32_t coercedAEqualsB = ForceCast(aEqualsB, resultType);
        int32_t coercedALessThanB = ForceCast(aLessThanB, resultType);

        if (coercedAGreaterThanB == CODE_CHUNK_INDEX_NONE || coercedAEqualsB == CODE_CHUNK_INDEX_NONE || coercedALessThanB == CODE_CHUNK_INDEX_NONE)
        {
            return CODE_CHUNK_INDEX_NONE;
        }

        return AddCodeChunk(resultType,
                            "select(abs({} - {}) > 0.00001f, select({} >= {}, {}, {}), {})",
                            GetParameterCode(a).data(),
                            GetParameterCode(b).data(),
                            GetParameterCode(a).data(),
                            GetParameterCode(b).data(),
                            GetParameterCode(coercedAGreaterThanB).data(),
                            GetParameterCode(coercedALessThanB).data(),
                            GetParameterCode(coercedAEqualsB).data());
    }
    else
    {
        MaterialValueType resultType = GetArithmeticResultType(GetParameterType(aGreaterThanB), GetParameterType(aLessThanB));

        int32_t coercedAGreaterThanB = ForceCast(aGreaterThanB, resultType);
        int32_t coercedALessThanB = ForceCast(aLessThanB, resultType);

        if (coercedAGreaterThanB == CODE_CHUNK_INDEX_NONE || coercedALessThanB == CODE_CHUNK_INDEX_NONE)
        {
            return CODE_CHUNK_INDEX_NONE;
        }

        return AddCodeChunk(resultType, "select({} >= {}, {}, {})", GetParameterCode(a).data(), GetParameterCode(b).data(), GetParameterCode(coercedAGreaterThanB).data(), GetParameterCode(coercedALessThanB).data());
    }
}

int32_t MaterialCompiler::StaticBool(bool Value)
{
    // TODO: optimize fmt to std::format
    if (Value)
    {
        return AddInlinedCodeChunk(MCT_StaticBool, "true");
    }
    else
    {
        return AddInlinedCodeChunk(MCT_StaticBool, "false");
    }
    //return AddInlinedCodeChunk(MCT_StaticBool, Value ? "true" : "false");
}

int32_t MaterialCompiler::PixelDepth()
{
    if (m_IsCompilePostProcess)
    {
        return Errorf("Post process doesn't support PixelDepth, use SceneTextureLookup");
    }
    return AddCodeChunk(MCT_Float, "input.positionNDC.z");
}

int32_t MaterialCompiler::SceneDepth()
{
    return SceneTextureLookup(CODE_CHUNK_INDEX_NONE, SceneTextureId::SceneDepth, false, false);
}
int32_t MaterialCompiler::SceneLinearDepth()
{
    return SceneTextureLookup(CODE_CHUNK_INDEX_NONE, SceneTextureId::SceneDepth, false, true);
}

int32_t MaterialCompiler::PixelLinearDepth()
{
    int32_t pixelDepth = PixelDepth();
    return AddCodeChunk(MCT_Float, ("GetLinearDepth({})"), GetParameterCode(pixelDepth));
}

int32_t MaterialCompiler::SkyAtmosphereLightDirection(uint32_t lightIndex)
{
    return AddCodeChunk(MCT_Float3, "ce_AtmosphereLightData[{}].LightDirection", lightIndex);
}

int32_t MaterialCompiler::SkyAtmosphereLightDiskLuminance(uint32_t lightIndex, int32_t diskAngularDiameterOverride)
{
    return AddCodeChunk(MCT_Float3,
                        ("GetLightDiskLuminance(normalize(input.positionWS - ce_CameraPos.xyz), {}, {})"),
                        lightIndex,
                        diskAngularDiameterOverride == INDEX_NONE ? GetParameterCode(Constant(-1.0f)) : GetParameterCode(diskAngularDiameterOverride)
                       );
}

int32_t MaterialCompiler::SkyAtmosphereLightIlluminanceOnGround(uint32_t lightIndex)
{
    return AddCodeChunk(MCT_Float3, "ce_LightIlluminanceOnGroundPostTransmittance[{}]", lightIndex);
}

int32_t MaterialCompiler::SkyAtmosphereViewLuminance()
{
    return AddCodeChunk(MCT_Float3, "GetSkyAtmosphereViewLuminance(normalize(input.positionWS - ce_CameraPos.xyz))");
}

int32_t MaterialCompiler::VertexColor()
{
    m_MacroSet.AddMacro(Macro_VertexNeedVertexColor);
    return AddCodeChunk(MCT_Float4, "input.vertexColor");
}

int32_t MaterialCompiler::Comment(Float4 color)
{
    return AddCodeChunk(MCT_Float3, "float4({}, {}, {})", color.x, color.y, color.z, color.w);
}

int32_t MaterialCompiler::Abs(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(GetParameterType(x), "abs({})", GetParameterCode(x).data());
}

int32_t MaterialCompiler::Add(int32_t a, int32_t b)
{
    if (a == CODE_CHUNK_INDEX_NONE || b == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    const MaterialValueType resultType = GetArithmeticResultType(a, b);

    return AddCodeChunk(resultType, "({} + {})", GetParameterCode(a).data(), GetParameterCode(b).data());
}

int32_t MaterialCompiler::Sobol(int32_t Cell, int32_t Index, int32_t Seed)
{
    if (Cell == CODE_CHUNK_INDEX_NONE || Index == CODE_CHUNK_INDEX_NONE || Seed == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(MCT_Float2, "(floor({}) + float2(SobolIndex(SobolPixel(uint2({})), uint({})) ^ uint2({} * 0x10000) & 0xffff) / 0x10000)", 
        GetParameterCode(Cell).data(), 
        GetParameterCode(Cell).data(),
        GetParameterCode(Index).data(),
        GetParameterCode(Seed).data());
}

int32_t MaterialCompiler::Sub(int32_t a, int32_t b)
{
    if (a == CODE_CHUNK_INDEX_NONE || b == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    const MaterialValueType resultType = GetArithmeticResultType(a, b);

    return AddCodeChunk(resultType, "({} - {})", GetParameterCode(a).data(), GetParameterCode(b).data());
}

int32_t MaterialCompiler::Mul(int32_t a, int32_t b)
{
    if (a == CODE_CHUNK_INDEX_NONE || b == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    const MaterialValueType resultType = GetArithmeticResultType(a, b);

    return AddCodeChunk(resultType, "({} * {})", GetParameterCode(a).data(), GetParameterCode(b).data());
}

int32_t MaterialCompiler::Div(int32_t a, int32_t b)
{
    if (a == CODE_CHUNK_INDEX_NONE || b == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    const MaterialValueType resultType = GetArithmeticResultType(a, b);

    return AddCodeChunk(resultType, "({} / {})", GetParameterCode(a).data(), GetParameterCode(b).data());
}

int32_t MaterialCompiler::Dot(int32_t a, int32_t b)
{
    if (a == CODE_CHUNK_INDEX_NONE || b == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    const MaterialValueType resultType = GetArithmeticResultType(a, b);

    return AddCodeChunk(MCT_Float, "dot({}, {})", GetParameterCode(a).data(), GetParameterCode(b).data());
}

int32_t MaterialCompiler::Cross(int32_t a, int32_t b)
{
    if (a == CODE_CHUNK_INDEX_NONE || b == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(MCT_Float3, "cross({}, {})", GetParameterCode(a).data(), GetParameterCode(b).data());
}

int32_t MaterialCompiler::Power(int32_t base, int32_t exponent)
{
    if (base == CODE_CHUNK_INDEX_NONE || exponent == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(GetParameterType(base), "pow({}, {})", GetParameterCode(base).data(), GetParameterCode(exponent).data());
}

int32_t MaterialCompiler::Logarithm2(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(GetParameterType(x), "log2({})", GetParameterCode(x).data());
}

int32_t MaterialCompiler::Logarithm10(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(GetParameterType(x), "log10({})", GetParameterCode(x).data());
}

int32_t MaterialCompiler::SquareRoot(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(GetParameterType(x), "sqrt({})", GetParameterCode(x).data());
}

int32_t MaterialCompiler::Length(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(MCT_Float, "length({})", GetParameterCode(x).data());
}

int32_t MaterialCompiler::Normalize(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(GetParameterType(x), "normalize({})", GetParameterCode(x).data());
}

int32_t MaterialCompiler::Step(int32_t y, int32_t x)
{
    if (y == CODE_CHUNK_INDEX_NONE || x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    const MaterialValueType resultType = GetArithmeticResultType(y, x);

    return AddCodeChunk(resultType, "step({}, {})", GetParameterCode(y).data(), GetParameterCode(x).data());
}

int32_t MaterialCompiler::SmoothStep(int32_t x, int32_t y, int32_t a)
{
    if (x == CODE_CHUNK_INDEX_NONE || y == CODE_CHUNK_INDEX_NONE || a == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    MaterialValueType resultType = GetArithmeticResultType(y, x);
    MaterialValueType alphaType = resultType == (*m_CurrentScopeChunks)[x].m_Type ? resultType : MCT_Float1;

    return AddCodeChunk(resultType, "smoothstep({}, {}, {})", GetParameterCode(y).data(), GetParameterCode(x).data(), CoerceParameter(a, alphaType).data());
}

int32_t MaterialCompiler::InvLerp(int32_t x, int32_t y, int32_t a)
{
    if (x == CODE_CHUNK_INDEX_NONE || y == CODE_CHUNK_INDEX_NONE || a == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    int32_t numerator = Sub(a, x);
    int32_t denominator = Sub(y, x);

    return Div(numerator, denominator);
}

int32_t MaterialCompiler::Lerp(int32_t x, int32_t y, int32_t a)
{
    if (x == CODE_CHUNK_INDEX_NONE || y == CODE_CHUNK_INDEX_NONE || a == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    MaterialValueType resultType = GetArithmeticResultType(x, y);
    MaterialValueType alphaType = resultType == (*m_CurrentScopeChunks)[x].m_Type ? resultType : MCT_Float1;

    return AddCodeChunk(resultType, "lerp({}, {}, {})", GetParameterCode(x).data(), GetParameterCode(y).data(), CoerceParameter(a, alphaType).data());
}

int32_t MaterialCompiler::Min(int32_t a, int32_t b)
{
    if (a == CODE_CHUNK_INDEX_NONE || b == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(GetParameterType(a), "min({}, {})", GetParameterCode(a).data(), CoerceParameter(b, GetParameterType(a)).data());
}

int32_t MaterialCompiler::Max(int32_t a, int32_t b)
{
    if (a == CODE_CHUNK_INDEX_NONE || b == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(GetParameterType(a), "max({}, {})", GetParameterCode(a).data(), CoerceParameter(b, GetParameterType(a)).data());
}

int32_t MaterialCompiler::Clamp(int32_t x, int32_t a, int32_t b)
{
    if (x == CODE_CHUNK_INDEX_NONE || a == CODE_CHUNK_INDEX_NONE || b == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(GetParameterType(x), "min(max({}, {}), {})", GetParameterCode(x).data(), CoerceParameter(a, GetParameterType(x)).data(), CoerceParameter(b, GetParameterType(x)).data());
}

int32_t MaterialCompiler::Saturate(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(GetParameterType(x), "saturate({})", GetParameterCode(x).data());
}

int32_t MaterialCompiler::AppendVector(int32_t a, int32_t b)
{
    if (a == CODE_CHUNK_INDEX_NONE || b == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    const MaterialValueType typeA = GetParameterType(a);
    const MaterialValueType typeB = GetParameterType(b);
    const int32_t numComponentsA = GetNumComponents(typeA);
    const int32_t numComponentsB = GetNumComponents(typeB);
    const int32_t numResultComponents = numComponentsA + numComponentsB;

    if (numResultComponents > 4)
    {
        return Errorf("Can't append {} to {}", HLSLTypeString(typeA), HLSLTypeString(typeB));
    }

    return AddCodeChunk(GetVectorType(numResultComponents), "float{}({}, {})", numResultComponents, GetParameterCode(a).data(), GetParameterCode(b).data());
}

int32_t MaterialCompiler::DDX(int32_t a)
{
    if (a == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(GetParameterType(a), "ddx({})", GetParameterCode(a).data());
}

int32_t MaterialCompiler::DDY(int32_t a)
{
    if (a == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(GetParameterType(a), "ddy({})", GetParameterCode(a).data());
}

int32_t MaterialCompiler::TextureCoordinate(uint32_t coordinateIndex)
{
    const uint32_t maxNumCoordinates = 3;

    if (coordinateIndex >= maxNumCoordinates)
    {
        return Errorf("Only {} texture coordinate sets can be used by this feature level, currently using {}", maxNumCoordinates, coordinateIndex + 1);
    }

    if (m_IsCompilePostProcess)
    {
        if (coordinateIndex!=0)
        return Errorf("Post process only support TextureCoordinate-0");
    }

    auto macros = m_MacroSet.GetMacros();
    std::string texCoordCode;
    int texNum = coordinateIndex + 1;
    switch (coordinateIndex)
    {
    case 0:
        texCoordCode = "input.uvs[0].xy";
        break;
    case 1:
        texCoordCode = "input.uvs[0].zw";
        break;
    case 2:
        texCoordCode = "input.uvs[1].xy";
        break;
    }

    if (auto pair = macros.find(Macro_NumMaterialTexcoords); pair != macros.end())
    {
        texNum = std::max(texNum, std::stoi(pair->second));
    }
    m_MacroSet.AddMacro(Macro_NumMaterialTexcoords, std::to_string(texNum));

    return AddCodeChunk(MCT_Float2, "{}", texCoordCode);
}

int32_t MaterialCompiler::ScreenUV()
{
    if (!IsShaderFrequency(SF_Surface))
    {
        return Errorf("Current shader state don't support ScreenUV");
    }

    return AddCodeChunk(MCT_Float2, "input.screenUV");
}

int32_t MaterialCompiler::PixelPosition()
{
    return AddCodeChunk(MCT_Float2, "(input.positionNDC.xy + 1.0) * 0.5 * ce_ScreenParams.xy");
}

int32_t MaterialCompiler::WorldPosition()
{
    if (m_IsCompilePostProcess)
    {
        return Errorf("Post process doesn't support WorldPosition, use SceneTextureLookup to get depth");
    }
    if (IsShaderFrequency(SF_WorldPositionOffset))
    {
        return AddCodeChunk(MCT_Float3, "positionWS");
    }
    else
    {
        return AddCodeChunk(MCT_Float3, "input.positionWS");
    }
}

int32_t MaterialCompiler::TilePosition()
{
    if (m_IsCompilePostProcess)
    {
        return Errorf("Post process doesn't support TilePosition");
    }

    return AddCodeChunk(MCT_Float3, "GetTilePosition(objectData, primitiveData)");
}

int32_t MaterialCompiler::WorldGeometryNormal()
{
    if (m_IsCompilePostProcess)
    {
        return Errorf("Post process doesn't support WorldGeometryNormal");
    }

    m_MacroSet.AddMacro(Macro_VertexNeedNormal);

    return AddCodeChunk(MCT_Float3, "input.normalWS");
}

int32_t MaterialCompiler::WorldTangent()
{
    if (m_IsCompilePostProcess)
    {
        return Errorf("Post process doesn't support WorldTangent");
    }

    m_MacroSet.AddMacro(Macro_VertexNeedTangent);
    return AddCodeChunk(MCT_Float3, "input.tangentWS");
}

int32_t MaterialCompiler::Time()
{
    return AddCodeChunk(MaterialValueType::MCT_Float, "ce_Time");
}

int32_t MaterialCompiler::EyeAdaption()
{
    return AddCodeChunk(MaterialValueType::MCT_Float, "ce_PreExposure");
}

int32_t MaterialCompiler::RotateAboutAxis(int32_t NormalizedRotationAxisAndAngleIndex, int32_t PositionOnAxisIndex, int32_t PositionIndex)
{
    if (NormalizedRotationAxisAndAngleIndex == INDEX_NONE || PositionOnAxisIndex == INDEX_NONE || PositionIndex == INDEX_NONE)
    {
        return INDEX_NONE;
    }
    else
    {
        const MaterialValueType PositionOnAxisType = GetParameterType(PositionOnAxisIndex);
        const MaterialValueType PositionType = GetParameterType(PositionIndex);
        const MaterialValueType InputType = IsLWCType(PositionOnAxisType) || IsLWCType(PositionType) ? MCT_LWCVector3 : MCT_Float3;
        return AddCodeChunk(MCT_Float3, "RotateAboutAxis({}, {}, {})", CoerceParameter(NormalizedRotationAxisAndAngleIndex, MCT_Float4), CoerceParameter(PositionOnAxisIndex, InputType), CoerceParameter(PositionIndex, InputType));
    }
}

int32_t MaterialCompiler::MaterialParameterCollection(const std::string& mpcPath, const MpcProperty& property)
{
    uint32_t mpcIndex = 0;
    MaterialValueType valueType = MaterialValueType::MCT_Float1;
    MaterialParameterType parameterType = MaterialParameterType::None;

    if (property.first == MpcDynamicEnum::PropertyNone)
    {
        return Errorf("Select invalid parameter in material parameter collection");
    }

    if (const auto v = std::get_if<float>(&property.second))
    {
        mpcIndex = m_AllParameterFromMpcSet.AddParameterCollectionParameter(mpcPath, property.first);
        valueType = MaterialValueType::MCT_Float1;
        parameterType = MaterialParameterType::Scalar;
        const std::string propertyName = "MPC" + std::to_string(mpcIndex) + "_" + property.first;
        m_AllUniformExpressionSet.FindOrAddParameter(MaterialParameterType::Scalar, propertyName, *v);
    }
    else if (const auto v2 = std::get_if<Float4>(&property.second))
    {
        mpcIndex = m_AllParameterFromMpcSet.AddParameterCollectionParameter(mpcPath, property.first);
        valueType = MaterialValueType::MCT_Float4;
        parameterType = MaterialParameterType::Vector;
        const std::string propertyName = "MPC" + std::to_string(mpcIndex) + "_" + property.first;
        m_AllUniformExpressionSet.FindOrAddParameter(MaterialParameterType::Vector, propertyName, *v2);
    }
    else
    {
        Assert(false);
    }

    return AddCodeChunk(valueType, "MPC{}_{}", mpcIndex, property.first);
}

int32_t MaterialCompiler::TerrainColor()
{
    auto textureCoordinate = TextureCoordinate(0);
    return AddCodeChunk(MaterialValueType::MCT_Float4, "GetTerrainColor({}, objectData)", GetParameterCode(textureCoordinate));
}

int32_t MaterialCompiler::TerrainCoords(int32_t terrainCoords)
{
    return AddCodeChunk(MaterialValueType::MCT_Float2, "GetTerrainLayerCoords({}, objectData)", GetParameterCode(terrainCoords));
}

int32_t MaterialCompiler::StaticTerrainLayerWeight(uint32_t layerIndex)
{
    auto textureCoordinate = TextureCoordinate(0);
    return AddCodeChunk(MaterialValueType::MCT_Float1, "GetStaticTerrainLayerWeight{}({}, objectData)[{}]", layerIndex / 4, GetParameterCode(textureCoordinate), layerIndex % 4);
}

int32_t MaterialCompiler::VertexID()
{
    if (!IsShaderFrequency(SF_WorldPositionOffset) && !IsShaderFrequency(SF_GenerateCustomInterpolators))
    {
        return Errorf("Current shader state don't support VertexID");
    }

    return AddInlinedCodeChunk(MaterialValueType::MCT_UInt1, "vsInput.vertexID");
}

int32_t MaterialCompiler::TransformByMatrix(std::string_view matrixName, std::string_view invMatrixName, int32_t A, TransformElementType elementType)
{
    switch (elementType)
    {
    case TransformElementType::Position:
        return AddCodeChunk(MaterialValueType::MCT_Float4, "mul({}, {})", matrixName, GetParameterCode(A));
    case TransformElementType::Normal:
        return AddCodeChunk(MaterialValueType::MCT_Float3, "mul(float4({}, 0), {}).xyz", GetParameterCode(A), invMatrixName);
    case TransformElementType::Vector:
        return AddCodeChunk(MaterialValueType::MCT_Float3, "mul({}, float4({}, 0)).xyz", matrixName, GetParameterCode(A));
    default:
        return Errorf("Invalid element type: {}", elementType);
    }
}

int32_t MaterialCompiler::Transform(MaterialCommonBasis SourceCoordBasis, MaterialCommonBasis DestCoordBasis, int32_t A, TransformElementType elementType)
{
    if (A == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    {
        // validation
        // if (m_ShaderFrequency != SF_Surface && m_ShaderFrequency != SF_WorldPositionOffset)
        //{
        //    return CODE_CHUNK_INDEX_NONE;
        //}

        // if (m_ShaderFrequency != SF_Surface && m_ShaderFrequency != SF_WorldPositionOffset)
        //{
        // if (SourceCoordBasis == MaterialCommonBasis::Local || DestCoordBasis == MaterialCommonBasis::Local)
        //{
        //     return Errorf(TEXT("Local space is only supported for vertex, compute or pixel shader"));
        // }
        // }

        if (elementType == TransformElementType::Position && (SourceCoordBasis == MaterialCommonBasis::Tangent || DestCoordBasis == MaterialCommonBasis::Tangent))
        {
            return Errorf(("Tangent basis not available for position transformations"));
        }

        // Construct float3(0,0,x) out of the input if it is a scalar
        // This way artists can plug in a scalar and it will be treated as height, or a vector displacement
        const auto SourceType = GetParameterType(A);
        const auto NumInputComponents = GetNumComponents(SourceType);
        if (NumInputComponents == 0)
        {
            return Errorf("Input has 0 component");
        }

        if (NumInputComponents == 1u && SourceCoordBasis == MaterialCommonBasis::Tangent)
        {
            A = AppendVector(Constant2(0, 0), A);
        }
        else if (NumInputComponents < 3u)
        {
            return Errorf(("input must be a 3-component vector (current: {}: {}) or a scalar (if source is Tangent)"), GetParameterCode(A), SourceType);
        }

        if (elementType == TransformElementType::Position)
        {
            if (NumInputComponents == 3)
            {
                A = AppendVector(A, Constant(1));
            }
        }
        else
        {
            if (NumInputComponents == 4)
            {
                A = ValidCast(A, MCT_Float3);
            }
        }
    }

    if (SourceCoordBasis == DestCoordBasis)
    {
        // no transformation needed
        return A;
    }

    int32_t intermediateResult = CODE_CHUNK_INDEX_NONE;

    if (SourceCoordBasis == cross::MaterialCommonBasis::Tangent || DestCoordBasis == cross::MaterialCommonBasis::Tangent)
    {
        m_MacroSet.AddMacro(Macro_VertexNeedNormal);
        m_MacroSet.AddMacro(Macro_VertexNeedTangent);
    }

    switch (SourceCoordBasis)
    {
    case cross::MaterialCommonBasis::Tangent:
        if (IsShaderFrequency(SF_WorldPositionOffset) || IsShaderFrequency(SF_GenerateCustomInterpolators))
        {
            intermediateResult = AddCodeChunk(MaterialValueType::MCT_Float3, "mul({}, GetTangentToWorldMatrix(input))", GetParameterCode(A));
        }
        else
        {
            intermediateResult = AddCodeChunk(MaterialValueType::MCT_Float3, "mul({}, input.TangentToWorld)", GetParameterCode(A));
        }
        break;
    case cross::MaterialCommonBasis::Local:
        if (m_InputParams.defines.Domain == MaterialDomain::Foliage)
            return Errorf("Can not use primitiveData in foliage domain.");
        
        intermediateResult = TransformByMatrix("primitiveData.ce_RootToWorld", "primitiveData.ce_WorldToRoot", A, elementType);
        break;
    case cross::MaterialCommonBasis::World:
        switch (DestCoordBasis)
        {
        case cross::MaterialCommonBasis::Tangent:
            if (IsShaderFrequency(SF_WorldPositionOffset) || IsShaderFrequency(SF_GenerateCustomInterpolators))
            {
                return AddCodeChunk(MaterialValueType::MCT_Float3, "mul(GetTangentToWorldMatrix(input), {})", GetParameterCode(A));
            }
            else
            {
                return AddCodeChunk(MaterialValueType::MCT_Float3, "mul(input.TangentToWorld, {})", GetParameterCode(A));
            }
        case cross::MaterialCommonBasis::Local:
        {
            if (m_InputParams.defines.Domain == MaterialDomain::Foliage)
            {
               return Errorf("Can not use primitiveData in foliage domain.");
            }else
            return TransformByMatrix("primitiveData.ce_WorldToRoot", "primitiveData.ce_RootToWorld", A, elementType);
        }
            
        case cross::MaterialCommonBasis::Camera:
            return TransformByMatrix("ce_View", "ce_InvView", A, elementType);
        case cross::MaterialCommonBasis::Instance:
            return TransformByMatrix("objectData.ce_InvWorld", "objectData.ce_World", A, elementType);
        default:
            return Errorf("Can't transform form {} to {}", SourceCoordBasis, DestCoordBasis);
        }
        break;
    case cross::MaterialCommonBasis::Camera:
        intermediateResult = TransformByMatrix("ce_InvView", "ce_View", A, elementType);
        break;
    case cross::MaterialCommonBasis::Instance:
        intermediateResult = TransformByMatrix("objectData.ce_World", "objectData.ce_InvWorld", A, elementType);
        break;
    default:
        return Errorf("Can't transform form {} to {}", SourceCoordBasis, DestCoordBasis);
    }
    return Transform(MaterialCommonBasis::World, DestCoordBasis, intermediateResult, elementType);
}

int32_t MaterialCompiler::TextureSample(SamplerState samplerState, int32_t texture, int32_t coordinate, int32_t level, int32_t bias, int32_t ddx, int32_t ddy)
{
    if (texture == CODE_CHUNK_INDEX_NONE || coordinate == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    // Sampling with implicit lod is only allowed in pixel shaders
    if (IsShaderFrequency(SF_WorldPositionOffset) || IsShaderFrequency(SF_GenerateCustomInterpolators))
    {
        samplerState.MipValueMode = TextureMipValueMode::MipLevel;
        level = Constant(0);
    }

    MaterialValueType textureType = GetParameterType(texture);

    if (!EnumHasAnyFlags(textureType, MCT_Texture))
    {
        Errorf("Sampling unknown texture type: {}", HLSLTypeString(textureType));
        return CODE_CHUNK_INDEX_NONE;
    }

    auto mipValueMode = samplerState.MipValueMode;
    if (mipValueMode == TextureMipValueMode::MipLevel)
    {
        if (level == CODE_CHUNK_INDEX_NONE)
        {
            return CODE_CHUNK_INDEX_NONE;
        }
        else if (!IsFloatNumericType(GetParameterType(level)))
        {
            return Errorf("Invalid Level parameter");
        }
    }
    else if (mipValueMode == TextureMipValueMode::MipBias)
    {
        if (bias == CODE_CHUNK_INDEX_NONE)
        {
            return CODE_CHUNK_INDEX_NONE;
        }
        else if (!IsFloatNumericType(GetParameterType(bias)))
        {
            return Errorf("Invalid Bias parameter");
        }
    }
    else if (mipValueMode == TextureMipValueMode::Derivative)
    {
        if (ddx == CODE_CHUNK_INDEX_NONE || ddy == CODE_CHUNK_INDEX_NONE)
        {
            return CODE_CHUNK_INDEX_NONE;
        }
        else if (!IsFloatNumericType(GetParameterType(ddx)))
        {
            return Errorf("Invalid DDX(UVs) parameter");
        }
        else if (!IsFloatNumericType(GetParameterType(ddy)))
        {
            return Errorf("Invalid DDY(UVs) parameter");
        }
    }

    // Get TextureName
    std::string textureName = GetParameterCode(texture);

    // Get Sampler
    std::string samplerName;
    {
        for (auto& sampler : m_AllSamplers)
        {
            if (sampler.SamplerState == samplerState)
            {
                samplerName = sampler.Name;
                break;
            }
        }

        if (samplerName.empty())
        {
            samplerName = "Sampler" + std::to_string(m_AllSamplers.size());

            m_AllSamplers.push_back(MaterialSampler{samplerName, samplerState});
        }

        bool isSamplerFound = false;
        for (auto& sampler : m_Samplers)
        {
            if (sampler.Name == samplerName)
            {
                isSamplerFound = true;
                break;
            }
        }

        if (!isSamplerFound)
        {
            m_Samplers.push_back(MaterialSampler{samplerName, samplerState});
        }
    }

    // Get UV
    int sampleCoordinate;
    if (textureType == MaterialValueType::MCT_TextureCube)
    {
        sampleCoordinate = ValidCast(coordinate, MaterialValueType::MCT_Float3);
    }
    else
    {
        sampleCoordinate = ValidCast(coordinate, MaterialValueType::MCT_Float2);
    }

    if (sampleCoordinate == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    // Assemble SampleCode
    std::string sampleCodeFinite;
    if (textureType == MaterialValueType::MCT_TextureVirtual || textureType == MaterialValueType::MCT_TextureVirtualNormal)
    {
        auto SamplerAddressModeToString = [](TextureAddressMode addressMode) {
            switch (addressMode)
            {
            case TextureAddressMode::Clamp:
                return "VTADDRESSMODE_CLAMP";
            case TextureAddressMode::Wrap:
                return "VTADDRESSMODE_WRAP";
            case TextureAddressMode::Mirror:
                return "VTADDRESSMODE_MIRROR";
            default:
                return "VTADDRESSMODE_WRAP";
            }
        };

        int index = FindOrAddVtIndex(textureName);
        if (index < 0)
        {
            Errorf("There cannot be more than 4 virtual textures in this material");
            return CODE_CHUNK_INDEX_NONE;
        }
        else
        {
            auto addressMode = SamplerAddressModeToString(samplerState.AddressMode);

            if (IsShaderFrequency(SF_WorldPositionOffset) || IsShaderFrequency(SF_GenerateCustomInterpolators))
            {
                sampleCodeFinite =
                    fmt::format("CustomizedSampleVT_VS({}, input.positionNDC.xy, VT_PARA({}, {}), {}, {}, {}, vtFeedbackParams, {}, {} \
                                \n#if ENABLE_STOCHASTIC_FILTERING \
                                \n    , randParams                \
                                \n#endif                          \
                        \n)",
                                GetParameterCode(sampleCoordinate),
                                textureName,
                                index,
                                samplerName,
                                level,
                                m_NumVirtualTextureFeedbackRequests,
                                addressMode,
                                addressMode);
            }
            else
            {
                sampleCodeFinite = fmt::format("CustomizedSampleVT({}, input.positionNDC.xy, VT_PARA({}, {}), {}, {}, vtFeedbackParams, {}, {} \
                                                \n#if ENABLE_STOCHASTIC_FILTERING \
                                                \n    , randParams                \
                                                \n#endif                          \
                        \n)",
                                               GetParameterCode(sampleCoordinate),
                                               textureName,
                                               index,
                                               samplerName,
                                               m_NumVirtualTextureFeedbackRequests,
                                               addressMode,
                                               addressMode);
            }

            ++m_NumVirtualTextureFeedbackRequests;
        }
    }
    else if (mipValueMode == TextureMipValueMode::None)
    {
        sampleCodeFinite = fmt::format("{}.Sample({}, {})", textureName, samplerName, GetParameterCode(sampleCoordinate));
    }
    else if (mipValueMode == TextureMipValueMode::MipLevel)
    {
        sampleCodeFinite = fmt::format("{}.SampleLevel({}, {}, {})", textureName, samplerName, GetParameterCode(sampleCoordinate), GetParameterCode(level));
    }
    else if (mipValueMode == TextureMipValueMode::MipBias)
    {
        sampleCodeFinite = fmt::format("{}.SampleBias({}, {}, {})", textureName, samplerName, GetParameterCode(sampleCoordinate), GetParameterCode(bias));
    }
    else if (mipValueMode == TextureMipValueMode::Derivative)
    {
        sampleCodeFinite = fmt::format("{}.SampleGrad({}, {}, {}, {})", textureName, samplerName, GetParameterCode(sampleCoordinate), GetParameterCode(ddx), GetParameterCode(ddy));
    }

    if (textureType == MCT_Texture2DNormal || textureType == MCT_TextureVirtualNormal)
    {
        return AddCodeChunk(MCT_Float4, "float4(UnpackNormalmapRGorAG({}), 0.0f)", sampleCodeFinite);
    }
    else
    {
        return AddCodeChunk(MCT_Float4, "{}", sampleCodeFinite);
    }
}

int32_t MaterialCompiler::CustomExpression(MaterialExpressionCustom* expressionCustom, ExpressionOutput* output, std::vector<int32_t> compiledInputs)
{
    MaterialExpressionCustomEntry* customEntry = nullptr;
    for (auto& entry : m_ExpressionCustomEntries)
    {
        if (entry.Expression == expressionCustom)
        {
            customEntry = &entry;
        }
    }

    // Create Definition
    if (!customEntry)
    {
        std::string outputTypeString;
        MaterialValueType outputType;
        switch (expressionCustom->m_OutputType)
        {
        case CustomMaterialOutputType::Float2:
            outputType = MCT_Float2;
            outputTypeString = "float2";
            break;
        case CustomMaterialOutputType::Float3:
            outputType = MCT_Float3;
            outputTypeString = "float3";
            break;
        case CustomMaterialOutputType::Float4:
            outputType = MCT_Float4;
            outputTypeString = "float4";
            break;
        default:
            outputType = MCT_Float;
            outputTypeString = "float";
            break;
        }

        // Declare implementation function
        std::string inputParamDecl;
        for (int i = 0; i < expressionCustom->m_CustomInputs.size(); i++)
        {
            if (expressionCustom->m_CustomInputs[i].Name.empty())
            {
                continue;
            }

            if (i != 0)
            {
                inputParamDecl += ", ";
            }

            std::string inputNameString = expressionCustom->m_CustomInputs[i].Name;
            switch (GetParameterType(compiledInputs[i]))
            {
            case MCT_Bool:
                inputParamDecl += "bool ";
                inputParamDecl += inputNameString;
                break;
            case MCT_Float:
            case MCT_Float1:
                inputParamDecl += "float ";
                inputParamDecl += inputNameString;
                break;
            case MCT_Float2:
                inputParamDecl += "float2 ";
                inputParamDecl += inputNameString;
                break;
            case MCT_Float3:
                inputParamDecl += "float3 ";
                inputParamDecl += inputNameString;
                break;
            case MCT_Float4:
                inputParamDecl += "float4 ";
                inputParamDecl += inputNameString;
                break;
            case MCT_UInt:
            case MCT_UInt1:
                inputParamDecl += "uint ";
                inputParamDecl += inputNameString;
                break;
            case MCT_UInt2:
                inputParamDecl += "uint2 ";
                inputParamDecl += inputNameString;
                break;
            case MCT_UInt3:
                inputParamDecl += "uint3 ";
                inputParamDecl += inputNameString;
                break;
            case MCT_UInt4:
                inputParamDecl += "uint4 ";
                inputParamDecl += inputNameString;
                break;
            case MCT_Texture2D:
                // should provide a texture sampler in shader code
                inputParamDecl += "Texture2D ";
                inputParamDecl += inputNameString;
                break;
            default:
                return Errorf("Bad type {} for {} input {}", HLSLTypeString(GetParameterType(compiledInputs[i])), expressionCustom->m_Name, inputNameString);
                break;
            }
        }

        for (const auto& customOutput : expressionCustom->m_AdditionalCustomOutputs)
        {
            if (customOutput.Name.empty())
            {
                continue;
            }

            inputParamDecl += ", inout ";
            std::string outputNameString = customOutput.Name;
            switch (customOutput.OutputType)
            {
            case CustomMaterialOutputType::Float1:
                inputParamDecl += "float ";
                inputParamDecl += outputNameString;
                break;
            case CustomMaterialOutputType::Float2:
                inputParamDecl += "float2 ";
                inputParamDecl += outputNameString;
                break;
            case CustomMaterialOutputType::Float3:
                inputParamDecl += "float3 ";
                inputParamDecl += outputNameString;
                break;
            case CustomMaterialOutputType::Float4:
                inputParamDecl += "float4 ";
                inputParamDecl += outputNameString;
                break;
            default:
                return Errorf("Bad type {} for {} output {}", customOutput.OutputType, expressionCustom->m_Name, outputNameString);
                break;
            }
        }

        int32_t expressionCustomEntryIndex = static_cast<int32_t>(m_ExpressionCustomEntries.size());
        std::string code = expressionCustom->m_Code.empty() ? "0" : expressionCustom->m_Code;
        if (code.find("return") == std::string::npos)
        {
            code = "return " + code + ";";
        }

        MaterialExpressionCustomEntry& entry = m_ExpressionCustomEntries.emplace_back();
        entry.Expression = expressionCustom;
        entry.Implementation += fmt::format("{0} CustomExpression{1}({2})\n{{\n\t{3}\n}}\n", outputTypeString, expressionCustomEntryIndex, inputParamDecl, code);
        entry.FunctionName = fmt::format("CustomExpression{}", expressionCustomEntryIndex);
        customEntry = &entry;
    }

    auto& outputCodeIndices = customEntry->OutputCodeIndices[m_ShaderFrequency];
    auto& outputCodeIndicesVec = customEntry->OutputCodeIndicesVec[m_ShaderFrequency];
    if (auto iter = outputCodeIndices.find(output); iter != outputCodeIndices.end())
    {
        return iter->second;
    }
    else
    {
        // Create local temp variables to hold results of additional outputs
        for (auto& customOutput : expressionCustom->m_AdditionalCustomOutputs)
        {
            if (customOutput.Name.empty())
            {
                continue;
            }

            const std::string outputName = customOutput.Name;
            int32_t outputCodeIndex = CODE_CHUNK_INDEX_NONE;
            switch (customOutput.OutputType)
            {
            case CustomMaterialOutputType::Float1:
                outputCodeIndex = AddCodeChunk(MCT_Float, "0.0f");
                break;
            case CustomMaterialOutputType::Float2:
                outputCodeIndex = AddCodeChunk(MCT_Float2, "float2(0.0f, 0.0f)");
                break;
            case CustomMaterialOutputType::Float3:
                outputCodeIndex = AddCodeChunk(MCT_Float3, "float3(0.0f, 0.0f, 0.0f)");
                break;
            case CustomMaterialOutputType::Float4:
                outputCodeIndex = AddCodeChunk(MCT_Float4, "float4(0.0f, 0.0f, 0.0f, 0.0f)");
                break;
            }
            outputCodeIndices[&customOutput.Output] = (outputCodeIndex);
            outputCodeIndicesVec.push_back(outputCodeIndex);
        }

        // Add call to implementation function
        std::string codeChunk = fmt::format("{}(", customEntry->FunctionName);
        for (int i = 0; i < compiledInputs.size(); i++)
        {
            if (expressionCustom->m_CustomInputs[i].Name.empty())
            {
                continue;
            }

            std::string paramCode = GetParameterCode(compiledInputs[i]);
            MaterialValueType paramType = GetParameterType(compiledInputs[i]);

            if (i != 0)
            {
                codeChunk += ", ";
            }

            codeChunk += paramCode;

        }

        for (const auto& codeChunkIndex : outputCodeIndicesVec)
        {
            codeChunk += ", ";
            codeChunk += GetParameterCode(codeChunkIndex);
        }

        codeChunk += ")";

        MaterialValueType outputType;
        switch (expressionCustom->m_OutputType)
        {
        case CustomMaterialOutputType::Float2:
            outputType = MCT_Float2;
            break;
        case CustomMaterialOutputType::Float3:
            outputType = MCT_Float3;
            break;
        case CustomMaterialOutputType::Float4:
            outputType = MCT_Float4;
            break;
        default:
            outputType = MCT_Float;
            break;
        }

        outputCodeIndices[&expressionCustom->m_Output] = AddCodeChunk(outputType, "{}", codeChunk);

        return outputCodeIndices[output];
    }
}

int32_t MaterialCompiler::ComponentMask(int32_t vector, bool r, bool g, bool b, bool a)
{
    if (vector == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    MaterialValueType vectorType = GetParameterType(vector);
    uint32_t validComponentCount = 0u;
    if (vectorType & MCT_Float4)
        validComponentCount = 4u;
    else if (vectorType & MCT_Float3)
        validComponentCount = 3u;
    else if (vectorType & MCT_Float2)
        validComponentCount = 2u;
    else if (vectorType & MCT_Float1)
        validComponentCount = 1u;

    if ((a && validComponentCount < 4u) || (b && validComponentCount < 3u) || (g && validComponentCount < 2u) || (r && validComponentCount < 1u))
    {
        return Errorf("Not enough components in ({}: {}) for component mask {}{}{}{}", GetParameterCode(vector), HLSLTypeString(vectorType), r, g, b, a);
    }

    MaterialValueType resultType;
    switch ((r ? 1 : 0) + (g ? 1 : 0) + (b ? 1 : 0) + (a ? 1 : 0))
    {
    case 1:
        resultType = MCT_Float;
        break;
    case 2:
        resultType = MCT_Float2;
        break;
    case 3:
        resultType = MCT_Float3;
        break;
    case 4:
        resultType = MCT_Float4;
        break;
    default:
        return Errorf("Couldn't determine result type of component mask {}{}{}{}", r, g, b, a);
    };

    std::string maskString = fmt::format("{}{}{}{}", r ? "r" : "", g ? ((vectorType == MCT_Float) ? "r" : "g") : "", b ? ((vectorType == MCT_Float) ? "r" : "b") : "", a ? ((vectorType == MCT_Float) ? "r" : "a") : "");

    return AddCodeChunk(resultType, "{}.{}", GetParameterCode(vector), maskString);
}

int32_t MaterialCompiler::SceneTextureLookup(int32_t ViewportUV, SceneTextureId InSceneTextureId, bool bFiltered, bool bLinear)
{
    if (m_ShaderFrequency != SF_Surface)
    {
        // we can relax this later if needed
        return Error("Can't use Scene Texture Lookup in Non Pixel Shader");
    }

    UseSceneTextureId(InSceneTextureId, true);

    int32_t BufferUV;
    if (ViewportUV != CODE_CHUNK_INDEX_NONE)
    {
        BufferUV = AddCodeChunk(MCT_Float2, "clamp({}, 0, 1)", CoerceParameter(ViewportUV, MCT_Float2));
    }
    else
    {
        BufferUV = ScreenUV();
    }

    if (InSceneTextureId == SceneTextureId::SceneColor)
    {
        return AddCodeChunk(MCT_Float4, ("ce_{}.SampleLevel(ce_Sampler_{}, {}, 0)"), InSceneTextureId, bFiltered ? "Clamp" : "Point", CoerceParameter(BufferUV, MCT_Float2));
    }
    else if (InSceneTextureId == SceneTextureId::SceneDepth)
    {
        if (bLinear)
        {
            return AddCodeChunk(MCT_Float, ("GetLinearDepth(ce_{}.SampleLevel(ce_Sampler_{}, {}, 0).x)"), InSceneTextureId, bFiltered ? "Clamp" : "Point", CoerceParameter(BufferUV, MCT_Float2));
        }
        else
        {
            return AddCodeChunk(MCT_Float, ("ce_{}.SampleLevel(ce_Sampler_{}, {}, 0).x"), InSceneTextureId, bFiltered ? "Clamp" : "Point", CoerceParameter(BufferUV, MCT_Float2));
        }
    }
    return Error("Invalid Texture ID");
}

int32_t MaterialCompiler::GetSceneTextureViewSize(SceneTextureId SceneTextureId, bool InvProperty)
{
    // if (m_ShaderFrequency != SF_Surface && m_ShaderFrequency != SF_WorldPositionOffset)
    //{
    //     // we can relax this later if needed
    //     return CODE_CHUNK_INDEX_NONE;
    // }

    UseSceneTextureId(SceneTextureId, false);

    if (InvProperty)
    {
        return AddInlinedCodeChunk(MCT_Float2, "ce_ScreenParams.zw");
    }
    else
    {
        return AddInlinedCodeChunk(MCT_Float2, "ce_ScreenParams.xy");
    }
}

int32_t MaterialCompiler::AddCodeChunkInner(MaterialValueType type, const char* formattedCode, bool isInlined)
{
    if (type == MCT_Unknown)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    int32_t codeIndex = CODE_CHUNK_INDEX_NONE;

    if (type == MCT_VoidStatement)
    {
        codeIndex = static_cast<int32_t>(m_CurrentScopeChunks->size());
        const std::string statement = std::string(formattedCode) + LINE_TERMINATOR;
        m_CurrentScopeChunks->push_back(ShaderCodeChunk(statement.data(), statement.data(), type, false));
    }
    else if (isInlined)
    {
        codeIndex = static_cast<int32_t>(m_CurrentScopeChunks->size());
        m_CurrentScopeChunks->push_back(ShaderCodeChunk(formattedCode, formattedCode, type, true));
    }
    else if ((type & (MCT_Float | MCT_LWCType | MCT_VTPageTableResult | MCT_UInt)) || type == MCT_ShadingModel || type == MCT_MaterialAttributes || type == MCT_Strata || type == MCT_UInt)
    {
        codeIndex = static_cast<int32_t>(m_CurrentScopeChunks->size());
        std::string symbolName = CreateSymbolName("local");
        std::string localVariableDefinition = std::string(HLSLTypeString(type)) + " " + symbolName + " = " + formattedCode + ";" + LINE_TERMINATOR;
        m_CurrentScopeChunks->push_back(ShaderCodeChunk(localVariableDefinition.data(), symbolName.data(), type, false));
    }
    else
    {
        assert(false);
        return CODE_CHUNK_INDEX_NONE;
    }

    return codeIndex;
}

std::string MaterialCompiler::CastValue(std::string_view code, MaterialValueType srcType, MaterialValueType dstType, MaterialCastFlags flags)
{
    using namespace gbf;

    const bool allowTruncate = EnumHasAnyFlags(flags, MaterialCastFlags::AllowTruncate);
    const bool allowAppendZeroes = EnumHasAnyFlags(flags, MaterialCastFlags::AllowAppendZeroes);
    bool replicateScalar = EnumHasAnyFlags(flags, MaterialCastFlags::ReplicateScalar);

    if (srcType == dstType)
    {
        return std::string(code);
    }
    else if (IsFloatNumericOrVectorType(srcType) && IsFloatNumericOrVectorType(dstType))
    {
        const uint32 numSrcComponents = GetNumComponents(srcType);
        const uint32 numDstComponents = GetNumComponents(dstType);

        if (numSrcComponents != 1)
        {
            replicateScalar = false;
        }
        if (!replicateScalar && !allowAppendZeroes && numDstComponents > numSrcComponents)
        {
            if (numDstComponents == 4 && numSrcComponents == 3)
            {
                ///Apppend Vector 
                std::string result;
                result += fmt::format("float4({}.{}, 1.0)", code, "xyz");
                return result;
            }
            else
            {
                Errorf("Cannot cast from smaller type {} to larger type {}.", HLSLTypeString(srcType), HLSLTypeString(dstType));
                return std::string();
            }
        }
        if (!replicateScalar && !allowTruncate && numDstComponents < numSrcComponents)
        {
            Errorf("Cannot cast from larger type {} to smaller type {}.", HLSLTypeString(srcType), HLSLTypeString(dstType));
            return std::string();
        }

        std::string result;
        uint32_t numComponents = 0u;
        bool needClosingParam = false;

        if (numSrcComponents == numDstComponents)
        {
            numComponents = numDstComponents;
            result += code;
        }
        else if (replicateScalar)
        {
            // 1 -> 2,3,4
            numComponents = numDstComponents;
            result += fmt::format("(({}){})", HLSLTypeString(dstType), code);
        }
        else
        {
            numComponents = std::min(numSrcComponents, numDstComponents);
            if (numComponents < numDstComponents)
            {
                // src < dst
                result = fmt::format("{}(", HLSLTypeString(dstType));
                needClosingParam = true;
            }
            if (numComponents == numSrcComponents)
            {
                // src < dst
                result += code;
            }
            else
            {
                // src > dst
                static const char* mask[] = {"<ERROR>", "x", "xy", "xyz", "xyzw"};
                assert(numComponents <= 4);
                result += fmt::format("{}.{}", code, mask[numComponents]);
            }
        }

        if (needClosingParam)
        {
            for (uint32_t componentIndex = numComponents; componentIndex < numDstComponents; componentIndex++)
            {
                if (componentIndex > 0)
                {
                    result += ", ";
                }

                result += "0.0f";
            }
            numComponents = numDstComponents;
            result += ")";
        }
        assert(numComponents == numDstComponents);
        return result;
    }
    else
    {
        Errorf("Cannot cast between non-numeric types {} to {}.", HLSLTypeString(srcType), HLSLTypeString(dstType));
        return std::string();
    }
}

std::string MaterialCompiler::CoerceValue(std::string_view code, MaterialValueType srcType, MaterialValueType dstType)
{
    MaterialCastFlags castFlags = MaterialCastFlags::ReplicateScalar;
    if (dstType == MCT_Float || dstType == MCT_Float1 || dstType == MCT_LWCScalar)
    {
        // CoerceValue allows truncating to scalar types only
        castFlags |= MaterialCastFlags::AllowTruncate;
    }
    return CastValue(code, srcType, dstType, castFlags);
}

std::string MaterialCompiler::CoerceParameter(int32_t index, MaterialValueType dstType)
{
    return CoerceValue(GetParameterCode(index), GetParameterType(index), dstType);
}

int32_t MaterialCompiler::ValidCast(int32_t codeIndex, MaterialValueType dstType)
{
    if (codeIndex == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    const MaterialCastFlags flags = MaterialCastFlags::ValidCast;
    MaterialValueType srcType = GetParameterType(codeIndex);

    if (srcType & dstType)
    {
        return codeIndex;
    }
    else if ((srcType & (MaterialValueType::MCT_Bool | MaterialValueType::MCT_StaticBool)) &&
             (dstType & (MaterialValueType::MCT_Bool | MaterialValueType::MCT_StaticBool)))
    {
        return codeIndex;
    }
    else if (IsFloatNumericType(srcType) && IsFloatNumericType(dstType))
    {
        std::string finiteCode = CastValue(GetParameterCode(codeIndex), srcType, dstType, flags);
        if (finiteCode.empty())
        {
            return CODE_CHUNK_INDEX_NONE;
        }
        else
        {
            return AddInlinedCodeChunk(dstType, "{}", finiteCode.c_str());
        }
    }
    else
    {
        return Errorf("Cannot cast from {} to {}.", HLSLTypeString(srcType), HLSLTypeString(dstType));
    }
}

int32_t MaterialCompiler::ForceCast(int32_t codeIndex, MaterialValueType dstType, MaterialCastFlags forceCastFlags)
{
    if (codeIndex == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    const MaterialCastFlags flags = MaterialCastFlags::ValidCast | MaterialCastFlags::AllowAppendZeroes | forceCastFlags;
    MaterialValueType srcType = GetParameterType(codeIndex);

    if (srcType & dstType)
    {
        return codeIndex;
    }
    else if (IsFloatNumericType(srcType) && IsFloatNumericType(dstType))
    {
        std::string finiteCode = CastValue(GetParameterCode(codeIndex), srcType, dstType, flags);
        return AddInlinedCodeChunk(dstType, "{}", finiteCode);
    }
    else
    {
        return Errorf("Cannot force a cast from {} to {}.", HLSLTypeString(srcType), HLSLTypeString(dstType));
    }
}

int32_t MaterialCompiler::BaseTypeCast(int32_t index, MaterialValueType dstType)
{
    assert(dstType == MCT_Float || dstType == MCT_UInt || dstType == MCT_SInt);

    if (index == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    MaterialValueType srcType = GetParameterType(index);
    if (srcType & dstType)
    {
        return index;
    }

    uint32_t validComponentCount = GetNumericComponentCount(srcType);
    MaterialValueType dstTypeActual = MakeMaterialValueType(dstType, validComponentCount);

    return AddInlinedCodeChunk(dstTypeActual, "(({}){})", HLSLTypeString(dstTypeActual), GetParameterCode(index));
}

MaterialValueType MaterialCompiler::GetParameterType(int32_t index) const
{
    return m_CurrentScopeChunks->at(index).m_Type;
}

bool MaterialCompiler::GetStaticBoolValue(int32_t boolIndex, bool& bSucceed)
{
    bSucceed = true;
    if (boolIndex == INDEX_NONE)
    {
        bSucceed = false;
        return false;
    }

    if (GetParameterType(boolIndex) != MCT_StaticBool)
    {
        Errorf("Failed to cast {} input to static bool type", HLSLTypeString(GetParameterType(boolIndex)));
        bSucceed = false;
        return false;
    }

    if (GetParameterCode(boolIndex).find("true") != std::string::npos)
    {
        return true;
    }

    return false;
}

std::string MaterialCompiler::GetParameterCode(int32_t index, const char* defaultCode)
{
    if (index == -1 && defaultCode)
    {
        return defaultCode;
    }

    const ShaderCodeChunk& codeChunk = m_CurrentScopeChunks->at(index);
    if (codeChunk.m_IsInlined)
    {
        return codeChunk.m_Definition;
    }
    else
    {
        return codeChunk.m_SymbolName;
    }
}

std::string MaterialCompiler::CreateSymbolName(const char* symbolNameHint)
{
    return std::string(symbolNameHint) + std::to_string(m_NextSymbolIndex++);
}

const char* MaterialCompiler::HLSLTypeString(MaterialValueType type) const
{
    switch (type)
    {
    case MCT_Float1:
        return "float";
    case MCT_Float2:
        return "float2";
    case MCT_Float3:
        return "float3";
    case MCT_Float4:
        return "float4";
    case MCT_Float:
        return "float";
    case MCT_Texture2D:
        return "Texture2D";
    case MCT_Texture2DNormal:
        return "Texture2DNormal";
    case MCT_TextureCube:
        return "TextureCube";
    case MCT_Texture2DArray:
        return "Texture2DArray";
    case MCT_TextureCubeArray:
        return "TextureCubeArray";
    case MCT_VolumeTexture:
        return "volumeTexture";
    case MCT_StaticBool:
        return "static bool";
    case MCT_Bool:
        return "bool";
    case MCT_MaterialAttributes:
        return "FMaterialAttributes";
    case MCT_TextureExternal:
        return "TextureExternal";
    case MCT_TextureVirtual:
        return "TextureVirtual";
    case MCT_VTPageTableResult:
        return "VTPageTableResult";
    case MCT_ShadingModel:
        return "uint";
    case MCT_UInt:
        return "uint";
    case MCT_UInt1:
        return "uint";
    case MCT_UInt2:
        return "uint2";
    case MCT_UInt3:
        return "uint3";
    case MCT_UInt4:
        return "uint4";
    case MCT_SInt:
        return "int";
    case MCT_SInt1:
        return "int";
    case MCT_SInt2:
        return "int2";
    case MCT_SInt3:
        return "int3";
    case MCT_SInt4:
        return "int4";
    case MCT_Strata:
        return "FStrataData";
    case MCT_LWCScalar:
        return "FLWCScalar";
    case MCT_LWCVector2:
        return "FLWCVector2";
    case MCT_LWCVector3:
        return "FLWCVector3";
    case MCT_LWCVector4:
        return "FLWCVector4";
    default:
        return "unknown";
    };
}

MaterialValueType MaterialCompiler::GetArithmeticResultType(MaterialValueType typeA, MaterialValueType typeB)
{
    if (!IsNumericType(typeA) || !IsNumericType(typeB))
    {
        Errorf("Attempting to perform arithmetic on non-numeric types: {} {}", HLSLTypeString(typeA), HLSLTypeString(typeB));
        return MCT_Unknown;
    }

    if (typeA == typeB)
    {
        return typeA;
    }
    else if (typeA & typeB)
    {
        if (typeA == MCT_Float)
        {
            return typeB;
        }
        else
        {
            assert(typeB == MCT_Float);
            return typeA;
        }
    }
    else if ((typeA & MCT_Float) && (typeB && MCT_Float))
    {
        if (typeA == MCT_Float1)
        {
            return typeB;
        }

        if (typeB == MCT_Float1)
        {
            return typeA;
        }
    }

    Errorf("Arithmetic between types {} and {} are undefined", HLSLTypeString(typeA), HLSLTypeString(typeB));
    return MCT_Unknown;
}

MaterialValueType MaterialCompiler::GetArithmeticResultType(int32_t a, int32_t b)
{
    MaterialValueType typeA = m_CurrentScopeChunks->at(a).m_Type;
    MaterialValueType typeB = m_CurrentScopeChunks->at(b).m_Type;

    return GetArithmeticResultType(typeA, typeB);
}

MaterialParameterType MaterialCompiler::ToParameterType(MaterialValueType valueType)
{
    switch (valueType)
    {
    case MaterialValueType::MCT_Float:
        return MaterialParameterType::Scalar;
    case MaterialValueType::MCT_Float4:
        return MaterialParameterType::Vector;
    case MaterialValueType::MCT_Texture2D:
    case MaterialValueType::MCT_Texture2DNormal:
        return MaterialParameterType::Texture2D;
    case MaterialValueType::MCT_TextureCube:
        return MaterialParameterType::TextureCube;
    case MaterialValueType::MCT_TextureVirtual:
    case MaterialValueType::MCT_TextureVirtualNormal:
        return MaterialParameterType::VirtualTexture;
    }

    Assert(false);
    return MaterialParameterType::None;
}

void MaterialCompiler::UseSceneTextureId(SceneTextureId SceneTextureId, bool bTextureLookup)
{
    m_SceneTextures[SceneTextureId] |= bTextureLookup;

    if (SceneTextureId == SceneTextureId::SceneColor && m_InputParams.defines.Domain != MaterialDomain::Surface && m_InputParams.defines.Domain != MaterialDomain::PostProcess)
    {
        Errorf(("SceneColor lookups are only available when MaterialDomain = Surface or PostProcess."));
    }
}

int MaterialCompiler::FindOrAddVtIndex(const std::string& vtParameterName)
{
    for (UInt32 i = 0; i < m_InputParams.VTs->size(); ++i)
    {
        if ((*m_InputParams.VTs)[i] == vtParameterName)
        {
            return i;
        }
    }
    if (m_InputParams.VTs->size() >= 4)
    {
        m_InputParams.overflowVTs->insert(vtParameterName);
        return -1;
    }
    m_InputParams.VTs->push_back(vtParameterName);
    return static_cast<int>(m_InputParams.VTs->size() - 1);
}

int MaterialCompiler::FindVtIndex(const std::string& vtParameterName)
{
    for (UInt32 i = 0; i < m_InputParams.VTs->size(); ++i)
    {
        if ((*m_InputParams.VTs)[i] == vtParameterName)
        {
            return i;
        }
    }
    return -1;
}

bool MaterialCompiler::HasVT() const
{
    return !m_InputParams.VTs->empty();
}

int32_t MaterialCompiler::Error(const char* text)
{
    auto& currentFunctionStack = m_FunctionStacks[m_ShaderFrequency];

    if (currentFunctionStack.size() > 1)
    {
        // we are inside a function
        auto* errorFunction = currentFunctionStack[1]->m_FunctionCall;
        errorFunction->m_ErrorMessage = text;
    }
    else if (currentFunctionStack.back()->m_ExpressionStack.size() > 0)
    {
        MaterialExpression* errorExpression = currentFunctionStack.back()->m_ExpressionStack.back().expression;
        assert(errorExpression);

        if (errorExpression->m_ErrorMessage.empty())
        {
            errorExpression->m_ErrorMessage = text;
        }
    }

    if (m_InputParams.addErrorMessage)
    {
        m_InputParams.addErrorMessage(text);
    }
    

    return CODE_CHUNK_INDEX_NONE;
}

}
