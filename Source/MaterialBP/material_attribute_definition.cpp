#include "material_attribute_definition.h"

namespace cross 
{
std::map<EMaterialProperty, std::shared_ptr<MaterialAttribute>> MaterialAttributeMap::g_Property2AttributeMap;
MaterialAttributeMap g_MaterialAttributeMap;

std::shared_ptr<MaterialAttribute> MaterialAttributeMap::create(const MaterialAttribute& materialAttribute)
{
    if (g_Property2AttributeMap.find(materialAttribute.m_Attribute) != g_Property2AttributeMap.end())
    {
        throw std::invalid_argument("Duplicate attribute type");
    }
    auto instance = std::shared_ptr<MaterialAttribute>(new MaterialAttribute(materialAttribute));
    g_Property2AttributeMap[materialAttribute.m_Attribute] = instance;
    return instance;
}

MaterialAttributeMap::MaterialAttributeMap()
{
    MaterialAttributeMap::create(MaterialAttribute(EMaterialProperty::BaseColor, MaterialValueType::MCT_Float3, Float4(1.0f, 1.0f, 1.0f, 0.0f), ShaderFrequency::SF_Surface));
    MaterialAttributeMap::create(MaterialAttribute(EMaterialProperty::Metallic, MaterialValueType::MCT_Float1, Float4(0.0f, 0.0f, 0.0f, 0.0f), ShaderFrequency::SF_Surface));
    MaterialAttributeMap::create(MaterialAttribute(EMaterialProperty::Specular, MaterialValueType::MCT_Float1, Float4(0.5f, 0.0f, 0.0f, 0.0f), ShaderFrequency::SF_Surface));
    MaterialAttributeMap::create(MaterialAttribute(EMaterialProperty::Roughness, MaterialValueType::MCT_Float1, Float4(0.5f, 0.0f, 0.0f, 0.0f), ShaderFrequency::SF_Surface));
    MaterialAttributeMap::create(MaterialAttribute(EMaterialProperty::Opacity, MaterialValueType::MCT_Float1, Float4(1.0f, 0.0f, 0.0f, 0.0f), ShaderFrequency::SF_Surface));
    MaterialAttributeMap::create(MaterialAttribute(EMaterialProperty::OpacityMask, MaterialValueType::MCT_Float1, Float4(1.0f, 0.0f, 0.0f, 0.0f), ShaderFrequency::SF_Surface));
    MaterialAttributeMap::create(MaterialAttribute(EMaterialProperty::Normal, MaterialValueType::MCT_Float3, Float4(0.0f, 0.0f, 1.0f, 0.0f), ShaderFrequency::SF_Surface));
    MaterialAttributeMap::create(MaterialAttribute(EMaterialProperty::AmbientOcclusion, MaterialValueType::MCT_Float1, Float4(1.0f, 0.0f, 0.0f, 0.0f), ShaderFrequency::SF_Surface));
    MaterialAttributeMap::create(MaterialAttribute(EMaterialProperty::EmissiveColor, MaterialValueType::MCT_Float3, Float4(0.0f, 0.0f, 0.0f, 0.0f), ShaderFrequency::SF_Surface));
    MaterialAttributeMap::create(MaterialAttribute(EMaterialProperty::SubsurfaceColor, MaterialValueType::MCT_Float3, Float4(1.0f, 1.0f, 1.0f, 0.0f), ShaderFrequency::SF_Surface));
    // Not supported attributes
    MaterialAttributeMap::create(MaterialAttribute(EMaterialProperty::WorldPositionOffset, MaterialValueType::MCT_Float3, Float4(0.0f, 0.0f, 0.0f, 0.0f), ShaderFrequency::SF_WorldPositionOffset));
    MaterialAttributeMap::create(MaterialAttribute(EMaterialProperty::ShadingModel, MaterialValueType::MCT_Float1, Float4(0.0f, 0.0f, 0.0f, 0.0f), ShaderFrequency::SF_Surface));
    MaterialAttributeMap::create(MaterialAttribute(EMaterialProperty::Anisotropy, MaterialValueType::MCT_Float1, Float4(0.0f, 0.0f, 0.0f, 0.0f), ShaderFrequency::SF_Surface));
    MaterialAttributeMap::create(MaterialAttribute(EMaterialProperty::Tangent, MaterialValueType::MCT_Float3, Float4(1.0f, 0.0f, 0.0f, 0.0f), ShaderFrequency::SF_Surface));
    MaterialAttributeMap::create(MaterialAttribute(EMaterialProperty::ClearCoat, MaterialValueType::MCT_Float1, Float4(1.0f, 0.0f, 0.0f, 0.0f), ShaderFrequency::SF_Surface));
    MaterialAttributeMap::create(MaterialAttribute(EMaterialProperty::ClearCoatRoughness, MaterialValueType::MCT_Float1, Float4(1.0f, 0.0f, 0.0f, 0.0f), ShaderFrequency::SF_Surface));
    MaterialAttributeMap::create(MaterialAttribute(EMaterialProperty::PixelDepthOffset, MaterialValueType::MCT_Float1, Float4(0.0f, 0.0f, 0.0f, 0.0f), ShaderFrequency::SF_Surface));
    MaterialAttributeMap::create(MaterialAttribute(EMaterialProperty::Refraction, MaterialValueType::MCT_Float3, Float4(1.0f, 0.0f, 0.0f, 0.0f), ShaderFrequency::SF_Surface));
    MaterialAttributeMap::create(MaterialAttribute(EMaterialProperty::Displacement, MaterialValueType::MCT_Float1, Float4(0.5f, 0.0f, 0.0f, 0.0f), ShaderFrequency::SF_Surface));

}

std::shared_ptr<MaterialAttribute> MaterialAttributeMap::GetMaterialAttributeDefinition(EMaterialProperty attribute)
{
    auto it = g_Property2AttributeMap.find(attribute);
    if (it != g_Property2AttributeMap.end())
    {
        return it->second;
    }
    return nullptr;
}

bool MaterialAttributeMap::IsSupportedAttribute(EMaterialProperty attribute)
{
    return int(attribute) <= SUPPORTED_MATERIAL_ATTRIBUTES;
}

std::string MaterialAttributeMap::GetMaterialAttributeName(EMaterialProperty attribute)
{
    std::string name = attribute == EMaterialProperty::None ? "" : std::string(magic_enum::enum_name(attribute));
    if (!IsSupportedAttribute(attribute))
    {
        name += "(Unsupported)";
    }
    return name;
}
} // namespace cross