#pragma once

#include "material_expression_parameter.h"

namespace cross {
class CEMeta(Cli, Reflect) Material_API MaterialExpressionTextureParameter : public MaterialExpressionParameter
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetMenuName() const override
    {
        return "TextureParam";
    }

    virtual void DoHandlePropertyChange(IMaterialEditor * editor) override;

    bool IsVirtualTexture() const
    {
        return m_TextureType == MCT_TextureVirtual || m_TextureType == MaterialValueType::MCT_TextureVirtualNormal;
    }

    bool IsTypeMaching(const MaterialParameter* param) const override { return dynamic_cast<const MaterialParameterTexture*>(param); }

    std::shared_ptr<MaterialParameter> ToParameter() const override
    {
        return gbf::reflection::make_shared_with_rtti<MaterialParameterTexture>();
    }

    void CopyFrom(const MaterialParameter* src) override
    {
        MaterialExpressionParameter::CopyFrom(src);
        auto _src = dynamic_cast<const MaterialParameterTexture*>(src);
        m_TextureString = _src->Value;
    }

    void CopyTo(MaterialParameter* dst) const override
    {
        MaterialExpressionParameter::CopyTo(dst);
        auto _dst = dynamic_cast<MaterialParameterTexture*>(dst);
        _dst->Value = m_TextureString;
    }

    CEProperty(Reflect, EditorPropertyInfo(bHide = true))
    MaterialValueType m_TextureType = MCT_Texture2D;

    CEMeta(Reflect)
    ExpressionOutput m_Result;

    CEProperty(Reflect,
               EditorPropertyInfo(
                   PropertyType = "StringAsResource", FileTypeDescriptor = "Texture Assets#nda", ObjectClassID1 = ClassIDType.CLASS_Texture, ObjectClassID2 = ClassIDType.CLASS_TextureVirtual))
    std::string m_TextureString = gResourceMgr.GetDefaultTexturePath();

    //CEProperty(Reflect)
    //std::string m_VirtualTextureLayer = "";
};
}   // namespace cross