#pragma once

#include "material_expression_shader_const.h"

namespace cross {
class CEMeta(Cli) Material_API MaterialExpressionShaderConstFloat : public MaterialExpressionShaderConst
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetMenuName() const override
    {
        return "ShaderConst Float";
    }

public:
    CEProperty(Reflect)
    float m_Value = 0.0f;
};
}   // namespace cross