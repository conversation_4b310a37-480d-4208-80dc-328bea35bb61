#pragma once

#include "material_expression.h"
#include "material_expression_parameter.h"

namespace cross {
class CEMeta(Cli) MaterialExpressionTextureSampleParameter : public MaterialExpressionParameter
{
public:
    CE_Virtual_Serialize_Deserialize;

    std::string GetMenuName() const
    {
        return "TextureSampleParam";
    }

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    bool IsVirtualTexture() const
    {
        return m_TextureType == MCT_TextureVirtual || m_TextureType == MaterialValueType::MCT_TextureVirtualNormal;
    }

    virtual void DoHandlePropertyChange(IMaterialEditor * editor) override;

    bool IsTypeMaching(const MaterialParameter* param) const override { return dynamic_cast<const MaterialParameterTexture*>(param); }

    std::shared_ptr<MaterialParameter> ToParameter() const override
    {
        return gbf::reflection::make_shared_with_rtti<MaterialParameterTexture>();
    }

    void CopyFrom(const MaterialParameter* src) override
    {
        MaterialExpressionParameter::CopyFrom(src);
        auto _src = dynamic_cast<const MaterialParameterTexture*>(src);
        m_TextureString = _src->Value;
        //m_VirtualTextureLayer = _src->VirtualTextureLayer;
    }

    void CopyTo(MaterialParameter* dst) const override
    {
        MaterialExpressionParameter::CopyTo(dst);
        auto _dst = dynamic_cast<MaterialParameterTexture*>(dst);
        _dst->Value = m_TextureString;
        //_dst->VirtualTextureLayer = m_VirtualTextureLayer;
    }

public:
    CEProperty(Reflect)
    ExpressionInput m_UV;

    CEProperty(Reflect)
    ExpressionInput m_Level;

    CEProperty(Reflect)
    ExpressionInput m_Bias;

    CEProperty(Reflect)
    ExpressionInput m_DDX;

    CEProperty(Reflect)
    ExpressionInput m_DDY;

     CEProperty(Reflect,
               EditorPropertyInfo(
                   PropertyType = "StringAsResource", FileTypeDescriptor = "Texture Assets#nda", ObjectClassID1 = ClassIDType.CLASS_Texture, ObjectClassID2 = ClassIDType.CLASS_TextureUDIM, ObjectClassID3 = ClassIDType.CLASS_Texture2DArray))
    std::string m_TextureString = gResourceMgr.GetDefaultTexturePath();

    CEProperty(Reflect, EditorPropertyInfo(PropertyType = "Struct"))
    SamplerState m_SamplerState;

    CEProperty(Reflect, EditorPropertyInfo(bHide = true))
    MaterialValueType m_TextureType = MCT_Texture2D;

    CEMeta(Reflect, meta(RedirectTo = m_RGBA, ColorMask = RGB))
    ExpressionOutput m_RGB;

    CEMeta(Reflect, meta(RedirectTo = m_RGBA, ColorMask = R))
    ExpressionOutput m_R;

    CEMeta(Reflect, meta(RedirectTo = m_RGBA, ColorMask = G))
    ExpressionOutput m_G;

    CEMeta(Reflect, meta(RedirectTo = m_RGBA, ColorMask = B))
    ExpressionOutput m_B;

    CEMeta(Reflect, meta(RedirectTo = m_RGBA, ColorMask = A))
    ExpressionOutput m_A;

    CEMeta(Reflect)
    ExpressionOutput m_RGBA;
};
}   // namespace cross