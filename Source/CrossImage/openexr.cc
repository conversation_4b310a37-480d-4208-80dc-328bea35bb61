
#include "image.h"

#if _WINDOWS

#define IMATH_HALF_NO_LOOKUP_TABLE

#include <openexr.h>
#include <OpenEXRConfig.h>
#include <ImfInputFile.h>
#include <ImfChannelList.h>
#include <ImfStringAttribute.h>
#include <ImfMatrixAttribute.h>
#include <ImfArray.h>
#include <ImfRgbaFile.h>
#include <half.h>
#include <ImathBox.h>

#include <cfloat>
#include <iostream>
#include <limits>
#include <vector>

#include "cmft/image.h"

namespace imageio {
    using namespace Imf_3_1;
    using namespace Imath_3_1;

    bool load_exr(const char* pFilename, cmft::Image& img, bool isSRGB)
    {
        RgbaInputFile file(pFilename);

        Array2D<Rgba> pixels;

        Box2i dw = file.header().dataWindow();
        int width = dw.max.x - dw.min.x + 1;
        int height = dw.max.y - dw.min.y + 1;
        pixels.resizeErase(height, width);

        file.setFrameBuffer(&pixels[0][0] - dw.min.x - dw.min.y * width, 1, width);
        file.readPixels(dw.min.y, dw.max.y);

        cmft::imageCreate(img, width, height);

        const int stride = 4;
        for (int y = 0; y < height; ++y)
        {
            for (int x = 0; x < width; ++x)
            {
                const Rgba  &inPixel = pixels[y][x];
                float* outPixel = static_cast<float*>(img.m_data) + (y * width + x) * stride;
                outPixel[0] = isSRGB ? pow(inPixel.r, 1 / 2.2f) : static_cast<float>(inPixel.r);
                outPixel[1] = isSRGB ? pow(inPixel.g, 1 / 2.2f) : static_cast<float>(inPixel.g);
                outPixel[2] = isSRGB ? pow(inPixel.b, 1 / 2.2f) : static_cast<float>(inPixel.b);
                outPixel[3] = isSRGB ? pow(inPixel.a, 1 / 2.2f) : static_cast<float>(inPixel.a);
            }
        }

		return true;
	}

    void save_exr(const char* pFilename, int width, int height, void* float4Pixels, bool isSRGB)
    {
        int count = width * height;
        std::vector<Rgba> half4Pixels;
        half4Pixels.resize(count);
        const int stride = 4;
        for (int y = 0; y < height; ++y)
        {
            for (int x = 0; x < width; ++x)
            {
                Rgba& half4Pixel = half4Pixels[y * width + x];
                float* float4Pixel = static_cast<float*>(float4Pixels) + (y * width + x) * stride;
                float r = float4Pixel[0];
                float g = float4Pixel[1];
                float b = float4Pixel[2];
                float a = float4Pixel[3];
                half4Pixel.r = half(isSRGB ? pow(r, 2.2f) : r);
                half4Pixel.g = half(isSRGB ? pow(g, 2.2f) : g);
                half4Pixel.b = half(isSRGB ? pow(b, 2.2f) : b);
                half4Pixel.a = half(isSRGB ? pow(a, 2.2f) : a);
            }
        }

        RgbaOutputFile file(pFilename, width, height, WRITE_RGBA);
        file.setFrameBuffer(reinterpret_cast<const Rgba*>(half4Pixels.data()), 1, width);
        file.writePixels(height);
    }
}

#endif
