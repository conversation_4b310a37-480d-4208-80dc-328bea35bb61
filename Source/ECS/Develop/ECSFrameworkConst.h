#pragma once
#include <stdint.h>
#include "CrossBase/BitWise.h"

namespace cross::ecs
{
class ECSConst
{
public:
	static const uint32_t sMaxComponentCount{ 256 };
	static const uint32_t sMaxComponentPerEntity{ 256 };
	static const uint32_t sBuildInGameComponentCount{ 2 };
	static const uint32_t sBuildInRenderComponentCount{ 1 };
};

class ComponentBitMask : public TBitMask<ECSConst::sMaxComponentCount>
{
public:

	ComponentBitMask() = default;

	ComponentBitMask(const TBitMask<ECSConst::sMaxComponentCount>& other)
		:TBitMask<ECSConst::sMaxComponentCount>(other)
	{
	}

    ComponentBitMask(UInt32 startBit, UInt32 endBit)
        :TBitMask<ECSConst::sMaxComponentCount>(startBit, endBit)
    {
    }

    ComponentBitMask(UInt64 v0, UInt64 v1, UInt64 v2, UInt64 v3)
    {
        static_assert(ECSConst::sMaxComponentCount == 256);
        mStorage[0] = v0;
        mStorage[1] = v1;
        mStorage[2] = v2;
        mStorage[3] = v3;
    }

	uint64_t CalculateHash() const
	{
		return HashFunction::HashString64((const char*)GetData(), GetSizeInByte());
	}

	struct BitIndexIterator
	{
	public:
		inline bool IsValid()const { return mBlockIndex != -1; }
		inline SInt32 GetBitIndex()const { return mBlockIndex * ComponentBitMask::BitsPerBlock + mBitIndex; }

		bool operator == (BitIndexIterator const& other) const
		{
			return (mBlockIndex == other.mBlockIndex)
				&& (mBitIndex == other.mBitIndex)
				&& (mSrcMask == other.mSrcMask);
		}

		bool operator != (BitIndexIterator const& other) const { return !(*this == other); }

		BitIndexIterator operator ++(int)
		{
			mBlockIndex = (mBitIndex < ComponentBitMask::BitsPerBlock - 1) ? mBlockIndex : (mBlockIndex + 1);
			for (size_t i = mBlockIndex; i < ComponentBitMask::blocknumber; i++)
			{
                UInt64 one64 = static_cast<UInt64>(1);
                UInt64 clearMask = (one64 << (mBitIndex + one64)) - one64;
				clearMask = ~clearMask;
				UInt64 mask = mSrcMask->mStorage[i] & clearMask;
				if (mask > 0)
				{
					mBlockIndex = (SInt16)i;
					mBitIndex = (UInt8)BitScanForw(mask);
					return *this;
				}
				else
				{
					i++;
				}
			}
			mSrcMask = nullptr;
			mBlockIndex = -1;
			mBitIndex = 0;

			return *this;
		}

	protected:
		BitIndexIterator() = default;

		BitIndexIterator(const ComponentBitMask* mask, SInt16 blockIndex, UInt8 bitIndex)
			:mSrcMask(mask), mBlockIndex(blockIndex), mBitIndex(bitIndex) {}

	private:
		const ComponentBitMask* mSrcMask{ nullptr };
		SInt16 mBlockIndex{ -1 };
		UInt8 mBitIndex{ 0 };

		friend class ComponentBitMask;
	};

	BitIndexIterator GetBitIndexIteratorBegin() const
	{
		for (size_t i = 0; i < blocknumber; i++)
		{
			if (mStorage[i] != 0)
			{
				auto idx = BitScanForw(mStorage[i]);
				Assert(idx >= 0);
				return BitIndexIterator(this, (SInt16)i, (UInt8)idx);
			}
		}
		return BitIndexIterator();
	}

	BitIndexIterator GetBitIndexIteratorEnd() const
	{
		return BitIndexIterator();
	}
};

inline ComponentBitMask operator ~(const ComponentBitMask& mask) 
{ 
	return (ComponentBitMask)(~(static_cast<TBitMask<ECSConst::sMaxComponentCount>>(mask)));
}

inline ComponentBitMask operator &(const ComponentBitMask& mask0, const ComponentBitMask& mask1)
{
	return (ComponentBitMask)((static_cast<TBitMask<ECSConst::sMaxComponentCount>>(mask0)) 
		& (static_cast<TBitMask<ECSConst::sMaxComponentCount>>(mask1)));
}

}
