#pragma once
#include "ECS/Develop/Framework/Types.h"
#include "ECS/ECSSFINAE.h"

// prototype registry
namespace cross::ecs
{
	class Framework;
    class PrototypeRegistry final
    {
    public:
        ECS_API explicit PrototypeRegistry();

        ~PrototypeRegistry() = default;

        PrototypeRegistry(PrototypeRegistry const&) = delete;

        PrototypeRegistry& operator=(PrototypeRegistry) = delete;

        PrototypeRegistry(PrototypeRegistry&&) = default;

        PrototypeRegistry& operator=(PrototypeRegistry&&) = default;

    public:
        // initialize
        ECS_API void Init(ComponentDescMap& componentsRegistry, Framework& frameworkInstance);

        // create new prototypes
        ECS_API PrototypePtr GetOrCreatePrototype(const ComponentDesc** types,  size_t count, bool bIsRenderStore = false);

        template <typename ... Types, typename = TEnableIfAreAllDifferentComponents<AtLeastOne, Types...>>
        PrototypePtr GetOrCreatePrototype(bool isGameComponent);

        ECS_API PrototypePtr GetOrCreatePrototypeImp(ComponentBitMask& mask, bool isGameType);

        ECS_API PrototypePtr MergePrototype(std::initializer_list<const PrototypePtr>& prototypes);

        ECS_API PrototypePtr CalculatePrototypeSub(PrototypePtr const& srcType, PrototypePtr const& subType);

        template <typename ... Types, typename = TEnableIfAreAllDifferentComponents<AtLeastOne, Types...>>
        auto CalculatePrototypeAdd(PrototypePtr const& prototype);

        template <typename ... Types, typename = TEnableIfAreAllDifferentComponents<AtLeastOne, Types...>>
        PrototypePtr CalculatePrototypeSub(PrototypePtr const& prototype);

        ECS_API PrototypePtr GetEmptyPrototype(bool bIsRenderStore = false)
        {
            if (bIsRenderStore)
                return mEmptyPrototypeR;
            else
                return mEmptyPrototypeG;
        }
        // prototype interface
        ECS_API static bool PrototypeHasComponent(PrototypePtr const& prototype, const ComponentDesc* typeInfo);

        ECS_API static auto ProtyotypeGetComponentInfo(PrototypePtr const& prototype, const ComponentDesc* typeInfo) -> ComponentInfo*;

        template <typename Component, typename = std::enable_if_t<TIsDataComponentV<Component>>>
        static bool PrototypeHasComponent(PrototypePtr const& prototype);

        template <typename Component, typename = std::enable_if_t<TIsDataComponentV<Component>>>
        static auto ProtyotypeGetComponentInfo(PrototypePtr const& prototype) -> ComponentInfo*;

    private: // prototype implementation details
        static void InitPrototypeCapacity(PrototypePtr prototype);

        static UInt32 ChunkSizeWithCapacity(PrototypePtr prototype, UInt32 capacity, std::vector<UInt32>& result);


    private:
        PrototypePtr mEmptyPrototypeG{nullptr};
        PrototypePtr mEmptyPrototypeR{nullptr};
        Framework* mFramework{ nullptr };
        SInt32 mIdComponentBitIndex{ -1 };
        SInt32 mMetaComponentBitIndex{ -1 };
        std::unordered_map<UInt64, PrototypePtr> mGameTypeMap;
        std::unordered_map<UInt64, PrototypePtr> mRenderTypeMap;
        std::unique_ptr<std::mutex>  mMutex;
    };

    namespace detail 
    {
        template <typename T, size_t ... Indices>
        constexpr auto CreateArrayWithIndexSequence(std::index_sequence<Indices...>, std::enable_if_t<
            // check tow things
            // 1. T is a type of integer
            // 2. Indices shall not overflow
            std::conjunction_v<TIsStrictIntegral<T>, std::bool_constant<(sizeof...(Indices)<(std::numeric_limits<T>::max)())>>, int> = 0) noexcept -> std::array<T, sizeof...(Indices)>
        {
            return { Indices... };
        }
    }

    template <typename ... Types, typename>
    inline PrototypePtr PrototypeRegistry::GetOrCreatePrototype(bool isGameComponent)
    {
        constexpr size_t count = sizeof...(Types);
        std::array<const ComponentDesc*, count> types = { Types::GetDesc()... };
        ComponentBitMask mask;
        for (size_t i = 0; i < count; i++)
        {
            mask.Set(types[i]->GetMaskBitIndex(), true);
            Assert(types[i]->IsGameComponent() == isGameComponent || types[i]->IsBuildInComponent());
        }
        return GetOrCreatePrototypeImp(mask, isGameComponent);
    }

    template <typename ... Types, typename>
    inline auto PrototypeRegistry::CalculatePrototypeAdd(PrototypePtr const& prototype)
    {
        constexpr UInt32 typesCount = sizeof...(Types);
        std::array<const ComponentDesc*, typesCount> typesInfo = { Types::GetDesc()... };

        ComponentBitMask mask = prototype->ComponentMask;
		for (UInt32 i = 0; i < typesCount; i++)
		{
			auto* desc = typesInfo[i];
			mask.Set(desc->GetMaskBitIndex(), true);
		}

		if (mask == prototype->ComponentMask)
			return prototype;

        return GetOrCreatePrototypeImp(mask, prototype->IsGameType);
    }

    template <typename ... Types, typename>
    inline PrototypePtr PrototypeRegistry::CalculatePrototypeSub(PrototypePtr const& prototype)
    {
        constexpr UInt32 Count = sizeof...(Types);
        std::array<ComponentDesc*, Count> typesInfo = { Types::GetDesc()... };
        auto newPrototype = CalculatePrototypeSub(prototype, typesInfo.data(), Count);
        return newPrototype;
    }

    template <typename Component, typename>
    inline bool PrototypeRegistry::PrototypeHasComponent(PrototypePtr const& prototype)
    {
        return PrototypeHasComponent(prototype, Component::GetDesc());
    }

    template <typename Component, typename>
    inline auto PrototypeRegistry::ProtyotypeGetComponentInfo(PrototypePtr const& prototype) -> ComponentInfo*
    {
        return ProtyotypeGetComponentInfo(prototype, Component::GetDesc());
    }

}