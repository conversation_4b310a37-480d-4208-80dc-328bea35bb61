#pragma once
#include "ECS/Develop/Framework/Types.h"

// Prototype interface
namespace cross::ecs
{
    template<typename T>
    using TIsComponent = std::is_base_of<IComponent, T>;
    template <typename T>
    using TIsDataComponent = std::is_base_of<IComponent, T>;
     template <typename T>
    using TIsProxyComponent = std::is_base_of<ProxyComponent, T>;
	template <typename T>
    using ComponentIsTag = std::conjunction<TIsComponent<T>, std::is_empty<T>>;

    template <typename T>
    constexpr bool TIsDataComponentV = TIsDataComponent<T>::value;
    template<typename T>
    constexpr bool TIsProxyComponentV = TIsProxyComponent<T>::value;
	template <typename T>
	using TIsPrototype = std::is_same<PrototypePtr, T>;
	template <typename T>
	constexpr bool TIsPrototypeV = TIsPrototype<T>::value;

    using AtLeastOne = std::integral_constant<size_t, 1>;
    using AtLeastTwo = std::integral_constant<size_t, 2>;

    template <typename AtLeatCount, typename ... Comps>
    using TEnableIfAreAllDataComponents = std::enable_if_t<std::conjunction_v<
        std::bool_constant<(sizeof...(Comps) >= AtLeatCount::value)>,               // at least one LowerBound
        TIsDataComponent<TRemoveRCVT<Comps>>...                                     // all inherit from IDataComponent
    >>;

    template <typename AtLeastCount, typename ... Comps>
    using TEnableIfAreAllDifferentComponents = std::enable_if_t<std::conjunction_v<
        std::bool_constant<(sizeof...(Comps) >= AtLeastCount::value)>,               // at least one component
		TIsComponent<TRemoveRCVT<Comps>>...,                                    // all inherit from IDataComponent
        std::negation<TTupleHashDuplicate<TRemoveRCVT<Comps>...>>                   // no duplicate types
    >>;

	template <typename AtLeastCount, typename ...Protos>
	using TEnableIfAreAllPrototypes = std::enable_if_t<std::conjunction_v<
		std::bool_constant<(sizeof...(Protos) >= AtLeastCount::value)>,               // at least one component
		TIsPrototype<TRemoveRCVT<Protos>>...                                 // all inherit from IDataComponent
		>>;
}