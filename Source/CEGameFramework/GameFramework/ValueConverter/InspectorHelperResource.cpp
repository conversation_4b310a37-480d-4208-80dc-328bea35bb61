#include "InspectorHelperResource.h"
#include "ResourceClassRegisterFactory.h"
#include "Converter.h"
#include "Resource/Resource.h"
#include "Resource/ResourceManager.h"
#include "Resource/BaseClasses/RegisterClassesForward.h"
#include "Resource/Texture/TextureCube.h"
#include "Resource/Texture/Texture2DArray.h"
#include "Resource/Texture/TextureUDIM.h"
#include "Resource/Animation/Animator/AnimatorResource.h"
#include "Resource/Animation/Sequence/AnimSequenceRes.h"
#include "Resource/Animation/Curve/CurveControllerRes.h"
#include "Resource/Animation/Skeleton/SkeletonResource.h"
#include "Resource/ParticleSystem/ParticleSystemResource.h"
#include "Resource/ParticleSystem/ParticleEmitterResource.h"
#include "Resource/TerrainResource.h"
#include "Resource/FontResource.h"
#include "Resource/RenderTextureResource.h"
#include "Resource/RuntimePCG/PCGResource.h"
#include "Resource/AirportResource.h"
#include "Resource/MaterialParameterCollection.h"
#include "Resource/MaterialFunction.h"
#include "Resource/BinaryResource.h"
#include "Resource/InstanceDataResource.h"
#include "Resource/WorkflowGraphResource.h"
#include "Resource/InputActionMappingResource.h"
#include "Resource/DataAssetResource.h"
#include "Resource/TsResource.h"
#include "Runtime/GameWorld/Prefab/PrefabManager.h"
#include "Runtime/GameWorld/WorldPartition/WorldPartitionBlockResource.h"
#include "Runtime/Animation/Skeleton/SkeletonPhysicsResource.h"
#include "Runtime/Animation/MotionMatch/MMBase/MotionDataAsset.h"
#include "GamePlayBaseFramework/meta/reflection/builder/class_builder.hpp"

namespace cegf{

// class gbf::logic::VarInspectorHelper_cross__WorkflowGraphResourceRef

    static std::string __ReplacePtrToRef(const std::string& in)
    {
        std::string str = in;
        std::string_view from = "Ptr";
        std::string_view to = "Ref";

        if (str.size() >= from.size() && str.compare(str.size() - from.size(), from.size(), from) == 0)
        {
            str.replace(str.size() - from.size(), from.size(), to);
        }
        return str;
    }



#define REGISTER_RES_RUNTIME_CLASS(res) \
    {                                                                                                                                                                                                                                         \
        __register_cxx_type<res>(#res).constructor();                                                                                                                                                                                   \
        RegisterResourceRefClass((#res), gbf::reflection::query_meta_class<res>());\
        gbf::machine::VValueConverterFactory::Register(std::make_unique<cegf::VValueConverterImpl<res>>());                                                                                                                \
                                                                                                                                    \
    }

void ResourceClassRegisterFactory::RegisterResourceRuntimeClass()
{
    //REGISTER_RES_RUNTIME_CLASS(cross::anim::AnimResourceBasePtr)
    //REGISTER_RES_RUNTIME_CLASS(cross::anim::AnimCmpResPtr)
    //REGISTER_RES_RUNTIME_CLASS(cross::anim::AnimatrixResPtr)
    //REGISTER_RES_RUNTIME_CLASS(cross::anim::AnimSeqResPtr)
    REGISTER_RES_RUNTIME_CLASS(cross::anim::AnimatorResPtr)
    //REGISTER_RES_RUNTIME_CLASS(cross::anim::AnimBlendSpaceResPtr)
    //REGISTER_RES_RUNTIME_CLASS(cross::anim::MotionDataAssetPtr)

    //REGISTER_RES_RUNTIME_CLASS(cross::fx::ParticleSystemResPtr)
    //REGISTER_RES_RUNTIME_CLASS(cross::fx::ParticleEmitterResPtr)

    //REGISTER_RES_RUNTIME_CLASS(cross::ScriptResourcePtr)
    //REGISTER_RES_RUNTIME_CLASS(cross::ScriptPtr)
    //REGISTER_RES_RUNTIME_CLASS(cross::TsPtr)
    REGISTER_RES_RUNTIME_CLASS(cross::TexturePtr)
    REGISTER_RES_RUNTIME_CLASS(cross::ShaderPtr)
    REGISTER_RES_RUNTIME_CLASS(cross::ComputeShaderPtr)
    REGISTER_RES_RUNTIME_CLASS(cross::MaterialPtr )
    //REGISTER_RES_RUNTIME_CLASS(cross::MaterialInterfacePtr)
    //REGISTER_RES_RUNTIME_CLASS(cross::MaterialFunctionPtr)
    //REGISTER_RES_RUNTIME_CLASS(cross::MPCPtr)
    REGISTER_RES_RUNTIME_CLASS(cross::Texture2DPtr)
    REGISTER_RES_RUNTIME_CLASS(cross::TextureVirtualPtr)
    REGISTER_RES_RUNTIME_CLASS(cross::TextureCubePtr)
    //REGISTER_RES_RUNTIME_CLASS(cross::Texture3DPtr)
    //REGISTER_RES_RUNTIME_CLASS(cross::TextureUDIMPtr)
    //REGISTER_RES_RUNTIME_CLASS(cross::Texture2DArrayPtr)
    REGISTER_RES_RUNTIME_CLASS(cross::PrefabResourcePtr )
    //REGISTER_RES_RUNTIME_CLASS(cross::WorldBlockPtr )
    //REGISTER_RES_RUNTIME_CLASS(cross::ParticlePtr)
    //REGISTER_RES_RUNTIME_CLASS(cross::EmitterPtr)
    //REGISTER_RES_RUNTIME_CLASS(cross::ParticleSystemPtr)
    //REGISTER_RES_RUNTIME_CLASS(cross::SfxPtr)
    //REGISTER_RES_RUNTIME_CLASS(cross::StaticMeshPtr)
    //REGISTER_RES_RUNTIME_CLASS(cross::TrailEmitterPtr)
    //REGISTER_RES_RUNTIME_CLASS(cross::ParticlesPtr)
    //REGISTER_RES_RUNTIME_CLASS(cross::AudioAssetPtr)
    //REGISTER_RES_RUNTIME_CLASS(cross::AssetPtr)
    //REGISTER_RES_RUNTIME_CLASS(cross::ResourcePtr)
	//REGISTER_RES_RUNTIME_CLASS(cross::RenderSyncResourcePtr)
    //REGISTER_RES_RUNTIME_CLASS(cross::FxPtr )
    REGISTER_RES_RUNTIME_CLASS(cross::MeshAssetDataResourcePtr)
    //REGISTER_RES_RUNTIME_CLASS(cross::PCGResourcePtr)
    //REGISTER_RES_RUNTIME_CLASS(cross::PCGraphResourcePtr)
    REGISTER_RES_RUNTIME_CLASS(cross::CurveControllerResPtr)
    //REGISTER_RES_RUNTIME_CLASS(cross::TerrainResourcePtr )
    REGISTER_RES_RUNTIME_CLASS(cross::FontResourcePtr)
    //REGISTER_RES_RUNTIME_CLASS(cross::RenderTextureResourcePtr)
    //REGISTER_RES_RUNTIME_CLASS(cross::BinaryResourcePtr)
    //REGISTER_RES_RUNTIME_CLASS(cross::InstanceDataResourcePtr)
    REGISTER_RES_RUNTIME_CLASS(cross::WorkflowGraphResourcePtr)
    REGISTER_RES_RUNTIME_CLASS(cross::InputActionMappingResourcePtr)
    REGISTER_RES_RUNTIME_CLASS(cross::DataAssetResourcePtr)
}

void ResourceClassRegisterFactory::RegisterResourceInspectorHelperClass()
{
    
}

}  // namespace gbf::logic