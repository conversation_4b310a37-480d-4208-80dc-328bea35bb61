#include "GameFramework/Components/CharacterMovementComponent.h"
#include "GameFramework/GameObjects/Character.h"
//#include "CharacterMovementSystemG.h"

namespace cegf
{


CharacterMovementComponent::CharacterMovementComponent()
{
}

CharacterMovementComponent::~CharacterMovementComponent()
{
    mOwnerCharacter = nullptr;
}

void CharacterMovementComponent::GetRelatedECSComponentBitMask(cross::ecs::ComponentBitMask& bitMask) const
{
    MoveComponent::GetRelatedECSComponentBitMask(bitMask);
    bitMask.Set(cross::CharacterMovementComponentG::GetDesc()->GetMaskBitIndex(), true);
}

void CharacterMovementComponent::Jump()
{
    auto comp = GetECSComponent<cross::CharacterMovementComponentG>();
    auto moveSys = GetSystem<cross::CharacterMovementSystemG>();
    moveSys->Jump(comp.Write());
}

void CharacterMovementComponent::StopJumping() 
{
    auto comp = GetECSComponent<cross::CharacterMovementComponentG>();
    auto moveSys = GetSystem<cross::CharacterMovementSystemG>();
    moveSys->StopJumping(comp.Write());
}
cross::Float3 CharacterMovementComponent::GetCurrentVelocityInWorldSpace()const {
    auto comp = GetECSComponent<cross::CharacterMovementComponentG>();
	auto moveSys = GetSystem<cross::CharacterMovementSystemG>();
	return moveSys->GetCurrentVelocityInWorldSpace(comp.Read());
}
cross::Float3 CharacterMovementComponent::GetCurrentVelocityInRootSpace() const
{
    auto comp = GetECSComponent<cross::CharacterMovementComponentG>();
    auto moveSys = GetSystem<cross::CharacterMovementSystemG>();
    return moveSys->GetCurrentVelocityInRootSpace(comp.Read());
}
void CharacterMovementComponent::SetMaxWalkSpeed(float maxSpeed) {
    auto comp = GetECSComponent<cross::CharacterMovementComponentG>();
    auto moveSys = GetSystem<cross::CharacterMovementSystemG>();
    moveSys->SetMaxWalkSpeed(comp.Write(),maxSpeed);
}
cross::Float3A CharacterMovementComponent::GetAngleBetweenAimForwardAndCurrentVelocity() const
{
    auto comp = GetECSComponent<cross::CharacterMovementComponentG>();
    auto moveSys = GetSystem<cross::CharacterMovementSystemG>();
    return moveSys->GetAngleBetweenAimForwardAndCurrentVelocity(comp.Read());
}
void CharacterMovementComponent::SetAngleBetweenActorForwardAndCurrentVelocity(float yawOffset)
{
    auto comp = GetECSComponent<cross::CharacterMovementComponentG>();
    auto moveSys = GetSystem<cross::CharacterMovementSystemG>();
    moveSys->SetAngleBetweenActorForwardAndCurrentVelocity(comp.Write(),yawOffset);
}
void CharacterMovementComponent::Crouch() {
    auto comp = GetECSComponent<cross::CharacterMovementComponentG>();
    auto moveSys = GetSystem<cross::CharacterMovementSystemG>();
    moveSys->Crouch(comp.Write());
}
void CharacterMovementComponent::UnCrouch()
{
    auto comp = GetECSComponent<cross::CharacterMovementComponentG>();
    auto moveSys = GetSystem<cross::CharacterMovementSystemG>();
    moveSys->UnCrouch(comp.Write());
}
void CharacterMovementComponent::SetAimGameObject(GameObject* aim) {
    auto comp = GetECSComponent<cross::CharacterMovementComponentG>();
    auto moveSys = GetSystem<cross::CharacterMovementSystemG>();
    moveSys->SetAimEntity(comp.Write(), aim->GetObjectEntityID());
}

cross::Float3 CharacterMovementComponent::GetLastInputVector()
{
    auto comp = GetECSComponent<cross::CharacterMovementComponentG>();
    auto moveSys = GetSystem<cross::CharacterMovementSystemG>();
    return moveSys->GetLastInputVector(comp.Write());
}

void CharacterMovementComponent::SetRotationMode(int RotationMode)
{
    auto comp = GetECSComponent<cross::CharacterMovementComponentG>();
    auto moveSys = GetSystem<cross::CharacterMovementSystemG>();
    return moveSys->SetRotationMode(comp.Write(), RotationMode);
}
}
