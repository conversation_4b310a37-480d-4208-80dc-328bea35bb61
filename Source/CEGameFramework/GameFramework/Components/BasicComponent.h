#pragma once
#include "GameFramework/Components/Component.h"
#include "GameFramework/GameFrameworkGlobals.h"
#include "PhysicsEngine/PhysicsEngine.h"

namespace cegf
{
class GAMEFRAMEWORK_API CEMeta(Cli, Reflect) BasicComponent : public GameObjectComponent
{
public:
    CEMeta(Reflect, Cli) 
    BasicComponent();

    virtual ~BasicComponent();
    
    virtual void Init() override;

    virtual void GetRelatedECSComponentBitMask(cross::ecs::ComponentBitMask& bitMask) const override;

    const cross::BoundingBox& GetLocalAABB();

    const cross::BoundingBox& GetWorldAABB();

    void SetLocalAABB(const cross::BoundingBox& localAABB);

    void SetWorldAABB(const cross::BoundingBox& worldAABB);

    void AddForce(const cross::Float3A& force);

    void AddImpulse(const cross::Float3A& impulse);

    void AddLinearVelocity(const cross::Float3A& velocity);

    void AddLinearAcceleration(const cross::Float3A& acceleration);

    cross::CollisionType GetCollisionType() const;

    void SetCollisionType(cross::CollisionType type);

    cross::CollisionChannelBit GetCollisionChannel() const;

    void SetCollisionChannel(cross::CollisionChannelBit channel);
};

}
