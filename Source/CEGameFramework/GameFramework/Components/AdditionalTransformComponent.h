#pragma once
#include "GameFramework/Components/Component.h"

namespace cegf
{
class GAMEFRAMEWORK_API CEMeta(Reflect) AdditionalTransformComponent : public GameObjectComponent
{
public:
    CEMeta(Reflect) AdditionalTransformComponent() = default;

    virtual ~AdditionalTransformComponent() override = default;
    
    virtual void Init() override;
    
    virtual void GetRelatedECSComponentBitMask(cross::ecs::ComponentBitMask& bitMask) const override;

    void SetAdditionalLocation(cross::Float3 const& location);

    void SetAdditionalTransform(cross::TRS_A const& transform);
};

}
