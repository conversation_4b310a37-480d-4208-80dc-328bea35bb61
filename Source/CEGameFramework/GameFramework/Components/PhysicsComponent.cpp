#include "GameFramework/GameWorld.h"
#include "GameFramework/Components/PhysicsComponent.h"

namespace cegf {

void PhysicsComponent::Init()
{
    GameObjectComponent::Init();
}
void PhysicsComponent::StartGame()
{
    GameObjectComponent::StartGame();
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {       
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->RegisterCollisionCallback(comp.Write());
    }
}

void PhysicsComponent::GetRelatedECSComponentBitMask(cross::ecs::ComponentBitMask& bitMask) const
{
    GameObjectComponent::GetRelatedECSComponentBitMask(bitMask);
    bitMask.Set(cross::PhysicsComponentG::GetDesc()->GetMaskBitIndex(), true);
}

void PhysicsComponent::BindCollisionEvent(cross::CollisionEventType eventType, std::function<void(GameObject*)> eventCallback)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    auto comp = GetECSComponent<cross::PhysicsComponentG>();
    switch (eventType)
    {
    case cross::CollisionEventType::OnCollisionEnter:
    {
        mCollisionEnterCBs.push_back(std::move(eventCallback));
        if (mCollisionEnterCBs.size() == 1)
        {
            sys->RegisterCollisionEnterCallback(comp.Write(), std::bind(&PhysicsComponent::OnCollisionEnter, this, std::placeholders::_1));
        }
        break;
    }
    case cross::CollisionEventType::OnCollisionStay:
    {
        mCollisionStayCBs.push_back(std::move(eventCallback));
        if (mCollisionStayCBs.size() == 1)
        {
            sys->RegisterCollisionStayCallback(comp.Write(), std::bind(&PhysicsComponent::OnCollisionStay, this, std::placeholders::_1));
        }
        break;
    }
    case cross::CollisionEventType::OnCollisionExit:
    {
        mCollisionExitCBs.push_back(std::move(eventCallback));
        if (mCollisionExitCBs.size() == 1)
        {
            sys->RegisterCollisionExitCallback(comp.Write(), std::bind(&PhysicsComponent::OnCollisionExit, this, std::placeholders::_1));
        }
        break;
    }
    case cross::CollisionEventType::OnTriggerEnter:
    {
        mTriggerEnterCBs.push_back(std::move(eventCallback));
        if (mTriggerEnterCBs.size() == 1)
        {
            sys->RegisterTriggerEnterCallback(comp.Write(), std::bind(&PhysicsComponent::OnTriggerEnter, this, std::placeholders::_1));
        }
        break;
    }
    case cross::CollisionEventType::OnTriggerExit:
    {
        mTriggerExitCBs.push_back(std::move(eventCallback));
        if (mTriggerExitCBs.size() == 1)
        {
            sys->RegisterTriggerExitCallback(comp.Write(), std::bind(&PhysicsComponent::OnTriggerExit, this, std::placeholders::_1));
        }
        break;
    }
    default:
        break;
    }
}
void PhysicsComponent::OnCollisionEnter(cross::ecs::EntityID entity) {
    auto* go = GetWorld()->GetGameObject(entity);
    if (go != nullptr)
    {
        for (auto& cb : mCollisionEnterCBs)
        {
            cb(go);
        }
    }   
}
void PhysicsComponent::OnCollisionStay(cross::ecs::EntityID entity)
{
    auto* go = GetWorld()->GetGameObject(entity);
    if (go != nullptr)
    {
        for (auto& cb : mCollisionStayCBs)
        {
            cb(go);
        }
    }
}
void PhysicsComponent::OnCollisionExit(cross::ecs::EntityID entity) {
    auto* go = GetWorld()->GetGameObject(entity);
    if (go != nullptr)
    {
        for (auto& cb : mCollisionExitCBs)
        {
            cb(go);
        }
    }
}
void PhysicsComponent::OnTriggerEnter(cross::ecs::EntityID entity) {
    auto* go = GetWorld()->GetGameObject(entity);
    if (go != nullptr)
    {
        for (auto& cb : mTriggerEnterCBs)
        {
            cb(go);
        }
    }
}
void PhysicsComponent::OnTriggerExit(cross::ecs::EntityID entity) {
    auto* go = GetWorld()->GetGameObject(entity);
    if (go != nullptr)
    {
        for (auto& cb : mTriggerExitCBs)
        {
            cb(go);
        }
    }
}
void PhysicsComponent::OnShapeChanged()
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->OnShapeChanged(comp.Write());
    }
}

void PhysicsComponent::OnShapeChanged(UInt32 modelIndex, const cross::MeshAssetDataResourcePtr& collisionRes)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->OnShapeChanged(comp.Write());
    }
}

void PhysicsComponent::OnTransformChanged(const cross::TRS_A& transform)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->OnTransformChanged(comp.Write(), transform);
    }
}

void PhysicsComponent::AddPhysicsActorToPhysicsScene(cross::PhysicsActor* actor)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        sys->AddPhysicsActorToPhysicsScene(actor);
    }
}

void PhysicsComponent::RemovePhysicsActorFromPhysicsScene(cross::PhysicsActor* actor)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        sys->RemovePhysicsActorFromPhysicsScene(actor);
    }
}

void PhysicsComponent::SetDebugViewOption(const cross::PhysicsSceneDebugViewOption& option)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        sys->SetDebugViewOption(option);
    }
}

cross::PhysicsSimpleCollision* PhysicsComponent::GetPhysicsSimpleCollision()
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        return sys->GetPhysicsSimpleCollision(comp.Read());
    }
    return nullptr;
}

void PhysicsComponent::InitPhysics()
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->InitPhysics(comp.Write());
    }
}

UInt32 PhysicsComponent::RayCast(const cross::TRSVector3Type& origin, const cross::Float3& unitDir, float maxDistance, cross::CollisionMask mask, cross::HitFlag flag, UInt32 maxHit, cross::PhysicsHitResult* outResults,
                                 cross::PhysicsActor* self)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        return sys->RayCast(origin, unitDir, maxDistance, mask, flag, maxHit,self, outResults);
    }
    return 0;
}

UInt32 PhysicsComponent::Sweep(cross::PhysicsGeometryBase* geometry, const cross::TRSVector3Type& position, const cross::TRSQuaternionType& rotation, const cross::TRSVector3Type& scale, const cross::Float3& unitDir, float maxDistance,
                               cross::CollisionMask mask,
                               cross::HitFlag flag, UInt32 maxHit, cross::PhysicsHitResult* outResults, cross::PhysicsActor* self)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        sys->Sweep(geometry, position, rotation, scale, unitDir, maxDistance, mask, flag, maxHit,self, outResults);
    }
    return 0;
}

UInt32 PhysicsComponent::Overlap(cross::PhysicsGeometryBase* geometry, const cross::TRSVector3Type& position, const cross::TRSQuaternionType& rotation, const cross::TRSVector3Type& scale, cross::CollisionMask mask, UInt32 maxHit,
                                 cross::PhysicsHitResult* outResults, cross::PhysicsActor* self)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        sys->Overlap(geometry, position, rotation, scale, mask, maxHit,self, outResults);
    }
    return 0;
}

void PhysicsComponent::EnableCollisionEvent(bool reportContactPoints)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->SetCollisionEventEnable(comp.Write(), true, reportContactPoints);
    }
}

void PhysicsComponent::DisableCollisionEvent()
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->SetCollisionEventEnable(comp.Write(), false);
    }
}

// cross::CollisionCallback PhysicsComponent::GetEntityCollisionCallback() const
//{
//     cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
//     if (sys)
//     {
//         auto comp = GetECSComponent<cross::PhysicsComponentG>();
//         return sys->GetCollisionCallback(comp.Read());
//     }
//     return nullptr;
// }
//
// void PhysicsComponent::SetEntityCollisonCallback(cross::CollisionCallback entityCollisionCallback)
//{
//     cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
//     if (sys)
//     {
//         auto comp = GetECSComponent<cross::PhysicsComponentG>();
//         sys->SetCollisonCallback(comp.Write(), entityCollisionCallback);
//     }
// }
void PhysicsComponent::OnEntityCollisionEvent(cross::ecs::EntityID collidedEntityID, const cross::CollisionInfo& collisionInfo)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->CollisionEnterEvent(comp.Read(), collidedEntityID, collisionInfo);
    }
}

void PhysicsComponent::AddForce(const cross::Float3A& force)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->AddForce(comp.Write(), force);
    }
}

void PhysicsComponent::AddImpulse(const cross::Float3A& impulse)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->AddImpulse(comp.Write(), impulse);
    }
}

void PhysicsComponent::AddLinearVelocity(const cross::Float3A& velocity)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->AddLinearVelocity(comp.Write(), velocity);
    }
}

void PhysicsComponent::AddLinearAcceleration(const cross::Float3A& acceleration)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->AddLinearAcceleration(comp.Write(), acceleration);
    }
}

void PhysicsComponent::ClearForce()
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->ClearForce(comp.Write());
    }
}

void PhysicsComponent::ClearImpulse()
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->ClearImpulse(comp.Write());
    }
}

void PhysicsComponent::ClearLinearVelocity()
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->ClearLinearVelocity(comp.Write());
    }
}

void PhysicsComponent::ClearLinearAcceleration()
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->ClearLinearAcceleration(comp.Write());
    }
}

void PhysicsComponent::AddTorque(const cross::Float3& torque)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->AddTorque(comp.Write(), torque);
    }
}

void PhysicsComponent::AddImpulseTorque(const cross::Float3& impulse)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->AddImpulseTorque(comp.Write(), impulse);
    }
}

void PhysicsComponent::AddAngularVelocity(const cross::Float3& velocity)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->AddAngularVelocity(comp.Write(), velocity);
    }
}

void PhysicsComponent::AddAngularAcceleration(const cross::Float3& acceleration)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->AddAngularAcceleration(comp.Write(), acceleration);
    }
}

void PhysicsComponent::ClearTorque()
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->ClearTorque(comp.Write());
    }
}

void PhysicsComponent::ClearImpulseTorque()
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->ClearImpulseTorque(comp.Write());
    }
}

void PhysicsComponent::ClearAngularVelocity()
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->ClearAngularVelocity(comp.Write());
    }
}

void PhysicsComponent::ClearAngularAcceleration()
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->ClearAngularAcceleration(comp.Write());
    }
}

cross::Float3 PhysicsComponent::GetAngularVelocity() const
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        return sys->GetAngularVelocity(comp.Read());
    }
    return {0.0f, 0.0f, 0.0f};
}

cross::Float3 PhysicsComponent::GetLinearVelocity() const
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        return sys->GetLinearVelocity(comp.Read());
    }
    return {0.0f, 0.0f, 0.0f};
}

// Geometry from StaticMesh
const cross::PhysicsCollision* PhysicsComponent::GetPhysicsGeometry() const
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        return sys->GetPhysicsGeometry(comp.Read());
    }
    return nullptr;
}

const std::vector<cross::PhysicsGeometryBox>* PhysicsComponent::GetBoxGeometry() const
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        return &sys->GetBoxGeometry(comp.Read());
    }
    return nullptr;
}

const std::vector<cross::PhysicsGeometrySphere>* PhysicsComponent::GetSphereGeometry() const
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        return &sys->GetSphereGeometry(comp.Read());
    }
    return nullptr;
}

const std::vector<cross::PhysicsGeometryCapsule>* PhysicsComponent::GetCapsuleGeometry() const
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        return &sys->GetCapsuleGeometry(comp.Read());
    }
    return nullptr;
}

const std::vector<cross::PhysicsGeometryPlane>* PhysicsComponent::GetPlaneGeometry() const
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        return &sys->GetPlaneGeometry(comp.Read());
    }
    return nullptr;
}

const std::vector<cross::PhysicsGeometryConvex>* PhysicsComponent::GetConvexGeometry() const
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        return &sys->GetConvexGeometry(comp.Read());
    }
    return nullptr;
}

const std::vector<cross::PhysicsGeometryMesh>* PhysicsComponent::GetMeshGeometry() const
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        return &sys->GetMeshGeometry(comp.Read());
    }
    return nullptr;
}

// Geometry extra added
const cross::PhysicsSimpleCollision* PhysicsComponent::GetExtraShape() const
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        return sys->GetExtraShape(comp.Read());
    }
    return nullptr;
}

cross::PhysicsSimpleCollision* PhysicsComponent::GetExtraShape()
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        return sys->GetExtraShape(comp.Read());
    }
    return nullptr;
}

void PhysicsComponent::AddExtraBoxShape(const cross::PhysicsGeometryBox& boxGeo)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->AddExtraBoxShape(comp.Write(), boxGeo);
    }
}

void PhysicsComponent::AddExtraSphereShape(const cross::PhysicsGeometrySphere& sphereGeo)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->AddExtraSphereShape(comp.Write(), sphereGeo);
    }
}

void PhysicsComponent::AddExtraCapsuleShape(const cross::PhysicsGeometryCapsule& capsuleGeo)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->AddExtraCapsuleShape(comp.Write(), capsuleGeo);
    }
}
void PhysicsComponent::AddExtraConvexShape(const cross::PhysicsGeometryConvex& convexGeo)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->AddExtraConvexShape(comp.Write(), convexGeo);
    }
}
float PhysicsComponent::UpdateMassAndInertia(float density)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        return sys->UpdateMassAndInertia(comp.Write(), density);
    }
    return 0.0f;
}

float PhysicsComponent::SetMassAndUpdateInertia(float mass)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        return sys->UpdateMassAndInertia(comp.Write(), mass);
    }
    return 0.0f;
}

#ifdef CROSSENGINE_EDITOR

std::vector<cross::PhysicsGeometryBox> PhysicsComponent::GetExtraBoxes() const
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        return sys->GetExtraBoxes(comp.Read());
    }
    return {};
}

void PhysicsComponent::SetExtraBoxes(const std::vector<cross::PhysicsGeometryBox>& boxes)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->SetExtraBoxes(comp.Write(), boxes);
    }
}

std::vector<cross::PhysicsGeometrySphere> PhysicsComponent::GetExtraSpheres() const
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        return sys->GetExtraSpheres(comp.Read());
    }
    return {};
}

void PhysicsComponent::SetExtraSpheres(const std::vector<cross::PhysicsGeometrySphere>& spheres)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->SetExtraSpheres(comp.Write(), spheres);
    }
}

std::vector<cross::PhysicsGeometryCapsule> PhysicsComponent::GetExtraCapsules() const
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        return sys->GetExtraCapsules(comp.Read());
    }
    return {};
}

void PhysicsComponent::SetExtraCapsules(const std::vector<cross::PhysicsGeometryCapsule>& capsules)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->SetExtraCapsules(comp.Write(), capsules);
    }
}
#endif

cross::PhysicsSimpleCollision PhysicsComponent::GetExtraCollision() const
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        return sys->GetExtraCollision(comp.Read());
    }
    return {};
}

void PhysicsComponent::SetExtraCollision(const cross::PhysicsSimpleCollision& inValue)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->SetExtraCollision(comp.Write(), inValue);
    }
}

bool PhysicsComponent::GetEnable()
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        return sys->GetEnable(comp.Read());
    }
    return false;
}

void PhysicsComponent::SetEnable(bool enable)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->UpdateMassAndInertia(comp.Write(), enable);
    }
}

float PhysicsComponent::GetMass() const
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        return sys->GetMass(comp.Read());
    }
    return 0.0f;
}

void PhysicsComponent::SetMass(float mass)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->SetMass(comp.Write(), mass);
    }
}

void PhysicsComponent::SetStartAsleep(bool startupAsleep)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->UpdateMassAndInertia(comp.Write(), startupAsleep);
    }
}

bool PhysicsComponent::GetStartAsleep() const
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        return sys->GetStartAsleep(comp.Read());
    }
    return 0.0f;
}

void PhysicsComponent::SetUseMeshCollision(bool enable)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->SetUseMeshCollision(comp.Write(), enable);
    }
}

bool PhysicsComponent::GetUseMeshCollision() const
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        return sys->GetUseMeshCollision(comp.Read());
    }
    return false;
}

void PhysicsComponent::SetIsDynamic(bool enable)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->SetIsDynamic(comp.Write(), enable);
    }
}

bool PhysicsComponent::GetIsDynamic() const
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        return sys->GetIsDynamic(comp.Read());
    }
    return false;
}

void PhysicsComponent::SetEnableGravity(bool enable)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->SetEnableGravity(comp.Write(), enable);
    }
}

bool PhysicsComponent::GetEnableGravity()
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        return sys->GetEnableGravity(comp.Read());
    }
    return false;
}

bool PhysicsComponent::GetIsTrigger() const
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        return sys->GetIsTrigger(comp.Read());
    }
    return false;
}

void PhysicsComponent::SetIsTrigger(bool isTrigger)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->SetIsTrigger(comp.Write(), isTrigger);
    }
}

bool PhysicsComponent::GetIsKinematic() const
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        return sys->GetIsKinematic(comp.Read());
    }
    return false;
}

void PhysicsComponent::SetIsKinematic(bool isKinematic)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->SetIsKinematic(comp.Write(), isKinematic);
    }
}

float PhysicsComponent::GetMaxDepenetrationVelocity()
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        return sys->GetMaxDepenetrationVelocity(comp.Read());
    }
    return 0.0f;
}

void PhysicsComponent::SetMaxDepenetrationVelocity(float velocity)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->SetMaxDepenetrationVelocity(comp.Write(), velocity);
    }
}

const cross::Float3 PhysicsComponent::GetMassSpaceInertiaTensorMultiplier() const
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        return sys->GetMassSpaceInertiaTensorMultiplier(comp.Read());
    }
    return {0.0f, 0.0f, 0.0f};
}

void PhysicsComponent::SetMassSpaceInertiaTensorMultiplier(const cross::Float3& multiplier)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->SetMassSpaceInertiaTensorMultiplier(comp.Write(), multiplier);
    }
}

cross::CollisionType PhysicsComponent::GetCollisionType() const
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        return sys->GetCollisionType(comp.Read());
    }
    return cross::CollisionType::NoCollision;
}

void PhysicsComponent::SetCollisionType(cross::CollisionType type)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->SetCollisionType(comp.Write(), type);
    }
}

cross::CollisionMask PhysicsComponent::GetCollisionMask() const
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        return sys->GetCollisionMask(comp.Read());
    }
    return cross::CollisionMask::None();
}

void PhysicsComponent::SetCollisionMask(cross::CollisionMask mask)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->SetCollisionMask(comp.Write(), mask);
    }
}

cross::MaterialType PhysicsComponent::GetMaterialType() const
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        return sys->GetMaterialType(comp.Read());
    }
    return cross::MaterialType::None;
}

void PhysicsComponent::SetMaterialType(cross::MaterialType materialType)
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->SetMaterialType(comp.Write(), materialType);
    }
}

cross::PhysicsScene* PhysicsComponent::GetPhysicsScene() const
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        return sys->GetPhysicsScene();
    }
    return nullptr;
}

const cross::PhysicsActor* PhysicsComponent::GetPhysicsActor() const
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        return sys->GetPhysicsActor(comp.Read());
    }
    return nullptr;
}

float PhysicsComponent::GetLinearDamping() const
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        return sys->GetLinearDamping(comp.Read());
    }
    return 0.0f;
}

void PhysicsComponent::SetLinearDamping(float linerDamping) const
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        sys->SetLinearDamping(comp.Write(), linerDamping);
    }
}

const cross::PhysicsQuery* PhysicsComponent::SceneQuery() const
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        return sys->SceneQuery();
    }
    return nullptr;
}

cross::PhysicsControllerManager* PhysicsComponent::GetControllerManager()
{
    cross::PhysicsSystemG* sys = GetSystem<cross::PhysicsSystemG>();
    if (sys)
    {
        return sys->GetControllerManager();
    }
    return nullptr;
}

}   // namespace cegf