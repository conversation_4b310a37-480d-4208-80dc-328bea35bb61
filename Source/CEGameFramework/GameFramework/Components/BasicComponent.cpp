#include "GameFramework/Components/BasicComponent.h"
#include "Runtime/GameWorld/AABBSystemG.h"
#include "Runtime/GameWorld/RenderPropertySystemG.h"
#include "Runtime/GameWorld/PhysicsSystemG.h"

namespace cegf
{

BasicComponent::BasicComponent()
{
    
}

BasicComponent::~BasicComponent()
{
    
}

void BasicComponent::Init()
{
    GameObjectComponent::Init();
    //cross::PhysicsSystemG* physSys = GetSystem<cross::PhysicsSystemG>();
    //if (physSys)
    //{
    //    auto comp = GetECSComponent<cross::PhysicsComponentG>();

    //    physSys->InitPhysics(comp.Write());
    //}
}

void BasicComponent::GetRelatedECSComponentBitMask(cross::ecs::ComponentBitMask& bitMask) const
{

    GameObjectComponent::GetRelatedECSComponentBitMask(bitMask);
    bitMask.Set(cross::PhysicsComponentG::GetDesc()->GetMaskBitIndex(), true);
    bitMask.Set(cross::AABBComponentG::GetDesc()->GetMaskBitIndex(), true);
    bitMask.Set(cross::RenderPropertyComponentG::GetDesc()->GetMaskBitIndex(), true);
}
 
const cross::BoundingBox& BasicComponent::GetLocalAABB()
{
    auto comp = GetECSComponent<cross::AABBComponentG>();
    auto aabbSys = GetSystem<cross::AABBSystemG>();
    return aabbSys->GetLocalAABB(comp.Read());
}

const cross::BoundingBox& BasicComponent::GetWorldAABB()
{
    auto comp = GetECSComponent<cross::AABBComponentG>();
    auto aabbSys = GetSystem<cross::AABBSystemG>();
    return aabbSys->GetWorldAABB(comp.Read());
}

void BasicComponent::SetLocalAABB(const cross::BoundingBox& localAABB)
{
    auto comp = GetECSComponent<cross::AABBComponentG>();
    auto aabbSys = GetSystem<cross::AABBSystemG>();
    aabbSys->SetLocalAABB(comp.Write(), localAABB);
}

void BasicComponent::SetWorldAABB(const cross::BoundingBox& worldAABB)
{
    auto comp = GetECSComponent<cross::AABBComponentG>();
    auto aabbSys = GetSystem<cross::AABBSystemG>();
    aabbSys->SetWorldAABB(comp.Write(), worldAABB);
}

void BasicComponent::AddForce(const cross::Float3A& force)
{
    auto comp = GetECSComponent<cross::PhysicsComponentG>();
    auto physSys = GetSystem<cross::PhysicsSystemG>();
    physSys->AddForce(comp.Write(), force);
}

void BasicComponent::AddImpulse(const cross::Float3A& impulse)
{
    auto comp = GetECSComponent<cross::PhysicsComponentG>();
    auto physSys = GetSystem<cross::PhysicsSystemG>();
    physSys->AddImpulse(comp.Write(), impulse);
}

void BasicComponent::AddLinearVelocity(const cross::Float3A& velocity)
{
    auto comp = GetECSComponent<cross::PhysicsComponentG>();
    auto physSys = GetSystem<cross::PhysicsSystemG>();
    physSys->AddLinearVelocity(comp.Write(), velocity);
}

void BasicComponent::AddLinearAcceleration(const cross::Float3A& acceleration)
{
    auto comp = GetECSComponent<cross::PhysicsComponentG>();
    auto physSys = GetSystem<cross::PhysicsSystemG>();
    physSys->AddLinearAcceleration(comp.Write(), acceleration);
}

cross::CollisionType BasicComponent::GetCollisionType() const
{
    auto comp = GetECSComponent<cross::PhysicsComponentG>();
    auto physSys = GetSystem<cross::PhysicsSystemG>();
    return physSys->GetCollisionType(comp.Read()); 
}

void BasicComponent::SetCollisionType(cross::CollisionType collisionType)
{
    auto comp = GetECSComponent<cross::PhysicsComponentG>();
    auto physSys = GetSystem<cross::PhysicsSystemG>();
    physSys->SetCollisionType(comp.Write(), collisionType);
}

cross::CollisionChannelBit BasicComponent::GetCollisionChannel() const
{
    auto comp = GetECSComponent<cross::PhysicsComponentG>();
    auto physSys = GetSystem<cross::PhysicsSystemG>();
    return static_cast<cross::CollisionChannelBit>(physSys->GetCollisionMask(comp.Read()));
}

void BasicComponent::SetCollisionChannel(cross::CollisionChannelBit channel)
{
    auto comp = GetECSComponent<cross::PhysicsComponentG>();
    auto physSys = GetSystem<cross::PhysicsSystemG>();
    physSys->SetCollisionMask(comp.Write(), channel);
}

}

