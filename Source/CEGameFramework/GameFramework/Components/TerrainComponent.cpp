#include "TerrainComponent.h"

#include "Runtime/GameWorld/TerrainSystemG.h"
#include "Runtime/GameWorld/RenderPropertySystemG.h"

namespace cegf
{
bool TerrainComponent::GetTerrainEnable() const
{
    cross::TerrainSystemG* sys = GetSystem<cross::TerrainSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::TerrainComponentG>();
        return sys->GetTerrainEnable(comp.Read());
    }
    return {};
}

void TerrainComponent::SetTerrainEnable(bool enable)
{
    cross::TerrainSystemG* sys = GetSystem<cross::TerrainSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::TerrainComponentG>();
        sys->SetTerrainEnable(comp.Write(), enable);
    }
}

cross::TerrainSurfaceType TerrainComponent::GetSurfaceType() const
{
    cross::TerrainSystemG* sys = GetSystem<cross::TerrainSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::TerrainComponentG>();
        return sys->GetSurfaceType(comp.Read());
    }
    return {};
}

UInt32 TerrainComponent::GetGridSizeX() const
{
    cross::TerrainSystemG* sys = GetSystem<cross::TerrainSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::TerrainComponentG>();
        return sys->GetGridSizeX(comp.Read());
    }
    return {};
}

UInt32 TerrainComponent::GetGridSizeY() const
{
    cross::TerrainSystemG* sys = GetSystem<cross::TerrainSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::TerrainComponentG>();
        return sys->GetGridSizeY(comp.Read());
    }
    return {};
}

UInt32 TerrainComponent::GetBlockSize() const
{
    cross::TerrainSystemG* sys = GetSystem<cross::TerrainSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::TerrainComponentG>();
        return sys->GetBlockSize(comp.Read());
    }
    return {};
}

UInt32 TerrainComponent::GetTileSize() const
{
    cross::TerrainSystemG* sys = GetSystem<cross::TerrainSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::TerrainComponentG>();
        return sys->GetTileSize(comp.Read());
    }
    return {};
}

UInt32 TerrainComponent::GetTextureSize() const
{
    cross::TerrainSystemG* sys = GetSystem<cross::TerrainSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::TerrainComponentG>();
        return sys->GetTextureSize(comp.Read());
    }
    return {};
}

float TerrainComponent::GetWGS84SemiMajor() const
{
    cross::TerrainSystemG* sys = GetSystem<cross::TerrainSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::TerrainComponentG>();
        return sys->GetWGS84SemiMajor(comp.Read());
    }
    return {};
}

cross::Float3 TerrainComponent::GetWorldScale() const
{
    cross::TerrainSystemG* sys = GetSystem<cross::TerrainSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::TerrainComponentG>();
        return sys->GetWorldScale(comp.Read());
    }
    return {};
}

cross::Float3 TerrainComponent::GetWorldTranslation() const
{
    cross::TerrainSystemG* sys = GetSystem<cross::TerrainSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::TerrainComponentG>();
        return sys->GetWorldTranslation(comp.Read());
    }
    return {};
}

UInt32 TerrainComponent::GetGridDimX() const
{
    cross::TerrainSystemG* sys = GetSystem<cross::TerrainSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::TerrainComponentG>();
        return sys->GetGridDimX(comp.Read());
    }
    return {};
}

UInt32 TerrainComponent::GetGridDimY() const
{
    cross::TerrainSystemG* sys = GetSystem<cross::TerrainSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::TerrainComponentG>();
        return sys->GetGridDimY(comp.Read());
    }
    return {};
}

std::string TerrainComponent::GetTerrainPath()
{
    cross::TerrainSystemG* sys = GetSystem<cross::TerrainSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::TerrainComponentG>();
        return sys->GetTerrainPath(comp.Read());
    }
    return {};
}

void TerrainComponent::SetTerrainPath(const std::string& terrainPath)
{
    cross::TerrainSystemG* sys = GetSystem<cross::TerrainSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::TerrainComponentG>();
        sys->SetTerrainPath(comp.Write(), terrainPath);
    }
}

cross::TerrainInfo TerrainComponent::GetTerrainInfo() const
{
    cross::TerrainSystemG* sys = GetSystem<cross::TerrainSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::TerrainComponentG>();
        return sys->GetTerrainInfo(comp.Read());
    }
    return {};
}

UInt32 TerrainComponent::GetNumBlendLayers() const
{
    cross::TerrainSystemG* sys = GetSystem<cross::TerrainSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::TerrainComponentG>();
        return sys->GetNumBlendLayers(comp.Read());
    }
    return {};
}

void TerrainComponent::GetRelatedECSComponentBitMask(cross::ecs::ComponentBitMask& bitMask) const
{
    GameObjectComponent::GetRelatedECSComponentBitMask(bitMask);
    bitMask.Set(cross::TerrainComponentG::GetDesc()->GetMaskBitIndex(), true);
    bitMask.Set(cross::RenderPropertyComponentG::GetDesc()->GetMaskBitIndex(), true);
}
UInt64 TerrainComponent::CreateCustomTerrainOverride(cross::Float2 latRange, cross::Float2 lonRange) {
    cross::TerrainSystemG* sys = GetSystem<cross::TerrainSystemG>();
    auto comp = GetECSComponent<cross::TerrainComponentG>();
    return sys->CreateCustomTerrainOverride(comp.Write(),latRange,lonRange);
}
void TerrainComponent::RemoveCustomTerrainOverride(UInt64 handle) {
    cross::TerrainSystemG* sys = GetSystem<cross::TerrainSystemG>();
    auto comp = GetECSComponent<cross::TerrainComponentG>();
    return sys->RemoveCustomTerrainOverride(comp.Write(), handle);
}
} // namespace cegf
