#pragma once
#include "GameFramework/Components/BasicComponent.h"
#include "GameFramework/GameFrameworkGlobals.h"
#include "Runtime/GameWorld/PhysicsSystemG.h"

namespace cross {
class PhysicsSystemG;
}

namespace cegf {
class GAMEFRAMEWORK_API CEMeta(Cli,Reflect, Puerts) PhysicsComponent : public GameObjectComponent
{
public:
    CEMeta(Reflect)
    PhysicsComponent() = default;

    virtual ~PhysicsComponent() = default;

    virtual void Init() override;

    virtual void GetRelatedECSComponentBitMask(cross::ecs::ComponentBitMask& bitMask) const override;

    virtual void StartGame() override;

public:
    CEFunction(ScriptCallable)
    void BindCollisionEvent(cross::CollisionEventType eventType, std::function<void(cegf::GameObject*)> eventCallback);

    void OnCollisionEnter(cross::ecs::EntityID);
    void OnCollisionStay(cross::ecs::EntityID);
    void OnCollisionExit(cross::ecs::EntityID);
    void OnTriggerEnter(cross::ecs::EntityID);
    void OnTriggerExit(cross::ecs::EntityID);

    void OnShapeChanged();

    void OnShapeChanged(UInt32 modelIndex, const cross::MeshAssetDataResourcePtr& collisionRes);

    void OnTransformChanged(const cross::TRS_A& transform);

    void AddPhysicsActorToPhysicsScene(cross::PhysicsActor* actor);

    void RemovePhysicsActorFromPhysicsScene(cross::PhysicsActor* actor);

    void SetDebugViewOption(const cross::PhysicsSceneDebugViewOption& option);

    cross::PhysicsSimpleCollision* GetPhysicsSimpleCollision();

    void InitPhysics();

    UInt32 RayCast(const cross::TRSVector3Type& origin,
                   const cross::Float3& unitDir,
                   float maxDistance,
                   cross::CollisionMask mask,
                   cross::HitFlag flag = cross::HitFlag::Default,
                   UInt32 maxHit = 1,
                   cross::PhysicsHitResult* outResults = nullptr,
                   cross::PhysicsActor* self = nullptr);

    UInt32 Sweep(cross::PhysicsGeometryBase * geometry,
                 const cross::TRSVector3Type& position,
                 const cross::TRSQuaternionType& rotation,
                 const cross::TRSVector3Type& scale,
                 const cross::Float3& unitDir,
                 float maxDistance,
                 cross::CollisionMask mask,
                 cross::HitFlag flag = cross::HitFlag::Default,
                 UInt32 maxHit = 1,
                 cross::PhysicsHitResult* outResults = nullptr,
                 cross::PhysicsActor* self = nullptr);

    UInt32 Overlap(cross::PhysicsGeometryBase * geometry,
                   const cross::TRSVector3Type& position,
                   const cross::TRSQuaternionType& rotation,
                   const cross::TRSVector3Type& scale,
                   cross::CollisionMask mask,
                   UInt32 maxHit = 1,
                   cross::PhysicsHitResult* outResults = nullptr,
                   cross::PhysicsActor* self = nullptr);

    void EnableCollisionEvent(bool reportContactPoints = false);

    void DisableCollisionEvent();

    cross::CollisionCallback GetEntityCollisionCallback() const;

    void SetEntityCollisonCallback(cross::CollisionCallback entityCollisionCallback);
    
    void OnEntityCollisionEvent(cross::ecs::EntityID collidedEntityID, const cross::CollisionInfo& collisionInfo);

    void AddForce(const cross::Float3A& force);

    void AddImpulse(const cross::Float3A& impulse);

    void AddLinearVelocity(const cross::Float3A& velocity);

    void AddLinearAcceleration(const cross::Float3A& acceleration);

    void ClearForce();

    void ClearImpulse();

    void ClearLinearVelocity();

    void ClearLinearAcceleration();

    void AddTorque(const cross::Float3& torque);

    void AddImpulseTorque(const cross::Float3& impulse);

    void AddAngularVelocity(const cross::Float3& velocity);

    void AddAngularAcceleration(const cross::Float3& acceleration);

    void ClearTorque();

    void ClearImpulseTorque();

    void ClearAngularVelocity();

    void ClearAngularAcceleration();

    cross::Float3 GetAngularVelocity() const;

    cross::Float3 GetLinearVelocity() const;

    // Geometry from StaticMesh
    const cross::PhysicsCollision* GetPhysicsGeometry() const;
    const std::vector<cross::PhysicsGeometryBox>* GetBoxGeometry() const;
    const std::vector<cross::PhysicsGeometrySphere>* GetSphereGeometry() const;
    const std::vector<cross::PhysicsGeometryCapsule>* GetCapsuleGeometry() const;
    const std::vector<cross::PhysicsGeometryPlane>* GetPlaneGeometry() const;
    const std::vector<cross::PhysicsGeometryConvex>* GetConvexGeometry() const;
    const std::vector<cross::PhysicsGeometryMesh>* GetMeshGeometry() const;

    // Geometry extra added
    const cross::PhysicsSimpleCollision* GetExtraShape() const;

    cross::PhysicsSimpleCollision* GetExtraShape();

    void AddExtraBoxShape(const cross::PhysicsGeometryBox& boxGeo);

    void AddExtraSphereShape(const cross::PhysicsGeometrySphere& sphereGeo);

    void AddExtraCapsuleShape(const cross::PhysicsGeometryCapsule& capsuleGeo);

    void AddExtraConvexShape(const cross::PhysicsGeometryConvex& convexGeo);

    float UpdateMassAndInertia(float density);

    float SetMassAndUpdateInertia(float mass);

#ifdef CROSSENGINE_EDITOR
    std::vector<cross::PhysicsGeometryBox> GetExtraBoxes() const;

    void SetExtraBoxes(const std::vector<cross::PhysicsGeometryBox>& boxes);

    std::vector<cross::PhysicsGeometrySphere> GetExtraSpheres() const;

    void SetExtraSpheres(const std::vector<cross::PhysicsGeometrySphere>& spheres);

    std::vector<cross::PhysicsGeometryCapsule> GetExtraCapsules() const;

    void SetExtraCapsules(const std::vector<cross::PhysicsGeometryCapsule>& capsules);
#endif

    cross::PhysicsSimpleCollision GetExtraCollision() const;

    void SetExtraCollision(const cross::PhysicsSimpleCollision& inValue);

    bool GetEnable();

    void SetEnable(bool enable);

    float GetMass() const;

    void SetMass(float mass);

    void SetStartAsleep(bool startupAsleep);

    bool GetStartAsleep() const;

    void SetUseMeshCollision(bool enable);

    bool GetUseMeshCollision() const;

    void SetIsDynamic(bool enable);

    bool GetIsDynamic() const;

    void SetEnableGravity(bool enable);

    bool GetEnableGravity();

    bool GetIsTrigger() const;

    void SetIsTrigger(bool isTrigger);

    bool GetIsKinematic() const;
    CEFunction(ScriptCallable)
    void SetIsKinematic(bool isKinematic);

    float GetMaxDepenetrationVelocity();

    void SetMaxDepenetrationVelocity(float velocity);

    const cross::Float3 GetMassSpaceInertiaTensorMultiplier() const;

    void SetMassSpaceInertiaTensorMultiplier(const cross::Float3& multiplier);

    cross::CollisionType GetCollisionType() const;

    void SetCollisionType(cross::CollisionType type);

    cross::CollisionMask GetCollisionMask() const;

    void SetCollisionMask(cross::CollisionMask mask);

    cross::MaterialType GetMaterialType() const;

    void SetMaterialType(cross::MaterialType materialType);

    cross::PhysicsScene* GetPhysicsScene() const;

    const cross::PhysicsActor* GetPhysicsActor() const;

    float GetLinearDamping() const;

    void SetLinearDamping(float linerDamping) const;

    const cross::PhysicsQuery* SceneQuery() const;

    cross::PhysicsControllerManager* GetControllerManager();

protected:
    std::vector<std::function<void(GameObject*)>> mCollisionEnterCBs;
    std::vector<std::function<void(GameObject*)>> mCollisionStayCBs;
    std::vector<std::function<void(GameObject*)>> mCollisionExitCBs;
    std::vector<std::function<void(GameObject*)>> mTriggerEnterCBs;
    std::vector<std::function<void(GameObject*)>> mTriggerExitCBs;
};

}   // namespace cegf
