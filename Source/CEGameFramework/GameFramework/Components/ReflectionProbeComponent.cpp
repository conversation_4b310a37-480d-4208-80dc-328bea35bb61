#include "ReflectionProbeComponent.h"

#include "Runtime/GameWorld/ReflectionProbeSystemG.h"

namespace cegf
{
void ReflectionProbeComponent::SetReflectionProbeEnable(bool val)
{
    cross::ReflectionProbeSystemG* sys = GetSystem<cross::ReflectionProbeSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::ReflectionProbeComponentG>();
        sys->SetReflectionProbeEnable(comp.Write(), val);
    }
}

bool ReflectionProbeComponent::GetReflectionProbeEnable() const
{
    cross::ReflectionProbeSystemG* sys = GetSystem<cross::ReflectionProbeSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::ReflectionProbeComponentG>();
        return sys->GetReflectionProbeEnable(comp.Read());
    }
    return {};
}

void ReflectionProbeComponent::SetReflectionProbeNearPlane(float val)
{
    cross::ReflectionProbeSystemG* sys = GetSystem<cross::ReflectionProbeSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::ReflectionProbeComponentG>();
        sys->SetReflectionProbeNearPlane(comp.Write(), val);
    }
}

float ReflectionProbeComponent::GetReflectionProbeNearPlane() const
{
    cross::ReflectionProbeSystemG* sys = GetSystem<cross::ReflectionProbeSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::ReflectionProbeComponentG>();
        return sys->GetReflectionProbeNearPlane(comp.Read());
    }
    return {};
}

void ReflectionProbeComponent::SetReflectionProbeRefleProbeShapeType(cross::ReflectionProbeShapeType val)
{
    cross::ReflectionProbeSystemG* sys = GetSystem<cross::ReflectionProbeSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::ReflectionProbeComponentG>();
        sys->SetReflectionProbeRefleProbeShapeType(comp.Write(), val);
    }
}

cross::ReflectionProbeShapeType ReflectionProbeComponent::GetReflectionProbeRefleProbeShapeType() const
{
    cross::ReflectionProbeSystemG* sys = GetSystem<cross::ReflectionProbeSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::ReflectionProbeComponentG>();
        return sys->GetReflectionProbeRefleProbeShapeType(comp.Read());
    }
    return {};
}

void ReflectionProbeComponent::SetReflectionProbeSphereRadius(float val)
{
    cross::ReflectionProbeSystemG* sys = GetSystem<cross::ReflectionProbeSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::ReflectionProbeComponentG>();
        sys->SetReflectionProbeSphereRadius(comp.Write(), val);
    }
}

float ReflectionProbeComponent::GetReflectionProbeSphereRadius() const
{
    cross::ReflectionProbeSystemG* sys = GetSystem<cross::ReflectionProbeSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::ReflectionProbeComponentG>();
        return sys->GetReflectionProbeSphereRadius(comp.Read());
    }
    return {};
}

void ReflectionProbeComponent::SetReflectionProbeBlendDistance(float val)
{
    cross::ReflectionProbeSystemG* sys = GetSystem<cross::ReflectionProbeSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::ReflectionProbeComponentG>();
        sys->SetReflectionProbeBlendDistance(comp.Write(), val);
    }
}

float ReflectionProbeComponent::GetReflectionProbeBlendDistance() const
{
    cross::ReflectionProbeSystemG* sys = GetSystem<cross::ReflectionProbeSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::ReflectionProbeComponentG>();
        return sys->GetReflectionProbeBlendDistance(comp.Read());
    }
    return {};
}

void ReflectionProbeComponent::SetReflectionProbeIntensity(float val)
{
    cross::ReflectionProbeSystemG* sys = GetSystem<cross::ReflectionProbeSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::ReflectionProbeComponentG>();
        sys->SetReflectionProbeIntensity(comp.Write(), val);
    }
}

float ReflectionProbeComponent::GetReflectionProbeIntensity() const
{
    cross::ReflectionProbeSystemG* sys = GetSystem<cross::ReflectionProbeSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::ReflectionProbeComponentG>();
        return sys->GetReflectionProbeIntensity(comp.Read());
    }
    return {};
}

void ReflectionProbeComponent::SetReflectionProbeBoxSize(cross::Float3 val)
{
    cross::ReflectionProbeSystemG* sys = GetSystem<cross::ReflectionProbeSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::ReflectionProbeComponentG>();
        sys->SetReflectionProbeBoxSize(comp.Write(), val);
    }
}

cross::Float3 ReflectionProbeComponent::GetReflectionProbeBoxSize() const
{
    cross::ReflectionProbeSystemG* sys = GetSystem<cross::ReflectionProbeSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::ReflectionProbeComponentG>();
        return sys->GetReflectionProbeBoxSize(comp.Read());
    }
    return {};
}

void ReflectionProbeComponent::SetReflectionProbeRefleProbeType(cross::ReflectionProbeType val)
{
    cross::ReflectionProbeSystemG* sys = GetSystem<cross::ReflectionProbeSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::ReflectionProbeComponentG>();
        sys->SetReflectionProbeRefleProbeType(comp.Write(), val);
    }
}

cross::ReflectionProbeType ReflectionProbeComponent::GetReflectionProbeRefleProbeType() const
{
    cross::ReflectionProbeSystemG* sys = GetSystem<cross::ReflectionProbeSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::ReflectionProbeComponentG>();
        return sys->GetReflectionProbeRefleProbeType(comp.Read());
    }
    return {};
}

void ReflectionProbeComponent::SetReflectionProbeRefreshMode(cross::ReflectionProbeRefreshMode val)
{
    cross::ReflectionProbeSystemG* sys = GetSystem<cross::ReflectionProbeSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::ReflectionProbeComponentG>();
        sys->SetReflectionProbeRefreshMode(comp.Write(), val);
    }
}

cross::ReflectionProbeRefreshMode ReflectionProbeComponent::GetReflectionProbeRefreshMode() const
{
    cross::ReflectionProbeSystemG* sys = GetSystem<cross::ReflectionProbeSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::ReflectionProbeComponentG>();
        return sys->GetReflectionProbeRefreshMode(comp.Read());
    }
    return {};
}

void ReflectionProbeComponent::SetReflectionProbeTimeSlicing(cross::ReflectionProbeTimeSlicing val)
{
    cross::ReflectionProbeSystemG* sys = GetSystem<cross::ReflectionProbeSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::ReflectionProbeComponentG>();
        sys->SetReflectionProbeTimeSlicing(comp.Write(), val);
    }
}

cross::ReflectionProbeTimeSlicing ReflectionProbeComponent::GetReflectionProbeTimeSlicing() const
{
    cross::ReflectionProbeSystemG* sys = GetSystem<cross::ReflectionProbeSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::ReflectionProbeComponentG>();
        return sys->GetReflectionProbeTimeSlicing(comp.Read());
    }
    return {};
}

void ReflectionProbeComponent::SetReflectionProbeBoxProjection(bool val)
{
    cross::ReflectionProbeSystemG* sys = GetSystem<cross::ReflectionProbeSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::ReflectionProbeComponentG>();
        sys->SetReflectionProbeBoxProjection(comp.Write(), val);
    }
}

bool ReflectionProbeComponent::GetReflectionProbeBoxProjection() const
{
    cross::ReflectionProbeSystemG* sys = GetSystem<cross::ReflectionProbeSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::ReflectionProbeComponentG>();
        return sys->GetReflectionProbeBoxProjection(comp.Read());
    }
    return {};
}

UInt32 ReflectionProbeComponent::GetReflectionProbeResolution() const
{
    cross::ReflectionProbeSystemG* sys = GetSystem<cross::ReflectionProbeSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::ReflectionProbeComponentG>();
        return sys->GetReflectionProbeResolution(comp.Read());
    }
    return {};
}

void ReflectionProbeComponent::SetReflectionProbeCaptureCacheOffset(UInt32 val)
{
    cross::ReflectionProbeSystemG* sys = GetSystem<cross::ReflectionProbeSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::ReflectionProbeComponentG>();
        sys->SetReflectionProbeCaptureCacheOffset(comp.Write(), val);
    }
}

UInt32 ReflectionProbeComponent::GetReflectionProbeCaptureCacheOffset()
{
    cross::ReflectionProbeSystemG* sys = GetSystem<cross::ReflectionProbeSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::ReflectionProbeComponentG>();
        return sys->GetReflectionProbeCaptureCacheOffset(comp.Read());
    }
    return {};
}

void ReflectionProbeComponent::SetReflectionProbeCaptureSavePath(const std::string& val)
{
    cross::ReflectionProbeSystemG* sys = GetSystem<cross::ReflectionProbeSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::ReflectionProbeComponentG>();
        sys->SetReflectionProbeCaptureSavePath(comp.Write(), val);
    }
}

void ReflectionProbeComponent::SetReflectionProbeIndex(UInt16 val)
{
    cross::ReflectionProbeSystemG* sys = GetSystem<cross::ReflectionProbeSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::ReflectionProbeComponentG>();
        sys->SetReflectionProbeIndex(comp.Write(), val);
    }
}

UInt16 ReflectionProbeComponent::GetReflectionProbeIndex()
{
    cross::ReflectionProbeSystemG* sys = GetSystem<cross::ReflectionProbeSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::ReflectionProbeComponentG>();
        return sys->GetReflectionProbeIndex(comp.Read());
    }
    return {};
}

std::string ReflectionProbeComponent::GetReflectionProbeReflectionTexturePath() const
{
    cross::ReflectionProbeSystemG* sys = GetSystem<cross::ReflectionProbeSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::ReflectionProbeComponentG>();
        return sys->GetReflectionProbeReflectionTexturePath(comp.Read());
    }
    return {};
}

void ReflectionProbeComponent::SetReflectionProbeShow(bool isShow)
{
    cross::ReflectionProbeSystemG* sys = GetSystem<cross::ReflectionProbeSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::ReflectionProbeComponentG>();
        sys->SetReflectionProbeShow(comp.Write(), isShow);
    }
}

void ReflectionProbeComponent::GetRelatedECSComponentBitMask(cross::ecs::ComponentBitMask& bitMask) const
{
    GameObjectComponent::GetRelatedECSComponentBitMask(bitMask);
    bitMask.Set(cross::ReflectionProbeComponentG::GetDesc()->GetMaskBitIndex(), true);
}
} // namespace cegf
