#pragma once

#include "GameFramework/Components/Component.h"
#include "Runtime/GameWorld/SkyAtmosphereSystemG.h"

namespace cegf
{
class GAMEFRAMEWORK_API CEMeta(Reflect, Cli, WorkflowType, Puerts) SkyAtmosphereComponent : public GameObjectComponent
{
public:
    CEMeta(Reflect) SkyAtmosphereComponent() = default;
    CEFunction(Reflect, Cli, ScriptCallable, WorkflowExecutable)
    cross::SkyAtmosphereConfig GetSkyAtmosphereConfig() const;
    CEFunction(Reflect, Cli, ScriptCallable, WorkflowExecutable)
    void SetSkyAtmosphereConfig(const cross::SkyAtmosphereConfig& inConfig);
    CEFunction(Reflect, Cli, ScriptCallable, WorkflowExecutable)
    cross::SkyAtmosphereOuterParam GetSkyAtmosphereOuterParam() const;
    CEFunction(Reflect, C<PERSON>, ScriptCallable, WorkflowExecutable)
    void SetSkyAtmosphereOuterParam(const cross::SkyAtmosphereOuterParam& inParam);
    
    virtual void GetRelatedECSComponentBitMask(cross::ecs::ComponentBitMask& bitMask) const override;
};
} // namespace cegf
