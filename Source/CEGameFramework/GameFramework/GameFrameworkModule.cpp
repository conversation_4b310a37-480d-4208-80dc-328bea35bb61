#include "GameFrameworkModule.h"
#include "Runtime/Interface/CrossEngine.h"
#include "CECommon/Common/GlobalSystemDesc.h"
#include "GameFrameworkSystem.h"
#include "ValueConverter/ResourceClassRegisterFactory.h"
#include "GameFramework/Input/InputActionMapping/InputActionMappingRegistry.h"

#pragma warning(push)
#pragma warning(disable : 4100)

void GameFrameworkRegister();
void GameFramework_NodeReflection();
    // void SystemRegisterR();
// void SystemRegisterG();
namespace cross::scripts {
void CodeGenRegisterGeneratedClass();
}

namespace cegf {

void GameFrameworkModule::RegisterGameFrameworkClasses()
{
    cegf::ResourceClassRegisterFactory::Instance().RegisterResourceRuntimeClass();
    cegf::InputActionMappingRegistry::Instance().RegisterInputActionMappingClasses();
}

GameFrameworkModule::GameFrameworkModule()
{
    GameFrameworkRegister();
    GameFramework_NodeReflection();
    cross::scripts::CodeGenRegisterGeneratedClass();
    RegisterGameFrameworkClasses();
    GameFrameworkSystem* frameworkSystem = GameFrameworkSystem::CreateInstance();
    const cross::GlobalSystemDesc& frameworkSystemDesc = GameFrameworkSystem::GetDesc();
    frameworkSystemDesc.mUpdatePriority = 0;
    frameworkSystemDesc.mBeginFramePriority = 0;
    frameworkSystemDesc.mEndFramePriority = 4000;
    cross::EngineGlobal::GetEngine()->AddGlobalSystem(frameworkSystem);

    cross::GOSerializerComponentG::GetDesc();
}

GameFrameworkModule::~GameFrameworkModule()
{
    
}

}// namespace cross
#pragma warning(pop)
