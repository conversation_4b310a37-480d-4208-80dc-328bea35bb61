#include "Dispatchers.h"


namespace cegf { namespace evt {

    void TransformJointEventDispatcherBase::SubscribeInternal()
    {
        mWorld->GetGameSystem<cross::TransformSystemG>()->SubscribeRemainedEvent<cross::TransformJointEvent>(this, true);
    }

    void TransformJointEventDispatcherBase::UnSubscribeInternal()
    {
        mWorld->GetGameSystem<cross::TransformSystemG>()->Unsubscribe<cross::TransformJointEvent>(this);
    }

    void GOEventDispatcherBase::RegisterListener(Listener* inListener)
    {
        if (inListener != nullptr && inListener->Accept(GetEventCallerID()))
        {
            std::lock_guard<std::mutex> lock(mListenerSetMutex);
            mListeners.insert(inListener);
        }
    }

    void GOEventDispatcherBase::UnregisterListener(Listener* inListener)
    {
        {
            std::lock_guard<std::mutex> lock(mListenerSetMutex);
            if (auto itr = mListeners.find(inListener); itr != mListeners.end())
            {
                mListeners.erase(inListener);
            }
        }
    }

    void TransformParentJointEventDispatcher::NotifyEvent(const cross::SystemEventBase& event, UInt32& flag)
    {
        if (event.mEventType == cross::RemainedEventUpdatedEvent::sEventType)
        {
            const cross::RemainedEventUpdatedEvent& ee = TYPE_CAST(const cross::RemainedEventUpdatedEvent&, event);
            if (ee.mData.mRemainedEventType == cross::TransformJointEvent::sEventType)
            {
                auto transformSystem = mWorld->GetGameSystem<cross::TransformSystemG>();
                for (auto index = ee.mData.mFirstIndex; index <= ee.mData.mLastIndex; index++)
                {
                    std::lock_guard<std::mutex> lock(mListenerSetMutex);
                    const cross::TransformJointEvent& e = transformSystem->GetRemainedEvent<cross::TransformJointEvent>(index);
                    auto& eventData = e.mData;
                    for (auto listener : mListeners)
                    {
                        if (listener->GetOwnerEntityID() == cross::ecs::EntityID::InvalidHandle())
                        {
                            continue;
                        }

                        if (eventData.mChildEntity == listener->GetOwnerEntityID())
                        {
                            listener->NotifyEvent(GetEventCallerID());
                        }
                    }
                }
            }
        }
    }

    void TransformChildrenJointEventDispatcher::NotifyEvent(const cross::SystemEventBase& event, UInt32& flag)
    {
        if (event.mEventType == cross::RemainedEventUpdatedEvent::sEventType)
        {
            const cross::RemainedEventUpdatedEvent& ee = TYPE_CAST(const cross::RemainedEventUpdatedEvent&, event);
            if (ee.mData.mRemainedEventType == cross::TransformJointEvent::sEventType)
            {
                auto transformSystem = mWorld->GetGameSystem<cross::TransformSystemG>();
                for (auto index = ee.mData.mFirstIndex; index <= ee.mData.mLastIndex; index++)
                {
                    std::lock_guard<std::mutex> lock(mListenerSetMutex);
                    const cross::TransformJointEvent& e = transformSystem->GetRemainedEvent<cross::TransformJointEvent>(index);
                    auto& eventData = e.mData;
                    for (auto listener : mListeners)
                    {
                        if (listener->GetOwnerEntityID() == cross::ecs::EntityID::InvalidHandle())
                        {
                            continue;
                        }

                        if (eventData.mOldParentEntity == listener->GetOwnerEntityID() || eventData.mNewParentEntity == listener->GetOwnerEntityID())
                        {
                            listener->NotifyEvent(GetEventCallerID());
                        }
                    }
                }
            }
        }
    }
}}