#include "GameFramework/HLOD/HLODProxy.h"
#include "GameFramework/GameWorld.h"

namespace cegf
{

HLODProxyObject* HLODProxyObject::AsHLODProxy(GameObject* object)
{
    return dynamic_cast<HLODProxyObject*>(object);
}

HLODProxyObject::HLODProxyObject()
{
    // TODO(hendrikwang): Use event to gather HLOD objects instead of tick
    // Enable tick in editor
    mTickFunction->bCanEverTick = true;
    mTickFunction->bCanTickInEditorViewport = true;
}

void HLODProxyObject::InitializeComponents()
{
    GameObject::InitializeComponents();

    mHLODComponent = GetComponent<HLODComponent>();
    if (mHLODComponent == nullptr)
    {
        mHLODComponent = AddComponent<HLODComponent>();
    }
}

void HLODProxyObject::PostInitializeComponents()
{
    GameObject::PostInitializeComponents();
}

void HLODProxyObject::StartGame()
{
    GameObject::StartGame();

    OnHLODSettingChanged();
}

void HLODProxyObject::Tick(float deltaTime)
{
    QUICK_SCOPED_CPU_TIMING("HLODProxyObject::Tick")

    GameObject::Tick(deltaTime);

    if (!GetCrossGameWorld()->IsEntityAlive(GetObjectEntityID()))
    {
        return;
    }

    // Update children
    if (GetChildNum() > mHLODObjects.size())
    {
        for (int index = mHLODObjects.size(); index < GetChildNum(); index++)
        {
            auto* hlodObject = HLODObject::AsHLODObject(GetChild(index));
            if (hlodObject)
            {
                AddHLODObject(hlodObject);
            }
        }
    }

    // Update hierarchy
    const auto& setup = mHLODComponent->GetHLODSetting();
    bool hasOverrideMaterial = gResourceMgr.CheckFileByGuid(setup.OverrideBaseMaterial);
    for (const auto& hlodObject : mUninitializedHLODObjects)
    {
        if (hasOverrideMaterial)
        {
            hlodObject->SetOverrideMaterial(setup.OverrideBaseMaterial);
        }
        hlodObject->SetTransitionScreenSize(setup.Setting.TransitionScreenSize);
        if (setup.Setting.UseOverrideDrawDistance)
        {
            hlodObject->SetDrawDistance(setup.Setting.OverrideDrawDistance);
        }
        hlodObject->SetForcedLODLevel(mHLODComponent->GetForcedHLODModel());
    }
    mUninitializedHLODObjects.clear();

    {
        std::scoped_lock lock(mMutex);

        // NOTE: If we use TaskEventArray and WaitForCompletion, there is a possibility that the main thread will try to execute a long-time task during the waiting process,
        // which additionally lengthens the whole timespan.
        // Therefore, we only wait for tasks that are dispatched here
        // UPDATE: Use coroutine to only wait for sub-tasks dispatched by HLODObjects

        AddUpdateTasks(mHLODObjects.size());

        // Dispatch immediately
        {
            QUICK_SCOPED_CPU_TIMING("HLODProxyObject::Dispatch");
            for (const auto& hlodObject : mHLODObjects)
            {
                // Dispatch immediately
                cross::threading::Dispatch([this, hlodObject](auto) {
                    if (hlodObject)
                    {
                        hlodObject->Update();
                    }

                    RemoveUpdateTasks(1);
                });
            }

        }

        {
            QUICK_SCOPED_CPU_TIMING("HLODProxyObject::Wait");
            while (mWaitingUpdateTasks > 0)
            {
                ;
            }
        }
    }
}

void HLODProxyObject::AddHLODObject(HLODObject* hlodObject)
{
    std::scoped_lock lock(mMutex);

    mHLODObjects.emplace_back(hlodObject);
    mUninitializedHLODObjects.emplace_back(hlodObject);
}

void HLODProxyObject::RemoveHLODObject(HLODObject* hlodObject)
{
    std::scoped_lock lock(mMutex);

    auto it = std::find(mHLODObjects.begin(), mHLODObjects.end(), hlodObject);
    if (it != mHLODObjects.end())
    {
        mHLODObjects.erase(it);
    }

    auto initIt = std::find(mUninitializedHLODObjects.begin(), mUninitializedHLODObjects.end(), hlodObject);
    if (initIt != mUninitializedHLODObjects.end())
    {
        mUninitializedHLODObjects.erase(initIt);
    }
}

void HLODProxyObject::OnHLODSettingAssetChanged()
{
    mHLODResource = TypeCast<cross::resource::HLODResource>(gResourceMgr.GetResource(mHLODComponent->GetHLODSettingAsset().data()));
    if (!mHLODResource)
    {
        return;
    }

    // Override runtime settings from asset
    auto setup = mHLODResource->GetHLODSetting();
    mHLODComponent->SetHLODSetting(setup);
    OnHLODSettingChanged();
}

void HLODProxyObject::OnHLODSettingChanged()
{
    const auto& setup = mHLODComponent->GetHLODSetting();

    {
        std::scoped_lock lock(mMutex);

        cross::threading::ParallelFor(mHLODObjects.size(), [&](UInt32 index) {
            auto* hlodObject = mHLODObjects[index];

            // Material override
            hlodObject->SetOverrideMaterial(setup.OverrideBaseMaterial);

            // Transition screen size
            hlodObject->SetTransitionScreenSize(setup.Setting.TransitionScreenSize);

            // Draw distance
            if (setup.Setting.UseOverrideDrawDistance)
            {
                hlodObject->SetDrawDistance(setup.Setting.OverrideDrawDistance);
            }
        });
    }
}

void HLODProxyObject::OnForcedHLODModelChanged() 
{
    const auto& level = mHLODComponent->GetForcedHLODModel();

    {
        std::scoped_lock lock(mMutex);

        cross::threading::ParallelFor(mHLODObjects.size(), [&](UInt32 index) {
            auto* hlodObject = mHLODObjects[index];

            // Material override
            hlodObject->SetForcedLODLevel(level);
        });
    }
}

int HLODProxyObject::GetHLODLevel(HLODObject* hlodObject) const
{
    if (!hlodObject || !mHLODComponent)
    {
        return -1;
    }

    if (!mHLODResource)
    {
        return -1;
    }

    auto* assetDesc = mHLODResource->GetAssetDesc(hlodObject->GetName());
    if (!assetDesc)
    {
        return -1;
    }

    return assetDesc->HLODLevel;
}

void HLODProxyObject::AddUpdateTasks(int count)
{
    mWaitingUpdateTasks.fetch_add(count);
}

void HLODProxyObject::RemoveUpdateTasks(int count)
{
    mWaitingUpdateTasks.fetch_sub(count);
}

} // namespace cegf
