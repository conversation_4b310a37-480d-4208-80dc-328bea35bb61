#include "GameFramework/HLOD/HLODComponent.h"
#include "GameFramework/GameWorld.h"
#include "Runtime/GameWorld/ModelSystemG.h"
#include "GameFramework/HLOD/HLODProxy.h"

namespace cegf
{

HLODComponent::HLODComponent()
{
}

void HLODComponent::Serialize(SerializeNode& node, SerializeContext& context) const
{
    GameObjectComponent::Serialize(node, context);

    node["mHLODSettingAsset"] = mHLODSettingAsset;

    SerializeNode settingNode;
    mHLODSetting.Serialize(settingNode, context);
    node["mHLODSetting"] = std::move(settingNode);

    node["mForcedHLODModel"] = mForcedHLODModel;
}

bool HLODComponent::Deserialize(const DeserializeNode& in, SerializeContext& context)
{
    GameObjectComponent::Deserialize(in, context);
    if (auto t = in.HasMember("mHLODSettingAsset"))
    {
        mHLODSettingAsset = t->AsString();
    }

    // Override from asset
    auto hlodResource = TypeCast<cross::resource::HLODResource>(gResourceMgr.GetResource(mHLODSettingAsset.data()));
    if (hlodResource)
    {
        mHLODSetting = hlodResource->GetHLODSetting();
    }

    if (auto t = in.HasMember("mHLODSetting"))
    {
        mHLODSetting.Deserialize(*t, context);
    }
    
    if (auto t = in.HasMember("mForcedHLODModel"))
    {
        mForcedHLODModel = t->AsInt32();
    }

    return true;
}

void HLODComponent::StartGame()
{
    GameObjectComponent::StartGame();
}

std::string HLODComponent::GetHLODSettingAsset() const
{
    return mHLODSettingAsset;
}

void HLODComponent::SetHLODSettingAsset(const std::string& value)
{
    if (mHLODSettingAsset == value)
    {
        return;
    }

    mHLODSettingAsset = value;
    auto* hlodProxyObject = dynamic_cast<HLODProxyObject*>(GetOwner());
    if (hlodProxyObject)
    {
        hlodProxyObject->OnHLODSettingAssetChanged();
    }
}

cross::HLODSetup HLODComponent::GetHLODSetting() const
{
    return mHLODSetting;
}

void HLODComponent::SetHLODSetting(const cross::HLODSetup& value)
{
    if (mHLODSetting == value)
    {
        return;
    }

    mHLODSetting = value;
    auto* hlodProxyObject = dynamic_cast<HLODProxyObject*>(GetOwner());
    if (hlodProxyObject)
    {
        hlodProxyObject->OnHLODSettingChanged();
    }
}

int HLODComponent::GetForcedHLODModel() const
{
    return mForcedHLODModel;
}

void HLODComponent::SetForcedHLODModel(int value)
{
    mForcedHLODModel = value;
    auto* hlodProxyObject = dynamic_cast<HLODProxyObject*>(GetOwner());
    if (hlodProxyObject)
    {
        hlodProxyObject->OnForcedHLODModelChanged();
    }
}

} // namespace cegf
