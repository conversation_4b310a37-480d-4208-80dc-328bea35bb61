#include "GameFrameworkTest.h"
#include "GameFramework/GameEngine.h"
#include "GameFramework/Tests/TestCharacter.h"
#include "GameFramework/Tests/TestPlayerController.h"
#include "GameFramework/Camera/CameraObject.h"
#include "GameFramework/Components/ModelComponent.h"

namespace cegf
{
float GameFrameworkTest::OperationInternalTime = 10.0f;

GameFrameworkTest::GameFrameworkTest()
{
    Reset();
}

GameFrameworkTest::~GameFrameworkTest()
{
    Reset();
}

void GameFrameworkTest::Reset()
{
    mCurrentWorldId = 0;
    mCurrentWorld = nullptr;
    mTestGameObjectList.clear();
    mLastOperationTime = 0.0f;
    mStage = AutoTestStage::TestCameraSwitch;
}

void GameFrameworkTest::Tick(float deltaTime)
{
    if (mCurrentWorld && mCurrentWorld->IsLoaded())
    {
        ExecTest(deltaTime);

        for (GameObjectPtr& objPtr : mTestGameObjectList)
        {
            objPtr->Tick(deltaTime);
        }
    }
}

void GameFrameworkTest::OnNotifyWorldEvent(const cross::WorldLifeEventType& eventType, UInt32 worldId)
{
    if (eventType == cross::WorldLifeEventType::Create)
    {
        GameWorld* world = gGameEngine->GetGameWorld(worldId);
        if (world)
        {
            mCurrentWorld = world;
            mCurrentWorldId = worldId;
        }
    }
    else if (eventType == cross::WorldLifeEventType::Destroy)
    {
        if (worldId == mCurrentWorldId)
        {
            Reset();
        }
    }
}

void GameFrameworkTest::ExecTest(float deltaTime)
{
    if (mLastOperationTime > OperationInternalTime)
    {
        switch (mStage)
        {
            case AutoTestStage::TestCreateGameObject:
                CreateGameObject();
                mStage = AutoTestStage::TestAddComponent;
                break;
            case AutoTestStage::TestAddComponent:
                AddComponent();
                mStage = AutoTestStage::TestRemoveComponent;
                break;
            case AutoTestStage::TestRemoveComponent:
                RemoveComponent();
                mStage = AutoTestStage::TestDestroyGameObject;
                break;
            case AutoTestStage::TestDestroyGameObject:
                DestroyGameObject();
                mStage = AutoTestStage::TestCreateGameObject;
                break;
            case AutoTestStage::TestCameraSwitch:
                TestCamera();
                break;
        }
        mLastOperationTime = 0.f;
        mCurrentWorld->GetCrossGameWorld()->CallEditorUpdateHierarchyCallback();
    }
    else
    {
         mLastOperationTime += deltaTime;
    }
}

void GameFrameworkTest::CreateGameObject()
{
    //cross::Float3A location = createNum % 2 ? cross::Float3A(-351.f + 200 * createNum, 10.f, -1783.f) : cross::Float3A((-351.f - 200.0f * createNum), 10.f, -1783.f);
    cross::TRSVector3AType location(-551.f, 500.f, -1783.f); 
    // TestCharacterPtr newCharacter = TYPE_CAST_SHARD_PTR(TestCharacter, world->CreateGameObject<TestCharacter>(location, cross::QuaternionA(0.f, 0.f, 0.f, 0.0f)));
    TestCharacterPtr newCharacter = TYPE_CAST_SHARD_PTR(TestCharacter, mCurrentWorld->CreateGameObject("cegf::TestCharacter", location, cross::TRSQuaternionAType(0.f, 0.f, 0.f, 0.0f), cross::TRSVector3AType::One()));
    ModelComponent* modelComp = newCharacter->GetModelComponent();
    modelComp->SetModelAssetPath("EngineResource/Model/Cube.nda");
    mTestGameObjectList.emplace_back(newCharacter);
    LOG_DEBUG("test create game object {}", newCharacter->GetName());
}

void GameFrameworkTest::DestroyGameObject()
{
    if (!mTestGameObjectList.empty())
    {
        GameObject* obj = mTestGameObjectList[0].get(); 
        LOG_DEBUG("test destroy game object {}", obj->GetName());
        mCurrentWorld->DestroyGameObject(obj);
        mTestGameObjectList.erase(mTestGameObjectList.begin());
    }
}

void GameFrameworkTest::AddComponent()
{
    if (mTestGameObjectList.empty())
    {
        return;
    }
    GameObject* obj = mTestGameObjectList[0].get();
    obj->AddComponent<TestComponent>();
}

void GameFrameworkTest::RemoveComponent()
{
    if (mTestGameObjectList.empty())
    {
        return;
    }
    GameObject* obj = mTestGameObjectList[0].get();
    GameObjectComponent* comp = obj->GetComponent<TestComponent>();
    obj->RemoveComponent(comp);
}

void GameFrameworkTest::TestCamera()
{
    if (!mTestGameObjectList.empty())
    {
        return;
    }

    //create character
    CreateGameObject();
    TestCharacter* character = TYPE_CAST(TestCharacter*, mTestGameObjectList[0].get());

    //create Camera object
    cross::TRSVector3AType cameraLocation = character->GetWorldTranslation() + cross::TRSVector3AType(0.0f, 150.0f, -300.0f);
    cross::TRSQuaternionAType cameraRotation = character->GetWorldRotation();
    cross::TRSVector3AType cameraScale = cross::TRSVector3AType::One();
    CameraObjectPtr cameraObject = mCurrentWorld->CreateGameObject<CameraObject>(cameraLocation, cameraRotation, cameraScale);
    mTestGameObjectList.emplace_back(cameraObject);

    //create player controller
    TestPlayerControllerPtr newPlayerController = mCurrentWorld->CreateGameObject<TestPlayerController>(character->GetLocalTranslation(), character->GetLocalRotation(), character->GetLocalScale());
    newPlayerController->Possess(character);
    mTestGameObjectList.emplace_back(newPlayerController);
    newPlayerController->SetViewTarget(cameraObject.get());
}

}
 
