#pragma once
#include "GameFramework/Objects/ObjectBase.h"
#include "GameFramework/GameWorld.h"
#include "GameFramework/GameObjects/Character.h"
#include "GameFramework/Components/Component.h"
#include "Runtime/GameWorld/WorldSystemG.h"

namespace cegf
{

enum AutoTestStage
{
    None,
    TestCreateGameObject,
    TestAddComponent,
    TestRemoveComponent,
    TestDestroyGameObject,
    TestCameraSwitch
};

class GameFrameworkTest : public ObjectBase
{
public:
    GameFrameworkTest();

    virtual ~GameFrameworkTest();

    void Tick(float deltaTime);

    void OnNotifyWorldEvent(const cross::WorldLifeEventType& eventType, UInt32 worldId);

protected:
    void Reset();

    void ExecTest(float deltaTime);

    void CreateGameObject();

    void DestroyGameObject();

    void AddComponent();

    void RemoveComponent();

    void TestCamera(); 

protected:
    static float OperationInternalTime;

    GameWorld* mCurrentWorld;

    UInt32 mCurrentWorldId = 0;

    using GameObjectList = std::vector<GameObjectPtr>;
    GameObjectList mTestGameObjectList;

    AutoTestStage mStage;

    float mLastOperationTime = 0.f;

    bool mIsPlayInPIE = false;
};

using GameFrameworkTestPtr = std::shared_ptr<GameFrameworkTest>;

}   // namespace cegf