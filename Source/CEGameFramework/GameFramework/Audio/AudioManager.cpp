#include "AudioManager.h"
#include <Runtime/Audio/AudioEngine.h>

namespace cegf {
cross::AudioEngine::ID AudioManager::GetID(std::string const& str) const
{
    return cross::EngineGlobal::Inst().GetAudioEngine()->StringToID(str.c_str());
}

bool AudioManager::LanguageSet(std::string const& language_name) const
{
    return 0 == cross::EngineGlobal::Inst().GetAudioEngine()->LanguageSet(language_name.c_str());
}

bool AudioManager::RootpathSet(std::string const& rootpath) const
{
    return 0 == cross::EngineGlobal::Inst().GetAudioEngine()->RootpathSet(rootpath.c_str());
}

bool AudioManager::Load(std::string const& bank_name) const
{
    return 0 == cross::EngineGlobal::Inst().GetAudioEngine()->BankLoad(bank_name.c_str());
}

bool AudioManager::Unload(std::string const& bank_name) const
{
    return 0 == cross::EngineGlobal::Inst().GetAudioEngine()->BankUnload(bank_name.c_str());
}

bool AudioManager::Prepare(std::string const& event_name) const
{
    cross::AudioEngine* const audio_engine = cross::EngineGlobal::Inst().GetAudioEngine();
    return Prepare(audio_engine->StringToID(event_name.c_str()));
}

bool AudioManager::Prepare(cross::AudioEngine::EventID const & event_id) const
{
    cross::AudioEngine* const audio_engine = cross::EngineGlobal::Inst().GetAudioEngine();
    if (audio_engine->EventPrepare(&event_id, 1)) return false;
    return true;
}

bool AudioManager::Unprepare(std::string const& event_name) const
{
    cross::AudioEngine* const audio_engine = cross::EngineGlobal::Inst().GetAudioEngine();
    return Unprepare(audio_engine->StringToID(event_name.c_str()));
}

bool AudioManager::Unprepare(cross::AudioEngine::EventID const & event_id) const
{
    cross::AudioEngine* const audio_engine = cross::EngineGlobal::Inst().GetAudioEngine();
    if (audio_engine->EventUnprepare(&event_id, 1)) return false;
    return true;
}
bool AudioManager::StateSet(std::string const& state_name, std::string const& state_value) const
{
    cross::AudioEngine* const audio_engine = cross::EngineGlobal::Inst().GetAudioEngine();
    return 0 == audio_engine->StateSet(audio_engine->StringToID(state_name.c_str()), audio_engine->StringToID(state_value.c_str()));
}

bool AudioManager::StateSet(cross::AudioEngine::StateID const& state_id, cross::AudioEngine::StateValueID const& value_id) const 
{
    cross::AudioEngine* const audio_engine = cross::EngineGlobal::Inst().GetAudioEngine();
    return 0 == audio_engine->StateSet(state_id, value_id);
}

bool AudioManager::Set(std::string const& param_name, float const value, int const duration_ms, AudioCurveInterpolation const curve) const
{
    cross::AudioEngine* const audio_engine = cross::EngineGlobal::Inst().GetAudioEngine();
    return Set(audio_engine->StringToID(param_name.c_str()), value, duration_ms, curve);
}

bool AudioManager::Set(cross::AudioEngine::ParamID const & param_id, float const value, int const duration_ms, AudioCurveInterpolation const curve) const
{
    cross::AudioEngine* const audio_engine = cross::EngineGlobal::Inst().GetAudioEngine();
    return 0 == audio_engine->ParamSet(param_id, value, duration_ms, Convert(curve));
}

cross::AudioEngine::Index AudioManager::OutputGetNum() const
{
    cross::AudioEngine* const audio_engine = cross::EngineGlobal::Inst().GetAudioEngine();
    return audio_engine->OutputGetNum();
}

cross::AudioEngine::Index AudioManager::OutputGetDefault() const
{
    cross::AudioEngine* const audio_engine = cross::EngineGlobal::Inst().GetAudioEngine();
    return audio_engine->OutputGetDefault();
}
    
std::string AudioManager::OutputGetName(cross::AudioEngine::Index const output_index) const
{
    cross::AudioEngine* const audio_engine = cross::EngineGlobal::Inst().GetAudioEngine();
    std::string name;
    audio_engine->OutputGetName(output_index, name);
    return name;
}
    
bool AudioManager::OutputAdd(cross::AudioEngine::Index const output_index, cross::AudioEngine::ObjectID const object_id) const
{
    cross::AudioEngine* const audio_engine = cross::EngineGlobal::Inst().GetAudioEngine();
    return 0 == audio_engine->OutputAdd(output_index, object_id);
}
    
bool AudioManager::OutputRemove(cross::AudioEngine::Index const output_index) const
{
    cross::AudioEngine* const audio_engine = cross::EngineGlobal::Inst().GetAudioEngine();
    return 0 == audio_engine->OutputRemove(output_index);
}
    
bool AudioManager::OutputReplace(cross::AudioEngine::Index const output_index_src, cross::AudioEngine::Index const output_index_dest) const
{
    cross::AudioEngine* const audio_engine = cross::EngineGlobal::Inst().GetAudioEngine();
    return 0 == audio_engine->OutputReplace(output_index_src, output_index_dest);
}

bool AudioManager::OutputSetVolume(cross::AudioEngine::Index const output_index, float const volume) const
{
    cross::AudioEngine* const audio_engine = cross::EngineGlobal::Inst().GetAudioEngine();
    return 0 == audio_engine->OutputSetVolume(output_index, volume);
}

bool AudioManager::SetOutside(std::string const & reverb_aux_bus, float const reverb_level, float const aux_send_level_to_self) const
{
    cross::AudioEngine* const audio_engine = cross::EngineGlobal::Inst().GetAudioEngine();
    return 0 == audio_engine->SpatialSetOutside(reverb_aux_bus, reverb_level, aux_send_level_to_self);
}

}   // namespace cegf