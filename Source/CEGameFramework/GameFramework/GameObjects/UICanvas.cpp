#include "GameFramework/GameObjects/UICanvas.h"

namespace cegf
{
UICanvas::UICanvas()
{
    mTickFunction->bCanEverTick = true;
}

UICanvas::~UICanvas()
{
    CanvasComponent = nullptr;
    GameObject::Destroyed();
}
void UICanvas::Serialize(SerializeNode& node, SerializeContext& context) const {
    GameObject::Serialize(node, context);
}
bool UICanvas::Deserialize(const DeserializeNode& in, SerializeContext& context) 
{
    GameObject::Deserialize(in, context);
    return true;
}
void UICanvas::InitializeComponents()
{
    GameObject::InitializeComponents();
    CanvasComponent = AddComponent<UIComponent>();
}
void UICanvas::PostInitializeComponents()
{
    GameObject::PostInitializeComponents();
    CanvasComponent->Init();
}
void UICanvas::Tick(float deltaTime)
{
    GameObject::Tick(deltaTime);
}
}
