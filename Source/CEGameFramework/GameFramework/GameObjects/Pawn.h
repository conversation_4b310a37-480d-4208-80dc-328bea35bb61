#pragma once
#include "GameFramework/GameObjects/GameObject.h"
#include "GameFramework/Components/PawnMovementComponent.h"
#include "CEGameplay/CharacterMovementSystemG.h"
#include "CEGameplay/CharacterMovementComponent.h"

namespace cegf
{
class Controller;
class PawnMovementComponent;

class GAMEFRAMEWORK_API CEMeta(Reflect, WorkflowType, Cli, Puerts) Pawn : public GameObject
{
public:
    CEGameplayInternal() 
    StaticMetaClassName(Pawn)
    Pawn();

    virtual ~Pawn();

    virtual void Destroyed() override;

    virtual void InitializeComponents() override;

    virtual void PostInitializeComponents() override;

    virtual void FinishCreating() override;
    virtual void StartGame() override;
    virtual void EndGame() override;
    
    void SetController(Controller* controller) { mController = controller; }
    
    Controller* GetController() const { return mController; }

    //returns Pawn's eye location
    virtual cross::TRSVector3AType GetPawnViewLocation() const;

    //get the view rotation of the Pawn (direction they are looking, normally Controller->ControlRotation).
    virtual cross::TRSQuaternionAType GetPawnViewRotation() const; 

    virtual void PossessedByController(Controller* newController);

    virtual void UnPossessedByController();

    //Add input (affecting Pitch) to the Controller's ControlRotation
    virtual void AddControllerPitchInput(float val);

    //Add input (affecting Yaw) to the Controller's ControlRotation
    virtual void AddControllerYawInput(float val);

    //Add input (affecting Roll) to the Controller's ControlRotation
    virtual void AddControllerRollInput(float val);

    bool IsMoveInputIgnored() const
    {
        //return mController != nullptr && mController->IsMoveInputIgnored();
        return false;
    }

    virtual PawnMovementComponent* GetMovementComponent() const;

	CEFunction(WorkflowExecutable)
    virtual void AddMovementInput(cross::Float3 worldDirection, float ScaleValue = 1.0f, bool bForce = false);

    CEFunction(WorkflowPure)
        virtual cross::Float3 GetF3_X() const
    {
            return {1.0f, 0.0f, 0.0f};
    }
    CEFunction(WorkflowPure)
    virtual cross::Float3 GetF3_Y() const
    {
        return {0.0f, 1.0f, 0.0f};
    }
    CEFunction(WorkflowPure)
    virtual cross::Float3 GetF3_Z() const
    {
        return {0.0f, 0.0f, 1.0f};
    }
    CEFunction(WorkflowPure)
    virtual cross::Float3 GenVec(float x, float y, float z) const
    {
        return {x, y, z};
    }

    void PostEditChangeProperty(PropertyChangedEvent & PropertyChangedEvent) override;

private:
    void SpawnDefaultController();
    void DetachFromController();

protected:
    class InputComponent* mInputComponent;
    Controller* mController = nullptr;

    float mBaseEyeHeight = 0.0f;
};

}
