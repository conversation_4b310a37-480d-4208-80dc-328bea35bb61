#include "GameFramework/Camera/CameraObject.h"
#include "GameFramework/Camera/CameraComponent.h"

namespace cegf
{
    
 CameraObject::CameraObject()
 {
     mTickFunction->bCanEverTick = true;
 }

 CameraObject::~CameraObject()
 {
    mCameraComponent = nullptr;
 }

void CameraObject::InitializeComponents()
{
    Pawn::InitializeComponents();
    mCameraComponent = TYPE_CAST(CameraComponent*, AddComponent<CameraComponent>());
}

}
