#pragma once

#include "visual/virtual_machine/vmcore_define.hpp"

namespace gbf {
namespace machine {

class VIRTUAL_MACHINE_API IInstruction {
  friend class VCoroutine;
 public:
  IInstruction(RunScope* run_scope);
  virtual ~IInstruction();

  const VObject& GetSourceObject() const noexcept;

  MemoryScopePtr GetLocalMemoryScope() noexcept;

  NamedMemoryScopePtr GetGlobalMemoryScope() noexcept;

  RunScope* GetRunScope() noexcept { return m_run_scope; }

 protected:
  virtual VMRunStepStatus RunImpl(VCoroutine* coroutine_) = 0;

 protected:
  RunScope* m_run_scope;
};

}  // namespace machine
}  // namespace gbf
