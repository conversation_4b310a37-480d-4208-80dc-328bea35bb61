#include "visual/virtual_machine/runtime/expr/iterm.hpp"
#include "reflection/builder/class_builder.hpp"
#include "reflection/runtime/cxx_runtime.hpp"

////// #include "visual/virtual_machine/extend/vmvalue.h"
////#include "visual/virtual_machine/meta/imetadatastack.h"
////#include "visual/virtual_machine/meta/metafieldinfo.h"
////#include "visual/virtual_machine/meta/metamethodinfo.h"
////#include "visual/virtual_machine/meta/metaparameterinfo.h"

namespace gbf {
namespace machine {

ITerm::ITerm() {}

ITerm::~ITerm() {}

bool ITerm::CanUseAsLeftValue() const noexcept {
  // now only support field as left value
  return term_type_ == VMTermType::Field;
}

void ITerm::SerializeTo<PERSON>son(IVMStreamWriter& writer) {
  const auto* meta = this->__rtti_meta();
  assert(meta && "Must has a register meta here!");
  writer.AddStringProperty("class_type", meta->name());
  writer.AddIntProperty("term_type", (int)term_type_);
}

void ITerm::DeserializeFromJson(IVMStreamReadNode& node) {
  int term_type = node.GetIntProperty("term_type");
  (void)term_type;
  assert(term_type == (int)term_type_);
}

std::shared_ptr<ITerm> ITerm::CreateFromJson(IVMStreamReadNode& node) {
  auto type_name = node.GetStringProperty("class_type");
  auto* meta = __type_of_name(type_name);
  assert(meta && "Must has a register meta here!");

  auto uo = reflection::cxx::CreateShared(*meta);
  std::shared_ptr<ITerm> termptr;
  reflection::__unbox(uo, termptr);

  if (termptr) {
    termptr->DeserializeFromJson(node);
  }
  
  return termptr;
}

}  // namespace machine
}  // namespace gbf
