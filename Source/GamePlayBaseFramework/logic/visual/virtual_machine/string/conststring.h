#pragma once

#include <cassert>
#include <cstring>
#include <iostream>
#include <vector>

#include "visual/virtual_machine/string/cachedstring.h"
#include "visual/virtual_machine/vmcore_define.hpp"

namespace gbf {
namespace machine {
//-------------------------------------------------------------------------------------------
class VIRTUAL_MACHINE_API ConstString {
 public:
  using value_type = char;

  // map hasher
  struct const_string_hasher {
    size_t operator()(const ConstString& str) const { return (size_t)str.hash_code(); }
  };

  struct const_string_abc_less {
    bool operator()(const ConstString& l, const ConstString& r) const { return strcmp(l.c_str(), r.c_str()) < 0; }
  };

  ////static const ConstString& EmptyConstString();

  static const size_t npos = static_cast<size_t>(-1);

  static ConstString EMPTY_STRING;

 public:
  // use global string cache
  ConstString();

  ConstString(const char* raw_str);

  ConstString(const char* raw_str, size_t offset, size_t count);

  ConstString(const char* raw_str, size_t raw_len, size_t offset, size_t count);

  ConstString(const std::string& str);

  ////ConstString(const char* raw_str, CacheStringManager* parentCache);

  ConstString(const ConstString& r);

  ~ConstString();

  ConstString& operator=(const ConstString& r);

  ConstString& operator=(const char* s);

  ConstString& operator=(const std::string& s);

  char at(size_t pos) const {
    assert(pos < length());

    return c_str()[pos];
  }

  char operator[](size_t pos) const { return at(pos); }

  const char* c_str() const { return m_cached_str->c_str(); }

  size_t length() const { return m_cached_str->length(); }

  size_t size() const { return m_cached_str->length(); }

  uint64_t hash_code() const { return m_cached_str->hash_code(); }

  bool operator<(const ConstString& r) const { return hash_code() < r.hash_code(); }

  bool operator==(const ConstString& r) const { return hash_code() == r.hash_code(); }

  bool operator!=(const ConstString& r) const { return hash_code() != r.hash_code(); }

  operator std::string() const { return std::string(m_cached_str->c_str(), m_cached_str->length()); }

  inline friend std::ostream& operator<<(std::ostream& o, const ConstString& c) {
    o << c.c_str();
    return o;
  }

  void clear();

  bool empty() const { return length() == 0; }

  size_t find_first_of(const ConstString& rights, size_t offset = 0) const noexcept;

  size_t find_first_not_of(const ConstString& rights, size_t offset = 0) const noexcept;

  size_t find_last_of(const ConstString& rights, size_t offset = npos) const noexcept;

  size_t find_last_not_of(const ConstString& rights, size_t offset = npos) const noexcept;

  size_t find_first_of(char _Ch, size_t offset = 0) const noexcept;

  size_t find_first_not_of(char _Ch, size_t offset = 0) const noexcept;

  size_t find_last_of(char _Ch, size_t offset = npos) const noexcept;

  size_t find_last_not_of(char _Ch, size_t offset = npos) const noexcept;

  ConstString substr(const size_t _Off = 0, const size_t _Count = npos) const noexcept;

  size_t find(const char _Ch, const size_t _Start_at = 0) const noexcept;

  size_t find(const ConstString& valstr, const size_t _Start_at = 0) const noexcept;

  ConstString operator+(const ConstString& _Right) const;

  ConstString operator+(const char* _Right) const;

  ConstString operator+(char _Right) const;

  ConstString& operator+=(const ConstString& _Right);

  ConstString& operator+=(const char* _Right);

  ConstString& operator+=(char _Right);

  const CachedString* get_cached_str() const { return m_cached_str; }

 private:
  void free();

  static const char* string_find(const char* str, size_t strlen, char val) noexcept {
    for (size_t i = 0; i < strlen; i++) {
      if (str[i] == val) {
        return &str[i];
      }
    }
    return nullptr;
  }

  static const char* string_find_str(const char* str, size_t strlen, const char* valstr) noexcept { return strstr(str, valstr); }

  static const char* string_find_not_of(const char* str, size_t strlen, char val) noexcept {
    for (size_t i = 0; i < strlen; i++) {
      if (str[i] != val) {
        return &str[i];
      }
    }
    return nullptr;
  }

 private:
  CachedString* m_cached_str;
};

using ConstStringArray = std::vector<ConstString>;

////ConstStringArray CORE_API StringVectorToConstStringArray(const StringVector& strVec);
////StringVector CORE_API ConstStringArrayToStringVector(const ConstStringArray& strArray);
}  // namespace machine
}  // namespace gbf

namespace std {
template <>
struct hash<gbf::machine::ConstString> {
  size_t operator()(gbf::machine::ConstString const& str) const noexcept { return static_cast<size_t>(str.hash_code()); }
};
}  // namespace std
