#pragma once

#include "visual/blueprint/blueprint_define.hpp"
#include "visual/blueprint/details/node/blueprint_action_node.h"
#include "visual/virtual_machine/runtime/vcoroutine.hpp"

namespace gbf { namespace logic {
    class UBlueprintGraphFunction;

    class BLUEPRINT_API CEMeta(Reflect) UBlueprintFunctionReturn : public UBlueprintActionNode
    {
        friend class UBlueprintGraphFunction;

    public:
        CEMeta(Reflect) 
        UBlueprintFunctionReturn()
            : UBlueprintFunctionReturn(nullptr){};
        UBlueprintFunctionReturn(UBlueprintGraphFunction* sf);

        void SetSequenceFunction(UBlueprintGraphFunction* sf);

        bool CanDeleteByUser() const noexcept override
        {
            return false;
        }

    protected:
        void InitializeSlotsImpl();
        UBlueprintActionNode::ProcessingInfo RtActivateLogicImpl(machine::VCoroutine* coroutine_, UBlueprintExecSlot* slot) override;

    protected:
        UBlueprintGraphFunction* m_function;
    };
}}   // namespace gbf::logic
