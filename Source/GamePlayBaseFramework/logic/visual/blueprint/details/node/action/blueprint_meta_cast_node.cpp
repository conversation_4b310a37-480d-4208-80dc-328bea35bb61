#include "visual/blueprint/details/node/action/blueprint_meta_cast_node.h"

#include "visual/blueprint/details/node/blueprint_data_slot.h"

#include "core/utils/string_util.h"
#include "visual/virtual_machine/utils/vvalue_util.hpp"

namespace gbf { namespace logic {
    UBlueprintMetaCastNode::UBlueprintMetaCastNode()
        : UBlueprintMetaCastNode("gbf::reflection::RttiBase")
    {}

    UBlueprintMetaCastNode::UBlueprintMetaCastNode(const std::string& to_type_name)
        : UBlueprintActionNode(BlueprintSlotAvailableFlag::All, "MetaCast")
    {
        Init(to_type_name);
    }

    const std::string& UBlueprintMetaCastNode::get_to_type() const
    {
        return m_ToType;
    }

    void UBlueprintMetaCastNode::SerializeTo<PERSON>son(machine::IVMStreamWriter& writer)
    {
        UBlueprintActionNode::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(writer);
        writer.AddStringProperty("to_type", m_ToType);
    }

    void UBlueprintMetaCastNode::DeserializeFields(machine::IVMStreamReadNode& node)
    {
        UBlueprintActionNode::DeserializeFields(node);
        auto temp_str = node.GetStringProperty("to_type");
        Init(temp_str);
    }

    bool UBlueprintMetaCastNode::Init(const std::string& to_type_name)
    {
        if (to_type_name == m_ToType)
        {
            return true;
        }
        auto to_class = reflection::query_meta_class_by_name(to_type_name);
        if (!to_class)
        {
            return false;
        }
        if (to_class->GetBaseOffset(*reflection::query_meta_class<reflection::RttiBase>()) == -1)
        {
            return false;
        }
        m_ToType = to_type_name;
        m_ToClass = to_class;
        this->m_title = StringUtil::Format("CastTo::%s", to_type_name.c_str());

        m_in_variable_slots.clear();
        m_out_variable_slots.clear();
        m_out_instruction_slots.clear();
        m_in_instruction_slots.clear();

        // in slots
        AddExecSlotC((uint32_t)CustomSlotId::In, "");
        AddDataSlotC((uint32_t)CustomSlotId::InVar, machine::VValueKind::kUser, "InObject");

        // out slots
        AddExecSlotC((uint32_t)CustomSlotId::OutTrue, "CastTrue");
        AddExecSlotC((uint32_t)CustomSlotId::OutFalse, "CastFalse");

        AddDataSlotC((uint32_t)CustomSlotId::InVar, machine::VValueKind::kUser, to_type_name);
        return true;
    }

    UBlueprintActionNode::ProcessingInfo UBlueprintMetaCastNode::RtActivateLogicImpl(machine::VCoroutine* coroutine_, UBlueprintExecSlot* slot)
    {
        ProcessingInfo info;
        info.State = LogicState::Ok;

        auto* local = coroutine_->GetCurrentMemoryScope();
        auto* global = coroutine_->GetGlobalMemoryScope();
        auto input_var = RtTryGetDataSlotValue<reflection::RttiBase*>(NVM_GET_C_LINK_DIR(CustomSlotId::InVar), NVM_GET_C_SLOT_INDEX(CustomSlotId::InVar), local, global, nullptr);
        if (!input_var)
        {
            RtActiveOuputLink(coroutine_, NVM_GET_C_SLOT_INDEX(CustomSlotId::OutFalse));
        }
        else
        {
            if (input_var->__rtti_meta_class_->GetBaseOffset(*m_ToClass) == -1)
            {
                RtTrySetDataSlotValue<reflection::RttiBase*>(NVM_GET_C_LINK_DIR(CustomSlotId::InVar), NVM_GET_C_SLOT_INDEX(CustomSlotId::InVar), local, global, nullptr);
                RtActiveOuputLink(coroutine_, NVM_GET_C_SLOT_INDEX(CustomSlotId::OutFalse));
            }
            else
            {
                RtTrySetDataSlotValue<reflection::RttiBase*>(NVM_GET_C_LINK_DIR(CustomSlotId::InVar), NVM_GET_C_SLOT_INDEX(CustomSlotId::InVar), local, global, input_var);
                RtActiveOuputLink(coroutine_, NVM_GET_C_SLOT_INDEX(CustomSlotId::OutTrue));
            }
        }

        return info;
    }

}}   // namespace gbf::logic