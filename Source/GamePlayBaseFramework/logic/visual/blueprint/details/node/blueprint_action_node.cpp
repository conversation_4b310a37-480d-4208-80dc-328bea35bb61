#include "visual/blueprint/details/node/blueprint_action_node.h"

namespace gbf { namespace logic {

    UBlueprintActionNode::UBlueprintActionNode(BlueprintSlotAvailableFlag flag, std::string_view title)
        : UBlueprintNode(BlueprintNodeType::Action, flag, title)
    {}

    UBlueprintActionNode::ProcessingInfo UBlueprintActionNode::RtActivate(machine::VCoroutine* coroutine_, UBlueprintExecSlot* slot)
    {
        return RtActivateLogicImpl(coroutine_, slot);
        ////return m_processing_state;
    }

}}   // namespace gbf::logic
