#pragma once

#if 0

#include "visual/blueprint/blueprint_define.hpp"

#include "visual/blueprint/details/node/expression/blueprint_lazy_evaluation_node.h"

#include "visual/virtual_machine/runtime/memory_scope.hpp"
#include "visual/virtual_machine/runtime/named_memory_scope.hpp"

namespace gbf {
namespace logic {

class BLUEPRINT_API NGXRAFieldNode : public NGLazyEvaluationNode {
 public:
  NGXRAFieldNode();
  NGXRAFieldNode(machine::VValueKind type);

  void set_value_type(machine::VValueKind type);
  machine::VValueKind get_value_type();
  static const std::vector<machine::VValueKind>& get_candidate_types();

 protected:
  void init(machine::VValueKind type);
  bool Evaluate(machine::MemoryScope* local, machine::NamedMemoryScope* global) override;

 protected:
  static std::vector<machine::VValueKind> CandidateTypes;
};

class BLUEPRINT_API NGYGBFieldNode : public NGLazyEvaluationNode {
 public:
  NGYGBFieldNode();
  NGYGBFieldNode(machine::VValueKind type);

  void set_value_type(machine::VValueKind type);
  machine::VValueKind get_value_type();
  static const std::vector<machine::VValueKind>& get_candidate_types();

 protected:
  void init(machine::VValueKind type);
  bool Evaluate(machine::MemoryScope* local, machine::NamedMemoryScope* global) override;

 protected:
  static std::vector<machine::VValueKind> CandidateTypes;
};

class BLUEPRINT_API NGZBCFieldNode : public NGLazyEvaluationNode {
 public:
  NGZBCFieldNode();
  NGZBCFieldNode(machine::VValueKind type);

  void set_value_type(machine::VValueKind type);
  machine::VValueKind get_value_type();
  static const std::vector<machine::VValueKind>& get_candidate_types();

 protected:
  void init(machine::VValueKind type);
  bool Evaluate(machine::MemoryScope* local, machine::NamedMemoryScope* global) override;

 protected:
  static std::vector<machine::VValueKind> CandidateTypes;
};

class BLUEPRINT_API NGWAWFieldNode : public NGLazyEvaluationNode {
 public:
  NGWAWFieldNode();
  NGWAWFieldNode(machine::VValueKind type);

  void set_value_type(machine::VValueKind type);
  machine::VValueKind get_value_type();
  static const std::vector<machine::VValueKind>& get_candidate_types();

 protected:
  void init(machine::VValueKind type);
  bool Evaluate(machine::MemoryScope* local, machine::NamedMemoryScope* global) override;

 protected:
  static std::vector<machine::VValueKind> CandidateTypes;
};
}  // namespace logic
}  // namespace gbf

#endif

