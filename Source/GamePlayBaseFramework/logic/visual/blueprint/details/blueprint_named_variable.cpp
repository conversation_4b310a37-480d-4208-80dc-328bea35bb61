
#include "visual/blueprint/details/blueprint_named_variable.h"
#include "visual/blueprint/details/blueprint_context.h"
#include "core/utils/string_util.h"
#include "visual/virtual_machine/utils/vvalue_util.hpp"

namespace gbf { namespace logic {
    UBlueprintNamedVariable::UBlueprintNamedVariable(const std::string& name, machine::VValueKind value_type)
        : UBlueprintNamedVariable(name, machine::VValueUtil::CreateDefaultValueByType(value_type), value_type)
    {}

    UBlueprintNamedVariable::UBlueprintNamedVariable(const std::string& name, const std::string& type_name)
        : UBlueprintNamedVariable(name, reflection::detail::ValueKindFromString(type_name))
    {}

    std::string UBlueprintNamedVariable::get_event_name(const std::string& variable_name)
    {
        return StringUtil::Format("OnVariableChanged[%s]", variable_name.data());
    }

    void UBlueprintNamedVariable::set_name(const std::string& new_name)
    {
        m_name = new_name;
        m_name_changed_event_name = get_event_name(m_name);
    }

    UBlueprintNamedVariable::UBlueprintNamedVariable(const std::string& name, const machine::VValue& value, machine::VValueKind value_kind)
        : m_name(name)
        , m_name_changed_event_name(get_event_name(name))
        , m_kind(value_kind)
        , m_default_variable(value)
    {
        _update_type_id();

        if (m_kind == machine::VValueKind::kUser)
        {
            if (m_default_variable == machine::VValue::nothing)
            {
                m_default_variable = machine::VValue(std::string("gbf::reflection::RttiBase"));
            }
        }
    }

    void UBlueprintNamedVariable::SerializeToJson(machine::IVMStreamWriter& writer)
    {
        writer.AddStringProperty("name", m_name);
        writer.AddStringProperty("description", m_desciption);
        writer.AddIntProperty("kind", (int)m_kind);
        writer.AddIntProperty("category", (int)m_category);
        writer.AddUint64Property("typeid", m_type_id);

        writer.StartObject("value");
        if (m_kind == machine::VValueKind::kUser)
        {
            if(auto* converter = machine::VValueConverterFactory::Get(m_type_id);converter)
                converter->Serialize(m_default_variable , writer);
        }
        else
        {
            machine::VValueUtil::SaveVValueToJsonMap(m_default_variable, writer);
        }
        writer.EndObject();
    }

    void UBlueprintNamedVariable::DeserializeFromJson(machine::IVMStreamReadNode& node)
    {
        m_desciption = node.GetStringProperty("description");
        m_kind = (machine::VValueKind)node.GetIntProperty("kind");
        m_category = (VariableCategory)node.GetIntProperty("category");
        m_type_id = node.GetUint64Property("typeid");

        auto value_node = node.GetNode("value");
        if (value_node)
        {
            m_default_variable = machine::VValueUtil::CreateVValueFromJsonMap(*value_node);

            if (m_kind == reflection::ValueKind::kUser)
            {
                if (auto* converter = machine::VValueConverterFactory::Get(m_type_id); converter)
                    m_default_variable = converter->Deserialize(*value_node);
            }
        }
    }

    void UBlueprintNamedVariable::AddListener(ListenerType slot)
    {
        m_variable_listeners.push_back(std::move(slot));
    }

    void UBlueprintNamedVariable::_update_type_id()
    {
        if (m_kind <= machine::VValueKind::kString)
        {
            m_type_id = machine::VValueUtil::ValueKindToTypeId(m_kind);
        }
        else
        {
            m_type_id = UINT64_MAX;
        }
    } 

    void UBlueprintNamedVariable::set_type_id_for_user_obj(reflection::TypeId id)
    {
        if (m_type_id == id)
        {
            return;
        }

        m_type_id = id;

        if (m_kind == reflection::ValueKind::kUser)
        {
            if (auto* converter = machine::VValueConverterFactory::Get(m_type_id); converter)
            {
                auto meta = gbf::reflection::query_meta_class_by_id(id);
                if (meta)
                {
                    gbf::reflection::UserObject holder_obj = gbf::reflection::make_user_object_by_class(meta);
                    gbf::reflection::UserObject wrapper_obj = gbf::reflection::make_raw_pointer_user_object(holder_obj.GetPointer(), std::move(holder_obj));
                    m_default_variable = wrapper_obj;
                }
            }
            else
            {
                m_default_variable = reflection::make_value("converter not found");
            }
        }
    }
    void UBlueprintNamedVariable::Notify(UBlueprintContext* context, const machine::VValue& value)
    {
        // for (auto const& listener : m_variable_listeners)
        //{
        //	listener(context, value);
        // }

        // TODO ... better performance for variable changed event
        context->OnGlobalEvent(get_event_name(), value);
        context->Update();
    }

    void UBlueprintNamedVariable::set_default_value(const machine::VValue& inVal)
    {
        if (m_kind == inVal.kind())
        {
            m_default_variable = inVal;
        }
        else if (m_kind != reflection::ValueKind::kUser)
        {
            auto new_val = machine::VValueUtil::PromoteTo(inVal, m_kind);
            if (new_val != machine::VValue::nothing)
            {
                m_default_variable = new_val;
            }
        }
    }
    void UBlueprintNamedVariable::set_value_with_type(const machine::VValue& value, machine::VValueKind value_kind)
    {
        m_default_variable = value;
        m_kind = value_kind;
        _update_type_id();
    }
}}   // namespace gbf::logic
