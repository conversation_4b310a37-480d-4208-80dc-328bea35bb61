#include "visual/blueprint/details/blueprint_graph_group.h"
#include "visual/blueprint/blueprint.hpp"
#include "visual/blueprint/serialize_impl/jsonvmstreamreader.h"
#include "visual/blueprint/serialize_impl/jsonvmstreamwriter.h"
#include "reflection/objects/make_user_object.hpp"
#include "visual/virtual_machine/utils/vvalue_util.hpp"
#include "core/imodules/ilog_module.h"

namespace gbf { namespace logic {
    UBlueprintGraphGroup::UBlueprintGraphGroup(const std::string& group_name, UBlueprintWorkspace* parent_workspace)
        : m_group_name(group_name)
        , m_parent_workspace(parent_workspace)
    {}

    UBlueprintGraphGroup::~UBlueprintGraphGroup() {}

    UBlueprintGraph* UBlueprintGraphGroup::GetGraphByIndex(size_t index)
    {
        assert(index < m_graph_array.size());
        return m_graph_array[index].get();
    }

    UBlueprintGraph* UBlueprintGraphGroup::GetGraphByName(const std::string& name)
    {
        auto iter = std::find_if(m_graph_array.cbegin(), m_graph_array.cend(), [&name](UBlueprintGraphPtr graph) { return name == graph->name(); });
        if (iter != m_graph_array.cend())
            return (*iter).get();
        return nullptr;
    }

    UBlueprintGraphFunction* UBlueprintGraphGroup::GetFunctionByIndex(size_t index)
    {
        assert(index < m_function_array.size());
        return m_function_array[index].get();
    }

    UBlueprintGraphFunction* UBlueprintGraphGroup::GetFunctionByName(const std::string& name)
    {
        auto itr = std::find_if(m_function_array.cbegin(), m_function_array.cend(), [&name](UBlueprintGraphFunctionPtr item) { return name == item->name(); });
        if (itr != m_function_array.cend())
            return (*itr).get();
        return nullptr;
    }

    UBlueprintNamedVariable* UBlueprintGraphGroup::GetNamedVariableByIndex(size_t index)
    {
        assert(index < m_variable_array.size());
        return m_variable_array[index].get();
    }

    UBlueprintNamedVariable* UBlueprintGraphGroup::GetNamedVariableByName(const std::string& name)
    {
        auto itr = std::find_if(m_variable_array.cbegin(), m_variable_array.cend(), [&name](UBlueprintNamedVariablePtr item) { return name == item->get_name(); });
        if (itr != m_variable_array.cend())
            return (*itr).get();
        return nullptr;
    }

    UBlueprintEvent* UBlueprintGraphGroup::GetEventByIndex(size_t index)
    {
        assert(index < m_all_ng_events.size());
        return m_all_ng_events[index].get();
    }

    UBlueprintEvent* UBlueprintGraphGroup::GetEventByName(const std::string& name)
    {
        auto itr = std::find_if(m_all_ng_events.cbegin(), m_all_ng_events.cend(), [&name](UBlueprintEventPtr item) { return name == item->get_event_name(); });
        if (itr != m_all_ng_events.cend())
            return (*itr).get();
        return nullptr;
    }

    uint32_t UBlueprintGraphGroup::GetGraphCount() const noexcept
    {
        return static_cast<uint32_t>(m_graph_array.size());
    }

    uint32_t UBlueprintGraphGroup::GetFunctionCount() const noexcept
    {
        return static_cast<uint32_t>(m_function_array.size());
    }

    uint32_t UBlueprintGraphGroup::GetVariableCount() const noexcept
    {
        return static_cast<uint32_t>(m_variable_array.size());
    }

    uint32_t UBlueprintGraphGroup::GetEventCount() const noexcept
    {
        return static_cast<uint32_t>(m_all_ng_events.size());
    }

    UBlueprintGraph* UBlueprintGraphGroup::CreateGraph(const std::string& name, const std::string& desc)
    {
        auto seq = reflection::make_shared_with_rtti<UBlueprintGraph>(name, desc, m_parent_workspace, this);
        m_graph_array.emplace_back(seq);
        if (m_fire_event)
            m_parent_workspace->FireOnGraphCreateEvent(seq.get());
        return seq.get();
    }

    UBlueprintGraphFunction* UBlueprintGraphGroup::CreateFunction(const std::string& name, const std::string& desc)
    {
        auto func = reflection::make_shared_with_rtti<UBlueprintGraphFunction>(name, desc, m_parent_workspace, this);
        m_function_array.emplace_back(func);
        if (m_fire_event)
            m_parent_workspace->FireOnFunctionCreateEvent(func.get());
        return func.get();
    }

    UBlueprintNamedVariable* UBlueprintGraphGroup::CreateNamedVariable(const std::string& name, machine::VValueKind type)
    {
        return CreateNamedVariable(name, machine::VValueUtil::CreateDefaultValueByType(type), type);
    }

    UBlueprintNamedVariable* UBlueprintGraphGroup::CreateNamedVariable(const std::string& name, const std::string& type)
    {
        auto kind = reflection::detail::ValueKindFromString(type);
        return CreateNamedVariable(name, machine::VValueUtil::CreateDefaultValueByType(kind), kind);
    }

    UBlueprintNamedVariable* UBlueprintGraphGroup::CreateNamedVariable(const std::string& name, const machine::VValue& value, machine::VValueKind kind)
    {
        auto var = reflection::make_shared_with_rtti<UBlueprintNamedVariable>(name, value, kind);
        m_variable_array.emplace_back(var);
        if (m_fire_event)
            m_parent_workspace->FireOnNamedVariableCreateEvent(var.get(), this);
        return var.get();
    }

    void UBlueprintGraphGroup::DestroyGraph(const std::string& name)
    {
        auto itr = std::find_if(m_graph_array.begin(), m_graph_array.end(), [&name](UBlueprintGraphPtr graph) { return graph->name() == name; });

        if (itr != m_graph_array.end())
        {
            if (m_fire_event)
                m_parent_workspace->FireOnGraphDestroyEvent((*itr).get());
            m_graph_array.erase(itr);
        }
    }

    void UBlueprintGraphGroup::DestroyFunction(const std::string& name)
    {
        auto itr = std::find_if(m_function_array.begin(), m_function_array.end(), [&name](UBlueprintGraphFunctionPtr func) { return func->name() == name; });

        if (itr != m_function_array.end())
        {
            if (m_fire_event)
                m_parent_workspace->FireOnFunctionCreateEvent((*itr).get());
            m_function_array.erase(itr);
        }
    }

    void UBlueprintGraphGroup::DestroyNamedVariable(const std::string& name)
    {
        auto itr = std::find_if(m_variable_array.begin(), m_variable_array.end(), [&name](UBlueprintNamedVariablePtr var) { return var->get_name() == name; });

        if (itr != m_variable_array.end())
        {
            if (m_fire_event)
                m_parent_workspace->FireOnNamedVariableDestroyEvent((*itr).get(), this);
            m_variable_array.erase(itr);
        }
    }

    void UBlueprintGraphGroup::ClearAll()
    {
        if (m_fire_event)
        {
            for (auto& seq : m_graph_array)
            {
                m_parent_workspace->FireOnGraphDestroyEvent(seq.get());
            }
        }
        m_graph_array.clear();

        if (m_fire_event)
        {
            for (auto& func : m_function_array)
            {
                m_parent_workspace->FireOnFunctionDestroyEvent(func.get());
            }
        }
        m_function_array.clear();

        if (m_fire_event)
        {
            for (auto& var : m_variable_array)
            {
                m_parent_workspace->FireOnNamedVariableDestroyEvent(var.get(), this);
            }
        }
        m_variable_array.clear();

        if (m_fire_event)
        {
            for (auto& eventIns : m_all_ng_events)
            {
                m_parent_workspace->FireOnEventDestroyEvent(eventIns.get(), this);
            }
        }
        m_all_ng_events.clear();
    }
    void UBlueprintGraphGroup::SaveToJson(machine::IVMStreamWriter& writer)
    {
        writer.StartObject("");

        writer.AddIntProperty("ng_version", m_parent_workspace->GetVersion());

        writer.AddStringProperty("self_agent", m_default_agent);

        // write variables to json
        writer.StartArray("variable_list");
        for (auto& variable : m_variable_array)
        {
            writer.StartObject("");
            variable->SerializeToJson(writer);
            writer.EndObject();
        }
        writer.EndArray();

        writer.StartArray("event_list");
        for (auto& event : m_all_ng_events)
        {
            event->SerializeToJson(writer);
        }
        writer.EndArray();

        writer.StartArray("function_list");
        // write function to json
        for (auto& func : m_function_array)
        {
            writer.StartObject("");
            func->SerializeToJson(writer);
            writer.EndObject();
        }
        writer.EndArray();

        writer.StartArray("sequence_list");
        // write sequence to json
        for (auto& seq : m_graph_array)
        {
            writer.StartObject("");
            seq->SerializeToJson(writer);
            writer.EndObject();
        }
        writer.EndArray();
        writer.EndObject();
    }
    void UBlueprintGraphGroup::SaveToMemory(ByteBuffer& mem_chunk)
    {
        JsonVMStreamWriter writer;

        writer.SetIndent('\t', 1);

        SaveToJson(writer);

        writer.SaveToMemory(mem_chunk);
    }

    void UBlueprintGraphGroup::LoadFromMemory(ByteBuffer& mem_chunk)
    {
        ClearAll();
        mem_chunk.ReadPosition(0);

        JsonVMStreamReader tmpreader;
        auto* dom = tmpreader.LoadFromMemory(mem_chunk);

        if (dom->IsNull())
        {
            return;
        }

        LoadFromJson(dom);
    }

    cegf::DynamicBlueprintBinding*  UBlueprintGraphGroup::GetDynamicBindingObject(const reflection::MetaClass* inClass) const
    {
        for (auto binding_obj : m_dynamic_binding_objects)
        {
            if (binding_obj && binding_obj->__type_id() == inClass->id())
            {
                return binding_obj.get();
            }
        }
        return nullptr;
    }

    void UBlueprintGraphGroup::BuildDynamicBindingObjects()
    {
        m_dynamic_binding_objects.clear();

        for (auto subgraph : m_graph_array)
        {
            for (auto node_i = 0; node_i < subgraph->GetNodesCount(); node_i++)
            {
                auto node_ptr = subgraph->GetNodeByIndex(node_i);
                if (node_ptr == nullptr)
                {
                    continue;
                }
                const auto* binding_meta_class = node_ptr->GetDynamicBindingClass();
                if (binding_meta_class)
                {
                    cegf::DynamicBlueprintBinding* dynamic_binding_object = GetDynamicBindingObject(binding_meta_class);
                    if (dynamic_binding_object == nullptr)
                    {
                        auto binding_obj = static_pointer_cast<cegf::DynamicBlueprintBinding>(gbf::reflection::make_user_object_by_class(binding_meta_class).Ref<cegf::DynamicBlueprintBinding>().shared_from_this());
                        m_dynamic_binding_objects.push_back(binding_obj);
                        dynamic_binding_object = binding_obj.get();
                    }
                    node_ptr->RegisterDynamicBinding(dynamic_binding_object);
                }
            }
        }
    }

    void UBlueprintGraphGroup::LoadFromJson(machine::IVMStreamReadNode* dom)
    {
        int file_version = dom->GetIntProperty("ng_version");
        assert(file_version == m_parent_workspace->GetVersion() && "NGSheduler::load_from_memory() can not recogonize this version of nodegraph file!");
        (file_version);

        if (dom->HasProperty("self_agent"))
        {
            m_default_agent = dom->GetStringProperty("self_agent");
        }

        if (dom->HasProperty("variable_list"))
        {
            auto* json_variablie_list = dom->GetNode("variable_list");
            assert(json_variablie_list->IsArray());

            for (size_t i = 0; i < json_variablie_list->GetArraySize(); i++)
            {
                auto* variable_node = json_variablie_list->GetArrayElement(i);
                auto tmp_name = variable_node->GetStringProperty("name");
                auto new_var = CreateNamedVariable(tmp_name);
                new_var->DeserializeFromJson(*variable_node);
            }
        }

        if (dom->HasProperty("event_list"))
        {
            auto* json_event_list = dom->GetNode("event_list");
            assert(json_event_list->IsArray());

            for (size_t i = 0; i < json_event_list->GetArraySize(); i++)
            {
                auto* event_node = json_event_list->GetArrayElement(i);
                auto tmp_name = event_node->GetStringProperty("Name");
                auto* newEvent = CreateBpEvent(tmp_name);
                newEvent->DeserializeFromJson(*event_node);
            }
        }

        auto* json_function_list = dom->GetNode("function_list");
        for (size_t i = 0; i < json_function_list->GetArraySize(); i++)
        {
            auto* json_func_item = json_function_list->GetArrayElement(i);
            auto tmp_name = json_func_item->GetStringProperty("name");
            auto tmp_desc = json_func_item->GetStringProperty("description");
            auto* new_func = CreateFunction(tmp_name, tmp_desc);
            new_func->DeserializeFromJson(*json_func_item);
        }

        auto* json_sequence_list = dom->GetNode("sequence_list");
        for (size_t i = 0; i < json_sequence_list->GetArraySize(); i++)
        {
            auto* json_seq_item = json_sequence_list->GetArrayElement(i);
            auto tmp_name = json_seq_item->GetStringProperty("name");
            auto tmp_desc = json_seq_item->GetStringProperty("description");
            auto* new_seq = CreateGraph(tmp_name, tmp_desc);
            new_seq->DeserializeFromJson(*json_seq_item);
        }
    }

    UBlueprintContextPtr UBlueprintGraphGroup::CreateContext(UBlueprintGraphPtr main_seq)
    {
        auto global_mem_scope = reflection::make_shared_with_rtti<machine::NamedMemoryScope>();

        // global memory scope initialize
        for (auto& variable : m_variable_array)
        {
            //if (variable->get_value_type() != machine::VValueKind::kUser)
            {
                auto tmp_value = std::make_shared<machine::VValue>(variable->get_default_value().Clone());
                global_mem_scope->CreateValue(variable->get_name(), tmp_value);
            }
           //else
           //{
           //    if (variable->get_var_category() == VariableCategory::Struct)
           //    {
           //        auto tmp_value = std::make_shared<machine::VValue>(variable->get_default_value().Clone());
           //        global_mem_scope->CreateValue(variable->get_name(), tmp_value);
           //        const auto* meta_class = reflection::query_meta_class_by_id(variable->get_type_id());
           //        if (meta_class == nullptr)
           //        {
           //            assert(false);
           //            ERR_DEF("no meta_class for %s registered, please add runtime reflection information", variable->get_name().c_str());
           //            global_mem_scope->CreateValue(variable->get_name(), std::make_shared<machine::VValue>(machine::VValue::nothing));
           //        }
           //        else if (meta_class->GetStorageVtableByType(reflection::StorageType::StorageRemoteShared)->ctor_default_ == nullptr)
           //        {
           //            assert(false);
           //            ERR_DEF("no constructor for %s registered, please add constructor runtime reflection", meta_class->name().c_str());
           //            global_mem_scope->CreateValue(variable->get_name(), std::make_shared<machine::VValue>(machine::VValue::nothing));
           //        }
           //        else
           //        {
           //            auto holder_obj = reflection::UserObject{meta_class->GetUsedMetaInfoByType(reflection::StorageType::StorageRemoteShared)};
           //            //reflection::make_user_object_by_class(meta_class);
           //            const auto& wrapper_obj = reflection::make_raw_pointer_user_object(holder_obj.GetPointer(), std::move(holder_obj));
           //            global_mem_scope->CreateValue(variable->get_name(), std::make_shared<machine::VValue>(wrapper_obj));
           //        }
           //    }
           //    else
           //    {
           //        global_mem_scope->CreateValue(variable->get_name(), std::make_shared<machine::VValue>(machine::VValue::nothing));
           //    }
           //}
        }

        auto new_context = reflection::make_shared_with_rtti<UBlueprintContext>(main_seq, global_mem_scope);
        new_context->InitRunEnvironment(global_mem_scope);
        return new_context;
    }

    UBlueprintEvent* UBlueprintGraphGroup::CreateBpEvent(const std::string& name)
    {
        auto itr = std::find_if(m_all_ng_events.begin(), m_all_ng_events.end(), [&name](auto the_event) { return name == the_event->get_event_name(); });

        if (m_all_ng_events.end() != itr)
            return nullptr;

        auto newEvent = reflection::make_shared_with_rtti<UBlueprintEvent>(name);
        m_all_ng_events.emplace_back(newEvent);
        m_parent_workspace->FireOnEventCreateEvent(newEvent.get(), this);

        return newEvent.get();
    }

    void UBlueprintGraphGroup::DeleteEvent(const std::string& name)
    {
        auto itr = std::find_if(m_all_ng_events.begin(), m_all_ng_events.end(), [&name](auto the_event) { return name == the_event->get_event_name(); });

        if (itr != m_all_ng_events.end())
        {
            if (m_fire_event)
                m_parent_workspace->FireOnEventDestroyEvent((*itr).get(), this);
            m_all_ng_events.erase(itr);
        }
    }

    UBlueprintEvent* UBlueprintGraphGroup::GetEvent(const std::string& name)
    {
        auto itr = std::find_if(m_all_ng_events.begin(), m_all_ng_events.end(), [&name](auto the_event) { return name == the_event->get_event_name(); });

        if (m_all_ng_events.end() == itr)
            return nullptr;

        return (*itr).get();
    }
    void RenameFunction(const std::string& new_name, const std::string& pre_name);
    void RenameVariable(const std::string& new_name, const std::string& pre_name);

    logic::UBlueprintGraph* UBlueprintGraphGroup::GetGraphByGraphID(size_t graph_id) const
    {
        auto itr = std::find_if(m_graph_array.begin(), m_graph_array.end(), [graph_id](UBlueprintGraphPtr graph) { return graph->id() == graph_id; });
        if (itr != m_graph_array.end())
        {
            return itr->get();
        }
        return nullptr;
    }

}   // namespace logic
}   // namespace gbf
