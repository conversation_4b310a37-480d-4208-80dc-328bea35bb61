#pragma once

#include <memory>

namespace gbf {
namespace logic {

class UBtreeGraph;
using UBtreeGraphPtr = std::shared_ptr<UBtreeGraph>;

class UBtreeNode;
using UBtreeNodePtr = std::shared_ptr<UBtreeNode>;

class UBtreeConnection;
using UBtreeConnectionPtr = std::shared_ptr<UBtreeConnection>;

class UBtreeContext;
using UBtreeContextPtr = std::shared_ptr<UBtreeContext>;

class BtreeInstructionImpl;
using BtreeInstructionImplPtr = std::shared_ptr<BtreeInstructionImpl>;

class UBtreeSlot;
using UBtreeSlotPtr = std::shared_ptr<UBtreeSlot>;

class UBtreeEffector;
using UBtreeEffectorPtr = std::shared_ptr<UBtreeEffector>;
class UBtreePreCondition;
using UBtreePreConditionPtr = std::shared_ptr<UBtreePreCondition>;


class UBtreeWorkspace;

class UBtreeRootNode;
using UBtreeRootNodePtr = std::shared_ptr<UBtreeRootNode>;
//-----------------------------------------------------------------------------------------------

enum class BTNodeType : int {
  UnKnown = 0,
};

enum class BTLinkDirection : int {
  In = 0,
  Out = 1,
};

enum class BTExcuteMode : int {
  Root = 0,
  NormalNoChild = 1,  // no child mode
  Decorator = 2,      // decorator
  Sequence = 3,       // execute as sequence
  Selector = 4,       // execute as selector
  Loop = 5,           // CapBTSelectorLoop
  Parallel = 10,      // execute as parallel(child will be a child coroutine)
};

enum class BTEffectPhaseType : int {
  OnSuccess = 0x01,
  OnFailure = 0x02,
  OnBoth = 0x03,
};

enum class BTPreconditionPhaseType : int {
  OnEnter = 0x01,
  OnUpdate = 0x02,
  Both = 0x03,
};

enum class BTChildFinishPolicy : int {
  CHILDFINISH_ONCE,
  CHILDFINISH_LOOP,
};

enum class BTExitPolicy : int {
  EXIT_NONE,
  EXIT_ABORT_RUNNINGSIBLINGS,
};

enum class BTFailurePolicy : int {
  FAIL_ON_ONE,
  FAIL_ON_ALL,
};

enum class BTSuccessPolicy : int {
  SUCCESS_ON_ONE,
  SUCCESS_ON_ALL,
};

enum class BtreeValueType : int { Invalid = 0, Plain, Blackboard, Field };

}  // namespace logic
}  // namespace gbf
