#pragma once

#include "visual/blueprint/blueprint_define.hpp"

#include "visual/blueprint/extend/btree/btree_fwd.h"

#include "visual/virtual_machine/runtime/serialize/ivmstreamreadnode.h"
#include "visual/virtual_machine/runtime/serialize/ivmstreamwriter.h"
#include "visual/virtual_machine/runtime/vcoroutine.hpp"
#include "reflection/objects/make_user_object.hpp"

namespace gbf {
namespace logic {
class UBtreeSlot;
class BLUEPRINT_API UBtreeConnection : public reflection::RttiBase {
  friend class UBtreeGraph;

 public:
  UBtreeConnection(UBtreeSlot* out_slot, UBtreeSlot* in_slot);
  virtual ~UBtreeConnection();

  uint64_t get_connect_id() const noexcept;

  UBtreeSlot* get_out_slot() const noexcept { return m_out_slot; }

  UBtreeSlot* get_in_slot() const noexcept { return m_in_slot; }

 protected:
  void SerializeTo<PERSON>son(machine::IVMStreamWriter& writer);
  void DeserializeFromJson(machine::IVMStreamReadNode& node);
 protected:
  uint64_t m_connect_id = 0;

  UBtreeSlot* m_out_slot;
  UBtreeSlot* m_in_slot;
};
}  // namespace logic
}  // namespace gbf
