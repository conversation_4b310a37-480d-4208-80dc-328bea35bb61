#include "visual/blueprint/extend/btree/node/attachment/btree_pre_condition.h"
#include "reflection/objects/make_user_object.hpp"

namespace gbf {
namespace logic {
UBtreePreCondition::UBtreePreCondition() {}

UBtreePreCondition::~UBtreePreCondition() {}

bool UBtreePreCondition::IsNeedWork(BTPreconditionPhaseType now_phase_type) const noexcept {
  return ((int)m_phase_type & (int)now_phase_type) != 0;
}

void UBtreePreCondition::SerializeTo<PERSON><PERSON>(machine::IVMStreamWriter& writer) {
  base::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(writer);
  writer.AddIntProperty("phase_type", (int)m_phase_type);
  writer.AddBoolProperty("combined_use_and", m_combined_use_and);
}

void UBtreePreCondition::DeserializeFrom<PERSON>son(machine::IVMStreamReadNode& node) {
  base::Deserialize<PERSON><PERSON><PERSON><PERSON>(node);
  m_phase_type = (BTPreconditionPhaseType)node.GetIntProperty("phase_type");
  m_combined_use_and = node.GetBoolProperty("combined_use_and");
}

UBtreePreConditionPtr UBtreePreCondition::CreateFromJson(machine::IVMStreamReadNode& node) {
  auto condition = reflection::make_shared_with_rtti<UBtreePreCondition>();
  condition->DeserializeFromJson(node);
  return condition;
}

}  // namespace logic
}  // namespace gbf
