#include "visual/blueprint/extend/btree/node/decorator/btree_decorator_count.h"
// #include "visual/virtual_machine/extend/vmvalue.h"
#include "visual/virtual_machine/runtime/expr/term_field_const.hpp"
#include "reflection/objects/make_user_object.hpp"
#include "reflection/objects/make_value.hpp"

namespace gbf {
namespace logic {

UBtreeDecoratorCount::UBtreeDecoratorCount() { m_title = "DecoratorCount"; }

UBtreeDecoratorCount::~UBtreeDecoratorCount() {}

int UBtreeDecoratorCount::GetCount(machine::VCoroutine* coroutine_, const machine::VObject& object) noexcept {
  // assert(m_count != nullptr);
  if (!m_count) return 0;
  auto val = m_count.Evaluate(coroutine_, object);
  return reflection::value_cast<int>(val);
}

void UBtreeDecoratorCount::Serial<PERSON><PERSON><PERSON><PERSON><PERSON>(machine::IVMStreamWriter& writer) {
  base::Serialize<PERSON><PERSON><PERSON><PERSON>(writer);
  writer.StartObject("DecoratorCount");
  m_count.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(writer);
  writer.EndObject();
}
void UBtreeDecoratorCount::DeserializeFromJson(machine::IVMStreamReadNode& node) {
  base::DeserializeFromJson(node);
  auto* value = node.GetNode("DecoratorCount");
  assert(value != nullptr);
  if (value != nullptr) {
    m_count.DeserializeFromJson(*value);
  }
}
}  // namespace logic
}  // namespace gbf
