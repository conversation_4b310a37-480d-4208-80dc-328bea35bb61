#pragma once

#include "visual/blueprint/blueprint_define.hpp"

#include "visual/blueprint/extend/btree/btree_fwd.h"
#include "visual/blueprint/extend/btree/node/btree_node.h"

#include "visual/virtual_machine/runtime/vcoroutine.hpp"

namespace gbf {
namespace logic {
class BLUEPRINT_API UBtreeParallel : public UBtreeNode {
    using base = UBtreeNode;
 public:
  UBtreeParallel();
  ~UBtreeParallel();

  machine::VMRunStepStatus UpdateLogic(machine::VCoroutine* coroutine_, const machine::VObject& obj) override;
 protected:
  void InitializeSlotsImpl() override;
 public:
  void set_child_finish_policy(BTChildFinishPolicy child_finish_policy) { m_child_finish_policy = child_finish_policy; }
  BTChildFinishPolicy get_child_finish_policy() { return m_child_finish_policy; }

  void set_exit_policy(BTExitPolicy exit_policy) { m_exit_policy = exit_policy; }
  BTExitPolicy get_exit_policy() { return m_exit_policy; }

  void set_failure_policy(BTFailurePolicy failure_policy) { m_failure_policy = failure_policy; }
  BTFailurePolicy get_failure_policy() { return m_failure_policy; }

  void set_success_policy(BTSuccessPolicy success_policy) { m_success_policy = success_policy; }
  BTSuccessPolicy get_success_policy() { return m_success_policy; }
 protected:
  BTChildFinishPolicy m_child_finish_policy = BTChildFinishPolicy::CHILDFINISH_ONCE;
  BTExitPolicy m_exit_policy = BTExitPolicy::EXIT_NONE;
  BTFailurePolicy m_failure_policy = BTFailurePolicy::FAIL_ON_ONE;
  BTSuccessPolicy m_success_policy = BTSuccessPolicy::SUCCESS_ON_ONE;
 protected:
  void SerializeToJson(machine::IVMStreamWriter& writer) override;

  void DeserializeFromJson(machine::IVMStreamReadNode& node) override;
 public:
  static void __meta_auto_register() {
    __register_cxx_type<UBtreeParallel>()
        .base<UBtreeNode>()
        .constructor<>()
        .property("child_finish_policy", &UBtreeParallel::m_child_finish_policy)
        .property("exit_policy", &UBtreeParallel::m_exit_policy)
        .property("failure_policy", &UBtreeParallel::m_failure_policy)
        .property("success_policy", &UBtreeParallel::m_success_policy);
  }
};
}  // namespace logic
}  // namespace gbf
