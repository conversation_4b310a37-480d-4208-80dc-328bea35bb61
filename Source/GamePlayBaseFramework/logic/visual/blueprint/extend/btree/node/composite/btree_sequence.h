#pragma once

#include "visual/blueprint/extend/btree/node/btree_node.h"

#include "visual/virtual_machine/runtime/vcoroutine.hpp"

namespace gbf {
namespace logic {
class BLUEPRINT_API UBtreeSequence : public UBtreeNode {
 public:
  UBtreeSequence();
  ~UBtreeSequence();

  machine::VMRunStepStatus UpdateLogic(machine::VCoroutine* coroutine_, const machine::VObject& obj) override;
 protected:
  void InitializeSlotsImpl() override;
 protected:
 public:
  static void __meta_auto_register() {
    __register_cxx_type<UBtreeSequence>()
        .base<UBtreeNode>()
        .constructor<>();
  }
};
}  // namespace logic
}  // namespace gbf
