#include "visual/blueprint/extend/btree/node/attachment/btree_attachment.h"
#include "visual/virtual_machine/runtime/expr/iterm.hpp"
#include "visual/virtual_machine/runtime/expr/iterm_field.hpp"
#include "visual/virtual_machine/runtime/vcoroutine.hpp"
#include "visual/virtual_machine/runtime/expr/evaluate_unit.hpp"
// #include "visual/virtual_machine/extend/vmvalue.h"
#include "visual/virtual_machine/runtime/vscheduler.hpp"
#include "reflection/objects/make_value.hpp"

namespace gbf {
namespace logic {

UBtreeAttachment::UBtreeAttachment() {}

UBtreeAttachment::~UBtreeAttachment() {}

bool UBtreeAttachment::ExecuteLogic(machine::VCoroutine* coroutine_, const machine::VObject& obj) {
  bool is_valid = false;

  // action
  if (m_operator_type == machine::VMOperatorType::Invalid) {
    is_valid = false;
  }
  // assign
  else if (m_operator_type == machine::VMOperatorType::Assign) {
    if (m_operator_left && m_operator_left.CanUseAsLeftValue() && m_operator_right_2) {
      m_operator_left.Assign(coroutine_, obj, m_operator_right_2.Evaluate(coroutine_, obj));
      is_valid = true;
    }
  } else {
    auto evl_group = machine::EvaluateUnit::GetEvaluateGroup(m_operator_type);
    // compute
    if (evl_group == machine::VMEvalGroupType::Math) {
      if (m_operator_left && m_operator_left.CanUseAsLeftValue() && m_operator_right_1 && m_operator_right_2) {
        auto right_val1 = m_operator_right_1.Evaluate(coroutine_, obj);
        auto right_val2 = m_operator_right_2.Evaluate(coroutine_, obj);
        m_operator_left.Assign(coroutine_, obj,
                                machine::EvaluateUnit::DoOperation(m_operator_type, right_val1, right_val2));
        is_valid = true;
      }
    }
    // compare
    else if (evl_group == machine::VMEvalGroupType::MathCompare || evl_group == machine::VMEvalGroupType::NormalComapre) {
      if (m_operator_left  && m_operator_right_2 ) {
        auto left_val = m_operator_left.Evaluate(coroutine_, obj);
        auto right_val2 = m_operator_right_2.Evaluate(coroutine_, obj);
        auto ret_val = machine::EvaluateUnit::DoOperation(m_operator_type, left_val, right_val2);
        is_valid = reflection::value_cast<bool>(ret_val);
      }
    }
  }

  return is_valid;
}

void UBtreeAttachment::SerializeToJson(machine::IVMStreamWriter& writer) {
  writer.AddIntProperty("operator_type", (int)m_operator_type);

  if (m_operator_left) {
    writer.StartObject("operator_left");
    m_operator_left.SerializeToJson(writer);
    writer.EndObject();
  }

  if (m_operator_right_1) {
    writer.StartObject("operator_right_1");
    m_operator_right_1.SerializeToJson(writer);
    writer.EndObject();
  }

  if (m_operator_right_2) {
    writer.StartObject("operator_right_2");
    m_operator_right_2.SerializeToJson(writer);
    writer.EndObject();
  }
}
void UBtreeAttachment::DeserializeFromJson(machine::IVMStreamReadNode& node) {
  m_operator_type = (machine::VMOperatorType)node.GetIntProperty("operator_type");

  auto* value = node.GetNode("operator_left");
  if (value != nullptr && value->IsObject()) m_operator_left.DeserializeFromJson(*value);

  value = node.GetNode("operator_right_1");
  if (value != nullptr && value->IsObject()) m_operator_right_1.DeserializeFromJson(*value);

  value = node.GetNode("operator_right_2");
  if (value != nullptr && value->IsObject()) m_operator_right_2.DeserializeFromJson(*value);
}

}  // namespace logic
}  // namespace gbf
