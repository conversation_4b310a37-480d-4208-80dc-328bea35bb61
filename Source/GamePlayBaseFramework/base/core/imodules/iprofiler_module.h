#pragma once

#include <memory>
#include "core/basetypes.hpp"
#include "core/config.hpp"
#include "core/modules/imodule.h"

namespace gbf {
enum class ProfilerGroupType {
  kThreadData = 0,
  kScripts,
  kPhysics,
  kBehaviourTree,
  kNetworkJob,
  kLoading,
  kOther,
  kGC,
  kLogicJob,
  kGameObject,
  kReflection,
  kWorkJob,
  kSlowJob,
  kSleep,
  kSkill,
  kMove,
  kAI,
  kTotalCount,
};

struct ProfilerMethodInfo {
 public:
  ProfilerGroupType group = ProfilerGroupType::kOther;
  std::string method_name;

  const char* extra_source_info;
  int64 name_id = 0;
};


class ProfilerCustomLineInfo {
 public:
  std::string line_name;
  std::string graph_kind_name;
  std::string unit_name;
  uint32_t color;

  int64 name_id;
  int64 graph_id;
  int64 unit_id;
};
////using ProfilerCustomLineInfoPtr = std::shared_ptr<ProfilerCustomLineInfo>;

static const char kModuleProfilerName[] = "ProfilerModule";

typedef void(*ConnectionChangedCallback)(bool connected, const wchar_t* p_recording_filename, void* user_data);

class IProfilerModule : public IModule {
 public:
  // method from IModule

 public:
  virtual ProfilerMethodInfo CreateMethodInfo(const std::string& name, ProfilerGroupType groupType,
                                              const char* extSourceInfo) = 0;
  virtual ProfilerMethodInfo CreateMethodInfo(const std::string& name, uint32 color, const char* extSourceInfo) = 0;

  virtual ProfilerCustomLineInfo CreateCustomLineInfo(const std::string& name, const std::string& graphKind,
                                                      const std::string& unitName, uint32_t color) = 0;

  virtual void OnFrameStart() = 0;

  virtual size_t BeginSample(const ProfilerMethodInfo& methodInfo) = 0;

  virtual size_t BeginSampleDynamic(const char* methodName, const char* extraSourceInfo) = 0;

  virtual size_t BeginSampleLua(const char* source_name, int line, const char* namewhat, const char* methodname) = 0;

  virtual void EndSample() = 0;

  virtual void EndSampleWithFix(size_t expectLevel) = 0;

  virtual int64 GetClockCount() const = 0;

  virtual void StartScope() const = 0;

  virtual void StopScope() const = 0;

  virtual void AddTimeSpan(const ProfilerMethodInfo& methodInfo, int64 startTime, int64 endTime) = 0;
  
  virtual void AddTimeSpan(const char* methodName, const char* pSourceInfo, int64 startTime, int64 endTime) = 0;

  virtual void AddTimeSpan(int64 nameId, const char* pSourceInfo, int64 startTime, int64 endTime) = 0;

  virtual void AddTimeSpan(int64 nameId, const char* p_source_info, int64 start_time, int64 end_time, int thread_id, int core) = 0;

  virtual bool IsRemoteConnected() const = 0;

  virtual void SetThreadName(const std::string& threadName) = 0;

  virtual int64 RegisterString(const std::string& strName) = 0;

  virtual void SetCustomLineValue(const ProfilerCustomLineInfo& lineInfo, double val) = 0;

  virtual void SetCustomLineValue(const ProfilerCustomLineInfo& lineInfo, int64 val) = 0;

  virtual void SetNetworkPort(int port) = 0;

  virtual void CleanupThread() = 0;

  virtual void StartRecodeToFile() = 0;

  virtual void StopRecodeToFile() = 0;

  virtual void ThreadOrder(const std::string& threadName) = 0;

  virtual void SendSessionInfo(const char* p_name, const char* p_value) {};

  virtual void SendSessionInfo(const wchar_t* p_name, const wchar_t* p_value) {};


  virtual void RegisterConnectionChangedCallback(ConnectionChangedCallback p_callback, void* p_context) {};

  virtual void UnregisterConnectionChangedcallback(ConnectionChangedCallback p_callback) {};
};
}  // namespace gbf

#include "iprofiler_module.inl"
