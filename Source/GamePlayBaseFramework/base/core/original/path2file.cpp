#include "core/original/path2file.h"

#include <cstring>
#include <filesystem>

namespace rstudio_original {

CPath2File::CPath2File(void) { Initialize(); }

CPath2File::~CPath2File(void) {}

void CPath2File::Initialize() {
  m_HashTable.Initialize();
  m_HashTable.SetFuction(Compare, ELFHash);
}

CPath2File::TPathAndFile* CPath2File::GetPathAndFile(char* pszPath) {
  if (!pszPath) {
    return nullptr;
  }
  TPathAndFile pathFile;
  snprintf(pathFile.m_cPathName, MAX_PATH_NAME, "%s", pszPath);
  return m_HashTable.GetItemByKey(&pathFile);
}

FILE* CPath2File::GetFileByHash(char* pszPath) {
  if (!pszPath) {
    return nullptr;
  }
  TPathAndFile pathFile;
  snprintf(pathFile.m_cPathName, MAX_PATH_NAME, "%s", pszPath);
  TPathAndFile* temp = m_HashTable.GetItemByKey(&pathFile);
  if (temp) {
    return temp->m_pFile;
  }
  return nullptr;
}

int CPath2File::SaveFileToName(char* pszPath, FILE* pFile) {
  if (!pszPath || !pFile) {
    return -1;
  }
  TPathAndFile pathFile;
  snprintf(pathFile.m_cPathName, MAX_PATH_NAME, "%s", pszPath);
  pathFile.m_pFile = pFile;
  int iRet = m_HashTable.CreatItemByKey(&pathFile);

  return iRet;
}

int CPath2File::DelFileByHash(char* pszPath) {
  if (!pszPath) {
    return -1;
  }
  TPathAndFile pathFile;
  snprintf(pathFile.m_cPathName, MAX_PATH_NAME, "%s", pszPath);
  return m_HashTable.DelItemByKey(&pathFile);
}

FILE* CPath2File::OpenFileIfDirNotExist(char* pszPath) {
#if GBF_CORE_PLATFORM != GBF_CORE_PLATFORM_ANDROID
  if (std::filesystem::create_directories(std::filesystem::path(pszPath))) {
    return fopen(pszPath, "a+");
  }
 #endif
  return nullptr;
}

FILE* CPath2File::GetOpenFd(char* pszPath) {
  FILE* pFile = GetFileByHash(pszPath);
  if (!pFile) {
    pFile = fopen(pszPath, "a+");
    if ((!pFile) && (errno == ENOENT)) {
      pFile = OpenFileIfDirNotExist(pszPath);
    }

    if (pFile) {
      if (SaveFileToName(pszPath, pFile) < 0) {
        CloseFile(pFile);
        pFile = nullptr;
        DelFileByHash(pszPath);
      }
    }
  }

  return pFile;
}

FILE* CPath2File::GetReOpenFd(char* pszPath) {
  FILE* fp = nullptr;
  TPathAndFile* pstPathAndFile = GetPathAndFile(pszPath);
  if (pstPathAndFile) {
    CloseFile(pstPathAndFile->m_pFile);

    fp = pstPathAndFile->m_pFile = fopen(pszPath, "a+");
    if (!pstPathAndFile->m_pFile) {
      DelFileByHash(pszPath);
    }
  }
  return fp;
}

int CPath2File::CloseFd(char* pszPath) {
  FILE* pFile = GetFileByHash(pszPath);
  if (pFile) {
    CloseFile(pFile);
    return DelFileByHash(pszPath);
  }
  return -1;
}

int CPath2File::CloseFile(FILE* pFile) {
  fclose(pFile);
  return 0;
}

void CPath2File::Reset() {
  CPath2File::TPathAndFile* pPathFile = nullptr;
  FILE* pFile;
  int iMax = m_HashTable.GetAllItem(pPathFile);
  for (int i = 0; i < iMax; ++i) {
    pFile = pPathFile[i].m_pFile;
    if (pFile) {
      CloseFile(pFile);
    }
  }
  // 关闭所有文件，重置Hash表
  m_HashTable.Reset();
}

int CPath2File::GetAllItem(TPathAndFile*& pPathFile) { return m_HashTable.GetAllItem(pPathFile); }

int CPath2File::Compare(const TPathAndFile* pPathName1, const TPathAndFile* pPathName2) {
  return strncmp(pPathName1->m_cPathName, pPathName2->m_cPathName, MAX_PATH_NAME);
}

int CPath2File::ELFHash(const TPathAndFile* pPathName) {
  int iHash = 0;
  int iPara = 0;
  int iLength = strlen(pPathName->m_cPathName);
  if (iLength > MAX_PATH_NAME) {
    iLength = MAX_PATH_NAME;
  }

  const char* pcKey = pPathName->m_cPathName;

  for (int i = 0; i < iLength; i++) {
    iHash = (iHash << 4) + pcKey[i];
    iPara = (iHash & 0xF0000000L);
    if (iPara != 0) {
      iHash ^= (iPara >> 24);
      iHash &= ~iPara;
    }
  }
  iHash &= 0x7FFFFFFF;

  return iHash;
}
}  // namespace gbf_original
