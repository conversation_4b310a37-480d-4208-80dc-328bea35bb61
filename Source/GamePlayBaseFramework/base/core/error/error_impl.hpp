#pragma once

#include <exception>
#include <string>
#include <string_view>

#include "core/config.hpp"
#include "core/utils/string_util.h"

namespace gbf {
    
/**
 * \brief Base class for every exception thrown in Ponder.
 */
class GBF_CORE_API Error : public std::exception
{
public:

    /**
     * \brief Destructor
     */
    virtual ~Error() throw();

    /**
     * \brief Return a description of the error
     *
     * \return Pointer to a string containing the error message
     */
    virtual const char* what() const throw();

    /**
     * \brief Return the error location (file + line + function)
     *
     * \return String containing the error location
     */
    virtual const char* where() const throw();

    /**
     * \brief Prepare an error to be thrown
     *
     * This function is meant for internal use only. It adds
     * the current context of execution (file, line and function)
     * to the given error and returns it.
     *
     * \param error Error to prepare
     * \param file Source filename
     * \param line Line number in the source file
     * \param function Name of the function where the error was thrown
     *
     * \return Modified error, ready to be thrown
     */
    template <typename T>
    static T prepare(T error, const std::string_view& file, int line, const std::string_view& function);

protected:

    /**
     * \brief Default constructor
     *
     * \param message Error message to return in what()
     */
    Error(const std::string_view message);

    /////**
    //// * \brief Helper function to convert anything to a string
    //// *
    //// * This is a convenience function provided to help derived
    //// * classes to easily build their full message
    //// *
    //// * \param x Value to convert
    //// *
    //// * \return \a x converted to a string
    //// */
    ////template <typename T>
    ////static std::string str(T x);

private:

    std::string m_message; ///< Error message
    std::string m_location; ///< Location of the error (file, line and function)
};

} // namespace ponder

#include "core/error/error_impl.inl"

/**
 * \brief Trigger a Ponder error
 */
#define GBF_ERROR(error) throw ::gbf::Error::prepare(error, __FILE__, __LINE__, __func__)

#define GBF_ASSERT_OR_ERROR(expr, error) if(GBF_LIKELY(expr)){ }else{ GBF_ERROR(error); }
