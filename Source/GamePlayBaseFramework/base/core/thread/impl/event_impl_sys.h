#pragma once

#include "core/thread/thread_config.hpp"

/*
  need -lrt in linux
*/

#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32
  #define WIN32_LEAN_AND_MEAN
  #include <windows.h>
#else
  #include <pthread.h>
#endif

namespace gbf {
namespace threads {

class EventImplSys {
 public:
  ////enum
  ////{
  ////	Fail = -1,
  ////	Succeed = 0,
  ////	Timeout = 1,
  ////};

  EventImplSys();
  ~EventImplSys();

  void Wait();

  bool TryWait();
  // return 0 if wait complete, return 1 if timeout, return -1 if error
  bool Wait(unsigned long millsecond);
  bool Signal();

  bool Reset();

 private:
  EventImplSys(const EventImplSys&);
  EventImplSys& operator=(const EventImplSys&);

#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32
  HANDLE event_;
#else
  pthread_cond_t cond_;
  pthread_mutex_t mutex_;
  volatile bool bflag_;
#endif
};

}  // namespace threads
}  // namespace gbf
