#pragma once

#include "core/thread/thread_config.hpp"

#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32
  #define WIN32_LEAN_AND_MEAN
  #include <windows.h>
#elif GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_APPLE || GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_APPLE_IOS
  #include <dispatch/dispatch.h>
#else
  #include <semaphore.h>
#endif

namespace gbf {
namespace threads {

class SemaphoreImplSys {
 public:
  ////enum
  ////{
  ////	Fail = -1,
  ////	Succeed = 0,
  ////	Timeout = 1,
  ////};
  SemaphoreImplSys(int initcount);
  ~SemaphoreImplSys();

  bool Wait(unsigned long millseconds);
  void Wait();
  void Signal();

  bool TryWait();

 private:
  SemaphoreImplSys(const SemaphoreImplSys&);
  SemaphoreImplSys& operator=(const SemaphoreImplSys&);

#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32
  HANDLE semaphore_;
#elif GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_APPLE || GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_APPLE_IOS
  dispatch_semaphore_t semaphore_;
#else
  sem_t semaphore_;
#endif
};
}  // namespace threads
}  // namespace gbf
