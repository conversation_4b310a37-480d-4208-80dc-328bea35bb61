#include "core/thread/impl/mutex_impl_sys.h"

namespace gbf {
namespace threads {

////#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32
////
////		mutex_impl_sys::mutex_impl_sys(bool need_recursive)
////		{
////			need_recursive;	//critical section always support recursive
////			InitializeCriticalSection(&m_CriticalSection);
////		}
////		mutex_impl_sys::~mutex_impl_sys()
////		{
////			DeleteCriticalSection(&m_CriticalSection);
////		}
////
////		void mutex_impl_sys::lock()
////		{
////			EnterCriticalSection(&m_CriticalSection);
////		}
////
////		////bool mutex_impl_sys::TryLock()
////		////{
////		////	if (TryEnterCriticalSection(&m_CriticalSection)) return true;
////		////	else return false;
////		////}
////
////		void mutex_impl_sys::unlock()
////		{
////			LeaveCriticalSection(&m_CriticalSection);
////		}
////
////#else
////
////		mutex_impl_sys::mutex_impl_sys(bool need_recursive)
////		{
////			pthread_mutexattr_t Attr;
////
////			if (need_recursive)
////			{
////				pthread_mutexattr_init(&Attr);
////				pthread_mutexattr_settype(&Attr, PTHREAD_MUTEX_RECURSIVE);
////			}
////
////			pthread_mutex_init(&m_Lock, &Attr);
////		}
////		mutex_impl_sys::~mutex_impl_sys()
////		{
////			pthread_mutex_destroy(&m_Lock);
////		}
////
////		void mutex_impl_sys::lock()
////		{
////			pthread_mutex_lock(&m_Lock);
////		}
////
////		////bool mutex_impl_sys::TryLock()
////		////{
////		////	if (pthread_mutex_trylock(&m_Lock) == 0) return true;
////		////	else return false;
////		////}
////
////		void mutex_impl_sys::unlock()
////		{
////			pthread_mutex_unlock(&m_Lock);
////		}
////
////#endif

MutexImplSys::MutexImplSys() {
  // need_recursive;	//critical section always support recursive
}
MutexImplSys::~MutexImplSys() {
  // DeleteCriticalSection(&m_CriticalSection);
}

void MutexImplSys::lock() { mutex_.lock(); }

////bool mutex_impl_sys::TryLock()
////{
////	if (TryEnterCriticalSection(&m_CriticalSection)) return true;
////	else return false;
////}

void MutexImplSys::unlock() { mutex_.unlock(); }
//-------------------------------------------------------------------------------------
RecursiveMutexImplSys::RecursiveMutexImplSys() {}

RecursiveMutexImplSys::~RecursiveMutexImplSys() {}

void RecursiveMutexImplSys::lock() { mutex_.lock(); }

void RecursiveMutexImplSys::unlock() { mutex_.unlock(); }

}  // namespace threads
}  // namespace gbf
