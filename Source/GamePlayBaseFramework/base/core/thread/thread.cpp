#include "core/thread/thread.h"
#include "core/thread/impl/thread_impl_sys.h"

namespace gbf {
namespace threads {

Thread::Thread() { m_impl = new ThreadImplSys(); }

Thread::Thread(ThreadJobFunction&& func, unsigned int stack_size /*= 0*/) {
  m_impl = new ThreadImplSys(std::move(func), stack_size);
}

Thread::Thread(const Thread& other) {
  m_impl = other.m_impl;
  Thread* pother = const_cast<Thread*>(&other);
  pother->m_impl = nullptr;
}

Thread& Thread::operator=(const Thread& other) {
  if (m_impl) {
    delete m_impl;
  }

  m_impl = other.m_impl;
  Thread* pother = const_cast<Thread*>(&other);
  pother->m_impl = nullptr;

  return *this;
}

Thread::~Thread() {
  if (m_impl) {
    delete m_impl;
    m_impl = nullptr;
  }
}

bool Thread::Run(ThreadJobFunction&& func, unsigned int stack_size /*= 0*/) {
  return m_impl->Run(std::move(func), stack_size);
}

bool Thread::Join() { return m_impl->Join(); }

bool Thread::Terminate() { return m_impl->Terminate(); }

void Thread::Detach() { m_impl->Detach(); }

size_t Thread::GetThreadId() const { return (size_t)m_impl->GetThreadId(); }

bool Thread::Joinable() const { return m_impl->Joinable(); }

void Thread::SetThreadPriority(int priority_value) { m_impl->SetThreadPriority(priority_value); }

void Thread::QueryRunningCores(std::vector<int>& out_cores) { m_impl->QueryRunningCores(out_cores); }

void Thread::BindThreadToCpuCores(const std::vector<int>& cores) { m_impl->BindThreadToCpuCores(cores); }

size_t Thread::CurrentThreadId() { return (size_t)ThreadImplSys::CurrentThreadId(); }

int Thread::HardwareConcurrency() { return (size_t)ThreadImplSys::HardwareConcurrency(); }

bool Thread::IsSupportBindCores() { return ThreadImplSys::IsSupportBindCores(); }

void Thread::QueryAllCpuCores(std::vector<int>& out_cores) { ThreadImplSys::QueryAllCpuCores(out_cores); }

bool Thread::IsSupportQueryRunningCores() { return ThreadImplSys::IsSupportQueryRunningCores(); }

int Thread::GetPriorityMinValue() { return ThreadImplSys::GetPriorityMinValue(); }

int Thread::GetPriorityMaxValue() { return ThreadImplSys::GetPriorityMaxValue(); }

void ThisThread::YieldThis() { ThisThreadImplSys::YieldThis(); }

void ThisThread::Sleep(unsigned long milliseconds) { ThisThreadImplSys::Sleep(milliseconds); }

int ThisThread::GetThreadId() { return ThisThreadImplSys::GetThreadId(); }

void ThisThread::SetThreadName(const std::string& thread_name) { ThisThreadImplSys::SetThreadName(thread_name); }

}  // namespace threads
}  // namespace gbf