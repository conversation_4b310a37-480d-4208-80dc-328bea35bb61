#include "core/utils/byte_buffer.h"
#include "core/utils/path_tool.hpp"

namespace gbf {

bool ByteBuffer::WriteDataToFile(const std::string& file_name) { 
  return PathTool::SaveDataToFile(file_name, Contents(), Size()); 
}

bool ByteBuffer::ReadDataFromFile(const std::string& file_name) {
  auto data_buf = PathTool::ReadFileData(file_name.c_str());
  if (!data_buf) {
    return false;
  }

  ResetAsBuffer();
  Append(data_buf->Contents(), data_buf->Size());
  data_buf.reset();
  return true;
}

}  // namespace gbf
