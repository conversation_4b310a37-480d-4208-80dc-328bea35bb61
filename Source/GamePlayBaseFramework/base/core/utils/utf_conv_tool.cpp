/*===--- ConvertUTF.c - Universal Character Names conversions ---------------===
 *
 *                     The LLVM Compiler Infrastructure
 *
 * This file is distributed under the University of Illinois Open Source
 * License. See LICENSE.TXT for details.
 *
 *===------------------------------------------------------------------------=*/
/*
 * Copyright 2001-2004 Unicode, Inc.
 *
 * Disclaimer
 *
 * This source code is provided as is by Unicode, Inc. No claims are
 * made as to fitness for any particular purpose. No warranties of any
 * kind are expressed or implied. The recipient agrees to determine
 * applicability of information provided. If this file has been
 * purchased on magnetic or optical media from Unicode, Inc., the
 * sole remedy for any claim will be exchange of defective media
 * within 90 days of receipt.
 *
 * Limitations on Rights to Redistribute This Code
 *
 * Unicode, Inc. hereby grants the right to freely use the information
 * supplied in this file in the creation of products supporting the
 * Unicode Standard, and to make copies of this file in any form
 * for internal or external distribution as long as this notice
 * remains attached.
 */

/* ---------------------------------------------------------------------

Conversions between UTF32, UTF-16, and UTF-8. Source code file.
Author: Mark E. Davis, 1994.
Rev History: Rick McGowan, fixes & updates May 2001.
Sept 2001: fixed const & error conditions per
mods suggested by S. Parent & A. Lillich.
June 2002: Tim Dodd added detection and handling of incomplete
source sequences, enhanced error detection, added casts
to eliminate compiler warnings.
July 2003: slight mods to back out aggressive FFFE detection.
Jan 2004: updated switches in from-UTF8 conversions.
Oct 2004: updated to use UNI_MAX_LEGAL_UTF32 in UTF-32 conversions.

See the header file "ConvertUTF.h" for complete documentation.

------------------------------------------------------------------------ */

#include "core/utils/utf_conv_tool.h"

#ifdef CVTUTF_DEBUG
#include <stdio.h>
#endif
#include <assert.h>

#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif
#if !defined(NOMINMAX) && defined(_MSC_VER)
#define NOMINMAX  // required to stop windows.h messing up std::min
#endif
#include <Windows.h>
#endif

/*
 * This code extensively uses fall-through switches.
 * Keep the compiler from warning about that.
 */
#if GBF_CORE_COMPILER == GBF_CORE_COMPILER_CLANG
#if __has_warning("-Wimplicit-fallthrough")
#define ConvertUTF_DISABLE_WARNINGS _Pragma("clang diagnostic push") _Pragma("clang diagnostic ignored \"-Wimplicit-fallthrough\"")
#define ConvertUTF_RESTORE_WARNINGS _Pragma("clang diagnostic pop")
#endif
#elif GBF_CORE_COMPILER == GBF_CORE_COMPILER_GNUC
#define ConvertUTF_DISABLE_WARNINGS _Pragma("GCC diagnostic push") _Pragma("GCC diagnostic ignored \"-Wimplicit-fallthrough\"")
#define ConvertUTF_RESTORE_WARNINGS _Pragma("GCC diagnostic pop")
#elif GBF_CORE_COMPILER == GBF_CORE_COMPILER_MSVC
#pragma warning(disable : 4127)
#endif
#ifndef ConvertUTF_DISABLE_WARNINGS
#define ConvertUTF_DISABLE_WARNINGS
#endif
#ifndef ConvertUTF_RESTORE_WARNINGS
#define ConvertUTF_RESTORE_WARNINGS
#endif

ConvertUTF_DISABLE_WARNINGS

    namespace llvm {
  static const int halfShift = 10; /* used for shifting by 10 bits */

  static const UTF32 halfBase = 0x0010000UL;
  static const UTF32 halfMask = 0x3FFUL;

#define UNI_SUR_HIGH_START (UTF32)0xD800
#define UNI_SUR_HIGH_END (UTF32)0xDBFF
#define UNI_SUR_LOW_START (UTF32)0xDC00
#define UNI_SUR_LOW_END (UTF32)0xDFFF

  /* --------------------------------------------------------------------- */

  /*
   * Index into the table below with the first byte of a UTF-8 sequence to
   * get the number of trailing bytes that are supposed to follow it.
   * Note that *legal* UTF-8 values can't have 4 or 5-bytes. The table is
   * left as-is for anyone who may want to do such conversion, which was
   * allowed in earlier algorithms.
   */
  static const char trailingBytesForUTF8[256] = {
      0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
      0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
      0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
      0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
      0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
      1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5};

  /*
   * Magic values subtracted from a buffer value during UTF8 conversion.
   * This table contains as many values as there might be trailing bytes
   * in a UTF-8 sequence.
   */
  static const UTF32 offsetsFromUTF8[6] = {0x00000000UL, 0x00003080UL, 0x000E2080UL, 0x03C82080UL, 0xFA082080UL, 0x82082080UL};

  /*
   * Once the bits are split out into bytes of UTF-8, this is a mask OR-ed
   * into the first byte, depending on how many bytes follow.  There are
   * as many entries in this table as there are UTF-8 sequence types.
   * (I.e., one byte sequence, two byte... etc.). Remember that sequencs
   * for *legal* UTF-8 will be 4 or fewer bytes total.
   */
  static const UTF8 firstByteMark[7] = {0x00, 0x00, 0xC0, 0xE0, 0xF0, 0xF8, 0xFC};

  /* --------------------------------------------------------------------- */

  /* The interface converts a whole buffer to avoid function-call overhead.
   * Constants have been gathered. Loops & conditionals have been removed as
   * much as possible for efficiency, in favor of drop-through switches.
   * (See "Note A" at the bottom of the file for equivalent code.)
   * If your compiler supports it, the "isLegalUTF8" call can be turned
   * into an inline function.
   */

  /* --------------------------------------------------------------------- */

  ConversionResult ConvertUTF32toUTF16(const UTF32** sourceStart, const UTF32* sourceEnd, UTF16** targetStart, UTF16* targetEnd,
                                       ConversionFlags flags) {
    ConversionResult result = conversionOK;
    const UTF32* source = *sourceStart;
    UTF16* target = *targetStart;
    while (source < sourceEnd) {
      UTF32 ch;
      if (target >= targetEnd) {
        result = targetExhausted;
        break;
      }
      ch = *source++;
      if (ch <= UNI_MAX_BMP) { /* Target is a character <= 0xFFFF */
                               /* UTF-16 surrogate values are illegal in UTF-32; 0xffff or 0xfffe are both reserved values */
        if (ch >= UNI_SUR_HIGH_START && ch <= UNI_SUR_LOW_END) {
          if (flags == strictConversion) {
            --source; /* return to the illegal value itself */
            result = sourceIllegal;
            break;
          } else {
            *target++ = UNI_REPLACEMENT_CHAR;
          }
        } else {
          *target++ = (UTF16)ch; /* normal case */
        }
      } else if (ch > UNI_MAX_LEGAL_UTF32) {
        if (flags == strictConversion) {
          result = sourceIllegal;
        } else {
          *target++ = UNI_REPLACEMENT_CHAR;
        }
      } else {
        /* target is a character in range 0xFFFF - 0x10FFFF. */
        if (target + 1 >= targetEnd) {
          --source; /* Back up source pointer! */
          result = targetExhausted;
          break;
        }
        ch -= halfBase;
        *target++ = (UTF16)((ch >> halfShift) + UNI_SUR_HIGH_START);
        *target++ = (UTF16)((ch & halfMask) + UNI_SUR_LOW_START);
      }
    }
    *sourceStart = source;
    *targetStart = target;
    return result;
  }

  /* --------------------------------------------------------------------- */

  ConversionResult ConvertUTF16toUTF32(const UTF16** sourceStart, const UTF16* sourceEnd, UTF32** targetStart, UTF32* targetEnd,
                                       ConversionFlags flags) {
    ConversionResult result = conversionOK;
    const UTF16* source = *sourceStart;
    UTF32* target = *targetStart;
    UTF32 ch, ch2;
    while (source < sourceEnd) {
      const UTF16* oldSource = source; /*  In case we have to back up because of target overflow. */
      ch = *source++;
      /* If we have a surrogate pair, convert to UTF32 first. */
      if (ch >= UNI_SUR_HIGH_START && ch <= UNI_SUR_HIGH_END) {
        /* If the 16 bits following the high surrogate are in the source buffer... */
        if (source < sourceEnd) {
          ch2 = *source;
          /* If it's a low surrogate, convert to UTF32. */
          if (ch2 >= UNI_SUR_LOW_START && ch2 <= UNI_SUR_LOW_END) {
            ch = ((ch - UNI_SUR_HIGH_START) << halfShift) + (ch2 - UNI_SUR_LOW_START) + halfBase;
            ++source;
          } else if (flags == strictConversion) { /* it's an unpaired high surrogate */
            --source;                             /* return to the illegal value itself */
            result = sourceIllegal;
            break;
          }
        } else {    /* We don't have the 16 bits following the high surrogate. */
          --source; /* return to the high surrogate */
          result = sourceExhausted;
          break;
        }
      } else if (flags == strictConversion) {
        /* UTF-16 surrogate values are illegal in UTF-32 */
        if (ch >= UNI_SUR_LOW_START && ch <= UNI_SUR_LOW_END) {
          --source; /* return to the illegal value itself */
          result = sourceIllegal;
          break;
        }
      }
      if (target >= targetEnd) {
        source = oldSource; /* Back up source pointer! */
        result = targetExhausted;
        break;
      }
      *target++ = ch;
    }
    *sourceStart = source;
    *targetStart = target;
#ifdef CVTUTF_DEBUG
    if (result == sourceIllegal) {
      fprintf(stderr, "ConvertUTF16toUTF32 illegal seq 0x%04x,%04x\n", ch, ch2);
      fflush(stderr);
    }
#endif
    return result;
  }
  ConversionResult ConvertUTF16toUTF8(const UTF16** sourceStart, const UTF16* sourceEnd, UTF8** targetStart, UTF8* targetEnd, ConversionFlags flags) {
    ConversionResult result = conversionOK;
    const UTF16* source = *sourceStart;
    UTF8* target = *targetStart;
    while (source < sourceEnd) {
      UTF32 ch;
      unsigned short bytesToWrite = 0;
      const UTF32 byteMask = 0xBF;
      const UTF32 byteMark = 0x80;
      const UTF16* oldSource = source; /* In case we have to back up because of target overflow. */
      ch = *source++;
      /* If we have a surrogate pair, convert to UTF32 first. */
      if (ch >= UNI_SUR_HIGH_START && ch <= UNI_SUR_HIGH_END) {
        /* If the 16 bits following the high surrogate are in the source buffer... */
        if (source < sourceEnd) {
          UTF32 ch2 = *source;
          /* If it's a low surrogate, convert to UTF32. */
          if (ch2 >= UNI_SUR_LOW_START && ch2 <= UNI_SUR_LOW_END) {
            ch = ((ch - UNI_SUR_HIGH_START) << halfShift) + (ch2 - UNI_SUR_LOW_START) + halfBase;
            ++source;
          } else if (flags == strictConversion) { /* it's an unpaired high surrogate */
            --source;                             /* return to the illegal value itself */
            result = sourceIllegal;
            break;
          }
        } else {    /* We don't have the 16 bits following the high surrogate. */
          --source; /* return to the high surrogate */
          result = sourceExhausted;
          break;
        }
      } else if (flags == strictConversion) {
        /* UTF-16 surrogate values are illegal in UTF-32 */
        if (ch >= UNI_SUR_LOW_START && ch <= UNI_SUR_LOW_END) {
          --source; /* return to the illegal value itself */
          result = sourceIllegal;
          break;
        }
      }
      /* Figure out how many bytes the result will require */
      if (ch < (UTF32)0x80) {
        bytesToWrite = 1;
      } else if (ch < (UTF32)0x800) {
        bytesToWrite = 2;
      } else if (ch < (UTF32)0x10000) {
        bytesToWrite = 3;
      } else if (ch < (UTF32)0x110000) {
        bytesToWrite = 4;
      } else {
        bytesToWrite = 3;
        ch = UNI_REPLACEMENT_CHAR;
      }

      target += bytesToWrite;
      if (target > targetEnd) {
        source = oldSource; /* Back up source pointer! */
        target -= bytesToWrite;
        result = targetExhausted;
        break;
      }
      switch (bytesToWrite) { /* note: everything falls through. */
        case 4:
          *--target = (UTF8)((ch | byteMark) & byteMask);
          ch >>= 6;
        case 3:
          *--target = (UTF8)((ch | byteMark) & byteMask);
          ch >>= 6;
        case 2:
          *--target = (UTF8)((ch | byteMark) & byteMask);
          ch >>= 6;
        case 1:
          *--target = (UTF8)(ch | firstByteMark[bytesToWrite]);
      }
      target += bytesToWrite;
    }
    *sourceStart = source;
    *targetStart = target;
    return result;
  }

  /* --------------------------------------------------------------------- */

  ConversionResult ConvertUTF32toUTF8(const UTF32** sourceStart, const UTF32* sourceEnd, UTF8** targetStart, UTF8* targetEnd, ConversionFlags flags) {
    ConversionResult result = conversionOK;
    const UTF32* source = *sourceStart;
    UTF8* target = *targetStart;
    while (source < sourceEnd) {
      UTF32 ch;
      unsigned short bytesToWrite = 0;
      const UTF32 byteMask = 0xBF;
      const UTF32 byteMark = 0x80;
      ch = *source++;
      if (flags == strictConversion) {
        /* UTF-16 surrogate values are illegal in UTF-32 */
        if (ch >= UNI_SUR_HIGH_START && ch <= UNI_SUR_LOW_END) {
          --source; /* return to the illegal value itself */
          result = sourceIllegal;
          break;
        }
      }
      /*
       * Figure out how many bytes the result will require. Turn any
       * illegally large UTF32 things (> Plane 17) into replacement chars.
       */
      if (ch < (UTF32)0x80) {
        bytesToWrite = 1;
      } else if (ch < (UTF32)0x800) {
        bytesToWrite = 2;
      } else if (ch < (UTF32)0x10000) {
        bytesToWrite = 3;
      } else if (ch <= UNI_MAX_LEGAL_UTF32) {
        bytesToWrite = 4;
      } else {
        bytesToWrite = 3;
        ch = UNI_REPLACEMENT_CHAR;
        result = sourceIllegal;
      }

      target += bytesToWrite;
      if (target > targetEnd) {
        --source; /* Back up source pointer! */
        target -= bytesToWrite;
        result = targetExhausted;
        break;
      }
      switch (bytesToWrite) { /* note: everything falls through. */
        case 4:
          *--target = (UTF8)((ch | byteMark) & byteMask);
          ch >>= 6;
        case 3:
          *--target = (UTF8)((ch | byteMark) & byteMask);
          ch >>= 6;
        case 2:
          *--target = (UTF8)((ch | byteMark) & byteMask);
          ch >>= 6;
        case 1:
          *--target = (UTF8)(ch | firstByteMark[bytesToWrite]);
      }
      target += bytesToWrite;
    }
    *sourceStart = source;
    *targetStart = target;
    return result;
  }

  /* --------------------------------------------------------------------- */

  /*
   * Utility routine to tell whether a sequence of bytes is legal UTF-8.
   * This must be called with the length pre-determined by the first byte.
   * If not calling this from ConvertUTF8to*, then the length can be set by:
   *  length = trailingBytesForUTF8[*source]+1;
   * and the sequence is illegal right away if there aren't that many bytes
   * available.
   * If presented with a length > 4, this returns false.  The Unicode
   * definition of UTF-8 goes up to 4-byte sequences.
   */

  static Boolean isLegalUTF8(const UTF8* source, int length) {
    UTF8 a;
    const UTF8* srcptr = source + length;
    switch (length) {
      default:
        return false;
        /* Everything else falls through when "true"... */
      case 4:
        if ((a = (*--srcptr)) < 0x80 || a > 0xBF) return false;
      case 3:
        if ((a = (*--srcptr)) < 0x80 || a > 0xBF) return false;
      case 2:
        if ((a = (*--srcptr)) < 0x80 || a > 0xBF) return false;

        switch (*source) {
            /* no fall-through in this inner switch */
          case 0xE0:
            if (a < 0xA0) return false;
            break;
          case 0xED:
            if (a > 0x9F) return false;
            break;
          case 0xF0:
            if (a < 0x90) return false;
            break;
          case 0xF4:
            if (a > 0x8F) return false;
            break;
          default:
            if (a < 0x80) return false;
        }

      case 1:
        if (*source >= 0x80 && *source < 0xC2) return false;
    }
    if (*source > 0xF4) return false;
    return true;
  }

  /* --------------------------------------------------------------------- */

  /*
   * Exported function to return whether a UTF-8 sequence is legal or not.
   * This is not used here; it's just exported.
   */
  Boolean isLegalUTF8Sequence(const UTF8* source, const UTF8* sourceEnd) {
    int length = trailingBytesForUTF8[*source] + 1;
    if (length > sourceEnd - source) {
      return false;
    }
    return isLegalUTF8(source, length);
  }

  /* --------------------------------------------------------------------- */

  static unsigned findMaximalSubpartOfIllFormedUTF8Sequence(const UTF8* source, const UTF8* sourceEnd) {
    UTF8 b1, b2, b3;

    assert(!isLegalUTF8Sequence(source, sourceEnd));

    /*
     * Unicode 6.3.0, D93b:
     *
     *   Maximal subpart of an ill-formed subsequence: The longest code unit
     *   subsequence starting at an unconvertible offset that is either:
     *   a. the initial subsequence of a well-formed code unit sequence, or
     *   b. a subsequence of length one.
     */

    if (source == sourceEnd) return 0;

    /*
     * Perform case analysis.  See Unicode 6.3.0, Table 3-7. Well-Formed UTF-8
     * Byte Sequences.
     */

    b1 = *source;
    ++source;
    if (b1 >= 0xC2 && b1 <= 0xDF) {
      /*
       * First byte is valid, but we know that this code unit sequence is
       * invalid, so the maximal subpart has to end after the first byte.
       */
      return 1;
    }

    if (source == sourceEnd) return 1;

    b2 = *source;
    ++source;

    if (b1 == 0xE0) {
      return (b2 >= 0xA0 && b2 <= 0xBF) ? 2 : 1;
    }
    if (b1 >= 0xE1 && b1 <= 0xEC) {
      return (b2 >= 0x80 && b2 <= 0xBF) ? 2 : 1;
    }
    if (b1 == 0xED) {
      return (b2 >= 0x80 && b2 <= 0x9F) ? 2 : 1;
    }
    if (b1 >= 0xEE && b1 <= 0xEF) {
      return (b2 >= 0x80 && b2 <= 0xBF) ? 2 : 1;
    }
    if (b1 == 0xF0) {
      if (b2 >= 0x90 && b2 <= 0xBF) {
        if (source == sourceEnd) return 2;

        b3 = *source;
        return (b3 >= 0x80 && b3 <= 0xBF) ? 3 : 2;
      }
      return 1;
    }
    if (b1 >= 0xF1 && b1 <= 0xF3) {
      if (b2 >= 0x80 && b2 <= 0xBF) {
        if (source == sourceEnd) return 2;

        b3 = *source;
        return (b3 >= 0x80 && b3 <= 0xBF) ? 3 : 2;
      }
      return 1;
    }
    if (b1 == 0xF4) {
      if (b2 >= 0x80 && b2 <= 0x8F) {
        if (source == sourceEnd) return 2;

        b3 = *source;
        return (b3 >= 0x80 && b3 <= 0xBF) ? 3 : 2;
      }
      return 1;
    }

    assert((b1 >= 0x80 && b1 <= 0xC1) || b1 >= 0xF5);
    /*
     * There are no valid sequences that start with these bytes.  Maximal subpart
     * is defined to have length 1 in these cases.
     */
    return 1;
  }

  /* --------------------------------------------------------------------- */

  /*
   * Exported function to return the total number of bytes in a codepoint
   * represented in UTF-8, given the value of the first byte.
   */
  unsigned getNumBytesForUTF8(UTF8 first) { return trailingBytesForUTF8[first] + 1; }

  /* --------------------------------------------------------------------- */

  /*
   * Exported function to return whether a UTF-8 string is legal or not.
   * This is not used here; it's just exported.
   */
  Boolean isLegalUTF8String(const UTF8** source, const UTF8* sourceEnd) {
    while (*source != sourceEnd) {
      int length = trailingBytesForUTF8[**source] + 1;
      if (length > sourceEnd - *source || !isLegalUTF8(*source, length)) return false;
      *source += length;
    }
    return true;
  }

  /* --------------------------------------------------------------------- */

  ConversionResult ConvertUTF8toUTF16(const UTF8** sourceStart, const UTF8* sourceEnd, UTF16** targetStart, UTF16* targetEnd, ConversionFlags flags) {
    ConversionResult result = conversionOK;
    const UTF8* source = *sourceStart;
    UTF16* target = *targetStart;
    while (source < sourceEnd) {
      UTF32 ch = 0;
      unsigned short extraBytesToRead = trailingBytesForUTF8[*source];
      if (extraBytesToRead >= sourceEnd - source) {
        result = sourceExhausted;
        break;
      }
      /* Do this check whether lenient or strict */
      if (!isLegalUTF8(source, extraBytesToRead + 1)) {
        result = sourceIllegal;
        break;
      }
      /*
       * The cases all fall through. See "Note A" below.
       */
      switch (extraBytesToRead) {
        case 5:
          ch += *source++;
          ch <<= 6; /* remember, illegal UTF-8 */
        case 4:
          ch += *source++;
          ch <<= 6; /* remember, illegal UTF-8 */
        case 3:
          ch += *source++;
          ch <<= 6;
        case 2:
          ch += *source++;
          ch <<= 6;
        case 1:
          ch += *source++;
          ch <<= 6;
        case 0:
          ch += *source++;
      }
      ch -= offsetsFromUTF8[extraBytesToRead];

      if (target >= targetEnd) {
        source -= (extraBytesToRead + 1); /* Back up source pointer! */
        result = targetExhausted;
        break;
      }
      if (ch <= UNI_MAX_BMP) { /* Target is a character <= 0xFFFF */
                               /* UTF-16 surrogate values are illegal in UTF-32 */
        if (ch >= UNI_SUR_HIGH_START && ch <= UNI_SUR_LOW_END) {
          if (flags == strictConversion) {
            source -= (extraBytesToRead + 1); /* return to the illegal value itself */
            result = sourceIllegal;
            break;
          } else {
            *target++ = UNI_REPLACEMENT_CHAR;
          }
        } else {
          *target++ = (UTF16)ch; /* normal case */
        }
      } else if (ch > UNI_MAX_UTF16) {
        if (flags == strictConversion) {
          result = sourceIllegal;
          source -= (extraBytesToRead + 1); /* return to the start */
          break;                            /* Bail out; shouldn't continue */
        } else {
          *target++ = UNI_REPLACEMENT_CHAR;
        }
      } else {
        /* target is a character in range 0xFFFF - 0x10FFFF. */
        if (target + 1 >= targetEnd) {
          source -= (extraBytesToRead + 1); /* Back up source pointer! */
          result = targetExhausted;
          break;
        }
        ch -= halfBase;
        *target++ = (UTF16)((ch >> halfShift) + UNI_SUR_HIGH_START);
        *target++ = (UTF16)((ch & halfMask) + UNI_SUR_LOW_START);
      }
    }
    *sourceStart = source;
    *targetStart = target;
    return result;
  }

  /* --------------------------------------------------------------------- */

  static ConversionResult ConvertUTF8toUTF32Impl(const UTF8** sourceStart, const UTF8* sourceEnd, UTF32** targetStart, UTF32* targetEnd,
                                                 ConversionFlags flags, Boolean InputIsPartial) {
    ConversionResult result = conversionOK;
    const UTF8* source = *sourceStart;
    UTF32* target = *targetStart;
    while (source < sourceEnd) {
      UTF32 ch = 0;
      unsigned short extraBytesToRead = trailingBytesForUTF8[*source];
      if (extraBytesToRead >= sourceEnd - source) {
        if (flags == strictConversion || InputIsPartial) {
          result = sourceExhausted;
          break;
        } else {
          result = sourceIllegal;

          /*
           * Replace the maximal subpart of ill-formed sequence with
           * replacement character.
           */
          source += findMaximalSubpartOfIllFormedUTF8Sequence(source, sourceEnd);
          *target++ = UNI_REPLACEMENT_CHAR;
          continue;
        }
      }
      if (target >= targetEnd) {
        result = targetExhausted;
        break;
      }

      /* Do this check whether lenient or strict */
      if (!isLegalUTF8(source, extraBytesToRead + 1)) {
        result = sourceIllegal;
        if (flags == strictConversion) {
          /* Abort conversion. */
          break;
        } else {
          /*
           * Replace the maximal subpart of ill-formed sequence with
           * replacement character.
           */
          source += findMaximalSubpartOfIllFormedUTF8Sequence(source, sourceEnd);
          *target++ = UNI_REPLACEMENT_CHAR;
          continue;
        }
      }
      /*
       * The cases all fall through. See "Note A" below.
       */
      switch (extraBytesToRead) {
        case 5:
          ch += *source++;
          ch <<= 6;
        case 4:
          ch += *source++;
          ch <<= 6;
        case 3:
          ch += *source++;
          ch <<= 6;
        case 2:
          ch += *source++;
          ch <<= 6;
        case 1:
          ch += *source++;
          ch <<= 6;
        case 0:
          ch += *source++;
      }
      ch -= offsetsFromUTF8[extraBytesToRead];

      if (ch <= UNI_MAX_LEGAL_UTF32) {
        /*
         * UTF-16 surrogate values are illegal in UTF-32, and anything
         * over Plane 17 (> 0x10FFFF) is illegal.
         */
        if (ch >= UNI_SUR_HIGH_START && ch <= UNI_SUR_LOW_END) {
          if (flags == strictConversion) {
            source -= (extraBytesToRead + 1); /* return to the illegal value itself */
            result = sourceIllegal;
            break;
          } else {
            *target++ = UNI_REPLACEMENT_CHAR;
          }
        } else {
          *target++ = ch;
        }
      } else { /* i.e., ch > UNI_MAX_LEGAL_UTF32 */
        result = sourceIllegal;
        *target++ = UNI_REPLACEMENT_CHAR;
      }
    }
    *sourceStart = source;
    *targetStart = target;
    return result;
  }

  ConversionResult ConvertUTF8toUTF32Partial(const UTF8** sourceStart, const UTF8* sourceEnd, UTF32** targetStart, UTF32* targetEnd,
                                             ConversionFlags flags) {
    return ConvertUTF8toUTF32Impl(sourceStart, sourceEnd, targetStart, targetEnd, flags, /*InputIsPartial=*/true);
  }

  ConversionResult ConvertUTF8toUTF32(const UTF8** sourceStart, const UTF8* sourceEnd, UTF32** targetStart, UTF32* targetEnd, ConversionFlags flags) {
    return ConvertUTF8toUTF32Impl(sourceStart, sourceEnd, targetStart, targetEnd, flags, /*InputIsPartial=*/false);
  }

  /* ---------------------------------------------------------------------

  Note A.
  The fall-through switches in UTF-8 reading code save a
  temp variable, some decrements & conditionals.  The switches
  are equivalent to the following loop:
  {
          int tmpBytesToRead = extraBytesToRead+1;
          do {
                  ch += *source++;
                  --tmpBytesToRead;
                  if (tmpBytesToRead) ch <<= 6;
          } while (tmpBytesToRead > 0);
  }
  In UTF-8 writing code, the switches on "bytesToWrite" are
  similarly unrolled loops.

  --------------------------------------------------------------------- */

  // -- Wrapper for std strings
  bool ConvertUTF8toWide(unsigned wideCharWidth, const UTF8* sourceStart, const UTF8* sourceEnd, char*& resultPtr, const UTF8*& errorPtr) {
    assert(wideCharWidth == 1 || wideCharWidth == 2 || wideCharWidth == 4);
    ConversionResult result = conversionOK;
    // Copy the character span over.
    if (wideCharWidth == 1) {
      const UTF8* pos = sourceStart;
      if (!isLegalUTF8String(&pos, sourceEnd)) {
        result = sourceIllegal;
        errorPtr = pos;
      } else {
        size_t size = sourceEnd - sourceStart;
        memcpy(resultPtr, sourceStart, size);
        resultPtr += size;
      }
    } else if (wideCharWidth == 2) {
      // FIXME: Make the type of the result buffer correct instead of
      // using reinterpret_cast.
      UTF16* targetStart = reinterpret_cast<UTF16*>(resultPtr);
      ConversionFlags flags = strictConversion;
      result = ConvertUTF8toUTF16(&sourceStart, sourceEnd, &targetStart, targetStart + (sourceEnd - sourceStart), flags);
      if (result == conversionOK)
        resultPtr = reinterpret_cast<char*>(targetStart);
      else
        errorPtr = sourceStart;
    } else if (wideCharWidth == 4) {
      // FIXME: Make the type of the result buffer correct instead of
      // using reinterpret_cast.
      UTF32* targetStart = reinterpret_cast<UTF32*>(resultPtr);
      ConversionFlags flags = strictConversion;
      result = ConvertUTF8toUTF32(&sourceStart, sourceEnd, &targetStart, targetStart + (sourceEnd - sourceStart), flags);
      if (result == conversionOK)
        resultPtr = reinterpret_cast<char*>(targetStart);
      else
        errorPtr = sourceStart;
    }
    assert((result != targetExhausted) && "ConvertUTF8toUTFXX exhausted target buffer");
    return result == conversionOK;
  }

  inline UTF16 SwapByteOrder(UTF16 value) {
    // TODO: use compile internal function(they will use CPU instruction)
    UTF16 hi = value << 8;
    UTF16 lo = value >> 8;
    return hi | lo;
  }

  bool ConvertUTF16ToUTF8String(const UTF16* sourceStart, const UTF16* sourceEnd, std::string& result) {
    assert(result.empty());

    // Avoid OOB by returning early on empty input.
    size_t size = sourceEnd - sourceStart;
    if (size == 0) return true;

    const UTF16* src = sourceStart;
    const UTF16* srcEnd = sourceEnd;

    // Byte swap if necessary.
    if (src[0] == UNI_UTF16_BYTE_ORDER_MARK_SWAPPED) {
      std::vector<UTF16> swappedData;
      swappedData.resize(size);
      for (size_t i = 0; i < size; ++i) swappedData[i] = SwapByteOrder(sourceStart[i]);

      src = &swappedData[0];
      srcEnd = &swappedData[size - 1] + 1;
    }

    // Skip the BOM for conversion.
    if (src[0] == UNI_UTF16_BYTE_ORDER_MARK_NATIVE) src++;

    // Just allocate enough space up front.  We'll shrink it later.  Allocate
    // enough that we can fit a null terminator without reallocating.
    result.resize(size * UNI_MAX_UTF8_BYTES_PER_CODE_POINT * 2 + 1);
    UTF8* dst = reinterpret_cast<UTF8*>(&result[0]);
    UTF8* dstEnd = dst + result.size();

    ConversionResult conv_result = ConvertUTF16toUTF8(&src, srcEnd, &dst, dstEnd, strictConversion);
    assert(conv_result != targetExhausted);

    if (conv_result != conversionOK) {
      result.clear();
      return false;
    }

    result.resize(reinterpret_cast<char*>(dst) - &result[0]);
    result.shrink_to_fit();
    return true;
  }
}  // namespace llvm

namespace gbf {
using namespace llvm;
static_assert(sizeof(wchar_t) == 1 || sizeof(wchar_t) == 2 || sizeof(wchar_t) == 4, "Expected wchar_t to be 1, 2, or 4 bytes");

bool ConvertStringUTF8ToUTF16(const std::string& source, std::u16string& result) {
  assert(sizeof(std::u16string::value_type) == 2 && "u16 value size must be 2 bytes");

  result.resize(source.size() + 1);
  char* resultPtr = reinterpret_cast<char*>(&result[0]);
  const UTF8* errorPtr;
  if (ConvertUTF8toWide(2, reinterpret_cast<const UTF8*>(source.data()), reinterpret_cast<const UTF8*>(source.data()) + source.size(), resultPtr,
                        errorPtr)) {
    result.resize(reinterpret_cast<char16_t*>(resultPtr) - &result[0]);
    result.shrink_to_fit();
    return true;
  } else {
    result.clear();
    return false;
  }
}
bool ConvertStringUTF16ToUTF8(const std::u16string& source, std::string& result) {
  assert(sizeof(char16_t) == 2 && "chat16_t size must be 2 bytes");
  return ConvertUTF16ToUTF8String(reinterpret_cast<const UTF16*>(source.data()), reinterpret_cast<const UTF16*>(source.data()) + source.size(),
                                  result);
}

bool ConvertStringUTF8toWide(const std::string& source, std::wstring& result) {
  result.resize(source.size() + 1);
  char* resultPtr = reinterpret_cast<char*>(&result[0]);
  const UTF8* errorPtr;
  if (ConvertUTF8toWide(sizeof(wchar_t), reinterpret_cast<const UTF8*>(source.data()), reinterpret_cast<const UTF8*>(source.data()) + source.size(),
                        resultPtr, errorPtr)) {
    result.resize(reinterpret_cast<wchar_t*>(resultPtr) - &result[0]);
    result.shrink_to_fit();
    return true;
  } else {
    result.clear();
    return false;
  }
}

bool ConvertStringUTF8toWide(const char* source, std::wstring& result) {
  size_t size = strlen(source);
  result.resize(size + 1);
  char* resultPtr = reinterpret_cast<char*>(&result[0]);
  const UTF8* errorPtr;
  if (ConvertUTF8toWide(sizeof(wchar_t), reinterpret_cast<const UTF8*>(source), reinterpret_cast<const UTF8*>(source) + size, resultPtr, errorPtr)) {
    result.resize(reinterpret_cast<wchar_t*>(resultPtr) - &result[0]);
    result.shrink_to_fit();
    return true;
  } else {
    result.clear();
    return false;
  }
}

bool ConvertStringWideToUTF8(const std::wstring& source, std::string& result) {
  if (sizeof(wchar_t) == 1) {
    const UTF8* start = reinterpret_cast<const UTF8*>(source.data());
    const UTF8* end = reinterpret_cast<const UTF8*>(source.data() + source.size());
    if (!isLegalUTF8String(&start, end)) return false;
    result.resize(source.size());
    memcpy(&result[0], source.data(), source.size());
    return true;
  } else if (sizeof(wchar_t) == 2) {
    return ConvertUTF16ToUTF8String(reinterpret_cast<const UTF16*>(source.data()), reinterpret_cast<const UTF16*>(source.data()) + source.size(),
                                    result);
  } else if (sizeof(wchar_t) == 4) {
    const UTF32* start = reinterpret_cast<const UTF32*>(source.data());
    const UTF32* end = reinterpret_cast<const UTF32*>(source.data() + source.size());
    result.resize(UNI_MAX_UTF8_BYTES_PER_CODE_POINT * source.size());
    UTF8* resultPtr = reinterpret_cast<UTF8*>(&result[0]);
    UTF8* resultEnd = resultPtr + result.size();
    if (ConvertUTF32toUTF8(&start, end, &resultPtr, resultEnd, strictConversion) == conversionOK) {
      result.resize(reinterpret_cast<char*>(resultPtr) - &result[0]);
      result.shrink_to_fit();
      return true;
    } else {
      result.clear();
      return false;
    }
  } else {
    assert(false && "Control should never reach this point; see static_assert further up");
  }
}

bool ConvertStringUTF8IsASCII(const char* source) {
  while (*source) {
    if ((UTF8)*source++ > 128) {
      return false;
    }
  }
  return true;
}

bool ConvertStringUTF8IsASCII(const std::string& source) {
  for (auto ch : source) {
    if ((UTF8)ch > 128) {
      return false;
    }
  }
  return true;
}

#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32
bool ConvertStringWideToWindowsNative(const std::wstring& source, std::string& result) {
  auto needLength = WideCharToMultiByte(CP_ACP, 0, source.c_str(), (int)source.size(), NULL, 0, NULL, FALSE);
  if (needLength > 0) {
    result.resize(needLength + 1);
    auto converted = WideCharToMultiByte(CP_ACP, 0, source.c_str(), -1, &result[0], (int)result.size(), NULL, FALSE);
    if (converted) {
      result.resize(converted);
      return true;
    } else {
      return false;
    }
  }
  return false;
}
#endif
}  // namespace gbf

ConvertUTF_RESTORE_WARNINGS
