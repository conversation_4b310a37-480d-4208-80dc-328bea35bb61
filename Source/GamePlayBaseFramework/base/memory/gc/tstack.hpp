#pragma once

////#include "memory/memory_define.hpp"

namespace gbf {
namespace gc {
// TStack class make a small vm that gc object can managed in this small vm
template <typename value_type, size_t VALUES_PER_NODE = 2048>
class tstack {
  // it's a value_type(gc object) groups
  struct value_type_group {
    value_type_group() { memset(values, 0, sizeof(values)); }

    value_type values[VALUES_PER_NODE];
    value_type_group* prev_group = nullptr;  // null if bottom
  };

  // current use group
  value_type_group* m_top_group;

  // last reserved group
  value_type_group* m_reserved_group;

  // the top gc object in the _stack
  value_type* m_top_value;

 public:
  class iterator {
    value_type_group* m_cur_node;
    value_type* m_cur_value;

   public:
    iterator(tstack& s) : m_cur_node(s.m_top_group), m_cur_value(s.m_top_value) {}
    value_type* get() {
      if (m_cur_value == m_cur_node->values) {
        if (m_cur_node->prev_group == nullptr) {
          return nullptr;
        }
        m_cur_node = m_cur_node->prev_group;
        m_cur_value = m_cur_node->values + VALUES_PER_NODE;
      }
      return --m_cur_value;
    }
  };
  tstack() : m_top_group(new value_type_group()), m_reserved_group(nullptr), m_top_value(m_top_group->values) {}
  ~tstack() {
    delete m_reserved_group;
    while (m_top_group != nullptr) {
      value_type_group* n = m_top_group->prev_group;
      delete m_top_group;
      m_top_group = n;
    }
  }
  bool empty() const { return m_top_group->prev_group == nullptr && m_top_value == m_top_group->values; }
  value_type* push() {
    if (m_top_value == m_top_group->values + VALUES_PER_NODE) {
      value_type_group* new_node;
      if (m_reserved_group != nullptr) {
        new_node = m_reserved_group;
        m_reserved_group = nullptr;
      } else {
        new_node = new value_type_group;
      }
      new_node->prev_group = m_top_group;
      m_top_group = new_node;
      m_top_value = new_node->values;
    }
    return m_top_value++;
  }
  value_type* pop() {
    if (m_top_value == m_top_group->values) {
      if (m_top_group->prev_group == nullptr) {
        return nullptr;
      }
      delete m_reserved_group;
      m_reserved_group = m_top_group;
      m_top_group = m_top_group->prev_group;
      m_top_value = m_top_group->values + VALUES_PER_NODE;
    }
    return --m_top_value;
  }
  value_type* preserve() { return m_top_value; }

  void restore(value_type* slot) {
    value_type_group* n = m_top_group;
    while (!(n->values <= slot && slot <= n->values + VALUES_PER_NODE)) {
      if (m_reserved_group == nullptr) {
        m_reserved_group = n;
        n = n->prev_group;
      } else {
        value_type_group* prev = n->prev_group;
        delete n;
        n = prev;
      }
    }
    // found
    m_top_group = n;
    m_top_value = slot;
  }
};
}  // namespace gc
}  // namespace gbf
