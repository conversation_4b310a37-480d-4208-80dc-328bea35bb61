#include "memory/gc/run_scope.hpp"

#include "memory/gc/gc_info.hpp"
#include "memory/gc/gc_manager.hpp"

namespace gbf {
namespace gc {

run_scope::run_scope(uint64_t scope_id): m_scope_id(scope_id) {
  m_scope_objects.reserve(64);
}

void run_scope::_destruct() {
  g_gc->free_scope(m_scope_id, m_scope_objects);
}

run_scope::~run_scope() {
  if (m_is_closed) return;

  _destruct();
  g_gc->may_trigger_gc();
  m_is_closed = true;
}

void run_scope::do_scope_mark() {
  for (gc_info* obj : m_scope_objects) {
    g_gc->mark_one_obj(obj);
  }
}

}  // namespace gc
}  // namespace gbf
