#pragma once

#include <memory>
#include "memory/gc/gc_fwd.hpp"
#include "memory/gc/gc_util.hpp"
#include "memory/gc/gc_vtable.hpp"
#include "memory/gc/smart_ptr.hpp"
#include "memory/memory_export.hpp"

namespace gbf {
namespace gc {
namespace detail {

template <typename T>
inline auto _make_local_impl() -> std::pair<gc_info*, void*> {
  auto ret_pair = gc_util::_allocate_gc_object(sizeof(T));
  gc_info* info = ret_pair.first;
  // fill vtable here
  constexpr auto* vtbl = detail::query_vtable_by_type<T>();
  info->vtbl_ = vtbl;

  return ret_pair;
}

}  // namespace detail

template <typename T, typename... Args>
inline auto make_local(run_scope_ptr run_scope, Args&&... args) -> local_ptr<T> {
  auto ret_pair = detail::_make_local_impl<T>();

  auto [info, ptr] = ret_pair;
  ////std::construct_at<T>( reinterpret_cast<T*>(ptr), static_cast<Args&&>(args)...);
  ::new (ptr) T{std::forward<Args>(args)...};
  info->do_post_ctor_action();

  gc_util::add_info_to_run_scope(run_scope, info);

  return local_ptr<T>(ret_pair.first, reinterpret_cast<T*>(ret_pair.second));
}

template <typename T>
inline auto make_local_with_copy(run_scope_ptr run_scope, const T& org) -> local_ptr<T> {
  static_assert(std::is_copy_constructible_v<T>, "Must support copy construct to run this!");
  auto ret_pair = detail::_make_local_impl<T>();

  auto [info, ptr] = ret_pair;
  ////std::construct_at<T>(reinterpret_cast<T*>(ptr), org);
  ::new (ptr) T{org};
  info->do_post_ctor_action();

  gc_util::add_info_to_run_scope(run_scope, info);

  return local_ptr<T>(ret_pair.first, reinterpret_cast<T*>(ret_pair.second));
}

template <typename T>
inline auto make_local_with_owned(run_scope_ptr run_scope, T&& org) -> local_ptr<T> {
  static_assert(std::is_nothrow_move_constructible_v<T>, "Must support nothrow move construct to run this!");
  auto ret_pair = detail::_make_local_impl<T>();

  auto [info, ptr] = ret_pair;
  ////std::construct_at<T>(reinterpret_cast<T*>(ptr), static_cast<T&&>(org));
  ::new (ptr) T{static_cast<T&&>(org)};
  info->do_post_ctor_action();

  gc_util::add_info_to_run_scope(run_scope, info);

  return local_ptr<T>(ret_pair.first, reinterpret_cast<T*>(ret_pair.second));
}


}  // namespace gc
}  // namespace gbf
