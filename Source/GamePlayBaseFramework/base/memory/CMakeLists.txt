cmake_minimum_required(VERSION 3.16)

include_directories(
    ${PROJECT_SOURCE_DIR}/base/
    ${PROJECT_SOURCE_DIR}/third_party/
    ${PROJECT_SOURCE_DIR}/externals/include/
)
link_directories(
    ${CMAKE_BINARY_DIR}
)

add_definitions(-DGBF_MEMORY_EXPORTS)

set(all_project_src "")


file(GLOB memory_src                    "*.*")
source_group(\\memory                       FILES ${memory_src})
list(APPEND all_project_src ${memory_src})

file(GLOB allocator_src                 "allocator/*.*")
source_group(\\memory\\allocator            FILES ${allocator_src})
list(APPEND all_project_src ${allocator_src})

file(GLOB gc_src                        "gc/*.*")
source_group(\\memory\\gc                   FILES ${gc_src})
list(APPEND all_project_src ${gc_src})

file(GLOB gc_detail_src                        "gc/detail/*.*")
source_group(\\memory\\gc\\detail                   FILES ${gc_detail_src})
list(APPEND all_project_src ${gc_detail_src})


file(GLOB gc_container_src              "gc/container/*.*")
source_group(\\memory\\gc\\container        FILES ${gc_container_src})
list(APPEND all_project_src ${gc_container_src})

file(GLOB reference_count_src           "reference_count/*.*")
source_group(\\memory\\reference_count      FILES ${reference_count_src})
list(APPEND all_project_src ${reference_count_src})

set(memory_name "memory")

add_library(${memory_name} SHARED
    ${all_project_src}
)

add_dependencies(${memory_name}
    gbf_core
)
 string(FIND "${CMAKE_CXX_FLAGS}" "fsanitize=address" _index)
    if(_index EQUAL -1)

    else()
        target_compile_definitions(${memory_name} PUBLIC USE_ASAN_CHECK)
 endif()
target_include_directories(${memory_name} PRIVATE ${MANAGED_THIRDPARTY_DIR}/tracy-0.10/public)
if("${CMAKE_GENERATOR_PLATFORM}" MATCHES "Win64")
		target_link_directories(${memory_name} PUBLIC  $<BUILD_INTERFACE:${MANAGED_THIRDPARTY_DIR}/mimalloc/Libraries/Win64> $<INSTALL_INTERFACE:ManagedThirdParty_new/mimalloc/Libraries/Win64>)
		target_link_libraries(${memory_name} PUBLIC mimalloc-override.lib gbf_core TracyClient)
else()
    target_link_libraries(${memory_name}
    PUBLIC gbf_core TracyClient)
endif()
#set(LIBRARY_OUTPUT_PATH ${CMAKE_BINARY_DIR})

#file(COPY ${network_name}.dll DESTINATION ${CMAKE_BINARY_DIR})
set_target_properties(${memory_name} PROPERTIES UNITY_BUILD ON)
SET_PROPERTY(TARGET ${memory_name} PROPERTY FOLDER "framework c++/base")
#message("!!!!dir2 is: ${PROJECT_SOURCE_DIR}")

