#pragma once

#include <map>
#include <memory>
#include <string>
#include <string_view>

#include "archive/archive_export.hpp"
#include "core/utils/string_builder.h"
#include "archive/coder/coder_helper.h"
#include "reflection/reflection_fwd.hpp"

namespace gbf {
namespace reflection {
class GBF_ARCHIVE_API CoderLuaTable {
 public:
  static std::string WriteUserObjectToLuaCode(const UserObject& uo, bool filterDefault = true);
  static std::string WriteArgsToLuaCode(const Args& args, bool filterDefault = true);
  static std::string WriteValueToLuaCode(const Value& val, bool filterDefault = true);

  static bool WriteUserObject(StringBuilder& sb, const UserObject& uo, bool filterDefault = true);
  static bool WriteArgs(StringBuilder& sb, const Args& args, bool filterDefault = true);

 protected:
  static bool WriteFieldValueNotArray(StringBuilder& sb, const Value& val, bool filterDefault = true);
  static bool WriteArrayObject(StringBuilder& sb, const ArrayObject& object, bool filterDefault = true);
};
}  // namespace reflection
}  // namespace gbf
