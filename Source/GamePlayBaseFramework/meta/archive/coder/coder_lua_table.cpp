#include "archive/coder/coder_lua_table.h"

#include "core/error/errors.hpp"
#include "core/utils/string_util.h"
////#include "reflection/builder/cpp/classbuilder.hpp"
#include "reflection/runtime/cxx_runtime.hpp"
#include "reflection/objects/make_value.hpp"
#include "reflection/meta/array_property.hpp"

namespace gbf {
namespace reflection {

bool CoderLuaTable::WriteUserObject(StringBuilder& sb, const UserObject& uo, bool filterDefault /*= true*/) {
  const auto& metaclass = uo.GetClass();

  std::vector<const Property*> needFields;
  if (filterDefault) {
    CoderHelper::FilterFields(metaclass, uo, needFields);
  } else {
    CoderHelper::GetAllFields(metaclass, uo, needFields);
  }

  sb.Append("{ ");

  for (auto& needField : needFields) {
    const auto& property = *needField;

    if (property.kind() == ValueKind::kArray) {
      auto const& arrayProperty = dynamic_cast<const reflection::ArrayProperty&>(property);
      auto itemKind = arrayProperty.element_type();

      const size_t count = arrayProperty.Size(uo);

      sb.AppendFormat("%s = ", property.name().c_str());

      sb.Append("{");
      // user & string is dynamic size
      for (size_t j = 0; j < count; ++j) {
        sb.AppendFormat("[%d] = ", (int)j);
        WriteFieldValueNotArray(sb, arrayProperty.Get(uo, j), filterDefault);
        sb.Append(", ");
      }
      sb.Append("}");

      sb.Append(", ");
    } else if (property.kind() == ValueKind::kUser) {
      // We need filter user object do not has property here~~
      auto puo = value_ref_as<UserObject>(property.Get(uo));
      if (puo.GetClass().GetPropertyCount() > 0) {
        sb.AppendFormat("%s = ", property.name().c_str());
        WriteUserObject(sb, puo, filterDefault);
        sb.Append(", ");
      }
    } else {
      auto val = property.Get(uo);
      sb.AppendFormat("%s = ", property.name().c_str());
      WriteFieldValueNotArray(sb, val, filterDefault);
      sb.Append(", ");
    }
  }

  sb.Append(" }");

  return true;
}

bool CoderLuaTable::WriteArgs(StringBuilder& sb, const Args& args, bool filterDefault /*= true*/) {
  sb.Append("{ ");

  for (size_t i = 0; i < args.GetCount(); i++) {
    const auto& val = args[i];
    sb.AppendFormat("[%d] = ", (int)i + 1);

    switch (val.kind()) {
      case reflection::ValueKind::kNone:
      case reflection::ValueKind::kBoolean:
      case reflection::ValueKind::kInteger:
      case reflection::ValueKind::kReal:
      case reflection::ValueKind::kString:
      case reflection::ValueKind::kEnum:
      case ValueKind::kUser: {
        WriteFieldValueNotArray(sb, val, filterDefault);
      } break;
      case ValueKind::kArray: {
        const auto& ao = value_ref_as<ArrayObject>(val);
        WriteArrayObject(sb, ao, filterDefault);
      } break;
      default:
        GBF_ERROR(MetaObjectWriteError("WriteFieldValueNoCompressMode(), Can not reach here!"));
    }

    sb.Append(", ");
  }

  sb.Append("}");
  return true;
}

bool CoderLuaTable::WriteFieldValueNotArray(StringBuilder& sb, const Value& val, bool filterDefault) {
  auto kind = val.kind();
  switch (kind) {
    case reflection::ValueKind::kNone: {
      sb.Append("nil");
    } break;
    case reflection::ValueKind::kBoolean: {
      sb.Append(value_cast<bool>(val));
    } break;
    case reflection::ValueKind::kInteger: {
      sb.Append(value_cast<int64_t>(val));
    } break;
    case reflection::ValueKind::kReal: {
      sb.Append(value_cast<double>(val));
    } break;
    case reflection::ValueKind::kString: {
      auto sv = value_cast<std::string_view>(val);
      sb.AppendFormat("'%s'", sv.data());
    } break;
    case reflection::ValueKind::kEnum: {
      auto& enumobj = value_ref_as<EnumObject>(val);
      sb.AppendFormat("'%s'", enumobj.GetName().data());
    } break;
    case ValueKind::kUser: {
      // recurse
      WriteUserObject(sb, value_ref_as<UserObject>(val), filterDefault);
    } break;
    case ValueKind::kArray:
    default:
      GBF_ERROR(MetaObjectWriteError("WriteFieldValueNoCompressMode(), Can not reach here!"));
  }
  return true;
}

bool CoderLuaTable::WriteArrayObject(StringBuilder& sb, const ArrayObject& object, bool filterDefault) {
  auto arrayCount = object.GetSize();
  sb.Append("{ ");
  // Iterate over the array elements
  for (size_t j = 0; j < arrayCount; ++j) {
    sb.AppendFormat("[%d] = ", (int)j + 1);
    WriteFieldValueNotArray(sb, object.GetElement(j), filterDefault);
    sb.Append(", ");
  }
  sb.Append("}");
  return true;
}

std::string CoderLuaTable::WriteUserObjectToLuaCode(const UserObject& uo, bool filterDefault /*= true*/) {
  StringBuilder sb;
  WriteUserObject(sb, uo, filterDefault);
  return sb.Str();
}

std::string CoderLuaTable::WriteArgsToLuaCode(const Args& args, bool filterDefault /*= true*/) {
  StringBuilder sb;
  WriteArgs(sb, args, filterDefault);
  return sb.Str();
}

std::string CoderLuaTable::WriteValueToLuaCode(const Value& val, bool filterDefault /*= true*/) {
  StringBuilder sb;
  if (val.kind() == ValueKind::kArray) {
    WriteArrayObject(sb, value_ref_as<ArrayObject>(val), filterDefault);
  } else {
    WriteFieldValueNotArray(sb, val, filterDefault);
  }
  return sb.Str();
}

}  // namespace reflection
}  // namespace gbf
