#include "archive/protobuf/cxx/cxx_callback_helper.hpp"
#include "core/imodules/ilog_module.h"

namespace protobuf {
namespace cxx {
CoderCallbackFunction CallbackHelper::msCallbackFunc = [](CoderCallbackLevel level, std::string_view info) {
  switch (level) {
    case CoderCallbackLevel::kInfo:
      INF_DEF("[lurapb coder]%s", info.data());
      break;
    case CoderCallbackLevel::kWarning:
      WRN_DEF("[lurapb coder]%s", info.data());
      break;
    case CoderCallbackLevel::kError:
      ERR_DEF("[lurapb coder]%s", info.data());
      break;
  }
};

void CallbackHelper::SetCallback(const CoderCallbackFunction& func) { msCallbackFunc = func; }

void CallbackHelper::NotifyInfo(std::string_view info) { msCallbackFunc(CoderCallbackLevel::kInfo, info); }

void CallbackHelper::NotifyWarning(std::string_view info) { msCallbackFunc(CoderCallbackLevel::kWarning, info); }

void CallbackHelper::NotifyError(std::string_view info) { msCallbackFunc(CoderCallbackLevel::kError, info); }

}  // namespace cplusplus
}  // namespace protobuf
