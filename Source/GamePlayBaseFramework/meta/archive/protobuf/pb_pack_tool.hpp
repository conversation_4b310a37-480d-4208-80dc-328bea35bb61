#pragma once

#include "archive/archive_export.hpp"

#include "core/utils/byte_buffer_view.h"
#include "core/utils/byte_buffer_writer.h"

#include "archive/protobuf/pb.h"

namespace protobuf {
class GBF_ARCHIVE_API LurapbPackTool {
 public:
  static PackResult PackToArray(const PbMsgDesc* metaMessage, const void* pMessage, void* data, int size);

  static UnpackResult UnpackFromArray(const PbMsgDesc* metaMessage, void* pMessage, const void* data, int size);

  static PackResult PackToBuffer(const PbMsgDesc* metaMessage, const void* pMessage,
                                 gbf::ByteBufferWriter& buf);

  static UnpackResult UnpackFromBuffer(const PbMsgDesc* metaMessage, void* pMessage, gbf::ByteBufferView buf);

  static std::string PackToHexString(const PbMsgDesc* metaMessage, const void* pMessage,
                                     size_t capacity = 100 * 1024);

  static UnpackResult UnpackFromHexString(const PbMsgDesc* metaMessage, void* pMessage,
                                          const std::string_view hexstr);

  static std::string PackToLuaCode(const PbMsgDesc* metaMessage, const void* pMessage);

  static std::string PackToXml(const PbMsgDesc* metaMessage, const void* pMessage, bool compact, int depth);

  ////static bool UnpackFromXml(const pb_msgdesc_t* metaMessage, void* pMessage, const std::string_view xmlstr);
  ////static UnpackResult UnpackFromXml(const pb_msgdesc_t* metaMessage, void* pMessage, const std::string_view hexstr);
};
}  // namespace protobuf
