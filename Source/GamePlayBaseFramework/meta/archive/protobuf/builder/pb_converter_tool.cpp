#include "archive/protobuf/builder/pb_converter_tool.h"
#include "core/error/errors.hpp"
#include "core/utils/memory_util.h"
#include "reflection/meta/meta_class.hpp"
#include "reflection/runtime/cxx_runtime.hpp"
#include "reflection/objects/userobject.hpp"

namespace protobuf {

gbf::reflection::Value PbConverterTool::PrimitivePointerGetAsValue(const void* objPointer,
                                                                       cxx::FieldDataType dataType,
                                                                       uint32_t dataOffset, uint32_t dataSize) {
  const char* dataPtr = (const char*)objPointer + dataOffset;
  switch (dataType) {
    case protobuf::cxx::FieldDataType::kBool:
      return ReadPointerAs<bool>(dataPtr, dataSize);
    case protobuf::cxx::FieldDataType::kDouble:
      return ReadPointerAs<double>(dataPtr, dataSize);
    case protobuf::cxx::FieldDataType::kFixed32:
      return ReadPointerAs<uint32_t>(dataPtr, dataSize);
    case protobuf::cxx::FieldDataType::kFixed64:
      return ReadPointerAs<uint64_t>(dataPtr, dataSize);
    case protobuf::cxx::FieldDataType::kFloat:
      return ReadPointerAs<float>(dataPtr, dataSize);
    case protobuf::cxx::FieldDataType::kInt32:
      return ReadPointerAs<int32_t>(dataPtr, dataSize);
    case protobuf::cxx::FieldDataType::kInt64:
      return ReadPointerAs<int64_t>(dataPtr, dataSize);
    case protobuf::cxx::FieldDataType::kSfixed32:
      return ReadPointerAs<int32_t>(dataPtr, dataSize);
    case protobuf::cxx::FieldDataType::kSfixed64:
      return ReadPointerAs<int64_t>(dataPtr, dataSize);
    case protobuf::cxx::FieldDataType::kSint32:
      return ReadPointerAs<int32_t>(dataPtr, dataSize);
    case protobuf::cxx::FieldDataType::kSint64:
      return ReadPointerAs<int64_t>(dataPtr, dataSize);
    case protobuf::cxx::FieldDataType::kUint32:
      return ReadPointerAs<uint32_t>(dataPtr, dataSize);
    case protobuf::cxx::FieldDataType::kUint64:
      return ReadPointerAs<uint64_t>(dataPtr, dataSize);
    default:
      GBF_ERROR(gbf::CanNotRunToHereError());
      return gbf::reflection::Value::nothing;
  }
}

bool PbConverterTool::PrimitivePointerSetAsValue(void* objPointer, cxx::FieldDataType dataType,
                                                 uint32_t dataOffset, uint32_t dataSize,
                                                 const gbf::reflection::Value& val) {
  char* dataPtr = (char*)objPointer + dataOffset;
  switch (dataType) {
    case protobuf::cxx::FieldDataType::kBool:
      return WritePointerAs<bool>(dataPtr, dataSize, val);
    case protobuf::cxx::FieldDataType::kDouble:
      return WritePointerAs<double>(dataPtr, dataSize, val);
    case protobuf::cxx::FieldDataType::kFixed32:
      return WritePointerAs<uint32_t>(dataPtr, dataSize, val);
    case protobuf::cxx::FieldDataType::kFixed64:
      return WritePointerAs<uint64_t>(dataPtr, dataSize, val);
    case protobuf::cxx::FieldDataType::kFloat:
      return WritePointerAs<float>(dataPtr, dataSize, val);
    case protobuf::cxx::FieldDataType::kInt32:
      return WritePointerAs<int32_t>(dataPtr, dataSize, val);
    case protobuf::cxx::FieldDataType::kInt64:
      return WritePointerAs<int64_t>(dataPtr, dataSize, val);
    case protobuf::cxx::FieldDataType::kSfixed32:
      return WritePointerAs<int32_t>(dataPtr, dataSize, val);
    case protobuf::cxx::FieldDataType::kSfixed64:
      return WritePointerAs<int64_t>(dataPtr, dataSize, val);
    case protobuf::cxx::FieldDataType::kSint32:
      return WritePointerAs<int32_t>(dataPtr, dataSize, val);
    case protobuf::cxx::FieldDataType::kSint64:
      return WritePointerAs<int64_t>(dataPtr, dataSize, val);
    case protobuf::cxx::FieldDataType::kUint32:
      return WritePointerAs<uint32_t>(dataPtr, dataSize, val);
    case protobuf::cxx::FieldDataType::kUint64:
      return WritePointerAs<uint64_t>(dataPtr, dataSize, val);
    default:
      GBF_ERROR(gbf::CanNotRunToHereError());
      return false;
  }
}

gbf::reflection::Value PbConverterTool::EnumPointerGetAsValue(const void* objPointer, uint32_t dataOffset,
                                                                  uint32_t dataSize) {
  const char* dataPtr = (const char*)objPointer + dataOffset;
  return ReadPointerAs<protobuf::pb_ssize_t>(dataPtr, dataSize);
}

bool PbConverterTool::EnumPointerSetAsValue(void* objPointer, uint32_t dataOffset, uint32_t dataSize,
                                            const gbf::reflection::Value& val) {
  char* dataPtr = (char*)objPointer + dataOffset;
  return WritePointerAs<protobuf::pb_ssize_t>(dataPtr, dataSize, val);
}

gbf::reflection::Value PbConverterTool::StringPointerGetAsValue(const void* objPointer, uint32_t dataOffset,
                                                                    uint32_t dataSize) {
  const char* dataPtr = (const char*)objPointer + dataOffset;
  std::string_view retStr(dataPtr, dataSize);
  return gbf::reflection::make_value(retStr);
}

bool PbConverterTool::StringPointerSetAsValue(void* objPointer, uint32_t dataOffset, uint32_t dataSize,
                                              const gbf::reflection::Value& val) {
  char* dataPtr = (char*)objPointer + dataOffset;
  if (gbf::reflection::value_can_convert_to<std::string_view>(val)) {
    ////memset(dataPtr, dataSize, 0);
    ////std::string_view sv = val.to<std::string_view>();
    ////pb_size_t needSize = std::min<pb_size_t>(sv.length(), dataSize);
    ////memcpy(dataPtr, sv.data(), needSize);
    auto csv = gbf::reflection::value_cast<std::string_view>(val);
    strncpy(dataPtr, csv.data(), dataSize);
    return true;
  }
  return false;
}

gbf::reflection::Value PbConverterTool::BytesPointerGetAsValue(const void* objPointer, uint32_t dataOffset,
                                                                   uint32_t dataSize) {
  const char* dataPtr = (const char*)objPointer + dataOffset;
  const PbBytesArrayType* arrayPtr = (const PbBytesArrayType*)dataPtr;

  std::string_view retStr((const char*)(arrayPtr->bytes), arrayPtr->size);

  return gbf::reflection::make_value(retStr);
}

bool PbConverterTool::BytesPointerSetAsValue(void* objPointer, uint32_t dataOffset, uint32_t dataSize,
                                             const gbf::reflection::Value& val) {
  char* dataPtr = (char*)objPointer + dataOffset;
  PbBytesArrayType* arrayPtr = (PbBytesArrayType*)dataPtr;
  if (gbf::reflection::value_can_convert_to<std::string_view>(val)) {
    auto sv = gbf::reflection::value_cast<std::string_view>(val);
    arrayPtr->size = std::min<pb_size_t>(sv.length(), dataSize);
    memcpy(arrayPtr->bytes, sv.data(), arrayPtr->size);
    return true;
  }
  return false;
}

gbf::reflection::Value PbConverterTool::MessagePointerGetAsValue(const void* objPointer, uint64_t typeId,
                                                                     uint32_t dataOffset, uint32_t dataSize) {
  void* realObjPointer = const_cast<char*>((const char*)objPointer + dataOffset);
  const auto* metaClass = gbf::reflection::query_meta_class_by_id(typeId);
  if (metaClass != nullptr) {
    return gbf::reflection::make_value(metaClass->GetUserObjectFromPointer(realObjPointer));
  } else {
    return gbf::reflection::Value::nothing;
  }
}

bool PbConverterTool::MessagePointerSetAsValue(void* objPointer, uint64_t typeId, uint32_t dataOffset,
                                               uint32_t dataSize, const gbf::reflection::Value& val) {
  void* realObjPointer = const_cast<char*>((const char*)objPointer + dataOffset);
  if (val.kind() == gbf::reflection::ValueKind::kUser) {
    const gbf::reflection::UserObject& otherUo = gbf::reflection::value_ref_as<const gbf::reflection::UserObject>(val);
    if (otherUo.GetClass().id() == typeId) {
      const auto& metaClass = otherUo.GetClass();
      auto uo = metaClass.GetUserObjectFromPointer(realObjPointer);
      const std::string_view kOpEqualName = "operator=";
      if (metaClass.HasFunction(kOpEqualName)) {
        const auto& func = uo.GetClass().GetFunction(kOpEqualName);
        gbf::reflection::cxx::Call(func, uo, val);
      } else {
        // deprecated, not use mode~~
        assert(dataSize == otherUo.GetClass().GetSizeof());
        // Just use memcpy(It's safe here?)
        memcpy(realObjPointer, otherUo.GetPointer(), dataSize);
      }

      return true;
    }
  }

  return false;
}

void PbConverterTool::ChangeHasFlag(void* objPointer, uint32_t dataOffset, uint32_t offsetForHas, bool hasFlag) {
  bool* hasFlagPtr = (bool*)((char*)objPointer + dataOffset - offsetForHas);
  *hasFlagPtr = hasFlag;
}

bool PbConverterTool::GetHasFlag(const void* objPointer, uint32_t dataOffset, uint32_t offsetForHas) {
  const bool* hasFlagPtr = (const bool*)((const char*)objPointer + dataOffset - offsetForHas);
  return *hasFlagPtr;
}

pb_size_t PbConverterTool::GetRepeatSize(const void* objPointer, uint32_t dataOffset, uint32_t offsetForSize) {
  const pb_size_t* sizePtr = (const pb_size_t*)((const char*)objPointer + dataOffset - offsetForSize);
  return *sizePtr;
}

void PbConverterTool::SetRepeatSize(void* objPointer, uint32_t dataOffset, uint32_t offsetForSize,
                                    pb_size_t repeatSize) {
  pb_size_t* sizePtr = (pb_size_t*)((char*)objPointer + dataOffset - offsetForSize);
  *sizePtr = repeatSize;
}

pb_size_t PbConverterTool::GetOneofCase(const void* objPointer, uint32_t dataOffset, uint32_t offsetForOneofCase) {
  const pb_size_t* sizePtr = (const pb_size_t*)((const char*)objPointer + dataOffset - offsetForOneofCase);
  return *sizePtr;
}

void PbConverterTool::SetOneofCase(void* objPointer, uint32_t dataOffset, uint32_t offsetForOneofCase,
                                   pb_size_t caseTag) {
  pb_size_t* sizePtr = (pb_size_t*)((char*)objPointer + dataOffset - offsetForOneofCase);
  *sizePtr = caseTag;
}

gbf::reflection::ValueKind PbConverterTool::GetValueKindFromPbType(protobuf::cxx::FieldDataType dataType) {
  return cxx::TypeHelper::ToReflectionValueKind(dataType);
}

bool PbConverterTool::IsPrimitiveOrEnumDefaultValue(const void* objPointer, uint32_t dataOffset, uint32_t dataSize) {
  const char* dataPtr = (const char*)objPointer + dataOffset;
  uint64_t test_val = 0;
  assert(dataSize <= sizeof(test_val));
  return memcmp(&test_val, dataPtr, dataSize) == 0;
}

bool PbConverterTool::IsStringDefaultValue(const void* objPointer, uint32_t dataOffset, uint32_t dataSize) {
  const char* dataPtr = (const char*)objPointer + dataOffset;
  return *dataPtr == 0;  // end with '/0'
}

bool PbConverterTool::IsBytesDefaultValue(const void* objPointer, uint32_t dataOffset, uint32_t dataSize) {
  const char* dataPtr = (const char*)objPointer + dataOffset;
  const PbBytesArrayType* arrayPtr = (const PbBytesArrayType*)dataPtr;

  return arrayPtr->size == 0;
}

bool PbConverterTool::IsMessageDefaultValue(const void* objPointer, uint32_t dataOffset, uint32_t dataSize) {
  const char* dataPtr = (const char*)objPointer + dataOffset;
  return gbf::MemoryUtil::IsMemoryZero(dataPtr, dataSize);
}

void PbConverterTool::SetPrimitiveOrEnumToDefaultValue(void* objPointer, uint32_t dataOffset, uint32_t dataSize) {
  char* dataPtr = (char*)objPointer + dataOffset;
  uint64_t test_val = 0;
  assert(dataSize <= sizeof(test_val));
  memcpy(dataPtr, &test_val, dataSize);
}

void PbConverterTool::SetStringToDefaultValue(void* objPointer, uint32_t dataOffset, uint32_t dataSize) {
  char* dataPtr = (char*)objPointer + dataOffset;
  *dataPtr = 0;  // end with '/0'
}

void PbConverterTool::SetBytesToDefaultValue(void* objPointer, uint32_t dataOffset, uint32_t dataSize) {
  char* dataPtr = (char*)objPointer + dataOffset;
  PbBytesArrayType* arrayPtr = (PbBytesArrayType*)dataPtr;
  arrayPtr->size = 0;
}

void PbConverterTool::SetMessageToDefaultValue(void* objPointer, uint32_t dataOffset, uint32_t dataSize) {
  char* dataPtr = (char*)objPointer + dataOffset;
  memset(dataPtr, 0, dataSize);
}

}  // namespace protobuf
