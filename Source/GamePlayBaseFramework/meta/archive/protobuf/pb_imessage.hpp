#pragma once
#include <string>
#include <string_view>
#include <vector>

#include "archive/protobuf/builder/pb_reflection_field_callback.h"
#include "archive/protobuf/pb_common.h"
#include "archive/protobuf/pb_pack_tool.hpp"
#include "reflection/reflection_export.hpp"

namespace protobuf {
template <class T>
class TMessage {
 public:
  TMessage() {}
  ~TMessage() {}

  const protobuf::PbMsgDesc* QueryMetaMessage() const { return T::GetMetaMessage(); }

  ////constexpr auto GetMetaTypeName() const
  ////{
  ////	return ::gbf::MetatypeHash::template name_pretty<T>();
  ////}

  protobuf::PackResult SerializeToArray(void* data, int size) const {
    return LurapbPackTool::PackToArray(QueryMetaMessage(), this, data, size);
  }

  protobuf::UnpackResult ParseFromArray(const void* data, int size) {
    return LurapbPackTool::UnpackFromArray(QueryMetaMessage(), this, data, size);
  }

  protobuf::PackResult SerializeToBuffer(gbf::ByteBufferWriter& buf) const {
    return LurapbPackTool::PackToBuffer(QueryMetaMessage(), this, buf);
  }

  protobuf::UnpackResult ParseFromBuffer(gbf::ByteBufferView buf) {
    return LurapbPackTool::UnpackFromBuffer(QueryMetaMessage(), this, buf);
  }

  std::string Utf8DebugString() const { return SerializeToLuaCode(); }
  std::string ShortDebugString() const { return SerializeToLuaCode(); }

  std::string InitializationErrorString() const { return "Not implement InitializationErrorString() here!"; }

  std::string SerializeToHexString(size_t capacity = 100 * 1024) const {
    return LurapbPackTool::PackToHexString(QueryMetaMessage(), this, capacity);
  }

  protobuf::UnpackResult ParseFromHexString(const std::string_view hexstr) {
    return LurapbPackTool::UnpackFromHexString(QueryMetaMessage(), this, hexstr);
  }

  std::string SerializeToLuaCode() const { return LurapbPackTool::PackToLuaCode(QueryMetaMessage(), this); }

  std::string SerializeToXml(bool compact, int depth) const {
    return LurapbPackTool::PackToXml(QueryMetaMessage(), this, compact, depth);
  }

  ////bool ParseFromXml(const std::string_view xmlstr)
  ////{
  ////	return LurapbPackTool::UnpackFromXml(QueryMetaMessage(), this, xmlstr);
  ////}
};

}  // namespace protobuf
