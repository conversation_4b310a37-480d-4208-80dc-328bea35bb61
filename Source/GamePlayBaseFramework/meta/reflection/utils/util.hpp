
#pragma once

#include <memory>
#include <type_traits>

#include "reflection/config.hpp"
#include "reflection/type.hpp"

namespace gbf {
namespace reflection {
namespace detail {

//------------------------------------------------------------------------------

template <bool C, typename T, typename F>
struct TIfC {
  using type = T;
};

template <typename T, typename F>
struct TIfC<false, T, F> {
  using type = F;
};

//------------------------------------------------------------------------------

class bad_conversion : public std::exception {};

template <typename T, typename F, typename O = void>
struct TConvertImpl {
  T operator()(const F& from) {
    const T result = static_cast<T>(from);
    return result;
  }
};

template <typename F>
Id to_str(F from) {
  return std::to_string(from);
}

template <typename F>
struct TConvertImpl<Id, F> {
  Id operator()(const F& from) { return detail::to_str(from); }
};

template <>
struct TConvertImpl<Id, bool> {
  Id operator()(const bool& from) {
    static const Id t("1"), f("0");
    return from ? t : f;
  }
};

GBF_REFLECTION_API bool Conv(const String& from, bool& to);
GBF_REFLECTION_API bool Conv(const String& from, char& to);
GBF_REFLECTION_API bool Conv(const String& from, unsigned char& to);
GBF_REFLECTION_API bool Conv(const String& from, short& to);
GBF_REFLECTION_API bool Conv(const String& from, unsigned short& to);
GBF_REFLECTION_API bool Conv(const String& from, int& to);
GBF_REFLECTION_API bool Conv(const String& from, unsigned int& to);
GBF_REFLECTION_API bool Conv(const String& from, long& to);
GBF_REFLECTION_API bool Conv(const String& from, unsigned long& to);
GBF_REFLECTION_API bool Conv(const String& from, long long& to);
GBF_REFLECTION_API bool Conv(const String& from, unsigned long long& to);
GBF_REFLECTION_API bool Conv(const String& from, float& to);
GBF_REFLECTION_API bool Conv(const String& from, double& to);

template <typename T>
struct TConvertImpl<T, Id,
                    typename std::enable_if<(std::is_integral<T>::value || std::is_floating_point<T>::value) &&
                                            !std::is_const<T>::value && !std::is_reference<T>::value>::type> {
  T operator()(const String& from) {
    T result;
    if (!Conv(from, result)) throw detail::bad_conversion();
    return result;
  }
};

template <typename T, typename F>
T Convert(const F& from) {
  return TConvertImpl<T, F>()(from);
}

//------------------------------------------------------------------------------
// make_unique supplied for compilers missing it (e.g. clang 5.0 on Travis Linux).
// source: http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2013/n3656.htm

template <class T>
struct _Unique_if {
  using _Single_object = std::unique_ptr<T>;
};

template <class T>
struct _Unique_if<T[]> {
  using _Unknown_bound = std::unique_ptr<T[]>;
};

template <class T, size_t N>
struct _Unique_if<T[N]> {
  using _Known_bound = void;
};

template <class T, class... Args>
typename _Unique_if<T>::_Single_object make_unique(Args&&... args) {
  return std::unique_ptr<T>(new T(std::forward<Args>(args)...));
}

template <class T>
typename _Unique_if<T>::_Unknown_bound make_unique(size_t n) {
  using U = typename std::remove_extent<T>::type;
  return std::unique_ptr<T>(new U[n]());
}

template <class T, class... Args>
typename _Unique_if<T>::_Known_bound make_unique(Args&&...) = delete;

//------------------------------------------------------------------------------
// Return true if all args true. Useful for variadic template expansions.
static inline bool AllTrue() { return true; }
static inline bool AllTrue(bool a0) { return a0; }
static inline bool AllTrue(bool a0, bool a1) { return a0 & a1; }
static inline bool AllTrue(bool a0, bool a1, bool a2) { return a0 & a1 & a2; }
static inline bool AllTrue(bool a0, bool a1, bool a2, bool a3) { return a0 & a1 & a2 & a3; }
static inline bool AllTrue(bool a0, bool a1, bool a2, bool a3, bool a4) { return a0 & a1 & a2 & a3 & a4; }
static inline bool AllTrue(bool a0, bool a1, bool a2, bool a3, bool a4, bool a5) { return a0 & a1 & a2 & a3 & a4 & a5; }
static inline bool AllTrue(bool a0, bool a1, bool a2, bool a3, bool a4, bool a5, bool a6) {
  return a0 & a1 & a2 & a3 & a4 & a5 & a6;
}
static inline bool AllTrue(bool a0, bool a1, bool a2, bool a3, bool a4, bool a5, bool a6, bool a7) {
  return a0 & a1 & a2 & a3 & a4 & a5 & a6 & a7;
}
static inline bool AllTrue(bool a0, bool a1, bool a2, bool a3, bool a4, bool a5, bool a6, bool a7, bool a8) {
  return a0 & a1 & a2 & a3 & a4 & a5 & a6 & a7 & a8;
}
static inline bool AllTrue(bool a0, bool a1, bool a2, bool a3, bool a4, bool a5, bool a6, bool a7, bool a8, bool a9) {
  return a0 & a1 & a2 & a3 & a4 & a5 & a6 & a7 & a8 & a9;
}
static inline bool AllTrue(bool a0, bool a1, bool a2, bool a3, bool a4, bool a5, bool a6, bool a7, bool a8, bool a9,
                           bool a10) {
  return a0 & a1 & a2 & a3 & a4 & a5 & a6 & a7 & a8 & a9 & a10;
}
static inline bool AllTrue(bool a0, bool a1, bool a2, bool a3, bool a4, bool a5, bool a6, bool a7, bool a8, bool a9,
                           bool a10, bool a11) {
  return a0 & a1 & a2 & a3 & a4 & a5 & a6 & a7 & a8 & a9 & a10 & a11;
}
static inline bool AllTrue(bool a0, bool a1, bool a2, bool a3, bool a4, bool a5, bool a6, bool a7, bool a8, bool a9,
                           bool a10, bool a11, bool a12) {
  return a0 & a1 & a2 & a3 & a4 & a5 & a6 & a7 & a8 & a9 & a10 & a11 & a12;
}
static inline bool AllTrue(bool a0, bool a1, bool a2, bool a3, bool a4, bool a5, bool a6, bool a7, bool a8, bool a9,
                           bool a10, bool a11, bool a12, bool a13) {
  return a0 & a1 & a2 & a3 & a4 & a5 & a6 & a7 & a8 & a9 & a10 & a11 & a12 & a13;
}
static inline bool AllTrue(bool a0, bool a1, bool a2, bool a3, bool a4, bool a5, bool a6, bool a7, bool a8, bool a9,
                           bool a10, bool a11, bool a12, bool a13, bool a14) {
  return a0 & a1 & a2 & a3 & a4 & a5 & a6 & a7 & a8 & a9 & a10 & a11 & a12 & a13 & a14;
}
static inline bool AllTrue(bool a0, bool a1, bool a2, bool a3, bool a4, bool a5, bool a6, bool a7, bool a8, bool a9,
                           bool a10, bool a11, bool a12, bool a13, bool a14, bool a15) {
  return a0 & a1 & a2 & a3 & a4 & a5 & a6 & a7 & a8 & a9 & a10 & a11 & a12 & a13 & a14 & a15;
}

// Get value type enum as string description.
GBF_REFLECTION_API std::string_view ValueKindAsString(ValueKind t);

GBF_REFLECTION_API ValueKind ValueKindFromString(std::string_view str);

template <class... Ts>
struct TOverloaded : Ts... {
  using Ts::operator()...;
};
template <class... Ts>
TOverloaded(Ts...) -> TOverloaded<Ts...>;

struct ClassOffsetCalculator {
  template<typename TDerieved, typename TBase>
  static int Calc() {
    char dummy[8];
    TDerieved* asDerived = reinterpret_cast<TDerieved*>(dummy);    //Just use a magic value here as dummy      
    TBase* asBase = static_cast<TBase*>(asDerived);
    return static_cast<int>(reinterpret_cast<char*>(asBase) - reinterpret_cast<char*>(asDerived));
  }
};


}  // namespace detail
}  // namespace reflection
}  // namespace gbf
