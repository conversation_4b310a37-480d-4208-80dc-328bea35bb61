
#pragma once

#include <memory>
#include <type_traits>

namespace gbf {
namespace reflection {
namespace detail {

/**
 * \brief Utility class which tells at compile-time if a type T is a smart pointer to a type U
 *
 * To detect a smart pointer type, we check using SFINAE if T implements an operator -> returning a U*
 */
template <typename T, typename U>
struct TIsSmartPointer {
  static constexpr bool value = false;
};

template <typename T, typename U>
struct TIsSmartPointer<std::unique_ptr<T>, U> {
  static constexpr bool value = true;
};

template <typename T, typename U>
struct TIsSmartPointer<std::shared_ptr<T>, U> {
  static constexpr bool value = true;
};

/**
 * \class DataType
 *
 * \brief Helper structure used to extract the raw type of a composed type
 *
 * DataType<T> recursively removes const, reference and pointer modifiers from the given type.
 * In other words:
 *
 * \li DataType<T>::Type == T
 * \li DataType<const T>::Type == DataType<T>::Type
 * \li DataType<T&>::Type == DataType<T>::Type
 * \li DataType<const T&>::Type == DataType<T>::Type
 * \li DataType<T*>::Type == DataType<T>::Type
 * \li DataType<const T*>::Type == DataType<T>::Type
 *
 * \remark DataType is able to detect smart pointers and properly extract the stored type
 */

// Generic version -- T doesn't match with any of our specialization, and is thus considered a raw data type
//  - int -> int, int[] -> int, int* -> int.
template <typename T, typename E = void>
struct TDataType {
  using Type = T;
};

// const
template <typename T>
struct TDataType<const T> : public TDataType<T> {};

template <typename T>
struct TDataType<T&> : public TDataType<T> {};

template <typename T>
struct TDataType<T*> : public TDataType<T> {};

template <typename T, size_t N>
struct TDataType<T[N]> : public TDataType<T> {};

// smart pointer
template <template <typename> class T, typename U>
struct TDataType<T<U>, typename std::enable_if<TIsSmartPointer<T<U>, U>::value>::type> {
  using Type = typename TDataType<U>::Type;
};

}  // namespace detail

template <class T>
T* GetPointer(T* p) {
  return p;
}

template <class T>
T* GetPointer(std::unique_ptr<T> const& p) {
  return p.get();
}

template <class T>
T* GetPointer(std::shared_ptr<T> const& p) {
  return p.get();
}




}  // namespace reflection
}  // namespace gbf
