#pragma once

#include <cassert>
#include <string>
#include <unordered_map>

#include "reflection/builder/function/cxx_func_caller.hpp"
#include "reflection/builder/function/cxx_ctor_caller.hpp"

#include "reflection/meta/meta_class.hpp"
#include "reflection/utils/userdata.hpp"
#include "reflection/traits/type_traits_extend.hpp"
#include "reflection/type.hpp"
#include "reflection/vtable/object_vtable.hpp"
#include "core/imodules/ilog_module.h"
#include "core/utils/string_util.h"

namespace gbf {
namespace reflection {

template <typename T>
class FunctionBuilder {
 public:
  FunctionBuilder(MetaClass* meta_class): target_(meta_class) {}

  template <typename F, typename... P>
  Function* function(IdRef name, F function, P... policies) 
  {
   auto ifunc = target_->functions_table_.find(std::string(name));
   auto func = AddOrGetFunction(name);
   if (GBF_UNLIKELY(ifunc != target_->functions_table_.end()))   
   {
       func->ClearByType(FuncLanguageType::AsCxx);
       func->set_call_type(FuncCallType::OverloadObjectCall);
       gbf::reflection::TSwAllowT tmpobj{func->AddCaller(FuncLanguageType::AsCxx, NewFunctionCaller(func.get(), function, policies...))};
       return func.get();
   }
   else
   {
       func->ClearByType(FuncLanguageType::AsCxx);
       func->set_call_type(FuncCallType::ObjectCall);
       func->AddCaller(FuncLanguageType::AsCxx, NewFunctionCaller(func.get(), function, policies...));
       return func.get();
   }
  }

  template <typename F, typename... P>
  Function* static_function(IdRef name, F function, P... policies) {
    auto func = AddOrGetFunction(name);
    func->ClearByType(FuncLanguageType::AsCxx);
    func->set_call_type(FuncCallType::StaticCall);
    func->AddCaller(FuncLanguageType::AsCxx, NewFunctionCaller(func.get(), function, policies...));
    return func.get();
  }

  template <typename... F>
  Function* overload(IdRef name, F... funcs) {
    auto func = AddOrGetFunction(name);
    func->ClearByType(FuncLanguageType::AsCxx);
    func->set_call_type(FuncCallType::OverloadObjectCall);
    gbf::reflection::TSwAllowT tmpobj{ (func->AddCaller(FuncLanguageType::AsCxx, NewFunctionCaller(func.get(), funcs)))... };
    return func.get();
  }

  /**
   * \brief Declare a constructor for the metaclass.
   *
   * Variable number of parameters can be passed.
   *
   * \return Reference to this, in order to chain other calls
   */
  template <typename... A>
  Constructor* constructor() {
    auto& ctor = const_cast<Constructor&>(target_->GetCtor());
    ctor.AddCaller(FuncLanguageType::AsCxx, new detail::cxx::TCtorCallerImpl<T, A...>(&ctor));
    return &ctor;
  }


 private:
  MetaClass::FunctionPtr AddOrGetFunction(IdRef name) {
    // Retrieve the class' functions indexed by name
    MetaClass::FunctionPtr func;
    // First remove any function that already exists with the same name
    auto ifunc = target_->functions_table_.find(std::string(name));
    if (GBF_UNLIKELY(ifunc != target_->functions_table_.end())) {
      WRN_M(LOG_MODULE_RELFECTION, "register class:%s function:%s twice!", target_->name().data(), name.data());
      ifunc->second = std::make_shared<Function>(name, std::string_view(target_->name()));
      ifunc->second->set_tag_number(target_->NextFunctionTagNumber());
      return ifunc->second;
    } else {
      // set tag number
      func = std::make_shared<Function>(name, std::string_view(target_->name()));
      func->set_tag_number(target_->NextFunctionTagNumber());
      // Insert the new function
      target_->AddFunction(name, func);
    }

    return func;
  }

  // Used by FunctionBuilder to create new function instance.
  template <typename F, typename... P>
  static inline IFuncCaller* NewFunctionCaller(Function* func, F function, P... return_policies) {
    using FuncTraits = detail::TFunctionTraits<F>;

    static_assert(FuncTraits::kind != FunctionKind::kNone, "Type is not a function");

    return new detail::cxx::TFunctionCallerImpl<F, FuncTraits, P...>(func, function);
  }

  ////MetaClass::FunctionPtr AddOrGetStaticProperty(IdRef name) {
  ////  MetaClass::FunctionTable& sps = target_->static_properties_;
  ////  auto iter = sps.find(name.data());
  ////  if (GBF_UNLIKELY(iter != sps.end())) {
  ////    WRN_M(LOG_MODULE_RELFECTION, "register class:%s function:%s twice!", target_->name().c_str(), function->name().c_str());
  ////    return iter->second;
  ////  }

  ////  auto func = std::make_shared<Function>(name, target_->name());
  ////  sps.emplace(std::make_pair(name, func));
  ////  return func;
  ////}
 private:
  MetaClass* target_ = nullptr;   // Target metaclass to fill
};

}
}

