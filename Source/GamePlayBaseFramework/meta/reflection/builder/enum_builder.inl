#pragma once

template <class T>
::gbf::reflection::MetaEnum const* __enum_of() {
  return ::gbf::reflection::query_meta_enum<T>();
}

template <class T>
auto __register_enum(std::string_view name = std::string_view()) {
  auto* meta_enum = const_cast<::gbf::reflection::MetaEnum*>(::gbf::reflection::query_meta_enum<T>());
  if (GBF_LIKELY(name.length() != 0)) {
    ::gbf::reflection::detail::bind_enum_alias_name(meta_enum, std::string(name));
  }
  return ::gbf::reflection::EnumBuilder(*meta_enum);
}

template <class T>
auto __append_register_enum() {
  auto* meta_enum = const_cast<::gbf::reflection::MetaEnum*>(::gbf::reflection::query_meta_enum<T>());
  return ::gbf::reflection::EnumBuilder(*meta_enum);
}
