
#include "reflection/meta/array_property.hpp"


namespace gbf {
namespace reflection {

ArrayProperty::ArrayProperty(IdRef name, ValueKind elementType, TypeId typeIndex, TypeId elementTypeIndex)
    : Property(name, ValueKind::kArray, typeIndex),
      element_type_(elementType),
      dynamic_(false),
      element_type_index_(elementTypeIndex) {}

ArrayProperty::~ArrayProperty() {}

size_t ArrayProperty::Size(const UserObject& object) const {
  // Check if the property is readable
  if (!IsReadable()) PONDER_ERROR(ForbiddenRead(name()));

  return GetSize(object);
}

void ArrayProperty::ReSize(const UserObject& object, size_t newSize) const {
  // Check if the array is dynamic
  if (!dynamic()) PONDER_ERROR(ForbiddenWrite(name()));

  // Check if the property is writable
  if (!IsWritable()) PONDER_ERROR(ForbiddenWrite(name()));

  SetSize(object, newSize);
}

Value ArrayProperty::Get(const UserObject& object, size_t index) const {
  // Check if the property is readable
  if (!IsReadable()) PONDER_ERROR(ForbiddenRead(name()));

  // Make sure that the index is not out of range
  const size_t range = Size(object);
  if (index >= range) PONDER_ERROR(OutOfRange(index, range));

  return GetElement(object, index);
}

void ArrayProperty::Set(const UserObject& object, size_t index, const Value& value) const {
  // Check if the property is writable
  if (!IsWritable()) PONDER_ERROR(ForbiddenWrite(name()));

  // Check if the index is in range
  const size_t range = Size(object);
  if (index >= range) PONDER_ERROR(OutOfRange(index, range));

  return SetElement(object, index, value);
}

void ArrayProperty::Insert(const UserObject& object, size_t before, const Value& value) const {
  // Check if the array is dynamic
  if (!dynamic()) PONDER_ERROR(ForbiddenWrite(name()));

  // Check if the property is writable
  if (!IsWritable()) PONDER_ERROR(ForbiddenWrite(name()));

  // Check if the index is in range
  const size_t range = Size(object) + 1;
  if (before >= range) PONDER_ERROR(OutOfRange(before, range));

  return InsertElement(object, before, value);
}

void ArrayProperty::Remove(const UserObject& object, size_t index) const {
  // Check if the array is dynamic
  if (!dynamic()) PONDER_ERROR(ForbiddenWrite(name()));

  // Check if the property is writable
  if (!IsWritable()) PONDER_ERROR(ForbiddenWrite(name()));

  // Check if the index is in range
  const size_t range = Size(object);
  if (index >= range) PONDER_ERROR(OutOfRange(index, range));

  return RemoveElement(object, index);
}


Value ArrayProperty::GetValue(const UserObject& object) const {
  ////// Return first element
  ////return Get(object, 0);

  PONDER_ERROR(ArrayPropertyCanNotSupportNormalGetOrSet{});
}

void ArrayProperty::SetValue(const UserObject& object, const Value& value) const {
  ////// Set first element
  ////Set(object, 0, value);

  PONDER_ERROR(ArrayPropertyCanNotSupportNormalGetOrSet{});
}

}  // namespace reflection
}  // namespace gbf
