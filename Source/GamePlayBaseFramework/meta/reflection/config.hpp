/****************************************************************************
**
** This file is part of the <PERSON><PERSON> library, formerly CAMP.
**
** The MIT License (MIT)
**
** Copyright (C) 2009-2014 TEGESO/TEGESOFT and/or its subsidiary(-ies) and mother company.
** Copyright (C) 2015-2020 Nick Trout.
**
** Permission is hereby granted, free of charge, to any person obtaining a copy
** of this software and associated documentation files (the "Software"), to deal
** in the Software without restriction, including without limitation the rights
** to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
** copies of the Software, and to permit persons to whom the Software is
** furnished to do so, subject to the following conditions:
** 
** The above copyright notice and this permission notice shall be included in
** all copies or substantial portions of the Software.
** 
** THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
** IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
** FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
** AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
** LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
** OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
** THE SOFTWARE.
**
****************************************************************************/

#pragma once

#include "reflection/reflection_export.hpp"

// Check we have C++17 support. Report issues to Github project.
#if defined(_MSC_VER)
    // Earlier MSVC compilers lack features or have C++17 bugs.
    static_assert(_MSC_VER >= 1911, "MSVC 2017 required");
    // We disable some annoying warnings of VC++
#   pragma warning(disable: 4275) // non dll-interface class 'X' used as base for dll-interface class 'Y'
#   pragma warning(disable: 4251) // class 'X' needs to have dll-interface to be used by clients of class 'Y'
#   include <ostream> // In future MSVC, <string> doesn't transitively <ostream>, ponder will
                      // compile failed with error C2027 and C2065, so add <ostream>.
#endif


#include "reflection/traits/id_traits.hpp"
#include <cassert>

