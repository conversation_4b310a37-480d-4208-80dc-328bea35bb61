#pragma once

#include <variant>

#include "reflection/reflection_fwd.hpp"

#include "reflection/error/errors.hpp"
#include "reflection/objects/userobject.hpp"
#include "reflection/objects/args.hpp"
#include "reflection/storage/data_holder.hpp"
#include "reflection/vtable/object_vtable.hpp"
#include "reflection/traits/type_traits.hpp"
#include "reflection/objects/classcast.hpp"
#include "reflection/objects/value.hpp"
#include "reflection/objects/valueimpl.hpp"
#include "reflection/meta/meta_class.hpp"

////#include "reflection/objects/objectholder.hpp"
#include "reflection/utils/util.hpp"
#include "memory/gc/gc_make.hpp"
#include "reflection/objects/rtti_base.hpp"



namespace gbf {
namespace reflection {

//-------------------------------------------------------------------------------------
template <typename T, typename StoragePolicy /* = default_storage_policy*/>
inline UserObject make_user_object(T& object, StoragePolicy policy /* = StoragePolicy{} */) {
  using PropTraits = typename detail::TTypeTraits<T>;
  using RealDataType = typename PropTraits::DataType;
  static_assert(!PropTraits::kIsRef, "Cannot make reference to reference");
  static_assert(std::is_base_of_v<storage_policy, StoragePolicy> && "Found not support storage policy here!");

  //special handle for remote_storage_policy && RttiBase pointer
  if constexpr (std::is_same_v<StoragePolicy, remote_storage_policy> && std::is_base_of_v<RttiBase, RealDataType>) {
    //Only external object need restore as a right type, others just do copy construct here
    return __box_rtti_object(&object);
  } else {
    auto* meta_info = query_meta_class<RealDataType>()->template GetUsedMetaInfo<StoragePolicy>();
    auto obj = UserObject{};
    obj.ResetMetaInfoInternal(meta_info);
    meta_info->obj_vtable->ctor_from_other_object_(&obj.GetDataHolderInternal(), &object, meta_info->meta_class);
    return obj;
  }
}

template <typename T, typename StoragePolicy /* = default_storage_policy*/>
inline UserObject make_user_object(T* object, StoragePolicy policy /* = StoragePolicy{} */) {
  using PropTraits = typename detail::TTypeTraits<T>;
  using RealDataType = typename PropTraits::DataType;
  static_assert(!PropTraits::kIsRef, "Cannot make reference to reference");
  static_assert(std::is_base_of_v<storage_policy, StoragePolicy>, "Found not support storage policy here!");

  // special handle for remote_storage_policy && RttiBase pointer
  if constexpr (std::is_same_v<StoragePolicy, remote_storage_policy> && std::is_base_of_v<RttiBase, RealDataType>) {
      if (!object->__has_rtti())
      {
          object->__bind_rtti_info(::gbf::reflection::query_meta_class<T>(), ::gbf::reflection::StorageType::StorageRemote, next_reflection_obj_id());
      }
    //Only external object need restore as a right type, others just do copy construct here
    return __box_rtti_object(object);
  } else {
    ////return UserObject{ query_meta_class<RealDataType>()->GetUsedMetaInfo<StoragePolicy>(), object };

    auto* meta_info = query_meta_class<RealDataType>()->template GetUsedMetaInfo<StoragePolicy>();
    auto obj = UserObject{};
    obj.ResetMetaInfoInternal(meta_info);
    meta_info->obj_vtable->ctor_from_other_object_(&obj.GetDataHolderInternal(), object, meta_info->meta_class);
    return obj;
  }
}

template <typename T, typename StoragePolicy /* = default_storage_policy*/>
inline UserObject make_user_object(T&& object, StoragePolicy policy /* = StoragePolicy{} */) {
  using PropTraits = typename detail::TTypeTraits<T>;
  using RealDataType = typename PropTraits::DataType;
  static_assert(!PropTraits::kIsRef, "Cannot make reference to reference");
  static_assert(std::is_base_of_v<storage_policy, StoragePolicy>, "Found not support storage policy here!");

  ////return UserObject{query_meta_class<RealDataType>()->GetUsedMetaInfo<StoragePolicy>(), &object};
  auto* meta_info = query_meta_class<RealDataType>()->template GetUsedMetaInfo<StoragePolicy>();
  auto obj = UserObject{};
  obj.ResetMetaInfoInternal(meta_info);
  meta_info->obj_vtable->ctor_from_other_object_(&obj.GetDataHolderInternal(), &object, meta_info->meta_class);
  return obj;
}

template <typename T>
inline T& user_object_ref_as(const UserObject& obj) {
  static_assert(!std::is_pointer_v<T> && !std::is_reference_v<T>, "user_object_ref_as<T>() T can not be pointer or reference type!");
  using NoConstT = std::remove_const_t<T>;
  constexpr auto tid = ::gbf::MetatypeHash::Hash<NoConstT>();
  auto* objptr = obj.GetPointerForBaseWithCache(tid); //obj.GetMetaInfo()->obj_vtable->to_pointer_(&(obj.GetDataHolderInternal()));
  return *reinterpret_cast<T*>(objptr);
}

template <typename T>
inline UserObject make_external_user_object(T* object) {
  return make_user_object(object, remote_storage_policy{});
}

template <typename T>
inline UserObject make_external_user_object(T& object) {
  return make_user_object(object, remote_storage_policy{});
}

//A RttiBase make_rtti implement
template<typename T, typename... A>
inline std::enable_if_t< std::is_base_of_v<RttiBase, T>, std::shared_ptr<T>> make_shared_with_rtti(A&&... args) {
  auto ptr = std::make_shared<T>(std::forward<A>(args)...);
  ptr->__bind_rtti_info(query_meta_class<T>(), StorageType::StorageRemoteShared, next_reflection_obj_id());
  return ptr;
}

//A Not RttiBase make_rtti implement
template <typename T, typename... A>
inline std::enable_if_t<!std::is_base_of_v<RttiBase, T>, std::shared_ptr<T>> make_shared_with_rtti(A&&... args) {
  return std::make_shared<T>(std::forward<A>(args)...);
  ////ptr->__bind_rtti_info(query_meta_class<T>(), StorageType::StorageRemoteShared, next_reflection_obj_id());
  ////return ptr;
}

template<typename T>
inline UserObject __box(const std::shared_ptr<T>& obj) {
  using PropTraits = typename detail::TTypeTraits<T>;
  using RealDataType = typename PropTraits::DataType;
  static_assert(!PropTraits::kIsRef, "Cannot make reference to reference");

  if constexpr (std::is_base_of_v<RttiBase, RealDataType>) {
    using RealType = typename std::shared_ptr<RttiBase>;

    DataHolder holder;
    auto* tl = reinterpret_cast<RealType*>(holder.get_buffer_pointer());
    new (tl) RealType{obj};

    const MetaClass* meta = obj->__has_rtti() ? obj->__rtti_meta() : nullptr;
    if (meta == nullptr) {
      meta = query_meta_class<RealDataType>();
      obj->__bind_rtti_info(meta, StorageType::StorageRemoteShared, next_reflection_obj_id());
    }
    // Do not need release here, holder dtor by previous call.
    // tl->~RealType();
    return UserObject{meta->GetUsedMetaInfoByType(StorageType::StorageRemoteShared), std::move(holder)};
  }
  else {
    using RealType = typename std::shared_ptr<RealDataType>;
    DataHolder holder;
    auto* tl = reinterpret_cast<RealType*>(holder.get_buffer_pointer());
    new (tl) RealType{obj};

    const MetaClass* meta = query_meta_class<RealDataType>();
    // Do not need release here, holder dtor by previous call.
    // tl->~RealType();
    return UserObject{ meta->GetUsedMetaInfoByType(StorageType::StorageRemoteShared), std::move(holder) };
  }
}

template<typename T>
inline UserObject __box(const gc::global_ptr<T>& obj) {
  using PropTraits = typename detail::TTypeTraits<T>;
  using RealDataType = typename PropTraits::DataType;
  static_assert(!PropTraits::kIsRef, "Cannot make reference to reference");

  if constexpr (std::is_base_of_v<RttiBase, RealDataType>) {
    using RealType = typename gc::global_ptr<RttiBase>;

    DataHolder holder;
    auto* tl = reinterpret_cast<RealType*>(holder.get_buffer_pointer());
    new (tl) RealType{obj};

    const MetaClass* meta = obj->__has_rtti() ? obj->__rtti_meta() : nullptr;
    if (meta == nullptr) {
      //create from external, we need bind meta here
      meta = query_meta_class<RealDataType>();
      obj->__bind_rtti_info(meta, StorageType::StorageGc, next_reflection_obj_id());
    }
    // Do not need release here, holder dtor by previous call.
    // tl->~RealType();
    return UserObject{meta->GetUsedMetaInfoByType(StorageType::StorageRemoteShared), std::move(holder)};
  } else {
    using RealType = typename std::shared_ptr<RealDataType>;
    DataHolder holder;
    auto* tl = reinterpret_cast<RealType*>(holder.get_buffer_pointer());
    new (tl) RealType{obj};

    const MetaClass* meta = query_meta_class<RealDataType>();
    // Do not need release here, holder dtor by previous call.
    // tl->~RealType();
    return UserObject{meta->GetUsedMetaInfoByType(StorageType::StorageRemoteShared), std::move(holder)};
  }
}

inline UserObject make_user_object_by_class(const MetaClass* meta)
{
    return UserObject{meta->GetUsedMetaInfoByType(StorageType::StorageRemoteShared)};
}

inline UserObject __box_rtti_object(RttiBase* obj) {
  if (!obj->__has_rtti()) {
   // PONDER_ERROR(RttiObjectNotBindWithValidInfo("Unknown Type"));
  }

  switch(obj->__storage_type()) {
  case StorageType::StorageRemoteShared:
    return __box(obj->shared_from_this());
  case StorageType::StorageGc: {
    gc::global_ptr<RttiBase> ptr = obj->gc_from_this();
    return __box(ptr);
  }
  case StorageType::StorageRemote: {
    DataHolder holder;
    holder.pobj_ = obj;
    return UserObject{obj->__used_meta_info(), std::move(holder)};
  }
  default:
    //Do not support here, box failed!
    return UserObject::nothing;
  }
}

template<typename T>
inline bool __unbox(const UserObject& obj, std::shared_ptr<T>& out_smart) {
  ////static_assert(std::is_base_of_v<T, Real>, "Must a valid base class for __unbox()!");
  out_smart.reset();
  
  //null object
  if (obj.IsEmpty()) return false;

  //storage type not matched
  if (obj.storage_type() != StorageType::StorageRemoteShared) return false;

  //not this type~~
  if constexpr(std::is_base_of_v<RttiBase, T>) {
    if (!obj.IsRttiObject()) {
      PONDER_ERROR(RttiObjectNotBindWithValidInfo("Unknown Type"));
    }
    const auto& holder = obj.GetDataHolderInternal();
    if (!std::is_same_v<RttiBase, T> && !obj.IsTypeOf<T>()) return false;
    
    using RealType = typename std::shared_ptr<RttiBase>;
    auto* tl = reinterpret_cast<RealType*>(holder.get_buffer_pointer());
    out_smart = std::static_pointer_cast<T>(*tl);
  }
  else {
    //Only the same type support in not rtti object for safe here
    if (!obj.template IsExplicitTypeOf<T>()) return false;

    const auto& holder = obj.GetDataHolderInternal();
    using RealType = typename std::shared_ptr<T>;
    auto* tl = reinterpret_cast<RealType*>(holder.get_buffer_pointer());
    out_smart = *tl;
  }

  return out_smart.operator bool();
}

template <typename T>
inline bool __unbox(const UserObject& obj, gc::global_ptr<T>& out_smart) {
  out_smart.clear_value();

  // null object
  if (obj.IsEmpty()) return false;

  // storage type not matched
  if (obj.storage_type() != StorageType::StorageGc) return false;

  // not this type~~
  if constexpr (std::is_base_of_v<RttiBase, T>) {
    if (!obj.IsRttiObject()) {
      PONDER_ERROR(RttiObjectNotBindWithValidInfo("Unknown Type"));
    }
    const auto& holder = obj.GetDataHolderInternal();
    if (!std::is_same_v<RttiBase, T> && !obj.IsTypeOf<T>()) return false;

    using RealType = typename gc::global_ptr<RttiBase>;
    auto* tl = reinterpret_cast<RealType*>(holder.get_buffer_pointer());
    out_smart = tl->cast_static<T>();
  } else {
    // Only the same type support in not rtti object for safe here
    if (!obj.template IsExplicitTypeOf<T>()) return false;

    const auto& holder = obj.GetDataHolderInternal();
    using RealType = typename gc::global_ptr<T>;
    auto* tl = reinterpret_cast<RealType*>(holder.get_buffer_pointer());
    out_smart = *tl;
  }

  return out_smart.operator bool();
}

class UOPointerWrapper
{
public:
    UOPointerWrapper() {}
    UOPointerWrapper(void* val) { ptr = val; }
    UOPointerWrapper(void* val, UserObject&& src)
    {
        ptr = val;
        holder = std::move(src);
    }

    void* ptr = nullptr;
    UserObject holder;
};

inline UserObject make_raw_pointer_user_object(void* val)
{
    return __box(std::make_shared<reflection::UOPointerWrapper>(val));
}

inline UserObject make_raw_pointer_user_object(void* val, UserObject&& src)
{
    return __box(std::make_shared<reflection::UOPointerWrapper>(val, std::move(src)));
}

template<typename T>
T* get_raw_pointer_user_object(const UserObject& val)
{
    return reinterpret_cast<T*>(val.Ref<reflection::UOPointerWrapper>().ptr);
}

}  // namespace reflection
}  // namespace gbf

