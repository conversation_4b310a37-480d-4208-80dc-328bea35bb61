

#pragma once

#include "reflection/config.hpp"

namespace gbf {
namespace reflection {
class MetaClass;

/**
 * \brief Convert a pointer from a source metaclass to a related target metaclass
 *
 * \param pointer Source pointer to convert
 * \param sourceClass Source metaclass to convert from
 * \param targetClass Target metaclass to convert to
 *
 * \return Converted pointer, or 0 on failure
 *
 * \throw ClassUnrelated sourceClass is not a base nor a derived of targetClass
 */
GBF_REFLECTION_API void* DoClassCast(void* pointer, const MetaClass& sourceClass, const MetaClass& targetClass);

}  // namespace reflection
}  // namespace gbf
