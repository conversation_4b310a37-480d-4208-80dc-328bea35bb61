#pragma once

#include "reflection/type.hpp"
#include "reflection/objects/valuemapper.hpp"

#pragma warning(push)
#pragma warning(disable : 4702 4100)

namespace gbf {
namespace reflection {
namespace detail {

/**
 * \brief Value visitor which converts the stored value to a type T
 */
template <typename T>
struct TConvertVisitor {
  using result_type = T;

  template <typename U>
  T operator()(const U& value) const {
    // Dispatch to the proper ValueConverter
    return reflection::detail::TValueMapper<T>::from(value);
  }

  // Optimization when source type is the same as requested type
  T operator()(const T& value) const { return value; }
  T operator()(T&& value) const { return std::move(value); }

  T operator()(NoType) const {
    // Error: trying to convert an empty value
    PONDER_ERROR(BadType(ValueKind::kNone, MapType<T>()));
  }
};

/**
 * \brief Binary value visitor which compares two values using operator <
 */
struct LessThanVisitor {
  using result_type = bool;

  template <typename T, typename U>
  bool operator()(const T&, const U&) const {
    // Different types : compare types identifiers
    return MapType<T>() < MapType<U>();
  }

  template <typename T>
  bool operator()(const T& v1, const T& v2) const {
    // Same types : compare values
    return v1 < v2;
  }

  bool operator()(NoType, NoType) const {
    // No type (empty values) : they're considered equal
    return false;
  }
};

/**
 * \brief Binary value visitor which compares two values using operator ==
 */
struct EqualVisitor {
  using result_type = bool;

  template <typename T, typename U>
  bool operator()(const T&, const U&) const {
    // Different types : not equal
    return false;
  }

  template <typename T>
  bool operator()(const T& v1, const T& v2) const {
    // Same types : compare values
    return v1 == v2;
  }

  bool operator()(NoType, NoType) const {
    // No type (empty values) : they're considered equal
    return true;
  }
};

struct PointerVisitor {
  void* operator()(NoType) const { return nullptr; }

  void* operator()(const bool& val) const { return nullptr; }

  void* operator()(const int64_t& val) const { return nullptr; }

  void* operator()(const double& val) const { return nullptr; }

  void* operator()(const reflection::String& val) const { return const_cast<char*>(val.c_str()); }

  void* operator()(const EnumObject& val) const { return nullptr; }

  void* operator()(const UserObject& val) const { return val.GetPointer(); }

  void* operator()(const ArrayObject& val) const { return nullptr; }

  void* operator()(const BuildInValueRef& val) const { return const_cast<void*>(val.GetRef<void>()); }
};

}  // namespace detail
}  // namespace reflection
}  // namespace gbf
#pragma warning(pop)