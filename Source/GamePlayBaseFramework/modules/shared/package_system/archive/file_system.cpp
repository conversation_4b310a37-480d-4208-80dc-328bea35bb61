#include "archive/file_system.hpp"

#include <sys/stat.h>

#include "core/utils/path_tool.hpp"
#include "core/imodules/ilog_module.h"


#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_LINUX || GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_APPLE || GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_APPLE_IOS || \
    GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_ANDROID
#include <sys/param.h>
#include "archive/search_ops.hpp"
#define MAX_PATH MAXPATHLEN
#endif

#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32
#define WIN32_LEAN_AND_MEAN
#if !defined(NOMINMAX) && defined(_MSC_VER)
#define NOMINMAX  // required to stop windows.h messing up std::min
#endif
#include <direct.h>
#include <io.h>
#include <windows.h>
#endif

namespace gbf {

bool FileSystemArchive::msIgnoreHidden = true;

//-----------------------------------------------------------------------
FileSystemArchive::FileSystemArchive(std::string_view name, std::string_view archType, bool readOnly) : Archive(name, archType) {
  // Even failed attempt to write to read only location violates Apple AppStore validation process.
  // And successful writing to some probe file does not prove that whole location with subfolders
  // is writable. Therefore we accept read only flag from outside and do not try to be too smart.
  mReadOnly = readOnly;
  mName = PathTool::NormalizeFilePath(mName);
}
//-----------------------------------------------------------------------
bool FileSystemArchive::IsCaseSensitive(void) const { return true; }
//-----------------------------------------------------------------------
static bool IsReservedDir(const char* fn) { return (fn[0] == '.' && (fn[1] == 0 || (fn[1] == '.' && fn[2] == 0))); }
//-----------------------------------------------------------------------
static bool IsAbsolutePath(const char* path) {
#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32
  if (isalpha(uchar(path[0])) && path[1] == ':') return true;
#endif
  return path[0] == '/' || path[0] == '\\';
}
//-----------------------------------------------------------------------
static std::string ConcatenatePath(std::string_view base, std::string_view name) {
  if (base.empty() || IsAbsolutePath(name.data())) {
    return std::string(name);
  } else {
    if (base.find_last_of('/') != base.length() - 1) {
      return StringUtil::Format("%s/%s", base.data(), name.data());
    } else {
      return StringUtil::Format("%s%s", base.data(), name.data());
    }
  }
}
//-----------------------------------------------------------------------
static void createDirIfNotExist(const char* dir) { PathTool::CreateDirIfNotExist(dir); }
//-----------------------------------------------------------------------

#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32 
static bool IsFileSensitiveMatch(const char* pszFileName) {
  WIN32_FIND_DATA ffd;
  HANDLE hFind = FindFirstFile(pszFileName, &ffd);
  if (hFind == INVALID_HANDLE_VALUE) {
    return true;
  }
  FindClose(hFind);

  std::string orientPath = pszFileName;
  std::string sensitivePath = ffd.cFileName;

  bool ret = StringUtil::EndsWith(orientPath, sensitivePath, false);
  if (!ret) {
    ERR_M(LOG_MODULE_RESOURCE, "[FileSystemArchive::open] Mis Match FilePath Sensitive! %s", pszFileName);
  }
  return ret;
}
#endif
//-----------------------------------------------------------------------
void FileSystemArchive::FindFiles(std::string_view pattern, bool recursive, bool dirs, StringVector* simpleList, FileInfoList* detailList) {
  intptr_t lHandle, res;
  struct _finddata_t tagData;

  // pattern can contain a directory name, separate it from mask
  size_t pos1 = pattern.find_last_of('/');
  size_t pos2 = pattern.find_last_of('\\');
  if (pos1 == pattern.npos || ((pos2 != pattern.npos) && (pos1 < pos2))) pos1 = pos2;
  std::string directory;
  if (pos1 != pattern.npos) directory = pattern.substr(0, pos1 + 1);

  std::string full_pattern = ConcatenatePath(mName, pattern);

  lHandle = _findfirst(full_pattern.c_str(), &tagData);
  res = 0;
  while (lHandle != -1 && res != -1) {
    if ((dirs == ((tagData.attrib & _A_SUBDIR) != 0)) && (!msIgnoreHidden || (tagData.attrib & _A_HIDDEN) == 0) &&
        (!dirs || !IsReservedDir(tagData.name))) {
      if (simpleList) {
        simpleList->push_back(directory + tagData.name);
      } else if (detailList) {
        FileInfo fi;
        fi.archive = this;
        fi.filename = directory + tagData.name;
        fi.basename = tagData.name;
        fi.path = directory;
        fi.compressedSize = tagData.size;
        fi.uncompressedSize = tagData.size;
        detailList->push_back(fi);
      }
    }
    res = _findnext(lHandle, &tagData);
  }
  // Close if we found any files
  if (lHandle != -1) _findclose(lHandle);

  // Now find directories
  if (recursive) {
    std::string base_dir = mName;
    if (!directory.empty()) {
      base_dir = ConcatenatePath(mName, directory);
      // Remove the last '/'
      base_dir.erase(base_dir.length() - 1);
    }
    base_dir.append("/*");

    // Remove directory name from pattern
    std::string mask("/");
    if (pos1 != pattern.npos)
      mask.append(pattern.substr(pos1 + 1));
    else
      mask.append(pattern);

    lHandle = _findfirst(base_dir.c_str(), &tagData);
    res = 0;
    while (lHandle != -1 && res != -1) {
      if ((tagData.attrib & _A_SUBDIR) && (!msIgnoreHidden || (tagData.attrib & _A_HIDDEN) == 0) && !IsReservedDir(tagData.name)) {
        // recurse
        base_dir = directory;
        base_dir.append(tagData.name).append(mask);
        FindFiles(base_dir, recursive, dirs, simpleList, detailList);
      }
      res = _findnext(lHandle, &tagData);
    }
    // Close if we found any files
    if (lHandle != -1) _findclose(lHandle);
  }
}
//-----------------------------------------------------------------------
FileSystemArchive::~FileSystemArchive() { Unload(); }
//-----------------------------------------------------------------------
void FileSystemArchive::Load() {
  // nothing to do here
}
//-----------------------------------------------------------------------
void FileSystemArchive::Unload() {
  // nothing to see here, move along
}
//-----------------------------------------------------------------------
ByteBufferPtr FileSystemArchive::ReadFileData(std::string_view filename, bool readOnly) {
  std::string fileName = StringUtil::ReplaceAll(std::string(filename), "\\", "/");
  std::string full_path = ConcatenatePath(mName, fileName);

  return PathTool::ReadFileDataWithNullTerminator(full_path.c_str());
}

DataStreamPtr FileSystemArchive::OpenFileStream(std::string_view filename, bool readOnly) {
  std::string fileName = StringUtil::ReplaceAll(std::string(filename), "\\", "/");
  std::string full_path = ConcatenatePath(mName, fileName);

  // Use file system to determine size
  // (quicker than streaming to the end and back)
  struct stat tagStat;
  int ret = stat(full_path.c_str(), &tagStat);
  bool isDir = tagStat.st_mode & S_IFDIR;
  if (ret != 0 || isDir) {
    // Problem getting file size
    ERR_M(LOG_MODULE_RESOURCE, "[FileSystemArchive::OpenStream] Is not a regular file:%s", filename.data());
    return {};
  }

  // assert(ret == 0 && "Problem getting file size" );
  (void)ret;  // Silence warning

  DataStreamPtr datastream;

  // Always open in binary mode
  // Also, always include reading
  std::ios::openmode mode = std::ios::in | std::ios::binary;
  std::istream* baseStream = nullptr;
  std::ifstream* roStream = nullptr;
  std::fstream* rwStream = nullptr;

  if (!readOnly && IsReadOnly()) {
    ERR_M(LOG_MODULE_RESOURCE, "[FileSystemArchive::OpenStream] Cannot open a file in read-write mode in a read-only archive");
    return {};
  }


  if (!readOnly) {
    mode |= std::ios::out;
    rwStream = new std::fstream();
    rwStream->open(full_path.c_str(), mode);
    baseStream = rwStream;
  } else {
    roStream = new std::ifstream();
    roStream->open(full_path.c_str(), mode);
    baseStream = roStream;
  }

  // Should check ensure open succeeded, in case fail for some reason.
  if (baseStream->fail()) {
    ERR_M(LOG_MODULE_RESOURCE, "[FileSystemArchive::OpenStream] Cannot open file:%s", filename.data());
    delete roStream;
    delete rwStream;
    return {};
  }

  /// Construct return stream, tell it to delete on destroy
  if (rwStream) {
    // use the writable stream
    datastream = std::make_shared<FileStreamDataStream>(fileName, rwStream, (size_t)tagStat.st_size, true);
  } else {
    // read-only stream
    datastream = std::make_shared<FileStreamDataStream>(fileName, roStream, (size_t)tagStat.st_size, true);
  }
  return datastream;
}
//---------------------------------------------------------------------
DataStreamPtr FileSystemArchive::CreateWritableFileStream(std::string_view filename) {
  if (IsReadOnly()) {
    printf("[FileSystemArchive::create] Cannot create a file in a read-only archive\n");
    return nullptr;
  }

  std::string fileName = StringUtil::ReplaceAll(std::string(filename), "\\", "/");
  std::string full_path = ConcatenatePath(mName, fileName);
  // create dir if not exist
  size_t pos = full_path.find_last_of('/');
  if (pos != full_path.npos) {
    createDirIfNotExist(full_path.substr(0, pos).c_str());
  }

  // Always open in binary mode
  // Also, always include reading
  std::ios::openmode mode = std::ios::out | std::ios::binary;
  std::fstream* rwStream = new std::fstream();
  rwStream->open(full_path.c_str(), mode);

  // Should check ensure open succeeded, in case fail for some reason.
  if (rwStream->fail()) {
    ERR_M(LOG_MODULE_RESOURCE, "[FileSystemArchive::create] Cannot open file:%s\n", filename.data());
    delete rwStream;
    return nullptr;
  }

  /// Construct return stream, tell it to delete on destroy
  return std::make_shared<FileStreamDataStream>(fileName, rwStream, 0, true);
}

//---------------------------------------------------------------------
void FileSystemArchive::RemoveFile(std::string_view filename) {
  if (IsReadOnly()) {
    printf("[FileSystemArchive::remove] Cannot remove a file in a read-only archive\n");
    return;
  }
  std::string fileName = StringUtil::ReplaceAll(std::string(filename), "\\", "/");
  std::string full_path = ConcatenatePath(mName, fileName);
  ::remove(full_path.c_str());
}
//-----------------------------------------------------------------------
StringVector FileSystemArchive::ListFileNames(bool recursive, bool dirs) {
  // directory change requires locking due to saved returns
  // Note that we have to tell the SharedPtr to use CAP_DELETE_T not CAP_DELETE by passing category
  StringVector ret;

  FindFiles("*", recursive, dirs, &ret, 0);

  return std::move(ret);
}
//-----------------------------------------------------------------------
FileInfoList FileSystemArchive::ListFileInfos(bool recursive, bool dirs) {
  // Note that we have to tell the SharedPtr to use CAP_DELETE_T not CAP_DELETE by passing category
  FileInfoList ret;

  FindFiles("*", recursive, dirs, 0, &ret);

  return std::move(ret);
}
//-----------------------------------------------------------------------
StringVector FileSystemArchive::FindMatchNames(std::string_view pattern, bool recursive, bool dirs) {
  // Note that we have to tell the SharedPtr to use CAP_DELETE_T not CAP_DELETE by passing category
  StringVector ret;

  FindFiles(pattern, recursive, dirs, &ret, 0);

  return std::move(ret);
}
//-----------------------------------------------------------------------
FileInfoList FileSystemArchive::FindMatchInfos(std::string_view pattern, bool recursive, bool dirs) {
  // Note that we have to tell the SharedPtr to use CAP_DELETE_T not CAP_DELETE by passing category
  FileInfoList ret;

  FindFiles(pattern, recursive, dirs, 0, &ret);

  return std::move(ret);
}
//-----------------------------------------------------------------------
bool FileSystemArchive::FileExist(std::string_view filename) {
  std::string fileName = StringUtil::ReplaceAll(std::string(filename), "\\", "/");
  std::string full_path = ConcatenatePath(mName, fileName);

  struct stat tagStat;
  bool ret = (stat(full_path.c_str(), &tagStat) == 0);
  bool isDir = tagStat.st_mode & S_IFDIR;
  ret &= !isDir;

  // stat will return true if the filename is absolute, but we need to check
  // the file is actually in this archive
  if (ret && IsAbsolutePath(filename.data())) {
    // only valid if full path starts with our base
    // case sensitive
    ret = StringUtil::StartsWith(full_path, mName, false);
  }

#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32
  if (ret) ret &= IsFileSensitiveMatch(full_path.c_str());
#endif

  return ret;
}

bool FileSystemArchive::ReplaceByEmbeded(const EmbededResourceInfo& info, bool use_compress, bool use_crypto) {
  if (IsReadOnly()) {
    ERR_M(LOG_MODULE_RESOURCE, "[FileSystemArchive::create] Cannot create a file in a read-only archive.");
    return false;
  }

  if (FileExist(info.file_name)) {
    RemoveFile(info.file_name);
  }

  std::string fileName = StringUtil::ReplaceAll(info.file_name, "\\", "/");
  std::string full_path = ConcatenatePath(mName, fileName);
  // create dir if not exist
  size_t pos = full_path.find_last_of('/');
  if (pos != full_path.npos) {
    createDirIfNotExist(full_path.substr(0, pos).c_str());
  }


  PathTool::SaveDataToFile(full_path, info.databuf->Contents(), info.databuf->NotReadSize());
  return true;
}

//---------------------------------------------------------------------
time_t FileSystemArchive::GetModifiedTime(std::string_view filename) {
  std::string full_path = ConcatenatePath(mName, filename);

  struct stat tagStat;
  bool ret = (stat(full_path.c_str(), &tagStat) == 0);

  if (ret) {
    return tagStat.st_mtime;
  } else {
    return 0;
  }
}

}  // namespace gbf
