#pragma once

#include <list>

#include "imod_shared/imodules/ipackage_system_module.h"
#include "archive/archive_fwd.hpp"
#include "package_system_export.hpp"

namespace gbf {

struct ResourceLocation {
  ArchivePtr archive;
  bool recursive;
};

using ResourceLocationList =  std::list<ResourceLocation*> ;
using ResourceLocationMap = std::map<std::string, ArchivePtr> ;

class GBF_PACKAGE_SYSTEM_API  ResourceGroup {
 public:
  std::string name;
  ResourceLocationList locList;
  ResourceLocationMap mResIndex;
  bool isInternal;

  ~ResourceGroup();
  void AddResourceLocation(ArchivePtr arch, bool recursive);
  void AddToIndex(std::string_view filename, ArchivePtr arch);
  void RemoveFromIndex(std::string_view filename, ArchivePtr arch);
  void RemoveFromIndex(ArchivePtr arch);
  ArchivePtr QueryArchive(std::string_view resName);
};


}
