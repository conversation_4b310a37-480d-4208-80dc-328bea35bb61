#pragma once

#include "imod_shared/imod_shared_export.hpp"


#include "core/core_global.hpp"

#include "imod_shared/imodules/iexception_system_module.h"
#include "imod_shared/imodules/ifile_system_module.h"
#include "imod_shared/imodules/ipackage_system_module.h"
#include "imod_shared/imodules/ijob_system_module.h"

#include "imod_shared/imodules/ilua_system_module.h"
#include "imod_shared/imodules/ipython_system_module.h"


GBF_IMOD_SHARED_API extern gbf::IFileSystemModule* GFileSystem;        // A simple file system.

GBF_IMOD_SHARED_API extern gbf::IPackageSystemModule* GPackageSystem;  // A package file system.


GBF_IMOD_SHARED_API extern gbf::IExceptionSystemModule* GExceptionSystem;

