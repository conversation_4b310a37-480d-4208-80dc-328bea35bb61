#pragma once

#include <cstdint>
#include "async_task/tasks/tasks_define.hpp"

namespace gbf::coro::tasks17 {

class ASYNC_TASKS_API iawaitable17 {
 public:
  // Nested types
  using resume_type = resume_object;

 public:
  iawaitable17() {}
  virtual ~iawaitable17() {}

 public:
  ////virtual const uint64_t query_resume_type_id() const { return 0; }

  virtual bool is_await_ready() { return false; }

  virtual void invoke_suspend(async_task17* task, coro_service_manager* manager) = 0;

  virtual void invoke_resume(async_task17* task, coro_service_manager* manager) {}
};
}  // namespace gbf::coro::tasks
