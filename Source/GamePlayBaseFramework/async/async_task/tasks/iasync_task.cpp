#include "async_task/tasks/iasync_task.hpp"
#include "async_task/jobs/job_system.hpp"
#include "async_task/jobs/job_system_slot.hpp"
#include "async_task/tasks/coro_service_manager.hpp"
#include "core/imodules/ilog_module.h"

namespace gbf::coro {
//--------------------------------------------------------------
iasync_task::iasync_task(uint64_t _task_id, coro_service_manager& _manager) : task_id_(_task_id), manager_(_manager) {}

iasync_task::~iasync_task() { bind_timeout_handle(0); }

CoroRunningState iasync_task::resume(JobType job_type) {
  // handle operate queue here
  do_queue_operates_impl();

  // already end task, just do early return.
  if (GBF_UNLIKELY(last_running_state_ < 0)) {
    if (!is_defer_called) {
      assert(last_running_state_ == CoroRunningState::kTerminate && "Only terminate can run to here!");
      // Try to call defer here, but do not call return callback.
      call_all_defers_impl();
      is_defer_called = true;
    }

    return last_running_state_;
  }

  jobs::job_system_slot::push_async_task(this, job_type);

  try {
    // do ResumeImpl() here
    last_running_state_ = resume_impl(job_type);
  } catch (std::exception& ex) {
    // exception catch here, we need to log error
    ERR_DEF("[coroutine error]coroutine resume with exception: %s", ex.what());
    last_running_state_ = CoroRunningState::kEndFailed;
  }

  jobs::job_system_slot::pop_async_task();

  if (last_running_state_ < 0) {
    // call return implement here
    call_all_defers_impl();
    is_defer_called = true;
    call_return_callback_impl();
  }

  return last_running_state_;
}

void iasync_task::bind_timeout_handle(uint64_t handle) {
  // timeout we can just ignore kill here~~
  if (sleep_handle_ != 0) {
    manager_.parent_job_system().kill_timer_job(sleep_handle_);
  }

  sleep_handle_ = handle;
}

void iasync_task::await_setting(AwaitMode mode, int awaitTimeMs) {
  await_mode_ = mode;
  await_timeout_ = awaitTimeMs;
}

iasync_task* iasync_task::this_thread_task() { return gbf::jobs::job_system_slot::this_thread_async_task(); }

void iasync_task::awake_wait_quit_task_impl() {
  // handle waiting list here
  for (auto wid : wait_quit_notify_array_) {
    wait_task_quit_resume_object we;
    we.task_id = wid;
    we.wait_target_id = task_id_;
    if (last_running_state_ == CoroRunningState::kEndFailed) {
      we.result = AwaitResult::kAwaitFailded;
    }
    manager_.awake_task_by(wid, std::move(we));
  }
}

void iasync_task::do_queue_operates_impl() {
  std::queue<coro_defer_function> tmp_op_queue;
  {
    threads::NormalMutex::LockGuard lock(op_mutex_);
    if (operate_queue_.empty()) return;  // do early return here
    operate_queue_.swap(tmp_op_queue);
  }

  // Use fifo to handle the queue items
  while (!tmp_op_queue.empty()) {
    auto& op = tmp_op_queue.front();
    op();
    tmp_op_queue.pop();
  }
}

gbf::coro::coro_service_manager* iasync_task::this_thread_coro_manager() {
  auto* task = this_thread_task();
  if (GBF_LIKELY(task)) {
    return &(task->manager_);
  }

  return nullptr;
}

}  // namespace gbf::coro
