#pragma once

#if defined(GBF_ENABLE_CPP20)

#include "async_task/tasks/coro_service_manager.hpp"
#include "async_task/tasks/coroutines/cotask20.hpp"
#include "async_task/tasks/resume_object.hpp"
#include "async_task/tasks/tasks_define.hpp"
#include "reflection/objects/userobject.hpp"

namespace gbf::coro::tasks {

//-------------------------------------------------------------------------------------
class ASYNC_TASKS_API next_frame {
 public:
  next_frame() = default;
  ~next_frame() = default;

  ////NextFrame(const NextFrame&);

  bool await_ready() { return false; }
  void await_suspend(coroutine_handle<>) const noexcept;
  void await_resume() const noexcept {}
};
//-------------------------------------------------------------------------------------
class ASYNC_TASKS_API sleep {
 public:
  sleep(int sleepTimeMs) : timeout_ms_(sleepTimeMs) {}
  bool await_ready();
  void await_suspend(coroutine_handle<>) const noexcept;
  void await_resume() const noexcept {}

 private:
  int timeout_ms_;
};
//////-------------------------------------------------------------------------------------
////class ASYNC_TASKS_API create_task {
//// public:
////  create_task() = delete;
////  ////CreateTask(const CreateTask&) = delete;
////  ~create_task() = default;
////
////  template <typename FUNC>
////  create_task(JobSlotType job_type, bool is_child, FUNC&& cfunc) : is_child_(is_child), sub_task_id_(0) {
////    create_task_func_ = [jtype = job_type, task_func = cfunc, this]() {
////      auto task = co_query_self();
////      auto taskInfo = co_query_manager()->create_task20(jtype, std::move(task_func));
////      sub_task_id_ = taskInfo.get_bind_task()->task_id();
////      if (is_child_) {
////        task->queue_for_add_child_task(sub_task_id_);
////      }
////    };
////  }
////
////  //-------------------------------------------------------------------------------------
////  bool await_ready() { return false; }
////
////  void await_suspend(coroutine_handle<>) const noexcept;
////
////  uint64_t await_resume() const noexcept { return sub_task_id_; }
////
//// private:
////  std::function<void()> create_task_func_;
////  ////CoTaskFunction task_func_;
////  bool is_child_;
////  uint64_t sub_task_id_;
////};
//////-------------------------------------------------------------------------------------
////class ASYNC_TASKS_API wait_task_finish {
//// public:
////  wait_task_finish() = delete;
////  ////WaitTaskFinish(const WaitTaskFinish&) = delete;
////  ~wait_task_finish() = default;
////
////  wait_task_finish(uint64_t tid, int timeoutMs) : wait_task_id_(tid), timeout_ms_(timeoutMs) {}
////  bool await_ready() { return false; }
////  void await_suspend(coroutine_handle<>) const noexcept;
////  void await_resume() const noexcept {}
////
//// private:
////  uint64_t wait_task_id_;
////  int timeout_ms_;
////};

//-------------------------------------------------------------------------------------
template <typename Func, typename CoTask20Type = reflection::detail::TFunctionTraits<Func>::ExposedType,
          typename CoReturnType = cotask_type_traits<CoTask20Type>::co_return_type, typename U = std::enable_if_t<cotask_type_traits<CoTask20Type>::value> >
class spawn_task {
 public:
  spawn_task() = delete;
  ~spawn_task() = default;
  spawn_task(JobType job_type, Func&& task_func) { 
    ret_type = co_query_manager()->create_task20(job_type, std::move(task_func));
    //add as child task
    co_query_self()->queue_for_add_child_task(ret_type.get_bind_task()->task_id());
  }

  auto operator co_await() { 
    return tasks::cotask_awaitable<CoReturnType>(ret_type.get_bind_task()); 
  }
private:
  CoTask20Type ret_type;
};

//-----------------------------------------------------------------------------------------------

class ASYNC_TASKS_API transfer {
 public:
  transfer() = delete;
  transfer(JobType target_job_type): target_job_type_(target_job_type) {

  }
  ~transfer() = default;

  bool await_ready() { return false; }
  void await_suspend(coroutine_handle<>) const noexcept;
  void await_resume() const noexcept {}
 private:
  JobType target_job_type_ = JobType::kInvalidJob;
};


}  // namespace gbf::coro::tasks

#endif
