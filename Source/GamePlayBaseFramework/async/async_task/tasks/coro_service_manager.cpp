#include "async_task/tasks/coro_service_manager.hpp"

#include "core/core_global.hpp"
#include "core/error/errors.hpp"
#include "core/imodules/ilog_module.h"
#include "core/imodules/iprofiler_module.h"
#include "reflection/builder/class_builder.hpp"

#include "async_task/tasks/async_task17.hpp"
#include "async_task/tasks/default_awaitable17.hpp"

#if defined(GBF_ENABLE_CPP20)
#include "async_task/tasks/async_task20.hpp"
#endif

#include "async_task/jobs/job_system.hpp"

namespace gbf::coro {

coro_service_manager::coro_service_manager(jobs::job_system& _parent_job_system) : parent_job_system_(_parent_job_system) { do_reflection_register(); }

coro_service_manager::~coro_service_manager() {}

bool coro_service_manager::request_wait_for_task_quit(iasync_task* task, JobType awake_job_type, uint64_t target_task_id, Await<PERSON>ode wait_mode,
                                                      uint64_t timeout_ms) {
  // Wait for self?
  auto src_task_id = task->task_id();
  if (GBF_UNLIKELY(task->task_id() == target_task_id)) return false;

  auto target_task = query_task_info(target_task_id);
  if (GBF_LIKELY(target_task)) {
    target_task->queue_for_add_wait_quit_nofity_task(src_task_id);
    request_task_suspend(task->shared_from_this(), awake_job_type, wait_mode, timeout_ms);
    return true;
  }

  return false;
}

void coro_service_manager::dispatch_async_task_impl(JobType job_type, async_task_ptr&& atask) {
  parent_job_system_.dispatch_async_task(std::move(atask), job_type);
}

async_task_ptr coro_service_manager::query_task_info(uint64_t tid) {
  threads::NormalMutex::LockGuard lock(all_task_map_mutex_);
  auto iter = all_task_map_.find(tid);
  if (GBF_LIKELY(iter != all_task_map_.end())) {
    return iter->second.lock();
  } else {
    return nullptr;
  }
}

coro_service_manager::waited_task_info* coro_service_manager::query_waited_task_info(uint64_t tid) {
  threads::NormalMutex::LockGuard lock(waited_task_map_mutex_);
  auto iter = waited_task_map_.find(tid);
  if (GBF_LIKELY(iter != waited_task_map_.end())) {
    return &(iter->second);
  } else {
    return nullptr;
  }
}

void coro_service_manager::add_task_to_waited(const async_task_ptr& task, JobType awake_job_type, AwaitMode await_mode, uint64_t timeout_ms) {
  threads::NormalMutex::LockGuard lock(waited_task_map_mutex_);
  waited_task_info info;
  info.tid = task->task_id();
  info.awake_job_type = awake_job_type;
  info.wait_task = task;
  info.await_mode = await_mode;
  info.timeout_ms = timeout_ms;

  waited_task_map_.emplace(std::make_pair(info.tid, info));
}

void coro_service_manager::remove_task_from_waited(uint64_t tid) {
  threads::NormalMutex::LockGuard lock(waited_task_map_mutex_);
  waited_task_map_.erase(tid);
}

void coro_service_manager::manual_terminate_task(uint64_t t_id) {
  auto task = query_task_info(t_id);
  if (GBF_LIKELY(task)) {
    task->queue_for_terminate();
  }
}

void coro_service_manager::request_task_transfer(iasync_task* task, JobType src_job_type, JobType target_job_type) {
  //Not need add to await list
  dispatch_async_task_impl(target_job_type, task->shared_from_this());
}

void coro_service_manager::request_task_suspend(const async_task_ptr task, JobType awake_job_type, AwaitMode wait_mode, uint64_t timeout_ms) {
  add_task_to_waited(task, awake_job_type, wait_mode, timeout_ms);
}

void coro_service_manager::add_task_info(const async_task_ptr& task) {
  threads::NormalMutex::LockGuard lock(all_task_map_mutex_);
  all_task_map_.emplace(std::make_pair(task->task_id(), task));
}

void coro_service_manager::remove_task_info(uint64_t tid) {
  threads::NormalMutex::LockGuard lock(all_task_map_mutex_);
  all_task_map_.erase(tid);
}

void coro_service_manager::do_reflection_register() {
  // basic types
  ////__register_cxx_type<CoTaskForScheduler>();
  ////__register_cxx_type<CoTask17<RpcResumeObject>>();

  ////__register_cxx_type<CoroScheduler>();

  // await handle register to ponder
  __register_cxx_type<tasks17::iawaitable17>();

  __register_cxx_type<tasks17::next_frame>().base<tasks17::iawaitable17>();

  __register_cxx_type<tasks17::sleep>().base<tasks17::iawaitable17>();

  __register_cxx_type<tasks17::create_task>().base<tasks17::iawaitable17>();

  ////__register_cxx_type<RpcRequestAHandle>().base<IAwaitHandle>();

  ////__register_type<EntityRpcRequestAHandle>().base<IAwaitHandle>();

  __register_cxx_type<tasks17::wait_task_finish>().base<tasks17::iawaitable17>();

  ////__register_cxx_type<tasks17::kill_me>().base<tasks17::iawaitable17>();

  ////__register_type<LockAHandle>().base<IAwaitHandle>();

  ////__register_type<UnLockAHandle>().base<IAwaitHandle>();

  ////__register_type<IRpcRequestAHandle>().base<IAwaitHandle>();

  // resume object register to ponder
  __register_cxx_type<resume_object>();

  __register_cxx_type<create_task_resume_object>().base<resume_object>();

  ////__register_cxx_type<RpcResumeObject>().base<ResumeObject>();

  __register_cxx_type<timeout_resume_object>().base<resume_object>();

  __register_cxx_type<wait_task_quit_resume_object>().base<resume_object>();

  __register_cxx_type<create_line_resume_object>().base<resume_object>();

  // finish event register to ponder

  ////__register_cxx_type<RpcReturnObject>();
  ////__register_type<RouterEntity>();
}
}  // namespace gbf::coro
