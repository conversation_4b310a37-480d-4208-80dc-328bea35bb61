#include "async_task/jobs/job_fence.hpp"
#include "async_task/jobs/job_ticket.hpp"

#include "async_task/jobs/job_system.hpp"

namespace gbf::jobs {

job_fence::job_fence(job_system* sys) : system_(sys) {}

job_fence::~job_fence() {}

void job_fence::fence_to(JobType slot_id) {
  ////PROFILER_AUTO_SCOPE_DYNAMIC("ThreadFence::fence_to", RStudio::ProfilerGroupType::kThreadData);
  event_.Reset();
  if (post_ticket_) {
    post_ticket_->discard();
    post_ticket_.reset();
  }

  job_ticket_ptr post_ticket = system_->request_ticket();
  post_ticket_ = post_ticket;

  job_fence_ptr fence_ptr = this->shared_from_this();
  system_->post(
      [fence_ptr, post_ticket]() {
        ////PROFILER_AUTO_SCOPE_DYNAMIC("ThreadFence::fence_to::Lambda", RStudio::ProfilerGroupType::kThreadData);

        if (post_ticket->is_expired()) return;

        fence_ptr->event_.Signal();
      },
      slot_id);
}

void job_fence::fence_to_strand(job_strand_ptr& strand) {
  ////PROFILER_AUTO_SCOPE_DYNAMIC("ThreadFence::fence_to", RStudio::ProfilerGroupType::kThreadData);

  event_.Reset();
  if (post_ticket_) {
    post_ticket_->discard();
    post_ticket_.reset();
  }

  job_ticket_ptr post_ticket = system_->request_ticket();
  post_ticket_ = post_ticket;

  job_fence_ptr fence_ptr = this->shared_from_this();
  strand->post([fence_ptr, post_ticket]() {
    ////PROFILER_AUTO_SCOPE_DYNAMIC("ThreadFence::fence_to_strand::Lambda", RStudio::ProfilerGroupType::kThreadData);

    if (post_ticket->is_expired()) return;

    fence_ptr->event_.Signal();
  });
}

void job_fence::wait() {
  ////PROFILER_AUTO_SCOPE(gProfThreadFenceWait);
  event_.Wait();
}

}  // namespace gbf::jobs
