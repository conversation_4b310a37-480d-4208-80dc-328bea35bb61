#pragma once

#include "async_task/async_task_config.hpp"
#include "async_task/asio_lite/associated_executor.hpp"
#include "async_task/asio_lite/memory/associated_allocator.hpp"
#include "async_task/asio_lite/executor_work_guard.hpp"


namespace asio {
namespace detail {

template <typename Handler>
class work_dispatcher
{
public:
  template <typename CompletionHandler>
  explicit work_dispatcher(CompletionHandler&& handler)
    : work_((get_associated_executor)(handler)),
      handler_(static_cast<CompletionHandler&&>(handler))
  {
  }

#if defined(ASIO_HAS_MOVE)
  work_dispatcher(const work_dispatcher& other)
    : work_(other.work_),
      handler_(other.handler_)
  {
  }

  work_dispatcher(work_dispatcher&& other)
    : work_(static_cast<executor_work_guard<
        typename associated_executor<Handler>::type>&&>(other.work_)),
      handler_(static_cast<Handler&&>(other.handler_))
  {
  }
#endif // defined(ASIO_HAS_MOVE)

  void operator()()
  {
    typename associated_allocator<Handler>::type alloc(
        (get_associated_allocator)(handler_));
    work_.get_executor().dispatch(
        static_cast<Handler&&>(handler_), alloc);
    work_.reset();
  }

private:
  executor_work_guard<typename associated_executor<Handler>::type> work_;
  Handler handler_;
};

} // namespace detail
} // namespace asio

