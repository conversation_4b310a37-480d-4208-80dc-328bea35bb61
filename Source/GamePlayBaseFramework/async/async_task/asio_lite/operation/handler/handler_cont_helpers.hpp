#pragma once

#include "async_task/async_task_config.hpp"
#include "async_task/asio_lite/memory/memory.hpp"
#include "async_task/asio_lite/operation/handler/handler_continuation_hook.hpp"

// Calls to asio_handler_is_continuation must be made from a namespace that
// does not contain overloads of this function. This namespace is defined here
// for that purpose.
namespace asio_handler_cont_helpers {

template <typename Context>
inline bool is_continuation(Context& context)
{
#if !defined(ASIO_HAS_HANDLER_HOOKS)
  return false;
#else
  using asio::asio_handler_is_continuation;
  return asio_handler_is_continuation(
      asio::detail::addressof(context));
#endif
}

} // namespace asio_handler_cont_helpers


