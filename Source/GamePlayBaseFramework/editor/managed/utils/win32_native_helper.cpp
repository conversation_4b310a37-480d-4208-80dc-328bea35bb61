#include "utils/win32_native_helper.h"

#include <windows.h>

namespace gbf::win32 {

void FillNativePoint(NativePoint* point, const ::POINT& win32_point) {
  point->x = win32_point.x;
  point->y = win32_point.y;
}
  
void FillNativeMsg(NativeMsg* msg, const ::MSG& win32_msg) {
  msg->hwnd = (uint64_t)win32_msg.hwnd;
  msg->message = win32_msg.message;
  msg->wParam = win32_msg.wParam;
  msg->lParam = win32_msg.lParam;
  msg->time = win32_msg.time;
  FillNativePoint(&msg->pt, win32_msg.pt);
}

void FillWin32Msg(::MSG& win32_msg, const NativeMsg& msg) {
  win32_msg.hwnd = (HWND)msg.hwnd;
  win32_msg.message = msg.message;
  win32_msg.wParam = msg.wParam;
  win32_msg.lParam = msg.lParam;
  win32_msg.time = msg.time;
  win32_msg.pt.x = msg.pt.x;
  win32_msg.pt.y = msg.pt.y;
}

//
//  FUNCTION: MyRegisterClass()
//
//  PURPOSE: Registers the window class.
//
ATOM MyRegisterClass(HINSTANCE hInstance) {

 ////auto win_proc = [](HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam) -> LRESULT { 
 ////  if(msg == 513) {
 ////    int a;
 ////    a = 1;
 ////  }

 ////  return DefWindowProc(hWnd, msg, wParam, lParam); 
 ////};


  WNDCLASSEXW wcex;

  wcex.cbSize = sizeof(WNDCLASSEX);

  wcex.style = CS_HREDRAW | CS_VREDRAW;
  wcex.lpfnWndProc = DefWindowProc;
  wcex.cbClsExtra = 0;
  wcex.cbWndExtra = 0;
  wcex.hInstance = hInstance;
  wcex.hIcon = nullptr;
  wcex.hCursor = nullptr;
  wcex.hbrBackground = CreateSolidBrush(RGB(0, 0, 0));  // HBRUSH(BLACK_BRUSH);
  wcex.lpszMenuName = nullptr;
  wcex.lpszClassName = L"GbfNativeHwndWindowClass";
  wcex.hIconSm = nullptr;

  return RegisterClassExW(&wcex);
}
//--------------------------------------------------------------------------------
bool NativeHelper::RegisterWindowClass(uint64_t hInstance) {
  ATOM ret = MyRegisterClass((HINSTANCE)((void*)(hInstance)));
  return true;
}

void NativeHelper::SetHwndSize(uint64_t hWnd, double width, double height) {
  RECT lpRect;
  HWND windowHandle = (HWND)(void*)(hWnd);
  ::GetWindowRect(windowHandle, &lpRect);
  ::SetWindowPos(windowHandle, 0, lpRect.left, lpRect.top, (int)width, (int)height, SWP_NOZORDER | SWP_FRAMECHANGED);
  ::GetWindowRect(windowHandle, &lpRect);
}

bool NativeHelper::GetMessage(NativeMsg* lpMsg, uint64_t hWnd, uint32_t wMsgFilterMin, uint32_t wMsgFilterMax) { 
  ::MSG msg;
  auto getsuc = ::GetMessage(&msg, (HWND)hWnd, (UINT)wMsgFilterMin, (UINT)wMsgFilterMax);
  if (getsuc) {
    FillNativeMsg(lpMsg, msg);
  }
  return getsuc;
}

bool NativeHelper::TranslateMessage(const NativeMsg* lpMsg) { 
  ::MSG msg;
  FillWin32Msg(msg, *lpMsg);
  return ::TranslateMessage(&msg);
}

int64_t NativeHelper::DispatchMessage(const NativeMsg* lpMsg) {
  ::MSG msg;
  FillWin32Msg(msg, *lpMsg);

  return ::DispatchMessage(&msg);
}

}  // namespace gbf::win32
