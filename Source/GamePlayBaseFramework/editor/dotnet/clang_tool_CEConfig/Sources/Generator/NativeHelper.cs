using Clangen.Model;
using CppAst;
using CppAst.General;
using System.Collections.Generic;

namespace Clangen
{
    public enum ContainerType
    {
        Unknown = 0,
        Vector = 1,
        List = 2,
        Set = 3,
        Map = 4,
        MultiMap = 5,
        UnorderedMap = 6,
        Pair = 7,
        SharedPtr = 8,
    }
    public static class NativeHelper
    {
        public static ContainerType GetStlContainerTypeFromCookName(string cookName)
        {
            ////int findPos = cookName.IndexOf('<');
            ////if (findPos == -1)
            ////{
            ////    return StlContainerType.Unknown;
            ////}

            string onlyName = cookName.Trim();
            switch (onlyName)
            {
                case "std.vector":
                case "std.__1.vector":
                    return ContainerType.Vector;
                case "std.list":
                case "std.__1.list":
                    return ContainerType.List;
                case "std.set":
                case "std.__1.set":
                    return ContainerType.Set;
                case "std.map":
                case "std.__1.map":
                    return ContainerType.Map;
                case "std.__1.multimap":
                case "std.multimap":
                    return ContainerType.MultiMap;
                case "std.unordered_map":
                case "std.__1.unordered_map":
                    return ContainerType.UnorderedMap;
                case "std.pair":
                case "std.__1.pair":
                    return ContainerType.Pair;
                case "std.shared_ptr":
                case "std.__1.shared_ptr":
                    return ContainerType.SharedPtr;
            }

            return ContainerType.Unknown;
        }
        private static CppType RemoveQualified(CppType type)
        {
            if (type.TypeKind == CppTypeKind.Qualified)
            {
                var newtype = (type as CppQualifiedType).ElementType;
                return RemoveQualified(newtype);
            }
            else
            {
                return type;
            }
        }

        private static bool IsConstQualified(CppType type)
        {
            if (type.TypeKind == CppTypeKind.Qualified)
            {
                var qualtype = type as CppQualifiedType;
                return qualtype.Qualifier == CppTypeQualifier.Const;
            }

            return false;
        }

        private static bool IsStdString(CppClass cls)
        {
            if (cls.TemplateKind == CppTemplateKind.TemplateSpecializedClass || cls.TemplateKind == CppTemplateKind.NormalClass)
            {
                if (cls.FullName == "std::__1::basic_string" || cls.FullName == "std::basic_string" || cls.FullName == "std::string" || cls.FullName == "std::basic_string<char, std::char_traits<char>, std::allocator<char>>")
                {
                    return true;
                }
            }

            return false;
        }
        public static TypeContentOffer NativeType2ContentOffer(TypeModel type)
        {
            return DeduceTemplateType(type.TypeDecl, null);
        }
        public static CppAst.General.TypeContentOffer DeduceTemplateType(CppType orgType, Dictionary<string, CppTemplateArgument> templateArgTypeMap)
        {
            bool is_const_qualified = IsConstQualified(orgType);
            CppType not_qualified_type = RemoveQualified(orgType);

            TypeContentOffer retOffer = null;
            switch (not_qualified_type.TypeKind)
            {
                case CppTypeKind.Primitive:
                    {
                        var primtype = not_qualified_type as CppPrimitiveType;
                        retOffer = new BuildInTypeContentOffer(primtype.ToString(), is_const_qualified, primtype.Kind == CppPrimitiveKind.Void);
                    }
                    break;
                case CppTypeKind.Typedef:
                    {
                        var typedef = not_qualified_type as CppTypedef;
                        string fullCppName = typedef.FullName;

                        //Just deduce canonical type here~~
                        var aliasType = DeduceTemplateType(typedef.GetCanonicalType(), templateArgTypeMap);
                        retOffer = new TypedefTypeContentOffer(aliasType, fullCppName, is_const_qualified);
                    }
                    break;
                case CppTypeKind.StructOrClass:
                    {
                        var cls = not_qualified_type as CppClass;
                        string className = cls.FullName;

                        if (IsStdString(cls))
                        {
                            retOffer = new StringTypeContentOffer("std::string", StringTypeKind.StdString, is_const_qualified, 0);
                        }
                        else
                        {
                            if (cls.TemplateKind == CppTemplateKind.TemplateSpecializedClass && cls.Name == "ScopedComponentAccess")
                            {
                                AstContent asttree = CliGenerator.AstTree;
                                string componentname = cls.TemplateSpecializedArguments[0].ArgString;
                                var compclass = asttree.QueryClass(componentname);
                                string systemname;
                                if (compclass.SystemClass != null)
                                {
                                    systemname = compclass.SystemClass.FullName;
                                }
                                else
                                {
                                    systemname = componentname.Replace("Component", "System");
                                }
                                if (cls.TemplateSpecializedArguments[1].ArgAsInteger == 1)
                                {
                                    retOffer = new ComponentHandleContentOffer(Clangen.NamespaceTool.GetOnlyLeafName("cross::ecs::EntityIDStruct"), "cross::ecs::EntityIDStruct", is_const_qualified, true, cls.TemplateSpecializedArguments[0].ArgString, systemname);
                                }
                                else if (cls.TemplateSpecializedArguments[1].ArgAsInteger == 0)
                                {
                                    retOffer = new ComponentHandleContentOffer(Clangen.NamespaceTool.GetOnlyLeafName("cross::ecs::EntityIDStruct"), "cross::ecs::EntityIDStruct", is_const_qualified, false, cls.TemplateSpecializedArguments[0].ArgString, systemname);
                                }

                            }
                            else
                            {
                                retOffer = new RecordTypeContentOffer(Clangen.NamespaceTool.GetOnlyLeafName(className), className, is_const_qualified);
                            }
                        }
                    }
                    break;
                case CppTypeKind.Enum:
                    {
                        var e = not_qualified_type as CppEnum;
                        retOffer = new EnumTypeContentOffer(e.FullName);

                    }
                    break;
                case CppTypeKind.TemplateArgumentType:
                    {
                        //ToDo: add Template argument fix here
                    }
                    break;
                case CppTypeKind.TemplateParameterType:
                    {
                    }
                    break;
                case CppTypeKind.Array:
                    {
                        var arr_type = not_qualified_type as CppArrayType;
                        var elem_type = arr_type.ElementType;
                        is_const_qualified = IsConstQualified(elem_type);
                        elem_type = RemoveQualified(elem_type);
                        if (elem_type.TypeKind == CppTypeKind.Primitive && (elem_type as CppPrimitiveType).Kind == CppPrimitiveKind.Char)
                        {
                            //char [];
                            retOffer = new StringTypeContentOffer("char[]", StringTypeKind.CharArray, is_const_qualified, (int)arr_type.Size);
                        }
                        else
                        {
                            retOffer = DeduceTemplateType(arr_type.ElementType, templateArgTypeMap);
                            retOffer.IsBuildInArray = true;
                        }
                    }
                    break;
                case CppTypeKind.Function:
                    {

                    }
                    break;
                case CppTypeKind.Qualified:
                    {

                    }
                    break;
                case CppTypeKind.Pointer:
                    {
                        var pt_type = not_qualified_type as CppPointerType;
                        var elem_type = pt_type.ElementType;
                        is_const_qualified = IsConstQualified(elem_type);
                        elem_type = RemoveQualified(elem_type);
                        if (orgType.FullName == "char**")
                        {
                            retOffer = new StringTypeContentOffer(pt_type.ToString(), StringTypeKind.OldStringArray, is_const_qualified, 0);
                        }
                        else if (orgType.TypeKind == CppTypeKind.Pointer && elem_type.TypeKind == CppTypeKind.Function) // Function pointer
                        {
                            List<string> args = new List<string>();
                            args.Add("void");
                            retOffer = new DelegateTypeContentOffer("void", args, elem_type.FullName);
                        }

                        //raw string
                        else if (elem_type.TypeKind == CppTypeKind.Primitive && (elem_type as CppPrimitiveType).Kind == CppPrimitiveKind.Char)
                        {
                            retOffer = new StringTypeContentOffer(pt_type.ToString(), StringTypeKind.RawString, is_const_qualified, 0);
                        }
                        else
                        {
                            retOffer = new PointerOrReferenceTypeContentOffer(DeduceTemplateType(pt_type.ElementType, templateArgTypeMap),
                                "*",
                                TypeContentKind.Pointer,
                                is_const_qualified);
                        }
                    }
                    break;
                case CppTypeKind.Reference:
                    {
                        var ref_type = not_qualified_type as CppReferenceType;
                        retOffer = new PointerOrReferenceTypeContentOffer(DeduceTemplateType(ref_type.ElementType, templateArgTypeMap),
                            "&",
                            TypeContentKind.LReference,
                            is_const_qualified);
                    }
                    break;
                case CppTypeKind.Unexposed:
                    {
                        retOffer = new UnknownTypeContentOffer(orgType.ToString());
                    }
                    break;

            }
            return retOffer;
        }
    }
}
