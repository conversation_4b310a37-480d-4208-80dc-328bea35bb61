using CppAst;
using CppAst.General;
using DotLiquid;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Clangen.Model
{
    class FunctionReflectModel : Drop
    {
        public string RegisterFunc { get; }
        public string ReturnPolicy { get; }

        bool NeedWarpFunc(FunctionModel func)
        {
            bool needWrapParam = func.Parameters.Where(p => p.Type.IsCVRef)
                                   .Select(p => p.Type.WithoutConstRef)
                                   .Any(t => !t.IsScopedComponent && (t.IsBuiltinType || t.IsString));
            return needWrapParam;
        }

        string GenWrappedFunc(FunctionModel func, string className)
        {
            bool isSystem = func.Parent != null && func.Parent.IsSystem;

            List<string> paramNames = new List<string>();
            List<string> paramTypes = new List<string>();
            List<string> paramTypeName = new List<string>();
            if (isSystem && !func.IsStatic)
            {
                paramNames.Add("gworld");
                paramTypes.Add("cross::GameWorld*");
            }

            foreach (var param in func.Parameters)
            {
                string typeName = param.Type.FullName;
                if (param.Type.IsCVRef)
                {
                    var dataType = param.Type.WithoutConstRef;
                    if (dataType.IsScopedComponent)
                    {
                        typeName = "UInt64";
                    }
                    else if (dataType.IsBuiltinType || dataType.IsString)
                    {
                        typeName = dataType.FullName;
                    }
                }
                paramTypes.Add(typeName);
                paramNames.Add(param.Name);
            }

            for (int i = 0; i < paramNames.Count; i++)
            {
                paramTypeName.Add(paramTypes[i] + " " + paramNames[i]);
            }

            string callFunc;

            if (isSystem || func.IsStatic)
            {
                callFunc = $"{className}::{func.Name}({string.Join(", ", paramNames)})";
            }
            else
            {
                paramTypeName.Insert(0, $"{className}* thiz");
                callFunc = $"thiz->{func.Name}({string.Join(", ", paramNames)})";
            }

            string paramStr = string.Join(", ", paramTypeName);


            return $"[]({paramStr}){{ return {callFunc}; }}";
        }

        public FunctionReflectModel(FunctionModel func)
        {
            if (func.ReturnType.IsPointer || func.ReturnType.IsReference)
                ReturnPolicy = "policy::ReturnInternalRef()";
            else if (func.ReturnType.IsVoid)
                ReturnPolicy = "policy::ReturnNone()";
            else
                ReturnPolicy = "policy::ReturnCopy()";

            string className = "";
            if (func.Parent != null)
                className = func.Parent.IsSystem ? "cross::" + func.Parent.InterfaceName : func.Parent.FullName;

            if (NeedWarpFunc(func))
            {
                RegisterFunc = GenWrappedFunc(func, className);
            }
            else
            {
                if (func.Parent != null)
                    RegisterFunc = "&" + className + "::" + func.Name;
                else
                    RegisterFunc = "&" + func.FullName;
            }
        }
    }

    class FunctionScriptModel : Drop
    {
        public string Decl => ReturnType + " " + Name + "(" + Parameters + ")";
        public bool IsManualConvertArguments { get; }
        public string Parameters { get; }
        public string ReturnType { get; }
        public string Name { get; }
        public FunctionScriptModel(FunctionModel func)
        {
            IsManualConvertArguments = false;
            Name = func.Name;
            var parameterlist = new List<string>();
            for (int i = 0; i < func.Parameters.Count; i++)
            {
                if (i == 0 && !(func.Parent != null && func.Parent.IsGameplay))
                    continue;
                var param = func.Parameters[i];
                if (param.Type.FullName == "const script::Arguments&")
                {
                    parameterlist.Add(param.Type.FullName + " " + param.Name);
                    IsManualConvertArguments = true;
                }
                else
                {
                    parameterlist.Add("const LocalValue&" + " " + param.Name);
                }
            }
            Parameters = string.Join(", ", parameterlist);
            ReturnType = IsManualConvertArguments ? "LocalValue" : (func.ReturnType.IsVoid ? "void" : "LocalValue");
        }
    }

    class ParameterModel : EntityModel
    {
        new public string Name { get; set; }
        public TypeModel Type { get; }
        public object InitValue { get; }
        public string InitExpr { get; }
        //For CLI usage
        public string BridgeCppInputTypeName
        {
            get
            {
                return CliGenerator.TypeCarryToolCli.ToTypeName(TypeCarryItem.TypeConverterType.BridgeCliInput, NativeHelper.NativeType2ContentOffer(Type));
            }
        }
        public ParameterModel(CppParameter param) :
            base(param)
        {
            CEMeta = new Parser.CEMetaManager(param.MetaAttributes);
            Type = new TypeModel(param.Type);
            Name = param.Name;
            InitValue = param.InitValue != null ? param.InitValue.Value : null;
            InitExpr = param.InitExpression != null ? param.InitExpression.ToString() : string.Empty;
        }
    }

    class FunctionModel : EntityModel
    {
        //public string Name => FuncDecl.Name;
        public string FullName { get; set; }
        public string Namespace { get; }

        //private FieldNode FieldDecl;
        public bool IsCeFunction => CEMeta.IsCeFunction;
        public ClassModel Parent;
        public TypeModel ReturnType { get; }
        public List<ParameterModel> Parameters { get; }
        public CppFunction FuncDecl;

        private FunctionReflectModel _reflect = null;
        public FunctionReflectModel Reflect => _reflect ??= new FunctionReflectModel(this);

        private FunctionScriptModel _script = null;
        public FunctionScriptModel Script => _script ??= new FunctionScriptModel(this);

        public FunctionModel(CppFunction decl, ClassModel parent, string Namespace = null)
            : base(decl)
        {
            Name = decl.Name;
            CEMeta = new Parser.CEMetaManager(decl.MetaAttributes);
            if (parent != null)
            {
                this.Namespace = parent.Namespace;
                FullName = parent.FullName + "::" + Name;
            }
            else
            {
                this.Namespace = Namespace ?? "";
                FullName = string.IsNullOrEmpty(Namespace) ? Name : Namespace + "::" + Name;
            }

            FuncDecl = decl;
            Parent = parent;

            ReturnType = new TypeModel(decl.ReturnType);

            Parameters = new List<ParameterModel>();

            int idx = 1;
            foreach (var item in FuncDecl.Parameters)
            {
                var Parameter = new ParameterModel(item);
                if (Parameter.Name == "")
                    Parameter.Name = $"_param_{idx++}";
                Parameters.Add(Parameter);
            }
        }

        public bool Equals(FunctionModel other)
        {
            bool parentEquals = Parent == other.Parent || (Parent != null && other.Parent != null && Parent.Equals(other.Parent));
            return other != null && Declaration == other.Declaration && parentEquals;
        }

        public override bool Equals(object obj) => Equals(obj as FunctionModel);
        public override int GetHashCode()
        {
            return Declaration.GetHashCode();
        }
        public bool IsStatic => FuncDecl.StorageQualifier == CppStorageQualifier.Static;

        public bool IsConst => FuncDecl.Flags.HasFlag(CppFunctionFlags.Const);
        public bool IsDefaulted => FuncDecl.Flags.HasFlag(CppFunctionFlags.Defaulted);
        public bool IsPure => FuncDecl.Flags.HasFlag(CppFunctionFlags.Pure);
        public bool IsVirtual => FuncDecl.Flags.HasFlag(CppFunctionFlags.Virtual);
        public bool IsMethod => FuncDecl.Flags.HasFlag(CppFunctionFlags.Method);
        public bool IsDeleted => FuncDecl.Flags.HasFlag(CppFunctionFlags.Deleted);

        public bool IsConstructor => FuncDecl.IsConstructor;

        public bool IsCallable => CEMeta.HasKey("ScriptCallable");
        public bool IsScriptImpl => CEMeta.HasKey("ScriptImplable");
        public bool IsMethodAsProperty => CEMeta.HasKey("ScriptAsProperty");
        public bool IsWorkflowExecutable => IsCeFunction && CEMeta.HasKey("WorkflowExecutable");
        public bool IsWorkflowPure => IsCeFunction && CEMeta.HasKey("WorkflowPure");
        public bool IsWorkflowEvent => IsCeFunction && CEMeta.HasKey("WorkflowEvent");
        public bool IsWorkflowExport => IsWorkflowExecutable || IsWorkflowPure || IsWorkflowEvent;
        public override bool NeedBindingScript
        {
            get
            {
                bool bNeedBindingScript = CEMeta.HasKey("Script") || IsCallable;
                return bNeedBindingScript;
                //if (Parent != null && Parent.IsGameplay)
                //{
                //    return bNeedBindingScript;
                //}
                //else
                //{
                //    return IsPublic && bNeedBindingScript;
                //}
            }
        }
        public override bool NeedBindingTs => base.NeedBindingTs || NeedBindingScript;
        private string _strParameters;
        private string _strParametersTS;
        public string StrParameters => _strParameters ??= string.Join(", ", Parameters.Select(p => $"{p.Type.FullName} {p.Name}"));

        public string StrParametersTypeScript => _strParametersTS ??= string.Join(", ", Parameters.Select(p => $"{p.Name} : {p.Type.ToTypeScriptName()} "));
        public string StrParametersNoType => string.Join(", ", Parameters.Select(p => $"{p.Name}"));

        public string StrParametersNoName => string.Join(", ", Parameters.Select(p => $"{p.Type.FullName}"));

        private string _strSwigParameters;
        public string StrSwigParameters
        {
            get
            {
                if (_strSwigParameters == null)
                {
                    var parameterList = new List<string>();
                    foreach (var p in Parameters)
                    {
                        var t = (p.Type.IsDelegate ? p.Type.AliasName : p.Type.FullName) + " " + p.Name;
                        parameterList.Add(t);
                    }

                    _strSwigParameters = string.Join(", ", parameterList);
                }
                return _strSwigParameters;
            }
        }

        private string _strInterfaceParameters;
        public string InterfaceParameters
        {
            get
            {
                if (_strInterfaceParameters == null)
                {
                    var list = new List<string>();
                    if (!IsStatic)
                    {
                        list.Add("cross::GameWorld* gworld");
                    }
                    foreach (var p in Parameters)
                    {
                        if (p.Type.WithoutConstRef.IsScopedComponent)
                        {
                            list.Add($"UInt64 {p.Name}");
                        }
                        else
                        {
                            list.Add($"{p.Type.FullName} {p.Name}");
                        }
                    }
                    _strInterfaceParameters = string.Join(", ", list);
                    //_strInterfaceParameters = string.Join(", ", Parameters.Select(p => $"{p.Type} {p.Name}").ToList());
                }
                return _strInterfaceParameters;
            }
        }

        public string declarationFunc(string strParameters)
        {
            var builder = new StringBuilder();
            if (IsStatic)
            {
                builder.Append("static ");
            }

            if (IsVirtual)
            {
                builder.Append("virtual ");
            }

            builder.Append(ReturnType.FullName + " ");
            builder.Append(Name);
            builder.Append("(");
            builder.Append(strParameters);
            builder.Append(")");

            if (IsConst)
            {
                builder.Append(" const");
            }

            if (IsPure)
            {
                builder.Append(" = 0");
            }
            builder.Append(";");
            return builder.ToString();
        }

        public string SwigDeclaration => declarationFunc(StrSwigParameters);

        virtual public string Declaration => declarationFunc(StrParameters);

        public override string ToString()
        {
            return Declaration;
        }

        public string TypeList => string.Join(", ", Parameters.Select(p => $"{p.Type.FullName}"));
        public string TypeScriptFunctionSignature
        {
            get
            {
                var builder = new StringBuilder();
                builder.Append(ReturnType.FullName + " ");
                if (IsStatic)
                {
                    builder.Append("(*)");
                }
                else
                {
                    builder.Append($"({Parent.FullName}::*)");
                }
                builder.Append("(");
                builder.Append(StrParameters.Replace("(*)", ""));
                builder.Append(")");

                if (IsConst)
                {
                    builder.Append(" const");
                }

                return builder.ToString();
            }
        }
    }
}