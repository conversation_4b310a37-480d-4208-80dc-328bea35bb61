using CppAst;
using CppAst.General;
using DotLiquid;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace Clangen.Model
{
    enum ComponentAccessType
    {
        Read,
        Write
    };

    public class TypeModel : Drop
    {
        public CppType TypeDecl;
        public CppType CanonicalTypeDecl;
        public bool IsVoid { get; } = false;
        public bool IsBuiltinType { get; } = false;
        public bool IsString { get; } = false;
        public bool IsStdFunction { get { return Name == "function"; } }
        public bool IsClass { get; } = false;
        public bool IsEnum { get; } = false;
        public bool IsSpecializedTemplate { get; } = false;
        public bool IsFunction { get; } = false;

        public bool IsConst { get; } = false;
        public bool IsVolatile { get; } = false;
        public bool IsReference { get; } = false;
        public bool IsPointer { get; } = false;
        public bool IsArray { get; } = false;
        public bool IsDelegate { get; } = false;

        public bool IsCVRefPtr { get { return IsConst | IsVolatile | IsReference | IsPointer; } }
        public bool IsCVRef { get { return IsConst | IsVolatile | IsReference; } }

        public bool IsValueUDT { get { return !IsPointer && !IsReference && (TypeDecl.TypeKind == CppTypeKind.StructOrClass); } }
        public bool IsScopedComponent { get; } = false;
        public string ScopedComponentType { get; } = "";
        public string ScopedComponentAccessMethod { get; } = "";

        public string Name { get; }
        public string _fullName = null;
        public string _aliasName = null;
        public string AliasName => _aliasName ??= FullName;

        public string TemplateCookName => FullName.Replace("std::", "").Replace("::", "_").Replace(", ", "_")
                    .Replace("<", "_").Replace(">", "").Replace(" ", "_");

        public string StdFunctionTypeScriptName { get; }

        public TypeModel DataType;

        public static string RemoveCV(string typeDescription)
        {
            // Regular expression to match const, volatile, &, && and leading/trailing spaces
            string pattern = @"\b(const|volatile)\b|\s*&\s*|\s*&\s*|\s*&&\s*";

            // Replace matched patterns with an empty string
            string result = Regex.Replace(typeDescription, pattern, string.Empty);

            // Trim any leading or trailing whitespace
            result = result.Trim();

            return result;
        }

        public TypeModel(CppType Decl)
        {
            if (Decl.TypeKind == CppTypeKind.Typedef)
            {
                _aliasName = ((CppTypedef)Decl).Name;
            }

            TypeDecl = Decl.GetCanonicalType();
            Name = TypeDecl.GetDisplayName();
            CanonicalTypeDecl = Decl.GetCanonicalType();
            DataType = (TypeDecl is CppTypeWithElementType t) ? new TypeModel(t.ElementType) : this;

            if (TypeDecl.TypeKind == CppTypeKind.Pointer)
            {
                if ((TypeDecl as CppPointerType).ElementType.TypeKind == CppTypeKind.Function)
                {
                    IsDelegate = true;
                    _fullName = _aliasName ?? TypeDecl.GetDisplayName();
                }
            }

            _fullName = TypeUtils.TypeName(Decl);

            IsString = RemoveCV(_fullName) == "std::string";
            IsBuiltinType = TypeDecl.TypeKind == CppTypeKind.Primitive || IsString;
            IsPointer = TypeDecl.TypeKind == CppTypeKind.Pointer;
            IsReference = TypeDecl.TypeKind == CppTypeKind.Reference;
            IsArray = TypeDecl.TypeKind == CppTypeKind.Array;
            IsConst = TypeDecl.TypeKind == CppTypeKind.Qualified && ((CppQualifiedType)(TypeDecl)).Qualifier == CppTypeQualifier.Const;
            IsVolatile = TypeDecl.TypeKind == CppTypeKind.Qualified && ((CppQualifiedType)(TypeDecl)).Qualifier == CppTypeQualifier.Volatile;
            IsClass = TypeDecl.TypeKind == CppTypeKind.StructOrClass;
            IsEnum = TypeDecl.TypeKind == CppTypeKind.Enum;
            IsFunction = TypeDecl.TypeKind == CppTypeKind.Function;
            IsVoid = _fullName == "void";

            IsSpecializedTemplate = IsClass && _fullName.IndexOf('<') > 0;

            IsScopedComponent = IsSpecializedTemplate && _fullName.IndexOf("::ScopedComponentAccess<") > 0;

            if (IsScopedComponent)
            {
                var specializedTemplate = (CppClass)TypeDecl;
                ScopedComponentType = ((CppClass)(specializedTemplate.TemplateSpecializedArguments[0].ArgAsType)).FullName;
                var ComponentAccessTypeKind = (ComponentAccessType)(specializedTemplate.TemplateSpecializedArguments[1].ArgAsInteger);
                if (ComponentAccessTypeKind == ComponentAccessType.Read)
                {
                    ScopedComponentAccessMethod = "Read";
                }
                if (ComponentAccessTypeKind == ComponentAccessType.Write)
                {
                    ScopedComponentAccessMethod = "Write";
                }
            }


            if (IsStdFunction)
            {
                var specializedTemplate = (CppClass)TypeDecl;
                var functionType = (CppFunctionType)specializedTemplate.TemplateSpecializedArguments[0].ArgAsType;
                var returnStr = new TypeModel(functionType.ReturnType).ToTypeScriptReturnName();
                var StrParametersTypeScript = string.Join(", ", functionType.Parameters.Select((p, index) => string.Format("{0} : {1}", p.Name == "" ? "_param_" + index : p.Name, new TypeModel(p.Type).ToTypeScriptName())));
                StdFunctionTypeScriptName = $"({StrParametersTypeScript}) => {returnStr}";
            }
        }

        private string ToTypeScriptNameImpl(bool bReturnType)
        {
            var prim = TypeDecl as CppPrimitiveType;
            if (_fullName == "const char*")
            {
                return "cstring";
            }
            else if (IsReference)
            {
                if (bReturnType)
                {
                    return string.Format("{0}", WithoutConstRefPtr.ToTypeScriptName());
                }

                if (WithoutRefPtr.IsConst) // const T&
                {
                    return string.Format("{0}", WithoutConstRefPtr.ToTypeScriptName());
                }
                else // T&
                {
                    return string.Format("$Ref<{0}>", WithoutConstRefPtr.ToTypeScriptName());
                }
            }
            else if (IsPointer)
            {
                // int, float, std::string can't be T* or const T*
                if (WithoutConstRefPtr.IsBuiltinType || WithoutConstRefPtr.IsString)
                {
                    return $"[script type can't be (const T*)/(T*), current Type is '{_fullName}']";
                }

                return string.Format("{0}", WithoutConstRefPtr.ToTypeScriptName());
            }
            else if (IsStdFunction)
            {
                return StdFunctionTypeScriptName;
            }
            else if (IsString)
            {
                return "string";
            }

            else if (IsBuiltinType && (prim.Kind == CppPrimitiveKind.Short
                || prim.Kind == CppPrimitiveKind.Int
                || prim.Kind == CppPrimitiveKind.UnsignedShort
                || prim.Kind == CppPrimitiveKind.UnsignedInt
                || prim.Kind == CppPrimitiveKind.LongDouble
                || prim.Kind == CppPrimitiveKind.Float
                || prim.Kind == CppPrimitiveKind.Double))
            {
                return "number";
            }
            else if (IsBuiltinType && (
                prim.Kind == CppPrimitiveKind.LongLong ||
                prim.Kind == CppPrimitiveKind.UnsignedLongLong))
            {
                return "bigint";
            }
            else if (IsBuiltinType && (prim.Kind == CppPrimitiveKind.Bool))
            {
                return "boolean";
            }
            else if (IsConst)
            {
                return WithoutConst.ToTypeScriptName();
            }
            else if (IsClass)
            {
                return Name;
            }
            else if (IsEnum)
            {
                return FullName.Replace("::", ".");
            }
            return "void";
        }

        public string ToTypeScriptReturnName()
        {
            return ToTypeScriptNameImpl(true);
        }

        public string ToTypeScriptName()
        {
            return ToTypeScriptNameImpl(false);
        }

        TypeModel _typeWithoutConst = null;
        public TypeModel WithoutConst
        {
            get
            {
                if (_typeWithoutConst == null)
                {
                    if (IsConst)
                    {
                        var t = TypeDecl;
                        while (t.TypeKind == CppTypeKind.Qualified)
                        {
                            t = ((CppTypeWithElementType)t).ElementType;
                        }
                        _typeWithoutConst = new TypeModel(t);
                    }
                    else
                    {
                        _typeWithoutConst = this;
                    }
                }
                return _typeWithoutConst;
            }
        }
        TypeModel _typeWithoutRefPtr = null;
        public TypeModel WithoutRefPtr
        {
            get
            {
                if (_typeWithoutRefPtr == null)
                {
                    if (IsReference || IsPointer)
                    {
                        var t = TypeDecl;
                        while (t.TypeKind == CppTypeKind.Reference || t.TypeKind == CppTypeKind.Pointer)
                        {
                            t = ((CppTypeWithElementType)t).ElementType;
                        }
                        _typeWithoutRefPtr = new TypeModel(t);
                    }
                    else
                    {
                        _typeWithoutRefPtr = this;
                    }
                }
                return _typeWithoutRefPtr;
            }
        }

        TypeModel _typeWithoutCVRef = null;
        public TypeModel WithoutConstRef
        {
            get
            {
                if (_typeWithoutCVRef == null)
                {
                    if (IsCVRef)
                    {
                        var t = TypeDecl;
                        while (t.TypeKind == CppTypeKind.Reference || t.TypeKind == CppTypeKind.Qualified)
                        {
                            t = ((CppTypeWithElementType)t).ElementType;
                        }
                        _typeWithoutCVRef = new TypeModel(t);
                    }
                    else
                    {
                        _typeWithoutCVRef = this;
                    }
                }
                return _typeWithoutCVRef;
            }
        }
        public string BridgeCppInputTypeName
        {
            get
            {
                return CliGenerator.TypeCarryToolCli.ToTypeName(TypeCarryItem.TypeConverterType.BridgeCliInput, NativeHelper.NativeType2ContentOffer(this));
            }
        }

        public string ManagedCliTypeName => BridgeCppInputTypeName;
        TypeModel _typeWithoutConstRefPtr = null;
        public TypeModel WithoutConstRefPtr
        {
            get
            {
                if (_typeWithoutConstRefPtr == null)
                {
                    if (IsCVRefPtr)
                    {
                        var t = TypeDecl;
                        while (t.TypeKind == CppTypeKind.Pointer || t.TypeKind == CppTypeKind.Reference || t.TypeKind == CppTypeKind.Qualified)
                        {
                            t = ((CppTypeWithElementType)t).ElementType;
                        }
                        _typeWithoutConstRefPtr = new TypeModel(t);
                    }
                    else
                    {
                        _typeWithoutConstRefPtr = this;
                    }
                }
                return _typeWithoutConstRefPtr;
            }
        }
        public bool NeedIgnore
        {
            get
            {
                return ManagedCliTypeName.Contains("UnknowKeeper");
            }
        }
        public string NativeCppTypeName
        {
            get
            {
                return FullName;
            }
        }
        public string FullName => _fullName;

        public override string ToString()
        {
            return $"Type {FullName}";
        }

        protected bool Equals(TypeModel other)
        {
            return FullName == other.FullName;
            //return TypeDecl == other.TypeDecl;
        }

        public override bool Equals(object obj)
        {
            return ReferenceEquals(this, obj) || obj is TypeModel other && Equals(other);
        }

        public override int GetHashCode()
        {
            return FullName.GetHashCode();
            //return TypeDecl.GetHashCode();
        }
    }


    static public class TypeUtils
    {
        static Regex StdContainerRegex = new Regex(@"std::(?<type>\w+)<.+>", RegexOptions.Compiled);
        static Dictionary<string, string> Dict = new Dictionary<string, string>();
        static public void ShowAllType()
        {
            foreach (var item in Dict)
            {
                Console.WriteLine("{0} => {1}", item.Key, item.Value);
            }
        }

        static public string TypeName(CppType cppType)
        {
            // if (Dict.TryGetValue(cppType.FullName, out var typeName))
            // {
            //     return typeName;
            // }

            var builder = new StringBuilder();

            switch (cppType.TypeKind)
            {
                case CppTypeKind.Primitive:
                    builder.Append(((CppPrimitiveType)cppType).ToString());
                    break;
                case CppTypeKind.StructOrClass:
                    var classType = cppType as CppClass;
                    if (classType.SpecializedTemplate != null)
                    {
                        builder.Append(TemplateTypeName(classType));
                    }
                    else
                    {
                        builder.Append(classType.FullName);
                    }
                    break;
                case CppTypeKind.Enum:
                    builder.Append((cppType as CppEnum).FullName);
                    break;

                case CppTypeKind.Unexposed:
                    builder.Append((cppType as CppUnexposedType).Name);
                    break;
                case CppTypeKind.Function:
                    builder.Append((cppType as CppFunctionType).ToString());
                    break;
                case CppTypeKind.Qualified:
                    {
                        var t = cppType as CppQualifiedType;
                        if (t.Qualifier == CppTypeQualifier.Const) builder.Append("const ");
                        if (t.Qualifier == CppTypeQualifier.Volatile) builder.Append("volatile ");
                    }
                    builder.Append(TypeName((cppType as CppTypeWithElementType).ElementType));
                    break;
                case CppTypeKind.Reference:
                    builder.Append(TypeName((cppType as CppTypeWithElementType).ElementType));
                    builder.Append("&");
                    break;
                case CppTypeKind.Pointer:
                    builder.Append(TypeName((cppType as CppTypeWithElementType).ElementType));
                    builder.Append("*");
                    break;
                case CppTypeKind.Array:
                    builder.Append(TypeName((cppType as CppTypeWithElementType).ElementType));
                    builder.Append($"[{(cppType as CppArrayType).Size}]");
                    break;
                case CppTypeKind.Typedef:
                    builder.Append(TypeName((cppType as CppTypedef).ElementType));
                    break;
                default:
                    builder.Append("- Unknown Type -");
                    break;
            }
            // Dict[cppType.FullName] = builder.ToString();

            return builder.ToString();
        }

        static public string TemplateArgToString(CppTemplateArgument arg)
        {
            switch (arg.ArgKind)
            {
                case CppTemplateArgumentKind.AsType:
                    return TypeName(arg.ArgAsType);
                case CppTemplateArgumentKind.AsInteger:
                    return arg.ArgAsInteger.ToString();
                default:
                    return arg.ArgAsUnknown;
            }
        }

        static public string TemplateTypeName(CppClass cppType)
        {
            var fullName = cppType.FullName;
            MatchCollection matches = StdContainerRegex.Matches(fullName);
            if (matches.Count > 0)
            {
                string typeName = matches[0].Groups["type"].Value;
                //Console.WriteLine("typeName: {0}", typeName);
                var builder = new StringBuilder();
                if (typeName == "basic_string")
                {
                    return "std::string";
                }
                if (typeName == "basic_string_view")
                {
                    return "std::string_view";
                }

                var templateArguments = new List<CppTemplateArgument>();
                switch (typeName)
                {
                    case "unique_ptr":
                    case "set":
                    case "unordered_set":
                    case "vector":
                        templateArguments.Add(cppType.TemplateSpecializedArguments[0]);
                        break;
                    case "map":
                    case "unordered_map":
                        templateArguments.Add(cppType.TemplateSpecializedArguments[0]);
                        templateArguments.Add(cppType.TemplateSpecializedArguments[1]);
                        break;
                    default:
                        templateArguments = cppType.TemplateSpecializedArguments;
                        break;
                }

                builder.Append("std::");
                builder.Append(typeName);
                builder.Append("<");
                builder.Append(string.Join(", ", templateArguments.Select(a => TemplateArgToString(a))));
                builder.Append(">");
                return builder.ToString();
            }
            else
            {
                return fullName;
            }
        }

        static public List<CppType> TemplateParams(CppClass cppType)
        {
            var fullName = cppType.FullName;
            var templateArguments = new List<CppType>();
            MatchCollection matches = StdContainerRegex.Matches(fullName);
            if (matches.Count > 0)
            {
                string typeName = matches[0].Groups["type"].Value;

                switch (typeName)
                {
                    case "unique_ptr":
                    case "set":
                    case "unordered_set":
                    case "vector":
                    case "list":
                    case "deque":
                        templateArguments.Add(cppType.TemplateSpecializedArguments[0].ArgAsType);
                        break;
                    case "pair":
                    case "map":
                    case "unordered_map":
                    case "multimap":
                    case "multiset":
                        templateArguments.Add(cppType.TemplateSpecializedArguments[0].ArgAsType);
                        templateArguments.Add(cppType.TemplateSpecializedArguments[1].ArgAsType);
                        break;
                    default:
                        break;
                }
                return templateArguments;
            }
            else
            {
                return templateArguments;
            }
        }
    }
}