using ClangSharp.Interop;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Reflection;

namespace Clangen
{
    class Program
    {
        static int Main(string[] args)
        {
        
#if DEBUG
            Log.Warning("Do not use a debug version when publish!");
#else
            try
            {
#endif

                string subCommand = args.Length > 0 ? args[0] : "";
                List<IClangToolCommand> SubCommands = new List<IClangToolCommand>()
            {
                new CECodeGenCommand(),
                new GBFCodeGenCommand(),
                new InstallCommand(),
                new InsertLogCommand(),
            };

                var command = SubCommands.FirstOrDefault(c => c.CommandName == subCommand, null);
                if (command != null)
                {
                    if (command.Init(args))
                    {
                        var stopwatch = new Stopwatch();
                        stopwatch.Start();
                        Log.Info($"{command.CommandName}");
                        command.Run();
                        Log.Info($"costs {stopwatch.ElapsedMilliseconds / 1e3:0.00}s");
                    }
                    else
                        return 1;
                }
                else if (subCommand == "--version")
                {
                    var assembly = Assembly.GetExecutingAssembly();
                    string ProductVersion = FileVersionInfo.GetVersionInfo(assembly.Location).ProductVersion;
                    Console.WriteLine($"clang-tool version {ProductVersion}");
                    Console.WriteLine($"{clang.getClangVersion().CString}, {clangsharp.getVersion().CString}");
                }
                else
                {
                    Console.WriteLine($"Usage:");
                    foreach (var cmd in SubCommands)
                    {
                        Console.WriteLine($"  clang-tool {cmd.CommandName} [-h] [options]");
                    }
                    return 1;
                }
#if !DEBUG
            }
            catch (Exception ex)
            {
                Log.Error(ex.Message);
                // Console.WriteLine(ex.ToString());
                return 1;
            }
#endif
            return 0;
        }
    }
}
