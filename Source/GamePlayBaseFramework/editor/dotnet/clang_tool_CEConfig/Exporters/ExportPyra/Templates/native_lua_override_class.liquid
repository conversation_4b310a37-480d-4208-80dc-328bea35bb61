// {{ this_class.unscoped_name }} export start
static int ponder_expose_override_class_to_lura_{{ this_class.identify_name }}(lua_State* L)
{
/*
    //call base class helper
    class {{ this_class.base_call_helper_use_name}}
    {
    public:
    {%- for method in this_class.virtual_method_list -%}
{{ method.render_lua_base_helper_definition }}
    {%- endfor -%}
	};

    //lua override delegate class 
    class {{ this_class.delegate_use_name }} : public {{ this_class.name }}
	{
    public:
        {{ this_class.delegate_use_name }}(const char* subClassName, lua_State* state):
            {{ this_class.unscoped_name }}()
            ,mInnerTable(state)
            ,mBaseTable(state)
            ,L(state)
            ,mSubClassName(subClassName)
            ,mSelfRef(state)
        {
			luabridge::LuaStackRecover tmpRecover(L);
            mInnerTable = luabridge::newTable(L);   //For save local data in lua.
            lua_rawgetp(L, LUA_REGISTRYINDEX, luabridge::ClassInfo <{{ this_class.base_call_helper_use_name}}>::getStaticKey()); // Stack: st
            mBaseTable = luabridge::LuaRef::fromStack(L);

			luabridge::PushLuaOverrideFunction<{{ this_class.delegate_use_name }}>(L, mSubClassName, "__init");
			if (lua_isfunction(L, -1))
			{
				luabridge::Stack<{{ this_class.delegate_use_name }}*>::push(L, this);
                mSelfRef = luabridge::LuaRef::fromStack(L, -1);
				lua_pcall(L, 1, 0, 0);
			}
        }

        virtual ~{{ this_class.delegate_use_name }}()
        {
			luabridge::LuaStackRecover tmpRecover(L);

			luabridge::PushLuaOverrideFunction<{{ this_class.delegate_use_name }}>(L, mSubClassName, "__destroy");
			if (lua_isfunction(L, -1))
			{
				mSelfRef.push();
				lua_pcall(L, 1, 0, 0);
			}
        }

    {%- for method in this_class.virtual_method_list -%}
{{ method.render_lua_override }}
    {%- endfor -%}
    public:
		luabridge::LuaRef mInnerTable;
        luabridge::LuaRef mBaseTable;
    protected:
        lua_State* L;
        const char* mSubClassName;
        luabridge::LuaRef mSelfRef;
	};


    luabridge::getGlobalNamespace(L)
        {% for ns in this_class.export_namespace_list %}.beginNamespace("{{ns}}"){% endfor %}
{%- comment -%} class export start {%- endcomment -%}
            .beginClass<{{ this_class.name }}>("{{ this_class.unscoped_name }}")
                //add CreateOverride() method here.
                .addStaticFunction("CreateOverride", &luabridge::CreateOverride<{{ this_class.delegate_use_name }}>)
            .endClass() 
            //native class export end
            //delegate class export start
            .deriveClass<{{ this_class.delegate_use_name }}, {{ this_class.name }}>("{{ this_class.delegate_use_name }}")
                .addConstructor<void (*) (const char*, lua_State*)>()
                .enableOverrideInLua()
                .addProperty("__st", &{{ this_class.delegate_use_name }}::mInnerTable, false)
                .addProperty("base", &{{ this_class.delegate_use_name }}::mBaseTable, false)
                //methods export here
            {%- for method in this_class.virtual_method_list -%}
                {%- unless method.with_complex_params -%}
                .addFunction("{{ method.target_name }}", &{{ this_class.delegate_use_name }}::{{ method.name }}) 
                {%- endunless -%}
            {%- endfor -%}
            .endClass()
            .beginClass<{{ this_class.base_call_helper_use_name }}>("{{ this_class.base_call_helper_use_name }}")
            {%- for method in this_class.virtual_method_list -%}
                {%- unless method.with_complex_params -%}
                .addStaticFunction("{{ method.target_name }}", &{{ this_class.base_call_helper_use_name }}::{{ method.name }}) 
                {%- endunless -%}
            {%- endfor -%}
            .endClass()
{%- comment -%} class export end {%- endcomment -%}
        {% for ns in this_class.export_namespace_list %}.endNamespace(){% endfor %};
*/
    return 0;
}
