using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
////using ;

namespace CppAst.General
{
    public enum ContentDropType : int
    {
        ClassInstance = 0,
        ClassTemplate,
        ClassTemplateSpecialization,
        FunctionGroup,
        OverrideClass,
    }
    public class ContentContainerDrop : DotLiquid.Drop
    {
        protected string mLeafName;
        protected string mFullName;
        protected string mCookNamespace;
        protected Exporter.BaseClassInfo mBaseClassInfo;

        protected Exporter mExporter;

        #region Properties
        public List<MethodContainerDrop> Methods { get; } = new List<MethodContainerDrop>();
        ////public ConstructorDrop Constructor { get; set; }
        public MethodContainerDrop Constructor { get; set; }
        public List<FieldDrop> Fields { get; } = new List<FieldDrop>();
        public List<EnumDrop> Enums { get; } = new List<EnumDrop>();
        public List<PropertyDrop> Properties { get; } = new List<PropertyDrop>();

        public List<StlContainerDrop> StlContainers { get; } = new List<StlContainerDrop>();

        public List<ClassDrop> EmbededClasses { get; } = new List<ClassDrop>();

        public bool HasConstructor { get; set; } = false;

        public List<MethodContainerDrop> OnlyNotVirtualMethodList
        {
            get
            {
                var notVirtualList = new List<MethodContainerDrop>();
                foreach (var method in Methods)
                {
                    if (!method.IsVirtual)
                    {
                        notVirtualList.Add(method);
                    }
                }
                return notVirtualList;
            }
        }

        public List<MethodContainerDrop> OnlyVirtualMethodList
        {
            get
            {
                var virtualList = new List<MethodContainerDrop>();
                foreach (var method in Methods)
                {
                    if (method.IsVirtual)
                    {
                        virtualList.Add(method);
                    }
                }
                return virtualList;
            }
        }

        ////public bool IsClassOverride { get; set; }

        public string OverrideDelegateName
        {
            get
            {
                return string.Format("{0}_Native_Proxy", IdentifyName);
            }
        }


        public ContentDropType DropType
        {
            get;
            private set;
        }

        public virtual bool HasDefaultConstructor
        {
            get
            {
                return false;
            }
        }

        public string DefaultNamespace
        {
            get
            {
                return mCookNamespace;
            }
        }

        ////public bool HasAliasNamespace { get; set; } = false;

        public bool HasBaseClass
        {
            get
            {
                return mBaseClassInfo != null;
            }
        }

        public string BaseClassName
        {
            get
            {
                if (mBaseClassInfo != null)
                {
                    return Clangen.NamespaceTool.CookNamespaceToCppNamespace(mBaseClassInfo.Name + mBaseClassInfo.TemplateParamsString);
                }
                else
                {
                    return "";
                }
            }
        }

        public string BaseClassIdentifyName
        {
            get
            {
                return Clangen.NamespaceTool.GetIdentifyName(mBaseClassInfo.Name) + mBaseClassInfo.TemplateParamsString;
            }
        }

        public string BaseClassLeafName
        {
            get
            {
                return Clangen.NamespaceTool.GetOnlyLeafName(BaseClassName);
            }
        }

        public bool IsBaseClassTemplate
        {
            get
            {
                //ToDo: add template support here~
                ////if(mBaseClassInfo != null)
                ////{
                ////    return mBaseClassInfo.BaseType == ClassDeclNode.BaseClassType.BaseClassTemplate;
                ////}
                ////else
                {
                    return false;
                }
            }
        }

        public string Name
        {
            get
            {
                return mFullName;
            }
        }

        public string AliasName
        {
            get
            {
                if (DropType == ContentDropType.ClassTemplateSpecialization)
                {
                    string cookName = Clangen.NamespaceTool.CppNamespaceToCookNamespace(mFullName);
                    string aliasName = mExporter.UsedAliasNameMap.GetFinalyAliasName(cookName);

                    if (cookName == aliasName)
                    {
                        return mFullName;
                    }
                    else
                    {
                        return Clangen.NamespaceTool.CookNamespaceToCppNamespace(aliasName);
                    }
                }
                else
                {
                    return mFullName;
                }

            }
        }

        public string IdentifyName
        {
            get
            {
                return Clangen.NamespaceTool.GetIdentifyName(Name);
            }
        }

        public string UnscopedName
        {
            get
            {
                if (DropType == ContentDropType.ClassTemplateSpecialization)
                {
                    string cookName = Clangen.NamespaceTool.CppNamespaceToCookNamespace(mFullName);
                    string aliasName = mExporter.UsedAliasNameMap.GetFinalyAliasName(cookName);

                    if (cookName == aliasName)
                    {
                        return Clangen.NamespaceTool.GetIdentifyName(aliasName);
                    }
                    else
                    {
                        return Clangen.NamespaceTool.GetOnlyLeafName(aliasName);
                    }
                }

                return mLeafName;
            }
        }

        public string NoNamespaceName
        {
            get
            {
                string baseName = UnscopedName;
                if (IsEmbedded)
                {
                    baseName = string.Format("{0}::{1}", ParentClassDrop.UnscopedName, baseName);
                }
                return baseName;
            }
        }

        public bool IsRootGCObject
        {
            get
            {
                return UnscopedName == "GCObject";
            }
        }

        public bool NeedIgnore
        {
            get
            {
                return IsRootGCObject;
            }
        }

        public ContentContainerDrop ParentClassDrop
        {
            get;
            set;
        }

        public bool IsEmbedded
        {
            get
            {
                return ParentClassDrop != null;
            }
        }

        public string ExportNamespace
        {
            get
            {
                if (IsRootGCObject)
                {
                    return "ClangenCli";
                }

                if (DropType == ContentDropType.ClassTemplateSpecialization)
                {
                    string cookName = Clangen.NamespaceTool.CppNamespaceToCookNamespace(mFullName);
                    string aliasName = mExporter.UsedAliasNameMap.GetFinalyAliasName(cookName);
                    string nsName, leafName;
                    Clangen.NamespaceTool.DivideNamespaceAndClassName(aliasName, out nsName, out leafName);
                    nsName = mExporter.QueryConfig().ConvertToExportNameSpace(nsName);

                    return nsName;
                }

                if (IsEmbedded)
                {
                    return string.Format("{0}.{1}", ParentClassDrop.ExportNamespace, ParentClassDrop.UnscopedName);
                }
                else
                {
                    return mExporter.QueryConfig().ConvertToExportNameSpace(DefaultNamespace);
                }

            }
        }

        public List<string> ExportNamespaceList
        {
            get
            {
                return Clangen.NamespaceTool.NamespaceToArray(ExportNamespace);
            }
        }

        public string CliClassName
        {
            get
            {
                return string.Format("{0}::{1}", Clangen.NamespaceTool.CookNamespaceToCppNamespace(ExportNamespace), UnscopedName);
            }
        }

        public List<TypeOfferDrop> RelativeArrayTypeList { get; private set; } = new List<TypeOfferDrop>();

        #endregion


        public ContentContainerDrop(ContentDropType dropType, string leafName, string fullName, string cookNamespace, Exporter parentExporter)
        {
            mLeafName = leafName;
            mFullName = fullName;
            mCookNamespace = cookNamespace;
            DropType = dropType;
            mExporter = parentExporter;
        }


        public void AddField(CppField field)
        {
            //Not need ignore here, just handle void* to IntPtr
            ////if(TypeHandleTool.IsPointer(field.NativeFieldDecl.Type.Handle) && TypeHandleTool.IsVoid(field.NativeFieldDecl.Type.Handle.PointeeType.CanonicalType))
            ////{
            ////    return;
            ////}

            ////if(Name.Contains("GUILD_CONFIG"))
            ////{
            ////    int a;
            ////    a = 1;
            ////}

            ////if(!TypeHandleTool.IsArray(field.NativeFieldDecl.Type.Handle) || TypeHandleTool.IsCharArray(field.NativeFieldDecl.Type.Handle))
            {
                Fields.Add(mExporter.CreateFieldDrop(this, mExporter.GenerateCppFieldContentOffer(field), GetUsedClassNameForChild()));
            }
        }

        public EnumDrop AddEnum(CppEnum e)
        {
            var enumdrop = mExporter.CreateEnumDrop(GetUsedClassNameForChild(), e);
            Enums.Add(enumdrop);
            return enumdrop;
        }

        public void AddProperty(TypeProperty prop)
        {
            Properties.Add(new PropertyDrop(GetUsedClassNameForChild(), prop.Property, prop.Get, prop.Set));
        }

        public void AddMethod(CppFunction m, Dictionary<string, CppTemplateArgument> allTempArgTypeMap, string targetName, bool is_overload)
        {
            var findMethods = Methods.Where(md => m.Name == md.Name);
            if (findMethods.Count() > 0)
            {
                Debug.Assert(findMethods.Count() == 1);
                var method = findMethods.First();
                method.AddMethod(m, allTempArgTypeMap);
            }
            else
            {
                var method = mExporter.CreateMethodContainerDrop(this as ClassDrop, m, allTempArgTypeMap, is_overload, targetName);

                method.ClassDelegateUseName = OverrideDelegateName;

                Methods.Add(method);
            }
        }

        public bool HasMethod(string methodName)
        {
            return Methods.Count(x => x.Name == methodName) > 0;
        }

        public void AddConstructor(CppFunction ctor, Dictionary<string, CppTemplateArgument> allTempArgTypeMap, int ctor_count)
        {
            if (!HasConstructor)
            {
                Debug.Assert(Constructor == null);
                Constructor = mExporter.CreateMethodContainerDrop(this as ClassDrop, ctor, allTempArgTypeMap, ctor_count > 1);
                HasConstructor = true;
            }
            else
            {
                Debug.Assert(Constructor != null);
                ////Constructor.AddConstructor(ctor);
                Constructor.AddMethod(ctor, allTempArgTypeMap);
            }
        }

        public void SetBaseClassInfo(Exporter.BaseClassInfo subClassInfo)
        {
            mBaseClassInfo = subClassInfo;
        }

        public bool IsGcClass
        {
            get
            {
                //ToDo: add RttiBase class support here~~
                ////if (mLeafName == "GCObject")
                ////{
                ////    return true;
                ////}

                //////Only handle for ClassDeclNode
                ////var contentNode = mExporter.ParentAST.QueryBaseClassInfo(mBaseClassInfo) as ContentContainerNode;
                ////while (contentNode != null)
                ////{
                ////    var baseInfo = contentNode.FirstBaseClass;
                ////    if(baseInfo != null)
                ////    {
                ////        if(baseInfo.BaseType == ClassDeclNode.BaseClassType.BaseClassInstance)
                ////        {
                ////            string leafName = Clangen.NamespaceTool.GetOnlyLeafName(baseInfo.Name);
                ////            if(leafName == "GCObject")
                ////            {
                ////                return true;
                ////            }
                ////        }
                ////    }

                ////    contentNode = mExporter.ParentAST.QueryBaseClassInfo(baseInfo) as ContentContainerNode;
                ////}

                return false;
            }
        }

        public virtual string GetUsedClassNameForChild()
        {
            return Name;
        }

        public string RpcServiceName
        {
            get;
            set;
        }

        public string RpcServiceId
        {
            get;
            set;
        }

        public string EntityGroupName
        {
            get;
            set;
        }

        public bool NeedLura
        {
            get;
            set;
        }

        public bool NeedRpc
        {
            get;
            set;
        }

        public bool IsEntityRpc
        {
            get;
            set;
        }
    }
}