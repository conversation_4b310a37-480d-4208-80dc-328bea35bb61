
NameSpaces:
  - Original: "g6.game"
    Destination: "MGame"
  - Original: "g6"
    Destination: "MG6"
  - Original: "g6.math"
    Destination: "MMath"
  - Original: "g6.scene"
    Destination: "MScene"
  - Original: "g6.vmcore"
    Destination: "MVmcore"
  - Original: "g6.gbf"
    Destination: "MGbf"


#IncludeFiles:
#  LuaIncludeFile: "deps/lua/include/lua.hpp"
#  LuaBridgeIncludeFile: "deps/LuaBridge/include/LuaBridge/LuaBridge.h"

Classes:
  g6.gbf.NodeGraphArchiveFactory:
  g6.gbf.NodeGraphArchive:
  g6.gbf.NGActionNode:
  g6.gbf.NGActionNode.ProcessingInfo:
  g6.gbf.NGBranch:
    Attributes:
      Category: "控制流程"
      DisplayName: "分支"
      Browsable: true
  g6.gbf.NGCallFunction:
    Attributes:
      Category: "函数"
      DisplayName: "调用"
      Browsable: false
  g6.gbf.NGConnection:
  g6.gbf.NGContext:
  g6.gbf.NGDelay:
    Attributes:
      Category: "时间"
      DisplayName: "延时执行"
      Browsable: true
  g6.gbf.NGDoN:
    Attributes:
      Category: "控制流程"
      DisplayName: "执行N次"
      Browsable: true
  g6.gbf.NGDoOnce:
    Attributes:
      Category: "控制流程"
      DisplayName: "执行单次"
      Browsable: true
  g6.gbf.NGEvent:
  g6.gbf.NGEventNode:
  g6.gbf.NGEventOnEnterFunction:
    Attributes:
      Category: "事件"
      DisplayName: "函数进入事件"
      Browsable: true
  g6.gbf.NGEventTestStarted:
    Attributes:
      Category: "事件"
      DisplayName: "测试事件"
      Browsable: true
  g6.gbf.NGEventOnLevelGameStart:
    Attributes:
      Category: "事件"
      DisplayName: "关卡游戏开始"
      Browsable: true
  g6.gbf.NGEventOnLevelGameEnd:
    Attributes:
      Category: "事件"
      DisplayName: "关卡游戏结束"
      Browsable: true
  g6.gbf.NGEventOnLevelPrepareBegin:
    Attributes:
      Category: "事件"
      DisplayName: "准备加载关卡"
      Browsable: true
  g6.gbf.NGEventOnLevelPrepareEnd:
    Attributes:
      Category: "事件"
      DisplayName: "加载关卡完成"
      Browsable: true
  g6.gbf.NGEventOnVariableChanged:
  g6.gbf.NGFlipFlop:
    Attributes:
      Category: "控制流程"
      DisplayName: FlipFlop
      Browsable: true
  g6.gbf.NGForLoop:
    Attributes:
      Category: "控制流程"
      DisplayName: "For循环"
      Browsable: true
  g6.gbf.NGForLoopWithBreak:
    Attributes:
      Category: "控制流程"
      DisplayName: "带中断循环"
      Browsable: true
  g6.gbf.NGFunctionReturn:
    Attributes:
      Category: "函数"
      DisplayName: "返回执行"
      Browsable: false
  g6.gbf.NGGate:
    Attributes:
      Category: "控制流程"
      DisplayName: "NGGate"
      Browsable: true
  g6.gbf.NGGetNamedVariableNode:
  g6.gbf.NGGraph:
  g6.gbf.NGGraphBase:
  g6.gbf.NGGraphFunction:
  g6.gbf.NGGraphGroup:
  g6.gbf.NGInstructionSlot:
  g6.gbf.NGLogMessage:
    Attributes:
      Category: "日志"
      DisplayName: "打印到控制台"
      Browsable: true
  g6.gbf.NGMetaMethodNode:
  g6.gbf.NGMetaEventNode:
    Attributes:
      Category: "事件"
      DisplayName: NGMetaEventNode
      Browsable: false
  g6.gbf.NGNamedVariable:
  g6.gbf.NGNode:
  g6.gbf.NGNumbericPromoteExprNode:
    Attributes:
      Category: "表达式操作"
      DisplayName: "提升变量类型"
      Browsable: true
  g6.gbf.NGSetNamedVariableNode:
    Attributes:
      Category: "变量操作"
      DisplayName: "设置全局变量"
      Browsable: true
  g6.gbf.NGSlot:
  g6.gbf.NGVariableSlot:
  g6.gbf.NGGraphGroup:
  g6.gbf.NGWorkspace:
  g6.gbf.NGWorkspace.Listener:
    IsOverride: true 
  g6.gbf.NGLuaParamMeta:
  g6.gbf.NGLuaMethodMeta:
  g6.gbf.NGLuaObjectDescriptor:
  g6.gbf.NGLuaAgentContainer:
  g6.gbf.NGLuaAgentMetaInfo:
  g6.gbf.NGMetaFieldSetNode:
  g6.gbf.NGMetaFieldGetNode:
  g6.gbf.NGXRAFieldNode:
    Attributes:
      Category: "表达式操作"
      DisplayName: "获取x分量"
      Browsable: true
  g6.gbf.NGYGBFieldNode:
    Attributes:
      Category: "表达式操作"
      DisplayName: "获取y分量"
      Browsable: true
  g6.gbf.NGZBCFieldNode:
    Attributes:
      Category: "表达式操作"
      DisplayName: "获取z分量"
      Browsable: true
  g6.gbf.NGWAWFieldNode:
    Attributes:
      Category: "表达式操作"
      DisplayName: "获取w分量"
      Browsable: true
  g6.gbf.NGBinaryExpressionNode:
    Attributes:
      Category: "表达式操作"
      DisplayName: "二元表达式"
      Browsable: true
  g6.gbf.NGUnaryExpessionNode:
    Attributes:
      Category: "表达式操作"
      DisplayName: "一元表达式"
      Browsable: true
  g6.gbf.NGFunctionSlot:
  g6.vmcore.MetaParameterInfo:
#  g6.vmcore.MetaTypeInfo:
  g6.vmcore.VMCoroutine:
  g6.vmcore.VMInstruction:
  g6.vmcore.VMMemoryScope:
  g6.vmcore.VMNamedMemoryScope:
  g6.vmcore.VMRunScope:
  g6.vmcore.VMTerm:
  g6.vmcore.VMTermConst:
  g6.vmcore.VMTermField:
  g6.vmcore.VMTermMethod:
  g6.vmcore.VMValue:
    Skips:
      - "value_info"
  g6.math.MathTool:
    Skips:
      - "TestStringNoConst"
  g6.math.Sphere:
  g6.math.Vector2:
  g6.math.Vector3:
  g6.scene.Bone:
  g6.scene.MovComponent:
    IsOverride: true
  g6.math.IntVector:
  g6.math.IntMap:
  g6.math.NodeTestPtr:
#  g6.math.Vector3:
#  g6.math.Matrix3:
#    Skips:
#      - "EigenSolveSymmetric"
#  g6.math.Matrix4:
#    Skips:
#      - "operator="

Enums:
  - g6.TestEnum
  - g6.math.*.kMyTestVal
  - g6.math.WithNameEnum
  - g6.vmcore.VMRunStepStatus
  - g6.vmcore.VMValueType
  - g6.vmcore.VMEvalGroupType
  - g6.vmcore.VMOperatorType
  - g6.vmcore.VMTermType
  - g6.gbf.NGNodeType
  - g6.gbf.NGSlotAvailableFlag
  - g6.gbf.NGLinkDirection
  - g6.gbf.NGSlotType
  - g6.gbf.NGFunctionSlotType
  - g6.gbf.NGGraphState
  - g6.gbf.NGGraphType
  #- GameComponentType

Functions:
  g6.math.TestCreateVector3:
  g6.math.TestDestroyVector3:

SkipClasses:
  #- Clonable
