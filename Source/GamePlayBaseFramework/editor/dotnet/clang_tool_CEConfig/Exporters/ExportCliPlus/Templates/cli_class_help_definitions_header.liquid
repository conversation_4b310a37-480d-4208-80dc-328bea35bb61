public :
{%- if this_class.has_base_class -%}
    {{ this_class.unscoped_name }}(const {{ this_class.name }} * obj, bool created_by_clr);
{%- else -%}
    {{ this_class.unscoped_name }}(const {{ this_class.name }} * obj, bool created_by_clr);
{%- endif -%}
    {{ this_class.unscoped_name }}(const {{ this_class.name }} * obj): {{ this_class.unscoped_name }}(obj, false){}

{%- if this_class.is_gc_class -%}
public:
    //GCObject creator for activator
    {{ this_class.unscoped_name }}(ClangenCli::RttiBase^ keeper)
        : {{ this_class.unscoped_name }}((const {{ this_class.name }} *)keeper->get_native())
    {
    }

{%- endif -%}
public :
    virtual ~{{ this_class.unscoped_name }}()
    {
{%- unless this_class.has_base_class -%}
        this->!{{ this_class.unscoped_name }}();
{%- endunless -%}
    }

    !{{ this_class.unscoped_name }}()
    {
{%- unless this_class.has_base_class -%}
        finalize();
{%- endunless -%}
    }

{%- unless this_class.has_base_class -%}
    void finalize()
    {
{%- if this_class.is_gc_class -%}
        if(_native)
        {
            ClangenNative::GNativeFinalizer->AddToDelete(_native);
            _native = nullptr;
        }
{%- else -%}
        if(_created_by_clr && _native)
        {
            delete _native;
            _native = nullptr;
        }
{%- endif -%}
        System::GC::SuppressFinalize(this);
    }
{%- endunless -%}

public :
{%- unless this_class.has_base_class -%}
    {{ this_class.name }}* _native = nullptr;
    bool _created_by_clr = false;

{%- endunless -%}
//Public Declarations
public:
{%- unless this_class.is_gc_class -%}
    inline bool operator==({{ this_class.unscoped_name }}^ t) 
    {
        return Equals(t);
    }
    
    virtual bool Equals(Object^ obj) override
    {
        {{ this_class.unscoped_name }}^ clr = dynamic_cast<{{ this_class.unscoped_name }}^>(obj);
        if (clr == CLR_NULL)
        {
            return false;
        }
        
        return (_native == clr->_native);
    }
{%- endunless -%}

    virtual int GetHashCode() override
    {
        return (int)(reinterpret_cast<long long>(_native));
    }


    static operator {{ this_class.unscoped_name }}^ (const {{ this_class.name }}* t);

{%- unless this_class.is_gc_class -%}
    inline static operator {{ this_class.unscoped_name }}^ (const {{ this_class.name }}& t)
    {
        return ({{ this_class.unscoped_name }}^)(&t);
    }
{%- endunless -%}

    inline static operator {{ this_class.name }}* ({{ this_class.unscoped_name }}^ t)
    {
        return (t == CLR_NULL) ? 0 : static_cast<{{ this_class.name }}*>(t->_native);
    }

    inline static operator {{ this_class.name }}& ({{ this_class.unscoped_name }}^ t)
    {
        return *static_cast<{{ this_class.name }}*>(t->_native);
    }
