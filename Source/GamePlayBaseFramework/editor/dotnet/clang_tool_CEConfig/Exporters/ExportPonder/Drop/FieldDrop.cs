using DotLiquid;

namespace CppAst.Ponder
{
    public class FieldDrop : General.FieldDrop
    {
        private static Template msBridgeBodyLT;

        static FieldDrop()
        {
            msBridgeBodyLT = Clangen.TemplateHelper.ParseTemplate("Exporters/ExportPonder/Templates/native_field.liquid");
        }

        public FieldDrop(General.ContentContainerDrop parentDrop, General.FieldContentOffer f, string className, Exporter exporter) :
            base(parentDrop, f, className, exporter)
        {
        }

        internal string GenerateRenderContent(DotLiquid.Template tempLT, string useCase, bool stripEndLineWrap = false)
        {
            string result = tempLT.Render(DotLiquid.Hash.FromAnonymousObject(new
            {
                field = this,
                use_case = useCase,
            }));

            return stripEndLineWrap ? Clangen.NamespaceTool.TrimTailLineWrap(result) : result;
        }


        public string Render
        {
            get
            {
                string useCase = "Normal";
                if (IsStatic)
                {
                    useCase = "Static";
                }
                else
                {
                    if (IsCharArray)
                    {
                        useCase = "CharArray";
                    }
                }

                return GenerateRenderContent(msBridgeBodyLT, useCase, true);
            }
        }

    }
}
