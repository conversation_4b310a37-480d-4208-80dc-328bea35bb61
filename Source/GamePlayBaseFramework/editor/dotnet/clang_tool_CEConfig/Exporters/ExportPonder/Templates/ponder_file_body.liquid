#include "{{ out_file_name }}.hpp"

//--->export include files begin

{{ includes }}
//--->export include files end

//All class instances export here
{%- for class in classes -%}
{{ class.render }}
{%- endfor -%}

//All class template export here 
{%- for template_class in template_classes -%}
{{ template_class.render }}
{%- endfor -%}

//All class template instance export here
{%- for template_instance in template_instance_list -%}
{{ template_instance.render }}
{%- endfor -%}

//All global enums export here 
{%- for enum in enums -%}
{{ enum.render }}
{%- endfor -%}

{% comment %}
//All global function groups export here
{%- for group in function_groups -%}
{{ group.render }}
{%- endfor -%}
{% endcomment %}

void register_ponder_reflection_{{ out_file_name }}()
{
    //Call class template specialization here
{%- for template_instance in template_instance_list -%}
    ponder_register_class_template_instance_{{ template_instance.identify_name }}();
{%- endfor -%}

{%- for class in classes -%}
    {%- for embed_class in class.embeded_classes -%}
    //embeded here
    ponder_register_class_{{ embed_class.identify_name }}();
    {%- endfor -%}
    ponder_register_class_{{ class.identify_name }}();
{%- endfor -%}

{%- for enum in enums -%}
    ponder_register_enum_{{ enum.identify_name | replace: "::", "_" }}();
{%- endfor -%}

{%- for group in function_groups -%}
    ponder_register_global_functions_{{ group.identify_name }}();
{%- endfor -%}
}

//----------------------------------------------------------------------------------------------
//All class instances expose to lua here
{%- for class in classes -%}
    {%- if class.need_lura -%}
{{ class.render_lua }}
    {%- endif -%}
{%- endfor -%}

{% comment %}
//ToDo: implement template instance export to lua support
//All class template instance export here
{%- for template_instance in template_instance_list -%}
{{ template_instance.render }}
{%- endfor -%}
{% endcomment %}

//All global enums expose to lua here 
{%- for enum in enums -%}
{{ enum.render_lua }}
{%- endfor -%}

//All lua override class export here 
{%- for override_class in override_classes -%}
{{ override_class.render_override }}
{%- endfor -%}

static int ponder_register_all_needed_typedefs_to_lua_{{ out_file_name }}(lua_State* L)
{
{%- for typedef in typedefs -%}
    luabridge::getGlobalNamespace(L)
        {% for ns in typedef.export_namespace_list %}.beginNamespace("{{ns}}"){% endfor %}
            .declareClassAlias<{{typedef.alias_type}}>("{{typedef.alias_name}}")
        {% for ns in typedef.export_namespace_list %}.endNamespace(){% endfor %};

{%- endfor -%}

    return 0;
}

void expose_ponder_to_lua_{{ out_file_name }}(lua_State* L)
{
    //Call class template specialization here
{% comment %}
//ToDo: implement template instance export to lua support
{%- for template_instance in template_instance_list -%}
    //ponder_register_class_template_instance_{{ template_instance.identify_name }}();
{%- endfor -%}
{% endcomment %}
{%- for class in classes -%}
    {%- if class.need_lura -%}
    ponder_expose_class_to_lura_{{ class.identify_name }}(L);   //Container class export first
        {%- for embed_class in class.embeded_classes -%}
    //embeded here
    ponder_expose_class_to_lura_{{ embed_class.identify_name }}(L);
        {%- endfor -%}
    {%- endif -%}
{%- endfor -%}

{%- for override_class in override_classes -%}
    ponder_expose_override_class_to_lura_{{ override_class.identify_name }}(L);
{%- endfor -%}

{%- for enum in enums -%}
    ponder_expose_enum_to_lura_{{ enum.identify_name | replace: "::", "_" }}(L);
{%- endfor -%}

{% comment %}
{%- for group in function_groups -%}
    ponder_register_global_functions_{{ group.identify_name }}(L);
{%- endfor -%}
{% endcomment %}

    ponder_register_all_needed_typedefs_to_lua_{{ out_file_name }}(L);
}

//----------------------------------------------------------------------------------------------
void expose_ponder_to_rpc_server_{{ out_file_name }}(gbf::logic::ServiceServerPtr serviceServer)
 {
{%- for class in classes -%}
    {%- if class.need_rpc -%}
        {%- if class.is_entity_rpc -%}
    {{ class.render_entity_rpc_service }}
        {%- else -%}
    {{ class.render_rpc_service }}
        {%- endif -%}
    {%- endif -%}
{%- endfor -%}
 }
//----------------------------------------------------------------------------------------------
