struct {{class.name}} {%- if class.render_base.size > 0 -%}: public {{class.render_base}}{%- endif %}
{
public:
{%- comment -%} Embeded Enum {%- endcomment -%}
{%- for enum in class.enums -%}
{%- if enum.need_binding_editor -%}
{% include "Swig/enum" with enum %}
{%- endif -%}
{%- endfor -%}
{%- comment -%} fields {%- endcomment -%}
{%- for field in class.fields -%}
{%- if field.need_binding_editor -%}
{%- if field.cs_attributes.size > 0 -%}
    %csattributes {{field.name}} "{%- for item in field.cs_attributes -%}[{{- item -}}]{%- endfor -%}"
{%- endif -%}
    {{field.declaration}}
{%- endif -%}
{%- endfor -%}
{%- comment -%} Constructors {%- endcomment -%}
{%- for ctor in class.editor_constructors -%}
{{ctor.access_specifier}}:
    {{ctor.declaration}}
{%- endfor -%}
{%- comment -%} Destructors {%- endcomment -%}
{%- if class.destructor -%}
{{class.destructor.access_specifier}}:
    {{class.destructor.declaration}}
{%- endif -%}
public:
{%- comment -%} Functions {%- endcomment -%}
{%- for func in class.functions -%}
{%- if func.need_binding_editor -%}
    {{func.swig_declaration}}
{%- endif -%}
{%- endfor -%}
};
