/* ----------------------------------------------------------------------------
 * This file was automatically generated by CodeGen {% include 'version' %}
 * --------------------------------------------------------------------------*/
#include "CrossBase/Serialization/SerializeTemplateBase.h"
{%- for include in includes -%}
#include "{{include}}"
{%- endfor -%}

{%- for class in classes -%}
// ===== {{class.full_name}} Serialize =====
cross::SerializeNode {{class.full_name}}::Serialize(cross::Context& context) const
{
    cross::SerializeNode node;
    this->Serialize(node, context);
    return node;
}

void {{class.full_name}}::Serialize(cross::SerializeNode& node, cross::Context& context) const
{
    {%- if class.precheck_serialize_node_method -%}
    // Precheck
    if (!this->{{class.precheck_serialize_node_method}}(node)) { return false;}
    {%- endif -%}

    // Bases serialize
    {%- for base in class.base_classes -%}
    {{base.full_name}}::Serialize(node, context);
    {%- endfor -%}

    // Fields serialize
    {%- for field in class.fields -%}
    {%- if field.need_serialization -%}
    cross::SerializeNode t_{{field.name}};
    cross::Serialize(this->{{field.name}}, t_{{field.name}}, context);
    node["{{field.name}}"] = std::move(t_{{field.name}});
    {%- endif -%}
    {%- endfor -%}

    {%- if class.additional_serialize_method -%}
    // Additional serialize
    this->{{class.additional_serialize_method}}(node, context);
    {%- endif -%}
}

// ===== {{class.full_name}} Deserialize =====
bool {{class.full_name}}::Deserialize(const cross::DeserializeNode& in, cross::Context& context)
{
    bool ret = true;
    {%- if class.precheck_deserialize_node_method -%}
    // Precheck
    if (!this->{{class.precheck_deserialize_node_method}}(in)) { return false;}
    {%- endif -%}

    // Bases deserialize
    {%- for base in class.base_classes -%}
    {{base.full_name}}::Deserialize(in, context);
    {%- endfor -%}

    // Fields serialize
    {%- for field in class.fields -%}
    {%- if field.need_serialization -%}
    auto t_{{field.name}} = in.HasMember("{{field.name}}");
    if (t_{{field.name}}) {
        ret &= cross::Deserialize(*t_{{field.name}}, this->{{field.name}}, context);
    } else {
        ret = false;
    }
    {%- endif -%}
    {%- endfor -%}

    {%- if class.additional_deserialize_method -%}
    // Additional serialize
    this->{{class.additional_deserialize_method}}(in, context);
    {%- endif -%}
    return ret;
}
bool {{class.full_name}}::PostDeserialize(const cross::DeserializeNode& in, cross::Context& context)
{
    bool ret = true;
    {%- if class.precheck_deserialize_node_method -%}
    // Precheck
    if (!this->{{class.precheck_deserialize_node_method}}(in)) { return false;}
    {%- endif -%}

    // Bases deserialize
    {%- for base in class.base_classes -%}
    ret &= {{base.full_name}}::PostDeserialize(in, context);
    {%- endfor -%}

    // Fields serialize
    {%- for field in class.fields -%}
    {%- if field.need_serialization -%}
    auto t_{{field.name}} = in.HasMember("{{field.name}}");
    if (t_{{field.name}}) {
        ret &= cross::PostDeserialize(*t_{{field.name}}, this->{{field.name}}, context);
    } else {
        ret = false;
    }
    {%- endif -%}
    {%- endfor -%}
    return ret;
}

{%- endfor -%}