/* ----------------------------------------------------------------------------
 * This file was automatically generated by CodeGen {% include 'version' %}
 * --------------------------------------------------------------------------*/
#pragma once
#include "ScriptEngine/ScriptEngine.h"
#include "Scripts/BaseLayer/ScriptMath.h"
#include "Scripts/BaseLayer/ScriptConvert.h"
#include "Runtime/Scripts/EngineLayer/ScriptWorld.h"

{%- for include in includes -%}
#include "{{include}}"
{%- endfor -%}

namespace cross::scripts {
{%- for class in classes -%}
{%- if class.is_system -%}
{%- include 'ScriptBinding/component.header' with class-%}
{%- elsif class.need_binding_lua -%}
{%- include 'ScriptBinding/class.header' with class-%}
{%- endif -%}

{%- endfor -%}
}

// script convert
namespace cross::scripts {
{%- for class in classes -%}
{%- unless class.is_system -%}
{%- if class.is_gameplay -%}
{%- elsif class.need_binding_lua -%}
CONVERTER_SPEC({{class.full_name}}, {{class.script_name}});
{%- endif -%}
{%- endunless -%}
{%- endfor -%}
}