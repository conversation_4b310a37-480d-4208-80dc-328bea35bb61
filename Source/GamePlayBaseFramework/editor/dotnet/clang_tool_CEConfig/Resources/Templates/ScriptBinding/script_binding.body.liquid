/* ----------------------------------------------------------------------------
 * This file was automatically generated by CodeGen {% include 'version' %}
 * --------------------------------------------------------------------------*/
#include "EnginePrefix.h"
#include "Runtime/Scripts/EngineLayer/ScriptWorld.h"

{%- for include in includes -%}
#include "{{include}}"
{%- endfor -%}

using LocalValue = script::Local<script::Value>;
namespace cross::scripts {
{%- for class in classes -%}
{%- if class.is_system -%}
{%- include 'ScriptBinding/component.body' with class -%}
{%- elsif class.need_binding_lua -%}
{%- include 'ScriptBinding/class.body' with class -%}
{%- endif -%}

{%- endfor -%}
}