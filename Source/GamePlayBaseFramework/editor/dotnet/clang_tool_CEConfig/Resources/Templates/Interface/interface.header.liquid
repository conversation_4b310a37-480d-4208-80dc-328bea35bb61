/* ----------------------------------------------------------------------------
 * This file was automatically generated by CodeGen {% include 'version' %}
 * --------------------------------------------------------------------------*/
#pragma once
#include "EnginePrefix.h"
#include "Runtime/GameWorld/GameWorld.h"

{%- for include in includes -%}
#include "{{include}}"
{%- endfor -%}

namespace cross {
{%- for system in systems -%}
struct {{system.interface_name}}{
{%- for func in system.functions -%}
{%- if func.is_ce_function -%}
    static {{func.return_type.full_name}} {{func.name}}({{func.interface_parameters}});
{%- endif -%}
{%- endfor -%}

{%- for comp in system.component_classes -%}
{%- for prop in comp.fields -%}
{%- if prop.need_reflection or prop.need_binding_script -%}
{%- if prop.setter -%} {%- unless prop.setter_exists -%}
    static void {{prop.setter}}(cross::GameWorld* gworld, UInt64 entityId, {{prop.type.full_name}} value);
{%- endunless -%} {%- endif -%}
{%- if prop.getter -%} {%- unless prop.getter_exists -%}
    static {{prop.type.full_name}} {{prop.getter}}(cross::GameWorld* gworld, UInt64 entityId);
{%- endunless -%} {%- endif -%}
{%- endif -%}
{%- endfor -%}
{%- endfor -%}

    {{system.interface_name}}() = delete;
};

{%- endfor -%}
}