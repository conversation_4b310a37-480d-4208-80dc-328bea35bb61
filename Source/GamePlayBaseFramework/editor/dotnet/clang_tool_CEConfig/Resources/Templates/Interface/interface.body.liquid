/* ----------------------------------------------------------------------------
 * This file was automatically generated by CodeGen {% include 'version' %}
 * --------------------------------------------------------------------------*/
#include "EnginePrefix.h"
#include "Runtime/GameWorld/GameWorld.h"

{%- for include in includes -%}
#include "{{include}}"
{%- endfor -%}

namespace cross {
{%- for system in systems -%}
{%- capture interface_name -%}{{system.interface_name}}{%- endcapture -%}
{%- for func in system.functions -%}
{%- if func.is_ce_function -%}
{{func.return_type.full_name}} {{interface_name}}::{{func.name}}({{func.interface_parameters}})
{
    {%- if func.is_static -%}
    auto system = static_cast<{{system.full_name}}*>(nullptr);
    {%- else -%}
    auto system = gworld->GetGameSystem<{{system.full_name}}>();
    {%- endif -%}

    {%- assign arguments = "" -%}
    {%- for param in func.parameters -%}
    {%- assign t = param.type.without_const_ref -%}
    {%- if t.is_scoped_component -%}
    auto t_{{param.name}} = gworld->GetComponent<{{t.scoped_component_type}}>(cross::ecs::EntityID({{param.name}})).{{t.scoped_component_access_method}}();
    {%- capture arguments -%}{{arguments}}, t_{{param.name}}{%- endcapture -%}
    {%- else -%}
    {%- capture arguments -%}{{arguments}}, {{param.name}}{%- endcapture -%}
    {%- endif -%}
    {%- endfor -%}

    return system->{{func.name}}({{arguments | slice: 2, arguments.size}});
}
{%- endif -%}
{%- endfor -%}

{%- for comp in system.component_classes -%}
{%- for prop in comp.fields -%}
{%- if prop.need_reflection or prop.need_binding_script -%}
{%- if prop.setter -%} {%- unless prop.setter_exists -%}
void {{interface_name}}::{{prop.setter}}(cross::GameWorld* gworld, UInt64 entityId, {{prop.type.full_name}} value)
{
    auto comp = gworld->GetComponent<{{prop.parent.full_name}}>(cross::ecs::EntityID(entityId)).Write();
    comp->{{prop.name}} = value;
}
{%- endunless -%} {%- endif -%}

{%- if prop.getter -%} {%- unless prop.getter_exists -%}
{{prop.type.full_name}} {{interface_name}}::{{prop.getter}}(cross::GameWorld* gworld, UInt64 entityId)
{
    auto comp = gworld->GetComponent<{{prop.parent.full_name}}>(cross::ecs::EntityID(entityId)).Read();
    return comp->{{prop.name}};
}

{%- endunless -%} {%- endif -%}
{%- endif -%}
{%- endfor -%}
{%- endfor -%}
{%- endfor -%}
}