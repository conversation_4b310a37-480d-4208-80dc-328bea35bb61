/* ----------------------------------------------------------------------------
 * This file was automatically generated by CodeGen {% include 'version' %}
 * --------------------------------------------------------------------------*/
#pragma once
#pragma warning(disable:4100)
#include "visual/blueprint/blueprint_define.hpp"

#include "visual/blueprint/details/node/blueprint_action_node.h"
#include "visual/blueprint/details/node/struct/blueprint_make_struct.h"
#include "visual/blueprint/details/node/struct/blueprint_break_struct.h"
#include "visual/blueprint/details/node/struct/blueprint_set_member_in_struct.h"
#include "visual/blueprint/details/blueprint_graph_group.h"
#include "visual/blueprint/details/blueprint_named_variable.h"

#include  "GameFramework/Objects/CastToTemplate.hpp"
#include  "GameFramework/ValueConverter/InspectHelper.h"

{%- for include in includes -%}
#include "{{include}}"
{%- endfor -%}

namespace gbf { namespace logic {

{%- for class in classes -%}
class {{module_api_str}}  CEMeta() UBlueprintNode_CastTo{{class.name}} : public UBlueprintNode_CastTo<{{class.full_name}}>{};
class {{module_api_str}}  CEMeta() UBlueprintNode_GetClass_{{class.name}} : public UBlueprintNode_GetClass<{{class.full_name}}>{};
{%- for function in class.functions -%}
{%- if function.is_workflow_export == true -%}
// {{function.declaration }}  
{% capture cur_class_name %}UBlueprintNode_{{class.name}}_{{function.name}}{% endcapture %}class {{module_api_str}} CEMeta() {{cur_class_name}} : {%- if function.is_workflow_executable == true -%}public UBlueprintActionNode{%-elsif function.is_workflow_pure == true -%}public UBlueprintNode{%- endif -%}
    {
    public:
        CEMeta()
        {{cur_class_name}}()
        {%- if function.is_workflow_executable == true -%}
        : UBlueprintActionNode(BlueprintSlotAvailableFlag::NodeVarIn, "{{function.name}}")
{%-elsif function.is_workflow_pure == true -%}
        : UBlueprintNode(BlueprintNodeType::Variable, BlueprintSlotAvailableFlag::Var, "{{function.name}}")
{%- endif -%}
        {
            InitializeSlotsImpl();
        }
    
    protected:
        {%- if function.is_static == false -%}
        int target_slot_id = 0; 
        {%- endif -%}
        {% for param in function.parameters %}int {{param.name}}_slot_id = 0;
        {% endfor %}
        void InitializeSlotsImpl();

{%- if function.is_workflow_executable == true -%}
        ProcessingInfo RtActivateLogicImpl(machine::VCoroutine * coroutine_, UBlueprintExecSlot * slot) override;
{%-elsif function.is_workflow_pure == true -%}
        machine::VValuePtr RtPullResult(UBlueprintDataSlot* slot, machine::MemoryScope* local, machine::NamedMemoryScope* global) override;
{%- endif -%}
    };

{%- endif -%}
{%- endfor -%}
{%- if class.is_workflow_struct == true -%}
{%- if class.is_rtti_class == false -%}

{% capture cur_struct_name %}VarInspectorHelper_{{class.full_name | replace: ":", "_"}}{% endcapture %} class {{module_api_str}} CEMeta(Cli, Reflect) {{cur_struct_name}} : public VarInspectorHelper
{
public:
    using DataType = {{class.full_name}};

    CEMeta(Cli, Reflect)
    {{cur_struct_name}}()
    {}

    CEMeta(Cli)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", DisplayName = "{{class.name}}"))
    DataType mValue;

    CEMeta(Cli)
    void SyncFromRuntime() override
    {
        auto bp_var = mGraphGroupProvider()->GetNamedVariableByName(mVarName);
        const auto& val = bp_var->get_default_value();
        auto& wrapper_obj = reflection::value_ref_as<reflection::UserObject>(val);
        mValue = *(reflection::get_raw_pointer_user_object<DataType>(wrapper_obj));
    }

    CEMeta(Cli)
    void UpdateToRuntime() override
    {
        auto bp_var = mGraphGroupProvider()->GetNamedVariableByName(mVarName);
        const auto& val = bp_var->get_default_value();
        auto& wrapper_obj = reflection::value_ref_as<reflection::UserObject>(val); 
        *(reflection::get_raw_pointer_user_object<DataType>(wrapper_obj)) = mValue;
    }
};
    

{% capture cur_class_name %}UBlueprintMakeStruct_{{class.full_name | replace: ":", "_"}}{% endcapture %}    class {{module_api_str}} CEMeta() {{cur_class_name}} : public UBlueprintMakeStruct
    {
    public:
        CEMeta()
        {{cur_class_name}}()
        {
            m_title = "Make {{class.name}}";
            InitializeSlotsImpl();
        }

        CEMeta()
        static inline std::string StructFullName =  "{{class.full_name}}";

    protected:
        {% for field in class.fields %}{%- if field.is_public == true and field.is_writable == true-%}int {{field.name}}_slot_id = 0;{%- endif -%}
        {% endfor %}
        void InitializeSlotsImpl();
        machine::VValuePtr RtPullResult(UBlueprintDataSlot* slot, machine::MemoryScope* local, machine::NamedMemoryScope* global) override;
    };

{% capture cur_class_name %}UBlueprintBreakStruct_{{class.full_name | replace: ":", "_"}}{% endcapture %}    class {{module_api_str}} CEMeta() {{cur_class_name}} : public UBlueprintBreakStruct
    {
    public:
        CEMeta()
        {{cur_class_name}}()
        {
            m_title = "Break {{class.name}}";
            InitializeSlotsImpl();
        }
    protected:
        {% for field in class.fields %}{%- if field.is_public == true and field.is_readable == true-%}int {{field.name}}_slot_id = 0;{%- endif -%}
        {% endfor %}
        void InitializeSlotsImpl();
        machine::VValuePtr RtPullResult(UBlueprintDataSlot* slot, machine::MemoryScope* local, machine::NamedMemoryScope* global) override;
    };

{% capture cur_class_name %}UBlueprintSetMemberIn_{{class.full_name | replace: ":", "_"}}{% endcapture %}    class {{module_api_str}} CEMeta() {{cur_class_name}} : public UBlueprintSetMemberInStruct
    {
    public:
        {{cur_class_name}}()
        {
            m_title = "SetMemberIn {{class.name}}";
            InitializeSlotsImpl();
        }
    protected:        
        int target_slot_id = 0; 
        {% for field in class.fields %}{%- if field.is_public == true and field.is_writable == true-%}int {{field.name}}_slot_id = 0;{%- endif -%}
        {% endfor %}
        void InitializeSlotsImpl();
        gbf::logic::UBlueprintActionNode::ProcessingInfo RtActivateLogicImpl(gbf::machine::VCoroutine*, gbf::logic::UBlueprintExecSlot*) override;
    };
{%- endif -%}
{%- endif -%}
{%- endfor -%}
}}