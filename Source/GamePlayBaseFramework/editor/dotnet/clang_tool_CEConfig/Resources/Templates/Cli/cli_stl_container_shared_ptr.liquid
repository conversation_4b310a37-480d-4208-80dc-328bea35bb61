{%- assign native_name = Type.full_cpp_name -%}
{%- assign class_name = Type.unscoped_name -%}
{%- assign element_managed_name = Type.first_param_drop.managed_cli_type_name_no_symbol -%}
{%- assign element_native_name = Type.first_param_drop.native_cpp_type_name -%}
{%- unless Type.is_embedded -%} {{ "public "}} {%- endunless -%} ref class {{ class_name }} : public {{ element_managed_name }}
{
public :
    {{ native_name }}* mSharedPtr;
    {{ class_name }}(const {{ native_name }}& sharedPtr) : {{ element_managed_name }}(sharedPtr.get())
    {
        mSharedPtr = new {{ native_name }}(sharedPtr);
    }
    {{ class_name }}(const {{ native_name }}* sharedPtr) : {{ element_managed_name }}((*sharedPtr).get())
    {
        mSharedPtr = new {{ native_name }}(*sharedPtr);
    }
    !{{ class_name }}()
    {
        if (mSharedPtr != nullptr)
        {
            //Captain::GNativeFinalizer->AddToDelete(mSharedPtr);
            delete mSharedPtr;
            mSharedPtr = nullptr;
        }
    }
    ~{{ class_name }}()
    {
        this->!{{ class_name }}();
    }
public:
    static operator {{ class_name }}^ (const {{ native_name }}& ptr) 
    {
        if (!ptr) return nullptr;
        return gcnew {{ class_name }}(const_cast<{{ native_name }}&>(ptr));
    }
    static operator {{ native_name }}& ({{ class_name }}^ t) {
        if (CLR_NULL == t) return *((gcnew {{ class_name }}({{ native_name }}()))->mSharedPtr);
        return *(t->mSharedPtr);
    }
    static operator {{ native_name }}* ({{ class_name }}^ t) {
        if (CLR_NULL == t) return (gcnew {{ class_name }}({{ native_name }}()))->mSharedPtr;
        return t->mSharedPtr;
    }
    {{ class_name }}({{ element_managed_name }}^ obj) : {{ element_managed_name }}(({{ element_native_name }}*)obj->_native, false)
    {
        mSharedPtr = new {{ native_name }}(static_cast<{{ element_native_name }}*>(obj->_native));
    }
    virtual bool Equals(Object^ obj) override
    {
        {{ class_name }}^ clr = dynamic_cast<{{ class_name }}^>(obj);
        if (clr == CLR_NULL)
            return false;
        return (_native == clr->_native);
    }
    bool Equals({{ class_name }}^ obj)
    {
        if (obj == CLR_NULL)
            return false;
        return (_native == obj->_native);
    }
    static bool operator == ({{ class_name }}^ val1, {{ class_name }}^ val2)
    {
        if ((Object^)val1 == (Object^)val2) return true;
        if ((Object^)val1 == nullptr || (Object^)val2 == nullptr) return false;
        return (val1->_native == val2->_native);
    }
    static bool operator != ({{ class_name }}^ val1, {{ class_name }}^ val2)
    {
        return !(val1 == val2);
    }
    virtual int GetHashCode() override
    {
        return (int)reinterpret_cast<long long>(_native);
    }
    [System::ComponentModel::BrowsableAttribute(false)]
    property System::IntPtr NativePtr
    {
        System::IntPtr get() { return (System::IntPtr)mSharedPtr; }
    }
    [System::ComponentModel::BrowsableAttribute(false)]
    property bool Unique
    {
        bool get()
        {
            return (*mSharedPtr).unique();
        }
    }
    [System::ComponentModel::BrowsableAttribute(false)]
    property int UseCount
    {
        int get()
        {
            return (*mSharedPtr).use_count();
        }
    }
    [System::ComponentModel::BrowsableAttribute(false)]
    property {{ element_managed_name }}^ Target
    {
        {{ element_managed_name }}^ get()
        {
            return static_cast<{{ element_native_name }}*>(_native);
        }
    }
};