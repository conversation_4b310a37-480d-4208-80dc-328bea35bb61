#pragma once
#include "workflow_node.h"
#include "workflow_node_registry.h"
namespace gbf { namespace logic {
    class UBlueprintGraph;
}}   // namespace gbf::logic

namespace cross {
class WorkflowEditor;
struct WorkflowEditorCallback;

class WorkflowGraphPin : public GraphPin
{
public:
    WorkflowSlot* m_Slot = nullptr;
    bool is_exec = false;
};

class WorkflowGraph : public Graph
{
public:
    WorkflowGraph(WorkflowEditor& editor, WorkflowEditorCallback* callback);

    WorkflowGraphNode* AddNode(WorkflowNode* in_node);
    void DeleteNode(WorkflowNode* in_node);

    void UpdateNodePosition(WorkflowNode* in_node);

    void CreateLink(WorkflowSlot* outputPin, WorkflowSlot* inputPin);
    void BreakLink(WorkflowSlot* outputPin, WorkflowSlot* inputPin);

    void ReconstructLinks();

    void Reset(gbf::logic::UBpGraphBase* new_logic_graph);

    void BreakUpLinksForNode(const gbf::logic::UBlueprintNode* in_logic_node);
    void RefreshNodeSlots(const gbf::logic::UBlueprintNode* in_logic_node);

    void OnVariableTypeChanged(const std::string& name, WorkflowVariableType new_type);
    void OnVariableRenamed(const std::string& old_name, const std::string& new_name);
    void OnVariableDeleted(const std::string& name);

protected:
    void TraverseAllVariableNodes(const std::function<void(WorkflowGraphNode*)>& lambda);
    void TraverseNamedVariableNodes(const std::string& name, const std::function<void(WorkflowGraphNode*)>& lambda);

protected:
    void OnBeginAction() override;

    void OnEndAction() override;

    void OnCreateLink(GraphPin* outputPin, GraphPin* inputPin) override;

    void OnBreakLink(GraphPin* outputPin, GraphPin* inputPin) override;

    void OnNodeMoved(GraphNode* node, int32_t dstPosX, int32_t dstPosY) override;

    void OnDeleteNodes(const std::vector<GraphNode*> nodes) override;

    void OnShowCreateNodeDialog(bool isFirstShow, GraphPin* pin) override;

    void OnShowNodesContextMenu(std::vector<GraphNode*> nodes) override;

    void OnNodesSelected(const std::vector<GraphNode*> nodes) override;

    void OnUndo() override;

    void OnRedo() override;

    void OnCopy() override;

    void OnPaste(int32_t x, int32_t y) override;

    void OnCut() override;

    ExpressionCreateMenuInfo GetGraphCategories(int pinType) override;

    virtual void CreateNewNode(std::string nodeClassName, std::string extra_info, int position_x, int position_y, GraphPin* pin, ExpressionCreateNodeInfo* createInfo=nullptr) override;

    void LinkNodeWithPin(WorkflowNode* newNode, GraphPin* pin);

    void AllocatePinsForNode(WorkflowNode* newNode);


public:
    

    int SearchInputTextCallback(ImGuiInputTextCallbackData* data);

    const WorkflowNodeRegistry::NodeCreator* FindSelectedNodeCreators(const std::vector<const WorkflowNodeRegistry::NodeCreator*>& nodeCreators);
    gbf::logic::UBpGraphBase* GetLogicGraph()
    {
        return m_LogicGraph;
    };
    
public:
    template<typename T>
    static decltype(auto) Cast(T ptr)
    {
        if constexpr (std::is_same_v<std::decay_t<T>, GraphNode*>)
        {
            return static_cast<WorkflowGraphNode*>(ptr);
        }
        else if constexpr (std::is_same_v<std::decay_t<T>, GraphPin*>)
        {
            return static_cast<WorkflowGraphPin*>(ptr);
        }
        else
        {
            // static_assert should never appear in a constexpr if node
            // static_assert(false);
            return nullptr;
        }
    }

protected:
    // std::vector<std::unique_ptr<WorkflowNode>> m_WorkFlowNodes;
    gbf::logic::UBpGraphBase* m_LogicGraph = nullptr;

private:
    WorkflowEditor& m_Editor;

    // for ShowCreateNodeDialog
    std::string m_SearchText;
    int32_t m_SelectedNodeIndex;
    bool m_IsNeedRefreshSelectedNode;
};
}   // namespace cross