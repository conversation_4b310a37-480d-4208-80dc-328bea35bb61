#include "crude/runtime/crude/crude_pin.h"
#include "crude/runtime/crude/crude_node.h"
#include "crude/runtime/crude/crude_blueprint.h"

namespace crude_blueprint {

//
// -------[ Pin ]-------
//

Pin::Pin(Node* node, PinType type, std::string_view name)
    : m_Id(node ? node->m_Blueprint->MakePinId(this) : 0), m_Node(node), m_Type(type), m_Name(name) {}

Pin::~Pin() {
  if (m_Node) m_Node->m_Blueprint->ForgetPin(this);
}

PinType Pin::GetType() const { return m_Type; }

LinkQueryResult Pin::CanLinkTo(const Pin& pin) const {
  auto result = m_Node->AcceptLink(*this, pin);
  if (!result) return result;

  auto result2 = pin.m_Node->AcceptLink(*this, pin);
  if (!result2) return result2;

  if (result.Reason().empty()) return result2;

  return result;
}

bool Pin::LinkTo(const Pin& pin) {
  if (!CanLinkTo(pin)) return false;

  if (m_Link) Unlink();

  m_Link = &pin;

  m_Node->WasLinked(*this, pin);
  pin.m_Node->WasLinked(*this, pin);

  return true;
}

void Pin::Unlink() {
  if (m_Link == nullptr) return;

  auto link = m_Link;

  m_Link = nullptr;

  m_Node->WasUnlinked(*this, *link);
  link->m_Node->WasUnlinked(*this, *link);
}

bool Pin::IsLinked() const { return m_Link != nullptr; }

const Pin* Pin::GetLink() const { return m_Link; }

bool Pin::IsInput() const {
  for (auto pin : m_Node->GetInputPins())
    if (pin->m_Id == m_Id) return true;

  return false;
}

bool Pin::IsOutput() const {
  for (auto pin : m_Node->GetOutputPins())
    if (pin->m_Id == m_Id) return true;

  return false;
}

bool Pin::IsProvider() const {
  auto outputToInput = (GetValueType() != PinType::Flow);

  auto pins = outputToInput ? m_Node->GetOutputPins() : m_Node->GetInputPins();

  for (auto pin : pins)
    if (pin->m_Id == m_Id) return true;

  return false;
}

bool Pin::IsReceiver() const {
  auto outputToInput = (GetValueType() != PinType::Flow);

  auto pins = outputToInput ? m_Node->GetInputPins() : m_Node->GetOutputPins();

  for (auto pin : pins)
    if (pin->m_Id == m_Id) return true;

  return false;
}

bool Pin::Load(const crude_json::value& value) {
  if (!detail::GetTo<crude_json::number>(value, "id", m_Id))  // required
    return false;

  uint32_t linkId;
  if (detail::GetTo<crude_json::number>(value, "link", linkId))  // optional
  {
    static_assert(sizeof(linkId) <= sizeof(void*), "Pin ID is expected to fit into the pointer.");
    // HACK: We store raw ID here, Blueprint::Load will expand it to valid pointer.
    m_Link = reinterpret_cast<Pin*>(static_cast<uintptr_t>(linkId));
  }

  return true;
}

void Pin::Save(crude_json::value& value) const {
  value["id"] = crude_json::number(m_Id);                    // required
  if (!m_Name.empty()) value["name"] = std::string(m_Name);  // optional, to make data readable for humans
  if (m_Link) value["link"] = crude_json::number(m_Link->m_Id);
}

PinType Pin::GetValueType() const { return m_Type; }

PinValue Pin::GetValue() const { return PinValue{}; }

//
// -------[ Pins Serialization ]-------
//

bool AnyPin::SetValueType(PinType type) {
  if (GetValueType() == type) return true;

  if (m_InnerPin) {
    m_Node->m_Blueprint->ForgetPin(m_InnerPin.get());
    m_InnerPin.reset();
  }

  if (type == PinType::Any) return true;

  m_InnerPin = m_Node->CreatePin(type);

  if (auto link = GetLink()) {
    if (link->GetValueType() != type) {
      Unlink();
      LinkTo(*link);
    }
  }

  auto linkedToSet = m_Node->m_Blueprint->FindPinsLinkedTo(*this);
  for (auto linkedTo : linkedToSet) {
    if (linkedTo->GetValueType() == type) continue;

    linkedTo->Unlink();
    linkedTo->LinkTo(*this);
  }

  return true;
}

bool AnyPin::SetValue(PinValue value) {
  if (!m_InnerPin) return false;

  return m_InnerPin->SetValue(std::move(value));
}

bool AnyPin::Load(const crude_json::value& value) {
  if (!Pin::Load(value)) return false;

  PinType type = PinType::Any;
  if (!detail::GetTo<double>(value, "type", type)) return false;

  if (type != PinType::Any) {
    m_InnerPin = m_Node->CreatePin(type);

    if (!value.contains("inner")) return false;

    if (!m_InnerPin->Load(value["inner"])) return false;
  }

  return true;
}

void AnyPin::Save(crude_json::value& value) const {
  Pin::Save(value);

  value["type"] = static_cast<double>(GetValueType());
  if (m_InnerPin) m_InnerPin->Save(value["inner"]);
}

bool BoolPin::Load(const crude_json::value& value) {
  if (!Pin::Load(value)) return false;

  if (!detail::GetTo<bool>(value, "value", m_Value))  // required
    return false;

  return true;
}

void BoolPin::Save(crude_json::value& value) const {
  Pin::Save(value);

  value["value"] = m_Value;  // required
}

bool Int32Pin::Load(const crude_json::value& value) {
  if (!Pin::Load(value)) return false;

  if (!detail::GetTo<crude_json::number>(value, "value", m_Value))  // required
    return false;

  return true;
}

void Int32Pin::Save(crude_json::value& value) const {
  Pin::Save(value);

  value["value"] = crude_json::number(m_Value);  // required
}

bool FloatPin::Load(const crude_json::value& value) {
  if (!Pin::Load(value)) return false;

  if (!detail::GetTo<crude_json::number>(value, "value", m_Value))  // required
    return false;

  return true;
}

void FloatPin::Save(crude_json::value& value) const {
  Pin::Save(value);

  value["value"] = crude_json::number(m_Value);  // required
}

void StringPin::Save(crude_json::value& value) const {
  Pin::Save(value);

  value["value"] = m_Value;  // required
}

bool StringPin::Load(const crude_json::value& value) {
  if (!Pin::Load(value)) return false;

  if (!detail::GetTo<crude_json::string>(value, "value", m_Value))  // required
    return false;

  return true;
}

}

