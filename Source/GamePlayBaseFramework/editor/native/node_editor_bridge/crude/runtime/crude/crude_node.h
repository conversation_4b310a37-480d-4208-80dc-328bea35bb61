#pragma once

#include "crude/runtime/crude/crude_utils.h"
#include "external/nonstd/span.hpp"
#include "crude/runtime/crude/crude_pin.h"
#include "crude/runtime/crude/crude_node_type_info.h"
#include "crude/runtime/crude/crude_link_query_result.h"


namespace crude_blueprint {
// Base for all nodes
//
// Node is a function with can have multiple entry points and multiple exit points.
// Execution of the node can behave differently depending of entry point passed to Execute(),
// also may lead to different exit point depending on node internal state.
//
// Execution to work required node to have one or more Flow pins. Otherwise it cannot be linked with
// other nodes. Implementation of execution logic is optional since node may not have any Flow
// pin. In such case node can do its thing in EvaluatePin() function and do something more
// than simply return value held by a pin.
//
// Reset(), Execute() and EvaluatePin() operate on provided execution context. All input
// pins should be evaluated using `context.GetPinValue<>(pin)` instead of accessed directly
// by calling `pin.GetValue()`. Later is valid only for output pins, former is valid
// for input pins.
// Not sticking to these rules will lead to bugs. One node can be executed in multiple context
// exactly like a class method can be run on different instances on the object in C++. Blueprint
// is a like a class not an instance. Using `pin.GetValue()` on input pins is similar to
// having static members in class.
//
// Output pins values can be updated only in Execute() and Reset() by
// `context.SetPinValue(pin, value)`. Other nodes will read it using `context.GetPinValue<>(pin)`
// call.
//
// EvaluatePin() wlays calculate value each time it is called. Execution context nor node itself
// is changed in the process. This is like calling method marked as a `const` in C++.
//
// Nodes can create or destroy pins at will. It is advised to not do that while executing
// a blueprint. That would be analogous to self-modifying code (which is neat trick, but
// a hell to debug).
//
struct Node {
  Node(Blueprint& blueprint);
  virtual ~Node() = default;

  template <typename T>
  std::unique_ptr<T> CreatePin(std::string_view name = "");
  std::unique_ptr<Pin> CreatePin(PinType pinType, std::string_view name = "");

  virtual void Reset(Context& context)  // Reset state of the node before execution. Allows to set initial state for the specified execution context.
  {}

  virtual FlowPin Execute(
      Context& context,
      FlowPin& entryPoint)  // Executes node logic from specified entry point. Returns exit point (flow pin on output side) or nothing.
  {
    return {};
  }

  virtual PinValue EvaluatePin(const Context& context, const Pin& pin) const {
    return pin.GetValue();
  }

  virtual NodeTypeInfo GetTypeInfo() const { return {}; }

  virtual std::string_view GetName() const;

  virtual LinkQueryResult AcceptLink(const Pin& receiver, const Pin& provider)
      const;  // Checks if node accept link between these two pins. There node can filter out unsupported link types.
  virtual void WasLinked(const Pin& receiver, const Pin& provider);    // Notifies node that link involving one of its pins has been made.
  virtual void WasUnlinked(const Pin& receiver, const Pin& provider);  // Notifies node that link involving one of its pins has been broken.

  virtual nonstd::span<Pin*> GetInputPins() { return {}; }   // Returns list of input pins of the node
  virtual nonstd::span<Pin*> GetOutputPins() { return {}; }  // Returns list of output pins of the node

  virtual bool Load(const crude_json::value& value);
  virtual void Save(crude_json::value& value) const;

  uint32_t m_Id;
  Blueprint* m_Blueprint;

 protected:
};

template <typename T>
inline std::unique_ptr<T> crude_blueprint::Node::CreatePin(std::string_view name /*= ""*/) {
  if (auto pin = CreatePin(T::TypeId, name))
    return unique_ptr<T>(static_cast<T*>(pin.release()));
  else
    return nullptr;
}

}

