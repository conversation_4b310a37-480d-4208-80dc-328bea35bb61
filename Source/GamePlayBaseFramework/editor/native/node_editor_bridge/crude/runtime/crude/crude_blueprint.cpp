#include "crude/runtime/crude/crude_blueprint.h"

#include <stdio.h>
#include <algorithm>
#include <map>

#include "crude/runtime/crude/crude_nodes_implement.h"
#include "crude/runtime/crude/crude_node.h"
#include "crude/runtime/crude/crude_node_registry.h"
////#include "crude_json.h"



namespace crude_blueprint {

//
// -------[ Blueprint ]-------
//

Blueprint::Blueprint(std::shared_ptr<NodeRegistry> nodeRegistry) : m_NodeRegistry(std::move(nodeRegistry)) {
  if (!m_NodeRegistry) m_NodeRegistry = std::make_shared<NodeRegistry>();
}

Blueprint::Blueprint(const Blueprint& other) : m_NodeRegistry(other.m_NodeRegistry), m_Context(other.m_Context) {
  crude_json::value value;
  other.Save(value);
  Load(value);
}

Blueprint::Blueprint(Blueprint&& other)
    : m_NodeRegistry(std::move(other.m_NodeRegistry)),
      m_Generator(std::move(other.m_Generator)),
      m_Nodes(std::move(other.m_Nodes)),
      m_Pins(std::move(other.m_Pins)),
      m_Context(std::move(other.m_Context)) {
  for (auto& node : m_Nodes) node->m_Blueprint = this;
}

Blueprint::~Blueprint() { Clear(); }

Blueprint& Blueprint::operator=(const Blueprint& other) {
  if (this == &other) return *this;

  Clear();

  m_NodeRegistry = other.m_NodeRegistry;
  m_Context = other.m_Context;

  crude_json::value value;
  other.Save(value);
  Load(value);

  return *this;
}

Blueprint& Blueprint::operator=(Blueprint&& other) {
  if (this == &other) return *this;

  m_NodeRegistry = std::move(other.m_NodeRegistry);
  m_Generator = std::move(other.m_Generator);
  m_Nodes = std::move(other.m_Nodes);
  m_Pins = std::move(other.m_Pins);
  m_Context = std::move(other.m_Context);

  for (auto& node : m_Nodes) node->m_Blueprint = this;

  return *this;
}


Node* Blueprint::CreateNode(std::string_view nodeTypeName) {
  if (!m_NodeRegistry) return nullptr;

  auto node = m_NodeRegistry->Create(nodeTypeName, *this);
  if (!node) return nullptr;

  m_Nodes.push_back(node);

  return node;
}

void Blueprint::DeleteNode(Node* node) {
  auto nodeIt = std::find(m_Nodes.begin(), m_Nodes.end(), node);
  if (nodeIt == m_Nodes.end()) return;

  delete *nodeIt;

  m_Nodes.erase(nodeIt);
}

void Blueprint::ForgetPin(Pin* pin) {
  auto pinIt = std::find(m_Pins.begin(), m_Pins.end(), pin);
  if (pinIt == m_Pins.end()) return;

  m_Pins.erase(pinIt);
}

void Blueprint::Clear() {
  m_Context.Stop();

  for (auto node : m_Nodes) delete node;

  m_Nodes.resize(0);
  m_Pins.resize(0);
  m_Generator = IdGenerator();
  m_Context = Context();
}

nonstd::span<Node*> Blueprint::GetNodes() { return m_Nodes; }

nonstd::span<const Node* const> Blueprint::GetNodes() const {
  const Node* const* begin = m_Nodes.data();
  const Node* const* end = m_Nodes.data() + m_Nodes.size();
  return nonstd::make_span(begin, end);
}

nonstd::span<Pin*> Blueprint::GetPins() { return m_Pins; }

nonstd::span<const Pin* const> Blueprint::GetPins() const {
  const Pin* const* begin = m_Pins.data();
  const Pin* const* end = m_Pins.data() + m_Pins.size();
  return nonstd::make_span(begin, end);
}

Node* Blueprint::FindNode(uint32_t nodeId) { return const_cast<Node*>(const_cast<const Blueprint*>(this)->FindNode(nodeId)); }

const Node* Blueprint::FindNode(uint32_t nodeId) const {
  for (auto& node : m_Nodes) {
    if (node->m_Id == nodeId) return node;
  }

  return nullptr;
}

Pin* Blueprint::FindPin(uint32_t pinId) { return const_cast<Pin*>(const_cast<const Blueprint*>(this)->FindPin(pinId)); }

const Pin* Blueprint::FindPin(uint32_t pinId) const {
  for (auto& pin : m_Pins) {
    if (pin->m_Id == pinId) return pin;
  }

  return nullptr;
}

std::shared_ptr<NodeRegistry> Blueprint::GetNodeRegistry() const { return m_NodeRegistry; }

// Context& Blueprint::GetContext()
//{
//     return m_Context;
// }

const Context& Blueprint::GetContext() const { return m_Context; }

void Blueprint::SetContextMonitor(ContextMonitor* monitor) { m_Context.SetContextMonitor(monitor); }

ContextMonitor* Blueprint::GetContextMonitor() { return m_Context.GetContextMonitor(); }

const ContextMonitor* Blueprint::GetContextMonitor() const { return m_Context.GetContextMonitor(); }

void Blueprint::Start(EntryPointNode& entryPointNode) {
  auto nodeIt = std::find(m_Nodes.begin(), m_Nodes.end(), static_cast<Node*>(&entryPointNode));
  if (nodeIt == m_Nodes.end()) return;

  ResetState();

  m_Context.Start(entryPointNode.m_Exit);
}

StepResult Blueprint::Step() { return m_Context.Step(); }

void Blueprint::Stop() { m_Context.Stop(); }

StepResult Blueprint::Execute(EntryPointNode& entryPointNode) {
  auto nodeIt = std::find(m_Nodes.begin(), m_Nodes.end(), static_cast<Node*>(&entryPointNode));
  if (nodeIt == m_Nodes.end()) return StepResult::Error;

  ResetState();

  return m_Context.Execute(entryPointNode.m_Exit);
}

Node* Blueprint::CurrentNode() { return m_Context.CurrentNode(); }

const Node* Blueprint::CurrentNode() const { return m_Context.CurrentNode(); }

Node* Blueprint::NextNode() { return m_Context.NextNode(); }

const Node* Blueprint::NextNode() const { return m_Context.NextNode(); }

FlowPin Blueprint::CurrentFlowPin() const { return m_Context.CurrentFlowPin(); }

StepResult Blueprint::LastStepResult() const { return m_Context.LastStepResult(); }

uint32_t Blueprint::StepCount() const { return m_Context.StepCount(); }

bool Blueprint::Load(const crude_json::value& value) {
  if (!value.is_object()) return false;

  const crude_json::array* nodeArray = nullptr;
  if (!detail::GetPtrTo(value, "nodes", nodeArray))  // required
    return false;

  Blueprint blueprint{m_NodeRegistry};

  IdGenerator generator;
  std::map<uint32_t, Pin*> pinMap;

  for (auto& nodeValue : *nodeArray) {
    std::string typeName;
    if (!detail::GetTo<crude_json::string>(nodeValue, "type_name", typeName))  // required
      return false;

    auto node = m_NodeRegistry->Create(typeName, blueprint);
    if (!node) return false;

    blueprint.m_Nodes.push_back(node);

    if (!node->Load(nodeValue)) return false;

    // Collect pins for m_Link resolver
    for (auto pin : node->GetInputPins()) pinMap[pin->m_Id] = pin;
    for (auto pin : node->GetOutputPins()) pinMap[pin->m_Id] = pin;
  }

  const crude_json::object* stateObject = nullptr;
  if (!detail::GetPtrTo(value, "state", stateObject))  // required
    return false;

  uint32_t generatorState = 0;
  if (!detail::GetTo<crude_json::number>(*stateObject, "generator_state", generatorState))  // required
    return false;

  // HACK: Pin::Load store pin ID in m_Link. Let's resolve ids to valid pointers.
  for (auto& entry : pinMap) {
    auto& pin = *entry.second;
    if (pin.m_Link == nullptr) continue;

    auto linkedPinId = static_cast<uint32_t>(reinterpret_cast<uintptr_t>(pin.m_Link));
    auto linketPinIt = pinMap.find(linkedPinId);
    if (linketPinIt == pinMap.end()) return false;

    pin.m_Link = linketPinIt->second;
  }

  for (auto& node : blueprint.m_Nodes) node->m_Blueprint = this;

  Clear();

  m_Generator.SetState(generatorState);

  m_Nodes.swap(blueprint.m_Nodes);
  m_Pins.swap(blueprint.m_Pins);

  return true;
}

void Blueprint::Save(crude_json::value& value) const {
  auto& nodesValue = value["nodes"];  // required
  nodesValue = crude_json::array();
  for (auto& node : m_Nodes) {
    crude_json::value nodeValue;

    nodeValue["type_name"] = std::string(node->GetTypeInfo().m_Name);     //

    node->Save(nodeValue);

    nodesValue.push_back(nodeValue);
  }

  auto& stateValue = value["state"];                                        // required
  stateValue["generator_state"] = crude_json::number(m_Generator.State());  // required
}

bool Blueprint::Load(std::string_view path) {
  auto value = crude_json::value::load(std::string(path));
  if (!value.second) return false;

  return Load(value.first);
}

bool Blueprint::Save(std::string_view path) const {
  crude_json::value value;
  Save(value);

  return value.save(std::string(path), 4);
}

uint32_t Blueprint::MakeNodeId(Node* node) {
  (void)node;
  return m_Generator.GenerateId();
}

uint32_t Blueprint::MakePinId(Pin* pin) {
  m_Pins.push_back(pin);

  return m_Generator.GenerateId();
}

bool Blueprint::HasPinAnyLink(const Pin& pin) const {
  if (pin.IsLinked()) return true;

  for (auto& p : m_Pins) {
    auto linkedPin = p->GetLink();

    if (linkedPin && linkedPin->m_Id == pin.m_Id) return true;
  }

  return false;
}

std::vector<Pin*> Blueprint::FindPinsLinkedTo(const Pin& pin) const {
  std::vector<Pin*> result;

  for (auto& p : m_Pins) {
    auto linkedPin = p->GetLink();

    if (linkedPin && linkedPin->m_Id == pin.m_Id) result.push_back(p);
  }

  return result;
}

void Blueprint::ResetState() {
  m_Context.ResetState();

  for (auto node : m_Nodes) node->Reset(m_Context);
}

}  // namespace crude_blueprint
