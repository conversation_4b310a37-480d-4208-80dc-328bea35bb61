#pragma once
#include "EditorImGuiContext.h"
namespace cross {
class MaterialExpression;
struct NODE_EDITOR_BRIDGE_API CEMeta(Cli) TempStringVec
{
    CEMeta(Cli)
    std::vector<std::string> stringVec;
};

struct NODE_EDITOR_BRIDGE_API CEMeta(Cli) ExpressionCreateNodeInfo
{
    CEMeta(Cli)
    ExpressionCreateNodeInfo()
    {
        menuName = "";
        className = "";
        materialFunctionGuid = "";
        createFunction = nullptr;
    }

    CEMeta(Cli)
        ExpressionCreateNodeInfo(const ExpressionCreateNodeInfo & expressionCreateNodeInfo) {
            menuName = expressionCreateNodeInfo.menuName;
            className = expressionCreateNodeInfo.className;
            materialFunctionGuid = expressionCreateNodeInfo.materialFunctionGuid;
            createFunction = expressionCreateNodeInfo.createFunction;
    }

    CEMeta(Cli)
    std::string menuName;

    CEMeta(Cli)
    std::string className;

    CEMeta(Cli)
    std::string materialFunctionGuid;

    std::function<std::shared_ptr<MaterialExpression>()> createFunction;
};

struct NODE_EDITOR_BRIDGE_API CEMeta(Cli) ExpressionCreateGroupInfo
{
    CEMeta(Cli)
    ExpressionCreateGroupInfo()
    {
            categoryName = "";
            ExpressionInfos = {};
    }

    CEMeta(Cli)
    ExpressionCreateGroupInfo(const ExpressionCreateGroupInfo& expressionCreateGroupInfo)
    {
            categoryName = expressionCreateGroupInfo.categoryName;
            ExpressionInfos = expressionCreateGroupInfo.ExpressionInfos;
    }

    CEMeta(Cli)
    std::string categoryName;

    CEMeta(Cli)
    std::vector<ExpressionCreateNodeInfo> ExpressionInfos;
};

struct NODE_EDITOR_BRIDGE_API CEMeta(Cli) ExpressionCreateMenuInfo
{
    CEMeta(Cli)
    ExpressionCreateMenuInfo()
    {
            Groups = {};
    }

    CEMeta(Cli)
    ExpressionCreateMenuInfo(const ExpressionCreateMenuInfo& expressionCreateMenuInfo)
    {
            Groups = expressionCreateMenuInfo.Groups;
    }

    CEMeta(Cli)
    std::vector<ExpressionCreateGroupInfo> Groups;

    CEMeta(Cli)
    uint64_t PinHandle;
};

struct NODE_EDITOR_BRIDGE_API CEMeta(Cli, IsOverride) GraphCallback : EditorImGuiCallback
{
    CEMeta(Cli, IsOverride)
    virtual void OnCreateMenuAtPosition(int position_x, int position_y, ExpressionCreateMenuInfo menuCategories) {}
};
}// namespace cross