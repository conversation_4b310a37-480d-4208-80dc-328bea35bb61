#include "material_editor.h"

#include "material_expression_comment.h"
#include "MaterialBP/material_expression_add.h"
#include "MaterialBP/material_expression_constant.h"
#include "MaterialBP/material_expression_constant2vector.h"
#include "MaterialBP/material_expression_constant3vector.h"
#include "MaterialBP/material_expression_constant4vector.h"
#include "MaterialBP/material_expression_parameter.h"
#include "MaterialBP/material_expression_surface_shader.h"
#include "MaterialBP/material_expression_sine.h"
#include "MaterialBP/material_expression_custom.h"
#include "MaterialBP/material_expression_function_output.h"
#include "MaterialBP/material_expression_function_call.h"
#include "MaterialBP/material_expression_scalar_parameter.h"
#include "MaterialBP/material_expression_vector_parameter.h"
#include "MaterialBP/material_expression_texture_parameter.h"
#include "MaterialBP/material_expression_shader_const_bool.h"
#include "MaterialBP/material_expression_gpuscene_data.h"
#include "MaterialBP/material_expression_texture_sample_parameter.h"
#include "material_expression_material_parameter_collection.h"
#include "material_expression_named_reroute.h"
#include "crude/runtime/material/material_preview.h"

#include "AssetPipeline/Import/AssetImporterManager.h"
#include "AssetPipeline/Import/ShaderImporter/ShaderImporter.h"
#include "AssetPipeline/Interface/AssetPipeline.h"
#include "CrossBase/String/StringCodec.h"
#include "Resource/AssetStreaming.h"
#include "Resource/MaterialFunction.h"

#include "reflection/meta/meta_class.hpp"
#include "reflection/meta/user_property.hpp"
#include "editor/imgui_extras.h"
#include "imgui_node_editor_internal.h"
#include "material_expression_texture_sample.h"

extern void node_editor_bridgeRegister();
bool gRegistered = false;

namespace cross {
MaterialEditor::MaterialEditor()
    : EditorImGuiContext()
    , m_MaterialGraph(nullptr)
    , m_TransactionManager(*this, nullptr)
    , m_NeedUpdateExpressionState(false)
{
    RegisterExpressions();
}

MaterialEditor::MaterialEditor(const char* fileName, MaterialEditorCallback* callback)
    : EditorImGuiContext{callback}
    , m_MaterialGraph(new MaterialGraph(*this, callback))
    , m_TransactionManager(*this, callback)
    , m_NeedUpdateExpressionState(false)
{
    namespace ed = ax::NodeEditor;
    ed::Config config;
    m_Editor = ed::CreateEditor(&config);

    ImEx::MostRecentlyUsedList::Install(mGuiContext);

    m_HeaderBackground = EditorUICanvasInterface::Instance().CreateIMGUIImage("EngineResource/MaterialEditor/BlueprintBackground.nda");
    auto size = EditorUICanvasInterface::Instance().GetIMGUIImageSize(m_HeaderBackground);
    m_NodeBuilder = std::make_unique<node_editor::utility::NodeBuilderBlueprint>(m_DefaultFont, m_SubTitleFont, m_HeaderBackground, size.x, size.y);

    RegisterExpressions();

    Open(fileName);
}

MaterialEditor::~MaterialEditor()
{
    namespace ed = ax::NodeEditor;
    if (m_Editor)
    {
        EditorUICanvasInterface::Instance().DestroyIMGUIImage(m_HeaderBackground);
        ed::DestroyEditor(m_Editor);
    }
}

void MaterialEditor::FillImguiConfigNodeEditorConfig(ax::NodeEditor::Config& config)
{
    // if (m_MaterialGraph)m_MaterialGraph->FillImguiNodeEditorConfig(config);
}

void MaterialEditor::OnUpdate(const Float2& offset, const Float2& size)
{
    ImGui::SetCurrentContext(mGuiContext);

    namespace ed = ax::NodeEditor;

    constexpr auto flags =
        ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoMove | ImGuiWindowFlags_NoScrollbar | ImGuiWindowFlags_NoScrollWithMouse | ImGuiWindowFlags_NoSavedSettings | ImGuiWindowFlags_NoBringToFrontOnFocus;

    ed::SetCurrentEditor(m_Editor);

    auto& io = ImGui::GetIO();
    ImGui::SetNextWindowPos(ImVec2(0, 0));
    ImGui::SetNextWindowSize(io.DisplaySize);

    const auto windowBorderSize = ImGui::GetStyle().WindowBorderSize;
    const auto windowRounding = ImGui::GetStyle().WindowRounding;
    ImGui::PushStyleVar(ImGuiStyleVar_WindowBorderSize, 0.0f);
    ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, 0.0f);
    ImGui::Begin("Content", nullptr, flags);
    ImGui::PushStyleVar(ImGuiStyleVar_WindowBorderSize, windowBorderSize);
    ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, windowRounding);

    ed::Begin("###main_editor");
    OnFrame();
    ed::End();

    ImGui::PopStyleVar(2);
    ImGui::End();
    ImGui::PopStyleVar(2);
}

void MaterialEditor::OnFrame()
{
    if (m_MaterialGraph)
    {
        m_MaterialGraph->Tick(m_NodeBuilder.get());
    }

    // UpdateExpressionState in main thread
    if (m_Preview && m_Preview->IsWorldReady())
    {
        if (m_ImportTasks.Empty() && m_NeedUpdateExpressionState)
        {
            m_NeedUpdateExpressionState = false;
            DoUpdateExpressionsState();
        }

        if (!m_ImportTasks.Empty() && m_ImportTasks.Complete())
        {
            m_ImportCompleteFunction();
            m_ImportTasks.Reset();
        }
    }
}

void MaterialEditor::ZoomToSurfaceDataExpression()
{
    ZoomToExpression(m_SurfaceShaderExpression);
}

void MaterialEditor::ZoomToFunctionOutput()
{
    if (!m_FunctionOutputExpressions.empty())
    {
        ZoomToExpression(m_FunctionOutputExpressions[0]);
    }
}

void MaterialEditor::ZoomToExpression(MaterialExpression* expression)
{
    assert(expression);
    auto* editorContext = reinterpret_cast<ed::Detail::EditorContext*>(m_Editor);
    ImVec2 min{static_cast<float>(expression->m_EditorPositionX), static_cast<float>(expression->m_EditorPositionY)};
    ImRect rect{min, min + ImVec2{100, 500}};
    editorContext->NavigateTo(rect, true);
}


std::string MaterialEditor::HLSLCode()
{
    auto inputParams = GetMaterialCompilerInputParameters();
    inputParams.previewExpression = nullptr;
    MaterialCompiler compiler(inputParams);
    CompiledShaderCodes shaderCodes;
    compiler.Translate(shaderCodes);

    switch (mMaterialDefines.Domain)
    {
    case MaterialDomain::Foliage:
    case MaterialDomain::Surface:
    {
        return "---------------------Forward---------------------\n\n" + shaderCodes.ForwardShaderCode.ShaderCode + "\n\n\n---------------------GPass---------------------\n\n" + shaderCodes.GPassShaderCode.ShaderCode +
               "\n\n\n---------------------VSMDepth---------------------\n\n" + shaderCodes.VSMDepthShaderCode.ShaderCode + "\n\n\n---------------------Shadow---------------------\n\n" + shaderCodes.ShadowShaderCode.ShaderCode;
    }
    case MaterialDomain::MeshDecal:
    {
        return "---------------------MeshDecal---------------------\n\n" + shaderCodes.MeshDecalShaderCode.ShaderCode;
    }
    case MaterialDomain::PostProcess:
    {
        return "---------------------PostProcess---------------------\n\n" + shaderCodes.ForwardShaderCode.ShaderCode;
    }

    default:
        Assert(false);
        return fmt::format("Unknown domain: {}", mMaterialDefines.Domain);
    }
}
void MaterialEditor::SetMaterialEditorCallback(MaterialEditorCallback* callback)
{
    mCallback = callback;
    m_MaterialGraph = std::make_unique<MaterialGraph>(*this, callback);
    m_TransactionManager = MaterialTransactionManager{*this, callback};
}

void MaterialEditor::Undo()
{
    m_TransactionManager.Undo();
}

void MaterialEditor::Redo()
{
    m_TransactionManager.Redo();
}

void MaterialEditor::BeginAction()
{
    m_TransactionManager.BeginAction();
}

void MaterialEditor::EndAction()
{
    m_TransactionManager.EndAction();
}

MaterialExpression* MaterialEditor::Action_CreateExpression(const std::string& expressionName, int32_t positionX, int32_t positionY, std::function<std::shared_ptr<MaterialExpression>()> createFunction)
{
    std::shared_ptr<MaterialExpression> newExpression = FindSharedPtrExpression(CreateExpression(expressionName, positionX, positionY, createFunction));

    m_TransactionManager.EmplaceTransaction<MaterialTransactionCreateExpression>(newExpression);

    return newExpression.get();
}

MaterialExpression* MaterialEditor::Action_CreateExpressionFunctionCall(const std::string& materialFunction, int32_t positionX, int32_t positionY)
{
    std::shared_ptr<MaterialExpression> newExpression = FindSharedPtrExpression(CreateExpressionFunctionCall(materialFunction, positionX, positionY));

    m_TransactionManager.EmplaceTransaction<MaterialTransactionCreateExpression>(newExpression);

    return newExpression.get();
}

void MaterialEditor::Action_DeleteExpression(MaterialExpression* expression)
{
    if (dynamic_cast<MaterialExpressionVertexShader*>(expression) || dynamic_cast<MaterialExpressionSurfaceShader*>(expression))
    {
        return;
    }

    auto expressionPtr = FindSharedPtrExpression(expression);

    m_TransactionManager.EmplaceTransaction<MaterialTransactionDeleteExpression>(expressionPtr);

    DeleteExpression(expressionPtr);
}

void MaterialEditor::Action_MoveExpression(MaterialExpression* expression, int32_t targetPositionX, int32_t targetPositionY)
{
    m_TransactionManager.EmplaceTransaction<MaterialTransactionMoveExpression>(expression, targetPositionX, targetPositionY);

    MoveExpression(expression, targetPositionX, targetPositionY);
}

void MaterialEditor::Action_CreateLink(ExpressionOutput* output, ExpressionInput* input)
{
    m_TransactionManager.EmplaceTransaction<MaterialTransactionCreateLink>(output, input);

    CreateLink(output, input);
}

void MaterialEditor::Action_BreakLink(ExpressionInput* input)
{
    m_TransactionManager.EmplaceTransaction<MaterialTransactionBreakLink>(input);

    BreakLink(input);
}

void MaterialEditor::Action_ConvertToDstExpression(const std::string& expressionName, MaterialExpression* srcExpression)
{
    auto* dstExpression = CreateExpression(expressionName, srcExpression->m_EditorPositionX, srcExpression->m_EditorPositionY);

    auto srcExpressionPtr = FindSharedPtrExpression(srcExpression);
    auto dstExpressionPtr = FindSharedPtrExpression(dstExpression);

    ConvertToDstExpression(srcExpressionPtr, dstExpressionPtr);

    m_TransactionManager.EmplaceTransaction<MaterialTransactionConvertToDstExpression>(srcExpressionPtr, dstExpressionPtr);
}

void MaterialEditor::UpdateExpressionAppearance(MaterialExpression* expression)
{
    if (FindSharedPtrExpression(expression))
    {
        if (m_MaterialGraph)
        {
            m_MaterialGraph->UpdateExpression(expression);
        }

        if (m_MaterialGraph)
        {
            m_MaterialGraph->ReconstructLinks();
        }
    }
}

MaterialExpression* MaterialEditor::CreateExpression(std::string expressionName, int32_t positionX, int32_t positionY, std::function<std::shared_ptr<MaterialExpression>()> callback)
{
    namespace ed = ax::NodeEditor;

    auto newExpression = callback ? callback() : MaterialExpressionRegistry::CreateExpression(expressionName);

    newExpression->m_EditorPositionX = positionX;
    newExpression->m_EditorPositionY = positionY;

    if (auto comment = std::dynamic_pointer_cast<MaterialExpressionComment>(newExpression))
    {
        ImGui::SetCurrentContext(mGuiContext);
        ed::SetCurrentEditor(m_Editor);

        auto nNodes = ed::GetSelectedNodes(nullptr, 0);
        if (nNodes)
        {
            std::vector<ed::Detail::NodeId> nodeIDs(nNodes);
            ed::GetSelectedNodes(nodeIDs.data(), nNodes);

            ImVec2 topLeft{FLT_MAX, FLT_MAX};
            ImVec2 bottomRight{-FLT_MAX, -FLT_MAX};
            for (auto& nodeID : nodeIDs)
            {
                auto pos = ed::GetNodePosition(nodeID);
                auto size = ed::GetNodeSize(nodeID);
                topLeft.x = std::min(topLeft.x, pos.x);
                topLeft.y = std::min(topLeft.y, pos.y);
                bottomRight.x = std::max(bottomRight.x, (pos + size).x);
                bottomRight.y = std::max(bottomRight.y, (pos + size).y);
            }

            // extend comment to fully wrap nodes
            topLeft.x -= 15;
            topLeft.y -= 60;

            comment->m_EditorPositionX = topLeft.x;
            comment->m_EditorPositionY = topLeft.y;
            comment->m_Width = bottomRight.x - topLeft.x;
            comment->m_Height = bottomRight.y - topLeft.y;
        }
    }

    newExpression->m_Id = m_IdGenerator.GenerateId();

    AddExpression(newExpression);

    newExpression->OnPropertyChange(this);

    return newExpression.get();
}

MaterialExpression* MaterialEditor::CreateExpressionFunctionCall(std::string materialFunctionGuid, int32_t positionX, int32_t positionY)
{
    auto newExpression = MaterialExpressionRegistry::CreateExpression(gbf::reflection::query_meta_class<MaterialExpressionFunctionCall>()->name());
    newExpression->m_EditorPositionX = positionX;
    newExpression->m_EditorPositionY = positionY;

    // load MaterialFunction Resource
    auto expressionfunctionCall = static_cast<MaterialExpressionFunctionCall*>(newExpression.get());
    expressionfunctionCall->m_MaterialFunction = materialFunctionGuid;
    expressionfunctionCall->m_Name = "Invalid MaterialFunction";
    expressionfunctionCall->RefreshPinsAndEditorData();

    newExpression->m_Id = m_IdGenerator.GenerateId();

    AddExpression(newExpression);

    newExpression->DoHandlePropertyChange(this);

    return newExpression.get();
}

void MaterialEditor::AddExpression(std::shared_ptr<MaterialExpression> expression)
{
    if (std::find(m_Expressions.begin(), m_Expressions.end(), expression) != m_Expressions.end())
    {
        // already exist.
        return;
    }

    TryAllocateNameOrId(expression.get());

    m_Expressions.emplace_back(expression);

    if (m_MaterialGraph)
        m_MaterialGraph->AddExpression(expression.get());

    if (auto* vertexShaderExpression = dynamic_cast<MaterialExpressionVertexShader*>(expression.get()))
    {
        m_VertexShaderExpression = vertexShaderExpression;
    }

    if (auto* surfaceShaderExpression = dynamic_cast<MaterialExpressionSurfaceShader*>(expression.get()))
    {
        m_SurfaceShaderExpression = surfaceShaderExpression;
    }

    if (auto* expressionParameter = dynamic_cast<MaterialExpressionParameter*>(expression.get()))
    {
        m_ParameterAndShaderConstSet.AddParameter(expressionParameter);
    }

    if (auto* expressionTextureObject = dynamic_cast<MaterialExpressionTextureObject*>(expression.get()))
    {
        m_ParameterAndShaderConstSet.AddTextureObject(expressionTextureObject);
    }

    if (auto* functionOutput = dynamic_cast<MaterialExpressionFunctionOutput*>(expression.get()))
    {
        m_FunctionOutputExpressions.push_back(functionOutput);
    }

    if (auto* namedRerouteDeclaration = dynamic_cast<MaterialExpressionNamedRerouteDeclaration*>(expression.get()))
    {
        m_NamedRerouteIdGenerator.m_NamedRerouteExpressions.push_back(expression.get());
    }

    SetResourceChanged(true);
}

void MaterialEditor::DeleteExpression(std::shared_ptr<MaterialExpression> expression)
{
    // update ExpressionsState before m_PreviewExpression set to nullptr
    UpdateExpressionsState(expression.get());
    if (auto* expressionFunctionCall = dynamic_cast<MaterialExpressionFunctionCall*>(expression.get()))
    {
        expressionFunctionCall->RemoveFunctionParameters(this);
    }

    if (auto* expressionParameter = dynamic_cast<MaterialExpressionParameter*>(expression.get()))
    {
        m_ParameterAndShaderConstSet.RemoveParameter(expressionParameter);
    }

    if (auto* expressionTextureObject = dynamic_cast<MaterialExpressionTextureObject*>(expression.get()))
    {
        m_ParameterAndShaderConstSet.RemoveTextureObject(expressionTextureObject);
    }

    if (auto* functionOutput = dynamic_cast<MaterialExpressionFunctionOutput*>(expression.get()))
    {
        auto iter = std::find(m_FunctionOutputExpressions.begin(), m_FunctionOutputExpressions.end(), expression.get());
        assert(iter != m_FunctionOutputExpressions.end());
        m_FunctionOutputExpressions.erase(iter);
        m_FunctionInputOutputIdGenerator.RemoveId(functionOutput->m_FuncInoutId);
    }

    if (auto* functionInput = dynamic_cast<MaterialExpressionFunctionInput*>(expression.get()))
    {
        m_FunctionInputOutputIdGenerator.RemoveId(functionInput->m_FuncInoutId);
    }

    if (dynamic_cast<MaterialExpressionNamedRerouteDeclaration*>(expression.get()))
    {
        m_NamedRerouteIdGenerator.DeleteDeclaration(expression.get());
    }

    for (auto dstExpression : m_Expressions)
    {
        for (auto* inputPin : dstExpression->GetInputPins())
        {
            if (inputPin->m_LinkedExpressionOutput && inputPin->m_LinkedExpressionOutput->m_ParentExpression == expression.get())
            {
                inputPin->m_LinkedExpressionOutput = nullptr;
            }
        }
    }

    if (m_MaterialGraph)
    {
        m_MaterialGraph->DeleteExpression(expression.get());
        m_MaterialGraph->ReconstructLinks();
    }

    if (m_PreviewExpression == expression.get())
    {
        m_PreviewExpression = nullptr;
    }

    if (auto iter = std::find(m_Expressions.begin(), m_Expressions.end(), expression); iter != m_Expressions.end())
    {
        m_Expressions.erase(iter);
    }

    SetResourceChanged(true);
}

void MaterialEditor::MoveExpression(MaterialExpression* expression, int32_t targetPositionX, int32_t targetPositionY)
{
    expression->m_EditorPositionX = targetPositionX;
    expression->m_EditorPositionY = targetPositionY;

    if (m_MaterialGraph)
        m_MaterialGraph->UpdateExpressionPosition(expression);

    SetResourceChanged(true);
}

void MaterialEditor::CreateLink(ExpressionOutput* output, ExpressionInput* input)
{
    if (!IsExpressionPinValid(output) || !IsExpressionPinValid(input))
    {
        return;
    }

    input->m_LinkedExpressionOutput = output;

    if (m_MaterialGraph)
        m_MaterialGraph->CreateLink(output, input);

    UpdateExpressionsState(GetParentExpression(input));

    SetResourceChanged(true);
}

void MaterialEditor::BreakLink(ExpressionInput* input)
{
    if (!IsExpressionPinValid(input))
    {
        return;
    }

    auto pre_output = input->m_LinkedExpressionOutput;
    input->m_LinkedExpressionOutput = nullptr;

    if (m_MaterialGraph)
        m_MaterialGraph->BreakLink(pre_output, input);

    UpdateExpressionsState(GetParentExpression(input));

    SetResourceChanged(true);
}

void MaterialEditor::ConvertToDstExpression(std::shared_ptr<MaterialExpression> srcExpression, std::shared_ptr<MaterialExpression> dstExpression)
{
    AddExpression(dstExpression);

    UInt32 srcExpressionInputPinCount = srcExpression->GetInputPins().size();
    UInt32 srcExpressionOutputPinCount = srcExpression->GetOutputPins().size();
    UInt32 dstExpressionInputPinCount = dstExpression->GetInputPins().size();
    UInt32 dstExpressionOutputPinCount = dstExpression->GetOutputPins().size();

    for (UInt32 i = 0; i < srcExpressionInputPinCount && i < dstExpressionInputPinCount; i++)
    {
        auto* srcInputPin = srcExpression->GetInputPins()[i];
        auto* dstInputPin = dstExpression->GetInputPins()[i];

        dstInputPin->m_LinkedExpressionOutput = srcInputPin->m_LinkedExpressionOutput;
    }

    for (UInt32 i = 0; i < srcExpressionOutputPinCount && i < dstExpressionOutputPinCount; i++)
    {
        auto* srcOutputPin = srcExpression->GetOutputPins()[i];
        auto* dstOutputPin = dstExpression->GetOutputPins()[i];

        for (auto* inputPin : GetLinkedInputPins(srcOutputPin))
        {
            inputPin->m_LinkedExpressionOutput = dstOutputPin;
        }
    }

    // Fucking ugly...
    {
        if (auto* dstExpressionScalar = dynamic_cast<MaterialExpressionScalarParameter*>(dstExpression.get()))
        {
            Assert(dynamic_cast<MaterialExpressionConstant*>(srcExpression.get()));
            dstExpressionScalar->m_DefaultValue = static_cast<MaterialExpressionConstant*>(srcExpression.get())->m_Const;
        }
        else if (auto* dstExpressionVector = dynamic_cast<MaterialExpressionVectorParameter*>(dstExpression.get()))
        {
            if (auto* srcExpressionConst2 = dynamic_cast<MaterialExpressionConstant2Vector*>(srcExpression.get()))
            {
                dstExpressionVector->m_DefaultValue.x = srcExpressionConst2->m_X;
                dstExpressionVector->m_DefaultValue.y = srcExpressionConst2->m_Y;
            }
            else if (auto* srcExpressionConst3 = dynamic_cast<MaterialExpressionConstant3Vector*>(srcExpression.get()))
            {
                dstExpressionVector->m_DefaultValue.x = srcExpressionConst3->m_Value.x;
                dstExpressionVector->m_DefaultValue.y = srcExpressionConst3->m_Value.y;
                dstExpressionVector->m_DefaultValue.z = srcExpressionConst3->m_Value.z;
            }
            else if (auto* srcExpressionConst4 = dynamic_cast<MaterialExpressionConstant4Vector*>(srcExpression.get()))
            {
                dstExpressionVector->m_DefaultValue.x = srcExpressionConst4->m_Value.x;
                dstExpressionVector->m_DefaultValue.y = srcExpressionConst4->m_Value.y;
                dstExpressionVector->m_DefaultValue.z = srcExpressionConst4->m_Value.z;
                dstExpressionVector->m_DefaultValue.w = srcExpressionConst4->m_Value.w;
            }
        }
        else if (auto* dstExpressionConst = dynamic_cast<MaterialExpressionConstant*>(dstExpression.get()))
        {
            Assert(dynamic_cast<MaterialExpressionScalarParameter*>(srcExpression.get()));
            dstExpressionConst->m_Const = static_cast<MaterialExpressionScalarParameter*>(srcExpression.get())->m_DefaultValue;
        }
        else if (auto* dstExpressionConst2 = dynamic_cast<MaterialExpressionConstant2Vector*>(dstExpression.get()))
        {
            auto* srcExpressionVector = static_cast<MaterialExpressionVectorParameter*>(srcExpression.get());
            Assert(srcExpressionVector);

            dstExpressionConst2->m_X = srcExpressionVector->m_DefaultValue.x;
            dstExpressionConst2->m_Y = srcExpressionVector->m_DefaultValue.y;
        }
        else if (auto* dstExpressionConst3 = dynamic_cast<MaterialExpressionConstant3Vector*>(dstExpression.get()))
        {
            auto* srcExpressionVector = static_cast<MaterialExpressionVectorParameter*>(srcExpression.get());
            Assert(srcExpressionVector);

            dstExpressionConst3->m_Value.x = srcExpressionVector->m_DefaultValue.x;
            dstExpressionConst3->m_Value.y = srcExpressionVector->m_DefaultValue.y;
            dstExpressionConst3->m_Value.z = srcExpressionVector->m_DefaultValue.z;
        }
        else if (auto* dstExpressionConst4 = dynamic_cast<MaterialExpressionConstant4Vector*>(dstExpression.get()))
        {
            auto* srcExpressionVector = static_cast<MaterialExpressionVectorParameter*>(srcExpression.get());
            Assert(srcExpressionVector);

            dstExpressionConst4->m_Value.x = srcExpressionVector->m_DefaultValue.x;
            dstExpressionConst4->m_Value.y = srcExpressionVector->m_DefaultValue.y;
            dstExpressionConst4->m_Value.z = srcExpressionVector->m_DefaultValue.z;
            dstExpressionConst4->m_Value.w = srcExpressionVector->m_DefaultValue.w;
        }
        else if (auto* dstExpressionTextureParam = dynamic_cast<MaterialExpressionTextureParameter*>(dstExpression.get()))
        {
            auto* srcExpressionTextureObject = static_cast<MaterialExpressionTextureObject*>(srcExpression.get());
            Assert(srcExpressionTextureObject);

            dstExpressionTextureParam->m_TextureString = srcExpressionTextureObject->m_TextureString;
            dstExpressionTextureParam->OnPropertyChange(this);
        }
        else if (auto* dstExpressionTextureObject = dynamic_cast<MaterialExpressionTextureObject*>(dstExpression.get()))
        {
            auto* srcExpressionTextureParam = static_cast<MaterialExpressionTextureParameter*>(srcExpression.get());
            Assert(srcExpressionTextureParam);

            dstExpressionTextureObject->m_TextureString = srcExpressionTextureParam->m_TextureString;
            dstExpressionTextureObject->OnPropertyChange(this);
        }
    }

    if (auto* dstExpressionParam = dynamic_cast<MaterialExpressionParameter*>(dstExpression.get()))
    {
        m_ParameterAndShaderConstSet.UpdateParameter(dstExpressionParam);
    }

    if (m_MaterialGraph)
        m_MaterialGraph->ReconstructLinks();

    DeleteExpression(srcExpression);

    UpdateExpressionsState();

    SetResourceChanged(true);
}

std::shared_ptr<MaterialExpression> MaterialEditor::FindSharedPtrExpression(MaterialExpression* expression)
{
    for (auto expressionPtr : m_Expressions)
    {
        if (expressionPtr.get() == expression)
        {
            return expressionPtr;
        }
    }

    return nullptr;
}

void MaterialEditor::OpenFx(ResourcePtr resource)
{
    auto* srcFx = static_cast<cross::resource::Fx*>(resource.get());
    m_Fx = gResourceMgr.CreateTempResourceAs<resource::Fx>();
    m_Fx->CopyFrom(*srcFx);

    if (!m_Fx->GetExpressionsString().empty())
    {
        auto expressions = MaterialExpression::DeserializeExpressions(cross::DeserializeNode::ParseFromJson(m_Fx->GetExpressionsString()));

        for (auto expression : expressions)
        {
            expression->m_Id = m_IdGenerator.GenerateId();

            if (auto* funcInput = dynamic_cast<MaterialExpressionFunctionInput*>(expression.get()); funcInput)
            {
                if (funcInput->m_FuncInoutId != 0)
                {
                    m_FunctionInputOutputIdGenerator.AddId(funcInput->m_FuncInoutId);
                }
            }

            AddExpression(expression);
        }

        for (auto expression : expressions)
        {
            expression->DoHandlePropertyChange(this);
        }
    }

    // if current fx don't have SetSurfaceDataExpresison, create one
    if (m_VertexShaderExpression == nullptr)
    {
        CreateExpression(gbf::reflection::query_meta_class<MaterialExpressionVertexShader>()->name(), 100, -200);
    }

    if (m_SurfaceShaderExpression == nullptr)
    {
        CreateExpression(gbf::reflection::query_meta_class<MaterialExpressionSurfaceShader>()->name(), 100, 0);
    }

    mMaterialDefines = m_Fx->GetMaterialDefines();
    Sync(mMaterialDefines, m_Fx);

    OnMaterialDefinesChange();

    SetResourceChanged(false);
}

void MaterialEditor::OpenMaterialFunction(ResourcePtr resource)
{
    auto* srcMaterialFunction = static_cast<cross::resource::MaterialFunction*>(resource.get());
    m_MaterialFunction = gResourceMgr.CreateTempResourceAs<resource::MaterialFunction>();
    m_MaterialFunction->CopyFrom(*srcMaterialFunction);

    auto defaultFx = TypeCast<cross::resource::Fx>(gAssetStreamingManager->LoadSynchronously("PipelineResource/FFSRP/Shader/Material/Lit/FxTemplate.fx.nda"));
    m_Fx = gResourceMgr.CreateResourceAs<cross::resource::Fx>();
    m_Fx->CopyFrom(*defaultFx);

    if (!m_MaterialFunction->GetExpressionsString().empty())
    {
        auto expressions = MaterialExpression::DeserializeExpressions(cross::DeserializeNode::ParseFromJson(m_MaterialFunction->GetExpressionsString()));

        for (auto expression : expressions)
        {
            if (auto* funcInput = dynamic_cast<MaterialExpressionFunctionInput*>(expression.get()); funcInput)
            {
                if (funcInput->m_FuncInoutId != -1)
                {
                    m_FunctionInputOutputIdGenerator.AddId(funcInput->m_FuncInoutId);
                }
            }

            if (auto* funcOutput = dynamic_cast<MaterialExpressionFunctionOutput*>(expression.get()); funcOutput)
            {
                if (funcOutput->m_FuncInoutId != -1)
                {
                    m_FunctionInputOutputIdGenerator.AddId(funcOutput->m_FuncInoutId);
                }
            }
        }

        for (auto expression : expressions)
        {
            expression->m_Id = m_IdGenerator.GenerateId();

            AddExpression(expression);
        }

        for (auto expression : expressions)
        {
            expression->DoHandlePropertyChange(this);
        }
    }

    // if current MaterialFunction don't have FunctionOutputExpression, create one
    if (m_FunctionOutputExpressions.empty())
    {
        CreateExpression(gbf::reflection::query_meta_class<MaterialExpressionFunctionOutput>()->name(), 100, 0);
    }

    m_PreviewExpression = m_FunctionOutputExpressions.back();

    SetResourceChanged(false);
}

std::vector<ExpressionInput*> MaterialEditor::GetLinkedInputPins(ExpressionOutput* output)
{
    std::vector<ExpressionInput*> result;

    for (auto& expressionPtr : m_Expressions)
    {
        for (auto* inputPin : expressionPtr->GetInputPins())
        {
            if (inputPin->m_LinkedExpressionOutput == output)
            {
                result.emplace_back(inputPin);
            }
        }
    }

    return result;
}

void MaterialEditor::SelectExpressions(std::vector<MaterialExpression*> expressions)
{
    m_SelectedExpressions = expressions;

    static_cast<MaterialEditorCallback*>(mCallback)->OnActiveDetailTab();
}

void MaterialEditor::Open(const char* filePath)
{
    // load and copy Resource
    auto srcResource = gAssetStreamingManager->LoadSynchronously(filePath);
    assert(srcResource);

    // save resource guid
    m_ResourceFileGuid = srcResource->GetGuid_Str();

    m_TempShaderFilePath = PathHelper::GetCurrentDirectoryPath() + "\\Intermediate\\MaterialEditor\\" + std::to_string(reinterpret_cast<uint64_t>(this));
    std::filesystem::create_directories(PathHelper::GetCurrentDirectoryPath() + "\\Intermediate\\MaterialEditor");

    if (auto* fx = dynamic_cast<resource::Fx*>(srcResource.get()); fx)
    {
        if (fx->GetVersion() >= 2)
        {
            OpenFx(srcResource);
        }
    }
    else if (dynamic_cast<resource::MaterialFunction*>(srcResource.get()))
    {
        OpenMaterialFunction(srcResource);
    }
    else
    {
        assert(false);
        return;
    }

    if (m_MaterialGraph)
    {
        m_MaterialGraph->ReconstructLinks();
        InitializeNamedReroutes();
    }

    m_TransactionManager.Clear();

    UpdateExpressionsState();
}

cross::SerializeNode MaterialEditor::SerializeExpressions(std::vector<MaterialExpression*> expressions)
{
    cross::SerializeNode expressionsNode;
    for (auto* expression : expressions)
    {
        cross::SerializeNode expressionNode;
        cross::SerializeContext context;
        expression->Serialize(expressionNode, context);
        expressionsNode.PushBack(std::move(expressionNode));
    }

    return expressionsNode;
}

void MaterialEditor::UpdateUITextureAndExpressionTexture(MaterialExpression* expressionTex)
{
    static std::vector<std::unique_ptr<TextureExpressionHandler>> handlers;
    if (handlers.empty())
    {
        handlers.push_back(std::make_unique<TextureObjectHandler>());
        handlers.push_back(std::make_unique<TextureParameterHandler>());
    }

    for (const auto& handler : handlers)
    {
        if (handler->CanHandle(expressionTex))
        {
            handler->UpdateTexture(expressionTex, this);
            break;
        }
    }
}
void MaterialEditor::OpenMaterialFunction(const std::string& materialFunctionGuid)
{
    static_cast<MaterialEditorCallback*>(mCallback)->OnOpenMaterialFunction(materialFunctionGuid.c_str());
}

bool MaterialEditor::Apply(bool force)
{
    if (!force && !IsResourceChanged() || (!m_Fx && !m_MaterialFunction))
    {
        return false;
    }

    bool result;

    if (IsEditFx())
    {
        result = Apply_Fx();
    }
    else
    {
        result = Apply_MaterialFunction();
    }

    SetResourceChanged(false);

    return result;
}

bool MaterialEditor::Compile(bool saveToFile)
{
    using namespace cross;
    using namespace cross::resource;
    using namespace cross::editor;

    auto* shaderImporter = static_cast<GraphicsShaderImporter*>(AssetImporterManager::Instance().GetAssetImporter(cross::editor::AssetType::Shader));

    for (auto& expressionPtr : m_Expressions)
    {
        expressionPtr->m_ErrorMessage.clear();
    }

    // Update ShaderImportSettings
    {
        cross::editor::ShaderImportSettings shaderImportSetting;
        shaderImportSetting.GenAllVariants = true;
        SetShaderImportSettings(&shaderImportSetting);
    }
     
    // Compile
    auto inputParams = GetMaterialCompilerInputParameters();
    if (saveToFile)
    {
        inputParams.previewExpression = nullptr;
    }
    auto compiler = std::make_shared<MaterialCompiler>(inputParams);

    CompiledShaderCodes shaderCodes;

    if (!compiler->Translate(shaderCodes))
    {
        return false;
    }

    mCompilationOutput = compiler->GetCompilationOutput();
    struct ShaderBinaryDatas
    {
        std::vector<UInt8> GPassBinaryData;
        std::vector<UInt8> ForwardPassBinaryData;
        std::vector<UInt8> VSMPassBinaryData;
        std::vector<UInt8> ShadowPassBinaryData;
        std::vector<UInt8> MeshDecalBinaryData;
    };
    auto shaderBinaryDatas = std::make_shared<ShaderBinaryDatas>();

    auto GPassTempShaderFilePath = m_TempShaderFilePath + "GPass";
    auto ForwardPassTempShaderFilePath = m_TempShaderFilePath + "ForwardPass";
    auto VSMPassTempShaderFilePath = m_TempShaderFilePath + "VSMDepth";
    auto ShadowPassTempShaderFilePath = m_TempShaderFilePath + "Shadow";
    auto MeshDecalTempShaderFilePath = m_TempShaderFilePath + "MeshDecal";

    auto importTasks = std::make_shared<cross::threading::TaskEventArray>();

    auto WriteCodeToFile = [](std::string_view code, const std::string& fileName)
    {
        std::ofstream ifs(fileName.data());
        Assert(ifs.is_open());
        ifs << code;
        ifs.close();
    };

    if (shaderCodes.NeedCompileForward)
    {
        WriteCodeToFile(shaderCodes.ForwardShaderCode.ShaderCode, ForwardPassTempShaderFilePath);
        importTasks->Add(shaderImporter->CompileShaderAsync(ForwardPassTempShaderFilePath, shaderBinaryDatas->ForwardPassBinaryData));
    }
    if (shaderCodes.NeedCompileGPass)
    {
        WriteCodeToFile(shaderCodes.GPassShaderCode.ShaderCode, GPassTempShaderFilePath);
        importTasks->Add(shaderImporter->CompileShaderAsync(GPassTempShaderFilePath, shaderBinaryDatas->GPassBinaryData));
    }
    if (shaderCodes.NeedCompileShadow)
    {
        WriteCodeToFile(shaderCodes.ShadowShaderCode.ShaderCode, ShadowPassTempShaderFilePath);
        importTasks->Add(shaderImporter->CompileShaderAsync(ShadowPassTempShaderFilePath, shaderBinaryDatas->ShadowPassBinaryData));
    }
    if (shaderCodes.NeedCompileVSMDepth)
    {
        WriteCodeToFile(shaderCodes.VSMDepthShaderCode.ShaderCode, VSMPassTempShaderFilePath);
        importTasks->Add(shaderImporter->CompileShaderAsync(VSMPassTempShaderFilePath, shaderBinaryDatas->VSMPassBinaryData));
    }
    if (shaderCodes.NeedCompileMeshDecal)
    {
        WriteCodeToFile(shaderCodes.MeshDecalShaderCode.ShaderCode, MeshDecalTempShaderFilePath);
        importTasks->Add(shaderImporter->CompileShaderAsync(MeshDecalTempShaderFilePath, shaderBinaryDatas->MeshDecalBinaryData));
    }

    threading::Dispatch(*importTasks, [=](const auto&) {
        // clear
        EngineGlobal::GetFileSystem()->RemoveFile(GPassTempShaderFilePath);
        EngineGlobal::GetFileSystem()->RemoveFile(GPassTempShaderFilePath + ".nda");

        EngineGlobal::GetFileSystem()->RemoveFile(ForwardPassTempShaderFilePath);
        EngineGlobal::GetFileSystem()->RemoveFile(ForwardPassTempShaderFilePath + ".nda");

        EngineGlobal::GetFileSystem()->RemoveFile(VSMPassTempShaderFilePath);
        EngineGlobal::GetFileSystem()->RemoveFile(VSMPassTempShaderFilePath + ".nda");

        EngineGlobal::GetFileSystem()->RemoveFile(ShadowPassTempShaderFilePath);
        EngineGlobal::GetFileSystem()->RemoveFile(ShadowPassTempShaderFilePath + ".nda");

        EngineGlobal::GetFileSystem()->RemoveFile(MeshDecalTempShaderFilePath);
        EngineGlobal::GetFileSystem()->RemoveFile(MeshDecalTempShaderFilePath + ".nda");
    });

    auto OnComplete = [=]()
    {
        bool isCompileSuccess = true;
        importTasks->Traverse([&isCompileSuccess](const auto& taskEventPtr) {
            if (!taskEventPtr->GetReturnValue<bool>())
            {
                isCompileSuccess = false;
                return true;
            }
            return false;
        });

        if (isCompileSuccess)
        {
            threading::FlushRenderingCommands();

            FxPtr fx;

            if (saveToFile)
            {
                fx = TYPE_CAST(resource::Fx*, gAssetStreamingManager->LoadSynchronously(m_ResourceFileGuid).get());
            }
            else
            {
                fx = m_Fx;
            }

            auto CreateShaderAndSetFx = [=](const std::string& passName, const std::vector<UInt8>& shaderBinaryData, bool needPass) {
                if (needPass)
                {
                    auto shaderPtr = ResourceManager::Instance().CreateTempResourceAs<Shader>();
                    shaderPtr->SetupWithFlatbuffers(shaderBinaryData.data(), shaderBinaryData.size());

                    fx->CreatePass(passName);
                    MaterialExpression::InitPassState(fx.get(), passName);
                    fx->SetShader(passName, shaderPtr);
                    fx->GetPass(passName).mShaderCode = Base64Codec::encode(shaderBinaryData);
                }
                else
                {
                    fx->DeletePass(passName);
                }
            };

            CreateShaderAndSetFx("gpass", shaderBinaryDatas->GPassBinaryData, shaderCodes.NeedCompileGPass);
            CreateShaderAndSetFx("forward", shaderBinaryDatas->ForwardPassBinaryData, shaderCodes.NeedCompileForward);
            CreateShaderAndSetFx("VSMDepth", shaderBinaryDatas->VSMPassBinaryData, shaderCodes.NeedCompileVSMDepth);
            CreateShaderAndSetFx("shadow_all", shaderBinaryDatas->ShadowPassBinaryData, shaderCodes.NeedCompileShadow);
            CreateShaderAndSetFx("decal_gpass", shaderBinaryDatas->MeshDecalBinaryData, shaderCodes.NeedCompileMeshDecal);

            // Update Fx Properties
            m_ParameterAndShaderConstSet.ApplyParametersAndShaderConstsToFx(fx, this);
            ApplyPropertiesToFx(*compiler, fx.get());

            // Update Fx State
            auto& defines = GetMaterialDefines();
            UpdateMaterialState(fx, defines);

            // Serialize Fx
            std::vector<MaterialExpression*> expressions(m_Expressions.size());
            std::transform(m_Expressions.begin(), m_Expressions.end(), expressions.begin(), [](auto& expressionPtr) { return expressionPtr.get(); });
            //for (auto& expression : expressions)
            //{
            //    if (auto* expressionParam = dynamic_cast<MaterialExpressionTextureParameter*>(expression))
            //    {
            //        int index = compiler->FindVtIndex(expressionParam->m_ParameterName);
            //        expressionParam->m_VirtualTextureLayer = (index == -1) ? "" : "_VT_" + std::to_string(index);
            //    }
            //}

            fx->SetVersion(2);
            fx->SetExpressionString(SerializeExpressions(expressions).FormatToJson());
            fx->SetMaterialParameterCollectionUsage(compiler->GetMaterialParameterCollectionUsage());
            fx->NotifyChangeRecursively();

            threading::FlushRenderingCommands();

            return fx;
        }

        return FxPtr{};
    };

    if (saveToFile)
    {
        importTasks->WaitForCompletion();
        if (auto fx = OnComplete(); !fx)
        {
            return false;
        }
        else
        {
            fx->Serialize(SerializeNode(), fx->GetName());
        }
    }
    else
    {
        m_ImportTasks = std::move(*importTasks);
        m_ImportCompleteFunction = OnComplete;
    }
    return true;
}

bool MaterialEditor::Apply_Fx()
{
    Assert(m_Fx);

    for (auto& expression : m_Expressions)
    {
        if (auto* expressionParam = dynamic_cast<MaterialExpressionParameter*>(expression.get()))
        {
            expressionParam->m_IsVisibleInMaterialInstanceEditor = false;
        }

        expression->m_ErrorMessage.clear();
    }

    return Compile(true);
}

bool MaterialEditor::Apply_MaterialFunction()
{
    assert(m_MaterialFunction);

    auto srcMaterialFunctionPtr = gAssetStreamingManager->LoadSynchronously(m_ResourceFileGuid);
    auto* srcMaterialFunction = static_cast<cross::resource::MaterialFunction*>(srcMaterialFunctionPtr.get());

    bool compileSuccess = true;

    if (compileSuccess)
    {
        // Serialize MaterialFunction
        std::vector<MaterialExpression*> expressions(m_Expressions.size());
        std::transform(m_Expressions.begin(), m_Expressions.end(), expressions.begin(), [](auto& expressionPtr) { return expressionPtr.get(); });

        srcMaterialFunction->CopyFrom(*m_MaterialFunction);
        srcMaterialFunction->SetExpressionString(SerializeExpressions(expressions).FormatToJson());
        srcMaterialFunction->Serialize(SerializeNode(), srcMaterialFunction->GetName());
    }

    return compileSuccess;
}

void MaterialEditor::SetPreviewExpression(MaterialExpression* expression)
{
    m_PreviewExpression = expression;

    UpdateExpressionsState();
}

void MaterialEditor::UpdateExpressionsState(MaterialExpression* expressionChanged)
{
    if (!m_NeedUpdateExpressionState)
    {
        if (!expressionChanged)
        {
            m_NeedUpdateExpressionState = true;
        }
        else
        {
            m_NeedUpdateExpressionState = IsMaterialExpressionAffectFinalResult(expressionChanged);
        }
    }
}

bool MaterialEditor::IsMaterialExpressionAffectFinalResult(MaterialExpression* expressionChanged)
{
    // TODO: @jahwang, refactor this function with decent topological sort
    // @copilot: refactor this function with decent topological sort

    // only recompile shader when the changed expression affect the final result
    bool isFindExpression = false;
    bool isReenterExpression = false;

    std::vector<MaterialExpression*> expressions;

    if (IsEditFx())
    {
        expressions.push_back(m_VertexShaderExpression);
        expressions.push_back(m_SurfaceShaderExpression);
    }
    else
    {
        expressions = m_FunctionOutputExpressions;
    }

    if (m_PreviewExpression)
    {
        expressions.push_back(m_PreviewExpression);
    }

    while (!expressions.empty())
    {
        auto* currExpression = expressions.back();
        expressions.pop_back();

        if (currExpression == expressionChanged)
        {
            isFindExpression = true;
            break;
        }

        for (auto* inputPin : currExpression->GetInputPins())
        {
            if (inputPin->m_LinkedExpressionOutput)
            {
                auto* expressionToPush = inputPin->m_LinkedExpressionOutput->m_ParentExpression;
                if (auto iter = std::find(expressions.begin(), expressions.end(), expressionToPush); iter != expressions.end())
                {
                    // reenter the expression, let compiler to handle this error
                    isReenterExpression = true;
                    break;
                }

                expressions.push_back(inputPin->m_LinkedExpressionOutput->m_ParentExpression);
            }
        }

        if (isReenterExpression)
        {
            break;
        }
    }

    return isFindExpression || isReenterExpression;
}

void MaterialEditor::TryAllocateNameOrId(MaterialExpression* expression)
{
    if (auto* expressionParameter = dynamic_cast<MaterialExpressionParameter*>(expression))
    {
        if (expressionParameter->m_ParameterName.empty())
        {
            expressionParameter->m_ParameterName = expressionParameter->m_Name.empty() ? m_ParameterAndShaderConstSet.GenerateName("Param") : expressionParameter->m_Name;
        }
        if (expressionParameter->m_Name.empty())
        {
            expressionParameter->m_Name = expressionParameter->m_ParameterName;
        }
    }

    if (auto* expressionTextureObject = dynamic_cast<MaterialExpressionTextureObject*>(expression))
    {
        if (expressionTextureObject->m_TextureObjectName.empty())
        {
            expressionTextureObject->m_TextureObjectName = m_ParameterAndShaderConstSet.GenerateName("TextureObject");
        }
    }

    if (auto* expressionGPUSceneData = dynamic_cast<MaterialExpressionGPUSceneData*>(expression))
    {
        if (expressionGPUSceneData->m_Name.empty())
        {
            expressionGPUSceneData->m_Name = m_ParameterAndShaderConstSet.GenerateName("GPUSceneData");
        }
    }

    if (auto* functionInput = dynamic_cast<MaterialExpressionFunctionInput*>(expression); functionInput && functionInput->m_FuncInoutId == -1)
    {
        functionInput->m_FuncInoutId = m_FunctionInputOutputIdGenerator.GenerateId();
    }

    if (auto* functionOutput = dynamic_cast<MaterialExpressionFunctionOutput*>(expression); functionOutput && functionOutput->m_FuncInoutId == -1)
    {
        functionOutput->m_FuncInoutId = m_FunctionInputOutputIdGenerator.GenerateId();
    }

    if (auto* namedRerouteDeclaration = dynamic_cast<MaterialExpressionNamedRerouteDeclaration*>(expression); namedRerouteDeclaration && namedRerouteDeclaration->m_VariableGuid == "")
    {
        namedRerouteDeclaration->m_VariableGuid = m_NamedRerouteIdGenerator.GenerateId();
    }
}

void MaterialEditor::CopyExpressionsSelected()
{
    std::vector<MaterialExpression*> filteredExpression;

    for (auto* expression : m_SelectedExpressions)
    {
        if (dynamic_cast<MaterialExpressionVertexShader*>(expression) || dynamic_cast<MaterialExpressionSurfaceShader*>(expression))
        {
            continue;
        }
        filteredExpression.push_back(expression);
    }

    auto expressionStr = SerializeExpressions(filteredExpression).FormatToJson();

    static_cast<MaterialEditorCallback*>(mCallback)->PassCopyExpressionsToGlobal(expressionStr.c_str());
}

void MaterialEditor::Action_PasteExpressions(const std::string& expressionsStr, int32_t targetX, int32_t targetY)
{
    auto node = cross::DeserializeNode::ParseFromJson(expressionsStr);
    auto expressions = MaterialExpression::DeserializeExpressions(node);
    for (auto& expression : expressions)
    {
        expression->m_Id = m_IdGenerator.GenerateId();

        // make duplicated texture objects standalone
        if (auto* expressionTextureObject = dynamic_cast<MaterialExpressionTextureObject*>(expression.get()))
        {
            expressionTextureObject->m_TextureObjectName = m_ParameterAndShaderConstSet.GenerateName("TextureObject");
        }
        // make duplicated reroute declaration name standalone
        else if (auto* expressionRerouteDeclaration = dynamic_cast<MaterialExpressionNamedRerouteDeclaration*>(expression.get()))
        {
            expressionRerouteDeclaration->m_VariableGuid = m_NamedRerouteIdGenerator.GenerateId();
            MakeNamedRerouteNameUnique(expression.get());
        }
        // update function input/output id
        else if (auto* functionInput = dynamic_cast<MaterialExpressionFunctionInput*>(expression.get()))
        {
            functionInput->m_FuncInoutId = m_FunctionInputOutputIdGenerator.GenerateId();
        }
        else if (auto* functionOutput = dynamic_cast<MaterialExpressionFunctionOutput*>(expression.get()))
        {
            functionOutput->m_FuncInoutId = m_FunctionInputOutputIdGenerator.GenerateId();
        }

        AddExpression(expression);
    }

    for (auto expression : expressions)
    {
        expression->DoHandlePropertyChange(this);
    }

    if (m_MaterialGraph)
        m_MaterialGraph->ReconstructLinks();

    int32_t minX = INT_MAX, minY = INT_MAX;
    int32_t maxX = INT_MIN, maxY = INT_MIN;
    for (auto& expression : expressions)
    {
        minX = std::min(minX, expression->m_EditorPositionX);
        minY = std::min(minY, expression->m_EditorPositionY);
        maxX = std::max(maxX, expression->m_EditorPositionX);
        maxY = std::max(maxY, expression->m_EditorPositionY);
    }

    ImGui::SetCurrentContext(mGuiContext);
    ed::SetCurrentEditor(m_Editor);

    auto nodePosition = ax::NodeEditor::ScreenToCanvas(ImVec2{(float)targetX, (float)targetY});
    int32_t offsetX = nodePosition.x - (minX + maxX) / 2;
    int32_t offsetY = nodePosition.y - (minY + maxY) / 2;

    for (auto& expression : expressions)
    {
        expression->m_EditorPositionX += offsetX;
        expression->m_EditorPositionY += offsetY;

        if (m_MaterialGraph)
            m_MaterialGraph->UpdateExpressionPosition(expression.get());
    }

    m_TransactionManager.EmplaceTransaction<MaterialTransactionPasteExpressions>(expressions);
}

void MaterialEditor::DoUpdateExpressionsState()
{
    Compile(false);

    if (!IsEditFx())
    {
        MaterialCompiler compiler(GetMaterialCompilerInputParameters(), true);
        compiler.CallMaterialFunctionOutputs(m_FunctionOutputExpressions);
    }
}

void MaterialEditor::OnPropertyChange(MaterialExpression* expression)
{
    expression->OnPropertyChange(this);

    SetResourceChanged(true);
}

void MaterialEditor::OnMaterialDefinesChange()
{
    UpdateMaterialState(m_Fx, mMaterialDefines);

    // sync render state to material define if advanced mode was closed, in case advanced mode was triggered later
    if (!mMaterialDefines.EnableAdvancedMode)
    {
        Sync(mMaterialDefines, m_Fx);
    }

    // tell SurfaceDataExpression to update its pins
    assert(m_SurfaceShaderExpression);
    m_SurfaceShaderExpression->UpdatePinEnables(GetMaterialDefines());

    UpdateExpressionsState();

    SetResourceChanged(true);
}

void MaterialEditor::OnMaterialFunctionDefinesChange()
{
    SetResourceChanged(true);
}

void MaterialEditor::SetPassRenderGroup(std::string passID, UInt32 renderGroup)
{
    m_Fx->SetRenderGroup(passID, renderGroup);
}

bool MaterialEditor::IsCurrentFxRenderGPass() const
{
    auto& defines = m_Fx->GetMaterialDefines();
    return defines.BlendMode == MaterialBlendMode::Opaque || defines.BlendMode == MaterialBlendMode::Masked;
}

void MaterialEditor::UpdateMaterialState(FxPtr fx, const MaterialDefines& newMaterialDefines)
{
    auto& materialDefines = fx->GetMaterialDefines();
    switch (materialDefines.Domain)
    {
    // TODO: Remove Foliage and opimize postprocess.
    case MaterialDomain::Foliage:
    case MaterialDomain::PostProcess:
    case MaterialDomain::Surface:
    {
        SyncForSurfaceDomain(fx, newMaterialDefines);

        auto oldDefinesOpaque = materialDefines.BlendMode == MaterialBlendMode::Opaque || materialDefines.BlendMode == MaterialBlendMode::Masked;
        auto newDefinesOpaque = newMaterialDefines.BlendMode == MaterialBlendMode::Opaque || newMaterialDefines.BlendMode == MaterialBlendMode::Masked;
        if (oldDefinesOpaque != newDefinesOpaque)
        {
            fx->NotifyChangeRecursively();
        }
        
        break;
    }
    case MaterialDomain::MeshDecal:
        SyncForMeshDecalDomain(fx, newMaterialDefines, mCompilationOutput);
        break;
    default:
        Assert(false);
        break;
    }

    materialDefines = newMaterialDefines;
}



void MaterialEditor::ClearInvalidLinks()
{
    auto IsExpressionOutputValid = [&](ExpressionOutput* output) {
        for (auto& expression : m_Expressions)
        {
            for (auto* outputPin : expression->m_Outputs)
            {
                if (outputPin == output)
                {
                    return true;
                }
            }
        }

        return false;
    };

    for (auto& expression : m_Expressions)
    {
        for (auto* inputPin : expression->m_Inputs)
        {
            if (!IsExpressionOutputValid(inputPin->m_LinkedExpressionOutput))
            {
                inputPin->m_LinkedExpressionOutput = nullptr;
            }
        }
    }
}

void MaterialEditor::OnMaterialFunctionChange(const std::string& materialFunctionGuid)
{
    for (auto expression : m_Expressions)
    {
        auto* expressionFunctionCall = dynamic_cast<MaterialExpressionFunctionCall*>(expression.get());
        if (expressionFunctionCall)
        {
            if (expressionFunctionCall->m_MaterialFunction == materialFunctionGuid)
            {
                expressionFunctionCall->OnPropertyChange(this);
            }
        }
    }
}

void MaterialEditor::OnParameterChange(MaterialParameter* parameter)
{
    for (auto& expression : m_Expressions)
    {
        if (auto* expressionParameter = dynamic_cast<MaterialExpressionParameter*>(expression.get()))
        {
            if (parameter->ParameterName == expressionParameter->m_ParameterName)
            {
                Assert(parameter->DisplayName == expressionParameter->m_Name);

                expressionParameter->CopyFrom(parameter);
                expressionParameter->DoHandlePropertyChange(this);
            }
        }
    }

    SetResourceChanged(true);
}

void MaterialEditor::OnParameterChange(MaterialExpressionParameter* expressionParameter)
{
    MaterialParameter* materialParameter = m_ParameterAndShaderConstSet.UpdateParameter(expressionParameter);

    OnParameterChange(materialParameter);
}

void MaterialEditor::AddParameter(MaterialExpressionParameter* parameter)
{
    m_ParameterAndShaderConstSet.AddParameter(parameter);
}

void MaterialEditor::RemoveParameter(MaterialExpressionParameter* parameter)
{
    m_ParameterAndShaderConstSet.RemoveParameter(parameter);
}

void MaterialEditor::MakeNamedRerouteNameUnique(MaterialExpression* expression)
{
    auto* namedRerouteDeclaration = dynamic_cast<MaterialExpressionNamedRerouteDeclaration*>(expression);
    m_NamedRerouteIdGenerator.ClearNameSet();
    for (auto* rerouteExpression : m_NamedRerouteIdGenerator.m_NamedRerouteExpressions)
    {
        if (rerouteExpression != expression)
        {
            auto* declaration = dynamic_cast<MaterialExpressionNamedRerouteDeclaration*>(rerouteExpression);
            declaration->m_Name = m_NamedRerouteIdGenerator.AddName(declaration->m_Name);
        }
    }
    auto* declaration = dynamic_cast<MaterialExpressionNamedRerouteDeclaration*>(expression);
    declaration->m_Name = m_NamedRerouteIdGenerator.AddName(declaration->m_Name);
}

void MaterialEditor::OnMaterialParameterCollectionSelectChange()
{
    for (auto& expression : m_Expressions)
    {
        if (auto* mpcExpression = dynamic_cast<MaterialExpressionMaterialParameterCollection*>(expression.get()))
        {
            mpcExpression->OnSelectedChange(this);
        }
    }
}

void MaterialEditor::OnMaterialParameterCollectionChange(const std::string& mpcGuid)
{
    for (auto& expression : m_Expressions)
    {
        if (auto* mpcExpression = dynamic_cast<MaterialExpressionMaterialParameterCollection*>(expression.get()))
        {
            mpcExpression->OnPropertyChange(this);
            static_cast<MaterialEditorCallback*>(mCallback)->OnReInspect();
        }
    }

    SetResourceChanged(true);
}

void MaterialEditor::RegisterMaterialFunction(const std::string& materialFunctions)
{
    MaterialExpressionRegistry::RegisterMaterialFunction(materialFunctions);
}

void MaterialEditor::ClearMaterialFunctions()
{
    MaterialExpressionRegistry::ClearMaterialFunctions();
}

void MaterialEditor::OnResourceDragEnd(std::string resourceGUID, UInt32 positionX, UInt32 positionY)
{
    ImGui::SetCurrentContext(mGuiContext);
    ed::SetCurrentEditor(m_Editor);

    auto resourcePtr = gAssetStreamingManager->LoadSynchronously(resourceGUID).get();
    auto nodePosition = ax::NodeEditor::ScreenToCanvas(ImVec2{(float)positionX, (float)positionY});

    if (dynamic_cast<resource::Texture*>(resourcePtr))
    {
        auto* expressionTextureObject = static_cast<MaterialExpressionTextureObject*>(Action_CreateExpression(gbf::reflection::query_meta_class<MaterialExpressionTextureObject>()->name(), nodePosition.x, nodePosition.y));
        expressionTextureObject->m_TextureString = resourceGUID;
        expressionTextureObject->OnPropertyChange(this);
    }
    else if (dynamic_cast<resource::MaterialFunction*>(resourcePtr))
    {
        if (m_MaterialFunction && resourcePtr->GetGuid_Str() == m_ResourceFileGuid)
        {
            return;
        }

        auto* expressionFunctionCall = static_cast<MaterialExpressionFunctionCall*>(Action_CreateExpressionFunctionCall(resourceGUID, nodePosition.x, nodePosition.y));
        expressionFunctionCall->OnPropertyChange(this);
    }
}

bool MaterialEditor::IsExpressionPinValid(ExpressionPin* pin)
{
    for (auto& expression : m_Expressions)
    {
        for (auto* inputPin : expression->m_Inputs)
        {
            if (pin == inputPin)
            {
                return true;
            }
        }

        for (auto* outputPin : expression->m_Outputs)
        {
            if (pin == outputPin)
            {
                return true;
            }
        }
    }

    return false;
}

MaterialExpression* MaterialEditor::GetParentExpression(ExpressionInput* input)
{
    for (auto expression : m_Expressions)
    {
        for (auto* inputPin : expression->GetInputPins())
        {
            if (inputPin == input)
            {
                return expression.get();
            }
        }
    }

    return nullptr;
}

void MaterialEditor::ApplyPropertiesToFx(MaterialCompiler& compiler, resource::Fx* fx)
{
    fx->RemoveUnusedProps();

    for (auto& parameterInfo : compiler.GetParameters())
    {
        if (parameterInfo.m_Type == MaterialParameterType::Scalar)
        {
            fx->SetFloat(parameterInfo.m_ParameterName, std::get<float>(parameterInfo.m_Value));
        }
        else if (parameterInfo.m_Type == MaterialParameterType::Vector)
        {
            fx->SetFloat4(parameterInfo.m_ParameterName, std::get<Float4>(parameterInfo.m_Value).data());
        }
        else if (IsTexture(parameterInfo.m_Type))
        {
            auto texture = TypeCast<resource::Texture>(gAssetStreamingManager->LoadSynchronously(std::get<std::string>(parameterInfo.m_Value)));
            if (texture->GetTextureInfo().UDIM || texture->GetTextureData()->mVTStreaming)
            {
                int index = compiler.FindVtIndex(parameterInfo.m_ParameterName);
                if (index >= 0)
                {
                    fx->SetTexture(fmt::format("{}_VT_{}", parameterInfo.m_ParameterName, index), texture);
                }
            }
            else
            {
                fx->SetTexture(parameterInfo.m_ParameterName, texture);
            }
        }
    }

    for (auto& shaderConstInfo : compiler.GetShaderConsts())
    {
        if (shaderConstInfo.Type == MaterialShaderConstType::Bool)
        {
            fx->SetBool(shaderConstInfo.ShaderConstName, std::get<bool>(shaderConstInfo.Value));
        }
        else if (shaderConstInfo.Type == MaterialShaderConstType::Float)
        {
            Assert(false);
        }
    }

    for (auto& sampler : compiler.GetSamplers())
    {
        fx->SetSamplerState(sampler.Name, sampler.SamplerState);
    }
}

void MaterialEditor::RegisterExpressions()
{
    if (!gRegistered)
    {
        node_editor_bridgeRegister();

        MaterialExpression::RegisterExpressions();
        gRegistered = true;
    }
}

MaterialCompiler::InputParameters MaterialEditor::GetMaterialCompilerInputParameters()
{
    if (auto* callback = GetCallback())
        callback->ClearMessages();
    return MaterialCompiler::InputParameters{
        .defines = GetMaterialDefines(),
        .vertexShaderExpression = GetVertexShaderExpression(),
        .surfaceShaderExpression = GetSurfaceShaderExpression(),
        .previewExpression = GetPreviewExpression(),
        .VTs = &m_VTs,
        .overflowVTs = &m_OverflowVTs,
        .clearMessage = GetCallback() ? std::bind(&MaterialEditorCallback::ClearMessages, GetCallback()) : std::function<void()>{nullptr},
        .addErrorMessage = GetCallback() ? std::bind(&MaterialEditorCallback::AddErrorMessage, GetCallback(), std::placeholders::_1) : std::function<void(const char*)>{nullptr}
    };
}

std::string MaterialParameterAndShaderConstSet::GenerateName(const std::string& prefix)
{
    srand(GetTickCount());
    while (true)
    {
        std::string parameterName = prefix + std::to_string(rand());
        if (!m_NameSet.count(parameterName))
        {
            return parameterName;
        }
    }
    return "";
}

std::string NamedRerouteGenerator::GenerateId()
{
    srand(GetTickCount());
    while (true)
    {
        auto newGuid = CrossUUID::GenerateCrossUUID().ToString();
        if (!m_IdSet.count(newGuid))
        {
            return newGuid;
        }
    }
    return "";
}

std::string NamedRerouteGenerator::GenerateName(const std::string& prefix)
{
    srand(GetTickCount());
    if (!m_NameSet.count(prefix))
    {
        return prefix;
    }
    while (true)
    {
        std::string parameterName = prefix + std::to_string(rand());
        if (!m_NameSet.count(parameterName))
        {
            return parameterName;
        }
    }
    return "";
}

std::string NamedRerouteGenerator::AddName(const std::string& prefix)
{
    auto name = GenerateName(prefix);
    m_NameSet.insert(name);
    return name;
}

void NamedRerouteGenerator::DeleteDeclaration(MaterialExpression* expression)
{
    auto declaration = dynamic_cast<MaterialExpressionNamedRerouteDeclaration*>(expression);
    m_NameSet.erase(declaration->m_Name);
    m_IdSet.erase(declaration->m_VariableGuid);
    m_NamedRerouteExpressions.erase(std::remove(m_NamedRerouteExpressions.begin(), m_NamedRerouteExpressions.end(), expression), m_NamedRerouteExpressions.end());
}

void MaterialParameterAndShaderConstSet::AddParameter(MaterialExpressionParameter* expressionParameter)
{
    // If current parameter exists, count++
    for (auto& parameter : m_Parameters)
    {
        if (parameter->DisplayName == expressionParameter->m_Name && expressionParameter->IsTypeMaching(parameter.get()))
        {
            parameter->ReferenceCount++;
            expressionParameter->CopyFrom(parameter.get());
            m_ExpressionToParameterMap[expressionParameter] = parameter.get();
            return;
        }
    }

    auto parameter = expressionParameter->ToParameter();
    expressionParameter->CopyTo(parameter.get());
    m_Parameters.push_back(parameter);
    PushParameter(expressionParameter->m_Group, parameter.get());
    m_ExpressionToParameterMap[expressionParameter] = parameter.get();

     // Add ParamName to NameSet
    m_NameSet.insert(expressionParameter->m_ParameterName);
}

void MaterialParameterAndShaderConstSet::AddTextureObject(MaterialExpressionTextureObject* expressionTextureObject)
{
    m_TextureObjects.push_back(expressionTextureObject);

    m_NameSet.insert(expressionTextureObject->m_TextureObjectName);
}

void MaterialParameterAndShaderConstSet::RemoveTextureObject(MaterialExpressionTextureObject* expressionTextureObject)
{
    m_TextureObjects.erase(std::find(m_TextureObjects.begin(), m_TextureObjects.end(), expressionTextureObject));

    m_NameSet.erase(expressionTextureObject->m_TextureObjectName);
}

void MaterialParameterAndShaderConstSet::RemoveParameter(MaterialExpressionParameter* expressionParameter)
{
    MaterialParameter* parameter = m_ExpressionToParameterMap[expressionParameter];
    parameter->ReferenceCount--;

    // If count == 0, do remove
    if (parameter->ReferenceCount == 0)
    {
        RemoveParameterFromParameterGroup(parameter);

        // Remove parameter in m_Parameters
        for (auto iter = m_Parameters.begin(); iter != m_Parameters.end(); iter++)
        {
            if (iter->get() == parameter)
            {
                m_Parameters.erase(iter);
                break;
            }
        }

        // Remove ParamName
        m_NameSet.erase(expressionParameter->m_ParameterName);
    }

    m_ExpressionToParameterMap.erase(expressionParameter);
}

MaterialParameter* MaterialParameterAndShaderConstSet::UpdateParameter(MaterialExpressionParameter* expressionParameter)
{
    Assert(m_ExpressionToParameterMap.count(expressionParameter));

    MaterialParameter* parameter = m_ExpressionToParameterMap[expressionParameter];

    if (parameter->DisplayName == expressionParameter->m_Name)
    {
        Assert(parameter->ParameterName == expressionParameter->m_ParameterName);
        Assert(expressionParameter->IsTypeMaching(parameter));

        // If SortPriority changed
        bool isNeedRepushParameter = false;
        if (expressionParameter->m_SortPriority != parameter->SortPriority)
        {
            parameter->SortPriority = expressionParameter->m_SortPriority;
            isNeedRepushParameter = true;
        }

        // If group changed
        MaterialParameterGroup* group = GetParameterGroup(parameter);
        if (group->Name != expressionParameter->m_Group || isNeedRepushParameter)
        {
            RemoveParameterFromParameterGroup(parameter);

            PushParameter(expressionParameter->m_Group, parameter);
        }

        expressionParameter->CopyTo(parameter);
    }
    else
    {
        RemoveParameter(expressionParameter);

        if (auto* parameter = GetParameterByDisplayNameAndType(expressionParameter); parameter)
        {
            expressionParameter->CopyFrom(parameter);
        }
        else
        {
            if (m_NameSet.count(expressionParameter->m_ParameterName))
            {
                expressionParameter->m_ParameterName = GenerateName("Param");
            }
        }

        AddParameter(expressionParameter);
    }

    return m_ExpressionToParameterMap[expressionParameter];
}

void MaterialParameterAndShaderConstSet::ClearParameters()
{
    for (auto parameter : m_Parameters)
    {
        m_NameSet.erase(parameter->ParameterName);
    }

    m_ParameterGroups.clear();
    m_Parameters.clear();
}

std::vector<MaterialParameter*> MaterialParameterAndShaderConstSet::GetAllParameters()
{
    std::vector<MaterialParameter*> result;
    result.resize(m_Parameters.size());
    std::transform(m_Parameters.begin(), m_Parameters.end(), result.begin(), [](auto& pair) { return pair.get(); });
    return result;
}

int FindVtIndex(const std::string& vtParameterName, std::vector<std::string>& VTs)
{
    for (UInt32 i = 0; i < VTs.size(); ++i)
    {
        if ((VTs)[i] == vtParameterName)
        {
            return i;
        }
    }
    return -1;
}

void MaterialParameterAndShaderConstSet::ApplyParametersAndShaderConstsToFx(FxPtr fx, MaterialEditor* materialEditor)
{
    resource::ParameterInfos params;

    for (auto& parameter : m_Parameters)
    {
        if (auto* shaderConst = dynamic_cast<MaterialParameterBool*>(parameter.get()))
        {
            params.mBoolParams.emplace(shaderConst->DisplayName, *shaderConst);
        }
        if (auto* scalarParameter = dynamic_cast<MaterialParameterScalar*>(parameter.get()))
        {
            params.mScalarParams.emplace(scalarParameter->DisplayName, *scalarParameter);
        }
        else if (auto* vectorParameter = dynamic_cast<MaterialParameterVector*>(parameter.get()))
        {
            params.mVectorParams.emplace(vectorParameter->DisplayName, *vectorParameter);
        }
        else if (auto* textureParameter = dynamic_cast<MaterialParameterTexture*>(parameter.get()))
        {
            auto param = *textureParameter;
            auto texture = TypeCast<resource::Texture>(gAssetStreamingManager->LoadSynchronously(textureParameter->Value));
            if (texture->GetTextureData()->mVTStreaming)
            {
                int index = FindVtIndex(textureParameter->ParameterName, materialEditor->GetVTs());
                if (index >= 0)
                {
                    auto parameterName = fmt::format("{}_VT_{}", textureParameter->ParameterName, index);
                    param.ParameterName = parameterName;
                }
            }

            params.mTextureParams.emplace(textureParameter->DisplayName, param);
        }
    }

    fx->GetParameterInfos() = std::move(params);
}

void MaterialEditor::CreateMaterialExpression(ExpressionCreateNodeInfo createInfo, int position_x, int position_y)
{
    ImGui::SetCurrentContext(mGuiContext);
    ed::SetCurrentEditor(m_Editor);
    m_MaterialGraph->CreateNodeAtPos(createInfo, position_x, position_y);
}

void MaterialParameterAndShaderConstSet::PushParameter(const std::string& groupName, MaterialParameter* parameter)
{
    MaterialParameterGroup* targetGroup = nullptr;
    for (auto& parameterGroup : m_ParameterGroups)
    {
        if (parameterGroup.Name == groupName)
        {
            targetGroup = &parameterGroup;
            break;
        }
    }

    if (!targetGroup)
    {
        MaterialParameterGroup group;
        group.Name = groupName;
        m_ParameterGroups.push_back(std::move(group));

        targetGroup = &m_ParameterGroups.back();
    }

    auto& parameters = targetGroup->Parameters;
    parameters.push_back(parameter);
    std::sort(parameters.begin(), parameters.end(), [](MaterialParameter* a, MaterialParameter* b) {
        if (a->SortPriority != b->SortPriority)
        {
            return a->SortPriority < b->SortPriority;
        }
        return a->DisplayName < b->DisplayName;
    });

    std::sort(m_ParameterGroups.begin(), m_ParameterGroups.end(), [](MaterialParameterGroup& a, MaterialParameterGroup& b) { return a.Name < b.Name; });
}

MaterialParameterGroup* MaterialParameterAndShaderConstSet::GetParameterGroup(MaterialParameter* parameter)
{
    for (auto& parameterGroup : m_ParameterGroups)
    {
        if (auto iter = std::find(parameterGroup.Parameters.begin(), parameterGroup.Parameters.end(), parameter); iter != parameterGroup.Parameters.end())
        {
            return &parameterGroup;
        }
    }

    return nullptr;
}

MaterialParameter* MaterialParameterAndShaderConstSet::GetParameterByDisplayNameAndType(const MaterialExpressionParameter* paramExpression)
{
    for (auto& parameter : m_Parameters)
    {
        if (parameter->DisplayName == paramExpression->m_Name && paramExpression->IsTypeMaching(parameter.get()))
        {
            return parameter.get();
        }
    }

    return nullptr;
}

void MaterialParameterAndShaderConstSet::RemoveParameterFromParameterGroup(MaterialParameter* parameter)
{
    for (auto groupIter = m_ParameterGroups.begin(); groupIter != m_ParameterGroups.end(); groupIter++)
    {
        auto& parameterGroup = *groupIter;

        for (auto paramIter = parameterGroup.Parameters.begin(); paramIter != parameterGroup.Parameters.end(); paramIter++)
        {
            if ((*paramIter) == parameter)
            {
                parameterGroup.Parameters.erase(paramIter);

                if (parameterGroup.Parameters.empty())
                {
                    m_ParameterGroups.erase(groupIter);
                }

                return;
            }
        }
    }
}

void MaterialEditor::Sync(MaterialDefines& dst, FxPtr src)
{
    dst.AdvancedRenderStates = {};

    for (auto& [name, pass] : src->GetAllPass())
    {
        dst.AdvancedRenderStates.mPassID.emplace_back(name.GetName());
        dst.AdvancedRenderStates.mRenderStates.emplace_back(pass.mState);
    }
}

UITexturePtr MaterialEditor::GetUITexture(MaterialExpression* expression)
{
    {
        if (m_ExpressionToUITextureMap.find(expression) == m_ExpressionToUITextureMap.end())
        {
            UpdateUITextureAndExpressionTexture(expression);
        }
        return m_ExpressionToUITextureMap.at(expression);
    }
}
void MaterialEditor::SyncForSurfaceDomain(FxPtr dst, const MaterialDefines& src)
{
    switch (src.BlendMode)
    {
    case MaterialBlendMode::Opaque:
    case MaterialBlendMode::Masked:
        dst->SetPassEnable("forward", false);
        dst->SetPassEnable("gpass", true);
        dst->SetPassEnable("shadow_all", true);
        dst->SetPassEnable("VSMDepth", true);
        break;
    case MaterialBlendMode::Translucent:
    case MaterialBlendMode::Additive:
    case MaterialBlendMode::Modulate:
    case MaterialBlendMode::AlphaComposite:
    {
        dst->SetPassEnable("forward", true);
        dst->SetPassEnable("gpass", false);
        dst->SetPassEnable("shadow_all", false);
        dst->SetPassEnable("VSMDepth", false);

        constexpr static NGITargetBlendStateDesc gReactiveMaskBlend{
            false,
            false,
            BlendFactor::One,
            BlendFactor::Zero,
            BlendOp::Add,
            BlendFactor::Zero,
            BlendFactor::Zero,
            BlendOp::Add,
            LogicOp::NoOp,
            ColorMask::All,
        };

        switch (src.BlendMode)
        {
        case MaterialBlendMode::Translucent:
        {
            constexpr static NGITargetBlendStateDesc gTranslucentBlend{
                true,
                false,
                BlendFactor::SrcAlpha,
                BlendFactor::InvSrcAlpha,
                BlendOp::Add,
                BlendFactor::Zero,
                BlendFactor::Zero,
                BlendOp::Add,
                LogicOp::NoOp,
                ColorMask::All,
            };
            NGITargetBlendStateDesc gSeparateTranslucentBlend{
                true,
                false,
                BlendFactor::SrcAlpha,
                BlendFactor::InvSrcAlpha,
                BlendOp::Add,
                BlendFactor::Zero,
                BlendFactor::InvSrcAlpha,
                BlendOp::Add,
                LogicOp::NoOp,
                src.EnableSeparateTranslucency ? ColorMask::All : ColorMask::None,
            };
            dst->SetBlendState("forward", {.TargetCount = 3, .TargetBlendState = {gTranslucentBlend, gSeparateTranslucentBlend, gReactiveMaskBlend}});
            break;
        }
        case MaterialBlendMode::Additive:
        {
            constexpr static NGITargetBlendStateDesc gAdditiveBlend{
                true,
                false,
                BlendFactor::One,
                BlendFactor::One,
                BlendOp::Add,
                BlendFactor::Zero,
                BlendFactor::One,
                BlendOp::Add,
                LogicOp::NoOp,
                ColorMask::All,
            };
            constexpr static NGITargetBlendStateDesc gSepareteAdditiveBlend{
                true,
                false,
                BlendFactor::One,
                BlendFactor::One,
                BlendOp::Add,
                BlendFactor::Zero,
                BlendFactor::One,
                BlendOp::Add,
                LogicOp::NoOp,
                ColorMask::All,
            };
            dst->SetBlendState("forward", {.TargetCount = 3, .TargetBlendState = {gAdditiveBlend, gSepareteAdditiveBlend, gReactiveMaskBlend}});
            break;
        }
        case MaterialBlendMode::Modulate:
        {
            AssertMsg(!src.EnableSeparateTranslucency, "Not Implemented");
            constexpr static NGITargetBlendStateDesc gModulateBlend{
                true,
                false,
                BlendFactor::DestColor,
                BlendFactor::Zero,
                BlendOp::Add,
                BlendFactor::Zero,
                BlendFactor::Zero,
                BlendOp::Add,
                LogicOp::NoOp,
                ColorMask::All,
            };
            dst->SetBlendState("forward", {.TargetCount = 3, .TargetBlendState = {gModulateBlend, gModulateBlend, gReactiveMaskBlend}});
            break;
        }
        case MaterialBlendMode::AlphaComposite:
        {
            AssertMsg(!src.EnableSeparateTranslucency, "Not Implemented");
            constexpr static NGITargetBlendStateDesc gAlphaCompositeBlend{
                true,
                false,
                BlendFactor::One,
                BlendFactor::InvSrcAlpha,
                BlendOp::Add,
                BlendFactor::Zero,
                BlendFactor::Zero,
                BlendOp::Add,
                LogicOp::NoOp,
                ColorMask::All,
            };
            dst->SetBlendState("forward", {.TargetCount = 3, .TargetBlendState = {gAlphaCompositeBlend, gAlphaCompositeBlend, gReactiveMaskBlend}});
            break;
        }
        default:
            break;
        }
        break;
    }
    default:
        break;
    }

    dst->SetBool(NAME_ID("ALPHA_CLIPPING"), src.BlendMode == MaterialBlendMode::Masked);
    dst->SetFloat(NAME_ID("MATERIAL_TYPE"), ToUnderlying(src.ShadingModel));
    dst->SetFloat(NAME_ID("MATERIAL_BLEND_MODE"), ToUnderlying(src.BlendMode));
    dst->SetBool(NAME_ID("DISABLE_FORWARD_SSR"), src.ForceDisableSSR);

    if (src.Domain == MaterialDomain::PostProcess)
    {
        dst->SetDepthStencilState("forward", {.EnableDepth = false, .EnableDepthWrite = false, .EnableStencil = false});
    }

    if (src.TwoSided)
    {
        constexpr static NGIRasterizationStateDesc gNoCulling{
            .FillMode = FillMode::Solid,
            .CullMode = CullMode::None,
            .FaceOrder = FaceOrder::CW,
            .EnableDepthClip = true,
            .RasterMode = RasterizationMode::DefaultRaster,
        };
        dst->SetRasterizerState("VSMDepth", gNoCulling);
        dst->SetRasterizerState("shadow_all", gNoCulling);
        dst->SetRasterizerState("gpass", gNoCulling);
        dst->SetRasterizerState("forward", gNoCulling);
        dst->SetBool(NAME_ID("DOUBLE_SIDED"), true);
        dst->SetFloat4(NAME_ID("_DoubleSidedConstants"), std::vector<float>{-1.0f, -1.0f, -1.0f, -1.0f}.data());
    }
    else
    {
        constexpr static NGIRasterizationStateDesc gBackFaceCulling{
            .FillMode = FillMode::Solid,
            .CullMode = CullMode::Back,
            .FaceOrder = FaceOrder::CW,
            .EnableDepthClip = true,
            .RasterMode = RasterizationMode::DefaultRaster,
        };
        dst->SetRasterizerState("VSMDepth", gBackFaceCulling);
        dst->SetRasterizerState("shadow_all", gBackFaceCulling);
        dst->SetRasterizerState("gpass", gBackFaceCulling);
        dst->SetRasterizerState("forward", gBackFaceCulling);
        dst->SetBool(NAME_ID("DOUBLE_SIDED"), false);
        dst->SetFloat4(NAME_ID("_DoubleSidedConstants"), std::vector<float>{0.0, 0.0, 0.0f, 0.0f}.data());
    }

    dst->SetBool(NAME_ID("ForceDisableExponentialFog"), src.ForceDisableExponentialFog);
    dst->SetBool(NAME_ID("ForceDisableVolumetricFog"), src.ForceDisableVolumetricFog);
    dst->SetBool(NAME_ID("ForceDisableCloudFog"), src.ForceDisableCloudFog);
    dst->SetBool(NAME_ID("CE_ENABLE_SEPERATE_TRANSLUCENCY"), src.EnableSeparateTranslucency);

    if (src.EnableAdvancedMode)
    {
        for (UInt32 i = 0; i < src.AdvancedRenderStates.mPassID.size(); ++i)
        {
            dst->SetRenderState(src.AdvancedRenderStates.mPassID[i], src.AdvancedRenderStates.mRenderStates[i]);
        }
    }
}

void MaterialEditor::SyncForMeshDecalDomain(FxPtr dst, const MaterialDefines& src, MaterialCompiler::CompilationOutput const& compilationOutput)
{
    constexpr static NGITargetBlendStateDesc gMeshDecalWithBlend{
        true,
        false,
        BlendFactor::SrcAlpha,
        BlendFactor::InvSrcAlpha,
        BlendOp::Add,
        BlendFactor::Zero,
        BlendFactor::InvSrcAlpha,
        BlendOp::Add,
        LogicOp::NoOp,
        ColorMask::All,
    };
    constexpr static NGITargetBlendStateDesc gMeshDecalWithoutBlend{
        true,
        false,
        BlendFactor::Zero,
        BlendFactor::One,
        BlendOp::Add,
        BlendFactor::Zero,
        BlendFactor::One,
        BlendOp::Add,
        LogicOp::NoOp,
        ColorMask::All,
    };
    constexpr static NGITargetBlendStateDesc gMeshDecalSceneColorBlend{
        true,
        false,
        BlendFactor::SrcAlpha,
        BlendFactor::One,
        BlendOp::Add,
        BlendFactor::One,
        BlendFactor::Zero,
        BlendOp::Add,
        LogicOp::NoOp,
        ColorMask::All,
    };
    dst->SetBlendState(
        "decal_gpass", 
        {
            .TargetCount = 4,
            .TargetBlendState = {
                compilationOutput.mBaseColorConnencted ? gMeshDecalWithBlend : gMeshDecalWithoutBlend,
                compilationOutput.mNormalConnencted ? gMeshDecalWithBlend : gMeshDecalWithoutBlend,
                compilationOutput.mMetallicConnencted || compilationOutput.mRoughnessConnencted || compilationOutput.mSpecularConnencted ? gMeshDecalWithBlend : gMeshDecalWithoutBlend,
                compilationOutput.mEmissiveColorConnencted ? gMeshDecalSceneColorBlend : gMeshDecalWithoutBlend,
            }
        }
    );

    constexpr static NGIStencilOperation gMeshDecalOp{
        StencilOp::Keep,
        StencilOp::Keep,
        StencilOp::Replace,
        NGIComparisonOp::Always,
    };
    constexpr static NGIDepthStencilStateDesc gMeshDecalDepthStencilState{
        true,
        false,
        NGIComparisonOp::GreaterEqual,
        true,
        0x20,
        0x20,
        gMeshDecalOp,
        gMeshDecalOp,
    };
    dst->SetDepthStencilState("decal_gpass" , gMeshDecalDepthStencilState);

    constexpr static NGIDynamicStateDesc gMeshDecalDynamicState{
        0x20,
    };
    dst->SetDynamicState("decal_gpass", gMeshDecalDynamicState);

    if (src.TwoSided)
    {
        constexpr static NGIRasterizationStateDesc gMeshDecalNoCulling{
            .FillMode = FillMode::Solid,
            .CullMode = CullMode::None,
            .FaceOrder = FaceOrder::CW,
            .EnableDepthClip = true,
            .RasterMode = RasterizationMode::DefaultRaster,
        };
        dst->SetRasterizerState("decal_gpass", gMeshDecalNoCulling);
        dst->SetBool(NAME_ID("DOUBLE_SIDED"), true);
        dst->SetFloat4(NAME_ID("_DoubleSidedConstants"), std::vector<float>{-1.0f, -1.0f, -1.0f, -1.0f}.data());
    }
    else
    {
        constexpr static NGIRasterizationStateDesc gMeshDecalBackFaceCulling{
            .FillMode = FillMode::Solid,
            .CullMode = CullMode::Back,
            .FaceOrder = FaceOrder::CW,
            .EnableDepthClip = true,
            .RasterMode = RasterizationMode::DefaultRaster,
        };
        dst->SetRasterizerState("decal_gpass", gMeshDecalBackFaceCulling);
        dst->SetBool(NAME_ID("DOUBLE_SIDED"), false);
        dst->SetFloat4(NAME_ID("_DoubleSidedConstants"), std::vector<float>{0.0, 0.0, 0.0f, 0.0f}.data());
    }

    dst->SetBool(NAME_ID("ALPHA_CLIPPING"), false);
    dst->SetFloat(NAME_ID("MATERIAL_TYPE"), ToUnderlying(MaterialShadingModel::Standard));
    dst->SetFloat(NAME_ID("MATERIAL_BLEND_MODE"), ToUnderlying(src.BlendMode));
    dst->SetBool(NAME_ID("DISABLE_FORWARD_SSR"), true);

    dst->SetBool(NAME_ID("ForceDisableExponentialFog"), true);
    dst->SetBool(NAME_ID("ForceDisableVolumetricFog"), true);
    dst->SetBool(NAME_ID("ForceDisableCloudFog"), true);
    dst->SetBool(NAME_ID("CE_ENABLE_SEPERATE_TRANSLUCENCY"), false);
}

MaterialExpression* MaterialEditor::FindRerouteDeclarationByGuid(const std::string variableGuid) const
{
    for (const auto& expression : m_Expressions)
    {
        if (auto declaration = std::dynamic_pointer_cast<MaterialExpressionNamedRerouteDeclaration>(expression))
        {
            if (declaration->m_VariableGuid == variableGuid)
            {
                return declaration.get();
            }
        }
    }
    return nullptr;
}
MaterialExpression* MaterialEditor::FindRerouteDeclarationByName(const std::string name) const
{
    for (const auto& expression : m_Expressions)
    {
        if (auto declaration = std::dynamic_pointer_cast<MaterialExpressionNamedRerouteDeclaration>(expression))
        {
            if (declaration->m_Name == name)
            {
                return declaration.get();
            }
        }
    }
    return nullptr;
}
void TextureExpressionHandler::HandleVirtualTexture(TexturePtr texturePtr, MaterialInterfacePtr material, TextureType textureType, MaterialValueType& outTextureType)
{
    std::string filePath = "Intermediate/Thumb/" + std::string(texturePtr->GetAsset()->GetName()) + ".thumb.nda";
    auto vtThumbnailPtr = TypeCast<resource::Texture>(gAssetStreamingManager->LoadSynchronously(filePath));
    if (vtThumbnailPtr)
    {
        material->SetTexture(NAME_ID("color_texture"), vtThumbnailPtr);
    }

    outTextureType = (textureType == TextureType::NormalTexture) ? MCT_TextureVirtualNormal : MCT_TextureVirtual;
}
void TextureExpressionHandler::HandleRegularTexture(TexturePtr texturePtr, MaterialInterfacePtr material, TextureType textureType, MaterialValueType& outTextureType) 
{
    material->SetTexture(NAME_ID("color_texture"), texturePtr);

    switch (textureType)
    {
    case TextureType::ImageTexture:
        outTextureType = MCT_Texture2D;
        break;
    case TextureType::NormalTexture:
        outTextureType = MCT_Texture2DNormal;
        break;
    case TextureType::RectangularCubeMap:
    case TextureType::LUT_Cube:
        outTextureType = MCT_TextureCube;
        break;
    default:
        outTextureType = MCT_Texture2D;
    }
}
void TextureExpressionHandler::UpdateTextureCommon(MaterialExpression* expression, const std::string& texturePath, MaterialEditor* editor, MaterialValueType& outTextureType)
{
    Assert(!texturePath.empty());
    std::string tmpTexturePath = texturePath.empty() ? gResourceMgr.GetDefaultTexturePath(): texturePath;
    TexturePtr texturePtr{TYPE_CAST(resource::Texture*, gAssetStreamingManager->LoadSynchronously(tmpTexturePath).get())};
    if (!texturePtr)
        texturePtr = TYPE_CAST(resource::Texture*, gAssetStreamingManager->LoadSynchronously(gResourceMgr.GetDefaultTexturePath()).get());

    auto fxPtr = TypeCast<resource::Fx>(gAssetStreamingManager->LoadSynchronously("EngineResource/Shader/EditorIMGUINew.fx.nda"));
    auto uiMaterial = resource::Material::CreateMaterialInstance(fxPtr);

    UITexturePtr uiTexPtr = GetOrCreateUITexture(expression, editor);
    *uiTexPtr = UITexture{"", static_cast<int>(texturePtr->GetWidth()), static_cast<int>(texturePtr->GetHeight()), uiMaterial, texturePtr.get()};

    auto& material = uiTexPtr->mImageMaterial;
    auto textureProvider = texturePtr->GetTextureData();
    bool isVirtualTexture = textureProvider->mVTStreaming;
    auto textureType = textureProvider->mResourceInfo.Type;

    // Set material properties
    material->SetBool(NAME_ID("IS_SRGB"), textureProvider->mResourceInfo.ColorSpace == ColorSpace::SRGB);
    material->SetBool(NAME_ID("IS_NORMAL"), textureType == TextureType::NormalTexture);
    material->SetBool(NAME_ID("IS_CUBEMAP"), textureType == TextureType::RectangularCubeMap);

    if (isVirtualTexture)
    {
        HandleVirtualTexture(texturePtr, material, textureType, outTextureType);
    }
    else
    {
        HandleRegularTexture(texturePtr, material, textureType, outTextureType);
    }
}
void TextureParameterHandler::UpdateTexture(MaterialExpression* expression, MaterialEditor* editor)
{
    if (auto* texParam = dynamic_cast<MaterialExpressionTextureParameter*>(expression))
    {
        UpdateTextureCommon(expression, texParam->m_TextureString, editor, texParam->m_TextureType);
    }
    else if (auto* texSampleParam = dynamic_cast<MaterialExpressionTextureSampleParameter*>(expression))
    {
        UpdateTextureCommon(expression, texSampleParam->m_TextureString, editor, texSampleParam->m_TextureType);
    }
}
    
bool TextureParameterHandler::CanHandle(MaterialExpression* expression) const
{
    return dynamic_cast<MaterialExpressionTextureParameter*>(expression) != nullptr || dynamic_cast<MaterialExpressionTextureSampleParameter*>(expression) != nullptr;
}

bool TextureObjectHandler::CanHandle(MaterialExpression* expression) const
{
    return dynamic_cast<MaterialExpressionTextureObject*>(expression) != nullptr || dynamic_cast<MaterialExpressionTextureSample*>(expression) != nullptr;
}

void TextureObjectHandler::UpdateTexture(MaterialExpression* expression, MaterialEditor* editor)
{
    if (auto* expressionTexObj = dynamic_cast<MaterialExpressionTextureObject*>(expression))
    {
        UpdateTextureCommon(expression, expressionTexObj->m_TextureString, editor, expressionTexObj->m_TextureType);
    }
    else if (auto* expressionTexObj = dynamic_cast<MaterialExpressionTextureSample*>(expression))
    {
        UpdateTextureCommon(expression, expressionTexObj->m_DefaultTexture, editor, expressionTexObj->m_DefaultTextureType);
    }
}
}   // namespace cross

