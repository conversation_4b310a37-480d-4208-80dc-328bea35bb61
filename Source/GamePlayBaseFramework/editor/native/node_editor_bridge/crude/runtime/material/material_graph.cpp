#include "material_graph.h"
#include <assert.h>
#include "CrossBase/String/Word.h"
#include "editor/crude_logger.h"
#include "material_editor.h"
#include "MaterialBP/material_expression.h"
#include "MaterialBP/material_expression_add.h"
#include "MaterialBP/material_expression_registry.h"
#include "MaterialBP/material_expression_texture_parameter.h"
#include "MaterialBP/material_expression_function_input.h"
#include "MaterialBP/material_expression_function_output.h"
#include "MaterialBP/material_expression_function_call.h"
#include "MaterialBP/material_expression_shader_const_bool.h"
#include "MaterialBP/material_expression_comment.h"
#include "MaterialBP/material_expression_constant.h"
#include "MaterialBP/material_expression_constant2vector.h"
#include "MaterialBP/material_expression_constant3vector.h"
#include "MaterialBP/material_expression_constant4vector.h"
#include "MaterialBP/material_expression_scalar_parameter.h"
#include "MaterialBP/material_expression_vector_parameter.h"
#include "MaterialBP/material_expression_texture_object.h"
#include "MaterialBP/material_expression_texture_parameter.h"
#include "MaterialBP/material_expression_texture_sample_parameter.h"
#include "MaterialBP/material_expression_texture_object.h"
#include "MaterialBP/material_expression_texture_sample.h"
#include "MaterialBP/material_expression_named_reroute.h"
#include "MaterialBP/material_expression_rerouter.h"

namespace cross {
MaterialGraphNode* MaterialGraph::AddExpression(MaterialExpression* expression)
{
    MaterialGraphNode* newNode = AddGraphNode<MaterialGraphNode>();

    newNode->m_GetName = [=]() { return expression->GetCaption(); };
    newNode->m_GetSubTitle = [=]() { return m_Editor.GetPreviewExpression() == expression ? "Previewing" : ""; };
    newNode->m_GetDescription = [=]() { return expression->m_Description; };
    newNode->m_GetErrorMessage = [=]() { return expression->m_ErrorMessage; };
    newNode->m_GetTitleColor = [=]() { return m_Editor.GetPreviewExpression() == expression ? ImColor(0.0f, 0.0f, 1.0f, 1.0f) : expression->GetTitleColor(); };
    newNode->m_GetNodeSize = [=]() { return expression->GetNodeSize(); };
    newNode->m_SetCustomAttrInt2 = [=](int x, int y) { return expression->SetCustomAttrInt2(x, y, &m_Editor); };

    if (auto* textureParameter = dynamic_cast<MaterialExpressionTextureParameter*>(expression))
    {
        newNode->m_GetDisplayTexture = [=]() { return m_Editor.GetUITexture(expression); };
    }
    if (auto* textureParameter = dynamic_cast<MaterialExpressionTextureSampleParameter*>(expression))
    {
        newNode->m_GetDisplayTexture = [=]() { return m_Editor.GetUITexture(expression); };
    }

    if (auto* textureObject = dynamic_cast<MaterialExpressionTextureObject*>(expression))
    {
        newNode->m_GetDisplayTexture = [=]() { return m_Editor.GetUITexture(expression); };
    }

    if (auto comment = dynamic_cast<MaterialExpressionComment*>(expression); comment)
    {
        newNode->m_NodeDrawingType = GraphNodeType::Comment;
        newNode->m_ShowBubbleWhenZoomed = [=]() { return comment->mShowBubbleWhenZoomed; };
    }

    if (auto reroute = dynamic_cast<MaterialExpressionRerouter*>(expression))
    {
        newNode->m_NodeDrawingType = GraphNodeType::Reroute;
    }

    newNode->m_PositionX = expression->m_EditorPositionX;
    newNode->m_PositionY = expression->m_EditorPositionY;
    newNode->m_MaterialExpression = expression;
    expression->m_GraphNode = newNode;

    for (ExpressionInput* input : expression->GetInputPins())
    {
        auto* newPin = AllocateGraphPin<MaterialGraphPin>();
        newPin->m_Name = input->m_Name;
        newPin->m_Node = newNode;
        newPin->m_Type = PinType::Input;
        newPin->m_BindedPropertyName = input->m_BindedPropertyName;
        newPin->m_GetEnable = [=]() { return input->m_Enable; };
        newPin->m_GetBindedPropertyValue = input->m_GetBindedPropertyValue;
        newPin->m_ExpressionInput = input;
        input->m_GraphPin = newPin;
        newNode->m_InputPins.emplace_back(newPin);
    }

    for (ExpressionOutput* output : expression->GetOutputPins())
    {
        auto* newPin = AllocateGraphPin<MaterialGraphPin>();
        newPin->m_Name = output->m_Name;
        newPin->m_Node = newNode;
        newPin->m_Type = PinType::Output;
        newPin->m_BindedPropertyName = output->m_BindedPropertyName;
        newPin->m_GetEnable = [=]() { return output->m_Enable; };
        newPin->m_GetBindedPropertyValue = output->m_GetBindedPropertyValue;
        newPin->m_ExpressionOutput = output;
        output->m_GraphPin = newPin;
        newNode->m_OutputPins.emplace_back(newPin);
    }

    return newNode;
}

void MaterialGraph::DeleteExpression(MaterialExpression* expression)
{
    DoDeleteNode(expression->m_GraphNode);
}

void MaterialGraph::UpdateExpression(MaterialExpression* expression)
{
    MaterialGraphNode* node = expression->m_GraphNode;

    node->m_GetName = [=]() { return expression->GetCaption(); };
    node->m_GetDescription = [=]() { return expression->m_Description; };
    node->m_GetErrorMessage = [=]() { return expression->m_ErrorMessage; };

    if (auto* textureParameter = dynamic_cast<MaterialExpressionTextureParameter*>(expression))
    {
        node->m_GetDisplayTexture = [=]() { return m_Editor.GetUITexture(expression); };
    }
    if (auto* textureParameter = dynamic_cast<MaterialExpressionTextureSampleParameter*>(expression))
    {
        node->m_GetDisplayTexture = [=]() { return m_Editor.GetUITexture(expression); };
    }
    if (dynamic_cast<MaterialExpressionComment*>(expression))
    {
        node->m_NodeDrawingType = GraphNodeType::Comment;
    }
    if (dynamic_cast<MaterialExpressionRerouter*>(expression))
    {
        node->m_NodeDrawingType = GraphNodeType::Reroute;
    }

    node->m_PositionX = expression->m_EditorPositionX;
    node->m_PositionY = expression->m_EditorPositionY;

    DoClearNodePins(node);

    node->m_InputPins.clear();
    node->m_OutputPins.clear();

    for (ExpressionInput* input : expression->GetInputPins())
    {
        auto* newPin = AllocateGraphPin<MaterialGraphPin>();
        newPin->m_Name = input->m_Name;
        newPin->m_Node = node;
        newPin->m_Type = PinType::Input;
        newPin->m_BindedPropertyName = input->m_BindedPropertyName;
        newPin->m_GetEnable = [=]() { return input->m_Enable; };
        newPin->m_GetBindedPropertyValue = input->m_GetBindedPropertyValue;
        newPin->m_ExpressionInput = input;
        input->m_GraphPin = newPin;
        node->m_InputPins.emplace_back(newPin);
    }

    for (ExpressionOutput* output : expression->GetOutputPins())
    {
        auto* newPin = AllocateGraphPin<MaterialGraphPin>();
        newPin->m_Name = output->m_Name;
        newPin->m_Node = node;
        newPin->m_Type = PinType::Output;
        newPin->m_BindedPropertyName = output->m_BindedPropertyName;
        newPin->m_GetEnable = [=]() { return output->m_Enable; };
        newPin->m_GetBindedPropertyValue = output->m_GetBindedPropertyValue;
        newPin->m_ExpressionOutput = output;
        output->m_GraphPin = newPin;
        node->m_OutputPins.emplace_back(newPin);
    }
}

void MaterialGraph::UpdateExpressionPosition(MaterialExpression* expression)
{
    SetNodePosition(expression->m_GraphNode, expression->m_EditorPositionX, expression->m_EditorPositionY);
}

void MaterialGraph::CreateLink(ExpressionOutput* outputPin, ExpressionInput* inputPin)
{
    // only allowed one pin linking to inputPin
    assert(inputPin->m_GraphPin->m_LinkedPins.size() <= 1);

    if (inputPin->m_GraphPin->m_LinkedPins.size() == 1)
    {
        auto* srcOutputPin = inputPin->m_GraphPin->m_LinkedPins[0];
        DoBreakLink(srcOutputPin, inputPin->m_GraphPin);
    }

    DoCreateLink(outputPin->m_GraphPin, inputPin->m_GraphPin);
}

void MaterialGraph::BreakLink(ExpressionOutput* outputPin, ExpressionInput* inputPin)
{
    DoBreakLink(outputPin->m_GraphPin, inputPin->m_GraphPin);
}

void MaterialGraph::ReconstructLinks()
{
    for (auto& nodePtr : m_Nodes)
    {
        for (auto* pinPtr : nodePtr->m_InputPins)
        {
            pinPtr->m_LinkedPins.clear();
        }
        for (auto* pinPtr : nodePtr->m_OutputPins)
        {
            pinPtr->m_LinkedPins.clear();
        }
    }

    for (auto& nodePtr : m_Nodes)
    {
        MaterialExpression* expression = Cast(nodePtr.get())->m_MaterialExpression;

        for (ExpressionInput* input : expression->GetInputPins())
        {
            ExpressionOutput* output = input->m_LinkedExpressionOutput;
            if (output)
            {
                input->m_GraphPin->LinkTo(output->m_GraphPin);
            }
        }
    }
}

void MaterialGraph::OnBeginAction()
{
    m_Editor.BeginAction();
}

void MaterialGraph::OnEndAction()
{
    m_Editor.EndAction();
}

void MaterialGraph::OnCreateLink(GraphPin* outputPin, GraphPin* inputPin)
{
    m_Editor.Action_CreateLink(Cast(outputPin)->m_ExpressionOutput, Cast(inputPin)->m_ExpressionInput);
}

void MaterialGraph::OnBreakLink(GraphPin* outputPin, GraphPin* inputPin)
{
    m_Editor.Action_BreakLink(Cast(inputPin)->m_ExpressionInput);
}

void MaterialGraph::OnNodeMoved(GraphNode* node, int32_t dstPosX, int32_t dstPosY)
{
    m_Editor.Action_MoveExpression(Cast(node)->m_MaterialExpression, dstPosX, dstPosY);
}

void MaterialGraph::OnDeleteNodes(const std::vector<GraphNode*> nodes)
{
    m_Editor.BeginAction();
    for (auto* node : nodes)
    {
        m_Editor.Action_DeleteExpression(Cast(node)->m_MaterialExpression);
    }
    m_Editor.EndAction();
}

const MaterialExpressionRegistry::ExpressionCreateInfo* MaterialGraph::FindSelectedExpressionCreateInfo(const std::vector<MaterialExpressionRegistry::ExpressionCreateInfo>& expressionCreateInfos)
{
    if (m_IsNeedRefreshSelectedExpression)
    {
        int minLength = INT_MAX;
        const MaterialExpressionRegistry::ExpressionCreateInfo* selectedExpressionCreator = nullptr;

        // find expressionName start with seachText
        for (auto& expressionCreator : expressionCreateInfos)
        {
            std::string_view expressionDisplayName = expressionCreator.menuName;

            if (cross::ToLower(std::string(expressionDisplayName))._Starts_with(m_SearchText))
            {
                if (expressionDisplayName.size() < minLength)
                {
                    minLength = expressionDisplayName.size();
                    selectedExpressionCreator = &expressionCreator;
                }
            }
        }

        // otherwise select expression with minimum length
        if (!selectedExpressionCreator)
        {
            minLength = INT_MAX;

            for (auto& expressionCreator : expressionCreateInfos)
            {
                std::string_view expressionDisplayName = expressionCreator.menuName;

                if (expressionDisplayName.size() < minLength)
                {
                    minLength = expressionDisplayName.size();
                    selectedExpressionCreator = &expressionCreator;
                }
            }
        }

        return selectedExpressionCreator;
    }
    else
    {
        if (expressionCreateInfos.size() > 0)
        {
            m_SelectedExpressionIndex = m_SelectedExpressionIndex % expressionCreateInfos.size();
            return &expressionCreateInfos[m_SelectedExpressionIndex];
        }
        else
        {
            return nullptr;
        }
    }
}

static int _SearchInputTextCallback(ImGuiInputTextCallbackData* data)
{
    return reinterpret_cast<MaterialGraph*>(data->UserData)->SearchInputTextCallback(data);
}

int MaterialGraph::SearchInputTextCallback(ImGuiInputTextCallbackData* data)
{
    if ((data->EventFlag & ImGuiInputTextFlags_CallbackHistory) != 0)
    {
        if (data->EventKey == ImGuiKey_UpArrow)
        {
            m_SelectedExpressionIndex--;
        }
        else if (data->EventKey == ImGuiKey_DownArrow)
        {
            m_SelectedExpressionIndex++;
        }
    }

    if ((data->EventFlag & ImGuiInputTextFlags_CallbackEdit) != 0)
    {
        m_SearchText = cross::ToLower(std::string(data->Buf));
        m_IsNeedRefreshSelectedExpression = true;
    }

    return 0;
}

void MaterialGraph::OnShowCreateNodeDialog(bool isFirstShow, GraphPin* pin)
{
    auto CreateExpressionFunc = [&](std::string className, std::string materialFunction, int32_t x, int32_t y, std::function<std::shared_ptr<MaterialExpression>()> createFunction) {
        if (createFunction)
        {
            return m_Editor.Action_CreateExpression(className, x, y, createFunction);
        }
        else
        {
            if (className == gbf::reflection::query_meta_class<MaterialExpressionFunctionCall>()->name())
            {
                return m_Editor.Action_CreateExpressionFunctionCall(materialFunction, x, y);
            }
            else
            {
                return m_Editor.Action_CreateExpression(className, x, y);
            }
        }
    };

    m_Editor.BeginAction();

    auto nodePosition = ax::NodeEditor::ScreenToCanvas(ImGui::GetMousePosOnOpeningCurrentPopup());

    if (isFirstShow)
    {
        m_SearchText = "";
    }

    MaterialExpression* newExpression = nullptr;
    m_IsNeedRefreshSelectedExpression = false;

    bool isEditFx = m_Editor.IsEditFx();

    // filter expressions
    std::vector<MaterialExpressionRegistry::ExpressionCreateInfo> expressionCreateInfos;

    for (auto& createInfo : MaterialExpressionRegistry::GetAllExpressionCreateInfos(m_Editor.GetExpressions()))
    {
        // ignore FunctionInput FunctionOutput
        if (isEditFx && (createInfo.className == gbf::reflection::query_meta_class<MaterialExpressionFunctionInput>()->name() || createInfo.className == gbf::reflection::query_meta_class<MaterialExpressionFunctionOutput>()->name()))
        {
            continue;
        }

        if (pin)
        {
            if (pin->m_Type == PinType::Input)
            {
                if (createInfo.hasOutput)
                {
                    expressionCreateInfos.push_back(createInfo);
                }
            }
            else
            {
                if (createInfo.hasInput)
                {
                    expressionCreateInfos.push_back(createInfo);
                }
            }
        }
        else
        {
            expressionCreateInfos.push_back(createInfo);
        }
    }

    // handle search action
    bool isInSearchMode = false;
    bool isNeedCreateSelectedExpression = false;
    {
        // focus to InputText
        if (isFirstShow)
        {
            ImGui::SetKeyboardFocusHere(0);
        }

        char buf[256];
        if (ImGui::InputTextWithHint("", "Search", buf, 256, ImGuiInputTextFlags_EnterReturnsTrue | ImGuiInputTextFlags_CallbackHistory | ImGuiInputTextFlags_CallbackEdit, _SearchInputTextCallback, this))
        {
            // create selected expression when press [Enter]
            isNeedCreateSelectedExpression = true;
        }

        // filter
        std::vector<MaterialExpressionRegistry::ExpressionCreateInfo> searchFilteredExpressionCreateInfos;
        if (m_SearchText.size() > 0)
        {
            for (auto& createInfo : expressionCreateInfos)
            {
                if (cross::ToLower(createInfo.menuName).find(m_SearchText) != std::string::npos)
                {
                    searchFilteredExpressionCreateInfos.push_back(createInfo);
                }
            }
            expressionCreateInfos = std::move(searchFilteredExpressionCreateInfos);
            isInSearchMode = true;
        }
    }

    // find best match expression
    const MaterialExpressionRegistry::ExpressionCreateInfo* expressionSelected = nullptr;
    if (isInSearchMode)
    {
        expressionSelected = FindSelectedExpressionCreateInfo(expressionCreateInfos);
    }

    // show expressions
    {
        if (isInSearchMode)
        {
            ImGui::SetNextItemOpen(true);
        }
        else if (isFirstShow)
        {
            ImGui::SetNextItemOpen(false);
        }

        if (!expressionCreateInfos.empty() && ImGui::TreeNode("Default"))
        {
            for (auto& createInfo : expressionCreateInfos)
            {
                bool isCurrentCreatorSelected = expressionSelected == &createInfo;

                if (ImGui::Selectable(createInfo.menuName.c_str(), isCurrentCreatorSelected))
                {
                    newExpression = CreateExpressionFunc(createInfo.className, createInfo.materialFunctionGuid, nodePosition.x, nodePosition.y, createInfo.createFunction);
                }
            }

            ImGui::TreePop();
        }
    }

    if (!newExpression && isNeedCreateSelectedExpression && expressionSelected)
    {
        newExpression = CreateExpressionFunc(expressionSelected->className, expressionSelected->materialFunctionGuid, nodePosition.x, nodePosition.y, expressionSelected->createFunction);
    }

    // create link
    if (newExpression)
    {
        if (pin)
        {
            auto* materialPin = Cast(pin);
            if (pin->m_Type == PinType::Output)
            {
                auto* inputPin = newExpression->GetInputPins()[0]->m_GraphPin;
                OnCreateLink(pin, inputPin);
            }
            else
            {
                // pin is input
                cross::GraphPin* srcOutputPin = pin->m_LinkedPins.empty() ? nullptr : pin->m_LinkedPins[0];
                auto* outputPin = newExpression->GetOutputPins()[0]->m_GraphPin;
                OnCreateLink(outputPin, pin);

                if (srcOutputPin && newExpression->GetInputPins().size() > 0)
                {
                    auto* inputPin = newExpression->GetInputPins()[0]->m_GraphPin;
                    OnCreateLink(srcOutputPin, inputPin);
                }
            }
        }

        ImGui::CloseCurrentPopup();
    }

    m_Editor.EndAction();
}

template<typename T>
static std::string GetTypeName()
{
    return gbf::reflection::query_meta_class<T>()->name();
}

void MaterialGraph::OnShowNodesContextMenu(std::vector<GraphNode*> nodes)
{
    auto nodePosition = ax::NodeEditor::ScreenToCanvas(ImGui::GetMousePosOnOpeningCurrentPopup());

    // Previewing Node
    if (nodes.size() == 1)
    {
        MaterialExpression* selectedExpression = Cast(nodes[0])->m_MaterialExpression;

        if (bool isSelectedExpressionCanBePreviewed = !selectedExpression->GetOutputPins().empty())
        {
            MaterialExpression* currPreviewExpression = m_Editor.GetPreviewExpression();

            if (selectedExpression != currPreviewExpression)
            {
                if (ImGui::Selectable("Start Previewing Node"))
                {
                    m_Editor.SetPreviewExpression(selectedExpression);
                }
            }
            else
            {
                if (ImGui::Selectable("Stop Previewing Node"))
                {
                    m_Editor.SetPreviewExpression(nullptr);
                }
            }
        }
    }

    // Convert To Parameter
    if (nodes.size() == 1)
    {
        static const std::unordered_map<std::string, std::pair<std::string, std::string>> ConvertTypeMap{
            {GetTypeName<MaterialExpressionConstant>(),         {GetTypeName<MaterialExpressionScalarParameter>(), "Convert To Parameter"}},
            {GetTypeName<MaterialExpressionConstant2Vector>(),  {GetTypeName<MaterialExpressionVectorParameter>(), "Convert To Parameter"}},
            {GetTypeName<MaterialExpressionConstant3Vector>(),  {GetTypeName<MaterialExpressionVectorParameter>(), "Convert To Parameter"}},
            {GetTypeName<MaterialExpressionConstant4Vector>(),  {GetTypeName<MaterialExpressionVectorParameter>(), "Convert To Parameter"}},
            {GetTypeName<MaterialExpressionTextureObject>(),    {GetTypeName<MaterialExpressionTextureParameter>(), "Convert To Parameter"}},
            {GetTypeName<MaterialExpressionRerouter>(),         {GetTypeName<MaterialExpressionNamedRerouteDeclaration>(), "Convert To NamedReroute Declaration"}},
        };

        MaterialExpression* selectedExpression = Cast(nodes[0])->m_MaterialExpression;
        auto selectedExpressionClassName = selectedExpression->__rtti_meta()->name();

        if (auto iter = ConvertTypeMap.find(selectedExpressionClassName); iter != ConvertTypeMap.end())
        {
            if (ImGui::Selectable(iter->second.second.c_str()))
            {
                m_Editor.Action_ConvertToDstExpression(iter->second.first, selectedExpression);
            }
        }
    }

    // Convert To Const
    if (nodes.size() == 1)
    {
        static const std::unordered_map<std::string, std::string> ConvertTypeMap{
            {GetTypeName<MaterialExpressionScalarParameter>(), GetTypeName<MaterialExpressionConstant>()},
            {GetTypeName<MaterialExpressionVectorParameter>(), GetTypeName<MaterialExpressionConstant4Vector>()},
            {GetTypeName<MaterialExpressionTextureParameter>(), GetTypeName<MaterialExpressionTextureObject>()},
        };

        MaterialExpression* selectedExpression = Cast(nodes[0])->m_MaterialExpression;
        auto selectedExpressionClassName = selectedExpression->__rtti_meta()->name();

        if (auto iter = ConvertTypeMap.find(selectedExpressionClassName); iter != ConvertTypeMap.end())
        {
            if (ImGui::Selectable("Convert To Constant"))
            {
                m_Editor.Action_ConvertToDstExpression(iter->second, selectedExpression);
            }
        }
    }

    ImGui::Separator();

    if (ImGui::Selectable("Delete"))
    {
        m_Editor.BeginAction();
        for (auto* node : nodes)
        {
            m_Editor.Action_DeleteExpression(Cast(node)->m_MaterialExpression);
        }
        m_Editor.EndAction();
    }
    else if (ImGui::Selectable("Cut"))
    {
        OnCut();
    }
    else if (ImGui::Selectable("Copy"))
    {
        OnCopy();
    }
}

void MaterialGraph::OnNodeDoubleClicked(GraphNode* node)
{
    MaterialExpression* expression = Cast(node)->m_MaterialExpression;
    if (auto* expressionFunctionCall = dynamic_cast<MaterialExpressionFunctionCall*>(expression); expressionFunctionCall && expressionFunctionCall->m_MaterialFunctionEditorData.MaterialFunction)
    {
        m_Editor.OpenMaterialFunction(expressionFunctionCall->m_MaterialFunction);
    }
    if (auto* expressionRerouteUsage = dynamic_cast<MaterialExpressionNamedRerouteUsage*>(expression))
    {
        m_Editor.ZoomToExpression(expressionRerouteUsage->m_Declaration);
    }
}

void MaterialGraph::OnNodesSelected(const std::vector<GraphNode*> nodes)
{
    std::vector<MaterialExpression*> expressions(nodes.size());

    std::transform(nodes.begin(), nodes.end(), expressions.begin(), [this](GraphNode* node) { return Cast(node)->m_MaterialExpression; });

    m_Editor.SelectExpressions(expressions);
}

void MaterialGraph::OnUndo()
{
    m_Editor.Undo();
}

void MaterialGraph::OnRedo()
{
    m_Editor.Redo();
}

void MaterialGraph::OnCopy()
{
    m_Editor.CopyExpressionsSelected();
}

void MaterialGraph::OnPaste(int32_t x, int32_t y)
{
    //m_Editor.Action_PasteExpressions(x, y);
}

void MaterialGraph::OnCut()
{
    OnCopy();

    m_Editor.BeginAction();
    for (auto* expression : m_Editor.GetSelectedExpressions())
    {
        m_Editor.Action_DeleteExpression(expression);
    }
    m_Editor.EndAction();
}

ExpressionCreateMenuInfo MaterialGraph::GetGraphCategories(int pinType)
{
    std::map<std::string, std::vector<ExpressionCreateNodeInfo> > expressionCategoryMap;
    for (auto& createInfo : MaterialExpressionRegistry::GetAllExpressionCreateInfos(m_Editor.GetExpressions()))
    {
        if ((pinType == -1 && !createInfo.hasOutput) || (pinType == 1 && !createInfo.hasInput))
        {
            continue;
        }

        if (m_Editor.IsEditFx())
        {
            if (createInfo.className == gbf::reflection::query_meta_class<MaterialExpressionFunctionInput>()->name() || createInfo.className == gbf::reflection::query_meta_class<MaterialExpressionFunctionOutput>()->name())
            {
                continue;
            }
        }

        auto categories = createInfo.expressionCategories;
        auto expressionClassName = createInfo.className;
        auto expressionMenuName = createInfo.menuName;
        auto expressionMaterialFunctionGuid = createInfo.materialFunctionGuid;
        auto expressionCreator = createInfo.createFunction;
        for (auto category : categories)
        {
            ExpressionCreateNodeInfo nodeInfo;
            nodeInfo.className = expressionClassName;
            nodeInfo.materialFunctionGuid = expressionMaterialFunctionGuid;
            nodeInfo.menuName = expressionMenuName;
            nodeInfo.createFunction = expressionCreator;
            expressionCategoryMap[category].emplace_back(nodeInfo);
        }
    }

    ExpressionCreateMenuInfo menuInfo;
    for (auto mapIter = expressionCategoryMap.begin(); mapIter != expressionCategoryMap.end(); mapIter++)
    {
        ExpressionCreateGroupInfo groupInfo;
        groupInfo.categoryName = mapIter->first;
        groupInfo.ExpressionInfos = mapIter->second;
        menuInfo.Groups.push_back(groupInfo);
    }
    return menuInfo;
}

void MaterialGraph::InitializeNamedReroutes()
{
    std::unordered_map<std::string, MaterialExpressionNamedRerouteDeclaration*> declarationMap;

    // Get all NamedRerouteDeclaration
    for (auto& nodePtr : m_Nodes)
    {
        MaterialExpression* expression = Cast(nodePtr.get())->m_MaterialExpression;
        if (auto* declaration = dynamic_cast <MaterialExpressionNamedRerouteDeclaration*>(expression))
        {
            declarationMap[declaration->m_VariableGuid] = declaration;
        }
    }

    for (auto& nodePtr : m_Nodes)
    {
        MaterialExpression* expression = Cast(nodePtr.get())->m_MaterialExpression;
        if (auto* usage = dynamic_cast<MaterialExpressionNamedRerouteUsage*>(expression))
        {
            auto it = declarationMap.find(usage->m_DeclarationGuid);
            if (it != declarationMap.end())
            {
                usage->m_Declaration = it->second;
            }
            else
            {
                usage->m_Declaration = nullptr;
                LOG_ERROR("NamedRerouteUsage with GUID {} has no matching Declaration.", usage->m_DeclarationGuid);
            }
        }
    }
    
    for (auto& nodePtr : m_Nodes)
    {
        MaterialExpression* expression = Cast(nodePtr.get())->m_MaterialExpression;
        if (auto* functionCall = dynamic_cast<MaterialExpressionFunctionCall*>(expression))
        {
            functionCall->InitializeNamedReroutes();
        }
    }
}

void MaterialGraph::CreateNewNode(std::string expressionClassName, std::string materialFunctionGuid, int position_x, int position_y, GraphPin* pin, ExpressionCreateNodeInfo* createInfo)
{
    auto CreateExpressionFunc = [&](std::string className, std::string materialFunction, int32_t x, int32_t y, std::function<std::shared_ptr<MaterialExpression>()> createFunction) {
        if (createInfo && createInfo->createFunction)
        {
            return m_Editor.Action_CreateExpression(className, x, y, createInfo->createFunction);
        }
        else
        {
            if (className == gbf::reflection::query_meta_class<MaterialExpressionFunctionCall>()->name())
            {
                return m_Editor.Action_CreateExpressionFunctionCall(materialFunction, x, y);
            }
            else
            {
                return m_Editor.Action_CreateExpression(className, x, y);
            }
        }
    };
    auto nodePosition = ax::NodeEditor::ScreenToCanvas(ImVec2{(float)position_x, (float)position_y});
    auto createFunction = createInfo ? createInfo->createFunction : nullptr;
    MaterialExpression* newExpression = CreateExpressionFunc(expressionClassName, materialFunctionGuid, nodePosition.x, nodePosition.y, createFunction);
    if (newExpression)
    {
        if (pin)
        {
            auto* materialPin = Cast(pin);
            if (pin->m_Type == PinType::Output && !newExpression->GetInputPins().empty())
            {
                // HACK...
                auto* pinExpression = Cast(pin->m_Node)->m_MaterialExpression;
                GraphPin* inputPin = nullptr;

                if ((dynamic_cast<MaterialExpressionTextureObject*>(pinExpression) || dynamic_cast<MaterialExpressionTextureParameter*>(pinExpression) || dynamic_cast<MaterialExpressionTextureSampleParameter*>(pinExpression)) &&
                    dynamic_cast<MaterialExpressionTextureSample*>(newExpression))
                {
                    inputPin = newExpression->GetInputPins()[1]->m_GraphPin;
                }
                else
                {
                    inputPin = newExpression->GetInputPins()[0]->m_GraphPin;
                }

                OnCreateLink(pin, inputPin);
            }
            else if (pin->m_Type == PinType::Input && !newExpression->GetOutputPins().empty())
            {
                // pin is input
                cross::GraphPin* srcOutputPin = nullptr;
                if (!pin->m_LinkedPins.empty())
                {
                    srcOutputPin = pin->m_LinkedPins[0];
                }
                auto* outputPin = newExpression->GetOutputPins()[0]->m_GraphPin;
                OnCreateLink(outputPin, pin);

                if (srcOutputPin && newExpression->GetInputPins().size() > 0)
                {
                    auto* inputPin = newExpression->GetInputPins()[0]->m_GraphPin;
                    OnCreateLink(srcOutputPin, inputPin);
                }
            }
        }
    }

    CloseCurrentDialog();
}

}   // namespace cross
