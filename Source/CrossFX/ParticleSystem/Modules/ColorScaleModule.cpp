#include "ColorScaleModule.h"
#include "ModulePipeline.h"
#include "../ParticleSystemUtils.h"

namespace cross::fx {

void ColorScaleModule::Update(ModulePipelineContext& context)
{
    QUICK_SCOPED_CPU_TIMING("ColorScaleModule");
    const UInt32 particleCount = static_cast<UInt32>(context.ParticleStates.Size());
    ColorRGBAf* colorScale = context.ParticleParams->GetParameterAddr<ColorRGBAf>(ParticleVariable(RendererProp::COLOR, ParticleDataType::Float));
    Float3* position = context.ParticleParams->GetParameterAddr<Float3>(ParticleVariable(RendererProp::POSITION, ParticleDataType::Float3));
    for (auto particleIndex = 0u; particleIndex < particleCount; ++particleIndex)
    {
        const float particleAge = context.ParticleStates.GetNormalizedAge(particleIndex);
        Rand random(context.ParticleStates.GetRandomSeed(particleIndex));
        colorScale[particleIndex] = context.ParticleStates.startColor[particleIndex] * mColorScale->ColorScale.Evaluate(particleAge, random);
        if (mColorScale->ScaleWithCamera)
        {
            const Float3& particlePosition = context.LocalSpace ? ParticleSystemUtils::TransformDoublePoint(context.WorldMatrix, position[particleIndex]) : position[particleIndex];
            float scale = ParticleSystemUtils::GetScaleByDistance(particlePosition, context.BaseTile, context.CameraPosition, context.CameraTile, mColorScale->CameraRemapScale);
            if ((mColorScale->ScaleChannel & ScaleColorChannel::SCALE_CHANNEL_R) != ScaleColorChannel::None)
            {
                colorScale[particleIndex].r *= scale;
            }
            if ((mColorScale->ScaleChannel & ScaleColorChannel::SCALE_CHANNEL_G) != ScaleColorChannel::None)
            {
                colorScale[particleIndex].g *= scale;
            }
            if ((mColorScale->ScaleChannel & ScaleColorChannel::SCALE_CHANNEL_B) != ScaleColorChannel::None)
            {
                colorScale[particleIndex].b *= scale;
            }
            if ((mColorScale->ScaleChannel & ScaleColorChannel::SCALE_CHANNEL_A) != ScaleColorChannel::None)
            {
                colorScale[particleIndex].a *= scale;
            }
        }
    }
}

void ColorScaleModule::Flush(const ParticleEmitterInfo& emitterInfo)
{
    mColorScale = std::make_shared<ColorScaleInfo>(emitterInfo.ColorScale);
    mEnabled = mColorScale->Enabled;
}

size_t ColorScaleModule::Transfer(const ParticleEmitterInfo& emitterInfo, ParticleCurvePool& curvePool, size_t offset, std::vector<UInt8>& output)
{
    Float3 padding;
    offset += TransferProperty(mColorScale->ColorScale, curvePool, output, offset);
    offset += TransferProperty(mColorScale->ScaleWithCamera, curvePool, output, offset);
    offset += TransferProperty(padding, curvePool, output, offset);
    // Transfer enum mask to float4 for compute shader used.
    {
        Float4 scaleChannel;
        scaleChannel.x = (mColorScale->ScaleChannel & ScaleColorChannel::SCALE_CHANNEL_R) != ScaleColorChannel::None ? 1.0f : 0.0f;
        scaleChannel.y = (mColorScale->ScaleChannel & ScaleColorChannel::SCALE_CHANNEL_G) != ScaleColorChannel::None ? 1.0f : 0.0f;
        scaleChannel.z = (mColorScale->ScaleChannel & ScaleColorChannel::SCALE_CHANNEL_B) != ScaleColorChannel::None ? 1.0f : 0.0f;
        scaleChannel.w = (mColorScale->ScaleChannel & ScaleColorChannel::SCALE_CHANNEL_A) != ScaleColorChannel::None ? 1.0f : 0.0f;
        offset += TransferProperty(scaleChannel, curvePool, output, offset);
    }
    offset += TransferProperty(mColorScale->CameraRemapScale, curvePool, output, offset);
    return offset;
}

}   // namespace cross::fx