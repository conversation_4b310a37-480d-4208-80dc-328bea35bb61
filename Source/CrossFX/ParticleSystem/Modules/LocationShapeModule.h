#pragma once
#include "ModuleBase.h"
#include "ModulePipeline.h"
#include "../ParticleAttributeNames.h"
#include "../ParticleEmitMesh.h"
#include "Resource/ParticleSystem/ParticleEmitterInfo.h"

namespace cross::fx {

class LocationShapeModule : public ModuleBase
{
public:
    friend class ParticleEmitterResource;

public:
    LocationShapeModule(ModuleScope scope, ModuleIndex index, ParticleEmitterResPtr res)
        : ModuleBase(scope, index)
    {
        mLocationShape = std::make_shared<LocationShapeInfo>(res->GetLocationShape());
        mEnabled = mLocationShape->Enabled;
        __PARTICLE_CURVE_INIT(mLocationShape);
    };

    LocationShapeModule(ModuleScope scope, ModuleIndex index, const ParticleEmitterInfo& src)
        : ModuleBase(scope, index)
    {
        mLocationShape = std::make_shared<LocationShapeInfo>(src.LocationShape);
        mEnabled = mLocationShape->Enabled;
        __PARTICLE_CURVE_INIT(mLocationShape);
    };

    void Update(ModulePipelineContext& context) override;

    void Flush(const ParticleEmitterInfo& emitterInfo) override;

    size_t Transfer(const ParticleEmitterInfo& emitterInfo, ParticleCurvePool& curvePool, size_t offset, std::vector<UInt8>& output) override;

    void UpdateInplace(ParticleCurvePool& curvePool, std::vector<UInt8>& output) override;

    const ModuleInfoPtr GetModuleInfo() const override { return mLocationShape; };

    void ResetSeed(UInt32 randomSeed) override;

    inline void ClearSampleMesh() { mSampleMeshes.clear(); }
    inline void AddSampleMesh(const ParticleEmitMesh& mesh) { mSampleMeshes.emplace_back(mesh); }
    inline UInt32 GetSampleLodIndex() const { return mLocationShape->LodIndex; }

private:
    float GetEmissionArc(float radius, float emissionFraction, float elapsedTime);
    float GetPingPongArc(float deltaArc);
    float GetLoopArc(float deltaArc);

    void EmitFromBox(ModulePipelineContext& context);
    void EmitFromSphere(ModulePipelineContext& context);
    void EmitFromCone(ModulePipelineContext& context);
    void EmitFromCircle(ModulePipelineContext& context);
    void EmitFromMesh(ModulePipelineContext& context);

    /* Apply particle's origin velocity with world matrix which was not local space and start speed. */
    void TransformParticleSpeed(const ModulePipelineContext& context, const Float3& input, UInt32 particleIndex, Float3* outputAddr);
    void UpdateTilePosition(ModulePipelineContext& context, UInt32 particleIndex);
 
private:
    float mLastRadian = 0.0f;
    float mRemainderArc = 0.0f;
    float mSign = 1.0f;
    float mSpreadBursted = false;
    bool mFirstSpread = true;
    float mArcGap = 0.0f;
    float mMaxArcRadian = 0.0f;
    float mLastArc = 0.0f;

private:
    std::shared_ptr<LocationShapeInfo> mLocationShape;
    std::vector<ParticleEmitMesh> mSampleMeshes;
    Rand mRandom;
};

}   // namespace cross::fx