#pragma once
#include <vector>
#include <map>
#include <string>
#include "ParticleMemoryStrategy.h"
#include "CrossBase/Log.h"

namespace cross::fx
{

using ParticleOffsetBind = std::map<ParticleVariable, SInt32>;

struct ParticleID
{
    UInt32 ID;
    UInt32 SlotIndex;

    ParticleID() = default;

    ParticleID(UInt32 id, UInt32 slot) : ID(id), SlotIndex(slot) {}

    bool operator<(const ParticleID& Other) const
    {
        return ID < Other.ID;
    }

    bool operator==(const ParticleID& Other) const
    {
        return ID == Other.ID && SlotIndex == Other.SlotIndex;
    }
};

struct ParticleParamBindings
{
    friend struct ParticleParameterStore;
    bool Find(const ParticleVariable& param) const { return mParamOffsets.find(param) != mParamOffsets.end(); }

    void Add(const ParticleVariable& param)
    {
        mParamOffsets.try_emplace(param, mParamSize);
        mParamSize += static_cast<UInt32>(param.ParamType);
    }

    void Reset()
    {
        mParamOffsets.clear();
        mParamSize = 0;
    }

    UInt32 GetOffset(const ParticleVariable& param) const
    {
        auto result = mParamOffsets.find(param);
        return (result == mParamOffsets.end()) ? 0u : result->second;
    }

    inline UInt32 GetParamSize() const { return mParamSize; }

protected:
    ParticleOffsetBind mParamOffsets;
    UInt32             mParamSize{0u};
};

struct ParticleParameterStore
{
public:
    ParticleParameterStore(UInt32 count = 0);
    ~ParticleParameterStore() = default;

    void Reset();

    void BindParameter(const ParticleVariable& param);

    std::optional<UInt32> GetParameterOffset(const ParticleVariable& param) const;

    std::vector<UInt32> AddParticles(UInt32 count);

    void MarkDeath(ParticleID particle);

    inline void ClearData() 
    { 
        mStore.clear(); 
        mDeadSlots.clear();
    }

    inline UInt32 GetAvailableCount() const 
    { 
        UInt32 totalCnt = GetStoreSize() / GetParticleSize();
        return totalCnt - static_cast<UInt32>(mDeadSlots.size());
    }

    inline UInt32 GetStoreSize() const { return static_cast<UInt32>(mStore.size()); }

    inline UInt32 GetParticleSize() const { return mParamBindings.GetParamSize(); }

    const ParticleOffsetBind& GetParamOffsets() const { return mParamBindings.mParamOffsets; }

    const UInt8* GetData() const { return mStore.data(); }

    const std::map<ParticleID, UInt32>& GetDeadSlots() const { return mDeadSlots; }

    template<typename T>
    T* GetData(UInt32 offset)
    {
        Assert(mStore.size() > offset);
        return reinterpret_cast<T*>(mStore.data() + offset);
    }

    template<typename T>
    const T* GetData(UInt32 offset) const
    {
        Assert(mStore.size() > offset);
        return reinterpret_cast<const T*>(mStore.data() + offset);
    }

    template<typename T>
    T* GetParameterAddr(const ParticleVariable& param)
    {
        if (auto paramOffset = GetParameterOffset(param); paramOffset != std::nullopt)
        {
            UInt32 count = GetStoreSize() / GetParticleSize();
            return GetData<T>(paramOffset.value() * count);
        }
        //AssertMsg(false, "GetParameterAddr fail : {}", param.Name.GetName());
        return nullptr;
    }

private:
    ParticleStore mStore;
    std::map<ParticleID, UInt32> mDeadSlots;
    ParticleParamBindings mParamBindings;
    ParticleCopyTempStore mCopyTemp;
};

}   // namespace cross::fx