/*
 * <PERSON><PERSON> is pleased to support the open source community by making <PERSON>uer<PERSON> available.
 * Copyright (C) 2020 THL A29 Limited, a Tencent company.  All rights reserved.
 * Puerts is licensed under the BSD 3-Clause License, except for the third-party components listed in the file 'LICENSE' which may
 * be subject to their corresponding license terms. This file is subject to the terms and conditions defined in file 'LICENSE',
 * which is part of this source code package.
 */

#pragma once

#include "functional"

#if USING_IN_UNREAL_ENGINE
#include "CoreMinimal.h"
#else
#include "CoreMinimal.h"
#ifndef JSENV_API
    #define JSENV_API
#endif
#define PUERTS_FORCEINLINE V8_INLINE
#define UPTRINT uintptr_t
#endif

#include <string>

#pragma warning(push, 0)
#include "v8.h"
#pragma warning(pop)

#include "NamespaceDef.h"

#include "TypeInfo.hpp"

namespace PUERTS_NAMESPACE
{
class CFunctionInfo;
struct JSENV_API JSFunctionInfo
{
    const char* Name;
    v8::FunctionCallback Callback;
    void* Data = nullptr;
    const CFunctionInfo* ReflectionInfo = nullptr;
};

struct JSENV_API JSPropertyInfo
{
    const char* Name;
    v8::FunctionCallback Getter;
    v8::FunctionCallback Setter;
    void* Data = nullptr;
};

typedef void (*FinalizeFunc)(void* Ptr);

typedef void* (*InitializeFunc)(const v8::FunctionCallbackInfo<v8::Value>& Info);

struct NamedFunctionInfo;
struct NamedPropertyInfo;

struct JSENV_API JSClassDefinition
{
    RegisterType ClassRegisterType;
    const void* TypeId;
    const void* SuperTypeId;
    const char* ScriptName;
    const char* UETypeName;
    InitializeFunc Initialize;
    JSFunctionInfo* Methods;       //成员方法
    JSFunctionInfo* Functions;     //静态方法
    JSPropertyInfo* Properties;    //成员属性
    JSPropertyInfo* Variables;     //静态属性
    FinalizeFunc Finalize;
    // int InternalFieldCount;
    NamedFunctionInfo* ConstructorInfos;
    NamedFunctionInfo* MethodInfos;
    NamedFunctionInfo* FunctionInfos;
    NamedPropertyInfo* PropertyInfos;
    NamedPropertyInfo* VariableInfos;
    void* Data = nullptr;
};

#define JSClassEmptyDefinition                      \
    {                                               \
       ::PUERTS_NAMESPACE::RegisterType::Core, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 \
    }
}    // namespace PUERTS_NAMESPACE

#ifdef __cplusplus
extern "C" {
#endif
void JSENV_API RegisterJSClass(const puerts::JSClassDefinition& ClassDefinition);

void JSENV_API SetClassTypeInfo(const void* TypeId, const puerts::NamedFunctionInfo* ConstructorInfos, const puerts::NamedFunctionInfo* MethodInfos,
    const puerts::NamedFunctionInfo* FunctionInfos, const puerts::NamedPropertyInfo* PropertyInfos, const puerts::NamedPropertyInfo* VariableInfos);

void JSENV_API ForeachRegisterClass(std::function<void(const puerts::JSClassDefinition* ClassDefinition)>);

#if CROSS_ENGINE_ADD_ON
typedef const PUERTS_NAMESPACE::JSClassDefinition* (*PFN_FindClassByID)(const void* TypeId);
extern PFN_FindClassByID FindClassByID;
typedef const PUERTS_NAMESPACE::JSClassDefinition* (*PFN_FindCppTypeClassByName)(const char* Name);
extern PFN_FindCppTypeClassByName FindCppTypeClassByName;
#else
JSENV_API const puerts::JSClassDefinition* FindClassByID(const void* TypeId);

JSENV_API const puerts::JSClassDefinition* FindCppTypeClassByName(const char* Name);
#endif

typedef void (*AddonRegisterFunc)(v8::Local<v8::Context> Context, v8::Local<v8::Object> Exports);

AddonRegisterFunc FindAddonRegisterFunc(const char* Name);

void RegisterAddon(const char* Name, AddonRegisterFunc RegisterFunc);

#if USING_IN_UNREAL_ENGINE
JSENV_API const JSClassDefinition* FindClassByType(UStruct* Type);
#endif
#ifdef __cplusplus
}
#endif

#define PUERTS_MODULE(Name, RegFunc)                           \
    static struct FAutoRegisterFor##Name                       \
    {                                                          \
        FAutoRegisterFor##Name()                               \
        {                                                      \
            PUERTS_NAMESPACE::RegisterAddon(#Name, (RegFunc)); \
        }                                                      \
    } _AutoRegisterFor##Name
