/*
 * <PERSON><PERSON> is pleased to support the open source community by making <PERSON>uer<PERSON> available.
 * Copyright (C) 2020 THL A29 Limited, a Tencent company.  All rights reserved.
 * Puerts is licensed under the BSD 3-Clause License, except for the third-party components listed in the file 'LICENSE' which may
 * be subject to their corresponding license terms. This file is subject to the terms and conditions defined in file 'LICENSE',
 * which is part of this source code package.
 */

#include "CppObjectMapper.h"
#include "DataTransfer.h"
#include "JsEnumRegister.h"
#include "V8UtilsUser.h"
#include <ranges>
#include <string_view>
//#include <tracy/Tracy.hpp>

//#include "Profiling/Profiling.h"

namespace PUERTS_NAMESPACE
{
static void ThrowException(v8::Isolate* Isolate, const char* Message)
{
    auto ExceptionStr = v8::String::NewFromUtf8(Isolate, Message, v8::NewStringType::kNormal).ToLocalChecked();
    Isolate->ThrowException(v8::Exception::Error(ExceptionStr));
}

void FCppObjectMapper::LoadCppType(const v8::FunctionCallbackInfo<v8::Value>& Info)
{
    v8::Isolate* Isolate = Info.GetIsolate();
    v8::Isolate::Scope IsolateScope(Isolate);
    v8::HandleScope HandleScope(Isolate);
    v8::Local<v8::Context> Context = Isolate->GetCurrentContext();
    v8::Context::Scope ContextScope(Context);

    if (!Info[0]->IsString())
    {
        ThrowException(Isolate, "#0 argument expect a string");
        return;
    }

    std::string TypeName = *(v8::String::Utf8Value(Isolate, Info[0]));


    // cpp class
    {
        auto ClassDef = FindCppTypeClassByName(TypeName.c_str());
        if (ClassDef)
        {
            Info.GetReturnValue().Set(GetTemplateOfClass(Isolate, ClassDef)->GetFunction(Context).ToLocalChecked());
            return;
        }
    }
    

    auto EnumDefToV8Object = [Isolate, &Context](const JSEnumDefinition* EnumDef) {
        auto Result = v8::Object::New(Isolate);

        auto EnumFiled = EnumDef->EnumFileds;
        while (EnumFiled && EnumFiled->Name)
        {
            // js number is int32 
            Assert(static_cast<int64_t>(-std::numeric_limits<int32_t>::max()) <= EnumFiled->value && EnumFiled->value <= static_cast<int64_t>(std::numeric_limits<int32_t>::max()));
            Result->Set(Context, FV8Utils::ToV8String(Isolate, EnumFiled->Name), v8::Int32::New(Isolate, static_cast<int32_t>(EnumFiled->value)));
            Result->Set(Context, v8::Int32::New(Isolate, static_cast<int32_t>(EnumFiled->value)), FV8Utils::ToV8String(Isolate, EnumFiled->Name));
            ++EnumFiled;
        }
        return Result;
    };

    // enum without namespace
    {
        auto EnumDef = FindCppTypeEnumByName(TypeName.c_str());
        if (EnumDef)
        {
            auto Result = EnumDefToV8Object(EnumDef);

            Info.GetReturnValue().Set(Result);
            return;
        }
    }
    
    // enum with namespace
    {
        auto EnumDefArraySize = FindCppTypeEnumByNamespace(TypeName.c_str(), nullptr, 0);
        if (EnumDefArraySize != 0)
        {
            std::vector<const JSEnumDefinition*> EnumDefArray(EnumDefArraySize);
            FindCppTypeEnumByNamespace(TypeName.c_str(), EnumDefArray.data(), EnumDefArray.size());

            // sort by namespace
            // cross.a
            // cross.z
            // cross.ce.A
            // cross.ce.B
            // cross.gg.B
            // cross.gg.C
            std::sort(EnumDefArray.begin(), EnumDefArray.end(), [](const JSEnumDefinition* left, const JSEnumDefinition* right)->bool
                {
                    auto left_split_view = std::string_view(left->EnumName) | std::views::split('.');
                    auto right_split_view = std::string_view(right->EnumName) | std::views::split('.');
                    auto left_it = left_split_view.begin();
                    auto right_it = right_split_view.begin();
                    while (std::string_view((*left_it).data(), (*left_it).size()) == std::string_view((*right_it).data(), (*right_it).size()))
                    {
                        ++left_it;
                        ++right_it;
                    }

                    //  return left first
                    if ((*left_it).empty()) return true;
                    //  return right first
                    if ((*right_it).empty()) return false;

                    // left is enum return left first
                    if (std::next(left_it) == std::ranges::end(left_split_view) && std::next(right_it) != std::ranges::end(right_split_view))
                        return true;

                    // right is enum return left first
                    if (std::next(left_it) != std::ranges::end(left_split_view) && std::next(right_it) == std::ranges::end(right_split_view))
                        return false;

                    // default, don't care left is same right
                    return std::string_view((*left_it).data(), (*left_it).size()) < std::string_view((*right_it).data(), (*right_it).size());
                });
            auto Result = v8::Object::New(Isolate);
            std::string_view currentNamespace = "";
            for (auto enumDef : EnumDefArray)
            {
                auto enumNameSplitView = std::string_view(enumDef->EnumName) | std::views::split('.');
                v8::Local<v8::Object> Current = Result;
                for (auto enumNameSpaceIt = enumNameSplitView.begin(); enumNameSpaceIt != enumNameSplitView.end(); ++enumNameSpaceIt)
                {
                    // first name space is return v8 object key
                    if (enumNameSpaceIt == enumNameSplitView.begin()) continue;

                    // enum name
                    if (std::next(enumNameSpaceIt) == enumNameSplitView.end())
                    {
                        std::string enumName((*enumNameSpaceIt).data(), (*enumNameSpaceIt).size());
                        auto v8enumName = FV8Utils::ToV8String(Isolate, enumName.data());
                        // other enum namespace can't be enum name
                        auto MaybeEnum = Current->Get(Context, v8enumName);
                        Assert(MaybeEnum.IsEmpty() || MaybeEnum.ToLocalChecked()->IsUndefined());
                        Current->Set(Context, v8enumName, EnumDefToV8Object(enumDef));
                    }
                    else
                    {
                        // string_view don't has \0
                        std::string Namespace((*enumNameSpaceIt).data(), (*enumNameSpaceIt).size());
                        auto v8Namespace = FV8Utils::ToV8String(Isolate, Namespace.data());
                        v8::MaybeLocal<v8::Value> MaybeNamespaceRet = Current->Get(Context, v8Namespace);
                        fmt::print("{} {} {} {}", Namespace,
                            MaybeNamespaceRet.IsEmpty(),
                            MaybeNamespaceRet.ToLocalChecked().IsEmpty(),
                            MaybeNamespaceRet.ToLocalChecked()->IsUndefined());
                        if (MaybeNamespaceRet.IsEmpty() || MaybeNamespaceRet.ToLocalChecked()->IsUndefined())
                        {
                            auto newCurrent = v8::Object::New(Isolate);
                            Current->Set(Context, v8Namespace, newCurrent);
                            Current = newCurrent;
                        }
                        else
                        {
                            Assert(MaybeNamespaceRet.ToLocalChecked()->IsObject());
                            Current = MaybeNamespaceRet.ToLocalChecked().As<v8::Object>();
                        }
                    }
                }
            }
            Info.GetReturnValue().Set(Result);
            return;
        }
    }

    const std::string ErrMsg = "can not find type: " + TypeName;
    ThrowException(Isolate, ErrMsg.c_str());
}

static void PointerNew(const v8::FunctionCallbackInfo<v8::Value>& Info)
{
    // do nothing
}

void FCppObjectMapper::Initialize(v8::Isolate* InIsolate, v8::Local<v8::Context> InContext)
{
    auto LocalTemplate = v8::FunctionTemplate::New(InIsolate, PointerNew);
    LocalTemplate->InstanceTemplate()->SetInternalFieldCount(4);    // 0 Ptr, 1, CDataName
    PointerConstructor = v8::UniquePersistent<v8::Function>(InIsolate, LocalTemplate->GetFunction(InContext).ToLocalChecked());
}

v8::Local<v8::Value> FCppObjectMapper::FindOrAddCppObject(
    v8::Isolate* Isolate, v8::Local<v8::Context>& Context, const void* TypeId, void* Ptr, bool PassByPointer)
{
    if (Ptr == nullptr)
    {
        return v8::Undefined(Isolate);
    }

    if (PassByPointer)
    {
        auto Iter = CDataCache.find(Ptr);
        if (Iter != CDataCache.end())
        {
            auto CacheNodePtr = Iter->second.Find(TypeId);
            if (CacheNodePtr)
            {
                return CacheNodePtr->Value.Get(Isolate);
            }
        }
    }

    // create and link
    auto ClassDefinition = FindClassByID(TypeId);
    if (ClassDefinition)
    {
        auto Result = GetTemplateOfClass(Isolate, ClassDefinition)->InstanceTemplate()->NewInstance(Context).ToLocalChecked();
        BindCppObject(Isolate, const_cast<JSClassDefinition*>(ClassDefinition), Ptr, Result, PassByPointer);
        return Result;
    }
    else
    {
        assert(false);
        auto Result = PointerConstructor.Get(Isolate)->NewInstance(Context, 0, nullptr).ToLocalChecked();
        DataTransfer::SetPointer(Isolate, Result, Ptr, 0);
        DataTransfer::SetPointer(Isolate, Result, TypeId, 1);
        return v8::Undefined(Context->GetIsolate());
    }
}

bool FCppObjectMapper::IsInstanceOfCppObject(v8::Isolate* Isolate, const void* TypeId, v8::Local<v8::Object> JsObject)
{
    if (!JsObject->IsObject())
    {
        return false;
    }

    if (DataTransfer::GetPointerFast<const void>(JsObject, 1) == TypeId)
    {
        return true;
    }
    auto ClassDefinition = FindClassByID(TypeId);
    if (ClassDefinition)
    {
        auto Template = GetTemplateOfClass(Isolate, ClassDefinition);
        return Template->HasInstance(JsObject);
    }
    return false;
}

std::weak_ptr<int> FCppObjectMapper::GetJsEnvLifeCycleTracker()
{
    return std::weak_ptr<int>(Ref);
}

static void CDataNew(const v8::FunctionCallbackInfo<v8::Value>& Info)
{
    v8::Isolate* Isolate = Info.GetIsolate();
    v8::Isolate::Scope IsolateScope(Isolate);
    v8::HandleScope HandleScope(Isolate);
    v8::Local<v8::Context> Context = Isolate->GetCurrentContext();
    v8::Context::Scope ContextScope(Context);

    if (Info.IsConstructCall())
    {
        auto Self = Info.This();
        JSClassDefinition* ClassDefinition =
            reinterpret_cast<JSClassDefinition*>((v8::Local<v8::External>::Cast(Info.Data()))->Value());
        void* Ptr = nullptr;

        if (ClassDefinition->Initialize)
            Ptr = ClassDefinition->Initialize(Info);
        if (Ptr == nullptr)
            return;

        DataTransfer::IsolateData<ICppObjectMapper>(Isolate)->BindCppObject(Isolate, ClassDefinition, Ptr, Self, false);
    }
    else
    {
        ThrowException(Isolate, "only call as Construct is supported!");
    }
}

v8::Local<v8::FunctionTemplate> FCppObjectMapper::GetTemplateOfClass(v8::Isolate* Isolate, const JSClassDefinition* ClassDefinition)
{
    auto Iter = CDataNameToTemplateMap.find(ClassDefinition->TypeId);
    if (Iter == CDataNameToTemplateMap.end())
    {
        auto Template = v8::FunctionTemplate::New(
            Isolate, CDataNew, v8::External::New(Isolate, const_cast<void*>(reinterpret_cast<const void*>(ClassDefinition))));
        Template->InstanceTemplate()->SetInternalFieldCount(4);

        JSPropertyInfo* PropertyInfo = ClassDefinition->Properties;
        while (PropertyInfo && PropertyInfo->Name && PropertyInfo->Getter)
        {
            v8::PropertyAttribute PropertyAttribute = v8::DontDelete;
            if (!PropertyInfo->Setter)
                PropertyAttribute = (v8::PropertyAttribute)(PropertyAttribute | v8::ReadOnly);
            auto Data = PropertyInfo->Data ? static_cast<v8::Local<v8::Value>>(v8::External::New(Isolate, PropertyInfo->Data))
                                           : v8::Local<v8::Value>();
            Template->PrototypeTemplate()->SetAccessorProperty(
                v8::String::NewFromUtf8(Isolate, PropertyInfo->Name, v8::NewStringType::kNormal).ToLocalChecked(),
                PropertyInfo->Getter ? v8::FunctionTemplate::New(Isolate, PropertyInfo->Getter, Data)
                                     : v8::Local<v8::FunctionTemplate>(),
                PropertyInfo->Setter ? v8::FunctionTemplate::New(Isolate, PropertyInfo->Setter, Data)
                                     : v8::Local<v8::FunctionTemplate>(),
                PropertyAttribute);
            ++PropertyInfo;
        }

        PropertyInfo = ClassDefinition->Variables;
        while (PropertyInfo && PropertyInfo->Name && PropertyInfo->Getter)
        {
            v8::PropertyAttribute PropertyAttribute = v8::DontDelete;
            if (!PropertyInfo->Setter)
                PropertyAttribute = (v8::PropertyAttribute)(PropertyAttribute | v8::ReadOnly);
            auto Data = PropertyInfo->Data ? static_cast<v8::Local<v8::Value>>(v8::External::New(Isolate, PropertyInfo->Data))
                                           : v8::Local<v8::Value>();
            Template->SetAccessorProperty(
                v8::String::NewFromUtf8(Isolate, PropertyInfo->Name, v8::NewStringType::kNormal).ToLocalChecked(),
                PropertyInfo->Getter ? v8::FunctionTemplate::New(Isolate, PropertyInfo->Getter, Data)
                                     : v8::Local<v8::FunctionTemplate>(),
                PropertyInfo->Setter ? v8::FunctionTemplate::New(Isolate, PropertyInfo->Setter, Data)
                                     : v8::Local<v8::FunctionTemplate>(),
                PropertyAttribute);
            ++PropertyInfo;
        }

        JSFunctionInfo* FunctionInfo = ClassDefinition->Methods;
        while (FunctionInfo && FunctionInfo->Name && FunctionInfo->Callback)
        {
#ifndef WITH_QUICKJS
            auto FastCallInfo = FunctionInfo->ReflectionInfo ? FunctionInfo->ReflectionInfo->FastCallInfo() : nullptr;
            if (FastCallInfo)
            {
                Template->PrototypeTemplate()->Set(
                    v8::String::NewFromUtf8(Isolate, FunctionInfo->Name, v8::NewStringType::kNormal).ToLocalChecked(),
                    v8::FunctionTemplate::New(Isolate, FunctionInfo->Callback,
                        FunctionInfo->Data ? static_cast<v8::Local<v8::Value>>(v8::External::New(Isolate, FunctionInfo->Data))
                                           : v8::Local<v8::Value>(),
                        v8::Local<v8::Signature>(), 0, v8::ConstructorBehavior::kThrow, v8::SideEffectType::kHasSideEffect,
                        FastCallInfo));
            }
            else
#endif
            {
                Template->PrototypeTemplate()->Set(
                    v8::String::NewFromUtf8(Isolate, FunctionInfo->Name, v8::NewStringType::kNormal).ToLocalChecked(),
                    v8::FunctionTemplate::New(Isolate, FunctionInfo->Callback,
                        FunctionInfo->Data ? static_cast<v8::Local<v8::Value>>(v8::External::New(Isolate, FunctionInfo->Data))
                                           : v8::Local<v8::Value>()));
            }
            ++FunctionInfo;
        }
        FunctionInfo = ClassDefinition->Functions;
        while (FunctionInfo && FunctionInfo->Name && FunctionInfo->Callback)
        {
#ifndef WITH_QUICKJS
            auto FastCallInfo = FunctionInfo->ReflectionInfo ? FunctionInfo->ReflectionInfo->FastCallInfo() : nullptr;
            if (FastCallInfo)
            {
                Template->Set(v8::String::NewFromUtf8(Isolate, FunctionInfo->Name, v8::NewStringType::kNormal).ToLocalChecked(),
                    v8::FunctionTemplate::New(Isolate, FunctionInfo->Callback,
                        FunctionInfo->Data ? static_cast<v8::Local<v8::Value>>(v8::External::New(Isolate, FunctionInfo->Data))
                                           : v8::Local<v8::Value>(),
                        v8::Local<v8::Signature>(), 0, v8::ConstructorBehavior::kThrow, v8::SideEffectType::kHasSideEffect,
                        FastCallInfo));
            }
            else
#endif
            {
                Template->Set(v8::String::NewFromUtf8(Isolate, FunctionInfo->Name, v8::NewStringType::kNormal).ToLocalChecked(),
                    v8::FunctionTemplate::New(Isolate, FunctionInfo->Callback,
                        FunctionInfo->Data ? static_cast<v8::Local<v8::Value>>(v8::External::New(Isolate, FunctionInfo->Data))
                                           : v8::Local<v8::Value>()));
            }
            ++FunctionInfo;
        }

        if (ClassDefinition->SuperTypeId)
        {
            if (auto SuperDefinition = FindClassByID(ClassDefinition->SuperTypeId))
            {
                Template->Inherit(GetTemplateOfClass(Isolate, SuperDefinition));
            }
        }

        Template->SetClassName(FV8Utils::ToV8String(Isolate, ClassDefinition->ScriptName));

        CDataNameToTemplateMap[ClassDefinition->TypeId] = v8::UniquePersistent<v8::FunctionTemplate>(Isolate, Template);

        return Template;
    }
    else
    {
        return v8::Local<v8::FunctionTemplate>::New(Isolate, Iter->second);
    }
}

static void CDataGarbageCollectedWithFree(const v8::WeakCallbackInfo<JSClassDefinition>& Data)
{
    //QUICK_SCOPED_CPU_TIMING_DYNAMIC(std::string(Data.GetParameter()->ScriptName));
    JSClassDefinition* ClassDefinition = Data.GetParameter();
    void* Ptr = DataTransfer::MakeAddressWithHighPartOfTwo(Data.GetInternalField(0), Data.GetInternalField(1));
    if (ClassDefinition->Finalize)
        ClassDefinition->Finalize(Ptr);
    DataTransfer::IsolateData<ICppObjectMapper>(Data.GetIsolate())->UnBindCppObject(ClassDefinition, Ptr);
}

static void CDataGarbageCollectedWithoutFree(const v8::WeakCallbackInfo<JSClassDefinition>& Data)
{
    //QUICK_SCOPED_CPU_TIMING("CDataGarbageCollectedWithoutFree");
    JSClassDefinition* ClassDefinition = Data.GetParameter();
    void* Ptr = DataTransfer::MakeAddressWithHighPartOfTwo(Data.GetInternalField(0), Data.GetInternalField(1));
    DataTransfer::IsolateData<ICppObjectMapper>(Data.GetIsolate())->UnBindCppObject(ClassDefinition, Ptr);
}

void FCppObjectMapper::BindCppObject(
    v8::Isolate* Isolate, JSClassDefinition* ClassDefinition, void* Ptr, v8::Local<v8::Object> JSObject, bool PassByPointer)
{
    //QUICK_SCOPED_CPU_TIMING("BindCppObject");
    DataTransfer::SetPointer(Isolate, JSObject, Ptr, 0);
    DataTransfer::SetPointer(Isolate, JSObject, ClassDefinition->TypeId, 1);

    auto Iter = CDataCache.find(Ptr);
    FObjectCacheNode* CacheNodePtr;
    if (Iter != CDataCache.end())
    {
        CacheNodePtr = Iter->second.Add(ClassDefinition->TypeId);
    }
    else
    {
        auto Ret = CDataCache.insert({Ptr, FObjectCacheNode(ClassDefinition->TypeId)});
        CacheNodePtr = &Ret.first->second;
    }
    CacheNodePtr->Value.Reset(Isolate, JSObject);

    if (!PassByPointer)
    {
        CDataFinalizeMap[Ptr] = ClassDefinition->Finalize;
        CacheNodePtr->Value.SetWeak<JSClassDefinition>(
            ClassDefinition, CDataGarbageCollectedWithFree, v8::WeakCallbackType::kInternalFields);
    }
    else
    {
        CacheNodePtr->Value.SetWeak<JSClassDefinition>(
            ClassDefinition, CDataGarbageCollectedWithoutFree, v8::WeakCallbackType::kInternalFields);
    }
}

void FCppObjectMapper::UnBindCppObject(JSClassDefinition* ClassDefinition, void* Ptr)
{
    //QUICK_SCOPED_CPU_TIMING("UnBindCppObject");

    CDataFinalizeMap.erase(Ptr);
    auto Iter = CDataCache.find(Ptr);
    if (Iter != CDataCache.end())
    {
        auto Removed = Iter->second.Remove(ClassDefinition->TypeId, true);
        if (!Iter->second.TypeId)    // last one
        {
            CDataCache.erase(Ptr);
        }
    }
}

void FCppObjectMapper::UnInitialize(v8::Isolate* InIsolate)
{
    for (auto Iter = CDataFinalizeMap.begin(); Iter != CDataFinalizeMap.end(); Iter++)
    {
        if (Iter->second)
            Iter->second(Iter->first);
    }
    CDataCache.clear();
    CDataFinalizeMap.clear();
    CDataNameToTemplateMap.clear();
    PointerConstructor.Reset();
}

}    // namespace PUERTS_NAMESPACE
