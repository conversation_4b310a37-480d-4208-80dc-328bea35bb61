#pragma once
#include "CoreMinimal.h"
#include "NamespaceDef.h"

namespace PUERTS_NAMESPACE {

template<class ContainType, class ValueType>
bool Contains(const ContainType& Contain, const ValueType& value)
{
    return Contain.find(value) != Contain.end();
}

FString GetPath(const FString& p);

FString BufferToString(const uint8_t* data, size_t size);

std::vector<std::string> ParseIntoArray(const std::string& str, char delimiter);

bool EndsWith(const FString& str, const FString& suffix);

bool StartsWith(const std::string& str, const std::string& prefix);

FString PathNormalize(const FString& PathIn);

}   // namespace PUERTS_NAMESPACE