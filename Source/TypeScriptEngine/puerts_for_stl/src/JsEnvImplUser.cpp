#include "CoreMinimal.h"
#include "JsEnvImplUser.h"
#include "JSClassRegister.h"
#include "PromiseRejectCallback.hpp"
#include "PuertsUtils.h"
#include <sstream>
#include <cassert>
#include <filesystem>
#include <v8-profiler.h>

#include "String/StringHelper.h"
#include "CrossBase/FileSystem/PathHelper.h"
#include "Serialization/SerializeNode.h"
#define check assert
namespace PUERTS_NAMESPACE
{
    std::unique_ptr<v8::Platform> V8Platform;
    void StartupJsEnvModule(const std::string& InFlags)
    {
        // This code will execute after your module is loaded into memory (but after global variables are initialized, of course.)
#if defined(WITH_NODEJS)
        V8Platform = node::MultiIsolatePlatform::Create(4);
#else
#if defined(USING_SINGLE_THREAD_PLATFORM)
        V8Platform = v8::platform::NewSingleThreadedDefaultPlatform();
#else
        V8Platform = v8::platform::NewDefaultPlatform();
#endif
#endif

        v8::V8::SetFlagsFromString(InFlags.c_str());


        v8::V8::InitializePlatform(V8Platform.get());
        v8::V8::Initialize();
    }
    void ShutdownJsEnvModule()
    {
        v8::V8::Dispose();
#if V8_MAJOR_VERSION > 9
        v8::V8::DisposePlatform();
#else
        v8::V8::ShutdownPlatform();
#endif
    }

    void LoadPesapiDll(const v8::FunctionCallbackInfo<v8::Value>& Info);

    static void FNameToArrayBuffer(const v8::FunctionCallbackInfo<v8::Value>& Info)
    {
        FName Name = FV8Utils::ToFName(Info.GetIsolate(), Info[0]);
        v8::Local<v8::ArrayBuffer> Ab = v8::ArrayBuffer::New(Info.GetIsolate(), sizeof(FName));
        void* Buff = DataTransfer::GetArrayBufferData(Ab);
        ::memcpy(Buff, &Name, sizeof(FName));
        Info.GetReturnValue().Set(Ab);
    }

    static void ToCString(const v8::FunctionCallbackInfo<v8::Value>& Info)
    {
        auto Isolate = Info.GetIsolate();
        if (!Info[0]->IsString())
        {
            FV8Utils::ThrowException(Isolate, "expect a string");
            return;
        }

        v8::Local<v8::String> Str = Info[0]->ToString(Isolate->GetCurrentContext()).ToLocalChecked();

        const size_t Length = Str->Utf8Length(Isolate);
        v8::Local<v8::ArrayBuffer> Ab = v8::ArrayBuffer::New(Info.GetIsolate(), Length + 1);
        char* Buff = static_cast<char*>(DataTransfer::GetArrayBufferData(Ab));
        Str->WriteUtf8(Isolate, Buff);
        Buff[Length] = '\0';
        Info.GetReturnValue().Set(Ab);
    }

    static void ToCPtrArray(const v8::FunctionCallbackInfo<v8::Value>& Info)
    {
        const size_t Length = sizeof(void*) * Info.Length();

        v8::Local<v8::ArrayBuffer> Ret = v8::ArrayBuffer::New(Info.GetIsolate(), Length);
        void** Buff = static_cast<void**>(DataTransfer::GetArrayBufferData(Ret));

        for (int i = 0; i < Info.Length(); i++)
        {
            auto Val = Info[i];
            void* Ptr = nullptr;
            if (Val->IsArrayBufferView())
            {
                v8::Local<v8::ArrayBufferView> BuffView = Val.As<v8::ArrayBufferView>();
                auto Ab = BuffView->Buffer();
                Ptr = static_cast<char*>(DataTransfer::GetArrayBufferData(Ab)) + BuffView->ByteOffset();
            }
            else if (Val->IsArrayBuffer())
            {
                auto Ab = v8::Local<v8::ArrayBuffer>::Cast(Val);
                Ptr = static_cast<char*>(DataTransfer::GetArrayBufferData(Ab));
            }
            Buff[i] = Ptr;
        }
        Info.GetReturnValue().Set(Ret);
    }

    FJsEnvImpl::FJsEnvImpl(const FString& ScriptRoot)
        : FJsEnvImpl(std::make_shared<DefaultJSModuleLoader>(ScriptRoot), std::make_shared<FDefaultLogger>(), -1, nullptr, FString(),
            nullptr, nullptr)
    {
    }

    FJsEnvImpl::FJsEnvImpl(std::shared_ptr<IJSModuleLoader> InModuleLoader, std::shared_ptr<ILogger> InLogger, int InDebugPort,
        std::function<void(const FString&)> InOnSourceLoadedCallback, const FString InFlags, void* InExternalRuntime,
        void* InExternalContext)
    {
#if PLATFORM_IOS
        v8::V8::SetFlagsFromString("--jitless --no-expose-wasm");
#endif

#ifdef WITH_V8_FAST_CALL
        v8::V8::SetFlagsFromString("--turbo-fast-api-calls");
#endif

#if defined(USING_SINGLE_THREAD_PLATFORM)
        v8::V8::SetFlagsFromString("--single-threaded");
#endif

        // char GCFlags[] = "--expose-gc";
        // v8::V8::SetFlagsFromString(GCFlags, sizeof(GCFlags));

        


        if (!InFlags.empty())
        {
#if !defined(WITH_NODEJS) && !defined(WITH_QUICKJS)
            TArray<FString> FlagArray = ParseIntoArray(InFlags, ' ');
            for (auto& Flag : FlagArray)
            {
                static FString Max_Old_Space_Size_Name(PUERTS_TEXT("--max-old-space-size="));
                if (StartsWith(Flag, Max_Old_Space_Size_Name))
                {
                    size_t Val = atoi(Flag.substr(Max_Old_Space_Size_Name.length()).c_str());
                    CreateParams.constraints.set_max_old_generation_size_in_bytes(Val * 1024 * 1024);
                }
            }
#endif
        }

        Started = false;
        Inspector = nullptr;
        InspectorChannel = nullptr;

        ModuleLoader = std::move(InModuleLoader);
        Logger = std::move(InLogger);
        OnSourceLoadedCallback = InOnSourceLoadedCallback;
#if !defined(WITH_NODEJS)
#if V8_MAJOR_VERSION < 8 && !defined(WITH_QUICKJS)
        std::unique_ptr<v8::StartupData> NativesBlob;
        if (!NativesBlob)
        {
            NativesBlob = std::make_unique<v8::StartupData>();
            NativesBlob->data = (const char*)NativesBlobCode;
            NativesBlob->raw_size = sizeof(NativesBlobCode);
        }
        v8::V8::SetNativesDataBlob(NativesBlob.get());
#endif

        CreateParams.array_buffer_allocator = v8::ArrayBuffer::Allocator::NewDefaultAllocator();
#if WITH_QUICKJS
        MainIsolate = InExternalRuntime ? v8::Isolate::New(InExternalRuntime) : v8::Isolate::New(CreateParams);
#else
        check(!InExternalRuntime && !InExternalContext);
        MainIsolate = v8::Isolate::New(CreateParams);
#endif
        auto Isolate = MainIsolate;
#ifdef THREAD_SAFE
        v8::Locker Locker(Isolate);
#endif
        Isolate->SetData(0, static_cast<ICppObjectMapper*>(this));    //直接传this会有问题，强转后地址会变

        v8::Isolate::Scope Isolatescope(Isolate);
        v8::HandleScope HandleScope(Isolate);

#if WITH_QUICKJS
        v8::Local<v8::Context> Context =
            (InExternalRuntime && InExternalContext) ? v8::Context::New(Isolate, InExternalContext) : v8::Context::New(Isolate);
#else
        v8::Local<v8::Context> Context = v8::Context::New(Isolate);
#endif
#else
        int Argc = 1;
        const char* Argv[] = { "puerts" };
        std::vector<std::string> Args(Argv, Argv + Argc);
        std::vector<std::string> ExecArgs;
        std::vector<std::string> Errors;

        const int Ret = uv_loop_init(&NodeUVLoop);
        if (Ret != 0)
        {
            Logger->Error(FString::Printf(PUERTS_TEXT("Failed to initialize loop: %s\n"), UTF8_TO_TCHAR(uv_err_name(Ret))));
            return;
        }

        CreateParams.array_buffer_allocator = nullptr;
        NodeArrayBufferAllocator = node::ArrayBufferAllocator::Create();

        auto Platform = static_cast<node::MultiIsolatePlatform*>(IJsEnvModule::Get().GetV8Platform());
        MainIsolate = node::NewIsolate(NodeArrayBufferAllocator.get(), &NodeUVLoop, Platform);

        auto Isolate = MainIsolate;
#ifdef THREAD_SAFE
        v8::Locker Locker(Isolate);
#endif
        Isolate->SetData(0, static_cast<IObjectMapper*>(this));    //直接传this会有问题，强转后地址会变

        // v8::Locker locker(Isolate);
        // difference from embedding example, if lock, blow check fail:
        // Utils::ApiCheck(
        //! v8::Locker::IsActive() ||
        //    internal_isolate->thread_manager()->IsLockedByCurrentThread() ||
        //    internal_isolate->serializer_enabled(),
        //"HandleScope::HandleScope",
        //"Entering the V8 API without proper locking in place");

        v8::Isolate::Scope Isolatescope(Isolate);

        NodeIsolateData =
            node::CreateIsolateData(Isolate, &NodeUVLoop, Platform, NodeArrayBufferAllocator.get());    // node::FreeIsolateData

        v8::HandleScope HandleScope(Isolate);

        v8::Local<v8::Context> Context = node::NewContext(Isolate);

#endif

        DefaultContext.Reset(Isolate, Context);

        v8::Context::Scope ContextScope(Context);

#if defined(WITH_NODEJS)
        // kDefaultFlags = kOwnsProcessState | kOwnsInspector, if kOwnsInspector set, inspector_agent.cc:681
        // CHECK_EQ(start_io_thread_async_initialized.exchange(true), false) fail!
        NodeEnv = CreateEnvironment(NodeIsolateData, Context, Args, ExecArgs, node::EnvironmentFlags::kOwnsProcessState);

        v8::MaybeLocal<v8::Value> LoadenvRet = node::LoadEnvironment(NodeEnv,
            "const publicRequire ="
            "  require('module').createRequire(process.cwd() + '/');"
            "globalThis.require = publicRequire;");

        if (LoadenvRet.IsEmpty())    // There has been a JS exception.
        {
            return;
        }

        // the same as raw v8
        Isolate->SetMicrotasksPolicy(v8::MicrotasksPolicy::kAuto);

        StartPolling();
#endif

        v8::Local<v8::Object> Global = Context->Global();

        v8::Local<v8::Object> PuertsObj = v8::Object::New(Isolate);
        Global->Set(Context, FV8Utils::InternalString(Isolate, "puerts"), PuertsObj).Check();

        auto This = v8::External::New(Isolate, this);

        MethodBindingHelper<&FJsEnvImpl::EvalScript>::Bind(Isolate, Context, Global, "__tgjsEvalScript", This);

        MethodBindingHelper<&FJsEnvImpl::Log>::Bind(Isolate, Context, Global, "__tgjsLog", This);

        MethodBindingHelper<&FJsEnvImpl::SearchModule>::Bind(Isolate, Context, Global, "__tgjsSearchModule", This);

        MethodBindingHelper<&FJsEnvImpl::LoadModule>::Bind(Isolate, Context, Global, "__tgjsLoadModule", This);

        //MethodBindingHelper<&FJsEnvImpl::LoadUEType>::Bind(Isolate, Context, PuertsObj, "loadUEType", This);

        MethodBindingHelper<&FJsEnvImpl::LoadCppType>::Bind(Isolate, Context, PuertsObj, "loadCPPType", This);

        //MethodBindingHelper<&FJsEnvImpl::UEClassToJSClass>::Bind(Isolate, Context, Global, "__tgjsUEClassToJSClass", This);

        //MethodBindingHelper<&FJsEnvImpl::NewContainer>::Bind(Isolate, Context, Global, "__tgjsNewContainer", This);

        //MethodBindingHelper<&FJsEnvImpl::MergeObject>::Bind(Isolate, Context, Global, "__tgjsMergeObject", This);

        //MethodBindingHelper<&FJsEnvImpl::NewObjectByClass>::Bind(Isolate, Context, Global, "__tgjsNewObject", This);

        //MethodBindingHelper<&FJsEnvImpl::SetJsTakeRefInTs>::Bind(Isolate, Context, Global, "__tgjsSetJsTakeRef", This);

        //MethodBindingHelper<&FJsEnvImpl::NewStructByScriptStruct>::Bind(Isolate, Context, Global, "__tgjsNewStruct", This);

#if !defined(ENGINE_INDEPENDENT_JSENV)
        MethodBindingHelper<&FJsEnvImpl::MakeUClass>::Bind(Isolate, Context, Global, "__tgjsMakeUClass", This);

        MethodBindingHelper<&FJsEnvImpl::Mixin>::Bind(Isolate, Context, Global, "__tgjsMixin", This);
#endif

        MethodBindingHelper<&FJsEnvImpl::FindModule>::Bind(Isolate, Context, Global, "__tgjsFindModule", This);

        MethodBindingHelper<&FJsEnvImpl::SetInspectorCallback>::Bind(Isolate, Context, Global, "__tgjsSetInspectorCallback", This);

        MethodBindingHelper<&FJsEnvImpl::DispatchProtocolMessage>::Bind(
            Isolate, Context, Global, "__tgjsDispatchProtocolMessage", This);

        Isolate->SetPromiseRejectCallback(&PromiseRejectCallback<FJsEnvImpl>);
        Global
            ->Set(Context, FV8Utils::ToV8String(Isolate, "__tgjsSetPromiseRejectCallback"),
                v8::FunctionTemplate::New(Isolate, &SetPromiseRejectCallback<FJsEnvImpl>)->GetFunction(Context).ToLocalChecked())
            .Check();

        //#if !defined(WITH_NODEJS)
        MethodBindingHelper<&FJsEnvImpl::SetTimeout>::Bind(Isolate, Context, Global, "setTimeout", This);

        MethodBindingHelper<&FJsEnvImpl::ClearInterval>::Bind(Isolate, Context, Global, "clearTimeout", This);

        MethodBindingHelper<&FJsEnvImpl::SetInterval>::Bind(Isolate, Context, Global, "setInterval", This);

        MethodBindingHelper<&FJsEnvImpl::ClearInterval>::Bind(Isolate, Context, Global, "clearInterval", This);
        //#endif

        MethodBindingHelper<&FJsEnvImpl::RequestMinorGC>::Bind(Isolate, Context, Global, "__tgjsRequestMinorGC", This);

        MethodBindingHelper<&FJsEnvImpl::RequestMajorGC>::Bind(Isolate, Context, Global, "__tgjsRequestMajorGC", This);

#if USE_WASM3
        MethodBindingHelper<&FJsEnvImpl::Wasm_NewMemory>::Bind(Isolate, Context, Global, "__tgjsWasm_NewMemory", This);
        MethodBindingHelper<&FJsEnvImpl::Wasm_MemoryGrowth>::Bind(Isolate, Context, Global, "__tgjsWasm_MemoryGrowth", This);
        MethodBindingHelper<&FJsEnvImpl::Wasm_MemoryBuffer>::Bind(Isolate, Context, Global, "__tgjsWasm_MemoryBuffer", This);
        MethodBindingHelper<&FJsEnvImpl::Wasm_TableGrowth>::Bind(Isolate, Context, Global, "__tgjsWasm_TableGrow", This);
        MethodBindingHelper<&FJsEnvImpl::Wasm_TableSet>::Bind(Isolate, Context, Global, "__tgjsWasm_TableSet", This);
        MethodBindingHelper<&FJsEnvImpl::Wasm_TableLen>::Bind(Isolate, Context, Global, "__tgjsWasm_TableLen", This);
        MethodBindingHelper<&FJsEnvImpl::Wasm_Instance>::Bind(Isolate, Context, Global, "__tgjsWasm_Instance", This);
        MethodBindingHelper<&FJsEnvImpl::Wasm_OverrideWebAssembly>::Bind(
            Isolate, Context, Global, "__tgjsWasm_OverrideWebAssembly", This);
#endif

        MethodBindingHelper<&FJsEnvImpl::DumpStatisticsLog>::Bind(Isolate, Context, Global, "dumpStatisticsLog", This);

        Global
            ->Set(Context, FV8Utils::ToV8String(Isolate, "__tgjsFNameToArrayBuffer"),
                v8::FunctionTemplate::New(Isolate, FNameToArrayBuffer)->GetFunction(Context).ToLocalChecked())
            .Check();

        PuertsObj
            ->Set(Context, FV8Utils::ToV8String(Isolate, "toCString"),
                v8::FunctionTemplate::New(Isolate, ToCString)->GetFunction(Context).ToLocalChecked())
            .Check();

        PuertsObj
            ->Set(Context, FV8Utils::ToV8String(Isolate, "toCPtrArray"),
                v8::FunctionTemplate::New(Isolate, ToCPtrArray)->GetFunction(Context).ToLocalChecked())
            .Check();

#if !defined(WITH_QUICKJS)
        PuertsObj
            ->Set(Context, FV8Utils::ToV8String(Isolate, "load"),
                v8::FunctionTemplate::New(Isolate, LoadPesapiDll)->GetFunction(Context).ToLocalChecked())
            .Check();
#endif

        FString DllExt =
#if PLATFORM_WINDOWS
            PUERTS_TEXT(".dll");
#elif PLATFORM_MAC || PLATFORM_IOS
            PUERTS_TEXT(".dylib");
#else
            PUERTS_TEXT(".so");
#endif
        PuertsObj->Set(Context, FV8Utils::ToV8String(Isolate, "dll_ext"), FV8Utils::ToV8String(Isolate, DllExt)).Check();

        //MethodBindingHelper<&FJsEnvImpl::ReleaseManualReleaseDelegate>::Bind(Isolate, Context, PuertsObj, "releaseManualReleaseDelegate", This);

        /* TODO using std contain replace
                ArrayTemplate = v8::UniquePersistent<v8::FunctionTemplate>(Isolate, FScriptArrayWrapper::ToFunctionTemplate(Isolate));

                SetTemplate = v8::UniquePersistent<v8::FunctionTemplate>(Isolate, FScriptSetWrapper::ToFunctionTemplate(Isolate));

                MapTemplate = v8::UniquePersistent<v8::FunctionTemplate>(Isolate, FScriptMapWrapper::ToFunctionTemplate(Isolate));

                FixSizeArrayTemplate = v8::UniquePersistent<v8::FunctionTemplate>(Isolate, FFixSizeArrayWrapper::ToFunctionTemplate(Isolate));
        */

        CppObjectMapper.Initialize(Isolate, Context);

        /* TODO need imply
                DelegateTemplate = v8::UniquePersistent<v8::FunctionTemplate>(Isolate, FDelegateWrapper::ToFunctionTemplate(Isolate));

                MulticastDelegateTemplate =
                    v8::UniquePersistent<v8::FunctionTemplate>(Isolate, FMulticastDelegateWrapper::ToFunctionTemplate(Isolate));

                SoftObjectPtrTemplate = v8::UniquePersistent<v8::FunctionTemplate>(Isolate, FSoftObjectWrapper::ToFunctionTemplate(Isolate));

                DynamicInvoker = MakeShared<DynamicInvokerImpl, ESPMode::ThreadSafe>(this);
                MixinInvoker = DynamicInvoker;
        #if !defined(ENGINE_INDEPENDENT_JSENV)
                TsDynamicInvoker = MakeShared<TsDynamicInvokerImpl, ESPMode::ThreadSafe>(this);
        #endif
        */

        Inspector = CreateV8Inspector(InDebugPort, &Context);

        ExecuteModule("puerts/first_run.js");
#if !defined(WITH_NODEJS)
        ExecuteModule("puerts/polyfill.js");
#endif
        ExecuteModule("puerts/log.js");
        ExecuteModule("puerts/modular.js");
        // TODO impl
        //ExecuteModule("puerts/uelazyload.js");
        ExecuteModule("puerts/cpplazyload.js");
        ExecuteModule("puerts/events.js");
        ExecuteModule("puerts/promises.js");
        ExecuteModule("puerts/argv.js");
        ExecuteModule("puerts/jit_stub.js");
        ExecuteModule("puerts/hot_reload.js");
        ExecuteModule("puerts/pesaddon.js");

        Require.Reset(Isolate, PuertsObj->Get(Context, FV8Utils::ToV8String(Isolate, "__require")).ToLocalChecked().As<v8::Function>());

        GetESMMain.Reset(
            Isolate, PuertsObj->Get(Context, FV8Utils::ToV8String(Isolate, "getESMMain")).ToLocalChecked().As<v8::Function>());

        ReloadJs.Reset(Isolate, PuertsObj->Get(Context, FV8Utils::ToV8String(Isolate, "__reload")).ToLocalChecked().As<v8::Function>());
#if !PUERTS_FORCE_CPP_UFUNCTION
        MergeInstance.Reset(
            Isolate, PuertsObj->Get(Context, FV8Utils::ToV8String(Isolate, "__mergeInstance")).ToLocalChecked().As<v8::Function>());
#endif

        RemoveListItem.Reset(
            Isolate, PuertsObj->Get(Context, FV8Utils::ToV8String(Isolate, "__removeListItem")).ToLocalChecked().As<v8::Function>());

        GenListApply.Reset(
            Isolate, PuertsObj->Get(Context, FV8Utils::ToV8String(Isolate, "__genListApply")).ToLocalChecked().As<v8::Function>());

        // TODO need to impl
        /*DelegateProxiesCheckerHandler =
            FUETicker::GetCoreTicker().AddTicker(FTickerDelegate::CreateRaw(this, &FJsEnvImpl::CheckDelegateProxies), 1);

        ManualReleaseCallbackMap.Reset(Isolate, v8::Map::New(Isolate));

        UserObjectRetainer.SetName(PUERTS_TEXT("Puerts_UserObjectRetainer"));
        SysObjectRetainer.SetName(PUERTS_TEXT("Puerts_SysObjectRetainer"));
        */

#ifdef SINGLE_THREAD_VERIFY
        BoundThreadId = FPlatformTLS::GetCurrentThreadId();
#endif

#if USE_WASM3
        PuertsWasmEnv = std::make_shared<WasmEnv>();
        //创建默认的runtime
        PuertsWasmRuntimeList.Add(std::make_shared<WasmRuntime>(PuertsWasmEnv.get()));
        ExecuteModule("puerts/wasm3_helper.js");
#endif

        // need Require function
        FString RequireResourcePtrScript = R"((function() {const cpp = require("cpp");cpp.ResourcePtr;cpp.AssetStreamingManager;cpp.GameObject;cpp.GameWorld;})())";
        EvalScript(Context, Isolate, RequireResourcePtrScript);
        ExecuteModule("CEEditorProperty.mjs");
        CEPropertyFunction.GetEditorPropertyFiledsJson.Reset(Isolate, PuertsObj->Get(Context, FV8Utils::ToV8String(Isolate, "GetEditorPropertyFiledsJson")).ToLocalChecked().As<v8::Function>());
        CEPropertyFunction.SetPropertyValueJson.Reset(Isolate, PuertsObj->Get(Context, FV8Utils::ToV8String(Isolate, "SetPropertyValueJson")).ToLocalChecked().As<v8::Function>());
        CEPropertyFunction.GetReferenceResource.Reset(Isolate, PuertsObj->Get(Context, FV8Utils::ToV8String(Isolate, "GetReferenceResource")).ToLocalChecked().As<v8::Function>());

    }

    void FJsEnvImpl::EvalScript(v8::Local<v8::Context> Context, v8::Isolate* Isolate, FString script)
    {
#if V8_MAJOR_VERSION > 8
        v8::ScriptOrigin Origin(Isolate, FV8Utils::ToV8String(Isolate, "chunk"));
#else
        v8::ScriptOrigin Origin(FV8Utils::ToV8String(Isolate, "chunk"));
#endif
        v8::Local<v8::String> Source = FV8Utils::ToV8String(Isolate, script);
        v8::TryCatch TryCatch(Isolate);

        auto CompiledScript = v8::Script::Compile(Context, Source, &Origin);
        if (CompiledScript.IsEmpty())
        {
            Logger->Error(FV8Utils::TryCatchToString(Isolate, &TryCatch));
            return;
        }
        (void)(CompiledScript.ToLocalChecked()->Run(Context));
        if (TryCatch.HasCaught())
        {
            Logger->Error(FV8Utils::TryCatchToString(Isolate, &TryCatch));
        }
    }

    FJsEnvImpl::~FJsEnvImpl()
    {
#if USE_WASM3
        PuertsWasmRuntimeList.Empty();
        PuertsWasmEnv.reset();
        for (auto Item : PuertsWasmCachedLinkFunctionList)
        {
            Item->CachedFunction.Reset();
            delete Item;
        }
        PuertsWasmCachedLinkFunctionList.Empty();
#endif

#ifdef SINGLE_THREAD_VERIFY
        ensureMsgf(BoundThreadId == FPlatformTLS::GetCurrentThreadId(), PUERTS_TEXT("Access by illegal thread!"));
#endif
#if defined(WITH_NODEJS)
        StopPolling();
#endif

#ifndef WITH_QUICKJS
        for (auto& KV : HashToModuleInfo)
        {
            delete KV.second;
        }
        HashToModuleInfo.clear();
        PathToModule.clear();
#endif

        /*for (int i = 0; i < ManualReleaseCallbackList.size(); i++)
        {
            if (ManualReleaseCallbackList[i].IsValid(true))
            {
                ManualReleaseCallbackList[i].Get()->JsFunction.Reset();
            }
        }
        ManualReleaseCallbackMap.Reset();*/
        InspectorMessageHandler.Reset();
        Require.Reset();
        GetESMMain.Reset();
        ReloadJs.Reset();
        JsPromiseRejectCallback.Reset();

        //FUETicker::GetCoreTicker().RemoveTicker(DelegateProxiesCheckerHandler);

        {
            auto Isolate = MainIsolate;
#ifdef THREAD_SAFE
            v8::Locker Locker(Isolate);
#endif
            v8::Isolate::Scope IsolateScope(Isolate);
            v8::HandleScope HandleScope(Isolate);

            //TypeToTemplateInfoMap.Empty();

            CppObjectMapper.UnInitialize(Isolate);

            //ObjectMap.Empty();

            //for (auto& KV : StructCache)
            //{
            //    FObjectCacheNode* PNode = &KV.Value;
            //    while (PNode)
            //    {
            //        PNode->Value.Reset();
            //        PNode = PNode->Next;
            //    }
            //}

            //for (auto& KV : ContainerCache)
            //{
            //    if (KV.Value.NeedRelease)
            //    {
            //        switch (KV.Value.Type)
            //        {
            //        case EArray:
            //            delete static_cast<FScriptArrayEx*>(KV.Key);
            //            break;
            //        case EMap:
            //            delete static_cast<FScriptMapEx*>(KV.Key);
            //            break;
            //        case ESet:
            //            delete static_cast<FScriptSetEx*>(KV.Key);
            //            break;
            //        }
            //    }
            //}
            //ContainerCache.Empty();

            //for (auto Iter = DelegateMap.begin(); Iter != DelegateMap.end(); Iter++)
            //{
            //    Iter->second.JSObject.Reset();
            //    if (Iter->second.Proxy.IsValid(true))
            //    {
            //        Iter->second.Proxy->JsFunction.Reset();
            //    }

            //    if (!Iter->second.PassByPointer)
            //    {
            //        delete ((FScriptDelegate*)Iter->first);
            //    }
            //    Iter->second.JsCallbacks.Reset();
            //}

            //for (auto& KV : AutoReleaseCallbacksMap)
            //{
            //    for (auto& Callback : KV.Value)
            //    {
            //        if (Callback.IsValid(true))
            //        {
            //            Callback->JsFunction.Reset();
            //        }
            //    }
            //}

            //TsFunctionMap.Empty();
            //MixinFunctionMap.Empty();

#if !defined(ENGINE_INDEPENDENT_JSENV)
            TsDynamicInvoker.Reset();
            BindInfoMap.Empty();
#endif

            //for (auto Iter = TimerInfos.CreateIterator(); Iter; ++Iter)
            //{
            //    Iter->Value.Callback.Reset();
            //    FUETicker::GetCoreTicker().RemoveTicker(Iter->Value.TickerHandle);
            //}
            //TimerInfos.Empty();

#if !defined(ENGINE_INDEPENDENT_JSENV)
            for (auto& GeneratedClass : GeneratedClasses)
            {
                if (auto JSGeneratedClass = Cast<UJSGeneratedClass>(GeneratedClass))
                {
                    if (JSGeneratedClass->IsValidLowLevelFast() && !UEObjectIsPendingKill(JSGeneratedClass))
                    {
                        JSGeneratedClass->Release();
                    }
                }
                else if (auto JSWidgetGeneratedClass = Cast<UJSWidgetGeneratedClass>(GeneratedClass))
                {
                    if (JSWidgetGeneratedClass->IsValidLowLevelFast() && !UEObjectIsPendingKill(JSWidgetGeneratedClass))
                    {
                        JSWidgetGeneratedClass->Release();
                    }
                }
                else if (auto JSAnimGeneratedClass = Cast<UJSAnimGeneratedClass>(GeneratedClass))
                {
                    if (JSWidgetGeneratedClass->IsValidLowLevelFast() && !UEObjectIsPendingKill(JSWidgetGeneratedClass))
                    {
                        JSAnimGeneratedClass->Release();
                    }
                }
            }
#endif

#if defined(WITH_NODEJS)
            node::EmitExit(NodeEnv);
            node::Stop(NodeEnv);
            node::FreeEnvironment(NodeEnv);
            node::FreeIsolateData(NodeIsolateData);

            auto Platform = static_cast<node::MultiIsolatePlatform*>(IJsEnvModule::Get().GetV8Platform());
            Platform->UnregisterIsolate(Isolate);
#endif

            if (InspectorChannel)
            {
                delete InspectorChannel;
                InspectorChannel = nullptr;
            }

            if (Inspector)
            {
                delete Inspector;
                Inspector = nullptr;
            }

           /* DynamicInvoker.Reset();
            MixinInvoker.Reset();

            SoftObjectPtrTemplate.Reset();
            MulticastDelegateTemplate.Reset();
            DelegateTemplate.Reset();
            FixSizeArrayTemplate.Reset();
            MapTemplate.Reset();
            SetTemplate.Reset();
            ArrayTemplate.Reset();*/
#if !PUERTS_FORCE_CPP_UFUNCTION
            MergeInstance.Reset();
#endif
            RemoveListItem.Reset();
            GenListApply.Reset();
            CEPropertyFunction.GetEditorPropertyFiledsJson.Reset();
            CEPropertyFunction.SetPropertyValueJson.Reset();
            CEPropertyFunction.GetReferenceResource.Reset();
        }

#if !defined(ENGINE_INDEPENDENT_JSENV)
        for (size_t i = 0; i < MixinClasses.Num(); i++)
        {
            if (MixinClasses[i].IsValid())
            {
                UJSGeneratedClass::Restore(MixinClasses[i].Get());
            }
        }
#endif

#if PUERTS_REUSE_STRUCTWRAPPER_FUNCTIONTEMPLATE
        for (auto Iter = TypeReflectionMap.CreateIterator(); Iter; ++Iter)
        {
            Iter->Value->CachedFunctionTemplate.Reset();
        }
#endif

        DefaultContext.Reset();
        MainIsolate->Dispose();
        MainIsolate = nullptr;
        delete CreateParams.array_buffer_allocator;

        //GUObjectArray.RemoveUObjectDeleteListener(static_cast<FUObjectArray::FUObjectDeleteListener*>(this));

        // quickjs will call UnBind in vm dispose, so cleanup move to here
       /* for (auto& KV : StructCache)
        {
            FObjectCacheNode* PNode = &KV.Value;
            while (PNode)
            {
                if (PNode->UserData)
                {
                    FScriptStructWrapper* ScriptStructWrapper = (FScriptStructWrapper*)(PNode->UserData);
                    ScriptStructWrapper->Free(KV.Key);
                }
                PNode = PNode->Next;
            }
        }
        StructCache.Empty();*/
    }

#pragma region IJsEnv
    void FJsEnvImpl::Start(const FString& ModuleNameOrScript, const TArray<std::tuple<FString, const ClassTypeId*, ClassObject*>>& Arguments, bool IsScript)
    {
#ifdef SINGLE_THREAD_VERIFY
        ensureMsgf(BoundThreadId == FPlatformTLS::GetCurrentThreadId(), PUERTS_TEXT("Access by illegal thread!"));
#endif
        if (Started)
        {
            Logger->Error("Started yet!");
            return;
        }

        auto Isolate = MainIsolate;
#ifdef THREAD_SAFE
        v8::Locker Locker(Isolate);
#endif
        v8::Isolate::Scope IsolateScope(Isolate);
        v8::HandleScope HandleScope(Isolate);
        auto Context = v8::Local<v8::Context>::New(Isolate, DefaultContext);
        v8::Context::Scope ContextScope(Context);

        if (!SetPuertsArgv(Context, Isolate, Arguments))
        {
            return;
        }

        if (IsScript)
        {
#if V8_MAJOR_VERSION > 8
            v8::ScriptOrigin Origin(Isolate, FV8Utils::ToV8String(Isolate, "chunk"));
#else
            v8::ScriptOrigin Origin(FV8Utils::ToV8String(Isolate, "chunk"));
#endif
            v8::Local<v8::String> Source = FV8Utils::ToV8String(Isolate, ModuleNameOrScript);
            v8::TryCatch TryCatch(Isolate);

            auto CompiledScript = v8::Script::Compile(Context, Source, &Origin);
            if (CompiledScript.IsEmpty())
            {
                Logger->Error(FV8Utils::TryCatchToString(Isolate, &TryCatch));
                return;
            }
            (void)(CompiledScript.ToLocalChecked()->Run(Context));
            if (TryCatch.HasCaught())
            {
                Logger->Error(FV8Utils::TryCatchToString(Isolate, &TryCatch));
            }
        }
        else
        {
            v8::TryCatch TryCatch(Isolate);
            v8::Local<v8::Value> Args[] = { FV8Utils::ToV8String(Isolate, ModuleNameOrScript) };
            __USE(Require.Get(Isolate)->Call(Context, v8::Undefined(Isolate), 1, Args));
            if (TryCatch.HasCaught())
            {
                Logger->Error(FV8Utils::TryCatchToString(Isolate, &TryCatch));
            }
        }
        Started = true;
    }

    bool FJsEnvImpl::IdleNotificationDeadline(double DeadlineInSeconds)
    {
#ifdef THREAD_SAFE
        v8::Locker Locker(MainIsolate);
#endif
#ifndef WITH_QUICKJS
        return MainIsolate->IdleNotificationDeadline(DeadlineInSeconds);
#else
        return true;
#endif
    }

    void FJsEnvImpl::LowMemoryNotification()
    {
#ifdef SINGLE_THREAD_VERIFY
        ensureMsgf(BoundThreadId == FPlatformTLS::GetCurrentThreadId(), PUERTS_TEXT("Access by illegal thread!"));
#endif
#ifdef THREAD_SAFE
        v8::Locker Locker(MainIsolate);
#endif
        MainIsolate->LowMemoryNotification();
    }

    void FJsEnvImpl::RequestMinorGarbageCollectionForTesting()
    {
#ifdef THREAD_SAFE
        v8::Locker Locker(MainIsolate);
#endif
#ifndef WITH_QUICKJS
        MainIsolate->RequestGarbageCollectionForTesting(v8::Isolate::kMinorGarbageCollection);
#endif
    }

    void FJsEnvImpl::RequestFullGarbageCollectionForTesting()
    {
#ifdef THREAD_SAFE
        v8::Locker Locker(MainIsolate);
#endif
#ifndef WITH_QUICKJS
        MainIsolate->RequestGarbageCollectionForTesting(v8::Isolate::kFullGarbageCollection);
#endif
    }

void FJsEnvImpl::AddGCPrologueCallback(v8::Isolate::GCCallbackWithData callback, void* data, v8::GCType gc_type_filter)
    {
#ifdef THREAD_SAFE
        v8::Locker Locker(MainIsolate);
#endif
#ifndef WITH_QUICKJS
        MainIsolate->AddGCPrologueCallback(callback, data, gc_type_filter);
#endif
    }

    void FJsEnvImpl::AddGCEpilogueCallback(v8::Isolate::GCCallbackWithData callback, void* data, v8::GCType gc_type_filter)
    {
#ifdef THREAD_SAFE
        v8::Locker Locker(MainIsolate);
#endif
#ifndef WITH_QUICKJS
        MainIsolate->AddGCEpilogueCallback(callback, data, gc_type_filter);
#endif
    }


    
class FileOutputStream : public v8::OutputStream
    {
    public:
        explicit FileOutputStream(const std::string& filename)
        {
            file_.open(filename, std::ios::binary);
            if (!file_.is_open())
            {
                Assert(false);
                //LOG_ERROR("open {} failed", filename);
            }
        }

        ~FileOutputStream() override
        {
            if (file_.is_open())
            {
                file_.close();
            }
        }

        WriteResult WriteAsciiChunk(char* data, int size) override
        {
            if (!file_.is_open())
            {
                return kAbort;
            }
            file_.write(static_cast<const char*>(data), size);
            return file_.good() ? kContinue : kAbort;
        }

        virtual void EndOfStream() override
        {
            if (file_.is_open())
            {
                file_.close();
            }
        }

    private:
        std::ofstream file_;
    };

    void FJsEnvImpl::OutputSnapShot(const std::string& filename)
    {
        OutputSnapShot(MainIsolate, filename);
    }

    void FJsEnvImpl::OutputSnapShot(v8::Isolate* isolate, const std::string& filename)
    {
        v8::HeapProfiler* profiler = isolate->GetHeapProfiler();

        const v8::HeapSnapshot* snapshot = profiler->TakeHeapSnapshot();

        FileOutputStream output(filename);

        snapshot->Serialize(&output);
        delete snapshot;
    }

    void FJsEnvImpl::WaitDebugger(double Timeout)
    {
#ifdef THREAD_SAFE
        v8::Locker Locker(MainIsolate);
#endif

        const auto startTime = std::chrono::system_clock::now();
        while (Inspector && !Inspector->Tick())
        {
            if (Timeout > 0)
            {
                auto now = std::chrono::system_clock::now();
                double duration_ms = static_cast<double>(std::chrono::duration_cast<std::chrono::milliseconds>(now - startTime).count());
                double duration_s = duration_ms / 1000.0;
                if (duration_s >= Timeout)
                {
                    break;
                }
            }
        }
    }

    void FJsEnvImpl::JsHotReload(FName ModuleName, const FString& JsSource)
    {
#ifdef SINGLE_THREAD_VERIFY
        ensureMsgf(BoundThreadId == FPlatformTLS::GetCurrentThreadId(), PUERTS_TEXT("Access by illegal thread!"));
#endif
        auto Isolate = MainIsolate;
        v8::Isolate::Scope IsolateScope(Isolate);
        v8::HandleScope HandleScope(Isolate);
        auto Context = DefaultContext.Get(Isolate);
        v8::Context::Scope ContextScope(Context);
        auto LocalReloadJs = ReloadJs.Get(Isolate);

        FString OutPath, OutDebugPath;

        if (ModuleLoader->Search(PUERTS_TEXT(""), ModuleName, OutPath, OutDebugPath))
        {
            OutPath = std::filesystem::absolute(OutPath).generic_string();
            Logger->Info(fmt::format(PUERTS_TEXT("reload js module [{}]"), OutPath));
            v8::TryCatch TryCatch(Isolate);
            v8::Handle<v8::Value> Args[] = { FV8Utils::ToV8String(Isolate, ModuleName), FV8Utils::ToV8String(Isolate, OutPath),
                FV8Utils::ToV8String(Isolate, JsSource) };

            (void)(LocalReloadJs->Call(Context, v8::Undefined(Isolate), 3, Args));

            if (TryCatch.HasCaught())
            {
                Logger->Error(fmt::format(PUERTS_TEXT("reload module exception {}"), FV8Utils::TryCatchToString(Isolate, &TryCatch)));
            }
        }
        else
        {
            Logger->Warn(fmt::format(PUERTS_TEXT("not find js module [{}]"), ModuleName));
            return;
        }
    }

    bool FJsEnvImpl::ReloadModule(FName ModuleName, const FString& JsSource)
    {
#ifdef SINGLE_THREAD_VERIFY
        ensureMsgf(BoundThreadId == FPlatformTLS::GetCurrentThreadId(), PUERTS_TEXT("Access by illegal thread!"));
#endif
#ifdef THREAD_SAFE
        v8::Locker Locker(MainIsolate);
#endif
        JsHotReload(ModuleName, JsSource);
        return true;
    }

    void FJsEnvImpl::ReloadSource(const FString& Path, const std::string& JsSource)
    {
#ifdef SINGLE_THREAD_VERIFY
        ensureMsgf(BoundThreadId == FPlatformTLS::GetCurrentThreadId(), PUERTS_TEXT("Access by illegal thread!"));
#endif
#ifdef THREAD_SAFE
        v8::Locker Locker(MainIsolate);
#endif
        auto Isolate = MainIsolate;
        v8::Isolate::Scope IsolateScope(Isolate);
        v8::HandleScope HandleScope(Isolate);
        auto Context = DefaultContext.Get(Isolate);
        v8::Context::Scope ContextScope(Context);
        auto LocalReloadJs = ReloadJs.Get(Isolate);

        Logger->Info(fmt::format(PUERTS_TEXT("reload js [{}]"), Path));
        v8::TryCatch TryCatch(Isolate);
        v8::Handle<v8::Value> Args[] = {
            v8::Undefined(Isolate), FV8Utils::ToV8String(Isolate, Path), FV8Utils::ToV8String(Isolate, JsSource.c_str()) };

        (void)(LocalReloadJs->Call(Context, v8::Undefined(Isolate), 3, Args));

        if (TryCatch.HasCaught())
        {
            Logger->Error(fmt::format(PUERTS_TEXT("reload module exception {}"), FV8Utils::TryCatchToString(Isolate, &TryCatch)));
        }
    }

    void FJsEnvImpl::OnSourceLoaded(std::function<void(const FString&)> Callback)
    {
        OnSourceLoadedCallback = Callback;

    }

    FString FJsEnvImpl::CurrentStackTrace()
    {
#ifndef WITH_QUICKJS
        v8::Isolate* Isolate = MainIsolate;
#ifdef THREAD_SAFE
        v8::Locker Locker(Isolate);
#endif
        v8::Isolate::Scope IsolateScope(Isolate);
        v8::HandleScope HandleScope(Isolate);

        std::string StackTrace = StackTraceToString(Isolate, v8::StackTrace::CurrentStackTrace(Isolate, 10, v8::StackTrace::kDetailed));
        return UTF8_TO_TCHAR(StackTrace.c_str());
#else
        return PUERTS_TEXT("");
#endif
    }

    void FJsEnvImpl::InitExtensionMethodsMap()
    {
        // TODO UE Impl
    }
#pragma endregion IJsEnv

#pragma region ICppObjectMapper

    void FJsEnvImpl::BindCppObject(v8::Isolate* Isolate, JSClassDefinition* ClassDefinition, void* Ptr, v8::Local<v8::Object> JSObject,
        bool PassByPointer)
    {
        CppObjectMapper.BindCppObject(Isolate, ClassDefinition, Ptr, JSObject, PassByPointer);
    }

    void FJsEnvImpl::UnBindCppObject(JSClassDefinition* ClassDefinition, void* Ptr)
    {
        CppObjectMapper.UnBindCppObject(ClassDefinition, Ptr);
    }

    v8::Local<v8::Value> FJsEnvImpl::FindOrAddCppObject(
        v8::Isolate* Isolate, v8::Local<v8::Context>& Context, const void* TypeId, void* Ptr, bool PassByPointer)
    {
        return CppObjectMapper.FindOrAddCppObject(Isolate, Context, TypeId, Ptr, PassByPointer);
    }

    bool FJsEnvImpl::IsInstanceOfCppObject(v8::Isolate* Isolate, const void* TypeId, v8::Local<v8::Object> JsObject)
    {
        return CppObjectMapper.IsInstanceOfCppObject(Isolate, TypeId, JsObject);
    }

    std::weak_ptr<int> FJsEnvImpl::GetJsEnvLifeCycleTracker()
    {
        return CppObjectMapper.GetJsEnvLifeCycleTracker();
    }
#pragma endregion ICppObjectMapper


#pragma region BindingFunction
    void FJsEnvImpl::EvalScript(const v8::FunctionCallbackInfo<v8::Value>& Info)
    {
        v8::Isolate* Isolate = Info.GetIsolate();
        v8::Isolate::Scope IsolateScope(Isolate);
        v8::HandleScope HandleScope(Isolate);
        v8::Local<v8::Context> Context = Isolate->GetCurrentContext();
        v8::Context::Scope ContextScope(Context);

        CHECK_V8_ARGS(EArgString, EArgString);

        v8::Local<v8::String> Source = Info[0]->ToString(Context).ToLocalChecked();

#ifndef WITH_QUICKJS
        bool IsESM = Info[2]->BooleanValue(Isolate);

        if (IsESM)
        {
            FString FullPath = FV8Utils::ToFString(Isolate, Info[3]);
            v8::Local<v8::Module> RootModule;

            if (!FetchESModuleTree(Context, FullPath).ToLocal(&RootModule))
            {
                return;
            }

            if (RootModule->InstantiateModule(Context, ResolveModuleCallback).FromMaybe(false))
            {
                auto MaybeResult = RootModule->Evaluate(Context);
                v8::Local<v8::Value> Result;
                if (MaybeResult.ToLocal(&Result))
                {
                    if (Result->IsPromise())
                    {
                        v8::Local<v8::Promise> ResultPromise(Result.As<v8::Promise>());
                        while (ResultPromise->State() == v8::Promise::kPending)
                        {
                            Isolate->PerformMicrotaskCheckpoint();
                        }

                        if (ResultPromise->State() == v8::Promise::kRejected)
                        {
                            ResultPromise->MarkAsHandled();
                            Isolate->ThrowException(ResultPromise->Result());
                            return;
                        }
                    }
                    Info.GetReturnValue().Set(RootModule->GetModuleNamespace());
                }
            }

            return;
        }
#endif

        v8::String::Utf8Value UrlArg(Isolate, Info[1]);
        FString ScriptUrl = UTF8_TO_TCHAR(*UrlArg);
#if PLATFORM_WINDOWS
        // 修改URL分隔符格式，否则无法匹配Inspector协议在打断点时发送的正则表达式，导致断点失败
        FString FormattedScriptUrl = std::filesystem::path(ScriptUrl).lexically_normal().string();
        //FString FormattedScriptUrl = ScriptUrl.Replace(PUERTS_TEXT("/"), PUERTS_TEXT("\\"));
#else
        FString FormattedScriptUrl = ScriptUrl;
#endif
        v8::Local<v8::String> Name = FV8Utils::ToV8String(Isolate, FormattedScriptUrl);
#if V8_MAJOR_VERSION > 8
        v8::ScriptOrigin Origin(Isolate, Name);
#else
        v8::ScriptOrigin Origin(Name);
#endif
        auto Script = v8::Script::Compile(Context, Source, &Origin);
        if (Script.IsEmpty())
        {
            return;
        }
        auto Result = Script.ToLocalChecked()->Run(Context);
        if (Result.IsEmpty())
        {
            return;
        }
        Info.GetReturnValue().Set(Result.ToLocalChecked());

        if (OnSourceLoadedCallback)
        {
            OnSourceLoadedCallback(FormattedScriptUrl);
        }
    }

    void FJsEnvImpl::Log(const v8::FunctionCallbackInfo<v8::Value>& Info)
    {
        v8::Isolate* Isolate = Info.GetIsolate();
        v8::Isolate::Scope IsolateScope(Isolate);
        v8::HandleScope HandleScope(Isolate);
        v8::Local<v8::Context> Context = Isolate->GetCurrentContext();
        v8::Context::Scope ContextScope(Context);

        CHECK_V8_ARGS(EArgInt32, EArgString);

        auto Level = Info[0]->Int32Value(Context).ToChecked();

        FString Msg = FV8Utils::ToFString(Isolate, Info[1]);
        switch (Level)
        {
        case 1:
            Logger->Info(Msg);
            break;
        case 2:
            Logger->Warn(Msg);
            break;
        case 3:
            Logger->Error(Msg);
            break;
        default:
            Logger->Log(Msg);
            break;
        }
    }
    void FJsEnvImpl::SearchModule(const v8::FunctionCallbackInfo<v8::Value>& Info)
    {
        v8::Isolate* Isolate = Info.GetIsolate();
        v8::Local<v8::Context> Context = Isolate->GetCurrentContext();

        CHECK_V8_ARGS(EArgString, EArgString);

        FString ModuleName = FV8Utils::ToFString(Isolate, Info[0]);
        FString RequiringDir = FV8Utils::ToFString(Isolate, Info[1]);
        FString OutPath;
        FString OutDebugPath;

        if (ModuleLoader->Search(RequiringDir, ModuleName, OutPath, OutDebugPath))
        {
            auto Result = v8::Array::New(Isolate);
            Result->Set(Context, 0, FV8Utils::ToV8String(Isolate, OutPath)).Check();
            Result->Set(Context, 1, FV8Utils::ToV8String(Isolate, OutDebugPath)).Check();
            Info.GetReturnValue().Set(Result);
        }
    }
    void FJsEnvImpl::LoadModule(const v8::FunctionCallbackInfo<v8::Value>& Info)
    {
        v8::Isolate* Isolate = Info.GetIsolate();
        v8::Isolate::Scope IsolateScope(Isolate);
        v8::HandleScope HandleScope(Isolate);
        v8::Local<v8::Context> Context = Isolate->GetCurrentContext();
        v8::Context::Scope ContextScope(Context);

        CHECK_V8_ARGS(EArgString);

        FString Path = FV8Utils::ToFString(Isolate, Info[0]);
        TArray<uint8> Data;
        if (!ModuleLoader->Load(Path, Data))
        {
            FV8Utils::ThrowException(Isolate, "can not load module");
            return;
        }

        Info.GetReturnValue().Set(FV8Utils::ToV8StringFromFileContent(Isolate, Data));
    }
    void FJsEnvImpl::LoadCppType(const v8::FunctionCallbackInfo<v8::Value>& Info)
    {
        CppObjectMapper.LoadCppType(Info);
    }
    void FJsEnvImpl::NewContainer(const v8::FunctionCallbackInfo<v8::Value>& Info)
    {
        // TODO implt stl 
    }
    void FJsEnvImpl::MergeObject(const v8::FunctionCallbackInfo<v8::Value>& Info)
    {
        // TODO UE impl
    }
    void FJsEnvImpl::NewObjectByClass(const v8::FunctionCallbackInfo<v8::Value>& Info)
    {
        // TODO UE impl
    }
    void FJsEnvImpl::SetJsTakeRefInTs(const v8::FunctionCallbackInfo<v8::Value>& Info)
    {
        // TODO UE impl
    }
    void FJsEnvImpl::NewStructByScriptStruct(const v8::FunctionCallbackInfo<v8::Value>& Info)
    {
        // TODO UE impl
    }
    void FJsEnvImpl::FindModule(const v8::FunctionCallbackInfo<v8::Value>& Info)
    {
        v8::Isolate* Isolate = Info.GetIsolate();
        v8::Isolate::Scope Isolatescope(Isolate);
        v8::HandleScope HandleScope(Isolate);
        v8::Local<v8::Context> Context = Isolate->GetCurrentContext();
        v8::Context::Scope ContextScope(Context);

        CHECK_V8_ARGS(EArgString);

        std::string Name = *(v8::String::Utf8Value(Isolate, Info[0]));

        auto Func = FindAddonRegisterFunc(Name.c_str());

        if (Func)
        {
            auto Exports = v8::Object::New(Isolate);
            Func(Context, Exports);
            Info.GetReturnValue().Set(Exports);
        }
    }
    void FJsEnvImpl::SetInspectorCallback(const v8::FunctionCallbackInfo<v8::Value>& Info)
    {
#ifndef WITH_QUICKJS
        v8::Isolate* Isolate = Info.GetIsolate();
        v8::Isolate::Scope Isolatescope(Isolate);
        v8::HandleScope HandleScope(Isolate);
        v8::Local<v8::Context> Context = Isolate->GetCurrentContext();
        v8::Context::Scope ContextScope(Context);

        if (!Inspector)
            return;

        CHECK_V8_ARGS(EArgFunction);

        if (!InspectorChannel)
        {
            InspectorChannel = Inspector->CreateV8InspectorChannel();
            InspectorChannel->OnMessage(
                [this](std::string Message)
                {
                    // UE_LOG(LogTemp, Warning, PUERTS_TEXT("<-- %s"), UTF8_TO_TCHAR(Message.c_str()));
                    v8::Isolate::Scope IsolatescopeObject(MainIsolate);
                    v8::HandleScope HandleScopeObject(MainIsolate);
                    v8::Local<v8::Context> ContextInner = DefaultContext.Get(MainIsolate);
                    v8::Context::Scope ContextScopeObject(ContextInner);

                    auto Handler = InspectorMessageHandler.Get(MainIsolate);

                    v8::Local<v8::Value> Args[] = { FV8Utils::ToV8String(MainIsolate, Message.c_str()) };

                    v8::TryCatch TryCatch(MainIsolate);
                    __USE(Handler->Call(ContextInner, ContextInner->Global(), 1, Args));
                    if (TryCatch.HasCaught())
                    {
                        Logger->Error(fmt::format(
                            PUERTS_TEXT("inspector callback exception {}"), FV8Utils::TryCatchToString(MainIsolate, &TryCatch)));
                    }
                });
        }

        InspectorMessageHandler.Reset(Isolate, v8::Local<v8::Function>::Cast(Info[0]));
#endif    // !WITH_QUICKJS
    }
    void FJsEnvImpl::DispatchProtocolMessage(const v8::FunctionCallbackInfo<v8::Value>& Info)
    {
#ifndef WITH_QUICKJS
        v8::Isolate* Isolate = Info.GetIsolate();
        v8::Isolate::Scope Isolatescope(Isolate);
        v8::HandleScope HandleScope(Isolate);
        v8::Local<v8::Context> Context = Isolate->GetCurrentContext();
        v8::Context::Scope ContextScope(Context);

        CHECK_V8_ARGS(EArgString);

        if (InspectorChannel)
        {
            FString Message = FV8Utils::ToFString(Isolate, Info[0]);
            // UE_LOG(LogTemp, Warning, PUERTS_TEXT("--> %s"), *Message);
            InspectorChannel->DispatchProtocolMessage(TCHAR_TO_UTF8(Message));
        }
#endif    // !WITH_QUICKJS
    }
    void FJsEnvImpl::SetTimeout(const v8::FunctionCallbackInfo<v8::Value>& Info)
    {
        CHECK_V8_ARGS(EArgFunction, EArgNumber);

        SetFTickerDelegate(Info, false);
    }

    void FJsEnvImpl::SetFTickerDelegate(const v8::FunctionCallbackInfo<v8::Value>& Info, bool Continue)
    {
        // TODO need impl
    }

    void FJsEnvImpl::RemoveFTickerDelegateHandle(int HandleId)
    {
        // TODO need impl
    }


    void FJsEnvImpl::ClearInterval(const v8::FunctionCallbackInfo<v8::Value>& Info)
    {
        v8::Isolate* Isolate = Info.GetIsolate();
        v8::Local<v8::Context> Context = Isolate->GetCurrentContext();

        // todo - mocha 7.0.1，当reporter为JSON，调用clearTimeout时，可能不传值，或传Null、Undefined过来。暂将其忽略
        if (Info.Length() == 0)
        {
            Logger->Warn(PUERTS_TEXT("Calling ClearInterval with 0 argument."));
        }
        else if (Info[0]->IsNullOrUndefined())
        {
            // 屏蔽这条只在mocha中出现的警告
            // Logger->Warn(PUERTS_TEXT("Calling ClearInterval with a Null or Undefined."));
        }
        else
        {
            CHECK_V8_ARGS(EArgInt32);
            int HandleId = Info[0]->Int32Value(Context).ToChecked();
            RemoveFTickerDelegateHandle(HandleId);
        }
    }
    void FJsEnvImpl::SetInterval(const v8::FunctionCallbackInfo<v8::Value>& Info)
    {
        v8::Isolate* Isolate = Info.GetIsolate();
        v8::Isolate::Scope IsolateScope(Isolate);
        v8::HandleScope HandleScope(Isolate);
        v8::Local<v8::Context> Context = Isolate->GetCurrentContext();
        v8::Context::Scope ContextScope(Context);

        CHECK_V8_ARGS(EArgFunction, EArgNumber);

        SetFTickerDelegate(Info, true);
    }
    void FJsEnvImpl::DumpStatisticsLog(const v8::FunctionCallbackInfo<v8::Value>& Info)
    {
#ifndef WITH_QUICKJS
        v8::HeapStatistics Statistics;

        v8::Isolate* Isolate = Info.GetIsolate();
        v8::Isolate::Scope IsolateScope(Isolate);
        v8::HandleScope HandleScope(Isolate);
        v8::Local<v8::Context> Context = Isolate->GetCurrentContext();
        v8::Context::Scope ContextScope(Context);

        Isolate->GetHeapStatistics(&Statistics);

        FString StatisticsLog = fmt::format(PUERTS_TEXT("------------------------\n"
            "Dump Statistics of V8:\n"
            "total_heap_size: {}\n"
            "total_heap_size_executable: {}\n"
            "total_physical_size: {}\n"
            "total_available_size: {}\n"
            "used_heap_size: {}\n"
            "heap_size_limit: {}\n"
            "malloced_memory: {}\n"
            "external_memory: {}\n"
            "peak_malloced_memory: {}\n"
            "number_of_native_contexts: {}\n"
            "number_of_detached_contexts: {}\n"
            "does_zap_garbage: {}\n"
            "------------------------\n"),
            Statistics.total_heap_size(), Statistics.total_heap_size_executable(), Statistics.total_physical_size(),
            Statistics.total_available_size(), Statistics.used_heap_size(), Statistics.heap_size_limit(), Statistics.malloced_memory(),
            Statistics.external_memory(), Statistics.peak_malloced_memory(), Statistics.number_of_native_contexts(),
            Statistics.number_of_detached_contexts(), Statistics.does_zap_garbage());

        Logger->Info(StatisticsLog);
#endif    // !WITH_QUICKJS
    }
    void FJsEnvImpl::ReleaseManualReleaseDelegate(const v8::FunctionCallbackInfo<v8::Value>& Info)
    {
        // TODO UE impl
    }

    void FJsEnvImpl::RequestMinorGC(const v8::FunctionCallbackInfo<v8::Value>& Info)
    {
        RequestMinorGarbageCollectionForTesting();
    }

    void FJsEnvImpl::RequestMajorGC(const v8::FunctionCallbackInfo<v8::Value>& Info)
    {
        RequestFullGarbageCollectionForTesting();
    }

#pragma endregion BindingFunction

    bool FJsEnvImpl::SetPuertsArgv(v8::Local<v8::Context> Context, v8::Isolate* Isolate, const TArray<std::tuple<FString, const ClassTypeId*, ClassObject*>>& Arguments)
    {
        if (Arguments.empty())
            return true;

        auto MaybeTGameTGJS = Context->Global()->Get(Context, FV8Utils::ToV8String(Isolate, "puerts"));

        if (MaybeTGameTGJS.IsEmpty() || !MaybeTGameTGJS.ToLocalChecked()->IsObject())
        {
            Logger->Error("global.puerts not found!");
            return false;
        }

        auto TGJS = MaybeTGameTGJS.ToLocalChecked()->ToObject(Context).ToLocalChecked();

        auto MaybeArgv = TGJS->Get(Context, FV8Utils::ToV8String(Isolate, "argv"));

        if (MaybeArgv.IsEmpty() || !MaybeArgv.ToLocalChecked()->IsObject())
        {
            Logger->Error("global.puerts.argv not found!");
            return false;
        }

        auto Argv = MaybeArgv.ToLocalChecked()->ToObject(Context).ToLocalChecked();

        auto MaybeArgvAdd = Argv->Get(Context, FV8Utils::ToV8String(Isolate, "add"));

        if (MaybeArgvAdd.IsEmpty() || !MaybeArgvAdd.ToLocalChecked()->IsFunction())
        {
            Logger->Error("global.puerts.argv.add not found!");
            return false;
        }

        auto ArgvAdd = MaybeArgvAdd.ToLocalChecked().As<v8::Function>();

        for (int i = 0; i < Arguments.size(); i++)
        {
            auto [name, classTypeId, Object] = Arguments[i];
            v8::Local<v8::Value> Args[2] = {FV8Utils::ToV8String(Isolate, name), FindOrAddCppObject(Isolate, Context, classTypeId, Object, true)};
            (void)(ArgvAdd->Call(Context, Argv, 2, Args));
        }
        return true;
    }

    void FJsEnvImpl::Tick(float DeltaTime)
    {
        if (Inspector)
            Inspector->Tick();
    }

    bool FJsEnvImpl::ExecuteModule(const FString& ModuleName)
    {
        FString OutPath;
        FString DebugPath;
        TArray<uint8> Data;

        FString ErrInfo;
        if (!LoadFile(PUERTS_TEXT(""), ModuleName, OutPath, DebugPath, Data, ErrInfo))
        {
            Logger->Error(ErrInfo);
            return false;
        }

        // #if UE_BUILD_DEBUG || UE_BUILD_DEVELOPMENT
        //     if (!DebugPath.IsEmpty())
        //         OutPath = DebugPath;
        // #endif

        auto Isolate = MainIsolate;
        v8::Isolate::Scope IsolateScope(Isolate);
        v8::HandleScope HandleScope(Isolate);
        auto Context = v8::Local<v8::Context>::New(Isolate, DefaultContext);
        v8::Context::Scope ContextScope(Context);

#ifndef WITH_QUICKJS
        if (EndsWith(OutPath, ".mjs"))
        {
            v8::TryCatch TryCatch(Isolate);
            v8::Local<v8::Module> RootModule;

            if (!FetchESModuleTree(Context, OutPath).ToLocal(&RootModule))
            {
                check(TryCatch.HasCaught());
                Logger->Error(FV8Utils::TryCatchToString(Isolate, &TryCatch));
                return false;
            }

            if (RootModule->InstantiateModule(Context, ResolveModuleCallback).FromMaybe(false))
            {
                __USE(RootModule->Evaluate(Context));
            }

            if (TryCatch.HasCaught())
            {
                Logger->Error(FV8Utils::TryCatchToString(Isolate, &TryCatch));
                return false;
            }
        }
        else
#endif
        {
            v8::Local<v8::String> Source = FV8Utils::ToV8StringFromFileContent(Isolate, Data);

#if PLATFORM_WINDOWS
            // 修改URL分隔符格式，否则无法匹配Inspector协议在打断点时发送的正则表达式，导致断点失败
            //FString FormattedScriptUrl = DebugPath.Replace(PUERTS_TEXT("/"), PUERTS_TEXT("\\"));
            FString FormattedScriptUrl = std::filesystem::path(DebugPath).lexically_normal().string();
#else
            FString FormattedScriptUrl = DebugPath;
#endif
            v8::Local<v8::String> Name = FV8Utils::ToV8String(Isolate, FormattedScriptUrl);
#if V8_MAJOR_VERSION > 8
            v8::ScriptOrigin Origin(Isolate, Name);
#else
            v8::ScriptOrigin Origin(Name);
#endif
            v8::TryCatch TryCatch(Isolate);

            auto CompiledScript = v8::Script::Compile(Context, Source, &Origin);
            if (CompiledScript.IsEmpty())
            {
                Logger->Error(FV8Utils::TryCatchToString(Isolate, &TryCatch));
                return false;
            }
            __USE(CompiledScript.ToLocalChecked()->Run(Context));
            if (TryCatch.HasCaught())
            {
                Logger->Error(FV8Utils::TryCatchToString(Isolate, &TryCatch));
                return false;
            }
        }
        return true;
    }

    bool FJsEnvImpl::IsJavaScriptInheritanceCpp(v8::Isolate* Isolate, v8::Local<v8::Context> Context, v8::Local<v8::Function> exported_class)
    {
        std::vector<std::string> InheritanceChain{};

        v8::Local<v8::Value> prototype;
        if (exported_class->Get(Context, FV8Utils::ToV8String(Isolate, "prototype")).ToLocal(&prototype) && prototype->IsObject())
        {
            while (!prototype->IsUndefined() && prototype->IsObject())
            {
                v8::Local<v8::String> class_name = prototype.As<v8::Object>()->GetConstructorName();
                InheritanceChain.push_back(FV8Utils::ToFString(Isolate, class_name));
                prototype = prototype.As<v8::Object>()->GetPrototype();
            }    
        }

        bool bInheritance = false;
        for (auto& className : InheritanceChain)
        {
            auto cppType = FindCppTypeClassByName(className.c_str());
            if (cppType)
            {
                bInheritance = true;
                break;
            }
        }

        return bInheritance;
    }

    cross::TypeScriptObject FJsEnvImpl::CreateTypeScriptObject(const FString& ModuleName, const ClassTypeId* ObjectTypeId, ClassObject* Object, std::string_view scriptEditorFieldsJson)
    {
        auto Isolate = MainIsolate;
#ifdef THREAD_SAFE
        v8::Locker Locker(Isolate);
#endif
        v8::Isolate::Scope IsolateScope(Isolate);
        v8::HandleScope HandleScope(Isolate);
        auto Context = v8::Local<v8::Context>::New(Isolate, DefaultContext);
        v8::Context::Scope ContextScope(Context);

        auto LocalRequire = Require.Get(Isolate);

        v8::TryCatch TryCatch(Isolate);

        v8::Local<v8::Value> Args[] = {FV8Utils::ToV8String(Isolate, ModuleName)};

        auto MaybeRet = LocalRequire->Call(Context, v8::Undefined(Isolate), 1, Args);

        if (TryCatch.HasCaught())
        {
            Logger->Error(fmt::format("load module [{}] exception {}", ModuleName, FV8Utils::TryCatchToString(Isolate, &TryCatch)));
            return {};
        }
#ifndef CROSSENGINE_RELEASE
        Inspector->SyncInspectorState();
#endif

        if (!MaybeRet.IsEmpty())
        {
            auto Ret = MaybeRet.ToLocalChecked().As<v8::Object>();

            v8::Local<v8::Value> Val;

            auto MaybeFunc = Ret->Get(Context, FV8Utils::ToV8String(Isolate, "default"));
            if (!(MaybeFunc.ToLocal(&Val) && Val->IsFunction()))
            {
                FString ClassName = cross::PathHelper::GetBaseFileName(std::string(ModuleName), true);
                MaybeFunc = Ret->Get(Context, FV8Utils::ToV8String(Isolate, ClassName));
            }

            if (!(MaybeFunc.ToLocal(&Val) && Val->IsFunction()))
            {
                Logger->Error(fmt::format("module {} don't export default class or same name class", ModuleName));
                return {};
            }

            auto Func = Val.As<v8::Function>();
            bool bInheritance = IsJavaScriptInheritanceCpp(Isolate, Context, Func);
            if (bInheritance)
            {
                Logger->Error(fmt::format("module {}, is Inheritance Cpp Class, which is not support", ModuleName));
                return {};
            }

            // create typescript object
            {
                v8::Local<v8::Function> ClassConstructor = Func;

                v8::Local<v8::Object> instance;

                if (ClassConstructor->NewInstance(Context, 0, nullptr).ToLocal(&instance))
                {
                    if (TryCatch.HasCaught())
                    {
                        Logger->Error(fmt::format("new {} error {}", ModuleName, FV8Utils::TryCatchToString(Isolate, &TryCatch)));
                        return {};
                    }

                    v8::Local<v8::Object> cppInstance = FindOrAddCppObject(Isolate, Context, ObjectTypeId, Object, true).As<v8::Object>();

                    v8::Local<v8::Value> MergeArgs[] = { instance, cppInstance , v8::Object::New(Isolate) };

                    // merge javascript class instance to native object for Composition 
                    __USE(MergeInstance.Get(Isolate)->Call(Context, v8::Undefined(Isolate), 3, MergeArgs));

                    if (TryCatch.HasCaught())
                    {
                        Logger->Error(fmt::format("load module [{}] when MergePrototype(), exception {}", ModuleName, FV8Utils::TryCatchToString(Isolate, &TryCatch)));
                        return {};
                    }

                    auto ret = cross::TypeScriptObject{ Context, cppInstance };
                    SetScriptValue(ret, FString(scriptEditorFieldsJson));

                    ret.Action("Constructor");
                    return ret;
                }
            }
        }
        else
        {
            Logger->Error(fmt::format("module [{}] invalid", ModuleName));
        }
        return {};
    }

    std::string FJsEnvImpl::GetScriptEditorFieldsJson(const cross::TypeScriptObject& typeScriptObject)
    {
        if (!typeScriptObject)
            return "";
        auto Isolate = MainIsolate;
#ifdef THREAD_SAFE
        v8::Locker Locker(Isolate);
#endif
        v8::Isolate::Scope IsolateScope(Isolate);
        v8::HandleScope HandleScope(Isolate);
        auto Context = v8::Local<v8::Context>::New(Isolate, DefaultContext);
        v8::Context::Scope ContextScope(Context);

        v8::TryCatch TryCatch(Isolate);

        v8::Local<v8::Value> Args[] = {typeScriptObject.GObject.Get(Isolate)};

        auto MaybeRet = CEPropertyFunction.GetEditorPropertyFiledsJson.Get(Isolate)->Call(Context, v8::Undefined(Isolate), 1, Args);
        if (TryCatch.HasCaught())
        {
            Logger->Error(fmt::format("GetEditorPropertyFiledsJson failed exception {}", FV8Utils::TryCatchToString(Isolate, &TryCatch)));
            return "";
        }

        if (!MaybeRet.IsEmpty())
        {
            auto Ret = MaybeRet.ToLocalChecked().As<v8::String>();
            return FV8Utils::ToFString(Isolate, Ret);
        }

        return "";
    }

    bool FJsEnvImpl::SetScriptValue(const cross::TypeScriptObject& typeScriptObject, const std::string& scriptDefaultValueJson)
    {
        if (!typeScriptObject)
            return false;

        auto Isolate = MainIsolate;
#ifdef THREAD_SAFE
        v8::Locker Locker(Isolate);
#endif
        v8::Isolate::Scope IsolateScope(Isolate);
        v8::HandleScope HandleScope(Isolate);
        auto Context = v8::Local<v8::Context>::New(Isolate, DefaultContext);
        v8::Context::Scope ContextScope(Context);

        v8::TryCatch TryCatch(Isolate);

        v8::Local<v8::Value> Args[] = {typeScriptObject.GObject.Get(Isolate), FV8Utils::ToV8String(Isolate, scriptDefaultValueJson)};

        auto MaybeRet = CEPropertyFunction.SetPropertyValueJson.Get(Isolate)->Call(Context, v8::Undefined(Isolate), 2, Args);
        if (TryCatch.HasCaught())
        {
            Logger->Error(fmt::format("SetScriptValue failed exception {}", FV8Utils::TryCatchToString(Isolate, &TryCatch)));
            return false;
        }

        if (!MaybeRet.IsEmpty())
        {
            auto Ret = MaybeRet.ToLocalChecked().As<v8::Boolean>();
            return Ret->BooleanValue(Isolate);
        }

        return false;
    }

    void FJsEnvImpl::GetReferenceResource(const cross::TypeScriptObject& typeScriptObject, const std::string& resourceGuid)
    {
        if (!typeScriptObject || resourceGuid.empty())
            return;

        auto Isolate = MainIsolate;
#ifdef THREAD_SAFE
        v8::Locker Locker(Isolate);
#endif
        v8::Isolate::Scope IsolateScope(Isolate);
        v8::HandleScope HandleScope(Isolate);
        auto Context = v8::Local<v8::Context>::New(Isolate, DefaultContext);
        v8::Context::Scope ContextScope(Context);

        v8::TryCatch TryCatch(Isolate);

        v8::Local<v8::Value> Args[] = { typeScriptObject.GObject.Get(Isolate), FV8Utils::ToV8String(Isolate, resourceGuid) };

        (void)CEPropertyFunction.GetReferenceResource.Get(Isolate)->Call(Context, v8::Undefined(Isolate), 2, Args);
        if (TryCatch.HasCaught())
        {
            Logger->Error(fmt::format("GetReferenceResource failed exception {}", FV8Utils::TryCatchToString(Isolate, &TryCatch)));
        }
    }


    bool FJsEnvImpl::LoadFile(const FString& RequiringDir, const FString& ModuleName, FString& OutPath, FString& OutDebugPath,
        TArray<uint8>& Data, FString& ErrInfo)
    {
        if (ModuleLoader->Search(RequiringDir, ModuleName, OutPath, OutDebugPath))
        {
            if (!ModuleLoader->Load(OutPath, Data))
            {
                ErrInfo = fmt::format(PUERTS_TEXT("can not load [{}]"), ModuleName);
                return false;
            }
        }
        else
        {
            ErrInfo = fmt::format(PUERTS_TEXT("can not find [{}]"), ModuleName);
            return false;
        }
        return true;
    }

#pragma region JavaScript
    v8::MaybeLocal<v8::Module> FJsEnvImpl::FetchESModuleTree(v8::Local<v8::Context> Context, const FString& FileName)
    {
        const auto Isolate = Context->GetIsolate();
        if (Contains(PathToModule,FileName))
        {
            return PathToModule[FileName].Get(Isolate);
        }

        Logger->Info(fmt::format(PUERTS_TEXT("Fetch ES Module: {}"), FileName));
        TArray<uint8> Data;
        if (!ModuleLoader->Load(FileName, Data))
        {
            FV8Utils::ThrowException(MainIsolate, fmt::format(PUERTS_TEXT("can not load [{}]"), FileName));
            return v8::MaybeLocal<v8::Module>();
        }

        FString Script = BufferToString(Data.data(), Data.size());
        //FFileHelper::BufferToString(Script, Data.GetData(), Data.Num());

#if V8_MAJOR_VERSION > 8
        v8::ScriptOrigin Origin(
            Isolate, FV8Utils::ToV8String(Isolate, FileName), 0, 0, false, -1, v8::Local<v8::Value>(), false, false, true);
#else
        v8::ScriptOrigin Origin(FV8Utils::ToV8String(Isolate, FileName), v8::Local<v8::Integer>(), v8::Local<v8::Integer>(),
            v8::Local<v8::Boolean>(), v8::Local<v8::Integer>(), v8::Local<v8::Value>(), v8::Local<v8::Boolean>(),
            v8::Local<v8::Boolean>(), v8::True(Isolate));
#endif
        v8::ScriptCompiler::Source Source(FV8Utils::ToV8String(Isolate, Script), Origin);

        v8::Local<v8::Module> Module;
        if (!v8::ScriptCompiler::CompileModule(Isolate, &Source).ToLocal(&Module))
        {
            return v8::MaybeLocal<v8::Module>();
        }

        PathToModule.emplace(FileName, v8::Global<v8::Module>(Isolate, Module));
        FModuleInfo* Info = new FModuleInfo;
        Info->Module.Reset(Isolate, Module);
        HashToModuleInfo.emplace(Module->GetIdentityHash(), Info);

        auto DirName = GetPath(FileName);

#if V8_MAJOR_VERSION >= 9
        v8::Local<v8::FixedArray> module_requests = Module->GetModuleRequests();
        for (int i = 0, Length = module_requests->Length(); i < Length; ++i)
        {
            v8::Local<v8::ModuleRequest> module_request = module_requests->Get(Context, i).As<v8::ModuleRequest>();
            auto RefModuleName = FV8Utils::ToFString(Isolate, module_request->GetSpecifier());
#else
        for (int i = 0, Length = Module->GetModuleRequestsLength(); i < Length; ++i)
        {
            auto RefModuleName = FV8Utils::ToFString(Isolate, Module->GetModuleRequest(i));
#endif
            FString OutPath;
            FString OutDebugPath;
            bool IsESM = false; 
            if (ModuleLoader->Search(DirName, RefModuleName, OutPath, OutDebugPath))
            {
                if (EndsWith(OutPath,PUERTS_TEXT("package.json")))
                {
                    TArray<uint8> PackageData;
                    if (ModuleLoader->Load(OutPath, PackageData))
                    {
                        FString PackageScript = BufferToString(PackageData.data(), PackageData.size());
                        //FFileHelper::BufferToString(PackageScript, PackageData.GetData(), PackageData.Num());

                        bool success = false;
                        cross::SerializeNode node = cross::SerializeNode::ParseFromJson(PackageScript, &success);
                        if (success)
                        {
                            std::string_view TypeName = node.Value("type","");
                            std::string_view MainFile = node.Value("main", "");
                            if (TypeName == "module")
                            {
                                IsESM = true;
                            }
                            if (!MainFile.empty())
                            {
                                FString ESMMainOutPath;
                                FString ESMMainOutDebugPath;
                                if (ModuleLoader->Search(GetPath(OutPath), FString(MainFile), ESMMainOutPath, ESMMainOutDebugPath))
                                {
                                    OutPath = ESMMainOutPath;
                                }
                            }
                        }
                        else
                        {
                            LOG_ERROR("Parse {} failed.", OutPath);
                        }

                        /*v8::Local<v8::Value> Args[] = { FV8Utils::ToV8String(Isolate, PackageScript) };

                        auto MaybeRet = GetESMMain.Get(Isolate)->Call(Context, v8::Undefined(Isolate), 1, Args);

                        v8::Local<v8::Value> ESMMainValue;
                        if (MaybeRet.ToLocal(&ESMMainValue) && ESMMainValue->IsString())
                        {
                            FString ESMMain = FV8Utils::ToFString(Isolate, ESMMainValue);
                            FString ESMMainOutPath;
                            FString ESMMainOutDebugPath;
                            if (ModuleLoader->Search(GetPath(OutPath), ESMMain, ESMMainOutPath, ESMMainOutDebugPath))
                            {
                                OutPath = ESMMainOutPath;
                            }
                        }*/
                    }
                }
                if (EndsWith(OutPath, PUERTS_TEXT(".mjs")) || (IsESM && EndsWith(OutPath,PUERTS_TEXT(".js"))))
                {
                    auto RefModule = FetchESModuleTree(Context, OutPath);
                    if (RefModule.IsEmpty())
                    {
                        return v8::MaybeLocal<v8::Module>();
                    }
                    Info->ResolveCache.emplace(RefModuleName, v8::Global<v8::Module>(Isolate, RefModule.ToLocalChecked()));
                    continue;
                }
            }

            auto RefModule = FetchCJSModuleAsESModule(Context, ((EndsWith(OutPath, PUERTS_TEXT(".cjs")) || !IsESM) && !OutPath.empty()) ? OutPath : RefModuleName);

            if (RefModule.IsEmpty())
            {
                FV8Utils::ThrowException(
                    MainIsolate, fmt::format(PUERTS_TEXT("can not resolve [{}], import by [{}]"), RefModuleName, FileName));
                return v8::MaybeLocal<v8::Module>();
            }

            Info->ResolveCache.emplace(RefModuleName, v8::Global<v8::Module>(Isolate, RefModule.ToLocalChecked()));
        }

        return Module;
    }

    v8::MaybeLocal<v8::Module> FJsEnvImpl::FetchCJSModuleAsESModule(v8::Local<v8::Context> Context, const FString& ModuleName)
    {
#if V8_MAJOR_VERSION < 8
        FV8Utils::ThrowException(
            MainIsolate, FString::Printf(PUERTS_TEXT("V8_MAJOR_VERSION < 8 not support fetch CJS module [%s] from ESM"), *ModuleName));
        return v8::MaybeLocal<v8::Module>();
#else
        const auto Isolate = Context->GetIsolate();

        Logger->Info(fmt::format(PUERTS_TEXT("ESM Fetch CJS Module: {}"), ModuleName));

        v8::Local<v8::Value> Args[] = { FV8Utils::ToV8String(Isolate, ModuleName) };

        auto MaybeRet = Require.Get(Isolate)->Call(Context, v8::Undefined(Isolate), 1, Args);

        if (MaybeRet.IsEmpty())
        {
            return v8::MaybeLocal<v8::Module>();
        }

        auto CJSValue = MaybeRet.ToLocalChecked();
        std::vector<v8::Local<v8::String>> ExportNames = {
            v8::String::NewFromUtf8(Isolate, "default", v8::NewStringType::kNormal).ToLocalChecked() };

        if (CJSValue->IsObject())
        {
            auto JsObject = CJSValue->ToObject(Context).ToLocalChecked();
            auto Keys = JsObject->GetOwnPropertyNames(Context).ToLocalChecked();
            for (decltype(Keys->Length()) i = 0; i < Keys->Length(); ++i)
            {
                v8::Local<v8::Value> Key;
                if (Keys->Get(Context, i).ToLocal(&Key))
                {
                    // UE_LOG(LogTemp, Warning, PUERTS_TEXT("---'%s' '%s'"), *ModuleName, *FV8Utils::ToFString(Isolate, Key));
                    ExportNames.push_back(Key->ToString(Context).ToLocalChecked());
                }
            }
        }
        
        v8::Local<v8::Module> SyntheticModule = v8::Module_CreateSyntheticModule_Without_Stl(
            Isolate, FV8Utils::ToV8String(Isolate, ModuleName), ExportNames.data(), ExportNames.size(), [](v8::Local<v8::Context> ContextInner, v8::Local<v8::Module> Module) -> v8::MaybeLocal<v8::Value> {
                const auto IsolateInner = ContextInner->GetIsolate();
                auto Self = static_cast<FJsEnvImpl*>(FV8Utils::IsolateData<ICppObjectMapper>(IsolateInner));

                const auto ModuleInfoIt = Self->FindModuleInfo(Module);
                check(ModuleInfoIt != Self->HashToModuleInfo.end());
                auto CJSValueInner = ModuleInfoIt->second->CJSValue.Get(IsolateInner);

                __USE(Module->SetSyntheticModuleExport(IsolateInner, v8::String::NewFromUtf8(IsolateInner, "default", v8::NewStringType::kNormal).ToLocalChecked(), CJSValueInner));

                if (CJSValueInner->IsObject())
                {
                    auto JsObjectInner = CJSValueInner->ToObject(ContextInner).ToLocalChecked();
                    auto KeysInner = JsObjectInner->GetOwnPropertyNames(ContextInner).ToLocalChecked();
                    for (decltype(KeysInner->Length()) ii = 0; ii < KeysInner->Length(); ++ii)
                    {
                        v8::Local<v8::Value> KeyInner;
                        v8::Local<v8::Value> ValueInner;
                        if (KeysInner->Get(ContextInner, ii).ToLocal(&KeyInner) && JsObjectInner->Get(ContextInner, KeyInner).ToLocal(&ValueInner))
                        {
                            // UE_LOG(LogTemp, Warning, PUERTS_TEXT("-----set '%s'"), *FV8Utils::ToFString(IsolateInner, KeyInner));
                            __USE(Module->SetSyntheticModuleExport(IsolateInner, KeyInner->ToString(ContextInner).ToLocalChecked(), ValueInner));
                        }
                    }
                }

                return v8::MaybeLocal<v8::Value>(v8::True(IsolateInner));
            });

        FModuleInfo* Info = new FModuleInfo;
        Info->Module.Reset(Isolate, SyntheticModule);
        Info->CJSValue.Reset(Isolate, CJSValue);
        HashToModuleInfo.emplace(SyntheticModule->GetIdentityHash(), Info);

        return SyntheticModule;
#endif
    }


    std::unordered_multimap<int, FJsEnvImpl::FModuleInfo*>::iterator FJsEnvImpl::FindModuleInfo(v8::Local<v8::Module> Module)
    {
        auto Range = HashToModuleInfo.equal_range(Module->GetIdentityHash());
        for (auto It = Range.first; It != Range.second; ++It)
        {
            if (It->second->Module == Module)
            {
                return It;
            }
        }
        return HashToModuleInfo.end();
    }


    v8::MaybeLocal<v8::Module> FJsEnvImpl::ResolveModuleCallback(v8::Local<v8::Context> Context, v8::Local<v8::String> Specifier,
#if V8_MAJOR_VERSION >= 9
        v8::Local<v8::FixedArray> ImportAttributes,    // not implement yet
#endif
        v8::Local<v8::Module> Referrer)
    {
        auto Self = static_cast<FJsEnvImpl*>(FV8Utils::IsolateData<ICppObjectMapper>(Context->GetIsolate()));
        const auto ItModuleInfo = Self->FindModuleInfo(Referrer);
        check(ItModuleInfo != Self->HashToModuleInfo.end());
        const auto RefModuleName = FV8Utils::ToFString(Context->GetIsolate(), Specifier);
        auto ItRefModule = ItModuleInfo->second->ResolveCache.find(RefModuleName);
        check(ItRefModule != ItModuleInfo->second->ResolveCache.end());
        return ItRefModule->second.Get(Context->GetIsolate());
    }

#pragma endregion JavaScript


}
