#include "JsEnumRegister.h"
#include <map>
namespace PUERTS_NAMESPACE {

static JSEnumFiledInfo* EnumFiledDuplicate(JSEnumFiledInfo* Arr)
{
    if (Arr == nullptr)
        return nullptr;
    int Count = 0;
    while (true)
    {
        if (Arr[Count++].Name == nullptr)
            break;
    }
    JSEnumFiledInfo* Ret = new JSEnumFiledInfo[Count];
    ::memcpy(Ret, Arr, sizeof(JSEnumFiledInfo) * Count);
    return Ret;
}

JSEnumDefinition* JSEnumDefinitionDuplicate(const JSEnumDefinition* EnumDefinition)
{
    auto Ret = new JSEnumDefinition;
    ::memcpy(Ret, EnumDefinition, sizeof(JSEnumDefinition));
    Ret->EnumFileds = EnumFiledDuplicate(EnumDefinition->EnumFileds);
    return Ret;
}

void JSEnumDefinitionDelete(JSEnumDefinition* EnumDefinition)
{
    delete[] EnumDefinition->EnumFileds;
    delete EnumDefinition;
}

class JsEnumRegister
{
public:
    JsEnumRegister() = default;
    ~JsEnumRegister()
    {
        for (auto& item : CDataNameToEnumDefinition)
        {
            JSEnumDefinitionDelete(item.second);
        }
        CDataNameToEnumDefinition.clear();
    }
    void RegisterJSEnum(const JSEnumDefinition& EnumDefinition)
    {
        auto cd_iter = CDataNameToEnumDefinition.find(EnumDefinition.EnumName);
        if (cd_iter != CDataNameToEnumDefinition.end())
        {
            JSEnumDefinitionDelete(cd_iter->second);
        }
        CDataNameToEnumDefinition[EnumDefinition.EnumName] = JSEnumDefinitionDuplicate(&EnumDefinition);
    }
    void ForeachRegisterEnum(std::function<void(const JSEnumDefinition* EnumDefinition)> callback)
    {
        for (auto& item : CDataNameToEnumDefinition)
        {
            callback(item.second);
        }
    }
    const JSEnumDefinition* FindCppTypeEnumByName(const std::string& Name)
    {
        auto item = CDataNameToEnumDefinition.find(Name);
        if (item == CDataNameToEnumDefinition.end())
        {
            return nullptr;
        }
        else
        {
            return item->second;
        }
    }

    std::vector<const JSEnumDefinition*> FindCppTypeEnumByNamespace(const std::string& Namespace)
    {
        std::vector<const JSEnumDefinition*> ret;
        for (auto& item : CDataNameToEnumDefinition)
        {
            if (item.first.starts_with(Namespace + '.'))
            {
                ret.push_back(item.second);
            }
        }
        return ret;
    }

private:
    std::map<std::string, JSEnumDefinition*> CDataNameToEnumDefinition;
};
}   // namespace PUERTS_NAMESPACE

puerts::JsEnumRegister* GetJsEnumRegister()
{
    static puerts::JsEnumRegister S_JsEnumRegister;
    return &S_JsEnumRegister;
}

void RegisterJSEnum(const puerts::JSEnumDefinition& EnumDefinition)
{
    GetJsEnumRegister()->RegisterJSEnum(EnumDefinition);
}

void ForeachRegisterEnum(std::function<void(const puerts::JSEnumDefinition* EnumDefinition)> callback)
{
    GetJsEnumRegister()->ForeachRegisterEnum(callback);
}
const puerts::JSEnumDefinition* FindCppTypeEnumByName(const char* Name)
{
    return GetJsEnumRegister()->FindCppTypeEnumByName(Name);
}

size_t FindCppTypeEnumByNamespace(const char* Namespace, const puerts::JSEnumDefinition** outEnumDefinition, size_t outEnumDefinitionLen)
{
    auto enumArray = GetJsEnumRegister()->FindCppTypeEnumByNamespace(Namespace);
    if (outEnumDefinition != nullptr)
    {
        for (size_t i =0; i< std::min(outEnumDefinitionLen, enumArray.size()); ++i)
        {
            outEnumDefinition[i] = enumArray[i];
        }
    }
    return enumArray.size();
}
