#pragma once
#include "PlatformDefs.h"
#include "CEMetaMacros.h"

namespace cross
{
	enum class CEMeta(Cli) EditorKey : SInt32
	{
		Unknown,
		A,
		B,
		C,
		D,
		E,
		F,
		G,
		H,
		I,
		J,
		K,
		L,
		M,
		N,
		O,
		P,
		Q,
		R,
		S,
		T,
		U,
		V,
		W,
		X,
		Y,
		Z,
		Num0,
		Num1,
		Num2,
		Num3,
		Num4,
		Num5,
		Num6,
		Num7,
		Num8,
		Num9,
		OpenBracket,
		CloseBracket,
		Slash,
		Dot,
		Minus,
		Equals,
		Semi,
		BackSlash,
		SingleQuote,
		BackQuote,
		Comma,
		Left,
		Right,
		Up,
		Down,
		PageUp,
		PageDown,
		Home,
		End,
		Backspace,
		Delete,
		Tab,
		Enter,
		Break,
		Escape,
		Space,
		F1,
		F2,
		F3,
		F4,
		F5,
		F6,
		F7,
		F8,
		F9,
		F10,
		F11,
		F12,
		Control,
		Shift,
		Alt,
		Window,
		LeftButton,
		RightButton,
		MiddleButton
	};
}