#pragma once
#include "ECS/Develop/Framework.h"
#include "ECS/Develop/Framework/Types.h"
#include "CrossBase/Math/CrossMath.h"
#include "CECommon/Common/GameSystemBase.h"
#include "CECommon/Common/FrameContainer.h"
#include "RenderEngine/RenderWorldConst.h"
#include "RenderEngine/LightMapSystemR.h"
#if CROSSENGINE_EDITOR
    #include "Editor/LightmapBaker/TLBSDataProtocol/BakingCoreDefine.h"

#ifdef LIGHTMAP_BAKE_SYSTEM_ENABLE
    #include "Editor/LightmapBaker/TLBSContext.h"
#else
namespace GPUBaking {
using MaterialBakePtr = std::shared_ptr<LightMapOutInfo>;
};   // namespace GPUBaking
#endif

#endif
// lightmap bake for editor
namespace cross {
struct LightMapEditorComponentG : ecs::IComponent
{
    CEComponentInternal(SystemType = LightMapBakeSystemG)
    CEFunction(Reflect) 
    static ecs::ComponentDesc* GetDesc();

public:
    CEMeta(<PERSON><PERSON><PERSON>, Editor) CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "")) Float2 mResolution{64, 64};
    CEMeta(Serialize, Editor) CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "")) float mDiffuseBoost{1.0f};
    CEMeta(Serialize, Editor) CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "")) bool mEnable{true};
    CEMeta(Serialize, Editor) CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "")) bool mCastShadow{true};
    CEMeta(Serialize, Editor) CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "")) bool mForceBakeLightMap{false};

    CE_Serialize_Deserialize;

    friend class LightMapBakeSystemG;
};

#if CROSSENGINE_EDITOR
class LightMapBakeSystemG : public GameSystemBase
{
    CESystemInternal(ComponentType = LightMapEditorComponentG)
    DEFINE_COMPONENT_READER_WRITER(LightMapEditorComponentG, LightMapEditorComponentReader, LightMapEditorComponentWriter) 
public: 
    CEFunction(Reflect) static LightMapBakeSystemG* CreateInstance();

    virtual void Release() override;
    /// <summary>
    ///
    /// </summary>
    /// <param name="frameParam"></param>
    /// <param name="elapsedTime"></param>
    virtual void OnBeginFrame(FrameParam* frameParam) override;

    virtual RenderSystemBase* GetRenderSystem() override
    {
        return nullptr;
    }

    virtual void NotifyAddRenderSystemToRenderWorld() override
    {
        mIsRenderObjectOwner = false;
    };

public:
    void StartLightmapBaker();

    int GetLightmapBakerResult() const
    {
        return mBakeStatus;
    }

    void OnBakerFinished();

public:
    CEFunction(Editor) bool GetLightMapAbleToUse(UInt64 entityID) const;

    CEFunction(Editor) bool GetAutoJoinBakeHint(UInt64 entityID) const;

    // engine data interface (ModelComponent or FoliageComponent)
    const MeshAssetDataResourcePtr GetModelAsset(ecs::EntityID entityID) const;

    size_t GetSubModelCount(ecs::EntityID entityID) const;

    MaterialInterfacePtr GetModelMaterial(ecs::EntityID entityID, int subModelIndex) const;

    void RefreshLightMap();

private:
    bool isApplyLightMap(ecs::EntityID entityID) const;

    bool isApplyShadowMap(ecs::EntityID entityID) const;

    bool isUseDirectionality(ecs::EntityID entityID) const;

    void CopyLightMapFileReference();

    void BakerGatherSceneInfo();

    void BakeTerrainTexture(FrameParam* frameParam);

    bool IsBakeTextureCompleted();

    void FinishBaker(const std::map<cross::ecs::EntityID, GPUBaking::LightMapOutInfo*>* lmOutMap);

    cross::MaterialInterfacePtr CreateBakeMaterialInst(cross::MaterialInterfacePtr from, const std::string& bakeMatFileName, const std::string& proxyMatName, cross::TexturePtr texture, GPUBaking::MaterialBakePtr matBake);

    void CopyMaterialProperty(cross::MaterialInterfacePtr from, cross::MaterialInterfacePtr to);

private:
    int ReadBakeConfig();

public:
#    define LIGHTMAP_EDITOR_COMPONENT_PROPERTY(PROP, TYPE)                                                                                                                                                                                     \
        CEFunction(Editor) inline void SetLightMap##PROP(const LightMapEditorComponentWriter& comp, const TYPE& val)                                                                                                                           \
        {                                                                                                                                                                                                                                      \
            comp->m##PROP = val;                                                                                                                                                                                                               \
        }                                                                                                                                                                                                                                      \
        CEFunction(Editor) inline const TYPE GetLightMap##PROP(const LightMapEditorComponentReader& comp) const                                                                                                                                \
        {                                                                                                                                                                                                                                      \
            return comp->m##PROP;                                                                                                                                                                                                              \
        }

    LIGHTMAP_EDITOR_COMPONENT_PROPERTY(Enable, bool);
    LIGHTMAP_EDITOR_COMPONENT_PROPERTY(Resolution, Float2);
    LIGHTMAP_EDITOR_COMPONENT_PROPERTY(CastShadow, bool);
    LIGHTMAP_EDITOR_COMPONENT_PROPERTY(ForceBakeLightMap, bool);
    LIGHTMAP_EDITOR_COMPONENT_PROPERTY(DiffuseBoost, float);

    CEFunction(Editor) void SetLightMapRefresh(const LightMapEditorComponentWriter& comp, bool refresh);

protected:
    LightMapBakeSystemG();

    virtual ~LightMapBakeSystemG();

public:
    static SerializeNode SerializeLightMapBakeComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr);

    static void DeserializeLightMapBakeComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr);

    static void PostDeserializeLightMapBakeComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId);

private:
    bool mIsRenderObjectOwner{true};
    std::atomic<int> mBakeStatus{BAKE_COMPLETED};
    float mBakeDuration{0.f};

private:
    enum BAKE_STATUS
    {
        BAKE_FINISH_FAILED = -1,
        BAKE_START = 0,
        BAKE_GATHER_SCENE_INFO = 1,
        BAKE_GENERATOR_TEXTURE = 2,
        BAKE_GENERATOR_TEXTURE_FINISH = 3,
        BAKE_START_JOB = 4,
        BAKE_FINISH_SUCCESS = 100,
        BAKE_COMPLETED = 101,
    };
};
#endif
}   // namespace cross