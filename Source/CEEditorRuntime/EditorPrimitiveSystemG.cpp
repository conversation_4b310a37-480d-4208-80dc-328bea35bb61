#include "EnginePrefix.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "RenderEngine/PrimitiveGenerator.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "Runtime/GameWorld/PrimitiveRenderSystemG.h"
#include "EditorPrimitiveSystemG.h"
#include "Runtime/GameWorld/DecalSystemG.h"
#include "Runtime/GameWorld/ModelSystemG.h"
#include "Runtime/GameWorld/TransformSystemG.h"
#include "Runtime/GameWorld/CameraSystemG.h"
#include "Runtime/GameWorld/AABBSystemG.h"
#include "Runtime/GameWorld/RenderPropertySystemG.h"
#include "Runtime/GameWorld/LightSystemG.h"
#include "Runtime/GameWorld/ReflectionProbeSystemG.h"
#include "Runtime/GameWorld/SkyLightSystemG.h"
#include "Runtime/GameWorld/SkyAtmosphereSystemG.h"
#include "Runtime/GameWorld/CloudSystemG.h"
#include "Runtime/GameWorld/PostProcessVolumeSystemG.h"
#include "Runtime/GameWorld/ScriptSystemG.h"
#include "Runtime/GameWorld/ParticleSimulationSystemG.h"
#include "Runtime/GameWorld/EntityMetaSystem.h"
#include "Runtime/GameWorld/FoliageSystemG.h"
#include "Runtime/GameWorld/TerrainSystemG.h"
#include "Runtime/GameWorld/WorldPartition/WorldLoadingSystemG.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/SettingsManager.h"
#include "Resource/AssetStreaming.h"
#include "Resource/ResourceManager.h"

namespace cross {
SerializeNode SerializeEditorPrimitiveComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr)
{
    auto compPtr = static_cast<EditorPrimitiveComponentG*>(componentPtr);
    SerializeContext context;
    SerializeNode outNode = compPtr->Serialize(context);
    return outNode;
}

void DeserializeEditorPrimitiveComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr)
{
    if (json.IsNull())
        return;

    auto compPtr = static_cast<EditorPrimitiveComponentG*>(componentPtr);
    if (json.IsObject())
    {
        SerializeContext context;
        compPtr->Deserialize(json, context);
    }
}

void PostDeserializeEditorPrimitiveComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId) {}

ecs::ComponentDesc* EditorPrimitiveComponentG::GetDesc()
{
    return EngineGlobal::GetECSFramework().CreateOrGetGameComponentDesc<cross::EditorPrimitiveComponentG>({ false, true, true },
        SerializeEditorPrimitiveComponent, DeserializeEditorPrimitiveComponent, PostDeserializeEditorPrimitiveComponent);
}

EditorPrimitiveSystemG* EditorPrimitiveSystemG::CreateInstance()
{
    EditorPrimitiveSystemG* system = new EditorPrimitiveSystemG();
    return system;
}

EditorPrimitiveSystemG::EditorPrimitiveSystemG()
{
    mTrianglesLayout.AddVertexChannelLayout(VertexChannel::Position0, VertexFormat::Float3, 0);
    mTrianglesLayout.AddVertexChannelLayout(VertexChannel::Normal0, VertexFormat::Float3, 12);
    mTrianglesLayout.AddVertexChannelLayout(VertexChannel::TexCoord0, VertexFormat::Float2, 24);
    mTrianglesLayout.AddVertexChannelLayout(VertexChannel::Color0, VertexFormat::UByte4_Norm, 32);
}

EditorPrimitiveSystemG::~EditorPrimitiveSystemG() {}

void EditorPrimitiveSystemG::Release()
{
    delete this;
}

void EditorPrimitiveSystemG::OnBeginFrame(FrameParam* frameParam)
{
    mCurFrameAllocator = frameParam->GetFrameAllocator();
}

void EditorPrimitiveSystemG::OnFirstUpdate(FrameParam* frameParam)
{
    mPrimitiveRenderSystem = mGameWorld->GetGameSystem<PrimitiveRenderSystemG>();
    mRenderPropertySystem = mGameWorld->GetGameSystem<RenderPropertySystemG>();
    mAABBSystem = mGameWorld->GetGameSystem<AABBSystemG>();
    mCameraSystem = mGameWorld->GetGameSystem<CameraSystemG>();
    mLightSystem = mGameWorld->GetGameSystem<LightSystemG>();
    mReflectionProbeSystem = mGameWorld->GetGameSystem<ReflectionProbeSystemG>();
    mTransfromSystem = mGameWorld->GetGameSystem<TransformSystemG>();
    mEntityMetaSystem = mGameWorld->GetGameSystem<EntityMetaSystem>();
}

void EditorPrimitiveSystemG::OnEndFrame(FrameParam* frameParam)
{
    mCurFrameAllocator = nullptr;
}

void EditorPrimitiveSystemG::DrawTriangles(UInt8* vertexData, UInt16* indexData, UInt16 vertexCount, UInt16 indexCount, MaterialPtr material)
{
    PrimitiveData* newTrianglesData = reinterpret_cast<PrimitiveData*>(mCurFrameAllocator->Allocate(sizeof(PrimitiveData), FRAME_STAGE_GAME_RENDER));
    new (newTrianglesData) PrimitiveData(vertexData, indexData, vertexCount, indexCount, indexCount / 3, mTrianglesLayout, mTianglesTopology);

    mPrimitiveRenderSystem->DrawPrimitive(newTrianglesData, Float4x4A::Identity(), material);
}

void EditorPrimitiveSystemG::DrawTriangles(PrimitiveData& primitive, MaterialPtr material, Float4x4A worldTransform)
{
    PrimitiveData* newTrianglesData = reinterpret_cast<PrimitiveData*>(mCurFrameAllocator->Allocate(sizeof(PrimitiveData), FRAME_STAGE_GAME_RENDER));
    new (newTrianglesData) PrimitiveData(
        primitive.GetVertexData(), 
        primitive.GetIndexArray(), 
        primitive.GetVertexCount(), 
        primitive.GetIndexCount(), 
        primitive.GetPrimitiveCount(), 
        primitive.GetVertexLayout(), 
        primitive.GetPrimitiveTopology());

    mPrimitiveRenderSystem->DrawPrimitive(newTrianglesData, worldTransform, material);
}

MaterialPtr EditorPrimitiveSystemG::GetOrCreateEditorMaterial(const std::string& materialPath)
{
    UniqueString stringid{materialPath.c_str()};
    if (mUsedStaticMaterial.find(stringid) != mUsedStaticMaterial.end())
    {
        return mUsedStaticMaterial[stringid];
    }
    else
    {
        auto material = cross::TypeCast<cross::resource::Material>(gAssetStreamingManager->LoadSynchronously(materialPath));
        mUsedStaticMaterial.emplace(stringid, material);
        return mUsedStaticMaterial[stringid];
    }
}

MeshAssetDataResourcePtr EditorPrimitiveSystemG::GetOrCreateEditorModel(const std::string& meshPath)
{
    UniqueString stringid{meshPath.c_str()};
    if (mUsedModel.find(stringid) != mUsedModel.end()) 
    {
        return mUsedModel[stringid];
    }
    else
    {
        auto resourcePtr = gAssetStreamingManager->LoadSynchronously(meshPath);
        auto meshResource = cross::TypeCast<cross::resource::MeshAssetDataResource>(resourcePtr);
        mUsedModel.emplace(stringid, meshResource);
        return mUsedModel[stringid];
    }
}

void EditorPrimitiveSystemG::RegisterRemoveEditorSelectedDrawingEventHandler(ecs::EntityID entity, void const * const identify, RemoveEditorSelectedDrawingEventCallback callback, std::string_view name)
{
    cross::ecs::TGetComponentTypeT<EditorPrimitiveComponentG> component = mGameWorld->GetComponent<EditorPrimitiveComponentG>(entity);
    if(false == component.IsValid())
    {
        LOG_ERROR("Can not get EditorPrimitiveComponentG, did you forget to add it in GetRelatedECSComponentBitMask");
        return;
    }
    EditorPrimitiveComponentWriter const& writer = component.Write();
    writer->mRemoveEditorSelectedDrawingHandlers[identify] = RemoveEditorSelectedDrawingEventHandler(callback, name);
}

void EditorPrimitiveSystemG::RegisterDrawEditorSelectedEntityEventHandler(ecs::EntityID entity, void const * const identify, DrawEditorSelectedEntityEventCallback callback, std::string_view name)
{
    cross::ecs::TGetComponentTypeT<EditorPrimitiveComponentG> component = mGameWorld->GetComponent<EditorPrimitiveComponentG>(entity);
    if(false == component.IsValid())
    {
        LOG_ERROR("Can not get EditorPrimitiveComponentG, did you forget to add it in GetRelatedECSComponentBitMask");
        return;
    }
    EditorPrimitiveComponentWriter const& writer = component.Write();
    writer->mDrawEditorSelectedEntityEventHandlers[identify] = DrawEditorSelectedEntityEventHandler(callback, name);
}

void EditorPrimitiveSystemG::UnregisterRemoveEditorSelectedDrawingEventHandler(ecs::EntityID entity, void const * const identify)
{
    cross::ecs::TGetComponentTypeT<EditorPrimitiveComponentG> component = mGameWorld->GetComponent<EditorPrimitiveComponentG>(entity);
    if(false == component.IsValid())
    {
        LOG_ERROR("Can not get EditorPrimitiveComponentG, did you forget to add it in GetRelatedECSComponentBitMask");
        return;
    }
    EditorPrimitiveComponentWriter const& writer = component.Write();
    writer->mRemoveEditorSelectedDrawingHandlers.erase(identify);
}

void EditorPrimitiveSystemG::UnregisterDrawEditorSelectedEntityEventHandler(ecs::EntityID entity, void const * const identify)
{
    cross::ecs::TGetComponentTypeT<EditorPrimitiveComponentG> component = mGameWorld->GetComponent<EditorPrimitiveComponentG>(entity);
    if(false == component.IsValid())
    {
        LOG_ERROR("Can not get EditorPrimitiveComponentG, did you forget to add it in GetRelatedECSComponentBitMask");
        return;
    }
    EditorPrimitiveComponentWriter const& writer = component.Write();
    writer->mDrawEditorSelectedEntityEventHandlers.erase(identify);
}

void EditorPrimitiveSystemG::RemoveEditorSelectedDrawing()
{
    for (const auto& renderPropertyH : mGameWorld->Query<RenderPropertyComponentG>())
    {
        // remove highlight
        if (mRenderPropertySystem->HasRuntimeRenderEffect(renderPropertyH.Read(), RenderEffectTag::EditorHighlight))
        {
            mRenderPropertySystem->RemoveRuntimeRenderEffect(renderPropertyH.Write(), RenderEffectTag::EditorHighlight);
        }
    }

    for (const auto& aabbH : mGameWorld->Query<AABBComponentG>())
    {
        // remove aabb
        if (!EngineGlobal::GetSettingMgr()->GetCullingVisualizationEnable())
        {
            mAABBSystem->SetShowAABB(aabbH.Write(), false);
        }
    }

    // remove frustum
    auto cameras = mGameWorld->Query<CameraComponentG>();
    for (const auto& cameraH : cameras)
    {
        mCameraSystem->SetFrustumShow(cameraH.Write(), false);
    }

    // remove light boundary
    auto lights = mGameWorld->Query<LightComponentG>();
    for (const auto& lightH : lights)
    {
        mLightSystem->SetBoundaryShow(lightH.Write(), false);
    }

    // remove reflectionProbe
    auto reflectionProbes = mGameWorld->Query<ReflectionProbeComponentG>();
    for (const auto& rpH : reflectionProbes)
    {
        mReflectionProbeSystem->SetReflectionProbeShow(rpH.Write(), false);
    }

    // Decal
    auto DecalSystem = mGameWorld->GetGameSystem<DecalSystemG>();
    if (DecalSystem)
    {
        auto decals = mGameWorld->Query<DecalComponentG>();

        for (const auto& decalH : decals)
        {
            DecalSystem->SetDecalWireFrameShow(decalH.Write(), false);
        }
    }

    // Post process
    auto ppvSystem = mGameWorld->GetGameSystem<PostProcessVolumeSystemG>();
    if (ppvSystem)
    {
        auto ppvs = mGameWorld->Query<PostProcessVolumeComponentG>();
        for (const auto& ppvH : ppvs)
        {
            ppvSystem->SetSelected(ppvH.Write(), false);
        }
    }

    // callbacks
    for(ecs::EntityID entity : mSelectedEntities)
    {
        auto const& component = mGameWorld->GetComponent<EditorPrimitiveComponentG>(entity);
        if(component.IsValid())
        {
            EditorPrimitiveComponentReader const& reader = component.Read();
            for(robin_hood::pair<void const *, RemoveEditorSelectedDrawingEventHandler> const & handler : reader->mRemoveEditorSelectedDrawingHandlers)
            {
                handler.second.OnExecute(entity);
            }
        }
    }

    // clear selected entities
    for (const auto& entity : mSelectedEntities)
    {
        if (mGameWorld->IsEntityAlive(entity))
        {
            mEntityMetaSystem->SetSelected(entity, false);
        }
    }
    mSelectedEntities.clear();
}

bool EditorPrimitiveSystemG::DrawEditorSelectedEntity(ecs::EntityID entity)
{
    if (mGameWorld->IsEntityAlive(entity))
    {
        mSelectedEntities.insert(entity);
        mEntityMetaSystem->SetSelected(entity, true);
    }

    // callbacks
    {
        auto const& component = mGameWorld->GetComponent<EditorPrimitiveComponentG>(entity);
        if(component.IsValid())
        {
            EditorPrimitiveComponentReader const& reader = component.Read();
            for(robin_hood::pair<void const *, DrawEditorSelectedEntityEventHandler> const & handler : reader->mDrawEditorSelectedEntityEventHandlers)
            {
                handler.second.OnExecute(entity);
            }
        }
    }

    // particle system
    auto [renderPropertyH, aabbH, particleSystemH] = mGameWorld->GetComponent<RenderPropertyComponentG, AABBComponentG, ParticleSystemComponentG>(entity);
    if (renderPropertyH.IsValid())
    {
        // highlight
        // Support particle
        mRenderPropertySystem->AddRuntimeRenderEffect(renderPropertyH.Write(), RenderEffectTag::EditorHighlight);
        
        if (mShowSelectedAABB && aabbH.IsValid())
        {
            mAABBSystem->SetShowAABB(aabbH.Write(), true);
        }
    }
    else
    {
        // frustum
        auto cameraH = mGameWorld->GetComponent<CameraComponentG>(entity);
        if (cameraH.IsValid() && mCameraSystem->GetMainCamera() != cameraH.GetEntityID())
        {
            mCameraSystem->SetFrustumShow(cameraH.Write(), true);
        }
    }

    // light boundary
    auto lightH = mGameWorld->GetComponent<LightComponentG>(entity);
    if (lightH.IsValid())
    {
        mLightSystem->SetBoundaryShow(lightH.Write(), true);
    }

    // ReflectionProbe
    auto rpH = mGameWorld->GetComponent<ReflectionProbeComponentG>(entity);
    if (rpH.IsValid())
    {
        mReflectionProbeSystem->SetReflectionProbeShow(rpH.Write(), true);
    }

    // Decal
    auto DecalSystem = mGameWorld->GetGameSystem<DecalSystemG>();
    if (mGameWorld->HasComponent<DecalComponentG>(entity) && DecalSystem)
    {
        auto decalH = mGameWorld->GetComponent<DecalComponentG>(entity);
        DecalSystem->SetDecalWireFrameShow(decalH.Write(), true);
    }

    // Post process
    auto ppvSystem = mGameWorld->GetGameSystem<PostProcessVolumeSystemG>();
    if (mGameWorld->HasComponent<PostProcessVolumeComponentG>(entity) && ppvSystem)
    {
        auto ppvH = mGameWorld->GetComponent<PostProcessVolumeComponentG>(entity);
        ppvSystem->SetSelected(ppvH.Write(), true);
    }

    return true;
}

void EditorPrimitiveSystemG::DrawTextureInGizmo(std::string texturePath)
{
    PrimitiveData primitive;
    PrimitiveGenerator::GenerateIconPlane(&primitive, nullptr);

    auto material = GetOrCreateEditorMaterial("EngineResource/Material/TexturePreviewMaterial.nda");
    auto realPath = EngineGlobal::GetFileSystem()->GetRelativePath(texturePath);
    auto texture = cross::TypeCast<cross::resource::Texture>(gAssetStreamingManager->LoadSynchronously(texturePath));
    auto textureInfo = texture->GetTextureData()->mResourceInfo;
    if (textureInfo.Dimension == TextureDimension::TexCube)
    {
        material->SetTexture("cube_texture", texture);
        material->SetFloat("aspectRatio", 2.0f);
    }
    else if (textureInfo.Dimension == TextureDimension::Tex3D)
    {
        material->SetTexture("volume_texture", texture);
        material->SetFloat("aspectRatio", textureInfo.Width * 1.0f / textureInfo.Height);
    }
    else
    {
        material->SetTexture("color_texture", texture);
        material->SetFloat("aspectRatio", textureInfo.Width * 1.0f / textureInfo.Height);
    }
    
    material->SetBool("isUnPack", textureInfo.Format == TextureFormat::BC5);
    material->SetBool("isSRGB", textureInfo.ColorSpace == ColorSpace::SRGB);
    material->SetBool("isCubeMap", textureInfo.Dimension == TextureDimension::TexCube);
    material->SetBool("isTexture3D", textureInfo.Dimension == TextureDimension::Tex3D);

    DrawTriangles(primitive, material);
}

void EditorPrimitiveSystemG::DrawModelNormal(const char* path, cross::Float4x4 matrix)
{
    auto meshResource = GetOrCreateEditorModel(path);
    auto material = GetOrCreateEditorMaterial("EngineResource/Material/NormalDisplay.nda");

    cross::PrimitiveData prim1;
    cross::PrimitiveGenerator::GeneratePrimitiveFromMeshResource(&prim1, meshResource);

    mPrimitiveRenderSystem->DrawPrimitive(&prim1, matrix, material);
}

void EditorPrimitiveSystemG::DrawModelUV(const char* path, UInt32 uv, UInt32 lod)
{
    auto meshResource = GetOrCreateEditorModel(path);
    auto material = GetOrCreateEditorMaterial("EngineResource/Material/UVUnwrapMaterial" + std::to_string(uv) + ".nda");
    cross::PrimitiveData prim1;
    cross::PrimitiveGenerator::GeneratePrimitiveFromMeshResource(&prim1, meshResource, lod);
    cross::Float4x4 matrix = cross::Float4x4::Identity();
    mPrimitiveRenderSystem->DrawPrimitive(&prim1, matrix, material);
}

void EditorPrimitiveSystemG::World_DrawPrimitiveTriangles(cross::IGameWorld* world, UInt8* vertexData, UInt16* indexData, UInt16 vertexCount, UInt16 indexCount, const char* materialPath)
{
    auto editorPrimitiveSystem = ((cross::GameWorld*)world)->GetGameSystem<cross::EditorPrimitiveSystemG>();
    auto material = editorPrimitiveSystem->GetOrCreateEditorMaterial(materialPath);
    editorPrimitiveSystem->DrawTriangles(vertexData, indexData, vertexCount, indexCount, material);
}

void EditorPrimitiveSystemG::World_DrawMeshAABBTree(cross::IGameWorld* world, bool flag) 
{
    if (flag)
    {
        cross::GameWorld* gworld = (cross::GameWorld*)world;
        auto worldLoadingSystem = gworld->GetGameSystem<cross::WorldLoadingSystemG>();
        if (worldLoadingSystem->GetMeshAABBTree() != nullptr)
        {
            // std::vector<cross::BoundingBox> boxes = worldLoadingSystem->GetMeshBVTree()->GetBoundingBoxes();
            std::vector<cross::BoundingBox> boxes = worldLoadingSystem->GetMeshAABBTree()->GetBoundingBoxes();
            auto primitiveSystem = gworld->GetGameSystem<cross::PrimitiveRenderSystemG>();
            cross::Float4x4 matrix = cross::Float4x4::Identity();
            int index = 0;

            std::vector<cross::BoundingBox> redBoxes;
            std::vector<cross::BoundingBox> greenBoxes;

            for (const auto& box : boxes)
            {
                if (index % 2 == 0)
                {
                    redBoxes.emplace_back(box);
                }
                else
                {
                    greenBoxes.emplace_back(box);
                }
                ++index;
            }

            cross::PrimitiveData prim1;
            cross::PrimitiveGenerator::GenerateAABBVectorFrame(&prim1, redBoxes);
            cross::ColorRGBAf color1 = ColorRGBAf(1.0f, 0.0f, 0.0f);
            primitiveSystem->DrawPrimitive(&prim1, matrix, cross::PrimitiveRenderSystemG::PrimitiveLook(color1));

            cross::PrimitiveData prim2;
            cross::PrimitiveGenerator::GenerateAABBVectorFrame(&prim2, greenBoxes);
            cross::ColorRGBAf color2 = ColorRGBAf(0.0f, 1.0f, 0.0f);
            primitiveSystem->DrawPrimitive(&prim2, matrix, cross::PrimitiveRenderSystemG::PrimitiveLook(color2));
        }
    }
}

void EditorPrimitiveSystemG::World_DrawInstanceAABBTree(cross::IGameWorld* world, bool flag) 
{
#define AABBSIZE 2000
    if (flag)
    {
        cross::GameWorld* gworld = (cross::GameWorld*)world;
        auto foliageSys = gworld->GetGameSystem<cross::FoliageSystemG>();
        for (const auto& foliage : gworld->Query<cross::FoliageComponentG>())
        {
            if (foliageSys->GetInstanceBVHAABBTree(foliage.Read()) != nullptr)
            {
                std::vector<cross::BoundingBox> boxes = foliageSys->GetInstanceBVHAABBTree(foliage.Read())->GetBoundingBoxes();
                auto primitiveSystem = gworld->GetGameSystem<cross::PrimitiveRenderSystemG>();
                cross::Float4x4 matrix = cross::Float4x4::Identity();

                // splitVector
                size_t step = AABBSIZE;
                std::vector<std::vector<cross::BoundingBox>> result;
                if (step >= boxes.size())
                {
                    result.push_back(boxes);
                }
                else
                {
                    std::vector<cross::BoundingBox>::iterator curPtr = boxes.begin(), endPtr = boxes.end(), end;
                    while (curPtr < endPtr)
                    {
                        end = static_cast<size_t>(endPtr - curPtr) > step ? step + curPtr : endPtr;
                        step = static_cast<size_t>(endPtr - curPtr) > step ? step : endPtr - curPtr;
                        result.push_back(std::vector<cross::BoundingBox>(curPtr, end));
                        curPtr += step;
                    }
                }

                for (auto& box : result)
                {
                    cross::PrimitiveData prim;
                    cross::PrimitiveGenerator::GenerateAABBVectorFrame(&prim, box);
                    cross::ColorRGBAf color = ColorRGBAf(1.0f, 1.0f, 0.0f);
                    primitiveSystem->DrawPrimitive(&prim, matrix, cross::PrimitiveRenderSystemG::PrimitiveLook(color));
                }
            }
        }
        auto worldLoadingSystem = gworld->GetGameSystem<cross::WorldLoadingSystemG>();
        if (worldLoadingSystem->GetInstanceAABBTree() != nullptr)
        {
            std::vector<cross::BoundingBox> boxes = worldLoadingSystem->GetInstanceAABBTree()->GetBoundingBoxes();
            auto primitiveSystem = gworld->GetGameSystem<cross::PrimitiveRenderSystemG>();
            cross::Float4x4 matrix = cross::Float4x4::Identity();
            cross::PrimitiveData prim;
            cross::PrimitiveGenerator::GenerateAABBVectorFrame(&prim, boxes);
            cross::ColorRGBAf color = ColorRGBAf(0.0f, 0.0f, 1.0f);
            primitiveSystem->DrawPrimitive(&prim, matrix, cross::PrimitiveRenderSystemG::PrimitiveLook(color));
        }
    }
}

void EditorPrimitiveSystemG::World_DrawTerrainAABBTree(cross::IGameWorld* world, bool flag)
{
#ifdef CROSSENGINE_EDITOR
    if (flag)
    {
        cross::GameWorld* gworld = (cross::GameWorld*)world;
        auto terrainSys = gworld->GetGameSystem<cross::TerrainSystemG>();
        for (const auto& terrain : gworld->Query<cross::TerrainComponentG>())
        {
            if (terrainSys->GetTerrainAABBTree(terrain.Read()) != nullptr)
            {
                std::vector<cross::BoundingBox> boxes = terrainSys->GetTerrainAABBTree(terrain.Read())->GetBoundingBoxes();

                cross::PrimitiveData prim;
                cross::PrimitiveGenerator::GenerateAABBVectorFrame(&prim, boxes);
                cross::ColorRGBAf color1 = ColorRGBAf(1.0f, 0.0f, 1.0f);
                auto primitiveSystem = gworld->GetGameSystem<cross::PrimitiveRenderSystemG>();
                cross::Float4x4 matrix = cross::Float4x4::Identity();
                primitiveSystem->DrawPrimitive(&prim, matrix, cross::PrimitiveRenderSystemG::PrimitiveLook(color1));
            }
        }
    }
#endif
}
}   // namespace cross
