#pragma once
#include "CrossBase/Math/CrossMath.h"
#include "CrossBase/NodeTransform/NodeTransformSIMD.h"
#include "CrossBase/Template/EnumClassFlags.h"
#include "ECS/Develop/Framework/Types.h"
#include "CrossPhysics/PhysicsEngine/PhysicsScene.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "Runtime/GameWorld/PhysicsSystemG.h"

namespace cross {
/*
 * Structure containing information about one hit of a sweep, such as point of impact and surface normal at that point.
 */
struct HitResult
{
    /*
     * 'PositionRatio' of impact along sweep direction (ranging from 0.0 to 1.0) if there is a hit, indicating ratio between StartPos and EndPos.
     * For swept movement (but not queries) this may be pulled back slightly from the actual position of impact, to prevent precision problems with adjacent geometry.
     */
    float PositionRatio{0.f};

    /* The distance from the StartPos to the impact Location in world space. This value is 0 if there was an initial overlap (sweep started inside another colliding object). */
    float Distance{0.f};

    /*
     * The location in world space where the moving shape would end up against the impacted object, if there is a hit. Equal to the point of impact for line tests.
     * Example: for a sphere trace test, this is the point where the center of the sphere would be located when it touched the other object.
     * For swept movement (but not queries) this may not equal the final location of the shape since hits are pulled back slightly to prevent precision issues from overlapping another surface.
     */
    TRSVector3Type Location = TRSVector3Type::Zero();

    /*
     * Location in world space of the actual contact of the trace shape (box, sphere, ray, etc) with the impacted object.
     * Example: for a sphere trace test, this is the point where the surface of the sphere touches the other object.
     * @note: In the case of initial overlap (bStartPenetrating=true), ImpactPoint will be the same as Location because there is no meaningful single impact point to report.
     */
    TRSVector3Type ImpactPoint = TRSVector3Type::Zero();

    /*
     * Normal of the hit in world space, for the object that was swept. Equal to ImpactNormal for line tests.
     * This is computed for capsules and spheres, otherwise it will be the same as ImpactNormal.
     * Example: for a sphere trace test, this is a normalized vector pointing in towards the center of the sphere at the point of impact.
     */
    Float3 Normal = Float3::Zero();

    /*
     * Normal of the hit in world space, for the object that was hit by the sweep, if any.
     * For example if a sphere hits a flat plane, this is a normalized vector pointing out from the plane.
     * In the case of impact with a corner or edge of a surface, usually the "most opposing" normal (opposed to the query direction) is chosen.
     */
    Float3 ImpactNormal = Float3::Zero();

    /*
     * Start location of the trace.
     * For example if a sphere is swept against the world, this is the starting location of the center of the sphere.
     */
    TRSVector3Type StartPos = TRSVector3Type::Zero();

    /*
     * End location of the trace; this is NOT where the impact occurred (if any), but the furthest point in the attempted sweep.
     * For example if a sphere is swept against the world, this would be the center of the sphere if there was no blocking hit.
     */
    TRSVector3Type EndPos = TRSVector3Type::Zero();

    /* Indicates if this hit was a result of blocking collision. If false, there was no hit or it was an overlap/touch instead. */
    bool bBlockingHit{false};

    /*
     * Whether the trace started in penetration, i.e. with an initial blocking overlap.
     * In the case of penetration, if PenetrationDepth > 0.f, then it will represent the distance along the Normal vector that will result in
     * minimal contact between the swept shape and the object that was hit. In this case, ImpactNormal will be the normal opposed to movement at that location
     * (ie, Normal may not equal ImpactNormal). ImpactPoint will be the same as Location, since there is no single impact point to report.
     */
    bool bStartPenetrating{false};

    /*
     * If this test started in penetration (bStartPenetrating is true) and a depenetration vector can be computed,
     * this value is the distance along Normal that will result in moving out of penetration.
     * If the distance cannot be computed, this distance will be zero.
     */
    float PenetrationDepth{0.f};

    /* Handle to the object hit by the trace. */
    ecs::EntityID HitEntity = ecs::EntityID::InvalidHandle();

    HitResult()
    {
        Init();
    }

    explicit HitResult(float ratio)
    {
        Init();
        PositionRatio = ratio;
    }

    explicit HitResult(TRSVector3Type start, TRSVector3Type end)
    {
        Init(start, end);
    }

    /* Initialize empty hit result with given time. */
    FORCEINLINE void Init()
    {
        HitEntity = ecs::EntityID::InvalidHandle();
        PositionRatio = 1.f;
    }

    /* Initialize empty hit result with given time, TraceStart, and TraceEnd */
    FORCEINLINE void Init(TRSVector3Type start, TRSVector3Type end)
    {
        Init();
        StartPos = start;
        EndPos = end;
    }

    /* Return true if there was a blocking hit that was not caused by starting in penetration. */
    FORCEINLINE bool IsValidBlockingHit() const
    {
        return bBlockingHit && !bStartPenetrating;
    }

    bool operator<(const HitResult& inHit) const
    {
        return Distance < inHit.Distance;
    }

    /* Static utility function that returns the first 'blocking' hit in an array of results. */
    static HitResult* GetFirstBlockingHit(std::vector<HitResult>& inHits)
    {
        for (auto& hit : inHits)
        {
            if (hit.bBlockingHit)
            {
                return &hit;
            }
        }
        return nullptr;
    }

    /* Static utility function that returns the number of blocking hits in array. */
    static UInt32 GetBlockingHitsNum(const std::vector<HitResult>& inHits)
    {
        UInt32 blockHitNum = 0;
        for (auto& hit : inHits)
        {
            if (hit.bBlockingHit)
            {
                ++blockHitNum;
            }
        }
        return blockHitNum;
    }

    /* Static utility function that returns the number of overlapping hits in array. */
    static UInt32 GetOverlapHitsNum(const std::vector<HitResult>& inHits)
    {
        return static_cast<UInt32>(inHits.size() - GetBlockingHitsNum(inHits));
    }
};

/* Data about the floor for walking movement, used by CharacterMovementComponent. */
struct FindFloorResult
{
    /*
     * True if there was a blocking hit in the floor test that was NOT in initial penetration.
     * The HitResult can give more info about other circumstances.
     */
    bool bBlockingHit{false};

    /* True if the hit found a valid walkable floor. */
    bool bWalkableFloor{false};

    /* The distance to the floor, computed from the swept capsule trace. */
    float FloorDist{0.f};

    /* True if the hit found a valid walkable floor using a line trace (rather than a sweep test, which happens when the sweep test fails to yield a walkable surface). */
    bool bLineTrace{false};

    /* The distance to the floor, computed from the trace. Only valid if bLineTrace is true. */
    float LineDist;

    /* Hit result of the test that found a floor. Includes more specific data about the point of impact and surface normal at that point. */
    HitResult FloorHitResult;

public:
    FindFloorResult()
        : bBlockingHit(false)
        , bWalkableFloor(false)
        , FloorDist(0.f)
        , bLineTrace(false)
        , LineDist(0.f)
        , FloorHitResult(1.f)
    {}

    /* Returns true if the floor result hit a walkable surface. */
    bool IsWalkableFloor() const
    {
        return bBlockingHit && bWalkableFloor;
    }

    void Reset()
    {
        bBlockingHit = false;
        bWalkableFloor = false;
        FloorDist = 0.f;
        bLineTrace = false;
        LineDist = 0.f;
        FloorHitResult.Init();
    }

    float GetDistanceToFloor() const
    {
        return bLineTrace ? LineDist : FloorDist;
    }

    void SetFromSweepResult(const HitResult& hit, float sweepFloorDist, bool isWalkableFloor)
    {
        bBlockingHit = hit.IsValidBlockingHit();
        bWalkableFloor = isWalkableFloor;
        FloorDist = sweepFloorDist;
        bLineTrace = false;
        LineDist = 0.f;
        FloorHitResult = hit;
    }

    void SetFromLineTrace(const HitResult& hit, float sweepFloorDist, float lineDist, bool isWalkableFloor)
    {
        Assert(FloorHitResult.bBlockingHit);
        if (FloorHitResult.bBlockingHit && hit.bBlockingHit)
        {
            // Override most of the sweep result with the line result, but save some values
            HitResult oldHit(FloorHitResult);
            FloorHitResult = hit;

            // Restore some of the old values. We want the new normals and hit actor, however.
            FloorHitResult.PositionRatio = oldHit.PositionRatio;
            FloorHitResult.ImpactPoint = oldHit.ImpactPoint;
            FloorHitResult.Location = oldHit.Location;
            FloorHitResult.StartPos = oldHit.StartPos;
            FloorHitResult.EndPos = oldHit.EndPos;

            bLineTrace = true;
            FloorDist = sweepFloorDist;
            LineDist = lineDist;
            bWalkableFloor = isWalkableFloor;
        }
    }
};

struct StepDownResult
{
    bool FloorFound{false};

    FindFloorResult Floor;
};


/////////////////////////////////////////////
// WorldCollision
//
/////////////////////////////////////////////
class WorldCollision
{
public:
    static HitFlag DefaultHitFlag()
    {
        HitFlag result{0};
        EnumAddFlags(result, HitFlag::Position);
        EnumAddFlags(result, HitFlag::Normal);
        EnumAddFlags(result, HitFlag::MTD);
        return result;
    }

    // Entity move along inDelta's direction and move inDelta's length to sweep
    static bool SweepMulti(GameWorld* inWorld, ecs::EntityID inEntity, const Float3& inDelta, std::vector<HitResult>& outHitResults, CollisionChannelBit inCollisionChannel, const PhysicsActor* self = nullptr, bool drawDebug = false);
    static bool SweepMulti(GameWorld* inWorld, PhysicsGeometryBase* geometry, const TRS& globalStartTransform, const Float3& inDelta, std::vector<HitResult>& outHitResults, HitFlag hitFlag, CollisionChannelBit inCollisionChannel,
                           bool drawDebug = false);

    static bool SweepSingle(GameWorld* inWorld, PhysicsGeometryBase* geometry, const TRS& globalStartTransform, const Float3& inDelta, HitResult& outHitResult, HitFlag hitFlag, CollisionChannelBit inCollisionChannel,
                            const PhysicsActor* self = nullptr, bool drawDebug = false);

    static bool RayCastSingle(GameWorld* inWorld, const TRSVector3Type& startPos, const Float3& inDelta, HitResult& outHitResult, HitFlag hitFlag, CollisionChannelBit inCollisionChannel, const PhysicsActor* self = nullptr,
                              bool drawDebug = false);

    static bool OverlapTest(GameWorld* inWorld, PhysicsGeometryBase* geometry, const TRS& globalTransform, CollisionChannelBit inCollisionChannel);

protected:
    static bool SweepInternal(PhysicsSystemG* physicsSys, PhysicsGeometryBase* geometry, const TRS& globalStartTransform, const Float3& inDelta, std::vector<HitResult>& outHitResults, HitFlag hitFlag,
                              CollisionChannelBit inCollisionChannel, const PhysicsActor* self = nullptr);

    static bool RayCastInternal(PhysicsSystemG* physicsSys, const TRSVector3Type& startPos, const Float3& inDelta, std::vector<HitResult>& outHitResults, HitFlag hitFlag, CollisionChannelBit inCollisionChannel, const PhysicsActor* self = nullptr);

    static bool ConvertTraceResult(const PhysicsHitResult& hit, HitFlag hitFlag, HitResult& outHitResult, const TRS& globalStartTransform, const Float3& delta);
    static void ConvertOverlappedResult(const PhysicsHitResult& hit, HitFlag hitFlag, HitResult& outHitResult, const TRS& globalStartTransform, const Float3& delta);
};


/////////////////////////////////////////////
// WorldCollisionUtility
//
/////////////////////////////////////////////
class WorldCollisionUtility
{
public:
    static void VisualizePhysicsGeometry(GameWorld* inWorld, const PhysicsGeometryBase* inPhyGeo, const Float4x4& globalTransform);

public:
    static void VisualizeHitResult(GameWorld* inWorld, const HitResult& hitResult);
    static void VisualizeRayCast(GameWorld* inWorld, const Float3& startPos, const Float3& delta);

public:
    /* Get specific type PhysicsGeometry from entity's Physics Component, collect extra added shapes first */
    static std::vector<const PhysicsGeometryBase*> GetPhysicsGeometries(GameWorld* inWorld, ecs::EntityID inEntity, PhysicsGeometryBase::Type geoType);
    /* Get all type PhysicsGeometry from entity's Physics Component, collect extra added shapes first */
    static std::vector<const PhysicsGeometryBase*> GetAllPhysicsGeometries(GameWorld* inWorld, ecs::EntityID inEntity);

    static TRS GetPhysicsGeometryLocalTransform(const PhysicsGeometryBase* inPhyGeo);
    static TRS GetPhysicsGeometryWorldTransform(GameWorld* inWorld, ecs::EntityID bindEntity, const PhysicsGeometryBase* inPhyGeo);
};

}   // namespace cross