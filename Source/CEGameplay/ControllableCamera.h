#pragma once
#include "CrossBase/Math/CrossMath.h"
#include "ControllableUnit.h"

namespace cross::cc {

enum class CEMeta(Editor, Reflect, Puerts) CameraOperationV
{
    Rotation_Yaw = 0,
    Rotation_Pitch,
    Rotation_Roll,
    Translate_Forward,
    Translate_Left,
    Translate_Up
};

enum class CEGameplay_API CEMeta(Reflect, Puerts)  CameraMode
{
    FPP = 0,
    TPP,
    FREE,
    LEFT_INSPECTION,
    RIGHT_INSPECTION,
    COUNT,
};

//struct CEGameplay_API InputAxisCameraMapping
//{
//    /** What the axis means in camera ctrl context */
//    CEMeta(Serialize, Editor) 
//    CameraOperationV Op = CameraOperationV::Rotation_Pitch;
//
//    /** Get value from which axis */
//    CEMeta(Serialize, Editor)
//    InputAxis Axis = InputAxis::Horizontal;
//
//    /** Inverts reported values for this axis or not */
//    CEMeta(Serialize, Editor) 
//    bool Invert = false;
//
//    InputAxisCameraMapping() = default;
//
//    InputAxisCameraMapping(CameraOperationV inOp, InputAxis inAxis, bool inInvert)
//        : Op(inOp)
//        , Axis(inAxis)
//        , Invert(inInvert)
//    {}
//
//    CE_Serialize_Deserialize;
//};

/**
 * A ControllableCamera is responsible for managing the camera position & rotation. a particular player's
 * ctrl may executed later by above calculated result as required. It defines the final view properties used by other systems (e.g. the renderer),
 * meaning you can think of it as your virtual eyeball in the world. It can compute the final camera properties directly
 * 
 * The ControllableCamera primary external responsibility is to reliably respond to
 * various Get*() functions, such as GetCameraViewPoint. Most everything else is
 * implementation detail and overrideable by user projects.
 *
 * By default, a ControllableCamera maintains a "view target", which is the primary entity
 * the camera is associated with. It can also apply various "post" effects to the final
 * view state, such as camera animations, shakes, post-process effects or special effects such as dirt on the lens.
 */
class CEGameplay_API CEMeta(Reflect, Puerts) ControllableCamera : public ControllableUnit
{
public:
    /** Rotation speed scaling ordered by Pitch | Yaw | Roll */
    CEMeta(Serialize, Editor, ScriptReadWrite) 
    Float3 RotationScale = Float3::One();

    /** Translate speed scaling in world space */
    CEMeta(Serialize, Editor, ScriptReadWrite) 
    Float3 TranslateScale = Float3::One();

    /** Minimum & Maximum view pitch, in degrees. */
    CEMeta(Serialize, Editor, ScriptReadWrite)
    Float2 ViewPitchMinMax = {0.f, 0.f};

    /** Minimum & Maximum view yaw, in degrees. */
    CEMeta(Serialize, Editor, ScriptReadWrite)
    Float2 ViewYawMinMax = {0.f, 0.f};

    /** Minimum & Maximum view roll, in degrees. */
    CEMeta(Serialize, Editor, ScriptReadWrite)
    Float2 ViewRollMinMax = {0.f, 0.f};

public:
    ControllableCamera(ecs::EntityID inSelf = ecs::EntityID::InvalidHandle())
        : ControllableUnit(inSelf)
    {}

    CE_Virtual_Serialize_Deserialize;

    /** operation value for current controller */
    float GetInputOperationValue(CameraOperationV op) const
    {
        return mAxisOperations[op];
    }

    /** called from script or any raw controller */
    void SetInputOperationValue(CameraOperationV op, float v)
    {
        mAxisOperations[op] = v;
    }

    virtual void ResetCameraSettings();

    //// ~ControllableUnit Begin~
    ////

    virtual void Possess(GameWorld* inWorld, ecs::EntityID inEntity) override;

    virtual void UnPossess(GameWorld* inWorld) override;

protected:
    virtual QuaternionA GetDesiredWorldRotation(GameWorld* inWorld) const override;

    ////
    //// ~ControllableUnit End~

    /**
     * Add Pitch (look up) input. This value is multiplied by InputPitchScale.
     */
    inline void AddPitchInputImpl(float axisDeltaValue)
    {
        mRotationEuler.x += axisDeltaValue * RotationScale.x;

        // Restrict clamp followed 
        if (std::abs(ViewPitchMinMax.x - ViewPitchMinMax.y) > 0.001f)
            mRotationEuler.x = std::clamp(mRotationEuler.x, ViewPitchMinMax.x / 180.0f * MathUtils::MathPi, ViewPitchMinMax.y / 180.0f * MathUtils::MathPi);
        else
            mRotationEuler.x = std::fmod(mRotationEuler.x, MathUtils::Math2Pi);
    }

    /**
     * Add Yaw (turn) input. This value is multiplied by InputYawScale.
     */
    inline void AddYawInputImpl(float axisDeltaValue)
    {
        mRotationEuler.y += axisDeltaValue * RotationScale.y;
      
        // Restrict clamp followed
        if (std::abs(ViewYawMinMax.x - ViewYawMinMax.y) > 0.001f)
            mRotationEuler.y = std::clamp(mRotationEuler.y, ViewYawMinMax.x / 180.0f * MathUtils::MathPi, ViewYawMinMax.y / 180.0f * MathUtils::MathPi);
        else
            mRotationEuler.y = std::fmod(mRotationEuler.y, MathUtils::Math2Pi);
    }

    /**
     * Add Roll input. This value is multiplied by InputRollScale.
     */
    inline void AddRollInputImpl(float deltaTime, float axisValue) 
    {
        mRotationEuler.z += axisValue * deltaTime * RotationScale.z;

        // Restrict clamp followed
        if (std::abs(ViewRollMinMax.x - ViewRollMinMax.y) > 0.001f)
            mRotationEuler.z = std::clamp(mRotationEuler.z, ViewRollMinMax.x / 180.0f * MathUtils::MathPi, ViewRollMinMax.y / 180.0f * MathUtils::MathPi);
        else
            mRotationEuler.z = std::fmod(mRotationEuler.z, MathUtils::Math2Pi);
    }

protected:
    /** Accumulated rotation by euler angle each tick. Each element clamp into 0.f - 1.f */
    Float3A mRotationEuler = Float3::Zero();

    // Abstract reltv operations, reset after update
    mutable CEHashMap<CameraOperationV, float> mAxisOperations;
};

class CEGameplay_API CEMeta(Reflect, Puerts) TestEnvControllableCamera : public ControllableCamera, public IHostedCtrlInterface<CameraOperationV>, public ControllableUnitRegister<TestEnvControllableCamera>
{
public:    
    CEMeta(Serialize, Editor)
    CECSAttribute(JsonConverter(typeof(EntityIDStructConverter)))
    ecs::EntityID Focuson = ecs::EntityID::InvalidHandle();

    /** Offset to focused entity */
    CEMeta(Serialize, Editor, ScriptReadWrite)
    Float3 FocusonOffset{0.0f, 0.0f, 0.0f};

    /** Offset to view target */
    CEMeta(Serialize, Editor, ScriptReadWrite)
    Float3 ViewTargetOffset{0.0f, 0.0f, 0.0f};

    /** Euler to view target */
    CEMeta(Serialize, Editor, ScriptReadWrite)
    Float3 ViewTargetEuler{0.0f, 0.0f, 0.0f}; 

    /** If true, camera lags behind target position to smooth its movement */
    CEMeta(Serialize, Editor, ScriptReadWrite)
    bool EnableCameraLag = false;

    /** If true, camera lags behind target rotation to smooth its movement */
    CEMeta(Serialize, Editor, ScriptReadWrite)
    bool EnableCameraRotationLag = false;

    /** If UseCameraLagSubstepping is true, sub-step camera damping so that it handles fluctuating frame rates well (though this comes at a cost) */
    CEMeta(Serialize, Editor, ScriptReadWrite)
    bool UseCameraLagSubstepping = false;

    /** If EnableCameraLag is true, controls how quickly camera reaches target position. Low values are slower (more lag), high values are faster (less lag), while zero is instant (no lag) */
    CEMeta(Serialize, Editor, ScriptReadWrite)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ValueMin = "0.0", ValueMax = "1000.0"))
    float CameraLagSpeed = 10.0f;   // 0.0f - 1000.0f

    /** If EnableCameraRotationLag is true, controls how quickly camera reaches target position.Low values are slower(more lag), high values are faster(less lag), while zero is instant(no lag) */
    CEMeta(Serialize, Editor, ScriptReadWrite)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ValueMin = "0.0", ValueMax = "1000.0"))
    float CameraRotationLagSpeed = 10.0f;   // 0.0f - 1000.0f

    /** Max time step used when sub-stepping camera lag */
    CEMeta(Serialize, Editor, ScriptReadWrite)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ValueMin = "0.005", ValueMax = "0.5"))
    float CameraLagMaxTimeStep = 1.f / 60.f;   // 0.005f - 0.5f

    /** Max distance the camera target may lag behind the current location. If set to zero, no max distance is enforced */
    CEMeta(Serialize, Editor, ScriptReadWrite)
    float CameraLagMaxDistance = 0.0f;

    /** Use fixed time step */
    CEMeta(Serialize, Editor, ScriptReadWrite)
    bool UseFixedUpdate = false;

    /** Additional offset applied when using MPCDI calibration */
    CEMeta(Serialize, Editor, ScriptReadWrite)
    double AdditionalCalibrationOffset = 0.0;

public:
    TestEnvControllableCamera(ecs::EntityID inSelf = ecs::EntityID::InvalidHandle())
        : ControllableCamera(inSelf)
    {}

    CE_Virtual_Serialize_Deserialize;
    static inline ControllableUnitType GetType()
    {
        return ControllableUnitType::TestEnvControllableCamera;
    }
    //// ~ControllableUnit Begin~
    virtual void Deserialize(GameWorld* inWorld, const DeserializeNode& in) override;

    virtual void Serialize(GameWorld* inWorld, SerializeNode& out) const override;

    virtual float GetOperationValue(CameraOperationV t, float deltaTime) const override;

    float GetOperationValueT(CameraOperationV t, float deltaTime) const;

    virtual bool IsOperationValidated(CameraOperationV t) const override;

    virtual void Tick(float deltaTime, GameWorld* inWorld) override;

    virtual void PostTick(float deltaTime, GameWorld* inWorld) override;

    float GetFreeSpeedGearValue(int gear) const;
    void SetFreeSpeedGearValue(int gear, float value);
   // 
    CEProperty(ScriptReadWrite)
    float mFreeSpeed = 1.0f;

    CEProperty(ScriptReadWrite)
    float mFreeSpeedAcc = 1.0f;

    CEFunction(ScriptCallable)
    CameraMode GetCameraMode() const;

    CEFunction(ScriptCallable)
    void SetCameraMode(CameraMode mode);

    CEFunction(ScriptCallable)
    void SetFreeSpeed(int gear);

    CEFunction(ScriptCallable)
    virtual void ResetCameraSettings() override;

    CEFunction(ScriptCallable)
    void ResetTransformCache();

protected:
    void TickImplFPP(GameWorld* inWorld);
    void TickImplFollow(float horionzalDeltaAxis, float verticalDeltaAxis, float offsetDelta, GameWorld* inWorld);
    void TickImplSpringArm(float deltaTime, GameWorld* inWorld);
    void TickImplFREE(Float3 rotationDelta, Double3 translationDelta, GameWorld* inWorld);

    void ApplyCalibrationOffset(GameWorld * inWorld, Double3& translation, Quaternion64& rotation);

protected:
    static constexpr int FreeSpeedGearCount = 10;

    CameraMode mMode = CameraMode::FPP;

    std::vector<float> mFreeSpeedGears = {1.0f, 0.01f, 0.1f, 0.5f, 1.5f, 2.5f, 5.0f, 10.0f, 15.0f, 50.0f};

    // Spring arm impl
    Double3 mPreviousArmOrigin = Double3::Zero();
    Double3 mPreviousTranslation = Double3::Zero();
    Quaternion64 mPreviousRotation = Quaternion64::Identity();

    // Calibration offset
    bool mCalibrationCalculated = false;
    double mCalibrationOffset = 0.0;
    Quaternion64 mCalibrationRotation = Quaternion64::Identity();
};

class CEGameplay_API OrbitFollowControllableCamera : public ControllableCamera
{
public:
    /** Offset to view target */
    CEMeta(Serialize, Editor) 
    float ViewTargetOffsetMax = 100.0f;
    
    CEMeta(Serialize, Editor) 
    CECSAttribute(JsonConverter(typeof(EntityIDStructConverter))) 
    ecs::EntityID Focuson = ecs::EntityID::InvalidHandle();

    CEMeta(Serialize, Editor) 
    Float3 FocusonOffset{0.0f, 0.0f, 0.0f};

public:
    OrbitFollowControllableCamera(ecs::EntityID inSelf = ecs::EntityID::InvalidHandle())
        : ControllableCamera(inSelf)
    {}

    void SetFocusonOffset(const Float3& focusonOffset)
    {
        FocusonOffset = focusonOffset;
    };

    bool IsFirstPersonPersMode() const {
        return mFirstPersonPersMode;
    }

    CE_Virtual_Serialize_Deserialize;

    //// ~ControllableUnit Begin~
    virtual void Deserialize(GameWorld* inWorld, const DeserializeNode& in) override;

    virtual void Serialize(GameWorld* inWorld, SerializeNode& out) const override;

protected:
    virtual Float3A GetDesiredWorldTranslation(GameWorld* inWorld) const override;

    //// ~ControllableUnit End~
    /** pitch & yaw cached by radians */
    void TickImpl(float deltaTime, float horionzalAxis, float verticalAxis, GameWorld* inWorld);

    void TickImplT(float horionzalDeltaAxis, float verticalDeltaAxis, GameWorld* inWorld);
    
    

protected:
    float mViewTargetOffsetClamped = 0.0f;
    bool mFirstPersonPersMode = true;
};

class CEGameplay_API HostedOrbitFollowCamera : public OrbitFollowControllableCamera, public IHostedCtrlInterface<CameraOperationV>, public ControllableUnitRegister<HostedOrbitFollowCamera>
{
public:
    /** This player's version of the Action Mappings */
    //CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "List", ChildPropertyType = "Struct", ToolTips = "InputAxisActionMapping")) 
    //std::vector<InputAxisCameraMapping> AxisMappings = 
    //{
    //    {CameraOperationV::Rotation_Yaw, InputAxis::Horizontal, false}, 
    //    {CameraOperationV::Rotation_Pitch, InputAxis::Vertical, false}
    //};

public:
    HostedOrbitFollowCamera(ecs::EntityID inSelf = ecs::EntityID::InvalidHandle())
        : OrbitFollowControllableCamera(inSelf)
    {}

    CE_Virtual_Serialize_Deserialize;

    /* Controller type for this unit */
    static inline ControllableUnitType GetType() { return ControllableUnitType::HostedOrbitFollowCamera; }

    virtual float GetOperationValue(CameraOperationV t, float deltaTime) const override;
    float GetOperationValueT(CameraOperationV t, float deltaTime) const;

    virtual bool IsOperationValidated(CameraOperationV t) const override;

    virtual void Tick(float deltaTime, GameWorld* inWorld) override;

    virtual void PostTick(float deltaTime, GameWorld* inWorld) override;

protected:
    // TODO(xtnwang), remove below in the future

    /* the scalar velocity influenced by input directly */
    mutable Float2 ScalarTargetVelocity = Float2(0.0f, 0.0f);

    /* Max Acceleration (rate of change of velocity), variable = 1.0f / reach max(1.0f) costed time in sec
     *   0 & infinite means INSTANT ASSIGN into normalized target value
     */
    mutable Float2 ScalarAcceleration = Float2(10.0f, 10.0f);
};

class CEGameplay_API ScriptedOrbitFollowCamera : public OrbitFollowControllableCamera, public ControllableUnitRegister<ScriptedOrbitFollowCamera>
{
public:
    ScriptedOrbitFollowCamera(ecs::EntityID inSelf = ecs::EntityID::InvalidHandle())
        : OrbitFollowControllableCamera(inSelf)
    {}

    CE_Virtual_Serialize_Deserialize;
    
    /* Controller type for this unit */
    static inline ControllableUnitType GetType() { return ControllableUnitType::ScriptOrbitFollowCamera; }
};

class CEGameplay_API FirstPersonPerspectCamera : public ControllableCamera
{
public:
    CEMeta(Serialize, Editor)
    float StandHeight = 1.0f;

    CEMeta(Serialize, Editor) 
    float MaxWalkableSlope = 30.0f;

    CEMeta(Serialize, Editor) 
    float MaxWalkableSpan = 0.1f;

public:
    FirstPersonPerspectCamera(ecs::EntityID inSelf = ecs::EntityID::InvalidHandle())
        : ControllableCamera(inSelf)
    {
        mAxisOperations = 
        {
            {CameraOperationV::Rotation_Yaw, 0.f},
            {CameraOperationV::Rotation_Pitch, 0.f},
            {CameraOperationV::Translate_Forward, 0.f},
            {CameraOperationV::Translate_Left, 0.f},
        };
    }

    CE_Virtual_Serialize_Deserialize;

    virtual void PostTick(float deltaTime, GameWorld* inWorld) override;

protected:
    /** pitch & yaw cached by radians */
    void TickImpl(float deltaTime, float horionzalAxis, float verticalAxis, Float2 forwardDirection, GameWorld* inWorld);

protected:
    Float3 mLastVelocity = Float3::Zero();

    // Sweep result with direction (0, -1, 0)
    Float3 mFloorHitPoint = Float3::Zero();
    // Sweep down hit something or not
    bool mFloorHitValidate = false;
    
    // Sweep result with current forward direction 
    Float3 mForwardHitPoint = Float3::Zero();
    // Sweep forward hit something or not
    bool mForwardHitValidate = false;

    SInt32 mFreezeCount = 0;
};

class CEGameplay_API HostedFppCamera : public FirstPersonPerspectCamera, public ControllableUnitRegister<HostedFppCamera>
{
public:
    CEMeta(Serialize, Editor) 
    float RunThreshold = 0.f;

    HostedFppCamera(ecs::EntityID inSelf = ecs::EntityID::InvalidHandle())
        : FirstPersonPerspectCamera(inSelf)
    {}

    CE_Virtual_Serialize_Deserialize;

    virtual float GetOperationValue(CameraOperationV t, float deltaTime) const;

    virtual void Tick(float deltaTime, GameWorld* inWorld) override;

    /* Controller type for this unit */
    static inline ControllableUnitType GetType() { return ControllableUnitType::HostedFppCamera; }

protected:
    /* the scalar velocity influenced by input directly */
    mutable Float2 ScalarTargetVelocity = Float2(0.0f, 0.0f);
};


}
