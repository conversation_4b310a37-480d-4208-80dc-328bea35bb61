#include "EnginePrefix.h"
#include "ControllableCamera.h"
#include "MotionMovementComponent.h"
#include "Runtime/GameWorld/CameraSystemG.h"
#include "Runtime/GameWorld/EntityMetaSystem.h"
#include "Runtime/GameWorld/ScriptSystemG.h"
#include "Runtime/Input/InputManager.h"
#include "ControllableUnitSystemG.h"
#include "Runtime/GameWorld/FFSWGS84SystemG.h"

namespace cross::cc {

    QuaternionA ControllableCamera::GetDesiredWorldRotation(GameWorld* inWorld) const
    {
        return Quaternion::EulerToQuaternion(mRotationEuler);
    }

    void ControllableCamera::Possess(GameWorld* inWorld, ecs::EntityID inEntity)
    {
        if (Initialized)
            return;

        Initialized = true;

        // entity should got a valid camera component in the very beginning
        auto cameraComp = inWorld->GetComponent<CameraComponentG>(inEntity);
        if (!cameraComp.IsValid()) 
        {
            auto nameSys = inWorld->GetGameSystem<EntityMetaSystem>();
            LOG_WARN("ControllableCamera Possess failed for be attached entity:{} should got camera comp first.", 
                nameSys->GetName(inWorld->GetComponent<ecs::EntityMetaComponentG>(inEntity).Read()));
            return;
        }

        // grab initialized rotation into euler angle
        //auto targetRot = GetTargetRot(inWorld);
        // mRotationEuler = Quaternion::QuaternionToEuler(targetRot);
        // keep roll got zero all the time
        // mRotationEuler.z = 0.f;

        auto* cameraSys = inWorld->GetGameSystem<CameraSystemG>();
        
        // make current main camera if needed
        if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() != AppStartUpType::AppStartUpTypeCrossEditor || 
            inWorld->GetWorldType() == WorldTypeTag::PIEWorld)
        {
            cameraSys->SetMainCamera(inEntity);
        }

        SettingsManager::MPCDI const& mpcdi = EngineGlobal::GetSettingMgr()->GetMPCDI();
        if (mpcdi.enabled)
        {
            cameraSys->SetPerspectiveOffCenter(
                cameraComp.Write()
                , MathUtils::ConvertToRadians((float)mpcdi.left)
                , MathUtils::ConvertToRadians((float)mpcdi.right)
                , MathUtils::ConvertToRadians((float)mpcdi.top)
                , MathUtils::ConvertToRadians((float)mpcdi.bottom));
        }

        ControllableUnit::Possess(inWorld, inEntity);
    }

    void ControllableCamera::UnPossess(GameWorld* inWorld) 
    {
        ControllableUnit::UnPossess(inWorld);
    }

    void ControllableCamera::ResetCameraSettings()
    {
        RotationScale = Float3::One();
        TranslateScale = Float3::One();
        ViewPitchMinMax = {0.f, 0.f};
        ViewYawMinMax = {0.f, 0.f};
        ViewRollMinMax = {0.f, 0.f};
    }

    void TestEnvControllableCamera::Deserialize(GameWorld* inWorld, const DeserializeNode& in)
    {
        ControllableUnit::Deserialize(inWorld, in);

        if (in.HasMember("Focuson") && in["Focuson"].IsString())
        {
            auto const& targetNode = in["Focuson"];
            std::string targetEncodeStr = targetNode.AsString();

            auto target = ecs::EntityID::InvalidHandle();

            if (targetEncodeStr.length() > 0)
            {
                if (inWorld != nullptr)
                    target = inWorld->GetEntity(EUID(targetEncodeStr));
                else
                {
                    UInt64 targetEncode = std::stoull(targetEncodeStr);
                    target = cross::ecs::EntityID(targetEncode);
                }
            }

            Focuson = target;
        }
        else
            Focuson = ecs::EntityID::InvalidHandle();
    }

    void TestEnvControllableCamera::Serialize(GameWorld* inWorld, SerializeNode& out) const
    {
        ControllableUnit::Serialize(inWorld, out);

        if (inWorld != nullptr)
        {
            if (Focuson != ecs::EntityID::InvalidHandle() && inWorld->IsEntityAlive(Focuson))
                out["Focuson"] = inWorld->GetEUID(Focuson).ToString();
            else
                out["Focuson"] = "";
        }
        // editor extract property
        else
            out["Focuson"] = Focuson != ecs::EntityID::InvalidHandle() ? std::to_string(Focuson.GetValue()) : "";
    }

    void TestEnvControllableCamera::SetFreeSpeed(int gear)
    {
       mFreeSpeed = mFreeSpeedGears[gear];
    }

    void TestEnvControllableCamera::TickImplFPP(GameWorld* inWorld)
    {
        SettingsManager::MPCDI const& mpcdi = EngineGlobal::GetSettingMgr()->GetMPCDI();
        auto sys = inWorld->GetGameSystem<TransformSystemG>();
        auto comp = inWorld->GetComponent<WorldTransformComponentG>(Entity);
        auto transSys = inWorld->GetGameSystem<TransformSystemG>();

        auto cameraTransComp = inWorld->GetComponent<WorldTransformComponentG>(mSelf);
        TRSQuaternionAType cameraRotation = GetEntityRotT(inWorld, mSelf);
        TRSQuaternionAType focusRotation = GetEntityRotT(inWorld, Focuson);
 
        TRSVector3AType cameraPosition = GetEntityPosT(inWorld, mSelf);
        TRSVector3AType focusPosition = GetEntityPosT(inWorld, Focuson) + focusRotation.Double3Rotate(Double3(FocusonOffset));

        // Position filter
        auto focusInCameraLocalSpace = Double4x4A::TransformPointF3(transSys->GetWorldInverseMatrixT(cameraTransComp.Write()), focusPosition);
        const double xaxis_fitter = 0.00;
        const double yaxis_fitter = 0.00;
        const double zaxis_fitter = 0.00;

        if (std::abs(focusInCameraLocalSpace.x) < xaxis_fitter)
        {
            focusInCameraLocalSpace.x = 0.0;
        }

        if (std::abs(focusInCameraLocalSpace.y) < yaxis_fitter)
        {
            focusInCameraLocalSpace.y = 0.0;
        }

        if (std::abs(focusInCameraLocalSpace.z) < zaxis_fitter)
        {
            focusInCameraLocalSpace.z = 0.0;
        }
        
        TRSVector3AType finalPosition = Double4x4A::TransformPointF3(transSys->GetWorldMatrixT(cameraTransComp.Read()), focusInCameraLocalSpace);
        // Rotation filter
        TRSQuaternionAType finalRotation;
        finalRotation = focusRotation;   // TRSQuaternionAType::Distance(focusRotation, cameraRotation) < 5e-9 ? cameraRotation : focusRotation;

        ApplyCalibrationOffset(inWorld, finalPosition, finalRotation);

        sys->SetWorldTranslationT(comp.Write(), finalPosition); 
        sys->SetWorldRotationT(comp.Write(), finalRotation);      
    }

    void TestEnvControllableCamera::TickImplFollow(float horionzalDeltaAxis, float verticalDeltaAxis, float offsetDelta, GameWorld* inWorld) {
        AddPitchInputImpl(verticalDeltaAxis);
        AddYawInputImpl(horionzalDeltaAxis);
        //mViewTargetOffset.z -= offsetDelta * TranslateScale.z;
#if defined(CE_USE_DOUBLE_TRANSFORM)  
        auto sys = inWorld->GetGameSystem<TransformSystemG>();
        auto comp = inWorld->GetComponent<WorldTransformComponentG>(Entity);

        // controllable unit handle position
        TRSQuaternionAType focusRotation = GetEntityRotT(inWorld, Focuson);
        Double3 focusPosition = GetEntityPosT(inWorld, Focuson) + focusRotation.Double3Rotate(Double3(FocusonOffset));

        TRSQuaternionAType cameraRotation = TRSQuaternionAType::EulerToQuaternion64(mRotationEuler);
        //TRSVector3AType cameraPosition = GetEntityPosT(inWorld, mSelf);

        Double3 EUNNormal;
        TransformSystemG::CartesianCoordinateTransform_ToWGS84(focusPosition.x / 100.0, focusPosition.y / 100.0, focusPosition.z / 100.0, EUNNormal);
        TRSQuaternionAType worldRotation = WGS84SystemG::CreateSphereSpaceWorldRotation(EUNNormal);

        auto localRotation = TRSQuaternionAType::CreateFromYawPitchRollInDegrees(-ViewTargetEuler.x, -ViewTargetEuler.y, -ViewTargetEuler.z);

        Double3 finalPosition = focusPosition + focusRotation.Double3Rotate({ViewTargetOffset.x, ViewTargetOffset.y, ViewTargetOffset.z});
        TRSQuaternionAType finalRotation = localRotation * focusRotation;

        ApplyCalibrationOffset(inWorld, finalPosition, finalRotation);

        sys->SetWorldTranslationT(comp.Write(), finalPosition);
        sys->SetWorldRotationT(comp.Write(), finalRotation);
#endif
    }

    void TestEnvControllableCamera::TickImplSpringArm(float deltaTime, GameWorld* inWorld)
    {
        if (Focuson == ecs::EntityID::InvalidHandle() || !inWorld->IsEntityAlive(Focuson))
        {
            return;
        }

        // Fixed update timestep
        if (UseFixedUpdate)
        {
            deltaTime = 1.0f / EngineGlobal::GetSettingMgr()->GetMaxTickRates();
        }

        Quaternion64 desiredRotation = GetEntityRotT(inWorld, Focuson);

        // Scale down the desired rotation of camera to impress the movement of the focused entity
        auto* wgs84Sys = inWorld->GetGameSystem<WGS84SystemG>();
        auto wgs84Comp = inWorld->GetComponent<FFSWGS84ComponentG>(Focuson);
        if (wgs84Comp.IsValid())
        {
            Double3 focusEuler = Double3(wgs84Sys->GetHeading(wgs84Comp.Read()), wgs84Sys->GetPitch(wgs84Comp.Read()), wgs84Sys->GetRoll(wgs84Comp.Read()));
            Double3 scaledEuler = focusEuler * Double3(RotationScale);
            Quaternion64 scaledRotation = Quaternion64::CreateFromYawPitchRollInDegrees(scaledEuler.x, scaledEuler.y, scaledEuler.z);
            desiredRotation = scaledRotation * desiredRotation;
        }

        // Apply lag to rotation if desired
        if (EnableCameraRotationLag && !mPreviousRotation.IsIdentity())
        {
            if (UseCameraLagSubstepping && deltaTime > CameraLagMaxTimeStep && CameraRotationLagSpeed > 0.f)
            {
                const Double3 armRotationStep = (Quaternion64::Quaternion64ToEuler(desiredRotation) - Quaternion64::Quaternion64ToEuler(mPreviousRotation)).Normalized() * (1.f / deltaTime);
                Double3 lerpTarget = Quaternion64::Quaternion64ToEuler(mPreviousRotation);
                float remainingTime = deltaTime;
                while (remainingTime > 1e-4)
                {
                    const float LerpAmount = std::min(CameraLagMaxTimeStep, remainingTime);
                    lerpTarget += armRotationStep * LerpAmount;
                    remainingTime -= LerpAmount;

                    desiredRotation = MathUtils::QInterpTo(mPreviousRotation, Quaternion64::EulerToQuaternion64(lerpTarget), LerpAmount, CameraRotationLagSpeed);
                    mPreviousRotation = desiredRotation;
                }
            }
            else
            {
                desiredRotation = MathUtils::QInterpTo(mPreviousRotation, desiredRotation, deltaTime, CameraRotationLagSpeed);
            }
        }
        mPreviousRotation = desiredRotation;

        // Get the spring arm 'origin', the target we want to look at

        Double3 armOrigin = GetEntityPosT(inWorld, Focuson) + GetEntityRotT(inWorld, Focuson).Double3Rotate(Double3(FocusonOffset));
        // We lag the target, not the actual camera position, so rotating the camera around does not have lag
        Double3 desiredTranslation = armOrigin;
        if (EnableCameraLag && !mPreviousTranslation.IsNearlyZero())
        {
            if (UseCameraLagSubstepping && deltaTime > CameraLagMaxTimeStep && CameraLagSpeed > 0.f)
            {
                const Double3 armMovementStep = (desiredTranslation - mPreviousTranslation) * (1.f / deltaTime);
                Double3 lerpTarget = mPreviousTranslation;

                float remainingTime = deltaTime;
                while (remainingTime > 1e-4)
                {
                    const float LerpAmount = std::min(CameraLagMaxTimeStep, remainingTime);
                    lerpTarget += armMovementStep * LerpAmount;
                    remainingTime -= LerpAmount;

                    desiredTranslation = MathUtils::VInterpTo(mPreviousTranslation, lerpTarget, LerpAmount, CameraLagSpeed);
                    mPreviousTranslation = desiredTranslation;
                }
            }
            else
            {
                desiredTranslation = MathUtils::VInterpTo(mPreviousTranslation, desiredTranslation, deltaTime, CameraLagSpeed);
            }

            // Clamp distance if requested
            if (CameraLagMaxDistance > 0.f)
            {
                const Double3 distance = desiredTranslation - armOrigin;
                if (distance.LengthSquared() > MathUtils::Square(CameraLagMaxDistance))
                {
                    desiredTranslation = armOrigin + distance.ClampToMaxSize(CameraLagMaxDistance);
                }
            }
        }

        mPreviousArmOrigin = armOrigin;
        mPreviousTranslation = desiredTranslation;

        // Add socket offset in local space
        desiredTranslation += Double3::Transform(Double3(ViewTargetOffset), desiredRotation);

        // Add socket rotation in local space
        Quaternion64 localRotation = Quaternion64::CreateFromYawPitchRollInDegrees(-ViewTargetEuler.x, -ViewTargetEuler.y, -ViewTargetEuler.z);
        desiredRotation = localRotation * desiredRotation;

        ApplyCalibrationOffset(inWorld, desiredTranslation, desiredRotation);

        // Set camera translation and rotation
        if (Entity != cross::ecs::EntityID::InvalidHandle() && inWorld->IsEntityAlive(Entity))
        {
            auto* transSys = inWorld->GetGameSystem<cross::TransformSystemG>();
            auto worldTransComp = inWorld->GetComponent<cross::WorldTransformComponentG>(Entity);

            transSys->SetWorldTranslationT(worldTransComp.Write(), desiredTranslation);
            transSys->SetWorldRotationT(worldTransComp.Write(), desiredRotation);
        }
    }

    void TestEnvControllableCamera::TickImplFREE(Float3 rotationDelta, Double3 translationDelta, GameWorld* inWorld)
    {
        AddPitchInputImpl(rotationDelta.x);
        AddYawInputImpl(rotationDelta.y);
#if defined(CE_USE_DOUBLE_TRANSFORM)  
        auto sys = inWorld->GetGameSystem<TransformSystemG>();
        auto comp = inWorld->GetComponent<WorldTransformComponentG>(Entity);

        TRSQuaternionAType cameraRotation = TRSQuaternionAType::EulerToQuaternion64(mRotationEuler);
        TRSVector3AType cameraPosition = GetEntityPosT(inWorld, mSelf);

        Double3 EUNNormal;
        TransformSystemG::CartesianCoordinateTransform_ToWGS84(cameraPosition.x, cameraPosition.y, cameraPosition.z, EUNNormal);
        TRSQuaternionAType worldRotation = WGS84SystemG::CreateSphereSpaceWorldRotation(EUNNormal);
      
        TRSQuaternionAType finalRotation = cameraRotation * worldRotation;
        //std::cout << finalRotation.x << '\t' << finalRotation.y << '\t' << finalRotation.z << '\t' << finalRotation.w << std::endl;
        TRSVector3AType finalPosition = cameraPosition+finalRotation.Double3Rotate(translationDelta * TranslateScale.x);

        sys->SetWorldTranslationT(comp.Write(), finalPosition); 
        sys->SetWorldRotationT(comp.Write(), finalRotation);
#endif
    }

    void TestEnvControllableCamera::PostTick(float deltaTime, GameWorld* inWorld) {
#if defined(CE_USE_DOUBLE_TRANSFORM)  
        
        if (Focuson == ecs::EntityID::InvalidHandle())
            return;

        auto& global = EngineGlobal::Inst();
        auto inputMgr = global.GetInputManager();
        auto user = inputMgr->GetUser({0});

        if (mMode == CameraMode::FPP)
        {
            mFreeSpeed = 1.0f;
            TickImplFPP(inWorld);
        }
        else 
        {
            float yaw_delta = GetOperationValueT(CameraOperationV::Rotation_Yaw, deltaTime);
            float pitch_delta = GetOperationValueT(CameraOperationV::Rotation_Pitch, deltaTime);
            float forward_delta = GetOperationValueT(CameraOperationV::Translate_Forward, deltaTime);
            if (mMode == CameraMode::TPP)
            {
                mFreeSpeed = 1.0f;
                TickImplSpringArm(deltaTime, inWorld);
            }
            else if (mMode == CameraMode::FREE)
            {
                TranslateScale = Float3(5000.0, 5000.0, 5000.0);

                float left_delta = GetOperationValueT(CameraOperationV::Translate_Left, deltaTime);
                float up_delta = GetOperationValueT(CameraOperationV::Translate_Up, deltaTime);
                
                Float3 rotationDelta(pitch_delta, yaw_delta, 0.f);
                Double3 translationDelta(left_delta, up_delta, forward_delta);

                /*if (user->IsPressed(input::CEKeys::LeftControl))
                {
                    if (user->IsPressed(input::CEKeys::Zero))
                        mFreeSpeed = mFreeSpeedGears[0];
                    else if (user->IsPressed(input::CEKeys::One))
                        mFreeSpeed = mFreeSpeedGears[1];
                    else if (user->IsPressed(input::CEKeys::Two))
                        mFreeSpeed = mFreeSpeedGears[2];
                    else if (user->IsPressed(input::CEKeys::Three))
                        mFreeSpeed = mFreeSpeedGears[3];
                    else if (user->IsPressed(input::CEKeys::Four))
                        mFreeSpeed = mFreeSpeedGears[4];
                    else if (user->IsPressed(input::CEKeys::Five))
                        mFreeSpeed = mFreeSpeedGears[5];
                    else if (user->IsPressed(input::CEKeys::Six))
                        mFreeSpeed = mFreeSpeedGears[6];
                    else if (user->IsPressed(input::CEKeys::Seven))
                        mFreeSpeed = mFreeSpeedGears[7];
                    else if (user->IsPressed(input::CEKeys::Eight))
                        mFreeSpeed = mFreeSpeedGears[8];
                    else if (user->IsPressed(input::CEKeys::Nine))
                        mFreeSpeed = mFreeSpeedGears[9];
                }*/
                /*if (user->GetKeyValue(input::CEKeys::LeftShift))
                {
                    translationDelta *= mFreeSpeed * 20.0f;
                }
                else
                {
                    translationDelta *= mFreeSpeed;
                }*/
                translationDelta *= mFreeSpeed * mFreeSpeedAcc;
                TickImplFREE(rotationDelta, translationDelta, inWorld);
            }
            else if (mMode == CameraMode::LEFT_INSPECTION || mMode == CameraMode::RIGHT_INSPECTION)
            {
                mFreeSpeed = 1.0f;
                TickImplSpringArm(deltaTime, inWorld);
            }
        }
#endif 
    }

    void TestEnvControllableCamera::ApplyCalibrationOffset(GameWorld* inWorld, Double3& translation, Quaternion64& rotation)
    {
        if (Entity == ecs::EntityID::InvalidHandle() || Focuson == ecs::EntityID::InvalidHandle() || !inWorld->IsEntityAlive(Entity) || !inWorld->IsEntityAlive(Focuson))
        {
            return;
        }

        const auto& mpcdi = EngineGlobal::GetSettingMgr()->GetMPCDI();
        if (mpcdi.enabled)
        {
            // Only do calculation once
            if (!mCalibrationCalculated)
            {
                auto* cameraSys = inWorld->GetGameSystem<CameraSystemG>();
                auto cameraComp = inWorld->GetComponent<CameraComponentG>(Entity);

                double fov = cameraSys->GetFOV(cameraComp.Read());
                double distance = (GetEntityPosT(inWorld, Focuson) - GetEntityPosT(inWorld, Entity)).Length();
                double halfHeight = std::tan(fov / 2.0) * distance;

                double calibratedFov = MathUtils::ConvertToRadians(mpcdi.top - mpcdi.bottom);

                if (std::abs(calibratedFov) < 1e-7)
                {
                    LOG_WARN("MPCDI data may be invalid!");
                    Assert(false);
                    return;
                }

                double calibratedDistance = 1.0 / std::tan(calibratedFov / 2.0) * halfHeight;

                cameraSys->SetPerspectiveOffCenter(
                    cameraComp.Write(),
                    MathUtils::ConvertToRadians((float)mpcdi.left),
                    MathUtils::ConvertToRadians((float)mpcdi.right),
                    MathUtils::ConvertToRadians((float)mpcdi.top),
                    MathUtils::ConvertToRadians((float)mpcdi.bottom));

                mCalibrationRotation = Quaternion64::CreateFromYawPitchRollInDegrees(mpcdi.yaw, -mpcdi.pitch, mpcdi.roll);
                mCalibrationOffset = calibratedDistance - distance;

                mCalibrationCalculated = true;
            }

            rotation = mCalibrationRotation * rotation;
            translation -= rotation.GetForwardVector() * (mCalibrationOffset + AdditionalCalibrationOffset);
        }
    }

    void TestEnvControllableCamera::Tick(float deltaTime, GameWorld* inWorld)
    {
        return;
    }

    float TestEnvControllableCamera::GetOperationValueT(CameraOperationV t, float deltaTime)const
    {
        if (mMode != CameraMode::FREE)
        {
            return 0.0f;
        }

        auto inputMgr = EngineGlobal::Inst().GetInputManager();
        auto user = inputMgr->GetUser({0});
        if (t == CameraOperationV::Rotation_Yaw)
        {
            float h_target = 0.f;
            h_target = user->GetKeyValue(input::CEKeys::CursorX).x;
            return h_target * deltaTime;
        }

        if (t == CameraOperationV::Rotation_Pitch)
        {
            float v_target = 0.0f;
            v_target = user->GetKeyValue(input::CEKeys::CursorY).x;
            return v_target * deltaTime;
        }
        if (t == CameraOperationV::Translate_Forward)
        {
            float forward = 0.f, back = 0.f;
            forward = user->GetKeyValue(input::CEKeys::W).x;
            back = user->GetKeyValue(input::CEKeys::S).x;
            return (forward - back) * deltaTime;
        }
        if (t == CameraOperationV::Translate_Left)
        {
            float left = 0.f, right = 0.f;
            left = user->GetKeyValue(input::CEKeys::A).x;
            right = user->GetKeyValue(input::CEKeys::D).x;
            return (right - left) * deltaTime;
        }
        if (t == CameraOperationV::Translate_Up)
        {
            float up = 0.f, down = 0.f;
            up = user->GetKeyValue(input::CEKeys::E).x;
            down = user->GetKeyValue(input::CEKeys::Q).x;
            return (up - down) * deltaTime;
        }
        throw new std::exception();
    }

    float TestEnvControllableCamera::GetOperationValue(CameraOperationV t, float deltaTime) const {
        return .0f;
    }

    bool TestEnvControllableCamera::IsOperationValidated(CameraOperationV t) const
    {
        return false;
    }

    CameraMode TestEnvControllableCamera::GetCameraMode() const
    {
        return mMode;
    }

    void TestEnvControllableCamera::SetCameraMode(CameraMode mode)
    {
        if (mMode != mode)
        {
            ResetTransformCache();
        }
        mMode = mode;
    }

    float TestEnvControllableCamera::GetFreeSpeedGearValue(int gear) const
    {
        auto gea = std::clamp(gear, 0, FreeSpeedGearCount - 1);
        return mFreeSpeedGears[gea];
    }

    void TestEnvControllableCamera::SetFreeSpeedGearValue(int gear, float value)
    {
        gear = int((gear > 0 && gear < FreeSpeedGearCount) ? gear : mFreeSpeedGears.size()); 
        gear = (gear < FreeSpeedGearCount) ? gear : (FreeSpeedGearCount - 1);
        mFreeSpeedGears[gear] = value;
        LOG_INFO("Change free camera gear {} to value {}", gear, value);
    }

    // NOTE: Focused entity is not cleared
    void TestEnvControllableCamera::ResetCameraSettings()
    {
        ControllableCamera::ResetCameraSettings();

        FocusonOffset = {0.0f, 0.0f, 0.0f};
        ViewTargetOffset = {0.0f, 0.0f, 0.0f};
        ViewTargetEuler = {0.0f, 0.0f, 0.0f};
        EnableCameraLag = false;
        EnableCameraRotationLag = false;
        UseCameraLagSubstepping = false;
        CameraLagSpeed = 10.0f;
        CameraRotationLagSpeed = 10.0f;
        CameraLagMaxTimeStep = 1.f / 60.f;
        CameraLagMaxDistance = 0.0f;
        UseFixedUpdate = false;
        AdditionalCalibrationOffset = 0.0;
    }

    void TestEnvControllableCamera::ResetTransformCache()
    {
        mPreviousArmOrigin = Double3::Zero();
        mPreviousTranslation = Double3::Zero();
        mPreviousRotation = Quaternion64::Identity();
    }

    void OrbitFollowControllableCamera::Deserialize(GameWorld* inWorld, const DeserializeNode& in)
    {
        ControllableUnit::Deserialize(inWorld, in);

        if (in.HasMember("Focuson") && in["Focuson"].IsString())
        {
            auto const& targetNode = in["Focuson"];
            std::string targetEncodeStr = targetNode.AsString();

            auto target = ecs::EntityID::InvalidHandle();
           
            if (targetEncodeStr.length() > 0) 
            {
                if (inWorld != nullptr)
                    target = inWorld->GetEntity(EUID(targetEncodeStr));
                else
                {
                    UInt64 targetEncode = std::stoull(targetEncodeStr);
                    target = cross::ecs::EntityID(targetEncode);
                }
            }
           
            Focuson = target;
        }
        else
            Focuson = ecs::EntityID::InvalidHandle();
    }

    void OrbitFollowControllableCamera::Serialize(GameWorld* inWorld, SerializeNode& out) const
    {
        ControllableUnit::Serialize(inWorld, out);
    
        if (Focuson != ecs::EntityID::InvalidHandle())
        {
            if (inWorld != nullptr)
            {    
                if (inWorld->IsEntityAlive(Focuson))
                    out["Focuson"] = inWorld->GetEUID(Focuson).ToString();
                else
                    out["Focuson"] = "";
            }
            else
            {
                out["Focuson"] = std::to_string(Focuson.GetValue());
            }
        }
        else
            out["Focuson"] = "";
    }
    
    Float3A OrbitFollowControllableCamera::GetDesiredWorldTranslation(GameWorld* inWorld) const
    {
        auto sys = inWorld->GetGameSystem<TransformSystemG>();

        auto focusWorldComp = inWorld->GetComponent<WorldTransformComponentG>(Focuson);
        auto focusWorldPos = sys->GetWorldTranslation(focusWorldComp.Read());

        return focusWorldPos + GetDesiredWorldRotation(inWorld).GetForwardVector() * mViewTargetOffsetClamped * -1.0f;
    }

    void OrbitFollowControllableCamera::TickImpl(float deltaTime, float horionzalDeltaAxis, float verticalDeltaAxis, GameWorld* inWorld)
    {
        AddPitchInputImpl(verticalDeltaAxis);
        AddYawInputImpl(horionzalDeltaAxis);

        auto movementComp = inWorld->GetComponent<MotionMovementComponentG>(Entity);
        Assert(movementComp.IsValid());

        auto physicsShape = GetPhysicShapeForSweep(inWorld);
        auto physicsSys = inWorld->GetGameSystem<PhysicsSystemG>();

        if (physicsShape != nullptr 
            && physicsSys != nullptr 
            && BlockMask != CollisionMask::None()) 
        {
            // grab collision sweep start & end
            auto startPos = GetEntityPosT(inWorld, Focuson);
            std::unique_ptr<cross::PhysicsHitResult[]> result = std::make_unique<cross::PhysicsHitResult[]>(1);

            // is got hit with scene or not
            auto hitNum =
                physicsSys->Sweep(physicsShape, startPos, TRSQuaternionType::Identity(), TRSVector3Type::One(), GetTargetRot(inWorld).GetForwardVector() * -1.0f, ViewTargetOffsetMax, BlockMask, cross::HitFlag::Default, 1, nullptr, result.get());

            if (hitNum)
                mViewTargetOffsetClamped = result[0].distance;
            else
                mViewTargetOffsetClamped = ViewTargetOffsetMax;
        }
        else
            mViewTargetOffsetClamped = ViewTargetOffsetMax;

        // desired assignment into movement component 
        auto movementCompW = movementComp.Write();
        movementCompW->TargetTranslation = GetConstriantDesiredPos(inWorld);
        movementCompW->TargetRotation = GetConstriantDesiredRot(inWorld);

        // disable lerp for rotation & pos
        movementCompW->TargetAngularVelocity = -1.0f;
        movementCompW->TargetVelocity = -1.0f;
    }

    void OrbitFollowControllableCamera::TickImplT(float horionzalDeltaAxis, float verticalDeltaAxis, GameWorld* inWorld)
    {
        AddPitchInputImpl(verticalDeltaAxis);
        AddYawInputImpl(horionzalDeltaAxis);
#if defined(CE_USE_DOUBLE_TRANSFORM)    
        mViewTargetOffsetClamped = ViewTargetOffsetMax;
        
        auto sys = inWorld->GetGameSystem<TransformSystemG>();
        auto comp = inWorld->GetComponent<WorldTransformComponentG>(Entity);

        // controllable unit handle position
        TRSQuaternionAType focusRotation = GetEntityRotT(inWorld, Focuson);
        Double3 focusPosition = GetEntityPosT(inWorld, Focuson) + focusRotation.Double3Rotate(Double3(FocusonOffset));

        TRSQuaternionAType cameraRotation = TRSQuaternionAType::EulerToQuaternion64(mRotationEuler);
        //TRSVector3AType cameraPosition = GetEntityPosT(inWorld, mSelf);

        Double3 EUNNormal;
        TransformSystemG::CartesianCoordinateTransform_ToWGS84(focusPosition.x, focusPosition.y, focusPosition.z, EUNNormal);
        TRSQuaternionAType worldRotation = WGS84SystemG::CreateSphereSpaceWorldRotation(EUNNormal);
        TRSQuaternionAType finalRotation = cameraRotation * worldRotation;
        
        Double3 finalPosition = focusPosition + finalRotation.Double3Rotate(Double3(0, 0, -mViewTargetOffsetClamped));

        sys->SetWorldTranslationT(comp.Write(), finalPosition);
        sys->SetWorldRotationT(comp.Write(), finalRotation);
#endif
    }

    void HostedOrbitFollowCamera::PostTick(float deltaTime, GameWorld* inWorld)
    {
#if defined(CE_USE_DOUBLE_TRANSFORM)  
        if (Focuson == ecs::EntityID::InvalidHandle())
            return;
        float yaw_delta = GetOperationValueT(CameraOperationV::Rotation_Yaw, deltaTime);
        float pitch_delta = GetOperationValueT(CameraOperationV::Rotation_Pitch, deltaTime);
        TickImplT(yaw_delta, pitch_delta, inWorld);
#endif 
    }

    void HostedOrbitFollowCamera::Tick(float deltaTime, GameWorld* inWorld)
    {
#if defined(CE_USE_DOUBLE_TRANSFORM)  
        return;
#else
        auto yaw_delta = GetOperationValue(CameraOperationV::Rotation_Yaw, deltaTime);
        auto pitch_delta = GetOperationValue(CameraOperationV::Rotation_Pitch, deltaTime);
        TickImpl(deltaTime, yaw_delta, pitch_delta, inWorld);
#endif
    }

    float HostedOrbitFollowCamera::GetOperationValueT(CameraOperationV t, float deltaTime) const
    {
        // TODO(xtnwang) : axis logic will be completed later, temporary code for now

        static const float MOVEMENT_SMALL_WEIGHT = 0.001f;
        auto inputMgr = EngineGlobal::Inst().GetInputManager();
        auto user = inputMgr->GetUser({0});
        if (t == CameraOperationV::Rotation_Yaw)
        {
            float h_target = 0.f;
            h_target = user->GetKeyValue(input::CEKeys::CursorX).x;
            // infinite hor lerp speed, assign normalized value immediately
            return h_target * deltaTime;
        }

        if (t == CameraOperationV::Rotation_Pitch)
        {
            float v_target = 0.0f;
            v_target = user->GetKeyValue(input::CEKeys::CursorY).x;
            // infinite ver lerp speed, assign normalized value immediately
            return v_target * deltaTime;
        }
        throw new std::exception();
    }

    float HostedOrbitFollowCamera::GetOperationValue(CameraOperationV t, float deltaTime) const
    {
        // TODO(xtnwang) : axis logic will be completed later, temporary code for now           
        return 0.0f;
        /*
        auto itr = std::find_if(AxisMappings.begin(), AxisMappings.end(), [t](auto const& elem) { 
            return elem.Op == t;
        });
        Assert(itr != AxisMappings.end());
 
        static const float MOVEMENT_SMALL_WEIGHT = 0.001f;

        if (itr->Axis == InputAxis::Horizontal) 
        {
            auto& global = EngineGlobal::Inst();
            auto inputMgr = global.GetInputManager();

            float h_target = 0.f;
#if CROSSENGINE_WIN
            if (std::abs(inputMgr->GetAxis(InputAxis::Horizontal)) > MOVEMENT_SMALL_WEIGHT)
                h_target = inputMgr->GetAxis(InputAxis::Horizontal) > 0.0f ? 1.0f : -1.0f;
#endif

            // infinite hor lerp speed, assign normalized value immediately
            if (ScalarAcceleration.x < MOVEMENT_SMALL_WEIGHT || 
                (std::abs)(h_target - ScalarTargetVelocity.x) < MOVEMENT_SMALL_WEIGHT)
                ScalarTargetVelocity.x = h_target;
            // valid hor lerp speed, execute here
            else if ((std::abs)(h_target - ScalarTargetVelocity.x) > MOVEMENT_SMALL_WEIGHT)
            {
                // move towards sign
                float h_sign = (h_target - ScalarTargetVelocity.x) > 0 ? 1.0f : -1.0f;
                // move source target grabbed here
                float h_source = (h_target * h_sign == 0.0f)   // if no input along this axis
                                     ? -1.0f * h_sign
                                     : -1.0f * h_target;
                // move delta value
                float delta_velocity = ScalarAcceleration.x * h_sign * deltaTime;

                ScalarTargetVelocity.x = std::clamp(ScalarTargetVelocity.x + delta_velocity, (std::min)(h_source, h_target), (std::max)(h_source, h_target));
            }

            return ScalarTargetVelocity.x;
        }

        if (itr->Axis == InputAxis::Vertical)
        {
            auto& global = EngineGlobal::Inst();
            auto inputMgr = global.GetInputManager();

            float v_target = 0.0f;
#if CROSSENGINE_WIN
            if (std::abs(inputMgr->GetAxis(InputAxis::Vertical)) > MOVEMENT_SMALL_WEIGHT)
                v_target = inputMgr->GetAxis(InputAxis::Vertical) > 0.0f ? 1.0f : -1.0f;
#endif

            // infinite ver lerp speed, assign normalized value immediately
            if (ScalarAcceleration.y < MOVEMENT_SMALL_WEIGHT)
                ScalarTargetVelocity.y = v_target;
            // valid ver lerp space, execute here
            else if ((std::abs)(v_target - ScalarTargetVelocity.y) > MOVEMENT_SMALL_WEIGHT)
            {
                // move towards sign
                float v_sign = (v_target - ScalarTargetVelocity.y) > 0 ? 1.0f : -1.0f;
                // move source target grabbed here
                float v_source = (v_target * v_sign == 0.0f)   // if no input along this axis
                                     ? -1.0f * v_sign
                                     : -1.0f * v_target;
                // move delta value
                float delta_velocity = ScalarAcceleration.y * v_sign * deltaTime;

                ScalarTargetVelocity.y = std::clamp(ScalarTargetVelocity.y + delta_velocity, (std::min)(v_source, v_target), (std::max)(v_source, v_target));
            }

            return ScalarTargetVelocity.y;
        }
    
        throw new std::exception();
        */
    }

    bool HostedOrbitFollowCamera::IsOperationValidated(CameraOperationV t) const
    {
        // TODO(xtnwang) : axis logic will be completed later, temporary code for now
        return false;
    }

    void FirstPersonPerspectCamera::PostTick(float deltaTime, GameWorld* inWorld) 
    {
        std::for_each(mAxisOperations.begin(), mAxisOperations.end(), [](auto& elem) { elem.second = 0.f; });
    }

    static HitFlag FppHitFlag()
    {
        HitFlag result{0};
        EnumAddFlags(result, HitFlag::Position);
        EnumAddFlags(result, HitFlag::Normal);
        EnumAddFlags(result, HitFlag::MTD);
        EnumAddFlags(result, HitFlag::MeshBothSides);
        return result;
    }

    void FirstPersonPerspectCamera::TickImpl(float deltaTime, float horionzalDeltaAxis, float verticalDeltaAxis, Float2 forwardDirection, GameWorld* inWorld) 
    {
        auto physicsSys = inWorld->GetGameSystem<PhysicsSystemG>();
        auto physicComp = inWorld->GetComponent<PhysicsComponentG>(Entity);
        if (!physicsSys->GetEnableGravity(physicComp.Read()))
        {
            return;
        }

        // fpp rotation 
        auto transSys = inWorld->GetGameSystem<TransformSystemG>();
        auto transComp = inWorld->GetComponent<WorldTransformComponentG>(Entity);
        Quaternion worldRot = transSys->GetWorldRotation(transComp.Read());
        mRotationEuler = Quaternion::QuaternionToEuler(worldRot);


        AddPitchInputImpl(verticalDeltaAxis);
        AddYawInputImpl(horionzalDeltaAxis);

        //if (std::abs(verticalDeltaAxis) > 0.0001f || std::abs(horionzalDeltaAxis) > 0.00001f)
        //    LOG_INFO("wxt horionzal value:{} vertical value: {}", horionzalDeltaAxis, verticalDeltaAxis);

        auto transCompW = transComp.Write();
        Quaternion curRot = Quaternion::EulerToQuaternion(mRotationEuler);
        transSys->SetWorldRotation(transCompW, curRot);
        
        // translation
        auto physicsShape = GetPhysicShapeForSweep(inWorld);
        auto currentPos = GetTargetPosT(inWorld);

        std::unique_ptr<cross::PhysicsHitResult[]> result = std::make_unique<cross::PhysicsHitResult[]>(1);

        // fpp translation -- xoz
        Float3 xoz_delta(0.f, 0.f, 0.f);

        if (physicsSys->GetEnableGravity(physicComp.Read()))
        {
            xoz_delta += curRot.GetForwardVector().ProjectOnPlane(Float3(0, 1, 0)) * forwardDirection.y * TranslateScale.z * deltaTime;
            xoz_delta += curRot.GetRightVector().ProjectOnPlane(Float3(0, 1, 0)) * forwardDirection.x * TranslateScale.x * deltaTime;    
        }

        LOG_INFO("xoz x{} y{}", xoz_delta.x, xoz_delta.y);

        auto hitNum = 0;

        if (xoz_delta.Length() > 0.0001f && physicsShape)
        {
            hitNum = physicsSys->Sweep(physicsShape,
                                currentPos,
                                TRSQuaternionType::Identity(), 
                                TRSVector3Type::One(), 
                                xoz_delta.Normalized(), 
                                xoz_delta.Length(), 
                                BlockMask, 
                                FppHitFlag(), 
                                1, nullptr,
                                result.get());
        }

        // slope or collision passed
        if (hitNum == 0)
            currentPos += TRSVector3Type( xoz_delta);
        else if (xoz_delta.Length() > 0.0001f && physicsShape)
        {
            auto& hitResult = *result.get();
            auto n_cross_v = hitResult.normal.Cross(xoz_delta.Normalized());
            auto fixed_direction = n_cross_v.Cross(hitResult.normal);

            if (std::abs(hitResult.position.y + StandHeight - currentPos.y) < MaxWalkableSpan)
                currentPos += TRSVector3Type(xoz_delta);
            else 
            {
                xoz_delta = fixed_direction.ProjectOnPlane(Float3(0, 1, 0)) * TranslateScale.z * deltaTime;

                if (xoz_delta.Length() > 0.0001f && physicsShape)
                {
                    hitNum = physicsSys->Sweep(physicsShape, currentPos, TRSQuaternionType::Identity(), TRSVector3Type::One(), xoz_delta.Normalized(), xoz_delta.Length(), BlockMask, cross::HitFlag::MeshBothSides, 1, nullptr, result.get());

                    if (hitNum == 0)
                        currentPos += TRSVector3Type(xoz_delta);
                }
            }
        }

        // fpp translation -- gravity
        if (physicsShape)
        {
            hitNum = physicsSys->Sweep(physicsShape,
                                       currentPos,
                                       TRSQuaternionType::Identity(),
                                       TRSVector3Type::One(),
                                       Float3(0, -1, 0),
                                       ((std::abs)(mLastVelocity.y) + 980 * deltaTime * 0.5f) * deltaTime * TranslateScale.y + StandHeight,
                                       BlockMask,
                                       FppHitFlag(),
                                       1,
                                       nullptr,
                                       result.get());

            // guarantee on the ground
            auto nextForwardPos = currentPos;
            if (hitNum)
            {
                if (xoz_delta.Length() > 0.0001f)
                {
                    #ifdef CE_USE_DOUBLE_TRANSFORM
                    auto nextHeight = GetAbsolutePosition(result[0].position, result[0].tile).y + StandHeight;
                    #else
                    auto nextHeight = result[0].position.y + StandHeight;
                    #endif
                    auto curHeight = currentPos.y;

                    if (nextHeight > curHeight)
                    {
                        float angleDeltaInRadian = std::atan2f(float(nextHeight - curHeight), xoz_delta.Length());

                        float anlgeDeltaInDegree = angleDeltaInRadian * 180.f / MathUtils::MathPi;

                        if (anlgeDeltaInDegree > MaxWalkableSlope && mFreezeCount < 20)
                        {
                            nextForwardPos -= TRSVector3Type(xoz_delta);
                            mFreezeCount++;
                        }
                        else
                        {
                            nextForwardPos.y = result[0].position.y + StandHeight;
                            mLastVelocity.y = 0.f;
                            mFreezeCount = 0;
                        }
                    }
                    else
                    {
                        nextForwardPos.y = result[0].position.y + StandHeight;
                        mLastVelocity.y = 0.f;
                        mFreezeCount = 0;
                    }
                }
                else
                {
                    nextForwardPos.y = result[0].position.y + StandHeight;
                    mLastVelocity.y = 0.f;
                    mFreezeCount = 0;
                }
            }
            else
            {
                nextForwardPos.y = nextForwardPos.y + (mLastVelocity.y + -980 * deltaTime * 0.5f) * deltaTime * TranslateScale.y;
                mLastVelocity.y = (mLastVelocity.y + -980 * deltaTime) * TranslateScale.y;
                mFreezeCount = 0;
            }


            transSys->SetWorldTranslationT(transComp.Write(), nextForwardPos);
        }
    }

    void HostedFppCamera::Tick(float deltaTime, GameWorld* inWorld) 
    {
        // check current is ctrled by other curve controller
        if (inWorld->HasComponent<ControllableUnitComponentG>(Entity)) 
        {
            auto characterSystem = inWorld->GetGameSystem<ControllableUnitSystemG>();
            auto ccComp = inWorld->GetComponent<ControllableUnitComponentG>(Entity);
            
            if (characterSystem->IsPlayingCurveControl(ccComp.Write())) 
            {
                mLastVelocity = Float3::Zero();
                return;
            }
        }

        // check current is lerping into spawn point or not
        if (SpawnSpot != ecs::EntityID::InvalidHandle() && mLerpingIntoSpawnPointTotalSec > 0.f) 
        { 
            mLerpingIntoSpawnPointLeftSec -= deltaTime;

            auto spawnTransSys = inWorld->GetGameSystem<TransformSystemG>();
            auto spawnTransComp = inWorld->GetComponent<WorldTransformComponentG>(SpawnSpot);

            auto targetTransComp = inWorld->GetComponent<WorldTransformComponentG>(Entity);

            auto spawnTranslation = spawnTransSys->GetWorldTranslation(spawnTransComp.Read());
            auto spawnRotation = spawnTransSys->GetWorldRotation(spawnTransComp.Read());

            if (mLerpingIntoSpawnPointLeftSec < 0.f)
            {
                // forbid repeat enter
                mLerpingIntoSpawnPointTotalSec = -1.0f;      

                spawnTransSys->SetWorldTranslation(targetTransComp.Write(), spawnTranslation);
                spawnTransSys->SetWorldRotation(targetTransComp.Write(), spawnRotation);
#if SUPPORT_LUA
                ScriptSystemG* scriptSys = inWorld->GetGameSystem<ScriptSystemG>();
                scriptSys->OnCurveControllerEvent(mSelf, CurveControllerEventType::SpawnSpotEnd);
#endif
                return;
            }

            auto lerpingIntoSpawnPointLeftSec = std::clamp(mLerpingIntoSpawnPointLeftSec, 0.f, mLerpingIntoSpawnPointTotalSec);
            float ratio = lerpingIntoSpawnPointLeftSec / mLerpingIntoSpawnPointTotalSec;

            Float3A lerpedTranslation = Float3::Lerp(mLerpingStartSpawnTranslation, spawnTranslation, 1.0f - ratio);
            Quaternion lerpedRotation = Quaternion::Slerp(mLerpingStartSpawnRotation, spawnRotation, 1.0f - ratio);

            spawnTransSys->SetWorldTranslation(targetTransComp.Write(), lerpedTranslation);
            spawnTransSys->SetWorldRotation(targetTransComp.Write(), lerpedRotation);
            return;
        }

        auto yaw_delta = GetOperationValue(CameraOperationV::Rotation_Yaw, deltaTime);
        auto pitch_delta = GetOperationValue(CameraOperationV::Rotation_Pitch, deltaTime);

        Float2 direction(0.f, 0.f);

#if CROSSENGINE_WIN 
        auto abstractInputForward = GetInputOperationValue(CameraOperationV::Translate_Forward);
        direction.y = abstractInputForward;
      
        auto abstractInputLeft = GetInputOperationValue(CameraOperationV::Translate_Left);
        direction.x = abstractInputLeft * -1.0f;
        /*
        auto& global = EngineGlobal::Inst();
        auto inputMgr = global.GetInputManager();
        direction.y = inputMgr->GetButton(InputButton::Key_W) ? 1.0f : (inputMgr->GetButton(InputButton::Key_S) ? -1.0f : 0.0f);
        direction.x = inputMgr->GetButton(InputButton::Key_D) ? 1.0f : (inputMgr->GetButton(InputButton::Key_A) ? -1.0f : 0.0f);*/
        LOG_INFO("direction {} {}", direction.x, direction.y);
#endif

        TickImpl(deltaTime, yaw_delta, pitch_delta, direction, inWorld);
    }

    float HostedFppCamera::GetOperationValue(CameraOperationV t, float deltaTime) const
    {
        // TODO(xtnwang) : axis logic will be completed later, temporary code for now

        static const float MOVEMENT_SMALL_WEIGHT = 0.001f;

        if (t == CameraOperationV::Rotation_Yaw)
        {
            auto& global = EngineGlobal::Inst();
            auto inputMgr = global.GetInputManager();

            float h_target = 0.f;
            if (GetInputOperationValue(CameraOperationV::Rotation_Yaw) > 0.001f) 
            {
                auto user = inputMgr->GetUser({0});
                h_target = user->GetKeyValue(input::CEKeys::CursorX).x;

                //LOG_INFO("wangxiaotian {}", h_target);
            }

            // infinite hor lerp speed, assign normalized value immediately
            ScalarTargetVelocity.x = h_target * deltaTime;

            return ScalarTargetVelocity.x;
        }

        if (t == CameraOperationV::Rotation_Pitch)
        {
            auto& global = EngineGlobal::Inst();
            auto inputMgr = global.GetInputManager();

            float v_target = 0.0f;
            if (GetInputOperationValue(CameraOperationV::Rotation_Pitch) > 0.001f) 
            {
                auto user = inputMgr->GetUser({0});
                v_target = user->GetKeyValue(input::CEKeys::CursorY).x;
            }

            // infinite ver lerp speed, assign normalized value immediately
            ScalarTargetVelocity.y = v_target * deltaTime;

            return ScalarTargetVelocity.y;
        }

        throw new std::exception();
    }

}


