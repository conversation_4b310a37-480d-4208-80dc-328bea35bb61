#include "EnginePrefix.h"
#include "CECommon/Common/EngineGlobal.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "CharacterMovementSystemG.h"
#include "Runtime/GameWorld/CameraSystemG.h"
#include "Runtime/GameWorld/ModelSystemG.h"
#include "Runtime/GameWorld/AnimatorSystemG.h"
#include "Runtime/GameWorld/InputSystemG.h"
#include "Runtime/Interface/CrossEngineImp.h"

namespace cross {

static inline float InterpToValue(float inValue, float inTargetValue, float interpSpeed, float deltaTime)
{
    float deltaValue = interpSpeed * deltaTime;
    float distance = std::abs(inTargetValue - inValue);

    if (distance > deltaValue)
    {
        if (inValue > inTargetValue)
            return inValue - deltaValue;
        else
            return inValue + deltaValue;
    }
    else
    {
        return inTargetValue;
    }
}

/////////////////////////////////////////////
// CharacterMovementSystemG
//
/////////////////////////////////////////////
CharacterMovementSystemG* CharacterMovementSystemG::CreateInstance()
{
    return new CharacterMovementSystemG();
}

CharacterMovementSystemG::CharacterMovementSystemG() {}

CharacterMovementSystemG::~CharacterMovementSystemG() {}

void CharacterMovementSystemG::NotifyEvent(const SystemEventBase& event, UInt32& flag) 
{
    GameSystemBase::NotifyEvent(event, flag);
    // Character Movement Event
    if (event.mEventType == CharacterMovementEvent::sEventType)
    {
        const CharacterMovementEvent* movementEvent = TYPE_CAST(const CharacterMovementEvent*, &event);
        OnCharacterMovementEvent(movementEvent->mData.mCharacterEntity, movementEvent->mData.mType);
    }
    else if (event.mEventType == OnSystemAddToGameWorldEvent::sEventType || event.mEventType == GameWorldSystemChangedEvent::sEventType)
    {
        // mGameWorld->SubscribeRemainedEvent<EntityLifeCycleEvent>(this, true);

        if (mPhysicsSys == nullptr)
        {
            mPhysicsSys = mGameWorld->GetGameSystem<PhysicsSystemG>();
        }
        if (mTransformSys == nullptr)
        {
            mTransformSys = mGameWorld->GetGameSystem<TransformSystemG>();
        }
    }
}

void CharacterMovementSystemG::OnFirstUpdate(FrameParam* frameParam) 
{
    // Subscribe events
    SubscribeEvent<CharacterMovementEvent>(this, 0);
}

// For Testing Only
#if ENABLE_CHARACTER_MOVEMENT_TEST_CODE
Float3 CharacterMovementSystemG::AccumlateScalarTargetVelocity(const CharacterMovementCompWriter& inHandle, float elapsedTime)
{
    auto& global = EngineGlobal::Inst();
    auto inputSys = global.GetEngine()->GetGlobalSystem<InputSystemG>();

    float h_target = 0.0f, v_target = 0.0f;

#if CROSSENGINE_WIN
    // character ctrl by hosted player
    v_target = inputSys->IsPressed({0}, "W") ? 1.0f : (inputSys->IsPressed({0}, "S") ? -1.0f : 0.0f);

    h_target = inputSys->IsPressed({0}, "D") ? 1.0f : (inputSys->IsPressed({0}, "A") ? -1.0f : 0.0f);
    // character ctrl by ai, do nothing for now
#endif

    Float2A TargetVelocity{0.f, 0.f};

    // valid ver lerp space, execute here
    if ((std::abs)(v_target - TargetVelocity.y) > MOVEMENT_SMALL_NUMBER)
    {
        // move towards sign
        float v_sign = (v_target - TargetVelocity.y) > 0 ? 1.0f : -1.0f;
        // move source target grabbed here
        float v_source = (v_target * v_sign == 0.0f)   // if no input along this axis
                             ? -1.0f * v_sign
                             : -1.0f * v_target;
        // move delta value
        float delta_velocity = 1000.f * elapsedTime * v_sign;

        TargetVelocity.y = std::clamp(TargetVelocity.y + delta_velocity, (std::min)(v_source, v_target), (std::max)(v_source, v_target));
    }

    if ((std::abs)(h_target - TargetVelocity.x) > MOVEMENT_SMALL_NUMBER)
    {
        // move towards sign
        float h_sign = (h_target - TargetVelocity.x) > 0 ? 1.0f : -1.0f;
        // move source target grabbed here
        float h_source = (h_target * h_sign == 0.0f)   // if no input along this axis
                             ? -1.0f * h_sign
                             : -1.0f * h_target;
        // move delta value
        float delta_velocity = 1000.f * elapsedTime * h_sign;

        TargetVelocity.x = std::clamp(TargetVelocity.x + delta_velocity, (std::min)(h_source, h_target), (std::max)(h_source, h_target));
    }

    Float3 aimingProjForwrd = {0, 0, 1};
    Float3 aimingProjRight = {1, 0, 0};

    if (inHandle->AimingEntity)
    {
        auto worldTransComp = mGameWorld->GetComponent<WorldTransformComponentG>(inHandle.GetEntityID());
        auto aimingWorldTransComp = mGameWorld->GetComponent<WorldTransformComponentG>(inHandle->AimingEntity);
        // calculate camera forward in the very beginning
        auto aimingForward = mTransformSys->GetWorldRotation(aimingWorldTransComp.Read()).GetForwardVector();
        auto aimingRight = mTransformSys->GetWorldRotation(aimingWorldTransComp.Read()).GetRightVector();
        // calculate character normal then for late projection
        auto characterNormal = mTransformSys->GetWorldRotation(worldTransComp.Read()).GetUpVector();

        aimingProjForwrd = aimingForward.ProjectOnPlane(characterNormal);
        aimingProjRight = aimingRight.ProjectOnPlane(characterNormal);
    }

    Float3A fwdVelocity, rightVelocity;
    if (std::abs(TargetVelocity.y) > 0.f)
    {
        fwdVelocity = aimingProjForwrd * TargetVelocity.y;
    }
    if (std::abs(TargetVelocity.x) > 0.f)
    {
        rightVelocity = aimingProjRight * TargetVelocity.x;
    }

    return fwdVelocity + rightVelocity;
}

void CharacterMovementSystemG::SetAimEntity(const CharacterMovementCompWriter& inHandle, cross::ecs::EntityID inAimingEntity)
{
    // Only entity with camera component should be a aiming entity
    if (!mGameWorld->HasComponent<CameraComponentG>(inAimingEntity))
    {
        return;
    }

    inHandle->AimingEntity = inAimingEntity;
}

void CharacterMovementSystemG::SetRotationMode(const CharacterMovementCompWriter& inHandle, UInt32 inMode)
{
    auto actualMode = MathUtils::Clamp<UInt32>(inMode, 0U, 1U);
    inHandle->RotationMode = static_cast<CharacterRotationMode>(actualMode);
}

void CharacterMovementSystemG::SetAngleBetweenActorForwardAndCurrentVelocity(const CharacterMovementCompWriter& inHandle, float yawOffset)
{
    if (!inHandle->AimingEntity)
    {
        LOG_ERROR("Invalid AimingEntity");
        return;
    }
    auto aimWorldComp = mGameWorld->GetComponent<WorldTransformComponentG>(inHandle->AimingEntity);
    auto aimWorldEuler = Quaternion::QuaternionToEuler(mTransformSys->GetWorldRotation(aimWorldComp.Read()));

    auto worldTransComp = mGameWorld->GetComponent<WorldTransformComponentG>(inHandle.GetEntityID());
    auto actorRotation = mTransformSys->GetWorldRotation(worldTransComp.Read());
    auto actorRotationR = actorRotation * Quaternion::CreateFromAxisAngle(Float3A(0, 1, 0), MathUtils::MathPi);
    auto actorRotationEuler = Quaternion::QuaternionToEuler(actorRotationR);

    inHandle->ActorRotationEuler.y = aimWorldEuler.y - actorRotationEuler.y + yawOffset;

    if (inHandle->ActorRotationEuler.y < MathUtils::MathPi * (-1.0f))
        inHandle->ActorRotationEuler.y += MathUtils::Math2Pi;

    if (inHandle->ActorRotationEuler.y > MathUtils::MathPi)
        inHandle->ActorRotationEuler.y -= MathUtils::Math2Pi;
}

Float3A CharacterMovementSystemG::GetAngleBetweenAimForwardAndCurrentVelocity(const CharacterMovementCompReader& inHandle)
{
    if (inHandle->AimingEntity == cross::ecs::EntityID::InvalidHandle())
    {
        return Float3A::Zero();
    }

    auto velocityForward = GetCurrentVelocityInWorldSpace(inHandle);
    if (velocityForward.Length() < MOVEMENT_SMALL_NUMBER) 
    {
        return Float3A::Zero();
    }

    auto worldTransComp = mGameWorld->GetComponent<WorldTransformComponentG>(inHandle.GetEntityID());
    auto aimingWorldTransComp = mGameWorld->GetComponent<WorldTransformComponentG>(inHandle->AimingEntity);

    // calculate camera forward in the very beginning
    auto aimingForward = mTransformSys->GetWorldRotation(aimingWorldTransComp.Read()).GetForwardVector();
    // calculate character normal then for late projection
    auto characterNormal = mTransformSys->GetWorldRotation(worldTransComp.Read()).GetUpVector();

    auto aimingForwrd = aimingForward.ProjectOnPlane(characterNormal);
    auto rotation = Quaternion::CreateFrom2Vectors(aimingForwrd, velocityForward.Normalized());
    return Quaternion::QuaternionToEuler(rotation);
}

/* 
 * When calculate character current rotation( actually CharacterFwd ), we make current rotation lerp to TargetRotation.
 * So TargetRotation = CharacterFwd + DeltaRot
 * TargetRotation depends on three variables: Velocity, CharacterFwd, CameraAimFwd;
 * CameraAimFwd only depends on Mouse Rotation Input.
 * Velocity will lerp to TargetVelocity which only determined by CameraAimFwd and KeyInput.
 * CharacterFwd will lerp to TargetRotation in some strategy:
 * In 8-Directional BlendSpace Moving, we want character to face Fwd when character is moving towards Fwd, Bwd, Left, Right,
 * and face LeftFwd when moving towards LeftFwd, RightBwd, face RightFwd when moving towards RightFwd, LeftBwd.
 * So we need an YawOffset to make CharacterFwd not always lerp to CameraAimFwd( DeltaRot will always = CameraAimFwd - CharacterFwd).
 * We make YawOffset = (Velocity - CameraAimFwd), and then DeltaRot = CameraAimFwd - CharacterFwd + YawOffset;
 * So CharacterFwd will lerp to (CameraAimFwd + Velocity - CameraAimFwd). When Velocity is eventually steady, CharacterFwd will be steady;
 *
 * Error situation:
 * When YawOffset = (Velocity - CharacterFwd), then CharacterFwd will lerp to (CameraAimFwd + Velocity - CharacterFwd). When Velocity is eventually steady, CharacterFwd is relative to itself;
 * This will make character shake repeatedly.
 */
void CharacterMovementSystemG::CalculateCurrentRotation(const CharacterMovementCompReader& inHandle, float elapsedTime)
{
    /* Cur rot speed in radians */
    static const float ActorRotationSpeed = 260.0f / 180.0f * MathUtils::MathPi;

    /* Grab euler angle of current actor */
    auto worldTransComp = mGameWorld->GetComponent<WorldTransformComponentG>(inHandle.GetEntityID());
    auto actorRot = mTransformSys->GetWorldRotation(worldTransComp.Read());
    auto actorEuler = Quaternion::QuaternionToEuler(actorRot);

    Quaternion resultRot;
    if (inHandle->RotationMode == CharacterRotationMode::LookingDirection)
    {
        // Grab euler angle of target rot from component
        auto targetEuler = actorEuler + inHandle->ActorRotationEuler;

        // Lerp from current to target
        actorEuler.y = InterpToValue(actorEuler.y, targetEuler.y, ActorRotationSpeed, elapsedTime);
        resultRot = Quaternion::EulerToQuaternion(actorEuler);
    }
    else if (inHandle->RotationMode == CharacterRotationMode::VelocityDirection)
    {
        worldTransComp = mGameWorld->GetComponent<WorldTransformComponentG>(inHandle.GetEntityID());
        auto actorRotation = mTransformSys->GetWorldRotation(worldTransComp.Read());
        actorRotation = actorRotation * Quaternion::CreateFromAxisAngle(Float3A(0, 1, 0), MathUtils::MathPi);

        auto deltaRot = Quaternion::CreateFrom2Vectors(actorRotation.GetForwardVector(), inHandle->Velocity);
        Float3 euler = Quaternion::QuaternionToEuler(deltaRot);
        euler.x = 0.f;
        euler.z = 0.f;
        euler.y = InterpToValue(0.0, euler.y, ActorRotationSpeed, elapsedTime);
        resultRot = actorRot * Quaternion::EulerToQuaternion(euler);
    }

    mTransformSys->SetWorldRotation(worldTransComp.Write(), resultRot);
}
#endif

void CharacterMovementSystemG::OnBuildUpdateTasks(FrameParam* frameParam) 
{
    SInt32 numThreads = (threading::TaskSystem::Get()->GetNumMasterThreads() + threading::TaskSystem::Get()->GetNumTaskThreads()) * 8;
    const SInt32 minSizeForSingleThread = 4;

    bool needUpdate = false;
    needUpdate = needUpdate || (EngineGlobal::GetSettingMgr()->GetAppStartUpType() == AppStartUpType::AppStartUpTypeCrossEditor && mGameWorld->GetWorldType() == WorldTypeTag::PIEWorld);
    needUpdate = needUpdate || EngineGlobal::GetSettingMgr()->GetAppStartUpType() == AppStartUpType::AppStartUpTypeStandAlone;

    if (!needUpdate)
    {
        return;
    }

    auto queryResult = mGameWorld->Query<CharacterMovementComponentG, PhysicsComponentG, WorldTransformComponentG>();
    const SInt32 entityNum = queryResult.GetEntityNum();
    const SInt32 taskCountForSingleThread = (std::max)((SInt32)std::ceil(entityNum * 1.0f / numThreads), minSizeForSingleThread);

    // make a cursor here
    SInt32 leftEntityNum = entityNum;

    auto elapsedTime = frameParam->GetDeltaTime();
    for (SInt32 Index = 0; Index < numThreads; Index += 1) 
    {
        CreateTaskFunction<threading::ThreadID::GameThreadLocal>(FrameTickStage::Update, {}, [this, Index, numThreads, elapsedTime, taskCountForSingleThread, entityNum]() mutable
        {
            QUICK_SCOPED_CPU_TIMING("CharacterMovementUpdate");
            // grab all character movement for execute, forbid other task modify store hierarchy
            auto queryResult = mGameWorld->Query<CharacterMovementComponentG, PhysicsComponentG, WorldTransformComponentG>();
            // grab start index for current thread
            SInt32 startIndex = Index * taskCountForSingleThread;

            for (UInt32 entityIdx = startIndex, endIndex = (std::min)(startIndex + taskCountForSingleThread, entityNum); entityIdx < endIndex; entityIdx++) 
            {
                auto&& [moveComp, physicsComp, transformComp] = queryResult[entityIdx];

                if (!physicsComp.IsValid() || !moveComp.IsValid())
                {
                    continue;
                }

                auto moveCompReader = moveComp.Read();
                auto moveCompWriter = moveComp.Write();

// For Testing Begin
#if ENABLE_CHARACTER_MOVEMENT_TEST_CODE
                AddInputVector(moveCompWriter, AccumlateScalarTargetVelocity(moveCompWriter, elapsedTime));
#endif
// For Testing End

                // Consume input
                Float3 lastInput = ConsumeInputVector(moveCompWriter);

                // We need to check the jump state before adjusting input acceleration, to minimize latency and to make sure acceleration respects our potentially new falling state.
                CheckJumpInput(moveCompWriter, elapsedTime);

                // Calculate acceleration
                Float3 constrainedInput = ConstrainInput(moveCompReader, lastInput);
                ApplyInputToAcceleration(moveCompWriter, constrainedInput);

                if (moveCompReader->MoveMode == MovementMode::MOVE_None)
                {
                    continue;
                }

                // Scoped update
                {
                    // Update position based on Floor movement
                    UpdateBasedOnFloorMovement(moveCompWriter);

                    // Process root motion from animation
                    ProcessRootMotionFromAnimation(moveCompWriter);

                    // Apply root motion to velocity
                    if (HasAnimRootMotion(moveCompReader))
                    {
                        // Convert local space root motion to world space, because animation root motion is always local
                        ConvertLocalRootMotionToWorld(moveCompWriter);

                        if (elapsedTime > 0.f)
                        {
                            // Calculate velocity from root motion translation
                            CalculateRootMotionVelocity(moveCompWriter, elapsedTime);
                            moveCompWriter->Velocity = ConstrainRootMotionVelocity(moveCompReader);
                        }
                    }

                    // Clear jump input now, to allow movement events to trigger it for next update.
                    ClearJumpInput(moveCompWriter, elapsedTime);
                    moveCompWriter->JumpApexAttemptsNum = 0;

                    // Physics movement
                    PerformPhysicsMovement(moveCompWriter, elapsedTime, 0U);

// For Testing Begin
#if ENABLE_CHARACTER_MOVEMENT_TEST_CODE
                    if (!HasAnimRootMotion(moveCompReader))
                    {
                        CalculateCurrentRotation(moveCompReader, elapsedTime);
                    }
#endif
// For Testing End

                    // Apply root motion rotation after movement is complete.
                    if (HasAnimRootMotion(moveCompReader))
                    {
                        ApplyRootMotionRotation(moveCompWriter);

                        // Root Motion has been used, reset it
                        moveCompWriter->RootMotion.Reset();
                    }

                    //
                    // OnMovementUpdated(elapsedTime, moveCompReader->LastUpdateLocation, moveCompReader->LastUpdateVelocity);
                }

                SaveFloorMovement(moveCompWriter);

                moveCompWriter->LastUpdateLocation = mTransformSys->GetWorldTranslation(transformComp.Read());
                moveCompWriter->LastUpdateRotation = mTransformSys->GetWorldRotation(transformComp.Read());
                moveCompWriter->LastUpdateVelocity = moveCompWriter->Velocity;

                if (moveCompReader->DrawDebugShape)
                {
                    DrawDebugShape(moveCompReader);
                }
            }
        });
        
        // no more valid task exists, out early
        leftEntityNum -= taskCountForSingleThread;
        if (leftEntityNum <= 0)
        {
            break;
        }
    }
}

void CharacterMovementSystemG::GetCharacterMovementComponent(const CharacterMovementCompReader& inHandle, cross::CharacterMovementComponentG& outValue) const
{
    outValue.DrawDebugShape = inHandle->DrawDebugShape;
    // General Settings
    outValue.GravityScale = inHandle->GravityScale;
    outValue.BrakingFriction = inHandle->BrakingFriction;
    outValue.MaxSimulationTimeStep = inHandle->MaxSimulationTimeStep;
    outValue.MaxSimulationIterations = inHandle->MaxSimulationIterations;
    outValue.MaxJumpApexAttemptsPerSimulation = inHandle->MaxJumpApexAttemptsPerSimulation;
    outValue.MaxAcceleration = inHandle->MaxAcceleration;

    // Walking Settings
    outValue.BrakingDecelerationWalking = inHandle->BrakingDecelerationWalking;
    outValue.MaxWalkSpeed = inHandle->MaxWalkSpeed;
    outValue.MaxWalkSpeedCrouched = inHandle->MaxWalkSpeedCrouched;
    outValue.MaxStepHeight = inHandle->MaxStepHeight;
    outValue.CanCrouch = inHandle->CanCrouch;
    outValue.CrouchedHalfHeight = inHandle->CrouchedHalfHeight;
    outValue.PerchRadiusThreshold = inHandle->PerchRadiusThreshold;
    outValue.WalkableFloorAngle = inHandle->WalkableFloorAngle;
    outValue.WalkableFloorY = inHandle->WalkableFloorY;
    outValue.IgnoreFloorRotation = inHandle->IgnoreFloorRotation;
    outValue.MaintainHorizontalGroundVelocity = inHandle->MaintainHorizontalGroundVelocity;
    outValue.CanWalkOffLedges = inHandle->CanWalkOffLedges;
    outValue.CanWalkOffLedgesWhenCrouching = inHandle->CanWalkOffLedgesWhenCrouching;
    outValue.LedgeCheckThreshold = inHandle->LedgeCheckThreshold;

    // Jumping & Falling Settings
    outValue.MaxFallingSpeed = inHandle->MaxFallingSpeed;
    outValue.BrakingDecelerationFalling = inHandle->BrakingDecelerationFalling;
    outValue.AirControl = inHandle->AirControl;
    outValue.AirControlBoostMultiplier = inHandle->AirControlBoostMultiplier;
    outValue.AirControlBoostVelocityThreshold = inHandle->AirControlBoostVelocityThreshold;
    outValue.FallingLateralFriction = inHandle->FallingLateralFriction;

    outValue.CanJump = inHandle->CanJump;
    outValue.JumpMaxHoldTime = inHandle->JumpMaxHoldTime;
    outValue.JumpMaxCount = inHandle->JumpMaxCount;
    outValue.JumpYVelocity = inHandle->JumpYVelocity;
    outValue.JumpOffJumpYFactor = inHandle->JumpOffJumpYFactor;
    outValue.ApplyGravityWhileHoldingJump = inHandle->ApplyGravityWhileHoldingJump;
    outValue.ImpartBaseVelocityX = inHandle->ImpartBaseVelocityX;
    outValue.ImpartBaseVelocityY = inHandle->ImpartBaseVelocityY;
    outValue.ImpartBaseVelocityZ = inHandle->ImpartBaseVelocityZ;
    outValue.ImpartBaseAngularVelocity = inHandle->ImpartBaseAngularVelocity;
}

void CharacterMovementSystemG::SetCharacterMovementComponent(const CharacterMovementCompWriter& inHandle, const cross::CharacterMovementComponentG& inValue)
{
    inHandle->DrawDebugShape = inValue.DrawDebugShape;

    inHandle->GravityScale = inValue.GravityScale;
    inHandle->BrakingFriction = inValue.BrakingFriction;
    inHandle->MaxSimulationTimeStep = inValue.MaxSimulationTimeStep;
    inHandle->MaxSimulationIterations = inValue.MaxSimulationIterations;
    inHandle->MaxJumpApexAttemptsPerSimulation = inValue.MaxJumpApexAttemptsPerSimulation;
    inHandle->MaxAcceleration = inValue.MaxAcceleration;

    inHandle->BrakingDecelerationWalking = inValue.BrakingDecelerationWalking;
    inHandle->MaxWalkSpeed = inValue.MaxWalkSpeed;
    inHandle->MaxWalkSpeedCrouched = inValue.MaxWalkSpeedCrouched;
    inHandle->MaxStepHeight = inValue.MaxStepHeight;
    inHandle->CanCrouch = inValue.CanCrouch;
    inHandle->CrouchedHalfHeight = inValue.CrouchedHalfHeight;
    inHandle->PerchRadiusThreshold = inValue.PerchRadiusThreshold;
    SetWalkableFloorAngle(inHandle, inValue.WalkableFloorAngle);

    inHandle->IgnoreFloorRotation = inValue.IgnoreFloorRotation;
    inHandle->MaintainHorizontalGroundVelocity = inValue.MaintainHorizontalGroundVelocity;
    inHandle->CanWalkOffLedges = inValue.CanWalkOffLedges;
    inHandle->CanWalkOffLedgesWhenCrouching = inValue.CanWalkOffLedgesWhenCrouching;
    inHandle->LedgeCheckThreshold = inValue.LedgeCheckThreshold;

    inHandle->MaxFallingSpeed = inValue.MaxFallingSpeed;
    inHandle->BrakingDecelerationFalling = inValue.BrakingDecelerationFalling;
    inHandle->AirControl = inValue.AirControl;
    inHandle->AirControlBoostMultiplier = inValue.AirControlBoostMultiplier;
    inHandle->AirControlBoostVelocityThreshold = inValue.AirControlBoostVelocityThreshold;
    inHandle->FallingLateralFriction = inValue.FallingLateralFriction;

    inHandle->CanJump = inValue.CanJump;
    inHandle->JumpMaxHoldTime = inValue.JumpMaxHoldTime;
    inHandle->JumpMaxCount = inValue.JumpMaxCount;
    inHandle->JumpYVelocity = inValue.JumpYVelocity;
    inHandle->JumpOffJumpYFactor = inValue.JumpOffJumpYFactor;
    inHandle->ApplyGravityWhileHoldingJump = inValue.ApplyGravityWhileHoldingJump;
    inHandle->ImpartBaseVelocityX = inValue.ImpartBaseVelocityX;
    inHandle->ImpartBaseVelocityY = inValue.ImpartBaseVelocityY;
    inHandle->ImpartBaseVelocityZ = inValue.ImpartBaseVelocityZ;
    inHandle->ImpartBaseAngularVelocity = inValue.ImpartBaseAngularVelocity;
}

const PhysicsGeometryBase* CharacterMovementSystemG::GetPhysicsGeometry(ecs::EntityID entity) const
{
    auto physicsComp = mGameWorld->GetComponent<PhysicsComponentG>(entity);
    if (!physicsComp.IsValid())
    {
        return nullptr;
    }
    // Collect shapes extra added
    const auto extraShape = mPhysicsSys->GetExtraShape(physicsComp.Read());
    if (extraShape != nullptr && !extraShape->mCapsuleGeometry.empty())
    {
        return &extraShape->mCapsuleGeometry.front();
    }

    return nullptr;
}

std::pair<float, float> CharacterMovementSystemG::GetScaledCapsuleSize(ecs::EntityID entity) const
{
    const auto capsule = static_cast<const PhysicsGeometryCapsule*>(GetPhysicsGeometry(entity));
    if (capsule)
    {
        auto worldTransComp = mGameWorld->GetComponent<WorldTransformComponentG>(entity);
        Float3 scale = mTransformSys->GetWorldScale(worldTransComp.Read());

        return {capsule->radius * scale.x, capsule->halfHeight * scale.y};
    }

    return {-1.f, -1.f};
}

std::pair<float, float> CharacterMovementSystemG::GetUnScaledCapsuleSize(ecs::EntityID entity) const
{
    const auto capsule = static_cast<const PhysicsGeometryCapsule*>(GetPhysicsGeometry(entity));
    if (capsule)
    {
        return {capsule->radius, capsule->halfHeight};
    }

    return {-1.f, -1.f};
}

TRS CharacterMovementSystemG::GetCapsuleWorldTransform(ecs::EntityID entity) const
{
    const auto capsule = static_cast<const PhysicsGeometryCapsule*>(GetPhysicsGeometry(entity));
    if (capsule)
    {
        return WorldCollisionUtility::GetPhysicsGeometryWorldTransform(mGameWorld, entity, capsule);
    }

    return TRS();
}

TRSVector3Type CharacterMovementSystemG::GetCapsuleWorldTranslation(ecs::EntityID entity) const
{
    return GetCapsuleWorldTransform(entity).mTranslation;
}

void CharacterMovementSystemG::SetCharacterCapsuleRadius(ecs::EntityID entity, float capsuleRadius)
{
    auto physicsComp = mGameWorld->GetComponent<PhysicsComponentG>(entity);
    if (!physicsComp.IsValid())
    {
        return;
    }

    // Collect shapes extra added
    auto extraShape = mPhysicsSys->GetExtraShape(physicsComp.Read());
    if (extraShape != nullptr && !extraShape->mCapsuleGeometry.empty())
    {
        auto& capsule = extraShape->mCapsuleGeometry.front();
        capsule.radius = capsuleRadius;
        // Re-Adjust Position
        capsule.position.y = capsule.halfHeight + capsuleRadius;

        mPhysicsSys->SetExtraCollision(physicsComp.Write(), *extraShape);
    }
}

void CharacterMovementSystemG::SetCharacterCapsuleHalfHeight(ecs::EntityID entity, float capsuleHalfHeight) 
{
    auto physicsComp = mGameWorld->GetComponent<PhysicsComponentG>(entity);
    if (!physicsComp.IsValid())
    {
        return;
    }

    // Collect shapes extra added
    auto extraShape = mPhysicsSys->GetExtraShape(physicsComp.Read());
    if (extraShape != nullptr && !extraShape->mCapsuleGeometry.empty())
    {
        auto& capsule = extraShape->mCapsuleGeometry.front();
        capsule.halfHeight = capsuleHalfHeight;
        // Re-Adjust Position
        capsule.position.y = capsuleHalfHeight + capsule.radius;

        mPhysicsSys->SetExtraCollision(physicsComp.Write(), *extraShape);
    }
}

float CharacterMovementSystemG::GetGravityScale(const CharacterMovementCompReader& inHandle) const 
{
    return inHandle->GravityScale;
}

void CharacterMovementSystemG::SetGravityScale(const CharacterMovementCompWriter& inHandle, float gravityScale) 
{
    inHandle->GravityScale = gravityScale;
}

float CharacterMovementSystemG::GetGravityY(const CharacterMovementCompReader& inHandle) const
{
    return -980.0f * inHandle->GravityScale;
}

float CharacterMovementSystemG::GetMaxSpeed(const CharacterMovementCompReader& inHandle) const
{
    switch (inHandle->MoveMode)
    {
    case MovementMode::MOVE_NavWalking:
    case MovementMode::MOVE_Walking:
        return IsCrouching(inHandle) ? inHandle->MaxWalkSpeedCrouched : inHandle->MaxWalkSpeed;
    case MovementMode::MOVE_Falling:
        return inHandle->MaxFallingSpeed;
    default:
        return 0.f;
    }
}

bool CharacterMovementSystemG::IsExceedingMaxSpeed(const CharacterMovementCompReader& inHandle, float maxSpeed) const
{
    // Allow 1% error tolerance, to account for numeric imprecision.
    static const float sOverVelocityPercent = 1.01f;

    float maxSpeedSquared = MathUtils::Square(maxSpeed);
    return inHandle->Velocity.LengthSquared() > maxSpeedSquared * sOverVelocityPercent;
}

void CharacterMovementSystemG::SetMaxWalkSpeed(const CharacterMovementCompWriter& inHandle, float maxWalkSpeed) 
{
    inHandle->MaxWalkSpeed = maxWalkSpeed;
}

void CharacterMovementSystemG::SetMaxWalkSpeedCrouched(const CharacterMovementCompWriter& inHandle, float maxCrouchSpeed)
{
    inHandle->MaxWalkSpeedCrouched = maxCrouchSpeed;
}

void CharacterMovementSystemG::SetMaxFallingSpeed(const CharacterMovementCompWriter& inHandle, float maxFallingSpeed)
{
    inHandle->MaxFallingSpeed = maxFallingSpeed;
}

float CharacterMovementSystemG::GetMaxAcceleration(const CharacterMovementCompReader& inHandle) const
{
    return inHandle->MaxAcceleration;
}

float CharacterMovementSystemG::GetMaxBrakingDeceleration(const CharacterMovementCompReader& inHandle) const 
{
    switch (inHandle->MoveMode)
    {
    case MovementMode::MOVE_NavWalking:
    case MovementMode::MOVE_Walking:
        return inHandle->BrakingDecelerationWalking;
    case MovementMode::MOVE_Falling:
        return inHandle->BrakingDecelerationFalling;
    default:
        return 0.f;
    }
}

float CharacterMovementSystemG::GetCrouchedHalfHeight(const CharacterMovementCompReader& inHandle) const
{
    return inHandle->CrouchedHalfHeight;
}

void CharacterMovementSystemG::SetCrouchedHalfHeight(const CharacterMovementCompWriter& inHandle, float halfHeight)
{
    inHandle->CrouchedHalfHeight = halfHeight;
}

float CharacterMovementSystemG::GetWalkableFloorAngle(const CharacterMovementCompReader& inHandle) const
{
    return inHandle->WalkableFloorAngle;
}

void CharacterMovementSystemG::SetWalkableFloorAngle(const CharacterMovementCompWriter& inHandle, float walkableFloorAngle)
{
    inHandle->WalkableFloorAngle = MathUtils::Clamp(walkableFloorAngle, 0.f, 90.f);
    inHandle->WalkableFloorY = std::cos(inHandle->WalkableFloorAngle / 180.0f * MathUtils::MathPi);
}

float CharacterMovementSystemG::GetWalkableFloorY(const CharacterMovementCompReader& inHandle) const
{
    return inHandle->WalkableFloorY;
}

bool CharacterMovementSystemG::IsWalkableFloor(const CharacterMovementCompReader& inHandle, const HitResult& hit) const
{
    if (!hit.IsValidBlockingHit())
    {
        // No hit or starting in penetration
        return false;
    }

    // Never walk up vertical surfaces.
    if (hit.ImpactNormal.y < sVerticalSlopeNormalY)
    {
        return false;
    }

    // Can't walk on this surface if it is too steep.
    if (hit.ImpactNormal.y < inHandle->WalkableFloorY)
    {
        return false;
    }

    return true;
}

float CharacterMovementSystemG::GetPerchRadiusThreshold(const CharacterMovementCompReader& inHandle) const 
{
    return MathUtils::Max(0.f, inHandle->PerchRadiusThreshold);
}

float CharacterMovementSystemG::GetValidPerchRadius(const CharacterMovementCompReader& inHandle) const
{
    // Check validation of character capsule
    auto [capsuleRadius, capsuleHalfHeight] = GetScaledCapsuleSize(inHandle.GetEntityID());
    if (capsuleRadius <= 0.f || capsuleHalfHeight <= 0.f)
    {
        return 0.f;
    }
    else
    {
        return MathUtils::Clamp(capsuleRadius - GetPerchRadiusThreshold(inHandle), 0.11f, capsuleRadius);
    }
}

bool CharacterMovementSystemG::ShouldCalculatePerchResult(const CharacterMovementCompReader& inHandle, const HitResult& hit, bool checkRadius) const
{
    if (!hit.IsValidBlockingHit())
    {
        return false;
    }

    // Don't try to perch if the edge radius is very small.
    if (GetPerchRadiusThreshold(inHandle) <= sSweepEdgeRejectThres)
    {
        return false;
    }

    if (checkRadius)
    {
        TRSScalarType distanceSq = (hit.ImpactPoint - hit.Location).LengthSquaredXZ();
        float standOnEdgeRadius = GetValidPerchRadius(inHandle);
        if (distanceSq <= MathUtils::Square(standOnEdgeRadius))
        {
            // Already within perch radius.
            return false;
        }
    }

    return true;
}

Float3 CharacterMovementSystemG::GetCurrentVelocityInWorldSpace(const CharacterMovementCompReader& inHandle) const
{
    return inHandle->Velocity;
}

Float3 CharacterMovementSystemG::GetCurrentVelocityInRootSpace(const CharacterMovementCompReader& inHandle) const 
{
    auto velocity = GetCurrentVelocityInWorldSpace(inHandle);
    if (velocity.Length() < MOVEMENT_SMALL_NUMBER)
    {
        return Float3::Zero();
    }

    auto worldTransComp = mGameWorld->GetComponent<WorldTransformComponentG>(inHandle.GetEntityID());
    auto CharacterRotation = mTransformSys->GetWorldRotation(worldTransComp.Read());
    CharacterRotation = CharacterRotation * Quaternion::CreateFromAxisAngle(Float3A(0, 1, 0), MathUtils::MathPi);

    return CharacterRotation.Inverse().Float3Rotate(velocity);
}

Float3 CharacterMovementSystemG::GetCurrentAcceleration(const CharacterMovementCompReader& inHandle) const
{
    return inHandle->Acceleration;
}

Float3 CharacterMovementSystemG::GetLastUpdateLocation(const CharacterMovementCompReader& inHandle) const
{
    return inHandle->LastUpdateLocation;
}

Quaternion CharacterMovementSystemG::GetLastUpdateRotation(const CharacterMovementCompReader& inHandle) const
{
    return inHandle->LastUpdateRotation;
}

Float3 CharacterMovementSystemG::GetLastUpdateVelocityInWorldSpace(const CharacterMovementCompReader& inHandle) const
{
    return inHandle->LastUpdateVelocity;
}

const FindFloorResult& CharacterMovementSystemG::GetCurrentFloor(const CharacterMovementCompReader& inHandle) const
{
    return inHandle->CurrentFloor;
}

void CharacterMovementSystemG::UpdateBasedOnFloorMovement(const CharacterMovementCompWriter& inHandle)
{
    auto floorEntity = inHandle->CurrentFloor.FloorHitResult.HitEntity;
    if (!inHandle->CurrentFloor.IsWalkableFloor() || floorEntity == ecs::EntityID::InvalidHandle())
    {
        return;
    }

    Quaternion deltaRot = Quaternion::Identity();
    Float3 deltaPos = Float3::Zero();

    auto characterEntity = inHandle.GetEntityID();
    auto characterWorldTransComp = mGameWorld->GetComponent<WorldTransformComponentG>(characterEntity);
    auto floorWorldTransComp = mGameWorld->GetComponent<WorldTransformComponentG>(floorEntity);

    Quaternion newFloorRot = mTransformSys->GetWorldRotation(floorWorldTransComp.Read());
    Float3 newFloorPos = mTransformSys->GetWorldTranslation(floorWorldTransComp.Read());

    // Find Change in rotation
    const bool bRotationChanged = !Quaternion::IsEqual(inHandle->LastFloorRotation, newFloorRot);
    if (bRotationChanged)
    {
        deltaRot = inHandle->LastFloorRotation.Inverse() * newFloorRot;
    }

    // Only if Floor moved
    if (bRotationChanged || !inHandle->LastFloorLocation.Equal(newFloorPos))
    {
        if (bRotationChanged && !inHandle->IgnoreFloorRotation)
        {
            Quaternion targetRot = mTransformSys->GetWorldRotation(characterWorldTransComp.Read()) * deltaRot;
            MoveImpl(characterEntity, Float3::Zero(), targetRot, false);
        }

        NodeTransform floorOldLocalToWorld{Float3::One(), inHandle->LastFloorRotation, inHandle->LastFloorLocation};
        NodeTransform floorNewLocalToWorld{Float3::One(), newFloorRot, newFloorPos};

        Float3A characterWorldPos = mTransformSys->GetWorldTranslation(characterWorldTransComp.Read());
        Float3 characterOldLocalPos = floorOldLocalToWorld.InverseTransformFloat3A(characterWorldPos);
        Float3 characterNewWorldPos = floorNewLocalToWorld.TransformFloat3A(characterOldLocalPos);

        deltaPos = characterNewWorldPos - characterWorldPos;
        Float3 floorMoveDelta = newFloorPos - inHandle->LastFloorLocation;
        if (floorMoveDelta.IsNearlyZero())
        {
            deltaPos.x = 0.f;
            deltaPos.z = 0.f;
        }

        HitResult moveOnFloorHit;
        MoveImpl(characterEntity, deltaPos, mTransformSys->GetWorldRotation(characterWorldTransComp.Read()), true, &moveOnFloorHit);
        if ((mTransformSys->GetWorldTranslation(characterWorldTransComp.Read()) - (characterWorldPos + deltaPos)).IsNearlyZero() == false)
        {
            // Unable to follow floor movement
            //Assert(false);
        }
    }
}

void CharacterMovementSystemG::SaveFloorMovement(const CharacterMovementCompWriter& inHandle)
{
    auto floorEntity = inHandle->CurrentFloor.FloorHitResult.HitEntity;
    if (!inHandle->CurrentFloor.IsWalkableFloor() || floorEntity == ecs::EntityID::InvalidHandle())
    {
        return;
    }

    auto floorWorldTransComp = mGameWorld->GetComponent<WorldTransformComponentG>(floorEntity);

    inHandle->LastFloorRotation = mTransformSys->GetWorldRotation(floorWorldTransComp.Read());
    inHandle->LastFloorLocation = mTransformSys->GetWorldTranslation(floorWorldTransComp.Read());
}

void CharacterMovementSystemG::SetMovementMode(const CharacterMovementCompWriter& inHandle, MovementMode inMoveMode)
{
    // Do nothing
    if (inHandle->MoveMode == inMoveMode) 
    {
        return;
    }

    MovementMode prevMode = inHandle->MoveMode;
    inHandle->MoveMode = inMoveMode;

    OnMovementModeChanged(inHandle, prevMode);
}

void CharacterMovementSystemG::OnMovementModeChanged(const CharacterMovementCompWriter& inHandle, MovementMode prevMode)
{
    auto characterEntity = inHandle.GetEntityID();
    auto moveComp = mGameWorld->GetComponent<CharacterMovementComponentG>(characterEntity);
    auto worldTransComp = mGameWorld->GetComponent<WorldTransformComponentG>(characterEntity);

    // React to changes in the movement mode.
    if (inHandle->MoveMode == MovementMode::MOVE_Walking)
    {
        // Walking uses only XZ velocity, and must be on a walkable floor, with a Base.
        inHandle->Velocity.y = 0.f;

        // make sure we update our new floor/base on initial entry of the walking physics
        FindFloor(moveComp.Read(), GetCapsuleWorldTranslation(characterEntity), inHandle->CurrentFloor);
        AdjustFloorHeight(inHandle);
    }
    else
    {
        inHandle->CurrentFloor.Reset();

        if (inHandle->MoveMode == MovementMode::MOVE_Falling)
        {
            inHandle->Velocity += GetImpartedMovementBaseVelocity(moveComp.Read());
        }
    }

    if (!inHandle->IsPressedJump || !IsFalling(moveComp.Read()))
    {
        ResetJumpState(inHandle);
    }

    // TODO(elricyang): BroadCast event
}

bool CharacterMovementSystemG::IsMovingOnGround(const CharacterMovementCompReader& inHandle) const
{
    return (inHandle->MoveMode == MovementMode::MOVE_Walking) || (inHandle->MoveMode == MovementMode::MOVE_NavWalking);
}

bool CharacterMovementSystemG::IsFalling(const CharacterMovementCompReader& inHandle) const
{
    return inHandle->MoveMode == MovementMode::MOVE_Falling;
}

bool CharacterMovementSystemG::IsCrouching(const CharacterMovementCompReader& inHandle) const 
{
    return inHandle->IsCrouched;
}

void CharacterMovementSystemG::OnJumped(const CharacterMovementCompWriter& inHandle) 
{
    CharacterMovementEvent event;
    event.mData.mType = CharacterMovementEventType::Jump;
    event.mData.mCharacterEntity = inHandle.GetEntityID();
    DispatchImmediateEvent<CharacterMovementEvent>(event);
}

void CharacterMovementSystemG::OnLanded(const CharacterMovementCompWriter& inHandle, const HitResult& hit) 
{
    CharacterMovementEvent event;
    event.mData.mType = CharacterMovementEventType::Landed;
    event.mData.mCharacterEntity = inHandle.GetEntityID();
    DispatchImmediateEvent<CharacterMovementEvent>(event);
}

void CharacterMovementSystemG::OnStartCrouch(const CharacterMovementCompWriter& inHandle, float halfHeightAdjust) 
{
    CharacterMovementEvent event;
    event.mData.mType = CharacterMovementEventType::StartCrouch;
    event.mData.mCharacterEntity = inHandle.GetEntityID();
    DispatchImmediateEvent<CharacterMovementEvent>(event);
}

void CharacterMovementSystemG::OnEndCrouch(const CharacterMovementCompWriter& inHandle, float halfHeightAdjust) 
{
    CharacterMovementEvent event;
    event.mData.mType = CharacterMovementEventType::EndCrouch;
    event.mData.mCharacterEntity = inHandle.GetEntityID();
    DispatchImmediateEvent<CharacterMovementEvent>(event);
}

void CharacterMovementSystemG::OnMovementBlocked(const CharacterMovementCompWriter& inHandle, const HitResult& impact) 
{
    CharacterMovementEvent event;
    event.mData.mType = CharacterMovementEventType::MovementBlocked;
    event.mData.mCharacterEntity = inHandle.GetEntityID();
    DispatchImmediateEvent<CharacterMovementEvent>(event);
}

void CharacterMovementSystemG::AddInputVector(const CharacterMovementCompWriter& inHandle, const Float3& inputVector) 
{
    inHandle->InputVector += inputVector;
    inHandle->InputVector.ClampToMaxSize(1.0f);
}

Float3 CharacterMovementSystemG::ConsumeInputVector(const CharacterMovementCompWriter& inHandle)
{
    inHandle->LastInputVector = inHandle->InputVector;
    inHandle->InputVector = Float3::Zero();
    return inHandle->LastInputVector;
}

Float3 CharacterMovementSystemG::GetLastInputVector(const CharacterMovementCompWriter& inHandle) const
{
    return inHandle->LastInputVector;
}

Float3 CharacterMovementSystemG::ConstrainInput(const CharacterMovementCompReader& inHandle, const Float3& inputAcceleration) const 
{
    // Walking or falling ignore up/down sliding
    if (inputAcceleration.y != 0.f && (IsMovingOnGround(inHandle) || IsFalling(inHandle)))
    {
        return Float3(inputAcceleration.x, 0.f, inputAcceleration.z);
    }

    return inputAcceleration;
}

void CharacterMovementSystemG::ApplyInputToAcceleration(const CharacterMovementCompWriter& inHandle, const Float3& inputAcceleration)
{
    Float3 ClamppedAcce = inputAcceleration.ClampToMaxSize(1.0f);
     inHandle->Acceleration =  inHandle->MaxAcceleration * ClamppedAcce;
}

bool CharacterMovementSystemG::HasAnimRootMotion(const CharacterMovementCompReader& inHandle) const
{
    return inHandle->RootMotion.HasRootMotion();
}

void CharacterMovementSystemG::ProcessRootMotionFromAnimation(const CharacterMovementCompWriter& inHandle)
{
    auto animatorSys = mGameWorld->GetGameSystem<AnimatorSystemG>();
    auto animComp = mGameWorld->GetComponent<AnimatorComponentG>(inHandle.GetEntityID());

    if (animComp.IsValid())
    {
        auto& animator = animatorSys->GetAnimator(animComp.Read());

        if (animator.IsPlayingRootMotion())
        {
            inHandle->RootMotion = animator.ConsumeRootMotion();
        }
    }
}

void CharacterMovementSystemG::ConvertLocalRootMotionToWorld(const CharacterMovementCompWriter& inHandle)
{
    auto moveComp = mGameWorld->GetComponent<CharacterMovementComponentG>(inHandle.GetEntityID());

    if (HasAnimRootMotion(moveComp.Read()))
    {
        auto [worldTransComp, localTransComp] = mGameWorld->GetComponent<WorldTransformComponentG, LocalTransformComponentG>(inHandle.GetEntityID());

        NodeTransform oldLocalTransform = {mTransformSys->GetLocalScale(localTransComp.Read()), mTransformSys->GetLocalRotation(localTransComp.Read()), mTransformSys->GetLocalTranslation(localTransComp.Read())};
        NodeTransform oldWorldTransform = {mTransformSys->GetWorldMatrix(worldTransComp.Read())};

        // Apply local root motion to local transform
        NodeTransform newLocalTransform = inHandle->RootMotion.GetRootMotionTransform() * oldLocalTransform;

        // Calculate new world transform when local transform changes
        NodeTransform newWorldTransform{NodeTransform::Identity()};

        auto parentEntity = mTransformSys->GetEntityParent(localTransComp.Read());
        // Have parent node
        if (parentEntity != ecs::EntityID::InvalidHandle())
        {
            auto parentWorldTransComp = mGameWorld->GetComponent<WorldTransformComponentG>(parentEntity);
            auto parentWorldTransform = NodeTransform{mTransformSys->GetWorldMatrix(parentWorldTransComp.Read())};

            newWorldTransform = newLocalTransform * parentWorldTransform;
        }
        else
        {
            newWorldTransform = newLocalTransform;
        }

        Float3 deltaTranslation = newWorldTransform.GetTranslation() - oldWorldTransform.GetTranslation();
        Quaternion deltaRotation = oldWorldTransform.GetRotation().Inverse() * newWorldTransform.GetRotation();

        NodeTransform worldRootMotion{Float3::One(), deltaRotation, deltaTranslation};
        // Set world root motion
        inHandle->RootMotion.SetRootMotion(worldRootMotion);
    }
}

void CharacterMovementSystemG::CalculateRootMotionVelocity(const CharacterMovementCompWriter& inHandle, float deltaSecs)
{
    if (deltaSecs > 0.f)
    {
        inHandle->RootMotionVelocity = inHandle->RootMotion.GetRootMotionTransform().GetTranslation() / deltaSecs;
    }
    else
    {
        inHandle->RootMotionVelocity = inHandle->Velocity;
    }
}

Float3 CharacterMovementSystemG::ConstrainRootMotionVelocity(const CharacterMovementCompReader& inHandle) const
{
    Float3 result {inHandle->RootMotionVelocity};

    // Do not override Velocity.Y if in falling physics, we want to keep the effect of gravity.
    if (IsFalling(inHandle))
    {
        result.y = inHandle->Velocity.y;
    }

    return result;
}

void CharacterMovementSystemG::ApplyRootMotionToVelocity(const CharacterMovementCompWriter& inHandle, float deltaSecs)
{
    auto moveComp = mGameWorld->GetComponent<CharacterMovementComponentG>(inHandle.GetEntityID());
    auto moveCompReader = moveComp.Read();
    
    if (HasAnimRootMotion(moveCompReader) && deltaSecs > 0.f)
    {
        inHandle->Velocity = ConstrainRootMotionVelocity(moveCompReader);
    }
}

void CharacterMovementSystemG::ApplyRootMotionRotation(const CharacterMovementCompWriter& inHandle)
{
    auto characterEntity = inHandle.GetEntityID();
    auto worldTransComp = mGameWorld->GetComponent<WorldTransformComponentG>(characterEntity);

    const auto oldRotation = mTransformSys->GetWorldRotation(worldTransComp.Read());
    const auto RootMotionRotation = inHandle->RootMotion.GetRootMotionTransform().GetRotation();
    if (!RootMotionRotation.IsIdentity())
    {
        Quaternion newRotation = oldRotation * RootMotionRotation;
        newRotation.Normalize();
        MoveImpl(characterEntity, Float3::Zero(), newRotation, true);
    }
}

float CharacterMovementSystemG::GetSimulationTimeStep(const CharacterMovementCompReader& inHandle, float remainingTime, UInt32 iterations) const
{
    if (remainingTime > inHandle->MaxSimulationTimeStep)
    {
        if (iterations < inHandle->MaxSimulationIterations)
        {
            // Subdivide moves to be no longer than MaxSimulationTimeStep seconds
            remainingTime = MathUtils::Min(inHandle->MaxSimulationTimeStep, remainingTime * 0.5f);
        }
    }

    // No less than sMinTickTime (to avoid potential divide-by-zero during simulation).
    return MathUtils::Max(sMinTickTime, remainingTime);
}

void CharacterMovementSystemG::ApplyBrakingToVelocity(const CharacterMovementCompWriter& inHandle, float deltaSecs, float friction, float brakingDeceleration) 
{
    auto moveComp = mGameWorld->GetComponent<CharacterMovementComponentG>(inHandle.GetEntityID());

    if (inHandle->Velocity.IsNearlyZero() || HasAnimRootMotion(moveComp.Read()) || deltaSecs < sMinTickTime)
    {
        return;
    }

    friction = MathUtils::Max(0.f, friction);
    brakingDeceleration = MathUtils::Max(0.f, brakingDeceleration);
    bool isZeroFriction = (friction == 0.f);
    bool isZeroBrakingDece = (brakingDeceleration == 0.f);

    if (isZeroFriction && isZeroBrakingDece)
    {
        return;
    }

    Float3 oldVelocity = inHandle->Velocity;
    float remainingTime = deltaSecs;
    
    const Float3 relativeAcce = isZeroBrakingDece ? Float3::Zero() : (-brakingDeceleration * inHandle->Velocity.SafeNormal());
    while (remainingTime >= sMinTickTime)
    {
        // Zero friction uses constant deceleration, so no need for iteration.
        const float tickTime = (remainingTime > inHandle->MaxSimulationTimeStep && !isZeroFriction) ? MathUtils::Min(inHandle->MaxSimulationTimeStep, remainingTime * 0.5f) : remainingTime;
        remainingTime -= tickTime;

        // Apply friction and braking
        inHandle->Velocity = inHandle->Velocity + ((-friction) * inHandle->Velocity + relativeAcce) * tickTime;

        // Do not reverse direction
        if ((inHandle->Velocity.Dot(oldVelocity)) <= 0.f)
        {
            inHandle->Velocity = Float3::Zero();
            return;
        }
    }

    // Clamp to zero if nearly zero, or if below min threshold and braking.
    const float velocityLenSq = inHandle->Velocity.LengthSquared();
    if (velocityLenSq <= MOVEMENT_SMALL_NUMBER || (!isZeroBrakingDece && velocityLenSq <= 100.0))
    {
        inHandle->Velocity = Float3::Zero();
    }
}

void CharacterMovementSystemG::CalculateVelocity(const CharacterMovementCompWriter& inHandle, float deltaSecs, float friction, float brakingDeceleration) 
{
    auto moveComp = mGameWorld->GetComponent<CharacterMovementComponentG>(inHandle.GetEntityID());

    // Do not update velocity when using root motion
    if (HasAnimRootMotion(moveComp.Read()) || deltaSecs < sMinTickTime)
    {
        return;
    }

    friction = MathUtils::Max(0.f, friction);
    const float maxAcceleration = GetMaxAcceleration(moveComp.Read());
    float maxSpeed = GetMaxSpeed(moveComp.Read());
    float maxSpeedSq = MathUtils::Square(maxSpeed);

    bool isZeroAcceleration = inHandle->Acceleration.IsNearlyZero();
    bool isVelocityExceedMax = IsExceedingMaxSpeed(moveComp.Read(), maxSpeed);

    // Only apply braking if acceleration is zero, or we are exceeding max speed and  need to slow down it
    if (isZeroAcceleration || isVelocityExceedMax)
    {
        Float3 oldVelocity = inHandle->Velocity;
        ApplyBrakingToVelocity(inHandle, deltaSecs, friction, brakingDeceleration);

        // Don't allow braking to lower us below max speed if we started above it.
        if (isVelocityExceedMax && inHandle->Velocity.LengthSquared() < maxSpeedSq && oldVelocity.Dot(inHandle->Acceleration) > 0.f)
        {
            inHandle->Velocity = oldVelocity.SafeNormal() * maxSpeed;
        }
    }
    else if (!isZeroAcceleration)
    {
        inHandle->Velocity += inHandle->Acceleration * deltaSecs;
        // Not more than max speed
        inHandle->Velocity.ClampToMaxSize(maxSpeed);
    }
}

void CharacterMovementSystemG::MaintainHorizontalGroundVelocity(const CharacterMovementCompWriter& inHandle) 
{
    if (inHandle->Velocity.y != 0.f)
    {
        if (inHandle->MaintainHorizontalGroundVelocity)
        {
            // Ramp movement already maintained the velocity, so we just want to remove the vertical velocity value.
            inHandle->Velocity.y = 0.f;
        }
        else
        {
            // Rescale velocity to be horizontal but maintain magnitude of last update.
            inHandle->Velocity = inHandle->Velocity.SafeNormalXZ() * inHandle->Velocity.Length();
        }
    }
}

Float3 CharacterMovementSystemG::CalculateGroundMoveDelta(const CharacterMovementCompReader& inHandle, const Float3& inDelta, const HitResult& rampHit, bool hitFromRayCast) const
{
    const Float3 floorNormal = rampHit.ImpactNormal;
    const Float3 contactNormal = rampHit.Normal;

    if (floorNormal.y < (1.f - MOVEMENT_SMALL_NUMBER) && 
        floorNormal.y > MOVEMENT_SMALL_NUMBER && 
        contactNormal.y > MOVEMENT_SMALL_NUMBER && 
        !hitFromRayCast && IsWalkableFloor(inHandle, rampHit))
    {
        // Compute a vector that moves parallel to the surface, by projecting the horizontal movement direction onto the ramp.
        const float floorDotDelta = floorNormal.Dot(inDelta);
        const Float3 rampMovement{inDelta.x, -floorDotDelta / floorNormal.y, inDelta.z};

        if (inHandle->MaintainHorizontalGroundVelocity)
        {
            return rampMovement;
        }
        else
        {
            return rampMovement.SafeNormal() * rampMovement.Length();
        }
    }

    return inDelta;
}

void CharacterMovementSystemG::PerformPhysicsMovement(const CharacterMovementCompWriter& inHandle, float deltaSecs, UInt32 iterations) 
{
    if (deltaSecs < sMinTickTime || iterations >= inHandle->MaxSimulationIterations)
    {
        return;
    }

    switch (inHandle->MoveMode)
    {
    case MovementMode::MOVE_Walking:
        PerformPhysicsWalking(inHandle, deltaSecs, iterations);
        break;
    case MovementMode::MOVE_Falling:
        PerformPhysicsFalling(inHandle, deltaSecs, iterations);
        break;
    default:
        break;
    }
}

void CharacterMovementSystemG::PerformPhysicsWalking(const CharacterMovementCompWriter& inHandle, float deltaSecs, UInt32 iterations)
{
    if (deltaSecs < sMinTickTime)
    {
        return;
    }

    auto characterEntity = inHandle.GetEntityID();
    auto worldTransComp = mGameWorld->GetComponent<WorldTransformComponentG>(characterEntity);
    auto moveComp = mGameWorld->GetComponent<CharacterMovementComponentG>(characterEntity);
    auto moveCompReader = moveComp.Read();

    bool checkedFall = false;
    bool triedLedgeMove = false;
    float remainingTime = deltaSecs;
    // Perform the movement
    while (remainingTime >= sMinTickTime && iterations < inHandle->MaxSimulationIterations)
    {
        ++iterations;
        const float tickTime = GetSimulationTimeStep(moveCompReader, remainingTime, iterations);
        remainingTime -= tickTime;

        // Save current states
        const TRSVector3Type characterOldPos = mTransformSys->GetWorldTranslationT(worldTransComp.Read());
        const TRSVector3Type capsuleOldPos = GetCapsuleWorldTranslation(characterEntity);
        const FindFloorResult oldFloor = inHandle->CurrentFloor;

        // Ensure velocity is horizontal.
        MaintainHorizontalGroundVelocity(inHandle);
        inHandle->Acceleration.y = 0.f;

        // Apply acceleration to velocity if no root motion
        if (!HasAnimRootMotion(moveCompReader))
        {
            CalculateVelocity(inHandle, tickTime, inHandle->BrakingFriction, GetMaxBrakingDeceleration(moveCompReader));
        }

        ApplyRootMotionToVelocity(inHandle, tickTime);

        if (IsFalling(moveCompReader)) 
        {
            // Root motion could have put us into Falling.
            // No movement has taken place this movement tick so we pass on full time/past iteration count
            PerformPhysicsMovement(inHandle, remainingTime + tickTime, iterations - 1);
            return;
        }

        // Move delta
        const Float3 moveDelta = inHandle->Velocity * tickTime;
        const bool isZeroDelta = moveDelta.IsNearlyZero();
        StepDownResult curStepDownResult;

        if (isZeroDelta)
        {
            remainingTime = 0.f;
        }
        else
        {
            // Attempt to move forward along current floor
            MoveAlongFloor(inHandle, tickTime, &curStepDownResult);

            if (IsFalling(moveCompReader))
            {
                // Player decided to jump up
                const float desiredDist = moveDelta.Length();
                if (desiredDist > MOVEMENT_SMALL_NUMBER)
                {
                    const TRSScalarType actualDist = (mTransformSys->GetWorldTranslationT(worldTransComp.Read()) - characterOldPos).LengthXZ();
                    remainingTime += tickTime * (1.0f - MathUtils::Min(1.0f, float(actualDist / desiredDist)));
                }

                PerformPhysicsMovement(inHandle, remainingTime, iterations);
                return;
            }
        }

        // Update floor, step up may have done it for us
        if (curStepDownResult.FloorFound)
        {
            inHandle->CurrentFloor = curStepDownResult.Floor;
        }
        else
        {
            FindFloor(moveCompReader, GetCapsuleWorldTranslation(characterEntity), inHandle->CurrentFloor, nullptr);
        }

        // Check for ledges when character can not walk off ledges and CurrentFloor is not walkable
        const bool needCheckLedges = !CanWalkOffLedges(moveCompReader);
        if (needCheckLedges && !inHandle->CurrentFloor.IsWalkableFloor()) {}
        else
        {
            // Validate the floor check
            if (inHandle->CurrentFloor.IsWalkableFloor())
            {
                AdjustFloorHeight(inHandle);
            }
            else if (inHandle->CurrentFloor.FloorHitResult.bStartPenetrating && remainingTime <= 0.f)
            {
                // The floor check failed because it started in penetration
                // We do not want to try to move downward because the downward sweep failed, rather we'd like to try to pop out of the floor.
                HitResult hit(inHandle->CurrentFloor.FloorHitResult);
                hit.EndPos = hit.StartPos + TRSVector3Type(0.f, sMaxFloorDist, 0.f);
                if (hit.HitEntity != characterEntity)
                {
                    const Float3 pullOut = GetPenetrationPullOut(characterEntity, hit);
                    ResolvePenetration(characterEntity, hit, pullOut, mTransformSys->GetWorldRotation(worldTransComp.Read()));
                }
            }

            // See if we need to start falling.
            if (!inHandle->CurrentFloor.IsWalkableFloor() && !inHandle->CurrentFloor.FloorHitResult.bStartPenetrating)
            {
                const bool mustJump = isZeroDelta;
                if ((mustJump || !checkedFall) && CheckFall(moveCompReader, oldFloor, moveDelta, capsuleOldPos, remainingTime, tickTime, iterations, mustJump))
                {
                    return;
                }
                checkedFall = true;
            }
        }

        // Allow overlap events and such to change physics state and velocity
        if (IsMovingOnGround(moveCompReader))
        {
            // Make velocity reflect actual move
            if (!HasAnimRootMotion(moveCompReader) && tickTime >= sMinTickTime)
            {
                inHandle->Velocity = Float3(mTransformSys->GetWorldTranslationT(worldTransComp.Read()) - characterOldPos) / tickTime;
                MaintainHorizontalGroundVelocity(inHandle);
            }
        }

        // If we didn't move at all this iteration then abort (since future iterations will also be stuck).
        if (mTransformSys->GetWorldTranslationT(worldTransComp.Read()) == characterOldPos)
        {
            remainingTime = 0.f;
            break;
        }
    }

    if (IsMovingOnGround(moveCompReader))
    {
        MaintainHorizontalGroundVelocity(inHandle);
    }
}

void CharacterMovementSystemG::PerformPhysicsFalling(const CharacterMovementCompWriter& inHandle, float deltaSecs, UInt32 iterations) 
{
    if (deltaSecs < sMinTickTime)
    {
        return;
    }

    auto characterEntity = inHandle.GetEntityID();
    auto worldTransComp = mGameWorld->GetComponent<WorldTransformComponentG>(characterEntity);
    auto moveComp = mGameWorld->GetComponent<CharacterMovementComponentG>(characterEntity);
    auto moveCompReader = moveComp.Read();

    Float3 fallAcceleration = GetFallingLateralAcceleration(moveCompReader, deltaSecs);
    fallAcceleration.y = 0.f;
    const bool hasLimitedAirControl = fallAcceleration.LengthSquaredXZ() > 0.f;

    float remainingTime = deltaSecs;
    while (remainingTime >= sMinTickTime && iterations < inHandle->MaxSimulationIterations)
    {
        ++iterations;
        float tickTime = GetSimulationTimeStep(moveCompReader, remainingTime, iterations);
        remainingTime -= tickTime;

        const TRSVector3Type characterOldPos = mTransformSys->GetWorldTranslationT(worldTransComp.Read());
        const Quaternion characterRot = mTransformSys->GetWorldRotation(worldTransComp.Read());

        const Float3 oldVelocity = inHandle->Velocity;

        // Apply Input
        const float maxDeceleration = GetMaxBrakingDeceleration(moveCompReader);
        if (!HasAnimRootMotion(moveCompReader))
        {
            // Calculate velocity
            inHandle->Velocity.y = 0.f;
            CalculateVelocity(inHandle, tickTime, inHandle->FallingLateralFriction, maxDeceleration);
            inHandle->Velocity.y = oldVelocity.y;
        }

        // Compute current gravity
        const Float3 gravity{0.f, GetGravityY(moveCompReader), 0.f};
        float gravityTime = tickTime;
        
        // If jump is providing force, gravity may be affected.
        bool endingJumpForce = false;
        if (inHandle->JumpForceTimeRemaining > 0.f)
        {
            // Consume some of the force time. Only the remaining time (if any) is affected by gravity when bApplyGravityWhileJumping=false.
            const float jumpForceTime = MathUtils::Min(inHandle->JumpForceTimeRemaining, tickTime);
            gravityTime = inHandle->ApplyGravityWhileHoldingJump ? tickTime : MathUtils::Max(0.f, tickTime - jumpForceTime);
            
            // Update Character state
            inHandle->JumpForceTimeRemaining -= jumpForceTime;
            if (inHandle->JumpForceTimeRemaining <= 0.f)
            {
                ResetJumpState(inHandle);
                endingJumpForce = true;
            }
        }

        // Apply gravity
        inHandle->Velocity = CalculateNewFallVelocity(moveCompReader, inHandle->Velocity, gravity, gravityTime);

        ApplyRootMotionToVelocity(inHandle, tickTime);

        // See if we need to sub-step to exactly reach the apex. This is important for avoiding "cutting off the top" of the trajectory as framerate varies.

        // Compute change in position (using midpoint integration method).
        Float3 adjusted = 0.5f * (oldVelocity + inHandle->Velocity) * tickTime;

        // Special handling if ending the jump force where we didn't apply gravity during the jump.
        if (endingJumpForce && !inHandle->ApplyGravityWhileHoldingJump)
        {
            // We had a portion of the time at constant speed then a portion with acceleration due to gravity.
            // Account for that here with a more correct change in position.
            const float nonGravityTime = MathUtils::Max(0.f, tickTime - gravityTime);
            adjusted = (oldVelocity * nonGravityTime) + (0.5f * (oldVelocity + inHandle->Velocity) * gravityTime);
        }

        // Move
        HitResult hit;
        SafeMovement(characterEntity, adjusted, characterRot, true, &hit, inHandle->DrawDebugShape);

        float lastMoveTimeSlice = tickTime;
        float subTickTimeRemaining = tickTime * (1.f - hit.PositionRatio);

        if (hit.bBlockingHit)
        {
            // Find valid landing spot
            if (IsValidLandingSpot(moveCompReader, GetCapsuleWorldTranslation(characterEntity), hit))
            {
                remainingTime += subTickTimeRemaining;
                ProcessLanded(moveCompReader, hit, remainingTime, iterations);
                return;
            }
            else
            {
                // Compute impact deflection based on final velocity, not integration step.
                // This allows us to compute a new velocity from the deflected vector, and ensures the full gravity effect is included in the slide result.
                adjusted = inHandle->Velocity * tickTime;

                // See if we can convert a normally invalid landing spot (based on the hit result) to a usable one.
                if (!hit.bStartPenetrating && ShouldCheckForValidLandingSpot(moveCompReader, tickTime, adjusted, hit))
                {
                    //capsulePos Double3
                    const TRSVector3Type capsulePos = GetCapsuleWorldTranslation(characterEntity);
                    FindFloorResult floor;
                    FindFloor(moveCompReader, capsulePos, floor);
                    if (floor.IsWalkableFloor() && IsValidLandingSpot(moveCompReader, capsulePos, floor.FloorHitResult))
                    {
                        remainingTime += subTickTimeRemaining;
                        ProcessLanded(moveCompReader, floor.FloorHitResult, remainingTime, iterations);
                        return;
                    }
                }

                HandleImpact(characterEntity, hit, lastMoveTimeSlice, adjusted);

                // If we've changed physics mode, abort.
                if (!IsFalling(moveCompReader))
                {
                    return;
                }

                // Limit air control based on what we hit.
                // We moved to the impact point using air control, but may want to deflect from there based on a limited air control acceleration.
                Float3 velocityNoAirControl = oldVelocity;
                Float3 airControlAcceleration = inHandle->Acceleration;
                if (hasLimitedAirControl)
                {
                    // Compute VelocityNoAirControl
                    inHandle->Velocity.y = 0.f;
                    CalculateVelocity(inHandle, tickTime, inHandle->FallingLateralFriction, maxDeceleration);
                    velocityNoAirControl = Float3(inHandle->Velocity.x, oldVelocity.y, inHandle->Velocity.z);
                    velocityNoAirControl = CalculateNewFallVelocity(moveCompReader, velocityNoAirControl, gravity, gravityTime);

                    // We already checked above.
                    const bool checkLandingSpot = false;
                    airControlAcceleration = (inHandle->Velocity - velocityNoAirControl) / tickTime;
                    const Float3 airControlDeltaV = LimitAirControl(moveCompReader, lastMoveTimeSlice, airControlAcceleration, hit, checkLandingSpot) * lastMoveTimeSlice;
                    adjusted = (velocityNoAirControl + airControlDeltaV) * lastMoveTimeSlice;
                }

                const Float3 oldHitNormal = hit.Normal;
                const Float3 oldHitImpactNormal = hit.ImpactNormal;
                Float3 delta = ComputeSlideVector(characterEntity, adjusted, 1.f - hit.PositionRatio, oldHitNormal, hit);

                // Compute velocity after deflection (only gravity component for RootMotion)
                if (subTickTimeRemaining > MOVEMENT_SMALL_NUMBER)
                {
                    Float3 newVelocity = delta / subTickTimeRemaining;
                    inHandle->Velocity = HasAnimRootMotion(moveCompReader) ? Float3(inHandle->Velocity.x, newVelocity.y, inHandle->Velocity.z) : newVelocity;
                }

                if (subTickTimeRemaining > MOVEMENT_SMALL_NUMBER && (delta.Dot(adjusted) > 0.f))
                {
                    // Move in deflected direction.
                    SafeMovement(characterEntity, delta, characterRot, true, &hit, inHandle->DrawDebugShape);

                    if (hit.bBlockingHit)
                    {
                        // hit second wall
                        lastMoveTimeSlice = subTickTimeRemaining;
                        subTickTimeRemaining *= (1.f - hit.PositionRatio);

                        if (IsValidLandingSpot(moveCompReader, GetCapsuleWorldTranslation(characterEntity), hit))
                        {
                            remainingTime += subTickTimeRemaining;
                            ProcessLanded(moveCompReader, hit, remainingTime, iterations);
                            return;
                        }

                        HandleImpact(characterEntity, hit, lastMoveTimeSlice, delta);

                        // If we've changed physics mode, abort.
                        if (!IsFalling(moveCompReader))
                        {
                            return;
                        }

                        // Act as if there was no air control on the last move when computing new deflection.
                        if (hasLimitedAirControl && hit.Normal.y > sVerticalSlopeNormalY)
                        {
                            const Float3 lastMoveNoAirControl = velocityNoAirControl * lastMoveTimeSlice;
                            delta = ComputeSlideVector(characterEntity, lastMoveNoAirControl, 1.f, oldHitNormal, hit);
                        }

                        TwoSurfaceAdjust(characterEntity, delta, hit, oldHitNormal);

                        // Limit air control, but allow a slide along the second wall.
                        if (hasLimitedAirControl)
                        {
                            const bool checkLandingSpot = false;   // we already checked above.
                            const Float3 airControlDeltaV = LimitAirControl(moveCompReader, subTickTimeRemaining, airControlAcceleration, hit, checkLandingSpot) * subTickTimeRemaining;

                            // Only allow if not back in to first wall
                            if (airControlDeltaV.Dot(oldHitNormal) > 0.f)
                            {
                                delta += airControlDeltaV * subTickTimeRemaining;
                            }
                        }

                        // Compute velocity after deflection (only gravity component for RootMotion)
                        if (subTickTimeRemaining > MOVEMENT_SMALL_NUMBER)
                        {
                            const Float3 newVelocity = delta / subTickTimeRemaining;
                            inHandle->Velocity = HasAnimRootMotion(moveCompReader) ? Float3(inHandle->Velocity.x, newVelocity.y, inHandle->Velocity.z) : newVelocity;
                        }

                        // bDitch=true means that pawn is straddling two slopes, neither of which he can stand on
                        bool bDitch = ((oldHitImpactNormal.y > 0.f) && (hit.ImpactNormal.y > 0.f) && (MathUtils::Abs(delta.y) <= MOVEMENT_SMALL_NUMBER) && ((hit.ImpactNormal.Dot(oldHitImpactNormal)) < 0.f));
                        SafeMovement(characterEntity, delta, characterRot, true, &hit, inHandle->DrawDebugShape);
                        if (hit.PositionRatio == 0.f)
                        {
                            // if we are stuck then try to side step
                            Float3 sideDelta = (oldHitNormal + hit.ImpactNormal).SafeNormalXZ();
                            if (sideDelta.IsNearlyZero())
                            {
                                sideDelta = Float3(oldHitNormal.z, 0.f, -oldHitNormal.x).SafeNormal();
                            }

                            SafeMovement(characterEntity, sideDelta, characterRot, true, &hit, inHandle->DrawDebugShape);
                        }

                        if (bDitch || IsValidLandingSpot(moveCompReader, GetCapsuleWorldTranslation(characterEntity), hit) || hit.PositionRatio == 0.f)
                        {
                            remainingTime = 0.f;
                            ProcessLanded(moveCompReader, hit, remainingTime, iterations);
                            return;
                        }
                        else if (GetPerchRadiusThreshold(moveCompReader) > 0.f && hit.PositionRatio == 1.f && oldHitImpactNormal.y >= inHandle->WalkableFloorY)
                        {
                            // We might be in a virtual 'ditch' within our perch radius. This is rare.
                            const TRSScalarType yMoveDist = MathUtils::Abs(mTransformSys->GetWorldTranslationT(worldTransComp.Read()).y - characterOldPos.y);
                            const TRSScalarType moveDistSq = (mTransformSys->GetWorldTranslationT(worldTransComp.Read()) - characterOldPos).LengthSquaredXZ();

                            if (yMoveDist <= 0.2f * tickTime && moveDistSq <= 4.f * tickTime)
                            {
                                inHandle->Velocity.x += 0.25f * GetMaxSpeed(moveCompReader);
                                inHandle->Velocity.z += 0.25f * GetMaxSpeed(moveCompReader);
                                inHandle->Velocity.y = MathUtils::Max<float>(inHandle->JumpYVelocity * 0.25f, 1.f);
                                delta = inHandle->Velocity * tickTime;
                                SafeMovement(characterEntity, delta, characterRot, true, &hit, inHandle->DrawDebugShape);
                            }
                        }
                    }
                }
            }
        }

        if (inHandle->Velocity.LengthSquaredXZ() <= MOVEMENT_SMALL_NUMBER * 10.f)
        {
            inHandle->Velocity.x = 0.f;
            inHandle->Velocity.z = 0.f;
        }
    }
}

bool CharacterMovementSystemG::CalculatePerchResult(const CharacterMovementCompReader& inHandle, float testRadius, const HitResult& hit, float maxFloorDist, FindFloorResult& outPerchFloorResult) const
{
    if (maxFloorDist <= 0.f)
    {
        return false;
    }

    // Check validation of character capsule
    auto [capsuleRadius, capsuleHalfHeight] = GetScaledCapsuleSize(inHandle.GetEntityID());
    if (capsuleRadius <= 0.f || capsuleHalfHeight <= 0.f)
    {
        return false;
    }
    // Sweep further than actual requested distance, because a reduced capsule radius means we could miss some hits that the normal radius would contact.
    const TRSVector3Type capsuleLocation = hit.StartPos;

    float hitAboveFloor = MathUtils::Max(0.0f, float(hit.ImpactPoint.y - (capsuleLocation.y - (capsuleHalfHeight + capsuleRadius))));
    float perchSweepDist = MathUtils::Max(0.f, maxFloorDist);
    float perchLineTraceDist = MathUtils::Max(0.f, maxFloorDist - hitAboveFloor);

    float actualSweepDist = perchSweepDist + capsuleRadius;
    CalculateFloorDistance(inHandle, capsuleLocation, perchLineTraceDist, actualSweepDist, testRadius, outPerchFloorResult, nullptr);

    if (!outPerchFloorResult.IsWalkableFloor())
    {
        return false;
    }
    else if (hitAboveFloor + outPerchFloorResult.FloorDist > maxFloorDist)
    {
        // Hit something past max distance
        outPerchFloorResult.bWalkableFloor = false;
        return false;
    }

    return true;
}

void CharacterMovementSystemG::MoveAlongFloor(const CharacterMovementCompWriter& inHandle, float deltaSecs, StepDownResult* outStepResult)
{
    if (!inHandle->CurrentFloor.IsWalkableFloor())
    {
        return;
    }

    auto characterEntity = inHandle.GetEntityID();
    auto worldTransComp = mGameWorld->GetComponent<WorldTransformComponentG>(characterEntity);
    auto moveComp = mGameWorld->GetComponent<CharacterMovementComponentG>(characterEntity);
    auto moveCompReader = moveComp.Read();

    // Move along the current floor
    Float3 moveDelta = inHandle->Velocity * deltaSecs;
    moveDelta.y = 0.f;

    HitResult hit;
    Float3 rampMoveDelta = CalculateGroundMoveDelta(moveCompReader, moveDelta, inHandle->CurrentFloor.FloorHitResult, inHandle->CurrentFloor.bLineTrace);
    SafeMovement(characterEntity, rampMoveDelta, mTransformSys->GetWorldRotation(worldTransComp.Read()), true, &hit, inHandle->DrawDebugShape);

    float lastMoveRatioSlice = deltaSecs;
    if (hit.bStartPenetrating)
    {
        // Allow this hit to be used as an impact we can deflect off, otherwise we do nothing the rest of the update and appear to hitch.
        HandleImpact(characterEntity, hit);
        SlideAlongSurface(characterEntity, moveDelta, 1.f, hit.Normal, hit, true);
    }
    else if (hit.IsValidBlockingHit())
    {
        // We impact something
        float positionRatioApplied = hit.PositionRatio;
        if ((hit.PositionRatio > 0.f) && (hit.Normal.y > MOVEMENT_SMALL_NUMBER) && IsWalkableFloor(moveCompReader, hit))
        {
            const float initRatioRemaining = 1.f - positionRatioApplied;
            rampMoveDelta = CalculateGroundMoveDelta(moveCompReader, moveDelta * initRatioRemaining, hit, false);
            lastMoveRatioSlice = initRatioRemaining * lastMoveRatioSlice;
            SafeMovement(characterEntity, rampMoveDelta, mTransformSys->GetWorldRotation(worldTransComp.Read()), true, &hit, inHandle->DrawDebugShape);

            const float secondHitRatio = hit.PositionRatio * initRatioRemaining;
            positionRatioApplied = MathUtils::Clamp(positionRatioApplied + secondHitRatio, 0.f, 1.0f);
        }

        if (hit.IsValidBlockingHit())
        {
            if (CanStepUp(moveCompReader, hit))
            {
                // Hit a barrier, try to step up
                const Float3 preStepUpPosition = mTransformSys->GetWorldTranslation(worldTransComp.Read());
                const Float3 gravityDir{0.f, -1.0f, 0.f};
                if (!StepUp(moveCompReader, gravityDir, moveDelta * (1.f - positionRatioApplied), hit, outStepResult))
                {
                    HandleImpact(characterEntity, hit, lastMoveRatioSlice, rampMoveDelta);
                    SlideAlongSurface(characterEntity, moveDelta, 1.f - positionRatioApplied, hit.Normal, hit, true);
                }
                else
                {
                    LOG_INFO("can step up!");
                    if (!inHandle->MaintainHorizontalGroundVelocity)
                    {
                        const float stepUpPosRatio = (1.0f - positionRatioApplied) * deltaSecs;
                        if (!HasAnimRootMotion(moveCompReader) && stepUpPosRatio >= MOVEMENT_SMALL_NUMBER)
                        {
                            inHandle->Velocity = (mTransformSys->GetWorldTranslation(worldTransComp.Read()) - preStepUpPosition) / stepUpPosRatio;
                            inHandle->Velocity.y = 0.f;
                        }
                    }
                }
            }
            else
            {
                HandleImpact(characterEntity, hit, lastMoveRatioSlice, rampMoveDelta);
                SlideAlongSurface(characterEntity, moveDelta, 1.f - positionRatioApplied, hit.Normal, hit, true);
            }
        }
    }
}

Float3 CharacterMovementSystemG::ComputeSlideVector(ecs::EntityID inEntity, const Float3& inDelta, float posRatio, const Float3& normal, const HitResult& hit) const
{
    Float3 result = MovementSystemBaseG::ComputeSlideVector(inEntity, inDelta, posRatio, normal, hit);
    
    auto moveComp = mGameWorld->GetComponent<CharacterMovementComponentG>(inEntity);
    auto moveCompReader = moveComp.Read();

    // Prevent boosting up slopes
    if (IsFalling(moveCompReader))
    {
        result = HandleSlopeBoosting(moveCompReader, result, inDelta, posRatio, normal, hit);
    }

    return result;
}

Float3 CharacterMovementSystemG::HandleSlopeBoosting(const CharacterMovementCompReader& inHandle, const Float3& slideResult, const Float3& inDelta, float posRatio, const Float3& normal, const HitResult& hit) const
{
    Float3 result = slideResult;
    if (result.y > 0.f)
    {
        // Don't move any higher than we originally intended.
        const float yLimit = inDelta.y * posRatio;
        if (result.y - yLimit > MOVEMENT_SMALL_NUMBER)
        {
            if (yLimit > 0.f)
            {
                // Rescale the entire vector (not just the y component) otherwise we change the direction and likely head right back into the impact.
                const float upPercent = yLimit / result.y;
                result *= upPercent;
            }
            else
            {
                // We were heading down but were going to deflect upwards. Just make the deflection horizontal.
                result = Float3::Zero();
            }

            // Make remaining portion of original result horizontal and parallel to impact normal.
            const Float3 remainderXZ = (slideResult - result) * Float3(1.f, 0.f, 1.f);
            const Float3 normalXZ = normal.SafeNormalXZ();
            const Float3 adjust = MovementSystemBaseG::ComputeSlideVector(inHandle.GetEntityID(), remainderXZ, 1.f, normalXZ, hit);
            result += adjust;
        }
    }

    return result;
}

float CharacterMovementSystemG::SlideAlongSurface(ecs::EntityID inEntity, const Float3& inDelta, float positionRatio, const Float3& normal, HitResult& outHitResult, bool handleImpact)
{
    if (!outHitResult.bBlockingHit) 
    {
        return 0.f;
    }

    auto moveComp = mGameWorld->GetComponent<CharacterMovementComponentG>(inEntity);
    auto moveCompReader = moveComp.Read();

    Float3 HitNormal{normal};
    if (IsMovingOnGround(moveCompReader))
    {
        // We don't want to be pushed up an unwalkable surface
        if (HitNormal.y > 0.f)
        {
            if (!IsWalkableFloor(moveCompReader, outHitResult))
            {
                HitNormal = HitNormal.SafeNormalXZ();
            }
        }
        else if (HitNormal.y < -MOVEMENT_SMALL_NUMBER)
        {
            // Don't push down into the floor when the impact is on the upper portion of the capsule.
            if (moveCompReader->CurrentFloor.FloorDist < sMinFloorDist && moveCompReader->CurrentFloor.bBlockingHit)
            {
                const Float3 floorNormal = moveCompReader->CurrentFloor.FloorHitResult.Normal;
                const bool bFloorOpposedToMovement = inDelta.Dot(floorNormal) < 0.f && (floorNormal.y < 1.f - MOVEMENT_SMALL_NUMBER);
                if (bFloorOpposedToMovement)
                {
                    HitNormal = floorNormal;
                }

                HitNormal = normal.SafeNormalXZ();
            }
        }
    }

    return MovementSystemBaseG::SlideAlongSurface(inEntity, inDelta, positionRatio, HitNormal, outHitResult, handleImpact);
}

void CharacterMovementSystemG::TwoSurfaceAdjust(ecs::EntityID inEntity, Float3& outDelta, const HitResult& hit, const Float3& oldHitNormal) const
{
    Float3 delta = outDelta;

    MovementSystemBaseG::TwoSurfaceAdjust(inEntity, outDelta, hit, oldHitNormal);

    auto moveComp = mGameWorld->GetComponent<CharacterMovementComponentG>(inEntity);
    auto moveCompReader = moveComp.Read();

    if (IsMovingOnGround(moveCompReader))
    {
        // Allow slides up walkable surfaces, but not unwalkable ones (treat those as vertical barriers).
        if (outDelta.y > 0.f)
        {
            if ((hit.Normal.y >= moveCompReader->WalkableFloorY || IsWalkableFloor(moveCompReader, hit)) &&
                hit.Normal.y > MOVEMENT_SMALL_NUMBER)
            {
                // Maintain horizontal velocity
                const float ratio = 1.f - hit.PositionRatio;
                const Float3 scaledDelta = outDelta.SafeNormal() * delta.Length();
                outDelta = Float3(delta.x, scaledDelta.y / hit.Normal.y, delta.z) * ratio;

                // Should never exceed MaxStepHeight in vertical component, so rescale if necessary.
                // This should be rare (Hit.Normal.Z above would have been very small) but we'd rather lose horizontal velocity than go too high.
                if (outDelta.y > moveCompReader->MaxStepHeight)
                {
                    const float reScale = moveCompReader->MaxStepHeight / outDelta.y;
                    outDelta *= reScale;
                }
            }
            else
            {
                outDelta.y = 0.f;
            }
        }
        else if (outDelta.y < 0.f)
        {
            // Don't push down into the floor.
            if (moveCompReader->CurrentFloor.FloorDist < sMinFloorDist && moveCompReader->CurrentFloor.bBlockingHit)
            {
                outDelta.y = 0.f;
            }
        }
    }
}

void CharacterMovementSystemG::HandleImpact(ecs::EntityID inEntity, const HitResult& hit, float posRatio, const Float3& moveDelta)
{
    auto moveComp = mGameWorld->GetComponent<CharacterMovementComponentG>(inEntity);

    OnMovementBlocked(moveComp.Write(), hit);
}

void CharacterMovementSystemG::AdjustFloorHeight(const CharacterMovementCompWriter& inHandle)
{
    if (!inHandle->CurrentFloor.IsWalkableFloor())
    {
        return;
    }

    auto characterEntity = inHandle.GetEntityID();
    auto moveComp = mGameWorld->GetComponent<CharacterMovementComponentG>(characterEntity);
    auto worldTransComp = mGameWorld->GetComponent<WorldTransformComponentG>(characterEntity);

    float oldFloorDist = inHandle->CurrentFloor.FloorDist;
    if (inHandle->CurrentFloor.bLineTrace)
    {
        if (oldFloorDist < sMinFloorDist && inHandle->CurrentFloor.LineDist >= sMinFloorDist)
        {
            // This would cause us to scale unwalkable walls
            return;
        }
        else
        {
            // Falling back to a line trace means the sweep was unwalkable (or in penetration). Use the line distance for the vertical adjustment.
            oldFloorDist = inHandle->CurrentFloor.LineDist;
        }
    }

    // Move up or down to maintain floor height.
    if (oldFloorDist < sMinFloorDist || oldFloorDist > sMaxFloorDist)
    {
        HitResult adjustHit;
        const TRSScalarType capsuleInitY = GetCapsuleWorldTranslation(characterEntity).y;
        const float avgFloorDist = (sMinFloorDist + sMaxFloorDist) * 0.5f;
        const float moveDist = avgFloorDist - oldFloorDist;
        SafeMovement(characterEntity, Float3(0.f, moveDist, 0.f), mTransformSys->GetWorldRotation(worldTransComp.Read()), true, &adjustHit, inHandle->DrawDebugShape);

        if (!adjustHit.IsValidBlockingHit())
        {
            inHandle->CurrentFloor.FloorDist += moveDist;
        }
        else if (moveDist > 0.f)
        {
            const TRSScalarType capsuleCurrentY = GetCapsuleWorldTranslation(characterEntity).y;
            inHandle->CurrentFloor.FloorDist += float(capsuleCurrentY - capsuleInitY);
        }
        else
        {
            Assert(moveDist < 0.f);
            const TRSVector3Type capsulePos = GetCapsuleWorldTranslation(characterEntity);
            const TRSScalarType capsuleCurrentY = capsulePos.y;

            if (IsWalkableFloor(moveComp.Read(), adjustHit))
            {
                inHandle->CurrentFloor.SetFromSweepResult(adjustHit, float(capsuleCurrentY - adjustHit.Location.y), true);
            }
        }
    }
}

void CharacterMovementSystemG::FindFloor(const CharacterMovementCompReader& inHandle, const TRSVector3Type& capsulePos, FindFloorResult& outFloorResult, const HitResult* downwardSweepResult) const
{
    // Check validation of character capsule
    auto [capsuleRadius, capsuleHalfHeight] = GetScaledCapsuleSize(inHandle.GetEntityID());
    if (capsuleRadius <= 0.f || capsuleHalfHeight <= 0.f)
    {
        outFloorResult.Reset();
        return;
    }

    float heightCheckAdjust = IsMovingOnGround(inHandle) ? sMaxFloorDist + MOVEMENT_SMALL_NUMBER : -sMaxFloorDist;
    float floorSweepDist = MathUtils::Max(sMaxFloorDist, inHandle->MaxStepHeight + heightCheckAdjust);
    float floorLineTraceDist = floorSweepDist;

    // Sweep floor
    if (floorSweepDist > 0.f || floorLineTraceDist)
    {
        CalculateFloorDistance(inHandle, capsulePos, floorLineTraceDist, floorSweepDist, capsuleRadius, outFloorResult, downwardSweepResult);
    }

    // outFloorResult.HitResult is now the result of the vertical floor check.
    // See if we should try to "perch" at this location.
    if (outFloorResult.bBlockingHit && !outFloorResult.bLineTrace)
    {
        if (ShouldCalculatePerchResult(inHandle, outFloorResult.FloorHitResult, true))
        {
            float maxPerchFloorDist = MathUtils::Max(sMaxFloorDist, inHandle->MaxStepHeight + heightCheckAdjust);

            FindFloorResult perchFloorResult;
            if (CalculatePerchResult(inHandle, GetValidPerchRadius(inHandle), outFloorResult.FloorHitResult, maxPerchFloorDist, perchFloorResult))
            {
                // Don't allow the floor distance adjustment to push us up too high, or we will move beyond the perch distance and fall next time.
                const float avgFloorDist = (sMinFloorDist + sMaxFloorDist) * 0.5f;
                const float moveUpDist = (avgFloorDist - outFloorResult.FloorDist);
                if (moveUpDist + perchFloorResult.FloorDist >= maxPerchFloorDist)
                {
                    outFloorResult.FloorDist = avgFloorDist;
                }

                // If the regular capsule is on an unwalkable surface but the perched one would allow us to stand, override the normal to be one that is walkable.
                if (!outFloorResult.bWalkableFloor)
                {
                    // Floor distances are used as the distance of the regular capsule to the point of collision, to make sure AdjustFloorHeight() behaves correctly.
                    outFloorResult.SetFromLineTrace(perchFloorResult.FloorHitResult, outFloorResult.FloorDist, MathUtils::Max(outFloorResult.FloorDist, sMinFloorDist), true);
                }
            }
            else
            {
                // We had no floor (or an invalid one because it was unwalkable), and couldn't perch here, so invalidate floor (which will cause us to start falling).
                outFloorResult.bWalkableFloor = false;
            }
        }
    }
}

void CharacterMovementSystemG::CalculateFloorDistance(const CharacterMovementCompReader& inHandle, const TRSVector3Type& capsulePos, 
    float lineTraceDist, float sweepDist, float sweepRadius, FindFloorResult& outFloorResult, const HitResult* downwardSweepResult) const
{
    outFloorResult.Reset();

    // Check validation of character capsule 
    auto [capsuleRadius, capsuleHalfHeight] = GetScaledCapsuleSize(inHandle.GetEntityID());
    if (capsuleRadius <= 0.f || capsuleHalfHeight <= 0.f)
    {
        return;
    }

    // Get character capsule world transform
    TRS capsuleWorldTrans = GetCapsuleWorldTransform(inHandle.GetEntityID());
    //Transform capsulePhyTrans;
    //capsulePhyTrans.translation = Float3(GetRelativePosition(capsuleWorldTrans.mTranslation, mPhysicsTile));
    //capsulePhyTrans.rotation = Quaternion(capsuleWorldTrans.mRotation);
    //capsulePhyTrans.scale = Float3(capsuleWorldTrans.mScale);
    //TRSQuaternionType capsuleRotation = capsuleWorldTrans.mRotation;
    auto* physicsSys = mGameWorld->GetGameSystem<PhysicsSystemG>();
    auto physicsComponent = mGameWorld->GetComponent<PhysicsComponentG>(inHandle.GetEntityID());
    const PhysicsActor* self = physicsSys->GetPhysicsActor(physicsComponent.Read());
    bool skipSweep = false;
    if (downwardSweepResult != nullptr && downwardSweepResult->IsValidBlockingHit())
    {
        // Only if the supplied sweep was vertical and downward.
        if ((downwardSweepResult->StartPos.y > downwardSweepResult->EndPos.y) &&
            (downwardSweepResult->StartPos - downwardSweepResult->EndPos).LengthSquaredXZ() <= MOVEMENT_SMALL_NUMBER)
        {
            // Reject hits that are barely on the cusp of the radius of the capsule
            if (IsWithinEdgeTolerance(downwardSweepResult->Location, downwardSweepResult->ImpactPoint, capsuleRadius))
            {
                // Don't try a redundant sweep, regardless of whether this sweep is usable.
                skipSweep = true;

                bool isWalkable = IsWalkableFloor(inHandle, *downwardSweepResult);
                float floorDist = float( capsulePos.y - downwardSweepResult->Location.y);
                outFloorResult.SetFromSweepResult(*downwardSweepResult, floorDist, isWalkable);

                if (isWalkable)
                {
                    // Use the supplied downward sweep as the floor hit result.
                    return;
                }
            }
        }
    }

    // We require the sweep distance to be >= the line distance, otherwise the HitResult can't be interpreted as the sweep result.
    if (sweepDist < lineTraceDist)
    {
        return;
    }

    // Sweep test
    if (!skipSweep && sweepDist > 0.f && sweepRadius > 0.f)
    {
        // Use a shorter height to avoid sweeps giving weird results if we start on a surface.
        // This also allows us to adjust out of penetrations.
        const float shrinkScale = 0.9f;
        const float shrinkScaleOverlap = 0.1f;
        float shrinkHeight = capsuleHalfHeight * (1.f - shrinkScale);
        float actualSweepDist = sweepDist + shrinkHeight;
        PhysicsGeometryCapsule capsuleGeo{Float3::Zero(), Quaternion::Identity(), sweepRadius, capsuleHalfHeight - shrinkHeight};

        HitResult hit;

        
        bool blockingHit = WorldCollision::SweepSingle(mGameWorld, &capsuleGeo, capsuleWorldTrans,
                                                       Float3(0.f, -actualSweepDist, 0.f),
                                                       hit,
                                                       WorldCollision::DefaultHitFlag(),
                                                       physicsSys->GetCollisionMask(physicsComponent.Read()), self,
                                                       inHandle->DrawDebugShape);
        if (blockingHit)
        {
            // Reject hits adjacent to us, we only care about hits on the bottom portion of our capsule.
            // Check 2D distance to impact point, reject if within a tolerance from radius.
            if (hit.bStartPenetrating || !IsWithinEdgeTolerance(capsulePos, hit.ImpactPoint, capsuleGeo.radius))
            {
                // Use a capsule with a slightly smaller radius and shorter height to avoid the adjacent object.
                // Capsule must not be nearly zero or the trace will fall back to a line trace from the start point and have the wrong length.
                capsuleGeo.radius = MathUtils::Max(0.f, capsuleGeo.radius - sSweepEdgeRejectThres - MOVEMENT_SMALL_NUMBER);
                if (!(capsuleGeo.radius < MOVEMENT_SMALL_NUMBER))
                {
                    shrinkHeight = capsuleHalfHeight * (1.0f - shrinkScaleOverlap);
                    actualSweepDist = sweepDist + shrinkHeight;
                    capsuleGeo.halfHeight = capsuleHalfHeight - shrinkHeight;

                    hit.Init();
                    blockingHit = WorldCollision::SweepSingle(mGameWorld, &capsuleGeo, capsuleWorldTrans,
                                                              Float3(0.f, -actualSweepDist, 0.f),
                                                              hit,
                                                              WorldCollision::DefaultHitFlag(),
                                                              physicsSys->GetCollisionMask(physicsComponent.Read()), self,
                                                              inHandle->DrawDebugShape);
                }
            }

            // Reduce hit distance by ShrinkHeight because we shrank the capsule for the trace.
            // We allow negative distances here, because this allows us to pull out of penetrations.
            const float maxPenetrationAdjust = MathUtils::Max(sMaxFloorDist, capsuleRadius);
            const float sweepResult = MathUtils::Max(-maxPenetrationAdjust, hit.PositionRatio * actualSweepDist - shrinkHeight);

            outFloorResult.SetFromSweepResult(hit, sweepResult, false);
            if (hit.IsValidBlockingHit() && IsWalkableFloor(inHandle, hit))
            {
                if (sweepResult <= sweepDist)
                {
                    outFloorResult.bWalkableFloor = true;
                    return;
                }
            }
        }
    }

    // Since we require a longer sweep than line trace, we don't want to run the line trace if the sweep missed everything.
    // We do however want to try a line trace if the sweep was stuck in penetration.
    if (!outFloorResult.bBlockingHit && !outFloorResult.FloorHitResult.bStartPenetrating)
    {
        outFloorResult.FloorDist = sweepDist;
        return;
    }

    // Line trace
    if (lineTraceDist > 0.f)
    {
        const float shrinkHeight = capsuleHalfHeight + capsuleRadius;
        const TRSVector3Type lineTraceStart = capsuleWorldTrans.mTranslation;
        const float actualTraceDist = lineTraceDist + shrinkHeight;
        const Float3 down = Float3(0.f, -actualTraceDist, 0.f);

        HitResult hit(1.f);
        bool blockingHit = WorldCollision::RayCastSingle(mGameWorld, lineTraceStart, down, hit, WorldCollision::DefaultHitFlag(), physicsSys->GetCollisionMask(physicsComponent.Read()), self, inHandle->DrawDebugShape);

        if (blockingHit)
        {
            if (hit.PositionRatio > 0.f)
            {
                // Reduce hit distance by ShrinkHeight because we started the trace higher than the base.
                // We allow negative distances here, because this allows us to pull out of penetrations.
                const float maxPenetrationAdjust = MathUtils::Max(sMaxFloorDist, capsuleRadius);
                const float lineResult = MathUtils::Max(-maxPenetrationAdjust, hit.PositionRatio * actualTraceDist - shrinkHeight);

                outFloorResult.bBlockingHit = true;
                if (lineResult <= lineTraceDist && IsWalkableFloor(inHandle, hit))
                {
                    outFloorResult.SetFromLineTrace(hit, outFloorResult.FloorDist, lineResult, true);
                    return;
                }
            }
        }
    }

    // No hits are acceptable
    outFloorResult.bWalkableFloor = false;
}

bool CharacterMovementSystemG::CanStepUp(const CharacterMovementCompReader& inHandle, const HitResult& hit) const 
{
    if (!hit.IsValidBlockingHit() || inHandle->MoveMode == MovementMode::MOVE_Falling)
    {
        return false;
    }

    if (hit.HitEntity == ecs::EntityID::InvalidHandle())
    {
        return false;
    }

    return true;
}

bool CharacterMovementSystemG::StepUp(const CharacterMovementCompReader& inHandle, const Float3& gravityDir, const Float3& inDelta, const HitResult& hit, StepDownResult* outStepDownResult)
{
    if (!CanStepUp(inHandle, hit) || inHandle->MaxStepHeight <= 0.f)
    {
        return false;
    }

    auto characterEntity = inHandle.GetEntityID();
    auto worldTransComp = mGameWorld->GetComponent<WorldTransformComponentG>(characterEntity);

    const Float3 characterOldPosition = mTransformSys->GetWorldTranslation(worldTransComp.Read());
    const Quaternion characterOldRotation = mTransformSys->GetWorldRotation(worldTransComp.Read());
    auto RevertMove = [&]() 
    {
        mTransformSys->SetWorldTranslation(worldTransComp.Write(), characterOldPosition);
        mTransformSys->SetWorldRotation(worldTransComp.Write(), characterOldRotation);
    };

    // Check validation of character capsule
    auto [capsuleRadius, capsuleHalfHeight] = GetScaledCapsuleSize(characterEntity);
    if (capsuleRadius <= 0.f || capsuleHalfHeight <= 0.f)
    {
        return false;
    }
    const TRSVector3Type capsuleOldPosition = GetCapsuleWorldTranslation(characterEntity);

    // Don't bother stepping up if top of capsule is hitting something.
    if (hit.ImpactPoint.y > capsuleOldPosition.y + capsuleHalfHeight)
    {
        return false;
    }

    // Gravity should be a normalized direction
    if (gravityDir.IsNearlyZero())
    {
        return false;
    }

    float stepUpHeight = inHandle->MaxStepHeight;
    float stepDownHeight = stepUpHeight;
    const float stepSideY = -1.f * hit.ImpactNormal.Dot(gravityDir);
    TRSScalarType characterInitFloorY = capsuleOldPosition.y - (capsuleHalfHeight + capsuleRadius);
    TRSScalarType characterFloorPointY = characterInitFloorY;

    if (IsMovingOnGround(inHandle) && inHandle->CurrentFloor.IsWalkableFloor())
    {
        // Since we float a variable amount off the floor, we need to enforce max step height off the actual point of impact with the floor.
        const float floorDist = MathUtils::Max(0.f, inHandle->CurrentFloor.GetDistanceToFloor());
        characterInitFloorY -= floorDist;
        stepUpHeight = MathUtils::Max(stepUpHeight - floorDist, 0.f);
        stepDownHeight = inHandle->MaxStepHeight + sMaxFloorDist * 2.0f;

        bool isHitVerticalFace = !IsWithinEdgeTolerance(hit.Location, hit.ImpactPoint, capsuleRadius);
        if (!isHitVerticalFace && !inHandle->CurrentFloor.bLineTrace)
        {
            characterFloorPointY = inHandle->CurrentFloor.FloorHitResult.ImpactPoint.y;
        }
        else
        {
            // Base floor point is the base of the capsule moved down by how far we are hovering over the surface we are hitting.
            characterFloorPointY -= inHandle->CurrentFloor.FloorDist;
        }
    }

    // Don't step up if the impact is below us, accounting for distance from floor.
    if (hit.ImpactPoint.y <= characterInitFloorY)
    {
        return false;
    }

    // StepUP 1, go up - treat as vertical wall    
    HitResult sweepUpHit;
    MoveImpl(characterEntity, -gravityDir * stepUpHeight, characterOldRotation, true, &sweepUpHit, inHandle->DrawDebugShape);
    if (sweepUpHit.bStartPenetrating)
    {
        // undo movement
        RevertMove();
        return false;
    }

    // StepUP 2, go forward
    HitResult TmpHit;
    MoveImpl(characterEntity, inDelta, characterOldRotation, true, &TmpHit, inHandle->DrawDebugShape);
    if (TmpHit.bBlockingHit)
    {
        if (TmpHit.bStartPenetrating)
        {
            // undo movement
            RevertMove();
            return false;
        }

        // If we hit something above us and also something ahead of us, we should notify about the upward hit as well.
        // The forward hit will be handled later (in the bSteppedOver case below).
        // In the case of hitting something above but not forward, we are not blocked from moving so we don't need the notification.
        if (sweepUpHit.bBlockingHit && TmpHit.bBlockingHit)
        {
            HandleImpact(characterEntity, sweepUpHit);
        }

        // Character run into a wall
        HandleImpact(characterEntity, TmpHit);
        if (IsFalling(inHandle)) 
        {
            return true;
        }

        // Adjust and retry again
        const float forwardHitRatio = TmpHit.PositionRatio;
        const float fowardSlideAmount = SlideAlongSurface(characterEntity, inDelta, 1.f - TmpHit.PositionRatio, TmpHit.Normal, TmpHit, true);
        if (IsFalling(inHandle))
        {
            // undo movement
            RevertMove();
            return false;
        }

        if (forwardHitRatio == 0.f && fowardSlideAmount == 0.f)
        {
            // undo movement
            RevertMove();
            return false;
        }
    }

    // StepUP 3, go down
    MoveImpl(characterEntity, gravityDir * stepDownHeight, characterOldRotation, true, &TmpHit, inHandle->DrawDebugShape);
    // If step down was initially penetrating abort the step up
    if (TmpHit.bStartPenetrating)
    {
        RevertMove();
        return false;
    }

    StepDownResult curStepDownResult;
    if (TmpHit.IsValidBlockingHit())
    {
        const TRSScalarType deltaY = TmpHit.ImpactPoint.y - characterFloorPointY;
        if (deltaY > inHandle->MaxStepHeight)
        {
            RevertMove();
            return false;
        }

        // Reject unwalkable surface normals here.
        if (!IsWalkableFloor(inHandle, TmpHit)) 
        {
            // Reject if normal opposes movement direction
            const bool normalTowardsMe = inDelta.Dot(TmpHit.ImpactNormal) < 0.f;
            if (normalTowardsMe)
            {
                RevertMove();
                return false;
            }

            // Also reject if we would end up being higher than our starting location by stepping down.
            // It's fine to step down onto an unwalkable normal below us, we will just slide off. Rejecting those moves would prevent us from being able to walk off the edge.
            if (TmpHit.Location.y > capsuleOldPosition.y)
            {
                RevertMove();
                return false;
            }
        }

        // Reject moves where the downward sweep hit something very close to the edge of the capsule. This maintains consistency with FindFloor as well.
        if (!IsWithinEdgeTolerance(TmpHit.Location, TmpHit.ImpactPoint, capsuleRadius))
        {
            RevertMove();
            return false;
        }

        // Don't step up onto invalid surfaces if traveling higher.
        if (deltaY > 0.f && !CanStepUp(inHandle, TmpHit))
        {
            RevertMove();
            return false;
        }

        // See if we can validate the floor as a result of this step down. In almost all cases this should succeed, and we can avoid computing the floor outside this method.
        if (outStepDownResult != nullptr)
        {
            FindFloor(inHandle, GetCapsuleWorldTranslation(characterEntity), curStepDownResult.Floor, &TmpHit);

            // Reject unwalkable normals if we end up higher than our initial height.
            // It's fine to walk down onto an unwalkable surface, don't reject those moves.
            if (TmpHit.Location.y > capsuleOldPosition.y)
            {
                // We should reject the floor result if we are trying to step up an actual step where we are not able to perch (this is rare).
                // In those cases we should instead abort the step up and try to slide along the stair.
                if (!curStepDownResult.Floor.bBlockingHit && stepSideY < 0.08f)
                {
                    RevertMove();
                    return false;
                }
            }

            curStepDownResult.FloorFound = true;
        }
    }

    if (outStepDownResult != nullptr)
    {
        *outStepDownResult = curStepDownResult;
    }

    return true;
}


bool CharacterMovementSystemG::CanWalkOffLedges(const CharacterMovementCompReader& inHandle) const
{
    if (!inHandle->CanWalkOffLedgesWhenCrouching && IsCrouching(inHandle))
    {
        return false;
    }

    return inHandle->CanWalkOffLedges;
}

void CharacterMovementSystemG::HandleWalkingOffLedges(const CharacterMovementCompReader& inHandle, const Float3& prevFloorImpactNormal, const Float3& prevFloorContactNormal, const TRSVector3Type& prevCapsulePos, float deltaSecs) const {}

bool CharacterMovementSystemG::CheckLedgeDirection(const CharacterMovementCompReader& inHandle, const TRSVector3Type& oldLocation, const Float3& sideStep, const Float3& gravityDir) const
{
    // Check validation of character capsule
    auto [capsuleRadius, capsuleHalfHeight] = GetScaledCapsuleSize(inHandle.GetEntityID());
    if (capsuleRadius <= 0.f || capsuleHalfHeight <= 0.f)
    {
        return false;
    }
    const auto phyGeo = static_cast<const PhysicsGeometryCapsule*>(GetPhysicsGeometry(inHandle.GetEntityID()));
    auto capsuleGeo = const_cast<PhysicsGeometryCapsule*>(phyGeo);
    TRS capsulePhyTrans;
    capsulePhyTrans.mTranslation = oldLocation;

    HitResult result;
    WorldCollision::SweepSingle(mGameWorld, capsuleGeo, capsulePhyTrans, sideStep, result, WorldCollision::DefaultHitFlag(), CollisionChannelBit::BlockAll);

    if (!result.bBlockingHit || IsWalkableFloor(inHandle, result))
    {
        if (!result.bBlockingHit)
        {
            //const TRSVector3Type sideDest = oldLocation + TRSVector3Type(sideStep);
            capsulePhyTrans.mTranslation += TRSVector3Type(sideStep);
            Float3 delta = gravityDir * (inHandle->MaxStepHeight + inHandle->LedgeCheckThreshold);
            //capsuleWorldTrans.SetTranslation(sideDest);

            WorldCollision::SweepSingle(mGameWorld, capsuleGeo, capsulePhyTrans, sideStep, result, WorldCollision::DefaultHitFlag(), CollisionChannelBit::BlockAll);
        }

        if (result.PositionRatio < 1.f && IsWalkableFloor(inHandle, result))
        {
            return true;
        }
    }

    return false;
}

Float3 CharacterMovementSystemG::GetLedgeMove(const CharacterMovementCompReader& inHandle, const TRSVector3Type& oldLocation, const Float3& delta, const Float3& gravityDir) const
{
    if (delta.IsNearlyZero())
    {
        return Float3::Zero();
    }

    Float3 sideDir{delta.z, 0.f, -1.f * delta.x};
    
    // Try left
    if (CheckLedgeDirection(inHandle, oldLocation, sideDir, gravityDir))
    {
        return sideDir;
    }

    // Try right
    sideDir *= -1.f;
    if (CheckLedgeDirection(inHandle, oldLocation, sideDir, gravityDir))
    {
        return sideDir;
    }

    return Float3::Zero();
}

bool CharacterMovementSystemG::CanCrouch(const CharacterMovementCompReader& inHandle) const
{
    return inHandle->CanCrouch && !inHandle->IsCrouched && IsMovingOnGround(inHandle);
}

void CharacterMovementSystemG::Crouch(const CharacterMovementCompWriter& inHandle) 
{
    auto entity = inHandle.GetEntityID();
    auto moveComp = mGameWorld->GetComponent<CharacterMovementComponentG>(entity);

    if (!CanCrouch(moveComp.Read()))
    {
        return;
    }

    // Check validation of character capsule
    const auto capsule = static_cast<const PhysicsGeometryCapsule*>(GetPhysicsGeometry(entity));
    if (capsule)
    {
        inHandle->UnCrouchHalfHeight = capsule->halfHeight;

        SetCharacterCapsuleHalfHeight(entity, inHandle->CrouchedHalfHeight);

        inHandle->IsCrouched = true;

        OnStartCrouch(inHandle, inHandle->UnCrouchHalfHeight - inHandle->CrouchedHalfHeight);
    }
}

void CharacterMovementSystemG::UnCrouch(const CharacterMovementCompWriter& inHandle) 
{
    auto entity = inHandle.GetEntityID();
    auto moveComp = mGameWorld->GetComponent<CharacterMovementComponentG>(entity);

    if (inHandle->IsCrouched == false)
    {
        return;
    }

    // Check validation of character capsule
    const auto capsule = static_cast<const PhysicsGeometryCapsule*>(GetPhysicsGeometry(entity));
    if (capsule)
    {
        SetCharacterCapsuleHalfHeight(entity, inHandle->UnCrouchHalfHeight);

        inHandle->IsCrouched = false;

        OnEndCrouch(inHandle, inHandle->CrouchedHalfHeight - inHandle->UnCrouchHalfHeight);
    }
}

UInt32 CharacterMovementSystemG::GetJumpMaxCount(const CharacterMovementCompReader& inHandle) const
{
    return inHandle->JumpMaxCount;
}

float CharacterMovementSystemG::GetJumpMaxHoldTime(const CharacterMovementCompReader& inHandle) const
{
    return inHandle->JumpMaxHoldTime;
}

float CharacterMovementSystemG::GetMaxJumpHeight(const CharacterMovementCompReader& inHandle) const
{
    const float gravityY = GetGravityY(inHandle);
    if (MathUtils::Abs(gravityY) > MOVEMENT_SMALL_NUMBER)
    {
        return MathUtils::Square(inHandle->JumpYVelocity) / (-2.0f * gravityY);
    }
    else
    {
        return 0.f;
    }
}

float CharacterMovementSystemG::GetMaxJumpHeightWithJumpTime(const CharacterMovementCompReader& inHandle) const
{
    const float maxJumpHeight = GetMaxJumpHeight(inHandle);
    return inHandle->JumpMaxHoldTime * inHandle->JumpYVelocity + maxJumpHeight;
}

Float3 CharacterMovementSystemG::GetImpartedMovementBaseVelocity(const CharacterMovementCompReader& inHandle) const
{
    Float3 result = Float3::Zero();

    return result;
}

bool CharacterMovementSystemG::CanJump(const CharacterMovementCompReader& inHandle) const
{
    // We can not jump when character is crouching
    if (inHandle->IsCrouched)
    {
        return false;
    }

    bool isJumpAllowed = inHandle->CanJump && IsMovingOnGround(inHandle);

    if (isJumpAllowed)
    {
        // Ensure JumpHoldTime and JumpCount are valid.
        if (!inHandle->IsJumpedLastFrame || inHandle->JumpMaxHoldTime < 0.f)
        {
            if (inHandle->JumpCurrentCount == 0 && IsFalling(inHandle))
            {
                isJumpAllowed = (inHandle->JumpCurrentCount + 1) < inHandle->JumpMaxCount;
            }
            else
            {
                isJumpAllowed = inHandle->JumpCurrentCount < inHandle->JumpMaxCount;
            }
        }
        else
        {
            // Only consider JumpKeyHoldTime as long as:
            // A) The jump limit hasn't been met OR
            // B) The jump limit has been met AND we were already jumping
            bool jumpKeyHold = (inHandle->IsPressedJump && inHandle->JumpKeyHoldTime < inHandle->JumpMaxHoldTime);
            isJumpAllowed = jumpKeyHold && ((inHandle->JumpCurrentCount < inHandle->JumpMaxCount) || (inHandle->IsJumpedLastFrame && inHandle->JumpCurrentCount == inHandle->JumpMaxCount));
        }
    }

    return isJumpAllowed;
}

void CharacterMovementSystemG::CheckJumpInput(const CharacterMovementCompWriter& inHandle, float deltaSecs)
{
    auto moveComp = mGameWorld->GetComponent<CharacterMovementComponentG>(inHandle.GetEntityID());
    auto moveCompReader = moveComp.Read();

    inHandle->JumpCurrentCountPreJump = inHandle->JumpCurrentCount;

    if (inHandle->IsPressedJump)
    {
        // If this is the first jump and we're already falling, then increment the JumpCount to compensate.
        bool isFirstJump = (inHandle->JumpCurrentCount == 0);
        if (isFirstJump && IsFalling(moveCompReader))
        {
            inHandle->JumpCurrentCount++;
        }

        bool didJump = DoJump(inHandle);
        if (didJump)
        {
            if (!inHandle->IsJumpedLastFrame)
            {
                inHandle->JumpCurrentCount++;
                inHandle->JumpForceTimeRemaining = inHandle->JumpMaxHoldTime;

                OnJumped(inHandle);
            }
        }

        inHandle->IsJumpedLastFrame = didJump;
    }
}

void CharacterMovementSystemG::ClearJumpInput(const CharacterMovementCompWriter& inHandle, float deltaSecs)
{
    if (inHandle->IsPressedJump)
    {
        inHandle->JumpKeyHoldTime += deltaSecs;

        // Don't disable bPressedJump right away if it's still held.
        // Don't modify JumpForceTimeRemaining because a frame of update may be remaining.
        if (inHandle->JumpKeyHoldTime >= inHandle->JumpMaxHoldTime)
        {
            inHandle->IsPressedJump = false;
        }
    }
    else
    {
        inHandle->JumpForceTimeRemaining = 0.f;
        inHandle->IsJumpedLastFrame = false;
    }
}

bool CharacterMovementSystemG::DoJump(const CharacterMovementCompWriter& inHandle)
{
    auto moveComp = mGameWorld->GetComponent<CharacterMovementComponentG>(inHandle.GetEntityID());

    if (CanJump(moveComp.Read()))
    {
        inHandle->Velocity.y = MathUtils::Max(inHandle->Velocity.y, inHandle->JumpYVelocity);
        SetMovementMode(inHandle, MovementMode::MOVE_Falling);
        return true;
    }

    return false;
}

void CharacterMovementSystemG::Jump(const CharacterMovementCompWriter& inHandle) 
{
    inHandle->IsPressedJump = true;
    inHandle->JumpKeyHoldTime = 0.f;
}

void CharacterMovementSystemG::StopJumping(const CharacterMovementCompWriter& inHandle)
{
    inHandle->IsPressedJump = false;
    ResetJumpState(inHandle);
}

void CharacterMovementSystemG::ResetJumpState(const CharacterMovementCompWriter& inHandle)
{
    inHandle->IsPressedJump = false;
    inHandle->IsJumpedLastFrame = false;
    inHandle->JumpKeyHoldTime = 0.f;
    inHandle->JumpForceTimeRemaining = 0.f;

    auto moveComp = mGameWorld->GetComponent<CharacterMovementComponentG>(inHandle.GetEntityID());
    if (!IsFalling(moveComp.Read()))
    {
        inHandle->JumpCurrentCount = 0;
        inHandle->JumpCurrentCountPreJump = 0;
    }
}

bool CharacterMovementSystemG::CheckFall(const CharacterMovementCompReader& inHandle, const FindFloorResult& oldFloor, const Float3& inDelta, const TRSVector3Type& capsuleOldPos, float remainingTime, float tickTime,
                                            UInt32 iterations, bool mustJump)
{
    if (mustJump || CanWalkOffLedges(inHandle))
    {
        HandleWalkingOffLedges(inHandle, oldFloor.FloorHitResult.ImpactNormal, oldFloor.FloorHitResult.Normal, capsuleOldPos, tickTime);

        if (IsMovingOnGround(inHandle))
        {
            auto moveComp = mGameWorld->GetComponent<CharacterMovementComponentG>(inHandle.GetEntityID());

            // If still walking, then fall. If not, assume the user set a different mode they want to keep.
            StartFalling(moveComp.Write(), iterations, remainingTime, tickTime, inDelta, capsuleOldPos);
        }
        return true;
    }

    return false;
}

void CharacterMovementSystemG::StartFalling(const CharacterMovementCompWriter& inHandle, UInt32 iterations, float remainingTime, float tickTime, const Float3& inDelta, const TRSVector3Type& capsuleOldPos)
{
    auto moveComp = mGameWorld->GetComponent<CharacterMovementComponentG>(inHandle.GetEntityID());

    // Start Falling
    const float desiredDist = inDelta.Length();
    const float actualDist = float((GetCapsuleWorldTranslation(inHandle.GetEntityID()) - capsuleOldPos).LengthXZ());

    remainingTime = (desiredDist < MOVEMENT_SMALL_NUMBER) ? 0.f : remainingTime + tickTime * (1.f - MathUtils::Min(1.f, actualDist / desiredDist));
    if (IsMovingOnGround(moveComp.Read()))
    {
        SetMovementMode(inHandle, MovementMode::MOVE_Falling);
    }

    PerformPhysicsMovement(inHandle, remainingTime, iterations);
}

Float3 CharacterMovementSystemG::GetFallingLateralAcceleration(const CharacterMovementCompReader& inHandle, float deltaTime) const
{
    // No acceleration in Y
    Float3 fallAcceleration{inHandle->Acceleration};
    fallAcceleration.y = 0.f;

    // Bound acceleration, falling object has minimal ability to impact acceleration
    if (!HasAnimRootMotion(inHandle) && fallAcceleration.LengthSquaredXZ() > 0.f)
    {
        float tickAirControl = inHandle->AirControl;

        // Boost
        if (tickAirControl != 0.f)
        {
            // Allow a burst of initial acceleration
            if (inHandle->AirControlBoostMultiplier > 0.f && inHandle->Velocity.LengthSquaredXZ() < MathUtils::Square(inHandle->AirControlBoostVelocityThreshold))
            {
                tickAirControl = MathUtils::Min(1.f, inHandle->AirControlBoostMultiplier * tickAirControl);
            }
        }

        fallAcceleration = fallAcceleration * tickAirControl;
        fallAcceleration.ClampToMaxSize(GetMaxAcceleration(inHandle));
    }
    return fallAcceleration;
}

Float3 CharacterMovementSystemG::CalculateNewFallVelocity(const CharacterMovementCompReader& inHandle, const Float3& initialVelocity, const Float3& gravity, float deltaTime) const
{
    Float3 result = initialVelocity;

    if (deltaTime > 0.f)
    {
        // Apply gravity
        result += gravity * deltaTime;

        // Don't exceed max speed
        float maxSpeed = GetMaxSpeed(inHandle);
        if (result.LengthSquared() > MathUtils::Square(maxSpeed))
        {
            const Float3 gravityDir = gravity.SafeNormal();
            if (result.Dot(gravityDir) > maxSpeed)
            {
                result = Float3::PointPlaneProject(result, Float3::Zero(), gravityDir) + gravityDir * maxSpeed;
            }
        }
    }

    return result;
}
#pragma optimize("",off)
bool CharacterMovementSystemG::IsValidLandingSpot(const CharacterMovementCompReader& inHandle, const TRSVector3Type& capsulePos, const HitResult& hit) const
{
    if (!hit.bBlockingHit)
    {
        return false;
    }

    // Skip some checks if penetrating. Penetration will be handled by the FindFloor call (using a smaller capsule)
    if (!hit.bStartPenetrating)
    {
        // Reject unwalkable floor normals
        if (!IsWalkableFloor(inHandle, hit))
        {
            return false;
        }

        // Check validation of character capsule
        auto [capsuleRadius, capsuleHalfHeight] = GetScaledCapsuleSize(inHandle.GetEntityID());
        if (capsuleRadius <= 0.f || capsuleHalfHeight <= 0.f)
        {
            return false;
        }

        // Reject hits that are above our lower hemisphere (can happen when sliding down a vertical surface).
        const TRSScalarType lowerHemisphereY = hit.Location.y - capsuleHalfHeight;
        if (hit.ImpactPoint.y >= lowerHemisphereY)
        {
            return false;
        }

        // Reject hits that are barely on the cusp of the radius of the capsule
        if (!IsWithinEdgeTolerance(hit.Location, hit.ImpactPoint, capsuleRadius))
        {
            return false;
        }
    }
    else
    {
        // Penetration
        if (hit.Normal.y < MOVEMENT_SMALL_NUMBER)
        {
            // Normal is nearly horizontal or downward, that's a penetration adjustment next to a vertical or overhanging wall. Don't pop to the floor.
            return false;
        }
    }

    FindFloorResult floor;
    FindFloor(inHandle, capsulePos, floor, &hit);

    if (!floor.IsWalkableFloor())
    {
        return false;
    }

    return true;
}
#pragma optimize("", on)
bool CharacterMovementSystemG::ShouldCheckForValidLandingSpot(const CharacterMovementCompReader& inHandle, float deltaTime, const Float3& delta, const HitResult& hit) const
{
    // See if we hit an edge of a surface on the lower portion of the capsule.
    // In this case the normal will not equal the impact normal, and a downward sweep may find a walkable surface on top of the edge.
    if (hit.Normal.y > MOVEMENT_SMALL_NUMBER && !hit.Normal.Equal(hit.ImpactNormal))
    {
        // Check validation of character capsule
        auto [capsuleRadius, capsuleHalfHeight] = GetScaledCapsuleSize(inHandle.GetEntityID());
        if (capsuleRadius <= 0.f || capsuleHalfHeight <= 0.f)
        {
            return false;
        }

        if (IsWithinEdgeTolerance(GetCapsuleWorldTranslation(inHandle.GetEntityID()), hit.ImpactPoint, capsuleRadius))
        {
            return true;
        }
    }
    return false;
}

Float3 CharacterMovementSystemG::LimitAirControl(const CharacterMovementCompReader& inHandle, float deltaTime, const Float3& fallAcceleration, const HitResult& hit, bool bCheckForValidLandingSpot) const
{
    Float3 result{fallAcceleration};

    if (hit.IsValidBlockingHit() && hit.Normal.y > sVerticalSlopeNormalY) 
    {
        if (!bCheckForValidLandingSpot || !IsValidLandingSpot(inHandle, hit.Location, hit))
        {
            // If acceleration is into the wall, limit contribution.
            if (fallAcceleration.Dot(hit.Normal) < 0.f)
            {
                // Allow movement parallel to the wall, but not into it because that may push us up.
                const Float3 normalXZ = hit.Normal.SafeNormalXZ();
                result = fallAcceleration.ProjectOnPlane(normalXZ);
            }
        }
    }
    else if (hit.bStartPenetrating)
    {
        // Allow movement out of penetration.
        return result.Dot(hit.Normal) > 0.f ? result : Float3::Zero();
    }

    return result;
}

void CharacterMovementSystemG::ProcessLanded(const CharacterMovementCompReader& inHandle, const HitResult& hit, float remainingTime, UInt32 Iterations)
{
    auto moveComp = mGameWorld->GetComponent<CharacterMovementComponentG>(inHandle.GetEntityID());

    // OnLanded
    OnLanded(moveComp.Write(), hit);

    if (IsFalling(inHandle))
    {
        SetPostLandedPhysics(inHandle, hit);
    }

    PerformPhysicsMovement(moveComp.Write(), remainingTime, Iterations);
}

void CharacterMovementSystemG::SetPostLandedPhysics(const CharacterMovementCompReader& inHandle, const HitResult& hit)
{
    auto moveComp = mGameWorld->GetComponent<CharacterMovementComponentG>(inHandle.GetEntityID());
    SetMovementMode(moveComp.Write(), MovementMode::MOVE_Walking);

    const Float3 preImpactAccel = inHandle->Acceleration + (IsFalling(inHandle) ? Float3(0.f, GetGravityY(inHandle), 0.f) : Float3::Zero());
    const Float3 preImpactVelocity = inHandle->Velocity;
    //ApplyImpactPhysicsForces
}

void CharacterMovementSystemG::DrawDebugShape(const CharacterMovementCompReader& inMoveComp) 
{
    auto characterEntity = inMoveComp.GetEntityID();
    // Check validation of character capsule
    auto [capsuleRadius, capsuleHalfHeight] = GetScaledCapsuleSize(characterEntity);
    if (capsuleRadius <= 0.f || capsuleHalfHeight <= 0.f)
    {
        return;
    }

    auto worldTransComp = mGameWorld->GetComponent<WorldTransformComponentG>(characterEntity);
    auto capsuleWorldTrans = GetCapsuleWorldTransform(characterEntity);
    Float3 capsuleBottom = Float3(capsuleWorldTrans.mTranslation) - Float3(0.f, capsuleHalfHeight + capsuleRadius, 0.f);

    auto primitiveSys = mGameWorld->GetGameSystem<PrimitiveRenderSystemG>();
   
    PrimitiveData primData;
    // World Space Velocity, Green
    PrimitiveGenerator::GenerateLine(&primData, capsuleBottom, capsuleBottom + inMoveComp->Velocity * 10.f);
    primitiveSys->DrawPrimitive(&primData, Float4x4::Identity(), cross::PrimitiveRenderSystemG::PrimitiveLook(cross::ColorRGBAf(0.0, 1.0, 0.0), PrimitiveDepth::SceneDepth, 5));

    // World Space Acceleration, Yellow
    PrimitiveGenerator::GenerateLine(&primData, capsuleBottom, capsuleBottom + inMoveComp->Acceleration * 10.f);
    primitiveSys->DrawPrimitive(&primData, Float4x4::Identity(), cross::PrimitiveRenderSystemG::PrimitiveLook(cross::ColorRGBAf(1.0, 1.0, 0.0), PrimitiveDepth::SceneDepth, 5));

    // Character Forward, Magenta
    Quaternion characterRot = mTransformSys->GetWorldRotation(worldTransComp.Read());
    PrimitiveGenerator::GenerateLine(&primData, capsuleBottom, capsuleBottom + characterRot.GetForwardVector() * 100.0f);
    primitiveSys->DrawPrimitive(&primData, Float4x4::Identity(), cross::PrimitiveRenderSystemG::PrimitiveLook(cross::ColorRGBAf(1.0, 0.0, 1.0), PrimitiveDepth::SceneDepth, 5));

    // Current Floor, Cyan
    if (inMoveComp->CurrentFloor.IsWalkableFloor())
    {
        PrimitiveGenerator::GeneratePlane(&primData, 50.0f);
        capsuleBottom = Float3(capsuleWorldTrans.mTranslation) + Float3(0.f, -capsuleHalfHeight - capsuleRadius, 0.f);
        Float4x4 trans = Float4x4::Compose(Float3::One(), Quaternion::Identity(), capsuleBottom);
        primitiveSys->DrawPrimitive(&primData, trans, cross::PrimitiveRenderSystemG::PrimitiveLook(cross::ColorRGBAf(0.0, 1.0, 1.0), PrimitiveDepth::SceneDepth, 5));
    }
}

SerializeNode CharacterMovementSystemG::SerializeCharacterMovementComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr)
{
    auto characterMovementCompPtr = static_cast<CharacterMovementComponentG*>(componentPtr);

    SerializeContext context;
    SerializeNode outNode = characterMovementCompPtr->Serialize(context);
    return outNode;
}

void CharacterMovementSystemG::DeserializeCharacterMovementComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr)
{
    if (json.IsNull())
        return;

    auto characterMovementCompPtr = static_cast<CharacterMovementComponentG*>(componentPtr);
    if (json.IsObject())
    {
        SerializeContext context;
        characterMovementCompPtr->Deserialize(json, context);
    }
}

void CharacterMovementSystemG::PostDeserializeCharacterMovementComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId) {}

}   // namespace cross
