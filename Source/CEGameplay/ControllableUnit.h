#pragma once
#include "CEGameplayAPI.h"
#include "ECS/Develop/Framework/Types.h"
#include "Configuration/CrossEngineConfig.h"
#include "CrossBase/Math/CrossMath.h"
#include "CrossBase/NodeTransform/NodeTransform.h"
#include "CrossPhysics/PhysicsEngine/PhysicsGeometry.h"
#include "CrossPhysics/PhysicsEngine/PhysicsShape.h"
#include "Runtime/GameWorld/TransformSystemG.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "Runtime/GameWorld/PhysicsSystemG.h"

namespace cross {
enum class CEMeta(Editor, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>uerts) CEGameplay_API ControllableUnitType
    {
        None,

        HostedOrbitFollowCamera,
        ScriptOrbitFollowCamera,
        
        HostedFppCamera,
        ScriptFppCamera,

        HostedFreeFlightCamera,
        ScriptFreeFlightCamera,

        HostedCharacterMovement,
        ScriptCharacterMovement,

        CurveController,

        VRCamera,

        ControllableAircraft,
        ControllableAirport,

        TestEnvControllableCamera,

        ControllableLandScape,

        ControllableTractor = 1000, 
        ControllableDocking,
        ControllableBirdStrike,
        ControllableTowbar,
        ControllableFfsVehicle,
        StatusSyncController,
        TrafficDemoController,
        ControllableMarshaller,
        ControllableGroundCrew,
};
}

namespace cross::cc {

class ControllableUnit;

using ControllableUnitProduceWithJson = std::function<std::unique_ptr<ControllableUnit>(const DeserializeNode& node, ecs::EntityID owner, GameWorld* world)>;

/* Provide navigation path finder with current agent's info */
class CEGameplay_API INavAgentInterface
{
public:
    /** Checks if the agent is actively following a navigation path */
    virtual bool IsFollowingAPath() const = 0;

    /**
     *  Retrieves Nav Agent's location
     */
    virtual Float3A GetNavAgentLocation() const = 0;

    /** Allow entity to specify additional offset (relative to NavLocation) when it's used as move goal */
    virtual Float3A GetMoveGoalOffset(const ControllableUnit* intController) const = 0;
};

/* Provide virtual operation value mapping from input event */
template<class T>
class CEGameplay_API IHostedCtrlInterface
{
public:
    virtual float GetOperationValue(T t, float deltaTime) const = 0;

    virtual bool IsOperationValidated(T t) const = 0;
};

using EntityPossessChanged = std::function<void(ecs::EntityID preEntity, ecs::EntityID curEntity)>;

/**
 * ControllableUnit is the base class of all controllers that can be possessed by players or AI.
 * They are the physical representations of players and creatures in a level.
 */
class CEGameplay_API CEMeta(Reflect, Puerts) ControllableUnit
{
public:
    bool Initialized = false;
    /** Entity currently being controlled by this controller. Use OnPossess() to take control of a entity */
    CEMeta(Serialize, Editor)
    CECSAttribute(JsonConverter(typeof(EntityIDStructConverter)))
    ecs::EntityID Entity = ecs::EntityID::InvalidHandle();

    virtual ~ControllableUnit() = default;
    //// Transform

    /** Reltv rotation with currently being controlled world space rotation */
    CEMeta(Serialize, Editor, ScriptReadWrite)
    Quaternion RotationOffset = Quaternion::Identity();

    /** Reltv translation with currently being controlled world space translation */
    CEMeta(Serialize, Editor, ScriptReadWrite)
    Float3 TranslationOffset = Float3::Zero();

//// Spawn

    /** Entity marking where this controller spawned in. */
    CEMeta(Serialize, Editor)
    CECSAttribute(JsonConverter(typeof(EntityIDStructConverter)))
    ecs::EntityID SpawnSpot = ecs::EntityID::InvalidHandle();

    /** Entity's validate check by the below boundary */
    CEMeta(Serialize, Editor)
    PhysicsGeometryBox InsideWorldBounds;

//// Collision

    /* Valid collision type & channel will Sweep using itself physic geometry shape */
    CEMeta(Serialize, Editor) CollisionType CollisionType = CollisionType::NoCollision;

    CEProperty(Editor)
    CECSAttribute(JsonConverter(typeof(CollisionMaskConverter)))
    CollisionMask BlockMask = CollisionMask::All();

//// Enable Translation & Rotation

    /** If true, this Entity's pitch will be updated to match Controller's ControlRotation pitch */
    CEMeta(Serialize, Editor, ScriptReadWrite)
    bool bUseControllerRotationPitch = true;

    /** If true, this Entity's yaw will be updated to match Controller's ControlRotation yaw */
    CEMeta(Serialize, Editor, ScriptReadWrite)
    bool bUseControllerRotationYaw = true;

    /** If true, this Entity's roll will be updated to match Controller's ControlRotation roll */
    CEMeta(Serialize, Editor, ScriptReadWrite)
    bool bUseControllerRotationRoll = true;

    /** If true, this Entity's roll will be updated to match Controller's ControlTranslate */
    CEMeta(Serialize, Editor, ScriptReadWrite)
    bool bUseControllerTranslate = true;

//// ControllableUnit finished

public:
    ControllableUnit(ecs::EntityID inSelf = ecs::EntityID::InvalidHandle())
        : mSelf(inSelf)
        , Entity(inSelf)
    {}

    CE_Virtual_Serialize_Deserialize;

    static std::unique_ptr<ControllableUnit> CreateInstance(GameWorld* inWorld, DeserializeNode const& inNode, ControllableUnitType inType, ecs::EntityID inOwner);

    /* Handle entities after Deserialize */
    virtual void Deserialize(GameWorld* inWorld, const DeserializeNode& in);

    /* Handle entities after Serialize */
    virtual void Serialize(GameWorld* inWorld, SerializeNode& out) const;

    /** Returns the particular entity's world pos */
    inline Float3A GetEntityPos(GameWorld* inWorld, ecs::EntityID target) const
    {
        auto sys = inWorld->GetGameSystem<TransformSystemG>();
        auto comp = inWorld->GetComponent<WorldTransformComponentG>(target);

        return sys->GetWorldTranslation(comp.Read());
    }
    inline TRSVector3Type GetEntityPosT(GameWorld* inWorld, ecs::EntityID target) const
    {
        auto sys = inWorld->GetGameSystem<TransformSystemG>();
        auto comp = inWorld->GetComponent<WorldTransformComponentG>(target);

        return sys->GetWorldTranslationT(comp.Read());
    }
    inline Float3A GetEntityScale(GameWorld* inWorld, ecs::EntityID target) const
    {
        auto sys = inWorld->GetGameSystem<TransformSystemG>();
        auto comp = inWorld->GetComponent<WorldTransformComponentG>(target);

        return sys->GetWorldScale(comp.Read());
    }
    /** Returns the particular entity's world rot */
    inline QuaternionA GetEntityRot(GameWorld* inWorld, ecs::EntityID target) const
    {
        auto sys = inWorld->GetGameSystem<TransformSystemG>();
        auto comp = inWorld->GetComponent<WorldTransformComponentG>(target);

        return sys->GetWorldRotation(comp.Read());
    }

    inline TRSQuaternionAType GetEntityRotT(GameWorld* inWorld, ecs::EntityID target) const
    {
        auto sys = inWorld->GetGameSystem<TransformSystemG>();
        auto comp = inWorld->GetComponent<WorldTransformComponentG>(target);

        return sys->GetWorldRotationT(comp.Read());
    }

    /** World Component's pos which give controllers a transform  */
    inline Float3A GetTargetPos(GameWorld* inWorld) const
    {
        auto pos_world = GetEntityPos(inWorld, Entity);
        auto rot_world = GetEntityRot(inWorld, Entity);
        return rot_world.Float3Rotate(TranslationOffset) + pos_world;
    }

    inline Double3 GetTargetPosT(GameWorld* inWorld) const
    {
        auto pos_world = GetEntityPosT(inWorld, Entity);
        auto rot_world = GetEntityRot(inWorld, Entity);
        return Double3(rot_world.Float3Rotate(TranslationOffset)) + pos_world;
    }

    /** World Component's rot which give controllers a transform  */
    inline QuaternionA GetTargetRot(GameWorld* inWorld) const
    {
        auto rot_world = GetEntityRot(inWorld, Entity);
        return RotationOffset * rot_world;
    }

    inline Float3A GetTargetProjectedForward(GameWorld* inWorld, Float3A inNormal) const
    {
        return GetTargetRot(inWorld).GetForwardVector().ProjectOnPlane(inNormal);
    }

    inline Float3A GetTargetProjectedRight(GameWorld* inWorld, Float3A inNormal) const
    {
        return GetTargetRot(inWorld).GetRightVector().ProjectOnPlane(inNormal);
    }

    inline Float3A GetTargetProjectedUp(GameWorld* inWorld, Float3A inNormal) const
    {
        return GetTargetRot(inWorld).GetUpVector().ProjectOnPlane(inNormal);
    }

    /** Get the final rotation calculated by controller */
    QuaternionA GetConstriantDesiredRot(GameWorld* inWorld) const
    {
        /** calculate target rotation first */
        auto desired_quat = GetDesiredWorldRotation(inWorld);

        if (bUseControllerRotationPitch && bUseControllerRotationYaw && bUseControllerRotationRoll)
            return desired_quat;

        auto desired_euler = Quaternion::QuaternionToEuler(desired_quat);

        /** grab current rotation before constraint happen */
        auto current_quat = GetTargetRot(inWorld);
        auto current_euler = Quaternion::QuaternionToEuler(current_quat);

        if (!bUseControllerRotationPitch)
        {
            desired_euler.x = current_euler.x;
        }

        if (!bUseControllerRotationYaw)
        {
            desired_euler.y = current_euler.y;
        }

        if (!bUseControllerRotationRoll)
        {
            desired_euler.z = current_euler.z;
        }

        return Quaternion::EulerToQuaternion(desired_euler);
    }

    /** Get the final translation calculated by controller */
    Float3A GetConstriantDesiredPos(GameWorld* inWorld) const
    {
        /** calculate target translation first */
        if (bUseControllerTranslate)
            return GetDesiredWorldTranslation(inWorld);

        return GetTargetPos(inWorld);
    }

    void ToSpawn(float inDuration, GameWorld* inWorld, ecs::EntityID inSpawnSpot = ecs::EntityID::InvalidHandle());

    /** Overridable native function for while Handles attaching this controller to the specified entity. */
    virtual void Possess(GameWorld* inWorld, ecs::EntityID inEntity);
    /** Overridable native function for when this controller unpossesses its entity. */
    virtual void UnPossess(GameWorld* inWorld);
    /** Freeze entity - stop sounds, animations, physics, weapon firing, realized something etc by Subclass logic */
    virtual void TurnOff(bool deactive) {}
    /* Processes player input then calculate Desired transform. */
    virtual void Tick(float deltaTime, GameWorld* inWorld) {}
    /* Processes after controller itself tick immediately. */
    virtual void PostTick(float deltaTime, GameWorld* inWorld) {}
    /** Returns whether this Controller is a local controller. */
    virtual bool IsLocalController() const { return true; }
    /** Returns whether this Controller handled by human.  */
    virtual bool IsPlayerController() const { return true; }
    /** Called when the entity is outside the hard limit on world bounds */
    virtual void OutsideWorldBounds(GameWorld* inWorld);

protected:

    // empty for reference
    static CEHashSet<ecs::EntityID> sStaticEmpty;

    /**
     * Get the desired rotation. This is the full aim rotation, which may be different than a camera orientation (for example in a third person view),
     * and may differ from the rotation of the controlled Pawn (which may choose not to visually pitch or roll, for example).
     */
    virtual QuaternionA GetDesiredWorldRotation(GameWorld* inWorld) const { return GetTargetRot(inWorld); }

    /** Get the desired translation. */
    virtual Float3A GetDesiredWorldTranslation(GameWorld* inWorld) const { return GetTargetPos(inWorld); }
    /** Get physic geometry shape for calculate  from cur pos to desired pos */
    virtual PhysicsGeometryBase* GetPhysicShapeForSweep(GameWorld* inWorld) const;

protected:
    /** Delegate broadcasted when possessing a new entity or unpossessing one */
    EntityPossessChanged mOnEntityPossessed = nullptr;

    /** Used to prevent re-entry of OutsideWorldBounds event. */
    bool mProcessingOutsideWorldBounds = false;

    /** Processed into spawn point current cursor */
    float mLerpingIntoSpawnPointLeftSec = -1.0f;

    /** Lerp processed ratio into spawn point, negative value means disable */
    float mLerpingIntoSpawnPointTotalSec = -1.0f;

    /** */
    Float3A mLerpingStartSpawnTranslation = Float3A::Zero();

    /** */
    Quaternion mLerpingStartSpawnRotation = Quaternion::Identity();

    /** The entities that got collisions with in the last frame */
    std::vector<ecs::EntityID> mLastCollisionedEntities;

    /** Entity which holding current controller, may diff with being controlled entity */
    const ecs::EntityID mSelf = ecs::EntityID::InvalidHandle();

protected:
    // Static: One and the same controller for all function calls.
    static std::map<ControllableUnitType, ControllableUnitProduceWithJson>& GetRegistry()
    {
        static std::map<ControllableUnitType, ControllableUnitProduceWithJson> registry;
        return registry;
    }

    template<class T> friend class ControllableUnitRegister;
};

template<class T>
class ControllableUnitRegister
{
private:
    static inline bool RegisterType()
    {
        const auto& cuType = T::GetType();
        if (cuType == ControllableUnitType::None)
            return false;

        auto& registery = ControllableUnit::GetRegistry();
        registery[cuType] = std::bind(&ControllableUnitRegister::Produce, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);

        return true;
    }

    static std::unique_ptr<ControllableUnit> Produce(const DeserializeNode& node, ecs::EntityID inOwner, GameWorld* world)
    {
        std::unique_ptr<ControllableUnit> instance = std::make_unique<T>(inOwner);
        if (node.IsObject())
            instance->Deserialize(world, node);
        return instance;
    }

private:
    static const inline bool sRegistered = RegisterType();
};

}
