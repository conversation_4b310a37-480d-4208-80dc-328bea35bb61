#pragma once
#include "CrossBase/Math/CrossMath.h"
#include "ECS/Develop/Framework.h"
#include "ECS/Develop/Framework/Types.h"
#include "CECommon/Common/GameSystemBase.h"
#include "RenderEngine/PrimitiveGenerator.h"
#include "CharacterMovementComponent.h"
#include "Runtime/GameWorld/TransformSystemG.h"
#include "Runtime/GameWorld/PrimitiveRenderSystemG.h"
#include "MovementSystemBaseG.h"
#if SUPPORT_LUA
#include "ScriptModule.h
#endif
#include "Runtime/GameWorld/ScriptComponentG.h"

namespace cross {
#define ENABLE_CHARACTER_MOVEMENT_TEST_CODE 1
enum class CharacterMovementEventType
{
    None = 0,
    Jump,
    Landed,
    StartCrouch,
    EndCrouch,
    MovementBlocked
};

struct CharacterMovementEventData
{
    CharacterMovementEventType mType;
    ecs::EntityID mCharacterEntity;
};

using CharacterMovementEvent = SystemEvent<CharacterMovementEventData>;

class CEGameplay_API CharacterMovementSystemG final : public MovementSystemBaseG, public SystemEventManager<CharacterMovementEvent>
{
    CESystemInternal(ComponentType = CharacterMovementComponentG)
public:
    using CharacterMovementCompHandle = ecs::ComponentHandle<CharacterMovementComponentG>;
    DEFINE_COMPONENT_READER_WRITER(CharacterMovementComponentG, CharacterMovementCompReader, CharacterMovementCompWriter)

    CEFunction(Reflect)
    static CharacterMovementSystemG* CreateInstance();

    virtual void Release() override
    {
        delete this;
    };

    virtual void NotifyAddRenderSystemToRenderWorld() override {}

    virtual RenderSystemBase* GetRenderSystem() override
    {
        return nullptr;
    }

    virtual void NotifyEvent(const SystemEventBase& event, UInt32& flag) override;

    virtual void OnFirstUpdate(FrameParam* frameParam) override;

    CEFunction(Reflect)
    virtual void OnBuildUpdateTasks(FrameParam* frameParam) override;

    // For Testing Only
#ifdef ENABLE_CHARACTER_MOVEMENT_TEST_CODE
    Float3 AccumlateScalarTargetVelocity(const CharacterMovementCompWriter& inHandle, float elapsedTime);

    /* set aiming entity for current character movement  */
    CEFunction(Script)
    void SetAimEntity(const CharacterMovementCompWriter& inHandle, cross::ecs::EntityID inAimingEntity);
    CEFunction(Script)
    void SetAngleBetweenActorForwardAndCurrentVelocity(const CharacterMovementCompWriter& inHandle, float yawOffset);
    CEFunction(Script)
    void SetRotationMode(const CharacterMovementCompWriter& inHandle, UInt32 inMode);
    /* Returns the euler angle between aiming entity's projected forward & actor's velocity */
    CEFunction(Script)
    Float3A GetAngleBetweenAimForwardAndCurrentVelocity(const CharacterMovementCompReader& inHandle);

    void CalculateCurrentRotation(const CharacterMovementCompReader& inHandle, float elapsedTime);
#endif

public:
    CEFunction(Editor)
    void GetCharacterMovementComponent(const CharacterMovementCompReader& inHandle, cross::CharacterMovementComponentG& outValue) const;
    CEFunction(Editor)
    void SetCharacterMovementComponent(const CharacterMovementCompWriter& inHandle, const cross::CharacterMovementComponentG& inValue);

    ////// Character Capsule
    /* Get Capsule from character entity's Physics Component, collect from extra added shapes */
    virtual const PhysicsGeometryBase* GetPhysicsGeometry(ecs::EntityID entity) const override;
    /* Return Scaled <CapsuleRadius, CapsuleHalfHeight> */
    std::pair<float, float> GetScaledCapsuleSize(ecs::EntityID entity) const;
    /* Return UnScaled <CapsuleRadius, CapsuleHalfHeight> */
    std::pair<float, float> GetUnScaledCapsuleSize(ecs::EntityID entity) const;
    TRS GetCapsuleWorldTransform(ecs::EntityID entity) const;
    TRSVector3Type GetCapsuleWorldTranslation(ecs::EntityID entity) const;
    void SetCharacterCapsuleRadius(ecs::EntityID entity, float capsuleRadius);
    void SetCharacterCapsuleHalfHeight(ecs::EntityID entity, float capsuleHalfHeight);

    ////// Movement Parameters
    CEFunction(Editor, Script)
    float GetGravityScale(const CharacterMovementCompReader& inHandle) const;
    CEFunction(Editor, Script)
    void SetGravityScale(const CharacterMovementCompWriter& inHandle, float gravityScale);
    CEFunction(Editor, Script)
    virtual float GetGravityY(const CharacterMovementCompReader& inHandle) const;

    CEFunction(Editor, Script)
    virtual float GetMaxSpeed(const CharacterMovementCompReader& inHandle) const;
    CEFunction(Script)
    virtual bool IsExceedingMaxSpeed(const CharacterMovementCompReader& inHandle, float maxSpeed) const;
    CEFunction(Editor, Script)
    void SetMaxWalkSpeed(const CharacterMovementCompWriter& inHandle, float maxWalkSpeed);
    CEFunction(Editor, Script)
    void SetMaxWalkSpeedCrouched(const CharacterMovementCompWriter& inHandle, float maxCrouchSpeed);
    CEFunction(Editor, Script)
    void SetMaxFallingSpeed(const CharacterMovementCompWriter& inHandle, float maxFallingSpeed);
    CEFunction(Editor, Script)
    virtual float GetMaxAcceleration(const CharacterMovementCompReader& inHandle) const;
    CEFunction(Editor, Script)
    virtual float GetMaxBrakingDeceleration(const CharacterMovementCompReader& inHandle) const;
    CEFunction(Editor, Script)
    virtual float GetCrouchedHalfHeight(const CharacterMovementCompReader& inHandle) const;
    CEFunction(Editor, Script)
    void SetCrouchedHalfHeight(const CharacterMovementCompWriter& inHandle, float halfHeight);
    CEFunction(Editor, Script)
    virtual float GetWalkableFloorAngle(const CharacterMovementCompReader& inHandle) const;
    CEFunction(Editor, Script)
    void SetWalkableFloorAngle(const CharacterMovementCompWriter& inHandle, float walkableFloorAngle);
    CEFunction(Editor, Script)
    virtual float GetWalkableFloorY(const CharacterMovementCompReader& inHandle) const;

    /* Return true if the hit result should be considered a walkable surface for the character. */
    virtual bool IsWalkableFloor(const CharacterMovementCompReader& inHandle, const HitResult& hit) const;

    /* Returns The distance from the edge of the capsule within which we don't allow the character to perch on the edge of a surface. */
    CEFunction(Editor, Script)
    virtual float GetPerchRadiusThreshold(const CharacterMovementCompReader& inHandle) const;
    /*
     * Returns the radius within which we can stand on the edge of a surface without falling (if this is a walkable surface).
     * Simply computed as the capsule radius minus the result of GetPerchRadiusThreshold().
     */
    CEFunction(Script)
    virtual float GetValidPerchRadius(const CharacterMovementCompReader& inHandle) const;
    /* Check if the result of sweep can be a valid location to perch, in which case we use CalculatePerchResult() to validate the location */
    virtual bool ShouldCalculatePerchResult(const CharacterMovementCompReader& inHandle, const HitResult& hit, bool checkRadius) const;
    /*
     * Compute the sweep result of the smaller capsule with radius specified by GetValidPerchRadius(),
     * and return true if the sweep contacts a valid walkable normal within MaxFloorDist of Hit.ImpactPoint.
     * This may be used to determine if the capsule can or cannot stay at the current location if perched on the edge of a small ledge or unwalkable surface.
     * Note: Only returns a valid result if ShouldComputePerchResult() returned true for the supplied hit value.
     *
     * @param TestRadius: Radius to use for the sweep, usually GetValidPerchRadius().
     * @param Hit: Result of the last sweep test before the query.
     * @param MaxFloorDist: Max distance to floor allowed by perching, from the supplied contact point (InHit.ImpactPoint).
     * @param OutPerchFloorResult: Contains the result of the perch floor test.
     * @return True if the current location is a valid spot at which to perch.
     */
    virtual bool CalculatePerchResult(const CharacterMovementCompReader& inHandle, float testRadius, const HitResult& hit, float maxFloorDist, FindFloorResult& outPerchFloorResult) const;

    CEFunction(Script)
    Float3 GetCurrentVelocityInWorldSpace(const CharacterMovementCompReader& inHandle) const;
    CEFunction(Script)
    Float3 GetCurrentVelocityInRootSpace(const CharacterMovementCompReader& inHandle) const;
    CEFunction(Script)
    Float3 GetCurrentAcceleration(const CharacterMovementCompReader& inHandle) const;
    CEFunction(Script)
    Float3 GetLastUpdateLocation(const CharacterMovementCompReader& inHandle) const;
    CEFunction(Script)
    Quaternion GetLastUpdateRotation(const CharacterMovementCompReader& inHandle) const;
    CEFunction(Script)
    Float3 GetLastUpdateVelocityInWorldSpace(const CharacterMovementCompReader& inHandle) const;

    const FindFloorResult& GetCurrentFloor(const CharacterMovementCompReader& inHandle) const;

    /* Update position based on Floor movement */
    virtual void UpdateBasedOnFloorMovement(const CharacterMovementCompWriter& inHandle);
    /* Update LastFloorRotation and LastFloorLocation if there is a valid floor, and store the relative location/rotation if necessary. */
    virtual void SaveFloorMovement(const CharacterMovementCompWriter& inHandle);

    ////// Movement States
    virtual void SetMovementMode(const CharacterMovementCompWriter& inHandle, MovementMode inMoveMode);
    CEFunction(Script)
    virtual bool IsMovingOnGround(const CharacterMovementCompReader& inHandle) const;
    CEFunction(Script)
    virtual bool IsFalling(const CharacterMovementCompReader& inHandle) const;
    CEFunction(Script)
    virtual bool IsCrouching(const CharacterMovementCompReader& inHandle) const;
    CEFunction(Script)
    virtual bool HasAnimRootMotion(const CharacterMovementCompReader& inHandle) const;

    ////// Movement Events
    /* Event called when the character has just started jumping */
    virtual void OnJumped(const CharacterMovementCompWriter& inHandle);
    /* Event called when landing from falling */
    virtual void OnLanded(const CharacterMovementCompWriter& inHandle, const HitResult& hit);
    /*
     * Event called when Character crouches.
     * 
     * @param HalfHeightAdjust: difference between default collision half-height, and actual crouched capsule half-height.
     * @param ScaledHalfHeightAdjust: difference after component scale is taken in to account.
     */
    virtual void OnStartCrouch(const CharacterMovementCompWriter& inHandle, float halfHeightAdjust);
    /* 
     * Event called when Character stops crouching. 
     * 
     * @param HalfHeightAdjust: difference between default collision half-height, and actual crouched capsule half-height.
     * @param ScaledHalfHeightAdjust: difference after component scale is taken in to account.
     */
    virtual void OnEndCrouch(const CharacterMovementCompWriter& inHandle, float halfHeightAdjust);
    /* Event called when the character's movement is blocked */
    virtual void OnMovementBlocked(const CharacterMovementCompWriter& inHandle, const HitResult& impact);
    /* Called after MovementMode has changed. Base implementation does special handling for starting certain modes. */
    virtual void OnMovementModeChanged(const CharacterMovementCompWriter& inHandle, MovementMode prevMode);
    
    ////// Process Input
    virtual void AddInputVector(const CharacterMovementCompWriter& inHandle, const Float3& inputVector);
    virtual Float3 ConsumeInputVector(const CharacterMovementCompWriter& inHandle);
    CEFunction(Script)
    Float3 GetLastInputVector(const CharacterMovementCompWriter& inHandle) const;
    
    ////// Physics movement
    virtual void PerformPhysicsMovement(const CharacterMovementCompWriter& inHandle, float deltaSecs, UInt32 iterations);
    virtual void PerformPhysicsWalking(const CharacterMovementCompWriter& inHandle, float deltaSecs, UInt32 iterations);
    virtual void PerformPhysicsFalling(const CharacterMovementCompWriter& inHandle, float deltaSecs, UInt32 iterations);

    float GetSimulationTimeStep(const CharacterMovementCompReader& inHandle, float remainingTime, UInt32 iterations) const;
    /* Slows towards stop. */
    virtual void ApplyBrakingToVelocity(const CharacterMovementCompWriter& inHandle, float deltaSecs, float friction, float brakingDeceleration);
    /* Updates Velocity and Acceleration based on the current state, applying the effects of friction and acceleration or deceleration. Does not apply gravity. */
    CEFunction(Script)
    virtual void CalculateVelocity(const CharacterMovementCompWriter& inHandle, float deltaSecs, float friction, float brakingDeceleration);

    /* Sweeps vertically to find the floor for the capsule at the given location. Will attempt to perch if ShouldCalculatePerchResult() returns true for the downward sweep result. */
    virtual void FindFloor(const CharacterMovementCompReader& inHandle, const TRSVector3Type& capsulePos, FindFloorResult& outFloorResult, const HitResult* downwardSweepResult = nullptr) const;

    /* Calculate distance to the floor from bottom sphere of capsule and store the result in outFloorResult.
     * This distance is the swept distance of the capsule to the first point impacted by the lower hemisphere.
     * 
     * @param sweepDist: If non-zero, max distance to use when sweeping a capsule downwards for the test.
     * @param SweepRadius: The radius to use for sweep tests. Should be <= capsule radius.
     * @param downwardSweepResult: If non-null and it contains valid blocking hit info, this will be used as the result of a downward sweep test instead of doing it as part of the update.
     * @param outFloorResult: Result of the floor check. The HitResult will contain the valid sweep or line test upon success, or the result of the sweep upon failure.
     */
    virtual void CalculateFloorDistance(const CharacterMovementCompReader& inHandle, const TRSVector3Type& capsulePos, 
        float lineTraceDist, float sweepDist, float sweepRadius, FindFloorResult& outFloorResult, const HitResult* downwardSweepResult = nullptr) const;

    /* Adjust distance from floor, trying to maintain a slight offset from the floor when walking (based on CurrentFloor) */
    virtual void AdjustFloorHeight(const CharacterMovementCompWriter& inHandle);

    ////// Step Up
    /* Returns true if we can step up on the entity in the given HitResult. */
    virtual bool CanStepUp(const CharacterMovementCompReader& inHandle, const HitResult& hit) const;
    /* Step up stair or slope */
    virtual bool StepUp(const CharacterMovementCompReader& inHandle, const Float3& gravityDir, const Float3& inDelta, const HitResult& hit, StepDownResult* outStepDownResult = nullptr);

    ////// Process Ledges
    /* Returns whether this character is currently allowed to walk off ledges */
    CEFunction(Script)
    virtual bool CanWalkOffLedges(const CharacterMovementCompReader& inHandle) const;
    virtual void HandleWalkingOffLedges(const CharacterMovementCompReader& inHandle, const Float3& prevFloorImpactNormal, const Float3& prevFloorContactNormal, const TRSVector3Type& prevCapsulePos, float deltaSecs) const;
    /* Returns true if there is a suitable floor SideStep from current position. */
    virtual bool CheckLedgeDirection(const CharacterMovementCompReader& inHandle, const TRSVector3Type& oldLocation, const Float3& sideStep, const Float3& gravityDir) const;
    /* inDelta is the current move delta (which ended up going over a ledge). return new delta which moves along the ledge */
    virtual Float3 GetLedgeMove(const CharacterMovementCompReader& inHandle, const TRSVector3Type& oldLocation, const Float3& delta, const Float3& gravityDir) const;

    ////// Crouch
    CEFunction(Script)
    virtual bool CanCrouch(const CharacterMovementCompReader& inHandle) const;
    CEFunction(Script)
    virtual void Crouch(const CharacterMovementCompWriter& inHandle);
    CEFunction(Script)
    virtual void UnCrouch(const CharacterMovementCompWriter& inHandle);

    ////// Jump & Falling
    CEFunction(Editor, Script)
    virtual UInt32 GetJumpMaxCount(const CharacterMovementCompReader& inHandle) const;
    CEFunction(Editor, Script)
    virtual float GetJumpMaxHoldTime(const CharacterMovementCompReader& inHandle) const;

    /* Compute the max jump height based on the JumpYVelocity velocity and gravity. This does not take into account the MaxJumpHoldTime. */
    CEFunction(Script)
    virtual float GetMaxJumpHeight(const CharacterMovementCompReader& inHandle) const;
    /* Compute the max jump height based on the JumpYVelocity velocity and gravity. This does take into account the MaxJumpHoldTime. */
    CEFunction(Script)
    virtual float GetMaxJumpHeightWithJumpTime(const CharacterMovementCompReader& inHandle) const;
    /*
     * If we have a movement base, get the velocity that should be imparted by that base, usually when jumping off of it.
     * Only applies the components of the velocity enabled by ImpartBaseVelocityX, ImpartBaseVelocityY, ImpartBaseVelocityZ.
     */
    CEFunction(Script)
    virtual Float3 GetImpartedMovementBaseVelocity(const CharacterMovementCompReader& inHandle) const;

    /* Check if the character can jump in the current state. */
    CEFunction(Script)
    virtual bool CanJump(const CharacterMovementCompReader& inHandle) const;
    /* Trigger jump if jump button has been pressed. */
    virtual void CheckJumpInput(const CharacterMovementCompWriter& inHandle, float deltaSecs);
    /* Update jump input state after having checked input. */
    virtual void ClearJumpInput(const CharacterMovementCompWriter& inHandle, float deltaSecs);
    /* Perform jump. Called when a jump has been detected because IsPressedJump was true. Checks CanJump(). */
    virtual bool DoJump(const CharacterMovementCompWriter& inHandle);

    /*
     * Make the character jump on the next update.
     * If you want your character to jump according to the time that the jump key is held, then you can set JumpMaxHoldTime to some non-zero value. 
     * Make sure in this case to call StopJumping() when you want the jump's Y-velocity to stop being applied (such as on a button up event), 
     * otherwise the character will carry on receiving the velocity until JumpKeyHoldTime reaches JumpMaxHoldTime.
     */
    CEFunction(Script)
    virtual void Jump(const CharacterMovementCompWriter& inHandle);
    /*
     * Stop the character from jumping on the next update.
     * Call this from an input event (such as a button 'up' event) to cease applying jump Y-velocity. 
     * If this is not called, then jump Y-velocity will be applied until JumpMaxHoldTime is reached.
     */
    CEFunction(Script)
    virtual void StopJumping(const CharacterMovementCompWriter& inHandle);

    /* Marks character as not trying to jump */
    virtual void ResetJumpState(const CharacterMovementCompWriter& inHandle);

    /* Check if character is falling */
    virtual bool CheckFall(const CharacterMovementCompReader& inHandle, const FindFloorResult& oldFloor, const Float3& inDelta, 
        const TRSVector3Type& capsuleOldPos, float remainingTime, float tickTime, UInt32 iterations, bool mustJump);
    /* Transition from walking to falling */
    virtual void StartFalling(const CharacterMovementCompWriter& inHandle, UInt32 iterations, float remainingTime, float tickTime, const Float3& inDelta, const TRSVector3Type& capsuleOldPos);
    /*
     * Get the lateral acceleration to use during falling movement. The Y component of the result is ignored.
     * Default implementation returns current Acceleration value modified by GetAirControl(), with Y component removed,
     * with magnitude clamped to GetMaxAcceleration().
     */
    virtual Float3 GetFallingLateralAcceleration(const CharacterMovementCompReader& inHandle, float deltaTime) const;
    /* Compute new falling velocity from given velocity and gravity. Applies the limits of the current Physics Volume's TerminalVelocity. */
    virtual Float3 CalculateNewFallVelocity(const CharacterMovementCompReader& inHandle, const Float3& initialVelocity, const Float3& gravity, float deltaTime) const;

    /* Verify that the supplied hit result is a valid landing spot when falling. */
    virtual bool IsValidLandingSpot(const CharacterMovementCompReader& inHandle, const TRSVector3Type& capsulePos, const HitResult& hit) const;

    /*
     * Determine whether we should try to find a valid landing spot after an impact with an invalid one (based on the Hit result).
     * For example, landing on the lower portion of the capsule on the edge of geometry may be a walkable surface, but could have reported an unwalkable impact normal.
     */
    virtual bool ShouldCheckForValidLandingSpot(const CharacterMovementCompReader& inHandle, float deltaTime, const Float3& delta, const HitResult& hit) const;

    /*
     * Limits the air control to use during falling movement, given an impact while falling.
     * 
     * @param DeltaTime: Time step for the current update.
     * @param FallAcceleration: Acceleration used during movement.
     * @param Hit	: Result of impact.
     * @param bCheckForValidLandingSpot If true, will use IsValidLandingSpot() to determine if HitResult is a walkable surface. If false, this check is skipped.
     * @return Modified air control acceleration to use during falling movement.
     */
    virtual Float3 LimitAirControl(const CharacterMovementCompReader& inHandle, float deltaTime, const Float3& fallAcceleration, const HitResult& hit, bool bCheckForValidLandingSpot) const;

    /* Handle landing against Hit surface over remaingTime and iterations, calling SetPostLandedPhysics() and starting the new movement mode. */
    virtual void ProcessLanded(const CharacterMovementCompReader& inHandle, const HitResult& hit, float remainingTime, UInt32 Iterations);
    /* Use new physics after landing. Defaults to walking. */
    virtual void SetPostLandedPhysics(const CharacterMovementCompReader& inHandle, const HitResult& hit);

public:
    static SerializeNode SerializeCharacterMovementComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr);
    static void DeserializeCharacterMovementComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr);
    static void PostDeserializeCharacterMovementComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId);

protected:
    CharacterMovementSystemG();
    virtual ~CharacterMovementSystemG();

    virtual Float3 ConstrainInput(const CharacterMovementCompReader& inHandle, const Float3& inputAcceleration) const;
    virtual void ApplyInputToAcceleration(const CharacterMovementCompWriter& inHandle, const Float3& inputAcceleration);

    ////// Root Motion Related
    virtual void ProcessRootMotionFromAnimation(const CharacterMovementCompWriter& inHandle);
    /* Convert local space root motion to world space */
    virtual void ConvertLocalRootMotionToWorld(const CharacterMovementCompWriter& inHandle);
    /* Calculate velocity from world space root motion */
    virtual void CalculateRootMotionVelocity(const CharacterMovementCompWriter& inHandle, float deltaSecs);
    virtual Float3 ConstrainRootMotionVelocity(const CharacterMovementCompReader& inHandle) const;
    /* Apply world space root motion translation to velocity */
    virtual void ApplyRootMotionToVelocity(const CharacterMovementCompWriter& inHandle, float deltaSecs);
    /* Apply world space root motion rotation to Character */
    virtual void ApplyRootMotionRotation(const CharacterMovementCompWriter& inHandle);

    ////// Walking On Ground
    /*
     * Adjusts velocity when walking so that Y velocity is zero.
     * When MaintainHorizontalGroundVelocity is false, also rescales the velocity vector to maintain the original magnitude, but in the horizontal direction.
     */
    virtual void MaintainHorizontalGroundVelocity(const CharacterMovementCompWriter& inHandle);
    /*
     * If on a walkable surface, this returns a vector that moves parallel to the surface.
     * The magnitude may be scaled if MaintainHorizontalGroundVelocity is true. If a ramp vector can't be computed, this will just return Delta.
     */
    virtual Float3 CalculateGroundMoveDelta(const CharacterMovementCompReader& inHandle, const Float3& inDelta, const HitResult& rampHit, bool hitFromRayCast) const;

    /*
     * Limit the slide vector when falling if the resulting slide might boost the character faster upwards. return new slide result.
     * 
     * @param SlideResult: Vector of movement for the slide (usually the result of ComputeSlideVector)
     * @param InDelta: Original attempted move
     * @param PosRatio:	 Amount of move to apply (between 0 and 1).
     * @param Normal: Normal opposed to movement. Not necessarily equal to Hit.Normal (but usually is).
     */
    virtual Float3 HandleSlopeBoosting(const CharacterMovementCompReader& inHandle, const Float3& slideResult, const Float3& inDelta, float posRatio, const Float3& normal, const HitResult& hit) const;

    /*
     * Move along the floor, using CurrentFloor and CalculateGroundMoveDelta() to get a movement direction.
     * If a second walkable surface is hit, it will also be moved along using the same approach.
     */
    virtual void MoveAlongFloor(const CharacterMovementCompWriter& inHandle, float deltaSecs, StepDownResult* outStepResult = nullptr);

    ////// Draw Debug Info
    void DrawDebugShape(const CharacterMovementCompReader& inHandle);

    ////// Character Movement Event Functions Begin
    template<typename... Args>
    void OnCharacterMovementEvent(ecs::EntityID characterEntity, CharacterMovementEventType type, Args&&... args)
    {
        #if SUPPORT_LUA
        const auto& scriptHandle = mGameWorld->GetComponent<ScriptComponentG>(characterEntity);

        if (!scriptHandle.IsValid() || !scriptHandle.Read()->mOnCreateFinished)
            return;

        auto scope = ScriptModule::Instance().Enter();
        try
        {
            auto object = scriptHandle.Read()->mScriptInstance->get();

            script::Local<script::Value> func;
            switch (type)
            {
            case CharacterMovementEventType::Jump:
                func = object.get("CharacterMovement_OnJumped");
                if (func.isFunction())
                {
                    func.asFunction().call(object, std::forward<Args>(args)...);
                }
                break;
            case CharacterMovementEventType::Landed:
                func = object.get("CharacterMovement_OnLanded");
                if (func.isFunction())
                {
                    func.asFunction().call(object, std::forward<Args>(args)...);
                }
                break;
            case CharacterMovementEventType::StartCrouch:
                func = object.get("CharacterMovement_OnStartCrouch");
                if (func.isFunction())
                {
                    func.asFunction().call(object, std::forward<Args>(args)...);
                }
                break;
            case CharacterMovementEventType::EndCrouch:
                func = object.get("CharacterMovement_OnEndCrouch");
                if (func.isFunction())
                {
                    func.asFunction().call(object, std::forward<Args>(args)...);
                }
                break;
            case CharacterMovementEventType::MovementBlocked:
                func = object.get("CharacterMovement_OnMovementBlocked");
                if (func.isFunction())
                {
                    func.asFunction().call(object, std::forward<Args>(args)...);
                }
                break;
            default:
                LOG_WARN("Script System do not support this event({})", type);
                break;
            }
        }
        catch (script::Exception const& e)
        {
            LOG_ERROR("ScriptEngine Exception:{}\n{}", e.message(), e.stacktrace());
        }
        catch (std::exception const& e)
        {
            LOG_ERROR("std exception:{}", e.what());
        }
        catch (...)
        {
            LOG_ERROR("lua other exception:{}", scriptHandle.Read()->mScriptPath);
        }
        #endif
    }
    ////// Character Movement Event Functions End

protected:
    ////// MovementSystemBase Interface begin
    virtual Float3 ComputeSlideVector(ecs::EntityID inEntity, const Float3& inDelta, float posRatio, const Float3& normal, const HitResult& hit) const override;

    virtual float SlideAlongSurface(ecs::EntityID inEntity, const Float3& inDelta, float positionRatio, const Float3& normal, HitResult& outHitResult, bool handleImpact = false) override;

    virtual void TwoSurfaceAdjust(ecs::EntityID inEntity, Float3& outDelta, const HitResult& hit, const Float3& oldHitNormal) const override;

    /* Handle a blocking impact. Calls ApplyImpactPhysicsForces() for the hit, if bEnablePhysicsInteraction is true. */
    virtual void HandleImpact(ecs::EntityID inEntity, const HitResult& hit, float posRatio = 0.f, const Float3& moveDelta = Float3::Zero()) override;
    ////// MovementSystemBase Interface end

protected:
    /*
     * Return true if the XOZ distance to the impact point is inside the edge tolerance (CapsuleRadius minus a small rejection threshold).
     * Useful for rejecting adjacent hits when finding a floor or landing spot.
     */
    virtual bool IsWithinEdgeTolerance(const TRSVector3Type& capsuleLocation, const TRSVector3Type& impactPoint, float capsuleRadius) const
    {
        TRSScalarType distFromCenterSq = (capsuleLocation - impactPoint).LengthSquaredXZ();
        float reducedRadius = MathUtils::Max(sSweepEdgeRejectThres + MOVEMENT_SMALL_NUMBER, capsuleRadius - sSweepEdgeRejectThres);
        return distFromCenterSq < MathUtils::Square(reducedRadius);
    }

private:
    /* 
     * Minimum delta time considered when ticking. Delta times below this are not considered. 
     * This is a very small non-zero value to avoid potential divide-by-zero in simulation code. 
     */
    const float sMinTickTime = 1e-6f;

    /* Minimum acceptable distance for Character capsule to float above floor when walking. */
    const float sMinFloorDist = 1.9f;
    /* Maximum acceptable distance for Character capsule to float above floor when walking. */
    const float sMaxFloorDist = 2.4f;
    /* Reject sweep impacts that are this close to the edge of the vertical portion of the capsule when performing vertical sweeps, and try again with a smaller capsule. */
    const float sSweepEdgeRejectThres = 0.15f;
    /* Slope is vertical if Abs(Normal.Y) <= this threshold. Accounts for precision problems that sometimes angle normals slightly off horizontal for vertical surface. */
    const float sVerticalSlopeNormalY = 0.001f;

    PhysicsSystemG* mPhysicsSys{nullptr};

    TransformSystemG* mTransformSys{nullptr};
};

}
