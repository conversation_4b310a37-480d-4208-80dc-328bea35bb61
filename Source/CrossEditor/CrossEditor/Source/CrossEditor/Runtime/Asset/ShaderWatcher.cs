using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading.Tasks;

namespace CrossEditor
{
    public class ShaderWatcher
    {
        private static ShaderWatcher gInstance = null;
        public static ShaderWatcher Instance
        {
            get
            {
                if (gInstance == null)
                {
                    gInstance = new ShaderWatcher();
                }
                return gInstance;
            }
        }

        public FileSystemWatcher mProjectWatcher;
        public FileSystemWatcher mEngineWatcher;
        public List<string> mFiles = new List<string>();
        public List<string> mModuleFiles = new List<string>();

        private delegate void ImportShaderAsync();
        private int Version = 0;
        private int CurrentVersion = 0;

        public void Initialize(string ProjectDirectory, string EngineResourceDirectory)
        {
            mProjectWatcher = new FileSystemWatcher();
            mProjectWatcher.Filters.Add("*.shader");
            mProjectWatcher.Filters.Add("*.compute");
            mProjectWatcher.Filters.Add("*.h");
            mProjectWatcher.Filters.Add("*.hlsl");
            mProjectWatcher.Path = ProjectDirectory;
            mProjectWatcher.Changed += new FileSystemEventHandler(OnChanged);
            mProjectWatcher.Created += new FileSystemEventHandler(OnChanged);
            mProjectWatcher.Renamed += new RenamedEventHandler(OnChanged);
            mProjectWatcher.EnableRaisingEvents = true;
            mProjectWatcher.IncludeSubdirectories = true;
            mProjectWatcher.NotifyFilter = NotifyFilters.LastWrite |
                                           NotifyFilters.CreationTime |
                                           NotifyFilters.DirectoryName |
                                           NotifyFilters.FileName;

            mEngineWatcher = new FileSystemWatcher();
            mEngineWatcher.Filters.Add("*.shader");
            mEngineWatcher.Filters.Add("*.compute");
            mEngineWatcher.Filters.Add("*.h");
            mEngineWatcher.Filters.Add("*.hlsl");
            mEngineWatcher.Path = EngineResourceDirectory;
            mEngineWatcher.Changed += new FileSystemEventHandler(OnChanged);
            mEngineWatcher.Created += new FileSystemEventHandler(OnChanged);
            mEngineWatcher.Renamed += new RenamedEventHandler(OnChanged);
            mEngineWatcher.EnableRaisingEvents = true;
            mEngineWatcher.IncludeSubdirectories = true;
            mEngineWatcher.NotifyFilter = NotifyFilters.LastWrite |
                                          NotifyFilters.CreationTime |
                                          NotifyFilters.DirectoryName |
                                          NotifyFilters.FileName;

            var ShaderConfig = EditorConfigManager.GetInstance().GetConfig<ShaderConfig>();
            if (ShaderConfig.BuildShaderMapsAtStartup)
            {
                AssetImporterManager.Instance().BuildShaderMaps();
            }
        }

        private void OnChanged(object source, FileSystemEventArgs e)
        {
            var FullPath = e.FullPath.Replace('\\', '/');

            string buildDir = ((BuildConfig)EditorConfigManager.GetInstance().GetConfig<BuildConfig>()).Staging;
            if (FullPath.StartsWith(buildDir))
                return;

            if (FullPath.EndsWith(".h") || FullPath.EndsWith(".hlsl"))
            {
                if (e.ChangeType != WatcherChangeTypes.Changed)
                    return;

                var ShaderConfig = EditorConfigManager.GetInstance().GetConfig<ShaderConfig>();
                if (!ShaderConfig.CheckShaderModules)
                    return;

                if (mModuleFiles.IndexOf(FullPath) == -1)
                    mModuleFiles.Add(FullPath);
            }
            else
            {
                if (e.ChangeType == WatcherChangeTypes.Created)
                {
                    var ShaderConfig = EditorConfigManager.GetInstance().GetConfig<ShaderConfig>();
                    if (!ShaderConfig.AutoImportNewFile)
                        return;
                }

                if (mFiles.IndexOf(FullPath) == -1)
                    mFiles.Add(FullPath);
            }
        }

        public void Update()
        {
            if (CurrentVersion == Version)
                return;

            CurrentVersion = Version;

            for (int i = 0; i < mModuleFiles.Count; i++)
            {
                vector_string outShaders = new vector_string();

                AssetImporterManager.Instance().CheckShaderModuleFile(mModuleFiles[i], outShaders, ShaderFileOutdateLevelE.ALL);
                foreach (var shader in outShaders)
                {
                    if (mFiles.IndexOf(shader) == -1)
                        mFiles.Add(shader);
                }
            }

            mModuleFiles.Clear();

            if (mFiles.Count == 0)
                return;

            List<string> allShaders = new List<string>(mFiles);

            ImportShaderAsync ImportShaderAsync = async () =>
            {
                List<string> ShaderImportSuccessNames = new List<string>();
                List<string> ShaderImportFailNames = new List<string>();

                for (int i = 0; i < allShaders.Count; i++)
                {
                    AssetType assetType = AssetImporterManager.Instance().GetAssetType(allShaders[i]);
                    if (assetType == AssetType.Shader || assetType == AssetType.ComputeShader)
                    {

                        Func<string, Task<AssetImportResult>> ImportShader = async (string ShaderPath) =>
                        {
                            return await ProjectUI.GetInstance().DoAssetImport(PathHelper.GetDirectoryName(ShaderPath), ShaderPath);
                        };

                        AssetImportResult Result = await ImportShader(allShaders[i]);

                        if (Result.bSuccess)
                        {
                            ShaderImportSuccessNames.Push(allShaders[i]);
                        }
                        else
                        {
                            ShaderImportFailNames.Push(allShaders[i]);
                        }
                    }
                }


                StringBuilder LogString = new StringBuilder();
                if (ShaderImportSuccessNames.Count > 0)
                    LogString.AppendLine(string.Format("\n  Shader Compile Success:{0}", ShaderImportSuccessNames.Count));

                if (ShaderImportFailNames.Count > 0)
                {
                    LogString.AppendLine(string.Format("\n  Shader Compile Fail:{0}", ShaderImportFailNames.Count));

                    foreach (string ShaderImportFailName in ShaderImportFailNames)
                        LogString.AppendLine("    " + ShaderImportFailName);

                    EditorLogger.Log(LogMessageType.Error, LogString.ToString());
                }
                else
                {
                    EditorLogger.Log(LogMessageType.Information, LogString.ToString());
                }
            };

            ImportShaderAsync();
            mFiles.Clear();
        }

        public void OnDeviceApplicationActivate(bool bApplicationActivated)
        {
            if (bApplicationActivated)
            {
                var ShaderConfig = EditorConfigManager.GetInstance().GetConfig<ShaderConfig>();
                if (ShaderConfig?.AutoImportDirtyShaderWhenEditorActivate ?? false)
                    Version++;
            }
        }
    }
}