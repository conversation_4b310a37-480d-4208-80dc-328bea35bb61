using CEngine;
using EditorUI;
using System;
using System.Runtime.InteropServices;

namespace CrossEditor
{
    class EditorUICanvas : IEditorUICanvas
    {
        static EditorUICanvas _Instance = new EditorUICanvas();
        EditorUICanvasInterface _Interface;
        Clicross.EditorUIRenderInterface _RenderInterface;
        EditorGeneralCallBack _Delegate;
        int _ImageWhite;

        public static EditorUICanvas GetInstance()
        {
            return _Instance;
        }

        public EditorUICanvas()
        {
        }

        public EditorUICanvasInterface GetUICanvasInterface()
        {
            return _Interface;
        }

        public Clicross.EditorUIRenderInterface GetUIRenderInterface()
        {
            return _RenderInterface;
        }

        public void Initialize(EditorGeneralCallBack Delegate, Clicross.IRenderWindow window)
        {
            _Delegate = Delegate;
            _Interface = EditorUICanvasInterface.Instance();
            _Interface.Initialize(Delegate);
            Clicross.EditorUICanvasInterface _interfacecli = Clicross.EditorUICanvasInterface.Instance();
            _ImageWhite = LoadUIImage("Editor/Frame/White.nda");

            _RenderInterface = new Clicross.EditorUIRenderInterface(_interfacecli, window);
        }

        public void SetSize(int Width, int Height)
        {
            _RenderInterface.SetSize(Width, Height);
        }

        string TranslateUIImageFilename(string Filename)
        {
            if (PathHelper.IsAbsolutePath(Filename))
            {
                return Filename;
            }
            else
            {
                string Filename1 = Filename.Replace(".png", ".nda");
                Filename1 = Filename1.Replace(".jpg", ".nda");
                if (Filename1.StartsWith("Editor/"))
                {
                    Filename1 = "EngineResource/" + Filename1;
                }
                return EditorUtilities.StandardFilenameToEditorFilename(Filename1);
            }
        }

        public int LoadUIImage(string Filename)
        {
            string Filename1 = TranslateUIImageFilename(Filename);
            if (FileHelper.IsFileExists(Filename1) == false)
            {
                DebugHelper.Assert(false);
                return -1;
            }
            return _Interface.LoadUIImage(Filename1);
        }

        public int LoadUIImage_SRGB(string Filename)
        {
            string Filename1 = TranslateUIImageFilename(Filename);
            if (FileHelper.IsFileExists(Filename1) == false)
            {
                DebugHelper.Assert(false);
                return -1;
            }
            return _Interface.LoadUIImageSRGB(Filename1);
        }

        public int LoadUIImage_RGBA(string Filename, bool cR, bool cG, bool cB, bool cA)
        {
            string Filename1 = TranslateUIImageFilename(Filename);
            if (FileHelper.IsFileExists(Filename1) == false)
            {
                DebugHelper.Assert(false);
                return -1;
            }
            return _Interface.LoadUIImageRGBA(Filename1, cR, cG, cB, cA);
        }

        public int LoadUIImage_UserDefined(string Filename, object Parameters)
        {
            string Filename1 = TranslateUIImageFilename(Filename);
            if (FileHelper.IsFileExists(Filename1) == false)
            {
                DebugHelper.Assert(false);
                return -1;
            }
            return _Interface.LoadUIImageUserDefined(Filename1, (UserParam)Parameters);
        }

        public bool ReleaseUIImage_UserDefined(string Filename, object Parameters)
        {
            string FileName1 = TranslateUIImageFilename(Filename);
            return _Interface.ReleaseUIImageUserDefined(FileName1, (UserParam)Parameters);

        }

        public int LoadUIImage_EcotopeMap(EcotopeMap map)
        {
            return _Interface.LoadUIImageEcotopeMap(EcotopeMap.getCPtr(map).Handle);
        }
        public void ClearReadBackFunc()
        {
            _Interface.ClearReadBackTask();
        }
        public int ReloadUIImage(string Filename)
        {
            string Filename1 = TranslateUIImageFilename(Filename);
            if (FileHelper.IsFileExists(Filename1) == false)
            {
                DebugHelper.Assert(false);
                return -1;
            }
            return _Interface.ReloadUIImage(Filename1);
        }

        public int GetImageWidth(int Image)
        {
            return _Interface.GetImageWidth(Image);
        }

        public int GetImageHeight(int Image)
        {
            return _Interface.GetImageHeight(Image);
        }

        public void SetFilter(int Image, TextureFilter TextureFilter)
        {
            _Interface.SetFilter(Image, (int)TextureFilter);
        }

        public int CreateUIImage(int Width, int Height)
        {
            return _Interface.CreateUIImage(Width, Height);
        }

        public int CreateUIImageByResource(IntPtr Resource)
        {
            return _Interface.CreateUIImageByResource(Resource);
        }

        public int CreateSceneImage(World World, int Width, int Height)
        {
            return _Interface.CreateSceneImage(World.GetNativePointer(), Width, Height);
        }
        public void SetSceneImageEnable(World World, bool bEnable)
        {
            // @cx
            //_Interface.SetSceneImageEnable(World.GetNativePointer(), bEnable);
        }
        public void ResizeSceneImage(int Image, World World, int Width, int Height)
        {
            _Interface.ResizeSceneImage(World.GetNativePointer(), Image, Width, Height);
        }

        public void SaveImage(Clicross.IRenderWindow window, int Image, string Filename, bool bSaveAlpha)
        {
            if (FileHelper.IsFileExists(Filename))
            {
                return;
            }
            string Directory = PathHelper.GetDirectoryName(Filename);
            DirectoryHelper.CreateDirectory(Directory);
            _Interface.SaveImage(window.GetPointer(), Image, Filename, bSaveAlpha);
        }

        public void UploadImage(int Image, uint[] Data)
        {
            _Interface.UploadImage(Image, Marshal.UnsafeAddrOfPinnedArrayElement(Data, 0));
        }

        public void UploadImageRect(int Image, uint[] Data, int x, int y, int width, int height)
        {
            _Interface.UploadImageRect(Image, Marshal.UnsafeAddrOfPinnedArrayElement(Data, 0), x, y, width, height);
        }

        public void DrawImage(int Image, int X, int Y, int Width, int Height, ref Color Color, bool flipy = false)
        {
            if (Image == -1)
            {
                return;
            }
            Clicross.Float4 colorf = new Clicross.Float4();
            colorf.x = Color.R;
            colorf.y = Color.G;
            colorf.z = Color.B;
            colorf.w = Color.A;
            _RenderInterface.DrawImage(Image, X, Y, Width, Height, colorf, flipy);
        }

        public void DrawImage(int Image, int X, int Y, int Width, int Height, int SourceX, int SourceY, int SourceWidth, int SourceHeight, ref Color Color)
        {
            if (Image == -1)
            {
                return;
            }
            Clicross.Float4 colorf = new Clicross.Float4();
            colorf.x = Color.R;
            colorf.y = Color.G;
            colorf.z = Color.B;
            colorf.w = Color.A;
            _RenderInterface.DrawImageEx(Image, X, Y, Width, Height, SourceX, SourceY, SourceWidth, SourceHeight, colorf);
        }

        public void DrawPolygonF(Vector2f[] Vertices, float Width, ref Color Color)
        {
            for (int i = 0; i < Vertices.Length; ++i)
            {
                int j = (i + 1) % Vertices.Length;
                DrawLineF(Vertices[i], Vertices[j], Width, ref Color);
            }
        }

        public void FillPolygonF(Vector2f[] Vertices, ref Color Color, int Batch = 1)
        {
            float[] Xs = new float[Vertices.Length];
            float[] Ys = new float[Vertices.Length];
            for (int i = 0; i < Vertices.Length; ++i)
            {
                Xs[i] = Vertices[i].X;
                Ys[i] = Vertices[i].Y;
            }
            Clicross.Float4 colorf = new Clicross.Float4();
            colorf.x = Color.R;
            colorf.y = Color.G;
            colorf.z = Color.B;
            colorf.w = Color.A;
            _RenderInterface.DrawPolygonF(_ImageWhite, Vertices.Length, Marshal.UnsafeAddrOfPinnedArrayElement(Xs, 0), Marshal.UnsafeAddrOfPinnedArrayElement(Ys, 0), colorf, false, Batch);

        }

        public void DrawCircleF(Color Color, Vector2f Center, float radius, float Width, int segment = 20)
        {
            _RenderInterface.DrawCircleF(_ImageWhite, Center.X, Center.Y, radius, Width, segment, Color.ToDword());
        }

        public void DrawCirclesF(Color[] Colors, Vector2f[] Centers, float[] Radiuses, float[] Widths, int segment = 20)
        {
            float[] Xs = new float[Centers.Length];
            float[] Ys = new float[Centers.Length];
            for (int i = 0; i < Centers.Length; i++)
            {
                Xs[i] = Centers[i].X;
                Ys[i] = Centers[i].Y;
            }
            uint[] ColorsF = new uint[Colors.Length];
            for (int i = 0; i < Colors.Length; i++)
            {
                Color Color = Colors[i];
                ColorsF[i] = Color.ToDword();
            }
            _RenderInterface.DrawCirclesF(
                _ImageWhite,
                Centers.Length,
                Marshal.UnsafeAddrOfPinnedArrayElement(Xs, 0),
                Marshal.UnsafeAddrOfPinnedArrayElement(Ys, 0),
                Marshal.UnsafeAddrOfPinnedArrayElement(Radiuses, 0),
                Marshal.UnsafeAddrOfPinnedArrayElement(Widths, 0),
                segment,
                Marshal.UnsafeAddrOfPinnedArrayElement(ColorsF, 0));
        }

        public void FillPolygon(Vector2i[] Vertices, ref Color Color)
        {
            float[] Xs = new float[Vertices.Length];
            float[] Ys = new float[Vertices.Length];
            for (int i = 0; i < Vertices.Length; ++i)
            {
                Xs[i] = Vertices[i].X;
                Ys[i] = Vertices[i].Y;
            }

            Clicross.Float4 colorf = new Clicross.Float4();
            colorf.x = Color.R;
            colorf.y = Color.G;
            colorf.z = Color.B;
            colorf.w = Color.A;
            _RenderInterface.DrawPolygonF(_ImageWhite, Vertices.Length, Marshal.UnsafeAddrOfPinnedArrayElement(Xs, 0), Marshal.UnsafeAddrOfPinnedArrayElement(Ys, 0), colorf, false, 1);
        }

        public void FillRectangle(int X, int Y, int Width, int Height, ref Color Color)
        {
            Clicross.Float4 colorf = new Clicross.Float4();
            colorf.x = Color.R;
            colorf.y = Color.G;
            colorf.z = Color.B;
            colorf.w = Color.A;
            _RenderInterface.DrawImage(_ImageWhite, X, Y, Width, Height, colorf, false);
        }

        public void FillRectangleF(float X, float Y, float Width, float Height, ref Color Color)
        {
            float X1 = X;
            float Y1 = Y;
            float X2 = X + Width;
            float Y2 = Y + Height;

            Vector2f[] Vertices = new Vector2f[]
            {
                new Vector2f(X1, Y1),
                new Vector2f(X2, Y1),
                new Vector2f(X2, Y2),
                new Vector2f(X1, Y2)
            };

            FillPolygonF(Vertices, ref Color);
        }

        public void DrawLine(int X1, int Y1, int X2, int Y2, ref Color Color)
        {
            int nX_1 = Math.Min(X1, X2);
            int nX_2 = Math.Max(X1, X2);
            int nY_1 = Math.Min(Y1, Y2);
            int nY_2 = Math.Max(Y1, Y2);
            if (nX_1 == nX_2)
            {
                DrawImage(_ImageWhite, nX_1, nY_1, 1, nY_2 - nY_1, ref Color);
            }
            else if (nY_1 == nY_2)
            {
                DrawImage(_ImageWhite, nX_1, nY_1, nX_2 - nX_1, 1, ref Color);
            }
        }
        public void DrawString(string text, ref Color col, int X, int Y, float scaleration)
        {
            Clicross.Float4 colorf = new Clicross.Float4();
            colorf.x = col.R;
            colorf.y = col.G;
            colorf.z = col.B;
            colorf.w = col.A;
            _RenderInterface.DrawString(text, colorf, X, Y, scaleration);
        }
        public void DrawLineF(Vector2f P0, Vector2f P1, float Width, ref Color Color)
        {
            var Direction = (P1 - P0).Normalize();
            var Normal = new Vector2f(Direction.Y, Direction.X * -1f);
            float Offset = Width * 0.5f;

            var Vertices = new Vector2f[]
            {
                P0 - Normal * Offset,
                P1 - Normal * Offset,
                P1 + Normal * Offset,
                P0 + Normal * Offset
            };

            float[] Xs = new float[4];
            float[] Ys = new float[4];

            for (int i = 0; i < 4; ++i)
            {
                Xs[i] = Vertices[i].X;
                Ys[i] = Vertices[i].Y;
            }
            Clicross.Float4 colorf = new Clicross.Float4();
            colorf.x = Color.R;
            colorf.y = Color.G;
            colorf.z = Color.B;
            colorf.w = Color.A;
            _RenderInterface.DrawPolygonF(_ImageWhite, 4, Marshal.UnsafeAddrOfPinnedArrayElement(Xs, 0), Marshal.UnsafeAddrOfPinnedArrayElement(Ys, 0), colorf, false, 1);
        }

        public void DrawLinesF(Vector2f[] Points, int Count, float Width, Color Color)
        {
            Clicross.Float4 colorf = new Clicross.Float4();
            colorf.x = Color.R;
            colorf.y = Color.G;
            colorf.z = Color.B;
            colorf.w = Color.A;
            _RenderInterface.DrawPolygonsF(_ImageWhite, Marshal.UnsafeAddrOfPinnedArrayElement(Points, 0), Count, Width, colorf);
        }

        public void DrawRectangle(int X, int Y, int Width, int Height, ref Color Color)
        {
            int X1 = X;
            int Y1 = Y;
            int X2 = X + Width;
            int Y2 = Y + Height;
            DrawLine(X1, Y1, X2, Y1, ref Color);
            DrawLine(X2, Y1, X2, Y2 + 1, ref Color);
            DrawLine(X2, Y2, X1, Y2, ref Color);
            DrawLine(X1, Y2, X1, Y1, ref Color);
        }

        public void DrawRectangleF(float X, float Y, float Width, float Height, ref Color Color)
        {
            float X1 = X;
            float Y1 = Y;
            float X2 = X + Width;
            float Y2 = Y + Height;
            DrawLineF(new Vector2f(X1, Y1), new Vector2f(X2, Y1), 1f, ref Color);
            DrawLineF(new Vector2f(X2, Y1), new Vector2f(X2, Y2 + 1), 1f, ref Color);
            DrawLineF(new Vector2f(X2, Y2), new Vector2f(X1, Y2), 1f, ref Color);
            DrawLineF(new Vector2f(X1, Y2), new Vector2f(X1, Y1), 1f, ref Color);
        }

        public void SetClipRect(int X, int Y, int Width, int Height)
        {
            _RenderInterface.SetClipRect(X, Y, Width, Height);
        }

        public void ClearClipRect()
        {
            _RenderInterface.ClearClipRect();
        }
    }
}
