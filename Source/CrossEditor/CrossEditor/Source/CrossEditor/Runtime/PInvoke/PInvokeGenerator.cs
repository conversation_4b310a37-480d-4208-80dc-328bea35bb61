using CEngine;
using EditorUI;
using System;
using System.IO;
using System.Reflection;
using System.Text;

namespace CrossEditor
{
    class PInvokeGenerator
    {
        static PInvokeGenerator _Instance = new PInvokeGenerator();

        public static PInvokeGenerator GetInstance()
        {
            return _Instance;
        }

        public PInvokeGenerator()
        {

        }

        const string PrefixCS =
            "// This file is generated by editor.\n" +
            "\n" +
            "using System.Runtime.InteropServices;\n" +
            "\n" +
            "using IWorld = System.IntPtr;\n" +
            "\n" +
            "namespace CrossEditor\n" +
            "{\n" +
            "    partial class Runtime\n" +
            "    {\n";

        const string HasComponentFormatterCS =
            "       [DllImport(\"CrossEngine\", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.Cdecl)]\n" +
            "       public static extern bool World_Has{0}Component(IWorld world, ulong entity);\n\n";

        const string CreateComponentFormatterCS =
            "       [DllImport(\"CrossEngine\", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.Cdecl)]\n" +
            "       public static extern void World_Create{0}Component(IWorld world, ulong entity);\n\n";

        const string DestroyComponentFormatterCS =
            "       [DllImport(\"CrossEngine\", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.Cdecl)]\n" +
            "       public static extern void World_Destroy{0}Component(IWorld world, ulong entity);\n\n";

        const string SetPropertyFormatterCS =
            "       [DllImport(\"CrossEngine\", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.Cdecl)]\n" +
            "       public static extern void World_Set{0}{1}(IWorld world, ulong entity, {2}{3} {4});\n\n";

        const string PostfixCS = "\t}\n}";

        const string PrefixCPP =
            "// This file is generated by editor.\n" +
            "\n";

        const string HasComponentFormatterCPP =
            "extern \"C\" ENGINE_API bool World_Has{0}Component(IWorld* world, UInt64 entity)\n" +
            "{{\n" +
            "   return world->Has{0}Component(entity);\n" +
            "}}\n\n";

        const string CreateComponentFormatterCPP =
            "extern \"C\" ENGINE_API void World_Create{0}Component(IWorld* world, UInt64 entity)\n" +
            "{{\n" +
            "   world->Create{0}Component(entity);\n" +
            "}}\n\n";

        const string DestroyComponentFormatterCPP =
            "extern \"C\" ENGINE_API void World_Destroy{0}Component(IWorld* world, UInt64 entity)\n" +
            "{{\n" +
            "   world->Destroy{0}Component(entity);\n" +
            "}}\n\n";

        const string SetPropertyFormatterCPP =
            "extern \"C\" ENGINE_API void World_Set{0}{1}(IWorld* world, UInt64 entity, {2} {3})\n" +
            "{{\n" +
            "   world->Set{0}{1}(entity, {3});\n" +
            "}}\n\n";

        string FirstCharToLower(string Identifier)
        {
            string String1 = Identifier.Substring(0, 1);
            if (Identifier.Length < 2)
            {
                return String1.ToLower();
            }
            string String2 = Identifier.Substring(1, Identifier.Length - 1);
            return String1.ToLower() + String2;
        }

        public void GeneratePInvoke()
        {
            if (EditorUtilities.IsPublishEditor())
                return;
            string FilenameCS = EditorUtilities.GetEngineDirectory() + "/Source/CrossEditor/CrossEditor/Source/CrossEditor/Runtime/PInvoke/Runtime_Auto.cs";
            string FilenameCPP = EditorUtilities.GetEngineDirectory() + "/Source/Runtime/Editor/EditorInterfaceAuto.ipp";

            StringBuilder StringBuilderCS = new StringBuilder();
            StringBuilderCS.Append(PrefixCS);
            StringBuilder StringBuilderCPP = new StringBuilder();
            StringBuilderCPP.Append(PrefixCPP);
            Type ThisType = typeof(PInvokeGenerator);
            Assembly Assembly = ThisType.Assembly;
            Type[] Types = Assembly.GetTypes();
            foreach (Type Type in Types)
            {
                if (Type.IsSubclassOf(typeof(Component)))
                {
                    string TypeName = Type.Name;
                    string HasComponentCS = string.Format(HasComponentFormatterCS, TypeName);
                    StringBuilderCS.Append(HasComponentCS);
                    string CreateComponentCS = string.Format(CreateComponentFormatterCS, TypeName);
                    StringBuilderCS.Append(CreateComponentCS);
                    string DestroyComponentCS = string.Format(DestroyComponentFormatterCS, TypeName);
                    StringBuilderCS.Append(DestroyComponentCS);
                    string HasComponentCPP = string.Format(HasComponentFormatterCPP, TypeName);
                    StringBuilderCPP.Append(HasComponentCPP);
                    string CreateComponentCPP = string.Format(CreateComponentFormatterCPP, TypeName);
                    StringBuilderCPP.Append(CreateComponentCPP);
                    string DestroyComponentCPP = string.Format(DestroyComponentFormatterCPP, TypeName);
                    StringBuilderCPP.Append(DestroyComponentCPP);
                    PropertyInfo[] Properties = Type.GetProperties();
                    foreach (PropertyInfo PropertyInfo in Properties)
                    {
                        string PropertyName = PropertyInfo.Name;
                        Type PropertyType = PropertyInfo.PropertyType;
                        AttributeList AttributeList1 = AttributeManager.GetInstance().GetAttributeList(PropertyInfo);
                        AttributeData AttributeData = AttributeList1.GetPropertyInfoAttr();
                        if (AttributeData != null)
                        {
                            object AttributeObject = AttributeData.GetNamedAttribute("bGenerateCode");
                            if (AttributeObject != null)
                            {
                                bool bGenerateCode = (bool)AttributeObject;
                                if (bGenerateCode == false)
                                {
                                    continue;
                                }
                            }
                        }
                        string LocalVariableRef = "";
                        string PropertyTypeNameCS = PropertyType.Name;
                        string PropertyTypeNameCPP = PropertyType.Name;
                        string LocalVariableNameCS = FirstCharToLower(PropertyName);
                        string LocalVariableNameCPP = FirstCharToLower(PropertyName);
                        if (PropertyType == typeof(bool))
                        {
                            PropertyTypeNameCS = "bool";
                            PropertyTypeNameCPP = "bool";
                        }
                        else if (PropertyType == typeof(sbyte))
                        {
                            PropertyTypeNameCS = "sbyte";
                            PropertyTypeNameCPP = "char";
                        }
                        else if (PropertyType == typeof(byte))
                        {
                            PropertyTypeNameCS = "byte";
                            PropertyTypeNameCPP = "unsigned char";
                        }
                        else if (PropertyType == typeof(short))
                        {
                            PropertyTypeNameCS = "short";
                            PropertyTypeNameCPP = "short";
                        }
                        else if (PropertyType == typeof(ushort))
                        {
                            PropertyTypeNameCS = "ushort";
                            PropertyTypeNameCPP = "unsigned short";
                        }
                        else if (PropertyType == typeof(int))
                        {
                            PropertyTypeNameCS = "int";
                            PropertyTypeNameCPP = "int";
                        }
                        else if (PropertyType == typeof(uint))
                        {
                            PropertyTypeNameCS = "uint";
                            PropertyTypeNameCPP = "unsigned int";
                        }
                        else if (PropertyType == typeof(long))
                        {
                            PropertyTypeNameCS = "long";
                            PropertyTypeNameCPP = "long long";
                        }
                        else if (PropertyType == typeof(ulong))
                        {
                            PropertyTypeNameCS = "ulong";
                            PropertyTypeNameCPP = "unsigned long long";
                        }
                        else if (PropertyType == typeof(float))
                        {
                            PropertyTypeNameCS = "float";
                            PropertyTypeNameCPP = "float";
                        }
                        else if (PropertyType == typeof(double))
                        {
                            PropertyTypeNameCS = "double";
                            PropertyTypeNameCPP = "double";
                        }
                        else if (PropertyType == typeof(string))
                        {
                            PropertyTypeNameCS = "string";
                            PropertyTypeNameCPP = "char const*";
                        }
                        else if (PropertyType.IsSubclassOf(typeof(Enum)))
                        {
                            PropertyTypeNameCS = "int";
                            PropertyTypeNameCPP = "int";
                        }
                        else if (PropertyType.IsSubclassOf(typeof(ValueType)))
                        {
                            LocalVariableRef = "ref ";
                            LocalVariableNameCPP = "* " + LocalVariableNameCPP;
                        }
                        string SetPropertyCS = string.Format(SetPropertyFormatterCS, TypeName, PropertyName, LocalVariableRef, PropertyTypeNameCS, LocalVariableNameCS);
                        StringBuilderCS.Append(SetPropertyCS);
                        string SetPropertyCPP = string.Format(SetPropertyFormatterCPP, TypeName, PropertyName, PropertyTypeNameCPP, LocalVariableNameCPP);
                        StringBuilderCPP.Append(SetPropertyCPP);
                    }
                }
            }
            StringBuilderCS.Append(PostfixCS);
            WriteTextFileIfChanged(FilenameCS, StringBuilderCS.ToString());
            WriteTextFileIfChanged(FilenameCPP, StringBuilderCPP.ToString());
        }

        public static bool WriteTextFileIfChanged(string Filename, string Content)
        {
            string Content1 = Content.Replace("\r\n", "\n");
            Content1 = Content1.Replace("\n", "\r\n");
            if (File.Exists(Filename) == false)
            {
                return WriteTextFile(Filename, Content1);
            }
            string OldText = ReadTextFile(Filename);
            if (OldText != Content1)
            {
                return WriteTextFile(Filename, Content1);
            }
            else
            {
                return WriteTextFile(Filename, OldText);
            }
        }

        public static string ReadTextFile(string Filename)
        {
            try
            {
                return File.ReadAllText(Filename, Encoding.ASCII);
            }
            catch
            {
                return "";
            }
        }

        public static bool WriteTextFile(string Filename, string Content)
        {
            try
            {
                string DirectoryName = Path.GetDirectoryName(Filename);
                if (Directory.Exists(DirectoryName) == false)
                {
                    Directory.CreateDirectory(DirectoryName);
                }
                File.WriteAllText(Filename, Content, Encoding.ASCII);
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
