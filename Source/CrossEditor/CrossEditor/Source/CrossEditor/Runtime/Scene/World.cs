using CEngine;
using System.Threading.Tasks;
using IGameWorld = System.IntPtr;

namespace CrossEditor
{
    public class World
    {
        public Clicross.IGameWorld _WorldInterface;
        public IGameWorld _World;
        public Entity Root;
        public bool Enable = true;
        public WorldTypeTag WorldType = WorldTypeTag.DefaultWorld;
        public string WorldName = "";
        public World(Clicross.IGameWorld World)
        {
            _WorldInterface = World;
            _World = World.GetPointer();
        }

        public IGameWorld GetNativePointer()
        {
            return _World;
        }

        public void Initialize()
        {
            if (Clicross.GameWorldInterface.World_GetRootEntity(_WorldInterface) != ulong.MaxValue)
            {
                Root = new Entity(this);
                Root.EntityID = Clicross.GameWorldInterface.World_GetRootEntity(_WorldInterface);
            }
        }
        public Entity CreateEntity()
        {
            Entity Entity = new Entity(this);
            Entity.RuntimeCreateEntity();
            return Entity;
        }
        public void SetBoneEntities()
        {

        }
        public void SetWorldEnableDelay(bool bEnable)
        {
            Task task = new System.Threading.Tasks.Task(() =>
            {
                this.Enable = bEnable;
                Clicross.GameWorldInterface.World_SetEnable(_WorldInterface, bEnable);
                EditorUICanvas.GetInstance().SetSceneImageEnable(this, bEnable);
            });
            SceneRuntime.GetInstance().WorldSetTaskQueue.Add(task);
        }
        public void SetWorldEnable(bool bEnable)
        {
            this.Enable = bEnable;
            Clicross.GameWorldInterface.World_SetEnable(_WorldInterface, bEnable);
        }
        public void SetWorldDrawingEnable(bool bEnable)
        {
            EditorUICanvas.GetInstance().SetSceneImageEnable(this, bEnable);
        }
        [PropertyInfo(PropertyType = "StringAsFile", ToolTips = "World Script Path.", DisplayName = "World Script", FileTypeDescriptor = "Script Files#js|lua")]
        public string WorldScriptPath
        {
            get
            {
                return CrossEngineApi.GetWorldScriptPath(_World);
            }
            set
            {
                CrossEngineApi.SetWorldScriptPath(_World, value);
            }

        }

        public bool GetWorldEnable()
        {
            return Clicross.GameWorldInterface.World_GetEnable(_WorldInterface);
        }
    }
}
