using CEngine;

namespace CrossEditor
{
    [ComponentAttribute(InspectorType = typeof(Inspector_PostProcessVolumeComponent))]
    class PostProcessVolumeComponent : Component
    {
        static string[] _NativeNames = { "cross::PostProcessVolumeComponentG" };
        static string _NativeSystemName = "cross::PostProcessVolumeSystemG";
        static int _ComponentOrder = 3;
        static int _GroupOrder = 2;

        public PostProcessVolumeComponentG ComponentData = new PostProcessVolumeComponentG();

        public override string NativeSystemName()
        {
            return _NativeSystemName;
        }

        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }

        public override int GroupOrder()
        {
            return _GroupOrder;
        }

        public override void SyncDataFromEngine()
        {
            PostProcessVolumeSystemG.GetPostProcessVolumeComponent(Entity.World.GetNativePointer(), Entity.EntityID, ComponentData);
        }

        public void OnComponentDataChanged()
        {
            PostProcessVolumeSystemG.SetPostProcessVolumeComponent(Entity.World.GetNativePointer(), Entity.EntityID, ComponentData);
        }

        public void SetEnableForEditorEyeIcon(bool bEnable)
        {
            PostProcessVolumeSystemG.SetEnable(Entity.World.GetNativePointer(), Entity.EntityID, bEnable);
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Extra adding")]
        public PostProcessVolumeComponentG AddComponentData
        {
            get
            {
                return ComponentData;
            }
            set
            {
                PostProcessVolumeSystemG.SetPostProcessVolumeComponent(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }

    }
}
