using CEngine;
namespace CrossEditor
{
    class Trajectory : Component
    {
        protected float _MaxSpeed = 200.0f;
        protected float _MoveResponse = 8.0f;
        protected float _TurnResponse = 8.0f;
        protected bool _StrafeMode = false;
        protected Vector3f _StrafeDirection;

        protected bool _TrajectoryWarpping = true;
        protected float _WarpRate = 30.0f;
        protected float _MinWarpDistance = 75.0f;
        protected Vector2f _WarpRange;

        static string[] _NativeNames = { "cross::TrajectoryComponentG" };
        public override string[] NativeNames()
        {
            return _NativeNames;
        }
        static int _ComponentOrder = 4;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }
        static int _GroupOrder = 4;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }
        public Trajectory()
        {
            _StrafeDirection = new Vector3f();
            _WarpRange = new Vector2f(0.1f, 30.0f);
        }

        public override void Reset()
        {
            base.Reset();
        }

        public override void SyncDataFromEngine()
        {

            _MaxSpeed = TrajectorySystemG.GetSpeed(Entity.World.GetNativePointer(), Entity.EntityID);
            _MoveResponse = TrajectorySystemG.GetMoveResponse(Entity.World.GetNativePointer(), Entity.EntityID);
            _TurnResponse = TrajectorySystemG.GetTurnResponse(Entity.World.GetNativePointer(), Entity.EntityID);
            _StrafeMode = TrajectorySystemG.GetStrafeMode(Entity.World.GetNativePointer(), Entity.EntityID);
            _StrafeDirection = new Vector3f(TrajectorySystemG.GetStrafeDirection(Entity.World.GetNativePointer(), Entity.EntityID));

            _TrajectoryWarpping = TrajectorySystemG.GetTrajectoryWarpping(Entity.World.GetNativePointer(), Entity.EntityID);
            _WarpRate = TrajectorySystemG.GetWarpRate(Entity.World.GetNativePointer(), Entity.EntityID);
            _MinWarpDistance = TrajectorySystemG.GetMinWarpDistance(Entity.World.GetNativePointer(), Entity.EntityID);
            _WarpRange = new Vector2f(TrajectorySystemG.GetWarpRange(Entity.World.GetNativePointer(), Entity.EntityID));
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Move Response", DefaultValue = 8.0f)]
        public float MoveResponse
        {
            get
            {
                return _MoveResponse;
            }
            set
            {
                _MoveResponse = value;
                TrajectorySystemG.SetMoveResponse(Entity.World.GetNativePointer(), Entity.EntityID, _MoveResponse);

            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Turn Response", DefaultValue = 8.0f)]
        public float TurnResponse
        {
            get { return _TurnResponse; }
            set
            {
                _TurnResponse = value;
                TrajectorySystemG.SetTurnResponse(Entity.World.GetNativePointer(), Entity.EntityID, _TurnResponse);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "MaxSpeed", DefaultValue = 200.0f)]
        public float MaxSpeed
        {
            get { return _MaxSpeed; }
            set
            {
                _MaxSpeed = value;
                TrajectorySystemG.SetSpeed(Entity.World.GetNativePointer(), Entity.EntityID, _MaxSpeed);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "StrafeMode, lock charactor direction", DefaultValue = false)]
        public bool StrafeMode
        {
            get { return _StrafeMode; }
            set
            {
                _StrafeMode = value;
                TrajectorySystemG.SetStrafeMode(Entity.World.GetNativePointer(), Entity.EntityID, _StrafeMode);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "StrafeMode, lock charactor direction")]
        public Vector3f StrafeDirection
        {
            get { return _StrafeDirection; }
            set
            {
                _StrafeDirection = value;
                TrajectorySystemG.SetStrafeDirection(Entity.World.GetNativePointer(), Entity.EntityID, _StrafeDirection.ToFloat3());
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "whether warping trajectory")]
        public bool TrajectoryWarpping
        {
            get { return _TrajectoryWarpping; }
            set
            {
                _TrajectoryWarpping = value;
                TrajectorySystemG.SetTrajectoryWarpping(Entity.World.GetNativePointer(), Entity.EntityID, _TrajectoryWarpping);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "WarpRate, degrees per second to warp the charater's rotation", DefaultValue = 30.0f)]
        public float WarpRate
        {
            get { return _WarpRate; }
            set
            {
                _WarpRate = value;
                TrajectorySystemG.SetWarpRate(Entity.World.GetNativePointer(), Entity.EntityID, _WarpRate);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "warp min distance, only warp when distance between desired position and character is larger then minDistance", DefaultValue = 30.0f)]
        public float MinWarpDistance
        {
            get { return _MinWarpDistance; }
            set
            {
                _MinWarpDistance = value;
                TrajectorySystemG.SetMinWarpDistance(Entity.World.GetNativePointer(), Entity.EntityID, _MinWarpDistance);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "warp range, only warp when angle difference is in this range")]
        public Vector2f WarpRange
        {
            get { return _WarpRange; }
            set
            {
                _WarpRange = value;
                TrajectorySystemG.SetWarpRange(Entity.World.GetNativePointer(), Entity.EntityID, _WarpRange.ToFloat2());
            }
        }



    }
}
