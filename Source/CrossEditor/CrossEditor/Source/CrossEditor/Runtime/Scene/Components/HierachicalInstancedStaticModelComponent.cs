using CEngine;
using System;
using System.Collections.Generic;
using cross;
using HierachicalInstancedStaticModelSystemG = CEngine.HierachicalInstancedStaticModelSystemG;

namespace CrossEditor
{
    [ComponentAttribute(DisplayUINames = "Mesh/HierachicalInstancedStaticModel Component", NeedRenderProperty = true)]
    class HierachicalInstancedStaticModelComponent : InstancedStaticModelComponent
	{
        static string[] _NativeNames = { "cross::HierachicalInstancedStaticModelComponentG" };
        private int _MaxInstancesPerLeaf = 16;
        private int _InternalNodeBranchingFactor = 16;
        private int _DensityLODCount = 1;
        private float _DensityLodDistanceScalar = 8;

        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        static new List<Type> _AssociateComponents = new List<Type> { typeof(InstancedStaticModelComponent) };

        static int _ComponentOrder = 2;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }

        static int _GroupOrder = 1;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Enable this mesh renderer.")]
        public override bool Enable
        {
            get { return _Enable; }
            set
            {
                _Enable = value;
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "MaxInstancesPerLeaf")]
        public int MaxInstancesPerLeaf
        {
            get
            {
                _MaxInstancesPerLeaf = HierachicalInstancedStaticModelSystemG.GetMaxInstancesPerLeaf(Entity.World.GetNativePointer(), Entity.EntityID);
                return _MaxInstancesPerLeaf;
            }
            set
            {
                _MaxInstancesPerLeaf = value;
                HierachicalInstancedStaticModelSystemG.SetMaxInstancesPerLeaf(Entity.World.GetNativePointer(), Entity.EntityID, value, true);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "InternalNodeBranchingFactor")]
        public int InternalNodeBranchingFactor
        {
            get
            {
                _InternalNodeBranchingFactor = HierachicalInstancedStaticModelSystemG.GetInternalNodeBranchingFactor(Entity.World.GetNativePointer(), Entity.EntityID);
                return _InternalNodeBranchingFactor;
            }
            set
            {
                _InternalNodeBranchingFactor = value;
                HierachicalInstancedStaticModelSystemG.SetInternalNodeBranchingFactor(Entity.World.GetNativePointer(), Entity.EntityID, value, true);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Density LOD Partition Count")]
        public int DensityLODCount
        {
            get
            {
                _DensityLODCount = HierachicalInstancedStaticModelSystemG.GetDensityLODCount(Entity.World.GetNativePointer(), Entity.EntityID);
                return _DensityLODCount;
            }
            set
            {
                _DensityLODCount = value;
                HierachicalInstancedStaticModelSystemG.SetDensityLODCount(Entity.World.GetNativePointer(), Entity.EntityID, value, true);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Distance parameter for density LOD switching; higher values move transitions farther away.")]
        public float DensityLodDistanceScalar
        {
            get
            {
                _DensityLodDistanceScalar = HierachicalInstancedStaticModelSystemG.GetDensityLodDistanceScalar(Entity.World.GetNativePointer(), Entity.EntityID);
                return _DensityLodDistanceScalar;
            }
            set
            {
                _DensityLodDistanceScalar = value;
                HierachicalInstancedStaticModelSystemG.SetDensityLodDistanceScalar(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }
    }
}
