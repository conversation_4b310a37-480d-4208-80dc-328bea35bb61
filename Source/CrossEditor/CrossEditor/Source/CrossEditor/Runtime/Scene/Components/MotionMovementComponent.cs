using CEngine;

namespace CrossEditor
{
    class MotionMovementComponent : Component
    {
        static string[] _NativeNames = { "cross::MotionMovementComponentG" };

        protected MotionMovementComponentG mMotionMovement = new MotionMovementComponentG();

        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        public MotionMovementComponent()
        { }

        static int _ComponentOrder = 3;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }

        static int _GroupOrder = 4;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }

        [PropertyInfo(PropertyType = "Struct", bAutoExpandStruct = true, ToolTips = "MotionMovement")]
        public MotionMovementComponentG MotionMovement
        {
            get
            {
                MotionMovementSystemG.GetMotionMovementComponent(Entity.World.GetNativePointer(), Entity.EntityID, mMotionMovement);
                return mMotionMovement;
            }
            set
            {
                mMotionMovement = value;
                MotionMovementSystemG.SetMoitonMovementComponent(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }
    }
}
