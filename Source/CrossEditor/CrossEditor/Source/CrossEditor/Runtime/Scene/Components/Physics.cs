using CEngine;
using EditorUI;
using System.ComponentModel;
using System.Reflection;

namespace CrossEditor
{
    public static class CollisionMaskUtils
    {
        public static void BlockWith(this CollisionMask self, params CollisionType[] collisionTypies)
        {
            self.ClearAll();
            foreach (var type in collisionTypies)
                self.Set(type);
        }
    }

    public class Physics : Component
    {
        static string[] _NativeNames = { "cross::PhysicsComponentG" };
        static string _NativeSystemName = "cross::PhysicsSystemG";
        PhysicsSimpleCollision _ExtraCollision = new PhysicsSimpleCollision();
        CollisionMask _BlockMask;

        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        public override string NativeSystemName()
        {
            return _NativeSystemName;
        }

        public Physics()
        {
        }

        static int _ComponentOrder = 0;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }
        static int _GroupOrder = 5;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }
        public override void Reset()
        {
            base.Reset();
        }
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Enable physics.")]
        public override bool Enable
        {
            get
            {
                _Enable = PhysicsSystemG.GetEnable(GetWorldPtr(), Entity.EntityID);
                return _Enable;
            }
            set
            {
                _Enable = value;
                PhysicsSystemG.SetEnable(GetWorldPtr(), Entity.EntityID, _Enable);
            }
        }

        //[PropertyInfo(PropertyType = "Auto", ToolTips = "EnableCollisionEvent")]
        //public bool EnableCollisionEvent
        //{
        //    get
        //    {
        //        return PhysicsSystemG.GetCollisionEventEnable(Entity.World.GetNativePointer(), Entity.EntityID);
        //    }
        //    set
        //    {
        //        PhysicsSystemG.SetCollisionEventEnable(Entity.World.GetNativePointer(), Entity.EntityID, value, false);

        //    }
        //}
        [PropertyInfo(PropertyType = "Auto", DisplayName = "Dynamic", ToolTips = "If true, this body will be a dynamic body. \nIf false, will be a static body which should not be moved anyway.")]
        public bool IsDynamic
        {
            get
            {
                return PhysicsSystemG.GetIsDynamic(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                PhysicsSystemG.SetIsDynamic(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }
        [PropertyInfo(PropertyType = "Auto", DisplayName = "Is Kinematic", ToolTips = "A kinematic body moved by transform, a non-kinematic moved by force. Only available when Simulate Physics is true")]
        public bool IsKinematic
        {
            get
            {
                return PhysicsSystemG.GetIsKinematic(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                PhysicsSystemG.SetIsKinematic(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }

        [PropertyInfo(PropertyType = "Auto", DisplayName = "Enable Gravity", ToolTips = "If the body should be effected by gravity.")]
        public bool EnableGravity
        {
            get
            {
                return PhysicsSystemG.GetEnableGravity(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                PhysicsSystemG.SetEnableGravity(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }

        [PropertyInfo(PropertyType = "Auto", DisplayName = "Use Mesh Collision", ToolTips = "A kinematic body moved by transform, a non-kinematic moved by force. Only available when Simulate Physics is true")]
        public bool UseMeshCollision
        {
            get
            {
                return PhysicsSystemG.GetUseMeshCollision(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                PhysicsSystemG.SetUseMeshCollision(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }

        [PropertyInfo(PropertyType = "Auto", DisplayName = "Is Trigger", ToolTips = "IsTrigger")]
        public bool IsTrigger
        {
            get
            {
                return PhysicsSystemG.GetIsTrigger(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                PhysicsSystemG.SetIsTrigger(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }


        //[PropertyInfo(PropertyType = "Auto", ToolTips = "OnlyUseExtraCollision")]
        //public bool OnlyUseExtraCollision
        //{
        //    get
        //    {
        //        return PhysicsSystemG.GetOnlyUseExtraCollision(Entity.World.GetNativePointer(), Entity.EntityID);
        //    }
        //    set
        //    {
        //        PhysicsSystemG.SetOnlyUseExtraCollision(Entity.World.GetNativePointer(), Entity.EntityID, value);
        //    }
        //}

        [PropertyInfo(PropertyType = "Auto", ToolTips = "StartupAsleep")]
        public bool StartupAsleep
        {
            get
            {
                return PhysicsSystemG.GetStartAsleep(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                PhysicsSystemG.SetStartAsleep(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "LinearDamping")]
        public float LinearDamping
        {
            get
            {
                return PhysicsSystemG.GetLinearDamping(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                PhysicsSystemG.SetLinearDamping(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Mass")]
        public float Mass
        {
            get
            {
                return PhysicsSystemG.GetMass(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                PhysicsSystemG.SetMass(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "MassSpaceInertiaTensorMultiplier")]
        public Float3 MassSpaceInertiaTensorMultiplier
        {
            get
            {
                return PhysicsSystemG.GetMassSpaceInertiaTensorMultiplier(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                PhysicsSystemG.SetMassSpaceInertiaTensorMultiplier(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "MaxDepenetrationVelocity")]
        public float MaxDepenetrationVelocity
        {
            get
            {
                return PhysicsSystemG.GetMaxDepenetrationVelocity(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                PhysicsSystemG.SetMaxDepenetrationVelocity(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "CollisionType")]
        public CollisionType CollisionType
        {
            get
            {
                return PhysicsSystemG.GetCollisionType(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                BlockMask.Set(CollisionType.Actor).Set(CollisionType.Terrain);
                PhysicsSystemG.SetCollisionType(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Block With")]
        public CollisionMask BlockMask
        {
            get
            {
                if (_BlockMask is null)
                    _BlockMask = PhysicsSystemG.GetCollisionMask(GetWorldPtr(), Entity.EntityID);
                return _BlockMask;
            }
            set
            {
                _BlockMask.SetValue(value.Value());
                PhysicsSystemG.SetCollisionMask(GetWorldPtr(), Entity.EntityID, _BlockMask);
            }
        }

        [PropertyInfo(PropertyType = "Auto", DisplayName = "MaterialType", ToolTips = "MaterialType")]
        public MaterialType MaterialTypeValue
        {
            get
            {
                return PhysicsSystemG.GetMaterialType(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                PhysicsSystemG.SetMaterialType(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }

        // 使用 PhysicsSimpleCollision 会奔溃, 将各个参数展平

        [PropertyInfo(PropertyType = "List", ChildPropertyType = "Struct", ToolTips = "ExtraBoxes")]
        public vector_cross_PhysicsGeometryBox ExtraBoxes
        {
            get
            {
                return PhysicsSystemG.GetExtraBoxes(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                PhysicsSystemG.SetExtraBoxes(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }

        [PropertyInfo(PropertyType = "List", ChildPropertyType = "Struct", ToolTips = "ExtraSpheres")]
        public vector_cross_PhysicsGeometrySphere ExtraSpheres
        {
            get
            {
                return PhysicsSystemG.GetExtraSpheres(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                PhysicsSystemG.SetExtraSpheres(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }

        [PropertyInfo(PropertyType = "List", ChildPropertyType = "Struct", ToolTips = "ExtraCapsules")]
        public vector_cross_PhysicsGeometryCapsule ExtraCapsules
        {
            get
            {
                return PhysicsSystemG.GetExtraCapsules(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                PhysicsSystemG.SetExtraCapsules(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }
        public override void OnComponentAddToEntity()
        {
            base.OnComponentAddToEntity();

            PhysicsSystemG.InitPhysics(Entity.World.GetNativePointer(), Entity.EntityID);
        }

        public void UpdateMassAndInertia(float density)
        {
            PhysicsSystemG.UpdateMassAndInertia(Entity.World.GetNativePointer(), Entity.EntityID, density);
        }
    }
}