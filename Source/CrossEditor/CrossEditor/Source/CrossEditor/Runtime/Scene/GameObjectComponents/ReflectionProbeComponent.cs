namespace CrossEditor
{
    [ComponentAttribute(DisplayUINames = "Rendering/ReflectionProbeComponent", InspectorType = typeof(Inspector_GameComponent))]
    public class ReflectionProbeGameComponent : GameObjectComponent
    {
        static string[] _NativeNames = { "cegf::ReflectionProbeComponent" };
        public override string[] NativeNames()
        {
            return _NativeNames;
        }
        protected override void CreateECSEditorComponent()
        {
            var comp = mGameObject.mEntity.GetComponent<ReflectionProbeComponent>();
            if (comp == null)
            {
                comp = mGameObject.mEntity.CreateComponent<ReflectionProbeComponent>();
            }

            mECSEditorComponents["ReflectionProbeComponent"] = comp;
        }

        public override void SyncDataFromEngine()
        {
            mECSEditorComponents["ReflectionProbeComponent"] = mGameObject.mEntity.GetComponent<ReflectionProbeComponent>();
        }
    }
}
