using CEngine;
using Clicegf;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    [ComponentAttribute(DisplayUINames = "Misc/UIComponent", InspectorType = typeof(Inspector_GameComponent))]
    public class UIComponent : GameObjectComponent
    {
        static string[] _NativeNames = { "cegf::UIComponent" };
        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        [PropertyInfo(PropertyType = "StringAsResource", ToolTips = "CanvasPage path", FileTypeDescriptor = "Page Files#html")]
        public string Path
        {
            get
            {
                return _Path;
            }
            set
            {
                _Path = value;
                var Uicomp = GetRuntimeOwnerGameObject().GetComponentByMetaClassName("cegf::UIComponent") as Clicegf.UIComponent;
                Uicomp.SetPagePath(_Path);

                if (_Path == "") return;
            }
        }
        private string _Path;
        private uint _Width;
        private uint _Height;
        //private bool _Visible;
        private bool _FitWindow;
        private int _Layer;
        private CanvasMode _CanvasMode;

        private string _DefaultFontName;

        private List<string> _UsedFonts = new List<string>();
        //private Entity _Entity;
        private List<float> _BlendValues = new List<float>();
        private List<float> _BlendValues2 = new List<float>();

        [PropertyInfo(PropertyType = "Auto")]
        public uint Width
        {
            get
            {
                return _Width;
            }
            set
            {
                _Width = value;
                var Uicomp = GetRuntimeOwnerGameObject().GetComponentByMetaClassName("cegf::UIComponent") as Clicegf.UIComponent;
                Uicomp.SetWidthHeight(_Width, _Height);
            }
        }
        [PropertyInfo(PropertyType = "Auto")]
        public uint Height
        {
            get
            {
                return _Height;
            }
            set
            {
                _Width = value;
                var Uicomp = GetRuntimeOwnerGameObject().GetComponentByMetaClassName("cegf::UIComponent") as Clicegf.UIComponent;
                Uicomp.SetWidthHeight(_Width, _Height);
            }
        }
        [PropertyInfo(PropertyType = "Auto")]
        public CanvasMode CanvasMode
        {
            get
            {
                return _CanvasMode;
            }
            set
            {
                _CanvasMode = value;
                var Uicomp = GetRuntimeOwnerGameObject().GetComponentByMetaClassName("cegf::UIComponent") as Clicegf.UIComponent;
                Uicomp.SetCanvasMode((Clicross.CanvasMode)_CanvasMode);
            }
        }
        [PropertyInfo(PropertyType = "Auto")]
        public int Layer
        {
            get
            {
                return _Layer;
            }
            set
            {
                _Layer = value;
                var Uicomp = GetRuntimeOwnerGameObject().GetComponentByMetaClassName("cegf::UIComponent") as Clicegf.UIComponent;
                Uicomp.SetLayer(value);
            }
        }
        [PropertyInfo(PropertyType = "Auto")]
        public bool FitWindow
        {
            get
            {
                return _FitWindow;
            }
            set
            {
                _FitWindow = value;
                var Uicomp = GetRuntimeOwnerGameObject().GetComponentByMetaClassName("cegf::UIComponent") as Clicegf.UIComponent;
                Uicomp.SetFitWindow(_FitWindow);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Default FontName.")]
        public string DefaultFontName
        {
            get
            {
                return _DefaultFontName;
            }
            set
            {
                _DefaultFontName = value;
                var Uicomp = GetRuntimeOwnerGameObject().GetComponentByMetaClassName("cegf::UIComponent") as Clicegf.UIComponent;
                Uicomp.SetDefaultFontName(_DefaultFontName);
            }
        }

        [PropertyInfo(PropertyType = "List", ChildPropertyType = "StringAsResource", DisplayName = "Used Fonts", FileTypeDescriptor = "#nda", ObjectClassID1 = ClassIDType.CLASS_FontResource)]
        public List<string> UsedFonts
        {
            get
            {
                return _UsedFonts;
            }
            set
            {
                _UsedFonts = value;
                var Uicomp = GetRuntimeOwnerGameObject().GetComponentByMetaClassName("cegf::UIComponent") as Clicegf.UIComponent;

                var usedFonts = new Vector_std_string_wrapper();
                for (int i = 0; i < _UsedFonts.Count; i++)
                {
                    var name = _UsedFonts[i];
                    usedFonts.holder.Add(name);
                }

                Uicomp.SetUsedFonts(usedFonts);
            }
        }

        public override void SyncDataFromEngine()
        {
            var Uicomp = GetRuntimeOwnerGameObject().GetComponentByMetaClassName("cegf::UIComponent") as Clicegf.UIComponent;
            _Path = Uicomp.GetPagePath();
            _Width = Uicomp.GetWidth();
            _Height = Uicomp.GetHeight();
            _FitWindow = Uicomp.GetFitWindow();
            _CanvasMode = (CanvasMode)Uicomp.GetCanvasMode();
            _Layer = Uicomp.GetLayer();
            _DefaultFontName = Uicomp.GetDefaultFontName();

            var usedFonts = new Vector_std_string_wrapper();
            Uicomp.GetUsedFonts(usedFonts);
            _UsedFonts.Clear();
            for (int i = 0; i < usedFonts.holder.Count; i++)
            {
                var name = usedFonts.holder[i];
                _UsedFonts.Add(name);
            }

        }
    }
}
