using CEngine;
using Clicross;
using System.ComponentModel;

namespace CrossEditor
{
    [ComponentAttribute(DisplayUINames = "Audio/AudioComponent", InspectorType = typeof(Inspector_GameComponent))]
    public class AudioComponent : GameObjectComponent
    {
        // variables
        private bool _IsSpatialListener;
        private string _Bank;

        // basic functions
        static string[] _NativeNames = { "cegf::AudioComponent" };
        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        public override void SyncDataFromEngine()
        {
            var comp = GetRuntimeOwnerGameObject().GetComponentByMetaClassName("cegf::AudioComponent") as Clicegf.AudioComponent;
            if (comp == null) return;
            _IsSpatialListener = comp.GetIsSpatialListener();
            _Bank = comp.GetBank();
        }

        // properties
        [PropertyInfo(PropertyType = "Auto", Category = "Listener")]
        public bool IsSpatialListener
        {
            get
            {
                return _IsSpatialListener;
            }
            set
            {
                _IsSpatialListener = value;
                var comp = GetRuntimeOwnerGameObject().GetComponentByMetaClassName("cegf::AudioComponent") as Clicegf.AudioComponent;
                comp.SetIsSpatialListener(_IsSpatialListener);
            }
        }

        [PropertyInfo(PropertyType = "Auto", Category = "Emitter")]
        public string Bank
        {
            get
            {
                return _Bank;
            }
            set
            {
                _Bank = value;
                var comp = GetRuntimeOwnerGameObject().GetComponentByMetaClassName("cegf::AudioComponent") as Clicegf.AudioComponent;
                comp.SetBank(_Bank);
            }
        }
    }

    [ComponentAttribute(DisplayUINames = "Audio/AudioObstacle", InspectorType = typeof(Inspector_GameComponent))]
    public class AudioObstacle : GameObjectComponent
    {
        // variables
        private bool _IsRoom;
        string _Texture = "Brick";
        bool _UseForReflectionAndDiffraction = true;
        bool _BypassPortalSubtraction = false;
        bool _IsSolid = false;
        string _ReverbAuxBus = "Room";
        float _ReverbLevel = 1.0f;
        float _TransmissionLoss = 1.0f;
        float _AuxSendLevelToSelf = 1.0f;
        float _Priority = 100.0f;

        // basic functions
        static string[] _NativeNames = { "cegf::AudioObstacle" };
        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        public override void SyncDataFromEngine()
        {
            var comp = GetRuntimeOwnerGameObject().GetComponentByMetaClassName("cegf::AudioObstacle") as Clicegf.AudioObstacle;
            if (comp == null) return;
            _IsRoom = comp.GetIsRoom();
            _Texture = comp.GetTexture();
            _UseForReflectionAndDiffraction = comp.GetUseForReflectionAndDiffraction();
            _BypassPortalSubtraction = comp.GetBypassPortalSubtraction();
            _IsSolid = comp.GetIsSolid();
            _ReverbAuxBus = comp.GetReverbAuxBus();
            _ReverbLevel = comp.GetReverbLevel();
            _TransmissionLoss = comp.GetTransmissionLoss();
            _AuxSendLevelToSelf = comp.GetAuxSendLevelToSelf();
            _Priority = comp.GetPriority();
        }

        // properties
        [PropertyInfo(PropertyType = "Auto")]
        public bool IsRoom
        {
            get
            {
                return _IsRoom;
            }
            set
            {
                _IsRoom = value;
                var comp = GetRuntimeOwnerGameObject().GetComponentByMetaClassName("cegf::AudioObstacle") as Clicegf.AudioObstacle;
                comp.SetIsRoom(_IsRoom);
            }
        }

        [PropertyInfo(PropertyType = "Auto")]
        public string Texture
        {
            get
            {
                return _Texture;
            }
            set
            {
                _Texture = value;
                var comp = GetRuntimeOwnerGameObject().GetComponentByMetaClassName("cegf::AudioObstacle") as Clicegf.AudioObstacle;
                comp.SetTexture(_Texture);
            }
        }

        [PropertyInfo(PropertyType = "Auto")]
        public bool UseForReflectionAndDiffraction
        {
            get
            {
                return _UseForReflectionAndDiffraction;
            }
            set
            {
                _UseForReflectionAndDiffraction = value;
                var comp = GetRuntimeOwnerGameObject().GetComponentByMetaClassName("cegf::AudioObstacle") as Clicegf.AudioObstacle;
                comp.SetUseForReflectionAndDiffraction(_UseForReflectionAndDiffraction);
            }
        }

        [PropertyInfo(PropertyType = "Auto")]
        public bool BypassPortalSubtraction
        {
            get
            {
                return _BypassPortalSubtraction;
            }
            set
            {
                _BypassPortalSubtraction = value;
                var comp = GetRuntimeOwnerGameObject().GetComponentByMetaClassName("cegf::AudioObstacle") as Clicegf.AudioObstacle;
                comp.SetBypassPortalSubtraction(_BypassPortalSubtraction);
            }
        }

        [PropertyInfo(PropertyType = "Auto")]
        public bool IsSolid
        {
            get
            {
                return _IsSolid;
            }
            set
            {
                _IsSolid = value;
                var comp = GetRuntimeOwnerGameObject().GetComponentByMetaClassName("cegf::AudioObstacle") as Clicegf.AudioObstacle;
                comp.SetIsSolid(_IsSolid);
            }
        }

        [PropertyInfo(PropertyType = "Auto")]
        public float TransmissionLoss
        {
            get
            {
                return _TransmissionLoss;
            }
            set
            {
                _TransmissionLoss = value;
                var comp = GetRuntimeOwnerGameObject().GetComponentByMetaClassName("cegf::AudioObstacle") as Clicegf.AudioObstacle;
                comp.SetTransmissionLoss(_TransmissionLoss);
            }
        }

        [PropertyInfo(PropertyType = "Auto", Category = "Room")]
        public string ReverbAuxBus
        {
            get
            {
                return _ReverbAuxBus;
            }
            set
            {
                _ReverbAuxBus = value;
                var comp = GetRuntimeOwnerGameObject().GetComponentByMetaClassName("cegf::AudioObstacle") as Clicegf.AudioObstacle;
                comp.SetReverbAuxBus(_ReverbAuxBus);
            }
        }

        [PropertyInfo(PropertyType = "Auto", Category = "Room")]
        public float ReverbLevel
        {
            get
            {
                return _ReverbLevel;
            }
            set
            {
                _ReverbLevel = value;
                var comp = GetRuntimeOwnerGameObject().GetComponentByMetaClassName("cegf::AudioObstacle") as Clicegf.AudioObstacle;
                comp.SetReverbLevel(_ReverbLevel);
            }
        }

        [PropertyInfo(PropertyType = "Auto", Category = "Room")]
        public float AuxSendLevelToSelf
        {
            get
            {
                return _AuxSendLevelToSelf;
            }
            set
            {
                _AuxSendLevelToSelf = value;
                var comp = GetRuntimeOwnerGameObject().GetComponentByMetaClassName("cegf::AudioObstacle") as Clicegf.AudioObstacle;
                comp.SetAuxSendLevelToSelf(_AuxSendLevelToSelf);
            }
        }

        [PropertyInfo(PropertyType = "Auto", Category = "Room")]
        public float Priority
        {
            get
            {
                return _Priority;
            }
            set
            {
                _Priority = value;
                var comp = GetRuntimeOwnerGameObject().GetComponentByMetaClassName("cegf::AudioObstacle") as Clicegf.AudioObstacle;
                comp.SetPriority(_Priority);
            }
        }
    }

    [ComponentAttribute(DisplayUINames = "Audio/AudioPortal", InspectorType = typeof(Inspector_GameComponent))]
    public class AudioPortal : GameObjectComponent
    {
        // variables
        private Clicross.Float3 _Extent;

        // basic functions
        static string[] _NativeNames = { "cegf::AudioPortal" };
        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        public override void SyncDataFromEngine()
        {
            var comp = GetRuntimeOwnerGameObject().GetComponentByMetaClassName("cegf::AudioPortal") as Clicegf.AudioPortal;
            _Extent = comp.GetExtent();
        }

        // properties
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Spline Points Position")]
        public Clicross.Float3 Extent
        {
            get
            {
                return _Extent;
            }
            set
            {
                _Extent = value;
                var comp = GetRuntimeOwnerGameObject().GetComponentByMetaClassName("cegf::AudioPortal") as Clicegf.AudioPortal;
                comp.SetExtent(_Extent);
            }
        }
    }
}
