using CEngine;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    public class Resource : IComparable<Resource>, IEquatable<Resource>
    {
        protected string _RelativePath = "";

        [PropertyInfo(PropertyType = "auto", bHide = true)]
        public ClassIDType ClassID { get; protected set; }
        [PropertyInfo(PropertyType = "auto", bHide = true)]
        public Clicross.Resource ResourcePtr { get; protected set; }
        [PropertyInfo(PropertyType = "auto", bHide = true)]
        public string Path { get { return _RelativePath; } protected set { _RelativePath = value; } }

        protected static Dictionary<ClassIDType, Type> ResourceRegister = new Dictionary<ClassIDType, Type>
        {
            { ClassIDType.CLASS_Material,typeof(Material)},
            { ClassIDType.CLASS_MaterialParameterCollection,typeof(MaterialParameterCollection)},
            { ClassIDType.CLASS_Texture,typeof(TextureResource)},
            { ClassIDType.CLASS_Texture2D,typeof(TextureResource)},
            { ClassIDType.CLASS_Texture3D,typeof(TextureResource)},
            { ClassIDType.CLASS_TextureCube,typeof(TextureResource)},
            { ClassIDType.CLASS_Texture2DVirtual,typeof(TextureResource)},
            { ClassIDType.CLASS_Texture2DArray,typeof(Texture2DArray)},
            { ClassIDType.CLASS_RenderTextureResource,typeof(RenderTexture)},
            { ClassIDType.CLASS_Fx,typeof(Fx)},
            { ClassIDType.CLASS_Shader,typeof(Shader)},
            { ClassIDType.CLASS_AnimatorRes, typeof(AnimatorResource)},
            { ClassIDType.CLASS_MotionDataAsset,typeof(MotionMatchAsset) },
            { ClassIDType.CLASS_AnimSequenceRes,typeof(PreviewAnimSeqAsset)},
            { ClassIDType.CLASS_AnimCompositeRes ,typeof(PreviewAnimCmpAsset)},
            { ClassIDType.CLASS_AnimatrixRes, typeof(PreviewAnimatrixAsset)},
            { ClassIDType.CLASS_AnimBlendSpaceRes,typeof(PreviewAnimBlendSpaceAsset)},
            { ClassIDType.CLASS_MeshAssetDataResource,typeof(MeshAssetDataResource)},
            { ClassIDType.CLASS_ParticleEmitterResource,typeof(ParticleEmitterResource)},
            { ClassIDType.CLASS_ParticleSystemResource,typeof(ParticleSystemResource)},
            { ClassIDType.CLASS_TerrainResource, typeof(TerrainResource)},
            { ClassIDType.CLASS_InstanceDataResource, typeof(InstanceDataResource)},
        };

        protected static HashSet<ClassIDType> ResourceAsFile = new HashSet<ClassIDType>
        {
            //ClassIDType.CLASS_Fx // we need the fx resource ptr now
        };

        protected Resource(Clicross.Resource ResPtr, ClassIDType ResClassID)
        {
            ResourcePtr = ResPtr;
            ClassID = ResClassID;
        }

        protected Resource()
        {
            ClassID = ClassIDType.CLASS_NullType;
        }

        ~Resource()
        {
            if (!ResourceAsFile.Contains(ClassID))
                Clicross.ResourceUtil.ReleaseResource(ResourcePtr);
        }

        public virtual void Reload()
        {
            ResourceManager.Instance().TryReloadResource(_RelativePath);
        }

        public static ClassIDType GetResourceTypeStatic(string ResPath)
        {
            return (ClassIDType)Clicross.ResourceUtil.ResourceGetClassID(ResPath);
        }

        public static Resource Get(string ResPath, bool Reload = true)
        {
            ClassIDType ClassID = GetResourceTypeStatic(ResPath);
            Clicross.Resource resource = null;
            if (!ResourceAsFile.Contains(ClassID))
            {
                resource = Clicross.ResourceUtil.ResourceGet(ResPath);
                if (resource == null || resource.GetPointer() == IntPtr.Zero)
                {
                    return null;
                }
            }

            if (ResourceRegister.ContainsKey(ClassID))
            {
                Type type = typeof(Resource);
                ResourceRegister.TryGetValue(ClassID, out type);

                Resource Res = (Resource)Activator.CreateInstance(type, resource);
                Res.Path = ResPath;
                if (Reload)
                {
                    Res.Reload();
                }

                return Res;
            }
            else
            {
                var Res = new Resource(resource, ClassID);
                Res.Path = ResPath;
                return Res;
            }
        }

        public static Resource Create(ClassIDType classid)
        {
            if (ResourceRegister.ContainsKey(classid))
            {
                Type type = typeof(Resource);
                ResourceRegister.TryGetValue(classid, out type);
                return (Resource)Activator.CreateInstance(type);
            }

            return new Resource();
        }
        public static bool IsObjectTexture(ClassIDType ClassID)
        {
            return ClassID == ClassIDType.CLASS_Texture || ClassID == ClassIDType.CLASS_Texture2D
                || ClassID == ClassIDType.CLASS_Texture3D || ClassID == ClassIDType.CLASS_TextureCube || ClassID == ClassIDType.CLASS_Texture2DVirtual;
        }
        public virtual void Save()
        {
            Clicross.ResourceUtil.ResourceSaveToFile(ResourcePtr, Path);
        }

        public virtual void SaveTo(string path)
        {
            Clicross.ResourceUtil.ResourceSaveToFile(ResourcePtr, path);
        }

        public int CompareTo(Resource other) { return other.Path == Path ? 0 : -1; }

        public int CompareTo(string PathOrGUID) { return ResourceManager.Instance().ConvertPathToGuid(PathOrGUID) == ResourceManager.Instance().ConvertPathToGuid(Path) ? 0 : -1; }

        public string GetGUID()
        {
            return ResourceManager.Instance().ConvertPathToGuid(Path);
        }

        public override bool Equals(object obj)
        {
            if (ReferenceEquals(null, obj)) return false;
            if (ReferenceEquals(this, obj)) return true;

            return Equals(obj as Resource);
        }

        public bool Equals(Resource other)
        {
            if (ResourcePtr.GetPointer() != IntPtr.Zero && ResourcePtr == other.ResourcePtr) return true;
            if (CompareTo(other.Path) == 0) return true;
            return false;
        }

        public override int GetHashCode()
        {
            return 0;
        }
    }
}
