using System.Collections.Generic;

namespace CrossEditor
{
    public class MaterialInstance : Material
    {
        const string Suffix = "_Instance";
        public MaterialInstance(Clicross.Resource resourcePtr) : base(resourcePtr)
        {
            Properties = new List<Property>();
            PassProperties = new List<PassProperty>();
        }

        public override void Reload()
        {
            base.Refresh();
        }
    }
}
