using CEngine;
using System;

namespace CrossEditor
{
    public struct Quaterniond
    {
        public double X;
        public double Y;
        public double Z;
        public double W;

        public Quaterniond(double X, double Y, double Z, double W)
        {
            this.X = X;
            this.Y = Y;
            this.Z = Z;
            this.W = W;
        }

        public Quaterniond(Quaternion q)
        {
            X = q.x;
            Y = q.y;
            Z = q.z;
            W = q.w;
        }

        public Quaterniond(Quaternion64 q)
        {
            X = q.x;
            Y = q.y;
            Z = q.z;
            W = q.w;
        }

        public double Length()
        {
            return Math.Sqrt(X * X + Y * Y + Z * Z + W * W);
        }

        public void Normalize()
        {
            double Length1 = Length();
            double InverseLength = 1.0 / Length1;
            X = X * InverseLength;
            Y = Y * InverseLength;
            Z = Z * InverseLength;
            W = W * InverseLength;
        }

        public Quaternion ToQuaternion()
        {
            Quaternion q = new Quaternion();
            q.x = (float)X;
            q.y = (float)Y;
            q.z = (float)Z;
            q.w = (float)W;
            return q;
        }

        public Quaternion64 ToQuaternion64()
        {
            Quaternion64 q = new Quaternion64();
            q.x = X;
            q.y = Y;
            q.z = Z;
            q.w = W;
            return q;
        }

        //local Y - X - Z order
        public static Quaterniond FromEuler(double pitch, double yaw, double roll)
        {
            pitch = ToRadian(pitch);
            yaw = ToRadian(yaw);
            roll = ToRadian(roll);
            double rollOver2 = roll * 0.5f;
            double sinRollOver2 = Math.Sin(rollOver2);
            double cosRollOver2 = Math.Cos(rollOver2);
            double pitchOver2 = pitch * 0.5f;
            double sinPitchOver2 = Math.Sin(pitchOver2);
            double cosPitchOver2 = Math.Cos(pitchOver2);
            double yawOver2 = yaw * 0.5f;
            double sinYawOver2 = Math.Sin(yawOver2);
            double cosYawOver2 = Math.Cos(yawOver2);
            Quaterniond result;
            result.W = cosYawOver2 * cosPitchOver2 * cosRollOver2 + sinYawOver2 * sinPitchOver2 * sinRollOver2;
            result.X = cosYawOver2 * sinPitchOver2 * cosRollOver2 + sinYawOver2 * cosPitchOver2 * sinRollOver2;
            result.Y = sinYawOver2 * cosPitchOver2 * cosRollOver2 - cosYawOver2 * sinPitchOver2 * sinRollOver2;
            result.Z = cosYawOver2 * cosPitchOver2 * sinRollOver2 - sinYawOver2 * sinPitchOver2 * cosRollOver2;

            return result;
        }

        public static Quaterniond FromRotationMatrix(Matrix4x4f rotmat)
        {
            Quaterniond result = new Quaterniond();
            result.W = (Math.Sqrt(rotmat.M11 + rotmat.M22 + rotmat.M33 + 1) * 0.5);
            result.X = (rotmat.M32 - rotmat.M23) / (4 * result.W);
            result.Y = (rotmat.M13 - rotmat.M31) / (4 * result.W);
            result.Z = (rotmat.M21 - rotmat.M12) / (4 * result.W);
            return result;
        }

        public static Quaterniond FromAxisRotation(Vector3d Axis, double Angle)
        {
            Quaterniond Result = new Quaterniond();
            double HalfAngle = Angle * 0.5f;
            double CosHalfAngle = Math.Cos(HalfAngle);
            double SinHalfAngle = Math.Sin(HalfAngle);
            Result.W = CosHalfAngle;
            Result.X = Axis.X * SinHalfAngle;
            Result.Y = Axis.Y * SinHalfAngle;
            Result.Z = Axis.Z * SinHalfAngle;
            return Result;
        }

        public static Quaterniond Concatenate(Quaterniond Q1, Quaterniond Q2)
        {
            Quaterniond q = new Quaterniond();
            q.X = (Q2.W * Q1.X) + (Q2.X * Q1.W) + (Q2.Y * Q1.Z) - (Q2.Z * Q1.Y);
            q.Y = (Q2.W * Q1.Y) - (Q2.X * Q1.Z) + (Q2.Y * Q1.W) + (Q2.Z * Q1.X);
            q.Z = (Q2.W * Q1.Z) + (Q2.X * Q1.Y) - (Q2.Y * Q1.X) + (Q2.Z * Q1.W);
            q.W = (Q2.W * Q1.W) - (Q2.X * Q1.X) - (Q2.Y * Q1.Y) - (Q2.Z * Q1.Z);
            return q;
        }

        //local Y - X - Z order
        public static Vector3d ToEuler(Quaterniond q1)
        {
            double x = q1.X, y = q1.Y, z = q1.Z, w = q1.W;
            double temp = 2 * (y * z - x * w);
            double eps = 1.192092896e-7f;
            Vector3d v;

            if (temp >= 1 - eps)
            {
                v.X = -Math.PI / 2;
                v.Y = Math.Atan2(y, w);
                v.Z = Math.Atan2(z, w);
            }
            else if (-temp >= 1 - eps)
            {
                v.X = Math.PI / 2;
                v.Y = Math.Atan2(y, w);
                v.Z = Math.Atan2(z, w);
            }
            else
            {

                v.X = -Math.Asin(temp);
                v.Y = Math.Atan2(x * z + y * w, 0.5f - x * x - y * y);
                v.Z = Math.Atan2(x * y + z * w, 0.5f - x * x - z * z);
            }
            v.X = ToAngle(v.X);
            v.Y = ToAngle(v.Y);
            v.Z = ToAngle(v.Z);
            return NormalizeAngles(v);
        }

        static Vector3d NormalizeAngles(Vector3d angles)
        {
            angles.X = NormalizeAngle(angles.X);
            angles.Y = NormalizeAngle(angles.Y);
            angles.Z = NormalizeAngle(angles.Z);
            return angles;
        }

        static double NormalizeAngle(double angle)
        {
            while (angle > 360)
                angle -= 360;
            while (angle < 0)
                angle += 360;
            return angle;
        }

        static double ToAngle(double radian)
        {
            return (radian / Math.PI * 180);
        }

        static double ToRadian(double angle)
        {
            return (angle / 180 * Math.PI);
        }

        public override string ToString()
        {
            return string.Format("({0}, {1}, {2}, {3})", X, Y, Z, W);
        }
    }
}
