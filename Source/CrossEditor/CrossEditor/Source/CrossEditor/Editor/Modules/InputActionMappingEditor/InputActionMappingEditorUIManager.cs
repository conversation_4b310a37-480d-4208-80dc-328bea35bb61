using EditorUI;
using System.Collections.Generic;

namespace CrossEditor
{
    public class InputActionMappingEditorUIManager
    {
        public static InputActionMappingEditorUIManager Instance { get; } = new InputActionMappingEditorUIManager();
        private Dictionary<string, InputActionMappingEditorUI> mInputActionMappingEditors = new Dictionary<string, InputActionMappingEditorUI>();

        InputActionMappingEditorUIManager() { }

        public void Update(long TimeElapsed)
        {
        }
        public void OpenInputActionMapping(string FilePath)
        {
            DockingCard dockingCard = null;
            if (!mInputActionMappingEditors.ContainsKey(FilePath))
            {
                var editor = new InputActionMappingEditorUI(FilePath);
                string fileName = FilePath.Substring(FilePath.LastIndexOf('/') + 1);
                editor.Initialize(fileName);
                dockingCard = editor.GetDockingCard();
                dockingCard.SetDocument(true);
                mInputActionMappingEditors.Add(FilePath, editor);
                dockingCard.CloseEvent += (DockingCard Sender, ref bool bNotToClose) =>
                {
                    mInputActionMappingEditors.Remove(FilePath);
                };
            }
            else
            {
                dockingCard = mInputActionMappingEditors[FilePath].GetDockingCard();
            }
            MainUI.GetInstance().ActivateDockingCard_WorkflowEditor(dockingCard);
        }
    }
}