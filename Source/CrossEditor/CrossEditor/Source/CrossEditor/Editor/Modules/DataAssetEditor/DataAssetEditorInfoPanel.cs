using CEngine;
using Clicross;
using Clicross.resource;
using EditorUI;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Reflection;
using ClassIDType = CEngine.ClassIDType;

namespace CrossEditor
{
    public class DataAssetEditorInfoPanel
    {
        int _RowHeight = 30;
        OperationBarUI _OperationBarUI = new OperationBarUI();
        ScrollView _ScrollView = new ScrollView();
        Panel _ScrollPanel;
        private DataAssetEditorContext _mContext;
        private List<Inspector> _mInfoItems = new List<Inspector>();

        public DataAssetEditorInfoPanel()
        {
            _OperationBarUI.Initialize();
            _OperationBarUI.GetPanelBar().SetText("Info");
            _OperationBarUI.GetPanelBar().SetTextAlign(TextAlign.CenterLeft);
            _OperationBarUI.GetPanelBar().SetFontSize(20);
            _OperationBarUI.GetPanelBar().SetTextOffsetX(10);

            _ScrollView.Initialize();
            _ScrollView.SetBackgroundColor(Color.FromRGBA(36, 36, 36, (int)byte.MaxValue));
            _ScrollView.GetHScroll().SetEnable(true);
            _ScrollView.GetVScroll().SetEnable(true);
            _ScrollView.PositionChangedEvent += (Control sender, bool poschange, bool sizechange) =>
            {
                RefreshInspectorUI();
            };
            _ScrollPanel = _ScrollView.GetScrollPanel();
        }

        public void BindProperty(DataAssetEditorContext context)
        {
            _mContext = context;

            // Generate DA Info items
            Inspector inspector;
            for(var it = _mContext.mInfoItems.Begin(); it != _mContext.mInfoItems.End(); ++ it)
            {
                DataAssetInfoItem infoItem = it.Value;
                if (infoItem != null)
                {
                    if (infoItem.name.Contains("Guid"))
                        inspector = CreateItemInspector_Asset(infoItem.name, infoItem.value);
                    else
                        inspector = CreateItemInspector(infoItem.name, infoItem.value);
                    inspector.SetContainer(_ScrollPanel);
                    _mInfoItems.Add(inspector);
                }
            }

            RefreshInspectorUI();
        }

        public void OnAddToParent(VContainer mContainer)
        {
            mContainer.AddFixedChild(_OperationBarUI.GetPanelBar());
            mContainer.AddSizableChild(_ScrollView, 1);
        }

        Inspector CreateItemInspector(string name, string value)
        {
            PropertyInfoAttribute PropertyInfoAttribute = new PropertyInfoAttribute();
            Inspector inspector;
            ObjectProperty ObjectProperty = new ObjectProperty();
            InspectorHandler inspectorHandler = new InspectorHandler();
            ObjectProperty.Name = "Value";
            ObjectProperty.DisplayName = name;
            ObjectProperty.DefaultValue = value;
            PropertyInfoAttribute.PropertyType = typeof(string).ToString();
            ObjectProperty.ReadOnly = true;
            ObjectProperty.GetPropertyValueFunction = (object Object, string PropertyName, ValueExtraProperty ValueExtraProperty) =>{ return value; };

            inspector = InspectorManager.GetInstance().CreatePropertyInspector(PropertyInfoAttribute.PropertyType, false);
            inspector.InspectProperty(ObjectProperty);
            inspectorHandler.UpdateLayout = RefreshInspectorUI;
            inspectorHandler.ReadValue = inspector.ReadValue;
            inspector.SetInspectorHandler(inspectorHandler);
            return inspector;
        }

        Inspector CreateItemInspector_Asset(string name, string guid)
        {
            PropertyInfoAttribute PropertyInfoAttribute = new PropertyInfoAttribute();
            Inspector inspector;
            ObjectProperty ObjectProperty = new ObjectProperty();
            InspectorHandler inspectorHandler = new InspectorHandler();
            ObjectProperty.Name = "Value";
            ObjectProperty.DisplayName = name;

            PropertyInfoAttribute.PropertyType = "StringAsResource";
            ObjectProperty.GetPropertyValueFunction =
                (object Object, string PropertyName, ValueExtraProperty ValueExtraProperty) =>
                {
                    return ResourceManager.Instance().ConvertGuidToPath(guid);
                };
            ObjectProperty.ReadOnly = true;
            ObjectProperty.PropertyInfoAttribute = PropertyInfoAttribute;

            inspector = InspectorManager.GetInstance().CreatePropertyInspector(PropertyInfoAttribute.PropertyType, false);
            inspector.InspectProperty(ObjectProperty);
            inspectorHandler.UpdateLayout = RefreshInspectorUI;
            inspectorHandler.ReadValue = inspector.ReadValue;
            inspector.SetInspectorHandler(inspectorHandler);
            ((Inspector_Property_Resource)inspector).SetResourcePathToUI(ResourceManager.Instance().ConvertGuidToPath(guid));
            return inspector;
        }

        private void RefreshInspectorUI()
        {
            int ScrollPanelWidth = _ScrollPanel.GetWidth();
            int Y = 0;
            if (_mInfoItems != null)
            {
                //Calculate the sum of the heights of all items 
                foreach (var item in _mInfoItems)
                {
                    Y += _RowHeight;
                }
                if (Y > _ScrollPanel.GetHeight())
                {
                    ScrollPanelWidth = _ScrollPanel.GetWidth() - ScrollView.SCROLL_BAR_SIZE;
                }

                //Set position of items
                Y = 0;
                foreach (var inspector in _mInfoItems)
                {
                    inspector.UpdateLayout(ScrollPanelWidth, ref Y);
                    if (Y > _ScrollView.GetHeight())
                    {
                        ScrollPanelWidth = _ScrollView.GetWidth() - ScrollView.SCROLL_BAR_SIZE;
                        Y = 0;
                        inspector.UpdateLayout(ScrollPanelWidth, ref Y);
                    }
                }
            }
            _ScrollPanel.SetHeight(Math.Max(_ScrollPanel.GetHeight(), Y + 50));
            _ScrollView.UpdateScrollBar();
        }

    }
}