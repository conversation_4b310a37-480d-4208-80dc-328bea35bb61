using System.Collections.Generic;

namespace CrossEditor
{
    public class EditOperation_DeleteStop : EditOperation
    {
        MoveStop CurMoveStop;
        int MouseX = 0;
        int MouseY = 0;
        bool IsOnlyStop = false;
        public EditOperation_DeleteStop(MoveStop MoveStop, int MouseX, int MouseY, bool IsOnlyStop = false)
        {
            this.CurMoveStop = MoveStop;
            this.MouseX = MouseX;
            this.MouseY = MouseY;
            this.IsOnlyStop = IsOnlyStop;
        }

        public override void Redo()
        {
            StopType StopType = CurMoveStop._Type;
            if (StopType == StopType.ColorStop)
            {
                LinearColorCurveEditorUI.GetInstance().GradientColor._ColorStops.Remove(CurMoveStop);
            }
            else
            {
                LinearColorCurveEditorUI.GetInstance().GradientColor._AlphaStops.Remove(CurMoveStop);
            }
            LinearColorCurveEditorUI.GetInstance().DeleteKeyInGradientAlpha(CurMoveStop._ContainPoints);
        }

        public override void Undo()
        {
            StopType StopType = CurMoveStop._Type;
            if (StopType == StopType.ColorStop)
            {
                LinearColorCurveEditorUI.GetInstance().GradientColor._ColorStops.Add(CurMoveStop);
            }
            else
            {
                LinearColorCurveEditorUI.GetInstance().GradientColor._AlphaStops.Add(CurMoveStop);
            }
            if (!IsOnlyStop)
            {
                List<Point> Points = LinearColorCurveEditorUI.GetInstance().AddKeyInGradient(MouseX, MouseY, StopType);
                CurMoveStop.SetPoints(Points);
            }
        }
    }
}
