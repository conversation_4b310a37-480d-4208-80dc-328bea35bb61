using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;

namespace CrossEditor
{
    public static class CurveMathHelper
    {
        public enum LOC_SIGN : uint { UP = 0b0001, BOTTOM = 0b0010, LEFT = 0b0100, RIGHT = 0b1000 };

        static public readonly float FloatPresicion = 1e-8f;

        static public bool IsNearlyZero(float N)
        {
            return (N >= 0f && N < FloatPresicion) || (N < 0f && N > -FloatPresicion);
        }

        static public bool IsPointInPolygon(Vector2m Point, Vector2m[] Polygon)
        {
            if (Polygon.Count() >= 3)
            {
                int IntersectCount = 0;
                for (int i = 0; i < Polygon.Count(); ++i)
                {
                    Vector2m P0 = Polygon[i];
                    Vector2m P1 = Polygon[(i + 1) % Polygon.Count()];

                    if (P0.Y > Point.Y && P1.Y > Point.Y || P0.Y < Point.Y && P1.Y < Point.Y)
                    {
                        continue;
                    }

                    if (P0.Y == P1.Y)
                    {
                        if (P0.Y == Point.Y && (P0.X >= Point.X || P1.X >= Point.X))
                        {
                            IntersectCount++;
                        }
                        continue;
                    }

                    decimal K = (P1.X - P0.X) / (P1.Y - P0.Y);
                    decimal X = K * (Point.Y - P0.Y) + P0.X;
                    if (X >= Point.X) IntersectCount++;
                }

                return IntersectCount % 2 != 0;
            }

            return false;
        }

        static public float Lerp(float Start, float End, float Alpha)
        {
            return Start + (End - Start) * Alpha;
        }

        static public int FindNearestPointIndex(float X, float Y, List<Vector2f> PointList)
        {
            int NearestPointIndex = -1;
            float MinDistance = float.MaxValue;

            for (int i = 0; i < PointList.Count(); ++i)
            {
                float DeltaX = PointList[i].X - X;
                float DeltaY = PointList[i].Y - Y;
                float Distance = DeltaX * DeltaX + DeltaY * DeltaY;
                if (Distance < MinDistance)
                {
                    NearestPointIndex = i;
                    MinDistance = Distance;
                }
            }

            return NearestPointIndex;
        }

        static public float FindNextInUnit(float Value, float Unit)
        {
            return (float)Math.Ceiling(Value / Unit) * Unit;
        }

        static public decimal FindNextInUnit(decimal Value, decimal Unit)
        {
            return Math.Ceiling(Value / Unit) * Unit;
        }

        static public float Distance(Vector2f P0, Vector2f P1)
        {
            float DeltaX = P1.X - P0.X;
            float DeltaY = P1.Y - P0.Y;
            return (float)Math.Sqrt(DeltaX * DeltaX + DeltaY * DeltaY);
        }

        static public float Distance(Vector2f Point, Vector2f LineBegin, Vector2f LineEnd)
        {
            double DeltaX = LineEnd.X - LineBegin.X;
            double DeltaXSq = DeltaX * DeltaX;
            double DeltaY = LineEnd.Y - LineBegin.Y;
            double DeltaYSq = DeltaY * DeltaY;

            double A = Math.Abs(DeltaX * (LineBegin.Y - Point.Y) - (LineBegin.X - Point.X) * DeltaY);
            double B = Math.Sqrt(DeltaXSq + DeltaYSq);

            return (float)(A / B);
        }

        static public float DegreeToRadian(float Angle)
        {
            return (float)(Angle / 180f * Math.PI);
        }

        static public float RadianToDegree(float Radian)
        {
            return (float)(Radian / Math.PI * 180f);
        }

        static public RectangleF GetBound(Vector2f P0, Vector2f P1)
        {
            float X = Math.Min(P0.X, P1.X);
            float Y = Math.Min(P0.Y, P1.Y);
            float Width = Math.Abs(P0.X - P1.X);
            float Height = Math.Abs(P0.Y - P1.Y);

            return new RectangleF(X, Y, Width, Height);
        }

        static public Vector2f[] GetSphere(Vector2f Origin, float Radius, int Segment)
        {
            double Delta = 2f * Math.PI / Segment;
            double Theta = 0;
            Vector2f[] SpherePoints = new Vector2f[Segment];
            for (int i = 0; i < Segment; ++i)
            {
                Vector2f NextPoint = new Vector2f()
                {
                    X = Origin.X + (float)Math.Cos(Theta) * Radius,
                    Y = Origin.Y + (float)Math.Sin(Theta) * Radius
                };
                SpherePoints[i] = NextPoint;
                Theta += Delta;
            }

            return SpherePoints;
        }

        static public void Swap<T>(ref T a, ref T b)
        {
            T tmp = a;
            a = b;
            b = tmp;
        }

        static public float SolveLinearEquation(float A, float B)
        {
            return IsNearlyZero(A) ? float.NaN : -B / A;
        }
        static public decimal SolveLinearEquation(decimal A, decimal B)
        {
            return IsNearlyZero((float)A) ? decimal.MinValue: -B / A;
        }

        static public Complex[] SolveQuadraticEquation(float A, float B, float C)
        {
            double BSquared = B * B;
            double Delta = BSquared - 4.0 * A * C;

            Complex X_0 = (-B + Complex.Sqrt(Delta)) / (2.0 * A);
            Complex X_1 = (-B - Complex.Sqrt(Delta)) / (2.0 * A);

            return new Complex[] { X_0, X_1 };
        }

        static public Complex[] SolveCubicEquation(float A, float B, float C, float D)
        {
            double ASquared = A * A;
            double ACubed = ASquared * A;
            double BSquared = B * B;
            double BCubed = BSquared * B;

            //double delta = 18.0 * A * B * C * D - 4.0 * BCubed * D + BSquared * C * C - 4.0 * A * C * C * C - 27.0 * ASquared * D * D;
            //Console.WriteLine(delta);

            double Delta_0 = BSquared - 3.0 * A * C;
            double Delta_1 = 2.0 * BCubed - 9.0 * A * B * C + 27.0 * ASquared * D;
            Complex Delta = Complex.Pow((Delta_1 - Complex.Sqrt(Delta_1 * Delta_1 - 4.0 * Delta_0 * Delta_0 * Delta_0)) / 2.0, 1.0 / 3.0);
            if (Delta == new Complex(0, 0))
                Delta = Complex.Pow((Delta_1 + Complex.Sqrt(Delta_1 * Delta_1 - 4.0 * Delta_0 * Delta_0 * Delta_0)) / 2.0, 1.0 / 3.0);

            Complex X_0 = -1.0 / (3.0 * A) * (B + Delta + Delta_0 / Delta);

            Complex P = new Complex(-1.0 / 2.0, Math.Sqrt(3.0) / 2.0);
            Complex Q = new Complex(-1.0 / 2.0, -Math.Sqrt(3.0) / 2.0);

            Complex X_1 = -1.0 / (3.0 * A) * (B + P * Delta + Q * Delta_0 / Delta);
            Complex X_2 = -1.0 / (3.0 * A) * (B + Q * Delta + P * Delta_0 / Delta);

            return new Complex[] { X_0, X_1, X_2 };
        }

        static public class CohenSutherland
        {

            public enum OutCode { INSEIDE = 0b0000, LEFT = 0b0001, RIGHT = 0b0010, BOTTOM = 0b0100, TOP = 0b1000 };

            static public OutCode ComputeOutCode(Vector2f Point, RectangleF Rect)
            {
                OutCode Code = OutCode.INSEIDE;
                if (Point.X < Rect.X) Code |= OutCode.LEFT;
                else if (Point.X > Rect.X + Rect.Width) Code |= OutCode.RIGHT;

                if (Point.Y < Rect.Y) Code |= OutCode.TOP;
                else if (Point.Y > Rect.Y + Rect.Height) Code |= OutCode.BOTTOM;

                return Code;
            }
            static public OutCode ComputeOutCode(Vector2m Point, RectangleM Rect)
            {
                OutCode Code = OutCode.INSEIDE;
                if (Point.X < Rect.X) Code |= OutCode.LEFT;
                else if (Point.X > Rect.X + Rect.Width) Code |= OutCode.RIGHT;

                if (Point.Y < Rect.Y) Code |= OutCode.TOP;
                else if (Point.Y > Rect.Y + Rect.Height) Code |= OutCode.BOTTOM;

                return Code;
            }
            static public bool Intersect(Vector2f P0, Vector2f P1, RectangleF Rect, out Vector2f Out0, out Vector2f Out1)
            {
                OutCode StartCode = ComputeOutCode(P0, Rect);
                OutCode EndCode = ComputeOutCode(P1, Rect);

                float XMin = Rect.X;
                float YMin = Rect.Y;
                float XMax = Rect.X + Rect.Width;
                float YMax = Rect.Y + Rect.Height;

                Out0 = P0;
                Out1 = P1;

                while (true)
                {
                    if ((StartCode | EndCode) == OutCode.INSEIDE)
                    {
                        return true;
                    }
                    else if ((StartCode & EndCode) != OutCode.INSEIDE)
                    {
                        return false;
                    }
                    else
                    {
                        float X = 0f, Y = 0f;
                        OutCode OutCodeOut = StartCode > EndCode ? StartCode : EndCode;
                        if ((OutCodeOut & OutCode.TOP) != OutCode.INSEIDE)
                        {
                            X = Out0.X + (Out0.X - Out1.X) * (YMin - Out0.Y) / (Out0.Y - Out1.Y);
                            Y = YMin;
                        }
                        else if ((OutCodeOut & OutCode.BOTTOM) != OutCode.INSEIDE)
                        {
                            X = Out0.X + (Out0.X - Out1.X) * (YMax - Out0.Y) / (Out0.Y - Out1.Y);
                            Y = YMax;
                        }
                        else if ((OutCodeOut & OutCode.RIGHT) != OutCode.INSEIDE)
                        {
                            Y = Out0.Y + (Out0.Y - Out1.Y) * (XMax - Out0.X) / (Out0.X - Out1.X);
                            X = XMax;
                        }
                        else if ((OutCodeOut & OutCode.LEFT) != OutCode.INSEIDE)
                        {
                            Y = Out0.Y + (Out0.Y - Out1.Y) * (XMin - Out0.X) / (Out0.X - Out1.X);
                            X = XMin;
                        }

                        if (OutCodeOut == StartCode)
                        {
                            Out0.X = X;
                            Out0.Y = Y;
                            StartCode = ComputeOutCode(Out0, Rect);
                        }
                        else
                        {
                            Out1.X = X;
                            Out1.Y = Y;
                            EndCode = ComputeOutCode(Out1, Rect);
                        }
                    }
                }
            }
            static public bool Intersect(Vector2m P0, Vector2m P1, RectangleM Rect, out Vector2m Out0, out Vector2m Out1)
            {
                OutCode StartCode = ComputeOutCode(P0, Rect);
                OutCode EndCode = ComputeOutCode(P1, Rect);

                decimal XMin = Rect.X;
                decimal YMin = Rect.Y;
                decimal XMax = Rect.X + Rect.Width;
                decimal YMax = Rect.Y + Rect.Height;

                Out0 = P0;
                Out1 = P1;

                while (true)
                {
                    if ((StartCode | EndCode) == OutCode.INSEIDE)
                    {
                        return true;
                    }
                    else if ((StartCode & EndCode) != OutCode.INSEIDE)
                    {
                        return false;
                    }
                    else
                    {
                        decimal X = 0, Y = 0;
                        OutCode OutCodeOut = StartCode > EndCode ? StartCode : EndCode;
                        if ((OutCodeOut & OutCode.TOP) != OutCode.INSEIDE)
                        {
                            X = Out0.X + (Out0.X - Out1.X) * (YMin - Out0.Y) / (Out0.Y - Out1.Y);
                            Y = YMin;
                        }
                        else if ((OutCodeOut & OutCode.BOTTOM) != OutCode.INSEIDE)
                        {
                            X = Out0.X + (Out0.X - Out1.X) * (YMax - Out0.Y) / (Out0.Y - Out1.Y);
                            Y = YMax;
                        }
                        else if ((OutCodeOut & OutCode.RIGHT) != OutCode.INSEIDE)
                        {
                            Y = Out0.Y + (Out0.Y - Out1.Y) * (XMax - Out0.X) / (Out0.X - Out1.X);
                            X = XMax;
                        }
                        else if ((OutCodeOut & OutCode.LEFT) != OutCode.INSEIDE)
                        {
                            Y = Out0.Y + (Out0.Y - Out1.Y) * (XMin - Out0.X) / (Out0.X - Out1.X);
                            X = XMin;
                        }

                        if (OutCodeOut == StartCode)
                        {
                            Out0.X = X;
                            Out0.Y = Y;
                            StartCode = ComputeOutCode(Out0, Rect);
                        }
                        else
                        {
                            Out1.X = X;
                            Out1.Y = Y;
                            EndCode = ComputeOutCode(Out1, Rect);
                        }
                    }
                }
            }
        }
    }

    public class RectangleF
    {
        public float X, Y, Width, Height;

        public RectangleF()
        {
            X = Y = Width = Height = 0;
        }

        public RectangleF(float X, float Y, float Width, float Height)
        {
            this.X = X;
            this.Y = Y;
            this.Width = Width;
            this.Height = Height;
        }

        public bool Contains(Vector2f Point)
        {
            return Point.X <= X + Width && Point.X >= X && Point.Y <= Y + Height && Point.Y >= Y;
        }

        public bool Contains(float X, float Y)
        {
            return X <= this.X + Width && X >= this.X && Y <= this.Y + Height && Y >= this.Y;
        }

        public bool Contains(RectangleF Bound)
        {
            return this.X <= Bound.X && this.Y <= Bound.Y && this.X + this.Width >= Bound.X + Bound.Width && this.Y + this.Height >= Bound.Y + Bound.Height;
        }
    }
    public class RectangleM
    {
        public decimal X, Y, Width, Height;

        public RectangleM()
        {
            X = Y = Width = Height = 0;
        }

        public RectangleM(decimal X, decimal Y, decimal Width, decimal Height)
        {
            this.X = X;
            this.Y = Y;
            this.Width = Width;
            this.Height = Height;
        }

        public bool Contains(Vector2m Point)
        {
            return Point.X <= X + Width && Point.X >= X && Point.Y <= Y + Height && Point.Y >= Y;
        }

        public bool Contains(decimal X, decimal Y)
        {
            return X <= this.X + Width && X >= this.X && Y <= this.Y + Height && Y >= this.Y;
        }

        public bool Contains(RectangleM Bound)
        {
            return this.X <= Bound.X && this.Y <= Bound.Y && this.X + this.Width >= Bound.X + Bound.Width && this.Y + this.Height >= Bound.Y + Bound.Height;
        }
    }
}
