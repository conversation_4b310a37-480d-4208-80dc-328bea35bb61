using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CrossEditor
{
    public delegate void PostModifiedCurvetEventHandler();
    public delegate void PostAddPointEventHandler(CurveManager Sender, Point NewPoint);
    public delegate void PostRemovePointEventHandler(CurveManager Sender, Point RemovedPoint);

    public class CurveManager
    {
        public string Name;

        public List<Point> Points;

        public float Width;
        public Color UnselectedColor;

        public CurveRepeatType EnterRepeatType;
        public CurveRepeatType LeaveRepeatType;
        public CurveUseType UseType;

        public List<BaseCurve> BaseCurves;

        public bool bSelected;
        public bool bGridOn;

        public event PostModifiedCurvetEventHandler PostModifiedCurveEvent;
        public event PostAddPointEventHandler PostAddPointEvent;
        public event PostRemovePointEventHandler PostRemovePointEvent;

        public CurveManager(CurveRepeatType EnterRepeatType = CurveRepeatType.CRT_CONSTANT,
                            CurveRepeatType LeaveRepeatType = CurveRepeatType.CRT_CYCLE)
        {
            Name = "";

            Points = new List<Point>();

            Width = 2;
            UnselectedColor = Color.White;

            this.EnterRepeatType = EnterRepeatType;
            this.LeaveRepeatType = LeaveRepeatType;
            UseType = CurveUseType.Common;

            BaseCurves = new List<BaseCurve>();

            bSelected = false;
            bGridOn = true;
        }
        public FloatCurveTrack RuntimeCurve
        {
            set
            {
                DebugHelper.Assert(value.Validate());
                Points.Clear();
                Name = value.Name.GetCString();
                foreach (var key in value.Keys)
                {
                    Point point;
                    switch (key.InterpType)
                    {
                        case KeyInterpType.KIT_LINEAR:
                            point = new LinearPoint();
                            break;
                        case KeyInterpType.KIT_CONSTANT:
                            point = new ConstantPoint();
                            break;
                        case KeyInterpType.KIT_SMOOTH:
                            point = new SmoothPoint();
                            break;
                        default:
                            point = new LinearPoint();
                            break;
                    }
                    point.Initialize(key.Time, key.IsDouble?(decimal)key.DoubleValue:(decimal)key.Value, 10, this);

                    if (point is SmoothPoint)
                    {
                        var cubicPoint = point as SmoothPoint;
                        cubicPoint.Mode = key.SmoothType;
                        cubicPoint.AutoWeighted = key.AutoWeighted;
                        cubicPoint.EventString = key.EventString;
                        cubicPoint.InitializeControlPoints((decimal)key.ArriveWeight, (decimal)key.ArriveTangent, (decimal)key.LeaveWeight, (decimal)key.LeaveTangent);
                    }
                    AddPoint(point);
                }

                EnterRepeatType = value.EnterType;
                LeaveRepeatType = value.LeaveType;
                UseType = value.UseType;

                UpdateCurves();
            }
            get
            {
                UpdateCurves();
                FloatCurveTrack runtimeCurve = new FloatCurveTrack();
                runtimeCurve.Name = new UniqueString(Name);
                foreach (var point in Points)
                {
                    FloatCurveKey key = new FloatCurveKey();
                    key.Value = (float)point.ValueY;
                    key.DoubleValue = (double)point.ValueY; // is double or not
                    key.IsDouble = true;
                    key.Time = point.ValueX;
                    switch (point.PointType)
                    {
                        case PointType.Linear:
                            key.InterpType = KeyInterpType.KIT_LINEAR;
                            break;
                        case PointType.Constant:
                            key.InterpType = KeyInterpType.KIT_CONSTANT;
                            break;
                        case PointType.Smooth:
                            key.InterpType = KeyInterpType.KIT_SMOOTH;
                            break;
                        default:
                            key.InterpType = KeyInterpType.KIT_LINEAR;
                            break;
                    }
                    if (point is SmoothPoint)
                    {
                        var cubicPoint = point as SmoothPoint;
                        key.SmoothType = cubicPoint.Mode;
                        key.AutoWeighted = cubicPoint.AutoWeighted;
                        key.ArriveWeight = (float)cubicPoint.ControlPointLeft.Weight;
                        key.ArriveTangent = (float)cubicPoint.ControlPointLeft.Tangent;
                        key.LeaveWeight = (float)cubicPoint.ControlPointRight.Weight;
                        key.LeaveTangent = (float)cubicPoint.ControlPointRight.Tangent;
                        key.EventString = cubicPoint.EventString;
                    }

                    runtimeCurve.Keys.Add(key);
                }

                runtimeCurve.EnterType = EnterRepeatType;
                runtimeCurve.LeaveType = LeaveRepeatType;
                runtimeCurve.UseType = UseType;

                DebugHelper.Assert(runtimeCurve.Validate());
                return runtimeCurve;
            }
        }
        public void PostModifiedCurve()
        {
            PostModifiedCurveEvent?.Invoke();
        }
        public void PostDeserialize()
        {
            foreach (var Pt in Points)
            {
                Pt.OwnerCurve = this;
                Pt.bSelected = false;
                Pt.PostDeserialize();
            }
            UpdateCurves();
        }

        public Double GetValue(float X)
        {
            foreach (BaseCurve Curve in BaseCurves)
            {
                if (Curve.GetIsInRange((decimal)X))
                {
                    return (Double)Curve.ComputeValue(X);
                }
            }
            return 0f;
        }

        public Double GetGradientValue(float X)
        {
            Double Y = 0f;
            foreach (BaseCurve Curve in BaseCurves)
            {
                if (Curve is LinearCurve || Curve is CubicBezierCurve || Curve is HalfLineCurve)
                {
                    if (Curve.GetIsInRange((decimal)X))
                    {
                        Y = (Double)Curve.ComputeValue(X);
                    }
                }
            }
            return Y;
        }

        #region Point Modify

        public bool CheckIsValidX(float x, Point Except = null)
        {
            foreach (var point in Points)
            {
                if (Math.Abs(point.ValueX - x) < 1e-5 && point != Except)
                {
                    return false;
                }
            }
            return true;
        }

        public bool CheckIsValidX(float x, List<Point> Except)
        {
            foreach (var point in Points)
            {
                if (Math.Abs(point.ValueX - x) < 1e-5 && Except.Contains(point))
                {
                    return false;
                }
            }
            return true;
        }

        public Point AddPoint(Point NewPoint)
        {
            NewPoint.OwnerCurve = this;
            Points.Add(NewPoint);
            Points.Sort();

            PostAddPointEvent?.Invoke(this, NewPoint);

            return NewPoint;
        }

        public Point AddNewPoint(float X, Double Y, float Size, PointType PointType)
        {
            Point NewPoint;
            switch (PointType)
            {
                case PointType.Linear:
                    NewPoint = new LinearPoint();
                    break;
                case PointType.Constant:
                    NewPoint = new ConstantPoint();
                    break;
                case PointType.Smooth:
                    NewPoint = new SmoothPoint();
                    break;
                default:
                    NewPoint = new LinearPoint();
                    break;
            }
            NewPoint.Initialize(X, (decimal)Y, Size, this);
            Points.Add(NewPoint);
            Points.Sort();

            PostAddPointEvent?.Invoke(this, NewPoint);

            return NewPoint;
        }

        public Point DeletePoint(Point Pt)
        {
            if (ControlPoint.IsSameType(Pt))
                return null;

            if (Points.Remove(Pt))
            {
                Pt.OwnerCurve = null;
                Pt.bSelected = false;

                PostRemovePointEvent?.Invoke(this, Pt);
                return Pt;
            }
            else
                return null;
        }

        public Point ChangePointType(Point Pt, PointType NewType)
        {
            if (ControlPoint.IsSameType(Pt))
                return null;

            int Index = Points.FindIndex(Pt.Equals);
            if (Pt.PointType != NewType && Index != -1)
            {
                Point NewPoint;
                switch (NewType)
                {
                    case PointType.Linear:
                        NewPoint = new LinearPoint();
                        break;
                    case PointType.Constant:
                        NewPoint = new ConstantPoint();
                        break;
                    case PointType.Smooth:
                        NewPoint = new SmoothPoint();
                        break;
                    default:
                        NewPoint = new LinearPoint();
                        break;
                }
                NewPoint.Initialize(Pt.ValueX, Pt.ValueY, Pt.Size, this);
                NewPoint.bSelected = true;

                Points[Index] = NewPoint;

                return NewPoint;
            }
            else
            {
                return null;
            }
        }

        public Point AssignLocToPoint(Point Pt, float NewX, Double NewY)
        {
            if (ControlPoint.IsSameType(Pt))
                return null;

            if (Points.Contains(Pt))
            {
                Pt.ValueX = NewX;
                Pt.ValueY = (decimal)NewY;

                return Pt;
            }

            return null;
        }

        #endregion Point Modify

        public void Draw(CurveGraphicsHelper CurveGraphicsHelper, UIManager UIManager)
        {
            UpdateCurves();

            foreach (BaseCurve Cur in BaseCurves)
            {
                if (bSelected) Cur.Size = 4f;
                Cur.Color = UnselectedColor;
                Cur.Draw(CurveGraphicsHelper, UIManager);
            }

            foreach (Point Pt in Points)
            {
                Pt.Draw(CurveGraphicsHelper, UIManager);
            }
        }

        public void Draw(UIManager UIManager, decimal StartX, decimal EndX, int ScreenX, int ScreenY, int Width, int Height, int SpanY)
        {
            int DrawSpan = SpanY;
            int DrawX = ScreenX;
            int DrawY = ScreenY + DrawSpan;
            int DrawWidth = Width;
            int DrawHeight = Height - DrawSpan * 2;
            Panel TrackPanel = new Panel();
            TrackPanel.Initialize();
            TrackPanel.SetPosition(DrawX, DrawY, DrawWidth, DrawHeight);
            Axis TrackAxis = new Axis();
            TrackAxis.Initialize(StartX, 0, EndX - StartX, 1, DrawWidth, DrawHeight);
            CurveGraphicsHelper TrackGraphicHelper = new CurveGraphicsHelper();
            TrackGraphicHelper.Initialize(TrackAxis, TrackPanel);

            UpdateCurves();
            Double Highest = Double.MinValue;
            Double Lowest = Double.MaxValue;
            foreach (var BaseCurve in BaseCurves)
            {
                if (BaseCurve is HalfLineCurve)
                {
                    Highest = (Double)BaseCurve.StartPoint.Y > Highest ? (Double)BaseCurve.StartPoint.Y : Highest;
                    Lowest = (Double)BaseCurve.StartPoint.Y < Lowest ? (Double)BaseCurve.StartPoint.Y : Lowest;
                }
                else if (BaseCurve is CycleCurve)
                    continue;
                else
                {
                    var Segments = BaseCurve.GetSegments(TrackGraphicHelper);
                    foreach (var Seg in Segments)
                    {
                        foreach (var Item in new[] { Seg.Item1, Seg.Item2 })
                        {
                            Highest = (Double)Item.Y > Highest ? (Double)Item.Y : Highest;
                            Lowest = (Double)Item.Y < Lowest ? (Double)Item.Y : Lowest;
                        }
                    }
                }
            }
            if (Highest == Lowest)
            {
                Highest += 1;
                Lowest -= 1;
            }

            TrackAxis.StartY = (decimal)Lowest;
            TrackAxis.UnitY = (decimal)(Highest - Lowest);
            TrackAxis.UpdateEndValue(TrackGraphicHelper);

            foreach (var BaseCurve in BaseCurves)
            {
                BaseCurve.Color = UnselectedColor;
                BaseCurve.Draw(TrackGraphicHelper, UIManager);
            }
        }

        public void UpdateCurves()
        {
            BaseCurves.Clear();

            Point LastPoint;
            if (Points.Count() > 0)
            {
                LastPoint = Points[0];
                if (LastPoint.PointType == PointType.Smooth)
                {
                    SmoothPoint Smooth = LastPoint as SmoothPoint;
                    Smooth.bControlPointActiveLeft = false;
                    Smooth.bControlPointActiveRight = true;
                    if (Smooth.Mode == KeySmoothMode.KSM_AUTO && Points.Count() > 1)
                    {
                        double Tangent = (Double)(Points[1].ValueY - Smooth.ValueY) / (Points[1].ValueX - Smooth.ValueX);

                        Smooth.ControlPointRight.Tangent = (decimal)Tangent;
                    }
                }

                for (int i = 1; i < Points.Count(); ++i)
                {
                    Point NowPoint = Points[i];
                    UpdateSmoothPoint(LastPoint, i);

                    var NewCurve = GenerateCurve(LastPoint, NowPoint);
                    BaseCurves.Add(NewCurve);

                    LastPoint = NowPoint;
                }

                if (LastPoint.PointType == PointType.Smooth)
                {
                    (LastPoint as SmoothPoint).bControlPointActiveLeft = true;
                    (LastPoint as SmoothPoint).bControlPointActiveRight = false;
                }
            }

            if (Points.Count() > 0)
            {
                if (Points.Count() > 1 && EnterRepeatType == CurveRepeatType.CRT_CYCLE)
                {
                    CycleCurve LeftCycle = new CycleCurve();
                    LeftCycle.Initialize(BaseCurves, false);
                    BaseCurves.Insert(0, LeftCycle);
                }
                else
                {
                    HalfLineCurve LeftHalf = new HalfLineCurve();
                    LeftHalf.Initialize(Points.First(), 180);
                    BaseCurves.Insert(0, LeftHalf);
                }

                if (Points.Count() > 1 && LeaveRepeatType == CurveRepeatType.CRT_CYCLE)
                {

                    CycleCurve RightCycle = new CycleCurve();
                    RightCycle.Initialize(BaseCurves.Skip(1), true);
                    BaseCurves.Add(RightCycle);
                }
                else
                {
                    HalfLineCurve RightHalf = new HalfLineCurve();
                    RightHalf.Initialize(Points.Last(), 0);
                    BaseCurves.Add(RightHalf);
                }
            }
            else
            {
                LastPoint = new LinearPoint();
                LastPoint.Initialize(0f, 0, 4, this);

                HalfLineCurve LeftHalf = new HalfLineCurve();
                LeftHalf.Initialize(LastPoint, 180);
                BaseCurves.Add(LeftHalf);

                HalfLineCurve RightHalf = new HalfLineCurve();
                RightHalf.Initialize(LastPoint, 0);
                BaseCurves.Add(RightHalf);
            }
        }

        //public CurveManager HitTest(float X, float Y, CurveGraphicsHelper CurveGraphicsHelper)
        //{
        //    foreach (Point Pt in Points)
        //    {
        //        if (Pt.HitTest(X, Y, CurveGraphicsHelper) != null)
        //        {
        //            return this;
        //        }
        //    }

        //    foreach (var BaseCurve in BaseCurves)
        //    {
        //        if (BaseCurve.HitTest(X, Y, CurveGraphicsHelper) != null)
        //        {
        //            return this;
        //        }
        //    }

        //    return null;
        //}

        public List<Point> HitTestOnPoint(float X, float Y, CurveGraphicsHelper CurveGraphicsHelper)
        {
            List<Point> HitResultList = new List<Point>();

            foreach (Point Pt in Points)
            {
                Point HitResult = Pt.HitTest((decimal)X, (decimal)Y, CurveGraphicsHelper);
                if (HitResult != null) HitResultList.Add(HitResult);
            }

            return HitResultList;
        }

        public bool HitTestOnCurve(float X, float Y, CurveGraphicsHelper CurveGraphicsHelper)
        {
            foreach (var BaseCurve in BaseCurves)
            {
                if (BaseCurve.HitTest((decimal)X, (decimal)Y, CurveGraphicsHelper) != null)
                {
                    return true;
                }
            }

            return false;
        }

        private void UpdateSmoothPoint(Point LastPoint, int i)
        {
            Point NowPoint = Points[i];
            if (NowPoint.PointType == PointType.Smooth)
            {
                var Smooth = NowPoint as SmoothPoint;

                Smooth.bControlPointActiveLeft = true;
                Smooth.bControlPointActiveRight = true;

                if (Smooth.Mode == KeySmoothMode.KSM_AUTO)
                {
                    decimal Tangent = 0;
                    if (i < Points.Count() - 1)
                        Tangent = (Points[i + 1].ValueY - LastPoint.ValueY) / (decimal)(Points[i + 1].ValueX - LastPoint.ValueX);
                    else
                        Tangent = (NowPoint.ValueY - LastPoint.ValueY) / (decimal)(NowPoint.ValueX - LastPoint.ValueX);


                    Smooth.ControlPointLeft.Tangent = (decimal)Tangent;
                    Smooth.ControlPointRight.Tangent = (decimal)Tangent;
                }
                Smooth.ControlPointLeft.UpdateValue(LastPoint);
            }

            if (LastPoint.PointType == PointType.Smooth)
                (LastPoint as SmoothPoint).ControlPointRight.UpdateValue(NowPoint);
        }

        private BaseCurve GenerateCurve(Point LastPoint, Point NowPoint)
        {
            BaseCurve NewCurve = null;
            switch (LastPoint.PointType) // Curve type is decided by the last point (in the left)
            {
                case PointType.Linear:
                    LinearCurve Linear = new LinearCurve();
                    Linear.Initialize(LastPoint.ToVector2m(), NowPoint.ToVector2m());
                    NewCurve = Linear;
                    break;

                case PointType.Constant:
                    ConstantCurve Constant = new ConstantCurve();
                    Constant.Initialize(LastPoint.ToVector2m(), NowPoint.ToVector2m());
                    NewCurve = Constant;
                    break;

                case PointType.Smooth:
                    Vector2m P1 = (LastPoint as SmoothPoint).ControlPointRight;

                    Vector2m P2;
                    if (NowPoint.PointType == PointType.Smooth)
                    {
                        P2 = (NowPoint as SmoothPoint).ControlPointLeft;
                    }
                    else
                    {
                        const decimal OneThird = 1 / 3;
                        P2 = new Vector2m((1 - OneThird) * (decimal)NowPoint.ValueX + OneThird * (decimal)LastPoint.ValueX, NowPoint.ValueY);
                    }

                    CubicBezierCurve Bezier = new CubicBezierCurve();
                    Bezier.Initialize(LastPoint, P1, P2, NowPoint);
                    NewCurve = Bezier;
                    break;

                default:
                    break;
            }

            return NewCurve;
        }

        public void SetUnselectedColor(Color Color)
        {
            UnselectedColor = Color;
        }


        public void SaveToXml(Record RecordCurveManager)
        {
            RecordCurveManager.SetTypeString(nameof(CurveManager));

            RecordCurveManager.SetString("Name", Name);
            RecordCurveManager.SetUnsignedInt("Color", UnselectedColor.ToDword());
            RecordCurveManager.SetFloat("Width", Width);
            RecordCurveManager.SetString("LeaveRepeatType", Enum.GetName(typeof(CurveRepeatType), LeaveRepeatType));
            RecordCurveManager.SetString("EnterRepeatType", Enum.GetName(typeof(CurveRepeatType), EnterRepeatType));

            foreach (var Pt in Points)
            {
                Record RecordPoint = RecordCurveManager.AddChild();
                Pt.SaveToXml(RecordPoint);
            }
        }

        public void LoadFromXml(Record RecordCurveManager)
        {
            Name = RecordCurveManager.GetString("Name");
            UnselectedColor = Color.FromDword(RecordCurveManager.GetUnsignedInt("Color"));
            Width = RecordCurveManager.GetFloat("Width");

            LeaveRepeatType = (CurveRepeatType)Enum.Parse(typeof(CurveRepeatType), RecordCurveManager.GetString("LeaveRepeatType"));
            EnterRepeatType = (CurveRepeatType)Enum.Parse(typeof(CurveRepeatType), RecordCurveManager.GetString("EnterRepeatType"));

            Points.Clear();
            for (int i = 0; i < RecordCurveManager.GetChildCount(); ++i)
            {
                Record RecordChild = RecordCurveManager.GetChild(i);
                Point NewPt = null;
                switch (RecordChild.GetTypeString())
                {
                    case nameof(ConstantPoint):
                        NewPt = new ConstantPoint();
                        NewPt.LoadFromXml(RecordChild);
                        break;

                    case nameof(LinearPoint):
                        NewPt = new LinearPoint();
                        NewPt.LoadFromXml(RecordChild);
                        break;

                    case nameof(SmoothPoint):
                        NewPt = new SmoothPoint();
                        NewPt.LoadFromXml(RecordChild);
                        break;
                }
                if (NewPt != null)
                    Points.Add(NewPt);
            }
        }
    }

    public class CurveEditorDataSource
    {
        public CurveEditorDataSource() { }
        public IEnumerable<CurveManager> InCurves;

        public virtual void OnSave()
        {
        }
    }

    public class CurveResourceDataSource : CurveEditorDataSource
    {

        public override void OnSave()
        {
        }
    }
}
