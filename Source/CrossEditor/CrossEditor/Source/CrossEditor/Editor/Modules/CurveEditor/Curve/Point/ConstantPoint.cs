using EditorUI;
using System;

namespace CrossEditor
{
    [Serializable()]
    public class ConstantPoint : Point
    {
        public ConstantPoint() : base()
        {
            PointType = PointType.Constant;
            UnSelectedColor = Color.FromRGB(173, 216, 230);
        }

        public override void Draw(CurveGraphicsHelper CurveGraphicsHelper, UIManager UIManager)
        {
            Color Color;
            if (bSelected)
            {
                Color = SelectedColor;
            }
            else
            {
                Color = UnSelectedColor;
            }

            CurveGraphicsHelper.FillRectangle(UIManager, Color, new Vector2f(ValueX, (float)ValueY), Size);
            CurveGraphicsHelper.DrawRectangle(UIManager, Color.FromRGB(0, 0, 0), 1f, new Vector2f(ValueX, (float)ValueY), Size);
        }
    }
}
