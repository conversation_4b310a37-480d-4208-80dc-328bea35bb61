using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace CrossEditor
{
    public class ComponentTrack : UneditableTrack
    {
        public const int HieraWidth = 20;

        static Dictionary<Type, string> PropertyTypeToCurveType = new Dictionary<Type, string>() {
            { typeof(Vector3f), "Float3" },
            { typeof(Float3), "Float3" },
            { typeof(Vector2f), "Float2" },
            { typeof(float), "Float1" },
            { typeof(double), "Float1" },
            { typeof(Double3), "Float3" },
            { typeof(Float4), "Float4" }
        };
        public static readonly HashSet<Type> NumericTypes = new HashSet<Type>() { typeof(float), typeof(int), typeof(double), typeof(decimal) };

        public enum DisplayComAndPro
        {
            All = 0,
            OnlyCom,
            OnlyPro,
        }

        /// <summary>
        /// Component's name, valid after settled
        /// </summary>
        public string ComponentStr { get { return _Component == null ? "" : _Component.GetType().ToString(); } }

        /// <summary>
        /// Component's attached property, valid after settled 
        /// </summary>
        public virtual string PropertyStr { get { return _Property == null ? "" : _Property.Name; } }

        public virtual bool IsPropertyInitialized { get { return _Component != null && _Property != null /*|| this is EventTrack*/; } }

        public ICollection<KeyFrame> GetSelectedKeys()
        {
            if (GetIsSelected() == false)
                return new List<KeyFrame>();
            return KeyFrames.Where(k =>
            {
                var holder = ParentTrack as ComponentTrackHolder;

                var distance = holder.GetScreenDistance(k);
                if (distance < 5)
                    return true;
                return false;
            }).ToList();
        }
        public ICollection<KeyFrame> GetSelectedKeys(List<KeyFrame> SelectedKeyFrames)
        {
            if (SelectedKeyFrames.Count == 0)
                return new List<KeyFrame>();
            return KeyFrames.Where(k =>
            {
                foreach (var KeyFrame in SelectedKeyFrames)
                {
                    if ((double)Math.Abs(KeyFrame.GetKeyValue() - k.GetKeyValue()) < 1e-5)
                    {
                        return true;
                    }
                }
                return false;
            }).ToList();
        }
        public KeyFrame GetSelectedPointKey(KeyFrame SelectedKeyFrame)
        {
            KeyFrame Result = null;
            if (SelectedKeyFrame == null)
                return Result;
            foreach (var KeyFrame in KeyFrames)
            {
                if ((double)Math.Abs(SelectedKeyFrame.GetKeyValue() - KeyFrame.GetKeyValue()) < 1e-5)
                {
                    var BindObject = KeyFrame.BindObject as ICollection<KeyFrame>;
                    foreach (var Point in BindObject)
                    {
                        if (Point.BindObject is Point)
                        {
                            if (((Point)Point.BindObject) == ((Point)SelectedKeyFrame.BindObject))
                            {
                                return Point;
                            }
                        }
                    }
                }
            }
            return Result;
        }

        public virtual void ExtractComponentValueToSelected(decimal axisPos)
        {
            if (IsPropertyInitialized == false)
                return;

            var selectedKeys = GetSelectedKeys();
            object propertyValue = RawGetPropertyValueFunction(_Component, _PropertyDisplayName);
            if (PropertyStr == "Rotation")
            {
                propertyValue = Quaternion64.EulerToQuaternion64((propertyValue as Double3).ToRadian());
            }

            if (selectedKeys.Count != 0)
            {
                foreach (var keyframe in selectedKeys)
                {
                    var bindObject = keyframe.BindObject as ICollection<KeyFrame>;
                    // handle all track got validate keys
                    foreach (var bindKey in bindObject)
                    {
                        (bindKey.OwnerTrack as NumericTrack).ModifyValue(bindKey, propertyValue);
                        axisPos = bindKey.GetKeyValue();
                    }

                    // handle all track no near key found
                    var notExistTracks = Children.Where(t =>
                    {
                        return bindObject.FirstOrDefault(b => b.OwnerTrack == t) == null;
                    }).ToList();

                    foreach (var track in notExistTracks)
                    {
                        track.AddNewKey(axisPos, propertyValue);
                    }
                }
            }
            else
            {
                foreach (var track in Children)
                {
                    track.AddNewKey(axisPos, propertyValue);
                }
            }
        }

        public virtual void ExtractComponentValueToEdit(decimal axisPos)
        {
            if (IsPropertyInitialized == false)
                return;

            object propertyValue = RawGetPropertyValueFunction(_Component, _PropertyDisplayName);
            if (PropertyStr == "Rotation")
            {
                propertyValue = Quaternion64.EulerToQuaternion64((propertyValue as Double3).ToRadian());
            }

            {
                foreach (var track in Children)
                {
                    var item = (NumericTrack)track;
                    item.WriteValue(propertyValue);
                }
            }
        }

        public virtual bool SetComponentAndProperty(Entity PreviewingEntity, UniqueString ComponentName, UniqueString PropertyName)
        {
            if (PreviewingEntity == null)
                return false;

            Previewing = PreviewingEntity;
            foreach (Type componentType in _ComponentCandidates)
            {
                if (componentType.FullName == ComponentName.GetCString())
                {
                    foreach (var propertyType in BuildEnumerablePropertyInfos(componentType))
                    {
                        if (propertyType.Name == PropertyName.GetCString())
                        {
                            _Component = Previewing.GetComponent(componentType);
                            SetTagObject(_Component);
                            _Property = propertyType;

                            return true;
                        }
                    }
                }
            }

            return false;
        }

        public void RemoveComponentValueFromSelected(List<KeyFrame> SelectedKeyFrames)
        {
            if (IsPropertyInitialized == false || SelectedKeyFrames == null)
                return;

            List<KeyFrame> PointKeyFrames = new List<KeyFrame>();
            // first process pointKeyFrame
            for (int index = SelectedKeyFrames.Count - 1; index >= 0; index--)
            {
                if (SelectedKeyFrames[index] != null)
                {
                    if (SelectedKeyFrames[index].BindObject is Point Point)
                    {
                        KeyFrame SelectKeyFrame = GetSelectedPointKey(SelectedKeyFrames[index]);
                        if (SelectKeyFrame != null)
                        {
                            PointKeyFrames.Add(SelectKeyFrame);
                            SelectedKeyFrames.Remove(SelectedKeyFrames[index]);
                        }
                    }
                }
            }
            // second process vectorKeyFrame
            ICollection<KeyFrame> selectedKeys = new List<KeyFrame>();
            if (SelectedKeyFrames == null)
            {
                selectedKeys = GetSelectedKeys();
            }
            else
            {
                selectedKeys = GetSelectedKeys(SelectedKeyFrames);
            }
            if (PointKeyFrames.Count != 0)
            {
                TimelineEditorUI.GetInstance().RemoveSelectedKeyFrames(PointKeyFrames);
            }
            if (selectedKeys.Count != 0)
            {
                foreach (var keyframe in selectedKeys)
                {
                    var bindObject = keyframe.BindObject as ICollection<KeyFrame>;
                    // handle all track got validate keys
                    foreach (var bindKey in bindObject)
                    {
                        var point = bindKey.BindObject as Point;
                        var ownerCurve = point.OwnerCurve;
                        if (ownerCurve == null) { break; }
                        point.OwnerCurve.DeletePoint(point);
                        ownerCurve.PostModifiedCurve();
                    }
                }
            }
        }

        /// <summary>
        /// If component & its property settled, return true
        /// </summary>
        protected ComponentTrackHolder TrackHolder { get { return ParentTrack as ComponentTrackHolder; } }

        #region models for current track

        protected Component _Component = null;

        public UniqueString ComponentName
        {
            get
            {
                if (_Component == null)
                {
                    return new UniqueString();
                }
                else
                {
                    return new UniqueString(ComponentStr);
                }
            }
        }

        protected PropertyInfo _Property = null;
        public UniqueString PropertyName
        {
            get
            {
                if (_Property == null)
                {
                    return new UniqueString();
                }
                else
                {
                    return new UniqueString(PropertyStr);
                }
            }
        }

        public UniqueString CurveType
        {
            get
            {
                if (_Property != null)
                {
                    if (PropertyName.GetCString() == "Rotation")
                    {
                        return new UniqueString("RotEuler");
                    }
                    else if (PropertyName.GetCString() == "Translation")
                    {
                        return new UniqueString("Double3");
                    }
                    else
                    {
                        Type PropertyType = _Property.PropertyType;
                        if (PropertyTypeToCurveType.ContainsKey(PropertyType))
                        {
                            return new UniqueString(PropertyTypeToCurveType[PropertyType]);
                        }
                        return new UniqueString("Float1");
                    }
                }
                return new UniqueString("");
            }
        }
        public UniqueString SystemName
        {
            get
            {
                if (_Component == null)
                {
                    return new UniqueString();
                }
                else
                {
                    return new UniqueString(_Component.NativeSystemName() != "" ? _Component.NativeSystemName() : _Component.NativeNames()[0]);
                }
            }
        }
        protected Entity _Previewing = null;

        protected IEnumerable<Type> _ComponentCandidates;

        protected IEnumerable<PropertyInfo> _PropertyCandidates;

        public virtual Entity Previewing { get { return _Previewing; } set { _Previewing = value; OnEntitySelected(); } }

        public Component PreviewingComponent { get { return _Component; } }

        public PropertyInfo PreviewingProperty { get { return _Property; } }

        protected Dictionary<string, Color> _TrackNameToColor;

        protected Button _ButtonAddKeyFrame;

        #endregion

        #region views for current track

        protected ComboBox _ComponentSelect = null;

        protected ComboBox _PropertySelect = null;

        protected Edit _ComponentLabel = null;

        protected Edit _PropertyLabel = null;

        protected string _PropertyDisplayName = "";

        protected DisplayComAndPro _Dispaly = DisplayComAndPro.OnlyPro;
        public int _DisplayLevel = 2;

        #endregion

        public ComponentTrack() : base()
        {
            LevelSequenceEntity = CinematicUI.GetInstance().GetLevelSequenceEntity();
        }

        public override void Initialize(ScaleUI ScaleUI, Track Parent, string Name, object TagObject = null)
        {
            _ScaleUI = ScaleUI;
            _Panel = ScaleUI.GetPanel();

            ParentTrack = Parent;
            Children = new List<Track>();
            KeyFrames = new List<KeyFrame>();

            _ButtonBar = new Button();
            _ButtonBar.Initialize();
            _ButtonBar.SetSize(0, 0);
            _ButtonBar.LeftMouseUpEvent += (Control Sender, int MouseX, int MouseY, ref bool bContinue) =>
            { OnTrackItemClicked(Sender, MouseX, MouseY, ref bContinue, Key.LeftButton); };
            _ButtonBar.RightMouseUpEvent += (Control Sender, int MouseX, int MouseY, ref bool bContinue) =>
            { OnTrackItemClicked(Sender, MouseX, MouseY, ref bContinue, Key.RightButton); };
            _ButtonBar.PaintEvent += OnTrackItemPaint;

            // Component's property Settled ui items Create

            _CheckExpand = new Check();
            _CheckExpand.Initialize();
            _CheckExpand.SetSize(0, 0);
            _CheckExpand.SetImageUnchecked(UIManager.LoadUIImage("Editor/Tree/Common/Folded.png"));
            _CheckExpand.SetImageChecked(UIManager.LoadUIImage("Editor/Tree/Common/NotFolded.png"));
            _CheckExpand.SetAutoCheck(true);
            _CheckExpand.SetChecked(true);

            _ComponentLabel = new Edit();
            _ComponentLabel.Initialize(EditMode.Simple_SingleLine);
            _ComponentLabel.SetText("Component");
            _ComponentLabel.SetTextAlign(TextAlign.CenterCenter);
            _ComponentLabel.SetSize(0, 0);
            _ComponentLabel.SetFontSize(FontSize);
            _ComponentLabel.SetTextColor(Color.FromRGB(204, 255, 204));
            _ComponentLabel.SetBackgroundColor(Color.EDITOR_UI_COLOR_KEY);
            _ComponentLabel.SetReadOnly(true);
            _ComponentLabel.SetEnable(false);

            _PropertyLabel = new Edit();
            _PropertyLabel.Initialize(EditMode.Simple_SingleLine);
            _PropertyLabel.SetSize(0, 0);
            _PropertyLabel.SetText("Property");
            _PropertyLabel.SetTextAlign(TextAlign.CenterCenter);
            _PropertyLabel.SetFontSize(FontSize);
            _PropertyLabel.SetBackgroundColor(Color.EDITOR_UI_COLOR_KEY);
            _PropertyLabel.SetReadOnly(true);
            _PropertyLabel.SetEnable(false);

            _ButtonAddKeyFrame = new Button();
            _ButtonAddKeyFrame.Initialize();
            _ButtonAddKeyFrame.SetText("+ Key");
            _ButtonAddKeyFrame.SetFontSize(FontSize);
            _ButtonAddKeyFrame.SetBackgroundColor(Color.FromRGB(0, 255, 0));
            _ButtonAddKeyFrame.SetSize(0, 0);
            _ButtonAddKeyFrame.SetVisible(true);
            _ButtonAddKeyFrame.ClickedEvent += OnButtonAddKeyFrameClicked;


            _ButtonBar.AddChild(_CheckExpand);
            _ButtonBar.AddChild(_ComponentLabel);
            _ButtonBar.AddChild(_PropertyLabel);
            _ButtonBar.AddChild(_ButtonAddKeyFrame);


            // Component's property not Settled ui items Create

            _ComponentSelect = new ComboBox();
            _ComponentSelect.Initialize();
            _ComponentSelect.SetSize(0, 0);
            _ComponentSelect.ItemSelectedEvent += OnComponentItemSelected;
            _ComponentSelect.SetFontSize(FontSize);

            _PropertySelect = new ComboBox();
            _PropertySelect.Initialize();
            _PropertySelect.SetSize(0, 0);
            _PropertySelect.ItemSelectedEvent += OnPropertyItemSelected;
            _PropertySelect.SetFontSize(FontSize);

            _ButtonBar.AddChild(_ComponentSelect);
            _ButtonBar.AddChild(_PropertySelect);

            if (TagObject is Entity)
                Previewing = TagObject as Entity;

            _TrackNameToColor = new Dictionary<string, Color>{
                { "x", Color.EDITOR_UI_TEST_COLOR_RED },
                { "y", Color.EDITOR_UI_TEST_COLOR_GREEN },
                { "z", Color.EDITOR_UI_TEST_COLOR_BLUE },
                { "r", Color.EDITOR_UI_TEST_COLOR_RED },
                { "g", Color.EDITOR_UI_TEST_COLOR_GREEN },
                { "b", Color.EDITOR_UI_TEST_COLOR_BLUE },
                { "Pitch", Color.EDITOR_UI_TEST_COLOR_RED },
                { "Yaw", Color.EDITOR_UI_TEST_COLOR_GREEN },
                { "Roll", Color.EDITOR_UI_TEST_COLOR_BLUE },
            };
        }

        public override void UpdateLayout(bool IsVisible, int Width, int Indent, ref int Y)
        {
            bVisible = IsVisible;

            int singleLayerHeight = ItemHeight - 2;
            // property is null process
            if (!IsPropertyInitialized /*|| this is EventTrack*/)
            {
                foreach (var Child in Children)
                    Child.UpdateLayout(bVisible && _CheckExpand.GetChecked(), Width, Indent + 1, ref Y);
                return;
            }

            GetTrackItem().SetVisible(true);
            GetTrackItem().SetPosition(0, Y, Width, singleLayerHeight);

            // modify visible by property validate
            bool showSettled = IsPropertyInitialized && bVisible;

            /* Settled view items */
            _ComponentLabel.SetVisible(showSettled);
            _PropertyLabel.SetVisible(showSettled);
            _CheckExpand.SetVisible(showSettled);

            if (showSettled)
            {
                int currentWidth = _DisplayLevel * 20;

                int CheckExpandY = (singleLayerHeight - INDENT_WIDTH) / 2;
                _CheckExpand.SetVisible(Children.Count > 0);
                _CheckExpand.SetPosition(currentWidth + SpanX, CheckExpandY, INDENT_WIDTH, INDENT_WIDTH);
                currentWidth += SpanX + INDENT_WIDTH;

                int leftItemCount = 2;

                int LabelNameWeight = (Width - currentWidth - SpanX * leftItemCount) / leftItemCount;
                int LabelNameHeight = singleLayerHeight;
                int LabelNameY = (singleLayerHeight - _ComponentLabel.CalculateTextHeight()) / 2;


                switch (_Dispaly)
                {
                    case DisplayComAndPro.OnlyCom:
                        _PropertyLabel.SetVisible(false);
                        _ComponentLabel.SetText(_Component.GetType().Name);
                        _ComponentLabel.SetPosition(currentWidth + SpanX, LabelNameY, Math.Max(_ComponentLabel.CalculateTextWidth(), 150), LabelNameHeight);
                        currentWidth += SpanX + LabelNameWeight;
                        break;

                    case DisplayComAndPro.OnlyPro:
                        _ComponentLabel.SetVisible(false);
                        _PropertyLabel.SetText(ContainTwoString(_PropertyDisplayName) != "" ? ContainTwoString(_PropertyDisplayName) : _Property.Name);
                        _PropertyLabel.SetPosition(currentWidth + SpanX, LabelNameY, Math.Max(_PropertyLabel.CalculateTextWidth(), 150), LabelNameHeight);
                        break;


                    case DisplayComAndPro.All:
                        _ComponentLabel.SetText(_Component.GetType().Name);
                        _ComponentLabel.SetPosition(currentWidth + SpanX, LabelNameY, Math.Max(_ComponentLabel.CalculateTextWidth(), 150), LabelNameHeight);
                        _PropertyLabel.SetText(_Property.Name);
                        _PropertyLabel.SetPosition(currentWidth + SpanX, LabelNameY, Math.Max(_PropertyLabel.CalculateTextWidth(), 150), LabelNameHeight);
                        currentWidth += SpanX + LabelNameWeight;

                        _PropertyLabel.SetText(_Property.Name);
                        _PropertyLabel.SetPosition(currentWidth + SpanX, LabelNameY, Math.Max(_PropertyLabel.CalculateTextWidth(), 150), LabelNameHeight);
                        break;
                }
            }

            int ButtonAddKeyFrameWidth = _ButtonAddKeyFrame.CalculateTextWidth();
            int ButtonAddKeyFrameHeight = ButtonHeight;
            int ButtonAddKeyFrameX = Width - ButtonAddKeyFrameWidth - SpanX;
            int ButtonAddKeyFrameY = (ItemHeight - ButtonAddKeyFrameHeight) / 2;
            _ButtonAddKeyFrame.SetPosition(ButtonAddKeyFrameX, ButtonAddKeyFrameY, ButtonAddKeyFrameWidth, ButtonAddKeyFrameHeight);
            _ButtonAddKeyFrame.SetVisible(IsVisible);

            /* Not settled view items */
            _ComponentSelect.SetVisible(!showSettled && bVisible);
            _PropertySelect.SetVisible(!showSettled && bVisible);

            if (!showSettled && bVisible && IsPropertyInitialized)
            {
                int currentWidth = HieraWidth;

                int Span = 10;
                int leftItemCount = 2;

                int ComboWeight = (Width - currentWidth - Span * leftItemCount) / leftItemCount;
                int ComboHeight = singleLayerHeight - 4;
                int ComboY = (singleLayerHeight - ComboHeight) / 2;


                _ComponentSelect.SetPosition(currentWidth + Span, ComboY, ComboWeight, ComboHeight);
                currentWidth += Span + ComboWeight;

                _PropertySelect.SetPosition(currentWidth + Span, ComboY, ComboWeight, ComboHeight);
            }

            if (bVisible)
                Y += singleLayerHeight;

            bool ChildVisibility = bVisible && _CheckExpand.GetChecked();
            foreach (var Child in Children)
                Child.UpdateLayout(ChildVisibility, Width, Indent + 1, ref Y);
        }

        protected override void ShowAddTrackMenu() { }

        public override void Update()
        {
            base.Update();

            // handle vector track
            if (Children.Count > 0)
            {
                KeyFrames.Clear();

                foreach (var Child in Children)
                {
                    foreach (var KeyFrame in Child.GetKeyFrames())
                    {
                        int Index = KeyFrames.BinarySearch(KeyFrame);
                        if (Index < 0)
                        {
                            VectorKeyFrame NewKeyFrame = new VectorKeyFrame(new List<KeyFrame>() { KeyFrame }, this, KeyFrame.GetKeyValue());
                            NewKeyFrame.SolidFrameCount = Children.Count;
                            KeyFrames.Add(NewKeyFrame);
                        }
                        else
                            (KeyFrames[Index].BindObject as ICollection<KeyFrame>).Add(KeyFrame);
                    }
                }

                KeyFrames.Sort();
            }
            // handle float track
            else
            { }
        }

        public override void Draw(UIManager UIManager)
        {
            RectangleF Bound = GetBound();
            if (Bound.Y == 0)
                return;

            var holder = ParentTrack as ComponentTrackHolder;

            // remove closest points in the very beginning
            if (Children.Count > 0)
            {
                float preKeyScreenX = -1.0f;
                var filtered = KeyFrames.Where(k =>
                {
                    float curKeyScreenX = (float)_ScaleUI.ValueToScreenX(k.GetKeyValue());

                    if (preKeyScreenX > 0 && Math.Abs(preKeyScreenX - curKeyScreenX) < 2)
                        return false;

                    preKeyScreenX = curKeyScreenX;
                    return true;
                });

                // draw the left keys
                foreach (var key in filtered)
                {
                    // auto select by cinematic ui's cursor
                    if (bSelected)
                    {
                        var distance = holder.GetScreenDistance(key);
                        if (distance < 5)
                            key.bSelected = true;
                    }
                }

                DrawKeyFrames();
            }
        }

        protected virtual IEnumerable<Type> BuildEnumerableComponents()
        {
            var candidates = typeof(Component)
                .Assembly.GetTypes()
                .Where(t =>
                {
                    if (_Previewing == null)
                        return t.IsSubclassOf(typeof(Component)) && !t.IsAbstract;

                    var components = _Previewing.Components;
                    var myFilteredList = components.Where(elt => elt.GetType().Equals(t)).ToList();
                    return myFilteredList.Count() > 0;
                });

            _ComponentSelect.ClearItems();
            foreach (var component in candidates)
            {
                bool flag = false;
                CinematicUI.GetInstance().ComponentNodes.TryGetValue(component.Name, out flag);
                if (flag)
                {
                    _ComponentSelect.AddItem(component.Name);
                }
            }

            return candidates;
        }

        protected bool IsDispalyProperty(string ComponentName, string PropertyName)
        {
            return true;
        }

        protected virtual IEnumerable<PropertyInfo> BuildEnumerablePropertyInfos(Type componentType)
        {
            if (componentType == null || componentType.IsSubclassOf(typeof(Component)) == false)
            {
                _PropertySelect.ClearItems();
                _PropertySelect.SetEnable(false);
                return new List<PropertyInfo>();
            }

            var properties = componentType.GetProperties();
            List<PropertyInfo> newProperty = new List<PropertyInfo>();
            foreach (var value in properties)
            {
                PropertyInfoAttribute PropertyInfoAttribute = PropertyInfoAttribute.GetPropertyInfoAttribute(value);
                if (PropertyInfoAttribute.PropertyType == "Struct")
                {
                    Type Type = value.PropertyType;
                    List<PropertyInfo> Properties = PropertyCollector.CollectPropertiesOfType(Type);
                    foreach (PropertyInfo PropertyInfo in Properties)
                    {
                        if (IsDispalyProperty(componentType.Name, PropertyInfo.Name))
                        {
                            newProperty.Add(PropertyInfo);
                        }
                    }
                }
                else if (IsDispalyProperty(componentType.Name, value.Name))
                {
                    newProperty.Add(value);
                }
            }
            _PropertySelect.SetEnable(true);
            _PropertySelect.ClearItems();
            foreach (var property in newProperty)
                _PropertySelect.AddItem(property.Name);

            return newProperty;
        }

        public override void SetIsSelected(bool IsSelected)
        {
            if (IsPropertyInitialized)
            {
                base.SetIsSelected(IsSelected);
                _ButtonBar.SetDownColor(Color.EDITOR_UI_HILIGHT_COLOR_GRAY);
                return;
            }

            if (IsSelected)
            {
                _ButtonBar.SetNormalColor(Color.EDITOR_UI_HILIGHT_COLOR_GRAY);
                _ButtonBar.SetDownColor(Color.EDITOR_UI_HILIGHT_COLOR_GRAY);
            }
            else
            {
                _ButtonBar.SetDownColor(Color.EDITOR_UI_HILIGHT_COLOR_GRAY);
                _ButtonBar.SetNormalColor(Color.EDITOR_UI_COLOR_KEY);
            }
        }

        protected virtual void OnComponentItemSelected(ComboBox Sender)
        {
            var selected = Sender.GetSelectedItemText();
            var componentType = _ComponentCandidates.FirstOrDefault(t => t.Name == selected);

            if (componentType != null)
            {
                /* find corresponding component in previewing entity */
                if (_Previewing != null)
                {
                    _Component = _Previewing.GetComponent(componentType);
                }
                /* create a empty component if no previewing */
                else
                {
                    _Component = Activator.CreateInstance(componentType) as Component;
                }

                SetTagObject(_Component);
                _PropertyCandidates = BuildEnumerablePropertyInfos(_Component.GetType());
            }
        }

        protected virtual void OnPropertyItemSelected(ComboBox Sender)
        {

            var selected = Sender.GetSelectedItemText();
            var candidate = _PropertyCandidates.FirstOrDefault(t => t.Name == selected);

            _Property = candidate;
            ComponentTrackHolder holder = ParentTrack as ComponentTrackHolder;
            if (holder.IsAleadyExisting(this))
            {
                _Property = null;
                ConsoleUI.GetInstance().AddLogItem(LogMessageType.Information,
                    string.Format("failed assignment for repeat element"));
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Cinematic Element", "Failed assignment for repeat element");
                return;
            }

            ControllableUnitSystemG.CreateCurveController(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, ComponentName, new UniqueString(_PropertyDisplayName), SystemName, CurveType,
                                                            _Component.Entity.GetEntityIdStruct());

            FloatCurveListInfo info = new FloatCurveListInfo();

            if (_Property.PropertyType.Equals(typeof(Vector3f)) || _Property.PropertyType.Equals(typeof(Double3)) || _Property.PropertyType.Equals(typeof(Float3)))
            {
                if (PropertyName.GetCString() == "Rotation")
                {
                    info.Items.Add(new FloatCurveListInfoItem());
                    info.Items.Add(new FloatCurveListInfoItem());
                    info.Items.Add(new FloatCurveListInfoItem());
                    info.Items.Add(new FloatCurveListInfoItem());
                    info.Items[0].CurveName = new UniqueString("x");
                    info.Items[1].CurveName = new UniqueString("y");
                    info.Items[2].CurveName = new UniqueString("z");
                    info.Items[3].CurveName = new UniqueString("w");
                }
                else
                {
                    info.Items.Add(new FloatCurveListInfoItem());
                    info.Items.Add(new FloatCurveListInfoItem());
                    info.Items.Add(new FloatCurveListInfoItem());
                    if (PropertyName.GetCString() == "Color")
                    {
                        info.Items[0].CurveName = new UniqueString("r");
                        info.Items[1].CurveName = new UniqueString("g");
                        info.Items[2].CurveName = new UniqueString("b");
                    }
                    else
                    {
                        info.Items[0].CurveName = new UniqueString("x");
                        info.Items[1].CurveName = new UniqueString("y");
                        info.Items[2].CurveName = new UniqueString("z");
                    }
                }
            }
            else if (_Property.PropertyType.Equals(typeof(Vector2f)) || _Property.PropertyType.Equals(typeof(Float2)))
            {
                info.Items.Add(new FloatCurveListInfoItem());
                info.Items.Add(new FloatCurveListInfoItem());
                info.Items[0].CurveName = new UniqueString("x");
                info.Items[1].CurveName = new UniqueString("y");
            }
            else if (_Property.PropertyType.Equals(typeof(float)) || _Property.PropertyType.Equals(typeof(string)) || _Property.PropertyType.Equals(typeof(bool)))
            {
                info.Items.Add(new FloatCurveListInfoItem());
                info.Items[0].CurveName = PropertyName;
            }

            ControllableUnitSystemG.SetCurveControllerInfo(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, ComponentName, new UniqueString(_PropertyDisplayName), info,
                                                            _Component.Entity.GetEntityIdStruct());
            RefreshTrackList();
            // Set default key at the init position.
            // ExtractComponentValueToSelected(0);
            ExtractComponentValueToEdit(0);

            if (_ScaleUI.GetCurrentUI() is CinematicUI)
            {
                CinematicUI CinematicUI = _ScaleUI.GetCurrentUI() as CinematicUI;
                if (CinematicUI.CheckIsPreviewing(_Component.Entity))
                {
                    CinematicUI.SetModified();
                }
            }
        }
        public virtual void RefreshTrackList()
        {
            ClearTrack();
            if (_Previewing == null)
            {
                return;
            }

            FloatCurveList list = new FloatCurveList();
            ControllableUnitSystemG.GetCurveControllerList(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, ComponentName, new UniqueString(_PropertyDisplayName), list,
                                                            _Previewing.GetEntityIdStruct());
            int index = 0;
            Entity tempPreviewingEntity = _Previewing;
            foreach (var item in list.mTracks)
            {
                var track = new NumericTrack();
                track.Initialize(_ScaleUI, this, item.Name.GetCString(), index);
                track.GetCurve().RuntimeCurve = item;
                track.SetTagObject(_Property);
                track.CanBeDeleted = false;
                track.CanBeEdit = !(PropertyStr == "Rotation" && item.Name.GetCString() != "t");

                track.GetCurve().PostModifiedCurveEvent += () =>
                {
                    bool ret = ControllableUnitSystemG.SetCurveControllerTrack(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, ComponentName, new UniqueString(_PropertyDisplayName), track.GetCurve().RuntimeCurve.Name, track.GetCurve().RuntimeCurve,
                                                                               tempPreviewingEntity.GetEntityIdStruct());
                    if (_ScaleUI.GetCurrentUI() is CinematicUI)
                    {
                        CinematicUI CinematicUI = _ScaleUI.GetCurrentUI() as CinematicUI;
                        if (ret && CinematicUI.CheckIsPreviewing(_Component.Entity))
                        {
                            CinematicUI.SetModified();
                        }
                    }
                };

                Color trackColor = Color.White;
                if (_TrackNameToColor.ContainsKey(item.Name.GetCString()))
                {
                    trackColor = _TrackNameToColor[item.Name.GetCString()];
                }
                track.GetCurve().UnselectedColor = trackColor;

                AddTrack(track);
                index++;
            }
        }

        public override List<MenuItem> GetMenuItems()
        {
            var MenuItems = new List<MenuItem>();
            //component Track need not edit curve item
            //if (IsPropertyInitialized && _Property.PropertyType.Equals(typeof(float)))
            //{
            //    MenuItem MenuItem_EditCurve = new MenuItem();
            //    MenuItem_EditCurve.SetText("Edit Curve");
            //    MenuItem_EditCurve.ClickedEvent += (Sender) =>
            //    {
            //        CurveEditorUI.GetInstance().LoadFromCurves(new[] { Curve });
            //        MainUI.GetInstance().ActivateDockingCard_CurveEditor();
            //    };
            //    MenuItems.Add(MenuItem_EditCurve);
            //}
            return MenuItems;
        }

        protected virtual void OnEntitySelected()
        {
            _ComponentCandidates = BuildEnumerableComponents();
            _PropertyCandidates = BuildEnumerablePropertyInfos(_Component != null ? _Component.GetType() : null);
        }

        public object RawGetPropertyValueFunction(object Object, string PropertyName)
        {
            Component Component = (Component)Object;
            if (Component is PostProcessVolumeComponent || Component is SkyAtmosphereComponent || Component is SkyLightComponent || Component is TODLightComponent ||Component is Light)
            {
                string[] SplitString = PropertyName.Split(".");
                List<String> StringList = new List<String>(SplitString);
                object Obj = new object();
                if (Component is PostProcessVolumeComponent)
                {
                    PostProcessVolumeComponent PostProcessVolumeComponent = (PostProcessVolumeComponent)Object;

                    Type DataType = PostProcessVolumeComponent.ComponentData.GetType();
                    PropertyInfo PropertyInfo1 = DataType.GetProperty(StringList[0]);
                    Obj = PropertyInfo1.GetValue(PostProcessVolumeComponent.ComponentData);
                }
                else if (Component is SkyAtmosphereComponent)
                {
                    SkyAtmosphereComponent SkyAtmosphereComponent = (SkyAtmosphereComponent)Component;
                    PropertyInfo PropertyInfo1 = SkyAtmosphereComponent.GetType().GetProperty(StringList[0]);
                    Obj = PropertyInfo1.GetValue(SkyAtmosphereComponent);
                }
                else if (Component is TODLightComponent)
                {
                    TODLightComponent TODLightComponent = (TODLightComponent)Component;
                    PropertyInfo PropertyInfo1 = TODLightComponent.GetType().GetProperty(StringList[0]);
                    Obj = PropertyInfo1.GetValue(TODLightComponent);
                }
                else if (Component is SkyLightComponent)
                {
                    SkyLightComponent SkyLightComponent = (SkyLightComponent)Component;
                    PropertyInfo PropertyInfo1 = SkyLightComponent.GetType().GetProperty(StringList[0]);
                    Obj = PropertyInfo1.GetValue(SkyLightComponent);
                }
                else if (Component is Light)
                {
                    Light LightComponent = (Light)Component;
                    System.Type DayaType = LightComponent.ComponentData.GetType();
                    PropertyInfo PropertyInfo1 = DayaType.GetProperty(StringList[0]);
                    Obj = PropertyInfo1.GetValue(LightComponent.ComponentData);
                }

                if (StringList.Count == 1)
                {
                    return Obj;

                }

                return RecursiveGetValue(Obj, StringList, 1);
            }
            Type Type = Object.GetType();
            PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
            if (PropertyInfo != null)
            {
                return PropertyInfo.GetValue(Object);
            }
            return null;
        }

        object RecursiveGetValue(Object Obj, List<String> SplitString, int index)
        {
            Type Type = Obj.GetType();
            PropertyInfo PropertyInfo = Type.GetProperty(SplitString[index]);
            object SubObj = PropertyInfo.GetValue(Obj);
            if (index == SplitString.Count - 1)
            {
                return SubObj;
            }
            else
            {
                return RecursiveGetValue(SubObj, SplitString, ++index);
            }
        }

        public void SetComponent(Component Component)
        {
            _Component = Component;
        }

        public void OnComponentItemSelected(string ComponentName)
        {
            var componentType = _ComponentCandidates.FirstOrDefault(t => t.Name == ComponentName);

            if (componentType != null)
            {
                /* find corresponding component in previewing entity */
                if (_Previewing != null)
                {
                    _Component = _Previewing.GetComponent(componentType);
                }
                /* create a empty component if no previewing */
                else
                {
                    _Component = Activator.CreateInstance(componentType) as Component;
                }

                SetTagObject(_Component);
            }
        }

        protected bool IsExistIn(PropertyInfo info, string Name, out PropertyInfo FactInfo, out string LinkName)
        {
            PropertyInfoAttribute PropertyInfoAttribute = PropertyInfoAttribute.GetPropertyInfoAttribute(info);
            if (PropertyInfoAttribute.PropertyType == "Struct" || PropertyInfoAttribute.ToolTips == "Extra adding")
            {
                Type Type = info.PropertyType;
                List<PropertyInfo> Properties = PropertyCollector.CollectPropertiesOfType(Type);
                foreach (PropertyInfo PropertyInfo in Properties)
                {
                    if (IsExistIn(PropertyInfo, Name, out FactInfo, out LinkName))
                    {
                        LinkName += "." + info.Name;
                        return true;
                    }
                }
            }
            else if (info.Name == Name)
            {
                FactInfo = info;
                LinkName = Name;
                return true;
            }
            FactInfo = null;
            LinkName = "";
            return false;
        }

        public void OnPropertyItemSelected(string Name)
        {
            _PropertyCandidates = BuildEnumerablePropertyInfos(_Component.GetType());
            _PropertyDisplayName = Name;
            string TrueName = "";

            string[] splitString = Name.Split(".");
            if (splitString.Length == 0)
            {
                TrueName = Name;
            }
            else
            {
                foreach (var value in splitString)
                {
                    TrueName = value;
                }
            }
            var candidate = _PropertyCandidates.FirstOrDefault(t => t.Name == TrueName);

            if (candidate == null)
            {
                PropertyInfo candidateProp = null;
                string LinkName = "";
                foreach (var likelyCandidate in _PropertyCandidates)
                {
                    PropertyInfoAttribute PropertyInfoAttribute = PropertyInfoAttribute.GetPropertyInfoAttribute(likelyCandidate);
                    if (PropertyInfoAttribute.PropertyType == "Struct" || PropertyInfoAttribute.ToolTips == "Extra adding")
                    {
                        if (IsExistIn(likelyCandidate, TrueName, out candidateProp, out LinkName))
                        {
                            candidate = likelyCandidate;
                            break;
                        }
                    }
                    else if (likelyCandidate.Name == Name)
                    {
                        candidate = likelyCandidate;
                        break;
                    }
                }
                _Property = candidateProp;
                _PropertyDisplayName = Name;
            }
            else
            {
                _Property = candidate;
            }

            ComponentTrackHolder holder = ParentTrack as ComponentTrackHolder;
            if (holder.IsAleadyExisting(this))
            {
                _Property = null;
                ConsoleUI.GetInstance().AddLogItem(LogMessageType.Information,
                    string.Format("failed assignment for repeat element"));
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Cinematic Element", "Failed assignment for repeat element");
                return;
            }

            if (_Property == null)
                return;

            ControllableUnitSystemG.CreateCurveController(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, ComponentName, new UniqueString(_PropertyDisplayName), SystemName, CurveType,
                                                            _Component.Entity.GetEntityIdStruct());
            FloatCurveListInfo info = GetFloatCurveListInfo(_Property);
            ControllableUnitSystemG.SetCurveControllerInfo(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, ComponentName, new UniqueString(_PropertyDisplayName), info,
                                                            _Component.Entity.GetEntityIdStruct());
            RefreshTrackList();
            // Set default key at the init position.
            //ExtractComponentValueToSelected(0);
            ExtractComponentValueToEdit(0);

            if (_ScaleUI.GetCurrentUI() is CinematicUI)
            {
                CinematicUI CinematicUI = _ScaleUI.GetCurrentUI() as CinematicUI;
                if (CinematicUI.CheckIsPreviewing(_Component.Entity))
                {
                    CinematicUI.SetModified();
                }
            }
        }

        public FloatCurveListInfo GetFloatCurveListInfo(PropertyInfo Property)
        {
            FloatCurveListInfo info = new FloatCurveListInfo();
            if (Property.PropertyType.Equals(typeof(Vector3f)) || Property.PropertyType.Equals(typeof(Double3)) || Property.PropertyType.Equals(typeof(Float3)))
            {
                if (PropertyName.GetCString() == "Rotation")
                {
                    info.Items.Add(new FloatCurveListInfoItem());
                    info.Items.Add(new FloatCurveListInfoItem());
                    info.Items.Add(new FloatCurveListInfoItem());
                    info.Items.Add(new FloatCurveListInfoItem());
                    info.Items.Add(new FloatCurveListInfoItem());
                    info.Items[0].CurveName = new UniqueString("x");
                    info.Items[1].CurveName = new UniqueString("y");
                    info.Items[2].CurveName = new UniqueString("z");
                    info.Items[3].CurveName = new UniqueString("w");
                    info.Items[4].CurveName = new UniqueString("t");
                }
                else
                {
                    info.Items.Add(new FloatCurveListInfoItem());
                    info.Items.Add(new FloatCurveListInfoItem());
                    info.Items.Add(new FloatCurveListInfoItem());
                    if (PropertyName.GetCString() == "Color")
                    {
                        info.Items[0].CurveName = new UniqueString("r");
                        info.Items[1].CurveName = new UniqueString("g");
                        info.Items[2].CurveName = new UniqueString("b");
                    }
                    else
                    {
                        info.Items[0].CurveName = new UniqueString("x");
                        info.Items[1].CurveName = new UniqueString("y");
                        info.Items[2].CurveName = new UniqueString("z");
                    }
                }
            }
            else if (Property.PropertyType.Equals(typeof(Vector2f)) || Property.PropertyType.Equals(typeof(Float2)))
            {
                info.Items.Add(new FloatCurveListInfoItem());
                info.Items.Add(new FloatCurveListInfoItem());
                info.Items[0].CurveName = new UniqueString("x");
                info.Items[1].CurveName = new UniqueString("y");
            }
            else if (Property.PropertyType.Equals(typeof(float)) || Property.PropertyType.Equals(typeof(string)) || Property.PropertyType.Equals(typeof(UInt32)) || Property.PropertyType.Equals(typeof(bool)))
            {
                info.Items.Add(new FloatCurveListInfoItem());
                info.Items[0].CurveName = PropertyName;
            }
            else if (Property.PropertyType.Equals(typeof(Float4)))
            {
                info.Items.Add(new FloatCurveListInfoItem());
                info.Items.Add(new FloatCurveListInfoItem());
                info.Items.Add(new FloatCurveListInfoItem());
                info.Items.Add(new FloatCurveListInfoItem());
                info.Items[0].CurveName = new UniqueString("x");
                info.Items[1].CurveName = new UniqueString("y");
                info.Items[2].CurveName = new UniqueString("z");
                info.Items[3].CurveName = new UniqueString("w");
            }

            return info;
        }

        protected void OnButtonAddKeyFrameClicked(Button Sender)
        {

            decimal PlayHeadValue = CinematicUI.GetInstance().GetPlayHeadValue();
            EditOperation_AddKeyFrame.OnAddKeyFrameOperationUndo UndoFunc = () =>
            {
                List<KeyFrame> keyFrames = GetCurHeadKeyFrames(PlayHeadValue);
                RemoveComponentValueFromSelected(keyFrames);
            };

            EditOperation_AddKeyFrame.OnAddKeyFrameOperationRedo RedoFunc = () =>
            {
                object PropertyValue = RawGetPropertyValueFunction(_Component, _PropertyDisplayName);
                if (_PropertyDisplayName == "Rotation")
                {
                    PropertyValue = Quaternion64.EulerToQuaternion64((PropertyValue as Double3).ToRadian());
                }
                foreach (var subtrack in this.GetChildList())
                {
                    ((NumericTrack)subtrack).PropertyAddKeyFrame(PlayHeadValue, PropertyValue);
                }
            };

            EditOperation_AddKeyFrame operation = new EditOperation_AddKeyFrame(UndoFunc, RedoFunc);
            EditOperationManager.GetInstance().AddOperation(operation);
            RedoFunc();
        }

        public Entity GetPrevieEntity()
        {
            return _Previewing;
        }

        public Component GetComponent()
        {
            return _Component;
        }

        public string GetDisplayPropertyName()
        {
            return _PropertyDisplayName;
        }

        protected string ContainTwoString(String value)
        {
            string[] SplitString = value.Split(".");
            List<string> SplitList = new List<string>(SplitString);
            string Result;
            if (SplitList.Count > 1)
            {
                Result = SplitList[SplitList.Count - 2] + "." + SplitList[SplitList.Count - 1];
            }
            else
            {
                Result = value;
            }
            return Result;
        }

        public float GetNearestKeyValue()
        {
            var holder = ParentTrack as ComponentTrackHolder;

            float FirstKeyValue = 0.0f;
            if (Children.Count > 0)
            {
                float preKeyScreenX = -1.0f;
                var filtered = KeyFrames.Where(k =>
                {
                    float curKeyScreenX = (float)_ScaleUI.ValueToScreenX(k.GetKeyValue());

                    if (preKeyScreenX > 0 && Math.Abs(preKeyScreenX - curKeyScreenX) < 2)
                        return false;

                    preKeyScreenX = curKeyScreenX;
                    return true;
                });


                foreach (var key in filtered)
                {
                    //var distance = holder.GetScreenDistance(key);
                    var distance = holder.GetKeyDistance(key);
                    if (distance < _ScaleUI.GetUnit())
                    {
                        FirstKeyValue = (float)key.GetKeyValue();
                        break;
                    }
                }
            }
            return FirstKeyValue;
        }

    }
}