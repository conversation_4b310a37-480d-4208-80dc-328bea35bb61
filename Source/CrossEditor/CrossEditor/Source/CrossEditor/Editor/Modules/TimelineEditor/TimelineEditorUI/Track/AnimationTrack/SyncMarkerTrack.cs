using EditorUI;

namespace CrossEditor
{
    public class SyncMarkerTrack : FinalTrack
    {
        public override void AddKey(KeyFrame Key)
        {
            base.AddKey(Key);
            Key.MoveEndEvent += OnKeyFrameMoveEnd;
        }

        public override void AddNewKey(decimal Key, object Value)
        {
            MSAPreviewContext.GetInstance().AddSubData(TagObject as PreviewAnimSeqAsset,
                MSAPreviewContext.AnimSubDataType.SyncMarker, Value as AnimSyncMarker);
        }

        public override KeyFrame RemoveKeyFrame(KeyFrame KeyFrame)
        {
            return MSAPreviewContext.GetInstance().RemoveSubData(TagObject as PreviewAnimSeqAsset,
                MSAPreviewContext.AnimSubDataType.SyncMarker, KeyFrame.BindObject as AnimSyncMarker) ?
                KeyFrame : null;
        }

        public override object GetValue(float Key)
        {
            int Index = KeyFrames.BinarySearch(new SimpleKeyFrame(null, this, (decimal)Key));

            if (Index > 0) return KeyFrames[Index];
            else return null;
        }

        public override void Draw(UIManager UIManager)
        {
            Color BackColor = GetIsSelected() ? SelectedColor : UnSelectedColor;

            RectangleF Bound = GetBound();
            int X = (int)Bound.X;
            int Y = (int)Bound.Y;
            int Width = (int)Bound.Width;
            int Height = (int)Bound.Height;

            if (Y == 0) return;

            EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
            EditorUICanvas.DrawLineF(new Vector2f(X, Y), new Vector2f(X + Width, Y), 1f, ref Color.EDITOR_UI_GRAY_DRAW_COLOR);
            EditorUICanvas.DrawLineF(new Vector2f(X, Y + Height), new Vector2f(X + Width, Y + Height), 1f, ref Color.EDITOR_UI_GRAY_DRAW_COLOR);

            DrawKeyFrames();
        }

        protected override void OnButtonAddKeyFrameClicked(Button Sender)
        {
            int X = _ButtonAddKeyFrame.GetScreenX();
            int Y = _ButtonAddKeyFrame.GetScreenY() + _ButtonAddKeyFrame.GetHeight();
            int Width = FontSize * 10;
            int Height = GetUIManager().GetDefaultFont(FontSize).GetCharHeight() + 2;

            ExtendedRenamer Renamer = new ExtendedRenamer(GetUIManager());
            Renamer.Rename("", X, Y, Width, Height, FontSize,
                (RenameSender, NewName, OldName) =>
                {
                    if (NewName == "")
                    {
                        RenameSender.SetError();
                        return;
                    }

                    bool bDuplicated = false;
                    foreach (var Child in ParentTrack.GetChildList())
                    {
                        var Result = Child.GetKeyFrames().Find(KF => (KF.BindObject as AnimSyncMarker).Name == NewName);
                        if (Result != null)
                        {
                            bDuplicated = true;
                            break;
                        }
                    }

                    if (bDuplicated)
                    {
                        RenameSender.SetError();
                        return;
                    }

                    decimal Key = _ScaleUI.GetCurrentUI().GetHeadLocation();
                    var Data = new AnimSyncMarker
                    {
                        Name = NewName,
                        TriggerTimePos = (float)_ScaleUI.FrameToSecond(Key)
                    };
                    AddNewKey(Key, Data);

                    RenameSender.Close();
                },
                (RenameSender) =>
                {
                    RenameSender.Close();
                });
        }

        protected override void OnKeyFrameMove(object Sender, MoveEvnetArgs Args)
        {
            var Key = Sender as KeyFrame;
            var Data = Key.BindObject as AnimSyncMarker;
            Data.TriggerTimePos = (float)_ScaleUI.FrameToSecond(Key.GetKeyValue());

            if (InspectorUI.GetInstance().GetObjectInspected() == Sender)
                InspectorUI.GetInstance().ReadValueAndUpdateLayout();
        }

        private void OnKeyFrameMoveEnd(object Sender, MoveEvnetArgs Args)
        {
            var Key = Sender as KeyFrame;
            var Data = Key.BindObject as AnimSyncMarker;
            MSAPreviewContext.GetInstance().SetSubDataByName(TagObject as PreviewAnimSeqAsset,
                MSAPreviewContext.AnimSubDataType.SyncMarker, Data.Name, Data);
        }
    }
}
