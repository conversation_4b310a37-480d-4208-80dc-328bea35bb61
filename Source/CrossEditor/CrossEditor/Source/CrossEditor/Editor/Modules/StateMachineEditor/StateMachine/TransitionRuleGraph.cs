using CEngine;
using EditorUI;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Text.Json.Nodes;

namespace CrossEditor
{
    public class TransitionRuleGraph : NodeGraphModel
    {
        public FlowNode_TransitionResult ResultNode;

        public TransitionRuleGraph() : base()
        {
            Type = NodeGraphType.TransitionRuleGraph;
            CreateResultNode();
        }

        public void CreateResultNode()
        {
            ResultNode = new FlowNode_TransitionResult();
            ResultNode.X = 200;
            ResultNode.Y = 200;

            ImportNode(ResultNode);
        }

        #region Save

        public override void SaveNodeToRecord(Node NodeToSave, Record RecordNode)
        {
            base.SaveNodeToRecord(NodeToSave, RecordNode);

            if (NodeToSave is Anim_FlowNode)
            {
                string JsonStr = JsonConvert.SerializeObject((NodeToSave as Anim_FlowNode).ToData(), Formatting.Indented);
                RecordNode.SetString("NodeJsonData", JsonStr);
            }
        }

        #endregion

        #region Load

        public override void LoadFromRecord(Record RecordNodeGraph)
        {
            base.LoadFromRecord(RecordNodeGraph);

            ResultNode = GetNodesOf<FlowNode_TransitionResult>()[0];
        }

        public override Node LoadNodeFromRecord(Record RecordNode)
        {
            Node Node = base.LoadNodeFromRecord(RecordNode);
            if (Node is Anim_FlowNode)
            {
                (Node as Anim_FlowNode).FromJsonData(RecordNode.GetString("NodeJsonData"));
            }
            return Node;
        }

        #endregion

        #region Load Old

        public void LoadFromRecord_Old(Record RecordNodeGraph)
        {
            Nodes.Clear();

            NodeID = RecordNodeGraph.GetInt("NodeID");
            ConnectionID = RecordNodeGraph.GetInt("ConnectionID");

            int Count = RecordNodeGraph.GetChildCount();
            for (int i = 0; i < Count; i++)
            {
                Record RecordChild = RecordNodeGraph.GetChild(i);
                string TypeString = RecordChild.GetTypeString();
                if (TypeString == "Connection")
                {
                    LoadConnectionFromRecord_Old(RecordChild);
                }
                else
                {
                    LoadNodeFromRecord_Old(RecordChild);
                }
            }

            ResultNode = GetNodesOf<FlowNode_TransitionResult>()[0];
        }

        public void LoadConnectionFromRecord_Old(Record RecordConnection)
        {
            Connection Connection = new Connection();

            Connection.ID = RecordConnection.GetInt("ID");

            int OutSlotNodeID = RecordConnection.GetInt("OutSlotNodeID");
            string OutSlotName = RecordConnection.GetString("OutSlotName");
            Slot OutSlot = FindNodeByID(OutSlotNodeID).FindOutSlot(OutSlotName);
            Connection.BindOutSlot(OutSlot);

            int InSlotNodeID = RecordConnection.GetInt("InSlotNodeID");
            string InSlotName = RecordConnection.GetString("InSlotName");
            Slot InSlot = FindNodeByID(InSlotNodeID).FindInSlot(InSlotName);
            Connection.BindInSlot(InSlot);

            AddConnection(Connection);
        }

        public void LoadNodeFromRecord_Old(Record RecordNode)
        {
            string TypeString = RecordNode.GetTypeString();
            Node Node = CreateNode(TypeString);
            if (Node == null)
            {
                DebugHelper.Assert(false);
            }
            else
            {
                Type NodeType = Node.GetType();
                PropertyInfo[] Properties = NodeType.GetProperties();
                foreach (PropertyInfo Info in Properties)
                {
                    PropertyInfoAttribute PropertyInfoAttribute = PropertyInfoAttribute.GetPropertyInfoAttribute(Info);
                    if (PropertyInfoAttribute.bHide == false && Info.Name != "NodeName")
                    {
                        object Value = RecordLoad(RecordNode, Info.PropertyType, Info.Name);
                        if (Value != null)
                        {
                            Info.SetValue(Node, Value);
                        }
                    }
                }
            }

            Node.ID = RecordNode.GetInt("ID");
            Node.X = RecordNode.GetInt("X");
            Node.Y = RecordNode.GetInt("Y");
            Node.SetOwner(this);

            AddNode(Node);

            if (Node is Anim_FlowNode)
            {
                (Node as Anim_FlowNode).FromJsonData(RecordNode.GetString("NodeJsonData"));
            }

        }

        #endregion

        public string ToExpression()
        {
            ResultNode = FindNodeByID(ResultNode.ID) as FlowNode_TransitionResult;
            return ResultNode.ToExpression();
        }

        public override List<MenuBuilder> BuildNodeMenu(Action<Node> ProcessNode)
        {
            List<MenuBuilder> MenuBuilders = new List<MenuBuilder>();

            MenuBuilders.Add(new MenuBuilder
            {
                Text = "LogicOp",
                bHasChild = true,
                Children = new List<MenuBuilder>
                {
                    new MenuBuilder { Text="And", Event=(Sender)=> { ProcessNode(new FlowNode_BinaryLogicOp(BinaryLogicOp.And)); } },
                    new MenuBuilder { Text="Or", Event=(Sender)=> { ProcessNode(new FlowNode_BinaryLogicOp(BinaryLogicOp.Or)); } },
                    new MenuBuilder { Text="Xor", Event=(Sender)=> { ProcessNode(new FlowNode_BinaryLogicOp(BinaryLogicOp.Xor)); } },
                    new MenuBuilder { bIsSeperator = true },
                    new MenuBuilder { Text="Not", Event=(Sender)=> { ProcessNode(new FlowNode_UnaryLogicOp(UnaryLogicOp.Not)); } }
                }
            });
            MenuBuilders.Add(new MenuBuilder
            {
                Text = "Compare",
                bHasChild = true,
                Children = new List<MenuBuilder>
                {
                    new MenuBuilder { Text="==", Event=(Sender)=> { ProcessNode(new FlowNode_Compare(Relation.EqualTo)); } },
                    new MenuBuilder { Text="!=", Event=(Sender)=> { ProcessNode(new FlowNode_Compare(Relation.InequalTo)); } },
                    new MenuBuilder { Text="<", Event=(Sender)=> { ProcessNode(new FlowNode_Compare(Relation.LowerTo)); } },
                    new MenuBuilder { Text="<=", Event=(Sender)=> { ProcessNode(new FlowNode_Compare(Relation.LowerEqualTo)); } },
                    new MenuBuilder { Text=">", Event=(Sender)=> { ProcessNode(new FlowNode_Compare(Relation.GreaterTo)); } },
                    new MenuBuilder { Text=">=", Event=(Sender)=> { ProcessNode(new FlowNode_Compare(Relation.GreaterEqualTo)); } }
                }
            });
            MenuBuilders.Add(new MenuBuilder
            {
                Text = "ArithOp",
                bHasChild = true,
                Children = new List<MenuBuilder>
                {
                    new MenuBuilder { Text="+", Event=(Sender)=> { ProcessNode(new FlowNode_BinaryArithOp(BinaryArithOp.Add)); } },
                    new MenuBuilder { Text="- ", Event=(Sender)=> { ProcessNode(new FlowNode_BinaryArithOp(BinaryArithOp.Substract)); } },
                    new MenuBuilder { Text="*", Event=(Sender)=> { ProcessNode(new FlowNode_BinaryArithOp(BinaryArithOp.Multiply)); } },
                    new MenuBuilder { Text="/", Event=(Sender)=> { ProcessNode(new FlowNode_BinaryArithOp(BinaryArithOp.Divide)); } },
                    new MenuBuilder { Text="%", Event=(Sender)=> { ProcessNode(new FlowNode_BinaryArithOp(BinaryArithOp.Modulo)); } },
                    new MenuBuilder { bIsSeperator = true },
                    new MenuBuilder { Text="- ", Event=(Sender)=> { ProcessNode(new FlowNode_UnaryArithOp(UnaryArithOp.Negative)); } }
                }
            });
            MenuBuilders.Add(new MenuBuilder
            {
                Text = "Constant",
                bHasChild = true,
                Children = new List<MenuBuilder>
                {
                    new MenuBuilder { Text="True", Event=(Sender)=> { ProcessNode(new FlowNode_Bool(true)); } },
                    new MenuBuilder { Text="False", Event=(Sender)=> { ProcessNode(new FlowNode_Bool(false)); } },
                    new MenuBuilder { Text="Integer", Event=(Sender)=> { ProcessNode(new FlowNode_Integer(0)); } },
                    new MenuBuilder { Text="Float", Event=(Sender)=> { ProcessNode(new FlowNode_Float(0.0f)); } }
                }
            });
            MenuBuilders.Add(new MenuBuilder
            {
                Text = "Convert",
                bHasChild = true,
                Children = new List<MenuBuilder>
                {
                    new MenuBuilder { Text="ToInt", Event=(Sender)=> { ProcessNode(new FlowNode_ToInt()); } },
                    new MenuBuilder { Text="ToFloat", Event=(Sender)=> { ProcessNode(new FlowNode_ToFloat()); } }
                }
            });

            {
                NodeGraphModel Graph = this;
                while (Graph.GetOwner() != null)
                {
                    Graph = Graph.GetOwner().GetOwner();
                }

                if (Graph != null && Graph is AnimGraph)
                {
                    AnimGraph AnimGraph = Graph as AnimGraph;
                    MenuBuilders.Add(new MenuBuilder
                    {
                        Text = "Param Nodes",
                        bHasChild = true,
                        Children = AnimGraph.BuildTransitionRuleParamMenu(ProcessNode)
                    });
                }
            }

            MenuBuilders.Add(BuildTimeRemainFuncNodeMenu(ProcessNode));

            return MenuBuilders;
        }

        public MenuBuilder BuildTimeRemainFuncNodeMenu(Action<Node> ProcessNode)
        {
            MenuBuilder MenuBuilder = new MenuBuilder();

            MenuBuilder.Text = "AnimTimeRemaining for ";
            MenuBuilder.bHasChild = true;
            MenuBuilder.Children = new List<MenuBuilder>();

            if (GetOwner() is TransitionRule Rule)
            {
                Transition Transition = Rule.Transition;
                StateGraph StateGraph = Transition.GetOutStateGraph();
                foreach (Anim_PlayAnimBaseNode Node in StateGraph.GetNodesOf<Anim_PlayAnimBaseNode>())
                {
                    foreach (string UsedAnim in Node.GetUsedAnims())
                    {
                        MenuBuilder.Children.Add(new MenuBuilder
                        {
                            Text = AnimUtil.TrimAnimPath(PathHelper.GetFileName(UsedAnim)),
                            Event = (Sender) => { ProcessNode(new FlowNode_AnimTimeRemaining(UsedAnim)); }
                        });
                    }
                }
            }
            else if (GetOwner() is ConduitNode Conduit)
            {
                // TODO: Search conduit output node.
            }

            return MenuBuilder;
        }

        public void AddNodeFromData(JObject jobject)
        {
            string TypeName = jobject["Type"].ToString();
            FlowNode Node = CreateNode(TypeName) as FlowNode;
            if (Node is null)
            {
                Node = CreateNode("FlowNode_Unknown") as FlowNode;
            }
            var jnode = JsonNode.Parse(jobject.ToString());
            Node.SetOwner(this);
            Node.FromJsonObject(jnode.AsObject());
            AddNode(Node);
            NodeID = Math.Max(NodeID, Node.ID + 1);
        }

        public void FromData(TransitionRuleGraphData Data)
        {
            Nodes.Clear();
            foreach (var jobject in Data.Nodes)
            {
                AddNodeFromData(jobject);
            }
            foreach (var LinkData in Data.Links)
            {
                AddConnectionFromData(LinkData);
            }

            ResultNode = GetNodesOf<FlowNode_TransitionResult>()[0];
        }

        public TransitionRuleGraphData ToData()
        {
            TransitionRuleGraphData GraphData = new TransitionRuleGraphData();

            foreach (var Node in Nodes)
            {
                if (Node is FlowNode FlowNode)
                {
                    var jsonObject = FlowNode.ToJsonObject();
                    GraphData.Nodes.Add(JObject.Parse(jsonObject.ToJsonString()));
                }
            }
            foreach (var Connection in Connections)
            {
                GraphData.Links.Add(Connection.ToData());
            }
            return GraphData;
        }
    }
}
