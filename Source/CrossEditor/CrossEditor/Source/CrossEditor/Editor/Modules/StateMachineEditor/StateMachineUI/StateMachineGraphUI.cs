using EditorUI;

namespace CrossEditor
{
    public class StateMachineGraphUI
    {
        static StateMachineGraphUI Instance = new StateMachineGraphUI("State Machine");

        StateMachineGraph StateMachineGraph;
        NodeGraphView View;

        public static StateMachineGraphUI GetInstance()
        {
            return Instance;
        }

        public StateMachineGraphUI()
        {
            StateMachineGraph = new StateMachineGraph();
            View = new NodeGraphView();
            View.BindModel(StateMachineGraph);
        }

        public StateMachineGraphUI(string Name) : this()
        {
            StateMachineGraph.Name = Name;
            View.UpdateDockingCardText();
            View.RefreshNavigator();
        }

        public void Initialize()
        {
            View.BeginConnectEvent += OnBeginConnect;
            View.ConnectingEvent += OnConnecting;
            View.HoverEvent += OnHover;
            View.DoubleClickEvent += OnDoubleClick;
        }

        public DockingCard GetDockingCard() => View.GetDockingCard();

        public NodeGraphView GetGraphView() => View;

        #region Event

        public void OnBeginConnect(object Context)
        {
            if (Context is SlotWrapper)
            {
                View.SlotA = ((SlotWrapper)Context).OutSlot;
                View.CurrentConnection = new Transition();
                View.CurrentConnection.OutSlot = View.SlotA;
            }
        }

        public void OnConnecting(object Context)
        {
            if (Context is SlotWrapper)
            {
                View.SetHoverSlot(((SlotWrapper)Context).InSlot);
            }
        }

        public void OnHover(object HoverObject)
        {
            if (HoverObject is SlotWrapper)
            {
                View.SetHoverSlot(((SlotWrapper)HoverObject).InSlot);
                View.SetCursor(SystemCursor.Cross);
            }
        }

        public void OnDoubleClick(object Context)
        {
            if (Context is Node)
            {
                Node Node = Context as Node;
                if (Node.bHasSubGraph)
                {
                    Node.SubGraph.Camera.RecoverToOriZoom();
                    View.BindModel(Node.SubGraph);
                }
            }
        }

        #endregion
    }
}
