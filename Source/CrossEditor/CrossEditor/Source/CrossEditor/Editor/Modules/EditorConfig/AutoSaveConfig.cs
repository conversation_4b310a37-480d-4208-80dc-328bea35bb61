using CEngine;
using System.IO;
using System.Reflection;
using System.Xml;
using System.Xml.Serialization;


namespace CrossEditor
{
    [XmlRoot]
    public class AutoSaveConfig
    {
        const string gConfigFileName = EditorConfig.EDITOR_CONFIG_RELATIVE_PATH + "AutoSaveConfig.config";

        [XmlElement("SaveScene")]
        [PropertyInfo(PropertyType = "Auto", ToolTips = "SaveScene")]
        public bool SaveScene { get; set; }


        [XmlElement("SavePrefab")]
        [PropertyInfo(PropertyType = "Auto", ToolTips = "SavePrefab")]
        public bool SavePrefab { get; set; }

        [XmlElement("Frequency")]
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Frequency in Minutes")]
        public int Frequency { get; set; }

        [XmlElement("Warning")]
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Warning in Second")]
        public int Warning { get; set; }

        public AutoSaveConfig()
        {
            SaveScene = true;
            SavePrefab = true;
            Frequency = 10;
            Warning = 10;
        }

        public void Load()
        {
            if (File.Exists(gConfigFileName) == false)
            {
                return;
            }
            XmlSerializer Ser = new XmlSerializer(typeof(AutoSaveConfig));

            using (StreamReader Reader = new StreamReader(gConfigFileName))
            {
                AutoSaveConfig config = (AutoSaveConfig)Ser.Deserialize(Reader);
                SaveScene = config.SaveScene;
                SavePrefab = config.SavePrefab;
                Frequency = config.Frequency;
                Warning = config.Warning;
                Reader.Close();
            }
        }

        public void Dump()
        {
            XmlSerializer Ser = new XmlSerializer(typeof(AutoSaveConfig));
            using (StreamWriter Writer = new StreamWriter(gConfigFileName))
            {
                Ser.Serialize(Writer, this);
                Writer.Close();
            }
        }
    }
}
