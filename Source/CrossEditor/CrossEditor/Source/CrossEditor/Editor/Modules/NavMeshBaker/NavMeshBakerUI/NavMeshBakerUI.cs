using EditorUI;

namespace CrossEditor
{
    public class NavMeshBakerUI : DockingUI
    {
        const int SPAN_X = 5;
        const int BUTTON_FONT_SIZE = 18;
        const int BUTTON_HEIGHT = 30;
        const int SEPARATOR_HEIGHT = 1;

        static NavMeshBakerUI _Instance = new NavMeshBakerUI();

        ScrollView _ScrollView;
        Panel _ScrollPanel;
        Inspector _Inspector;
        InspectorHandler _InspectorHandler;
        Button _ResetButton;
        Button _BakeButton;

        NavMeshBakerSettings _NavMeshBakerSettings;

        public static NavMeshBakerUI GetInstance()
        {
            return _Instance;
        }

        NavMeshBakerUI()
        {
            _NavMeshBakerSettings = new NavMeshBakerSettings();
            _NavMeshBakerSettings.Reset();
        }

        public bool Initialize()
        {
            _ScrollView = new ScrollView();
            _ScrollView.Initialize();
            _ScrollView.SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);
            _ScrollView.GetHScroll().SetEnable(false);

            _ScrollPanel = _ScrollView.GetScrollPanel();
            _ScrollPanel.SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);

            base.Initialize("NavMeshBaker", _ScrollView);

            _InspectorHandler = new InspectorHandler();
            _InspectorHandler.UpdateLayout += UpdateLayout;
            _InspectorHandler.InspectObject += () => { _Inspector.InspectObject(_NavMeshBakerSettings); };
            _InspectorHandler.ReadValue += () => { _Inspector.ReadValue(); };

            _Inspector = new Inspector_Struct();
            _Inspector.InspectObject(_NavMeshBakerSettings);
            _Inspector.SetContainer(_ScrollPanel);
            _Inspector.SetInspectorHandler(_InspectorHandler);

            _ResetButton = new Button();
            InitializeButton(_ResetButton, "Reset", OnResetButtonClicked);

            _BakeButton = new Button();
            InitializeButton(_BakeButton, "Bake", OnBakeButtonClicked);

            return true;
        }

        void InitializeButton(Button Button, string Text, ButtonClickedEventHandler OnButtonClicked)
        {
            Button.Initialize();
            Button.SetText(Text);
            Button.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            Button.SetFontSize(BUTTON_FONT_SIZE);
            Button.SetTextOffsetY(2);
            Button.ClickedEvent += OnButtonClicked;
            _ScrollPanel.AddChild(Button);
        }

        void Reset()
        {
            _NavMeshBakerSettings.Reset();
            foreach (Inspector Inspector in _Inspector.GetChildInspectors())
            {
                Inspector.ReadValue();
            }
        }

        public override void OnPositionChanged(Control Sender, bool bPositionChanged, bool bSizeChanged)
        {
            if (bSizeChanged)
            {
                UpdateLayout();
            }
        }

        void UpdateLayout()
        {
            int ScrollPanelWidth = _ScrollView.GetWidth();
            int Y = 5;
            if (_Inspector != null)
            {
                _Inspector.UpdateLayout(ScrollPanelWidth, ref Y);
                if (Y > _ScrollView.GetHeight())
                {
                    ScrollPanelWidth = _ScrollView.GetWidth() - ScrollView.SCROLL_BAR_SIZE;
                    Y = 0;
                    _Inspector.UpdateLayout(ScrollPanelWidth, ref Y);
                }
            }

            int AvailableWidth = _ScrollView.GetWidth() - 2 * SPAN_X;

            Y += 5;
            _ResetButton.SetPosition(SPAN_X, Y, AvailableWidth, BUTTON_HEIGHT);
            Y += BUTTON_HEIGHT;

            Y += 5;
            _BakeButton.SetPosition(SPAN_X, Y, AvailableWidth, BUTTON_HEIGHT);
            Y += BUTTON_HEIGHT;

            _ScrollPanel.SetSize(ScrollPanelWidth, Y);
            _ScrollView.UpdateScrollBar();
        }

        void OnResetButtonClicked(Button Sender)
        {
            Reset();
        }

        void OnBakeButtonClicked(Button Sender)
        {
            EditorScene.GetInstance().BakeNavMesh(_NavMeshBakerSettings.GetData());
        }

    }
}
