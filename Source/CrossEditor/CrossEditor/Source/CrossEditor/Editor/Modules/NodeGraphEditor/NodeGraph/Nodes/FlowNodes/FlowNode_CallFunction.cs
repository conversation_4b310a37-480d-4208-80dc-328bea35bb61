using CEngine;
using EditorUI;
using System;

namespace CrossEditor
{
    class FlowNode_CallFunction : FlowNode_StringContent
    {
        [PropertyInfo()]
        public string FunctionName { get; set; } = "";

        public FlowNode_CallFunction()
        {
            Name = "CallFunction";
            NodeType = NodeType.Function;

            AddOutSlot("Result", SlotType.DataFlow);
        }

        public override object Eval(int OutSlotIndex)
        {
            return false;
        }

        public override string GetStringContent()
        {
            return FunctionName;
        }

        public override string ToExpression()
        {
            return FunctionName;
        }
    }
}
