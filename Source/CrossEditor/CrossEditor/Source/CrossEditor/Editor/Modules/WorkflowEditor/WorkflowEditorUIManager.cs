using Clicross;
using EditorUI;
using System.Collections.Generic;

namespace CrossEditor
{
    public class WorkflowEditorUIManager
    {
        public static WorkflowEditorUIManager Instance { get; } = new WorkflowEditorUIManager();

        WorkflowEditorUIManager()
        {
            Clicross.WorkflowEditor.GetAvailableClasses();
            foreach (string name in WorkflowEditor.ClassNames)
            {
                ClassNames.Add(name);
            }
        }

        private Dictionary<string, WorkflowEditorUI> mWorkflowEditors = new Dictionary<string, WorkflowEditorUI>();

        public void Update(long TimeElapsed)
        {
            foreach (var item in mWorkflowEditors.Values)
            {
                item.Update(TimeElapsed);
            }

        }
        public List<string> ClassNames = new List<string>();
        public void OpenWorkflow(string FilePath, CEngine.ClassIDType id)
        {
            DockingCard dockingCard = null;
            if (!mWorkflowEditors.ContainsKey(FilePath))
            {
                var editor = new WorkflowEditorUI(FilePath);
                editor.Initialize();
                dockingCard = editor.GetDockingCard();
                dockingCard.SetDocument(true);
                mWorkflowEditors.Add(File<PERSON><PERSON>, editor);
                dockingCard.CloseEvent += (DockingCard Sender, ref bool bNotToClose) =>
                {
                    mWorkflowEditors.Remove(FilePath);
                };
            }
            else
            {
                dockingCard = mWorkflowEditors[FilePath].GetDockingCard();
            }
            MainUI.GetInstance().ActivateDockingCard_WorkflowEditor(dockingCard);
        }
        public static string CreateAndSave(UIManager UIManager, string className, string path)
        {
            string GraphFileName = className.Replace("::", "_");
            if (StringHelper.IsUnicodeString(GraphFileName))
            {
                CommonDialogUI.ShowSimpleOKDialog(UIManager, "Tips", "There is a non-ascii char/chars in workflow filename.");
                return "";
            }
            var NewFilename = path + "/" + ProjectUI.GetInstance().GetNewFileName(path, GraphFileName + "{0}.flow");
            WorkflowEditorContext.CreateAndSave(NewFilename, className);
            return NewFilename;
        }

        public void DoSave()
        {
            foreach (var item in mWorkflowEditors.Values)
            {
                if (item.IsFocused())
                {
                    item.DoSave();
                }
            }
        }
        public bool IsFocused()
        {
            foreach (var item in mWorkflowEditors.Values)
            {
                if (item.IsFocused())
                {
                    return true;
                }
            }
            return false;
        }
    }
}