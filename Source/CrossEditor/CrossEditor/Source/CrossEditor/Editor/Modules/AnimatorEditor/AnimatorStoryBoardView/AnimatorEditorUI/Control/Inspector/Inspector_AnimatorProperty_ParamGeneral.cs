using EditorUI;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    internal class Inspector_AnimatorProperty_ParamGeneral : Inspector_Property
    {
        protected ComboBox _ComboBoxParam;
        protected ComboBox _ComboBoxOperate;
        protected Edit _EditValue;
        protected Button _ButtonRemove;

        public GetPropertyValueFunction GetOperateValueFunction;
        public SetPropertyValueFunction SetOperateValueFunction;

        public GetPropertyValueFunction GetEditorValueFunction;
        public SetPropertyValueFunction SetEditorValueFunction;

        public SetPropertyValueFunction RemoveParamFunction;

        public List<IAnimatorParam> animParams = new List<IAnimatorParam>();

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);
            Control Container = GetValueContainer();

            // Remove Label
            Container.RemoveChild(_LabelName);

            // Combo Box Param
            _ComboBoxParam = new ComboBox();
            _ComboBoxParam.Initialize();

            foreach (IAnimatorParam param in animParams)
                _ComboBoxParam.AddItem(param.Name);

            _ComboBoxParam.ItemSelectedEvent += OnComboBoxParamItemSelected;
            _ComboBoxParam.SetSelectedItemIndex(0);
            Container.AddChild(_ComboBoxParam);

            // Combo Box Operate
            _ComboBoxOperate = new ComboBox();
            _ComboBoxOperate.Initialize();

            string[] operaters = typeof(StateCompareOperate).GetEnumNames();
            foreach (string operater in operaters)
                _ComboBoxOperate.AddItem(operater);

            _ComboBoxOperate.ItemSelectedEvent += OnComboBoxOperateItemSelected;
            _ComboBoxOperate.SetSelectedItemIndex(0);
            Container.AddChild(_ComboBoxOperate);

            // Edit Property value
            _EditValue = new Edit();
            _EditValue.SetFontSize(PROPERTY_FONT_SIZE);
            _EditValue.Initialize(EditMode.Simple_SingleLine);
            _EditValue.LoadSource("");
            _EditValue.SelfFocusChangedEvent += OnEditFocusChanged;
            EditContextUI.GetInstance().RegisterEdit(_EditValue);
            Container.AddChild(_EditValue);

            // Remove Button
            _ButtonRemove = new Button();
            _ButtonRemove.Initialize();
            _ButtonRemove.SetText("-");
            _ButtonRemove.SetFontSize(14);
            _ButtonRemove.SetTextAlign(TextAlign.CenterCenter);
            _ButtonRemove.ClickedEvent += OnRemoveButtonClicked;
            _ButtonRemove.SetToolTips("Remove Link");
            Container.AddChild(_ButtonRemove);
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            Vector2i RemoveButtonSize = new Vector2i(16, 16);

            int SpanTotal = SPAN_X * 4 + RemoveButtonSize.X;
            int CtrlWidth = (Width - SpanTotal) / 3;

            int CursorWidth = SPAN_X;
            _ComboBoxParam.SetPosition(SPAN_X, Y + 2, CtrlWidth, 16);

            CursorWidth += CtrlWidth + SPAN_X;
            _ComboBoxOperate.SetPosition(CursorWidth, Y + 2, CtrlWidth, 16);

            CursorWidth += CtrlWidth + SPAN_X;
            _EditValue.SetPosition(CursorWidth, Y + 2, CtrlWidth, 16);

            CursorWidth += CtrlWidth + SPAN_X;
            _ButtonRemove.SetPosition(CursorWidth, Y + 2, RemoveButtonSize.X, 16);

            Y += 20;
        }

        public override void ReadValue()
        {
            ReadValueParam();
            ReadValueOperator();
            ReadValueEditor();
        }

        public override void WriteValue()
        {
            WriteValueParam();
            WriteValueOperator();
        }

        #region Param Select

        public void ReadValueParam()
        {
            object PropertyValue = GetPropertyValue();
            string PropertyValueString = "";
            if (PropertyValue != null)
                PropertyValueString = PropertyValue.ToString();
            else
                PropertyValueString = "<null>";

            _ComboBoxParam.SetSelectedItemByText(PropertyValueString);
        }

        void WriteValueParam()
        {
            base.WriteValue();
            string ValueString = _ComboBoxParam.GetSelectedItemText();
            SetPropertyValue(ValueString);
        }

        void OnComboBoxParamItemSelected(ComboBox Sender)
        {
            string ValueString = _ComboBoxParam.GetSelectedItemText();
            WriteValueParam();
        }

        #endregion

        #region Operate Select

        public void ReadValueOperator()
        {
            object PropertyValue = GetOperateValueFunction(_Object, _ObjectProperty.Name, null);
            string PropertyValueString = "";
            if (PropertyValue != null)
                PropertyValueString = PropertyValue.ToString();
            else
                PropertyValueString = "<null>";

            _ComboBoxOperate.SetSelectedItemByText(PropertyValueString);
        }

        void WriteValueOperator()
        {
            Type Type = typeof(StateCompareOperate);
            string ValueString = _ComboBoxOperate.GetSelectedItemText();
            object NewValue = Enum.Parse(Type, ValueString);
            SetOperateValueFunction(_Object, _ObjectProperty.Name, NewValue, null);
        }

        void OnComboBoxOperateItemSelected(ComboBox Sender)
        {
            string ValueString = _ComboBoxOperate.GetSelectedItemText();
            WriteValueOperator();
        }

        #endregion

        void ReadValueEditor()
        {
            object PropertyValue = GetEditorValueFunction(_Object, _ObjectProperty.Name, null);
            string PropertyValueString = "";
            if (PropertyValue != null)
                PropertyValueString = PropertyValue.ToString();
            else
                PropertyValueString = "<null>";

            _EditValue.SetText(PropertyValueString);
        }

        void WriteValueEditor()
        {
            string ValueString = _EditValue.GetText();
            SetEditorValueFunction(_EditValue, _ObjectProperty.Name, ValueString, null);
        }

        void OnEditFocusChanged(Control Sender)
        {
            if (Sender.IsFocused())
                return;

            WriteValueEditor();
        }

        void OnRemoveButtonClicked(Button Sender)
        {
            RemoveParamFunction(_Object, _ObjectProperty.Name, null, null);
        }
    }
}
