using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Threading;

namespace CrossEditor
{
    public class ThumbnailUI
    {
        static ThumbnailUI _Instance = new ThumbnailUI();

        UIManager _UIManager;

        public const int ThumbnailWidth = 256;
        public const int ThumbnailHeight = 256;
        public bool _bRunFirstUpdate;
        public static bool bShowThumbnailWindow = false;
        public static bool bShowThumbnailLog = false;
        public static bool bRunThumbTest = false;
        bool _bExitRun;

        Queue<string> _TargetQueue;

        public static ThumbnailUI GetInstance()
        {
            return _Instance;
        }

        ThumbnailUI()
        {
            _bExitRun = false;
        }

        public UIManager GetUIManager()
        {
            return _UIManager;
        }

        public Device GetDevice()
        {
            return GetUIManager().GetDevice();
        }

        public bool Initialize(string ThumbnailProject)
        {
            Device Device = DeviceManager.GetInstance().CreateDevice("Thumbnail");
            VideoDriverType VideoDriverType = VideoDriverType.Null;
            string Icon = "Editor/Icons/TestIcon.ico";
            string Title = "Cross Engine";
            int X = 0;
            int Y = 0;
            int ScreenWidth = ThumbnailWidth;
            int ScreenHeight = ThumbnailHeight;
            VideoDriver VideoDriver = Device.CreateBuiltinVideoDriver(VideoDriverType);
            bool bSuccess = Device.Initialize(VideoDriver, Icon, Title, X, Y, ScreenWidth, ScreenHeight);
            if (!bSuccess)
            {
                return false;
            }

            Graphics2D Graphics2D = new Graphics2D(Device);
            Graphics2D.Initialize();
            UIManager UIManager = UIManagerList.GetInstance().CreateUIManager(Device, Graphics2D);
            UIManager.Initialize();

            _UIManager = UIManager;

            string ResourceDirectory = EditorUtilities.GetResourceDirectory();
            string ProjectDirectory = PathHelper.ToStandardForm(ThumbnailProject);
            ThumbnailRuntime.GetInstance().Initialize(ProjectDirectory, ResourceDirectory);

            _TargetQueue = new Queue<string>();

            if (bRunThumbTest)
            {
                bShowThumbnailLog = true;

                bool bTest1 = true;
                if (bTest1)
                {
                    _TargetQueue.Enqueue("Contents/Texture/water_normal0.nda");
                    _TargetQueue.Enqueue("Contents/Texture/DefaultMaterial_BaseMap.nda");
                    _TargetQueue.Enqueue("Contents/Material/WindowRainMtl.nda");
                    _TargetQueue.Enqueue("Contents/Material/ALSLight.nda");
                    _TargetQueue.Enqueue("Contents/Material/PapiLight.nda");
                }

                bool bTest2 = false;
                if (bTest2)
                {
                    string TargetDirectory = ProjectDirectory + "/Contents/Texture";
                    DirectoryWalker DirectoryWalker = new DirectoryWalker();
                    DirectoryWalker.WalkDirectory(TargetDirectory, true);
                    int Count = DirectoryWalker.GetDirectoryWalkItemCount();
                    for (int i = 0; i < Count; i++)
                    {
                        DirectoryWalkItem DirectoryWalkItem = DirectoryWalker.GetDirectoryWalkItem(i);
                        string Path = DirectoryWalkItem.Path;
                        string Extension = PathHelper.GetExtension(Path);
                        if (StringHelper.IgnoreCaseEqual(Extension, ".nda"))
                        {
                            string Path1 = Path.Substring(ProjectDirectory.Length + 1);
                            if (Path1.Contains("Paragon") ||
                                Path1.Contains("Contents/Material/Drone.nda") ||
                                Path1.Contains("Contents/WS/liantan.nda"))
                            {
                                continue;
                            }
                            if (Path1.Contains("Intermediate"))
                            {
                                continue;
                            }
                            _TargetQueue.Enqueue(Path1);
                        }
                    }
                }

                _TargetQueue.Enqueue("<Done>");

                GetDevice().Center();
                GetDevice().ShowNormal();
            }

            return true;
        }

        public void Release()
        {
            Device Device = GetDevice();
        }

        public void Run()
        {
            long Time1 = SystemHelper.GetTimeMs();
            while (!_bExitRun && GetDevice().Run())
            {
                long Time2 = SystemHelper.GetTimeMs();
                long TimeElapsed = Time2 - Time1;
                Update(TimeElapsed);
                Time1 = Time2;
                Thread.Sleep(1);
            }
        }

        void ExitRun()
        {
            GetDevice().Hide();
            _bExitRun = true;
        }

        void ImportThumbnailImage(string ImageFilename, string NdaFilename)
        {
            AssetType AssetType = AssetImporterManager.Instance().GetAssetType(ImageFilename);
            if (AssetType == AssetType.Texture)
            {
                string NDAPathTemp = ImageFilename + ".nda";
                TextureImportSetting settings = new TextureImportSetting();
                settings.ColorSpace = ImportColorSpace.Linear;
                settings.Compression = TextureCompression.Uncompressed;
                settings.GenerateMipmap = false;
                settings.SetEngineImportSetting();
                AssetImporterManager.Instance().ImportAsset(ImageFilename, NDAPathTemp);
                FileHelper.RenameFile(NDAPathTemp, NdaFilename);
            }
        }

        bool DetectThumbnailMutex()
        {
            try
            {
                Mutex MutexThumbnail = new Mutex(false, "Thumbnail");
                if (MutexThumbnail.WaitOne(0))
                {
                    MutexThumbnail.ReleaseMutex();
                    return false;
                }
                return true;
            }
            catch (AbandonedMutexException)
            {
                return false;
            }
        }

        void FetchFileToGenerateThumbnail()
        {
            string ProjectDirectory = MainUI.GetInstance().GetProjectDirectory();
            string FileToGenerateThumbnail = ProjectDirectory + "/" + ThumbnailHost.Filename_FileToGenerateThumbnail;
            string ThumbnailPNGPath = ThumbnailScene.GetThumbnailPNGPath(FileToGenerateThumbnail);
            if (FileHelper.IsFileExists(FileToGenerateThumbnail))
            {
                string ThumbnailTask = FileHelper.ReadTextFile(FileToGenerateThumbnail);
                FileHelper.DeleteFile(FileToGenerateThumbnail);
                FileHelper.DeleteFile(ThumbnailPNGPath);
                if (bShowThumbnailLog)
                {
                    Console.WriteLine("ThumbnailProcess: {0} Start.", ThumbnailTask);
                }
                _TargetQueue.Enqueue(ThumbnailTask);
            }
        }

        void SaveFileThumbnailGenerated(string ThumbnailPath)
        {
            string ProjectDirectory = MainUI.GetInstance().GetProjectDirectory();
            string FileThumbnailGenerated = ProjectDirectory + "/" + ThumbnailHost.Filename_FileThumbnailGenerated;
            string FileThumbnailGeneratedTemp = FileThumbnailGenerated + ".tmp";
            FileHelper.WriteTextFile(FileThumbnailGeneratedTemp, ThumbnailPath);
            FileHelper.RenameFile(FileThumbnailGeneratedTemp, FileThumbnailGenerated);
            if (bShowThumbnailLog)
            {
                Console.WriteLine("ThumbnailProcess: {0} Done.", ThumbnailPath);
            }
        }

        public void Update(long TimeElapsed)
        {
            if (bShowThumbnailWindow == false && bRunThumbTest == false)
            {
                //bool bThumbnailMutexDetected = DetectThumbnailMutex();
                //if (bThumbnailMutexDetected == false)
                //{
                //    Console.WriteLine("Thumbnail Mutex Closed, Stop Generating Thumbnails.");
                //    SystemHelper.ExitProcess(0);
                //    return;
                //}
            }

            FetchFileToGenerateThumbnail();

            ThumbnailScene ThumbnailScene = ThumbnailScene.GetInstance();
            string ThumbnailTask = "";
            bool bHasTarget = _TargetQueue.TryPeek(out ThumbnailTask);
            string ThumbnailPath = ThumbnailScene.GetThumbnailPath(ThumbnailTask);
            string ThumbnailPNGPath = ThumbnailScene.GetThumbnailPNGPath(ThumbnailTask);
            if (bHasTarget)
            {
                ThumbnailScene.GetInstance().GetWorld().SetWorldEnable(true);
                {
                    ThumbnailScene.GetInstance().Update();
                }

                if (ThumbnailScene.NeedGenerateThumbnail(ThumbnailTask, ThumbnailPath))
                {
                    if (ThumbnailTask.Contains("Intermediate/Thumb") == false && !ThumbnailTask.Contains("Done"))
                    {
                        if (bShowThumbnailLog)
                        {
                            Console.WriteLine(ThumbnailTask + " generating...");
                        }
                        if (ThumbnailScene._PreviewNdaPath != ThumbnailTask)
                        {
                            ThumbnailScene._PreviewNdaPath = ThumbnailTask;
                            ThumbnailScene.FrameCounter = 0;
                            ThumbnailScene.ClearScene();
                            ThumbnailScene.SaveThumbnail(ThumbnailPNGPath);
                            FileHelper.DeleteFile(ThumbnailPNGPath);
                        }
                    }
                }
            }
            else
            {
                ThumbnailScene.GetInstance().GetWorld().SetWorldEnable(false);
                //All job done 
            }
            if (FileHelper.IsFileExists(ThumbnailPNGPath) && ThumbnailTask.Length > 0)
            {
                _TargetQueue.Dequeue();
                ImportThumbnailImage(ThumbnailPNGPath, ThumbnailPath);
                SaveFileThumbnailGenerated(ThumbnailPath);
                ThumbnailScene._PreviewNdaPath = "";
                ResourceManager.Instance().TryReloadResource(ThumbnailPath);
            }
        }
    }
}
