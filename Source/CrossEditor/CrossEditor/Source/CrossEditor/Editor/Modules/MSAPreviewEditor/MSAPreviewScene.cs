using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    using AssetOperation = MSAPreviewContext.AssetOperation;

    class MSAPreviewScene : SceneBase
    {
        static MSAPreviewScene _Instance = new MSAPreviewScene();

        public string DefaultMaterialPath = CrossEngineApi.GetSettingManager().GetRenderPipelineSettingForEditor().DefaultMaterial;

        Entity _PreviewEntity = null;
        ModelComponent _PreviewEntity_ModelComp = null;
        Skeleton _PreviewEntity_SkeletonComp = null;
        Animator _PreviewEntity_AnimatorComp = null;
        MsaPreviewer _PreviewEntity_Previewer = null;

        bool _bNeedFetechSkPose = false;

        public static MSAPreviewScene GetInstance()
        {
            return _Instance;
        }

        MSAPreviewScene()
        {
            MSAPreviewContext.GetInstance().MsaAssetChangeCallBack += OnMsaAssetChanged;
            _SceneTemplateFilename = "PipelineResource/FFSRP/DefaultWorld/MeshPreview.world";
            _WorldType = (uint)WorldTypeTag.PreviewWorld;
        }

        public Tuple<RayPickResult, RayPickSkeletonPhysicsForMSASceneResult> RayPick(bool bHitGizmos, ref Vector3d RayStart, ref Vector3d RayEnd, ref Vector3d RayBaseTilePosition)
        {
            long Time1 = SystemHelper.GetTimeMs();
            RayPickResult RayPickResult = new RayPickResult();

            Vector3d Direction = RayEnd - RayStart;
            Direction.Normalize();

            if (bHitGizmos)
            {
                Vector3d HitPoint = new Vector3d();
                Clicross.GizmoPickResult hitres = new Clicross.GizmoPickResult();

                hitres = Clicross.GizmoManager.Gizmo_RayPick(_World._WorldInterface, new Clicross.Float3((float)RayStart.X, (float)RayStart.Y, (float)RayStart.Z), new Clicross.Float3((float)Direction.X, (float)Direction.Y, (float)Direction.Z), new Clicross.Float3((float)RayBaseTilePosition.X, (float)RayBaseTilePosition.Y, (float)RayBaseTilePosition.Z));
                if (hitres.geometryTag != 0)
                {
                    RayPickResult.HitPoint = HitPoint;
                    return new Tuple<RayPickResult, RayPickSkeletonPhysicsForMSASceneResult>(RayPickResult, null);
                }
            }

            RayPickSkeletonPhysicsForMSASceneResult result = CrossEngineApi.RayPickSkeletonPhysicsForMSAScene(_World.GetNativePointer(), _PreviewEntity.EntityID, RayStart, Direction.ToFloat3());
            RayPickResult.HitTag = GeometryTag.Default;
            RayPickResult.HitPoint = new Vector3d(result.hitPos.x, result.hitPos.y, result.hitPos.z);

            return new Tuple<RayPickResult, RayPickSkeletonPhysicsForMSASceneResult>(RayPickResult, result);
        }

        public override void Initialize(CrossEngine CrossEngine)
        {
            base.Initialize(CrossEngine);
        }

        protected override string GetSceneName()
        {
            return string.Format(@"{0}MSAPreviewWorld", Guid.NewGuid());
        }

        protected override string GetCameraEntityName()
        {
            return "EditorCamera";
        }

        protected override void InitWorldRoot()
        {
            base.InitWorldRoot();
            _World.Root.SetName("MSAPreviewRootEntity");

            //Entity SkySphere = GetRoot().SearchChildByName("SkySphere");
            //if (SkySphere != null)
            //{
            //    var settingMgr = CrossEngineApi.GetSettingManager();
            //    var setting = settingMgr.GetRenderPipelineSettingForEditor();
            //    if (setting.UseReverseZ)
            //    {
            //        var Models = SkySphere.GetModelComponent().Models;
            //        Models[0].SubModelProperties[0].MaterialPath = "EngineResource/Material/GeneralSkySphereMat_Reverse.nda";
            //        SkySphere.GetModelComponent().Models = Models;
            //    }
            //}
        }

        protected override void CustomInit()
        {
            if (_PreviewEntity == null)
            {
                MakePreviewEntityReady();
            }
        }

        public Entity GetPreviewEntity() { return _PreviewEntity; }

        public void SetPhysicsDebugViewOption(PhysicsSceneDebugViewOption Option)
        {
            CrossEngineApi.SetWorldPhysicsDebugView(_World.GetNativePointer(), Option);
        }

        protected override void UpdateImp()
        {
            base.UpdateImp();
            // will be called in EditorUpdateSystemG which will build task before AnimatorSystemG and SkeletonSystemG
            ProcessPreviewSceneUpdate();
        }

        public void OnSkeletonPhysics()
        {
            MSAPreviewContext PreviewContext = MSAPreviewContext.GetInstance();
            CrossEngineApi.SetSkeletonPhysicsResourceDataInEntity(_World.GetNativePointer(), _PreviewEntity.EntityID, PreviewContext.SkeltPhysics.Data);
        }

        private void OnMsaAssetChanged(MsaPreviewSingleAsset Asset, AssetOperation Operation, params object[] Args)
        {
            MSAPreviewContext PreviewContext = MSAPreviewContext.GetInstance();

            switch (Operation)
            {
                case AssetOperation.AddSkeletalMesh:
                    {
                        if (_PreviewEntity.Enable == false)
                        {
                            _PreviewEntity.Enable = true;
                        }

                        SetSkeltModelListOfPreviewEntity();
                    }
                    break;
                case AssetOperation.RemoveSkeletalMesh:
                    {
                        SetSkeltModelListOfPreviewEntity();

                        // hide _PreviewEntity when skeletal mesh count is zero
                        if (PreviewContext.SkeletalMeshes.Count == 0)
                        {
                            _PreviewEntity.Enable = false;
                        }
                    }
                    break;
                case AssetOperation.EditSkeletalMesh:
                    {
                        SetSkeltModelListOfPreviewEntity();
                    }
                    break;

                case AssetOperation.RemoveSkeleton:
                    {
                        Clicross.AnimationEditorUtil.Skeleton_ResetRuntimeSkeleton(_World._WorldInterface, _PreviewEntity.EntityID);
                    }
                    break;

                case AssetOperation.ReplaceSkeltPhysics:
                case AssetOperation.RemoveSkeltPhysics:
                case AssetOperation.EditSkeltPhysics:
                    {
                        RefreshSkeletonPhysics();
                    }
                    break;

                case AssetOperation.RemoveAnimationAsset:
                    {
                        // Reset skeleton pose to ref pose when animation number is zero
                        if (PreviewContext.AnimationNum() == 0)
                        {
                            CrossEngineApi.ResetSkPoseToRefPose(_World._World, _PreviewEntity.EntityID);
                            _bNeedFetechSkPose = true;
                        }
                    }
                    break;
                default:
                    break;
            }
        }

        private void RefreshSkeletonPhysics()
        {
            MSAPreviewContext PreviewContext = MSAPreviewContext.GetInstance();
            if (PreviewContext.SkeltPhysics != null)
            {
                _PreviewEntity_SkeletonComp.SkeletonPhysicsPath = PreviewContext.SkeltPhysics.AssetPath;
            }
            else
            {
                Clicross.AnimationEditorUtil.Skeleton_ResetSkeletonPhysics(_World._WorldInterface, _PreviewEntity.EntityID);
            }
        }

        private void MakePreviewEntityReady()
        {
            if (_PreviewEntity == null)
            {
                _PreviewEntity = _World.CreateEntity();
                _PreviewEntity.CreateComponent<Transform>();
            }

            if (_PreviewEntity_ModelComp == null)
                _PreviewEntity_ModelComp = _PreviewEntity.CreateComponent<ModelComponent>();

            if (_PreviewEntity_SkeletonComp == null)
                _PreviewEntity_SkeletonComp = _PreviewEntity.CreateComponent<Skeleton>();

            if (_PreviewEntity_AnimatorComp == null)
                _PreviewEntity_AnimatorComp = _PreviewEntity.CreateComponent<Animator>();

            if (_PreviewEntity_Previewer == null)
                _PreviewEntity_Previewer = _PreviewEntity.CreateComponent<MsaPreviewer>();

            _PreviewEntity.SetName("MSAPreviewEntity");
            Clicross.GameWorldInterface.World_JointToRoot(_World._WorldInterface, _PreviewEntity.EntityID);
            RenderPropertySystemG.SetCullingProperty(_PreviewEntity.World.GetNativePointer(), _PreviewEntity.EntityID, CullingProperty.CULLING_PROPERTY_ALWAYS_VISIBLE);
        }

        private void DestroyPreviewEntity()
        {
            _PreviewEntity.RuntimeRemove();

            _PreviewEntity_ModelComp = null;
            _PreviewEntity_SkeletonComp = null;
            _PreviewEntity_AnimatorComp = null;
            _PreviewEntity_Previewer = null;
        }

        private void SetSkeltModelListOfPreviewEntity()
        {
            MSAPreviewContext PreviewContext = MSAPreviewContext.GetInstance();

            // set model component
            List<Model> ModelList = new List<Model>();
            foreach (var SkeltMesh in PreviewContext.SkeletalMeshes)
            {
                ModelList.Add(SkeltMesh.GetModel());
            }

            if (ModelList.Count == 0)
            {
                ModelList.Add(new Model(Model.StaticMeshDefault()));
            }

            _PreviewEntity_ModelComp.Models = ModelList;
        }

        private void ProcessPreviewSceneUpdate()
        {
            MSAPreviewContext PreviewContext = MSAPreviewContext.GetInstance();

            // if is playing animation on a runtime skeleton, then need to FetchSkeletonPoseFromRuntime
            if (PreviewContext.SkeletonAdded() && (PreviewContext.AnimationNum() > 0 || _bNeedFetechSkPose))
            {
                PreviewContext.RunSkelt.FetchSkeletonPoseFromRuntime(_World._World, _PreviewEntity.EntityID);
                _bNeedFetechSkPose = false;
            }

            // show skeleton framework with/without animation
            if (MSAPreviewUI.GetInstance().GetMSAEditorSettings().bShowRunSkFrameWork && PreviewContext.SkeletonAdded())
            {
                PreviewSkeletonFrameWork(PreviewContext.RunSkelt);
            }

            // draw skeleton physics
            if (PreviewContext.SkeltPhysics != null)
            {
                PreviewSkeletonPhysics();
            }

            // draw static meshes bound to skeleton socket
            if (PreviewContext.StaticMeshNum() > 0)
            {
                PreviewSocketStaticMesh(PreviewContext.StaticMeshes);
            }
        }

        private void PreviewSkeletonPhysics()
        {
            MSAPreviewContext PreviewContext = MSAPreviewContext.GetInstance();
            int BoneIndex = -1;
            TreeItem SelectedItem = MSAPreviewUI.GetInstance().GetSelectedItem();
            if (SelectedItem != null && SelectedItem.GetTagObject() as SkeletonPhysicsShowItem != null)
            {
                PreviewBone SelectPhyBone = PreviewContext.RunSkelt.GetRefSkeleton().GetBoneList().Find(Bone => Bone.BoneName == SelectedItem.GetText());
                if (SelectPhyBone != null && SelectPhyBone.GetBoneType() == MsaHierItemType.RunSkeltBone)
                {
                    BoneIndex = SelectPhyBone.BoneIndex();
                }
            }
            PreviewContext.SkeltPhysics.DrawSkeletonPhysics(_World._World, PreviewContext.RunSkelt.GetRefSkeleton(), BoneIndex);
        }

        private void PreviewSkeletonFrameWork(PreviewSkeletonAsset SkeletonAsset)
        {
            int BoneIndex = -1;
            TreeItem SelectedItem = MSAPreviewUI.GetInstance().GetSelectedItem();
            if (SelectedItem != null && SelectedItem.GetTagObject() as PreviewBone != null)
            {
                PreviewBone Bone = SelectedItem.GetTagObject() as PreviewBone;
                if (Bone.GetBoneType() == MsaHierItemType.RunSkeltBone)
                {
                    BoneIndex = Bone.BoneIndex();
                }
            }
            else if (SelectedItem != null && SelectedItem.GetTagObject() as SkeletonPhysicsShowItem != null)
            {
                PreviewBone SelectPhyBone = SkeletonAsset.GetRefSkeleton().GetBoneList().Find(Bone => Bone.BoneName == SelectedItem.GetText());
                if (SelectPhyBone != null && SelectPhyBone.GetBoneType() == MsaHierItemType.RunSkeltBone)
                {
                    BoneIndex = SelectPhyBone.BoneIndex();
                }
            }

            // draw skeleton framework
            SkeletonAsset.GetRefSkeleton().DrawSkeletonFrameWork(_World._World, BoneIndex);
        }

        private void PreviewSocketStaticMesh(ICollection<PreviewStaticMeshAsset> StaticMeshAssets)
        { }
    }
}