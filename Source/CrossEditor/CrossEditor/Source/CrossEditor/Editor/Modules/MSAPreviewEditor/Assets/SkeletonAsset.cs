using CEngine;
using System.Collections.Generic;
using System.Diagnostics;

using IGameWorld = System.IntPtr;

namespace CrossEditor
{
    public class CompatibleFailInfo
    {
        public PreviewSkeletonAsset SkeletonAsset = null;

        public PreviewSkeletalMeshAsset SkeltMesh = null;
        public PreviewAnimSeqAsset AnimSeq = null;

        public BoneData RunSkMissedBone;
        public BoneData ResRefSkMissedBone;
    }

    // Actually used PreviewBone class in editor
    public class PreviewBone
    {
        public static string RetargetingModeStr = "RetargetingMode";
        public static string MirrorBoneNameStr = "MirrorBoneName";

        private BoneData _Data = new BoneData();

        public int ChildrenCount() { return _Data.ChildrenIndices.Count; }
        public MsaHierItemType GetBoneType() { return _Data.BoneType; }
        public int BoneIndex() { return _Data.BoneIndex; }
        public int ParentIndex() { return _Data.ParentIndex; }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Bone Name", bReadOnly = true)]
        public string BoneName { get { return _Data.Name.GetCString(); } set { } }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Bone Translate", bReadOnly = true)]
        public Float3 Translate { get { return _Data.RootSpaceTransform.GetTranslation(); } set { } }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Bone Rotation(Euler angle in radian)", bReadOnly = true)]
        public Quaternion Rotation { get { return _Data.RootSpaceTransform.GetRotation(); } set { } }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Bone Scale", bReadOnly = true)]
        public Float3 Scale { get { return _Data.RootSpaceTransform.GetScale(); } set { } }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Bone Retargeting Mode")]
        public BoneTranslateRetargetingMode RetargetingMode
        {
            get => (BoneTranslateRetargetingMode)Clicross.AnimationEditorUtil.SkeletonRes_GetBoneRetargetingMode(_OwnerRefSK.GetOwnerAsset().ResourcePtr as Clicross.skeleton.SkeletonResource, BoneIndex());
            set => Clicross.AnimationEditorUtil.RunSkelt_SetBoneRetargetingMode(
                        MSAPreviewScene.GetInstance().GetWorld()._WorldInterface,
                        MSAPreviewScene.GetInstance().GetPreviewEntity().EntityID,
                        _Data.BoneIndex,
                        (int)value);
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Mirror Bone Name")]
        public string MirrorBoneName
        {
            get => Clicross.AnimationEditorUtil.SkeletonRes_GetMirrorBoneName(_OwnerRefSK.GetOwnerAsset().ResourcePtr as Clicross.skeleton.SkeletonResource, BoneIndex()).GetCString();
            set => Clicross.AnimationEditorUtil.RunSkelt_SetBoneMirrorBoneName(
                        MSAPreviewScene.GetInstance().GetWorld()._WorldInterface,
                        MSAPreviewScene.GetInstance().GetPreviewEntity().EntityID,
                        _Data.BoneIndex,
                        new Clicross.UniqueString(value));
        }

        public PreviewBone(PreviewRefSkeleton RefSkeleton)
        {
            _OwnerRefSK = RefSkeleton;
        }

        public PreviewBone GetChildBone(int ChildIndex)
        {
            Debug.Assert(ChildIndex >= 0 && ChildIndex < ChildrenCount());

            int IndexInList = _Data.ChildrenIndices[ChildIndex];
            return _OwnerRefSK.GetBoneList()[IndexInList];
        }

        public PreviewBone GetParentBone()
        {
            if (ParentIndex() == -1)
                return null;

            return _OwnerRefSK.GetBoneList()[ParentIndex()];
        }

        public void DrawSingleBoneFrameWork(IGameWorld World, bool bSelected)
        {
            // if cur bone has parent, draw lines
            if (_Data.BoneIndex > 0)
            {
                CrossEngineApi.DrawBoneWithParent(World, _Data, GetParentBone()._Data, bSelected);
            }
            else
            {
                CrossEngineApi.DrawBoneWithoutParent(World, _Data, bSelected);
            }
        }

        // get local space & root space bone transform from skeleton pose
        public bool GetBoneTransformFromSkeletonPose(IGameWorld World, ulong Entity)
        {
            if (!CrossEngineApi.GetBoneTransformFromSkeletonComp(World, Entity, BoneIndex(), _Data))
            {
                return false;
            }

            return true;
        }

        public BoneData GetBoneData() { return _Data; }

        public PreviewRefSkeleton GetOwnerRefSkeleton() { return _OwnerRefSK; }

        protected PreviewRefSkeleton _OwnerRefSK = null;
    }

    public class PreviewRefSkeleton
    {
        public PreviewBone Root { get { return _Bones[0]; } }

        public PreviewRefSkeleton(MsaPreviewSingleAsset InAsset) { _OwnerAsset = InAsset; }

        public bool Load(string InPath)
        {
            int SkeltBoneNum = CrossEngineApi.GetRefSkBoneNumFromAsset(InPath);
            if (SkeltBoneNum <= 0)
                return false;

            _Bones.Clear();
            for (int i = 0; i < SkeltBoneNum; ++i)
            {
                PreviewBone CurBone = new PreviewBone(this);
                CrossEngineApi.GetRefSkBoneDataFromAsset(InPath, i, CurBone.GetBoneData());

                _Bones.Add(CurBone);
            }

            return true;
        }

        public int GetBoneNum() { return _Bones.Count; }

        public List<PreviewBone> GetBoneList() { return _Bones; }

        public MsaPreviewSingleAsset GetOwnerAsset() { return _OwnerAsset; }

        public void DrawSkeletonFrameWork(IGameWorld World, int SelectedBoneIndex)
        {
            foreach (PreviewBone Bone in _Bones)
            {
                bool bSelected = (Bone.BoneIndex() == SelectedBoneIndex);

                Bone.DrawSingleBoneFrameWork(World, bSelected);
            }
        }

        protected MsaPreviewSingleAsset _OwnerAsset = null;

        protected List<PreviewBone> _Bones = new List<PreviewBone>();
    }

    public class PreviewSkeletonAsset : MsaPreviewSingleAsset
    {
        private PreviewRefSkeleton _RefSkeleton;

        public PreviewSkeletonAsset()
        {
            _RefSkeleton = new PreviewRefSkeleton(this);
        }

        [PropertyInfo(PropertyType = "StringAsResource", ToolTips = "Skeleton Asset Path",
            FileTypeDescriptor = "Skeleton Assets#nda", ObjectClassID1 = ClassIDType.CLASS_SkeletonResource, bReadOnly = true)]
        public string AssetPath { get { return _RelativePath; } set {; } }

        public override MsaHierItemType GetMsaItemType() { return MsaHierItemType.SkeletonAsset; }

        public PreviewRefSkeleton GetRefSkeleton() { return _RefSkeleton; }

        public override bool Load(string InPath)
        {
            if (!base.Load(InPath))
                return false;

            if (!_RefSkeleton.Load(InPath))
                return false;

            return true;
        }

        public virtual bool Unload()
        {
            return true;
        }

        public bool FetchSkeletonPoseFromRuntime(IGameWorld World, ulong Entity)
        {
            List<PreviewBone> BoneList = _RefSkeleton.GetBoneList();

            foreach (PreviewBone Bone in BoneList)
            {
                if (!Bone.GetBoneTransformFromSkeletonPose(World, Entity))
                {
                    return false;
                }
            }

            return true;
        }

        public MsaCompatibleType IsCompatible(PreviewSkeletalMeshAsset SkeltMeshAsset, ref CompatibleFailInfo CompatibleFailedInfo)
        {
            CompatibleFailedInfo.SkeletonAsset = this;
            CompatibleFailedInfo.SkeltMesh = SkeltMeshAsset;
            CompatibleFailedInfo.RunSkMissedBone = new BoneData();
            CompatibleFailedInfo.ResRefSkMissedBone = new BoneData();

            return CrossEngineApi.IsSkeletonCompatible(_RelativePath, SkeltMeshAsset.GetRelativePath(),
                CompatibleFailedInfo.RunSkMissedBone, CompatibleFailedInfo.ResRefSkMissedBone);
        }

        public MsaCompatibleType IsCompatible(PreviewAnimAsset AnimAsset, ref CompatibleFailInfo CompatibleFailedInfo)
        {
            CompatibleFailedInfo.SkeletonAsset = this;
            CompatibleFailedInfo.RunSkMissedBone = new BoneData();
            CompatibleFailedInfo.ResRefSkMissedBone = new BoneData();

            return AnimAsset.IsCompatibleWithSkeleton(this, ref CompatibleFailedInfo);
        }
    }
}
