using CEngine;
using EditorUI;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;

namespace CrossEditor
{
    public enum PreviewState : uint
    {
        None,
        PreviewCamera,
        PreviewMaximized
    }
    public class EditorCameraInfo
    {
        public Double3 translation;
        public Double3 rotation;
        public CameraProjectionMode projectionMode;
        public float sensorWidth;
        public float sensorHeight;
        public float PerspectiveFov;
        public float PerspectiveFar;
        public float PerspectiveNear;
        public float FocalLength;
        public float MinFocalLength;
        public float MaxFocalLength;

    }
    [JsonObject(MemberSerialization.OptIn)]
    public class EditorScene : SceneBase
    {
        static EditorScene _Instance = new EditorScene();
        string _CurrentSceneFilename;
        bool _IsNewScene = false;

        bool _bDrawIcons;
        bool _bScreenShotMode;
        bool _bLoadingWorld;
        bool _bDrawMeshBVH;
        bool _bDrawInstanceAABBTree;
        bool _bDrawTerrainAABBTree;
        EditorCameraInfo _CachedCameraInfo = new EditorCameraInfo();
        PreviewState _PreviewState = PreviewState.None;
        int _ImageCamerePreview;
        int _CameraPreviewX;
        int _CameraPreviewY;
        int _PreviewWidth;
        int _PreviewHeight;

        int _LightmapBakeStatus;

        bool _bShowPhysicsRaycastInfo;

        public static EditorScene GetInstance()
        {
            return _Instance;
        }
        public PreviewState GetPreviewState()
        {
            return _PreviewState;
        }
        EditorScene()
        {
            _SceneTemplateFilename = "DefaultWorld/Default.world";
            _CurrentSceneFilename = "";

            _bDrawIcons = true;
            _bScreenShotMode = false;
            _bLoadingWorld = false;
            _bDrawMeshBVH = false;
            _bDrawInstanceAABBTree = false;

            _ImageCamerePreview = 0;
            _CameraPreviewX = 0;
            _CameraPreviewY = 0;
            _PreviewHeight = 128;
            _PreviewWidth = 128;

            _LightmapBakeStatus = 0;
            _bShowPhysicsRaycastInfo = false;
        }

        public override void Initialize(CrossEngine CrossEngine)
        {
            base.Initialize(CrossEngine);

            _World = _CrossEngine.CreateWorld("Temp");
            _World.Initialize();
        }

        #region Init&Load Scene

        protected override void InitWorld()
        {
            if (_World != null)
            {
                if (_World.Root != null)
                {
                    _World.Root.ResetWorld();
                }
                _CrossEngine.DestroyWorld(_World);
            }
            _World = _CrossEngine.CreateWorld(GetSceneName());
            _World.Initialize();

            _ImageScene = EditorUICanvas.GetInstance().CreateSceneImage(_World, _Width, _Height);
        }

        protected override void InitCameraEntity()
        {
            base.InitCameraEntity();
            _World.Root.RefreshTree();
        }

        public override void NewScene()
        {
            string PipelineDirectory = EditorUtilities.GetPipelineResourceDirectory();
            var absolutescenename = string.Format("{0}/{1}", PipelineDirectory, _SceneTemplateFilename);
            _IsNewScene = true;
            LoadScene(absolutescenename);
            _CurrentSceneFilename = "";
        }

        protected async override System.Threading.Tasks.Task<bool> LoadSceneImp(string SceneFilename)
        {
            _bLoadingWorld = true;
            GameScene.GetInstance().StopPIEPlay();
            long BeginTime = SystemHelper.GetTimeMs();

            // when create from newScene, do not save scene name
            if (!_IsNewScene)
            {
                _CurrentSceneFilename = SceneFilename;
            }
            else
            {
                _IsNewScene = false;
            }
            _CameraEntity = null;
            if (_PreviewCameraEntity != null)
                _ImageCamerePreview = 0;
            _PreviewCameraEntity = null;
            _PreviewState = PreviewState.None;
            _EditorEntities.Clear();
            // an implicit bug may happen
            // for example, the previous world is loading, so as the progress ui
            // after destroy such world, because no build hierarchy callback, the progress ui is always visible
            // it occurs when openLastScene is set true and  newScene() is called before openLastScene
            // it seems need a lot of code refractor to really solve this problem, for editor and runtime.
            // So for now, we rewrite function NewScene, so it can take last scene path as parameter  to hide this problem
            InitWorld();

            ProgressUI LoadingUI = new ProgressUI();
            Progress _Progress = new Progress("Loading Progress " + EditorUtilities.EditorFilenameToStandardFilename(SceneFilename), 2);
            LoadingUI.Initialize(GetUIManager(), _Progress, false);
            DialogUIManager.GetInstance().ShowDialogUI(LoadingUI);

            Background.SuspendBackgroundThreads();
            Clicross.GameWorldInterface.World_SetAlwaysEnable(_World._WorldInterface, true);
            await AsyncLoadWorldTask(SceneFilename, _World, _Progress);
            Background.ResumeBackgroundThreads();

            InitWorldRoot();
            InitCameraEntity();

            _Progress.Done();
            _Progress.Close();

            _bDrawIcons = false;
            SetDirty(false);
            ClearSelection();
            SetDrawIcons(!_bScreenShotMode);
            EditOperationManager.GetInstance().ClearAll();
            HierarchyUI HierarchyUI = HierarchyUI.GetInstance();
            HierarchyUI.ExitSearchMode();
            HierarchyUI.UpdateHierarchy();

            WorldPartitionUI.GetInstance().Refresh();
            //PCGManager.GetInstance().Load();
            TerrainEditorUI.GetInstance().Update(1);
            TerrainEditorUI.GetInstance().UpdateInspector();
            TerrainEditor.GetInstance().ResetTerrainDirty();

            long EndTime = SystemHelper.GetTimeMs();
            long DeltaTime = EndTime - BeginTime;
            Console.WriteLine("LoadScene(): {0}ms", DeltaTime);
            Clicross.GameWorldInterface.World_SetAlwaysEnable(_World._WorldInterface, true);
            EditorSceneUI.GetInstance().LoadSceneCameraMode();
            _bLoadingWorld = false;


            SetPhysicsDebugViewOption(EditorSceneUI.GetInstance().PhysicsDebugViewOption);

            return true;
        }

        public bool IsLoadingWorld()
        {
            return _bLoadingWorld;
        }

        public async System.Threading.Tasks.Task<bool> AsyncLoadWorldTask(string Filename, World World, Progress Progress)
        {
            Clicross.GameWorldInterface.World_LoadScene(World._WorldInterface, Filename);
            uint blockCount = CrossEngineApi.GetWorldLoadingBlockCount(World.GetNativePointer());

            int step = 0;
            Progress.SetStep(step, "Loading Preload Block...", 1);
            System.Threading.Tasks.Task<bool> PreBlockTask = new System.Threading.Tasks.Task<bool>(() =>
            {
                return true;
            });

            Queue<System.Threading.Tasks.Task<bool>> BlockTasks = new Queue<System.Threading.Tasks.Task<bool>>();
            CallBackUtil.SetBuildHierarchyCallback(_BuildHierarchyCallback = () =>
            {
                if (step == 0)
                {
                    step++;
                    uint blockCount = CrossEngineApi.GetWorldLoadingBlockCount(World.GetNativePointer());
                    Progress.SetStep(step, "Loading All Blocks...", (int)blockCount);
                    for (uint i = 0; i < blockCount; i++)
                    {
                        BlockTasks.Enqueue(new System.Threading.Tasks.Task<bool>(() =>
                        {
                            Progress.SetItem((int)i + 1, "Done.");
                            return true;
                        }));
                    }
                    PreBlockTask.RunSynchronously();
                }
                else
                {
                    if (BlockTasks.Count > 0)
                    {
                        BlockTasks.Dequeue().RunSynchronously();
                    }
                    else
                    {
                        GetWorld().Root.RefreshTree();
                        HierarchyUI.GetInstance().UpdateHierarchy();
                    }
                }
            }
            , World._World);
            CallBackUtil.SetEditorUpdateHierarchyCallback(_EditorHierarchyCallback = () =>
                {
                    GetWorld().Root.RefreshTree();
                    HierarchyUI.GetInstance().UpdateHierarchy();
                }, World._World);
            CallBackUtil.SetBuildEntityHierarchyCallback(_EditorEnitityHierarchyCallback = (ulong entityId, bool recurive) =>
            {
                Entity entity = GetRoot().SearchChildByEntityID(entityId);
                if (entity == null)
                    return;
                if (!recurive)
                {
                    entity.RefreshEntity(false);
                }
                else
                {
                    entity.RefreshTree();
                }
                HierarchyUI HierarchyUI = HierarchyUI.GetInstance();
                if (HierarchyUI.GetScene().GetWorld() == _World)
                {
                    HierarchyUI.UpdateHierarchy();
                }
            }, _World.GetNativePointer());
            var Result = await System.Threading.Tasks.Task.WhenAll(PreBlockTask);
            Result = await System.Threading.Tasks.Task.WhenAll(BlockTasks);
            return true;
        }

        protected override void SaveSceneImp(string SceneFilename)
        {
            if (SceneFilename == "")
            {
                SceneFilename = _CurrentSceneFilename;
            }
            else
            {
                _CurrentSceneFilename = SceneFilename;
            }


            //PCGManager.GetInstance().Save();

            //PaintToolEditor.GetInstance().SaveConfig();

            //HierarchyUI.GetInstance().CombineSceneFoliages();

            SaveScene(_CurrentSceneFilename, _World);

            TerrainEditor.GetInstance().SaveTerrain();

            EditorSceneUI.GetInstance().SaveSceneCameraMode();

            SetDirty(false);
        }

        public void SaveScene(string Path, World World)
        {
            string editData = JsonConvert.SerializeObject(this);
            if (World.Enable)
            {
                Clicross.GameWorldInterface.Editor_SaveData(World._WorldInterface, editData);
                Clicross.GameWorldInterface.World_SaveScene(World._WorldInterface, Path);
            }
        }

        #endregion

        #region Update

        public override void UpdateManipuilatorGizmo()
        {
            if (_bScreenShotMode)
            {
                return;
            }

            base.UpdateManipuilatorGizmo();
        }

        protected override void UpdateLevelGrid()
        {
            if (_bScreenShotMode)
            {
                return;
            }

            base.UpdateLevelGrid();
        }

        protected override void UpdateMeshBVH()
        {
            if (_bDrawMeshBVH)
            {
                Clicross.EditorPrimitiveSystemG.World_DrawMeshAABBTree(_World._WorldInterface, _bDrawMeshBVH);
            }
        }

        protected override void UpdateInstanceBVH()
        {
            if (_bDrawInstanceAABBTree)
            {
                Clicross.EditorPrimitiveSystemG.World_DrawInstanceAABBTree(_World._WorldInterface, _bDrawInstanceAABBTree);
            }
        }

        protected override void UpdateTerrainBVH()
        {
            if (_bDrawTerrainAABBTree)
            {
                Clicross.EditorPrimitiveSystemG.World_DrawTerrainAABBTree(_World._WorldInterface, _bDrawTerrainAABBTree);
            }
        }

        protected override void UpdateImp()
        {
            if (_bLoadingWorld)
            {
                return;
            }

            base.UpdateImp();
            if (_bVisible && _World.Enable)
            {
                HandleCameraPreview();
                CheckBakeLightmapResult();
                UpdateWorldPartitionBlockShowing();
            }
            if (_bShowPhysicsRaycastInfo)
            {
                ShowPhysicsRaycastInfo();
            }
        }

        public override void SetSequencePlayState(bool state)
        {
            base.SetSequencePlayState(state);
        }
        public void SyncEditorCamera(Camera cam, Transform transform)
        {
            Camera EditorCamera = GetCameraEntityCamera();
            Transform EditorCameraTrans = GetCameraEntityTransform();
            EditorCameraTrans.Translation = (transform.GetWorldTranslation());
            EditorCameraTrans.Rotation = transform.GetWorldRotation();
            EditorCamera.Mode = cam.Mode;
            EditorCamera.PerspectiveFov = cam.PerspectiveFov;
            EditorCamera.PerspectiveFar = cam.PerspectiveFar;
            EditorCamera.PerspectiveNear = cam.PerspectiveNear;
            EditorCamera.FocalLength = cam.FocalLength;
            EditorCamera.MinFocalLength = cam.MinFocalLength;
            EditorCamera.MaxFocalLength = cam.MaxFocalLength;
            EditorCamera.SensorWidth = cam.SensorWidth;
            EditorCamera.SensorHeight = cam.SensorHeight;
        }

        public void MaximizePreviewCamera()
        {
            if(_PreviewCameraEntity_Camera.Enable == true && _PreviewState == PreviewState.PreviewCamera)
            {
                Camera EditorCamera = GetCameraEntityCamera();

                Transform EditorCameraTrans = GetCameraEntityTransform();
                _CachedCameraInfo.translation = EditorCameraTrans.Translation;
                _CachedCameraInfo.rotation = EditorCameraTrans.Rotation;
                _CachedCameraInfo.projectionMode = EditorCamera.Mode;
                _CachedCameraInfo.PerspectiveFov = EditorCamera.PerspectiveFov;
                _CachedCameraInfo.MinFocalLength = EditorCamera.MinFocalLength;
                _CachedCameraInfo.MaxFocalLength = EditorCamera.MaxFocalLength;
                _CachedCameraInfo.sensorHeight = EditorCamera.SensorHeight;
                _CachedCameraInfo.sensorWidth = EditorCamera.SensorWidth;
                _CachedCameraInfo.PerspectiveFar = EditorCamera.PerspectiveFar;
                _CachedCameraInfo.PerspectiveNear = EditorCamera.PerspectiveNear;
                EditorCameraTrans.Translation = (_PreviewCameraEntity_Transform.GetWorldTranslation());
                EditorCameraTrans.Rotation = _PreviewCameraEntity_Transform.GetWorldRotation();
                EditorCamera.Mode = _PreviewCameraEntity_Camera.Mode;
                EditorCamera.PerspectiveFov = _PreviewCameraEntity_Camera.PerspectiveFov;
                EditorCamera.PerspectiveFar = _PreviewCameraEntity_Camera.PerspectiveFar;
                EditorCamera.PerspectiveNear = _PreviewCameraEntity_Camera.PerspectiveNear;
                EditorCamera.FocalLength = _PreviewCameraEntity_Camera.FocalLength;
                EditorCamera.MinFocalLength = _PreviewCameraEntity_Camera.MinFocalLength;
                EditorCamera.MaxFocalLength = _PreviewCameraEntity_Camera.MaxFocalLength;
                EditorCamera.SensorWidth = _PreviewCameraEntity_Camera.SensorWidth;
                EditorCamera.SensorHeight = _PreviewCameraEntity_Camera.SensorHeight;
                _PreviewCameraEntity_Camera.Enable = false;
                _PreviewState = PreviewState.PreviewMaximized;
            }
            else if( _PreviewState == PreviewState.PreviewMaximized)
            {
                Camera EditorCamera = GetCameraEntityCamera();
                Transform EditorCameraTrans = GetCameraEntityTransform();
                EditorCameraTrans.Translation = (_CachedCameraInfo.translation);
                EditorCameraTrans.Rotation = _CachedCameraInfo.rotation;
                EditorCamera.Mode = _CachedCameraInfo.projectionMode;
                EditorCamera.PerspectiveFov = _CachedCameraInfo.PerspectiveFov;
                EditorCamera.PerspectiveFar = _CachedCameraInfo.PerspectiveFar;
                EditorCamera.PerspectiveNear = _CachedCameraInfo.PerspectiveNear;
                EditorCamera.FocalLength = _CachedCameraInfo.FocalLength;
                EditorCamera.MinFocalLength = _CachedCameraInfo.MinFocalLength;
                EditorCamera.MaxFocalLength = _CachedCameraInfo.MaxFocalLength;
                EditorCamera.SensorWidth = _CachedCameraInfo.sensorWidth;
                EditorCamera.SensorHeight = _CachedCameraInfo.sensorHeight;
                _PreviewState = PreviewState.None;
            }
        }

        void HandleCameraPreview()
        {
            if (_PreviewCameraEntity_Camera.TargetHeight != (uint)EditorSceneUI.GetInstance().GetPanelHeight() || _PreviewCameraEntity_Camera.TargetWidth != (uint)EditorSceneUI.GetInstance().GetPanelWidth())
            {
                _PreviewCameraEntity_Camera.TargetHeight = (uint)EditorSceneUI.GetInstance().GetPanelHeight();
                _PreviewCameraEntity_Camera.TargetWidth = (uint)EditorSceneUI.GetInstance().GetPanelWidth();
                var info = new RenderTextureInfo
                {
                    Name = "PreviewCameraRenderTexture",
                    Dimension = TextureDimension.Tex2D,
                    Format = RenderTextureFormat.R8G8B8A8_UNorm,
                    Width = _PreviewCameraEntity_Camera.TargetWidth,
                    Height = _PreviewCameraEntity_Camera.TargetHeight,
                };
                RenderTexture renderTexture = new RenderTexture(info);
                _PreviewCameraEntity_Camera.SetRenderTexture(renderTexture);
            }


            Entity Selected = GetSelection();

            if (Selected != null && Selected.GetName() != GetCameraEntityName() && Selected.HasComponent(typeof(Camera)))
            {
                Camera EditorCamera = GetCameraEntityCamera();
                Transform EditorCameraTrans = GetCameraEntityTransform();
                if (_PreviewState == PreviewState.None || _PreviewState == PreviewState.PreviewCamera)
                {
                    _PreviewCameraEntity_Camera.Enable = true;
                    _PreviewState = PreviewState.PreviewCamera;
                    Transform trans = Selected.GetTransformComponent();

                    Camera selectCamera = (Camera)Selected.GetComponent(typeof(Camera));
                    _PreviewCameraEntity_Camera.Mode = selectCamera.Mode;
                    _PreviewCameraEntity_Camera.PerspectiveFov = selectCamera.PerspectiveFov;
                    _PreviewCameraEntity_Camera.PerspectiveFar = selectCamera.PerspectiveFar;
                    _PreviewCameraEntity_Camera.PerspectiveNear = selectCamera.PerspectiveNear;
                    _PreviewCameraEntity_Camera.FocalLength = selectCamera.FocalLength;
                    _PreviewCameraEntity_Camera.MinFocalLength = selectCamera.MinFocalLength;
                    _PreviewCameraEntity_Camera.MaxFocalLength = selectCamera.MaxFocalLength;
                    _PreviewCameraEntity_Camera.SensorWidth = selectCamera.SensorWidth;
                    _PreviewCameraEntity_Camera.SensorHeight = selectCamera.SensorHeight;
                    _PreviewCameraEntity_Transform.Translation = trans.GetWorldTranslation();
                    _PreviewCameraEntity_Transform.Rotation = trans.GetWorldRotation();
                    if (_ImageCamerePreview == 0)
                    {
                        _ImageCamerePreview = EditorUICanvas.GetInstance().GetUICanvasInterface().CreateSceneCameraImage(_World.GetNativePointer(), _PreviewCameraEntity.GetEntityIdStruct(), EditorSceneUI.GetInstance().GetPanelWidth(), EditorSceneUI.GetInstance().GetPanelHeight());
                    }
                    if (EditorUICanvas.GetInstance().GetUICanvasInterface().GetImageHeight(_ImageCamerePreview) != EditorSceneUI.GetInstance().GetPanelHeight() || EditorUICanvas.GetInstance().GetUICanvasInterface().GetImageWidth(_ImageCamerePreview) != EditorSceneUI.GetInstance().GetPanelWidth())
                    {
                        EditorUICanvas.GetInstance().GetUICanvasInterface().ResizeSceneCameraImage(_World.GetNativePointer(), _PreviewCameraEntity.GetEntityIdStruct(), _ImageCamerePreview, EditorSceneUI.GetInstance().GetPanelWidth(), EditorSceneUI.GetInstance().GetPanelHeight());
                    }
                }
            }
            else
            {
                _PreviewState = PreviewState.None;
                _PreviewCameraEntity_Camera.Enable = false;
            }

            Entity CurPilotEntity = HierarchyUI.GetInstance().GetCurPiolotEntity();
            if (CurPilotEntity != null)
            {
                HierarchyUI.GetInstance().MoveEntityToEditorCamera();
            }
        }

        public void UpdateWorldPartitionBlockShowing()
        {
            Inspector_Entity Inspector_Entity = InspectorUI.GetInstance().GetInspector() as Inspector_Entity;
            if (Inspector_Entity != null)
            {
                Inspector_Component Inspector_Component_WorldPartitionProperty = Inspector_Entity.FindComponentInspector<WorldPartitionProperty>();
                if (Inspector_Component_WorldPartitionProperty != null)
                {
                    Inspector_Property Inspector_Property_BelongedBlock = Inspector_Component_WorldPartitionProperty.FindChildInspector("BelongedBlock");
                    if (Inspector_Property_BelongedBlock != null)
                    {
                        Inspector_Property_BelongedBlock.ReadValue();
                    }
                }
            }
        }

        public void DrawCameraPreviewScene(UIManager UIManager)
        {
            if (_PreviewState == PreviewState.PreviewCamera && _ImageCamerePreview > 0)
            {
                if (GetSelection() == HierarchyUI.GetInstance().GetCurPiolotEntity()) return;
                EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
                Color ColorWhite = new Color(1.0f, 1.0f, 1.0f, 1.0f);
                if (_CrossEngine.GetRendererMode() == CrossRendererMode.OpenGLES30)
                {
                    //cross_editor_todo:
                    EditorUICanvas.DrawImage(_ImageCamerePreview, _CameraPreviewX, _CameraPreviewY, _PreviewWidth, _PreviewHeight, ref ColorWhite, true);
                }
                else
                {
                    //cross_editor_todo:
                    EditorUICanvas.DrawImage(_ImageCamerePreview, _CameraPreviewX, _CameraPreviewY, _PreviewWidth, _PreviewHeight, ref ColorWhite, false);
                }
            }
        }
        #endregion

        #region Get/Set

        public Camera GetSelectedPreviewCamera()
        {
            return _PreviewCameraEntity_Camera;
        }

        public Entity GetSelectedPreviewCameraEntity()
        {
            return _PreviewCameraEntity;
        }

        public bool GetScreenShotMode()
        {
            return _bScreenShotMode;
        }

        public void SetScreenShotMode(bool bScreenShotMode)
        {
            _bScreenShotMode = bScreenShotMode;
            OnSelectionListChanged();
        }

        public bool GetDrawIcons()
        {
            return _bDrawIcons;
        }

        public void SetDrawIcons(bool bDrawIcons)
        {
            _bDrawIcons = bDrawIcons;
            EditorIconSystemG.SetDrawIcons(_World.GetNativePointer(), _bDrawIcons);
        }

        public bool GetDrawMeshBVH()
        {
            return _bDrawMeshBVH;
        }

        public void SetDrawMeshBVH(bool bDrawMeshBVH)
        {
            _bDrawMeshBVH = bDrawMeshBVH;
        }

        public bool GetDrawInstanceAABBTree()
        {
            return _bDrawInstanceAABBTree;
        }

        public void SetDrawInstanceAABBTree(bool bDrawInstanceAABBTree)
        {
            _bDrawInstanceAABBTree = bDrawInstanceAABBTree;
        }

        public bool GetDrawTerrainAABBTree()
        {
            return _bDrawTerrainAABBTree;
        }

        public void SetDrawTerrainAABBTree(bool bDrawTerrainAABBTree)
        {
            _bDrawTerrainAABBTree = bDrawTerrainAABBTree;
        }

        public void SetCameraPreviewPosition(int X, int Y, int Width, int Height)
        {
            _CameraPreviewX = X;
            _CameraPreviewY = Y;
            if (_PreviewWidth != Width || _PreviewHeight != Height)
            {
                _PreviewWidth = Width;
                _PreviewHeight = Height;
                if (_ImageCamerePreview > 0 && _PreviewState == PreviewState.PreviewCamera)
                {
                    UIManager UIManager = GetUIManager();
                    EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
                    EditorUICanvas.GetUICanvasInterface().ResizeSceneCameraImage(_World.GetNativePointer(), _PreviewCameraEntity.GetEntityIdStruct(), _ImageCamerePreview, _PreviewWidth, _PreviewHeight);
                }
            }
        }

        public void SetPhysicsDebugViewOption(PhysicsSceneDebugViewOption DebugOption)
        {
            CrossEngineApi.SetWorldPhysicsDebugView(_World.GetNativePointer(), DebugOption);
        }

        public void SetShowPhysicsRaycastDebugInfo(bool b)
        {
            _bShowPhysicsRaycastInfo = b;
        }

        private void ShowPhysicsRaycastInfo()
        {
            Device Device = GetDevice();
            int MouseX = Device.GetMouseX();
            int MouseY = Device.GetMouseY();
            var panel = EditorSceneUI.GetInstance().GetPanel();
            int Width = panel.GetWidth();
            int Height = panel.GetHeight();
            int X1 = MouseX - panel.GetScreenX();
            int Y1 = MouseY - panel.GetScreenY();
            if (GetWorld() != null)
            {
                string msg;
                var worldPtr = GetWorld().GetNativePointer();
                if (X1 > 0 && X1 < Width && Y1 > 0 && Y1 < Height)
                {
                    Vector3d cameraPos = new Vector3d(TransformSystemG.GetWorldTranslationT(worldPtr, GetCameraEntity().EntityID));
                    Vector3d RayBaseTilePosition = ScreenToWorld(X1, Y1, Width, Height, out var RayStart, out var RayEnd);
                    Vector3d dir = (RayEnd + RayBaseTilePosition * TransformSystemG.GetLengthPerTile() - cameraPos);
                    var hitResult = PhysicsSystemG.RayCastForDebug(worldPtr, cameraPos, dir);
                    msg = $"raycast: hit";
                }
                else
                {
                    msg = "raycast: hit null";
                }
                Clicross.PrimitiveRenderSystemG.EditorLogScreen(GetWorld()._WorldInterface, msg);
            }
        }

        protected override string GetCameraEntityName()
        {
            return "EditorCamera";
        }

        public string GetTemplateSceneFilename()
        {
            return _SceneTemplateFilename;
        }

        public string GetCurrentSceneFilename()
        {
            return _CurrentSceneFilename;
        }

        public bool GetLoadingWorld()
        {
            return _bLoadingWorld;
        }

        #endregion

        public int World_IntersectSphere(ulong entity, Vector3f Center, float radius, Vector3d baseTilePosition)
        {
            int Count = Clicross.GameWorldInterface.World_IntersectSphere(_World._WorldInterface, entity, new Clicross.Float3((float)Center.X, (float)Center.Y, (float)Center.Z), radius, new Clicross.Float3((float)baseTilePosition.X, (float)baseTilePosition.Y, (float)baseTilePosition.Z));
            return Count;
        }

        public string CalculateNewName(Entity ParentEntity, string ReferenceName)
        {
            string StringPart;
            int NumberPart;
            int NumberPartDefault = 0;
            bool bHasNumberPart = EditorUtilities.SplitObjectName(ReferenceName, out StringPart, out NumberPart, NumberPartDefault);
            string Name = "";
            int i = NumberPart;
            if (bHasNumberPart)
            {
                i = Math.Max(i, 1);
            }
            while (true)
            {
                if (i == 0)
                {
                    Name = StringPart;
                }
                else
                {
                    Name = StringPart + i.ToString();
                }
                Entity Entity1 = ParentEntity.FindChildByName(Name);
                if (Entity1 == null)
                {
                    break;
                }
                i++;
            }
            return Name;
        }

        #region Lightmap

        public void BakeLightmap()
        {

        }

        public void LightmapSetComponentBatch(LightmapBakerSettings settings)
        {
            var selectedEntity = InspectorUI.GetInstance().GetObjectInspected();
            if (selectedEntity is List<Entity>)
            {
                foreach (var entity in (List<Entity>)selectedEntity)
                {
                    LightmapSetComponent(entity, settings);
                }
            }
            else if (selectedEntity is Entity)
            {
                LightmapSetComponent((Entity)selectedEntity, settings);
            }
        }

        public void ClearForceBakeLightMap()
        {
            List<Entity> AllEntityList = EnumEntities(GetRoot());
            foreach (Entity entity in AllEntityList)
            {
                LightMapBaker lmComp = (LightMapBaker)entity.GetComponent(typeof(LightMapBaker));
                if (lmComp != null)
                {
                    lmComp.ForceBakeLightMap = false;
                }
            }
        }

        public void LightmapSetComponent(Entity entity, LightmapBakerSettings settings)
        {
            if (LightMapBakeSystemG.GetLightMapAbleToUse(_World.GetNativePointer(), entity.EntityID))
            {
                if (settings.ComponentType == "LightMap")
                {
                    LightMapBaker LightMapBaker = (LightMapBaker)entity.GetComponent(typeof(LightMapBaker));
                    if (LightMapBaker == null)
                    {
                        LightMapBaker = entity.CreateComponent<LightMapBaker>();
                        LightMapBaker.Reset();
                    }
                    LightMapBaker.SetLightMapRefresh(true);
                    LightMapBaker.CastShadow = settings.CastShadow;
                    LightMapBaker.ApplyLightmap = settings.ApplyLightMap;
                    LightMapBaker.ApplyShadowmap = settings.ApplyShadowMap;
                    LightMapBaker.InvPenumbraSize = settings.InvPenumbraSize;
                    LightMapBaker.LightmapResolution = settings.Resolution;
                }
                else if (settings.ComponentType == "LightProbe")
                {
                    LightProbeComponent LightProbeComponent = (LightProbeComponent)entity.GetComponent(typeof(LightProbeComponent));
                    if (LightProbeComponent == null)
                    {
                        LightProbeComponent = entity.CreateComponent<LightProbeComponent>();
                        LightProbeComponent.Reset();
                    }
                }
                InspectorUI.GetInstance().InspectObject();
                EditorScene.GetInstance().SetDirty();
            }
            foreach (var child in entity.Children)
            {
                LightmapSetComponent(child, settings);
            }
        }

        public void LightmapSetDebugShaderConst(string nameID, bool GIOnly)
        {
            LightMapSystemG.SetDebugShaderConst(_World.GetNativePointer(), nameID, GIOnly);
        }

        public List<Entity> GetLightMapBakeEntity()
        {
            List<Entity> Result = new List<Entity>();
            List<Entity> AllEntityList = EnumEntities(GetRoot());
            foreach (Entity entity in AllEntityList)
            {
                LightMapBaker lmComp = (LightMapBaker)entity.GetComponent(typeof(LightMapBaker));
                if (lmComp != null)
                {
                    Result.Add(entity);
                }
            }
            return Result;
        }

        public int CheckBakeLightmapResult()
        {
            int bakeStatus = 0;// Runtime.World_LightmapBakerResult(_World.GetNativePointer());
            if (bakeStatus == 100)
            {
                HashSet<string> convertedFileNames = new HashSet<string>();
                string rootPath = CrossEngine.GetInstance().GetAssetPath() + "/";

                TextureImportSetting texImportSetting = new TextureImportSetting();
                texImportSetting.Type = TextureType.ImageTexture;
                texImportSetting.ColorSpace = ImportColorSpace.Linear;
                texImportSetting.Compression = LightingBakerUI.GetInstance().GetTextureCompressMode();
                texImportSetting.GenerateMipmap = true;

                List<Entity> AllEntityList = LightingBakerUI.GetInstance().GetLightMapBakeEntityList();
                foreach (Entity entity in AllEntityList)
                {
                    LightMapBaker lmComp = (LightMapBaker)entity.GetComponent(typeof(LightMapBaker));
                    if (lmComp != null)
                    {
                        void ImportLightMapAsset(string ndaName)
                        {
                            if (ndaName.Length > 0 && ndaName.EndsWith(".nda"))
                            {
                                string texPngName = ndaName.Replace(".nda", ".png");
                                if (!convertedFileNames.Contains(texPngName))
                                {
                                    Console.WriteLine("ImportLightMapAsset: {0}", ndaName);
                                    if (texImportSetting.Compression == TextureCompression.BC1
                                        || texImportSetting.Compression == TextureCompression.BC3
                                        || texImportSetting.Compression == TextureCompression.BC4
                                        || texImportSetting.Compression == TextureCompression.BC6H
                                        || texImportSetting.Compression == TextureCompression.BC7)
                                    {
                                        if (ndaName.EndsWith("LM.nda") || ndaName.EndsWith("TransferAZ.nda") || ndaName.EndsWith("TransferXY.nda"))
                                        {
                                            texImportSetting.Compression = TextureCompression.BC1;
                                        }
                                        else if (ndaName.EndsWith("SM.nda"))
                                        {
                                            texImportSetting.Compression = TextureCompression.BC4;
                                        }
                                        else
                                        {
                                            texImportSetting.Compression = TextureCompression.BC3;
                                        }
                                    }
                                    texImportSetting.SetEngineImportSetting();
                                    //CrossEditorAssetPipelinePINVOKE.SetTextureImportSettings(TextureImportSetting.getCPtr(texImportSetting));
                                    AssetImporterManager.Instance().ImportAsset(rootPath + texPngName, rootPath + ndaName);
                                    convertedFileNames.Add(texPngName);
                                }
                            }
                        }
                        string texNdaName = lmComp.GetDiffuseTex();
                        ImportLightMapAsset(texNdaName);

                        texNdaName = lmComp.GetTransferTex0();
                        ImportLightMapAsset(texNdaName);

                        texNdaName = lmComp.GetTransferTex1();
                        ImportLightMapAsset(texNdaName);

                        texNdaName = lmComp.GetShadowTex();
                        ImportLightMapAsset(texNdaName);

                        texNdaName = lmComp.GetSkyOcclusionTex();
                        ImportLightMapAsset(texNdaName);

                        texNdaName = lmComp.GetAOMaterialMaskTex();
                        ImportLightMapAsset(texNdaName);

                        texNdaName = lmComp.GetLocalLMTransferTex();
                        ImportLightMapAsset(texNdaName);
                    }
                }
                //Runtime.World_LightmapBakerFinished(_World.GetNativePointer());
                SaveScene();
            }
            else if (bakeStatus == -1)
            {

            }
            _LightmapBakeStatus = bakeStatus;
            return _LightmapBakeStatus;
        }

        public void BakeNavMesh(NavMeshBakerSettingsData NavMeshBakerSettingsData)
        {
            int Size = Marshal.SizeOf(typeof(NavMeshBakerSettingsData));
            System.IntPtr SettingPtr = Marshal.AllocHGlobal(Size);
            Marshal.StructureToPtr(NavMeshBakerSettingsData, SettingPtr, false);
            //Runtime.World_SetNavMeshBakerSettings(_World.GetNativePointer(), SettingPtr.ToInt64());
            Marshal.FreeHGlobal(SettingPtr);
            //Runtime.World_NavMeshBakerBakeNavMesh(_World.GetNativePointer());
        }

        #endregion

        #region Entity

        public EditOperation_SelectEntities BeginSelectEntities()
        {
            EditOperation_SelectEntities EditOperation_SelectEntities = new EditOperation_SelectEntities();
            List<Entity> TopEntitiesOld = ToTopEntities(_SelectionList);
            EditOperation_SelectEntities.UpdateSelectedEntitiesBefore(TopEntitiesOld);
            return EditOperation_SelectEntities;
        }

        public void EndSelectEntities(EditOperation_SelectEntities EditOperation_SelectEntities)
        {
            List<Entity> TopEntitiesNew = ToTopEntities(_SelectionList);
            EditOperation_SelectEntities.UpdateSelectedEntitiesAfter(TopEntitiesNew);
            if (EditOperation_SelectEntities.IsIdentity() == false)
            {
                EditOperationManager.GetInstance().AddOperation(EditOperation_SelectEntities);
            }
        }

        public override void DeleteEntities(List<Entity> Entities, bool bRecord = true)
        {
            base.DeleteEntities(Entities, bRecord);

            if (Entities.Contains(_PreviewCameraEntity))
            {
                _PreviewCameraEntity = null;
            }
        }

        #endregion

        #region Selection

        protected override void OnSelectionListChanged()
        {
            base.OnSelectionListChanged();

            // Piolot Camera Process
            Entity CurPiolotEntity = HierarchyUI.GetInstance().GetCurPiolotEntity();
            if (CurPiolotEntity != null && CurPiolotEntity.HasComponent(typeof(Camera)) &&
                EditorSceneUI.GetInstance().GetCameraView())
            {
                if (_SelectionList.Contains(CurPiolotEntity))
                {
                    _SelectionList.Remove(CurPiolotEntity);
                }
            }

            if (_SelectionList.Count == 0)
            {
                InspectorUI InspectorUI = InspectorUI.GetInstance();
                InspectorUI.SetObjectInspected(_World);
                InspectorUI.InspectObject();
            }

            if (GetScreenShotMode())
            {
                Clicross.EntityList list = new Clicross.EntityList();
                list.mEntities.Clear();
                Clicross.GameWorldInterface.World_SelectEntities(_World._WorldInterface, list);
            }

        }
        #endregion

        #region Ray Pick

        public void FallToGroundAsEntirety(List<Entity> Entities)
        {
            List<Entity> TopEntities = EditorScene.GetInstance().ToTopEntities(Entities);
            List<Entity> _Entities = new List<Entity>();
            List<Vector3d> _OldPoses = new List<Vector3d>();
            List<Vector3d> _NewPoses = new List<Vector3d>();
            Double3 CenterMin = new Double3(double.MaxValue, double.MaxValue, double.MaxValue);

            foreach (var Value in TopEntities)
            {
                _Entities.Add(Value);

                CenterMin = new Double3(double.MaxValue, double.MaxValue, double.MaxValue);
                Value.TraverseAll((Entity) =>
                {
                    Double3 Min = GetOrientedAABBMin(Entity);
                    CenterMin.x = Math.Min(CenterMin.x, Min.x);
                    CenterMin.y = Math.Min(CenterMin.y, Min.y);
                    CenterMin.z = Math.Min(CenterMin.z, Min.z);

                });

                Double3 TransformCoord = Value.GetTransformComponent().GetWorldTranslation();
                Vector3d RayStart = new Vector3d(CenterMin.x, CenterMin.y, CenterMin.z);
                _OldPoses.Add(new Vector3d(TransformCoord.x, TransformCoord.y, TransformCoord.z));
                Vector3d RayEnd = new Vector3d(0, 0, 0);
                Vector3d RayBaseTilePosition = new Vector3d(0, 0, 0);

                RayPickResultDouble RayPickResultDouble;
                if (GetCameraMode() == CameraMode.WGS84)
                {
                    RayEnd = new Vector3d(0, 0, 0);
                }
                else if (GetCameraMode() == CameraMode.Flat)
                {
                    RayEnd = RayStart + new Vector3d(0, -1, 0) * (1000);
                }
                RayPickResultDouble = RayPickDouble(ref RayStart, ref RayEnd, ref RayBaseTilePosition, RayPickFlag.Model | RayPickFlag.Terrain);
                if (RayPickResultDouble.HitEntity != null)
                {
                    Transform Transform = Value.GetTransformComponent();
                    Transform.SetWorldTranslation(TransformCoord.Subtract(CenterMin).Add(RayPickResultDouble.HitPoint));
                    Transform.SyncDataFromEngine();
                    Transform.RefreshTransform();
                    Double3 NewPos = TransformCoord.Subtract(CenterMin).Add(RayPickResultDouble.HitPoint);
                    _NewPoses.Add(new Vector3d(NewPos.x, NewPos.y, NewPos.z));
                }
                else
                {
                    _NewPoses.Add(RayStart);
                }
            }
            EditorOperation_FallGound EditorOperation_FallGound = new EditorOperation_FallGound(_Entities, _OldPoses, _NewPoses);
            EditOperationManager.GetInstance().AddOperation(EditorOperation_FallGound);
        }

        public void FallToGroundAsMesh(List<Entity> Entities)
        {
            List<Entity> TopEntities = EditorScene.GetInstance().ToTopEntities(Entities);
            List<Entity> _Entities = new List<Entity>();
            List<Vector3d> _OldPoses = new List<Vector3d>();
            List<Vector3d> _NewPoses = new List<Vector3d>();

            Double3 LowerResult = new Double3();
            foreach (var Value in TopEntities)
            {
                Queue<Entity> Queue = new Queue<Entity>();
                Queue.Enqueue(Value);

                while (Queue.Count != 0)
                {
                    Entity Head = Queue.Dequeue();
                    foreach (Entity Child in Head.Children)
                    {
                        Queue.Enqueue(Child);
                    }
                    if (!Head.HasComponent(typeof(ModelComponent)))
                    {
                        continue;
                    }
                    _Entities.Add(Head);
                    Double3 Min = GetWorldAABBMin(Head);
                    LowerResult = Min;

                    Double3 TransformCoord = Head.GetTransformComponent().GetWorldTranslation();
                    Vector3d RayStart = new Vector3d(LowerResult.x, LowerResult.y, LowerResult.z);
                    Vector3d RayEnd = new Vector3d(0, 0, 0);
                    Vector3d RayBaseTilePosition = new Vector3d(0, 0, 0);
                    _OldPoses.Add(RayStart);
                    RayPickResultDouble RayPickResultDouble;
                    if (GetCameraMode() == CameraMode.WGS84)
                    {
                        RayEnd = new Vector3d(0, 0, 0);
                    }
                    else if (GetCameraMode() == CameraMode.Flat)
                    {
                        RayEnd = RayStart + new Vector3d(0, -1, 0) * (1000);
                    }
                    RayPickResultDouble = RayPickDouble(ref RayStart, ref RayEnd, ref RayBaseTilePosition, RayPickFlag.Model | RayPickFlag.Terrain);
                    if (RayPickResultDouble.HitEntity != null)
                    {
                        Double3 NewPos = TransformCoord.Subtract(LowerResult).Add(RayPickResultDouble.HitPoint);
                        _NewPoses.Add(new Vector3d(NewPos.x, NewPos.y, NewPos.z));
                    }
                    else
                    {
                        _NewPoses.Add(RayStart);
                    }
                }

                for (int i = 0; i < _Entities.Count; i++)
                {
                    Transform Transform = _Entities[i].GetTransformComponent();
                    Transform.SetWorldTranslation(_NewPoses[i]);
                    Transform.SyncDataFromEngine();
                    Transform.RefreshTransform();
                }
            }
            EditorOperation_FallGound EditorOperation_FallGound = new EditorOperation_FallGound(_Entities, _OldPoses, _NewPoses);
            EditOperationManager.GetInstance().AddOperation(EditorOperation_FallGound);
        }

        public Double3 GetWorldAABBMin(Entity Entity)
        {
            Double3 Min = new Double3();
            Double3 EntityMin = new Double3(double.MaxValue, double.MaxValue, double.MaxValue);
            Double3 EntityMax = new Double3(double.MinValue, double.MinValue, double.MinValue);

            if (Entity.HasComponent(typeof(ModelComponent)))
            {
                ModelComponent ModelComponent = Entity.GetModelComponent();
                if (ModelComponent != null)
                {
                    Double3 ItemMin = new Double3();
                    Vector3d Vector3Min = new Vector3d();
                    //Vector3d Vector3Max = new Vector3d();

                    Clicross.ResourceAABB aabb = Clicross.GameWorldInterface.Resource_GetDynamicAABBData(Entity.World._WorldInterface, Entity.EntityID);
                    ItemMin = Vector3Min.ToDouble3();

                    EntityMin.x = Math.Min(aabb.min.x, ItemMin.x);
                    EntityMin.y = Math.Min(aabb.min.y, ItemMin.y);
                    EntityMin.z = Math.Min(aabb.min.z, ItemMin.z);
                }
                Min = EntityMin;
            }
            else
            {
                Min = new Double3(double.MaxValue, double.MaxValue, double.MaxValue);
            }
            return Min;
        }

        public Double3 GetOrientedAABBMin(Entity Entity)
        {
            Double3 BottomCenter = new Double3();
            Double3 EntityMin = new Double3(double.MaxValue, double.MaxValue, double.MaxValue);

            if (Entity.HasComponent(typeof(ModelComponent)))
            {
                ModelComponent ModelComponent = Entity.GetModelComponent();
                if (ModelComponent != null)
                {
                    Double3 ItemMin = new Double3();
                    Double3 ItemMax = new Double3();
                    Vector3d Vector3Min = new Vector3d();
                    //Vector3d Vector3Max = new Vector3d();

                    Clicross.ResourceAABB aabb = Clicross.GameWorldInterface.Resource_GetOrientedAABBData(Entity.World._WorldInterface, Entity.EntityID);
                    ItemMin = Vector3Min.ToDouble3();

                    EntityMin.x = Math.Min(aabb.min.x, ItemMin.x);
                    EntityMin.y = Math.Min(aabb.min.y, ItemMin.y);
                    EntityMin.z = Math.Min(aabb.min.z, ItemMin.z);
                }
                BottomCenter = EntityMin;
            }
            else
            {
                BottomCenter = new Double3(double.MaxValue, double.MaxValue, double.MaxValue);
            }
            return BottomCenter;
        }
        #endregion
    }
}
