using EditorUI;

namespace CrossEditor
{
    class Inspector_Property_NavMesh : Inspector_Property_File
    {
        bool _IsVisible;
        Button _ButtonVisualize;

        public Inspector_Property_NavMesh()
        {
            _IsVisible = false;
        }

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);
            Control Container = GetValueContainer();

            _ButtonVisualize = new Button();
            _ButtonVisualize.Initialize();
            _ButtonVisualize.SetFontSize(12);
            _ButtonVisualize.SetTextOffsetY(2);
            _ButtonVisualize.SetText("Visualize");
            _ButtonVisualize.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ButtonVisualize.SetToolTips("Visualize navigation mesh.");
            _ButtonVisualize.ClickedEvent += OnButtonVisualizeClicked;
            Container.AddChild(_ButtonVisualize);
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            int Y1 = _ChildContainer.GetHeight();
            int ButtonWidth = Width - 2 * SPAN_X;
            _ButtonVisualize.SetPosition(SPAN_X, Y1 + 5, ButtonWidth, 20);
            Y1 += 30;
            _ChildContainer.SetHeight(Y1);
            Y += 30;
        }

        void OnButtonVisualizeClicked(Button Sender)
        {
            if (_IsVisible)
            {
                _IsVisible = false;
                _ButtonVisualize.SetText("Visualize");
                _ButtonVisualize.SetToolTips("Visualize navigation mesh.");
            }
            else
            {
                _IsVisible = true;
                _ButtonVisualize.SetText("Hide");
                _ButtonVisualize.SetToolTips("Hide navigation mesh.");
            }
        }

    }
}
