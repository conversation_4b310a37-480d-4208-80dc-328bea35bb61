using CEngine;
using EditorUI;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    internal class Inspector_ControllableUnitComponent : Inspector_Component
    {
        protected ControllableUnitComponent _ControllerComponenet = null;

        public Inspector_ControllableUnitComponent(List<Entity> Entities)
            : base(Entities)
        {
        }

        public override void InspectObject(object Object, object Tag = null)
        {
            _ControllerComponenet = Object as ControllableUnitComponent;
            base.InspectObject(Object, Tag);
        }

        protected override void RefreshChildInspectors()
        {
            base.RefreshChildInspectors();

            var controller = _ControllerComponenet.ControllableUnit;
            if (controller == null)
                return;

            Type type = controller.GetType();
            List<PropertyInfo> Properties = PropertyCollector.CollectPropertiesOfType(type);

            foreach (PropertyInfo PropertyInfo in Properties)
            {
                if (PropertyInfo.Name != "Enable")
                {
                    AddPropertyInspector(PropertyInfo, _ControllerComponenet);
                }
            }
        }

        public override object GetPropertyValueFunction(object Object, string PropertyName, ValueExtraProperty ValueExtraProperty)
        {
            Type type = _ControllerComponenet.GetType();
            PropertyInfo PropertyInfo = type.GetProperty(PropertyName);
            if (PropertyInfo != null)
                return PropertyInfo.GetValue(Object);

            type = _ControllerComponenet.ControllableUnit.GetType();
            PropertyInfo = type.GetProperty(PropertyName);
            if (PropertyInfo != null)
            {
                string outJson = ControllableUnitSystemG.GetControllableUnitJson(
                    _ControllerComponenet.Entity.World.GetNativePointer(),
                    _ControllerComponenet.Entity.EntityID);

                _ControllerComponenet.ControllableUnit = JsonConvert.DeserializeObject(outJson, _ControllerComponenet.ControllableUnit.GetType())
                    as ControllableUnit;

                return PropertyInfo.GetValue(_ControllerComponenet.ControllableUnit);
            }

            return null;
        }

        public override void SetPropertyValueFunction(object Object, string PropertyName, object PropertyValue, SubProperty SubProperty)
        {
            Type type = Object.GetType();
            PropertyInfo PropertyInfo = type.GetProperty(PropertyName);
            if (PropertyInfo != null)
            {
                PropertyInfo.SetValue(Object, PropertyValue);
                // need refresh inspectors after setting PropertyInfo
                OperationQueue.GetInstance().AddOperation(() =>
                {
                    GetInspectorHandler().InspectObject();
                });
            }
            else
            {
                type = _ControllerComponenet.ControllableUnit.GetType();
                PropertyInfo = type.GetProperty(PropertyName);

                if (PropertyInfo != null)
                {
                    PropertyInfo.SetValue(_ControllerComponenet.ControllableUnit, PropertyValue);

                    var controllerJson = JsonConvert.SerializeObject(_ControllerComponenet.ControllableUnit);
                    ControllableUnitSystemG.SetControllableUnitJson(
                        _ControllerComponenet.Entity.World.GetNativePointer(),
                        _ControllerComponenet.Entity.EntityID,
                        controllerJson);

                    // need refresh inspectors after setting PropertyInfo
                    OperationQueue.GetInstance().AddOperation(() =>
                    {
                        GetInspectorHandler().InspectObject();
                    });
                }
            }

            EditorScene.GetInstance().SetDirty();
            if (PropertyName == "CurveCtrResPath" && Object is Component)
            {
                var cinematicUI = CinematicUI.GetInstance();
                if (cinematicUI.CheckIsPreviewing((Object as Component).Entity))
                {
                    cinematicUI.ReloadControllableUnit();
                }
            }
        }

    }

}
