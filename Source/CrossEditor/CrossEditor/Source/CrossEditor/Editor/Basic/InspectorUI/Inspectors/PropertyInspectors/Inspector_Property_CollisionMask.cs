using CEngine;
using System;

namespace CrossEditor
{
    class Inspector_Property_CollisionMask : Inspector_Property_Mask
    {
        static Type enumType = typeof(CollisionType);
        static string BlockNoneTag = "None";
        static uint BlockNoneExt = 0x40000000;
        static string BlockAllTag = "BlockAll";
        static uint BlockAllExt = 0x80000000;
        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);
        }

        public override void ReadValue()
        {
            CollisionMask collisionMask = (CollisionMask)GetPropertyValue();
            uint MaskValue = collisionMask.Value();
            _MaskBoxValue.SetMaskValue(MaskValue);
            if (MaskValue == 0 || MaskValue == 0x0000FFFF)
                _MaskBoxValue.GetValueEdit().SetText(MaskValue == 0 ? BlockNoneTag : BlockAllTag);
            if (_ValueExtraProperty._bHasMultipleValues)
            {
                _MaskBoxValue.GetValueEdit().SetText(MULTIPLE_VALUES_STRING);
            }
        }

        public override void WriteValue()
        {
            uint MaskValue = _MaskBoxValue.GetMaskValue();
            if ((MaskValue & BlockNoneExt) == BlockNoneExt)
                MaskValue = 0;
            if ((MaskValue & BlockAllExt) == BlockAllExt)
                MaskValue = 0x0000FFFF;
            _MaskBoxValue.SetMaskValue(MaskValue);

            CollisionMask collisionMask = (CollisionMask)GetPropertyValue();
            collisionMask.SetValue((ushort)MaskValue);
            SetPropertyValue(collisionMask);
            if (MaskValue == 0 || MaskValue == 0x0000FFFF)
                _MaskBoxValue.GetValueEdit().SetText(MaskValue == 0 ? BlockNoneTag : BlockAllTag);
        }

        public override void FillMaskBoxItems()
        {
            _MaskBoxValue.ClearItems();
            _MaskBoxValue.AddItem(BlockNoneTag, BlockNoneExt);
            foreach (string EnumName in enumType.GetEnumNames())
            {
                int value = (int)Enum.Parse(enumType, EnumName);
                if (value > 0)
                    _MaskBoxValue.AddItem(EnumName, 1u << (value - 1));
            }
            _MaskBoxValue.AddItem(BlockAllTag, BlockAllExt);
        }
    }
}
