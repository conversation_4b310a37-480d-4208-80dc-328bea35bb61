using CEngine;

namespace CrossEditor
{
    class Inspector_AdjustColor : Inspector_Struct_With_Property
    {
        protected AdjustColor _AdjustColor;
        public override void InspectObject(object Object, object Tag = null)
        {
            _AdjustColor = (AdjustColor)Object;
            base.InspectObject(_AdjustColor, Tag);
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);
        }
    }
}
