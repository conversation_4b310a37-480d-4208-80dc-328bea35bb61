using CEngine;
using EditorUI;
using System.Collections.Generic;
using System.Threading;

namespace CrossEditor
{
    public class ResourceTypeItem
    {
        public long ModifyTime;
        public ClassIDType ObjectClassID;
    }

    public class ResourceTypeCache
    {
        Dictionary<string, ResourceTypeItem> _Cache;
        object _Lock;
        ManualResetEvent _CacheThreadEvent;
        Thread _CacheThread;
        bool _CacheThreadDone;

        private List<string> _FilterIncludeList = new List<string> { ".nda", ".prefab", ".system", ".emitter", ".mpc", ".materialfunction" };

        public IReadOnlyList<string> FilterIncludeList => _FilterIncludeList.AsReadOnly();

        static ResourceTypeCache _Instance = null;

        public bool GetCacheState() => _CacheThreadDone;

        public static ResourceTypeCache GetInstance()
        {
            if (_Instance == null)
            {
                _Instance = new ResourceTypeCache();
            }
            return _Instance;
        }

        ResourceTypeCache()
        {
            _Cache = new Dictionary<string, ResourceTypeItem>();
            _Lock = new object();

            _CacheThreadEvent = new ManualResetEvent(true);
            _CacheThread = new Thread(CacheProject);
            _CacheThreadDone = false;
        }

        public void StartCache()
        {
            _CacheThread.Start();
        }

        public void EndCache()
        {
            _CacheThread.Join();
        }

        public void SuspendCacheThread()
        {
            if (_CacheThreadDone)
            {
                return;
            }

            _CacheThreadEvent.Reset();
        }

        public void ResumeCacheThread()
        {
            if (_CacheThreadDone)
            {
                return;
            }

            _CacheThreadEvent.Set();
        }

        void CacheProject()
        {
            string ProjectDirectory = MainUI.GetInstance().GetProjectDirectory();
            string ContentsDirectory = ProjectDirectory + "/Contents";
            CacheSingleDirectory(ContentsDirectory);

            string ResourceDirectory = EditorUtilities.GetResourceDirectory();

            string EngineResourceDirectory = ResourceDirectory + "/EngineResource";
            CacheSingleDirectory(EngineResourceDirectory);

            string PipelineResourceDirectory = ResourceDirectory + "/PipelineResource";
            CacheSingleDirectory(PipelineResourceDirectory);

            _CacheThreadDone = true;
        }

        void CacheSingleDirectory(string Directory)
        {
            DirectoryWalker DirectoryWalker = new DirectoryWalker();
            DirectoryWalker.WalkDirectory(Directory, true);
            int Count = DirectoryWalker.GetDirectoryWalkItemCount();
            for (int i = 0; i < Count; i++)
            {
                DirectoryWalkItem DirectoryWalkItem = DirectoryWalker.GetDirectoryWalkItem(i);
                if (DirectoryWalkItem.bIsDirectory == false)
                {
                    string EditorFilename = DirectoryWalkItem.Path;
                    string Extension = PathHelper.GetExtension(EditorFilename);
                    if (_FilterIncludeList.Contains(Extension.ToLower()))
                    {
                        if (EditorFilename.Contains("EngineResource/Editor/") == false)
                        {
                            _CacheThreadEvent.WaitOne();
                            GetResourceType_Cache(EditorFilename);
                        }
                    }
                }
            }
        }

        public ClassIDType GetResourceType_Cache(string Path)
        {
            ResourceTypeItem ResourceTypeItem = null;
            bool bExist = false;
            lock (_Lock)
            {
                bExist = _Cache.TryGetValue(Path, out ResourceTypeItem);
            }
            if (bExist)
            {
                long ModifyTime1 = FileHelper.GetFileModifyTime(Path);
                if (ResourceTypeItem.ModifyTime == ModifyTime1)
                {
                    return ResourceTypeItem.ObjectClassID;
                }
                else
                {
                    ResourceTypeItem.ModifyTime = ModifyTime1;
                    ResourceTypeItem.ObjectClassID = Resource.GetResourceTypeStatic(Path);
                    return ResourceTypeItem.ObjectClassID;
                }
            }
            else
            {
                ResourceTypeItem = new ResourceTypeItem();
                ResourceTypeItem.ModifyTime = FileHelper.GetFileModifyTime(Path);
                ResourceTypeItem.ObjectClassID = Resource.GetResourceTypeStatic(Path);
                lock (_Lock)
                {
                    _Cache[Path] = ResourceTypeItem;
                }
                return ResourceTypeItem.ObjectClassID;
            }
        }

        // No additional heavy file IO
        public ClassIDType GetResourceType_CacheOnly(string Path)
        {
            ResourceTypeItem ResourceTypeItem = null;
            if (_Cache.TryGetValue(Path, out ResourceTypeItem))
            {
                return ResourceTypeItem.ObjectClassID;
            }

            return ClassIDType.CLASS_NullType;
        }

        public List<string> GetResourceListByType(ClassIDType type)
        {
            if (!_CacheThreadDone)
            {
                _CacheThread.Join();
            }

            List<string> result = new List<string>();

            foreach (KeyValuePair<string, ResourceTypeItem> item in _Cache)
            {
                if (item.Value.ObjectClassID == type)
                {
                    result.Add(item.Key);
                }
            }

            return result;
        }
    }
}
