using CEngine;
namespace CrossEditor
{
    class LocalScaler : ManipulatorBase
    {
        public LocalScaler(ManipulatorHelper Owner) : base(Owner)
        {
        }

        public override void OnMouseDown(RayPickResult RayPickResult, int MouseX, int MouseY)
        {
            base.OnMouseDown(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, MouseY);

            if (_HitTag == GeometryTag.ScalerBox)
            {
                _Manipulator1 = new Vector3d(1.0f, -1.0f, 0.0f);
                _Manipulator1.Normalize();
            }
            else
            {
                Vector3d Origin = new Vector3d(0.0f, 0.0f, 0.0f);
                Vector3d Unit1 = new Vector3d(1.0f, 0.0f, 0.0f);
                if (_HitTag == GeometryTag.ScalerX)
                {
                    Unit1 = new Vector3d(1.0f, 0.0f, 0.0f);
                }
                else if (_HitTag == GeometryTag.ScalerY)
                {
                    Unit1 = new Vector3d(0.0f, 1.0f, 0.0f);
                }
                else if (_HitTag == GeometryTag.ScalerZ)
                {
                    Unit1 = new Vector3d(0.0f, 0.0f, 1.0f);
                }
                _Manipulator1 = CalculateManipulator_Local(Origin, Unit1);
            }
        }

        public override void OnMouseMove(int MouseX, int MouseY)
        {
            base.OnMouseMove(MouseX, MouseY);
            if (_MouseDown == false)
            {
                return;
            }

            int DeltaMouseX = MouseX - _SavedMouseX;
            int DeltaMouseY = MouseY - _SavedMouseY;
            double DistanceMoved1 = _Manipulator1.X * DeltaMouseX + _Manipulator1.Y * DeltaMouseY;
            double DistanceMoved2 = _Manipulator2.X * DeltaMouseX + _Manipulator2.Y * DeltaMouseY;
            DistanceMoved1 *= 0.005f;
            if (DistanceMoved1 < -0.8f)
            {
                DistanceMoved1 = -0.8f;
            }
            double Scaling1 = 1.0f + DistanceMoved1;

            double ScalingStep = _Owner.GetScalingStep();
            if (ScalingStep != 0.0f)
            {
                Scaling1 /= ScalingStep;

                Scaling1 = (int)Scaling1;
                if (Scaling1 <= 0.0f)
                {
                    Scaling1 = 1.0f;
                }

                Scaling1 *= ScalingStep;
            }

            Vector3d ScalingVector = new Vector3d(1.0f, 1.0f, 1.0f);
            Matrix4x4d Scaling = new Matrix4x4d();
            Scaling.LoadIdentity();
            if (_HitTag == GeometryTag.ScalerBox)
            {
                Scaling.SetScaling(Scaling1, Scaling1, Scaling1);
                ScalingVector.X = Scaling1;
                ScalingVector.Y = Scaling1;
                ScalingVector.Z = Scaling1;
            }
            else if (_HitTag == GeometryTag.ScalerX)
            {
                Scaling.SetScaling(Scaling1, 1.0f, 1.0f);
                ScalingVector.X = Scaling1;
            }
            else if (_HitTag == GeometryTag.ScalerY)
            {
                Scaling.SetScaling(1.0f, Scaling1, 1.0f);
                ScalingVector.Y = Scaling1;
            }
            else if (_HitTag == GeometryTag.ScalerZ)
            {
                Scaling.SetScaling(1.0f, 1.0f, Scaling1);
                ScalingVector.Z = Scaling1;
            }

            double NewScalingX = _Owner._OldScaling.X * ScalingVector.X;
            double NewScalingY = _Owner._OldScaling.Y * ScalingVector.Y;
            double NewScalingZ = _Owner._OldScaling.Z * ScalingVector.Z;
            _Owner._NewScaling = new Vector3d(NewScalingX, NewScalingY, NewScalingZ);
            _Owner.UpdatTargetMatrix();
        }

        public override void OnMouseUp(int MouseX, int MouseY)
        {
            base.OnMouseUp(MouseX, MouseY);
        }

        public override void AddEditOperation_ModifyProperty(object Object)
        {
            AddEditOperation_ModifyProperty(Object, "Translation", _Owner._OldTranslation_Pivot, _Owner._NewTranslation_Pivot);
            AddEditOperation_ModifyProperty(Object, "Scale", _Owner._OldScaling_Pivot, _Owner._NewScaling_Pivot);
        }
    }
}
