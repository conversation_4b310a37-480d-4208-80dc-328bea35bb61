using CEngine;
using Clicegf;
using EditorUI;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace CrossEditor
{
    class EditorSceneUI : SceneUIBase
    {
        const float M_PI = 3.1415926f;

        static EditorSceneUI _Instance = new EditorSceneUI();

        Panel _CameraPreviewPanel;

        public EditorMode _CurrentMode = EditorMode.Object_Mode;
        public PaintMode _PaintMode = PaintMode.Editor;
        public PaintObjectMode _PaintObjectMode = PaintObjectMode.Cluster;
        public PaintInstanceMode _PaintInstanceMode = PaintInstanceMode.Instance;

        const int OriginCicleR = 1000;
        const int MinCircleR = 0;
        const int MaxCircleR = 5000;
        float _CurrentCircleR = OriginCicleR;
        float _PaintSphereScale = 20.0f;
        float _PaintSphereExtraScale = 1.0f;
        const float SphereMinScale = 0.00f;
        const float SphereMaxScale = 100.0f;

        const string ClusterBluePath = "EngineResource/Material/ClusterBlue.nda";
        const string ClusterRedPath = "EngineResource/Material/ClusterRed.nda";

        PhysicsSceneDebugViewOption _PhysicsDebugViewOption = new PhysicsSceneDebugViewOption();
        MenuItem _ShowOptionPhysicsCollision;
        MenuItem _ShowOptionPhysicsJointsLimit;
        MenuItem _ShowOptionPhysicsJointsFrame;
        MenuItem _ShowOptionPhysicsRaycast;
        bool _bShowOptionPhysicsRaycast;
        Dictionary<MenuItem, string> _BakeMenuItem = new Dictionary<MenuItem, string>();
        MenuItem _BakeItem;

        MenuItem _ViewModeItem;

        public RayPickResult _RayPickResult;

        Button _ButtonShowOption;

        Button _ButtonExitPilot;
        Button _ButtonCameraShake;
        Label _PiolotEdit;

        //float _PaintSpeed = 0.0f;
        Dictionary<Entity, Vector3d> _PaintTriMap = new Dictionary<Entity, Vector3d>();
        Dictionary<Entity, Vector3d> _PaintSizeMap = new Dictionary<Entity, Vector3d>();
        Dictionary<Entity, Dictionary<int, Vector3d>> _PaintInstSizeMap = new Dictionary<Entity, Dictionary<int, Vector3d>>();

        Dictionary<ListViewItem, int> _PaintItemSize = new Dictionary<ListViewItem, int>();
        Entity _PaintSphere;
        bool _bPaintSphereDirty;
        bool _bEnablePaintSpere;
        int _PaintSphereSavedMouseX;
        int _PaintSphereSavedMouseY;
        Dictionary<string, Entity> _InstanceMap = new Dictionary<string, Entity>();
        List<string> _EntityPathList = new List<string>();
        float size = 0.0f;
        long _PreviousTime = 0;
        const long IntervalTime = 300;
        long _PreviousPaintTime = 0;
        const long IntervalPaintTime = 10;
        Entity _TestHitEntity;
        Vector3d SphereTilePosition;

        ListViewItem _CurrentPaintItem = new ListViewItem();
        //ClusterObjectDataInfo Info;
        List<Vector2f> Result = new List<Vector2f>();
        bool _bEraseInstance = false;

        //bool _bEcotopeMapDirty = false;
        bool _bCameraView = false;

        const float SphereModelRadius = 50.0f;
        const float EcotopeScale = 100.0f;
        const float EcotopeGridSize = 100.0f;
        bool _IsAllGeneBVHInstance = false;
        bool _IsDispalyDialog = false;
        CommonDialogUI PaintCommonDialogUI;
        string _CineCameraName = "";

        EditOperation_SelectEntities _EditOperation_SelectEntities;

        Dictionary<string, CameraMode> _SceneCameraMode;

        public static EditorSceneUI GetInstance()
        {
            return _Instance;
        }

        public EditorSceneUI()
        {
            _Name = "Scene";

            _DropPoint = new Vector3d();
            _ManipulatorHelper = new ManipulatorHelper(this);

            _bPaintSphereDirty = true;
            _bEnablePaintSpere = false;
            _PaintSphereSavedMouseX = 0;
            _PaintSphereSavedMouseY = 0;

            _SceneCameraMode = new Dictionary<string, CameraMode>();


        }

        public override bool Initialize()
        {
            InitPanel();
            InitToolBar();
            InitScene(EditorScene.GetInstance());

            _ButtonShowOption = new Button();
            _ButtonShowOption.Initialize();
            _ButtonShowOption.SetText("Show Options");
            _ButtonShowOption.SetFontSize(16);
            _ButtonShowOption.SetTextOffsetY(0);
            _ButtonShowOption.SetPosition(8, 8, _ButtonShowOption.CalculateTextWidth() + 8, 30);
            _ButtonShowOption.SetBorderColor(Color.EDITOR_UI_GRAY_TEXT_COLOR);
            _ButtonShowOption.SetNormalColor(Color.EDITOR_UI_CONTROL_BACK_COLOR);
            _ButtonShowOption.ClickedEvent += OnButtonShowOptionClicked;
            _Panel.AddChild(_ButtonShowOption);

            _ButtonExitPilot = new Button();
            _ButtonExitPilot.Initialize();
            _ButtonExitPilot.SetSize(16, 16);
            _ButtonExitPilot.SetPosition(8, 30, 16, 16);
            _ButtonExitPilot.SetImage(UIManager.LoadUIImage("Editor/Icons/Common/ExitPilot.png"));
            _ButtonExitPilot.SetToolTips("");
            _ButtonExitPilot.SetNormalColor(Color.EDITOR_UI_EDIT_BACK_COLOR);
            _ButtonExitPilot.SetBorderColor(Color.EDITOR_UI_HILIGHT_COLOR_ORANGE);
            _ButtonExitPilot.ClickedEvent += OnButtonExitPilotClicked;
            _ButtonExitPilot.SetVisible(false);
            _Panel.AddChild(_ButtonExitPilot);

            _ButtonCameraShake = new Button();
            _ButtonCameraShake.Initialize();
            _ButtonCameraShake.SetSize(16, 16);
            _ButtonCameraShake.SetPosition(25, 30, 16, 16);
            _ButtonCameraShake.SetImage(UIManager.LoadUIImage("Editor/Icons/Common/CameraShake.png"));
            _ButtonCameraShake.SetToolTips("");
            _ButtonCameraShake.SetNormalColor(Color.EDITOR_UI_EDIT_BACK_COLOR);
            _ButtonCameraShake.SetBorderColor(Color.EDITOR_UI_HILIGHT_COLOR_ORANGE);
            _ButtonCameraShake.ClickedEvent += OnButtonCameraShakeClicked;
            _ButtonCameraShake.SetVisible(false);
            _Panel.AddChild(_ButtonCameraShake);

            _PiolotEdit = new Label();
            _PiolotEdit.Initialize();
            _PiolotEdit.SetTextColor(Color.White);
            _PiolotEdit.SetText("[Piolot Active -]");
            _PiolotEdit.SetFontSize(16);
            _PiolotEdit.SetTextOffsetY(2);
            _PiolotEdit.SetPosition(42, 30, 200, 16);
            _PiolotEdit.SetVisible(false);
            _Panel.AddChild(_PiolotEdit);

            DragDropManager DragDropManager = DragDropManager.GetInstance();
            DragDropManager.DragBeginEvent += OnDragDropManagerDragBegin;
            DragDropManager.DragMoveEvent += OnDragDropManagerDragMove;
            DragDropManager.DragEndEvent += OnDragDropManagerDragEnd;
            DragDropManager.DragClearEvent += OnDragDropManagerDragClear;
            DragDropManager.DragCancelEvent += OnDragDropManagerDragCancel;

            base.Initialize(_Name, _Panel);

            _DockingCard.KeyUpEvent += CinematicUI.GetInstance().OnDockingCardKeyUpImpl;

            _CameraPreviewPanel = new Panel();
            _CameraPreviewPanel.SetPosition(32, 32, 256, 256);
            _Panel.AddChild(_CameraPreviewPanel);
            _CameraPreviewPanel.PaintEvent += OnCameraPanelPaint;

            _bShowOptionPhysicsRaycast = false;
            return true;
        }

        public EditorScene GetEditorScene()
        {
            return _Scene as EditorScene;
        }

        string GetSceneFilename()
        {
            string SceneFilename = EditorScene.GetInstance().GetCurrentSceneFilename();
            string SceneFilename1 = EditorUtilities.EditorDirectoryToStandardDirectory(SceneFilename);
            return SceneFilename1;
        }

        public void SaveSceneCameraMode()
        {
            string SceneFilename = GetSceneFilename();
            _SceneCameraMode[SceneFilename] = GetCameraModeComboBoxValue();
        }

        public void LoadSceneCameraMode()
        {
            string SceneFilename = GetSceneFilename();
            CameraMode CameraMode = CameraMode.Flat;
            _SceneCameraMode.TryGetValue(SceneFilename, out CameraMode);
            SetCameraModeComboBoxValue(CameraMode);
        }

        public void SaveUserConfig(Record RootRecord)
        {
            Record RecordSceneUI = RootRecord.AddChild();
            RecordSceneUI.SetTypeString("SceneUI");
            // Step info
            Record RecordSceneUIStepInfo = RecordSceneUI.AddChild();
            RecordSceneUIStepInfo.SetTypeString("StepInfo");
            RecordSceneUIStepInfo.SetString("CameraSpeed", _CameraSpeedComboBox.GetSelectedItemText());
            RecordSceneUIStepInfo.SetString("CameraRotation", _CameraRotationComboBox.GetSelectedItemText());
            RecordSceneUIStepInfo.SetString("TranslationStep", _TranslationStepComboBox.GetSelectedItemText());
            RecordSceneUIStepInfo.SetString("RotationStep", _RotationStepComboBox.GetSelectedItemText());
            RecordSceneUIStepInfo.SetString("ScalingStep", _ScalingStepComboBox.GetSelectedItemText());
            // Camera mode
            Record RecordSceneUICameraModes = RecordSceneUIStepInfo.AddChild();
            RecordSceneUICameraModes.SetTypeString("CameraModes");
            foreach (var Pair in _SceneCameraMode)
            {
                Record RecordSceneUICameraMode = RecordSceneUICameraModes.AddChild();
                RecordSceneUICameraMode.SetTypeString("CameraMode");
                RecordSceneUICameraMode.SetString("Scene", Pair.Key);
                RecordSceneUICameraMode.SetString("Mode", Pair.Value.ToString());
            }
            // Suface snapping
            Record RecordSurfaceSnapping = RecordSceneUI.AddChild();
            RecordSurfaceSnapping.SetTypeString("SurfaceSnapping");
            RecordSurfaceSnapping.SetBool("SurfaceSnapping", _ManipulatorHelper.GetSurfaceSnapping());
            RecordSurfaceSnapping.SetBool("RotateToNormal", _ManipulatorHelper.GetRotateToNormal());
            RecordSurfaceSnapping.SetFloat("SurfaceOffset", _ManipulatorHelper.GetSurfaceOffset());
        }

        public void LoadUserConfig(Record RootRecord)
        {
            _SceneCameraMode.Clear();
            Record RecordSceneUI = RootRecord.FindByTypeString("SceneUI");
            if (RecordSceneUI != null)
            {
                Record RecordSceneUIStepInfo = RecordSceneUI.FindByTypeString("StepInfo");
                if (RecordSceneUIStepInfo != null)
                {
                    _CameraSpeedComboBox.SetSelectedItemByText(RecordSceneUIStepInfo.GetString("CameraSpeed"));
                    _CameraSpeedComboBox.TriggerItemSelectedEvent();
                    _CameraRotationComboBox.SetSelectedItemByText(RecordSceneUIStepInfo.GetString("CameraRotation"));
                    _CameraRotationComboBox.TriggerItemSelectedEvent();
                    _TranslationStepComboBox.SetSelectedItemByText(RecordSceneUIStepInfo.GetString("TranslationStep"));
                    _TranslationStepComboBox.TriggerItemSelectedEvent();
                    _RotationStepComboBox.SetSelectedItemByText(RecordSceneUIStepInfo.GetString("RotationStep"));
                    _RotationStepComboBox.TriggerItemSelectedEvent();
                    _ScalingStepComboBox.SetSelectedItemByText(RecordSceneUIStepInfo.GetString("ScalingStep"));
                    _ScalingStepComboBox.TriggerItemSelectedEvent();
                    Record RecordSceneUICameraModes = RecordSceneUIStepInfo.FindByTypeString("CameraModes");
                    if (RecordSceneUICameraModes != null)
                    {
                        int CameraModeCount = RecordSceneUICameraModes.GetChildCount();
                        for (int i = 0; i < CameraModeCount; i++)
                        {
                            Record RecordSceneUICameraMode = RecordSceneUICameraModes.GetChild(i);
                            DebugHelper.Assert(RecordSceneUICameraMode.GetTypeString() == "CameraMode");
                            string SceneFilename1 = RecordSceneUICameraMode.GetString("Scene");
                            string ModeString = RecordSceneUICameraMode.GetString("Mode");
                            CameraMode CameraMode = CameraMode.Flat;
                            if (ModeString == "WGS84")
                            {
                                CameraMode = CameraMode.WGS84;
                            }
                            _SceneCameraMode[SceneFilename1] = CameraMode;
                        }
                    }
                }
                Record RecordSurfaceSnapping = RecordSceneUI.FindByTypeString("SurfaceSnapping");
                if (RecordSurfaceSnapping != null)
                {
                    if (_ManipulatorHelper.GetSurfaceSnapping() != RecordSurfaceSnapping.GetBool("SurfaceSnapping"))
                        _ManipulatorHelper.SwitchSurfaceSnapping();
                    if (_ManipulatorHelper.GetRotateToNormal() != RecordSurfaceSnapping.GetBool("RotateToNormal"))
                        _ManipulatorHelper.SwitchRotateToNormal();
                    _ManipulatorHelper.SetSurfaceOffset(RecordSurfaceSnapping.GetFloat("SurfaceOffset"));
                }
            }
        }

        public void OnDeleteKeyDown()
        {
            HierarchyUI.GetInstance().DoDelete();
        }

        public void OnMaximizePreviewCamera()
        {
            EditorScene EditorScene = GetEditorScene();
            EditorScene.MaximizePreviewCamera();
        }
        protected override void UpdateScene()
        {
            base.UpdateScene();

            // Update camera preview panel
            EditorScene EditorScene = GetEditorScene();
            if (EditorScene.GetSelectedPreviewCamera() != null && EditorScene.GetSelectedPreviewCamera().Entity.World != null)
            {
                float aspect = 1.0f;
                if (EditorScene.GetSelectedPreviewCamera().Mode == CameraProjectionMode.Perspective)
                {
                    aspect = EditorScene.GetSelectedPreviewCamera().PerspectiveAspect;
                }
                else
                {
                    aspect = EditorScene.GetSelectedPreviewCamera().OrthogonalWidth / EditorScene.GetSelectedPreviewCamera().OrthogonalHeight;
                }
                int basicwidth = (int)(_Panel.GetWidth() * 0.4);
                int basicheight = (int)(basicwidth / aspect);
                _CameraPreviewPanel.SetSize(basicwidth, basicheight);
            }

            int CameraX = _CameraPreviewPanel.GetScreenX();
            int CameraY = _CameraPreviewPanel.GetScreenY();
            int CameraWidth = _CameraPreviewPanel.GetWidth();
            int CameraHeight = _CameraPreviewPanel.GetHeight();
            EditorScene.SetCameraPreviewPosition(CameraX, CameraY, CameraWidth, CameraHeight);

            //if (_bEcotopeMapDirty)
            //{
            //    //Runtime.EditorPCG_EcotopeMap_UpdateTexture();
            //    PCGManager.GetInstance().GenerateFoliage();
            //    _bEcotopeMapDirty = false;
            //}
        }

        public override void Update(long TimeElapsed)
        {
            if (GetScene().GetWorld() == null || GetEditorScene().GetLoadingWorld())
            {
                return;
            }
            base.Update(TimeElapsed);
            float TimeElapsedS = TimeElapsed / 1000.0f;
            if (_CurrentMode == EditorMode.Object_Mode)
            {
                // Do something
            }
            else if (_CurrentMode == EditorMode.Paint_Mode)
            {
                //if (_PaintMode == PaintMode.Editor)
                //{
                //}
                //if (Result.Count != 0)
                //{
                //    if (Result.Count >= 0)
                //    {
                //        UpdateGenerate(Result, _CurrentPaintItem.Name);
                //    }
                //    Result.Clear();
                //    //int i = 0;
                //    //while (i <= 5)
                //    //{
                //    //    if (Result.Count >= 1)
                //    //    {
                //    //        UpdateGenerate(Result[Result.Count - 1], _CurrentPaintItem.Name);
                //    //        Result.RemoveAt(Result.Count - 1);
                //    //    }
                //    //    i++;
                //    //}
                //    //EditorScene.GetInstance().SetDirty();
                //}
            }
            else if (_CurrentMode == EditorMode.Terrain_Mode)
            {
                TerrainEditor.GetInstance().Update(TimeElapsedS);
            }
            UpdatePaintSphere();
        }

        public bool IsPaintSpereMode()
        {
            return _CurrentMode == EditorMode.Paint_Mode || _CurrentMode == EditorMode.Terrain_Mode || _CurrentMode == EditorMode.PCG_Mode;
        }

        public void SetPaintSphereDirty()
        {
            _bPaintSphereDirty = true;
        }

        void UpdatePaintSphere()
        {
            if ((_CurrentMode == EditorMode.PCG_Mode && !_bEnablePaintSpere) ||
                _bPaintSphereDirty == false ||
                !GetPanelVisible())
            {
                return;
            }
            _bPaintSphereDirty = false;
            EditorScene EditorScene = GetEditorScene();
            if (_CurrentMode == EditorMode.Paint_Mode || _CurrentMode == EditorMode.Terrain_Mode || _CurrentMode == EditorMode.PCG_Mode)
            {
                Vector3d RayStart = new Vector3d();
                Vector3d RayEnd = new Vector3d();
                if (EditorScene.GetCameraEntity() != null)
                {
                    if (_PaintSphere == null || _PaintSphere.World == null)
                    {
                        return;
                    }
                    Transform Component = _PaintSphere.GetTransformComponent();
                    Vector3d DirectionVector = RayEnd - RayStart;
                    DirectionVector.Normalize();

                    Vector3d Translation = RayStart + new Vector3d(40.0f, 40.0f, 40.0f) * DirectionVector;
                    bool bTerrainMode = (_CurrentMode == EditorMode.Terrain_Mode);
                    bool bPCGMode = (_CurrentMode == EditorMode.PCG_Mode);
                    bool bTerrainOnly = bTerrainMode;
                    bool bPaintErase = (_PaintMode == PaintMode.Erase);

                    if (_bEraseInstance)
                    {
                        return;
                    }
                    RayPickResult RayPickResult = CirclePick(bPaintErase, bTerrainOnly, true);
                    _RayPickResult = RayPickResult;
                    Entity HitEntity = RayPickResult.HitEntity;
                    if (HitEntity != null)
                    {
                        Translation = RayPickResult.HitPoint;
                        if (_TestHitEntity == null)
                        {
                            _TestHitEntity = RayPickResult.HitEntity;
                        }
                    }
                    else
                    {
                        if (bPCGMode)
                        {

                        }
                        else if (bTerrainMode == false)
                        {
                            //Vector3d HitPoint = new Vector3d();
                            Vector3d Direction = RayEnd - RayStart;
                            Direction.Normalize();
                            //if (Runtime.EditorPCG_EcotopeMap_Ray(ref RayStart, ref Direction, ref HitPoint))
                            //{
                            //    Translation = HitPoint;
                            //}
                        }
                    }
                    if (Component != null && Component.Entity != null && Component.Entity.World != null)
                    {
                        Component.Translation = Translation;
                    }

                    //if (_CurrentMode == EditorMode.Paint_Mode)
                    //{
                    //    if (_PaintSphereScale == 0)
                    //    {
                    //        SetPaintSphereValue(PaintToolEditor.GetInstance().GetRadius());
                    //    }
                    //}

                    if (_CurrentMode == EditorMode.Terrain_Mode)
                    {
                        if (HitEntity != null)
                        {
                            _PaintSphereScale = (float)TerrainEditor.GetInstance().CalculatePaintSphereScale();
                        }
                        else
                        {
                            _PaintSphereScale = 0.0f;
                        }
                        UpdatePaintSphereModelScale();
                    }
                }
            }
        }

        public RayPickResult PointPick(Vector2f Point, bool bInstanceIntsersect = false, bool bTerrainOnly = false, bool bMeshIntersect = true)
        {
            if (GetScene().GetCameraEntity() != null)
            {
                Device Device = GetDevice();
                int MouseX = (int)Point.X;
                int MouseY = (int)Point.Y;
                int X = _Panel.GetScreenX();
                int Y = _Panel.GetScreenY();
                int Width = _Panel.GetWidth();
                int Height = _Panel.GetHeight();
                int X1 = MouseX - X;
                int Y1 = MouseY - Y;
                EditorScene EditorScene = GetEditorScene();
                Vector3d RayStart;
                Vector3d RayEnd;
                SphereTilePosition = EditorScene.ScreenToWorld(X1, Y1, Width, Height, out RayStart, out RayEnd);
                RayPickFlag Flag = RayPickFlag.All ^ RayPickFlag.Gizmo ^ RayPickFlag.Icon;
                if (!bInstanceIntsersect) Flag ^= RayPickFlag.Foliage;
                if (!bMeshIntersect) Flag ^= RayPickFlag.Model;
                if (bTerrainOnly) Flag = RayPickFlag.Terrain;
                return EditorScene.RayPick(ref RayStart, ref RayEnd, ref SphereTilePosition, Flag);
            }
            return new RayPickResult();
        }

        public RayPickResult CirclePick(bool bInstanceIntsersect = false, bool bTerrainOnly = false, bool bMeshIntersect = true, bool bGizmo = false, List<Entity> IgnoreEntities = null)
        {
            if (GetScene().GetCameraEntity() != null)
            {
                Device Device = GetDevice();
                int MouseX = Device.GetMouseX();
                int MouseY = Device.GetMouseY();
                int X = _Panel.GetScreenX();
                int Y = _Panel.GetScreenY();
                int Width = _Panel.GetWidth();
                int Height = _Panel.GetHeight();
                int X1 = MouseX - X;
                int Y1 = MouseY - Y;
                EditorScene EditorScene = GetEditorScene();
                Vector3d RayStart;
                Vector3d RayEnd;
                SphereTilePosition = EditorScene.ScreenToWorld(X1, Y1, Width, Height, out RayStart, out RayEnd);
                RayPickFlag Flag = RayPickFlag.All ^ RayPickFlag.Gizmo ^ RayPickFlag.Icon;
                if (!bInstanceIntsersect) Flag ^= RayPickFlag.Foliage;
                if (!bMeshIntersect) Flag ^= RayPickFlag.Model;
                if (bTerrainOnly) Flag = RayPickFlag.Terrain;
                return EditorScene.RayPick(ref RayStart, ref RayEnd, ref SphereTilePosition, Flag);
            }
            return new RayPickResult();
        }

        float CalculateEcotopeRadius()
        {
            float SphereRadius = _PaintSphereScale * SphereModelRadius;
            float SphereRadiusEcotope = SphereRadius * EcotopeScale;
            float EcotopeRadius = SphereRadiusEcotope / EcotopeGridSize;
            return EcotopeRadius;
        }

        void PaintEcotopeMap(int MouseX, int MouseY, uint Color, float Radius)
        {
            int X = _Panel.GetScreenX();
            int Y = _Panel.GetScreenY();
            int Width = _Panel.GetWidth();
            int Height = _Panel.GetHeight();
            int X1 = MouseX - X;
            int Y1 = MouseY - Y;
            EditorScene EditorScene = GetEditorScene();
            Vector3d RayStart = new Vector3d();
            Vector3d RayEnd = new Vector3d();
            if (EditorScene.GetCameraEntity() != null)
            {
                EditorScene.ScreenToWorld(X1, Y1, Width, Height, out RayStart, out RayEnd);
                Vector3d HitPoint = new Vector3d();
                Vector3d Direction = RayEnd - RayStart;
                Direction.Normalize();
                // Runtime.EditorPCG_EcotopeMap_Ray(ref RayStart, ref Direction, ref HitPoint);
                int Width1 = 1024;
                int Height1 = 1024;
                int i = (int)(HitPoint.X / EcotopeGridSize);
                int j = Width1 - (int)(HitPoint.Z / EcotopeGridSize);
                int Size = (int)(Radius + 1.0f);
                for (int i1 = -Size; i1 <= Size; i1++)
                {
                    for (int j1 = -Size; j1 <= Size; j1++)
                    {
                        int i3 = i + i1;
                        int j3 = j + j1;
                        if (i3 >= 0 && i3 < Width1)
                        {
                            if (j3 >= 0 && j3 < Height1)
                            {
                                if (i1 * i1 + j1 * j1 < Radius * Radius)
                                {
                                    //Runtime.EditorPCG_EcotopeMap_SetPixel(i3, j3, Color);
                                    //_bEcotopeMapDirty = true;
                                }
                            }
                        }
                    }
                }
            }
        }

        void PaintEcotopeMap(int MouseX, int MouseY)
        {
            Device Device = GetDevice();
            bool bControl = Device.IsControlDown();
            bool bShift = Device.IsShiftDown();
            bool bAlt = Device.IsAltDown();
            bool bShiftOnly = !bControl && bShift && !bAlt;

            float Radius = CalculateEcotopeRadius();
            if ((_PaintMode == PaintMode.Editor) ^ bShiftOnly)
            {
                PaintEcotopeMap(MouseX, MouseY, 0xFFFFFFFF, Radius);
            }
            else
            {
                PaintEcotopeMap(MouseX, MouseY, 0x00000000, Radius);
            }
            GetEditorScene().SetDirty();
        }

        void UpdatePaintSphereDirty(int MouseX, int MouseY)
        {
            if (_PaintSphereSavedMouseX != MouseX || _PaintSphereSavedMouseY != MouseY)
            {
                _bPaintSphereDirty = true;
                _PaintSphereSavedMouseX = MouseX;
                _PaintSphereSavedMouseY = MouseY;
            }
        }

        #region Panel Event

        protected override void OnPanelLeftMouseDown(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            Sender.CaptureMouse();
            _bLeftMouseDown = true;

            Device Device = GetDevice();
            bool bControl = Device.IsControlDown();
            bool bShift = Device.IsShiftDown();
            bool bAlt = Device.IsAltDown();
            bool bAltOnly = !bControl && !bShift && bAlt;

            if (_CurrentMode == EditorMode.Object_Mode)
            {
                EditorScene EditorScene = GetEditorScene();
                _EditOperation_SelectEntities = EditorScene.BeginSelectEntities();

                if (!bAltOnly)
                {
                    RayPickResult RayPickResult = RayPick(RayPickFlag.Gizmo);
                    GeometryTag HitTag = RayPickResult.HitTag;
                    if (HitTag != GeometryTag.Default)
                    {
                        _bManipulating = true;
                        SetHighlightByHitTag(HitTag);
                        OnManipulatorMouseDown(RayPickResult, MouseX, MouseY);
                    }
                }
            }
            else if (_CurrentMode == EditorMode.Paint_Mode)
            {
                //if (_PaintInstanceMode == PaintInstanceMode.Ectope)
                //{
                //    PaintEcotopeMap(MouseX, MouseY);
                //}
                //else
                //{
                //    List<ListViewItem> listViewItems = PaintToolEditor.GetInstance().GetListViewItems();

                //    if (listViewItems.Count == 0)
                //    {
                //        if (!DialogUIManager.GetInstance().ShowingDialogUI())
                //        {
                //            CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Paint Fail", "Please select a object in listview UI.");
                //            return;
                //        }
                //    }

                //    Entity Root = GetEditorScene().GetRoot();
                //    foreach (var Item in listViewItems)
                //    {
                //        Entity Entity = Root.SearchChildByEUID(((AddInfo)(Item.Tag)).EUID);
                //        if (Entity == null) continue;
                //        FoliageComponent FoliageComponent = Entity.GetFoliageComponent();
                //        if (!FoliageComponent.GetIsFoliageBVHGenerate())
                //        {
                //            _IsAllGeneBVHInstance = false;
                //            break;
                //        }
                //        else
                //        {
                //            _IsAllGeneBVHInstance = true;
                //        }
                //    }

                //    if (DisplayGeneBVHDialog()) return;
                //    foreach (var Item in listViewItems)
                //    {
                //        Entity Entity = Root.SearchChildByEUID(((AddInfo)(Item.Tag)).EUID);
                //        if (Entity == null) continue;
                //        if (!_InstanceMap.ContainsKey(Entity.GetName()))
                //        {
                //            _InstanceMap.Add(Entity.GetName(), Entity);
                //        }
                //    }
                //    ClusterObject(MouseX, MouseY, 0);
                //}
            }
            else if (_CurrentMode == EditorMode.Terrain_Mode)
            {
                TerrainEditor.GetInstance().OnPanelLeftMouseDown(Sender, MouseX, MouseY, ref bContinue);
            }

            _SavedMouseX = MouseX;
            _SavedMouseY = MouseY;
        }

        protected override void OnPanelLeftMouseUp(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (_CurrentMode == EditorMode.Object_Mode)
            {
                _bSelectd = true;
                base.OnPanelLeftMouseUp(Sender, MouseX, MouseY, ref bContinue);
                if (_EditOperation_SelectEntities != null)
                {
                    GetEditorScene().EndSelectEntities(_EditOperation_SelectEntities);
                    _EditOperation_SelectEntities = null;
                }
            }
            else if (_CurrentMode == EditorMode.Paint_Mode)
            {
                if (_bLeftMouseDown)
                {
                    _bSelectd = false;
                    base.OnPanelLeftMouseUp(Sender, MouseX, MouseY, ref bContinue);
                }
            }
            else if (_CurrentMode == EditorMode.Terrain_Mode)
            {
                if (_bLeftMouseDown)
                {
                    _bSelectd = false;
                    base.OnPanelLeftMouseUp(Sender, MouseX, MouseY, ref bContinue);
                    TerrainEditor.GetInstance().OnPanelLeftMouseUp(Sender, MouseX, MouseY, ref bContinue);
                }
            }
        }

        protected override void OnPanelMouseMove(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            Device Device = GetDevice();
            bool bControl = Device.IsControlDown();
            bool bShift = Device.IsShiftDown();
            bool bAlt = Device.IsAltDown();
            bool bShiftOnly = !bControl && bShift && !bAlt;

            UpdatePaintSphereDirty(MouseX, MouseY);
            if (_CurrentMode == EditorMode.Object_Mode)
            {
                base.OnPanelMouseMove(Sender, MouseX, MouseY, ref bContinue);
            }
            else if (_CurrentMode == EditorMode.Paint_Mode)
            {
                _bEnableFrustumPicking = false;

                base.OnPanelMouseMove(Sender, MouseX, MouseY, ref bContinue);
                if (_bLeftMouseDown)
                {
                    if (_PaintInstanceMode == PaintInstanceMode.Ectope)
                    {
                        PaintEcotopeMap(MouseX, MouseY);
                    }
                    else
                    {
                        if (_PaintMode == PaintMode.Editor)
                        {
                            if (_PreviousTime == 0)
                            {
                                ClusterObject(MouseX, MouseY, 0);
                                _PreviousTime = DateTime.Now.Ticks / TimeSpan.TicksPerMillisecond;
                            }
                            else
                            {
                                long _CurrentTime = DateTime.Now.Ticks / TimeSpan.TicksPerMillisecond;
                                if (_CurrentTime - _PreviousTime > IntervalTime)
                                {
                                    ClusterObject(MouseX, MouseY, 0);
                                    _PreviousTime = _CurrentTime;
                                }
                            }

                        }
                        else
                        {
                            if (_PreviousPaintTime == 0)
                            {
                                ClusterObject(MouseX, MouseY, 1);
                                _PreviousPaintTime = DateTime.Now.Ticks / TimeSpan.TicksPerMillisecond;
                            }
                            else
                            {
                                long _CurrentTime = DateTime.Now.Ticks / TimeSpan.TicksPerMillisecond;
                                if (_CurrentTime - _PreviousTime > IntervalPaintTime)
                                {
                                    ClusterObject(MouseX, MouseY, 1);
                                    _PreviousPaintTime = _CurrentTime;
                                }
                            }

                        }
                    }
                    //_bLeftMouseDown = false;
                }

                //if (PaintToolEditor.GetInstance().IsEditorMode())
                //{
                //    if (bShiftOnly)
                //    {
                //        _PaintMode = PaintMode.Erase;
                //    }
                //    else
                //    {
                //        _PaintMode = PaintMode.Editor;
                //    }
                //    SwitchMaterial();
                //}

                _bEnableFrustumPicking = true;
            }
            else if (_CurrentMode == EditorMode.Terrain_Mode)
            {
                _bEnableFrustumPicking = false;
                base.OnPanelMouseMove(Sender, MouseX, MouseY, ref bContinue);
                _bEnableFrustumPicking = true;
                TerrainEditor.GetInstance().OnPanelMouseMove(Sender, MouseX, MouseY, ref bContinue);
            }
        }

        protected override void OnPanelMouseWheel(Control Sender, int MouseX, int MouseY, int MouseDeltaZ, int MouseDeltaW, ref bool bContinue)
        {
            base.OnPanelMouseWheel(Sender, MouseX, MouseY, MouseDeltaZ, MouseDeltaW, ref bContinue);

            if (_Panel.IsPointIn(MouseX, MouseY) && GetDevice().IsControlDownOnly() &&
                _CurrentMode == EditorMode.Paint_Mode && _PaintObjectMode == PaintObjectMode.Cluster)
            {
                _CurrentCircleR *= 1f + MouseDeltaZ * 0.1f;
                _CurrentCircleR = Math.Clamp(_CurrentCircleR, MinCircleR, MaxCircleR);

                _PaintSphereScale *= 1f + MouseDeltaZ * 0.1f;
                _PaintSphereScale = Math.Clamp(_PaintSphereScale, SphereMinScale, SphereMaxScale);
                UpdatePaintSphereModelScale();
                //PaintToolEditor.GetInstance().SetTrackBarValue((_PaintSphereScale - SphereMinScale) / (SphereMaxScale - SphereMinScale));
            }
        }

        protected override void OnPanelRightMouseDown(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            base.OnPanelRightMouseDown(Sender, MouseX, MouseY, ref bContinue);
            if (!_bEnablePaintSpere)
            {
                _bEnablePaintSpere = true;
            }
        }

        protected override void OnPanelRightMouseUp(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            base.OnPanelRightMouseUp(Sender, MouseX, MouseY, ref bContinue);
            if (_bEnablePaintSpere)
            {
                _bEnablePaintSpere = false;
            }
        }

        #endregion

        void UpdatePaintSphereModelScale()
        {
            if (_PaintSphere != null)
            {
                float PaintSphereScale = _PaintSphereScale;
                if (_PaintInstanceMode == PaintInstanceMode.Ectope)
                {
                    PaintSphereScale = _PaintSphereScale * EcotopeScale;
                }
                if (_CurrentMode == EditorMode.PCG_Mode)
                {
                    PaintSphereScale *= _PaintSphereExtraScale;
                }
                Transform Component = _PaintSphere.GetTransformComponent();
                Component.Scale = new Double3(PaintSphereScale, PaintSphereScale, PaintSphereScale);
            }
        }

        void OnCameraPanelPaint(Control Sender)
        {
            GetEditorScene().DrawCameraPreviewScene(Sender.GetUIManager());
        }

        public void DoAddEntity(Entity ParentEntity, Entity NewEntity, string ReferenceName)
        {
            ParentEntity.AddChildEntity(NewEntity);
            NewEntity.SetName(GetEditorScene().CalculateNewName(ParentEntity, ReferenceName));
        }

        void EndAddEntity(Entity Entity)
        {
            EditOperation_AddChildEntity EditOperation = new EditOperation_AddChildEntity(Entity.Parent, Entity);
            EditOperationManager.GetInstance().AddOperation(EditOperation);
            HierarchyUI HierarchyUI = HierarchyUI.GetInstance();
            HierarchyUI.UpdateHierarchy();
            HierarchyUI.SelectEntity(Entity);
        }

        void DropStaticMesh(Entity EntityToDrop, string ndapath, string filename)
        {
            World world = GetScene().GetWorld();
            GOContext GoContext = new GOContext(world._WorldInterface);
            GOHandle handle = new GOHandle();
            if (EntityToDrop != null)
            {
                var parent_go = GoContext.GetGameObject(EntityToDrop.EntityID);
                if (parent_go != null)
                {
                    handle.Parent = parent_go;
                }
                else
                {
                    EditorLogger.Log(LogMessageType.Error, "Can not create GameObject under Entity. The created GameObject will be move to root.");
                }
            }
            GoContext.CreateGameObject("cegf::StaticModel", handle);
            Clicross.GameWorldInterface.World_SetEntityName(world._WorldInterface, handle.EntityID, filename);
            GetScene().GetWorld().Root.RefreshTree();
            HierarchyUI.GetInstance().UpdateHierarchy();
            Clicegf.ModelComponent model = (Clicegf.ModelComponent)GoContext.GetGameObject(handle.EntityID).GetComponentByMetaClassName("cegf::ModelComponent");
            model.SetModelAssetPath(ndapath);
        }
        public override void DropPathes(Entity EntityToDrop, bool bCalculateDropLocation)
        {
            GetEditorScene().SetDirty();
            World World = GetEditorScene().GetWorld();
            ProjectUI ProjectUI = ProjectUI.GetInstance();
            List<string> PathesDragged = ProjectUI.GetPathesDragged();
            //bool bHasModel = false;
            //foreach (string PathDragged in PathesDragged)
            //{
            //    if (ProjectUI.IsPathModelAsset(PathDragged))
            //    {
            //        bHasModel = true;
            //    }
            //}

            SimpleDelegate SimpleDelegate = async () =>
            {
                foreach (string PathDragged in PathesDragged)
                {
                    string Extension = PathHelper.GetExtensionOfPath(PathDragged);
                    bool bIsWorld = StringHelper.IgnoreCaseEqual(Extension, "world");
                    bool bIsModel = ProjectUI.IsPathModelAsset(PathDragged);
                    bool bIsNda = StringHelper.IgnoreCaseEqual(Extension, "nda");
                    bool bIsUsd = StringHelper.IgnoreCaseEqual(Extension, "usd") || StringHelper.IgnoreCaseEqual(Extension, "usda");
                    bool bIsPrefab = StringHelper.IgnoreCaseEqual(Extension, "prefab") || StringHelper.IgnoreCaseEqual(Extension, "model");
                    bool bIsParticleSystem = StringHelper.IgnoreCaseEqual(Extension, "system");
                    if (bIsWorld)
                    {
                        GetEditorScene().LoadScene(PathDragged);
                        return;
                    }
                    if (bIsNda)
                    {
                        string WorldPath = PathDragged.Replace(".nda", ".world");
                        if (FileHelper.IsFileExists(WorldPath))
                        {
                            GetEditorScene().LoadScene(WorldPath);
                            return;
                        }
                    }
                    if (bIsPrefab)
                    {
                        long Start = SystemHelper.GetTimeMs();
                        Entity Prefab = EntityToDrop.AddChildByPrefab(PathDragged, false);
                        if (Prefab != null)
                        {
                            Transform Transform = Prefab.GetTransformComponent();
                            if (Transform != null)
                            {
                                Transform.Translation = _DropPoint;
                            }
                            HierarchyUI.GetInstance().UpdateHierarchy();
                        }
                        long End = SystemHelper.GetTimeMs();
                        Console.WriteLine("Spend Times : {0}", End - Start);
                        continue;
                    }

                    Entity EntityDropped = null;
                    if (bIsNda || bIsModel)
                    {
                        string Directory = PathHelper.GetDirectoryName(PathDragged);
                        string NDAPath = PathDragged;
                        if (bIsModel)
                        {
                            string FileName = PathHelper.GetNameOfPath(PathDragged);
                            NDAPath = Directory + "/" + FileName + ".nda";
                        }
                        if (FileHelper.IsFileExists(NDAPath) == false)
                        {
                            AssetImportResult ImportResult = await ProjectUI.DoAssetImport(Directory, PathDragged);
                            bool bResetScroll = false;
                            ProjectUI.RefreshListView(bResetScroll);
                        }
                        string NDAPath_Relative = EditorUtilities.EditorFilenameToStandardFilename(NDAPath);
                        ClassIDType ObjectClassID = Resource.GetResourceTypeStatic(NDAPath_Relative);
                        if (ObjectClassID == ClassIDType.CLASS_MeshAssetDataResource)
                        {
                            DropStaticMesh(EntityToDrop, NDAPath_Relative, PathHelper.GetNameOfPath(PathDragged));
                        }
                        else if (ObjectClassID == ClassIDType.CLASS_Material || ObjectClassID == ClassIDType.CLASS_Fx)
                        {
                            if (_DropRayPickResult != null)
                            {
                                Entity HitEntity = _DropRayPickResult.HitEntity;
                                if (HitEntity != null)
                                {
                                    if (Extension.Contains("nda", StringComparison.OrdinalIgnoreCase))
                                    {
                                        ClassIDType ObjectClassID2 = Resource.GetResourceTypeStatic(PathDragged);
                                        if (EditorUtilities.IsSpecificAsset(ObjectClassID2, ClassIDType.CLASS_Material) || EditorUtilities.IsSpecificAsset(ObjectClassID2, ClassIDType.CLASS_Fx))
                                        {
                                            ModelComponent ModelComponent = HitEntity.GetModelComponent();
                                            if (ModelComponent != null)
                                            {
                                                int ModelIndex = _DropRayPickResult.ModelIndex;
                                                int MeshIndex = _DropRayPickResult.MeshIndex;
                                                Model Model = ModelComponent.Models[ModelIndex];
                                                SubModelProperty SubModelProperty = Model.LODProperties[0].SubModels[MeshIndex];
                                                EditOperation_ChangeMaterial EditOperation = new EditOperation_ChangeMaterial(HitEntity, ModelIndex, MeshIndex, 0, SubModelProperty.MaterialPath, PathDragged);
                                                EditOperationManager.GetInstance().AddOperation(EditOperation);
                                                SubModelProperty.MaterialPath = PathDragged;
                                                ModelComponent.Models = ModelComponent.Models;
                                                InspectorUI.GetInstance().InspectObject();
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (bIsParticleSystem)
                    {
                        string PathRelative = EditorUtilities.EditorFilenameToStandardFilename(PathDragged);
                        string FileName = Path.GetFileNameWithoutExtension(PathRelative);
                        Entity Entity = DoCreateEntity(FileName, EntityToDrop == null, EntityToDrop);
                        ParticleSystemResource ParticleSystemResource = Resource.Get(PathDragged, true) as ParticleSystemResource;
                        if (Entity != null && ParticleSystemResource != null)
                        {
                            Transform Transform = Entity.CreateComponent<Transform>();
                            Transform.Scale = new Vector3f(1.0f, 1.0f, 1.0f);
                            ParticleSystemComponent ParticleComponent = Entity.CreateComponent<ParticleSystemComponent>();
                            ParticleComponent.SystemInfo = ParticleSystemResource.SystemInfo.SystemResourceSlot;
                            Entity.RuntimeJointToParent();
                            EndAddEntity(Entity);
                        }
                    }

                    var processors = CrossEngine.GetInstance().PathDragProcessors;
                    foreach (var processor in processors)
                    {
                        processor.DoProcess(PathDragged, EntityToDrop, _DropPoint);
                    }

                    if (EntityDropped != null)
                    {
                        EntityDropped.SetName(GetEditorScene().CalculateNewName(EntityToDrop, EntityDropped.GetName()));
                        EntityDropped.AfterAdd();
                        if (bCalculateDropLocation)
                        {
                            Transform Transform = EntityDropped.GetTransformComponent();
                            Transform.Translation = CalculateDropLocation();
                        }
                        EndAddEntity(EntityDropped);
                    }

                }
            };

            //if (bHasModel)
            //{
            //    ProjectUI.ShowModelImportUI(() =>
            //    {
            //        SimpleDelegate();
            //    });
            //}
            //else
            {
                SimpleDelegate();
            }
        }

        void GenerateEnitity(Vector2f ScreenPoint, Vector3d GeneratePoint, Vector3d GenerateFaceNormal, string NDAPath_Relative, ClusterObjectDataInfo Info)
        {
            string fileName = Path.GetFileNameWithoutExtension(NDAPath_Relative);

            Entity Root = GetEditorScene().GetRoot();
            Entity EntityFolder = Root.FindChildByName(fileName);
            if (EntityFolder == null)
            {
                EntityFolder = DoCreateEntity(fileName, true);
                if (EntityFolder != null)
                {
                    EntityFolder.bFolder = true;
                    EntityFolder.CreateComponent<Transform>();
                    EntityFolder.SetName(fileName);
                    Clicross.GameWorldInterface.Entity_SetFolderBool(EntityFolder.World._WorldInterface, EntityFolder.EntityID, EntityFolder.bFolder);
                    EntityFolder.bExpand = false;
                    Clicross.GameWorldInterface.Entity_SetExpandBool(EntityFolder.World._WorldInterface, EntityFolder.EntityID, EntityFolder.bExpand);
                    EntityFolder.RuntimeJointToParent();
                    //ClusterEndAddEntity(EntityFolder);
                    HierarchyUI.GetInstance().UpdateHierarchy();
                }
            }

            Entity Entity = DoCreateEntity(fileName, EntityFolder == null, EntityFolder);
            if (Entity != null)
            {
                Transform Transform = GetClusterTransform(Entity, Info, GeneratePoint, GenerateFaceNormal, true);

                ModelComponent ModelComponent = Entity.CreateComponent<ModelComponent>();
                List<Model> ModelList = new List<Model>();
                ModelList.Add(new Model(NDAPath_Relative));
                ModelComponent.Models = ModelList;
                ModelComponent.Entity = Entity;
                //ModelComponent.SetSameComponentMaterial(PaintEnity.GetModelComponent());
                ModelComponent.SetDefaultMaterial();
                ModelComponent.GPUSkin = true;
                LightMapBaker LightMap = Entity.CreateComponent<LightMapBaker>();
                LightMap.Entity = Entity;
                LightMap.ApplyLightmap = true;
                LightMap.ApplyShadowmap = true;
                Entity.RuntimeJointToParent();
                ClusterEndAddEntity(Entity);
                //_PaintTriMap.Add(Entity, GeneratePoint);
                Vector3d translation = new Vector3d(Transform.Translation.x, Transform.Translation.y, Transform.Translation.z);
                _PaintTriMap.Add(Entity, translation);
                _PaintSizeMap.Add(Entity, RandomNumber.GetInstance().GenerateUniformRandomSize(
                    new Vector3d(Info.ScaleX + size, Info.ScaleY + size, Info.ScaleZ + size), Info.SizeVariation));
                _EntityPathList.Add(fileName);
                ListViewItem[] itemsKey = new ListViewItem[_PaintItemSize.Count];
                _PaintItemSize.Keys.CopyTo(itemsKey, 0);
                for (int i = 0; i < _PaintItemSize.Count; i++)
                {
                    if (itemsKey[i].Name == fileName)
                    {
                        DicPlusOrSub(itemsKey[i], 0);
                    }
                }
            }
        }

        RTSData GenerateInstance(Vector3d GeneratePoint, Vector3d GenerateFaceNormal, string NDAPath_Relative, ClusterObjectDataInfo Info, Entity InstanceEntity)
        {
            Entity Entity;
            string fileName = Path.GetFileNameWithoutExtension(NDAPath_Relative);
            RTSData RTSData = new RTSData();
            if (InstanceEntity != null)
            {
                Entity TempEntity;
                _InstanceMap.TryGetValue(InstanceEntity.GetName(), out TempEntity);
                if (TempEntity == null)
                {
                    _InstanceMap.Add(InstanceEntity.GetName(), InstanceEntity);
                    Entity = InstanceEntity;
                }
            }
            if (InstanceEntity != null)
            {
                Entity = InstanceEntity;
                Matrix4x4d Matrix4x4d = new Matrix4x4d();
                Entity.GetTransformComponent().GetWorldMatrix(ref Matrix4x4d);
                Vector3d Relative = GeneratePoint;
                Matrix4x4d.Inverse().Transform(ref Relative);
                RTSData = GetInstanceTransformNew(Info, Relative, GenerateFaceNormal, false);
            }
            return RTSData;
        }

        void SetFoliageInstanceData(Entity Entity, List<RTSData> RTSDatas)
        {
            //FoliageComponentInstanceData instanceData = FoliageSystemG.GetEditorInstanceData(Entity.World.GetNativePointer(), Entity.EntityID);
            FoliageComponentInstanceData instanceData = new FoliageComponentInstanceData();
            vector_int IndexVector = new vector_int();

            if (Entity == null || Entity.World == null) return;

            uint DataSize = FoliageSystemG.GetEditorInstanceDataSize(Entity.World.GetNativePointer(), Entity.EntityID);
            int index = 0;
            foreach (var RTSData in RTSDatas)
            {
                FoliageComponentTransform transform = new FoliageComponentTransform();
                transform.mTranslation.x = (float)RTSData.Translation.X;
                transform.mTranslation.y = (float)RTSData.Translation.Y;
                transform.mTranslation.z = (float)RTSData.Translation.Z;
                transform.mScale.x = (float)RTSData.Scale.X;
                transform.mScale.y = (float)RTSData.Scale.Y;
                transform.mScale.z = (float)RTSData.Scale.Z;
                Quaterniond quaternionf = Quaterniond.FromEuler(RTSData.Rotation.X, RTSData.Rotation.Y, RTSData.Rotation.Z);
                transform.mRotation.x = (float)quaternionf.X;
                transform.mRotation.y = (float)quaternionf.Y;
                transform.mRotation.z = (float)quaternionf.Z;
                transform.mRotation.w = (float)quaternionf.W;
                transform.mDelete = RTSData.Delete;
                instanceData.mInstanceData.Add(transform);
                IndexVector.Add((int)DataSize + index);
                ++index;
            }
            FoliageSystemG.AddEditorInstanceData(Entity.World.GetNativePointer(), Entity.EntityID, instanceData, IndexVector);
        }


        Entity DoCreateEntity(string NamePrefix, bool bForceRootEntity, Entity Parent = null)
        {
            Entity ParentEntity = bForceRootEntity ? GetEditorScene().GetRoot() : Parent;
            if (ParentEntity == null)
            {
                return null;
            }
            string ReferenceName = NamePrefix;
            Entity NewEntity = ParentEntity.World.CreateEntity();
            NewEntity.CreateComponent<Transform>();//At least one transform Component
            DoAddEntity(ParentEntity, NewEntity, ReferenceName);
            return NewEntity;
        }

        void DropPathes()
        {
            World World = GetEditorScene().GetWorld();
            Entity EntityToDrop = World.Root;

            RayPickResult RayPickResult;
            if (_CurrentMode == EditorMode.Object_Mode)
            {
                RayPickResult = RayPick(RayPickFlag.All ^ RayPickFlag.Gizmo);
            }
            else
            {
                RayPickResult = CirclePick(true);
            }
            _DropRayPickResult = RayPickResult;
            _DropPoint = RayPickResult.HitPoint;

            bool bCalculateDropLocation = true;
            DropPathes(EntityToDrop, bCalculateDropLocation);
        }

        protected override void OnDragDropManagerDragEnd(DragDropManager Sender, UIManager UIManager, int MouseX, int MouseY, ref bool bContinue)
        {
            if (UIManager != GetUIManager())
            {
                return;
            }

            if (!IsDockingCardActive())
                return;

            ProjectUI ProjectUI = ProjectUI.GetInstance();
            if (ProjectUI.IsPathesDragging())
            {
                if (_Panel.IsPointIn(MouseX, MouseY))
                {
                    DropPathes();
                }
            }
        }

        public PhysicsSceneDebugViewOption PhysicsDebugViewOption => _PhysicsDebugViewOption;

        private void RefreshPhysicsDebugShowOptionMenuText()
        {
            _ShowOptionPhysicsCollision.SetText("Collision: " + (_PhysicsDebugViewOption.showCollision ? "On" : "Off"));
            _ShowOptionPhysicsJointsLimit.SetText("Joints Limits: " + (_PhysicsDebugViewOption.showJointLimit ? "On" : "Off"));
            _ShowOptionPhysicsJointsFrame.SetText("Joints Frame: " + (_PhysicsDebugViewOption.showJointFrame ? "On" : "Off"));
            _ShowOptionPhysicsRaycast.SetText("Raycast: " + (_bShowOptionPhysicsRaycast ? "On" : "Off"));
        }

        private void OnShowCollisionClicked(MenuItem Sender)
        {
            _PhysicsDebugViewOption.showCollision = !_PhysicsDebugViewOption.showCollision;
            GetEditorScene().SetPhysicsDebugViewOption(_PhysicsDebugViewOption);
            RefreshPhysicsDebugShowOptionMenuText();
        }

        private void OnShowJointsLimitClicked(MenuItem Sender)
        {
            _PhysicsDebugViewOption.showJointLimit = !_PhysicsDebugViewOption.showJointLimit;
            GetEditorScene().SetPhysicsDebugViewOption(_PhysicsDebugViewOption);
            RefreshPhysicsDebugShowOptionMenuText();
        }

        private void OnShowJointsFrameClicked(MenuItem Sender)
        {
            _PhysicsDebugViewOption.showJointFrame = !_PhysicsDebugViewOption.showJointFrame;
            GetEditorScene().SetPhysicsDebugViewOption(_PhysicsDebugViewOption);
            RefreshPhysicsDebugShowOptionMenuText();
        }

        private void OnShowPhysicsRaycastClicked(MenuItem Sender)
        {
            _bShowOptionPhysicsRaycast = !_bShowOptionPhysicsRaycast;
            GetEditorScene().SetShowPhysicsRaycastDebugInfo(_bShowOptionPhysicsRaycast);
            RefreshPhysicsDebugShowOptionMenuText();
        }

        private void OnDebugShaderConstClicked(MenuItem Sender)
        {
            bool status;
            string preText = Sender.GetText();
            if (preText.EndsWith(": False"))
            {
                string curText = preText.Replace(": False", ": True");
                Sender.SetText(curText);
                status = true;
            }
            else
            {
                string curText = preText.Replace(": True", ": False");
                Sender.SetText(curText);
                status = false;
            }
            GetEditorScene().LightmapSetDebugShaderConst(_BakeMenuItem[Sender], status);
        }

        private MenuItem BuildShowOptionPhysicsMenuItem()
        {
            MenuItem Item_Physics = new MenuItem();
            Item_Physics.SetText("Physics");

            Menu Menu_Physics = new Menu(GetUIManager());
            Menu_Physics.Initialize();

            _ShowOptionPhysicsCollision = new MenuItem();
            _ShowOptionPhysicsCollision.ClickedEvent += OnShowCollisionClicked;
            Menu_Physics.AddMenuItem(_ShowOptionPhysicsCollision);
            _ShowOptionPhysicsJointsLimit = new MenuItem();
            _ShowOptionPhysicsJointsLimit.ClickedEvent += OnShowJointsLimitClicked;
            Menu_Physics.AddMenuItem(_ShowOptionPhysicsJointsLimit);
            _ShowOptionPhysicsJointsFrame = new MenuItem();
            _ShowOptionPhysicsJointsFrame.ClickedEvent += OnShowJointsFrameClicked;
            Menu_Physics.AddMenuItem(_ShowOptionPhysicsJointsFrame);
            _ShowOptionPhysicsRaycast = new MenuItem();
            _ShowOptionPhysicsRaycast.ClickedEvent += OnShowPhysicsRaycastClicked;
            Menu_Physics.AddMenuItem(_ShowOptionPhysicsRaycast);
            RefreshPhysicsDebugShowOptionMenuText();

            Item_Physics.SetMenu(Menu_Physics);
            return Item_Physics;
        }

        private MenuItem BuildShowOptionSkeletonMenuItem()
        {
            MenuItem Item_Skeleton = new MenuItem();
            Item_Skeleton.SetText("Skelton");

            Menu Menu_Skeleton = new Menu(GetUIManager());
            Menu_Skeleton.Initialize();

            MenuItem MenuItem_ShowSkeletonPhysics = new MenuItem();
            MenuItem_ShowSkeletonPhysics.SetText("Skeleton Physics: On");
            //MenuItem_ShowSkeletonPhysics.ClickedEvent += OnSkeletonPhysicsShowCilcied;
            Menu_Skeleton.AddMenuItem(MenuItem_ShowSkeletonPhysics);

            Item_Skeleton.SetMenu(Menu_Skeleton);
            return Item_Skeleton;
        }

        private MenuItem BuildShowOptionBakeMenuItem()
        {
            if (_BakeItem == null)
            {
                _BakeItem = new MenuItem();
                _BakeItem.SetText("Bake");

                Menu Menu_Bake = new Menu(GetUIManager());
                Menu_Bake.Initialize();

                List<string> ShaderConstList = new List<string> {
                    //display name, shader const name
                    "DEBUG_LM_GI_ONLY", "DEBUG_LM_GI_ONLY",
                    "DEBUG_LM_COLOR_ONLY", "DEBUG_LM_COLOR_ONLY",
                    "DISABLE_LM_GI", "DISABLE_LM_GI",
                    "DISABLE_BAKE_SHADOW", "DISABLE_BAKE_SHADOW",
                    "DISABLE_BAKE_NORMAL", "DISABLE_BAKE_NORMAL",
                    "SHOW_DEBUG_LM_UV", "SHOW_DEBUG_LM_UV",
                    "SHOW_DEBUG_NORMAL", "SHOW_DEBUG_NORMAL",
                };
                for (int i = 0; i < ShaderConstList.Count; i += 2)
                {
                    MenuItem menuItem = new MenuItem();
                    menuItem.ClickedEvent += OnDebugShaderConstClicked;
                    menuItem.SetText(ShaderConstList[i] + ": " + "False");
                    Menu_Bake.AddMenuItem(menuItem);
                    _BakeMenuItem[menuItem] = ShaderConstList[i + 1];
                }

                _BakeItem.SetMenu(Menu_Bake);
            }
            return _BakeItem;
        }

        private MenuItem BuildViewModeMenuItem()
        {
            if (_ViewModeItem == null)
            {
                _ViewModeItem = new MenuItem();
                _ViewModeItem.SetText("ViewMode");

                Menu Menu_ViewMode = new Menu(GetUIManager());
                Menu_ViewMode.Initialize();

                List<string> EnumNameList = ReflectViewModeVisualizeType();
                foreach (string EnumName in EnumNameList)
                {
                    MenuItem MenuItem_ViewMode_ItemX = new MenuItem();
                    MenuItem_ViewMode_ItemX.SetText(EnumName);
                    MenuItem_ViewMode_ItemX.ClickedEvent += OnMenuItemViewModeItemXClicked;
                    Menu_ViewMode.AddMenuItem(MenuItem_ViewMode_ItemX);
                }

                _ViewModeItem.SetMenu(Menu_ViewMode);
            }
            return _ViewModeItem;
        }

        public ViewModeVisualizeType GetViewModeVisualizeType()
        {
            RenderPipelineEditor RenderPipelineEditor = RenderPipelineEditor.GetInstance();
            RenderPipelineSetting RenderPipelineSetting = RenderPipelineEditor.GetSetting();
            FFSRenderPipelineSetting FFSRenderPipelineSetting = RenderPipelineSetting as FFSRenderPipelineSetting;
            if (FFSRenderPipelineSetting != null)
            {
                return FFSRenderPipelineSetting.viewModeVisualizeType;
            }
            return ViewModeVisualizeType.Lit;
        }

        public void SetViewModeVisualizeType(ViewModeVisualizeType ViewModeVisualizeType)
        {
            RenderPipelineEditor RenderPipelineEditor = RenderPipelineEditor.GetInstance();
            RenderPipelineSetting RenderPipelineSetting = RenderPipelineEditor.GetSetting();
            FFSRenderPipelineSetting FFSRenderPipelineSetting = RenderPipelineSetting as FFSRenderPipelineSetting;
            if (FFSRenderPipelineSetting != null)
            {
                FFSRenderPipelineSetting.viewModeVisualizeType = ViewModeVisualizeType;
            }
            RenderPipelineEditor.UpdateRenderPipelineSettings();
        }

        private List<string> ReflectViewModeVisualizeType()
        {
            List<string> EnumNameList = new List<string>();
            Type Type = typeof(ViewModeVisualizeType);
            string[] EnumNames = Type.GetEnumNames();
            foreach (string EnumName in EnumNames)
            {
                EnumNameList.Add(EnumName);
            }
            return EnumNameList;
        }

        private ViewModeVisualizeType StringToViewModeVisualizeType(string String)
        {
            object EnumObject;
            if (Enum.TryParse(typeof(ViewModeVisualizeType), String, out EnumObject))
            {
                return (ViewModeVisualizeType)EnumObject;
            }
            return ViewModeVisualizeType.Lit;
        }

        private void OnMenuItemViewModeItemXClicked(MenuItem Sender)
        {
            string EnumName = Sender.GetText();
            ViewModeVisualizeType ViewModeVisualizeType = StringToViewModeVisualizeType(EnumName);
            SetViewModeVisualizeType(ViewModeVisualizeType);
        }


        // private void SetDisplayModeType(DisplayMode DisplayMode)
        // {
        //     RenderPipelineEditor RenderPipelineEditor = RenderPipelineEditor.GetInstance();
        //     RenderPipelineSetting RenderPipelineSetting = RenderPipelineEditor.GetSetting();
        //     FFSRenderPipelineSetting FFSRenderPipelineSetting = RenderPipelineSetting as FFSRenderPipelineSetting;
        //     if (FFSRenderPipelineSetting != null)
        //     {
        //         if (DisplayMode == DisplayMode.Wireframe)
        //         {
        //             FFSRenderPipelineSetting.ViewMode = ViewMode.WIREFRAME;
        //         }
        //         else
        //         {
        //             FFSRenderPipelineSetting.ViewMode = ViewMode.DEFAULT;
        //         }
        //         FFSRenderPipelineSetting.DisplayMode = DisplayMode;
        //     }
        //     RenderPipelineEditor.UpdateRenderPipelineSettings();
        // }



        private void OnButtonShowOptionClicked(Button Sender)
        {
            Menu MenuContextMenu = new Menu(GetUIManager());
            MenuContextMenu.Initialize();

            MenuItem MenuItem_ViewMode = BuildViewModeMenuItem();
            MenuContextMenu.AddMenuItem(MenuItem_ViewMode);

            MenuContextMenu.AddSeperator();

            MenuItem MenuItem_SwitchSelectedAABBShowing = new MenuItem();
            MenuItem_SwitchSelectedAABBShowing.SetText("Switch Selected AABB Showing");
            bool isshow = !EditorPrimitiveSystemG.IsShowSelectedAABB(GetEditorScene().GetWorld().GetNativePointer());
            MenuItem_SwitchSelectedAABBShowing.ClickedEvent +=
                S => EditorPrimitiveSystemG.SetShowSelectedAABB(GetEditorScene().GetWorld().GetNativePointer(), isshow);
            MenuContextMenu.AddMenuItem(MenuItem_SwitchSelectedAABBShowing);

            MenuItem MenuItem_SwitchIconsDrawing = new MenuItem();
            MenuItem_SwitchIconsDrawing.SetText("Switch Icons Showing");
            MenuItem_SwitchIconsDrawing.ClickedEvent += (S) =>
            {
                GetEditorScene().SetDrawIcons(!GetEditorScene().GetDrawIcons());
            };
            MenuContextMenu.AddMenuItem(MenuItem_SwitchIconsDrawing);

            MenuItem MenuItem_SwitchMeshBVHDrawing = new MenuItem();
            MenuItem_SwitchMeshBVHDrawing.SetText("Switch MeshBVH Showing");
            MenuItem_SwitchMeshBVHDrawing.ClickedEvent += (S) =>
            {
                GetEditorScene().SetDrawMeshBVH(!GetEditorScene().GetDrawMeshBVH());
            };
            MenuContextMenu.AddMenuItem(MenuItem_SwitchMeshBVHDrawing);

            MenuItem MenuItem_SwitchInstanceBVHDrawing = new MenuItem();
            MenuItem_SwitchInstanceBVHDrawing.SetText("Switch InstanceBVH Showing");
            MenuItem_SwitchInstanceBVHDrawing.SetEnable(true);
            MenuItem_SwitchInstanceBVHDrawing.ClickedEvent += (S) =>
            {
                GetEditorScene().SetDrawInstanceAABBTree(!GetEditorScene().GetDrawInstanceAABBTree());
            };
            MenuContextMenu.AddMenuItem(MenuItem_SwitchInstanceBVHDrawing);

            MenuItem MenuItem_SwitchTerrainBVHDrawing = new MenuItem();
            MenuItem_SwitchTerrainBVHDrawing.SetText("Switch TerrainBVH Showing");
            MenuItem_SwitchTerrainBVHDrawing.SetEnable(false);
            MenuItem_SwitchTerrainBVHDrawing.ClickedEvent += (S) =>
            {
                GetEditorScene().SetDrawTerrainAABBTree(!GetEditorScene().GetDrawTerrainAABBTree());
            };
            MenuContextMenu.AddMenuItem(MenuItem_SwitchTerrainBVHDrawing);

            MenuItem MenuItem_NoDrawMode = new MenuItem();
            MenuItem_NoDrawMode.SetText("Toggle No DrawWorld Mode");
            MenuItem_NoDrawMode.ClickedEvent += (S) =>
            {
                CrossEngineApi.ToggleNoDrawMode();
            };
            MenuContextMenu.AddMenuItem(MenuItem_NoDrawMode);
            MenuItem MenuItem_ShowMemStats = new MenuItem();
            MenuItem_ShowMemStats.SetText("Switch GPU Memory Stats");
            MenuItem_ShowMemStats.ClickedEvent += (S) =>
            {
                ScreenTerminalSystemG.ToggleShowMemStats(GetEditorScene().GetWorld().GetNativePointer());
            };
            MenuContextMenu.AddMenuItem(MenuItem_ShowMemStats);

            MenuContextMenu.AddSeperator();

            MenuItem MenuItem_ShowReflectionProbes = new MenuItem();
            MenuItem_ShowReflectionProbes.SetText("Switch State of Showing Reflection probes");
            MenuItem_ShowReflectionProbes.ClickedEvent +=
                S => CrossEngineApi.SetReflectionProbeShowState(GetEditorScene().GetWorld().GetNativePointer(),
                !CrossEngineApi.IsReflectionProbeShow(GetEditorScene().GetWorld().GetNativePointer()));
            MenuContextMenu.AddMenuItem(MenuItem_ShowReflectionProbes);

            MenuContextMenu.AddSeperator();

            MenuItem MenuItem_Physics = BuildShowOptionPhysicsMenuItem();
            MenuContextMenu.AddMenuItem(MenuItem_Physics);

            MenuItem MenuItem_Skeleton = BuildShowOptionSkeletonMenuItem();
            MenuContextMenu.AddMenuItem(MenuItem_Skeleton);

            MenuItem MenuItem_Bake = BuildShowOptionBakeMenuItem();
            MenuContextMenu.AddMenuItem(MenuItem_Bake);

            MenuContextMenu.AddSeperator();
            //MenuItem MenuItem_GenrateKalpaTower = new MenuItem();
            //MenuItem_GenrateKalpaTower.SetText("GenerateKalpaTowerAndAddToWorld");
            //MenuItem_GenrateKalpaTower.ClickedEvent +=
            //    S => ProceduralSceneKaplaTower.GetInstance().GenerateKaplaTowerAndAddToWorld(GetEditorScene().GetWorld());
            //MenuContextMenu.AddMenuItem(MenuItem_GenrateKalpaTower);

            //MenuItem MenuItem_GreatWall = new MenuItem();
            //MenuItem_GreatWall.SetText("ProcedureGreatWall");
            //MenuItem_GreatWall.ClickedEvent += S => ProcedureGreatWall.GetInstance().GenerateProcedureGreatWall(GetEditorScene().GetWorld());
            //MenuContextMenu.AddMenuItem(MenuItem_GreatWall);

            //MenuContextMenu.AddSeperator();

            BookmarkUI.BuildBookmarkMenu(MenuContextMenu);

            MenuContextMenu.AddSeperator();
            List<Entity> Cameras = GetEditorScene().GetRoot().GetEntityListByBlurSearch("CineCamera");
            EditorUI.Texture CheckedMark = UIManager.LoadUIImage("Editor/Icons/Special/CheckedMark.png");
            foreach (var Value in Cameras)
            {
                MenuItem MenuItem_CineCamera = new MenuItem();
                MenuItem_CineCamera.SetText(Value.GetName());
                if (_CineCameraName == Value.GetName())
                {
                    MenuItem_CineCamera.SetImage(CheckedMark);
                }
                MenuItem_CineCamera.ClickedEvent += (Sender) =>
                {
                    if (_CineCameraName != "" && _CineCameraName == Value.GetName())
                    {
                        OnButtonExitPilotClicked(null);
                    }
                    else
                    {
                        ActivePiolotRelated(true, Value.GetName());

                        _bCameraView = true;
                        _ButtonCameraShake.SetHoverColor(Color.FromRGB(246, 105, 94));
                        _ButtonCameraShake.SetNormalColor(Color.FromRGB(246, 105, 94));

                        HierarchyUI.GetInstance().MoveEditorCameraToEntity(Value);
                        HierarchyUI.GetInstance().SetCurPiolotEntity(Value);
                        if (Value != null && Value.HasComponent(typeof(Camera)))
                        {
                            Camera Camera = (Camera)Value.GetComponent(typeof(Camera));
                            Camera.SetMainCamera();
                            CinematicUI.GetInstance().AddPreviewingCamera(Value);

                        }
                        _CineCameraName = Value.GetName();
                    }

                };
                MenuContextMenu.AddMenuItem(MenuItem_CineCamera);
            }

            GetUIManager().GetContextMenu().ShowMenu(MenuContextMenu, Sender);
        }

        public void OnButtonExitPilotClicked(Button Sender)
        {
            SwitchToEditorCamera();
            HierarchyUI.GetInstance().SetCurPiolotEntity(null);
            ActivePiolotRelated(false);
            _CineCameraName = "";
        }

        private void OnButtonCameraShakeClicked(Button Sender)
        {
            if (_bCameraView)
            {
                SwitchToEditorCamera();
            }
            else
            {
                _bCameraView = true;
                _ButtonCameraShake.SetHoverColor(Color.FromRGB(246, 105, 94));
                _ButtonCameraShake.SetNormalColor(Color.FromRGB(246, 105, 94));
                Entity CurPiolotEntity = HierarchyUI.GetInstance().GetCurPiolotEntity();
                if (CurPiolotEntity != null && CurPiolotEntity.HasComponent(typeof(Camera)))
                {
                    Camera Camera = (Camera)CurPiolotEntity.GetComponent(typeof(Camera));
                    Camera.SetMainCamera();
                }
            }
            _CineCameraName = "";
        }

        private void SwitchToEditorCamera()
        {
            _bCameraView = false;
            _ButtonCameraShake.SetHoverColor(Color.EDITOR_UI_EDIT_BACK_COLOR);
            _ButtonCameraShake.SetNormalColor(Color.EDITOR_UI_EDIT_BACK_COLOR);

            Camera EditorCamera = EditorScene.GetInstance().GetCameraEntityCamera();
            EditorCamera.SetMainCamera();
        }

        protected override void OnManipulatorMouseMove(int MouseX, int MouseY)
        {
            base.OnManipulatorMouseMove(MouseX, MouseY);

            Entity Selection = GetScene().GetSelection();
            if (Selection == null || Selection.IsRoot())
            {
                return;
            }
            GetScene().SetDirty();
        }

        void ClusterObject(int MouseX, int MouseY, int PaintStyle = 0)
        {
            //if (_Panel.IsPointIn(MouseX, MouseY))
            //{
            //    List<ListViewItem> listViewItems = PaintToolEditor.GetInstance().GetListViewItems();
            //    for (int i = 0; i < listViewItems.Count; i++)
            //    {
            //        if (!_PaintItemSize.ContainsKey(listViewItems[i]))
            //            _PaintItemSize.Add(listViewItems[i], 0);
            //    }
            //    if (_PaintMode == PaintMode.Editor)
            //    {
            //        if (listViewItems.Count == 0)
            //        {
            //            if (!DialogUIManager.GetInstance().ShowingDialogUI())
            //            {
            //                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Paint Fail", "Please select a object in listview UI.");
            //            }

            //            return;
            //        }

            //        ListViewItem[] itemsKey = new ListViewItem[_PaintItemSize.Count];
            //        _PaintItemSize.Keys.CopyTo(itemsKey, 0);
            //        for (int j = 0; j < _PaintItemSize.Count; j++)
            //        {
            //            if (listViewItems.Contains(itemsKey[j]))
            //            {
            //                if (itemsKey[j].Path.Contains(".ectope"))
            //                {

            //                }
            //                else
            //                {
            //                    RayPickResult RayPickCenter = CirclePick();
            //                    int Radius = GetScreenRadius(RayPickCenter);
            //                    CirclePoint CirclePoint = new CirclePoint(Radius, MouseX, MouseY);
            //                    Info = PaintToolEditor.GetInstance().GetClusterObject(itemsKey[j]);
            //                    if (_PaintObjectMode == PaintObjectMode.Single)
            //                    {
            //                        Result.Add(new Vector2f(MouseX, MouseY));
            //                    }
            //                    else
            //                    {
            //                        Result = CirclePoint.GetRandPoint(Info.BrushFlow, _CurrentCircleR);
            //                        Result.Add(new Vector2f(MouseX, MouseY));
            //                    }
            //                    _PaintSpeed = Info.ObjectSpeed / 10;
            //                    _CurrentPaintItem = itemsKey[j];

            //                }
            //            }
            //        }
            //    }
            //    else
            //    {
            //        if (listViewItems.Count == 0)
            //        {
            //            if (!DialogUIManager.GetInstance().ShowingDialogUI())
            //            {
            //                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Erase Fail", "Please select a object in listview UI.");
            //            }
            //            return;
            //        }
            //        //GetEditorScene().ClearSelection();
            //        RayPickResult RayPickCenter = CirclePick(true, false, false);

            //        int Radius = GetScreenRadius(RayPickCenter);
            //        Vector3f Point = new Vector3f((float)_PaintSphere.GetTransformComponent().Translation.x,
            //            (float)_PaintSphere.GetTransformComponent().Translation.y, (float)_PaintSphere.GetTransformComponent().Translation.z);

            //        _bEraseInstance = true;
            //        Entity CurPaintEntity;
            //        _InstanceMap.TryGetValue(listViewItems[0].Name, out CurPaintEntity);


            //        if (CurPaintEntity == null) return;
            //        int Count = GetEditorScene().World_IntersectSphere(CurPaintEntity.EntityID, Point, _CurrentCircleR, SphereTilePosition);
            //        _bEraseInstance = false;

            //        // clean not necessary code 
            //        if (_PaintInstanceMode == PaintInstanceMode.Instance)
            //        {
            //            ListViewItem[] itemsKey = new ListViewItem[_PaintItemSize.Count];
            //            _PaintItemSize.Keys.CopyTo(itemsKey, 0);
            //            for (int i = 0; i < _PaintItemSize.Count; i++)
            //            {
            //                if (listViewItems.Contains(itemsKey[i]))
            //                {
            //                    DicPlusOrSub(itemsKey[i], 1, Count);
            //                }
            //            }
            //        }
            //        //OnDeleteKeyDown();
            //    }
            //}
        }

        //void UpdateGenerate(List<Vector2f> result, string pathName)
        //{
        //    long UpdateGenerate1 = SystemHelper.GetTimeMs();
        //    Entity Root = GetEditorScene().GetRoot();
        //    List<RayPickResult> RayPickResults = new List<RayPickResult>();
        //    foreach (var Point in result)
        //    {
        //        RayPickResult RayPickResult = PointPick(Point, false);
        //        RayPickResults.Add(RayPickResult);
        //    }

        //    List<RTSData> RTSDatas = new List<RTSData>();
        //    Entity CurInstanceEntity = Root.SearchChildByEUID(((AddInfo)(_CurrentPaintItem.Tag)).EUID);
        //    foreach (var RayPickResult in RayPickResults)
        //    {
        //        if (RayPickResult != null && RayPickResult.HitEntity != null)
        //        {
        //            if (_PaintInstanceMode == PaintInstanceMode.Instance)
        //            {
        //                if (DisplayGeneBVHDialog()) return;
        //                if (_InstanceMap.Values.Contains<Entity>(RayPickResult.HitEntity))
        //                    return;

        //                RTSData RTSData = GenerateInstance(RayPickResult.HitPoint, RayPickResult.HitFaceNormal, _CurrentPaintItem.Path, Info, CurInstanceEntity);
        //                RTSDatas.Add(RTSData);
        //            }
        //        }
        //    }

        //    SetFoliageInstanceData(CurInstanceEntity, RTSDatas);
        //    //ListViewItem[] itemsKey = new ListViewItem[_PaintItemSize.Count];
        //    //_PaintItemSize.Keys.CopyTo(itemsKey, 0);
        //    for (int i = 0; i < _PaintItemSize.Count; i++)
        //    {
        //        ListViewItem Item = _PaintItemSize.Keys.ElementAt<ListViewItem>(i);
        //        if (Item.Name == pathName)
        //        {
        //            DicPlusOrSub(Item, 0, RTSDatas.Count());
        //        }
        //    }
        //}

        int GetScreenRadius(RayPickResult RayPickCenter)
        {
            double _CenterX;
            double _CenterY;
            WorldToScreen(ref RayPickCenter.HitPoint, out _CenterX, out _CenterY);
            Vector3d Edge = RayPickCenter.HitPoint + new Vector3d(0.0f, 0.0f, _CurrentCircleR);
            double _PointX;
            double _PointY;
            WorldToScreen(ref Edge, out _PointX, out _PointY);
            int Radius = (int)Math.Sqrt((_PointX - _CenterX) * (_PointX - _CenterX) + (_PointY - _CenterY) * (_PointY - _CenterY));
            return Radius;

        }

        public void SetEditorMode(EditorMode Mode)
        {
            _CurrentMode = Mode;
        }

        public EditorMode GetEditorMode()
        {
            return _CurrentMode;
        }

        public void SetPaintMode(int value)
        {
            switch (value)
            {
                case 0:
                    _PaintMode = PaintMode.Editor;
                    break;
                case 1:
                    _PaintMode = PaintMode.Erase;
                    break;
            }
        }

        public PaintMode GetPaintMode()
        {
            return _PaintMode;
        }

        public void SetPaintObjectMode(int value)
        {
            if (value == 0)
            {
                _PaintObjectMode = PaintObjectMode.Cluster;
            }
            else
            {
                _PaintObjectMode = PaintObjectMode.Single;
            }

        }

        public PaintObjectMode GetPaintObjectMode()
        {
            return _PaintObjectMode;
        }

        public void SetPaintInstanceMode(int value)
        {
            if (value == 0)
            {
                _PaintInstanceMode = PaintInstanceMode.Entity;
            }
            else
            {
                _PaintInstanceMode = PaintInstanceMode.Instance;
            }
        }

        public void SetPaintEcotopeMode(int value)
        {
            if (value == 0)
            {
                _PaintInstanceMode = PaintInstanceMode.Entity;
            }
            else
            {
                _PaintInstanceMode = PaintInstanceMode.Ectope;
            }
        }

        public PaintInstanceMode GetPaintInstanceMode()
        {
            return _PaintInstanceMode;
        }

        public Entity GetPaintSphere()
        {
            return _PaintSphere;
        }

        public bool EnablePaintSphere { set => _bEnablePaintSpere = value; get => _bEnablePaintSpere; }

        public float GetPaintSphereScale()
        {
            return _PaintSphereScale;
        }

        public void CreatePaintSphere()
        {
            DeletePaintSphere();

            Entity Root = GetEditorScene().GetRoot();
            Entity EntityEditorSphere = Root.FindChildByName("EditorSphere");
            if (EntityEditorSphere != null)
            {
                Entity Parent = EntityEditorSphere.Parent;
                Parent.RemoveChildEntity(EntityEditorSphere);
                EntityEditorSphere.RuntimeRemove();
            }

            Entity Entity = DoCreateEntity("EditorSphere", true);
            if (Entity != null)
            {
                ModelComponent ModelComponent = Entity.CreateComponent<ModelComponent>();
                List<Model> ModelList = new List<Model>();
                ModelList.Add(new Model("EngineResource/Model/Sphere.nda"));
                ModelComponent.Models = ModelList;
                ModelComponent.Entity = Entity;
                ModelComponent.SetDefaultMaterial();
                ModelComponent.Intersection = false;
                LightMapBaker LightMap = Entity.CreateComponent<LightMapBaker>();
                LightMap.Entity = Entity;
                LightMap.ApplyLightmap = true;
                LightMap.ApplyShadowmap = true;
                Entity.RuntimeJointToParent();
                _PaintSphere = Entity;
                UpdatePaintSphereModelScale();
                SetPaintSphereMaterial(true);
            }
        }

        public void DeletePaintSphere()
        {
            if (_PaintSphere == null || _PaintSphere.World == null)
            {
                return;
            }
            if (_PaintSphere != null)
            {
                Entity Parent = _PaintSphere.Parent;
                if (Parent != null)
                {
                    Parent.RemoveChildEntity(_PaintSphere);
                }
                _PaintSphere.RuntimeRemove();
                HierarchyUI.GetInstance().UpdateHierarchy();
                HierarchyUI.GetInstance().GetTree().SelectItem(null);
                _PaintSphere = null;
            }
        }

        public void SetPaintSphereMaterial(bool bPositive)
        {
            if (_PaintSphere != null && _PaintSphere.World != null)
            {
                if (bPositive)
                {
                    ModelSystemG.SetModelLodMaterialPath(_PaintSphere.World.GetNativePointer(), _PaintSphere.EntityID,
                            ClusterBluePath, 0, 0, (uint)0);
                }
                else
                {
                    ModelSystemG.SetModelLodMaterialPath(_PaintSphere.World.GetNativePointer(), _PaintSphere.EntityID,
                            ClusterRedPath, 0, 0, (uint)0);
                }
            }
        }

        public void SwitchMaterial()
        {
            if (_PaintSphere == null || _PaintSphere.World == null)
            {
                return;
            }
            if (_CurrentMode == EditorMode.Paint_Mode)
            {
                if (_PaintMode == PaintMode.Editor)
                {
                    SetPaintSphereMaterial(true);
                }
                else if (_PaintMode == PaintMode.Erase)
                {
                    SetPaintSphereMaterial(false);
                }
            }
        }

        public void SwitchObject()
        {
            if (_CurrentMode == EditorMode.Paint_Mode)
            {
                if (_PaintSphere != null)
                {
                    UpdatePaintSphereModelScale();
                    if (_PaintMode == PaintMode.Editor)
                    {
                        SetPaintSphereMaterial(true);
                    }
                    else
                    {
                        SetPaintSphereMaterial(false);
                    }
                }
            }
        }

        public void RemoveBrushMap(Entity Entity)
        {
            _PaintTriMap.Remove(Entity);
            _PaintSizeMap.Remove(Entity);
        }

        // to do modify for davy
        public void GetEquations(double a, double b, double c, ref double Theta, ref double Beta)
        {
            Theta = Math.Acos(b) * 180 / M_PI;
            double ACosValue = (Theta != 0.0f ? c / Math.Sin(Theta * M_PI / 180) : 0.0f);
            if (ACosValue > 1)
            {
                ACosValue = 1.0f;
            }
            else if (ACosValue < -1)
            {
                ACosValue = -1.0f;
            }
            double BetaTemp = Math.Acos(ACosValue) * 180 / M_PI;
            double FirstEquation = Math.Round(Math.Sin(Theta * M_PI / 180) * Math.Sin(BetaTemp * M_PI / 180), 2);
            if (FirstEquation == Math.Round(a, 2))
            {
                Beta = BetaTemp;
            }
            else
            {
                Theta = -Theta;
                double ACosValueTemp = c / Math.Sin(Theta * M_PI / 180);
                if (ACosValueTemp > 1)
                {
                    ACosValueTemp = 1.0f;
                }
                else if (ACosValueTemp < -1)
                {
                    ACosValueTemp = -1.0f;
                }
                BetaTemp = Math.Acos(ACosValueTemp) * 180 / M_PI;
                double ThirdEquation = Math.Round(Math.Sin(Theta * M_PI / 180) * Math.Sin(BetaTemp * M_PI / 180), 2);
                if (ThirdEquation == Math.Round(c, 2))
                {
                    Beta = BetaTemp;
                }
                else
                {
                    Theta = 0;
                    Beta = 0;
                }
            }
        }

        void DicPlusOrSub(ListViewItem Item, int Mode, int Number = 1)
        {
            if (_PaintItemSize.ContainsKey(Item))
            {
                if (Mode == 0)
                    _PaintItemSize[Item] += Number;
                else
                    _PaintItemSize[Item] -= Number;
            }
        }

        public Dictionary<ListViewItem, int> GetPaintItemDic()
        {
            return _PaintItemSize;
        }

        void ClusterEndAddEntity(Entity Entity)
        {
            EditOperation_AddChildEntity EditOperation = new EditOperation_AddChildEntity(Entity.Parent, Entity);
            EditOperationManager.GetInstance().AddOperation(EditOperation);
            HierarchyUI HierarchyUI = HierarchyUI.GetInstance();
            //HierarchyUI.ShowHierarchy();
        }

        public void SetPaintSphereValue(float value)
        {
            value = Math.Clamp(value, 0.0f, 1.0f);
            _PaintSphereScale = value * (SphereMaxScale - SphereMinScale) + SphereMinScale;
            _CurrentCircleR = (MaxCircleR - MinCircleR) / (SphereMaxScale - SphereMinScale) * _PaintSphereScale;
            if (_PaintSphere != null)
            {
                UpdatePaintSphereModelScale();
            }
        }

        public void SetPaintSphereExtraScale(float value)
        {
            _PaintSphereExtraScale = value;
            if (_PaintSphere != null)
            {
                UpdatePaintSphereModelScale();
            }
        }

        public void Entity(string path)
        {
            string name = Path.GetFileNameWithoutExtension(path);
            Entity value;
            _InstanceMap.TryGetValue(name, out value);
            if (value != null)
            {
                FoliageComponent FoliageComponent = value.GetFoliageComponent();
                foreach (RTSData RTSData in FoliageComponent.InstanceData)
                {
                    Entity Entity = DoCreateEntity(name, value == null, value);
                    if (Entity != null)
                    {
                        Transform Transform = Entity.CreateComponent<Transform>();
                        if (Transform == null)
                        {
                            Transform = Entity.GetTransformComponent();
                        }
                        Transform.Scale = RTSData.Scale;
                        Transform.Translation = RTSData.Translation;
                        ModelComponent ModelComponent = Entity.CreateComponent<ModelComponent>();
                        List<Model> ModelList = new List<Model>();
                        ModelList.Add(new Model(path));
                        ModelComponent.Models = ModelList;
                        ModelComponent.Entity = Entity;
                        ModelComponent.SetDefaultMaterial();
                        ModelComponent.GPUSkin = true;
                        LightMapBaker LightMap = Entity.CreateComponent<LightMapBaker>();
                        LightMap.Entity = Entity;
                        LightMap.ApplyLightmap = true;
                        LightMap.ApplyShadowmap = true;
                        Entity.RuntimeJointToParent();
                        EndAddEntity(Entity);
                    }
                }

                value.RemoveComponent(value.GetFoliageComponent());
                value.RemoveComponent(value.GetModelComponent());
                value.RemoveComponent(value.GetComponent(typeof(LightMapBaker)));

                HierarchyUI.GetInstance().UpdateHierarchy();
            }
        }

        public void Instantiate(string path)
        {
            string fileName = Path.GetFileNameWithoutExtension(path);
            Entity value;
            _InstanceMap.TryGetValue(fileName, out value);

            if (value != null)
            {
                FoliageComponent FoliageComponent = value.CreateComponent<FoliageComponent>();
                FoliageComponent.Reset(path);
                LightMapBaker LightMap = value.CreateComponent<LightMapBaker>();
                LightMap.Entity = value;
                LightMap.ApplyLightmap = true;
                LightMap.ApplyShadowmap = true;

                List<RTSData> InstanceData = new List<RTSData>();
                foreach (Entity E in value.Children)
                {
                    RTSData RTSData = new RTSData();
                    Transform Transform = E.GetTransformComponent();
                    RTSData.Translation = new Vector3d(Transform.Translation.x, Transform.Translation.y, Transform.Translation.z);
                    RTSData.Scale = new Vector3d(Transform.Scale.x, Transform.Scale.y, Transform.Scale.z);
                    RTSData.Rotation = new Vector3d(Transform.Rotation.x, Transform.Rotation.y, Transform.Rotation.z);
                    InstanceData.Add(RTSData);
                }
                FoliageComponent.InstanceData = InstanceData;
                FoliageComponentInstanceData instanceData = new FoliageComponentInstanceData();
                foreach (RTSData rts in value.GetFoliageComponent().InstanceData)
                {
                    FoliageComponentTransform transform = new FoliageComponentTransform();
                    transform.mTranslation.x = (float)rts.Translation.X;
                    transform.mTranslation.y = (float)rts.Translation.Y;
                    transform.mTranslation.z = (float)rts.Translation.Z;
                    transform.mScale.x = (float)rts.Scale.X;
                    transform.mScale.y = (float)rts.Scale.Y;
                    transform.mScale.z = (float)rts.Scale.Z;
                    Quaterniond quaternionf = Quaterniond.FromEuler(rts.Rotation.X, rts.Rotation.Y, rts.Rotation.Z);
                    transform.mRotation.x = (float)quaternionf.X;
                    transform.mRotation.y = (float)quaternionf.Y;
                    transform.mRotation.z = (float)quaternionf.Z;
                    transform.mRotation.w = (float)quaternionf.W;
                    transform.mDelete = rts.Delete;
                    instanceData.mInstanceData.Add(transform);
                }
                FoliageSystemG.SetEditorInstanceData(value.World.GetNativePointer(), value.EntityID, instanceData, new vector_int());

                value.RuntimeJointToParent();
                ClusterEndAddEntity(value);
                Entity Parent = value;
                GetEditorScene().SetDirty();
                for (int i = (Parent.Children.Count - 1); i >= 0; i--)
                {
                    Parent.Children[i].RuntimeRemove();
                }
                Parent.RemoveChildEntities();
                Parent.RuntimeJointToParent();
                HierarchyUI.GetInstance().UpdateHierarchy();
                HierarchyUI.GetInstance().GetTree().SelectItem(null);
            }
        }

        public void SetScreenShotMode(bool bScreenShotMode)
        {
            bool bVisible = !bScreenShotMode;
            _ButtonShowOption.SetVisible(bVisible);
            _CameraModeComboBox.SetVisible(bVisible);
            _SnappingButton.SetVisible(bVisible);
            _CameraSpeedComboBox.SetVisible(bVisible);
            _CameraRotationComboBox.SetVisible(bVisible);
            _TranslationStepComboBox.SetVisible(bVisible);
            _RotationStepComboBox.SetVisible(bVisible);
            _ScalingStepComboBox.SetVisible(bVisible);
            GetEditorScene().SetDrawIcons(bVisible);
            VolumeTriggerSystemG.SetGizmoShowing(bVisible);
        }

        public Transform GetClusterTransform(Entity Entity, ClusterObjectDataInfo Info, Vector3d GeneratePoint, Vector3d GenerateFaceNormal, bool bObject)
        {
            Transform Transform;
            Transform = Entity.CreateComponent<Transform>();
            if (Transform == null)
            {
                Transform = Entity.GetTransformComponent();
            }

            size = Info.ObjectSize;
            if (size <= 0.5f)
            {
                size += 0.5f;
            }
            else
            {
                size *= 2.0f;
            }
            Transform.Translation = GeneratePoint;
            double Theta = 0.0;
            double Beta = 0.0;
            Vector3d UpVector = new Vector3d(0.0f, 1.0f, 0.0f);
            if (!Info.NormalOrientation)
            {
                Transform.Rotation = UpVector;
            }
            else
            {
                GetEquations(GenerateFaceNormal.X, GenerateFaceNormal.Y, GenerateFaceNormal.Z, ref Theta, ref Beta);
                Transform.Rotation = new Vector3d(Theta, Beta, 0.0f);
            }
            if (bObject)
                Transform.Scale = new Vector3d(0.0f, 0.0f, 0.0f);
            else
                Transform.Scale = new Vector3d(Info.ScaleX + 1.0f, Info.ScaleY + 1.0f, Info.ScaleZ + 1.0f);

            if (Info.ObjectLean != 0.0f)
            {
                Theta = 0.0f;
                Beta = 0.0f;
                Vector3d InterpolateVector = GenerateFaceNormal * Info.ObjectLean + UpVector * (1.0f - Info.ObjectLean);
                InterpolateVector.Normalize();
                GetEquations(InterpolateVector.X, InterpolateVector.Y, InterpolateVector.Z, ref Theta, ref Beta);
                Transform.Rotation = new Vector3d(Theta, Beta, 0.0f);
            }

            //calculate transform
            Transform.Translation += new Vector3d(Info.WorldXOffset, Info.WorldYOffset, Info.WorldZOffset);

            Vector3d LocalOffset = new Vector3d(Info.LocalXOffset, Info.LocalYOffset, Info.LocalZOffset);
            Vector3d UnitNormal = new Vector3d(GenerateFaceNormal.X, GenerateFaceNormal.Y, GenerateFaceNormal.Z);
            UnitNormal.Normalize();
            double Distance = Vector3d.DotProduct(ref LocalOffset, ref UnitNormal);
            Transform.Translation = Transform.Translation + UnitNormal * Distance;

            if (Info.LocalXRotation != 0 || Info.LocalYRotation != 0 || Info.LocalZRotation != 0)
            {
                Vector4d vector4fX = new Vector4d(GenerateFaceNormal.X, GenerateFaceNormal.Y, GenerateFaceNormal.Z, 1.0f);

                if (Info.LocalXRotation != 0)
                {
                    Matrix4x4d WorldXRotationMatrix = new Matrix4x4d();
                    WorldXRotationMatrix.LoadIdentity();
                    WorldXRotationMatrix.SetRotationX(MathHelper.DegreeToRadians(Info.LocalXRotation));
                    WorldXRotationMatrix.Transform(ref vector4fX);
                    GetEquations(vector4fX.X, vector4fX.Y, vector4fX.Z, ref Theta, ref Beta);
                }
                if (Info.LocalYRotation != 0)
                {
                    Matrix4x4d WorldYRotationMatrix = new Matrix4x4d();
                    WorldYRotationMatrix.LoadIdentity();
                    WorldYRotationMatrix.SetRotationY(MathHelper.DegreeToRadians(Info.LocalYRotation));
                    WorldYRotationMatrix.Transform(ref vector4fX);
                    GetEquations(vector4fX.X, vector4fX.Y, vector4fX.Z, ref Theta, ref Beta);
                }

                if (Info.LocalZRotation != 0)
                {
                    Matrix4x4d WorldZRotationMatrix = new Matrix4x4d();
                    WorldZRotationMatrix.LoadIdentity();
                    WorldZRotationMatrix.SetRotationZ(MathHelper.DegreeToRadians(Info.LocalZRotation));
                    WorldZRotationMatrix.Transform(ref vector4fX);
                    GetEquations(vector4fX.X, vector4fX.Y, vector4fX.Z, ref Theta, ref Beta);
                }
                Transform.Rotation = new Vector3d(Theta, Beta, 0.0);
            }
            if (Info.WorldXRotation != 0 || Info.WorldXRotation != 0 || Info.WorldXRotation != 0)
            {
                Transform.Rotation = new Vector3d(Info.WorldXRotation, Info.WorldYRotation, Info.WorldZRotation);
            }
            return Transform;
        }

        public RTSData GetInstanceTransformNew(ClusterObjectDataInfo Info, Vector3d GeneratePoint, Vector3d GenerateFaceNormal, bool bObject)
        {
            RTSData Transform = new RTSData();

            size = Info.ObjectSize;
            Transform.Scale = new Vector3d(size, size, size);
            if (Info.SizeVariation != 0)
            {
                Transform.Scale = RandomNumber.GetInstance().GenerateRandomSize(Transform.Scale, Info.SizeVariation);
            }
            Transform.Translation = GeneratePoint;
            Vector3d UpVector = new Vector3d(0.0f, 1.0f, 0.0f);
            if (!Info.NormalOrientation)
            {
                Transform.Rotation = UpVector;
            }
            else
            {
                Quaterniond QuaternionRotate = new Quaterniond(Quaternion64.CreateFrom2Vectors(new Double3(0.0, 1.0, 0.0), GenerateFaceNormal, new Double3(0.0, 1.0, 0.0)));
                Transform.Rotation = Quaterniond.ToEuler(QuaternionRotate);
            }
            if (Info.ObjectLean != 0.0f)
            {
                //Quaternion64 QuaternionRotateX = Quaternion64.CreateFromAxisAngle(new Double3(1.0, 0.0, 0.0), Info.ObjectLean * 90);
                //Quaternion64 QuaternionRotateZ = Quaternion64.CreateFromAxisAngle(new Double3(0.0, 0.0, 1.0), Info.ObjectLean * 90);
                //Quaterniond Result = new Quaterniond(Quaternion64.Concatenate(QuaternionRotateX, QuaternionRotateZ));
                Quaterniond Result = new Quaterniond(Quaternion64.CreateFromAxisAngle(GenerateFaceNormal, Info.ObjectLean * 90));
                Vector3d Result1 = Quaterniond.ToEuler(Result);
                Transform.Rotation = Result1;
            }
            return Transform;
        }

        public bool InstanceExist(string value)
        {
            return _InstanceMap.ContainsKey(value);
        }

        public bool DisplayGeneBVHDialog()
        {
            if (!_IsAllGeneBVHInstance)
            {
                if (!_IsDispalyDialog)
                {
                    PaintCommonDialogUI = new CommonDialogUI();
                    string Message = "Please generate instance bvh in selected instances.";
                    PaintCommonDialogUI.Initialize(UIManager.GetActiveUIManager(), "Paint Fail", Message, CommonDialogType.OK);
                    PaintCommonDialogUI.CloseEvent += (CommonDialogUI Sender1, CommonDialogResult Result) =>
                    {
                        _IsDispalyDialog = false;
                    };
                    DialogUIManager.GetInstance().ShowDialogUI(PaintCommonDialogUI);
                    _IsDispalyDialog = true;
                }
                return true;
            }
            return false;
        }

        public void DeleteHierarchyEditorSphere()
        {
            Entity Root = GetEditorScene().GetRoot();
            Entity EntityEditorSphere = Root.FindChildByName("EditorSphere");
            if (EntityEditorSphere != null)
            {
                OperationQueue.GetInstance().AddOperation(() =>
                {
                    Entity Parent = EntityEditorSphere.Parent;
                    Parent.RemoveChildEntity(EntityEditorSphere);
                    EntityEditorSphere.RuntimeRemove();
                    HierarchyUI.GetInstance().UpdateHierarchy();
                    HierarchyUI.GetInstance().GetTree().SelectItem(null);
                });
            }

            if (_CurrentMode == EditorMode.Paint_Mode)
            {
                OperationQueue.GetInstance().AddOperation(() =>
                {
                    CreatePaintSphere();
                });
            }
        }

        public override void HandleSaveBack()
        {
            long nowTs = new DateTimeOffset(DateTime.UtcNow).ToUnixTimeSeconds();
            long delta = nowTs - _LastSaveTs;
            AutoSaveConfig config = EditorConfigManager.GetInstance().GetConfig<AutoSaveConfig>();
            if (config.SaveScene && delta > config.Frequency * 60)
            {
                _LastSaveTs = nowTs;
                string curPath = EditorScene.GetInstance().GetCurrentSceneFilename();
                if (curPath != "")
                {
                    CrossEngineApiPINVOKE.SaveWorldBack(GetScene().GetWorld()._World, curPath);
                }
            }
        }

        public int GetSaveWarningTime()
        {
            AutoSaveConfig config = EditorConfigManager.GetInstance().GetConfig<AutoSaveConfig>();
            string curPath = EditorScene.GetInstance().GetCurrentSceneFilename();
            if (config != null && curPath != "" && config.SaveScene)
            {
                long nowTs = new DateTimeOffset(DateTime.UtcNow).ToUnixTimeSeconds();
                long warningTs = nowTs - _LastSaveTs - (config.Frequency * 60 - config.Warning);
                if (warningTs >= 0 && warningTs <= config.Warning)
                {
                    return (int)(config.Warning - warningTs);
                }
            }
            return -1;
        }

        public void RefreshInstanceMap(List<String> Names)
        {
            foreach (var Name in Names)
            {
                if (_InstanceMap.ContainsKey(Name))
                {
                    _InstanceMap.Remove(Name);
                }
            }
        }

        public void NewScene()
        {
            _PaintTriMap.Clear();
            _PaintSizeMap.Clear();
            _PaintItemSize.Clear();
            _InstanceMap.Clear();
        }

        #region Piolot
        public void ActivePiolotRelated(bool Flag, string EntityName = "")
        {
            _ButtonExitPilot.SetVisible(Flag);
            _ButtonCameraShake.SetVisible(Flag);
            if (Flag)
            {
                _PiolotEdit.SetText("[Piolot Active - " + EntityName + " ]");
                _PiolotEdit.SetPosition(42, 30, _PiolotEdit.CalculateTextWidth() + 8, 16);
            }
            _PiolotEdit.SetVisible(Flag);
        }

        public bool GetCameraView()
        {
            Entity CurPiolotEntity = HierarchyUI.GetInstance().GetCurPiolotEntity();
            if (CurPiolotEntity != null && CurPiolotEntity.HasComponent(typeof(Camera)) &&
                _bCameraView)
            {
                return true;
            }
            return false;
        }

        #endregion
    }
}
