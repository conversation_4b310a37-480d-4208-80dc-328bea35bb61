using System.Collections.Generic;

namespace CrossEditor
{
    class EntityTransformItem
    {
        public Entity _Entity;
        public Transform _Transform;
        public Matrix4x4d _SavedWorldMatrix;
        public Matrix4x4d _TargetWorldMatrix;

        public EntityTransformItem()
        {
        }

        public void Undo()
        {
            _Transform.SetWorldMatrix(ref _SavedWorldMatrix);
        }

        public void Redo()
        {
            _Transform.SetWorldMatrix(ref _TargetWorldMatrix);
        }
    }

    class EditOperation_EntitiesTransform : EditOperation
    {
        List<EntityTransformItem> _EntityTransformItemList;

        public EditOperation_EntitiesTransform()
        {
            _EntityTransformItemList = new List<EntityTransformItem>();
        }

        public void AddEntityTransformItem(EntityTransformItem EntityTransformItem)
        {
            _EntityTransformItemList.Add(EntityTransformItem);
        }

        public override void Undo()
        {
            foreach (EntityTransformItem EntityTransformItem in _EntityTransformItemList)
            {
                EntityTransformItem.Undo();
            }
            InspectorUI.GetInstance().InspectObject();
        }

        public override void Redo()
        {
            foreach (EntityTransformItem EntityTransformItem in _EntityTransformItemList)
            {
                EntityTransformItem.Redo();
            }
            InspectorUI.GetInstance().InspectObject();
        }
    }
}
