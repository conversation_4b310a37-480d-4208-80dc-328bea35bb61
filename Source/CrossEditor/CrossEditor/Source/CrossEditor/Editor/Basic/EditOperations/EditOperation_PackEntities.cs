using EditorUI;
using System.Collections.Generic;

namespace CrossEditor
{
    class EditOperation_PackEntities : EditOperation
    {
        List<Entity> _RootEntities;
        Entity _CurEntity;
        List<Entity> _SelectedEntities;

        public EditOperation_PackEntities(List<Entity> RootEntity, Entity CurEntity, List<Entity> SelectedEntities)
        {
            _RootEntities = RootEntity;
            _CurEntity = CurEntity;
            _SelectedEntities = SelectedEntities;
        }

        public override void Undo()
        {
            UnPack();
        }

        public override void Redo()
        {

            Pack();
        }

        void Pack()
        {
            for (int i = 0; i < _SelectedEntities.Count; i++)
            {
                _RootEntities[i].RemoveChildEntity(_SelectedEntities[i]);
                _SelectedEntities[i].Parent = _CurEntity;
                _SelectedEntities[i].RuntimeJointToParent();
            }

            _RootEntities[_SelectedEntities.Count].AddChildEntity(_CurEntity);
            HierarchyUI HierarchyUI = HierarchyUI.GetInstance();
            HierarchyUI.UpdateHierarchy();
            HierarchyUI.SelectEntity(_CurEntity);
            EditorScene.GetInstance().SetDirty();

            TreeItem Item = HierarchyUI.GetTree().FindItemByTagObject(_CurEntity);
            Item.SetExpanded(true);
        }

        void UnPack()
        {
            Entity CurEntity = _CurEntity;
            for (int i = 0; i < CurEntity.Children.Count; i++)
            {
                _RootEntities[i].AddChildEntity(CurEntity.Children[i]);
                CurEntity.Children[i].RuntimeJointToParent();
            }
            _RootEntities[_SelectedEntities.Count].RemoveChildEntity(CurEntity);
            HierarchyUI HierarchyUI = HierarchyUI.GetInstance();
            HierarchyUI.UpdateHierarchy();
            HierarchyUI.SelectEntity(null);
        }
    }
}
