using System.Collections.Generic;

namespace CrossEditor
{
    enum FoliageEditOperationType
    {
        BreakUpToEntities,
        CombineTogether,
    }

    class EditOperation_Foliage : EditOperation
    {
        FoliageEditOperationType _Type;
        Entity _Entity;
        List<Entity> _EntityList;
        List<RTSData> _RTSDataList;

        public EditOperation_Foliage(FoliageEditOperationType Type, Entity Entity, List<Entity> EntityList, List<RTSData> RTSDataList)
        {
            _Type = Type;
            _Entity = Entity;
            _EntityList = EntityList;
            _RTSDataList = RTSDataList;
        }

        public override void Undo()
        {
            if (_Type == FoliageEditOperationType.BreakUpToEntities)
            {
                CombineTogether();
            }
            else
            {
                BreakUpToEntities();
            }
        }

        public override void Redo()
        {
            if (_Type == FoliageEditOperationType.BreakUpToEntities)
            {
                BreakUpToEntities();
            }
            else
            {
                CombineTogether();
            }
        }

        void BreakUpToEntities()
        {
            foreach (Entity EntityItem in _EntityList)
            {
                _Entity.AddChildEntity(EntityItem);
                EntityItem.RuntimeAdd();
            }

            FoliageComponent FoliageComponent = _Entity.GetFoliageComponent();
            List<RTSData> EmptyInstanceData = new List<RTSData>();
            FoliageComponent.InstanceData = EmptyInstanceData;

            HierarchyUI.GetInstance().UpdateHierarchy();
            HierarchyUI.GetInstance().SelectEntity(_Entity);
        }

        void CombineTogether()
        {
            FoliageComponent FoliageComponent = _Entity.GetFoliageComponent();
            FoliageComponent.InstanceData = _RTSDataList;

            int Count = _EntityList.Count;
            for (int i = Count - 1; i >= 0; i--)
            {
                Entity EntityItem = _EntityList[i];
                _Entity.RemoveChildEntity(EntityItem);
                EntityItem.RuntimeRemove();
            }

            HierarchyUI.GetInstance().UpdateHierarchy();
            HierarchyUI.GetInstance().SelectEntity(_Entity);
        }
    }
}
