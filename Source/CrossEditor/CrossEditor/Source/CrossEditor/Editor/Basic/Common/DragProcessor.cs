using System;
using System.Text.RegularExpressions;

namespace CrossEditor
{
    // Drag a file[InPath] and joint the result to the root entity
    public class PathDragProcessor
    {
        // default RegExpPattern is REJECT ALL
        public virtual string RegExpPattern
        {
            get { return "^$|(.+)^"; }
        }

        private bool Match(string InPath)
        {
            if (InPath.Length == 0)
            {
                return false;
            }
            try
            {
                Regex.Match("", RegExpPattern);
            }
            catch (ArgumentException)
            {
                return false;
            }
            return Regex.IsMatch(InPath, RegExpPattern);
        }

        public void DoProcess(string InPath, Entity inRootEntity, Vector3d DropPoint)
        {
            if (Match(InPath))
            {
                DoProcessImpl(InPath, inRootEntity, DropPoint);
            }
        }

        protected virtual bool DoProcessImpl(string InPath, Entity inRootEntity, Vector3d DropPoint)
        {
            return true;
        }
    }
}
