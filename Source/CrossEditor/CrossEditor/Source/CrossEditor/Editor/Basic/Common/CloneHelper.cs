using EditorUI;
using System;
using System.Reflection;

namespace CrossEditor
{
    class CloneHelper
    {
        static object CloneList(object List)
        {
            Type ListType = List.GetType();
            PropertyInfo PropertyInfo_Count = ListType.GetProperty("Count");
            MethodInfo MethodInfo_GetItem = ListType.GetMethod("get_Item");
            MethodInfo MethodInfo_Add = ListType.GetMethod("Add");

            object[] Arguments1 = new object[1];

            object List2 = Activator.CreateInstance(ListType);
            int Count1 = (int)PropertyInfo_Count.GetValue(List);
            for (int i = 0; i < Count1; i++)
            {
                Arguments1[0] = i;
                object Item = MethodInfo_GetItem.Invoke(List, Arguments1);

                object ItemCloned = Clone(Item);

                Arguments1[0] = ItemCloned;
                MethodInfo_Add.Invoke(List2, Arguments1);
            }
            return List2;
        }

        static object CloneDictionary(object Dictionary)
        {
            Type DictionaryType = Dictionary.GetType();

            MethodInfo MethodInfo_GetEnumerator = DictionaryType.GetMethod("GetEnumerator");
            MethodInfo MethodInfo_Add = DictionaryType.GetMethod("Add");

            object Dictionary2 = Activator.CreateInstance(DictionaryType);

            object Enumerator = MethodInfo_GetEnumerator.Invoke(Dictionary, null);

            Type EnumeratorType = Enumerator.GetType();
            MethodInfo MethodInfo_Enumerator_MoveNext = EnumeratorType.GetMethod("MoveNext");
            PropertyInfo PropertyInfo_Enumerator_Current = EnumeratorType.GetProperty("Current");

            object[] Arguments2 = new object[2];
            while (true)
            {
                bool b = (bool)MethodInfo_Enumerator_MoveNext.Invoke(Enumerator, null);
                if (!b)
                {
                    break;
                }

                object KeyValuePair = PropertyInfo_Enumerator_Current.GetValue(Enumerator);

                Type KeyValuePairType = KeyValuePair.GetType();
                PropertyInfo PropertyInfo_KeyValuePair_Key = KeyValuePairType.GetProperty("Key");
                PropertyInfo PropertyInfo_KeyValuePair_Value = KeyValuePairType.GetProperty("Value");

                object Key = PropertyInfo_KeyValuePair_Key.GetValue(KeyValuePair);
                object Value = PropertyInfo_KeyValuePair_Value.GetValue(KeyValuePair);

                object KeyCloned = Clone(Key);
                object ValueCloned = Clone(Value);

                Arguments2[0] = KeyCloned;
                Arguments2[1] = ValueCloned;
                MethodInfo_Add.Invoke(Dictionary2, Arguments2);
            }
            return Dictionary2;
        }

        public static object Clone(object Object)
        {
            if (Object == null)
            {
                return null;
            }

            Type Type_ = Object.GetType();
            if (Type_.IsValueType || Type_ == typeof(string))
            {
                return Object;
            }

            if (Object is ICloneable)
            {
                return (Object as ICloneable).Clone();
            }

            object ObjectCloned = Activator.CreateInstance(Type_);
            if (Type_.Name == "Dictionary`2")
            {
                ObjectCloned = CloneDictionary(Object);
            }
            else if (Type_.Name == "List`1")
            {
                ObjectCloned = CloneList(Object);
            }
            else
            {
                PropertyInfo[] Properties = Type_.GetProperties();
                foreach (PropertyInfo PropertyInfo in Properties)
                {
                    AttributeList AttributeList = AttributeManager.GetInstance().GetAttributeList(PropertyInfo);
                    if (AttributeList != null)
                    {
                        if (PropertyInfo.CanRead && PropertyInfo.CanWrite)
                        {
                            if (PropertyInfo.Name != "Item")
                            {
                                object PropertyOld = PropertyInfo.GetValue(Object);
                                object PropertyNew = Clone(PropertyOld);
                                PropertyInfo.SetValue(ObjectCloned, PropertyNew);
                            }
                        }
                    }
                }
            }

            return ObjectCloned;
        }

        public static object CloneByConstructor(object Object)
        {
            if (Object == null)
            {
                return null;
            }

            Type Type = Object.GetType();
            if (Type.IsValueType || Type == typeof(string))
            {
                return Object;
            }

            if (Object is ICloneable)
            {
                return (Object as ICloneable).Clone();
            }

            ConstructorInfo[] Constructors = Type.GetConstructors();
            foreach (ConstructorInfo Constructor in Constructors)
            {
                ParameterInfo[] Parameters = Constructor.GetParameters();
                if (Parameters.Length == 1 && Parameters[0].ParameterType == Type)
                {
                    return Constructor.Invoke(new object[] { Object });
                }
            }
            return Object;
        }
    }
}
