launch_camera.ptx!!!!!launch_lightmap_indirect.ptx!!!!!launch_lightprobe_indirect.ptx!!!!!launch_postprocess.ptx!!!!!pathtrace_core.ptx!!!!!pathtrace_hit.ptx!!!!!triangle_mesh.ptx!!!!!triangle_mesh32.ptx!!!!!lightmap_emitter.ptx!!!!!lightprobe_emitter.ptx!!!!!pinhole_camera.ptx!!!!!//
// Generated by NVIDIA NVVM Compiler
//
// Compiler Build ID: CL-27506705
// Cuda compilation tools, release 10.2, V10.2.89
// Based on LLVM 3.4svn
//

.version 6.5
.target sm_50
.address_size 64

	// .globl	_Z11launch_mainv
.visible .global .align 4 .b8 debug_input[12];
.visible .global .align 4 .b8 SceneBounds[28];
.visible .global .align 8 .b8 launch_index[8];
.visible .global .align 4 .u32 frame_number;
.visible .global .align 8 .b8 debug_pos[8];
.visible .global .align 1 .b8 debug_output_buffer[1];
.visible .global .align 1 .b8 output_buffer[1];
.visible .global .align 1 .b8 sample_count_buffer[1];
.visible .global .align 1 .b8 output_albedo_buffer[1];
.visible .global .align 1 .b8 output_normal_buffer[1];
.visible .global .align 4 .b8 pathtrace[4];
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo11debug_inputE[8] = {82, 97, 121, 0, 12, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo11SceneBoundsE[8] = {82, 97, 121, 0, 28, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo12launch_indexE[8] = {82, 97, 121, 0, 8, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo12frame_numberE[8] = {82, 97, 121, 0, 4, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo9debug_posE[8] = {82, 97, 121, 0, 8, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo9pathtraceE[8] = {82, 97, 121, 0, 4, 0, 0, 0};
.visible .global .align 8 .u64 _ZN21rti_internal_register20reg_bitness_detectorE;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail0E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail1E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail2E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail3E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail4E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail5E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail6E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail7E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail8E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail9E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail0E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail1E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail2E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail3E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail4E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail5E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail6E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail7E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail8E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail9E;
.visible .global .align 4 .u32 _ZN21rti_internal_register14reg_rayIndex_xE;
.visible .global .align 4 .u32 _ZN21rti_internal_register14reg_rayIndex_yE;
.visible .global .align 4 .u32 _ZN21rti_internal_register14reg_rayIndex_zE;
.visible .global .align 1 .b8 _ZN21rti_internal_typename11debug_inputE[12] = {70, 68, 101, 98, 117, 103, 73, 110, 112, 117, 116, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename11SceneBoundsE[13] = {70, 83, 99, 101, 110, 101, 66, 111, 117, 110, 100, 115, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename12launch_indexE[6] = {117, 105, 110, 116, 50, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename12frame_numberE[13] = {117, 110, 115, 105, 103, 110, 101, 100, 32, 105, 110, 116, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename9debug_posE[5] = {105, 110, 116, 50, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename9pathtraceE[137] = {111, 112, 116, 105, 120, 58, 58, 98, 111, 117, 110, 100, 67, 97, 108, 108, 97, 98, 108, 101, 80, 114, 111, 103, 114, 97, 109, 73, 100, 60, 102, 108, 111, 97, 116, 51, 32, 40, 117, 105, 110, 116, 51, 44, 32, 117, 105, 110, 116, 51, 44, 32, 117, 110, 115, 105, 103, 110, 101, 100, 32, 105, 110, 116, 44, 32, 117, 110, 115, 105, 103, 110, 101, 100, 32, 105, 110, 116, 44, 32, 102, 108, 111, 97, 116, 51, 38, 44, 32, 117, 110, 115, 105, 103, 110, 101, 100, 32, 105, 110, 116, 38, 44, 32, 117, 110, 115, 105, 103, 110, 101, 100, 32, 105, 110, 116, 38, 44, 102, 108, 111, 97, 116, 51, 38, 32, 44, 102, 108, 111, 97, 116, 51, 38, 41, 62, 0};
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum11debug_inputE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum11SceneBoundsE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum12launch_indexE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum12frame_numberE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum9debug_posE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum9pathtraceE = 4921;
.visible .global .align 1 .b8 _ZN21rti_internal_semantic11debug_inputE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic11SceneBoundsE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic12launch_indexE[14] = {114, 116, 76, 97, 117, 110, 99, 104, 73, 110, 100, 101, 120, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_semantic12frame_numberE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic9debug_posE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic9pathtraceE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation11debug_inputE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation11SceneBoundsE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation12launch_indexE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation12frame_numberE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation9debug_posE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation9pathtraceE[1];

.visible .entry _Z11launch_mainv(

)
{
	.local .align 4 .b8 	__local_depot0[44];
	.reg .b64 	%SP;
	.reg .b64 	%SPL;
	.reg .pred 	%p<5>;
	.reg .f32 	%f<37>;
	.reg .b32 	%r<86>;
	.reg .b64 	%rd<104>;


	mov.u64 	%SPL, __local_depot0;
	cvta.local.u64 	%SP, %SPL;
	add.u64 	%rd24, %SP, 0;
	add.u64 	%rd3, %SPL, 0;
	add.u64 	%rd25, %SP, 12;
	add.u64 	%rd4, %SPL, 12;
	add.u64 	%rd26, %SP, 24;
	add.u64 	%rd1, %SPL, 24;
	add.u64 	%rd27, %SP, 28;
	add.u64 	%rd2, %SPL, 28;
	add.u64 	%rd28, %SP, 32;
	add.u64 	%rd29, %SPL, 32;
	ld.global.v2.u32 	{%r9, %r10}, [launch_index];
	mov.u32 	%r12, 0;
	mov.u32 	%r14, 1;
	mov.u64 	%rd30, output_buffer;
	cvta.global.u64 	%rd11, %rd30;
	mov.u32 	%r6, 2;
	mov.u32 	%r7, 16;
	// inline asm
	call (%rd7, %rd8, %rd9, %rd10), _rt_buffer_get_size_64, (%rd11, %r6, %r7);
	// inline asm
	cvt.u32.u64	%r15, %rd7;
	// inline asm
	call (%rd12, %rd13, %rd14, %rd15), _rt_buffer_get_size_64, (%rd11, %r6, %r7);
	// inline asm
	cvt.u32.u64	%r1, %rd13;
	cvt.u64.u32	%rd31, %r9;
	cvt.u64.u32	%rd32, %r10;
	// inline asm
	call (%rd17, %rd18, %rd19, %rd20), _rt_buffer_get_size_64, (%rd11, %r6, %r7);
	// inline asm
	mul.lo.s64 	%rd33, %rd17, %rd32;
	add.s64 	%rd34, %rd33, %rd31;
	cvt.u32.u64	%r16, %rd34;
	st.local.u32 	[%rd3], %r12;
	st.local.u32 	[%rd3+4], %r12;
	st.local.u32 	[%rd3+8], %r12;
	st.local.u32 	[%rd4], %r12;
	st.local.u32 	[%rd4+4], %r12;
	st.local.u32 	[%rd4+8], %r12;
	st.local.u32 	[%rd1], %r12;
	st.local.u32 	[%rd2], %r12;
	mov.u64 	%rd23, 0;
	st.local.u32 	[%rd29+4], %rd23;
	st.local.u32 	[%rd29], %rd23;
	st.local.u32 	[%rd29+8], %r12;
	ld.global.u32 	%r17, [frame_number];
	ld.global.u32 	%r8, [pathtrace];
	// inline asm
	call (%rd22), _rt_callable_program_from_id_v2_64, (%r8, %rd23);
	// inline asm
	// Callseq Start 0
	{
	.reg .b32 temp_param_reg;
	// <end>}
	.param .align 4 .b8 param0[12];
	st.param.b32	[param0+0], %r9;
	st.param.b32	[param0+4], %r10;
	st.param.b32	[param0+8], %r12;
	.param .align 4 .b8 param1[12];
	st.param.b32	[param1+0], %r15;
	st.param.b32	[param1+4], %r1;
	st.param.b32	[param1+8], %r14;
	.param .b32 param2;
	st.param.b32	[param2+0], %r16;
	.param .b32 param3;
	st.param.b32	[param3+0], %r17;
	.param .b64 param4;
	st.param.b64	[param4+0], %rd28;
	.param .b64 param5;
	st.param.b64	[param5+0], %rd27;
	.param .b64 param6;
	st.param.b64	[param6+0], %rd26;
	.param .b64 param7;
	st.param.b64	[param7+0], %rd24;
	.param .b64 param8;
	st.param.b64	[param8+0], %rd25;
	.param .align 4 .b8 retval0[12];
	prototype_0 : .callprototype (.param .align 4 .b8 _[12]) _ (.param .align 4 .b8 _[12], .param .align 4 .b8 _[12], .param .b32 _, .param .b32 _, .param .b64 _, .param .b64 _, .param .b64 _, .param .b64 _, .param .b64 _) ;
	call (retval0), 
	%rd22, 
	(
	param0, 
	param1, 
	param2, 
	param3, 
	param4, 
	param5, 
	param6, 
	param7, 
	param8
	)
	, prototype_0;
	ld.param.f32	%f1, [retval0+0];
	ld.param.f32	%f2, [retval0+4];
	ld.param.f32	%f3, [retval0+8];
	
	//{
	}// Callseq End 0
	ld.global.u32 	%r18, [frame_number];
	setp.gt.u32	%p1, %r18, 1;
	ld.global.v2.u32 	{%r19, %r20}, [launch_index];
	cvt.u64.u32	%rd5, %r19;
	cvt.u64.u32	%rd6, %r20;
	@%p1 bra 	BB0_2;
	bra.uni 	BB0_1;

BB0_2:
	mov.u64 	%rd88, sample_count_buffer;
	cvta.global.u64 	%rd65, %rd88;
	mov.u32 	%r51, 4;
	// inline asm
	call (%rd64), _rt_buffer_get_64, (%rd65, %r6, %r51, %rd5, %rd6, %rd23, %rd23);
	// inline asm
	ld.u32 	%r52, [%rd64];
	ld.global.v2.u32 	{%r53, %r54}, [launch_index];
	cvt.u64.u32	%rd72, %r53;
	cvt.u64.u32	%rd73, %r54;
	// inline asm
	call (%rd70), _rt_buffer_get_64, (%rd11, %r6, %r7, %rd72, %rd73, %rd23, %rd23);
	// inline asm
	ld.global.v2.u32 	{%r57, %r58}, [launch_index];
	cvt.u64.u32	%rd78, %r57;
	cvt.u64.u32	%rd79, %r58;
	// inline asm
	call (%rd76), _rt_buffer_get_64, (%rd11, %r6, %r7, %rd78, %rd79, %rd23, %rd23);
	// inline asm
	cvt.rn.f32.u32	%f16, %r52;
	ld.v4.f32 	{%f17, %f18, %f19, %f20}, [%rd76];
	fma.rn.ftz.f32 	%f25, %f16, %f17, %f1;
	fma.rn.ftz.f32 	%f26, %f16, %f18, %f2;
	fma.rn.ftz.f32 	%f27, %f16, %f19, %f3;
	fma.rn.ftz.f32 	%f28, %f16, %f20, 0f3F800000;
	ld.local.u32 	%r61, [%rd2];
	add.s32 	%r62, %r61, %r52;
	cvt.rn.f32.u32	%f29, %r62;
	rcp.approx.ftz.f32 	%f30, %f29;
	mul.ftz.f32 	%f31, %f28, %f30;
	mul.ftz.f32 	%f32, %f27, %f30;
	mul.ftz.f32 	%f33, %f26, %f30;
	mul.ftz.f32 	%f34, %f25, %f30;
	st.v4.f32 	[%rd70], {%f34, %f33, %f32, %f31};
	ld.global.v2.u32 	{%r63, %r64}, [launch_index];
	cvt.u64.u32	%rd84, %r63;
	cvt.u64.u32	%rd85, %r64;
	// inline asm
	call (%rd82), _rt_buffer_get_64, (%rd65, %r6, %r51, %rd84, %rd85, %rd23, %rd23);
	// inline asm
	st.u32 	[%rd82], %r62;
	bra.uni 	BB0_3;

BB0_1:
	// inline asm
	call (%rd36), _rt_buffer_get_64, (%rd11, %r6, %r7, %rd5, %rd6, %rd23, %rd23);
	// inline asm
	ld.local.u32 	%r31, [%rd2];
	cvt.rn.f32.u32	%f4, %r31;
	rcp.approx.ftz.f32 	%f5, %f4;
	mul.ftz.f32 	%f6, %f3, %f5;
	mul.ftz.f32 	%f7, %f2, %f5;
	mul.ftz.f32 	%f8, %f1, %f5;
	st.v4.f32 	[%rd36], {%f8, %f7, %f6, %f5};
	ld.global.v2.u32 	{%r32, %r33}, [launch_index];
	cvt.u64.u32	%rd44, %r32;
	cvt.u64.u32	%rd45, %r33;
	mov.u64 	%rd61, sample_count_buffer;
	cvta.global.u64 	%rd43, %rd61;
	mov.u32 	%r26, 4;
	// inline asm
	call (%rd42), _rt_buffer_get_64, (%rd43, %r6, %r26, %rd44, %rd45, %rd23, %rd23);
	// inline asm
	st.u32 	[%rd42], %r31;
	ld.global.v2.u32 	{%r36, %r37}, [launch_index];
	cvt.u64.u32	%rd50, %r36;
	cvt.u64.u32	%rd51, %r37;
	mov.u64 	%rd62, output_albedo_buffer;
	cvta.global.u64 	%rd49, %rd62;
	// inline asm
	call (%rd48), _rt_buffer_get_64, (%rd49, %r6, %r7, %rd50, %rd51, %rd23, %rd23);
	// inline asm
	ld.local.f32 	%f9, [%rd3+8];
	ld.local.f32 	%f10, [%rd3+4];
	ld.local.f32 	%f11, [%rd3];
	mov.f32 	%f12, 0f3F800000;
	st.v4.f32 	[%rd48], {%f11, %f10, %f9, %f12};
	ld.global.v2.u32 	{%r40, %r41}, [launch_index];
	cvt.u64.u32	%rd56, %r40;
	cvt.u64.u32	%rd57, %r41;
	mov.u64 	%rd63, output_normal_buffer;
	cvta.global.u64 	%rd55, %rd63;
	// inline asm
	call (%rd54), _rt_buffer_get_64, (%rd55, %r6, %r7, %rd56, %rd57, %rd23, %rd23);
	// inline asm
	ld.local.f32 	%f13, [%rd4+8];
	ld.local.f32 	%f14, [%rd4+4];
	ld.local.f32 	%f15, [%rd4];
	st.v4.f32 	[%rd54], {%f15, %f14, %f13, %f12};

BB0_3:
	ld.global.v2.u32 	{%r67, %r68}, [debug_pos];
	ld.global.v2.u32 	{%r71, %r72}, [launch_index];
	setp.ne.s32	%p2, %r67, %r71;
	add.s32 	%r75, %r1, -1;
	sub.s32 	%r76, %r75, %r68;
	setp.ne.s32	%p3, %r76, %r72;
	or.pred  	%p4, %p2, %p3;
	@%p4 bra 	BB0_5;

	ld.global.v2.u32 	{%r81, %r82}, [launch_index];
	cvt.u64.u32	%rd92, %r81;
	cvt.u64.u32	%rd93, %r82;
	// inline asm
	call (%rd90), _rt_buffer_get_64, (%rd11, %r6, %r7, %rd92, %rd93, %rd23, %rd23);
	// inline asm
	mov.f32 	%f35, 0f00000000;
	mov.f32 	%f36, 0f41200000;
	st.v4.f32 	[%rd90], {%f36, %f35, %f35, %f35};
	ld.local.u32 	%r85, [%rd1];
	mov.u64 	%rd103, debug_output_buffer;
	cvta.global.u64 	%rd97, %rd103;
	mov.u32 	%r80, 4;
	// inline asm
	call (%rd96), _rt_buffer_get_64, (%rd97, %r14, %r80, %rd23, %rd23, %rd23, %rd23);
	// inline asm
	st.u32 	[%rd96], %r85;

BB0_5:
	ret;
}


#####//
// Generated by NVIDIA NVVM Compiler
//
// Compiler Build ID: CL-27506705
// Cuda compilation tools, release 10.2, V10.2.89
// Based on LLVM 3.4svn
//

.version 6.5
.target sm_50
.address_size 64

	// .globl	_Z11launch_mainv
.visible .global .align 4 .b8 debug_input[12];
.visible .global .align 4 .b8 SceneBounds[28];
.visible .global .align 1 .b8 TexelToVertexMap[1];
.visible .global .align 1 .b8 output_buffer[1];
.visible .global .align 1 .b8 color_output_buffer[1];
.visible .global .align 1 .b8 sample_count_buffer[1];
.visible .global .align 8 .b8 launch_index[8];
.visible .global .align 4 .u32 max_iteration;
.visible .global .align 4 .b8 pathtrace[4];
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo11debug_inputE[8] = {82, 97, 121, 0, 12, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo11SceneBoundsE[8] = {82, 97, 121, 0, 28, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo12launch_indexE[8] = {82, 97, 121, 0, 8, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo13max_iterationE[8] = {82, 97, 121, 0, 4, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo9pathtraceE[8] = {82, 97, 121, 0, 4, 0, 0, 0};
.visible .global .align 8 .u64 _ZN21rti_internal_register20reg_bitness_detectorE;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail0E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail1E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail2E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail3E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail4E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail5E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail6E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail7E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail8E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail9E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail0E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail1E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail2E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail3E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail4E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail5E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail6E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail7E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail8E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail9E;
.visible .global .align 4 .u32 _ZN21rti_internal_register14reg_rayIndex_xE;
.visible .global .align 4 .u32 _ZN21rti_internal_register14reg_rayIndex_yE;
.visible .global .align 4 .u32 _ZN21rti_internal_register14reg_rayIndex_zE;
.visible .global .align 1 .b8 _ZN21rti_internal_typename11debug_inputE[12] = {70, 68, 101, 98, 117, 103, 73, 110, 112, 117, 116, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename11SceneBoundsE[13] = {70, 83, 99, 101, 110, 101, 66, 111, 117, 110, 100, 115, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename12launch_indexE[6] = {117, 105, 110, 116, 50, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename13max_iterationE[13] = {117, 110, 115, 105, 103, 110, 101, 100, 32, 105, 110, 116, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename9pathtraceE[105] = {111, 112, 116, 105, 120, 58, 58, 98, 111, 117, 110, 100, 67, 97, 108, 108, 97, 98, 108, 101, 80, 114, 111, 103, 114, 97, 109, 73, 100, 60, 102, 108, 111, 97, 116, 51, 32, 40, 117, 105, 110, 116, 51, 44, 32, 117, 105, 110, 116, 51, 44, 32, 117, 110, 115, 105, 103, 110, 101, 100, 32, 105, 110, 116, 44, 32, 117, 110, 115, 105, 103, 110, 101, 100, 32, 105, 110, 116, 44, 32, 102, 108, 111, 97, 116, 51, 38, 44, 32, 117, 110, 115, 105, 103, 110, 101, 100, 32, 105, 110, 116, 38, 41, 62, 0};
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum11debug_inputE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum11SceneBoundsE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum12launch_indexE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum13max_iterationE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum9pathtraceE = 4921;
.visible .global .align 1 .b8 _ZN21rti_internal_semantic11debug_inputE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic11SceneBoundsE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic12launch_indexE[14] = {114, 116, 76, 97, 117, 110, 99, 104, 73, 110, 100, 101, 120, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_semantic13max_iterationE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic9pathtraceE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation11debug_inputE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation11SceneBoundsE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation12launch_indexE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation13max_iterationE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation9pathtraceE[1];

.visible .entry _Z11launch_mainv(

)
{
	.local .align 4 .b8 	__local_depot0[16];
	.reg .b64 	%SP;
	.reg .b64 	%SPL;
	.reg .pred 	%p<5>;
	.reg .f32 	%f<115>;
	.reg .b32 	%r<112>;
	.reg .b64 	%rd<124>;


	mov.u64 	%SPL, __local_depot0;
	cvta.local.u64 	%SP, %SPL;
	add.u64 	%rd16, %SP, 0;
	add.u64 	%rd1, %SPL, 0;
	ld.global.v2.u32 	{%r21, %r22}, [launch_index];
	cvt.u64.u32	%rd12, %r21;
	cvt.u64.u32	%rd13, %r22;
	mov.u64 	%rd17, TexelToVertexMap;
	cvta.global.u64 	%rd11, %rd17;
	mov.u32 	%r19, 2;
	mov.u32 	%r20, 72;
	mov.u64 	%rd15, 0;
	// inline asm
	call (%rd10), _rt_buffer_get_64, (%rd11, %r19, %r20, %rd12, %rd13, %rd15, %rd15);
	// inline asm
	ld.u32 	%r23, [%rd10+64];
	setp.eq.s32	%p1, %r23, 0;
	@%p1 bra 	BB0_9;

	mov.u64 	%rd33, output_buffer;
	cvta.global.u64 	%rd22, %rd33;
	mov.u32 	%r29, 48;
	// inline asm
	call (%rd18, %rd19, %rd20, %rd21), _rt_buffer_get_size_64, (%rd22, %r19, %r29);
	// inline asm
	cvt.u32.u64	%r5, %rd18;
	// inline asm
	call (%rd23, %rd24, %rd25, %rd26), _rt_buffer_get_size_64, (%rd22, %r19, %r29);
	// inline asm
	cvt.u32.u64	%r6, %rd24;
	// inline asm
	call (%rd28, %rd29, %rd30, %rd31), _rt_buffer_get_size_64, (%rd22, %r19, %r29);
	// inline asm
	ld.global.u32 	%r30, [max_iteration];
	mov.f32 	%f114, 0f00000000;
	setp.lt.u32	%p2, %r30, 2;
	@%p2 bra 	BB0_8;

	mov.u32 	%r10, 0;
	add.u64 	%rd34, %SP, 4;
	add.u64 	%rd6, %SPL, 4;
	mul.lo.s64 	%rd37, %rd28, %rd13;
	add.s64 	%rd38, %rd37, %rd12;
	cvt.u32.u64	%r11, %rd38;
	mov.u32 	%r14, 1;
	mov.u32 	%r110, %r14;
	mov.u32 	%r111, %r10;

BB0_3:
	st.local.u32 	[%rd1], %r10;
	st.local.u32 	[%rd6+4], %rd15;
	st.local.u32 	[%rd6], %rd15;
	st.local.u32 	[%rd6+8], %r10;
	ld.global.u32 	%r33, [pathtrace];
	// inline asm
	call (%rd39), _rt_callable_program_from_id_v2_64, (%r33, %rd15);
	// inline asm
	// Callseq Start 0
	{
	.reg .b32 temp_param_reg;
	// <end>}
	.param .align 4 .b8 param0[12];
	st.param.b32	[param0+0], %r21;
	st.param.b32	[param0+4], %r22;
	st.param.b32	[param0+8], %r10;
	.param .align 4 .b8 param1[12];
	st.param.b32	[param1+0], %r5;
	st.param.b32	[param1+4], %r6;
	st.param.b32	[param1+8], %r14;
	.param .b32 param2;
	st.param.b32	[param2+0], %r11;
	.param .b32 param3;
	st.param.b32	[param3+0], %r110;
	.param .b64 param4;
	st.param.b64	[param4+0], %rd34;
	.param .b64 param5;
	st.param.b64	[param5+0], %rd16;
	.param .align 4 .b8 retval0[12];
	prototype_0 : .callprototype (.param .align 4 .b8 _[12]) _ (.param .align 4 .b8 _[12], .param .align 4 .b8 _[12], .param .b32 _, .param .b32 _, .param .b64 _, .param .b64 _) ;
	call (retval0), 
	%rd39, 
	(
	param0, 
	param1, 
	param2, 
	param3, 
	param4, 
	param5
	)
	, prototype_0;
	ld.param.f32	%f1, [retval0+0];
	ld.param.f32	%f2, [retval0+4];
	ld.param.f32	%f3, [retval0+8];
	
	//{
	}// Callseq End 0
	ld.local.u32 	%r39, [%rd1];
	add.s32 	%r111, %r39, %r111;
	ld.global.v2.u32 	{%r40, %r41}, [launch_index];
	cvt.u64.u32	%rd43, %r40;
	cvt.u64.u32	%rd44, %r41;
	// inline asm
	call (%rd41), _rt_buffer_get_64, (%rd22, %r19, %r29, %rd43, %rd44, %rd15, %rd15);
	// inline asm
	ld.v4.f32 	{%f22, %f23, %f24, %f25}, [%rd41];
	ld.v4.f32 	{%f26, %f27, %f28, %f29}, [%rd41+16];
	ld.v4.f32 	{%f30, %f31, %f32, %f33}, [%rd41+32];
	ld.global.v2.u32 	{%r44, %r45}, [launch_index];
	cvt.u64.u32	%rd49, %r44;
	cvt.u64.u32	%rd50, %r45;
	// inline asm
	call (%rd47), _rt_buffer_get_64, (%rd11, %r19, %r20, %rd49, %rd50, %rd15, %rd15);
	// inline asm
	ld.local.f32 	%f34, [%rd6];
	ld.f32 	%f35, [%rd47+48];
	ld.local.f32 	%f36, [%rd6+4];
	ld.f32 	%f37, [%rd47+52];
	mul.ftz.f32 	%f38, %f37, %f36;
	fma.rn.ftz.f32 	%f39, %f34, %f35, %f38;
	ld.local.f32 	%f40, [%rd6+8];
	ld.f32 	%f41, [%rd47+56];
	fma.rn.ftz.f32 	%f42, %f41, %f40, %f39;
	mov.f32 	%f43, 0f00000000;
	max.ftz.f32 	%f44, %f43, %f42;
	ld.global.v2.u32 	{%r48, %r49}, [launch_index];
	cvt.u64.u32	%rd8, %r48;
	cvt.u64.u32	%rd9, %r49;
	mul.ftz.f32 	%f16, %f1, %f44;
	mul.ftz.f32 	%f17, %f2, %f44;
	mul.ftz.f32 	%f18, %f3, %f44;
	setp.gt.u32	%p3, %r110, 1;
	@%p3 bra 	BB0_5;
	bra.uni 	BB0_4;

BB0_5:
	mov.u64 	%rd77, color_output_buffer;
	cvta.global.u64 	%rd66, %rd77;
	mov.u32 	%r58, 16;
	// inline asm
	call (%rd65), _rt_buffer_get_64, (%rd66, %r19, %r58, %rd8, %rd9, %rd15, %rd15);
	// inline asm
	ld.global.v2.u32 	{%r59, %r60}, [launch_index];
	cvt.u64.u32	%rd73, %r59;
	cvt.u64.u32	%rd74, %r60;
	// inline asm
	call (%rd71), _rt_buffer_get_64, (%rd66, %r19, %r58, %rd73, %rd74, %rd15, %rd15);
	// inline asm
	ld.local.u32 	%r63, [%rd1];
	cvt.rn.f32.u32	%f46, %r63;
	ld.v4.f32 	{%f47, %f48, %f49, %f50}, [%rd71];
	add.ftz.f32 	%f55, %f46, %f50;
	add.ftz.f32 	%f56, %f18, %f49;
	add.ftz.f32 	%f57, %f17, %f48;
	add.ftz.f32 	%f58, %f16, %f47;
	st.v4.f32 	[%rd65], {%f58, %f57, %f56, %f55};
	bra.uni 	BB0_6;

BB0_4:
	mov.u64 	%rd64, color_output_buffer;
	cvta.global.u64 	%rd59, %rd64;
	mov.u32 	%r53, 16;
	// inline asm
	call (%rd58), _rt_buffer_get_64, (%rd59, %r19, %r53, %rd8, %rd9, %rd15, %rd15);
	// inline asm
	ld.local.u32 	%r54, [%rd1];
	cvt.rn.f32.u32	%f45, %r54;
	st.v4.f32 	[%rd58], {%f16, %f17, %f18, %f45};

BB0_6:
	ld.local.f32 	%f59, [%rd6+4];
	mul.ftz.f32 	%f60, %f59, 0fBEFA2A2C;
	ld.local.f32 	%f61, [%rd6+8];
	mul.ftz.f32 	%f62, %f61, 0f3EFA2A2C;
	ld.local.f32 	%f63, [%rd6];
	mul.ftz.f32 	%f64, %f63, 0fBEFA2A2C;
	ld.global.v2.u32 	{%r70, %r71}, [launch_index];
	cvt.u64.u32	%rd80, %r70;
	cvt.u64.u32	%rd81, %r71;
	// inline asm
	call (%rd78), _rt_buffer_get_64, (%rd22, %r19, %r29, %rd80, %rd81, %rd15, %rd15);
	// inline asm
	fma.rn.ftz.f32 	%f65, %f1, %f62, %f24;
	fma.rn.ftz.f32 	%f66, %f1, %f60, %f23;
	fma.rn.ftz.f32 	%f67, %f1, %f64, %f25;
	fma.rn.ftz.f32 	%f68, %f1, 0f3E906EC1, %f22;
	st.v4.f32 	[%rd78], {%f68, %f66, %f65, %f67};
	fma.rn.ftz.f32 	%f69, %f2, %f62, %f28;
	fma.rn.ftz.f32 	%f70, %f2, %f60, %f27;
	fma.rn.ftz.f32 	%f71, %f2, %f64, %f29;
	fma.rn.ftz.f32 	%f72, %f2, 0f3E906EC1, %f26;
	st.v4.f32 	[%rd78+16], {%f72, %f70, %f69, %f71};
	fma.rn.ftz.f32 	%f73, %f3, %f62, %f32;
	fma.rn.ftz.f32 	%f74, %f3, %f60, %f31;
	fma.rn.ftz.f32 	%f75, %f3, %f64, %f33;
	fma.rn.ftz.f32 	%f76, %f3, 0f3E906EC1, %f30;
	st.v4.f32 	[%rd78+32], {%f76, %f74, %f73, %f75};
	ld.global.v2.u32 	{%r74, %r75}, [launch_index];
	cvt.u64.u32	%rd86, %r74;
	cvt.u64.u32	%rd87, %r75;
	mov.u64 	%rd97, sample_count_buffer;
	cvta.global.u64 	%rd85, %rd97;
	mov.u32 	%r69, 4;
	// inline asm
	call (%rd84), _rt_buffer_get_64, (%rd85, %r19, %r69, %rd86, %rd87, %rd15, %rd15);
	// inline asm
	ld.local.u32 	%r78, [%rd1];
	ld.u32 	%r79, [%rd84];
	add.s32 	%r80, %r78, %r79;
	ld.global.v2.u32 	{%r81, %r82}, [launch_index];
	cvt.u64.u32	%rd92, %r81;
	cvt.u64.u32	%rd93, %r82;
	// inline asm
	call (%rd90), _rt_buffer_get_64, (%rd85, %r19, %r69, %rd92, %rd93, %rd15, %rd15);
	// inline asm
	st.u32 	[%rd90], %r80;
	ld.global.u32 	%r85, [max_iteration];
	add.s32 	%r110, %r110, 1;
	setp.lt.u32	%p4, %r110, %r85;
	@%p4 bra 	BB0_3;

	cvt.rn.f32.u32	%f114, %r111;

BB0_8:
	ld.global.v2.u32 	{%r94, %r95}, [launch_index];
	cvt.u64.u32	%rd100, %r94;
	cvt.u64.u32	%rd101, %r95;
	// inline asm
	call (%rd98), _rt_buffer_get_64, (%rd22, %r19, %r29, %rd100, %rd101, %rd15, %rd15);
	// inline asm
	ld.global.v2.u32 	{%r98, %r99}, [launch_index];
	cvt.u64.u32	%rd106, %r98;
	cvt.u64.u32	%rd107, %r99;
	// inline asm
	call (%rd104), _rt_buffer_get_64, (%rd22, %r19, %r29, %rd106, %rd107, %rd15, %rd15);
	// inline asm
	ld.f32 	%f77, [%rd104];
	ld.f32 	%f78, [%rd104+4];
	ld.f32 	%f79, [%rd104+8];
	ld.f32 	%f80, [%rd104+12];
	ld.f32 	%f81, [%rd104+16];
	ld.f32 	%f82, [%rd104+20];
	ld.f32 	%f83, [%rd104+24];
	ld.f32 	%f84, [%rd104+28];
	ld.f32 	%f85, [%rd104+32];
	ld.f32 	%f86, [%rd104+36];
	ld.f32 	%f87, [%rd104+40];
	ld.f32 	%f88, [%rd104+44];
	div.approx.ftz.f32 	%f89, %f80, %f114;
	div.approx.ftz.f32 	%f90, %f79, %f114;
	div.approx.ftz.f32 	%f91, %f78, %f114;
	div.approx.ftz.f32 	%f92, %f77, %f114;
	st.v4.f32 	[%rd98], {%f92, %f91, %f90, %f89};
	div.approx.ftz.f32 	%f93, %f84, %f114;
	div.approx.ftz.f32 	%f94, %f83, %f114;
	div.approx.ftz.f32 	%f95, %f82, %f114;
	div.approx.ftz.f32 	%f96, %f81, %f114;
	st.v4.f32 	[%rd98+16], {%f96, %f95, %f94, %f93};
	div.approx.ftz.f32 	%f97, %f88, %f114;
	div.approx.ftz.f32 	%f98, %f87, %f114;
	div.approx.ftz.f32 	%f99, %f86, %f114;
	div.approx.ftz.f32 	%f100, %f85, %f114;
	st.v4.f32 	[%rd98+32], {%f100, %f99, %f98, %f97};
	ld.global.v2.u32 	{%r102, %r103}, [launch_index];
	cvt.u64.u32	%rd112, %r102;
	cvt.u64.u32	%rd113, %r103;
	mov.u64 	%rd123, color_output_buffer;
	cvta.global.u64 	%rd111, %rd123;
	mov.u32 	%r93, 16;
	// inline asm
	call (%rd110), _rt_buffer_get_64, (%rd111, %r19, %r93, %rd112, %rd113, %rd15, %rd15);
	// inline asm
	ld.global.v2.u32 	{%r106, %r107}, [launch_index];
	cvt.u64.u32	%rd118, %r106;
	cvt.u64.u32	%rd119, %r107;
	// inline asm
	call (%rd116), _rt_buffer_get_64, (%rd111, %r19, %r93, %rd118, %rd119, %rd15, %rd15);
	// inline asm
	ld.v4.f32 	{%f101, %f102, %f103, %f104}, [%rd116];
	rcp.approx.ftz.f32 	%f109, %f114;
	mul.ftz.f32 	%f110, %f109, %f104;
	mul.ftz.f32 	%f111, %f109, %f103;
	mul.ftz.f32 	%f112, %f109, %f102;
	mul.ftz.f32 	%f113, %f109, %f101;
	st.v4.f32 	[%rd110], {%f113, %f112, %f111, %f110};

BB0_9:
	ret;
}

	// .globl	_ZN9TSHVectorILi2EE15SHBasisFunctionERK6float3
.visible .func  (.param .align 16 .b8 func_retval0[16]) _ZN9TSHVectorILi2EE15SHBasisFunctionERK6float3(
	.param .b64 _ZN9TSHVectorILi2EE15SHBasisFunctionERK6float3_param_0
)
{
	.reg .f32 	%f<8>;
	.reg .b64 	%rd<2>;


	ld.param.u64 	%rd1, [_ZN9TSHVectorILi2EE15SHBasisFunctionERK6float3_param_0];
	ld.f32 	%f1, [%rd1+4];
	mul.ftz.f32 	%f2, %f1, 0fBEFA2A2C;
	ld.f32 	%f3, [%rd1+8];
	mul.ftz.f32 	%f4, %f3, 0f3EFA2A2C;
	ld.f32 	%f5, [%rd1];
	mul.ftz.f32 	%f6, %f5, 0fBEFA2A2C;
	mov.f32 	%f7, 0f3E906EC1;
	st.param.f32	[func_retval0+0], %f7;
	st.param.f32	[func_retval0+4], %f2;
	st.param.f32	[func_retval0+8], %f4;
	st.param.f32	[func_retval0+12], %f6;
	ret;
}

	// .globl	_ZN9TSHVectorILi3EE15SHBasisFunctionERK6float3
.visible .func  (.param .align 16 .b8 func_retval0[48]) _ZN9TSHVectorILi3EE15SHBasisFunctionERK6float3(
	.param .b64 _ZN9TSHVectorILi3EE15SHBasisFunctionERK6float3_param_0
)
{
	.reg .f32 	%f<24>;
	.reg .b64 	%rd<2>;


	ld.param.u64 	%rd1, [_ZN9TSHVectorILi3EE15SHBasisFunctionERK6float3_param_0];
	ld.f32 	%f1, [%rd1+4];
	mul.ftz.f32 	%f2, %f1, 0fBEFA2A2C;
	ld.f32 	%f3, [%rd1+8];
	mul.ftz.f32 	%f4, %f3, 0f3EFA2A2C;
	ld.f32 	%f5, [%rd1];
	mul.ftz.f32 	%f6, %f5, 0fBEFA2A2C;
	mul.ftz.f32 	%f7, %f5, %f5;
	mul.ftz.f32 	%f8, %f1, %f1;
	mul.ftz.f32 	%f9, %f3, %f3;
	mul.ftz.f32 	%f10, %f5, 0f3F8BD89D;
	mul.ftz.f32 	%f11, %f10, %f1;
	mul.ftz.f32 	%f12, %f1, 0fBF8BD89D;
	mul.ftz.f32 	%f13, %f12, %f3;
	fma.rn.ftz.f32 	%f14, %f9, 0f40400000, 0fBF800000;
	mul.ftz.f32 	%f15, %f14, 0f3EA17B0F;
	mul.ftz.f32 	%f16, %f5, 0fBF8BD89D;
	mul.ftz.f32 	%f17, %f16, %f3;
	sub.ftz.f32 	%f18, %f7, %f8;
	mul.ftz.f32 	%f19, %f18, 0f3F0BD89D;
	mov.f32 	%f20, 0f3E906EC1;
	st.param.f32	[func_retval0+0], %f20;
	st.param.f32	[func_retval0+4], %f2;
	st.param.f32	[func_retval0+8], %f4;
	st.param.f32	[func_retval0+12], %f6;
	st.param.f32	[func_retval0+16], %f11;
	st.param.f32	[func_retval0+20], %f13;
	st.param.f32	[func_retval0+24], %f15;
	st.param.f32	[func_retval0+28], %f17;
	st.param.f32	[func_retval0+32], %f19;
	ret;
}


#####//
// Generated by NVIDIA NVVM Compiler
//
// Compiler Build ID: CL-27506705
// Cuda compilation tools, release 10.2, V10.2.89
// Based on LLVM 3.4svn
//

.version 6.5
.target sm_50
.address_size 64

	// .globl	_Z11launch_mainv
.visible .global .align 4 .b8 debug_input[12];
.visible .global .align 4 .b8 SceneBounds[28];
.visible .global .align 4 .b8 WorldBrickMin[12];
.visible .global .align 4 .b8 WorldChildCellSize[12];
.visible .global .align 1 .b8 output_buffer[1];
.visible .global .align 4 .b8 launch_index[12];
.visible .global .align 4 .u32 max_iteration;
.visible .global .align 4 .b8 pathtrace[4];
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo11debug_inputE[8] = {82, 97, 121, 0, 12, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo11SceneBoundsE[8] = {82, 97, 121, 0, 28, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo13WorldBrickMinE[8] = {82, 97, 121, 0, 12, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo18WorldChildCellSizeE[8] = {82, 97, 121, 0, 12, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo12launch_indexE[8] = {82, 97, 121, 0, 12, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo13max_iterationE[8] = {82, 97, 121, 0, 4, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo9pathtraceE[8] = {82, 97, 121, 0, 4, 0, 0, 0};
.visible .global .align 8 .u64 _ZN21rti_internal_register20reg_bitness_detectorE;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail0E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail1E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail2E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail3E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail4E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail5E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail6E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail7E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail8E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail9E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail0E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail1E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail2E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail3E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail4E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail5E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail6E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail7E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail8E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail9E;
.visible .global .align 4 .u32 _ZN21rti_internal_register14reg_rayIndex_xE;
.visible .global .align 4 .u32 _ZN21rti_internal_register14reg_rayIndex_yE;
.visible .global .align 4 .u32 _ZN21rti_internal_register14reg_rayIndex_zE;
.visible .global .align 1 .b8 _ZN21rti_internal_typename11debug_inputE[12] = {70, 68, 101, 98, 117, 103, 73, 110, 112, 117, 116, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename11SceneBoundsE[13] = {70, 83, 99, 101, 110, 101, 66, 111, 117, 110, 100, 115, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename13WorldBrickMinE[7] = {102, 108, 111, 97, 116, 51, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename18WorldChildCellSizeE[7] = {102, 108, 111, 97, 116, 51, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename12launch_indexE[6] = {117, 105, 110, 116, 51, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename13max_iterationE[13] = {117, 110, 115, 105, 103, 110, 101, 100, 32, 105, 110, 116, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename9pathtraceE[105] = {111, 112, 116, 105, 120, 58, 58, 98, 111, 117, 110, 100, 67, 97, 108, 108, 97, 98, 108, 101, 80, 114, 111, 103, 114, 97, 109, 73, 100, 60, 102, 108, 111, 97, 116, 51, 32, 40, 117, 105, 110, 116, 51, 44, 32, 117, 105, 110, 116, 51, 44, 32, 117, 110, 115, 105, 103, 110, 101, 100, 32, 105, 110, 116, 44, 32, 117, 110, 115, 105, 103, 110, 101, 100, 32, 105, 110, 116, 44, 32, 102, 108, 111, 97, 116, 51, 38, 44, 32, 117, 110, 115, 105, 103, 110, 101, 100, 32, 105, 110, 116, 38, 41, 62, 0};
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum11debug_inputE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum11SceneBoundsE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum13WorldBrickMinE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum18WorldChildCellSizeE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum12launch_indexE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum13max_iterationE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum9pathtraceE = 4921;
.visible .global .align 1 .b8 _ZN21rti_internal_semantic11debug_inputE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic11SceneBoundsE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic13WorldBrickMinE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic18WorldChildCellSizeE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic12launch_indexE[14] = {114, 116, 76, 97, 117, 110, 99, 104, 73, 110, 100, 101, 120, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_semantic13max_iterationE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic9pathtraceE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation11debug_inputE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation11SceneBoundsE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation13WorldBrickMinE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation18WorldChildCellSizeE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation12launch_indexE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation13max_iterationE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation9pathtraceE[1];

.visible .entry _Z11launch_mainv(

)
{
	.local .align 4 .b8 	__local_depot0[16];
	.reg .b64 	%SP;
	.reg .b64 	%SPL;
	.reg .pred 	%p<3>;
	.reg .f32 	%f<189>;
	.reg .b32 	%r<38>;
	.reg .b64 	%rd<68>;


	mov.u64 	%SPL, __local_depot0;
	cvta.local.u64 	%SP, %SPL;
	ld.global.u32 	%r1, [launch_index];
	ld.global.u32 	%r2, [launch_index+4];
	ld.global.u32 	%r3, [launch_index+8];
	mov.u64 	%rd30, output_buffer;
	cvta.global.u64 	%rd19, %rd30;
	mov.u32 	%r19, 3;
	mov.u32 	%r20, 144;
	// inline asm
	call (%rd15, %rd16, %rd17, %rd18), _rt_buffer_get_size_64, (%rd19, %r19, %r20);
	// inline asm
	// inline asm
	call (%rd20, %rd21, %rd22, %rd23), _rt_buffer_get_size_64, (%rd19, %r19, %r20);
	// inline asm
	// inline asm
	call (%rd25, %rd26, %rd27, %rd28), _rt_buffer_get_size_64, (%rd19, %r19, %r20);
	// inline asm
	ld.global.u32 	%r21, [max_iteration];
	mov.f32 	%f188, 0f00000000;
	setp.lt.u32	%p1, %r21, 2;
	@%p1 bra 	BB0_4;

	add.u64 	%rd31, %SP, 0;
	add.u64 	%rd13, %SPL, 0;
	cvt.u64.u32	%rd32, %r2;
	mul.lo.s64 	%rd33, %rd25, %rd32;
	cvt.u64.u32	%rd34, %r1;
	add.s64 	%rd35, %rd33, %rd34;
	cvt.u32.u64	%r7, %rd35;
	cvt.u32.u64	%r8, %rd15;
	cvt.u32.u64	%r9, %rd21;
	mov.u32 	%r10, 1;
	add.u64 	%rd36, %SP, 4;
	add.u64 	%rd14, %SPL, 4;
	mov.u32 	%r23, 0;
	mov.u32 	%r36, %r10;
	mov.u32 	%r37, %r23;

BB0_2:
	st.local.u32 	[%rd13], %r23;
	mov.u64 	%rd50, 0;
	st.local.u32 	[%rd14+4], %rd50;
	st.local.u32 	[%rd14], %rd50;
	st.local.u32 	[%rd14+8], %r23;
	ld.global.u32 	%r24, [pathtrace];
	// inline asm
	call (%rd37), _rt_callable_program_from_id_v2_64, (%r24, %rd50);
	// inline asm
	// Callseq Start 0
	{
	.reg .b32 temp_param_reg;
	// <end>}
	.param .align 4 .b8 param0[12];
	st.param.b32	[param0+0], %r1;
	st.param.b32	[param0+4], %r2;
	st.param.b32	[param0+8], %r3;
	.param .align 4 .b8 param1[12];
	st.param.b32	[param1+0], %r8;
	st.param.b32	[param1+4], %r9;
	st.param.b32	[param1+8], %r10;
	.param .b32 param2;
	st.param.b32	[param2+0], %r7;
	.param .b32 param3;
	st.param.b32	[param3+0], %r36;
	.param .b64 param4;
	st.param.b64	[param4+0], %rd36;
	.param .b64 param5;
	st.param.b64	[param5+0], %rd31;
	.param .align 4 .b8 retval0[12];
	prototype_0 : .callprototype (.param .align 4 .b8 _[12]) _ (.param .align 4 .b8 _[12], .param .align 4 .b8 _[12], .param .b32 _, .param .b32 _, .param .b64 _, .param .b64 _) ;
	call (retval0), 
	%rd37, 
	(
	param0, 
	param1, 
	param2, 
	param3, 
	param4, 
	param5
	)
	, prototype_0;
	ld.param.f32	%f4, [retval0+0];
	ld.param.f32	%f5, [retval0+4];
	ld.param.f32	%f6, [retval0+8];
	
	//{
	}// Callseq End 0
	ld.local.u32 	%r30, [%rd13];
	add.s32 	%r37, %r30, %r37;
	ld.global.u32 	%rd41, [launch_index];
	ld.global.u32 	%rd42, [launch_index+4];
	ld.global.u32 	%rd43, [launch_index+8];
	// inline asm
	call (%rd39), _rt_buffer_get_64, (%rd19, %r19, %r20, %rd41, %rd42, %rd43, %rd50);
	// inline asm
	ld.v4.f32 	{%f7, %f8, %f9, %f10}, [%rd39];
	ld.v4.f32 	{%f15, %f16, %f17, %f18}, [%rd39+16];
	ld.v4.f32 	{%f23, %f24, %f25, %f26}, [%rd39+32];
	ld.v4.f32 	{%f28, %f29, %f30, %f31}, [%rd39+48];
	ld.v4.f32 	{%f36, %f37, %f38, %f39}, [%rd39+64];
	ld.v4.f32 	{%f44, %f45, %f46, %f47}, [%rd39+80];
	ld.v4.f32 	{%f49, %f50, %f51, %f52}, [%rd39+96];
	ld.v4.f32 	{%f57, %f58, %f59, %f60}, [%rd39+112];
	ld.v4.f32 	{%f65, %f66, %f67, %f68}, [%rd39+128];
	ld.local.f32 	%f70, [%rd14+4];
	mul.ftz.f32 	%f71, %f70, 0fBEFA2A2C;
	ld.local.f32 	%f72, [%rd14+8];
	mul.ftz.f32 	%f73, %f72, 0f3EFA2A2C;
	ld.local.f32 	%f74, [%rd14];
	mul.ftz.f32 	%f75, %f74, 0fBEFA2A2C;
	mul.ftz.f32 	%f76, %f74, %f74;
	mul.ftz.f32 	%f77, %f70, %f70;
	mul.ftz.f32 	%f78, %f72, %f72;
	mul.ftz.f32 	%f79, %f74, 0f3F8BD89D;
	mul.ftz.f32 	%f80, %f79, %f70;
	mul.ftz.f32 	%f81, %f70, 0fBF8BD89D;
	mul.ftz.f32 	%f82, %f81, %f72;
	fma.rn.ftz.f32 	%f83, %f78, 0f40400000, 0fBF800000;
	mul.ftz.f32 	%f84, %f83, 0f3EA17B0F;
	mul.ftz.f32 	%f85, %f74, 0fBF8BD89D;
	mul.ftz.f32 	%f86, %f85, %f72;
	sub.ftz.f32 	%f87, %f76, %f77;
	mul.ftz.f32 	%f88, %f87, 0f3F0BD89D;
	ld.global.u32 	%rd47, [launch_index];
	ld.global.u32 	%rd48, [launch_index+4];
	ld.global.u32 	%rd49, [launch_index+8];
	// inline asm
	call (%rd45), _rt_buffer_get_64, (%rd19, %r19, %r20, %rd47, %rd48, %rd49, %rd50);
	// inline asm
	fma.rn.ftz.f32 	%f89, %f4, %f75, %f10;
	fma.rn.ftz.f32 	%f90, %f4, %f73, %f9;
	fma.rn.ftz.f32 	%f91, %f4, %f71, %f8;
	fma.rn.ftz.f32 	%f92, %f4, 0f3E906EC1, %f7;
	st.v4.f32 	[%rd45], {%f92, %f91, %f90, %f89};
	fma.rn.ftz.f32 	%f93, %f4, %f86, %f18;
	fma.rn.ftz.f32 	%f94, %f4, %f82, %f16;
	fma.rn.ftz.f32 	%f95, %f4, %f80, %f15;
	fma.rn.ftz.f32 	%f96, %f4, %f84, %f17;
	st.v4.f32 	[%rd45+16], {%f95, %f94, %f96, %f93};
	fma.rn.ftz.f32 	%f97, %f4, %f88, %f23;
	st.v4.f32 	[%rd45+32], {%f97, %f24, %f25, %f26};
	fma.rn.ftz.f32 	%f101, %f5, %f75, %f31;
	fma.rn.ftz.f32 	%f102, %f5, %f73, %f30;
	fma.rn.ftz.f32 	%f103, %f5, %f71, %f29;
	fma.rn.ftz.f32 	%f104, %f5, 0f3E906EC1, %f28;
	st.v4.f32 	[%rd45+48], {%f104, %f103, %f102, %f101};
	fma.rn.ftz.f32 	%f105, %f5, %f86, %f39;
	fma.rn.ftz.f32 	%f106, %f5, %f82, %f37;
	fma.rn.ftz.f32 	%f107, %f5, %f80, %f36;
	fma.rn.ftz.f32 	%f108, %f5, %f84, %f38;
	st.v4.f32 	[%rd45+64], {%f107, %f106, %f108, %f105};
	fma.rn.ftz.f32 	%f109, %f5, %f88, %f44;
	st.v4.f32 	[%rd45+80], {%f109, %f45, %f46, %f47};
	fma.rn.ftz.f32 	%f113, %f6, %f75, %f52;
	fma.rn.ftz.f32 	%f114, %f6, %f73, %f51;
	fma.rn.ftz.f32 	%f115, %f6, %f71, %f50;
	fma.rn.ftz.f32 	%f116, %f6, 0f3E906EC1, %f49;
	st.v4.f32 	[%rd45+96], {%f116, %f115, %f114, %f113};
	fma.rn.ftz.f32 	%f117, %f6, %f86, %f60;
	fma.rn.ftz.f32 	%f118, %f6, %f82, %f58;
	fma.rn.ftz.f32 	%f119, %f6, %f80, %f57;
	fma.rn.ftz.f32 	%f120, %f6, %f84, %f59;
	st.v4.f32 	[%rd45+112], {%f119, %f118, %f120, %f117};
	fma.rn.ftz.f32 	%f121, %f6, %f88, %f65;
	st.v4.f32 	[%rd45+128], {%f121, %f66, %f67, %f68};
	ld.global.u32 	%r31, [max_iteration];
	add.s32 	%r36, %r36, 1;
	setp.lt.u32	%p2, %r36, %r31;
	@%p2 bra 	BB0_2;

	cvt.rn.f32.u32	%f188, %r37;

BB0_4:
	ld.global.u32 	%rd57, [launch_index];
	ld.global.u32 	%rd58, [launch_index+4];
	ld.global.u32 	%rd59, [launch_index+8];
	mov.u64 	%rd66, 0;
	// inline asm
	call (%rd55), _rt_buffer_get_64, (%rd19, %r19, %r20, %rd57, %rd58, %rd59, %rd66);
	// inline asm
	ld.global.u32 	%rd63, [launch_index];
	ld.global.u32 	%rd64, [launch_index+4];
	ld.global.u32 	%rd65, [launch_index+8];
	// inline asm
	call (%rd61), _rt_buffer_get_64, (%rd19, %r19, %r20, %rd63, %rd64, %rd65, %rd66);
	// inline asm
	ld.f32 	%f125, [%rd61];
	ld.f32 	%f126, [%rd61+4];
	ld.f32 	%f127, [%rd61+8];
	ld.f32 	%f128, [%rd61+12];
	ld.f32 	%f129, [%rd61+16];
	ld.f32 	%f130, [%rd61+20];
	ld.f32 	%f131, [%rd61+24];
	ld.f32 	%f132, [%rd61+28];
	ld.f32 	%f133, [%rd61+32];
	ld.f32 	%f134, [%rd61+48];
	ld.f32 	%f135, [%rd61+52];
	ld.f32 	%f136, [%rd61+56];
	ld.f32 	%f137, [%rd61+60];
	ld.f32 	%f138, [%rd61+64];
	ld.f32 	%f139, [%rd61+68];
	ld.f32 	%f140, [%rd61+72];
	ld.f32 	%f141, [%rd61+76];
	ld.f32 	%f142, [%rd61+80];
	ld.f32 	%f143, [%rd61+96];
	ld.f32 	%f144, [%rd61+100];
	ld.f32 	%f145, [%rd61+104];
	ld.f32 	%f146, [%rd61+108];
	ld.f32 	%f147, [%rd61+112];
	ld.f32 	%f148, [%rd61+116];
	ld.f32 	%f149, [%rd61+120];
	ld.f32 	%f150, [%rd61+124];
	ld.f32 	%f151, [%rd61+128];
	div.approx.ftz.f32 	%f152, %f128, %f188;
	div.approx.ftz.f32 	%f153, %f127, %f188;
	div.approx.ftz.f32 	%f154, %f126, %f188;
	div.approx.ftz.f32 	%f155, %f125, %f188;
	st.v4.f32 	[%rd55], {%f155, %f154, %f153, %f152};
	div.approx.ftz.f32 	%f156, %f132, %f188;
	div.approx.ftz.f32 	%f157, %f131, %f188;
	div.approx.ftz.f32 	%f158, %f130, %f188;
	div.approx.ftz.f32 	%f159, %f129, %f188;
	st.v4.f32 	[%rd55+16], {%f159, %f158, %f157, %f156};
	div.approx.ftz.f32 	%f160, %f133, %f188;
	st.v4.f32 	[%rd55+32], {%f160, %f161, %f162, %f163};
	div.approx.ftz.f32 	%f164, %f137, %f188;
	div.approx.ftz.f32 	%f165, %f136, %f188;
	div.approx.ftz.f32 	%f166, %f135, %f188;
	div.approx.ftz.f32 	%f167, %f134, %f188;
	st.v4.f32 	[%rd55+48], {%f167, %f166, %f165, %f164};
	div.approx.ftz.f32 	%f168, %f141, %f188;
	div.approx.ftz.f32 	%f169, %f140, %f188;
	div.approx.ftz.f32 	%f170, %f139, %f188;
	div.approx.ftz.f32 	%f171, %f138, %f188;
	st.v4.f32 	[%rd55+64], {%f171, %f170, %f169, %f168};
	div.approx.ftz.f32 	%f172, %f142, %f188;
	st.v4.f32 	[%rd55+80], {%f172, %f173, %f174, %f175};
	div.approx.ftz.f32 	%f176, %f146, %f188;
	div.approx.ftz.f32 	%f177, %f145, %f188;
	div.approx.ftz.f32 	%f178, %f144, %f188;
	div.approx.ftz.f32 	%f179, %f143, %f188;
	st.v4.f32 	[%rd55+96], {%f179, %f178, %f177, %f176};
	div.approx.ftz.f32 	%f180, %f150, %f188;
	div.approx.ftz.f32 	%f181, %f149, %f188;
	div.approx.ftz.f32 	%f182, %f148, %f188;
	div.approx.ftz.f32 	%f183, %f147, %f188;
	st.v4.f32 	[%rd55+112], {%f183, %f182, %f181, %f180};
	div.approx.ftz.f32 	%f184, %f151, %f188;
	st.v4.f32 	[%rd55+128], {%f184, %f185, %f186, %f187};
	ret;
}

	// .globl	_ZN9TSHVectorILi2EE15SHBasisFunctionERK6float3
.visible .func  (.param .align 16 .b8 func_retval0[16]) _ZN9TSHVectorILi2EE15SHBasisFunctionERK6float3(
	.param .b64 _ZN9TSHVectorILi2EE15SHBasisFunctionERK6float3_param_0
)
{
	.reg .f32 	%f<8>;
	.reg .b64 	%rd<2>;


	ld.param.u64 	%rd1, [_ZN9TSHVectorILi2EE15SHBasisFunctionERK6float3_param_0];
	ld.f32 	%f1, [%rd1+4];
	mul.ftz.f32 	%f2, %f1, 0fBEFA2A2C;
	ld.f32 	%f3, [%rd1+8];
	mul.ftz.f32 	%f4, %f3, 0f3EFA2A2C;
	ld.f32 	%f5, [%rd1];
	mul.ftz.f32 	%f6, %f5, 0fBEFA2A2C;
	mov.f32 	%f7, 0f3E906EC1;
	st.param.f32	[func_retval0+0], %f7;
	st.param.f32	[func_retval0+4], %f2;
	st.param.f32	[func_retval0+8], %f4;
	st.param.f32	[func_retval0+12], %f6;
	ret;
}

	// .globl	_ZN9TSHVectorILi3EE15SHBasisFunctionERK6float3
.visible .func  (.param .align 16 .b8 func_retval0[48]) _ZN9TSHVectorILi3EE15SHBasisFunctionERK6float3(
	.param .b64 _ZN9TSHVectorILi3EE15SHBasisFunctionERK6float3_param_0
)
{
	.reg .f32 	%f<24>;
	.reg .b64 	%rd<2>;


	ld.param.u64 	%rd1, [_ZN9TSHVectorILi3EE15SHBasisFunctionERK6float3_param_0];
	ld.f32 	%f1, [%rd1+4];
	mul.ftz.f32 	%f2, %f1, 0fBEFA2A2C;
	ld.f32 	%f3, [%rd1+8];
	mul.ftz.f32 	%f4, %f3, 0f3EFA2A2C;
	ld.f32 	%f5, [%rd1];
	mul.ftz.f32 	%f6, %f5, 0fBEFA2A2C;
	mul.ftz.f32 	%f7, %f5, %f5;
	mul.ftz.f32 	%f8, %f1, %f1;
	mul.ftz.f32 	%f9, %f3, %f3;
	mul.ftz.f32 	%f10, %f5, 0f3F8BD89D;
	mul.ftz.f32 	%f11, %f10, %f1;
	mul.ftz.f32 	%f12, %f1, 0fBF8BD89D;
	mul.ftz.f32 	%f13, %f12, %f3;
	fma.rn.ftz.f32 	%f14, %f9, 0f40400000, 0fBF800000;
	mul.ftz.f32 	%f15, %f14, 0f3EA17B0F;
	mul.ftz.f32 	%f16, %f5, 0fBF8BD89D;
	mul.ftz.f32 	%f17, %f16, %f3;
	sub.ftz.f32 	%f18, %f7, %f8;
	mul.ftz.f32 	%f19, %f18, 0f3F0BD89D;
	mov.f32 	%f20, 0f3E906EC1;
	st.param.f32	[func_retval0+0], %f20;
	st.param.f32	[func_retval0+4], %f2;
	st.param.f32	[func_retval0+8], %f4;
	st.param.f32	[func_retval0+12], %f6;
	st.param.f32	[func_retval0+16], %f11;
	st.param.f32	[func_retval0+20], %f13;
	st.param.f32	[func_retval0+24], %f15;
	st.param.f32	[func_retval0+28], %f17;
	st.param.f32	[func_retval0+32], %f19;
	ret;
}


#####//
// Generated by NVIDIA NVVM Compiler
//
// Compiler Build ID: CL-27506705
// Cuda compilation tools, release 10.2, V10.2.89
// Based on LLVM 3.4svn
//

.version 6.5
.target sm_50
.address_size 64

	// .globl	_Z18TonemapGammaAndHDRf
.visible .global .align 4 .b8 debug_input[12];
.visible .global .align 4 .b8 SceneBounds[28];
.visible .global .align 8 .b8 launch_index[8];
.visible .global .align 16 .b8 crop_rect[16];
.visible .global .align 1 .b8 output_buffer[1];
.visible .global .align 1 .b8 denoiser_output_buffer[1];
.visible .global .align 1 .b8 final_output_buffer[1];
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo11debug_inputE[8] = {82, 97, 121, 0, 12, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo11SceneBoundsE[8] = {82, 97, 121, 0, 28, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo12launch_indexE[8] = {82, 97, 121, 0, 8, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo9crop_rectE[8] = {82, 97, 121, 0, 16, 0, 0, 0};
.visible .global .align 8 .u64 _ZN21rti_internal_register20reg_bitness_detectorE;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail0E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail1E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail2E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail3E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail4E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail5E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail6E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail7E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail8E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail9E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail0E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail1E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail2E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail3E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail4E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail5E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail6E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail7E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail8E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail9E;
.visible .global .align 4 .u32 _ZN21rti_internal_register14reg_rayIndex_xE;
.visible .global .align 4 .u32 _ZN21rti_internal_register14reg_rayIndex_yE;
.visible .global .align 4 .u32 _ZN21rti_internal_register14reg_rayIndex_zE;
.visible .global .align 1 .b8 _ZN21rti_internal_typename11debug_inputE[12] = {70, 68, 101, 98, 117, 103, 73, 110, 112, 117, 116, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename11SceneBoundsE[13] = {70, 83, 99, 101, 110, 101, 66, 111, 117, 110, 100, 115, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename12launch_indexE[6] = {117, 105, 110, 116, 50, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename9crop_rectE[7] = {102, 108, 111, 97, 116, 52, 0};
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum11debug_inputE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum11SceneBoundsE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum12launch_indexE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum9crop_rectE = 4919;
.visible .global .align 1 .b8 _ZN21rti_internal_semantic11debug_inputE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic11SceneBoundsE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic12launch_indexE[14] = {114, 116, 76, 97, 117, 110, 99, 104, 73, 110, 100, 101, 120, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_semantic9crop_rectE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation11debug_inputE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation11SceneBoundsE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation12launch_indexE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation9crop_rectE[1];

.visible .func  (.param .b32 func_retval0) _Z18TonemapGammaAndHDRf(
	.param .b32 _Z18TonemapGammaAndHDRf_param_0
)
{
	.reg .f32 	%f<2>;


	ld.param.f32 	%f1, [_Z18TonemapGammaAndHDRf_param_0];
	st.param.f32	[func_retval0+0], %f1;
	ret;
}

	// .globl	_Z11launch_mainv
.visible .entry _Z11launch_mainv(

)
{
	.reg .pred 	%p<10>;
	.reg .f32 	%f<19>;
	.reg .b32 	%r<31>;
	.reg .b64 	%rd<33>;


	ld.global.u64 	%rd1, [launch_index];
	cvt.u32.u64	%r1, %rd1;
	cvt.rn.f32.u32	%f2, %r1;
	ld.global.v4.f32 	{%f3, %f4, %f5, %f6}, [crop_rect];
	setp.leu.ftz.f32	%p4, %f2, %f3;
	setp.gtu.ftz.f32	%p5, %f2, %f5;
	or.pred  	%p6, %p4, %p5;
	mov.pred 	%p9, 0;
	@%p6 bra 	BB1_3;

	shr.u64 	%rd4, %rd1, 32;
	cvt.u32.u64	%r2, %rd4;
	cvt.rn.f32.u32	%f1, %r2;
	ld.global.f32 	%f9, [crop_rect+4];
	setp.leu.ftz.f32	%p8, %f1, %f9;
	@%p8 bra 	BB1_3;

	ld.global.f32 	%f10, [crop_rect+12];
	setp.le.ftz.f32	%p9, %f1, %f10;

BB1_3:
	ld.global.v2.u32 	{%r3, %r4}, [launch_index];
	cvt.u64.u32	%rd2, %r3;
	cvt.u64.u32	%rd3, %r4;
	@%p9 bra 	BB1_5;
	bra.uni 	BB1_4;

BB1_5:
	mov.u64 	%rd31, output_buffer;
	cvta.global.u64 	%rd20, %rd31;
	mov.u32 	%r25, 2;
	mov.u32 	%r26, 16;
	mov.u64 	%rd30, 0;
	// inline asm
	call (%rd19), _rt_buffer_get_64, (%rd20, %r25, %r26, %rd2, %rd3, %rd30, %rd30);
	// inline asm
	ld.v4.f32 	{%f11, %f12, %f13, %f14}, [%rd19];
	ld.global.v2.u32 	{%r27, %r28}, [launch_index];
	cvt.u64.u32	%rd27, %r27;
	cvt.u64.u32	%rd28, %r28;
	mov.u64 	%rd32, final_output_buffer;
	cvta.global.u64 	%rd26, %rd32;
	// inline asm
	call (%rd25), _rt_buffer_get_64, (%rd26, %r25, %r26, %rd27, %rd28, %rd30, %rd30);
	// inline asm
	st.v4.f32 	[%rd25], {%f11, %f12, %f13, %f14};
	bra.uni 	BB1_6;

BB1_4:
	mov.u64 	%rd17, final_output_buffer;
	cvta.global.u64 	%rd6, %rd17;
	mov.u32 	%r9, 2;
	mov.u32 	%r10, 16;
	mov.u64 	%rd16, 0;
	// inline asm
	call (%rd5), _rt_buffer_get_64, (%rd6, %r9, %r10, %rd2, %rd3, %rd16, %rd16);
	// inline asm
	ld.global.v2.u32 	{%r11, %r12}, [launch_index];
	cvt.u64.u32	%rd13, %r11;
	cvt.u64.u32	%rd14, %r12;
	mov.u64 	%rd18, denoiser_output_buffer;
	cvta.global.u64 	%rd12, %rd18;
	// inline asm
	call (%rd11), _rt_buffer_get_64, (%rd12, %r9, %r10, %rd13, %rd14, %rd16, %rd16);
	// inline asm
	ld.v4.u32 	{%r15, %r16, %r17, %r18}, [%rd11];
	st.v4.u32 	[%rd5], {%r15, %r16, %r17, %r18};

BB1_6:
	ret;
}


#####//
// Generated by NVIDIA NVVM Compiler
//
// Compiler Build ID: CL-27506705
// Cuda compilation tools, release 10.2, V10.2.89
// Based on LLVM 3.4svn
//

.version 6.5
.target sm_50
.address_size 64

	// .globl	_Z18TonemapGammaAndHDRf
.visible .global .align 4 .b8 debug_input[12];
.visible .global .align 4 .b8 SceneBounds[28];
.weak .global .align 4 .b8 _ZZ8Prime128jE6Primes[512] = {2, 0, 0, 0, 3, 0, 0, 0, 5, 0, 0, 0, 7, 0, 0, 0, 11, 0, 0, 0, 13, 0, 0, 0, 17, 0, 0, 0, 19, 0, 0, 0, 23, 0, 0, 0, 29, 0, 0, 0, 31, 0, 0, 0, 37, 0, 0, 0, 41, 0, 0, 0, 43, 0, 0, 0, 47, 0, 0, 0, 53, 0, 0, 0, 59, 0, 0, 0, 61, 0, 0, 0, 67, 0, 0, 0, 71, 0, 0, 0, 73, 0, 0, 0, 79, 0, 0, 0, 83, 0, 0, 0, 89, 0, 0, 0, 97, 0, 0, 0, 101, 0, 0, 0, 103, 0, 0, 0, 107, 0, 0, 0, 109, 0, 0, 0, 113, 0, 0, 0, 127, 0, 0, 0, 131, 0, 0, 0, 137, 0, 0, 0, 139, 0, 0, 0, 149, 0, 0, 0, 151, 0, 0, 0, 157, 0, 0, 0, 163, 0, 0, 0, 167, 0, 0, 0, 173, 0, 0, 0, 179, 0, 0, 0, 181, 0, 0, 0, 191, 0, 0, 0, 193, 0, 0, 0, 197, 0, 0, 0, 199, 0, 0, 0, 211, 0, 0, 0, 223, 0, 0, 0, 227, 0, 0, 0, 229, 0, 0, 0, 233, 0, 0, 0, 239, 0, 0, 0, 241, 0, 0, 0, 251, 0, 0, 0, 1, 1, 0, 0, 7, 1, 0, 0, 13, 1, 0, 0, 15, 1, 0, 0, 21, 1, 0, 0, 25, 1, 0, 0, 27, 1, 0, 0, 37, 1, 0, 0, 51, 1, 0, 0, 55, 1, 0, 0, 57, 1, 0, 0, 61, 1, 0, 0, 75, 1, 0, 0, 81, 1, 0, 0, 91, 1, 0, 0, 93, 1, 0, 0, 97, 1, 0, 0, 103, 1, 0, 0, 111, 1, 0, 0, 117, 1, 0, 0, 123, 1, 0, 0, 127, 1, 0, 0, 133, 1, 0, 0, 141, 1, 0, 0, 145, 1, 0, 0, 153, 1, 0, 0, 163, 1, 0, 0, 165, 1, 0, 0, 175, 1, 0, 0, 177, 1, 0, 0, 183, 1, 0, 0, 187, 1, 0, 0, 193, 1, 0, 0, 201, 1, 0, 0, 205, 1, 0, 0, 207, 1, 0, 0, 211, 1, 0, 0, 223, 1, 0, 0, 231, 1, 0, 0, 235, 1, 0, 0, 243, 1, 0, 0, 247, 1, 0, 0, 253, 1, 0, 0, 9, 2, 0, 0, 11, 2, 0, 0, 29, 2, 0, 0, 35, 2, 0, 0, 45, 2, 0, 0, 51, 2, 0, 0, 57, 2, 0, 0, 59, 2, 0, 0, 65, 2, 0, 0, 75, 2, 0, 0, 81, 2, 0, 0, 87, 2, 0, 0, 89, 2, 0, 0, 95, 2, 0, 0, 101, 2, 0, 0, 105, 2, 0, 0, 107, 2, 0, 0, 119, 2, 0, 0, 129, 2, 0, 0, 131, 2, 0, 0, 135, 2, 0, 0, 141, 2, 0, 0, 147, 2, 0, 0, 149, 2, 0, 0, 161, 2, 0, 0, 165, 2, 0, 0, 171, 2, 0, 0, 179, 2, 0, 0, 189, 2, 0, 0, 197, 2, 0, 0, 207, 2, 0, 0};
.weak .global .align 4 .b8 _ZZ8Prime512jE6Primes[2048] = {2, 0, 0, 0, 3, 0, 0, 0, 5, 0, 0, 0, 7, 0, 0, 0, 11, 0, 0, 0, 13, 0, 0, 0, 17, 0, 0, 0, 19, 0, 0, 0, 23, 0, 0, 0, 29, 0, 0, 0, 31, 0, 0, 0, 37, 0, 0, 0, 41, 0, 0, 0, 43, 0, 0, 0, 47, 0, 0, 0, 53, 0, 0, 0, 59, 0, 0, 0, 61, 0, 0, 0, 67, 0, 0, 0, 71, 0, 0, 0, 73, 0, 0, 0, 79, 0, 0, 0, 83, 0, 0, 0, 89, 0, 0, 0, 97, 0, 0, 0, 101, 0, 0, 0, 103, 0, 0, 0, 107, 0, 0, 0, 109, 0, 0, 0, 113, 0, 0, 0, 127, 0, 0, 0, 131, 0, 0, 0, 137, 0, 0, 0, 139, 0, 0, 0, 149, 0, 0, 0, 151, 0, 0, 0, 157, 0, 0, 0, 163, 0, 0, 0, 167, 0, 0, 0, 173, 0, 0, 0, 179, 0, 0, 0, 181, 0, 0, 0, 191, 0, 0, 0, 193, 0, 0, 0, 197, 0, 0, 0, 199, 0, 0, 0, 211, 0, 0, 0, 223, 0, 0, 0, 227, 0, 0, 0, 229, 0, 0, 0, 233, 0, 0, 0, 239, 0, 0, 0, 241, 0, 0, 0, 251, 0, 0, 0, 1, 1, 0, 0, 7, 1, 0, 0, 13, 1, 0, 0, 15, 1, 0, 0, 21, 1, 0, 0, 25, 1, 0, 0, 27, 1, 0, 0, 37, 1, 0, 0, 51, 1, 0, 0, 55, 1, 0, 0, 57, 1, 0, 0, 61, 1, 0, 0, 75, 1, 0, 0, 81, 1, 0, 0, 91, 1, 0, 0, 93, 1, 0, 0, 97, 1, 0, 0, 103, 1, 0, 0, 111, 1, 0, 0, 117, 1, 0, 0, 123, 1, 0, 0, 127, 1, 0, 0, 133, 1, 0, 0, 141, 1, 0, 0, 145, 1, 0, 0, 153, 1, 0, 0, 163, 1, 0, 0, 165, 1, 0, 0, 175, 1, 0, 0, 177, 1, 0, 0, 183, 1, 0, 0, 187, 1, 0, 0, 193, 1, 0, 0, 201, 1, 0, 0, 205, 1, 0, 0, 207, 1, 0, 0, 211, 1, 0, 0, 223, 1, 0, 0, 231, 1, 0, 0, 235, 1, 0, 0, 243, 1, 0, 0, 247, 1, 0, 0, 253, 1, 0, 0, 9, 2, 0, 0, 11, 2, 0, 0, 29, 2, 0, 0, 35, 2, 0, 0, 45, 2, 0, 0, 51, 2, 0, 0, 57, 2, 0, 0, 59, 2, 0, 0, 65, 2, 0, 0, 75, 2, 0, 0, 81, 2, 0, 0, 87, 2, 0, 0, 89, 2, 0, 0, 95, 2, 0, 0, 101, 2, 0, 0, 105, 2, 0, 0, 107, 2, 0, 0, 119, 2, 0, 0, 129, 2, 0, 0, 131, 2, 0, 0, 135, 2, 0, 0, 141, 2, 0, 0, 147, 2, 0, 0, 149, 2, 0, 0, 161, 2, 0, 0, 165, 2, 0, 0, 171, 2, 0, 0, 179, 2, 0, 0, 189, 2, 0, 0, 197, 2, 0, 0, 207, 2, 0, 0, 215, 2, 0, 0, 221, 2, 0, 0, 227, 2, 0, 0, 231, 2, 0, 0, 239, 2, 0, 0, 245, 2, 0, 0, 249, 2, 0, 0, 1, 3, 0, 0, 5, 3, 0, 0, 19, 3, 0, 0, 29, 3, 0, 0, 41, 3, 0, 0, 43, 3, 0, 0, 53, 3, 0, 0, 55, 3, 0, 0, 59, 3, 0, 0, 61, 3, 0, 0, 71, 3, 0, 0, 85, 3, 0, 0, 89, 3, 0, 0, 91, 3, 0, 0, 95, 3, 0, 0, 109, 3, 0, 0, 113, 3, 0, 0, 115, 3, 0, 0, 119, 3, 0, 0, 139, 3, 0, 0, 143, 3, 0, 0, 151, 3, 0, 0, 161, 3, 0, 0, 169, 3, 0, 0, 173, 3, 0, 0, 179, 3, 0, 0, 185, 3, 0, 0, 199, 3, 0, 0, 203, 3, 0, 0, 209, 3, 0, 0, 215, 3, 0, 0, 223, 3, 0, 0, 229, 3, 0, 0, 241, 3, 0, 0, 245, 3, 0, 0, 251, 3, 0, 0, 253, 3, 0, 0, 7, 4, 0, 0, 9, 4, 0, 0, 15, 4, 0, 0, 25, 4, 0, 0, 27, 4, 0, 0, 37, 4, 0, 0, 39, 4, 0, 0, 45, 4, 0, 0, 63, 4, 0, 0, 67, 4, 0, 0, 69, 4, 0, 0, 73, 4, 0, 0, 79, 4, 0, 0, 85, 4, 0, 0, 93, 4, 0, 0, 99, 4, 0, 0, 105, 4, 0, 0, 127, 4, 0, 0, 129, 4, 0, 0, 139, 4, 0, 0, 147, 4, 0, 0, 157, 4, 0, 0, 163, 4, 0, 0, 169, 4, 0, 0, 177, 4, 0, 0, 189, 4, 0, 0, 193, 4, 0, 0, 199, 4, 0, 0, 205, 4, 0, 0, 207, 4, 0, 0, 213, 4, 0, 0, 225, 4, 0, 0, 235, 4, 0, 0, 253, 4, 0, 0, 255, 4, 0, 0, 3, 5, 0, 0, 9, 5, 0, 0, 11, 5, 0, 0, 17, 5, 0, 0, 21, 5, 0, 0, 23, 5, 0, 0, 27, 5, 0, 0, 39, 5, 0, 0, 41, 5, 0, 0, 47, 5, 0, 0, 81, 5, 0, 0, 87, 5, 0, 0, 93, 5, 0, 0, 101, 5, 0, 0, 119, 5, 0, 0, 129, 5, 0, 0, 143, 5, 0, 0, 147, 5, 0, 0, 149, 5, 0, 0, 153, 5, 0, 0, 159, 5, 0, 0, 167, 5, 0, 0, 171, 5, 0, 0, 173, 5, 0, 0, 179, 5, 0, 0, 191, 5, 0, 0, 201, 5, 0, 0, 203, 5, 0, 0, 207, 5, 0, 0, 209, 5, 0, 0, 213, 5, 0, 0, 219, 5, 0, 0, 231, 5, 0, 0, 243, 5, 0, 0, 251, 5, 0, 0, 7, 6, 0, 0, 13, 6, 0, 0, 17, 6, 0, 0, 23, 6, 0, 0, 31, 6, 0, 0, 35, 6, 0, 0, 43, 6, 0, 0, 47, 6, 0, 0, 61, 6, 0, 0, 65, 6, 0, 0, 71, 6, 0, 0, 73, 6, 0, 0, 77, 6, 0, 0, 83, 6, 0, 0, 85, 6, 0, 0, 91, 6, 0, 0, 101, 6, 0, 0, 121, 6, 0, 0, 127, 6, 0, 0, 131, 6, 0, 0, 133, 6, 0, 0, 157, 6, 0, 0, 161, 6, 0, 0, 163, 6, 0, 0, 173, 6, 0, 0, 185, 6, 0, 0, 187, 6, 0, 0, 197, 6, 0, 0, 205, 6, 0, 0, 211, 6, 0, 0, 217, 6, 0, 0, 223, 6, 0, 0, 241, 6, 0, 0, 247, 6, 0, 0, 251, 6, 0, 0, 253, 6, 0, 0, 9, 7, 0, 0, 19, 7, 0, 0, 31, 7, 0, 0, 39, 7, 0, 0, 55, 7, 0, 0, 69, 7, 0, 0, 75, 7, 0, 0, 79, 7, 0, 0, 81, 7, 0, 0, 85, 7, 0, 0, 87, 7, 0, 0, 97, 7, 0, 0, 109, 7, 0, 0, 115, 7, 0, 0, 121, 7, 0, 0, 139, 7, 0, 0, 141, 7, 0, 0, 157, 7, 0, 0, 159, 7, 0, 0, 181, 7, 0, 0, 187, 7, 0, 0, 195, 7, 0, 0, 201, 7, 0, 0, 205, 7, 0, 0, 207, 7, 0, 0, 211, 7, 0, 0, 219, 7, 0, 0, 225, 7, 0, 0, 235, 7, 0, 0, 237, 7, 0, 0, 247, 7, 0, 0, 5, 8, 0, 0, 15, 8, 0, 0, 21, 8, 0, 0, 33, 8, 0, 0, 35, 8, 0, 0, 39, 8, 0, 0, 41, 8, 0, 0, 51, 8, 0, 0, 63, 8, 0, 0, 65, 8, 0, 0, 81, 8, 0, 0, 83, 8, 0, 0, 89, 8, 0, 0, 93, 8, 0, 0, 95, 8, 0, 0, 105, 8, 0, 0, 113, 8, 0, 0, 131, 8, 0, 0, 155, 8, 0, 0, 159, 8, 0, 0, 165, 8, 0, 0, 173, 8, 0, 0, 189, 8, 0, 0, 191, 8, 0, 0, 195, 8, 0, 0, 203, 8, 0, 0, 219, 8, 0, 0, 221, 8, 0, 0, 225, 8, 0, 0, 233, 8, 0, 0, 239, 8, 0, 0, 245, 8, 0, 0, 249, 8, 0, 0, 5, 9, 0, 0, 7, 9, 0, 0, 29, 9, 0, 0, 35, 9, 0, 0, 37, 9, 0, 0, 43, 9, 0, 0, 47, 9, 0, 0, 53, 9, 0, 0, 67, 9, 0, 0, 73, 9, 0, 0, 77, 9, 0, 0, 79, 9, 0, 0, 85, 9, 0, 0, 89, 9, 0, 0, 95, 9, 0, 0, 107, 9, 0, 0, 113, 9, 0, 0, 119, 9, 0, 0, 133, 9, 0, 0, 137, 9, 0, 0, 143, 9, 0, 0, 155, 9, 0, 0, 163, 9, 0, 0, 169, 9, 0, 0, 173, 9, 0, 0, 199, 9, 0, 0, 217, 9, 0, 0, 227, 9, 0, 0, 235, 9, 0, 0, 239, 9, 0, 0, 245, 9, 0, 0, 247, 9, 0, 0, 253, 9, 0, 0, 19, 10, 0, 0, 31, 10, 0, 0, 33, 10, 0, 0, 49, 10, 0, 0, 57, 10, 0, 0, 61, 10, 0, 0, 73, 10, 0, 0, 87, 10, 0, 0, 97, 10, 0, 0, 99, 10, 0, 0, 103, 10, 0, 0, 111, 10, 0, 0, 117, 10, 0, 0, 123, 10, 0, 0, 127, 10, 0, 0, 129, 10, 0, 0, 133, 10, 0, 0, 139, 10, 0, 0, 147, 10, 0, 0, 151, 10, 0, 0, 153, 10, 0, 0, 159, 10, 0, 0, 169, 10, 0, 0, 171, 10, 0, 0, 181, 10, 0, 0, 189, 10, 0, 0, 193, 10, 0, 0, 207, 10, 0, 0, 217, 10, 0, 0, 229, 10, 0, 0, 231, 10, 0, 0, 237, 10, 0, 0, 241, 10, 0, 0, 243, 10, 0, 0, 3, 11, 0, 0, 17, 11, 0, 0, 21, 11, 0, 0, 27, 11, 0, 0, 35, 11, 0, 0, 41, 11, 0, 0, 45, 11, 0, 0, 63, 11, 0, 0, 71, 11, 0, 0, 81, 11, 0, 0, 87, 11, 0, 0, 93, 11, 0, 0, 101, 11, 0, 0, 111, 11, 0, 0, 123, 11, 0, 0, 137, 11, 0, 0, 141, 11, 0, 0, 147, 11, 0, 0, 153, 11, 0, 0, 155, 11, 0, 0, 183, 11, 0, 0, 185, 11, 0, 0, 195, 11, 0, 0, 203, 11, 0, 0, 207, 11, 0, 0, 221, 11, 0, 0, 225, 11, 0, 0, 233, 11, 0, 0, 245, 11, 0, 0, 251, 11, 0, 0, 7, 12, 0, 0, 11, 12, 0, 0, 17, 12, 0, 0, 37, 12, 0, 0, 47, 12, 0, 0, 49, 12, 0, 0, 65, 12, 0, 0, 91, 12, 0, 0, 95, 12, 0, 0, 97, 12, 0, 0, 109, 12, 0, 0, 115, 12, 0, 0, 119, 12, 0, 0, 131, 12, 0, 0, 137, 12, 0, 0, 145, 12, 0, 0, 149, 12, 0, 0, 157, 12, 0, 0, 179, 12, 0, 0, 181, 12, 0, 0, 185, 12, 0, 0, 187, 12, 0, 0, 199, 12, 0, 0, 227, 12, 0, 0, 229, 12, 0, 0, 235, 12, 0, 0, 241, 12, 0, 0, 247, 12, 0, 0, 251, 12, 0, 0, 1, 13, 0, 0, 3, 13, 0, 0, 15, 13, 0, 0, 19, 13, 0, 0, 31, 13, 0, 0, 33, 13, 0, 0, 43, 13, 0, 0, 45, 13, 0, 0, 61, 13, 0, 0, 63, 13, 0, 0, 79, 13, 0, 0, 85, 13, 0, 0, 105, 13, 0, 0, 121, 13, 0, 0, 129, 13, 0, 0, 133, 13, 0, 0, 135, 13, 0, 0, 139, 13, 0, 0, 141, 13, 0, 0, 163, 13, 0, 0, 171, 13, 0, 0, 183, 13, 0, 0, 189, 13, 0, 0, 199, 13, 0, 0, 201, 13, 0, 0, 205, 13, 0, 0, 211, 13, 0, 0, 213, 13, 0, 0, 219, 13, 0, 0, 229, 13, 0, 0, 231, 13, 0, 0, 243, 13, 0, 0, 253, 13, 0, 0, 255, 13, 0, 0, 9, 14, 0, 0, 23, 14, 0, 0, 29, 14, 0, 0, 33, 14, 0, 0, 39, 14, 0, 0, 47, 14, 0, 0, 53, 14, 0, 0, 59, 14, 0, 0, 75, 14, 0, 0, 87, 14, 0, 0};
.visible .global .align 1 .b8 SceneLightsData[1];
.visible .global .align 1 .b8 SceneLightPowers[1];
.visible .global .align 1 .b8 SceneObjectRelevantLightIndexData[1];
.visible .global .align 1 .b8 SceneObjectRelevantLightRangeData[1];
.visible .global .align 16 .b8 SkyLight[48];
.visible .global .align 4 .u32 SkyLightTextureSamplerID;
.visible .global .align 4 .b8 top_object[4];
.visible .global .align 4 .b8 top_shadower[4];
.visible .global .align 4 .b8 sample_emitter[4];
.visible .global .align 4 .u32 max_depth;
.visible .global .align 4 .u32 max_sky_bounces;
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo11debug_inputE[8] = {82, 97, 121, 0, 12, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo11SceneBoundsE[8] = {82, 97, 121, 0, 28, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo8SkyLightE[8] = {82, 97, 121, 0, 48, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo24SkyLightTextureSamplerIDE[8] = {82, 97, 121, 0, 4, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo10top_objectE[8] = {82, 97, 121, 0, 4, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo12top_shadowerE[8] = {82, 97, 121, 0, 4, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo14sample_emitterE[8] = {82, 97, 121, 0, 4, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo9max_depthE[8] = {82, 97, 121, 0, 4, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo15max_sky_bouncesE[8] = {82, 97, 121, 0, 4, 0, 0, 0};
.visible .global .align 8 .u64 _ZN21rti_internal_register20reg_bitness_detectorE;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail0E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail1E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail2E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail3E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail4E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail5E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail6E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail7E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail8E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail9E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail0E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail1E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail2E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail3E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail4E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail5E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail6E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail7E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail8E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail9E;
.visible .global .align 4 .u32 _ZN21rti_internal_register14reg_rayIndex_xE;
.visible .global .align 4 .u32 _ZN21rti_internal_register14reg_rayIndex_yE;
.visible .global .align 4 .u32 _ZN21rti_internal_register14reg_rayIndex_zE;
.visible .global .align 1 .b8 _ZN21rti_internal_typename11debug_inputE[12] = {70, 68, 101, 98, 117, 103, 73, 110, 112, 117, 116, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename11SceneBoundsE[13] = {70, 83, 99, 101, 110, 101, 66, 111, 117, 110, 100, 115, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename8SkyLightE[14] = {70, 83, 107, 121, 76, 105, 103, 104, 116, 68, 97, 116, 97, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename24SkyLightTextureSamplerIDE[4] = {105, 110, 116, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename10top_objectE[9] = {114, 116, 79, 98, 106, 101, 99, 116, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename12top_shadowerE[9] = {114, 116, 79, 98, 106, 101, 99, 116, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename14sample_emitterE[104] = {111, 112, 116, 105, 120, 58, 58, 98, 111, 117, 110, 100, 67, 97, 108, 108, 97, 98, 108, 101, 80, 114, 111, 103, 114, 97, 109, 73, 100, 60, 118, 111, 105, 100, 32, 40, 117, 105, 110, 116, 51, 44, 32, 117, 105, 110, 116, 51, 44, 82, 97, 110, 100, 111, 109, 83, 101, 113, 117, 101, 110, 99, 101, 38, 44, 117, 110, 115, 105, 103, 110, 101, 100, 32, 105, 110, 116, 38, 44, 82, 97, 121, 68, 101, 115, 99, 38, 44, 102, 108, 111, 97, 116, 38, 32, 79, 117, 116, 80, 100, 102, 41, 62, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename9max_depthE[13] = {117, 110, 115, 105, 103, 110, 101, 100, 32, 105, 110, 116, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename15max_sky_bouncesE[13] = {117, 110, 115, 105, 103, 110, 101, 100, 32, 105, 110, 116, 0};
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum11debug_inputE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum11SceneBoundsE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum8SkyLightE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum24SkyLightTextureSamplerIDE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum10top_objectE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum12top_shadowerE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum14sample_emitterE = 4921;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum9max_depthE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum15max_sky_bouncesE = 4919;
.visible .global .align 1 .b8 _ZN21rti_internal_semantic11debug_inputE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic11SceneBoundsE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic8SkyLightE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic24SkyLightTextureSamplerIDE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic10top_objectE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic12top_shadowerE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic14sample_emitterE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic9max_depthE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic15max_sky_bouncesE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation11debug_inputE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation11SceneBoundsE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation8SkyLightE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation24SkyLightTextureSamplerIDE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation10top_objectE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation12top_shadowerE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation14sample_emitterE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation9max_depthE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation15max_sky_bouncesE[1];

.visible .func  (.param .b32 func_retval0) _Z18TonemapGammaAndHDRf(
	.param .b32 _Z18TonemapGammaAndHDRf_param_0
)
{
	.reg .pred 	%p<2>;
	.reg .f32 	%f<8>;


	ld.param.f32 	%f5, [_Z18TonemapGammaAndHDRf_param_0];
	setp.gt.ftz.f32	%p1, %f5, 0f3F800000;
	lg2.approx.ftz.f32 	%f1, %f5;
	@%p1 bra 	BB0_2;
	bra.uni 	BB0_1;

BB0_2:
	mul.ftz.f32 	%f7, %f1, 0f3F317218;
	bra.uni 	BB0_3;

BB0_1:
	mul.ftz.f32 	%f6, %f1, 0f400CCCCD;
	ex2.approx.ftz.f32 	%f7, %f6;

BB0_3:
	st.param.f32	[func_retval0+0], %f7;
	ret;
}

	// .globl	_Z9pathtrace5uint3S_jjR6float3RjS2_S1_S1_
.visible .func  (.param .align 4 .b8 func_retval0[12]) _Z9pathtrace5uint3S_jjR6float3RjS2_S1_S1_(
	.param .align 4 .b8 _Z9pathtrace5uint3S_jjR6float3RjS2_S1_S1__param_0[12],
	.param .align 4 .b8 _Z9pathtrace5uint3S_jjR6float3RjS2_S1_S1__param_1[12],
	.param .b32 _Z9pathtrace5uint3S_jjR6float3RjS2_S1_S1__param_2,
	.param .b32 _Z9pathtrace5uint3S_jjR6float3RjS2_S1_S1__param_3,
	.param .b64 _Z9pathtrace5uint3S_jjR6float3RjS2_S1_S1__param_4,
	.param .b64 _Z9pathtrace5uint3S_jjR6float3RjS2_S1_S1__param_5,
	.param .b64 _Z9pathtrace5uint3S_jjR6float3RjS2_S1_S1__param_6,
	.param .b64 _Z9pathtrace5uint3S_jjR6float3RjS2_S1_S1__param_7,
	.param .b64 _Z9pathtrace5uint3S_jjR6float3RjS2_S1_S1__param_8
)
{
	.local .align 16 .b8 	__local_depot1[320];
	.reg .b64 	%SP;
	.reg .b64 	%SPL;
	.reg .pred 	%p<107>;
	.reg .b16 	%rs<4>;
	.reg .f32 	%f<1356>;
	.reg .b32 	%r<415>;
	.reg .f64 	%fd<28>;
	.reg .b64 	%rd<329>;


	mov.u64 	%SPL, __local_depot1;
	cvta.local.u64 	%SP, %SPL;
	ld.param.u32 	%r62, [_Z9pathtrace5uint3S_jjR6float3RjS2_S1_S1__param_0];
	ld.param.u32 	%r63, [_Z9pathtrace5uint3S_jjR6float3RjS2_S1_S1__param_0+4];
	ld.param.u32 	%r64, [_Z9pathtrace5uint3S_jjR6float3RjS2_S1_S1__param_0+8];
	ld.param.u32 	%r65, [_Z9pathtrace5uint3S_jjR6float3RjS2_S1_S1__param_1];
	ld.param.u32 	%r66, [_Z9pathtrace5uint3S_jjR6float3RjS2_S1_S1__param_1+4];
	ld.param.u32 	%r67, [_Z9pathtrace5uint3S_jjR6float3RjS2_S1_S1__param_1+8];
	ld.param.u32 	%r68, [_Z9pathtrace5uint3S_jjR6float3RjS2_S1_S1__param_2];
	ld.param.u32 	%r69, [_Z9pathtrace5uint3S_jjR6float3RjS2_S1_S1__param_3];
	ld.param.u64 	%rd49, [_Z9pathtrace5uint3S_jjR6float3RjS2_S1_S1__param_4];
	add.u64 	%rd56, %SP, 0;
	add.u64 	%rd1, %SPL, 0;
	add.u64 	%rd57, %SP, 276;
	add.u64 	%rd2, %SPL, 276;
	add.u64 	%rd58, %SP, 312;
	add.u64 	%rd59, %SPL, 312;
	add.u64 	%rd60, %SP, 256;
	add.u64 	%rd61, %SPL, 256;
	add.s64 	%rd3, %rd61, 4;
	st.local.u32 	[%rd61+4], %r68;
	st.local.u32 	[%rd61+8], %r69;
	shl.b32 	%r70, %r69, 4;
	add.s32 	%r71, %r70, -1556008596;
	shr.u32 	%r72, %r69, 5;
	add.s32 	%r73, %r72, -939442524;
	add.s32 	%r74, %r69, -1640531527;
	xor.b32  	%r75, %r71, %r74;
	xor.b32  	%r76, %r75, %r73;
	add.s32 	%r77, %r76, %r68;
	add.s32 	%r78, %r77, -1640531527;
	shl.b32 	%r79, %r77, 4;
	add.s32 	%r80, %r79, -1383041155;
	xor.b32  	%r81, %r80, %r78;
	shr.u32 	%r82, %r77, 5;
	add.s32 	%r83, %r82, 2123724318;
	xor.b32  	%r84, %r81, %r83;
	add.s32 	%r85, %r84, %r69;
	add.s32 	%r86, %r85, 1013904242;
	shl.b32 	%r87, %r85, 4;
	add.s32 	%r88, %r87, -1556008596;
	shr.u32 	%r89, %r85, 5;
	add.s32 	%r90, %r89, -939442524;
	xor.b32  	%r91, %r88, %r86;
	xor.b32  	%r92, %r91, %r90;
	add.s32 	%r93, %r92, %r77;
	add.s32 	%r94, %r93, 1013904242;
	shl.b32 	%r95, %r93, 4;
	add.s32 	%r96, %r95, -1383041155;
	xor.b32  	%r97, %r96, %r94;
	shr.u32 	%r98, %r93, 5;
	add.s32 	%r99, %r98, 2123724318;
	xor.b32  	%r100, %r97, %r99;
	add.s32 	%r101, %r100, %r85;
	add.s32 	%r102, %r101, -626627285;
	shl.b32 	%r103, %r101, 4;
	add.s32 	%r104, %r103, -1556008596;
	shr.u32 	%r105, %r101, 5;
	add.s32 	%r106, %r105, -939442524;
	xor.b32  	%r107, %r104, %r102;
	xor.b32  	%r108, %r107, %r106;
	add.s32 	%r109, %r108, %r93;
	add.s32 	%r110, %r109, -626627285;
	shl.b32 	%r111, %r109, 4;
	add.s32 	%r112, %r111, -1383041155;
	xor.b32  	%r113, %r112, %r110;
	shr.u32 	%r114, %r109, 5;
	add.s32 	%r115, %r114, 2123724318;
	xor.b32  	%r116, %r113, %r115;
	add.s32 	%r117, %r116, %r101;
	add.s32 	%r118, %r117, 2027808484;
	shl.b32 	%r119, %r117, 4;
	add.s32 	%r120, %r119, -1556008596;
	shr.u32 	%r121, %r117, 5;
	add.s32 	%r122, %r121, -939442524;
	xor.b32  	%r123, %r120, %r118;
	xor.b32  	%r124, %r123, %r122;
	add.s32 	%r125, %r124, %r109;
	add.s32 	%r126, %r125, 2027808484;
	shl.b32 	%r127, %r125, 4;
	add.s32 	%r128, %r127, -1383041155;
	xor.b32  	%r129, %r128, %r126;
	shr.u32 	%r130, %r125, 5;
	add.s32 	%r131, %r130, 2123724318;
	xor.b32  	%r132, %r129, %r131;
	add.s32 	%r133, %r132, %r117;
	add.s32 	%r134, %r133, 387276957;
	shl.b32 	%r135, %r133, 4;
	add.s32 	%r136, %r135, -1556008596;
	shr.u32 	%r137, %r133, 5;
	add.s32 	%r138, %r137, -939442524;
	xor.b32  	%r139, %r136, %r134;
	xor.b32  	%r140, %r139, %r138;
	add.s32 	%r141, %r140, %r125;
	add.s32 	%r142, %r141, 387276957;
	shl.b32 	%r143, %r141, 4;
	add.s32 	%r144, %r143, -1383041155;
	xor.b32  	%r145, %r144, %r142;
	shr.u32 	%r146, %r141, 5;
	add.s32 	%r147, %r146, 2123724318;
	xor.b32  	%r148, %r145, %r147;
	add.s32 	%r149, %r148, %r133;
	add.s32 	%r150, %r149, -1253254570;
	shl.b32 	%r151, %r149, 4;
	add.s32 	%r152, %r151, -1556008596;
	shr.u32 	%r153, %r149, 5;
	add.s32 	%r154, %r153, -939442524;
	xor.b32  	%r155, %r152, %r150;
	xor.b32  	%r156, %r155, %r154;
	add.s32 	%r157, %r156, %r141;
	add.s32 	%r158, %r157, -1253254570;
	shl.b32 	%r159, %r157, 4;
	add.s32 	%r160, %r159, -1383041155;
	xor.b32  	%r161, %r160, %r158;
	shr.u32 	%r162, %r157, 5;
	add.s32 	%r163, %r162, 2123724318;
	xor.b32  	%r164, %r161, %r163;
	add.s32 	%r165, %r164, %r149;
	add.s32 	%r166, %r165, 1401181199;
	shl.b32 	%r167, %r165, 4;
	add.s32 	%r168, %r167, -1556008596;
	shr.u32 	%r169, %r165, 5;
	add.s32 	%r170, %r169, -939442524;
	xor.b32  	%r171, %r168, %r166;
	xor.b32  	%r172, %r171, %r170;
	add.s32 	%r173, %r172, %r157;
	add.s32 	%r174, %r173, 1401181199;
	shl.b32 	%r175, %r173, 4;
	add.s32 	%r176, %r175, -1383041155;
	xor.b32  	%r177, %r176, %r174;
	shr.u32 	%r178, %r173, 5;
	add.s32 	%r179, %r178, 2123724318;
	xor.b32  	%r180, %r177, %r179;
	add.s32 	%r181, %r180, %r165;
	add.s32 	%r182, %r181, -239350328;
	shl.b32 	%r183, %r181, 4;
	add.s32 	%r184, %r183, -1556008596;
	shr.u32 	%r185, %r181, 5;
	add.s32 	%r186, %r185, -939442524;
	xor.b32  	%r187, %r184, %r182;
	xor.b32  	%r188, %r187, %r186;
	add.s32 	%r189, %r188, %r173;
	st.local.u32 	[%rd61+12], %r189;
	mov.u32 	%r1, 0;
	st.local.u32 	[%rd61+16], %r1;
	mov.u32 	%r191, 2;
	st.local.u32 	[%rd61], %r191;
	st.local.u32 	[%rd2], %r1;
	st.local.u32 	[%rd59], %r1;
	ld.global.u32 	%r61, [sample_emitter];
	mov.u64 	%rd55, 0;
	// inline asm
	call (%rd54), _rt_callable_program_from_id_v2_64, (%r61, %rd55);
	// inline asm
	add.u64 	%rd63, %SP, 280;
	// Callseq Start 0
	{
	.reg .b32 temp_param_reg;
	// <end>}
	.param .align 4 .b8 param0[12];
	st.param.b32	[param0+0], %r62;
	st.param.b32	[param0+4], %r63;
	st.param.b32	[param0+8], %r64;
	.param .align 4 .b8 param1[12];
	st.param.b32	[param1+0], %r65;
	st.param.b32	[param1+4], %r66;
	st.param.b32	[param1+8], %r67;
	.param .b64 param2;
	st.param.b64	[param2+0], %rd60;
	.param .b64 param3;
	st.param.b64	[param3+0], %rd57;
	.param .b64 param4;
	st.param.b64	[param4+0], %rd63;
	.param .b64 param5;
	st.param.b64	[param5+0], %rd58;
	prototype_0 : .callprototype ()_ (.param .align 4 .b8 _[12], .param .align 4 .b8 _[12], .param .b64 _, .param .b64 _, .param .b64 _, .param .b64 _) ;
	call 
	%rd54, 
	(
	param0, 
	param1, 
	param2, 
	param3, 
	param4, 
	param5
	)
	, prototype_0;
	
	//{
	}// Callseq End 0
	ld.local.f32 	%f353, [%rd59];
	mov.f32 	%f1353, 0f00000000;
	setp.le.ftz.f32	%p2, %f353, 0f00000000;
	mov.f32 	%f1354, %f1353;
	mov.f32 	%f1355, %f1353;
	@%p2 bra 	BB1_15;

	cvta.to.local.u64 	%rd66, %rd63;
	add.s64 	%rd4, %rd66, 12;
	ld.local.f32 	%f357, [%rd66+12];
	ld.local.f32 	%f358, [%rd66+16];
	ld.local.f32 	%f359, [%rd66+20];
	st.f32 	[%rd49], %f357;
	st.f32 	[%rd49+4], %f358;
	st.f32 	[%rd49+8], %f359;
	ld.local.f32 	%f354, [%rd66];
	ld.local.f32 	%f355, [%rd66+4];
	ld.local.f32 	%f356, [%rd66+8];
	ld.local.f32 	%f360, [%rd66+24];
	ld.local.f32 	%f361, [%rd66+28];
	cvta.to.local.u64 	%rd67, %rd56;
	mov.u32 	%r198, -1082130432;
	st.local.u32 	[%rd67], %r198;
	mov.u16 	%rs1, 0;
	st.local.u16 	[%rd67+28], %rs1;
	mov.f32 	%f20, 0f3F800000;
	st.local.v4.f32 	[%rd67+48], {%f20, %f20, %f20, %f20};
	ld.global.u32 	%r192, [top_object];
	mov.u32 	%r194, 255;
	mov.u32 	%r196, 80;
	// inline asm
	call _rt_trace_mask_flags_64, (%r192, %f354, %f355, %f356, %f357, %f358, %f359, %r1, %f360, %f361, %r194, %r1, %rd56, %r196);
	// inline asm
	ld.local.v2.u64 	{%rd321, %rd18}, [%rd67];
	ld.local.v2.u64 	{%rd323, %rd20}, [%rd67+16];
	ld.local.v2.u64 	{%rd74, %rd24}, [%rd67+64];
	add.s64 	%rd14, %rd67, 28;
	mov.f32 	%f23, 0f00000000;
	mov.f32 	%f21, %f20;
	mov.f32 	%f22, %f20;
	mov.f32 	%f24, %f23;
	mov.f32 	%f25, %f23;
	mov.f32 	%f29, %f20;
	bra.uni 	BB1_2;

BB1_134:
	mov.u32 	%r380, 255;
	ld.local.f32 	%f1229, [%rd4+-12];
	ld.local.f32 	%f1230, [%rd4+-8];
	ld.local.f32 	%f1231, [%rd4+-4];
	ld.local.f32 	%f1235, [%rd4+12];
	ld.local.f32 	%f1236, [%rd4+16];
	st.local.u32 	[%rd67], %r198;
	st.local.u16 	[%rd14], %rs1;
	mov.f32 	%f1237, 0f3F800000;
	st.local.v4.f32 	[%rd14+20], {%f1237, %f1237, %f1237, %f1237};
	ld.global.u32 	%r372, [top_object];
	mov.u32 	%r375, 0;
	// inline asm
	call _rt_trace_mask_flags_64, (%r372, %f1229, %f1230, %f1231, %f77, %f78, %f79, %r375, %f1235, %f1236, %r380, %r375, %rd56, %r196);
	// inline asm
	ld.local.v2.u64 	{%rd321, %rd18}, [%rd67];
	ld.local.v2.u64 	{%rd323, %rd20}, [%rd14+-12];
	ld.local.v2.u64 	{%rd305, %rd24}, [%rd14+36];

BB1_2:
	mov.b64	{%r2, %r3}, %rd323;
	mov.b64	{%r4, %r5}, %rd20;
	shr.u64 	%rd26, %rd20, 32;
	mov.b32 	 %f30, %r3;
	ld.global.u32 	%r199, [max_depth];
	setp.ge.u32	%p3, %r1, %r199;
	@%p3 bra 	BB1_14;

	mov.b64	{%r6, %r7}, %rd321;
	mov.b32 	 %f31, %r6;
	ld.local.f32 	%f374, [%rd4+12];
	setp.leu.ftz.f32	%p4, %f31, %f374;
	@%p4 bra 	BB1_5;

	ld.local.f32 	%f375, [%rd4+16];
	setp.lt.ftz.f32	%p5, %f31, %f375;
	@%p5 bra 	BB1_16;
	bra.uni 	BB1_5;

BB1_16:
	setp.gt.u64	%p13, %rd24, 281474976710655;
	@%p13 bra 	BB1_14;

	mov.b32 	 %f54, %r2;
	mov.b32 	 %f55, %r4;
	setp.ne.s32	%p14, %r1, 0;
	@%p14 bra 	BB1_19;

	ld.param.u64 	%rd309, [_Z9pathtrace5uint3S_jjR6float3RjS2_S1_S1__param_6];
	ld.param.u64 	%rd308, [_Z9pathtrace5uint3S_jjR6float3RjS2_S1_S1__param_8];
	ld.param.u64 	%rd307, [_Z9pathtrace5uint3S_jjR6float3RjS2_S1_S1__param_7];
	st.f32 	[%rd307], %f54;
	st.f32 	[%rd307+4], %f30;
	st.f32 	[%rd307+8], %f55;
	st.f32 	[%rd308], %f54;
	st.f32 	[%rd308+4], %f30;
	st.f32 	[%rd308+8], %f55;
	cvt.u32.u64	%r205, %rd26;
	and.b32  	%r206, %r205, 65535;
	st.u32 	[%rd309], %r206;

BB1_19:
	add.s32 	%r1, %r1, 1;
	ld.local.f32 	%f417, [%rd4];
	ld.local.f32 	%f418, [%rd4+4];
	ld.local.f32 	%f419, [%rd4+8];
	ld.local.f32 	%f420, [%rd4+-12];
	fma.rn.ftz.f32 	%f421, %f31, %f417, %f420;
	ld.local.f32 	%f422, [%rd4+-8];
	fma.rn.ftz.f32 	%f423, %f31, %f418, %f422;
	ld.local.f32 	%f424, [%rd4+-4];
	fma.rn.ftz.f32 	%f425, %f31, %f419, %f424;
	st.local.f32 	[%rd4+-12], %f421;
	st.local.f32 	[%rd4+-8], %f423;
	st.local.f32 	[%rd4+-4], %f425;
	and.b64  	%rd78, %rd26, 65535;
	shl.b64 	%rd79, %rd78, 32;
	and.b64  	%rd80, %rd20, -281474976710656;
	or.b64  	%rd81, %rd79, %rd80;
	mov.b64	{%r207, %r10}, %rd81;
	mov.b32 	 %f56, %r7;
	mov.b64	{%r208, %r209}, %rd18;
	mov.b32 	 %f57, %r208;
	mov.b32 	 %f58, %r209;
	ld.local.u32 	%r210, [%rd3];
	shl.b32 	%r211, %r210, 12;
	add.s32 	%r212, %r210, %r211;
	add.s32 	%r213, %r212, 2127912214;
	shr.u32 	%r214, %r213, 19;
	xor.b32  	%r215, %r213, %r214;
	xor.b32  	%r216, %r215, -949894596;
	shl.b32 	%r217, %r216, 5;
	add.s32 	%r218, %r216, %r217;
	add.s32 	%r219, %r218, 374761393;
	add.s32 	%r220, %r218, -369570787;
	shl.b32 	%r221, %r219, 9;
	xor.b32  	%r222, %r220, %r221;
	shl.b32 	%r223, %r222, 3;
	add.s32 	%r224, %r222, %r223;
	add.s32 	%r225, %r224, -42973499;
	shr.u32 	%r226, %r225, 16;
	xor.b32  	%r227, %r225, %r226;
	xor.b32  	%r228, %r227, -1252372727;
	ld.local.u32 	%r229, [%rd3+4];
	add.s32 	%r402, %r228, %r229;
	ld.local.u32 	%r12, [%rd3+12];
	and.b32  	%r230, %r12, 511;
	mul.wide.u32 	%rd82, %r230, 4;
	mov.u64 	%rd83, _ZZ8Prime512jE6Primes;
	add.s64 	%rd84, %rd83, %rd82;
	ld.global.u32 	%r13, [%rd84];
	cvt.rn.f64.u32	%fd1, %r13;
	rcp.rn.f64 	%fd2, %fd1;
	cvt.rn.ftz.f32.f64	%f59, %fd2;
	setp.eq.s32	%p15, %r402, 0;
	mov.f32 	%f1292, 0f00000000;
	mov.f32 	%f1279, 0f3F800000;
	mov.u32 	%r398, %r402;
	mov.f32 	%f1280, %f1292;
	@%p15 bra 	BB1_21;

BB1_20:
	rem.u32 	%r231, %r398, %r13;
	cvt.rn.f32.u32	%f426, %r231;
	mul.ftz.f32 	%f1279, %f59, %f1279;
	fma.rn.ftz.f32 	%f1280, %f1279, %f426, %f1280;
	div.u32 	%r398, %r398, %r13;
	setp.ne.s32	%p16, %r398, 0;
	@%p16 bra 	BB1_20;

BB1_21:
	add.s32 	%r232, %r12, 1;
	st.local.u32 	[%rd3+12], %r232;
	and.b32  	%r233, %r232, 511;
	mul.wide.u32 	%rd85, %r233, 4;
	add.s64 	%rd87, %rd83, %rd85;
	ld.global.u32 	%r16, [%rd87];
	cvt.rn.f64.u32	%fd3, %r16;
	rcp.rn.f64 	%fd4, %fd3;
	cvt.rn.ftz.f32.f64	%f65, %fd4;
	mov.f32 	%f1281, 0f3F800000;
	mov.u32 	%r399, %r402;
	mov.f32 	%f1282, %f1292;
	@%p15 bra 	BB1_23;

BB1_22:
	rem.u32 	%r234, %r399, %r16;
	cvt.rn.f32.u32	%f430, %r234;
	mul.ftz.f32 	%f1281, %f65, %f1281;
	fma.rn.ftz.f32 	%f1282, %f1281, %f430, %f1282;
	div.u32 	%r399, %r399, %r16;
	setp.ne.s32	%p18, %r399, 0;
	@%p18 bra 	BB1_22;

BB1_23:
	add.s32 	%r235, %r12, 4;
	st.local.u32 	[%rd3+12], %r235;
	ld.local.u32 	%r19, [%rd2];
	add.s32 	%r236, %r19, 4;
	st.local.u32 	[%rd2], %r236;
	sqrt.approx.ftz.f32 	%f434, %f1282;
	mul.ftz.f32 	%f435, %f434, %f434;
	mov.f32 	%f1283, 0f3F800000;
	sub.ftz.f32 	%f436, %f1283, %f435;
	sqrt.approx.ftz.f32 	%f437, %f436;
	mul.ftz.f32 	%f438, %f1280, 0f40C90FDB;
	cos.approx.ftz.f32 	%f439, %f438;
	mul.ftz.f32 	%f440, %f437, %f439;
	sin.approx.ftz.f32 	%f441, %f438;
	mul.ftz.f32 	%f442, %f437, %f441;
	cvt.ftz.f64.f32	%fd5, %f434;
	mul.f64 	%fd6, %fd5, 0d3FD45F306446F9B4;
	setp.ltu.ftz.f32	%p19, %f55, 0f00000000;
	selp.f32	%f443, 0fBF800000, 0f3F800000, %p19;
	add.ftz.f32 	%f444, %f55, %f443;
	mov.f32 	%f445, 0fBF800000;
	div.approx.ftz.f32 	%f446, %f445, %f444;
	mul.ftz.f32 	%f447, %f54, %f30;
	mul.ftz.f32 	%f71, %f446, %f447;
	mul.ftz.f32 	%f448, %f443, %f446;
	mul.ftz.f32 	%f449, %f448, %f54;
	fma.rn.ftz.f32 	%f72, %f54, %f449, 0f3F800000;
	mul.ftz.f32 	%f73, %f443, %f71;
	mul.ftz.f32 	%f74, %f443, %f54;
	mul.ftz.f32 	%f450, %f446, %f30;
	fma.rn.ftz.f32 	%f75, %f30, %f450, %f443;
	cvt.rn.ftz.f32.f64	%f76, %fd6;
	fma.rn.ftz.f32 	%f451, %f440, %f72, 0f00000000;
	fma.rn.ftz.f32 	%f452, %f442, %f71, %f451;
	fma.rn.ftz.f32 	%f77, %f434, %f54, %f452;
	fma.rn.ftz.f32 	%f453, %f440, %f73, 0f00000000;
	fma.rn.ftz.f32 	%f454, %f442, %f75, %f453;
	fma.rn.ftz.f32 	%f78, %f434, %f30, %f454;
	mul.ftz.f32 	%f455, %f74, %f440;
	sub.ftz.f32 	%f456, %f1292, %f455;
	mul.ftz.f32 	%f457, %f30, %f442;
	sub.ftz.f32 	%f458, %f456, %f457;
	fma.rn.ftz.f32 	%f79, %f434, %f55, %f458;
	and.b32  	%r237, %r235, 511;
	mul.wide.u32 	%rd88, %r237, 4;
	add.s64 	%rd90, %rd83, %rd88;
	ld.global.u32 	%r20, [%rd90];
	cvt.rn.f64.u32	%fd7, %r20;
	rcp.rn.f64 	%fd8, %fd7;
	cvt.rn.ftz.f32.f64	%f80, %fd8;
	mov.u32 	%r400, %r402;
	mov.f32 	%f1284, %f1292;
	@%p15 bra 	BB1_25;

BB1_24:
	rem.u32 	%r238, %r400, %r20;
	cvt.rn.f32.u32	%f459, %r238;
	mul.ftz.f32 	%f1283, %f80, %f1283;
	fma.rn.ftz.f32 	%f1284, %f1283, %f459, %f1284;
	div.u32 	%r400, %r400, %r20;
	setp.ne.s32	%p21, %r400, 0;
	@%p21 bra 	BB1_24;

BB1_25:
	add.s32 	%r239, %r12, 5;
	st.local.u32 	[%rd3+12], %r239;
	and.b32  	%r240, %r239, 511;
	mul.wide.u32 	%rd91, %r240, 4;
	add.s64 	%rd93, %rd83, %rd91;
	ld.global.u32 	%r23, [%rd93];
	cvt.rn.f64.u32	%fd9, %r23;
	rcp.rn.f64 	%fd10, %fd9;
	cvt.rn.ftz.f32.f64	%f86, %fd10;
	mov.f32 	%f1285, 0f3F800000;
	mov.u32 	%r401, %r402;
	mov.f32 	%f91, %f1292;
	@%p15 bra 	BB1_27;

BB1_26:
	rem.u32 	%r241, %r401, %r23;
	cvt.rn.f32.u32	%f463, %r241;
	mul.ftz.f32 	%f1285, %f86, %f1285;
	fma.rn.ftz.f32 	%f91, %f1285, %f463, %f91;
	div.u32 	%r401, %r401, %r23;
	setp.ne.s32	%p23, %r401, 0;
	@%p23 bra 	BB1_26;

BB1_27:
	add.s32 	%r242, %r12, 6;
	st.local.u32 	[%rd3+12], %r242;
	and.b32  	%r243, %r242, 511;
	mul.wide.u32 	%rd94, %r243, 4;
	add.s64 	%rd96, %rd83, %rd94;
	ld.global.u32 	%r26, [%rd96];
	cvt.rn.f64.u32	%fd11, %r26;
	rcp.rn.f64 	%fd12, %fd11;
	cvt.rn.ftz.f32.f64	%f92, %fd12;
	mov.f32 	%f1287, 0f3F800000;
	mov.f32 	%f97, %f1292;
	@%p15 bra 	BB1_29;

BB1_28:
	rem.u32 	%r244, %r402, %r26;
	cvt.rn.f32.u32	%f467, %r244;
	mul.ftz.f32 	%f1287, %f92, %f1287;
	fma.rn.ftz.f32 	%f97, %f1287, %f467, %f97;
	div.u32 	%r402, %r402, %r26;
	setp.ne.s32	%p25, %r402, 0;
	@%p25 bra 	BB1_28;

BB1_29:
	add.s32 	%r250, %r12, 8;
	st.local.u32 	[%rd3+12], %r250;
	add.s32 	%r251, %r19, 8;
	st.local.u32 	[%rd2], %r251;
	mov.b64	%rd109, {%r4, %r10};
	shr.u64 	%rd110, %rd109, 31;
	and.b64  	%rd99, %rd110, 131070;
	mov.u64 	%rd111, SceneObjectRelevantLightRangeData;
	cvta.global.u64 	%rd98, %rd111;
	mov.u32 	%r248, 4;
	mov.u32 	%r247, 1;
	// inline asm
	call (%rd97), _rt_buffer_get_64, (%rd98, %r247, %r248, %rd99, %rd55, %rd55, %rd55);
	// inline asm
	ld.u32 	%r29, [%rd97];
	cvt.u64.u32	%rd27, %r29;
	or.b64  	%rd105, %rd99, 1;
	// inline asm
	call (%rd103), _rt_buffer_get_64, (%rd98, %r247, %r248, %rd105, %rd55, %rd55, %rd55);
	// inline asm
	ld.u32 	%r252, [%rd103];
	cvt.u64.u32	%rd28, %r252;
	mov.pred 	%p106, -1;
	mov.u32 	%r413, -1;
	setp.le.u32	%p27, %r252, %r29;
	@%p27 bra 	BB1_57;

	mov.u64 	%rd117, SceneObjectRelevantLightIndexData;
	cvta.global.u64 	%rd116, %rd117;
	// inline asm
	call (%rd112, %rd113, %rd114, %rd115), _rt_buffer_get_size_64, (%rd116, %r247, %r248);
	// inline asm
	and.b64  	%rd118, %rd112, 4294967295;
	setp.gt.u64	%p29, %rd28, %rd118;
	@%p29 bra 	BB1_57;

	cvt.u32.u64	%r260, %rd28;
	sub.s32 	%r261, %r260, %r29;
	mov.u32 	%r262, 64;
	min.u32 	%r30, %r261, %r262;
	// inline asm
	call (%rd119), _rt_buffer_get_64, (%rd116, %r247, %r248, %rd27, %rd55, %rd55, %rd55);
	// inline asm
	ld.u32 	%rd127, [%rd119];
	mov.u64 	%rd132, SceneLightPowers;
	cvta.global.u64 	%rd126, %rd132;
	mov.u32 	%r259, 8;
	// inline asm
	call (%rd125), _rt_buffer_get_64, (%rd126, %r247, %r259, %rd127, %rd55, %rd55, %rd55);
	// inline asm
	ld.f32 	%f1289, [%rd125+4];
	st.local.f32 	[%rd1], %f1289;
	setp.lt.u32	%p30, %r30, 2;
	@%p30 bra 	BB1_42;

	add.s32 	%r264, %r30, -1;
	and.b32  	%r31, %r264, 3;
	setp.eq.s32	%p31, %r31, 0;
	mov.u32 	%r406, 1;
	@%p31 bra 	BB1_40;

	setp.eq.s32	%p32, %r31, 1;
	mov.u32 	%r265, 1;
	@%p32 bra 	BB1_34;
	bra.uni 	BB1_35;

BB1_34:
	mov.u32 	%r404, %r265;
	bra.uni 	BB1_39;

BB1_35:
	setp.eq.s32	%p33, %r31, 2;
	mov.u32 	%r266, 1;
	@%p33 bra 	BB1_36;
	bra.uni 	BB1_37;

BB1_36:
	mov.u32 	%r403, %r266;
	bra.uni 	BB1_38;

BB1_37:
	mov.u32 	%r390, 8;
	mov.u64 	%rd314, SceneLightPowers;
	cvta.global.u64 	%rd313, %rd314;
	cvt.u64.u32	%rd312, %r29;
	mov.u32 	%r389, 4;
	mov.u32 	%r403, 2;
	cvt.u32.u64	%r272, %rd312;
	add.s32 	%r273, %r272, 1;
	mov.u32 	%r269, 1;
	cvt.u64.u32	%rd135, %r273;
	mov.u64 	%rd144, 0;
	// inline asm
	call (%rd133), _rt_buffer_get_64, (%rd116, %r269, %r389, %rd135, %rd144, %rd144, %rd144);
	// inline asm
	ld.u32 	%rd141, [%rd133];
	// inline asm
	call (%rd139), _rt_buffer_get_64, (%rd313, %r269, %r390, %rd141, %rd144, %rd144, %rd144);
	// inline asm
	ld.f32 	%f470, [%rd139+4];
	add.ftz.f32 	%f1289, %f470, %f1289;
	st.local.f32 	[%rd1+4], %f1289;

BB1_38:
	mov.u32 	%r392, 8;
	mov.u64 	%rd316, SceneLightPowers;
	cvta.global.u64 	%rd315, %rd316;
	mov.u32 	%r391, 4;
	add.s32 	%r278, %r403, %r29;
	cvt.u64.u32	%rd149, %r278;
	mov.u64 	%rd158, 0;
	// inline asm
	call (%rd147), _rt_buffer_get_64, (%rd116, %r266, %r391, %rd149, %rd158, %rd158, %rd158);
	// inline asm
	ld.u32 	%rd155, [%rd147];
	// inline asm
	call (%rd153), _rt_buffer_get_64, (%rd315, %r266, %r392, %rd155, %rd158, %rd158, %rd158);
	// inline asm
	mul.wide.u32 	%rd161, %r403, 4;
	add.s64 	%rd162, %rd1, %rd161;
	ld.f32 	%f471, [%rd153+4];
	add.ftz.f32 	%f1289, %f471, %f1289;
	st.local.f32 	[%rd162], %f1289;
	add.s32 	%r404, %r403, 1;

BB1_39:
	mov.u32 	%r394, 8;
	mov.u64 	%rd318, SceneLightPowers;
	cvta.global.u64 	%rd317, %rd318;
	mov.u32 	%r393, 4;
	add.s32 	%r283, %r404, %r29;
	cvt.u64.u32	%rd165, %r283;
	mov.u64 	%rd174, 0;
	// inline asm
	call (%rd163), _rt_buffer_get_64, (%rd116, %r265, %r393, %rd165, %rd174, %rd174, %rd174);
	// inline asm
	ld.u32 	%rd171, [%rd163];
	// inline asm
	call (%rd169), _rt_buffer_get_64, (%rd317, %r265, %r394, %rd171, %rd174, %rd174, %rd174);
	// inline asm
	mul.wide.u32 	%rd177, %r404, 4;
	add.s64 	%rd178, %rd1, %rd177;
	ld.f32 	%f472, [%rd169+4];
	add.ftz.f32 	%f473, %f472, %f1289;
	st.local.f32 	[%rd178], %f473;
	add.s32 	%r406, %r404, 1;

BB1_40:
	setp.lt.u32	%p34, %r264, 4;
	@%p34 bra 	BB1_42;

BB1_41:
	mov.u32 	%r396, 8;
	mov.u64 	%rd320, SceneLightPowers;
	cvta.global.u64 	%rd319, %rd320;
	mov.u32 	%r395, 4;
	add.s32 	%r301, %r406, %r29;
	cvt.u64.u32	%rd181, %r301;
	mov.u32 	%r299, 1;
	mov.u64 	%rd226, 0;
	// inline asm
	call (%rd179), _rt_buffer_get_64, (%rd116, %r299, %r395, %rd181, %rd226, %rd226, %rd226);
	// inline asm
	ld.u32 	%rd187, [%rd179];
	// inline asm
	call (%rd185), _rt_buffer_get_64, (%rd319, %r299, %r396, %rd187, %rd226, %rd226, %rd226);
	// inline asm
	mul.wide.u32 	%rd229, %r406, 4;
	add.s64 	%rd230, %rd1, %rd229;
	add.s32 	%r302, %r406, -1;
	mul.wide.u32 	%rd231, %r302, 4;
	add.s64 	%rd232, %rd1, %rd231;
	ld.local.f32 	%f474, [%rd232];
	ld.f32 	%f475, [%rd185+4];
	add.ftz.f32 	%f476, %f475, %f474;
	st.local.f32 	[%rd230], %f476;
	add.s32 	%r303, %r406, 1;
	add.s32 	%r304, %r303, %r29;
	cvt.u64.u32	%rd193, %r304;
	// inline asm
	call (%rd191), _rt_buffer_get_64, (%rd116, %r299, %r395, %rd193, %rd226, %rd226, %rd226);
	// inline asm
	ld.u32 	%rd199, [%rd191];
	// inline asm
	call (%rd197), _rt_buffer_get_64, (%rd319, %r299, %r396, %rd199, %rd226, %rd226, %rd226);
	// inline asm
	mul.wide.u32 	%rd233, %r303, 4;
	add.s64 	%rd234, %rd1, %rd233;
	ld.f32 	%f477, [%rd197+4];
	add.ftz.f32 	%f478, %f477, %f476;
	st.local.f32 	[%rd234], %f478;
	add.s32 	%r305, %r406, 2;
	add.s32 	%r306, %r305, %r29;
	cvt.u64.u32	%rd205, %r306;
	// inline asm
	call (%rd203), _rt_buffer_get_64, (%rd116, %r299, %r395, %rd205, %rd226, %rd226, %rd226);
	// inline asm
	ld.u32 	%rd211, [%rd203];
	// inline asm
	call (%rd209), _rt_buffer_get_64, (%rd319, %r299, %r396, %rd211, %rd226, %rd226, %rd226);
	// inline asm
	mul.wide.u32 	%rd235, %r305, 4;
	add.s64 	%rd236, %rd1, %rd235;
	ld.f32 	%f479, [%rd209+4];
	add.ftz.f32 	%f480, %f479, %f478;
	st.local.f32 	[%rd236], %f480;
	add.s32 	%r307, %r406, 3;
	add.s32 	%r308, %r307, %r29;
	cvt.u64.u32	%rd217, %r308;
	// inline asm
	call (%rd215), _rt_buffer_get_64, (%rd116, %r299, %r395, %rd217, %rd226, %rd226, %rd226);
	// inline asm
	ld.u32 	%rd223, [%rd215];
	// inline asm
	call (%rd221), _rt_buffer_get_64, (%rd319, %r299, %r396, %rd223, %rd226, %rd226, %rd226);
	// inline asm
	mul.wide.u32 	%rd237, %r307, 4;
	add.s64 	%rd238, %rd1, %rd237;
	ld.f32 	%f481, [%rd221+4];
	add.ftz.f32 	%f482, %f481, %f480;
	st.local.f32 	[%rd238], %f482;
	add.s32 	%r406, %r406, 4;
	setp.lt.u32	%p35, %r406, %r30;
	@%p35 bra 	BB1_41;

BB1_42:
	add.s32 	%r309, %r30, -1;
	mul.wide.u32 	%rd239, %r309, 4;
	add.s64 	%rd30, %rd1, %rd239;
	setp.eq.s32	%p36, %r30, 0;
	@%p36 bra 	BB1_51;

	and.b32  	%r39, %r30, 3;
	setp.eq.s32	%p37, %r39, 0;
	mov.u32 	%r410, 0;
	@%p37 bra 	BB1_49;

	setp.eq.s32	%p38, %r39, 1;
	mov.u32 	%r408, 0;
	@%p38 bra 	BB1_48;

	setp.eq.s32	%p39, %r39, 2;
	mov.u32 	%r407, 0;
	@%p39 bra 	BB1_47;

	mov.u32 	%r407, 1;
	ld.local.f32 	%f483, [%rd1];
	ld.local.f32 	%f484, [%rd30];
	mov.f32 	%f485, 0f38D1B717;
	max.ftz.f32 	%f486, %f485, %f484;
	div.approx.ftz.f32 	%f487, %f483, %f486;
	st.local.f32 	[%rd1], %f487;

BB1_47:
	mul.wide.u32 	%rd240, %r407, 4;
	add.s64 	%rd241, %rd1, %rd240;
	ld.local.f32 	%f488, [%rd30];
	mov.f32 	%f489, 0f38D1B717;
	max.ftz.f32 	%f490, %f489, %f488;
	ld.local.f32 	%f491, [%rd241];
	div.approx.ftz.f32 	%f492, %f491, %f490;
	st.local.f32 	[%rd241], %f492;
	add.s32 	%r408, %r407, 1;

BB1_48:
	mul.wide.u32 	%rd242, %r408, 4;
	add.s64 	%rd243, %rd1, %rd242;
	ld.local.f32 	%f493, [%rd30];
	mov.f32 	%f494, 0f38D1B717;
	max.ftz.f32 	%f495, %f494, %f493;
	ld.local.f32 	%f496, [%rd243];
	div.approx.ftz.f32 	%f497, %f496, %f495;
	st.local.f32 	[%rd243], %f497;
	add.s32 	%r410, %r408, 1;

BB1_49:
	setp.lt.u32	%p40, %r30, 4;
	@%p40 bra 	BB1_51;

BB1_50:
	mul.wide.u32 	%rd244, %r410, 4;
	add.s64 	%rd245, %rd1, %rd244;
	ld.local.f32 	%f498, [%rd30];
	mov.f32 	%f499, 0f38D1B717;
	max.ftz.f32 	%f500, %f499, %f498;
	ld.local.f32 	%f501, [%rd245];
	div.approx.ftz.f32 	%f502, %f501, %f500;
	st.local.f32 	[%rd245], %f502;
	add.s32 	%r314, %r410, 1;
	mul.wide.u32 	%rd246, %r314, 4;
	add.s64 	%rd247, %rd1, %rd246;
	ld.local.f32 	%f503, [%rd30];
	max.ftz.f32 	%f504, %f499, %f503;
	ld.local.f32 	%f505, [%rd247];
	div.approx.ftz.f32 	%f506, %f505, %f504;
	st.local.f32 	[%rd247], %f506;
	add.s32 	%r315, %r410, 2;
	mul.wide.u32 	%rd248, %r315, 4;
	add.s64 	%rd249, %rd1, %rd248;
	ld.local.f32 	%f507, [%rd30];
	max.ftz.f32 	%f508, %f499, %f507;
	ld.local.f32 	%f509, [%rd249];
	div.approx.ftz.f32 	%f510, %f509, %f508;
	st.local.f32 	[%rd249], %f510;
	add.s32 	%r316, %r410, 3;
	mul.wide.u32 	%rd250, %r316, 4;
	add.s64 	%rd251, %rd1, %rd250;
	ld.local.f32 	%f511, [%rd30];
	max.ftz.f32 	%f512, %f499, %f511;
	ld.local.f32 	%f513, [%rd251];
	div.approx.ftz.f32 	%f514, %f513, %f512;
	st.local.f32 	[%rd251], %f514;
	add.s32 	%r410, %r410, 4;
	setp.lt.u32	%p41, %r410, %r30;
	@%p41 bra 	BB1_50;

BB1_51:
	mov.u32 	%r412, 0;
	mov.u32 	%r319, 1065353216;
	st.local.u32 	[%rd30], %r319;
	mov.u64 	%rd326, %rd1;
	mov.u64 	%rd328, %rd55;
	@%p36 bra 	BB1_54;

BB1_52:
	ld.local.f32 	%f515, [%rd326];
	setp.gt.ftz.f32	%p43, %f515, %f1284;
	@%p43 bra 	BB1_54;

	add.s32 	%r412, %r412, 1;
	setp.lt.u32	%p44, %r412, %r30;
	cvt.u64.u32	%rd328, %r412;
	mul.wide.u32 	%rd254, %r412, 4;
	add.s64 	%rd326, %rd1, %rd254;
	@%p44 bra 	BB1_52;

BB1_54:
	shl.b64 	%rd255, %rd328, 2;
	add.s64 	%rd256, %rd1, %rd255;
	ld.local.f32 	%f1292, [%rd256];
	setp.eq.s32	%p45, %r412, 0;
	@%p45 bra 	BB1_56;

	add.s32 	%r320, %r412, -1;
	mul.wide.u32 	%rd257, %r320, 4;
	add.s64 	%rd258, %rd1, %rd257;
	ld.local.f32 	%f516, [%rd258];
	sub.ftz.f32 	%f1292, %f1292, %f516;

BB1_56:
	mov.u32 	%r383, 4;
	add.s32 	%r323, %r412, %r29;
	cvt.u64.u32	%rd261, %r323;
	mov.u32 	%r321, 1;
	mov.u64 	%rd264, 0;
	// inline asm
	call (%rd259), _rt_buffer_get_64, (%rd116, %r321, %r383, %rd261, %rd264, %rd264, %rd264);
	// inline asm
	ld.u32 	%r413, [%rd259];
	mov.pred 	%p106, 0;

BB1_57:
	mov.f32 	%f193, 0f00000000;
	@%p106 bra 	BB1_88;

	mov.u32 	%r384, 1;
	cvt.s64.s32	%rd268, %r413;
	mov.u64 	%rd272, SceneLightsData;
	cvta.global.u64 	%rd267, %rd272;
	mov.u32 	%r325, 128;
	// inline asm
	call (%rd266), _rt_buffer_get_64, (%rd267, %r384, %r325, %rd268, %rd55, %rd55, %rd55);
	// inline asm
	ld.u32 	%r52, [%rd266+112];
	setp.eq.s32	%p47, %r52, 1;
	@%p47 bra 	BB1_82;
	bra.uni 	BB1_59;

BB1_82:
	ld.f32 	%f1302, [%rd266+8];
	setp.leu.ftz.f32	%p69, %f1302, 0f00000000;
	@%p69 bra 	BB1_85;

	ld.f32 	%f908, [%rd266+16];
	sub.ftz.f32 	%f909, %f908, %f56;
	ld.f32 	%f910, [%rd266+20];
	sub.ftz.f32 	%f911, %f910, %f57;
	ld.f32 	%f912, [%rd266+24];
	sub.ftz.f32 	%f913, %f912, %f58;
	mul.ftz.f32 	%f914, %f911, %f911;
	fma.rn.ftz.f32 	%f915, %f909, %f909, %f914;
	fma.rn.ftz.f32 	%f916, %f913, %f913, %f915;
	sqrt.approx.ftz.f32 	%f182, %f916;
	add.ftz.f32 	%f917, %f1302, %f1302;
	setp.geu.ftz.f32	%p70, %f182, %f917;
	@%p70 bra 	BB1_85;

	mov.f32 	%f918, 0f40000000;
	div.approx.ftz.f32 	%f1302, %f182, %f918;

BB1_85:
	mov.f32 	%f1306, 0f3F800000;
	mov.f32 	%f1303, 0f00000000;
	setp.leu.ftz.f32	%p71, %f1302, 0f00000000;
	mov.f32 	%f1304, %f1303;
	mov.f32 	%f1305, %f1303;
	@%p71 bra 	BB1_87;

	mul.ftz.f32 	%f924, %f91, 0f40C90FDB;
	fma.rn.ftz.f32 	%f925, %f97, 0fC0000000, 0f3F800000;
	mul.ftz.f32 	%f926, %f925, %f925;
	mov.f32 	%f927, 0f3F800000;
	sub.ftz.f32 	%f928, %f927, %f926;
	sqrt.approx.ftz.f32 	%f929, %f928;
	cos.approx.ftz.f32 	%f930, %f924;
	mul.ftz.f32 	%f931, %f929, %f930;
	sin.approx.ftz.f32 	%f932, %f924;
	mul.ftz.f32 	%f933, %f929, %f932;
	ld.f32 	%f934, [%rd266+16];
	sub.ftz.f32 	%f935, %f934, %f56;
	ld.f32 	%f936, [%rd266+20];
	sub.ftz.f32 	%f937, %f936, %f57;
	ld.f32 	%f938, [%rd266+24];
	sub.ftz.f32 	%f939, %f938, %f58;
	mul.ftz.f32 	%f940, %f1302, %f931;
	mul.ftz.f32 	%f941, %f1302, %f933;
	mul.ftz.f32 	%f942, %f925, %f1302;
	mul.ftz.f32 	%f943, %f941, %f937;
	fma.rn.ftz.f32 	%f944, %f940, %f935, %f943;
	fma.rn.ftz.f32 	%f945, %f942, %f939, %f944;
	setp.lt.ftz.f32	%p72, %f945, 0f00000000;
	neg.ftz.f32 	%f946, %f931;
	neg.ftz.f32 	%f947, %f933;
	neg.ftz.f32 	%f948, %f925;
	selp.f32	%f1303, %f946, %f931, %p72;
	selp.f32	%f1304, %f947, %f933, %p72;
	selp.f32	%f1305, %f948, %f925, %p72;
	mov.f32 	%f1306, 0f3DA2F983;
	bra.uni 	BB1_87;

BB1_59:
	setp.eq.s32	%p48, %r52, 4;
	mov.f32 	%f1306, 0f3F800000;
	mov.f32 	%f1303, 0f00000000;
	@%p48 bra 	BB1_60;
	bra.uni 	BB1_61;

BB1_60:
	mov.f32 	%f1304, %f1303;
	mov.f32 	%f1305, %f1303;
	bra.uni 	BB1_87;

BB1_61:
	setp.eq.s32	%p49, %r52, 2;
	@%p49 bra 	BB1_77;
	bra.uni 	BB1_62;

BB1_77:
	ld.f32 	%f1303, [%rd266+28];
	ld.f32 	%f1304, [%rd266+32];
	ld.f32 	%f1305, [%rd266+36];
	ld.f32 	%f161, [%rd266+8];
	setp.leu.ftz.f32	%p67, %f161, 0f00000000;
	@%p67 bra 	BB1_87;

	mul.ftz.f32 	%f162, %f1304, 0f00000000;
	sub.ftz.f32 	%f163, %f162, %f1305;
	mul.ftz.f32 	%f164, %f1303, 0f00000000;
	mul.ftz.f32 	%f165, %f1305, 0f00000000;
	sub.ftz.f32 	%f166, %f165, %f164;
	sub.ftz.f32 	%f167, %f1303, %f162;
	mul.ftz.f32 	%f863, %f166, %f166;
	fma.rn.ftz.f32 	%f864, %f163, %f163, %f863;
	fma.rn.ftz.f32 	%f865, %f167, %f167, %f864;
	sqrt.approx.ftz.f32 	%f168, %f865;
	setp.lt.ftz.f32	%p68, %f168, 0f38D1B717;
	@%p68 bra 	BB1_80;
	bra.uni 	BB1_79;

BB1_80:
	sub.ftz.f32 	%f867, %f162, %f165;
	sub.ftz.f32 	%f868, %f1305, %f164;
	mul.ftz.f32 	%f869, %f868, %f868;
	fma.rn.ftz.f32 	%f870, %f867, %f867, %f869;
	sub.ftz.f32 	%f871, %f164, %f1304;
	fma.rn.ftz.f32 	%f872, %f871, %f871, %f870;
	rsqrt.approx.ftz.f32 	%f873, %f872;
	mul.ftz.f32 	%f1299, %f867, %f873;
	mul.ftz.f32 	%f1300, %f868, %f873;
	mul.ftz.f32 	%f1301, %f871, %f873;
	bra.uni 	BB1_81;

BB1_62:
	setp.eq.s32	%p50, %r52, 0;
	@%p50 bra 	BB1_76;
	bra.uni 	BB1_63;

BB1_76:
	mov.b32 	 %f1243, %r4;
	mul.ftz.f32 	%f844, %f91, 0f40C90FDB;
	mul.ftz.f32 	%f845, %f97, %f97;
	mov.f32 	%f846, 0f3F800000;
	sub.ftz.f32 	%f847, %f846, %f845;
	sqrt.approx.ftz.f32 	%f848, %f847;
	cos.approx.ftz.f32 	%f849, %f844;
	mul.ftz.f32 	%f850, %f848, %f849;
	sin.approx.ftz.f32 	%f851, %f844;
	mul.ftz.f32 	%f852, %f848, %f851;
	fma.rn.ftz.f32 	%f853, %f850, %f72, 0f00000000;
	fma.rn.ftz.f32 	%f854, %f852, %f71, %f853;
	fma.rn.ftz.f32 	%f1303, %f97, %f54, %f854;
	fma.rn.ftz.f32 	%f855, %f850, %f73, 0f00000000;
	fma.rn.ftz.f32 	%f856, %f852, %f75, %f855;
	fma.rn.ftz.f32 	%f1304, %f97, %f30, %f856;
	mul.ftz.f32 	%f857, %f74, %f850;
	mov.f32 	%f858, 0f00000000;
	sub.ftz.f32 	%f859, %f858, %f857;
	mul.ftz.f32 	%f860, %f30, %f852;
	sub.ftz.f32 	%f861, %f859, %f860;
	fma.rn.ftz.f32 	%f1305, %f97, %f1243, %f861;
	mov.f32 	%f1306, 0f3E22F983;
	bra.uni 	BB1_87;

BB1_63:
	mov.f32 	%f1303, 0f00000000;
	setp.ne.s32	%p51, %r52, 3;
	mov.f32 	%f1304, %f1303;
	mov.f32 	%f1305, %f1303;
	mov.f32 	%f1306, %f1303;
	@%p51 bra 	BB1_87;

	ld.f32 	%f107, [%rd266+52];
	ld.f32 	%f108, [%rd266+56];
	ld.f32 	%f109, [%rd266+60];
	ld.v2.f32 	{%f528, %f529}, [%rd266];
	ld.f32 	%f110, [%rd266+16];
	ld.f32 	%f111, [%rd266+20];
	ld.f32 	%f112, [%rd266+24];
	ld.f32 	%f113, [%rd266+28];
	ld.f32 	%f114, [%rd266+32];
	ld.f32 	%f115, [%rd266+36];
	mul.ftz.f32 	%f1295, %f528, 0f3F000000;
	ld.v2.f32 	{%f532, %f533}, [%rd266+104];
	ld.f32 	%f120, [%rd266+68];
	ld.f32 	%f121, [%rd266+72];
	ld.f32 	%f122, [%rd266+64];
	mul.ftz.f32 	%f1296, %f529, 0f3F000000;
	mov.f32 	%f1297, 0f00000000;
	setp.leu.ftz.f32	%p52, %f532, 0f3D0F5C29;
	@%p52 bra 	BB1_65;

	sub.ftz.f32 	%f536, %f110, %f56;
	sub.ftz.f32 	%f537, %f111, %f57;
	sub.ftz.f32 	%f538, %f112, %f58;
	neg.ftz.f32 	%f539, %f113;
	mul.ftz.f32 	%f540, %f121, %f114;
	mul.ftz.f32 	%f541, %f120, %f115;
	sub.ftz.f32 	%f542, %f540, %f541;
	mul.ftz.f32 	%f543, %f122, %f115;
	mul.ftz.f32 	%f544, %f121, %f113;
	sub.ftz.f32 	%f545, %f543, %f544;
	mul.ftz.f32 	%f546, %f537, %f545;
	fma.rn.ftz.f32 	%f547, %f536, %f542, %f546;
	mul.ftz.f32 	%f548, %f120, %f113;
	mul.ftz.f32 	%f549, %f122, %f114;
	sub.ftz.f32 	%f550, %f548, %f549;
	fma.rn.ftz.f32 	%f551, %f538, %f550, %f547;
	mul.ftz.f32 	%f552, %f537, %f120;
	fma.rn.ftz.f32 	%f553, %f536, %f122, %f552;
	fma.rn.ftz.f32 	%f554, %f538, %f121, %f553;
	mul.ftz.f32 	%f555, %f537, %f114;
	mul.ftz.f32 	%f556, %f536, %f539;
	sub.ftz.f32 	%f557, %f556, %f555;
	mul.ftz.f32 	%f558, %f538, %f115;
	sub.ftz.f32 	%f559, %f557, %f558;
	mul.ftz.f32 	%f560, %f532, %f532;
	mov.f32 	%f561, 0f00000000;
	max.ftz.f32 	%f562, %f561, %f560;
	mov.f32 	%f563, 0f3F800000;
	sub.ftz.f32 	%f564, %f563, %f562;
	sqrt.approx.ftz.f32 	%f565, %f564;
	mul.ftz.f32 	%f566, %f532, %f533;
	min.ftz.f32 	%f124, %f559, %f566;
	div.approx.ftz.f32 	%f567, %f124, %f566;
	mul.ftz.f32 	%f568, %f533, %f565;
	mul.ftz.f32 	%f125, %f568, %f567;
	setp.ltu.ftz.f32	%p53, %f551, 0f00000000;
	selp.f32	%f126, 0fBF800000, 0f3F800000, %p53;
	setp.ltu.ftz.f32	%p54, %f554, 0f00000000;
	selp.f32	%f127, 0fBF800000, 0f3F800000, %p54;
	cvt.ftz.f64.f32	%fd13, %f551;
	abs.f64 	%fd14, %fd13;
	cvt.rn.ftz.f32.f64	%f569, %fd14;
	add.ftz.f32 	%f570, %f1295, %f125;
	max.ftz.f32 	%f571, %f569, %f570;
	mul.ftz.f32 	%f572, %f126, %f571;
	cvt.ftz.f64.f32	%fd15, %f554;
	abs.f64 	%fd16, %fd15;
	cvt.rn.ftz.f32.f64	%f573, %fd16;
	add.ftz.f32 	%f574, %f1296, %f125;
	max.ftz.f32 	%f575, %f573, %f574;
	mul.ftz.f32 	%f576, %f127, %f575;
	mul.ftz.f32 	%f577, %f126, %f570;
	mul.ftz.f32 	%f578, %f127, %f574;
	sub.ftz.f32 	%f128, %f572, %f577;
	sub.ftz.f32 	%f129, %f576, %f578;
	sub.ftz.f32 	%f579, %f559, %f124;
	max.ftz.f32 	%f130, %f579, %f561;
	mov.f32 	%f1293, 0f461C4000;
	setp.leu.ftz.f32	%p55, %f130, 0f3A83126F;
	mov.f32 	%f1294, %f1293;
	@%p55 bra 	BB1_68;

	cvt.ftz.f64.f32	%fd17, %f129;
	abs.f64 	%fd18, %fd17;
	cvt.ftz.f64.f32	%fd19, %f128;
	abs.f64 	%fd20, %fd19;
	cvt.rn.ftz.f32.f64	%f580, %fd20;
	cvt.rn.ftz.f32.f64	%f581, %fd18;
	rcp.approx.ftz.f32 	%f582, %f130;
	mul.ftz.f32 	%f1293, %f580, %f582;
	mul.ftz.f32 	%f1294, %f581, %f582;

BB1_68:
	mul.ftz.f32 	%f583, %f124, %f1293;
	sub.ftz.f32 	%f584, %f583, %f125;
	neg.ftz.f32 	%f585, %f126;
	max.ftz.f32 	%f587, %f561, %f585;
	mul.ftz.f32 	%f588, %f587, %f584;
	max.ftz.f32 	%f589, %f561, %f126;
	mul.ftz.f32 	%f590, %f584, %f589;
	sub.ftz.f32 	%f591, %f588, %f1295;
	sub.ftz.f32 	%f592, %f1295, %f590;
	mul.ftz.f32 	%f593, %f124, %f1294;
	sub.ftz.f32 	%f594, %f593, %f125;
	neg.ftz.f32 	%f595, %f127;
	max.ftz.f32 	%f596, %f561, %f595;
	mul.ftz.f32 	%f597, %f594, %f596;
	max.ftz.f32 	%f598, %f561, %f127;
	mul.ftz.f32 	%f599, %f594, %f598;
	sub.ftz.f32 	%f600, %f597, %f1296;
	sub.ftz.f32 	%f601, %f1296, %f599;
	min.ftz.f32 	%f602, %f591, %f1295;
	neg.ftz.f32 	%f603, %f1295;
	max.ftz.f32 	%f604, %f603, %f602;
	min.ftz.f32 	%f605, %f592, %f1295;
	max.ftz.f32 	%f606, %f603, %f605;
	min.ftz.f32 	%f607, %f600, %f1296;
	neg.ftz.f32 	%f608, %f1296;
	max.ftz.f32 	%f609, %f608, %f607;
	min.ftz.f32 	%f610, %f601, %f1296;
	max.ftz.f32 	%f611, %f608, %f610;
	add.ftz.f32 	%f612, %f604, %f606;
	add.ftz.f32 	%f613, %f609, %f611;
	sub.ftz.f32 	%f614, %f606, %f604;
	sub.ftz.f32 	%f615, %f611, %f609;
	mul.ftz.f32 	%f1295, %f614, 0f3F000000;
	mul.ftz.f32 	%f1296, %f615, 0f3F000000;
	mul.ftz.f32 	%f1297, %f612, 0fBF000000;
	mul.ftz.f32 	%f1298, %f613, 0fBF000000;
	bra.uni 	BB1_69;

BB1_79:
	rcp.approx.ftz.f32 	%f866, %f168;
	mul.ftz.f32 	%f1299, %f163, %f866;
	mul.ftz.f32 	%f1300, %f166, %f866;
	mul.ftz.f32 	%f1301, %f167, %f866;

BB1_81:
	mul.ftz.f32 	%f875, %f1305, %f1300;
	mul.ftz.f32 	%f876, %f1304, %f1301;
	sub.ftz.f32 	%f877, %f876, %f875;
	mul.ftz.f32 	%f878, %f1303, %f1301;
	mul.ftz.f32 	%f879, %f1305, %f1299;
	sub.ftz.f32 	%f880, %f879, %f878;
	mul.ftz.f32 	%f881, %f1304, %f1299;
	mul.ftz.f32 	%f882, %f1303, %f1300;
	sub.ftz.f32 	%f883, %f882, %f881;
	mul.ftz.f32 	%f884, %f91, 0f40C90FDB;
	cos.approx.ftz.f32 	%f885, %f884;
	sin.approx.ftz.f32 	%f886, %f884;
	sqrt.approx.ftz.f32 	%f887, %f97;
	mul.ftz.f32 	%f888, %f887, %f885;
	mul.ftz.f32 	%f889, %f887, %f886;
	mul.ftz.f32 	%f890, %f877, %f889;
	mul.ftz.f32 	%f891, %f880, %f889;
	mul.ftz.f32 	%f892, %f883, %f889;
	fma.rn.ftz.f32 	%f893, %f1299, %f888, %f890;
	fma.rn.ftz.f32 	%f894, %f1300, %f888, %f891;
	fma.rn.ftz.f32 	%f895, %f1301, %f888, %f892;
	mul.ftz.f32 	%f896, %f161, %f893;
	mul.ftz.f32 	%f897, %f161, %f894;
	mul.ftz.f32 	%f898, %f161, %f895;
	ld.global.f32 	%f899, [SceneBounds+24];
	add.ftz.f32 	%f900, %f899, %f899;
	fma.rn.ftz.f32 	%f901, %f1303, %f900, %f896;
	fma.rn.ftz.f32 	%f902, %f1304, %f900, %f897;
	fma.rn.ftz.f32 	%f903, %f1305, %f900, %f898;
	mul.ftz.f32 	%f904, %f902, %f902;
	fma.rn.ftz.f32 	%f905, %f901, %f901, %f904;
	fma.rn.ftz.f32 	%f906, %f903, %f903, %f905;
	rsqrt.approx.ftz.f32 	%f907, %f906;
	mul.ftz.f32 	%f1303, %f907, %f901;
	mul.ftz.f32 	%f1304, %f907, %f902;
	mul.ftz.f32 	%f1305, %f907, %f903;
	bra.uni 	BB1_87;

BB1_65:
	mov.f32 	%f1298, %f1297;

BB1_69:
	mov.f32 	%f1305, 0f00000000;
	mul.ftz.f32 	%f618, %f1297, %f107;
	sub.ftz.f32 	%f619, %f110, %f618;
	mul.ftz.f32 	%f620, %f1297, %f108;
	sub.ftz.f32 	%f621, %f111, %f620;
	mul.ftz.f32 	%f622, %f1297, %f109;
	sub.ftz.f32 	%f623, %f112, %f622;
	mul.ftz.f32 	%f624, %f1298, %f122;
	sub.ftz.f32 	%f143, %f619, %f624;
	mul.ftz.f32 	%f625, %f1298, %f120;
	sub.ftz.f32 	%f144, %f621, %f625;
	mul.ftz.f32 	%f626, %f1298, %f121;
	sub.ftz.f32 	%f145, %f623, %f626;
	add.ftz.f32 	%f146, %f1295, %f1295;
	mul.ftz.f32 	%f627, %f146, %f107;
	mul.ftz.f32 	%f628, %f146, %f108;
	mul.ftz.f32 	%f629, %f146, %f109;
	add.ftz.f32 	%f1303, %f91, 0fBF000000;
	fma.rn.ftz.f32 	%f630, %f1303, %f627, %f143;
	fma.rn.ftz.f32 	%f631, %f1303, %f628, %f144;
	fma.rn.ftz.f32 	%f632, %f1303, %f629, %f145;
	add.ftz.f32 	%f148, %f1296, %f1296;
	mul.ftz.f32 	%f633, %f148, %f122;
	mul.ftz.f32 	%f634, %f148, %f120;
	mul.ftz.f32 	%f635, %f148, %f121;
	add.ftz.f32 	%f1304, %f97, 0fBF000000;
	fma.rn.ftz.f32 	%f636, %f1304, %f633, %f630;
	fma.rn.ftz.f32 	%f637, %f1304, %f634, %f631;
	fma.rn.ftz.f32 	%f638, %f1304, %f635, %f632;
	sub.ftz.f32 	%f639, %f636, %f56;
	sub.ftz.f32 	%f640, %f637, %f57;
	sub.ftz.f32 	%f641, %f638, %f58;
	mul.ftz.f32 	%f642, %f640, %f640;
	fma.rn.ftz.f32 	%f643, %f639, %f639, %f642;
	fma.rn.ftz.f32 	%f644, %f641, %f641, %f643;
	rsqrt.approx.ftz.f32 	%f645, %f644;
	mul.ftz.f32 	%f150, %f639, %f645;
	mul.ftz.f32 	%f151, %f645, %f640;
	mul.ftz.f32 	%f152, %f645, %f641;
	mul.ftz.f32 	%f646, %f151, %f151;
	fma.rn.ftz.f32 	%f647, %f150, %f150, %f646;
	fma.rn.ftz.f32 	%f648, %f152, %f152, %f647;
	sqrt.approx.ftz.f32 	%f649, %f648;
	ld.f32 	%f650, [%rd266+100];
	mul.ftz.f32 	%f651, %f650, %f650;
	div.approx.ftz.f32 	%f652, %f649, %f651;
	mul.ftz.f32 	%f653, %f652, %f652;
	mov.f32 	%f654, 0f3F800000;
	sub.ftz.f32 	%f655, %f654, %f653;
	cvt.ftz.sat.f32.f32	%f656, %f655;
	mul.ftz.f32 	%f657, %f656, %f656;
	setp.lt.ftz.f32	%p56, %f657, 0f3727C5AC;
	@%p56 bra 	BB1_70;
	bra.uni 	BB1_71;

BB1_70:
	mov.f32 	%f1306, %f1305;
	bra.uni 	BB1_87;

BB1_71:
	mov.f32 	%f1305, 0f00000000;
	mul.ftz.f32 	%f660, %f113, %f150;
	mul.ftz.f32 	%f661, %f114, %f151;
	neg.ftz.f32 	%f662, %f661;
	sub.ftz.f32 	%f663, %f662, %f660;
	mul.ftz.f32 	%f664, %f115, %f152;
	sub.ftz.f32 	%f665, %f663, %f664;
	setp.le.ftz.f32	%p57, %f665, 0f00000000;
	@%p57 bra 	BB1_72;

	mov.f32 	%f1305, 0f00000000;
	sub.ftz.f32 	%f668, %f143, %f56;
	mul.ftz.f32 	%f669, %f109, %f120;
	mul.ftz.f32 	%f670, %f108, %f121;
	sub.ftz.f32 	%f671, %f670, %f669;
	mul.ftz.f32 	%f672, %f107, %f121;
	mul.ftz.f32 	%f673, %f109, %f122;
	sub.ftz.f32 	%f674, %f673, %f672;
	mul.ftz.f32 	%f675, %f108, %f122;
	mul.ftz.f32 	%f676, %f107, %f120;
	sub.ftz.f32 	%f677, %f676, %f675;
	sub.ftz.f32 	%f678, %f144, %f57;
	mul.ftz.f32 	%f679, %f108, %f678;
	fma.rn.ftz.f32 	%f680, %f107, %f668, %f679;
	sub.ftz.f32 	%f681, %f145, %f58;
	fma.rn.ftz.f32 	%f682, %f109, %f681, %f680;
	mul.ftz.f32 	%f683, %f120, %f678;
	fma.rn.ftz.f32 	%f684, %f122, %f668, %f683;
	fma.rn.ftz.f32 	%f685, %f121, %f681, %f684;
	mul.ftz.f32 	%f686, %f674, %f678;
	fma.rn.ftz.f32 	%f687, %f671, %f668, %f686;
	fma.rn.ftz.f32 	%f688, %f677, %f681, %f687;
	mul.ftz.f32 	%f689, %f146, 0f3F000000;
	sub.ftz.f32 	%f690, %f682, %f689;
	add.ftz.f32 	%f691, %f682, %f689;
	mul.ftz.f32 	%f692, %f148, 0f3F000000;
	sub.ftz.f32 	%f693, %f685, %f692;
	add.ftz.f32 	%f694, %f685, %f692;
	cvt.ftz.f64.f32	%fd21, %f688;
	abs.f64 	%fd22, %fd21;
	cvt.rn.ftz.f32.f64	%f695, %fd22;
	neg.ftz.f32 	%f696, %f695;
	mul.ftz.f32 	%f697, %f693, %f696;
	sub.ftz.f32 	%f698, %f697, %f697;
	mul.ftz.f32 	%f699, %f691, %f696;
	mul.ftz.f32 	%f700, %f690, %f696;
	sub.ftz.f32 	%f701, %f699, %f700;
	mul.ftz.f32 	%f702, %f690, %f693;
	mul.ftz.f32 	%f703, %f693, %f691;
	sub.ftz.f32 	%f704, %f702, %f703;
	mul.ftz.f32 	%f705, %f701, %f701;
	fma.rn.ftz.f32 	%f706, %f698, %f698, %f705;
	fma.rn.ftz.f32 	%f707, %f704, %f704, %f706;
	rsqrt.approx.ftz.f32 	%f708, %f707;
	mul.ftz.f32 	%f709, %f698, %f708;
	mul.ftz.f32 	%f710, %f701, %f708;
	mul.ftz.f32 	%f711, %f704, %f708;
	mul.ftz.f32 	%f712, %f694, %f696;
	sub.ftz.f32 	%f713, %f697, %f712;
	sub.ftz.f32 	%f714, %f699, %f699;
	mul.ftz.f32 	%f715, %f691, %f694;
	sub.ftz.f32 	%f716, %f715, %f703;
	mul.ftz.f32 	%f717, %f713, %f713;
	fma.rn.ftz.f32 	%f718, %f714, %f714, %f717;
	fma.rn.ftz.f32 	%f719, %f716, %f716, %f718;
	rsqrt.approx.ftz.f32 	%f720, %f719;
	mul.ftz.f32 	%f721, %f713, %f720;
	mul.ftz.f32 	%f722, %f714, %f720;
	mul.ftz.f32 	%f723, %f716, %f720;
	sub.ftz.f32 	%f724, %f712, %f712;
	sub.ftz.f32 	%f725, %f700, %f699;
	mul.ftz.f32 	%f726, %f690, %f694;
	sub.ftz.f32 	%f727, %f715, %f726;
	mul.ftz.f32 	%f728, %f724, %f724;
	fma.rn.ftz.f32 	%f729, %f725, %f725, %f728;
	fma.rn.ftz.f32 	%f730, %f727, %f727, %f729;
	rsqrt.approx.ftz.f32 	%f731, %f730;
	mul.ftz.f32 	%f732, %f724, %f731;
	mul.ftz.f32 	%f733, %f725, %f731;
	mul.ftz.f32 	%f734, %f727, %f731;
	sub.ftz.f32 	%f735, %f712, %f697;
	sub.ftz.f32 	%f736, %f700, %f700;
	sub.ftz.f32 	%f737, %f702, %f726;
	mul.ftz.f32 	%f738, %f735, %f735;
	fma.rn.ftz.f32 	%f739, %f736, %f736, %f738;
	fma.rn.ftz.f32 	%f740, %f737, %f737, %f739;
	rsqrt.approx.ftz.f32 	%f741, %f740;
	mul.ftz.f32 	%f742, %f735, %f741;
	mul.ftz.f32 	%f743, %f736, %f741;
	mul.ftz.f32 	%f744, %f737, %f741;
	mul.ftz.f32 	%f745, %f710, %f722;
	fma.rn.ftz.f32 	%f746, %f709, %f721, %f745;
	fma.rn.ftz.f32 	%f747, %f711, %f723, %f746;
	neg.ftz.f32 	%f748, %f747;
	abs.ftz.f32 	%f749, %f748;
	sub.ftz.f32 	%f751, %f654, %f749;
	mul.ftz.f32 	%f752, %f751, 0f3F000000;
	sqrt.approx.ftz.f32 	%f753, %f752;
	setp.gt.ftz.f32	%p58, %f749, 0f3F11EB85;
	selp.f32	%f754, %f753, %f749, %p58;
	mul.ftz.f32 	%f755, %f754, %f754;
	mov.f32 	%f756, 0f3C94D2E9;
	mov.f32 	%f757, 0f3D53F941;
	fma.rn.ftz.f32 	%f758, %f757, %f755, %f756;
	mov.f32 	%f759, 0f3D3F841F;
	fma.rn.ftz.f32 	%f760, %f758, %f755, %f759;
	mov.f32 	%f761, 0f3D994929;
	fma.rn.ftz.f32 	%f762, %f760, %f755, %f761;
	mov.f32 	%f763, 0f3E2AAB94;
	fma.rn.ftz.f32 	%f764, %f762, %f755, %f763;
	mul.ftz.f32 	%f765, %f755, %f764;
	fma.rn.ftz.f32 	%f766, %f765, %f754, %f754;
	add.ftz.f32 	%f767, %f766, %f766;
	mov.f32 	%f768, 0f3FC90FDB;
	sub.ftz.f32 	%f769, %f768, %f766;
	selp.f32	%f770, %f767, %f769, %p58;
	setp.gt.ftz.f32	%p59, %f747, 0f80000000;
	mov.f32 	%f771, 0f40490FDB;
	sub.ftz.f32 	%f772, %f771, %f770;
	selp.f32	%f773, %f772, %f770, %p59;
	mul.ftz.f32 	%f774, %f722, %f733;
	fma.rn.ftz.f32 	%f775, %f721, %f732, %f774;
	fma.rn.ftz.f32 	%f776, %f723, %f734, %f775;
	neg.ftz.f32 	%f777, %f776;
	abs.ftz.f32 	%f778, %f777;
	sub.ftz.f32 	%f779, %f654, %f778;
	mul.ftz.f32 	%f780, %f779, 0f3F000000;
	sqrt.approx.ftz.f32 	%f781, %f780;
	setp.gt.ftz.f32	%p60, %f778, 0f3F11EB85;
	selp.f32	%f782, %f781, %f778, %p60;
	mul.ftz.f32 	%f783, %f782, %f782;
	fma.rn.ftz.f32 	%f784, %f757, %f783, %f756;
	fma.rn.ftz.f32 	%f785, %f784, %f783, %f759;
	fma.rn.ftz.f32 	%f786, %f785, %f783, %f761;
	fma.rn.ftz.f32 	%f787, %f786, %f783, %f763;
	mul.ftz.f32 	%f788, %f783, %f787;
	fma.rn.ftz.f32 	%f789, %f788, %f782, %f782;
	add.ftz.f32 	%f790, %f789, %f789;
	sub.ftz.f32 	%f791, %f768, %f789;
	selp.f32	%f792, %f790, %f791, %p60;
	setp.gt.ftz.f32	%p61, %f776, 0f80000000;
	sub.ftz.f32 	%f793, %f771, %f792;
	selp.f32	%f794, %f793, %f792, %p61;
	mul.ftz.f32 	%f795, %f733, %f743;
	fma.rn.ftz.f32 	%f796, %f732, %f742, %f795;
	fma.rn.ftz.f32 	%f797, %f734, %f744, %f796;
	neg.ftz.f32 	%f798, %f797;
	abs.ftz.f32 	%f799, %f798;
	sub.ftz.f32 	%f800, %f654, %f799;
	mul.ftz.f32 	%f801, %f800, 0f3F000000;
	sqrt.approx.ftz.f32 	%f802, %f801;
	setp.gt.ftz.f32	%p62, %f799, 0f3F11EB85;
	selp.f32	%f803, %f802, %f799, %p62;
	mul.ftz.f32 	%f804, %f803, %f803;
	fma.rn.ftz.f32 	%f805, %f757, %f804, %f756;
	fma.rn.ftz.f32 	%f806, %f805, %f804, %f759;
	fma.rn.ftz.f32 	%f807, %f806, %f804, %f761;
	fma.rn.ftz.f32 	%f808, %f807, %f804, %f763;
	mul.ftz.f32 	%f809, %f804, %f808;
	fma.rn.ftz.f32 	%f810, %f809, %f803, %f803;
	add.ftz.f32 	%f811, %f810, %f810;
	sub.ftz.f32 	%f812, %f768, %f810;
	selp.f32	%f813, %f811, %f812, %p62;
	setp.gt.ftz.f32	%p63, %f797, 0f80000000;
	sub.ftz.f32 	%f814, %f771, %f813;
	selp.f32	%f815, %f814, %f813, %p63;
	mul.ftz.f32 	%f816, %f710, %f743;
	fma.rn.ftz.f32 	%f817, %f709, %f742, %f816;
	fma.rn.ftz.f32 	%f818, %f711, %f744, %f817;
	neg.ftz.f32 	%f819, %f818;
	abs.ftz.f32 	%f820, %f819;
	sub.ftz.f32 	%f821, %f654, %f820;
	mul.ftz.f32 	%f822, %f821, 0f3F000000;
	sqrt.approx.ftz.f32 	%f823, %f822;
	setp.gt.ftz.f32	%p64, %f820, 0f3F11EB85;
	selp.f32	%f824, %f823, %f820, %p64;
	mul.ftz.f32 	%f825, %f824, %f824;
	fma.rn.ftz.f32 	%f826, %f757, %f825, %f756;
	fma.rn.ftz.f32 	%f827, %f826, %f825, %f759;
	fma.rn.ftz.f32 	%f828, %f827, %f825, %f761;
	fma.rn.ftz.f32 	%f829, %f828, %f825, %f763;
	mul.ftz.f32 	%f830, %f825, %f829;
	fma.rn.ftz.f32 	%f831, %f830, %f824, %f824;
	add.ftz.f32 	%f832, %f831, %f831;
	sub.ftz.f32 	%f833, %f768, %f831;
	selp.f32	%f834, %f832, %f833, %p64;
	setp.gt.ftz.f32	%p65, %f818, 0f80000000;
	sub.ftz.f32 	%f835, %f771, %f834;
	selp.f32	%f836, %f835, %f834, %p65;
	mov.f32 	%f837, 0f40C90FDB;
	sub.ftz.f32 	%f838, %f837, %f815;
	sub.ftz.f32 	%f839, %f838, %f836;
	add.ftz.f32 	%f840, %f773, %f794;
	sub.ftz.f32 	%f153, %f840, %f839;
	abs.ftz.f32 	%f841, %f153;
	setp.geu.ftz.f32	%p66, %f841, 0f7F800000;
	@%p66 bra 	BB1_74;

	mov.f32 	%f1305, 0f00000000;
	rcp.approx.ftz.f32 	%f1306, %f153;
	bra.uni 	BB1_87;

BB1_72:
	mov.f32 	%f1306, %f1305;
	bra.uni 	BB1_87;

BB1_74:
	mov.f32 	%f1306, %f1305;

BB1_87:
	mul.ftz.f32 	%f193, %f1292, %f1306;

BB1_88:
	setp.leu.ftz.f32	%p73, %f193, 0f00000000;
	@%p73 bra 	BB1_127;

	mov.u32 	%r385, 1;
	cvt.s64.s32	%rd275, %r413;
	mov.u64 	%rd279, SceneLightsData;
	cvta.global.u64 	%rd274, %rd279;
	mov.u32 	%r327, 128;
	// inline asm
	call (%rd273), _rt_buffer_get_64, (%rd274, %r385, %r327, %rd275, %rd55, %rd55, %rd55);
	// inline asm
	ld.u32 	%r53, [%rd273+112];
	setp.eq.s32	%p74, %r53, 1;
	@%p74 bra 	BB1_98;
	bra.uni 	BB1_90;

BB1_98:
	ld.local.f32 	%f1315, [%rd4+-12];
	ld.local.f32 	%f1316, [%rd4+-8];
	ld.local.f32 	%f1317, [%rd4+-4];
	ld.f32 	%f1314, [%rd273+8];
	setp.gt.ftz.f32	%p79, %f1314, 0f00000000;
	ld.f32 	%f225, [%rd273+16];
	ld.f32 	%f226, [%rd273+20];
	ld.f32 	%f227, [%rd273+24];
	@%p79 bra 	BB1_100;
	bra.uni 	BB1_99;

BB1_100:
	ld.local.f32 	%f1313, [%rd4+-12];
	sub.ftz.f32 	%f1000, %f225, %f1313;
	ld.local.f32 	%f1312, [%rd4+-8];
	sub.ftz.f32 	%f1001, %f226, %f1312;
	ld.local.f32 	%f1311, [%rd4+-4];
	sub.ftz.f32 	%f1002, %f227, %f1311;
	mul.ftz.f32 	%f1003, %f1001, %f1001;
	fma.rn.ftz.f32 	%f1004, %f1000, %f1000, %f1003;
	fma.rn.ftz.f32 	%f1005, %f1002, %f1002, %f1004;
	sqrt.approx.ftz.f32 	%f234, %f1005;
	add.ftz.f32 	%f1006, %f1314, %f1314;
	setp.geu.ftz.f32	%p80, %f234, %f1006;
	@%p80 bra 	BB1_102;

	mov.f32 	%f1007, 0f40000000;
	div.approx.ftz.f32 	%f1314, %f234, %f1007;
	bra.uni 	BB1_102;

BB1_90:
	setp.eq.s32	%p75, %r53, 4;
	@%p75 bra 	BB1_97;
	bra.uni 	BB1_91;

BB1_97:
	ld.f32 	%f987, [%rd273+16];
	add.ftz.f32 	%f988, %f987, 0f00000000;
	ld.f32 	%f989, [%rd273+20];
	add.ftz.f32 	%f990, %f989, 0f00000000;
	ld.f32 	%f991, [%rd273+24];
	add.ftz.f32 	%f992, %f991, 0f00000000;
	ld.local.f32 	%f1315, [%rd4+-12];
	sub.ftz.f32 	%f993, %f988, %f1315;
	ld.local.f32 	%f1316, [%rd4+-8];
	sub.ftz.f32 	%f994, %f990, %f1316;
	ld.local.f32 	%f1317, [%rd4+-4];
	sub.ftz.f32 	%f995, %f992, %f1317;
	mul.ftz.f32 	%f996, %f994, %f994;
	fma.rn.ftz.f32 	%f997, %f993, %f993, %f996;
	fma.rn.ftz.f32 	%f998, %f995, %f995, %f997;
	sqrt.approx.ftz.f32 	%f1322, %f998;
	rsqrt.approx.ftz.f32 	%f999, %f998;
	mul.ftz.f32 	%f1318, %f993, %f999;
	mul.ftz.f32 	%f1319, %f994, %f999;
	mul.ftz.f32 	%f1320, %f995, %f999;
	bra.uni 	BB1_103;

BB1_99:
	ld.local.f32 	%f1313, [%rd4+-12];
	ld.local.f32 	%f1312, [%rd4+-8];
	ld.local.f32 	%f1311, [%rd4+-4];

BB1_102:
	fma.rn.ftz.f32 	%f1009, %f1303, %f1314, %f225;
	fma.rn.ftz.f32 	%f1010, %f1304, %f1314, %f226;
	fma.rn.ftz.f32 	%f1011, %f1305, %f1314, %f227;
	sub.ftz.f32 	%f1012, %f1009, %f1313;
	sub.ftz.f32 	%f1013, %f1010, %f1312;
	sub.ftz.f32 	%f1014, %f1011, %f1311;
	mul.ftz.f32 	%f1015, %f1013, %f1013;
	fma.rn.ftz.f32 	%f1016, %f1012, %f1012, %f1015;
	fma.rn.ftz.f32 	%f1017, %f1014, %f1014, %f1016;
	sqrt.approx.ftz.f32 	%f1322, %f1017;
	rsqrt.approx.ftz.f32 	%f1018, %f1017;
	mul.ftz.f32 	%f1318, %f1012, %f1018;
	mul.ftz.f32 	%f1319, %f1013, %f1018;
	mul.ftz.f32 	%f1320, %f1014, %f1018;
	bra.uni 	BB1_103;

BB1_91:
	setp.eq.s32	%p76, %r53, 2;
	@%p76 bra 	BB1_96;
	bra.uni 	BB1_92;

BB1_96:
	ld.local.f32 	%f1315, [%rd4+-12];
	ld.local.f32 	%f1316, [%rd4+-8];
	ld.local.f32 	%f1317, [%rd4+-4];
	mul.ftz.f32 	%f981, %f1304, %f1304;
	fma.rn.ftz.f32 	%f982, %f1303, %f1303, %f981;
	fma.rn.ftz.f32 	%f983, %f1305, %f1305, %f982;
	rsqrt.approx.ftz.f32 	%f984, %f983;
	mul.ftz.f32 	%f1318, %f1303, %f984;
	mul.ftz.f32 	%f1319, %f1304, %f984;
	mul.ftz.f32 	%f1320, %f1305, %f984;
	ld.global.f32 	%f985, [SceneBounds+24];
	add.ftz.f32 	%f1322, %f985, %f985;
	bra.uni 	BB1_103;

BB1_92:
	setp.eq.s32	%p77, %r53, 0;
	@%p77 bra 	BB1_95;
	bra.uni 	BB1_93;

BB1_95:
	ld.local.f32 	%f1315, [%rd4+-12];
	ld.local.f32 	%f1316, [%rd4+-8];
	ld.local.f32 	%f1317, [%rd4+-4];
	mov.f32 	%f1322, 0f6C4ECB8F;
	mov.f32 	%f1321, 0f00000000;
	mov.f32 	%f1318, %f1303;
	mov.f32 	%f1319, %f1304;
	mov.f32 	%f1320, %f1305;
	bra.uni 	BB1_104;

BB1_93:
	setp.ne.s32	%p78, %r53, 3;
	@%p78 bra 	BB1_104;

	ld.v2.f32 	{%f950, %f951}, [%rd273];
	mul.ftz.f32 	%f954, %f1303, %f950;
	mul.ftz.f32 	%f955, %f1304, %f951;
	ld.f32 	%f956, [%rd273+52];
	ld.f32 	%f957, [%rd273+56];
	ld.f32 	%f958, [%rd273+60];
	ld.f32 	%f959, [%rd273+16];
	fma.rn.ftz.f32 	%f960, %f954, %f956, %f959;
	ld.f32 	%f961, [%rd273+20];
	fma.rn.ftz.f32 	%f962, %f954, %f957, %f961;
	ld.f32 	%f963, [%rd273+24];
	fma.rn.ftz.f32 	%f964, %f954, %f958, %f963;
	ld.f32 	%f965, [%rd273+64];
	ld.f32 	%f966, [%rd273+68];
	ld.f32 	%f967, [%rd273+72];
	fma.rn.ftz.f32 	%f968, %f955, %f965, %f960;
	fma.rn.ftz.f32 	%f969, %f955, %f966, %f962;
	fma.rn.ftz.f32 	%f970, %f955, %f967, %f964;
	ld.local.f32 	%f1315, [%rd4+-12];
	sub.ftz.f32 	%f971, %f968, %f1315;
	ld.local.f32 	%f1316, [%rd4+-8];
	sub.ftz.f32 	%f972, %f969, %f1316;
	ld.local.f32 	%f1317, [%rd4+-4];
	sub.ftz.f32 	%f973, %f970, %f1317;
	mul.ftz.f32 	%f974, %f972, %f972;
	fma.rn.ftz.f32 	%f975, %f971, %f971, %f974;
	fma.rn.ftz.f32 	%f976, %f973, %f973, %f975;
	sqrt.approx.ftz.f32 	%f1322, %f976;
	rsqrt.approx.ftz.f32 	%f977, %f976;
	mul.ftz.f32 	%f1318, %f971, %f977;
	mul.ftz.f32 	%f1319, %f972, %f977;
	mul.ftz.f32 	%f1320, %f973, %f977;

BB1_103:
	mov.f32 	%f1321, 0f3A83126F;

BB1_104:
	mov.b32 	 %f1245, %r4;
	mul.ftz.f32 	%f1019, %f30, %f1319;
	fma.rn.ftz.f32 	%f1020, %f54, %f1318, %f1019;
	fma.rn.ftz.f32 	%f252, %f1245, %f1320, %f1020;
	cvt.ftz.sat.f32.f32	%f1021, %f252;
	mov.f32 	%f1022, 0f3C23D70A;
	sub.ftz.f32 	%f1023, %f1022, 0f3DCCCCCD;
	fma.rn.ftz.f32 	%f1024, %f1021, %f1023, 0f3DCCCCCD;
	fma.rn.ftz.f32 	%f1315, %f54, %f1024, %f1315;
	fma.rn.ftz.f32 	%f1316, %f30, %f1024, %f1316;
	fma.rn.ftz.f32 	%f1317, %f1245, %f1024, %f1317;
	st.local.u32 	[%rd67], %r198;
	st.local.u16 	[%rd14], %rs1;
	mov.f32 	%f1025, 0f3F800000;
	st.local.v4.f32 	[%rd14+20], {%f1025, %f1025, %f1025, %f1025};
	ld.global.u32 	%r54, [top_shadower];
	setp.gt.s32	%p81, %r1, 1;
	@%p81 bra 	BB1_106;
	bra.uni 	BB1_105;

BB1_106:
	mov.u32 	%r388, 1;
	mov.u32 	%r337, 36;
	// inline asm
	call _rt_trace_mask_flags_64, (%r54, %f1315, %f1316, %f1317, %f1318, %f1319, %f1320, %r388, %f1321, %f1322, %r388, %r337, %rd56, %r196);
	// inline asm
	bra.uni 	BB1_107;

BB1_105:
	mov.u32 	%r386, 1;
	mov.u32 	%r332, 32;
	// inline asm
	call _rt_trace_mask_flags_64, (%r54, %f1315, %f1316, %f1317, %f1318, %f1319, %f1320, %r386, %f1321, %f1322, %r386, %r332, %rd56, %r196);
	// inline asm

BB1_107:
	ld.local.v2.u64 	{%rd284, %rd285}, [%rd67];
	ld.local.v4.f32 	{%f1042, %f1043, %f1044, %f1045}, [%rd14+20];
	mov.b64	{%r339, %r340}, %rd284;
	mov.b32 	 %f1046, %r339;
	setp.gt.ftz.f32	%p82, %f1046, %f1321;
	setp.lt.ftz.f32	%p83, %f1046, %f1322;
	and.pred  	%p84, %p82, %p83;
	@%p84 bra 	BB1_127;

	max.ftz.f32 	%f1047, %f1042, %f1043;
	max.ftz.f32 	%f1048, %f1047, %f1044;
	setp.leu.ftz.f32	%p85, %f1048, 0f3A83126F;
	@%p85 bra 	BB1_127;

	mov.u32 	%r387, 1;
	// inline asm
	call (%rd287), _rt_buffer_get_64, (%rd274, %r387, %r327, %rd275, %rd55, %rd55, %rd55);
	// inline asm
	ld.u32 	%r55, [%rd287+112];
	setp.eq.s32	%p86, %r55, 1;
	@%p86 bra 	BB1_122;
	bra.uni 	BB1_110;

BB1_122:
	ld.f32 	%f282, [%rd287+88];
	ld.f32 	%f283, [%rd287+92];
	ld.f32 	%f284, [%rd287+96];
	ld.f32 	%f285, [%rd287+100];
	ld.f32 	%f1327, [%rd287+8];
	ld.f32 	%f287, [%rd287+16];
	ld.f32 	%f288, [%rd287+20];
	ld.f32 	%f289, [%rd287+24];
	setp.leu.ftz.f32	%p95, %f1327, 0f00000000;
	@%p95 bra 	BB1_125;

	sub.ftz.f32 	%f1153, %f287, %f1315;
	sub.ftz.f32 	%f1154, %f288, %f1316;
	mul.ftz.f32 	%f1155, %f1154, %f1154;
	fma.rn.ftz.f32 	%f1156, %f1153, %f1153, %f1155;
	sub.ftz.f32 	%f1157, %f289, %f1317;
	fma.rn.ftz.f32 	%f1158, %f1157, %f1157, %f1156;
	sqrt.approx.ftz.f32 	%f290, %f1158;
	add.ftz.f32 	%f1159, %f1327, %f1327;
	setp.geu.ftz.f32	%p96, %f290, %f1159;
	@%p96 bra 	BB1_125;

	mov.f32 	%f1160, 0f40000000;
	div.approx.ftz.f32 	%f1327, %f290, %f1160;

BB1_125:
	mul.ftz.f32 	%f1161, %f282, 0f3DA2F983;
	setp.gt.ftz.f32	%p97, %f1327, 0f00000000;
	selp.f32	%f1162, %f1161, %f282, %p97;
	mul.ftz.f32 	%f1163, %f283, 0f3DA2F983;
	selp.f32	%f1164, %f1163, %f283, %p97;
	mul.ftz.f32 	%f1165, %f284, 0f3DA2F983;
	selp.f32	%f1166, %f1165, %f284, %p97;
	fma.rn.ftz.f32 	%f1167, %f1303, %f1327, %f287;
	fma.rn.ftz.f32 	%f1168, %f1304, %f1327, %f288;
	fma.rn.ftz.f32 	%f1169, %f1305, %f1327, %f289;
	sub.ftz.f32 	%f1170, %f1167, %f1315;
	sub.ftz.f32 	%f1171, %f1168, %f1316;
	sub.ftz.f32 	%f1172, %f1169, %f1317;
	mul.ftz.f32 	%f1173, %f1171, %f1171;
	fma.rn.ftz.f32 	%f1174, %f1170, %f1170, %f1173;
	fma.rn.ftz.f32 	%f1175, %f1172, %f1172, %f1174;
	add.ftz.f32 	%f1176, %f1175, 0f38D1B717;
	rcp.approx.ftz.f32 	%f1177, %f1176;
	mul.ftz.f32 	%f1178, %f285, %f285;
	div.approx.ftz.f32 	%f1179, %f1175, %f1178;
	mul.ftz.f32 	%f1180, %f1179, %f1179;
	sub.ftz.f32 	%f1182, %f1025, %f1180;
	cvt.ftz.sat.f32.f32	%f1183, %f1182;
	mul.ftz.f32 	%f1184, %f1183, %f1183;
	mul.ftz.f32 	%f1185, %f1177, %f1184;
	mul.ftz.f32 	%f1330, %f1162, %f1185;
	mul.ftz.f32 	%f1329, %f1164, %f1185;
	mul.ftz.f32 	%f1328, %f1166, %f1185;
	bra.uni 	BB1_126;

BB1_110:
	setp.eq.s32	%p87, %r55, 4;
	@%p87 bra 	BB1_120;
	bra.uni 	BB1_111;

BB1_120:
	ld.f32 	%f1112, [%rd287+16];
	sub.ftz.f32 	%f1113, %f1112, %f1315;
	ld.f32 	%f1114, [%rd287+20];
	sub.ftz.f32 	%f1115, %f1114, %f1316;
	ld.f32 	%f1116, [%rd287+24];
	sub.ftz.f32 	%f1117, %f1116, %f1317;
	mul.ftz.f32 	%f1118, %f1115, %f1115;
	fma.rn.ftz.f32 	%f1119, %f1113, %f1113, %f1118;
	fma.rn.ftz.f32 	%f276, %f1117, %f1117, %f1119;
	rsqrt.approx.ftz.f32 	%f1120, %f276;
	mul.ftz.f32 	%f1121, %f1113, %f1120;
	mul.ftz.f32 	%f1122, %f1115, %f1120;
	mul.ftz.f32 	%f1123, %f1117, %f1120;
	ld.f32 	%f1124, [%rd287+28];
	mul.ftz.f32 	%f1125, %f1124, %f1121;
	ld.f32 	%f1126, [%rd287+32];
	mul.ftz.f32 	%f1127, %f1126, %f1122;
	neg.ftz.f32 	%f1128, %f1127;
	sub.ftz.f32 	%f1129, %f1128, %f1125;
	ld.f32 	%f1130, [%rd287+36];
	mul.ftz.f32 	%f1131, %f1130, %f1123;
	sub.ftz.f32 	%f277, %f1129, %f1131;
	ld.f32 	%f278, [%rd287];
	setp.ltu.ftz.f32	%p94, %f277, %f278;
	mov.f32 	%f1328, 0f00000000;
	mov.f32 	%f1329, %f1328;
	mov.f32 	%f1330, %f1328;
	@%p94 bra 	BB1_126;

	sub.ftz.f32 	%f1132, %f277, %f278;
	ld.f32 	%f1133, [%rd287+4];
	mul.ftz.f32 	%f1134, %f1132, %f1133;
	cvt.ftz.sat.f32.f32	%f1135, %f1134;
	mul.ftz.f32 	%f1136, %f1135, %f1135;
	ld.f32 	%f1137, [%rd287+100];
	mul.ftz.f32 	%f1138, %f1137, %f1137;
	div.approx.ftz.f32 	%f1139, %f1322, %f1138;
	mul.ftz.f32 	%f1140, %f1139, %f1139;
	sub.ftz.f32 	%f1142, %f1025, %f1140;
	cvt.ftz.sat.f32.f32	%f1143, %f1142;
	mul.ftz.f32 	%f1144, %f1143, %f1143;
	mul.ftz.f32 	%f1145, %f1136, %f1144;
	ld.f32 	%f1146, [%rd287+88];
	mul.ftz.f32 	%f1147, %f1146, %f1145;
	ld.f32 	%f1148, [%rd287+92];
	mul.ftz.f32 	%f1149, %f1148, %f1145;
	ld.f32 	%f1150, [%rd287+96];
	mul.ftz.f32 	%f1151, %f1150, %f1145;
	rcp.approx.ftz.f32 	%f1152, %f276;
	mul.ftz.f32 	%f1330, %f1152, %f1147;
	mul.ftz.f32 	%f1329, %f1152, %f1149;
	mul.ftz.f32 	%f1328, %f1152, %f1151;
	bra.uni 	BB1_126;

BB1_111:
	setp.eq.s32	%p88, %r55, 0;
	@%p88 bra 	BB1_116;
	bra.uni 	BB1_112;

BB1_116:
	ld.global.u32 	%r343, [SkyLight+16];
	mov.f32 	%f1328, 0f00000000;
	setp.lt.s32	%p92, %r343, 1;
	mov.f32 	%f1329, %f1328;
	mov.f32 	%f1330, %f1328;
	mov.f32 	%f1326, %f1305;
	@%p92 bra 	BB1_118;

	mul.ftz.f32 	%f1091, %f1304, %f1304;
	fma.rn.ftz.f32 	%f1092, %f1303, %f1303, %f1091;
	fma.rn.ftz.f32 	%f1093, %f1305, %f1305, %f1092;
	rsqrt.approx.ftz.f32 	%f1094, %f1093;
	mul.ftz.f32 	%f1088, %f1303, %f1094;
	mul.ftz.f32 	%f1089, %f1304, %f1094;
	mul.ftz.f32 	%f1326, %f1305, %f1094;
	ld.global.u32 	%r344, [SkyLightTextureSamplerID];
	mov.u32 	%r345, 6;
	mov.u32 	%r346, 0;
	// inline asm
	call (%f1084, %f1085, %f1086, %f1087), _rt_texture_get_base_id, (%r344, %r345, %f1088, %f1089, %f1326, %r346);
	// inline asm
	ld.global.v4.f32 	{%f1095, %f1096, %f1097, %f1098}, [SkyLight];
	mul.ftz.f32 	%f1330, %f1095, %f1084;
	mul.ftz.f32 	%f1329, %f1085, %f1096;
	mul.ftz.f32 	%f1328, %f1086, %f1097;

BB1_118:
	setp.leu.ftz.f32	%p93, %f1326, 0f00000000;
	@%p93 bra 	BB1_126;

	ld.global.v4.f32 	{%f1102, %f1103, %f1104, %f1105}, [SkyLight+32];
	fma.rn.ftz.f32 	%f1330, %f1102, 0f3EA2F983, %f1330;
	fma.rn.ftz.f32 	%f1329, %f1103, 0f3EA2F983, %f1329;
	fma.rn.ftz.f32 	%f1328, %f1104, 0f3EA2F983, %f1328;
	bra.uni 	BB1_126;

BB1_112:
	setp.eq.s32	%p89, %r55, 2;
	@%p89 bra 	BB1_115;
	bra.uni 	BB1_113;

BB1_115:
	ld.f32 	%f1330, [%rd287+88];
	ld.f32 	%f1329, [%rd287+92];
	ld.f32 	%f1328, [%rd287+96];
	bra.uni 	BB1_126;

BB1_113:
	setp.ne.s32	%p90, %r55, 3;
	@%p90 bra 	BB1_126;

	ld.f32 	%f1049, [%rd287+16];
	sub.ftz.f32 	%f1050, %f1049, %f1315;
	ld.f32 	%f1051, [%rd287+20];
	sub.ftz.f32 	%f1052, %f1051, %f1316;
	ld.f32 	%f1053, [%rd287+24];
	sub.ftz.f32 	%f1054, %f1053, %f1317;
	ld.f32 	%f1055, [%rd287+100];
	mul.ftz.f32 	%f1056, %f1055, %f1055;
	div.approx.ftz.f32 	%f1057, %f1322, %f1056;
	mul.ftz.f32 	%f1058, %f1057, %f1057;
	sub.ftz.f32 	%f1060, %f1025, %f1058;
	cvt.ftz.sat.f32.f32	%f1061, %f1060;
	mul.ftz.f32 	%f1062, %f1061, %f1061;
	ld.f32 	%f1063, [%rd287+88];
	mul.ftz.f32 	%f1064, %f1063, %f1062;
	ld.f32 	%f1065, [%rd287+92];
	mul.ftz.f32 	%f1066, %f1065, %f1062;
	ld.f32 	%f1067, [%rd287+96];
	mul.ftz.f32 	%f1068, %f1067, %f1062;
	ld.f32 	%f1069, [%rd287+28];
	mul.ftz.f32 	%f1070, %f1050, %f1069;
	ld.f32 	%f1071, [%rd287+32];
	mul.ftz.f32 	%f1072, %f1052, %f1071;
	neg.ftz.f32 	%f1073, %f1072;
	sub.ftz.f32 	%f1074, %f1073, %f1070;
	ld.f32 	%f1075, [%rd287+36];
	mul.ftz.f32 	%f1076, %f1054, %f1075;
	sub.ftz.f32 	%f1077, %f1074, %f1076;
	setp.gtu.ftz.f32	%p91, %f1077, 0f00000000;
	mul.ftz.f32 	%f1078, %f1064, 0f00000000;
	mul.ftz.f32 	%f1079, %f1066, 0f00000000;
	mul.ftz.f32 	%f1080, %f1068, 0f00000000;
	selp.f32	%f1328, %f1068, %f1080, %p91;
	selp.f32	%f1329, %f1066, %f1079, %p91;
	selp.f32	%f1330, %f1064, %f1078, %p91;

BB1_126:
	mov.b32 	 %f1246, %r4;
	cvt.ftz.f64.f32	%fd23, %f252;
	mov.f64 	%fd24, 0d0000000000000000;
	max.f64 	%fd25, %fd23, %fd24;
	cvt.rn.ftz.f32.f64	%f1186, %fd25;
	mul.ftz.f32 	%f1187, %f54, %f1186;
	mul.ftz.f32 	%f1188, %f30, %f1186;
	mul.ftz.f32 	%f1189, %f1246, %f1186;
	mul.ftz.f32 	%f1190, %f1187, 0f3EA2F983;
	mul.ftz.f32 	%f1191, %f1188, 0f3EA2F983;
	mul.ftz.f32 	%f1192, %f1189, 0f3EA2F983;
	mul.ftz.f32 	%f1330, %f1042, %f1330;
	mul.ftz.f32 	%f1193, %f1330, %f1190;
	mul.ftz.f32 	%f1329, %f1043, %f1329;
	mul.ftz.f32 	%f1194, %f1329, %f1191;
	mul.ftz.f32 	%f1328, %f1044, %f1328;
	mul.ftz.f32 	%f1195, %f1328, %f1192;
	mul.ftz.f32 	%f1196, %f20, %f1193;
	mul.ftz.f32 	%f1197, %f21, %f1194;
	mul.ftz.f32 	%f1198, %f22, %f1195;
	mul.ftz.f32 	%f1199, %f29, %f193;
	rcp.approx.ftz.f32 	%f1200, %f1199;
	fma.rn.ftz.f32 	%f23, %f1200, %f1196, %f23;
	fma.rn.ftz.f32 	%f24, %f1200, %f1197, %f24;
	fma.rn.ftz.f32 	%f25, %f1200, %f1198, %f25;

BB1_127:
	setp.eq.ftz.f32	%p98, %f76, 0f00000000;
	@%p98 bra 	BB1_14;

	mov.b32 	 %f1244, %r4;
	st.local.f32 	[%rd4], %f77;
	st.local.f32 	[%rd4+4], %f78;
	st.local.f32 	[%rd4+8], %f79;
	ld.local.f32 	%f1201, [%rd4+4];
	mul.ftz.f32 	%f1202, %f30, %f1201;
	fma.rn.ftz.f32 	%f1203, %f54, %f77, %f1202;
	fma.rn.ftz.f32 	%f1204, %f1244, %f79, %f1203;
	cvt.ftz.sat.f32.f32	%f1205, %f1204;
	mov.f32 	%f1206, 0f3C23D70A;
	sub.ftz.f32 	%f1207, %f1206, 0f3DCCCCCD;
	fma.rn.ftz.f32 	%f1208, %f1205, %f1207, 0f3DCCCCCD;
	ld.local.f32 	%f1209, [%rd4+-12];
	fma.rn.ftz.f32 	%f1210, %f54, %f1208, %f1209;
	st.local.f32 	[%rd4+-12], %f1210;
	ld.local.f32 	%f1211, [%rd4+-8];
	fma.rn.ftz.f32 	%f1212, %f30, %f1208, %f1211;
	st.local.f32 	[%rd4+-8], %f1212;
	ld.local.f32 	%f1213, [%rd4+-4];
	fma.rn.ftz.f32 	%f1214, %f1244, %f1208, %f1213;
	st.local.f32 	[%rd4+-4], %f1214;
	mul.ftz.f32 	%f29, %f29, %f76;
	mul.ftz.f32 	%f1215, %f30, %f76;
	mul.ftz.f32 	%f21, %f21, %f1215;
	mul.ftz.f32 	%f1216, %f54, %f76;
	mul.ftz.f32 	%f20, %f20, %f1216;
	max.ftz.f32 	%f1217, %f20, %f21;
	mul.ftz.f32 	%f1218, %f1244, %f76;
	mul.ftz.f32 	%f22, %f22, %f1218;
	max.ftz.f32 	%f323, %f1217, %f22;
	setp.geu.ftz.f32	%p99, %f323, 0f3F800000;
	setp.lt.u32	%p100, %r1, 5;
	or.pred  	%p101, %p99, %p100;
	@%p101 bra 	BB1_133;

	mov.u64 	%rd311, _ZZ8Prime512jE6Primes;
	mov.f32 	%f1345, 0f3F800000;
	sub.ftz.f32 	%f324, %f1345, %f323;
	ld.local.u32 	%r347, [%rd3];
	shl.b32 	%r348, %r347, 12;
	add.s32 	%r349, %r347, %r348;
	add.s32 	%r350, %r349, 2127912214;
	shr.u32 	%r351, %r350, 19;
	xor.b32  	%r352, %r350, %r351;
	xor.b32  	%r353, %r352, -949894596;
	shl.b32 	%r354, %r353, 5;
	add.s32 	%r355, %r353, %r354;
	add.s32 	%r356, %r355, 374761393;
	add.s32 	%r357, %r355, -369570787;
	shl.b32 	%r358, %r356, 9;
	xor.b32  	%r359, %r357, %r358;
	shl.b32 	%r360, %r359, 3;
	add.s32 	%r361, %r359, %r360;
	add.s32 	%r362, %r361, -42973499;
	shr.u32 	%r363, %r362, 16;
	xor.b32  	%r364, %r362, %r363;
	xor.b32  	%r365, %r364, -1252372727;
	ld.local.u32 	%r366, [%rd3+4];
	add.s32 	%r414, %r365, %r366;
	ld.local.u32 	%r57, [%rd3+12];
	and.b32  	%r367, %r57, 511;
	mul.wide.u32 	%rd294, %r367, 4;
	add.s64 	%rd296, %rd311, %rd294;
	ld.global.u32 	%r58, [%rd296];
	cvt.rn.f64.u32	%fd26, %r58;
	rcp.rn.f64 	%fd27, %fd26;
	cvt.rn.ftz.f32.f64	%f325, %fd27;
	setp.eq.s32	%p102, %r414, 0;
	mov.f32 	%f1346, 0f00000000;
	@%p102 bra 	BB1_131;

BB1_130:
	rem.u32 	%r368, %r414, %r58;
	cvt.rn.f32.u32	%f1222, %r368;
	mul.ftz.f32 	%f1345, %f325, %f1345;
	fma.rn.ftz.f32 	%f1346, %f1345, %f1222, %f1346;
	div.u32 	%r414, %r414, %r58;
	setp.ne.s32	%p103, %r414, 0;
	@%p103 bra 	BB1_130;

BB1_131:
	add.s32 	%r369, %r57, 1;
	st.local.u32 	[%rd3+12], %r369;
	ld.local.u32 	%r370, [%rd2];
	add.s32 	%r371, %r370, 1;
	st.local.u32 	[%rd2], %r371;
	setp.lt.ftz.f32	%p104, %f1346, %f324;
	@%p104 bra 	BB1_14;

	mov.f32 	%f1223, 0f3F800000;
	sub.ftz.f32 	%f1224, %f1223, %f324;
	rcp.approx.ftz.f32 	%f1225, %f1224;
	mul.ftz.f32 	%f20, %f20, %f1225;
	mul.ftz.f32 	%f21, %f21, %f1225;
	mul.ftz.f32 	%f22, %f22, %f1225;

BB1_133:
	max.ftz.f32 	%f1226, %f20, %f21;
	max.ftz.f32 	%f1227, %f1226, %f22;
	div.approx.ftz.f32 	%f1228, %f1227, %f29;
	setp.gt.ftz.f32	%p105, %f1228, 0f40A00000;
	@%p105 bra 	BB1_14;
	bra.uni 	BB1_134;

BB1_5:
	ld.global.u32 	%r200, [max_sky_bounces];
	setp.gt.u32	%p6, %r1, %r200;
	@%p6 bra 	BB1_14;

	ld.local.f32 	%f1275, [%rd4+8];
	ld.global.u32 	%r201, [SkyLight+16];
	mov.f32 	%f1272, 0f00000000;
	setp.lt.s32	%p7, %r201, 1;
	@%p7 bra 	BB1_7;

	ld.local.f32 	%f386, [%rd4];
	ld.local.f32 	%f387, [%rd4+4];
	mul.ftz.f32 	%f388, %f387, %f387;
	fma.rn.ftz.f32 	%f389, %f386, %f386, %f388;
	fma.rn.ftz.f32 	%f390, %f1275, %f1275, %f389;
	rsqrt.approx.ftz.f32 	%f391, %f390;
	mul.ftz.f32 	%f383, %f386, %f391;
	mul.ftz.f32 	%f384, %f387, %f391;
	mul.ftz.f32 	%f1275, %f1275, %f391;
	ld.global.u32 	%r202, [SkyLightTextureSamplerID];
	mov.u32 	%r203, 6;
	mov.u32 	%r204, 0;
	// inline asm
	call (%f379, %f380, %f381, %f382), _rt_texture_get_base_id, (%r202, %r203, %f383, %f384, %f1275, %r204);
	// inline asm
	ld.global.v4.f32 	{%f392, %f393, %f394, %f395}, [SkyLight];
	mul.ftz.f32 	%f1272, %f392, %f379;
	mul.ftz.f32 	%f1273, %f380, %f393;
	mul.ftz.f32 	%f1274, %f381, %f394;
	setp.leu.ftz.f32	%p8, %f395, 0f3C23D70A;
	setp.lt.u32	%p9, %r1, 2;
	or.pred  	%p10, %p9, %p8;
	@%p10 bra 	BB1_10;

	mul.ftz.f32 	%f1272, %f1272, %f395;
	mul.ftz.f32 	%f1273, %f1273, %f395;
	mul.ftz.f32 	%f1274, %f1274, %f395;
	bra.uni 	BB1_10;

BB1_7:
	mov.f32 	%f1273, %f1272;
	mov.f32 	%f1274, %f1272;

BB1_10:
	setp.leu.ftz.f32	%p11, %f1275, 0f00000000;
	@%p11 bra 	BB1_12;

	ld.global.v4.f32 	{%f399, %f400, %f401, %f402}, [SkyLight+32];
	fma.rn.ftz.f32 	%f1272, %f399, 0f3EA2F983, %f1272;
	fma.rn.ftz.f32 	%f1273, %f400, 0f3EA2F983, %f1273;
	fma.rn.ftz.f32 	%f1274, %f401, 0f3EA2F983, %f1274;

BB1_12:
	mul.ftz.f32 	%f406, %f1273, %f1273;
	fma.rn.ftz.f32 	%f407, %f1272, %f1272, %f406;
	fma.rn.ftz.f32 	%f408, %f1274, %f1274, %f407;
	sqrt.approx.ftz.f32 	%f409, %f408;
	setp.leu.ftz.f32	%p12, %f409, 0f00000000;
	@%p12 bra 	BB1_14;

	mul.ftz.f32 	%f410, %f20, %f1272;
	rcp.approx.ftz.f32 	%f411, %f29;
	mul.ftz.f32 	%f412, %f21, %f1273;
	mul.ftz.f32 	%f413, %f22, %f1274;
	fma.rn.ftz.f32 	%f23, %f410, %f411, %f23;
	fma.rn.ftz.f32 	%f24, %f412, %f411, %f24;
	fma.rn.ftz.f32 	%f25, %f413, %f411, %f25;

BB1_14:
	ld.param.u64 	%rd310, [_Z9pathtrace5uint3S_jjR6float3RjS2_S1_S1__param_5];
	add.ftz.f32 	%f1353, %f23, 0f00000000;
	add.ftz.f32 	%f1354, %f24, 0f00000000;
	add.ftz.f32 	%f1355, %f25, 0f00000000;
	ld.u32 	%r378, [%rd310];
	add.s32 	%r379, %r378, 1;
	st.u32 	[%rd310], %r379;

BB1_15:
	st.param.f32	[func_retval0+0], %f1353;
	st.param.f32	[func_retval0+4], %f1354;
	st.param.f32	[func_retval0+8], %f1355;
	ret;
}

	// .globl	_ZN9TSHVectorILi2EE15SHBasisFunctionERK6float3
.visible .func  (.param .align 16 .b8 func_retval0[16]) _ZN9TSHVectorILi2EE15SHBasisFunctionERK6float3(
	.param .b64 _ZN9TSHVectorILi2EE15SHBasisFunctionERK6float3_param_0
)
{
	.reg .f32 	%f<8>;
	.reg .b64 	%rd<2>;


	ld.param.u64 	%rd1, [_ZN9TSHVectorILi2EE15SHBasisFunctionERK6float3_param_0];
	ld.f32 	%f1, [%rd1+4];
	mul.ftz.f32 	%f2, %f1, 0fBEFA2A2C;
	ld.f32 	%f3, [%rd1+8];
	mul.ftz.f32 	%f4, %f3, 0f3EFA2A2C;
	ld.f32 	%f5, [%rd1];
	mul.ftz.f32 	%f6, %f5, 0fBEFA2A2C;
	mov.f32 	%f7, 0f3E906EC1;
	st.param.f32	[func_retval0+0], %f7;
	st.param.f32	[func_retval0+4], %f2;
	st.param.f32	[func_retval0+8], %f4;
	st.param.f32	[func_retval0+12], %f6;
	ret;
}

	// .globl	_ZN9TSHVectorILi3EE15SHBasisFunctionERK6float3
.visible .func  (.param .align 16 .b8 func_retval0[48]) _ZN9TSHVectorILi3EE15SHBasisFunctionERK6float3(
	.param .b64 _ZN9TSHVectorILi3EE15SHBasisFunctionERK6float3_param_0
)
{
	.reg .f32 	%f<24>;
	.reg .b64 	%rd<2>;


	ld.param.u64 	%rd1, [_ZN9TSHVectorILi3EE15SHBasisFunctionERK6float3_param_0];
	ld.f32 	%f1, [%rd1+4];
	mul.ftz.f32 	%f2, %f1, 0fBEFA2A2C;
	ld.f32 	%f3, [%rd1+8];
	mul.ftz.f32 	%f4, %f3, 0f3EFA2A2C;
	ld.f32 	%f5, [%rd1];
	mul.ftz.f32 	%f6, %f5, 0fBEFA2A2C;
	mul.ftz.f32 	%f7, %f5, %f5;
	mul.ftz.f32 	%f8, %f1, %f1;
	mul.ftz.f32 	%f9, %f3, %f3;
	mul.ftz.f32 	%f10, %f5, 0f3F8BD89D;
	mul.ftz.f32 	%f11, %f10, %f1;
	mul.ftz.f32 	%f12, %f1, 0fBF8BD89D;
	mul.ftz.f32 	%f13, %f12, %f3;
	fma.rn.ftz.f32 	%f14, %f9, 0f40400000, 0fBF800000;
	mul.ftz.f32 	%f15, %f14, 0f3EA17B0F;
	mul.ftz.f32 	%f16, %f5, 0fBF8BD89D;
	mul.ftz.f32 	%f17, %f16, %f3;
	sub.ftz.f32 	%f18, %f7, %f8;
	mul.ftz.f32 	%f19, %f18, 0f3F0BD89D;
	mov.f32 	%f20, 0f3E906EC1;
	st.param.f32	[func_retval0+0], %f20;
	st.param.f32	[func_retval0+4], %f2;
	st.param.f32	[func_retval0+8], %f4;
	st.param.f32	[func_retval0+12], %f6;
	st.param.f32	[func_retval0+16], %f11;
	st.param.f32	[func_retval0+20], %f13;
	st.param.f32	[func_retval0+24], %f15;
	st.param.f32	[func_retval0+28], %f17;
	st.param.f32	[func_retval0+32], %f19;
	ret;
}


#####//
// Generated by NVIDIA NVVM Compiler
//
// Compiler Build ID: CL-27506705
// Cuda compilation tools, release 10.2, V10.2.89
// Based on LLVM 3.4svn
//

.version 6.5
.target sm_50
.address_size 64

	// .globl	_Z18opacity_closesthitv
.visible .global .align 4 .b8 debug_input[12];
.visible .global .align 4 .b8 SceneBounds[28];
.visible .global .align 16 .b8 Payload[80];
.visible .global .align 16 .b8 geometric_normal[16];
.visible .global .align 16 .b8 shading_normal[16];
.visible .global .align 4 .b8 ray[36];
.visible .global .align 4 .f32 t_hit;
.visible .global .align 8 .b8 texcoord[8];
.visible .global .align 4 .u32 has_diffuse;
.visible .global .texref diffuse_tex;
.visible .global .align 4 .u32 has_transmission;
.visible .global .texref transmission_tex;
.visible .global .align 4 .u32 has_emissive;
.visible .global .texref emissive_tex;
.visible .global .align 4 .u32 mesh_instance_id;
.visible .global .align 4 .u32 blend_mode;
.visible .global .align 4 .u32 is_twoside;
.visible .global .align 4 .u32 is_subsurface;
.visible .global .align 4 .f32 mask_clip_value;
.visible .global .align 4 .u32 blend_diffuse_with_normal;
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo11debug_inputE[8] = {82, 97, 121, 0, 12, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo11SceneBoundsE[8] = {82, 97, 121, 0, 28, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo7PayloadE[8] = {82, 97, 121, 0, 80, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo16geometric_normalE[8] = {82, 97, 121, 0, 16, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo14shading_normalE[8] = {82, 97, 121, 0, 16, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo3rayE[8] = {82, 97, 121, 0, 36, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo5t_hitE[8] = {82, 97, 121, 0, 4, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo8texcoordE[8] = {82, 97, 121, 0, 8, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo11has_diffuseE[8] = {82, 97, 121, 0, 4, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo16has_transmissionE[8] = {82, 97, 121, 0, 4, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo12has_emissiveE[8] = {82, 97, 121, 0, 4, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo16mesh_instance_idE[8] = {82, 97, 121, 0, 4, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo10blend_modeE[8] = {82, 97, 121, 0, 4, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo10is_twosideE[8] = {82, 97, 121, 0, 4, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo13is_subsurfaceE[8] = {82, 97, 121, 0, 4, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo15mask_clip_valueE[8] = {82, 97, 121, 0, 4, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo25blend_diffuse_with_normalE[8] = {82, 97, 121, 0, 4, 0, 0, 0};
.visible .global .align 8 .u64 _ZN21rti_internal_register20reg_bitness_detectorE;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail0E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail1E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail2E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail3E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail4E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail5E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail6E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail7E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail8E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail9E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail0E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail1E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail2E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail3E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail4E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail5E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail6E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail7E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail8E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail9E;
.visible .global .align 4 .u32 _ZN21rti_internal_register14reg_rayIndex_xE;
.visible .global .align 4 .u32 _ZN21rti_internal_register14reg_rayIndex_yE;
.visible .global .align 4 .u32 _ZN21rti_internal_register14reg_rayIndex_zE;
.visible .global .align 1 .b8 _ZN21rti_internal_typename11debug_inputE[12] = {70, 68, 101, 98, 117, 103, 73, 110, 112, 117, 116, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename11SceneBoundsE[13] = {70, 83, 99, 101, 110, 101, 66, 111, 117, 110, 100, 115, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename7PayloadE[27] = {70, 77, 97, 116, 101, 114, 105, 97, 108, 67, 108, 111, 115, 101, 115, 116, 72, 105, 116, 80, 97, 121, 108, 111, 97, 100, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename16geometric_normalE[7] = {102, 108, 111, 97, 116, 52, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename14shading_normalE[7] = {102, 108, 111, 97, 116, 52, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename3rayE[11] = {111, 112, 116, 105, 120, 58, 58, 82, 97, 121, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename5t_hitE[6] = {102, 108, 111, 97, 116, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename8texcoordE[7] = {102, 108, 111, 97, 116, 50, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename11has_diffuseE[5] = {117, 105, 110, 116, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename16has_transmissionE[5] = {117, 105, 110, 116, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename12has_emissiveE[5] = {117, 105, 110, 116, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename16mesh_instance_idE[5] = {117, 105, 110, 116, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename10blend_modeE[5] = {117, 105, 110, 116, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename10is_twosideE[5] = {117, 105, 110, 116, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename13is_subsurfaceE[5] = {117, 105, 110, 116, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename15mask_clip_valueE[6] = {102, 108, 111, 97, 116, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename25blend_diffuse_with_normalE[5] = {117, 105, 110, 116, 0};
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum11debug_inputE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum11SceneBoundsE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum7PayloadE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum16geometric_normalE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum14shading_normalE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum3rayE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum5t_hitE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum8texcoordE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum11has_diffuseE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum16has_transmissionE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum12has_emissiveE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum16mesh_instance_idE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum10blend_modeE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum10is_twosideE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum13is_subsurfaceE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum15mask_clip_valueE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum25blend_diffuse_with_normalE = 4919;
.visible .global .align 1 .b8 _ZN21rti_internal_semantic11debug_inputE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic11SceneBoundsE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic7PayloadE[10] = {114, 116, 80, 97, 121, 108, 111, 97, 100, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_semantic16geometric_normalE[27] = {97, 116, 116, 114, 105, 98, 117, 116, 101, 32, 103, 101, 111, 109, 101, 116, 114, 105, 99, 95, 110, 111, 114, 109, 97, 108, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_semantic14shading_normalE[25] = {97, 116, 116, 114, 105, 98, 117, 116, 101, 32, 115, 104, 97, 100, 105, 110, 103, 95, 110, 111, 114, 109, 97, 108, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_semantic3rayE[13] = {114, 116, 67, 117, 114, 114, 101, 110, 116, 82, 97, 121, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_semantic5t_hitE[23] = {114, 116, 73, 110, 116, 101, 114, 115, 101, 99, 116, 105, 111, 110, 68, 105, 115, 116, 97, 110, 99, 101, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_semantic8texcoordE[19] = {97, 116, 116, 114, 105, 98, 117, 116, 101, 32, 116, 101, 120, 99, 111, 111, 114, 100, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_semantic11has_diffuseE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic16has_transmissionE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic12has_emissiveE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic16mesh_instance_idE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic10blend_modeE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic10is_twosideE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic13is_subsurfaceE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic15mask_clip_valueE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic25blend_diffuse_with_normalE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation11debug_inputE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation11SceneBoundsE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation7PayloadE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation16geometric_normalE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation14shading_normalE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation3rayE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation5t_hitE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation8texcoordE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation11has_diffuseE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation16has_transmissionE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation12has_emissiveE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation16mesh_instance_idE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation10blend_modeE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation10is_twosideE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation13is_subsurfaceE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation15mask_clip_valueE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation25blend_diffuse_with_normalE[1];

.visible .entry _Z18opacity_closesthitv(

)
{
	.reg .pred 	%p<9>;
	.reg .b16 	%rs<8>;
	.reg .f32 	%f<146>;
	.reg .b32 	%r<8>;
	.reg .b64 	%rd<2>;


	ld.global.v2.f32 	{%f48, %f49}, [texcoord];
	tex.2d.v4.f32.f32	{%f142, %f143, %f144, %f145}, [diffuse_tex, {%f48, %f49}];
	ld.global.v4.f32 	{%f52, %f53, %f54, %f55}, [shading_normal];
	mov.u32 	%r3, 7937;
	mov.f32 	%f47, 0f00000000;
	// inline asm
	call (%f32, %f33, %f34, %f35), _rt_transform_tuple, (%r3, %f52, %f53, %f54, %f47);
	// inline asm
	mul.ftz.f32 	%f56, %f33, %f33;
	fma.rn.ftz.f32 	%f57, %f32, %f32, %f56;
	fma.rn.ftz.f32 	%f58, %f34, %f34, %f57;
	rsqrt.approx.ftz.f32 	%f59, %f58;
	mul.ftz.f32 	%f60, %f32, %f59;
	mul.ftz.f32 	%f61, %f33, %f59;
	mul.ftz.f32 	%f62, %f34, %f59;
	ld.global.v4.f32 	{%f63, %f64, %f65, %f66}, [geometric_normal];
	// inline asm
	call (%f40, %f41, %f42, %f43), _rt_transform_tuple, (%r3, %f63, %f64, %f65, %f47);
	// inline asm
	mul.ftz.f32 	%f67, %f41, %f41;
	fma.rn.ftz.f32 	%f68, %f40, %f40, %f67;
	fma.rn.ftz.f32 	%f69, %f42, %f42, %f68;
	rsqrt.approx.ftz.f32 	%f70, %f69;
	mul.ftz.f32 	%f5, %f40, %f70;
	mul.ftz.f32 	%f6, %f41, %f70;
	mul.ftz.f32 	%f7, %f42, %f70;
	ld.global.f32 	%f141, [ray+12];
	mul.ftz.f32 	%f71, %f141, %f5;
	ld.global.f32 	%f140, [ray+16];
	mul.ftz.f32 	%f72, %f6, %f140;
	neg.ftz.f32 	%f73, %f72;
	sub.ftz.f32 	%f74, %f73, %f71;
	ld.global.f32 	%f139, [ray+20];
	mul.ftz.f32 	%f75, %f7, %f139;
	sub.ftz.f32 	%f76, %f74, %f75;
	mov.b32 	 %r4, %f76;
	and.b32  	%r5, %r4, -2147483648;
	or.b32  	%r6, %r5, 1065353216;
	mov.b32 	 %f77, %r6;
	mul.ftz.f32 	%f11, %f60, %f77;
	mul.ftz.f32 	%f12, %f61, %f77;
	mul.ftz.f32 	%f13, %f62, %f77;
	ld.global.u32 	%r7, [blend_diffuse_with_normal];
	setp.eq.s32	%p1, %r7, 0;
	@%p1 bra 	BB0_2;

	ld.global.v2.f32 	{%f78, %f79}, [texcoord];
	mul.ftz.f32 	%f82, %f79, 0f3F000000;
	tex.2d.v4.f32.f32	{%f83, %f84, %f85, %f86}, [diffuse_tex, {%f78, %f82}];
	ld.global.v2.f32 	{%f87, %f88}, [texcoord];
	fma.rn.ftz.f32 	%f91, %f88, 0f3F000000, 0f3F000000;
	tex.2d.v4.f32.f32	{%f92, %f93, %f94, %f95}, [diffuse_tex, {%f87, %f91}];
	cvt.ftz.sat.f32.f32	%f96, %f7;
	abs.ftz.f32 	%f97, %f96;
	mov.f32 	%f98, 0f3F800000;
	sub.ftz.f32 	%f99, %f98, %f97;
	mul.ftz.f32 	%f100, %f99, 0f3F000000;
	sqrt.approx.ftz.f32 	%f101, %f100;
	setp.gt.ftz.f32	%p2, %f97, 0f3F11EB85;
	selp.f32	%f102, %f101, %f97, %p2;
	mul.ftz.f32 	%f103, %f102, %f102;
	mov.f32 	%f104, 0f3C94D2E9;
	mov.f32 	%f105, 0f3D53F941;
	fma.rn.ftz.f32 	%f106, %f105, %f103, %f104;
	mov.f32 	%f107, 0f3D3F841F;
	fma.rn.ftz.f32 	%f108, %f106, %f103, %f107;
	mov.f32 	%f109, 0f3D994929;
	fma.rn.ftz.f32 	%f110, %f108, %f103, %f109;
	mov.f32 	%f111, 0f3E2AAB94;
	fma.rn.ftz.f32 	%f112, %f110, %f103, %f111;
	mul.ftz.f32 	%f113, %f103, %f112;
	fma.rn.ftz.f32 	%f114, %f113, %f102, %f102;
	add.ftz.f32 	%f115, %f114, %f114;
	mov.f32 	%f116, 0f3FC90FDB;
	sub.ftz.f32 	%f117, %f116, %f114;
	selp.f32	%f118, %f115, %f117, %p2;
	setp.lt.ftz.f32	%p3, %f96, 0f00000000;
	mov.f32 	%f119, 0f40490FDB;
	sub.ftz.f32 	%f120, %f119, %f118;
	selp.f32	%f121, %f120, %f118, %p3;
	add.ftz.f32 	%f122, %f121, %f121;
	div.approx.ftz.f32 	%f123, %f122, %f119;
	sub.ftz.f32 	%f124, %f92, %f83;
	sub.ftz.f32 	%f125, %f93, %f84;
	sub.ftz.f32 	%f126, %f94, %f85;
	sub.ftz.f32 	%f127, %f95, %f86;
	fma.rn.ftz.f32 	%f142, %f124, %f123, %f83;
	fma.rn.ftz.f32 	%f143, %f125, %f123, %f84;
	fma.rn.ftz.f32 	%f144, %f126, %f123, %f85;
	fma.rn.ftz.f32 	%f145, %f127, %f123, %f86;
	ld.global.f32 	%f141, [ray+12];
	ld.global.f32 	%f140, [ray+16];
	ld.global.f32 	%f139, [ray+20];

BB0_2:
	ld.global.f32 	%f28, [t_hit];
	ld.global.f32 	%f128, [ray];
	fma.rn.ftz.f32 	%f29, %f28, %f141, %f128;
	ld.global.f32 	%f129, [ray+4];
	fma.rn.ftz.f32 	%f30, %f28, %f140, %f129;
	ld.global.f32 	%f130, [ray+8];
	fma.rn.ftz.f32 	%f31, %f28, %f139, %f130;
	ld.global.u32 	%r1, [is_twoside];
	mov.u16 	%rs7, 0;
	setp.ne.s32	%p4, %r1, 0;
	@%p4 bra 	BB0_4;

	mul.ftz.f32 	%f131, %f6, %f140;
	fma.rn.ftz.f32 	%f132, %f5, %f141, %f131;
	fma.rn.ftz.f32 	%f133, %f7, %f139, %f132;
	setp.gt.ftz.f32	%p5, %f133, 0f00000000;
	selp.u16	%rs7, 1, 0, %p5;

BB0_4:
	setp.nan.ftz.f32	%p6, %f11, %f12;
	setp.nan.ftz.f32	%p7, %f13, %f13;
	or.pred  	%p8, %p6, %p7;
	st.global.v4.f32 	[Payload], {%f28, %f29, %f30, %f31};
	selp.f32	%f134, 0f00000000, %f12, %p8;
	selp.f32	%f135, 0f00000000, %f11, %p8;
	st.global.v2.f32 	[Payload+16], {%f135, %f134};
	selp.f32	%f136, 0f00000000, %f13, %p8;
	st.global.f32 	[Payload+24], %f136;
	ld.global.u16 	%rs4, [blend_mode];
	ld.global.u16 	%rs5, [mesh_instance_id];
	st.global.v2.u16 	[Payload+28], {%rs5, %rs4};
	st.global.v4.f32 	[Payload+32], {%f142, %f143, %f144, %f145};
	mov.f32 	%f137, 0f3F800000;
	st.global.v4.f32 	[Payload+48], {%f137, %f137, %f137, %f137};
	st.global.v4.f32 	[Payload+64], {%f47, %f47, %f47, %f47};
	cvt.u16.u32	%rs6, %r1;
	st.global.v2.u16 	[Payload+76], {%rs6, %rs7};
	ret;
}

	// .globl	_Z19emission_closesthitv
.visible .entry _Z19emission_closesthitv(

)
{
	.reg .pred 	%p<7>;
	.reg .b16 	%rs<8>;
	.reg .f32 	%f<128>;
	.reg .b32 	%r<8>;
	.reg .b64 	%rd<3>;


	ld.global.v2.f32 	{%f56, %f57}, [texcoord];
	tex.2d.v4.f32.f32	{%f124, %f125, %f126, %f127}, [diffuse_tex, {%f56, %f57}];
	ld.global.v2.f32 	{%f60, %f61}, [texcoord];
	tex.2d.v4.f32.f32	{%f5, %f6, %f7, %f8}, [emissive_tex, {%f60, %f61}];
	ld.global.v4.f32 	{%f64, %f65, %f66, %f67}, [shading_normal];
	mov.u32 	%r3, 7937;
	mov.f32 	%f55, 0f00000000;
	// inline asm
	call (%f40, %f41, %f42, %f43), _rt_transform_tuple, (%r3, %f64, %f65, %f66, %f55);
	// inline asm
	mul.ftz.f32 	%f68, %f41, %f41;
	fma.rn.ftz.f32 	%f69, %f40, %f40, %f68;
	fma.rn.ftz.f32 	%f70, %f42, %f42, %f69;
	rsqrt.approx.ftz.f32 	%f11, %f70;
	mul.ftz.f32 	%f12, %f42, %f11;
	ld.global.v4.f32 	{%f71, %f72, %f73, %f74}, [geometric_normal];
	// inline asm
	call (%f48, %f49, %f50, %f51), _rt_transform_tuple, (%r3, %f71, %f72, %f73, %f55);
	// inline asm
	mul.ftz.f32 	%f75, %f49, %f49;
	fma.rn.ftz.f32 	%f76, %f48, %f48, %f75;
	fma.rn.ftz.f32 	%f77, %f50, %f50, %f76;
	rsqrt.approx.ftz.f32 	%f78, %f77;
	mul.ftz.f32 	%f13, %f48, %f78;
	mul.ftz.f32 	%f14, %f49, %f78;
	mul.ftz.f32 	%f15, %f50, %f78;
	ld.global.f32 	%f16, [ray+12];
	ld.global.f32 	%f17, [ray+16];
	ld.global.f32 	%f18, [ray+20];
	ld.global.u32 	%r4, [blend_diffuse_with_normal];
	setp.eq.s32	%p1, %r4, 0;
	mov.f32 	%f121, %f18;
	mov.f32 	%f122, %f17;
	mov.f32 	%f123, %f16;
	@%p1 bra 	BB1_2;

	ld.global.v2.f32 	{%f79, %f80}, [texcoord];
	mul.ftz.f32 	%f83, %f80, 0f3F000000;
	tex.2d.v4.f32.f32	{%f84, %f85, %f86, %f87}, [diffuse_tex, {%f79, %f83}];
	ld.global.v2.f32 	{%f88, %f89}, [texcoord];
	fma.rn.ftz.f32 	%f92, %f89, 0f3F000000, 0f3F000000;
	tex.2d.v4.f32.f32	{%f93, %f94, %f95, %f96}, [diffuse_tex, {%f88, %f92}];
	sub.ftz.f32 	%f97, %f84, %f93;
	sub.ftz.f32 	%f98, %f85, %f94;
	sub.ftz.f32 	%f99, %f86, %f95;
	sub.ftz.f32 	%f100, %f87, %f96;
	cvt.ftz.sat.f32.f32	%f101, %f12;
	fma.rn.ftz.f32 	%f124, %f101, %f97, %f93;
	fma.rn.ftz.f32 	%f125, %f101, %f98, %f94;
	fma.rn.ftz.f32 	%f126, %f101, %f99, %f95;
	fma.rn.ftz.f32 	%f127, %f101, %f100, %f96;
	ld.global.f32 	%f123, [ray+12];
	ld.global.f32 	%f122, [ray+16];
	ld.global.f32 	%f121, [ray+20];

BB1_2:
	mul.ftz.f32 	%f102, %f15, %f18;
	mul.ftz.f32 	%f103, %f14, %f17;
	neg.ftz.f32 	%f104, %f103;
	mul.ftz.f32 	%f105, %f16, %f13;
	sub.ftz.f32 	%f106, %f104, %f105;
	sub.ftz.f32 	%f107, %f106, %f102;
	mov.b32 	 %r5, %f107;
	and.b32  	%r6, %r5, -2147483648;
	or.b32  	%r7, %r6, 1065353216;
	mov.b32 	 %f108, %r7;
	mul.ftz.f32 	%f109, %f40, %f11;
	mul.ftz.f32 	%f33, %f109, %f108;
	mul.ftz.f32 	%f110, %f41, %f11;
	mul.ftz.f32 	%f34, %f110, %f108;
	mul.ftz.f32 	%f35, %f12, %f108;
	ld.global.f32 	%f36, [t_hit];
	ld.global.f32 	%f111, [ray];
	fma.rn.ftz.f32 	%f37, %f36, %f123, %f111;
	ld.global.f32 	%f112, [ray+4];
	fma.rn.ftz.f32 	%f38, %f36, %f122, %f112;
	ld.global.f32 	%f113, [ray+8];
	fma.rn.ftz.f32 	%f39, %f36, %f121, %f113;
	ld.global.u32 	%r1, [is_twoside];
	mov.u16 	%rs7, 0;
	setp.ne.s32	%p2, %r1, 0;
	@%p2 bra 	BB1_4;

	mul.ftz.f32 	%f114, %f14, %f122;
	fma.rn.ftz.f32 	%f115, %f13, %f123, %f114;
	fma.rn.ftz.f32 	%f116, %f15, %f121, %f115;
	setp.gt.ftz.f32	%p3, %f116, 0f00000000;
	selp.u16	%rs7, 1, 0, %p3;

BB1_4:
	setp.nan.ftz.f32	%p4, %f33, %f34;
	setp.nan.ftz.f32	%p5, %f35, %f35;
	or.pred  	%p6, %p4, %p5;
	st.global.v4.f32 	[Payload], {%f36, %f37, %f38, %f39};
	selp.f32	%f117, 0f00000000, %f34, %p6;
	selp.f32	%f118, 0f00000000, %f33, %p6;
	st.global.v2.f32 	[Payload+16], {%f118, %f117};
	selp.f32	%f119, 0f00000000, %f35, %p6;
	st.global.f32 	[Payload+24], %f119;
	ld.global.u16 	%rs4, [blend_mode];
	ld.global.u16 	%rs5, [mesh_instance_id];
	st.global.v2.u16 	[Payload+28], {%rs5, %rs4};
	st.global.v4.f32 	[Payload+32], {%f124, %f125, %f126, %f127};
	mov.f32 	%f120, 0f3F800000;
	st.global.v4.f32 	[Payload+48], {%f120, %f120, %f120, %f120};
	st.global.v4.f32 	[Payload+64], {%f5, %f6, %f7, %f8};
	cvt.u16.u32	%rs6, %r1;
	st.global.v2.u16 	[Payload+76], {%rs6, %rs7};
	ret;
}

	// .globl	_Z22translucent_closesthitv
.visible .entry _Z22translucent_closesthitv(

)
{
	.reg .pred 	%p<6>;
	.reg .b16 	%rs<8>;
	.reg .f32 	%f<92>;
	.reg .b32 	%r<7>;
	.reg .b64 	%rd<4>;


	ld.global.v2.f32 	{%f40, %f41}, [texcoord];
	tex.2d.v4.f32.f32	{%f1, %f2, %f3, %f4}, [diffuse_tex, {%f40, %f41}];
	ld.global.v2.f32 	{%f44, %f45}, [texcoord];
	tex.2d.v4.f32.f32	{%f5, %f6, %f7, %f8}, [emissive_tex, {%f44, %f45}];
	ld.global.v2.f32 	{%f48, %f49}, [texcoord];
	tex.2d.v4.f32.f32	{%f9, %f10, %f11, %f12}, [transmission_tex, {%f48, %f49}];
	ld.global.v4.f32 	{%f52, %f53, %f54, %f55}, [shading_normal];
	mov.u32 	%r3, 7937;
	mov.f32 	%f39, 0f00000000;
	// inline asm
	call (%f24, %f25, %f26, %f27), _rt_transform_tuple, (%r3, %f52, %f53, %f54, %f39);
	// inline asm
	mul.ftz.f32 	%f56, %f25, %f25;
	fma.rn.ftz.f32 	%f57, %f24, %f24, %f56;
	fma.rn.ftz.f32 	%f58, %f26, %f26, %f57;
	sqrt.approx.ftz.f32 	%f16, %f58;
	ld.global.v4.f32 	{%f59, %f60, %f61, %f62}, [geometric_normal];
	// inline asm
	call (%f32, %f33, %f34, %f35), _rt_transform_tuple, (%r3, %f59, %f60, %f61, %f39);
	// inline asm
	mul.ftz.f32 	%f63, %f33, %f33;
	fma.rn.ftz.f32 	%f64, %f32, %f32, %f63;
	fma.rn.ftz.f32 	%f65, %f34, %f34, %f64;
	rsqrt.approx.ftz.f32 	%f66, %f65;
	mul.ftz.f32 	%f67, %f32, %f66;
	mul.ftz.f32 	%f68, %f33, %f66;
	mul.ftz.f32 	%f69, %f34, %f66;
	ld.global.f32 	%f70, [ray+12];
	mul.ftz.f32 	%f17, %f70, %f67;
	ld.global.f32 	%f71, [ray+16];
	mul.ftz.f32 	%f18, %f68, %f71;
	ld.global.f32 	%f72, [ray+20];
	mul.ftz.f32 	%f19, %f69, %f72;
	ld.global.f32 	%f20, [t_hit];
	ld.global.f32 	%f73, [ray];
	fma.rn.ftz.f32 	%f21, %f20, %f70, %f73;
	ld.global.f32 	%f74, [ray+4];
	fma.rn.ftz.f32 	%f22, %f20, %f71, %f74;
	ld.global.f32 	%f75, [ray+8];
	fma.rn.ftz.f32 	%f23, %f20, %f72, %f75;
	ld.global.u32 	%r1, [is_twoside];
	mov.u16 	%rs7, 0;
	setp.ne.s32	%p1, %r1, 0;
	@%p1 bra 	BB2_2;

	add.ftz.f32 	%f76, %f17, %f18;
	add.ftz.f32 	%f77, %f76, %f19;
	setp.gt.ftz.f32	%p2, %f77, 0f00000000;
	selp.u16	%rs7, 1, 0, %p2;

BB2_2:
	rcp.approx.ftz.f32 	%f78, %f16;
	neg.ftz.f32 	%f79, %f18;
	sub.ftz.f32 	%f80, %f79, %f17;
	sub.ftz.f32 	%f81, %f80, %f19;
	mov.b32 	 %r4, %f81;
	and.b32  	%r5, %r4, -2147483648;
	or.b32  	%r6, %r5, 1065353216;
	mov.b32 	 %f82, %r6;
	mul.ftz.f32 	%f83, %f24, %f78;
	mul.ftz.f32 	%f84, %f83, %f82;
	mul.ftz.f32 	%f85, %f25, %f78;
	mul.ftz.f32 	%f86, %f85, %f82;
	mul.ftz.f32 	%f87, %f26, %f78;
	mul.ftz.f32 	%f88, %f87, %f82;
	setp.nan.ftz.f32	%p3, %f88, %f88;
	setp.nan.ftz.f32	%p4, %f84, %f86;
	or.pred  	%p5, %p4, %p3;
	st.global.v4.f32 	[Payload], {%f20, %f21, %f22, %f23};
	selp.f32	%f89, 0f00000000, %f86, %p5;
	selp.f32	%f90, 0f00000000, %f84, %p5;
	st.global.v2.f32 	[Payload+16], {%f90, %f89};
	selp.f32	%f91, 0f00000000, %f88, %p5;
	st.global.f32 	[Payload+24], %f91;
	ld.global.u16 	%rs4, [blend_mode];
	ld.global.u16 	%rs5, [mesh_instance_id];
	st.global.v2.u16 	[Payload+28], {%rs5, %rs4};
	st.global.v4.f32 	[Payload+32], {%f1, %f2, %f3, %f4};
	st.global.v4.f32 	[Payload+48], {%f9, %f10, %f11, %f12};
	st.global.v4.f32 	[Payload+64], {%f5, %f6, %f7, %f8};
	cvt.u16.u32	%rs6, %r1;
	st.global.v2.u16 	[Payload+76], {%rs6, %rs7};
	ret;
}

	// .globl	_Z15radiance_anyhitv
.visible .entry _Z15radiance_anyhitv(

)
{
	.reg .pred 	%p<2>;
	.reg .b16 	%rs<2>;
	.reg .f32 	%f<9>;
	.reg .b64 	%rd<2>;


	ld.global.u16 	%rs1, [blend_mode];
	st.global.u16 	[Payload+30], %rs1;
	setp.eq.s16	%p1, %rs1, 0;
	@%p1 bra 	BB3_2;

	ld.global.v2.f32 	{%f1, %f2}, [texcoord];
	tex.2d.v4.f32.f32	{%f5, %f6, %f7, %f8}, [transmission_tex, {%f1, %f2}];
	st.global.v4.f32 	[Payload+48], {%f5, %f6, %f7, %f8};
	// inline asm
	call _rt_ignore_intersection, ();
	// inline asm

BB3_2:
	ret;
}

	// .globl	_Z15maskable_anyhitv
.visible .entry _Z15maskable_anyhitv(

)
{
	.reg .pred 	%p<2>;
	.reg .f32 	%f<10>;
	.reg .b64 	%rd<2>;


	ld.global.v2.f32 	{%f1, %f2}, [texcoord];
	tex.2d.v4.f32.f32	{%f5, %f6, %f7, %f8}, [transmission_tex, {%f1, %f2}];
	ld.global.f32 	%f9, [mask_clip_value];
	setp.geu.ftz.f32	%p1, %f5, %f9;
	@%p1 bra 	BB4_2;

	// inline asm
	call _rt_ignore_intersection, ();
	// inline asm

BB4_2:
	ret;
}

	// .globl	_Z18translucent_anyhitv
.visible .entry _Z18translucent_anyhitv(

)
{
	.reg .f32 	%f<9>;
	.reg .b64 	%rd<2>;


	ld.global.v2.f32 	{%f1, %f2}, [texcoord];
	tex.2d.v4.f32.f32	{%f5, %f6, %f7, %f8}, [transmission_tex, {%f1, %f2}];
	st.global.v4.f32 	[Payload+48], {%f5, %f6, %f7, %f8};
	// inline asm
	call _rt_ignore_intersection, ();
	// inline asm
	ret;
}

	// .globl	_Z13shadow_anyhitv
.visible .entry _Z13shadow_anyhitv(

)
{
	.reg .f32 	%f<2>;


	ld.global.f32 	%f1, [t_hit];
	st.global.f32 	[Payload], %f1;
	// inline asm
	call _rt_terminate_ray, ();
	// inline asm
	ret;
}

	// .globl	_Z22maskable_shadow_anyhitv
.visible .entry _Z22maskable_shadow_anyhitv(

)
{
	.reg .pred 	%p<2>;
	.reg .f32 	%f<11>;
	.reg .b64 	%rd<2>;


	ld.global.v2.f32 	{%f1, %f2}, [texcoord];
	tex.2d.v4.f32.f32	{%f5, %f6, %f7, %f8}, [transmission_tex, {%f1, %f2}];
	ld.global.f32 	%f9, [mask_clip_value];
	setp.lt.ftz.f32	%p1, %f5, %f9;
	@%p1 bra 	BB7_2;
	bra.uni 	BB7_1;

BB7_2:
	// inline asm
	call _rt_ignore_intersection, ();
	// inline asm
	bra.uni 	BB7_3;

BB7_1:
	ld.global.f32 	%f10, [t_hit];
	st.global.f32 	[Payload], %f10;
	// inline asm
	call _rt_terminate_ray, ();
	// inline asm

BB7_3:
	ret;
}

	// .globl	_Z25translucent_shadow_anyhitv
.visible .entry _Z25translucent_shadow_anyhitv(

)
{
	.reg .f32 	%f<9>;
	.reg .b64 	%rd<2>;


	ld.global.v2.f32 	{%f1, %f2}, [texcoord];
	tex.2d.v4.f32.f32	{%f5, %f6, %f7, %f8}, [transmission_tex, {%f1, %f2}];
	st.global.v4.f32 	[Payload+48], {%f5, %f6, %f7, %f8};
	// inline asm
	call _rt_ignore_intersection, ();
	// inline asm
	ret;
}

	// .globl	_Z4missv
.visible .entry _Z4missv(

)
{
	.reg .b32 	%r<2>;


	mov.u32 	%r1, -1082130432;
	st.global.u32 	[Payload], %r1;
	ret;
}

	// .globl	_Z11shadow_missv
.visible .entry _Z11shadow_missv(

)
{
	.reg .b32 	%r<2>;


	mov.u32 	%r1, -1082130432;
	st.global.u32 	[Payload], %r1;
	ret;
}


#####//
// Generated by NVIDIA NVVM Compiler
//
// Compiler Build ID: CL-27506705
// Cuda compilation tools, release 10.2, V10.2.89
// Based on LLVM 3.4svn
//

.version 6.5
.target sm_50
.address_size 64

	// .globl	_Z15mesh_attributesv
.visible .global .align 1 .b8 vertex_buffer[1];
.visible .global .align 1 .b8 normal_buffer[1];
.visible .global .align 1 .b8 texcoord_buffer[1];
.visible .global .align 1 .b8 index_buffer[1];
.visible .global .align 1 .b8 material_buffer[1];
.visible .global .align 8 .b8 texcoord[8];
.visible .global .align 16 .b8 geometric_normal[16];
.visible .global .align 16 .b8 shading_normal[16];
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo8texcoordE[8] = {82, 97, 121, 0, 8, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo16geometric_normalE[8] = {82, 97, 121, 0, 16, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo14shading_normalE[8] = {82, 97, 121, 0, 16, 0, 0, 0};
.visible .global .align 8 .u64 _ZN21rti_internal_register20reg_bitness_detectorE;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail0E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail1E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail2E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail3E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail4E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail5E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail6E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail7E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail8E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail9E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail0E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail1E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail2E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail3E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail4E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail5E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail6E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail7E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail8E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail9E;
.visible .global .align 4 .u32 _ZN21rti_internal_register14reg_rayIndex_xE;
.visible .global .align 4 .u32 _ZN21rti_internal_register14reg_rayIndex_yE;
.visible .global .align 4 .u32 _ZN21rti_internal_register14reg_rayIndex_zE;
.visible .global .align 1 .b8 _ZN21rti_internal_typename8texcoordE[7] = {102, 108, 111, 97, 116, 50, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename16geometric_normalE[7] = {102, 108, 111, 97, 116, 52, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename14shading_normalE[7] = {102, 108, 111, 97, 116, 52, 0};
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum8texcoordE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum16geometric_normalE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum14shading_normalE = 4919;
.visible .global .align 1 .b8 _ZN21rti_internal_semantic8texcoordE[19] = {97, 116, 116, 114, 105, 98, 117, 116, 101, 32, 116, 101, 120, 99, 111, 111, 114, 100, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_semantic16geometric_normalE[27] = {97, 116, 116, 114, 105, 98, 117, 116, 101, 32, 103, 101, 111, 109, 101, 116, 114, 105, 99, 95, 110, 111, 114, 109, 97, 108, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_semantic14shading_normalE[25] = {97, 116, 116, 114, 105, 98, 117, 116, 101, 32, 115, 104, 97, 100, 105, 110, 103, 95, 110, 111, 114, 109, 97, 108, 0};
.visible .global .align 1 .b8 _ZN23rti_internal_annotation8texcoordE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation16geometric_normalE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation14shading_normalE[1];

.visible .entry _Z15mesh_attributesv(

)
{
	.reg .f32 	%f<97>;
	.reg .b32 	%r<22>;
	.reg .b64 	%rd<65>;


	// inline asm
	call (%r1), _rt_get_primitive_index, ();
	// inline asm
	cvt.u64.u32	%rd3, %r1;
	mov.u64 	%rd61, index_buffer;
	cvta.global.u64 	%rd2, %rd61;
	mov.u32 	%r20, 1;
	mov.u32 	%r3, 6;
	mov.u64 	%rd60, 0;
	// inline asm
	call (%rd1), _rt_buffer_get_64, (%rd2, %r20, %r3, %rd3, %rd60, %rd60, %rd60);
	// inline asm
	ld.u16 	%rd45, [%rd1];
	mov.u64 	%rd62, vertex_buffer;
	cvta.global.u64 	%rd8, %rd62;
	mov.u32 	%r15, 16;
	ld.u16 	%rd51, [%rd1+2];
	ld.u16 	%rd57, [%rd1+4];
	// inline asm
	call (%rd7), _rt_buffer_get_64, (%rd8, %r20, %r15, %rd45, %rd60, %rd60, %rd60);
	// inline asm
	ld.v4.f32 	{%f3, %f4, %f5, %f6}, [%rd7];
	// inline asm
	call (%rd13), _rt_buffer_get_64, (%rd8, %r20, %r15, %rd51, %rd60, %rd60, %rd60);
	// inline asm
	ld.v4.f32 	{%f10, %f11, %f12, %f13}, [%rd13];
	// inline asm
	call (%rd19), _rt_buffer_get_64, (%rd8, %r20, %r15, %rd57, %rd60, %rd60, %rd60);
	// inline asm
	ld.v4.f32 	{%f17, %f18, %f19, %f20}, [%rd19];
	sub.ftz.f32 	%f24, %f17, %f3;
	sub.ftz.f32 	%f25, %f18, %f4;
	sub.ftz.f32 	%f26, %f19, %f5;
	sub.ftz.f32 	%f27, %f10, %f3;
	sub.ftz.f32 	%f28, %f11, %f4;
	sub.ftz.f32 	%f29, %f12, %f5;
	mul.ftz.f32 	%f30, %f29, %f25;
	mul.ftz.f32 	%f31, %f28, %f26;
	mul.ftz.f32 	%f32, %f27, %f26;
	mul.ftz.f32 	%f33, %f29, %f24;
	mul.ftz.f32 	%f34, %f28, %f24;
	mul.ftz.f32 	%f35, %f27, %f25;
	sub.ftz.f32 	%f36, %f34, %f35;
	sub.ftz.f32 	%f37, %f32, %f33;
	sub.ftz.f32 	%f38, %f30, %f31;
	mov.f32 	%f39, 0f00000000;
	st.global.v4.f32 	[geometric_normal], {%f38, %f37, %f36, %f39};
	// inline asm
	call (%f1, %f2), _rt_get_triangle_barycentrics, ();
	// inline asm
	mov.u64 	%rd63, normal_buffer;
	cvta.global.u64 	%rd26, %rd63;
	// inline asm
	call (%rd25), _rt_buffer_get_64, (%rd26, %r20, %r15, %rd51, %rd60, %rd60, %rd60);
	// inline asm
	ld.v4.f32 	{%f40, %f41, %f42, %f43}, [%rd25];
	// inline asm
	call (%rd31), _rt_buffer_get_64, (%rd26, %r20, %r15, %rd57, %rd60, %rd60, %rd60);
	// inline asm
	ld.v4.f32 	{%f48, %f49, %f50, %f51}, [%rd31];
	mul.ftz.f32 	%f56, %f2, %f48;
	mul.ftz.f32 	%f57, %f2, %f49;
	mul.ftz.f32 	%f58, %f2, %f50;
	mul.ftz.f32 	%f59, %f2, %f51;
	fma.rn.ftz.f32 	%f60, %f1, %f40, %f56;
	fma.rn.ftz.f32 	%f61, %f1, %f41, %f57;
	fma.rn.ftz.f32 	%f62, %f1, %f42, %f58;
	fma.rn.ftz.f32 	%f63, %f1, %f43, %f59;
	// inline asm
	call (%rd37), _rt_buffer_get_64, (%rd26, %r20, %r15, %rd45, %rd60, %rd60, %rd60);
	// inline asm
	mov.f32 	%f64, 0f3F800000;
	sub.ftz.f32 	%f65, %f64, %f1;
	sub.ftz.f32 	%f66, %f65, %f2;
	ld.v4.f32 	{%f67, %f68, %f69, %f70}, [%rd37];
	fma.rn.ftz.f32 	%f75, %f66, %f70, %f63;
	fma.rn.ftz.f32 	%f76, %f66, %f69, %f62;
	fma.rn.ftz.f32 	%f77, %f66, %f68, %f61;
	fma.rn.ftz.f32 	%f78, %f66, %f67, %f60;
	st.global.v4.f32 	[shading_normal], {%f78, %f77, %f76, %f75};
	mov.u64 	%rd64, texcoord_buffer;
	cvta.global.u64 	%rd44, %rd64;
	mov.u32 	%r21, 8;
	// inline asm
	call (%rd43), _rt_buffer_get_64, (%rd44, %r20, %r21, %rd45, %rd60, %rd60, %rd60);
	// inline asm
	ld.v2.f32 	{%f79, %f80}, [%rd43];
	// inline asm
	call (%rd49), _rt_buffer_get_64, (%rd44, %r20, %r21, %rd51, %rd60, %rd60, %rd60);
	// inline asm
	ld.v2.f32 	{%f83, %f84}, [%rd49];
	// inline asm
	call (%rd55), _rt_buffer_get_64, (%rd44, %r20, %r21, %rd57, %rd60, %rd60, %rd60);
	// inline asm
	ld.v2.f32 	{%f87, %f88}, [%rd55];
	mul.ftz.f32 	%f91, %f2, %f87;
	mul.ftz.f32 	%f92, %f2, %f88;
	fma.rn.ftz.f32 	%f93, %f1, %f83, %f91;
	fma.rn.ftz.f32 	%f94, %f1, %f84, %f92;
	fma.rn.ftz.f32 	%f95, %f66, %f80, %f94;
	fma.rn.ftz.f32 	%f96, %f66, %f79, %f93;
	st.global.v2.f32 	[texcoord], {%f96, %f95};
	ret;
}


#####//
// Generated by NVIDIA NVVM Compiler
//
// Compiler Build ID: CL-27506705
// Cuda compilation tools, release 10.2, V10.2.89
// Based on LLVM 3.4svn
//

.version 6.5
.target sm_50
.address_size 64

	// .globl	_Z15mesh_attributesv
.visible .global .align 1 .b8 vertex_buffer[1];
.visible .global .align 1 .b8 normal_buffer[1];
.visible .global .align 1 .b8 texcoord_buffer[1];
.visible .global .align 1 .b8 index_buffer[1];
.visible .global .align 1 .b8 material_buffer[1];
.visible .global .align 8 .b8 texcoord[8];
.visible .global .align 16 .b8 geometric_normal[16];
.visible .global .align 16 .b8 shading_normal[16];
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo8texcoordE[8] = {82, 97, 121, 0, 8, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo16geometric_normalE[8] = {82, 97, 121, 0, 16, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo14shading_normalE[8] = {82, 97, 121, 0, 16, 0, 0, 0};
.visible .global .align 8 .u64 _ZN21rti_internal_register20reg_bitness_detectorE;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail0E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail1E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail2E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail3E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail4E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail5E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail6E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail7E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail8E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail9E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail0E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail1E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail2E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail3E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail4E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail5E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail6E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail7E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail8E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail9E;
.visible .global .align 4 .u32 _ZN21rti_internal_register14reg_rayIndex_xE;
.visible .global .align 4 .u32 _ZN21rti_internal_register14reg_rayIndex_yE;
.visible .global .align 4 .u32 _ZN21rti_internal_register14reg_rayIndex_zE;
.visible .global .align 1 .b8 _ZN21rti_internal_typename8texcoordE[7] = {102, 108, 111, 97, 116, 50, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename16geometric_normalE[7] = {102, 108, 111, 97, 116, 52, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename14shading_normalE[7] = {102, 108, 111, 97, 116, 52, 0};
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum8texcoordE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum16geometric_normalE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum14shading_normalE = 4919;
.visible .global .align 1 .b8 _ZN21rti_internal_semantic8texcoordE[19] = {97, 116, 116, 114, 105, 98, 117, 116, 101, 32, 116, 101, 120, 99, 111, 111, 114, 100, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_semantic16geometric_normalE[27] = {97, 116, 116, 114, 105, 98, 117, 116, 101, 32, 103, 101, 111, 109, 101, 116, 114, 105, 99, 95, 110, 111, 114, 109, 97, 108, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_semantic14shading_normalE[25] = {97, 116, 116, 114, 105, 98, 117, 116, 101, 32, 115, 104, 97, 100, 105, 110, 103, 95, 110, 111, 114, 109, 97, 108, 0};
.visible .global .align 1 .b8 _ZN23rti_internal_annotation8texcoordE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation16geometric_normalE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation14shading_normalE[1];

.visible .entry _Z15mesh_attributesv(

)
{
	.reg .f32 	%f<97>;
	.reg .b32 	%r<22>;
	.reg .b64 	%rd<65>;


	// inline asm
	call (%r1), _rt_get_primitive_index, ();
	// inline asm
	cvt.u64.u32	%rd3, %r1;
	mov.u64 	%rd61, index_buffer;
	cvta.global.u64 	%rd2, %rd61;
	mov.u32 	%r20, 1;
	mov.u32 	%r3, 12;
	mov.u64 	%rd60, 0;
	// inline asm
	call (%rd1), _rt_buffer_get_64, (%rd2, %r20, %r3, %rd3, %rd60, %rd60, %rd60);
	// inline asm
	ld.u32 	%rd45, [%rd1];
	mov.u64 	%rd62, vertex_buffer;
	cvta.global.u64 	%rd8, %rd62;
	mov.u32 	%r15, 16;
	ld.u32 	%rd51, [%rd1+4];
	ld.u32 	%rd57, [%rd1+8];
	// inline asm
	call (%rd7), _rt_buffer_get_64, (%rd8, %r20, %r15, %rd45, %rd60, %rd60, %rd60);
	// inline asm
	ld.v4.f32 	{%f3, %f4, %f5, %f6}, [%rd7];
	// inline asm
	call (%rd13), _rt_buffer_get_64, (%rd8, %r20, %r15, %rd51, %rd60, %rd60, %rd60);
	// inline asm
	ld.v4.f32 	{%f10, %f11, %f12, %f13}, [%rd13];
	// inline asm
	call (%rd19), _rt_buffer_get_64, (%rd8, %r20, %r15, %rd57, %rd60, %rd60, %rd60);
	// inline asm
	ld.v4.f32 	{%f17, %f18, %f19, %f20}, [%rd19];
	sub.ftz.f32 	%f24, %f17, %f3;
	sub.ftz.f32 	%f25, %f18, %f4;
	sub.ftz.f32 	%f26, %f19, %f5;
	sub.ftz.f32 	%f27, %f10, %f3;
	sub.ftz.f32 	%f28, %f11, %f4;
	sub.ftz.f32 	%f29, %f12, %f5;
	mul.ftz.f32 	%f30, %f29, %f25;
	mul.ftz.f32 	%f31, %f28, %f26;
	mul.ftz.f32 	%f32, %f27, %f26;
	mul.ftz.f32 	%f33, %f29, %f24;
	mul.ftz.f32 	%f34, %f28, %f24;
	mul.ftz.f32 	%f35, %f27, %f25;
	sub.ftz.f32 	%f36, %f34, %f35;
	sub.ftz.f32 	%f37, %f32, %f33;
	sub.ftz.f32 	%f38, %f30, %f31;
	mov.f32 	%f39, 0f00000000;
	st.global.v4.f32 	[geometric_normal], {%f38, %f37, %f36, %f39};
	// inline asm
	call (%f1, %f2), _rt_get_triangle_barycentrics, ();
	// inline asm
	mov.u64 	%rd63, normal_buffer;
	cvta.global.u64 	%rd26, %rd63;
	// inline asm
	call (%rd25), _rt_buffer_get_64, (%rd26, %r20, %r15, %rd51, %rd60, %rd60, %rd60);
	// inline asm
	ld.v4.f32 	{%f40, %f41, %f42, %f43}, [%rd25];
	// inline asm
	call (%rd31), _rt_buffer_get_64, (%rd26, %r20, %r15, %rd57, %rd60, %rd60, %rd60);
	// inline asm
	ld.v4.f32 	{%f48, %f49, %f50, %f51}, [%rd31];
	mul.ftz.f32 	%f56, %f2, %f48;
	mul.ftz.f32 	%f57, %f2, %f49;
	mul.ftz.f32 	%f58, %f2, %f50;
	mul.ftz.f32 	%f59, %f2, %f51;
	fma.rn.ftz.f32 	%f60, %f1, %f40, %f56;
	fma.rn.ftz.f32 	%f61, %f1, %f41, %f57;
	fma.rn.ftz.f32 	%f62, %f1, %f42, %f58;
	fma.rn.ftz.f32 	%f63, %f1, %f43, %f59;
	// inline asm
	call (%rd37), _rt_buffer_get_64, (%rd26, %r20, %r15, %rd45, %rd60, %rd60, %rd60);
	// inline asm
	mov.f32 	%f64, 0f3F800000;
	sub.ftz.f32 	%f65, %f64, %f1;
	sub.ftz.f32 	%f66, %f65, %f2;
	ld.v4.f32 	{%f67, %f68, %f69, %f70}, [%rd37];
	fma.rn.ftz.f32 	%f75, %f66, %f70, %f63;
	fma.rn.ftz.f32 	%f76, %f66, %f69, %f62;
	fma.rn.ftz.f32 	%f77, %f66, %f68, %f61;
	fma.rn.ftz.f32 	%f78, %f66, %f67, %f60;
	st.global.v4.f32 	[shading_normal], {%f78, %f77, %f76, %f75};
	mov.u64 	%rd64, texcoord_buffer;
	cvta.global.u64 	%rd44, %rd64;
	mov.u32 	%r21, 8;
	// inline asm
	call (%rd43), _rt_buffer_get_64, (%rd44, %r20, %r21, %rd45, %rd60, %rd60, %rd60);
	// inline asm
	ld.v2.f32 	{%f79, %f80}, [%rd43];
	// inline asm
	call (%rd49), _rt_buffer_get_64, (%rd44, %r20, %r21, %rd51, %rd60, %rd60, %rd60);
	// inline asm
	ld.v2.f32 	{%f83, %f84}, [%rd49];
	// inline asm
	call (%rd55), _rt_buffer_get_64, (%rd44, %r20, %r21, %rd57, %rd60, %rd60, %rd60);
	// inline asm
	ld.v2.f32 	{%f87, %f88}, [%rd55];
	mul.ftz.f32 	%f91, %f2, %f87;
	mul.ftz.f32 	%f92, %f2, %f88;
	fma.rn.ftz.f32 	%f93, %f1, %f83, %f91;
	fma.rn.ftz.f32 	%f94, %f1, %f84, %f92;
	fma.rn.ftz.f32 	%f95, %f66, %f80, %f94;
	fma.rn.ftz.f32 	%f96, %f66, %f79, %f93;
	st.global.v2.f32 	[texcoord], {%f96, %f95};
	ret;
}


#####//
// Generated by NVIDIA NVVM Compiler
//
// Compiler Build ID: CL-27506705
// Cuda compilation tools, release 10.2, V10.2.89
// Based on LLVM 3.4svn
//

.version 6.5
.target sm_50
.address_size 64

	// .globl	_Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf
.visible .global .align 4 .b8 debug_input[12];
.visible .global .align 4 .b8 SceneBounds[28];
.weak .global .align 4 .b8 _ZZ8Prime128jE6Primes[512] = {2, 0, 0, 0, 3, 0, 0, 0, 5, 0, 0, 0, 7, 0, 0, 0, 11, 0, 0, 0, 13, 0, 0, 0, 17, 0, 0, 0, 19, 0, 0, 0, 23, 0, 0, 0, 29, 0, 0, 0, 31, 0, 0, 0, 37, 0, 0, 0, 41, 0, 0, 0, 43, 0, 0, 0, 47, 0, 0, 0, 53, 0, 0, 0, 59, 0, 0, 0, 61, 0, 0, 0, 67, 0, 0, 0, 71, 0, 0, 0, 73, 0, 0, 0, 79, 0, 0, 0, 83, 0, 0, 0, 89, 0, 0, 0, 97, 0, 0, 0, 101, 0, 0, 0, 103, 0, 0, 0, 107, 0, 0, 0, 109, 0, 0, 0, 113, 0, 0, 0, 127, 0, 0, 0, 131, 0, 0, 0, 137, 0, 0, 0, 139, 0, 0, 0, 149, 0, 0, 0, 151, 0, 0, 0, 157, 0, 0, 0, 163, 0, 0, 0, 167, 0, 0, 0, 173, 0, 0, 0, 179, 0, 0, 0, 181, 0, 0, 0, 191, 0, 0, 0, 193, 0, 0, 0, 197, 0, 0, 0, 199, 0, 0, 0, 211, 0, 0, 0, 223, 0, 0, 0, 227, 0, 0, 0, 229, 0, 0, 0, 233, 0, 0, 0, 239, 0, 0, 0, 241, 0, 0, 0, 251, 0, 0, 0, 1, 1, 0, 0, 7, 1, 0, 0, 13, 1, 0, 0, 15, 1, 0, 0, 21, 1, 0, 0, 25, 1, 0, 0, 27, 1, 0, 0, 37, 1, 0, 0, 51, 1, 0, 0, 55, 1, 0, 0, 57, 1, 0, 0, 61, 1, 0, 0, 75, 1, 0, 0, 81, 1, 0, 0, 91, 1, 0, 0, 93, 1, 0, 0, 97, 1, 0, 0, 103, 1, 0, 0, 111, 1, 0, 0, 117, 1, 0, 0, 123, 1, 0, 0, 127, 1, 0, 0, 133, 1, 0, 0, 141, 1, 0, 0, 145, 1, 0, 0, 153, 1, 0, 0, 163, 1, 0, 0, 165, 1, 0, 0, 175, 1, 0, 0, 177, 1, 0, 0, 183, 1, 0, 0, 187, 1, 0, 0, 193, 1, 0, 0, 201, 1, 0, 0, 205, 1, 0, 0, 207, 1, 0, 0, 211, 1, 0, 0, 223, 1, 0, 0, 231, 1, 0, 0, 235, 1, 0, 0, 243, 1, 0, 0, 247, 1, 0, 0, 253, 1, 0, 0, 9, 2, 0, 0, 11, 2, 0, 0, 29, 2, 0, 0, 35, 2, 0, 0, 45, 2, 0, 0, 51, 2, 0, 0, 57, 2, 0, 0, 59, 2, 0, 0, 65, 2, 0, 0, 75, 2, 0, 0, 81, 2, 0, 0, 87, 2, 0, 0, 89, 2, 0, 0, 95, 2, 0, 0, 101, 2, 0, 0, 105, 2, 0, 0, 107, 2, 0, 0, 119, 2, 0, 0, 129, 2, 0, 0, 131, 2, 0, 0, 135, 2, 0, 0, 141, 2, 0, 0, 147, 2, 0, 0, 149, 2, 0, 0, 161, 2, 0, 0, 165, 2, 0, 0, 171, 2, 0, 0, 179, 2, 0, 0, 189, 2, 0, 0, 197, 2, 0, 0, 207, 2, 0, 0};
.weak .global .align 4 .b8 _ZZ8Prime512jE6Primes[2048] = {2, 0, 0, 0, 3, 0, 0, 0, 5, 0, 0, 0, 7, 0, 0, 0, 11, 0, 0, 0, 13, 0, 0, 0, 17, 0, 0, 0, 19, 0, 0, 0, 23, 0, 0, 0, 29, 0, 0, 0, 31, 0, 0, 0, 37, 0, 0, 0, 41, 0, 0, 0, 43, 0, 0, 0, 47, 0, 0, 0, 53, 0, 0, 0, 59, 0, 0, 0, 61, 0, 0, 0, 67, 0, 0, 0, 71, 0, 0, 0, 73, 0, 0, 0, 79, 0, 0, 0, 83, 0, 0, 0, 89, 0, 0, 0, 97, 0, 0, 0, 101, 0, 0, 0, 103, 0, 0, 0, 107, 0, 0, 0, 109, 0, 0, 0, 113, 0, 0, 0, 127, 0, 0, 0, 131, 0, 0, 0, 137, 0, 0, 0, 139, 0, 0, 0, 149, 0, 0, 0, 151, 0, 0, 0, 157, 0, 0, 0, 163, 0, 0, 0, 167, 0, 0, 0, 173, 0, 0, 0, 179, 0, 0, 0, 181, 0, 0, 0, 191, 0, 0, 0, 193, 0, 0, 0, 197, 0, 0, 0, 199, 0, 0, 0, 211, 0, 0, 0, 223, 0, 0, 0, 227, 0, 0, 0, 229, 0, 0, 0, 233, 0, 0, 0, 239, 0, 0, 0, 241, 0, 0, 0, 251, 0, 0, 0, 1, 1, 0, 0, 7, 1, 0, 0, 13, 1, 0, 0, 15, 1, 0, 0, 21, 1, 0, 0, 25, 1, 0, 0, 27, 1, 0, 0, 37, 1, 0, 0, 51, 1, 0, 0, 55, 1, 0, 0, 57, 1, 0, 0, 61, 1, 0, 0, 75, 1, 0, 0, 81, 1, 0, 0, 91, 1, 0, 0, 93, 1, 0, 0, 97, 1, 0, 0, 103, 1, 0, 0, 111, 1, 0, 0, 117, 1, 0, 0, 123, 1, 0, 0, 127, 1, 0, 0, 133, 1, 0, 0, 141, 1, 0, 0, 145, 1, 0, 0, 153, 1, 0, 0, 163, 1, 0, 0, 165, 1, 0, 0, 175, 1, 0, 0, 177, 1, 0, 0, 183, 1, 0, 0, 187, 1, 0, 0, 193, 1, 0, 0, 201, 1, 0, 0, 205, 1, 0, 0, 207, 1, 0, 0, 211, 1, 0, 0, 223, 1, 0, 0, 231, 1, 0, 0, 235, 1, 0, 0, 243, 1, 0, 0, 247, 1, 0, 0, 253, 1, 0, 0, 9, 2, 0, 0, 11, 2, 0, 0, 29, 2, 0, 0, 35, 2, 0, 0, 45, 2, 0, 0, 51, 2, 0, 0, 57, 2, 0, 0, 59, 2, 0, 0, 65, 2, 0, 0, 75, 2, 0, 0, 81, 2, 0, 0, 87, 2, 0, 0, 89, 2, 0, 0, 95, 2, 0, 0, 101, 2, 0, 0, 105, 2, 0, 0, 107, 2, 0, 0, 119, 2, 0, 0, 129, 2, 0, 0, 131, 2, 0, 0, 135, 2, 0, 0, 141, 2, 0, 0, 147, 2, 0, 0, 149, 2, 0, 0, 161, 2, 0, 0, 165, 2, 0, 0, 171, 2, 0, 0, 179, 2, 0, 0, 189, 2, 0, 0, 197, 2, 0, 0, 207, 2, 0, 0, 215, 2, 0, 0, 221, 2, 0, 0, 227, 2, 0, 0, 231, 2, 0, 0, 239, 2, 0, 0, 245, 2, 0, 0, 249, 2, 0, 0, 1, 3, 0, 0, 5, 3, 0, 0, 19, 3, 0, 0, 29, 3, 0, 0, 41, 3, 0, 0, 43, 3, 0, 0, 53, 3, 0, 0, 55, 3, 0, 0, 59, 3, 0, 0, 61, 3, 0, 0, 71, 3, 0, 0, 85, 3, 0, 0, 89, 3, 0, 0, 91, 3, 0, 0, 95, 3, 0, 0, 109, 3, 0, 0, 113, 3, 0, 0, 115, 3, 0, 0, 119, 3, 0, 0, 139, 3, 0, 0, 143, 3, 0, 0, 151, 3, 0, 0, 161, 3, 0, 0, 169, 3, 0, 0, 173, 3, 0, 0, 179, 3, 0, 0, 185, 3, 0, 0, 199, 3, 0, 0, 203, 3, 0, 0, 209, 3, 0, 0, 215, 3, 0, 0, 223, 3, 0, 0, 229, 3, 0, 0, 241, 3, 0, 0, 245, 3, 0, 0, 251, 3, 0, 0, 253, 3, 0, 0, 7, 4, 0, 0, 9, 4, 0, 0, 15, 4, 0, 0, 25, 4, 0, 0, 27, 4, 0, 0, 37, 4, 0, 0, 39, 4, 0, 0, 45, 4, 0, 0, 63, 4, 0, 0, 67, 4, 0, 0, 69, 4, 0, 0, 73, 4, 0, 0, 79, 4, 0, 0, 85, 4, 0, 0, 93, 4, 0, 0, 99, 4, 0, 0, 105, 4, 0, 0, 127, 4, 0, 0, 129, 4, 0, 0, 139, 4, 0, 0, 147, 4, 0, 0, 157, 4, 0, 0, 163, 4, 0, 0, 169, 4, 0, 0, 177, 4, 0, 0, 189, 4, 0, 0, 193, 4, 0, 0, 199, 4, 0, 0, 205, 4, 0, 0, 207, 4, 0, 0, 213, 4, 0, 0, 225, 4, 0, 0, 235, 4, 0, 0, 253, 4, 0, 0, 255, 4, 0, 0, 3, 5, 0, 0, 9, 5, 0, 0, 11, 5, 0, 0, 17, 5, 0, 0, 21, 5, 0, 0, 23, 5, 0, 0, 27, 5, 0, 0, 39, 5, 0, 0, 41, 5, 0, 0, 47, 5, 0, 0, 81, 5, 0, 0, 87, 5, 0, 0, 93, 5, 0, 0, 101, 5, 0, 0, 119, 5, 0, 0, 129, 5, 0, 0, 143, 5, 0, 0, 147, 5, 0, 0, 149, 5, 0, 0, 153, 5, 0, 0, 159, 5, 0, 0, 167, 5, 0, 0, 171, 5, 0, 0, 173, 5, 0, 0, 179, 5, 0, 0, 191, 5, 0, 0, 201, 5, 0, 0, 203, 5, 0, 0, 207, 5, 0, 0, 209, 5, 0, 0, 213, 5, 0, 0, 219, 5, 0, 0, 231, 5, 0, 0, 243, 5, 0, 0, 251, 5, 0, 0, 7, 6, 0, 0, 13, 6, 0, 0, 17, 6, 0, 0, 23, 6, 0, 0, 31, 6, 0, 0, 35, 6, 0, 0, 43, 6, 0, 0, 47, 6, 0, 0, 61, 6, 0, 0, 65, 6, 0, 0, 71, 6, 0, 0, 73, 6, 0, 0, 77, 6, 0, 0, 83, 6, 0, 0, 85, 6, 0, 0, 91, 6, 0, 0, 101, 6, 0, 0, 121, 6, 0, 0, 127, 6, 0, 0, 131, 6, 0, 0, 133, 6, 0, 0, 157, 6, 0, 0, 161, 6, 0, 0, 163, 6, 0, 0, 173, 6, 0, 0, 185, 6, 0, 0, 187, 6, 0, 0, 197, 6, 0, 0, 205, 6, 0, 0, 211, 6, 0, 0, 217, 6, 0, 0, 223, 6, 0, 0, 241, 6, 0, 0, 247, 6, 0, 0, 251, 6, 0, 0, 253, 6, 0, 0, 9, 7, 0, 0, 19, 7, 0, 0, 31, 7, 0, 0, 39, 7, 0, 0, 55, 7, 0, 0, 69, 7, 0, 0, 75, 7, 0, 0, 79, 7, 0, 0, 81, 7, 0, 0, 85, 7, 0, 0, 87, 7, 0, 0, 97, 7, 0, 0, 109, 7, 0, 0, 115, 7, 0, 0, 121, 7, 0, 0, 139, 7, 0, 0, 141, 7, 0, 0, 157, 7, 0, 0, 159, 7, 0, 0, 181, 7, 0, 0, 187, 7, 0, 0, 195, 7, 0, 0, 201, 7, 0, 0, 205, 7, 0, 0, 207, 7, 0, 0, 211, 7, 0, 0, 219, 7, 0, 0, 225, 7, 0, 0, 235, 7, 0, 0, 237, 7, 0, 0, 247, 7, 0, 0, 5, 8, 0, 0, 15, 8, 0, 0, 21, 8, 0, 0, 33, 8, 0, 0, 35, 8, 0, 0, 39, 8, 0, 0, 41, 8, 0, 0, 51, 8, 0, 0, 63, 8, 0, 0, 65, 8, 0, 0, 81, 8, 0, 0, 83, 8, 0, 0, 89, 8, 0, 0, 93, 8, 0, 0, 95, 8, 0, 0, 105, 8, 0, 0, 113, 8, 0, 0, 131, 8, 0, 0, 155, 8, 0, 0, 159, 8, 0, 0, 165, 8, 0, 0, 173, 8, 0, 0, 189, 8, 0, 0, 191, 8, 0, 0, 195, 8, 0, 0, 203, 8, 0, 0, 219, 8, 0, 0, 221, 8, 0, 0, 225, 8, 0, 0, 233, 8, 0, 0, 239, 8, 0, 0, 245, 8, 0, 0, 249, 8, 0, 0, 5, 9, 0, 0, 7, 9, 0, 0, 29, 9, 0, 0, 35, 9, 0, 0, 37, 9, 0, 0, 43, 9, 0, 0, 47, 9, 0, 0, 53, 9, 0, 0, 67, 9, 0, 0, 73, 9, 0, 0, 77, 9, 0, 0, 79, 9, 0, 0, 85, 9, 0, 0, 89, 9, 0, 0, 95, 9, 0, 0, 107, 9, 0, 0, 113, 9, 0, 0, 119, 9, 0, 0, 133, 9, 0, 0, 137, 9, 0, 0, 143, 9, 0, 0, 155, 9, 0, 0, 163, 9, 0, 0, 169, 9, 0, 0, 173, 9, 0, 0, 199, 9, 0, 0, 217, 9, 0, 0, 227, 9, 0, 0, 235, 9, 0, 0, 239, 9, 0, 0, 245, 9, 0, 0, 247, 9, 0, 0, 253, 9, 0, 0, 19, 10, 0, 0, 31, 10, 0, 0, 33, 10, 0, 0, 49, 10, 0, 0, 57, 10, 0, 0, 61, 10, 0, 0, 73, 10, 0, 0, 87, 10, 0, 0, 97, 10, 0, 0, 99, 10, 0, 0, 103, 10, 0, 0, 111, 10, 0, 0, 117, 10, 0, 0, 123, 10, 0, 0, 127, 10, 0, 0, 129, 10, 0, 0, 133, 10, 0, 0, 139, 10, 0, 0, 147, 10, 0, 0, 151, 10, 0, 0, 153, 10, 0, 0, 159, 10, 0, 0, 169, 10, 0, 0, 171, 10, 0, 0, 181, 10, 0, 0, 189, 10, 0, 0, 193, 10, 0, 0, 207, 10, 0, 0, 217, 10, 0, 0, 229, 10, 0, 0, 231, 10, 0, 0, 237, 10, 0, 0, 241, 10, 0, 0, 243, 10, 0, 0, 3, 11, 0, 0, 17, 11, 0, 0, 21, 11, 0, 0, 27, 11, 0, 0, 35, 11, 0, 0, 41, 11, 0, 0, 45, 11, 0, 0, 63, 11, 0, 0, 71, 11, 0, 0, 81, 11, 0, 0, 87, 11, 0, 0, 93, 11, 0, 0, 101, 11, 0, 0, 111, 11, 0, 0, 123, 11, 0, 0, 137, 11, 0, 0, 141, 11, 0, 0, 147, 11, 0, 0, 153, 11, 0, 0, 155, 11, 0, 0, 183, 11, 0, 0, 185, 11, 0, 0, 195, 11, 0, 0, 203, 11, 0, 0, 207, 11, 0, 0, 221, 11, 0, 0, 225, 11, 0, 0, 233, 11, 0, 0, 245, 11, 0, 0, 251, 11, 0, 0, 7, 12, 0, 0, 11, 12, 0, 0, 17, 12, 0, 0, 37, 12, 0, 0, 47, 12, 0, 0, 49, 12, 0, 0, 65, 12, 0, 0, 91, 12, 0, 0, 95, 12, 0, 0, 97, 12, 0, 0, 109, 12, 0, 0, 115, 12, 0, 0, 119, 12, 0, 0, 131, 12, 0, 0, 137, 12, 0, 0, 145, 12, 0, 0, 149, 12, 0, 0, 157, 12, 0, 0, 179, 12, 0, 0, 181, 12, 0, 0, 185, 12, 0, 0, 187, 12, 0, 0, 199, 12, 0, 0, 227, 12, 0, 0, 229, 12, 0, 0, 235, 12, 0, 0, 241, 12, 0, 0, 247, 12, 0, 0, 251, 12, 0, 0, 1, 13, 0, 0, 3, 13, 0, 0, 15, 13, 0, 0, 19, 13, 0, 0, 31, 13, 0, 0, 33, 13, 0, 0, 43, 13, 0, 0, 45, 13, 0, 0, 61, 13, 0, 0, 63, 13, 0, 0, 79, 13, 0, 0, 85, 13, 0, 0, 105, 13, 0, 0, 121, 13, 0, 0, 129, 13, 0, 0, 133, 13, 0, 0, 135, 13, 0, 0, 139, 13, 0, 0, 141, 13, 0, 0, 163, 13, 0, 0, 171, 13, 0, 0, 183, 13, 0, 0, 189, 13, 0, 0, 199, 13, 0, 0, 201, 13, 0, 0, 205, 13, 0, 0, 211, 13, 0, 0, 213, 13, 0, 0, 219, 13, 0, 0, 229, 13, 0, 0, 231, 13, 0, 0, 243, 13, 0, 0, 253, 13, 0, 0, 255, 13, 0, 0, 9, 14, 0, 0, 23, 14, 0, 0, 29, 14, 0, 0, 33, 14, 0, 0, 39, 14, 0, 0, 47, 14, 0, 0, 53, 14, 0, 0, 59, 14, 0, 0, 75, 14, 0, 0, 87, 14, 0, 0};
.visible .global .align 1 .b8 TexelToVertexMap[1];
.visible .global .align 1 .b8 output_buffer[1];
.visible .global .align 1 .b8 color_output_buffer[1];
.visible .global .align 1 .b8 sample_count_buffer[1];
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo11debug_inputE[8] = {82, 97, 121, 0, 12, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo11SceneBoundsE[8] = {82, 97, 121, 0, 28, 0, 0, 0};
.visible .global .align 8 .u64 _ZN21rti_internal_register20reg_bitness_detectorE;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail0E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail1E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail2E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail3E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail4E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail5E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail6E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail7E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail8E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail9E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail0E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail1E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail2E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail3E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail4E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail5E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail6E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail7E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail8E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail9E;
.visible .global .align 4 .u32 _ZN21rti_internal_register14reg_rayIndex_xE;
.visible .global .align 4 .u32 _ZN21rti_internal_register14reg_rayIndex_yE;
.visible .global .align 4 .u32 _ZN21rti_internal_register14reg_rayIndex_zE;
.visible .global .align 1 .b8 _ZN21rti_internal_typename11debug_inputE[12] = {70, 68, 101, 98, 117, 103, 73, 110, 112, 117, 116, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename11SceneBoundsE[13] = {70, 83, 99, 101, 110, 101, 66, 111, 117, 110, 100, 115, 0};
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum11debug_inputE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum11SceneBoundsE = 4919;
.visible .global .align 1 .b8 _ZN21rti_internal_semantic11debug_inputE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic11SceneBoundsE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation11debug_inputE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation11SceneBoundsE[1];

.visible .func _Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf(
	.param .align 4 .b8 _Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_0[12],
	.param .align 4 .b8 _Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_1[12],
	.param .b64 _Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_2,
	.param .b64 _Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_3,
	.param .b64 _Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_4,
	.param .b64 _Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_5
)
{
	.reg .pred 	%p<6>;
	.reg .f32 	%f<69>;
	.reg .b32 	%r<47>;
	.reg .f64 	%fd<7>;
	.reg .b64 	%rd<25>;


	ld.param.u32 	%r1, [_Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_0];
	ld.param.u32 	%r2, [_Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_0+4];
	ld.param.u64 	%rd5, [_Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_2];
	ld.param.u64 	%rd2, [_Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_3];
	ld.param.u64 	%rd3, [_Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_4];
	ld.param.u64 	%rd4, [_Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_5];
	add.s64 	%rd1, %rd5, 4;
	ld.u32 	%r11, [%rd5+4];
	shl.b32 	%r12, %r11, 12;
	add.s32 	%r13, %r11, %r12;
	add.s32 	%r14, %r13, 2127912214;
	shr.u32 	%r15, %r14, 19;
	xor.b32  	%r16, %r14, %r15;
	xor.b32  	%r17, %r16, -949894596;
	shl.b32 	%r18, %r17, 5;
	add.s32 	%r19, %r17, %r18;
	add.s32 	%r20, %r19, 374761393;
	add.s32 	%r21, %r19, -369570787;
	shl.b32 	%r22, %r20, 9;
	xor.b32  	%r23, %r21, %r22;
	shl.b32 	%r24, %r23, 3;
	add.s32 	%r25, %r23, %r24;
	add.s32 	%r26, %r25, -42973499;
	shr.u32 	%r27, %r26, 16;
	xor.b32  	%r28, %r26, %r27;
	xor.b32  	%r29, %r28, -1252372727;
	ld.u32 	%r30, [%rd5+8];
	add.s32 	%r46, %r29, %r30;
	ld.u32 	%r4, [%rd5+16];
	and.b32  	%r31, %r4, 511;
	mul.wide.u32 	%rd6, %r31, 4;
	mov.u64 	%rd7, _ZZ8Prime512jE6Primes;
	add.s64 	%rd8, %rd7, %rd6;
	ld.global.u32 	%r5, [%rd8];
	cvt.rn.f64.u32	%fd1, %r5;
	rcp.rn.f64 	%fd2, %fd1;
	cvt.rn.ftz.f32.f64	%f1, %fd2;
	setp.eq.s32	%p1, %r46, 0;
	mov.f32 	%f16, 0f00000000;
	mov.f32 	%f63, 0f3F800000;
	mov.f32 	%f65, %f16;
	@%p1 bra 	BB0_4;

	mov.u32 	%r45, %r46;
	mov.f32 	%f64, %f16;

BB0_2:
	rem.u32 	%r32, %r45, %r5;
	cvt.rn.f32.u32	%f17, %r32;
	mul.ftz.f32 	%f63, %f1, %f63;
	fma.rn.ftz.f32 	%f64, %f63, %f17, %f64;
	div.u32 	%r45, %r45, %r5;
	setp.ne.s32	%p2, %r45, 0;
	@%p2 bra 	BB0_2;

	mul.ftz.f32 	%f65, %f64, 0f40C90FDB;

BB0_4:
	add.s32 	%r33, %r4, 1;
	st.u32 	[%rd1+12], %r33;
	and.b32  	%r34, %r33, 511;
	mul.wide.u32 	%rd9, %r34, 4;
	add.s64 	%rd11, %rd7, %rd9;
	ld.global.u32 	%r8, [%rd11];
	cvt.rn.f64.u32	%fd3, %r8;
	rcp.rn.f64 	%fd4, %fd3;
	cvt.rn.ftz.f32.f64	%f8, %fd4;
	mov.f32 	%f66, 0f3F800000;
	mov.f32 	%f68, %f16;
	@%p1 bra 	BB0_7;

	mov.f32 	%f68, %f16;

BB0_6:
	rem.u32 	%r35, %r46, %r8;
	cvt.rn.f32.u32	%f21, %r35;
	mul.ftz.f32 	%f66, %f8, %f66;
	fma.rn.ftz.f32 	%f68, %f66, %f21, %f68;
	div.u32 	%r46, %r46, %r8;
	setp.ne.s32	%p4, %r46, 0;
	@%p4 bra 	BB0_6;

BB0_7:
	add.s32 	%r40, %r4, 2;
	mov.u32 	%r38, 2;
	st.u32 	[%rd1+12], %r40;
	ld.u32 	%r41, [%rd2];
	add.s32 	%r42, %r41, 2;
	st.u32 	[%rd2], %r42;
	cvt.u64.u32	%rd21, %r2;
	cvt.u64.u32	%rd20, %r1;
	mov.u64 	%rd24, TexelToVertexMap;
	cvta.global.u64 	%rd13, %rd24;
	mov.u32 	%r39, 72;
	mov.u64 	%rd23, 0;
	// inline asm
	call (%rd12), _rt_buffer_get_64, (%rd13, %r38, %r39, %rd20, %rd21, %rd23, %rd23);
	// inline asm
	ld.f32 	%f22, [%rd12+48];
	ld.f32 	%f23, [%rd12+52];
	ld.f32 	%f24, [%rd12+56];
	sqrt.approx.ftz.f32 	%f25, %f68;
	mul.ftz.f32 	%f26, %f25, %f25;
	mov.f32 	%f27, 0f3F800000;
	sub.ftz.f32 	%f28, %f27, %f26;
	sqrt.approx.ftz.f32 	%f29, %f28;
	cos.approx.ftz.f32 	%f30, %f65;
	mul.ftz.f32 	%f31, %f29, %f30;
	sin.approx.ftz.f32 	%f32, %f65;
	mul.ftz.f32 	%f33, %f29, %f32;
	setp.ltu.ftz.f32	%p5, %f24, 0f00000000;
	selp.f32	%f34, 0fBF800000, 0f3F800000, %p5;
	add.ftz.f32 	%f35, %f24, %f34;
	mov.f32 	%f36, 0fBF800000;
	div.approx.ftz.f32 	%f37, %f36, %f35;
	mul.ftz.f32 	%f38, %f22, %f23;
	mul.ftz.f32 	%f39, %f37, %f38;
	mul.ftz.f32 	%f40, %f34, %f37;
	mul.ftz.f32 	%f41, %f40, %f22;
	fma.rn.ftz.f32 	%f42, %f22, %f41, 0f3F800000;
	mul.ftz.f32 	%f43, %f34, %f39;
	mul.ftz.f32 	%f44, %f34, %f22;
	mul.ftz.f32 	%f45, %f37, %f23;
	fma.rn.ftz.f32 	%f46, %f23, %f45, %f34;
	cvt.ftz.f64.f32	%fd5, %f25;
	mul.f64 	%fd6, %fd5, 0d3FD45F306446F9B4;
	cvt.rn.ftz.f32.f64	%f47, %fd6;
	fma.rn.ftz.f32 	%f48, %f31, %f42, 0f00000000;
	fma.rn.ftz.f32 	%f49, %f33, %f39, %f48;
	fma.rn.ftz.f32 	%f50, %f25, %f22, %f49;
	fma.rn.ftz.f32 	%f51, %f31, %f43, 0f00000000;
	fma.rn.ftz.f32 	%f52, %f33, %f46, %f51;
	fma.rn.ftz.f32 	%f53, %f25, %f23, %f52;
	mul.ftz.f32 	%f54, %f44, %f31;
	sub.ftz.f32 	%f56, %f16, %f54;
	mul.ftz.f32 	%f57, %f23, %f33;
	sub.ftz.f32 	%f58, %f56, %f57;
	fma.rn.ftz.f32 	%f59, %f25, %f24, %f58;
	// inline asm
	call (%rd18), _rt_buffer_get_64, (%rd13, %r38, %r39, %rd20, %rd21, %rd23, %rd23);
	// inline asm
	ld.f32 	%f60, [%rd18];
	ld.f32 	%f61, [%rd18+4];
	ld.f32 	%f62, [%rd18+8];
	st.f32 	[%rd3], %f60;
	st.f32 	[%rd3+4], %f61;
	st.f32 	[%rd3+8], %f62;
	st.f32 	[%rd3+12], %f50;
	st.f32 	[%rd3+16], %f53;
	st.f32 	[%rd3+20], %f59;
	mov.u32 	%r43, 1008981770;
	st.u32 	[%rd3+24], %r43;
	mov.u32 	%r44, 1399379109;
	st.u32 	[%rd3+28], %r44;
	st.f32 	[%rd4], %f47;
	ret;
}

	// .globl	_ZN9TSHVectorILi2EE15SHBasisFunctionERK6float3
.visible .func  (.param .align 16 .b8 func_retval0[16]) _ZN9TSHVectorILi2EE15SHBasisFunctionERK6float3(
	.param .b64 _ZN9TSHVectorILi2EE15SHBasisFunctionERK6float3_param_0
)
{
	.reg .f32 	%f<8>;
	.reg .b64 	%rd<2>;


	ld.param.u64 	%rd1, [_ZN9TSHVectorILi2EE15SHBasisFunctionERK6float3_param_0];
	ld.f32 	%f1, [%rd1+4];
	mul.ftz.f32 	%f2, %f1, 0fBEFA2A2C;
	ld.f32 	%f3, [%rd1+8];
	mul.ftz.f32 	%f4, %f3, 0f3EFA2A2C;
	ld.f32 	%f5, [%rd1];
	mul.ftz.f32 	%f6, %f5, 0fBEFA2A2C;
	mov.f32 	%f7, 0f3E906EC1;
	st.param.f32	[func_retval0+0], %f7;
	st.param.f32	[func_retval0+4], %f2;
	st.param.f32	[func_retval0+8], %f4;
	st.param.f32	[func_retval0+12], %f6;
	ret;
}

	// .globl	_ZN9TSHVectorILi3EE15SHBasisFunctionERK6float3
.visible .func  (.param .align 16 .b8 func_retval0[48]) _ZN9TSHVectorILi3EE15SHBasisFunctionERK6float3(
	.param .b64 _ZN9TSHVectorILi3EE15SHBasisFunctionERK6float3_param_0
)
{
	.reg .f32 	%f<24>;
	.reg .b64 	%rd<2>;


	ld.param.u64 	%rd1, [_ZN9TSHVectorILi3EE15SHBasisFunctionERK6float3_param_0];
	ld.f32 	%f1, [%rd1+4];
	mul.ftz.f32 	%f2, %f1, 0fBEFA2A2C;
	ld.f32 	%f3, [%rd1+8];
	mul.ftz.f32 	%f4, %f3, 0f3EFA2A2C;
	ld.f32 	%f5, [%rd1];
	mul.ftz.f32 	%f6, %f5, 0fBEFA2A2C;
	mul.ftz.f32 	%f7, %f5, %f5;
	mul.ftz.f32 	%f8, %f1, %f1;
	mul.ftz.f32 	%f9, %f3, %f3;
	mul.ftz.f32 	%f10, %f5, 0f3F8BD89D;
	mul.ftz.f32 	%f11, %f10, %f1;
	mul.ftz.f32 	%f12, %f1, 0fBF8BD89D;
	mul.ftz.f32 	%f13, %f12, %f3;
	fma.rn.ftz.f32 	%f14, %f9, 0f40400000, 0fBF800000;
	mul.ftz.f32 	%f15, %f14, 0f3EA17B0F;
	mul.ftz.f32 	%f16, %f5, 0fBF8BD89D;
	mul.ftz.f32 	%f17, %f16, %f3;
	sub.ftz.f32 	%f18, %f7, %f8;
	mul.ftz.f32 	%f19, %f18, 0f3F0BD89D;
	mov.f32 	%f20, 0f3E906EC1;
	st.param.f32	[func_retval0+0], %f20;
	st.param.f32	[func_retval0+4], %f2;
	st.param.f32	[func_retval0+8], %f4;
	st.param.f32	[func_retval0+12], %f6;
	st.param.f32	[func_retval0+16], %f11;
	st.param.f32	[func_retval0+20], %f13;
	st.param.f32	[func_retval0+24], %f15;
	st.param.f32	[func_retval0+28], %f17;
	st.param.f32	[func_retval0+32], %f19;
	ret;
}


#####//
// Generated by NVIDIA NVVM Compiler
//
// Compiler Build ID: CL-27506705
// Cuda compilation tools, release 10.2, V10.2.89
// Based on LLVM 3.4svn
//

.version 6.5
.target sm_50
.address_size 64

	// .globl	_Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf
.visible .global .align 4 .b8 debug_input[12];
.visible .global .align 4 .b8 SceneBounds[28];
.weak .global .align 4 .b8 _ZZ8Prime128jE6Primes[512] = {2, 0, 0, 0, 3, 0, 0, 0, 5, 0, 0, 0, 7, 0, 0, 0, 11, 0, 0, 0, 13, 0, 0, 0, 17, 0, 0, 0, 19, 0, 0, 0, 23, 0, 0, 0, 29, 0, 0, 0, 31, 0, 0, 0, 37, 0, 0, 0, 41, 0, 0, 0, 43, 0, 0, 0, 47, 0, 0, 0, 53, 0, 0, 0, 59, 0, 0, 0, 61, 0, 0, 0, 67, 0, 0, 0, 71, 0, 0, 0, 73, 0, 0, 0, 79, 0, 0, 0, 83, 0, 0, 0, 89, 0, 0, 0, 97, 0, 0, 0, 101, 0, 0, 0, 103, 0, 0, 0, 107, 0, 0, 0, 109, 0, 0, 0, 113, 0, 0, 0, 127, 0, 0, 0, 131, 0, 0, 0, 137, 0, 0, 0, 139, 0, 0, 0, 149, 0, 0, 0, 151, 0, 0, 0, 157, 0, 0, 0, 163, 0, 0, 0, 167, 0, 0, 0, 173, 0, 0, 0, 179, 0, 0, 0, 181, 0, 0, 0, 191, 0, 0, 0, 193, 0, 0, 0, 197, 0, 0, 0, 199, 0, 0, 0, 211, 0, 0, 0, 223, 0, 0, 0, 227, 0, 0, 0, 229, 0, 0, 0, 233, 0, 0, 0, 239, 0, 0, 0, 241, 0, 0, 0, 251, 0, 0, 0, 1, 1, 0, 0, 7, 1, 0, 0, 13, 1, 0, 0, 15, 1, 0, 0, 21, 1, 0, 0, 25, 1, 0, 0, 27, 1, 0, 0, 37, 1, 0, 0, 51, 1, 0, 0, 55, 1, 0, 0, 57, 1, 0, 0, 61, 1, 0, 0, 75, 1, 0, 0, 81, 1, 0, 0, 91, 1, 0, 0, 93, 1, 0, 0, 97, 1, 0, 0, 103, 1, 0, 0, 111, 1, 0, 0, 117, 1, 0, 0, 123, 1, 0, 0, 127, 1, 0, 0, 133, 1, 0, 0, 141, 1, 0, 0, 145, 1, 0, 0, 153, 1, 0, 0, 163, 1, 0, 0, 165, 1, 0, 0, 175, 1, 0, 0, 177, 1, 0, 0, 183, 1, 0, 0, 187, 1, 0, 0, 193, 1, 0, 0, 201, 1, 0, 0, 205, 1, 0, 0, 207, 1, 0, 0, 211, 1, 0, 0, 223, 1, 0, 0, 231, 1, 0, 0, 235, 1, 0, 0, 243, 1, 0, 0, 247, 1, 0, 0, 253, 1, 0, 0, 9, 2, 0, 0, 11, 2, 0, 0, 29, 2, 0, 0, 35, 2, 0, 0, 45, 2, 0, 0, 51, 2, 0, 0, 57, 2, 0, 0, 59, 2, 0, 0, 65, 2, 0, 0, 75, 2, 0, 0, 81, 2, 0, 0, 87, 2, 0, 0, 89, 2, 0, 0, 95, 2, 0, 0, 101, 2, 0, 0, 105, 2, 0, 0, 107, 2, 0, 0, 119, 2, 0, 0, 129, 2, 0, 0, 131, 2, 0, 0, 135, 2, 0, 0, 141, 2, 0, 0, 147, 2, 0, 0, 149, 2, 0, 0, 161, 2, 0, 0, 165, 2, 0, 0, 171, 2, 0, 0, 179, 2, 0, 0, 189, 2, 0, 0, 197, 2, 0, 0, 207, 2, 0, 0};
.weak .global .align 4 .b8 _ZZ8Prime512jE6Primes[2048] = {2, 0, 0, 0, 3, 0, 0, 0, 5, 0, 0, 0, 7, 0, 0, 0, 11, 0, 0, 0, 13, 0, 0, 0, 17, 0, 0, 0, 19, 0, 0, 0, 23, 0, 0, 0, 29, 0, 0, 0, 31, 0, 0, 0, 37, 0, 0, 0, 41, 0, 0, 0, 43, 0, 0, 0, 47, 0, 0, 0, 53, 0, 0, 0, 59, 0, 0, 0, 61, 0, 0, 0, 67, 0, 0, 0, 71, 0, 0, 0, 73, 0, 0, 0, 79, 0, 0, 0, 83, 0, 0, 0, 89, 0, 0, 0, 97, 0, 0, 0, 101, 0, 0, 0, 103, 0, 0, 0, 107, 0, 0, 0, 109, 0, 0, 0, 113, 0, 0, 0, 127, 0, 0, 0, 131, 0, 0, 0, 137, 0, 0, 0, 139, 0, 0, 0, 149, 0, 0, 0, 151, 0, 0, 0, 157, 0, 0, 0, 163, 0, 0, 0, 167, 0, 0, 0, 173, 0, 0, 0, 179, 0, 0, 0, 181, 0, 0, 0, 191, 0, 0, 0, 193, 0, 0, 0, 197, 0, 0, 0, 199, 0, 0, 0, 211, 0, 0, 0, 223, 0, 0, 0, 227, 0, 0, 0, 229, 0, 0, 0, 233, 0, 0, 0, 239, 0, 0, 0, 241, 0, 0, 0, 251, 0, 0, 0, 1, 1, 0, 0, 7, 1, 0, 0, 13, 1, 0, 0, 15, 1, 0, 0, 21, 1, 0, 0, 25, 1, 0, 0, 27, 1, 0, 0, 37, 1, 0, 0, 51, 1, 0, 0, 55, 1, 0, 0, 57, 1, 0, 0, 61, 1, 0, 0, 75, 1, 0, 0, 81, 1, 0, 0, 91, 1, 0, 0, 93, 1, 0, 0, 97, 1, 0, 0, 103, 1, 0, 0, 111, 1, 0, 0, 117, 1, 0, 0, 123, 1, 0, 0, 127, 1, 0, 0, 133, 1, 0, 0, 141, 1, 0, 0, 145, 1, 0, 0, 153, 1, 0, 0, 163, 1, 0, 0, 165, 1, 0, 0, 175, 1, 0, 0, 177, 1, 0, 0, 183, 1, 0, 0, 187, 1, 0, 0, 193, 1, 0, 0, 201, 1, 0, 0, 205, 1, 0, 0, 207, 1, 0, 0, 211, 1, 0, 0, 223, 1, 0, 0, 231, 1, 0, 0, 235, 1, 0, 0, 243, 1, 0, 0, 247, 1, 0, 0, 253, 1, 0, 0, 9, 2, 0, 0, 11, 2, 0, 0, 29, 2, 0, 0, 35, 2, 0, 0, 45, 2, 0, 0, 51, 2, 0, 0, 57, 2, 0, 0, 59, 2, 0, 0, 65, 2, 0, 0, 75, 2, 0, 0, 81, 2, 0, 0, 87, 2, 0, 0, 89, 2, 0, 0, 95, 2, 0, 0, 101, 2, 0, 0, 105, 2, 0, 0, 107, 2, 0, 0, 119, 2, 0, 0, 129, 2, 0, 0, 131, 2, 0, 0, 135, 2, 0, 0, 141, 2, 0, 0, 147, 2, 0, 0, 149, 2, 0, 0, 161, 2, 0, 0, 165, 2, 0, 0, 171, 2, 0, 0, 179, 2, 0, 0, 189, 2, 0, 0, 197, 2, 0, 0, 207, 2, 0, 0, 215, 2, 0, 0, 221, 2, 0, 0, 227, 2, 0, 0, 231, 2, 0, 0, 239, 2, 0, 0, 245, 2, 0, 0, 249, 2, 0, 0, 1, 3, 0, 0, 5, 3, 0, 0, 19, 3, 0, 0, 29, 3, 0, 0, 41, 3, 0, 0, 43, 3, 0, 0, 53, 3, 0, 0, 55, 3, 0, 0, 59, 3, 0, 0, 61, 3, 0, 0, 71, 3, 0, 0, 85, 3, 0, 0, 89, 3, 0, 0, 91, 3, 0, 0, 95, 3, 0, 0, 109, 3, 0, 0, 113, 3, 0, 0, 115, 3, 0, 0, 119, 3, 0, 0, 139, 3, 0, 0, 143, 3, 0, 0, 151, 3, 0, 0, 161, 3, 0, 0, 169, 3, 0, 0, 173, 3, 0, 0, 179, 3, 0, 0, 185, 3, 0, 0, 199, 3, 0, 0, 203, 3, 0, 0, 209, 3, 0, 0, 215, 3, 0, 0, 223, 3, 0, 0, 229, 3, 0, 0, 241, 3, 0, 0, 245, 3, 0, 0, 251, 3, 0, 0, 253, 3, 0, 0, 7, 4, 0, 0, 9, 4, 0, 0, 15, 4, 0, 0, 25, 4, 0, 0, 27, 4, 0, 0, 37, 4, 0, 0, 39, 4, 0, 0, 45, 4, 0, 0, 63, 4, 0, 0, 67, 4, 0, 0, 69, 4, 0, 0, 73, 4, 0, 0, 79, 4, 0, 0, 85, 4, 0, 0, 93, 4, 0, 0, 99, 4, 0, 0, 105, 4, 0, 0, 127, 4, 0, 0, 129, 4, 0, 0, 139, 4, 0, 0, 147, 4, 0, 0, 157, 4, 0, 0, 163, 4, 0, 0, 169, 4, 0, 0, 177, 4, 0, 0, 189, 4, 0, 0, 193, 4, 0, 0, 199, 4, 0, 0, 205, 4, 0, 0, 207, 4, 0, 0, 213, 4, 0, 0, 225, 4, 0, 0, 235, 4, 0, 0, 253, 4, 0, 0, 255, 4, 0, 0, 3, 5, 0, 0, 9, 5, 0, 0, 11, 5, 0, 0, 17, 5, 0, 0, 21, 5, 0, 0, 23, 5, 0, 0, 27, 5, 0, 0, 39, 5, 0, 0, 41, 5, 0, 0, 47, 5, 0, 0, 81, 5, 0, 0, 87, 5, 0, 0, 93, 5, 0, 0, 101, 5, 0, 0, 119, 5, 0, 0, 129, 5, 0, 0, 143, 5, 0, 0, 147, 5, 0, 0, 149, 5, 0, 0, 153, 5, 0, 0, 159, 5, 0, 0, 167, 5, 0, 0, 171, 5, 0, 0, 173, 5, 0, 0, 179, 5, 0, 0, 191, 5, 0, 0, 201, 5, 0, 0, 203, 5, 0, 0, 207, 5, 0, 0, 209, 5, 0, 0, 213, 5, 0, 0, 219, 5, 0, 0, 231, 5, 0, 0, 243, 5, 0, 0, 251, 5, 0, 0, 7, 6, 0, 0, 13, 6, 0, 0, 17, 6, 0, 0, 23, 6, 0, 0, 31, 6, 0, 0, 35, 6, 0, 0, 43, 6, 0, 0, 47, 6, 0, 0, 61, 6, 0, 0, 65, 6, 0, 0, 71, 6, 0, 0, 73, 6, 0, 0, 77, 6, 0, 0, 83, 6, 0, 0, 85, 6, 0, 0, 91, 6, 0, 0, 101, 6, 0, 0, 121, 6, 0, 0, 127, 6, 0, 0, 131, 6, 0, 0, 133, 6, 0, 0, 157, 6, 0, 0, 161, 6, 0, 0, 163, 6, 0, 0, 173, 6, 0, 0, 185, 6, 0, 0, 187, 6, 0, 0, 197, 6, 0, 0, 205, 6, 0, 0, 211, 6, 0, 0, 217, 6, 0, 0, 223, 6, 0, 0, 241, 6, 0, 0, 247, 6, 0, 0, 251, 6, 0, 0, 253, 6, 0, 0, 9, 7, 0, 0, 19, 7, 0, 0, 31, 7, 0, 0, 39, 7, 0, 0, 55, 7, 0, 0, 69, 7, 0, 0, 75, 7, 0, 0, 79, 7, 0, 0, 81, 7, 0, 0, 85, 7, 0, 0, 87, 7, 0, 0, 97, 7, 0, 0, 109, 7, 0, 0, 115, 7, 0, 0, 121, 7, 0, 0, 139, 7, 0, 0, 141, 7, 0, 0, 157, 7, 0, 0, 159, 7, 0, 0, 181, 7, 0, 0, 187, 7, 0, 0, 195, 7, 0, 0, 201, 7, 0, 0, 205, 7, 0, 0, 207, 7, 0, 0, 211, 7, 0, 0, 219, 7, 0, 0, 225, 7, 0, 0, 235, 7, 0, 0, 237, 7, 0, 0, 247, 7, 0, 0, 5, 8, 0, 0, 15, 8, 0, 0, 21, 8, 0, 0, 33, 8, 0, 0, 35, 8, 0, 0, 39, 8, 0, 0, 41, 8, 0, 0, 51, 8, 0, 0, 63, 8, 0, 0, 65, 8, 0, 0, 81, 8, 0, 0, 83, 8, 0, 0, 89, 8, 0, 0, 93, 8, 0, 0, 95, 8, 0, 0, 105, 8, 0, 0, 113, 8, 0, 0, 131, 8, 0, 0, 155, 8, 0, 0, 159, 8, 0, 0, 165, 8, 0, 0, 173, 8, 0, 0, 189, 8, 0, 0, 191, 8, 0, 0, 195, 8, 0, 0, 203, 8, 0, 0, 219, 8, 0, 0, 221, 8, 0, 0, 225, 8, 0, 0, 233, 8, 0, 0, 239, 8, 0, 0, 245, 8, 0, 0, 249, 8, 0, 0, 5, 9, 0, 0, 7, 9, 0, 0, 29, 9, 0, 0, 35, 9, 0, 0, 37, 9, 0, 0, 43, 9, 0, 0, 47, 9, 0, 0, 53, 9, 0, 0, 67, 9, 0, 0, 73, 9, 0, 0, 77, 9, 0, 0, 79, 9, 0, 0, 85, 9, 0, 0, 89, 9, 0, 0, 95, 9, 0, 0, 107, 9, 0, 0, 113, 9, 0, 0, 119, 9, 0, 0, 133, 9, 0, 0, 137, 9, 0, 0, 143, 9, 0, 0, 155, 9, 0, 0, 163, 9, 0, 0, 169, 9, 0, 0, 173, 9, 0, 0, 199, 9, 0, 0, 217, 9, 0, 0, 227, 9, 0, 0, 235, 9, 0, 0, 239, 9, 0, 0, 245, 9, 0, 0, 247, 9, 0, 0, 253, 9, 0, 0, 19, 10, 0, 0, 31, 10, 0, 0, 33, 10, 0, 0, 49, 10, 0, 0, 57, 10, 0, 0, 61, 10, 0, 0, 73, 10, 0, 0, 87, 10, 0, 0, 97, 10, 0, 0, 99, 10, 0, 0, 103, 10, 0, 0, 111, 10, 0, 0, 117, 10, 0, 0, 123, 10, 0, 0, 127, 10, 0, 0, 129, 10, 0, 0, 133, 10, 0, 0, 139, 10, 0, 0, 147, 10, 0, 0, 151, 10, 0, 0, 153, 10, 0, 0, 159, 10, 0, 0, 169, 10, 0, 0, 171, 10, 0, 0, 181, 10, 0, 0, 189, 10, 0, 0, 193, 10, 0, 0, 207, 10, 0, 0, 217, 10, 0, 0, 229, 10, 0, 0, 231, 10, 0, 0, 237, 10, 0, 0, 241, 10, 0, 0, 243, 10, 0, 0, 3, 11, 0, 0, 17, 11, 0, 0, 21, 11, 0, 0, 27, 11, 0, 0, 35, 11, 0, 0, 41, 11, 0, 0, 45, 11, 0, 0, 63, 11, 0, 0, 71, 11, 0, 0, 81, 11, 0, 0, 87, 11, 0, 0, 93, 11, 0, 0, 101, 11, 0, 0, 111, 11, 0, 0, 123, 11, 0, 0, 137, 11, 0, 0, 141, 11, 0, 0, 147, 11, 0, 0, 153, 11, 0, 0, 155, 11, 0, 0, 183, 11, 0, 0, 185, 11, 0, 0, 195, 11, 0, 0, 203, 11, 0, 0, 207, 11, 0, 0, 221, 11, 0, 0, 225, 11, 0, 0, 233, 11, 0, 0, 245, 11, 0, 0, 251, 11, 0, 0, 7, 12, 0, 0, 11, 12, 0, 0, 17, 12, 0, 0, 37, 12, 0, 0, 47, 12, 0, 0, 49, 12, 0, 0, 65, 12, 0, 0, 91, 12, 0, 0, 95, 12, 0, 0, 97, 12, 0, 0, 109, 12, 0, 0, 115, 12, 0, 0, 119, 12, 0, 0, 131, 12, 0, 0, 137, 12, 0, 0, 145, 12, 0, 0, 149, 12, 0, 0, 157, 12, 0, 0, 179, 12, 0, 0, 181, 12, 0, 0, 185, 12, 0, 0, 187, 12, 0, 0, 199, 12, 0, 0, 227, 12, 0, 0, 229, 12, 0, 0, 235, 12, 0, 0, 241, 12, 0, 0, 247, 12, 0, 0, 251, 12, 0, 0, 1, 13, 0, 0, 3, 13, 0, 0, 15, 13, 0, 0, 19, 13, 0, 0, 31, 13, 0, 0, 33, 13, 0, 0, 43, 13, 0, 0, 45, 13, 0, 0, 61, 13, 0, 0, 63, 13, 0, 0, 79, 13, 0, 0, 85, 13, 0, 0, 105, 13, 0, 0, 121, 13, 0, 0, 129, 13, 0, 0, 133, 13, 0, 0, 135, 13, 0, 0, 139, 13, 0, 0, 141, 13, 0, 0, 163, 13, 0, 0, 171, 13, 0, 0, 183, 13, 0, 0, 189, 13, 0, 0, 199, 13, 0, 0, 201, 13, 0, 0, 205, 13, 0, 0, 211, 13, 0, 0, 213, 13, 0, 0, 219, 13, 0, 0, 229, 13, 0, 0, 231, 13, 0, 0, 243, 13, 0, 0, 253, 13, 0, 0, 255, 13, 0, 0, 9, 14, 0, 0, 23, 14, 0, 0, 29, 14, 0, 0, 33, 14, 0, 0, 39, 14, 0, 0, 47, 14, 0, 0, 53, 14, 0, 0, 59, 14, 0, 0, 75, 14, 0, 0, 87, 14, 0, 0};
.visible .global .align 4 .b8 WorldBrickMin[12];
.visible .global .align 4 .b8 WorldChildCellSize[12];
.visible .global .align 1 .b8 output_buffer[1];
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo11debug_inputE[8] = {82, 97, 121, 0, 12, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo11SceneBoundsE[8] = {82, 97, 121, 0, 28, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo13WorldBrickMinE[8] = {82, 97, 121, 0, 12, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo18WorldChildCellSizeE[8] = {82, 97, 121, 0, 12, 0, 0, 0};
.visible .global .align 8 .u64 _ZN21rti_internal_register20reg_bitness_detectorE;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail0E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail1E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail2E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail3E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail4E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail5E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail6E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail7E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail8E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail9E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail0E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail1E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail2E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail3E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail4E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail5E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail6E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail7E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail8E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail9E;
.visible .global .align 4 .u32 _ZN21rti_internal_register14reg_rayIndex_xE;
.visible .global .align 4 .u32 _ZN21rti_internal_register14reg_rayIndex_yE;
.visible .global .align 4 .u32 _ZN21rti_internal_register14reg_rayIndex_zE;
.visible .global .align 1 .b8 _ZN21rti_internal_typename11debug_inputE[12] = {70, 68, 101, 98, 117, 103, 73, 110, 112, 117, 116, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename11SceneBoundsE[13] = {70, 83, 99, 101, 110, 101, 66, 111, 117, 110, 100, 115, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename13WorldBrickMinE[7] = {102, 108, 111, 97, 116, 51, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename18WorldChildCellSizeE[7] = {102, 108, 111, 97, 116, 51, 0};
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum11debug_inputE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum11SceneBoundsE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum13WorldBrickMinE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum18WorldChildCellSizeE = 4919;
.visible .global .align 1 .b8 _ZN21rti_internal_semantic11debug_inputE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic11SceneBoundsE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic13WorldBrickMinE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic18WorldChildCellSizeE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation11debug_inputE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation11SceneBoundsE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation13WorldBrickMinE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation18WorldChildCellSizeE[1];

.visible .func _Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf(
	.param .align 4 .b8 _Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_0[12],
	.param .align 4 .b8 _Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_1[12],
	.param .b64 _Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_2,
	.param .b64 _Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_3,
	.param .b64 _Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_4,
	.param .b64 _Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_5
)
{
	.reg .pred 	%p<5>;
	.reg .f32 	%f<50>;
	.reg .b32 	%r<45>;
	.reg .f64 	%fd<5>;
	.reg .b64 	%rd<12>;


	ld.param.u32 	%r11, [_Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_0+8];
	ld.param.u32 	%r10, [_Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_0+4];
	ld.param.u32 	%r9, [_Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_0];
	ld.param.u64 	%rd5, [_Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_2];
	ld.param.u64 	%rd2, [_Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_3];
	ld.param.u64 	%rd3, [_Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_4];
	ld.param.u64 	%rd4, [_Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_5];
	add.s64 	%rd1, %rd5, 4;
	ld.u32 	%r12, [%rd5+4];
	shl.b32 	%r13, %r12, 12;
	add.s32 	%r14, %r12, %r13;
	add.s32 	%r15, %r14, 2127912214;
	shr.u32 	%r16, %r15, 19;
	xor.b32  	%r17, %r15, %r16;
	xor.b32  	%r18, %r17, -949894596;
	shl.b32 	%r19, %r18, 5;
	add.s32 	%r20, %r18, %r19;
	add.s32 	%r21, %r20, 374761393;
	add.s32 	%r22, %r20, -369570787;
	shl.b32 	%r23, %r21, 9;
	xor.b32  	%r24, %r22, %r23;
	shl.b32 	%r25, %r24, 3;
	add.s32 	%r26, %r24, %r25;
	add.s32 	%r27, %r26, -42973499;
	shr.u32 	%r28, %r27, 16;
	xor.b32  	%r29, %r27, %r28;
	xor.b32  	%r30, %r29, -1252372727;
	ld.u32 	%r31, [%rd5+8];
	add.s32 	%r44, %r30, %r31;
	ld.u32 	%r2, [%rd5+16];
	and.b32  	%r32, %r2, 511;
	mul.wide.u32 	%rd6, %r32, 4;
	mov.u64 	%rd7, _ZZ8Prime512jE6Primes;
	add.s64 	%rd8, %rd7, %rd6;
	ld.global.u32 	%r3, [%rd8];
	cvt.rn.f64.u32	%fd1, %r3;
	rcp.rn.f64 	%fd2, %fd1;
	cvt.rn.ftz.f32.f64	%f1, %fd2;
	setp.eq.s32	%p1, %r44, 0;
	mov.f32 	%f48, 0f00000000;
	mov.f32 	%f44, 0f3F800000;
	mov.f32 	%f46, %f48;
	@%p1 bra 	BB0_4;

	mov.u32 	%r43, %r44;
	mov.f32 	%f45, %f48;

BB0_2:
	rem.u32 	%r33, %r43, %r3;
	cvt.rn.f32.u32	%f18, %r33;
	mul.ftz.f32 	%f44, %f1, %f44;
	fma.rn.ftz.f32 	%f45, %f44, %f18, %f45;
	div.u32 	%r43, %r43, %r3;
	setp.ne.s32	%p2, %r43, 0;
	@%p2 bra 	BB0_2;

	mul.ftz.f32 	%f46, %f45, 0f40C90FDB;

BB0_4:
	add.s32 	%r34, %r2, 1;
	st.u32 	[%rd1+12], %r34;
	and.b32  	%r35, %r34, 511;
	mul.wide.u32 	%rd9, %r35, 4;
	add.s64 	%rd11, %rd7, %rd9;
	ld.global.u32 	%r6, [%rd11];
	cvt.rn.f64.u32	%fd3, %r6;
	rcp.rn.f64 	%fd4, %fd3;
	cvt.rn.ftz.f32.f64	%f8, %fd4;
	mov.f32 	%f47, 0f3F800000;
	@%p1 bra 	BB0_7;

BB0_5:
	rem.u32 	%r36, %r44, %r6;
	cvt.rn.f32.u32	%f22, %r36;
	mul.ftz.f32 	%f47, %f8, %f47;
	fma.rn.ftz.f32 	%f48, %f47, %f22, %f48;
	div.u32 	%r44, %r44, %r6;
	setp.ne.s32	%p4, %r44, 0;
	@%p4 bra 	BB0_5;

	add.ftz.f32 	%f48, %f48, %f48;

BB0_7:
	add.s32 	%r37, %r2, 2;
	st.u32 	[%rd1+12], %r37;
	ld.u32 	%r38, [%rd2];
	add.s32 	%r39, %r38, 2;
	st.u32 	[%rd2], %r39;
	mov.f32 	%f23, 0f3F800000;
	sub.ftz.f32 	%f24, %f23, %f48;
	mul.ftz.f32 	%f25, %f24, %f24;
	sub.ftz.f32 	%f26, %f23, %f25;
	sqrt.approx.ftz.f32 	%f27, %f26;
	cos.approx.ftz.f32 	%f28, %f46;
	mul.ftz.f32 	%f29, %f27, %f28;
	sin.approx.ftz.f32 	%f30, %f46;
	mul.ftz.f32 	%f31, %f27, %f30;
	ld.global.f32 	%f32, [WorldChildCellSize];
	cvt.rn.f32.u32	%f33, %r9;
	ld.global.f32 	%f34, [WorldChildCellSize+4];
	cvt.rn.f32.u32	%f35, %r10;
	ld.global.f32 	%f36, [WorldChildCellSize+8];
	cvt.rn.f32.u32	%f37, %r11;
	ld.global.f32 	%f38, [WorldBrickMin];
	fma.rn.ftz.f32 	%f39, %f33, %f32, %f38;
	ld.global.f32 	%f40, [WorldBrickMin+4];
	fma.rn.ftz.f32 	%f41, %f35, %f34, %f40;
	ld.global.f32 	%f42, [WorldBrickMin+8];
	fma.rn.ftz.f32 	%f43, %f37, %f36, %f42;
	st.f32 	[%rd3], %f39;
	st.f32 	[%rd3+4], %f41;
	st.f32 	[%rd3+8], %f43;
	st.f32 	[%rd3+12], %f29;
	st.f32 	[%rd3+16], %f31;
	st.f32 	[%rd3+20], %f24;
	mov.u32 	%r40, 1008981770;
	st.u32 	[%rd3+24], %r40;
	mov.u32 	%r41, 1399379109;
	st.u32 	[%rd3+28], %r41;
	mov.u32 	%r42, 1034090883;
	st.u32 	[%rd4], %r42;
	ret;
}

	// .globl	_ZN9TSHVectorILi2EE15SHBasisFunctionERK6float3
.visible .func  (.param .align 16 .b8 func_retval0[16]) _ZN9TSHVectorILi2EE15SHBasisFunctionERK6float3(
	.param .b64 _ZN9TSHVectorILi2EE15SHBasisFunctionERK6float3_param_0
)
{
	.reg .f32 	%f<8>;
	.reg .b64 	%rd<2>;


	ld.param.u64 	%rd1, [_ZN9TSHVectorILi2EE15SHBasisFunctionERK6float3_param_0];
	ld.f32 	%f1, [%rd1+4];
	mul.ftz.f32 	%f2, %f1, 0fBEFA2A2C;
	ld.f32 	%f3, [%rd1+8];
	mul.ftz.f32 	%f4, %f3, 0f3EFA2A2C;
	ld.f32 	%f5, [%rd1];
	mul.ftz.f32 	%f6, %f5, 0fBEFA2A2C;
	mov.f32 	%f7, 0f3E906EC1;
	st.param.f32	[func_retval0+0], %f7;
	st.param.f32	[func_retval0+4], %f2;
	st.param.f32	[func_retval0+8], %f4;
	st.param.f32	[func_retval0+12], %f6;
	ret;
}

	// .globl	_ZN9TSHVectorILi3EE15SHBasisFunctionERK6float3
.visible .func  (.param .align 16 .b8 func_retval0[48]) _ZN9TSHVectorILi3EE15SHBasisFunctionERK6float3(
	.param .b64 _ZN9TSHVectorILi3EE15SHBasisFunctionERK6float3_param_0
)
{
	.reg .f32 	%f<24>;
	.reg .b64 	%rd<2>;


	ld.param.u64 	%rd1, [_ZN9TSHVectorILi3EE15SHBasisFunctionERK6float3_param_0];
	ld.f32 	%f1, [%rd1+4];
	mul.ftz.f32 	%f2, %f1, 0fBEFA2A2C;
	ld.f32 	%f3, [%rd1+8];
	mul.ftz.f32 	%f4, %f3, 0f3EFA2A2C;
	ld.f32 	%f5, [%rd1];
	mul.ftz.f32 	%f6, %f5, 0fBEFA2A2C;
	mul.ftz.f32 	%f7, %f5, %f5;
	mul.ftz.f32 	%f8, %f1, %f1;
	mul.ftz.f32 	%f9, %f3, %f3;
	mul.ftz.f32 	%f10, %f5, 0f3F8BD89D;
	mul.ftz.f32 	%f11, %f10, %f1;
	mul.ftz.f32 	%f12, %f1, 0fBF8BD89D;
	mul.ftz.f32 	%f13, %f12, %f3;
	fma.rn.ftz.f32 	%f14, %f9, 0f40400000, 0fBF800000;
	mul.ftz.f32 	%f15, %f14, 0f3EA17B0F;
	mul.ftz.f32 	%f16, %f5, 0fBF8BD89D;
	mul.ftz.f32 	%f17, %f16, %f3;
	sub.ftz.f32 	%f18, %f7, %f8;
	mul.ftz.f32 	%f19, %f18, 0f3F0BD89D;
	mov.f32 	%f20, 0f3E906EC1;
	st.param.f32	[func_retval0+0], %f20;
	st.param.f32	[func_retval0+4], %f2;
	st.param.f32	[func_retval0+8], %f4;
	st.param.f32	[func_retval0+12], %f6;
	st.param.f32	[func_retval0+16], %f11;
	st.param.f32	[func_retval0+20], %f13;
	st.param.f32	[func_retval0+24], %f15;
	st.param.f32	[func_retval0+28], %f17;
	st.param.f32	[func_retval0+32], %f19;
	ret;
}


#####//
// Generated by NVIDIA NVVM Compiler
//
// Compiler Build ID: CL-27506705
// Cuda compilation tools, release 10.2, V10.2.89
// Based on LLVM 3.4svn
//

.version 6.5
.target sm_50
.address_size 64

	// .globl	_Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf
.visible .global .align 4 .b8 debug_input[12];
.visible .global .align 4 .b8 SceneBounds[28];
.weak .global .align 4 .b8 _ZZ8Prime128jE6Primes[512] = {2, 0, 0, 0, 3, 0, 0, 0, 5, 0, 0, 0, 7, 0, 0, 0, 11, 0, 0, 0, 13, 0, 0, 0, 17, 0, 0, 0, 19, 0, 0, 0, 23, 0, 0, 0, 29, 0, 0, 0, 31, 0, 0, 0, 37, 0, 0, 0, 41, 0, 0, 0, 43, 0, 0, 0, 47, 0, 0, 0, 53, 0, 0, 0, 59, 0, 0, 0, 61, 0, 0, 0, 67, 0, 0, 0, 71, 0, 0, 0, 73, 0, 0, 0, 79, 0, 0, 0, 83, 0, 0, 0, 89, 0, 0, 0, 97, 0, 0, 0, 101, 0, 0, 0, 103, 0, 0, 0, 107, 0, 0, 0, 109, 0, 0, 0, 113, 0, 0, 0, 127, 0, 0, 0, 131, 0, 0, 0, 137, 0, 0, 0, 139, 0, 0, 0, 149, 0, 0, 0, 151, 0, 0, 0, 157, 0, 0, 0, 163, 0, 0, 0, 167, 0, 0, 0, 173, 0, 0, 0, 179, 0, 0, 0, 181, 0, 0, 0, 191, 0, 0, 0, 193, 0, 0, 0, 197, 0, 0, 0, 199, 0, 0, 0, 211, 0, 0, 0, 223, 0, 0, 0, 227, 0, 0, 0, 229, 0, 0, 0, 233, 0, 0, 0, 239, 0, 0, 0, 241, 0, 0, 0, 251, 0, 0, 0, 1, 1, 0, 0, 7, 1, 0, 0, 13, 1, 0, 0, 15, 1, 0, 0, 21, 1, 0, 0, 25, 1, 0, 0, 27, 1, 0, 0, 37, 1, 0, 0, 51, 1, 0, 0, 55, 1, 0, 0, 57, 1, 0, 0, 61, 1, 0, 0, 75, 1, 0, 0, 81, 1, 0, 0, 91, 1, 0, 0, 93, 1, 0, 0, 97, 1, 0, 0, 103, 1, 0, 0, 111, 1, 0, 0, 117, 1, 0, 0, 123, 1, 0, 0, 127, 1, 0, 0, 133, 1, 0, 0, 141, 1, 0, 0, 145, 1, 0, 0, 153, 1, 0, 0, 163, 1, 0, 0, 165, 1, 0, 0, 175, 1, 0, 0, 177, 1, 0, 0, 183, 1, 0, 0, 187, 1, 0, 0, 193, 1, 0, 0, 201, 1, 0, 0, 205, 1, 0, 0, 207, 1, 0, 0, 211, 1, 0, 0, 223, 1, 0, 0, 231, 1, 0, 0, 235, 1, 0, 0, 243, 1, 0, 0, 247, 1, 0, 0, 253, 1, 0, 0, 9, 2, 0, 0, 11, 2, 0, 0, 29, 2, 0, 0, 35, 2, 0, 0, 45, 2, 0, 0, 51, 2, 0, 0, 57, 2, 0, 0, 59, 2, 0, 0, 65, 2, 0, 0, 75, 2, 0, 0, 81, 2, 0, 0, 87, 2, 0, 0, 89, 2, 0, 0, 95, 2, 0, 0, 101, 2, 0, 0, 105, 2, 0, 0, 107, 2, 0, 0, 119, 2, 0, 0, 129, 2, 0, 0, 131, 2, 0, 0, 135, 2, 0, 0, 141, 2, 0, 0, 147, 2, 0, 0, 149, 2, 0, 0, 161, 2, 0, 0, 165, 2, 0, 0, 171, 2, 0, 0, 179, 2, 0, 0, 189, 2, 0, 0, 197, 2, 0, 0, 207, 2, 0, 0};
.weak .global .align 4 .b8 _ZZ8Prime512jE6Primes[2048] = {2, 0, 0, 0, 3, 0, 0, 0, 5, 0, 0, 0, 7, 0, 0, 0, 11, 0, 0, 0, 13, 0, 0, 0, 17, 0, 0, 0, 19, 0, 0, 0, 23, 0, 0, 0, 29, 0, 0, 0, 31, 0, 0, 0, 37, 0, 0, 0, 41, 0, 0, 0, 43, 0, 0, 0, 47, 0, 0, 0, 53, 0, 0, 0, 59, 0, 0, 0, 61, 0, 0, 0, 67, 0, 0, 0, 71, 0, 0, 0, 73, 0, 0, 0, 79, 0, 0, 0, 83, 0, 0, 0, 89, 0, 0, 0, 97, 0, 0, 0, 101, 0, 0, 0, 103, 0, 0, 0, 107, 0, 0, 0, 109, 0, 0, 0, 113, 0, 0, 0, 127, 0, 0, 0, 131, 0, 0, 0, 137, 0, 0, 0, 139, 0, 0, 0, 149, 0, 0, 0, 151, 0, 0, 0, 157, 0, 0, 0, 163, 0, 0, 0, 167, 0, 0, 0, 173, 0, 0, 0, 179, 0, 0, 0, 181, 0, 0, 0, 191, 0, 0, 0, 193, 0, 0, 0, 197, 0, 0, 0, 199, 0, 0, 0, 211, 0, 0, 0, 223, 0, 0, 0, 227, 0, 0, 0, 229, 0, 0, 0, 233, 0, 0, 0, 239, 0, 0, 0, 241, 0, 0, 0, 251, 0, 0, 0, 1, 1, 0, 0, 7, 1, 0, 0, 13, 1, 0, 0, 15, 1, 0, 0, 21, 1, 0, 0, 25, 1, 0, 0, 27, 1, 0, 0, 37, 1, 0, 0, 51, 1, 0, 0, 55, 1, 0, 0, 57, 1, 0, 0, 61, 1, 0, 0, 75, 1, 0, 0, 81, 1, 0, 0, 91, 1, 0, 0, 93, 1, 0, 0, 97, 1, 0, 0, 103, 1, 0, 0, 111, 1, 0, 0, 117, 1, 0, 0, 123, 1, 0, 0, 127, 1, 0, 0, 133, 1, 0, 0, 141, 1, 0, 0, 145, 1, 0, 0, 153, 1, 0, 0, 163, 1, 0, 0, 165, 1, 0, 0, 175, 1, 0, 0, 177, 1, 0, 0, 183, 1, 0, 0, 187, 1, 0, 0, 193, 1, 0, 0, 201, 1, 0, 0, 205, 1, 0, 0, 207, 1, 0, 0, 211, 1, 0, 0, 223, 1, 0, 0, 231, 1, 0, 0, 235, 1, 0, 0, 243, 1, 0, 0, 247, 1, 0, 0, 253, 1, 0, 0, 9, 2, 0, 0, 11, 2, 0, 0, 29, 2, 0, 0, 35, 2, 0, 0, 45, 2, 0, 0, 51, 2, 0, 0, 57, 2, 0, 0, 59, 2, 0, 0, 65, 2, 0, 0, 75, 2, 0, 0, 81, 2, 0, 0, 87, 2, 0, 0, 89, 2, 0, 0, 95, 2, 0, 0, 101, 2, 0, 0, 105, 2, 0, 0, 107, 2, 0, 0, 119, 2, 0, 0, 129, 2, 0, 0, 131, 2, 0, 0, 135, 2, 0, 0, 141, 2, 0, 0, 147, 2, 0, 0, 149, 2, 0, 0, 161, 2, 0, 0, 165, 2, 0, 0, 171, 2, 0, 0, 179, 2, 0, 0, 189, 2, 0, 0, 197, 2, 0, 0, 207, 2, 0, 0, 215, 2, 0, 0, 221, 2, 0, 0, 227, 2, 0, 0, 231, 2, 0, 0, 239, 2, 0, 0, 245, 2, 0, 0, 249, 2, 0, 0, 1, 3, 0, 0, 5, 3, 0, 0, 19, 3, 0, 0, 29, 3, 0, 0, 41, 3, 0, 0, 43, 3, 0, 0, 53, 3, 0, 0, 55, 3, 0, 0, 59, 3, 0, 0, 61, 3, 0, 0, 71, 3, 0, 0, 85, 3, 0, 0, 89, 3, 0, 0, 91, 3, 0, 0, 95, 3, 0, 0, 109, 3, 0, 0, 113, 3, 0, 0, 115, 3, 0, 0, 119, 3, 0, 0, 139, 3, 0, 0, 143, 3, 0, 0, 151, 3, 0, 0, 161, 3, 0, 0, 169, 3, 0, 0, 173, 3, 0, 0, 179, 3, 0, 0, 185, 3, 0, 0, 199, 3, 0, 0, 203, 3, 0, 0, 209, 3, 0, 0, 215, 3, 0, 0, 223, 3, 0, 0, 229, 3, 0, 0, 241, 3, 0, 0, 245, 3, 0, 0, 251, 3, 0, 0, 253, 3, 0, 0, 7, 4, 0, 0, 9, 4, 0, 0, 15, 4, 0, 0, 25, 4, 0, 0, 27, 4, 0, 0, 37, 4, 0, 0, 39, 4, 0, 0, 45, 4, 0, 0, 63, 4, 0, 0, 67, 4, 0, 0, 69, 4, 0, 0, 73, 4, 0, 0, 79, 4, 0, 0, 85, 4, 0, 0, 93, 4, 0, 0, 99, 4, 0, 0, 105, 4, 0, 0, 127, 4, 0, 0, 129, 4, 0, 0, 139, 4, 0, 0, 147, 4, 0, 0, 157, 4, 0, 0, 163, 4, 0, 0, 169, 4, 0, 0, 177, 4, 0, 0, 189, 4, 0, 0, 193, 4, 0, 0, 199, 4, 0, 0, 205, 4, 0, 0, 207, 4, 0, 0, 213, 4, 0, 0, 225, 4, 0, 0, 235, 4, 0, 0, 253, 4, 0, 0, 255, 4, 0, 0, 3, 5, 0, 0, 9, 5, 0, 0, 11, 5, 0, 0, 17, 5, 0, 0, 21, 5, 0, 0, 23, 5, 0, 0, 27, 5, 0, 0, 39, 5, 0, 0, 41, 5, 0, 0, 47, 5, 0, 0, 81, 5, 0, 0, 87, 5, 0, 0, 93, 5, 0, 0, 101, 5, 0, 0, 119, 5, 0, 0, 129, 5, 0, 0, 143, 5, 0, 0, 147, 5, 0, 0, 149, 5, 0, 0, 153, 5, 0, 0, 159, 5, 0, 0, 167, 5, 0, 0, 171, 5, 0, 0, 173, 5, 0, 0, 179, 5, 0, 0, 191, 5, 0, 0, 201, 5, 0, 0, 203, 5, 0, 0, 207, 5, 0, 0, 209, 5, 0, 0, 213, 5, 0, 0, 219, 5, 0, 0, 231, 5, 0, 0, 243, 5, 0, 0, 251, 5, 0, 0, 7, 6, 0, 0, 13, 6, 0, 0, 17, 6, 0, 0, 23, 6, 0, 0, 31, 6, 0, 0, 35, 6, 0, 0, 43, 6, 0, 0, 47, 6, 0, 0, 61, 6, 0, 0, 65, 6, 0, 0, 71, 6, 0, 0, 73, 6, 0, 0, 77, 6, 0, 0, 83, 6, 0, 0, 85, 6, 0, 0, 91, 6, 0, 0, 101, 6, 0, 0, 121, 6, 0, 0, 127, 6, 0, 0, 131, 6, 0, 0, 133, 6, 0, 0, 157, 6, 0, 0, 161, 6, 0, 0, 163, 6, 0, 0, 173, 6, 0, 0, 185, 6, 0, 0, 187, 6, 0, 0, 197, 6, 0, 0, 205, 6, 0, 0, 211, 6, 0, 0, 217, 6, 0, 0, 223, 6, 0, 0, 241, 6, 0, 0, 247, 6, 0, 0, 251, 6, 0, 0, 253, 6, 0, 0, 9, 7, 0, 0, 19, 7, 0, 0, 31, 7, 0, 0, 39, 7, 0, 0, 55, 7, 0, 0, 69, 7, 0, 0, 75, 7, 0, 0, 79, 7, 0, 0, 81, 7, 0, 0, 85, 7, 0, 0, 87, 7, 0, 0, 97, 7, 0, 0, 109, 7, 0, 0, 115, 7, 0, 0, 121, 7, 0, 0, 139, 7, 0, 0, 141, 7, 0, 0, 157, 7, 0, 0, 159, 7, 0, 0, 181, 7, 0, 0, 187, 7, 0, 0, 195, 7, 0, 0, 201, 7, 0, 0, 205, 7, 0, 0, 207, 7, 0, 0, 211, 7, 0, 0, 219, 7, 0, 0, 225, 7, 0, 0, 235, 7, 0, 0, 237, 7, 0, 0, 247, 7, 0, 0, 5, 8, 0, 0, 15, 8, 0, 0, 21, 8, 0, 0, 33, 8, 0, 0, 35, 8, 0, 0, 39, 8, 0, 0, 41, 8, 0, 0, 51, 8, 0, 0, 63, 8, 0, 0, 65, 8, 0, 0, 81, 8, 0, 0, 83, 8, 0, 0, 89, 8, 0, 0, 93, 8, 0, 0, 95, 8, 0, 0, 105, 8, 0, 0, 113, 8, 0, 0, 131, 8, 0, 0, 155, 8, 0, 0, 159, 8, 0, 0, 165, 8, 0, 0, 173, 8, 0, 0, 189, 8, 0, 0, 191, 8, 0, 0, 195, 8, 0, 0, 203, 8, 0, 0, 219, 8, 0, 0, 221, 8, 0, 0, 225, 8, 0, 0, 233, 8, 0, 0, 239, 8, 0, 0, 245, 8, 0, 0, 249, 8, 0, 0, 5, 9, 0, 0, 7, 9, 0, 0, 29, 9, 0, 0, 35, 9, 0, 0, 37, 9, 0, 0, 43, 9, 0, 0, 47, 9, 0, 0, 53, 9, 0, 0, 67, 9, 0, 0, 73, 9, 0, 0, 77, 9, 0, 0, 79, 9, 0, 0, 85, 9, 0, 0, 89, 9, 0, 0, 95, 9, 0, 0, 107, 9, 0, 0, 113, 9, 0, 0, 119, 9, 0, 0, 133, 9, 0, 0, 137, 9, 0, 0, 143, 9, 0, 0, 155, 9, 0, 0, 163, 9, 0, 0, 169, 9, 0, 0, 173, 9, 0, 0, 199, 9, 0, 0, 217, 9, 0, 0, 227, 9, 0, 0, 235, 9, 0, 0, 239, 9, 0, 0, 245, 9, 0, 0, 247, 9, 0, 0, 253, 9, 0, 0, 19, 10, 0, 0, 31, 10, 0, 0, 33, 10, 0, 0, 49, 10, 0, 0, 57, 10, 0, 0, 61, 10, 0, 0, 73, 10, 0, 0, 87, 10, 0, 0, 97, 10, 0, 0, 99, 10, 0, 0, 103, 10, 0, 0, 111, 10, 0, 0, 117, 10, 0, 0, 123, 10, 0, 0, 127, 10, 0, 0, 129, 10, 0, 0, 133, 10, 0, 0, 139, 10, 0, 0, 147, 10, 0, 0, 151, 10, 0, 0, 153, 10, 0, 0, 159, 10, 0, 0, 169, 10, 0, 0, 171, 10, 0, 0, 181, 10, 0, 0, 189, 10, 0, 0, 193, 10, 0, 0, 207, 10, 0, 0, 217, 10, 0, 0, 229, 10, 0, 0, 231, 10, 0, 0, 237, 10, 0, 0, 241, 10, 0, 0, 243, 10, 0, 0, 3, 11, 0, 0, 17, 11, 0, 0, 21, 11, 0, 0, 27, 11, 0, 0, 35, 11, 0, 0, 41, 11, 0, 0, 45, 11, 0, 0, 63, 11, 0, 0, 71, 11, 0, 0, 81, 11, 0, 0, 87, 11, 0, 0, 93, 11, 0, 0, 101, 11, 0, 0, 111, 11, 0, 0, 123, 11, 0, 0, 137, 11, 0, 0, 141, 11, 0, 0, 147, 11, 0, 0, 153, 11, 0, 0, 155, 11, 0, 0, 183, 11, 0, 0, 185, 11, 0, 0, 195, 11, 0, 0, 203, 11, 0, 0, 207, 11, 0, 0, 221, 11, 0, 0, 225, 11, 0, 0, 233, 11, 0, 0, 245, 11, 0, 0, 251, 11, 0, 0, 7, 12, 0, 0, 11, 12, 0, 0, 17, 12, 0, 0, 37, 12, 0, 0, 47, 12, 0, 0, 49, 12, 0, 0, 65, 12, 0, 0, 91, 12, 0, 0, 95, 12, 0, 0, 97, 12, 0, 0, 109, 12, 0, 0, 115, 12, 0, 0, 119, 12, 0, 0, 131, 12, 0, 0, 137, 12, 0, 0, 145, 12, 0, 0, 149, 12, 0, 0, 157, 12, 0, 0, 179, 12, 0, 0, 181, 12, 0, 0, 185, 12, 0, 0, 187, 12, 0, 0, 199, 12, 0, 0, 227, 12, 0, 0, 229, 12, 0, 0, 235, 12, 0, 0, 241, 12, 0, 0, 247, 12, 0, 0, 251, 12, 0, 0, 1, 13, 0, 0, 3, 13, 0, 0, 15, 13, 0, 0, 19, 13, 0, 0, 31, 13, 0, 0, 33, 13, 0, 0, 43, 13, 0, 0, 45, 13, 0, 0, 61, 13, 0, 0, 63, 13, 0, 0, 79, 13, 0, 0, 85, 13, 0, 0, 105, 13, 0, 0, 121, 13, 0, 0, 129, 13, 0, 0, 133, 13, 0, 0, 135, 13, 0, 0, 139, 13, 0, 0, 141, 13, 0, 0, 163, 13, 0, 0, 171, 13, 0, 0, 183, 13, 0, 0, 189, 13, 0, 0, 199, 13, 0, 0, 201, 13, 0, 0, 205, 13, 0, 0, 211, 13, 0, 0, 213, 13, 0, 0, 219, 13, 0, 0, 229, 13, 0, 0, 231, 13, 0, 0, 243, 13, 0, 0, 253, 13, 0, 0, 255, 13, 0, 0, 9, 14, 0, 0, 23, 14, 0, 0, 29, 14, 0, 0, 33, 14, 0, 0, 39, 14, 0, 0, 47, 14, 0, 0, 53, 14, 0, 0, 59, 14, 0, 0, 75, 14, 0, 0, 87, 14, 0, 0};
.visible .global .align 4 .b8 eye[12];
.visible .global .align 4 .b8 U[12];
.visible .global .align 4 .b8 V[12];
.visible .global .align 4 .b8 W[12];
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo11debug_inputE[8] = {82, 97, 121, 0, 12, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo11SceneBoundsE[8] = {82, 97, 121, 0, 28, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo3eyeE[8] = {82, 97, 121, 0, 12, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo1UE[8] = {82, 97, 121, 0, 12, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo1VE[8] = {82, 97, 121, 0, 12, 0, 0, 0};
.visible .global .align 4 .b8 _ZN21rti_internal_typeinfo1WE[8] = {82, 97, 121, 0, 12, 0, 0, 0};
.visible .global .align 8 .u64 _ZN21rti_internal_register20reg_bitness_detectorE;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail0E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail1E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail2E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail3E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail4E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail5E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail6E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail7E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail8E;
.visible .global .align 8 .u64 _ZN21rti_internal_register24reg_exception_64_detail9E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail0E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail1E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail2E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail3E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail4E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail5E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail6E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail7E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail8E;
.visible .global .align 4 .u32 _ZN21rti_internal_register21reg_exception_detail9E;
.visible .global .align 4 .u32 _ZN21rti_internal_register14reg_rayIndex_xE;
.visible .global .align 4 .u32 _ZN21rti_internal_register14reg_rayIndex_yE;
.visible .global .align 4 .u32 _ZN21rti_internal_register14reg_rayIndex_zE;
.visible .global .align 1 .b8 _ZN21rti_internal_typename11debug_inputE[12] = {70, 68, 101, 98, 117, 103, 73, 110, 112, 117, 116, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename11SceneBoundsE[13] = {70, 83, 99, 101, 110, 101, 66, 111, 117, 110, 100, 115, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename3eyeE[7] = {102, 108, 111, 97, 116, 51, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename1UE[7] = {102, 108, 111, 97, 116, 51, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename1VE[7] = {102, 108, 111, 97, 116, 51, 0};
.visible .global .align 1 .b8 _ZN21rti_internal_typename1WE[7] = {102, 108, 111, 97, 116, 51, 0};
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum11debug_inputE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum11SceneBoundsE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum3eyeE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum1UE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum1VE = 4919;
.visible .global .align 4 .u32 _ZN21rti_internal_typeenum1WE = 4919;
.visible .global .align 1 .b8 _ZN21rti_internal_semantic11debug_inputE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic11SceneBoundsE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic3eyeE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic1UE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic1VE[1];
.visible .global .align 1 .b8 _ZN21rti_internal_semantic1WE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation11debug_inputE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation11SceneBoundsE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation3eyeE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation1UE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation1VE[1];
.visible .global .align 1 .b8 _ZN23rti_internal_annotation1WE[1];

.visible .func _Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf(
	.param .align 4 .b8 _Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_0[12],
	.param .align 4 .b8 _Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_1[12],
	.param .b64 _Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_2,
	.param .b64 _Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_3,
	.param .b64 _Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_4,
	.param .b64 _Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_5
)
{
	.reg .pred 	%p<5>;
	.reg .f32 	%f<70>;
	.reg .b32 	%r<46>;
	.reg .f64 	%fd<5>;
	.reg .b64 	%rd<12>;


	ld.param.u32 	%r1, [_Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_0];
	ld.param.u32 	%r2, [_Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_0+4];
	ld.param.u32 	%r3, [_Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_1];
	ld.param.u32 	%r4, [_Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_1+4];
	ld.param.u64 	%rd5, [_Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_2];
	ld.param.u64 	%rd2, [_Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_3];
	ld.param.u64 	%rd3, [_Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_4];
	ld.param.u64 	%rd4, [_Z15primaryray_main5uint3S_R14RandomSequenceRjR7RayDescRf_param_5];
	add.s64 	%rd1, %rd5, 4;
	ld.u32 	%r13, [%rd5+4];
	shl.b32 	%r14, %r13, 12;
	add.s32 	%r15, %r13, %r14;
	add.s32 	%r16, %r15, 2127912214;
	shr.u32 	%r17, %r16, 19;
	xor.b32  	%r18, %r16, %r17;
	xor.b32  	%r19, %r18, -949894596;
	shl.b32 	%r20, %r19, 5;
	add.s32 	%r21, %r19, %r20;
	add.s32 	%r22, %r21, 374761393;
	add.s32 	%r23, %r21, -369570787;
	shl.b32 	%r24, %r22, 9;
	xor.b32  	%r25, %r23, %r24;
	shl.b32 	%r26, %r25, 3;
	add.s32 	%r27, %r25, %r26;
	add.s32 	%r28, %r27, -42973499;
	shr.u32 	%r29, %r28, 16;
	xor.b32  	%r30, %r28, %r29;
	xor.b32  	%r31, %r30, -1252372727;
	ld.u32 	%r32, [%rd5+8];
	add.s32 	%r45, %r31, %r32;
	ld.u32 	%r6, [%rd5+16];
	and.b32  	%r33, %r6, 511;
	mul.wide.u32 	%rd6, %r33, 4;
	mov.u64 	%rd7, _ZZ8Prime512jE6Primes;
	add.s64 	%rd8, %rd7, %rd6;
	ld.global.u32 	%r7, [%rd8];
	cvt.rn.f64.u32	%fd1, %r7;
	rcp.rn.f64 	%fd2, %fd1;
	cvt.rn.ftz.f32.f64	%f1, %fd2;
	setp.eq.s32	%p1, %r45, 0;
	mov.f32 	%f69, 0f00000000;
	mov.f32 	%f64, 0f3F800000;
	mov.f32 	%f66, %f69;
	@%p1 bra 	BB0_3;

	mov.u32 	%r44, %r45;
	mov.f32 	%f66, %f69;

BB0_2:
	rem.u32 	%r34, %r44, %r7;
	cvt.rn.f32.u32	%f16, %r34;
	mul.ftz.f32 	%f64, %f1, %f64;
	fma.rn.ftz.f32 	%f66, %f64, %f16, %f66;
	div.u32 	%r44, %r44, %r7;
	setp.ne.s32	%p2, %r44, 0;
	@%p2 bra 	BB0_2;

BB0_3:
	add.s32 	%r35, %r6, 1;
	st.u32 	[%rd1+12], %r35;
	and.b32  	%r36, %r35, 511;
	mul.wide.u32 	%rd9, %r36, 4;
	add.s64 	%rd11, %rd7, %rd9;
	ld.global.u32 	%r10, [%rd11];
	cvt.rn.f64.u32	%fd3, %r10;
	rcp.rn.f64 	%fd4, %fd3;
	cvt.rn.ftz.f32.f64	%f7, %fd4;
	mov.f32 	%f67, 0f3F800000;
	@%p1 bra 	BB0_5;

BB0_4:
	rem.u32 	%r37, %r45, %r10;
	cvt.rn.f32.u32	%f20, %r37;
	mul.ftz.f32 	%f67, %f7, %f67;
	fma.rn.ftz.f32 	%f69, %f67, %f20, %f69;
	div.u32 	%r45, %r45, %r10;
	setp.ne.s32	%p4, %r45, 0;
	@%p4 bra 	BB0_4;

BB0_5:
	add.s32 	%r38, %r6, 2;
	st.u32 	[%rd1+12], %r38;
	ld.u32 	%r39, [%rd2];
	add.s32 	%r40, %r39, 2;
	st.u32 	[%rd2], %r40;
	cvt.rn.f32.u32	%f21, %r3;
	rcp.approx.ftz.f32 	%f22, %f21;
	cvt.rn.f32.u32	%f23, %r4;
	rcp.approx.ftz.f32 	%f24, %f23;
	add.ftz.f32 	%f25, %f22, %f22;
	add.ftz.f32 	%f26, %f24, %f24;
	cvt.rn.f32.u32	%f27, %r1;
	cvt.rn.f32.u32	%f28, %r2;
	fma.rn.ftz.f32 	%f29, %f27, %f25, 0fBF800000;
	fma.rn.ftz.f32 	%f30, %f28, %f26, 0fBF800000;
	mov.f32 	%f31, 0f3F800000;
	sub.ftz.f32 	%f32, %f31, %f66;
	sub.ftz.f32 	%f33, %f31, %f69;
	fma.rn.ftz.f32 	%f34, %f32, %f25, %f29;
	fma.rn.ftz.f32 	%f35, %f33, %f26, %f30;
	ld.global.f32 	%f36, [eye];
	ld.global.f32 	%f37, [eye+4];
	ld.global.f32 	%f38, [eye+8];
	st.f32 	[%rd3], %f36;
	st.f32 	[%rd3+4], %f37;
	st.f32 	[%rd3+8], %f38;
	ld.global.f32 	%f39, [U];
	ld.global.f32 	%f40, [U+4];
	ld.global.f32 	%f41, [U+8];
	ld.global.f32 	%f42, [V];
	mul.ftz.f32 	%f43, %f35, %f42;
	ld.global.f32 	%f44, [V+4];
	mul.ftz.f32 	%f45, %f35, %f44;
	ld.global.f32 	%f46, [V+8];
	mul.ftz.f32 	%f47, %f35, %f46;
	fma.rn.ftz.f32 	%f48, %f39, %f34, %f43;
	fma.rn.ftz.f32 	%f49, %f40, %f34, %f45;
	fma.rn.ftz.f32 	%f50, %f34, %f41, %f47;
	ld.global.f32 	%f51, [W];
	add.ftz.f32 	%f52, %f48, %f51;
	ld.global.f32 	%f53, [W+4];
	add.ftz.f32 	%f54, %f49, %f53;
	ld.global.f32 	%f55, [W+8];
	add.ftz.f32 	%f56, %f50, %f55;
	mul.ftz.f32 	%f57, %f54, %f54;
	fma.rn.ftz.f32 	%f58, %f52, %f52, %f57;
	fma.rn.ftz.f32 	%f59, %f56, %f56, %f58;
	rsqrt.approx.ftz.f32 	%f60, %f59;
	mul.ftz.f32 	%f61, %f52, %f60;
	mul.ftz.f32 	%f62, %f54, %f60;
	mul.ftz.f32 	%f63, %f56, %f60;
	st.f32 	[%rd3+12], %f61;
	st.f32 	[%rd3+16], %f62;
	st.f32 	[%rd3+20], %f63;
	mov.u32 	%r41, 1008981770;
	st.u32 	[%rd3+24], %r41;
	mov.u32 	%r42, 1399379109;
	st.u32 	[%rd3+28], %r42;
	mov.u32 	%r43, 1065353216;
	st.u32 	[%rd4], %r43;
	ret;
}


#####