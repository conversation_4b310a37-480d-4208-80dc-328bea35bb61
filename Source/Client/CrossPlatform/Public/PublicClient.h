#pragma once

#include <optional>
#include "Runtime/Input/InputManager.h"
#include "Runtime/Interface/CrossEngine.h"

#if CROSSENGINE_WIN
#include "Client/CrossPlatform/Windows/WindowsApplication.h"
#elif CROSSENGINE_ANDROID

#elif CROSSENGINE_IOS

#elif CROSSENGINE_OSX

#endif

#include "Runtime/Input/Core/Public/PlatformInterface.h"
#include "Runtime/Input/Core/Public/SlateApplication.h"

namespace cross
{
    struct Rect
    {
    public:
        int Bottom;
        int Top;
        int Left;
        int Right;
        FullScreenMode Mode;
    };
    class PublicClient
    {
        using Callback = std::function<void()>;
       
    public:
        PublicClient();
        ~PublicClient();

    public:
        bool Initialize();

        bool InitWindow(void * hwnd);

        bool InitWorld();
        
        void SetScreenWidth(float screenWidth);
        float GetScreenWidth();
        
        void SetScreenHeight(float screenHeight);
        float GetScreenHeight();
        
        void SetScreenScale(float screenScale);
        float GetScreenScale();
        
        void SetView(void *view);
        void *GetView() const;
        
        void SetContext(void *context);
        void *GetContext();
        
        void SetEnginePath(const char* engineHome);
        const char* GetEnginePath() const;
        
        void SetAssetPath(const char* assetPath);
        const char* GetAssetPath() const;
        
#if CROSSENGINE_IOS || CROSSENGINE_ANDROID
        void SetDocumentPath(const char* documentPath);
        const char* GetDocumentPath() const;
#endif

        void SetLaunchScene(const char* scene);
        const char* GetLaunchScene() const;

        void SetFramePerSecond(int value);
        void Update();
        void OnShutDown();

#if CROSSENGINE_ANDROID
        void ReCreateSurface(NativeWindow nativeWindow);
#endif
        void ResizeRenderWindow(int width, int height);
        void FlushRendering();

        void OnTickBegin(Callback func);
        void OnTickEnd(Callback func);


        ICrossEngine* GetEngine() { return mCrossEngine.get(); }
        
        void SetWindowTopAndLeft(uint32_t inTop, uint32_t inLeft)
        {
            mWindowTop  = inTop;
            mWindowLeft = inLeft;
        }
        bool mWindowChanged{false};
        Rect mNewRECT;
        std::vector<Rect> mMonitorInfos;
    private:
        using CameraControllerPtr = std::unique_ptr<CameraController>;

        CrossEnginePtr      mCrossEngine{ nullptr };
        IRenderWindow* mRenderWindow{ nullptr };

        void* mView{ nullptr };
        float mScreenWidth{ 0 };
        float mScreenHeight{ 0 };
        float mScreenScale{ 0 };
        void* mContext{ nullptr };
        std::string mEngineHome;
        std::string mAssetPath;
        std::string mLaunchScene;
#if CROSSENGINE_IOS || CROSSENGINE_ANDROID
        std::string         mDocumentPath;
#endif

        Callback mTickBeginHook;
        Callback mTickEndHook;
        
        uint32_t mWindowTop  = 0;
        uint32_t mWindowLeft = 0;

        // Real device ptr
        std::shared_ptr<PlatformApplication> mPlatformApplication;
    };
}
