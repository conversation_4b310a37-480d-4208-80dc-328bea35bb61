// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_SHADERMAPS_CROSSSCHEMA_H_
#define FLATBUFFERS_GENERATED_SHADERMAPS_CROSSSCHEMA_H_

#include "flatbuffers/flatbuffers.h"

#include "CrossSchemaForward.h"
#include "BasicStruct_generated.h"

namespace CrossSchema {

struct ShaderModuleMap;
struct ShaderModuleMapBuilder;
struct ShaderModuleMapT;

struct ShaderSourceMap;
struct ShaderSourceMapBuilder;
struct ShaderSourceMapT;

struct ShaderMaps;
struct ShaderMapsBuilder;
struct ShaderMapsT;

inline const flatbuffers::TypeTable *ShaderModuleMapTypeTable();

inline const flatbuffers::TypeTable *ShaderSourceMapTypeTable();

inline const flatbuffers::TypeTable *ShaderMapsTypeTable();

struct ShaderModuleMapT : public flatbuffers::NativeTable {
  typedef ShaderModuleMap TableType;
  uint64_t guid = 0;
  std::string subname{};
  int8_t diridx = 0;
  uint64_t size = 0;
  uint64_t mtime = 0;
  uint64_t hash = 0;
  std::vector<CrossSchema::GUID> shaders{};
  std::string fullname{};
};

struct ShaderModuleMap FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef ShaderModuleMapT NativeTableType;
  typedef ShaderModuleMapBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_GUID = 4,
    VT_SUBNAME = 6,
    VT_DIRIDX = 8,
    VT_SIZE = 10,
    VT_MTIME = 12,
    VT_HASH = 14,
    VT_SHADERS = 16,
    VT_FULLNAME = 18
  };
CROSS_SCHEMA_API  uint64_t guid() const;
  CROSS_SCHEMA_API  bool mutate_guid(uint64_t _guid);
CROSS_SCHEMA_API  const flatbuffers::String *subname() const;
  CROSS_SCHEMA_API  flatbuffers::String *mutable_subname();
CROSS_SCHEMA_API  int8_t diridx() const;
  CROSS_SCHEMA_API  bool mutate_diridx(int8_t _diridx);
CROSS_SCHEMA_API  uint64_t size() const;
  CROSS_SCHEMA_API  bool mutate_size(uint64_t _size);
CROSS_SCHEMA_API  uint64_t mtime() const;
  CROSS_SCHEMA_API  bool mutate_mtime(uint64_t _mtime);
CROSS_SCHEMA_API  uint64_t hash() const;
  CROSS_SCHEMA_API  bool mutate_hash(uint64_t _hash);
CROSS_SCHEMA_API  const flatbuffers::Vector<const CrossSchema::GUID *> *shaders() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<const CrossSchema::GUID *> *mutable_shaders();
CROSS_SCHEMA_API  const flatbuffers::String *fullname() const;
  CROSS_SCHEMA_API  flatbuffers::String *mutable_fullname();
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API ShaderModuleMapT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(ShaderModuleMapT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<ShaderModuleMap> Pack(flatbuffers::FlatBufferBuilder &_fbb, const ShaderModuleMapT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API ShaderModuleMapBuilder {
  typedef ShaderModuleMap Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_guid(uint64_t guid);
  void add_subname(flatbuffers::Offset<flatbuffers::String> subname);
  void add_diridx(int8_t diridx);
  void add_size(uint64_t size);
  void add_mtime(uint64_t mtime);
  void add_hash(uint64_t hash);
  void add_shaders(flatbuffers::Offset<flatbuffers::Vector<const CrossSchema::GUID *>> shaders);
  void add_fullname(flatbuffers::Offset<flatbuffers::String> fullname);
  explicit ShaderModuleMapBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<ShaderModuleMap> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<ShaderModuleMap>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<ShaderModuleMap> CreateShaderModuleMap(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint64_t guid = 0,
    flatbuffers::Offset<flatbuffers::String> subname = 0,
    int8_t diridx = 0,
    uint64_t size = 0,
    uint64_t mtime = 0,
    uint64_t hash = 0,
    flatbuffers::Offset<flatbuffers::Vector<const CrossSchema::GUID *>> shaders = 0,
    flatbuffers::Offset<flatbuffers::String> fullname = 0);
struct ShaderModuleMap::Traits {
  using type = ShaderModuleMap;
  static auto constexpr Create = CreateShaderModuleMap;
};

CROSS_SCHEMA_API flatbuffers::Offset<ShaderModuleMap> CreateShaderModuleMapDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint64_t guid = 0,
    const char *subname = nullptr,
    int8_t diridx = 0,
    uint64_t size = 0,
    uint64_t mtime = 0,
    uint64_t hash = 0,
    const std::vector<CrossSchema::GUID> *shaders = nullptr,
    const char *fullname = nullptr);
CROSS_SCHEMA_API flatbuffers::Offset<ShaderModuleMap> CreateShaderModuleMap(flatbuffers::FlatBufferBuilder &_fbb, const ShaderModuleMapT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct ShaderSourceMapT : public flatbuffers::NativeTable {
  typedef ShaderSourceMap TableType;
  std::unique_ptr<CrossSchema::GUID> guid{};
  std::vector<uint64_t> modules{};
};

struct ShaderSourceMap FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef ShaderSourceMapT NativeTableType;
  typedef ShaderSourceMapBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_GUID = 4,
    VT_MODULES = 6
  };
CROSS_SCHEMA_API  const CrossSchema::GUID *guid() const;
  CROSS_SCHEMA_API  CrossSchema::GUID *mutable_guid();
CROSS_SCHEMA_API  const flatbuffers::Vector<uint64_t> *modules() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<uint64_t> *mutable_modules();
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API ShaderSourceMapT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(ShaderSourceMapT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<ShaderSourceMap> Pack(flatbuffers::FlatBufferBuilder &_fbb, const ShaderSourceMapT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API ShaderSourceMapBuilder {
  typedef ShaderSourceMap Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_guid(const CrossSchema::GUID *guid);
  void add_modules(flatbuffers::Offset<flatbuffers::Vector<uint64_t>> modules);
  explicit ShaderSourceMapBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<ShaderSourceMap> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<ShaderSourceMap>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<ShaderSourceMap> CreateShaderSourceMap(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::GUID *guid = 0,
    flatbuffers::Offset<flatbuffers::Vector<uint64_t>> modules = 0);
struct ShaderSourceMap::Traits {
  using type = ShaderSourceMap;
  static auto constexpr Create = CreateShaderSourceMap;
};

CROSS_SCHEMA_API flatbuffers::Offset<ShaderSourceMap> CreateShaderSourceMapDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::GUID *guid = 0,
    const std::vector<uint64_t> *modules = nullptr);
CROSS_SCHEMA_API flatbuffers::Offset<ShaderSourceMap> CreateShaderSourceMap(flatbuffers::FlatBufferBuilder &_fbb, const ShaderSourceMapT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct ShaderMapsT : public flatbuffers::NativeTable {
  typedef ShaderMaps TableType;
  std::vector<std::string> moduledirs{};
  std::vector<std::unique_ptr<CrossSchema::ShaderModuleMapT>> modulemaps{};
  std::vector<std::unique_ptr<CrossSchema::ShaderSourceMapT>> sourcemaps{};
};

struct ShaderMaps FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef ShaderMapsT NativeTableType;
  typedef ShaderMapsBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MODULEDIRS = 4,
    VT_MODULEMAPS = 6,
    VT_SOURCEMAPS = 8
  };
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *moduledirs() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *mutable_moduledirs();
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderModuleMap>> *modulemaps() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderModuleMap>> *mutable_modulemaps();
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderSourceMap>> *sourcemaps() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderSourceMap>> *mutable_sourcemaps();
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API ShaderMapsT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(ShaderMapsT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<ShaderMaps> Pack(flatbuffers::FlatBufferBuilder &_fbb, const ShaderMapsT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API ShaderMapsBuilder {
  typedef ShaderMaps Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_moduledirs(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> moduledirs);
  void add_modulemaps(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderModuleMap>>> modulemaps);
  void add_sourcemaps(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderSourceMap>>> sourcemaps);
  explicit ShaderMapsBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<ShaderMaps> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<ShaderMaps>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<ShaderMaps> CreateShaderMaps(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> moduledirs = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderModuleMap>>> modulemaps = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderSourceMap>>> sourcemaps = 0);
struct ShaderMaps::Traits {
  using type = ShaderMaps;
  static auto constexpr Create = CreateShaderMaps;
};

CROSS_SCHEMA_API flatbuffers::Offset<ShaderMaps> CreateShaderMapsDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<flatbuffers::Offset<flatbuffers::String>> *moduledirs = nullptr,
    const std::vector<flatbuffers::Offset<CrossSchema::ShaderModuleMap>> *modulemaps = nullptr,
    const std::vector<flatbuffers::Offset<CrossSchema::ShaderSourceMap>> *sourcemaps = nullptr);
CROSS_SCHEMA_API flatbuffers::Offset<ShaderMaps> CreateShaderMaps(flatbuffers::FlatBufferBuilder &_fbb, const ShaderMapsT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

inline const flatbuffers::TypeTable *ShaderModuleMapTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_ULONG, 0, -1 },
    { flatbuffers::ET_STRING, 0, -1 },
    { flatbuffers::ET_CHAR, 0, -1 },
    { flatbuffers::ET_ULONG, 0, -1 },
    { flatbuffers::ET_ULONG, 0, -1 },
    { flatbuffers::ET_ULONG, 0, -1 },
    { flatbuffers::ET_SEQUENCE, 1, 0 },
    { flatbuffers::ET_STRING, 0, -1 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::GUIDTypeTable
  };
  static const char * const names[] = {
    "guid",
    "subname",
    "diridx",
    "size",
    "mtime",
    "hash",
    "shaders",
    "fullname"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 8, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ShaderSourceMapTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_SEQUENCE, 0, 0 },
    { flatbuffers::ET_ULONG, 1, -1 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::GUIDTypeTable
  };
  static const char * const names[] = {
    "guid",
    "modules"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 2, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ShaderMapsTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_STRING, 1, -1 },
    { flatbuffers::ET_SEQUENCE, 1, 0 },
    { flatbuffers::ET_SEQUENCE, 1, 1 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::ShaderModuleMapTypeTable,
    CrossSchema::ShaderSourceMapTypeTable
  };
  static const char * const names[] = {
    "moduledirs",
    "modulemaps",
    "sourcemaps"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 3, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

CROSS_SCHEMA_API const CrossSchema::ShaderMaps *GetShaderMaps(const void *buf);
CROSS_SCHEMA_API const CrossSchema::ShaderMaps *GetSizePrefixedShaderMaps(const void *buf);
CROSS_SCHEMA_API ShaderMaps *GetMutableShaderMaps(void *buf);
CROSS_SCHEMA_API bool VerifyShaderMapsBuffer(flatbuffers::Verifier &verifier);
CROSS_SCHEMA_API bool VerifySizePrefixedShaderMapsBuffer(flatbuffers::Verifier &verifier);
CROSS_SCHEMA_API void FinishShaderMapsBuffer(flatbuffers::FlatBufferBuilder &fbb,flatbuffers::Offset<CrossSchema::ShaderMaps> root);
CROSS_SCHEMA_API void FinishSizePrefixedShaderMapsBuffer(flatbuffers::FlatBufferBuilder &fbb,flatbuffers::Offset<CrossSchema::ShaderMaps> root);
CROSS_SCHEMA_API std::unique_ptr<CrossSchema::ShaderMapsT> UnPackShaderMaps(const void *buf,const flatbuffers::resolver_function_t *res = nullptr);
CROSS_SCHEMA_API std::unique_ptr<CrossSchema::ShaderMapsT> UnPackSizePrefixedShaderMaps(const void *buf,const flatbuffers::resolver_function_t *res = nullptr);
}  // namespace CrossSchema

#endif  // FLATBUFFERS_GENERATED_SHADERMAPS_CROSSSCHEMA_H_
