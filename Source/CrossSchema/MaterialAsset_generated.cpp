#include "MaterialAsset_generated.h"
namespace CrossSchema {
 const flatbuffers::TypeTable * FloatProp::MiniReflectTypeTable(){
    return FloatPropTypeTable();
  }
const flatbuffers::String * FloatProp::name() const{
    return GetPointer<const flatbuffers::String *>(VT_NAME);
  }
  flatbuffers::String * FloatProp::mutable_name() {
    return GetPointer<flatbuffers::String *>(VT_NAME);
  }
bool FloatProp::KeyCompareLessThan(const FloatProp *o) const{
    return *name() < *o->name();
  }
int FloatProp::KeyCompareWithValue(const char *val) const {
    return strcmp(name()->c_str(), val);
  }
float  FloatProp::value() const{
    return GetField<float>(VT_VALUE, 0.0f);
  }
  bool FloatProp::mutate_value (float _value) {
    return SetField<float>(VT_VALUE, _value, 0.0f);
  }
 bool FloatProp::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyField<float>(verifier, VT_VALUE) &&
           verifier.EndTable();
  }
  void FloatPropBuilder::add_name(flatbuffers::Offset<flatbuffers::String> name) {
    fbb_.AddOffset(FloatProp::VT_NAME, name);
  }
  void FloatPropBuilder::add_value(float value) {
    fbb_.AddElement<float>(FloatProp::VT_VALUE, value, 0.0f);
  }
flatbuffers::Offset<FloatProp> CreateFloatProp(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> name,
    float value){
    FloatPropBuilder builder_(_fbb);
  builder_.add_value(value);
  builder_.add_name(name);
  return builder_.Finish();
}

flatbuffers::Offset<FloatProp> CreateFloatPropDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *name,
    float value) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  return CrossSchema::CreateFloatProp(
      _fbb,
      name__,
      value);
}

 const flatbuffers::TypeTable * Float2Prop::MiniReflectTypeTable(){
    return Float2PropTypeTable();
  }
const flatbuffers::String * Float2Prop::name() const{
    return GetPointer<const flatbuffers::String *>(VT_NAME);
  }
  flatbuffers::String * Float2Prop::mutable_name() {
    return GetPointer<flatbuffers::String *>(VT_NAME);
  }
bool Float2Prop::KeyCompareLessThan(const Float2Prop *o) const{
    return *name() < *o->name();
  }
int Float2Prop::KeyCompareWithValue(const char *val) const {
    return strcmp(name()->c_str(), val);
  }
const CrossSchema::float2 * Float2Prop::value() const{
    return GetStruct<const CrossSchema::float2 *>(VT_VALUE);
  }
  CrossSchema::float2 * Float2Prop::mutable_value() {
    return GetStruct<CrossSchema::float2 *>(VT_VALUE);
  }
 bool Float2Prop::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyField<CrossSchema::float2>(verifier, VT_VALUE) &&
           verifier.EndTable();
  }
  void Float2PropBuilder::add_name(flatbuffers::Offset<flatbuffers::String> name) {
    fbb_.AddOffset(Float2Prop::VT_NAME, name);
  }
  void Float2PropBuilder::add_value(const CrossSchema::float2 *value) {
    fbb_.AddStruct(Float2Prop::VT_VALUE, value);
  }
flatbuffers::Offset<Float2Prop> CreateFloat2Prop(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> name,
    const CrossSchema::float2 *value){
    Float2PropBuilder builder_(_fbb);
  builder_.add_value(value);
  builder_.add_name(name);
  return builder_.Finish();
}

flatbuffers::Offset<Float2Prop> CreateFloat2PropDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *name,
    const CrossSchema::float2 *value) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  return CrossSchema::CreateFloat2Prop(
      _fbb,
      name__,
      value);
}

 const flatbuffers::TypeTable * Float3Prop::MiniReflectTypeTable(){
    return Float3PropTypeTable();
  }
const flatbuffers::String * Float3Prop::name() const{
    return GetPointer<const flatbuffers::String *>(VT_NAME);
  }
  flatbuffers::String * Float3Prop::mutable_name() {
    return GetPointer<flatbuffers::String *>(VT_NAME);
  }
bool Float3Prop::KeyCompareLessThan(const Float3Prop *o) const{
    return *name() < *o->name();
  }
int Float3Prop::KeyCompareWithValue(const char *val) const {
    return strcmp(name()->c_str(), val);
  }
const CrossSchema::float3 * Float3Prop::value() const{
    return GetStruct<const CrossSchema::float3 *>(VT_VALUE);
  }
  CrossSchema::float3 * Float3Prop::mutable_value() {
    return GetStruct<CrossSchema::float3 *>(VT_VALUE);
  }
 bool Float3Prop::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyField<CrossSchema::float3>(verifier, VT_VALUE) &&
           verifier.EndTable();
  }
  void Float3PropBuilder::add_name(flatbuffers::Offset<flatbuffers::String> name) {
    fbb_.AddOffset(Float3Prop::VT_NAME, name);
  }
  void Float3PropBuilder::add_value(const CrossSchema::float3 *value) {
    fbb_.AddStruct(Float3Prop::VT_VALUE, value);
  }
flatbuffers::Offset<Float3Prop> CreateFloat3Prop(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> name,
    const CrossSchema::float3 *value){
    Float3PropBuilder builder_(_fbb);
  builder_.add_value(value);
  builder_.add_name(name);
  return builder_.Finish();
}

flatbuffers::Offset<Float3Prop> CreateFloat3PropDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *name,
    const CrossSchema::float3 *value) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  return CrossSchema::CreateFloat3Prop(
      _fbb,
      name__,
      value);
}

 const flatbuffers::TypeTable * Float4Prop::MiniReflectTypeTable(){
    return Float4PropTypeTable();
  }
const flatbuffers::String * Float4Prop::name() const{
    return GetPointer<const flatbuffers::String *>(VT_NAME);
  }
  flatbuffers::String * Float4Prop::mutable_name() {
    return GetPointer<flatbuffers::String *>(VT_NAME);
  }
bool Float4Prop::KeyCompareLessThan(const Float4Prop *o) const{
    return *name() < *o->name();
  }
int Float4Prop::KeyCompareWithValue(const char *val) const {
    return strcmp(name()->c_str(), val);
  }
const CrossSchema::float4 * Float4Prop::value() const{
    return GetStruct<const CrossSchema::float4 *>(VT_VALUE);
  }
  CrossSchema::float4 * Float4Prop::mutable_value() {
    return GetStruct<CrossSchema::float4 *>(VT_VALUE);
  }
 bool Float4Prop::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyField<CrossSchema::float4>(verifier, VT_VALUE) &&
           verifier.EndTable();
  }
  void Float4PropBuilder::add_name(flatbuffers::Offset<flatbuffers::String> name) {
    fbb_.AddOffset(Float4Prop::VT_NAME, name);
  }
  void Float4PropBuilder::add_value(const CrossSchema::float4 *value) {
    fbb_.AddStruct(Float4Prop::VT_VALUE, value);
  }
flatbuffers::Offset<Float4Prop> CreateFloat4Prop(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> name,
    const CrossSchema::float4 *value){
    Float4PropBuilder builder_(_fbb);
  builder_.add_value(value);
  builder_.add_name(name);
  return builder_.Finish();
}

flatbuffers::Offset<Float4Prop> CreateFloat4PropDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *name,
    const CrossSchema::float4 *value) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  return CrossSchema::CreateFloat4Prop(
      _fbb,
      name__,
      value);
}

 const flatbuffers::TypeTable * BoolProp::MiniReflectTypeTable(){
    return BoolPropTypeTable();
  }
const flatbuffers::String * BoolProp::name() const{
    return GetPointer<const flatbuffers::String *>(VT_NAME);
  }
  flatbuffers::String * BoolProp::mutable_name() {
    return GetPointer<flatbuffers::String *>(VT_NAME);
  }
bool BoolProp::KeyCompareLessThan(const BoolProp *o) const{
    return *name() < *o->name();
  }
int BoolProp::KeyCompareWithValue(const char *val) const {
    return strcmp(name()->c_str(), val);
  }
bool  BoolProp::value() const{
    return GetField<uint8_t>(VT_VALUE, 0) != 0;
  }
  bool BoolProp::mutate_value (bool _value) {
    return SetField<uint8_t>(VT_VALUE, static_cast<uint8_t>(_value), 0);
  }
 bool BoolProp::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyField<uint8_t>(verifier, VT_VALUE) &&
           verifier.EndTable();
  }
  void BoolPropBuilder::add_name(flatbuffers::Offset<flatbuffers::String> name) {
    fbb_.AddOffset(BoolProp::VT_NAME, name);
  }
  void BoolPropBuilder::add_value(bool value) {
    fbb_.AddElement<uint8_t>(BoolProp::VT_VALUE, static_cast<uint8_t>(value), 0);
  }
flatbuffers::Offset<BoolProp> CreateBoolProp(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> name,
    bool value){
    BoolPropBuilder builder_(_fbb);
  builder_.add_name(name);
  builder_.add_value(value);
  return builder_.Finish();
}

flatbuffers::Offset<BoolProp> CreateBoolPropDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *name,
    bool value) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  return CrossSchema::CreateBoolProp(
      _fbb,
      name__,
      value);
}

 const flatbuffers::TypeTable * IntProp::MiniReflectTypeTable(){
    return IntPropTypeTable();
  }
const flatbuffers::String * IntProp::name() const{
    return GetPointer<const flatbuffers::String *>(VT_NAME);
  }
  flatbuffers::String * IntProp::mutable_name() {
    return GetPointer<flatbuffers::String *>(VT_NAME);
  }
bool IntProp::KeyCompareLessThan(const IntProp *o) const{
    return *name() < *o->name();
  }
int IntProp::KeyCompareWithValue(const char *val) const {
    return strcmp(name()->c_str(), val);
  }
int32_t  IntProp::value() const{
    return GetField<int32_t>(VT_VALUE, 0);
  }
  bool IntProp::mutate_value (int32_t _value) {
    return SetField<int32_t>(VT_VALUE, _value, 0);
  }
 bool IntProp::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyField<int32_t>(verifier, VT_VALUE) &&
           verifier.EndTable();
  }
  void IntPropBuilder::add_name(flatbuffers::Offset<flatbuffers::String> name) {
    fbb_.AddOffset(IntProp::VT_NAME, name);
  }
  void IntPropBuilder::add_value(int32_t value) {
    fbb_.AddElement<int32_t>(IntProp::VT_VALUE, value, 0);
  }
flatbuffers::Offset<IntProp> CreateIntProp(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> name,
    int32_t value){
    IntPropBuilder builder_(_fbb);
  builder_.add_value(value);
  builder_.add_name(name);
  return builder_.Finish();
}

flatbuffers::Offset<IntProp> CreateIntPropDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *name,
    int32_t value) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  return CrossSchema::CreateIntProp(
      _fbb,
      name__,
      value);
}

 const flatbuffers::TypeTable * StringProp::MiniReflectTypeTable(){
    return StringPropTypeTable();
  }
const flatbuffers::String * StringProp::name() const{
    return GetPointer<const flatbuffers::String *>(VT_NAME);
  }
  flatbuffers::String * StringProp::mutable_name() {
    return GetPointer<flatbuffers::String *>(VT_NAME);
  }
bool StringProp::KeyCompareLessThan(const StringProp *o) const{
    return *name() < *o->name();
  }
int StringProp::KeyCompareWithValue(const char *val) const {
    return strcmp(name()->c_str(), val);
  }
const flatbuffers::String * StringProp::value() const{
    return GetPointer<const flatbuffers::String *>(VT_VALUE);
  }
  flatbuffers::String * StringProp::mutable_value() {
    return GetPointer<flatbuffers::String *>(VT_VALUE);
  }
 bool StringProp::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyOffset(verifier, VT_VALUE) &&
           verifier.VerifyString(value()) &&
           verifier.EndTable();
  }
  void StringPropBuilder::add_name(flatbuffers::Offset<flatbuffers::String> name) {
    fbb_.AddOffset(StringProp::VT_NAME, name);
  }
  void StringPropBuilder::add_value(flatbuffers::Offset<flatbuffers::String> value) {
    fbb_.AddOffset(StringProp::VT_VALUE, value);
  }
flatbuffers::Offset<StringProp> CreateStringProp(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> name,
    flatbuffers::Offset<flatbuffers::String> value){
    StringPropBuilder builder_(_fbb);
  builder_.add_value(value);
  builder_.add_name(name);
  return builder_.Finish();
}

flatbuffers::Offset<StringProp> CreateStringPropDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *name,
    const char *value) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto value__ = value ? _fbb.CreateString(value) : 0;
  return CrossSchema::CreateStringProp(
      _fbb,
      name__,
      value__);
}

 const flatbuffers::TypeTable * MaterialAsset::MiniReflectTypeTable(){
    return MaterialAssetTypeTable();
  }
const flatbuffers::String * MaterialAsset::parent() const{
    return GetPointer<const flatbuffers::String *>(VT_PARENT);
  }
  flatbuffers::String * MaterialAsset::mutable_parent() {
    return GetPointer<flatbuffers::String *>(VT_PARENT);
  }
const flatbuffers::String * MaterialAsset::fx_file() const{
    return GetPointer<const flatbuffers::String *>(VT_FX_FILE);
  }
  flatbuffers::String * MaterialAsset::mutable_fx_file() {
    return GetPointer<flatbuffers::String *>(VT_FX_FILE);
  }
const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::FloatProp>> * MaterialAsset::float_prop_array() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::FloatProp>> *>(VT_FLOAT_PROP_ARRAY);
  }
  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::FloatProp>> * MaterialAsset::mutable_float_prop_array() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::FloatProp>> *>(VT_FLOAT_PROP_ARRAY);
  }
const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::Float2Prop>> * MaterialAsset::float2_prop_array() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::Float2Prop>> *>(VT_FLOAT2_PROP_ARRAY);
  }
  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::Float2Prop>> * MaterialAsset::mutable_float2_prop_array() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::Float2Prop>> *>(VT_FLOAT2_PROP_ARRAY);
  }
const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::Float3Prop>> * MaterialAsset::float3_prop_array() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::Float3Prop>> *>(VT_FLOAT3_PROP_ARRAY);
  }
  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::Float3Prop>> * MaterialAsset::mutable_float3_prop_array() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::Float3Prop>> *>(VT_FLOAT3_PROP_ARRAY);
  }
const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::Float4Prop>> * MaterialAsset::float4_prop_array() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::Float4Prop>> *>(VT_FLOAT4_PROP_ARRAY);
  }
  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::Float4Prop>> * MaterialAsset::mutable_float4_prop_array() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::Float4Prop>> *>(VT_FLOAT4_PROP_ARRAY);
  }
const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::matrix4x4>> * MaterialAsset::matrix4x4_prop_array() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::matrix4x4>> *>(VT_MATRIX4X4_PROP_ARRAY);
  }
  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::matrix4x4>> * MaterialAsset::mutable_matrix4x4_prop_array() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::matrix4x4>> *>(VT_MATRIX4X4_PROP_ARRAY);
  }
const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::IntProp>> * MaterialAsset::int_prop_array() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::IntProp>> *>(VT_INT_PROP_ARRAY);
  }
  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::IntProp>> * MaterialAsset::mutable_int_prop_array() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::IntProp>> *>(VT_INT_PROP_ARRAY);
  }
const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::StringProp>> * MaterialAsset::texture_prop_array() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::StringProp>> *>(VT_TEXTURE_PROP_ARRAY);
  }
  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::StringProp>> * MaterialAsset::mutable_texture_prop_array() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::StringProp>> *>(VT_TEXTURE_PROP_ARRAY);
  }
const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::BoolProp>> * MaterialAsset::bool_prop_array() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::BoolProp>> *>(VT_BOOL_PROP_ARRAY);
  }
  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::BoolProp>> * MaterialAsset::mutable_bool_prop_array() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::BoolProp>> *>(VT_BOOL_PROP_ARRAY);
  }
 bool MaterialAsset::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PARENT) &&
           verifier.VerifyString(parent()) &&
           VerifyOffset(verifier, VT_FX_FILE) &&
           verifier.VerifyString(fx_file()) &&
           VerifyOffset(verifier, VT_FLOAT_PROP_ARRAY) &&
           verifier.VerifyVector(float_prop_array()) &&
           verifier.VerifyVectorOfTables(float_prop_array()) &&
           VerifyOffset(verifier, VT_FLOAT2_PROP_ARRAY) &&
           verifier.VerifyVector(float2_prop_array()) &&
           verifier.VerifyVectorOfTables(float2_prop_array()) &&
           VerifyOffset(verifier, VT_FLOAT3_PROP_ARRAY) &&
           verifier.VerifyVector(float3_prop_array()) &&
           verifier.VerifyVectorOfTables(float3_prop_array()) &&
           VerifyOffset(verifier, VT_FLOAT4_PROP_ARRAY) &&
           verifier.VerifyVector(float4_prop_array()) &&
           verifier.VerifyVectorOfTables(float4_prop_array()) &&
           VerifyOffset(verifier, VT_MATRIX4X4_PROP_ARRAY) &&
           verifier.VerifyVector(matrix4x4_prop_array()) &&
           verifier.VerifyVectorOfTables(matrix4x4_prop_array()) &&
           VerifyOffset(verifier, VT_INT_PROP_ARRAY) &&
           verifier.VerifyVector(int_prop_array()) &&
           verifier.VerifyVectorOfTables(int_prop_array()) &&
           VerifyOffset(verifier, VT_TEXTURE_PROP_ARRAY) &&
           verifier.VerifyVector(texture_prop_array()) &&
           verifier.VerifyVectorOfTables(texture_prop_array()) &&
           VerifyOffset(verifier, VT_BOOL_PROP_ARRAY) &&
           verifier.VerifyVector(bool_prop_array()) &&
           verifier.VerifyVectorOfTables(bool_prop_array()) &&
           verifier.EndTable();
  }
  void MaterialAssetBuilder::add_parent(flatbuffers::Offset<flatbuffers::String> parent) {
    fbb_.AddOffset(MaterialAsset::VT_PARENT, parent);
  }
  void MaterialAssetBuilder::add_fx_file(flatbuffers::Offset<flatbuffers::String> fx_file) {
    fbb_.AddOffset(MaterialAsset::VT_FX_FILE, fx_file);
  }
  void MaterialAssetBuilder::add_float_prop_array(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::FloatProp>>> float_prop_array) {
    fbb_.AddOffset(MaterialAsset::VT_FLOAT_PROP_ARRAY, float_prop_array);
  }
  void MaterialAssetBuilder::add_float2_prop_array(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::Float2Prop>>> float2_prop_array) {
    fbb_.AddOffset(MaterialAsset::VT_FLOAT2_PROP_ARRAY, float2_prop_array);
  }
  void MaterialAssetBuilder::add_float3_prop_array(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::Float3Prop>>> float3_prop_array) {
    fbb_.AddOffset(MaterialAsset::VT_FLOAT3_PROP_ARRAY, float3_prop_array);
  }
  void MaterialAssetBuilder::add_float4_prop_array(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::Float4Prop>>> float4_prop_array) {
    fbb_.AddOffset(MaterialAsset::VT_FLOAT4_PROP_ARRAY, float4_prop_array);
  }
  void MaterialAssetBuilder::add_matrix4x4_prop_array(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::matrix4x4>>> matrix4x4_prop_array) {
    fbb_.AddOffset(MaterialAsset::VT_MATRIX4X4_PROP_ARRAY, matrix4x4_prop_array);
  }
  void MaterialAssetBuilder::add_int_prop_array(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::IntProp>>> int_prop_array) {
    fbb_.AddOffset(MaterialAsset::VT_INT_PROP_ARRAY, int_prop_array);
  }
  void MaterialAssetBuilder::add_texture_prop_array(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::StringProp>>> texture_prop_array) {
    fbb_.AddOffset(MaterialAsset::VT_TEXTURE_PROP_ARRAY, texture_prop_array);
  }
  void MaterialAssetBuilder::add_bool_prop_array(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::BoolProp>>> bool_prop_array) {
    fbb_.AddOffset(MaterialAsset::VT_BOOL_PROP_ARRAY, bool_prop_array);
  }
flatbuffers::Offset<MaterialAsset> CreateMaterialAsset(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> parent,
    flatbuffers::Offset<flatbuffers::String> fx_file,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::FloatProp>>> float_prop_array,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::Float2Prop>>> float2_prop_array,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::Float3Prop>>> float3_prop_array,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::Float4Prop>>> float4_prop_array,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::matrix4x4>>> matrix4x4_prop_array,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::IntProp>>> int_prop_array,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::StringProp>>> texture_prop_array,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::BoolProp>>> bool_prop_array){
    MaterialAssetBuilder builder_(_fbb);
  builder_.add_bool_prop_array(bool_prop_array);
  builder_.add_texture_prop_array(texture_prop_array);
  builder_.add_int_prop_array(int_prop_array);
  builder_.add_matrix4x4_prop_array(matrix4x4_prop_array);
  builder_.add_float4_prop_array(float4_prop_array);
  builder_.add_float3_prop_array(float3_prop_array);
  builder_.add_float2_prop_array(float2_prop_array);
  builder_.add_float_prop_array(float_prop_array);
  builder_.add_fx_file(fx_file);
  builder_.add_parent(parent);
  return builder_.Finish();
}

flatbuffers::Offset<MaterialAsset> CreateMaterialAssetDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *parent,
    const char *fx_file,
    std::vector<flatbuffers::Offset<CrossSchema::FloatProp>> *float_prop_array,
    std::vector<flatbuffers::Offset<CrossSchema::Float2Prop>> *float2_prop_array,
    std::vector<flatbuffers::Offset<CrossSchema::Float3Prop>> *float3_prop_array,
    std::vector<flatbuffers::Offset<CrossSchema::Float4Prop>> *float4_prop_array,
    const std::vector<flatbuffers::Offset<CrossSchema::matrix4x4>> *matrix4x4_prop_array,
    std::vector<flatbuffers::Offset<CrossSchema::IntProp>> *int_prop_array,
    std::vector<flatbuffers::Offset<CrossSchema::StringProp>> *texture_prop_array,
    std::vector<flatbuffers::Offset<CrossSchema::BoolProp>> *bool_prop_array) {
  auto parent__ = parent ? _fbb.CreateString(parent) : 0;
  auto fx_file__ = fx_file ? _fbb.CreateString(fx_file) : 0;
  auto float_prop_array__ = float_prop_array ? _fbb.CreateVectorOfSortedTables<CrossSchema::FloatProp>(float_prop_array) : 0;
  auto float2_prop_array__ = float2_prop_array ? _fbb.CreateVectorOfSortedTables<CrossSchema::Float2Prop>(float2_prop_array) : 0;
  auto float3_prop_array__ = float3_prop_array ? _fbb.CreateVectorOfSortedTables<CrossSchema::Float3Prop>(float3_prop_array) : 0;
  auto float4_prop_array__ = float4_prop_array ? _fbb.CreateVectorOfSortedTables<CrossSchema::Float4Prop>(float4_prop_array) : 0;
  auto matrix4x4_prop_array__ = matrix4x4_prop_array ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::matrix4x4>>(*matrix4x4_prop_array) : 0;
  auto int_prop_array__ = int_prop_array ? _fbb.CreateVectorOfSortedTables<CrossSchema::IntProp>(int_prop_array) : 0;
  auto texture_prop_array__ = texture_prop_array ? _fbb.CreateVectorOfSortedTables<CrossSchema::StringProp>(texture_prop_array) : 0;
  auto bool_prop_array__ = bool_prop_array ? _fbb.CreateVectorOfSortedTables<CrossSchema::BoolProp>(bool_prop_array) : 0;
  return CrossSchema::CreateMaterialAsset(
      _fbb,
      parent__,
      fx_file__,
      float_prop_array__,
      float2_prop_array__,
      float3_prop_array__,
      float4_prop_array__,
      matrix4x4_prop_array__,
      int_prop_array__,
      texture_prop_array__,
      bool_prop_array__);
}

FloatPropT *FloatProp::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<FloatPropT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void FloatProp::UnPackTo(FloatPropT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = name(); if (_e) _o->name = _e->str(); }
  { auto _e = value(); _o->value = _e; }
}

CROSS_SCHEMA_API flatbuffers::Offset<FloatProp> FloatProp::Pack(flatbuffers::FlatBufferBuilder &_fbb, const FloatPropT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateFloatProp(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<FloatProp> CreateFloatProp(flatbuffers::FlatBufferBuilder &_fbb, const FloatPropT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const FloatPropT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _name = _fbb.CreateString(_o->name);
  auto _value = _o->value;
  return CrossSchema::CreateFloatProp(
      _fbb,
      _name,
      _value);
}

Float2PropT *Float2Prop::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<Float2PropT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void Float2Prop::UnPackTo(Float2PropT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = name(); if (_e) _o->name = _e->str(); }
  { auto _e = value(); if (_e) _o->value = std::unique_ptr<CrossSchema::float2>(new CrossSchema::float2(*_e)); }
}

CROSS_SCHEMA_API flatbuffers::Offset<Float2Prop> Float2Prop::Pack(flatbuffers::FlatBufferBuilder &_fbb, const Float2PropT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateFloat2Prop(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<Float2Prop> CreateFloat2Prop(flatbuffers::FlatBufferBuilder &_fbb, const Float2PropT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const Float2PropT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _name = _fbb.CreateString(_o->name);
  auto _value = _o->value ? _o->value.get() : 0;
  return CrossSchema::CreateFloat2Prop(
      _fbb,
      _name,
      _value);
}

Float3PropT *Float3Prop::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<Float3PropT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void Float3Prop::UnPackTo(Float3PropT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = name(); if (_e) _o->name = _e->str(); }
  { auto _e = value(); if (_e) _o->value = std::unique_ptr<CrossSchema::float3>(new CrossSchema::float3(*_e)); }
}

CROSS_SCHEMA_API flatbuffers::Offset<Float3Prop> Float3Prop::Pack(flatbuffers::FlatBufferBuilder &_fbb, const Float3PropT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateFloat3Prop(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<Float3Prop> CreateFloat3Prop(flatbuffers::FlatBufferBuilder &_fbb, const Float3PropT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const Float3PropT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _name = _fbb.CreateString(_o->name);
  auto _value = _o->value ? _o->value.get() : 0;
  return CrossSchema::CreateFloat3Prop(
      _fbb,
      _name,
      _value);
}

Float4PropT *Float4Prop::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<Float4PropT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void Float4Prop::UnPackTo(Float4PropT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = name(); if (_e) _o->name = _e->str(); }
  { auto _e = value(); if (_e) _o->value = std::unique_ptr<CrossSchema::float4>(new CrossSchema::float4(*_e)); }
}

CROSS_SCHEMA_API flatbuffers::Offset<Float4Prop> Float4Prop::Pack(flatbuffers::FlatBufferBuilder &_fbb, const Float4PropT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateFloat4Prop(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<Float4Prop> CreateFloat4Prop(flatbuffers::FlatBufferBuilder &_fbb, const Float4PropT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const Float4PropT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _name = _fbb.CreateString(_o->name);
  auto _value = _o->value ? _o->value.get() : 0;
  return CrossSchema::CreateFloat4Prop(
      _fbb,
      _name,
      _value);
}

BoolPropT *BoolProp::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<BoolPropT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void BoolProp::UnPackTo(BoolPropT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = name(); if (_e) _o->name = _e->str(); }
  { auto _e = value(); _o->value = _e; }
}

CROSS_SCHEMA_API flatbuffers::Offset<BoolProp> BoolProp::Pack(flatbuffers::FlatBufferBuilder &_fbb, const BoolPropT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateBoolProp(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<BoolProp> CreateBoolProp(flatbuffers::FlatBufferBuilder &_fbb, const BoolPropT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const BoolPropT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _name = _fbb.CreateString(_o->name);
  auto _value = _o->value;
  return CrossSchema::CreateBoolProp(
      _fbb,
      _name,
      _value);
}

IntPropT *IntProp::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<IntPropT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void IntProp::UnPackTo(IntPropT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = name(); if (_e) _o->name = _e->str(); }
  { auto _e = value(); _o->value = _e; }
}

CROSS_SCHEMA_API flatbuffers::Offset<IntProp> IntProp::Pack(flatbuffers::FlatBufferBuilder &_fbb, const IntPropT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateIntProp(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<IntProp> CreateIntProp(flatbuffers::FlatBufferBuilder &_fbb, const IntPropT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const IntPropT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _name = _fbb.CreateString(_o->name);
  auto _value = _o->value;
  return CrossSchema::CreateIntProp(
      _fbb,
      _name,
      _value);
}

StringPropT *StringProp::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<StringPropT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void StringProp::UnPackTo(StringPropT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = name(); if (_e) _o->name = _e->str(); }
  { auto _e = value(); if (_e) _o->value = _e->str(); }
}

CROSS_SCHEMA_API flatbuffers::Offset<StringProp> StringProp::Pack(flatbuffers::FlatBufferBuilder &_fbb, const StringPropT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateStringProp(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<StringProp> CreateStringProp(flatbuffers::FlatBufferBuilder &_fbb, const StringPropT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const StringPropT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _name = _fbb.CreateString(_o->name);
  auto _value = _o->value.empty() ? 0 : _fbb.CreateString(_o->value);
  return CrossSchema::CreateStringProp(
      _fbb,
      _name,
      _value);
}

MaterialAssetT *MaterialAsset::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<MaterialAssetT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void MaterialAsset::UnPackTo(MaterialAssetT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = parent(); if (_e) _o->parent = _e->str(); }
  { auto _e = fx_file(); if (_e) _o->fx_file = _e->str(); }
  { auto _e = float_prop_array(); if (_e) { _o->float_prop_array.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->float_prop_array[_i] = std::unique_ptr<CrossSchema::FloatPropT>(_e->Get(_i)->UnPack(_resolver)); } } }
  { auto _e = float2_prop_array(); if (_e) { _o->float2_prop_array.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->float2_prop_array[_i] = std::unique_ptr<CrossSchema::Float2PropT>(_e->Get(_i)->UnPack(_resolver)); } } }
  { auto _e = float3_prop_array(); if (_e) { _o->float3_prop_array.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->float3_prop_array[_i] = std::unique_ptr<CrossSchema::Float3PropT>(_e->Get(_i)->UnPack(_resolver)); } } }
  { auto _e = float4_prop_array(); if (_e) { _o->float4_prop_array.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->float4_prop_array[_i] = std::unique_ptr<CrossSchema::Float4PropT>(_e->Get(_i)->UnPack(_resolver)); } } }
  { auto _e = matrix4x4_prop_array(); if (_e) { _o->matrix4x4_prop_array.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->matrix4x4_prop_array[_i] = std::unique_ptr<CrossSchema::matrix4x4T>(_e->Get(_i)->UnPack(_resolver)); } } }
  { auto _e = int_prop_array(); if (_e) { _o->int_prop_array.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->int_prop_array[_i] = std::unique_ptr<CrossSchema::IntPropT>(_e->Get(_i)->UnPack(_resolver)); } } }
  { auto _e = texture_prop_array(); if (_e) { _o->texture_prop_array.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->texture_prop_array[_i] = std::unique_ptr<CrossSchema::StringPropT>(_e->Get(_i)->UnPack(_resolver)); } } }
  { auto _e = bool_prop_array(); if (_e) { _o->bool_prop_array.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->bool_prop_array[_i] = std::unique_ptr<CrossSchema::BoolPropT>(_e->Get(_i)->UnPack(_resolver)); } } }
}

CROSS_SCHEMA_API flatbuffers::Offset<MaterialAsset> MaterialAsset::Pack(flatbuffers::FlatBufferBuilder &_fbb, const MaterialAssetT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateMaterialAsset(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<MaterialAsset> CreateMaterialAsset(flatbuffers::FlatBufferBuilder &_fbb, const MaterialAssetT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const MaterialAssetT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _parent = _o->parent.empty() ? 0 : _fbb.CreateString(_o->parent);
  auto _fx_file = _o->fx_file.empty() ? 0 : _fbb.CreateString(_o->fx_file);
  auto _float_prop_array = _o->float_prop_array.size() ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::FloatProp>> (_o->float_prop_array.size(), [](size_t i, _VectorArgs *__va) { return CreateFloatProp(*__va->__fbb, __va->__o->float_prop_array[i].get(), __va->__rehasher); }, &_va ) : 0;
  auto _float2_prop_array = _o->float2_prop_array.size() ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::Float2Prop>> (_o->float2_prop_array.size(), [](size_t i, _VectorArgs *__va) { return CreateFloat2Prop(*__va->__fbb, __va->__o->float2_prop_array[i].get(), __va->__rehasher); }, &_va ) : 0;
  auto _float3_prop_array = _o->float3_prop_array.size() ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::Float3Prop>> (_o->float3_prop_array.size(), [](size_t i, _VectorArgs *__va) { return CreateFloat3Prop(*__va->__fbb, __va->__o->float3_prop_array[i].get(), __va->__rehasher); }, &_va ) : 0;
  auto _float4_prop_array = _o->float4_prop_array.size() ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::Float4Prop>> (_o->float4_prop_array.size(), [](size_t i, _VectorArgs *__va) { return CreateFloat4Prop(*__va->__fbb, __va->__o->float4_prop_array[i].get(), __va->__rehasher); }, &_va ) : 0;
  auto _matrix4x4_prop_array = _o->matrix4x4_prop_array.size() ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::matrix4x4>> (_o->matrix4x4_prop_array.size(), [](size_t i, _VectorArgs *__va) { return Creatematrix4x4(*__va->__fbb, __va->__o->matrix4x4_prop_array[i].get(), __va->__rehasher); }, &_va ) : 0;
  auto _int_prop_array = _o->int_prop_array.size() ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::IntProp>> (_o->int_prop_array.size(), [](size_t i, _VectorArgs *__va) { return CreateIntProp(*__va->__fbb, __va->__o->int_prop_array[i].get(), __va->__rehasher); }, &_va ) : 0;
  auto _texture_prop_array = _o->texture_prop_array.size() ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::StringProp>> (_o->texture_prop_array.size(), [](size_t i, _VectorArgs *__va) { return CreateStringProp(*__va->__fbb, __va->__o->texture_prop_array[i].get(), __va->__rehasher); }, &_va ) : 0;
  auto _bool_prop_array = _o->bool_prop_array.size() ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::BoolProp>> (_o->bool_prop_array.size(), [](size_t i, _VectorArgs *__va) { return CreateBoolProp(*__va->__fbb, __va->__o->bool_prop_array[i].get(), __va->__rehasher); }, &_va ) : 0;
  return CrossSchema::CreateMaterialAsset(
      _fbb,
      _parent,
      _fx_file,
      _float_prop_array,
      _float2_prop_array,
      _float3_prop_array,
      _float4_prop_array,
      _matrix4x4_prop_array,
      _int_prop_array,
      _texture_prop_array,
      _bool_prop_array);
}

const CrossSchema::MaterialAsset *GetMaterialAsset(const void *buf){
 return flatbuffers::GetRoot<CrossSchema::MaterialAsset>(buf);
}

const CrossSchema::MaterialAsset *GetSizePrefixedMaterialAsset(const void *buf) {
return flatbuffers::GetSizePrefixedRoot<CrossSchema::MaterialAsset>(buf);
}

MaterialAsset *GetMutableMaterialAsset(void *buf) {
return flatbuffers::GetMutableRoot<MaterialAsset>(buf);
}

bool VerifyMaterialAssetBuffer(flatbuffers::Verifier &verifier) {
return verifier.VerifyBuffer<CrossSchema::MaterialAsset>(nullptr);
}

bool VerifySizePrefixedMaterialAssetBuffer(flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<CrossSchema::MaterialAsset>(nullptr);
}

void FinishMaterialAssetBuffer(flatbuffers::FlatBufferBuilder &fbb,flatbuffers::Offset<CrossSchema::MaterialAsset> root) {
  fbb.Finish(root);
}

void FinishSizePrefixedMaterialAssetBuffer(flatbuffers::FlatBufferBuilder &fbb,flatbuffers::Offset<CrossSchema::MaterialAsset> root) {
fbb.FinishSizePrefixed(root);
}

std::unique_ptr<CrossSchema::MaterialAssetT> UnPackMaterialAsset(const void *buf,const flatbuffers::resolver_function_t *res) {
return std::unique_ptr<CrossSchema::MaterialAssetT>(GetMaterialAsset(buf)->UnPack(res));
}

std::unique_ptr<CrossSchema::MaterialAssetT> UnPackSizePrefixedMaterialAsset(const void *buf,const flatbuffers::resolver_function_t *res) {
return std::unique_ptr<CrossSchema::MaterialAssetT>(GetSizePrefixedMaterialAsset(buf)->UnPack(res));
}

}  // namespace CrossSchema
