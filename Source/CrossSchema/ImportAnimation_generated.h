// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_IMPORTANIMATION_CROSSSCHEMA_H_
#define FLATBUFFERS_GENERATED_IMPORTANIMATION_CROSSSCHEMA_H_

#include "flatbuffers/flatbuffers.h"

#include "CrossSchemaForward.h"
#include "BasicStruct_generated.h"
#include "ImportSkeleton_generated.h"

namespace CrossSchema {

struct Notify_ScriptImpl;
struct Notify_ScriptImplBuilder;
struct Notify_ScriptImplT;

struct Notify_JumpSectionScriptImpl;
struct Notify_JumpSectionScriptImplBuilder;
struct Notify_JumpSectionScriptImplT;

struct Notify_JumpSectionFormulaImpl;
struct Notify_JumpSectionFormulaImplBuilder;
struct Notify_JumpSectionFormulaImplT;

struct ImportAnimNotify;
struct ImportAnimNotifyBuilder;
struct ImportAnimNotifyT;

struct ImportAnimNotifyEvent;
struct ImportAnimNotifyEventBuilder;
struct ImportAnimNotifyEventT;

struct ImportAnimSyncMarker;
struct ImportAnimSyncMarkerBuilder;
struct ImportAnimSyncMarkerT;

struct ImportAnimTrackPerBone;
struct ImportAnimTrackPerBoneBuilder;
struct ImportAnimTrackPerBoneT;

struct ImportAnimation;
struct ImportAnimationBuilder;
struct ImportAnimationT;

inline const flatbuffers::TypeTable *Notify_ScriptImplTypeTable();

inline const flatbuffers::TypeTable *Notify_JumpSectionScriptImplTypeTable();

inline const flatbuffers::TypeTable *Notify_JumpSectionFormulaImplTypeTable();

inline const flatbuffers::TypeTable *ImportAnimNotifyTypeTable();

inline const flatbuffers::TypeTable *ImportAnimNotifyEventTypeTable();

inline const flatbuffers::TypeTable *ImportAnimSyncMarkerTypeTable();

inline const flatbuffers::TypeTable *ImportAnimTrackPerBoneTypeTable();

inline const flatbuffers::TypeTable *ImportAnimationTypeTable();

enum class CROSS_SCHEMA_API ImportAnimCompressType : int8_t {
  LinearReduction = 0,
  UniformSample = 1,
  Raw = 2,
  MIN = LinearReduction,
  MAX = Raw
};

inline const ImportAnimCompressType (&EnumValuesImportAnimCompressType())[3] {
  static const ImportAnimCompressType values[] = {
    ImportAnimCompressType::LinearReduction,
    ImportAnimCompressType::UniformSample,
    ImportAnimCompressType::Raw
  };
  return values;
}

inline const char * const *EnumNamesImportAnimCompressType() {
  static const char * const names[4] = {
    "LinearReduction",
    "UniformSample",
    "Raw",
    nullptr
  };
  return names;
}

inline const char *EnumNameImportAnimCompressType(ImportAnimCompressType e) {
  if (flatbuffers::IsOutRange(e, ImportAnimCompressType::LinearReduction, ImportAnimCompressType::Raw)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesImportAnimCompressType()[index];
}

enum class CROSS_SCHEMA_API ImportRootMotionLockType : int8_t {
  RefPose = 0,
  AnimFirstFrame = 1,
  Zero = 2,
  MIN = RefPose,
  MAX = Zero
};

inline const ImportRootMotionLockType (&EnumValuesImportRootMotionLockType())[3] {
  static const ImportRootMotionLockType values[] = {
    ImportRootMotionLockType::RefPose,
    ImportRootMotionLockType::AnimFirstFrame,
    ImportRootMotionLockType::Zero
  };
  return values;
}

inline const char * const *EnumNamesImportRootMotionLockType() {
  static const char * const names[4] = {
    "RefPose",
    "AnimFirstFrame",
    "Zero",
    nullptr
  };
  return names;
}

inline const char *EnumNameImportRootMotionLockType(ImportRootMotionLockType e) {
  if (flatbuffers::IsOutRange(e, ImportRootMotionLockType::RefPose, ImportRootMotionLockType::Zero)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesImportRootMotionLockType()[index];
}

enum class CROSS_SCHEMA_API ImportAdditiveSpaceType : int8_t {
  None = 0,
  LocalSpaceBase = 1,
  RotationOffsetRootSpace = 2,
  MIN = None,
  MAX = RotationOffsetRootSpace
};

inline const ImportAdditiveSpaceType (&EnumValuesImportAdditiveSpaceType())[3] {
  static const ImportAdditiveSpaceType values[] = {
    ImportAdditiveSpaceType::None,
    ImportAdditiveSpaceType::LocalSpaceBase,
    ImportAdditiveSpaceType::RotationOffsetRootSpace
  };
  return values;
}

inline const char * const *EnumNamesImportAdditiveSpaceType() {
  static const char * const names[4] = {
    "None",
    "LocalSpaceBase",
    "RotationOffsetRootSpace",
    nullptr
  };
  return names;
}

inline const char *EnumNameImportAdditiveSpaceType(ImportAdditiveSpaceType e) {
  if (flatbuffers::IsOutRange(e, ImportAdditiveSpaceType::None, ImportAdditiveSpaceType::RotationOffsetRootSpace)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesImportAdditiveSpaceType()[index];
}

enum class CROSS_SCHEMA_API ImportAdditiveBaseType : int8_t {
  None = 0,
  SkeletonRefPose = 1,
  AnimScaled = 2,
  AnimFrame = 3,
  MIN = None,
  MAX = AnimFrame
};

inline const ImportAdditiveBaseType (&EnumValuesImportAdditiveBaseType())[4] {
  static const ImportAdditiveBaseType values[] = {
    ImportAdditiveBaseType::None,
    ImportAdditiveBaseType::SkeletonRefPose,
    ImportAdditiveBaseType::AnimScaled,
    ImportAdditiveBaseType::AnimFrame
  };
  return values;
}

inline const char * const *EnumNamesImportAdditiveBaseType() {
  static const char * const names[5] = {
    "None",
    "SkeletonRefPose",
    "AnimScaled",
    "AnimFrame",
    nullptr
  };
  return names;
}

inline const char *EnumNameImportAdditiveBaseType(ImportAdditiveBaseType e) {
  if (flatbuffers::IsOutRange(e, ImportAdditiveBaseType::None, ImportAdditiveBaseType::AnimFrame)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesImportAdditiveBaseType()[index];
}

enum class CROSS_SCHEMA_API ImportAnimNotifyImpl : uint8_t {
  NONE = 0,
  Notify_ScriptImpl = 1,
  Notify_JumpSectionScriptImpl = 2,
  Notify_JumpSectionFormulaImpl = 3,
  MIN = NONE,
  MAX = Notify_JumpSectionFormulaImpl
};

inline const ImportAnimNotifyImpl (&EnumValuesImportAnimNotifyImpl())[4] {
  static const ImportAnimNotifyImpl values[] = {
    ImportAnimNotifyImpl::NONE,
    ImportAnimNotifyImpl::Notify_ScriptImpl,
    ImportAnimNotifyImpl::Notify_JumpSectionScriptImpl,
    ImportAnimNotifyImpl::Notify_JumpSectionFormulaImpl
  };
  return values;
}

inline const char * const *EnumNamesImportAnimNotifyImpl() {
  static const char * const names[5] = {
    "NONE",
    "Notify_ScriptImpl",
    "Notify_JumpSectionScriptImpl",
    "Notify_JumpSectionFormulaImpl",
    nullptr
  };
  return names;
}

inline const char *EnumNameImportAnimNotifyImpl(ImportAnimNotifyImpl e) {
  if (flatbuffers::IsOutRange(e, ImportAnimNotifyImpl::NONE, ImportAnimNotifyImpl::Notify_JumpSectionFormulaImpl)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesImportAnimNotifyImpl()[index];
}

template<typename T> struct ImportAnimNotifyImplTraits {
  static const ImportAnimNotifyImpl enum_value = ImportAnimNotifyImpl::NONE;
};

template<> struct ImportAnimNotifyImplTraits<CrossSchema::Notify_ScriptImpl> {
  static const ImportAnimNotifyImpl enum_value = ImportAnimNotifyImpl::Notify_ScriptImpl;
};

template<> struct ImportAnimNotifyImplTraits<CrossSchema::Notify_JumpSectionScriptImpl> {
  static const ImportAnimNotifyImpl enum_value = ImportAnimNotifyImpl::Notify_JumpSectionScriptImpl;
};

template<> struct ImportAnimNotifyImplTraits<CrossSchema::Notify_JumpSectionFormulaImpl> {
  static const ImportAnimNotifyImpl enum_value = ImportAnimNotifyImpl::Notify_JumpSectionFormulaImpl;
};

struct CROSS_SCHEMA_API ImportAnimNotifyImplUnion {
  ImportAnimNotifyImpl type;
  void *value;

  ImportAnimNotifyImplUnion() : type(ImportAnimNotifyImpl::NONE), value(nullptr) {}
  ImportAnimNotifyImplUnion(ImportAnimNotifyImplUnion&& u) FLATBUFFERS_NOEXCEPT :
    type(ImportAnimNotifyImpl::NONE), value(nullptr)
    { std::swap(type, u.type); std::swap(value, u.value); }
  ImportAnimNotifyImplUnion(const ImportAnimNotifyImplUnion &);
  ImportAnimNotifyImplUnion &operator=(const ImportAnimNotifyImplUnion &u)
    { ImportAnimNotifyImplUnion t(u); std::swap(type, t.type); std::swap(value, t.value); return *this; }
  ImportAnimNotifyImplUnion &operator=(ImportAnimNotifyImplUnion &&u) FLATBUFFERS_NOEXCEPT
    { std::swap(type, u.type); std::swap(value, u.value); return *this; }
  ~ImportAnimNotifyImplUnion() { Reset(); }

  void Reset();

#ifndef FLATBUFFERS_CPP98_STL
  template <typename T>
  void Set(T&& val) {
    using RT = typename std::remove_reference<T>::type;
    Reset();
    type = ImportAnimNotifyImplTraits<typename RT::TableType>::enum_value;
    if (type != ImportAnimNotifyImpl::NONE) {
      value = new RT(std::forward<T>(val));
    }
  }
#endif  // FLATBUFFERS_CPP98_STL

  static void *UnPack(const void *obj, ImportAnimNotifyImpl type, const flatbuffers::resolver_function_t *resolver);
  flatbuffers::Offset<void> Pack(flatbuffers::FlatBufferBuilder &_fbb, const flatbuffers::rehasher_function_t *_rehasher = nullptr) const;

  CrossSchema::Notify_ScriptImplT *AsNotify_ScriptImpl() {
    return type == ImportAnimNotifyImpl::Notify_ScriptImpl ?
      reinterpret_cast<CrossSchema::Notify_ScriptImplT *>(value) : nullptr;
  }
  const CrossSchema::Notify_ScriptImplT *AsNotify_ScriptImpl() const {
    return type == ImportAnimNotifyImpl::Notify_ScriptImpl ?
      reinterpret_cast<const CrossSchema::Notify_ScriptImplT *>(value) : nullptr;
  }
  CrossSchema::Notify_JumpSectionScriptImplT *AsNotify_JumpSectionScriptImpl() {
    return type == ImportAnimNotifyImpl::Notify_JumpSectionScriptImpl ?
      reinterpret_cast<CrossSchema::Notify_JumpSectionScriptImplT *>(value) : nullptr;
  }
  const CrossSchema::Notify_JumpSectionScriptImplT *AsNotify_JumpSectionScriptImpl() const {
    return type == ImportAnimNotifyImpl::Notify_JumpSectionScriptImpl ?
      reinterpret_cast<const CrossSchema::Notify_JumpSectionScriptImplT *>(value) : nullptr;
  }
  CrossSchema::Notify_JumpSectionFormulaImplT *AsNotify_JumpSectionFormulaImpl() {
    return type == ImportAnimNotifyImpl::Notify_JumpSectionFormulaImpl ?
      reinterpret_cast<CrossSchema::Notify_JumpSectionFormulaImplT *>(value) : nullptr;
  }
  const CrossSchema::Notify_JumpSectionFormulaImplT *AsNotify_JumpSectionFormulaImpl() const {
    return type == ImportAnimNotifyImpl::Notify_JumpSectionFormulaImpl ?
      reinterpret_cast<const CrossSchema::Notify_JumpSectionFormulaImplT *>(value) : nullptr;
  }
};

bool VerifyImportAnimNotifyImpl(flatbuffers::Verifier &verifier, const void *obj, ImportAnimNotifyImpl type);
bool VerifyImportAnimNotifyImplVector(flatbuffers::Verifier &verifier, const flatbuffers::Vector<flatbuffers::Offset<void>> *values, const flatbuffers::Vector<uint8_t> *types);

struct Notify_ScriptImplT : public flatbuffers::NativeTable {
  typedef Notify_ScriptImpl TableType;
  std::string callback_str{};
};

struct Notify_ScriptImpl FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef Notify_ScriptImplT NativeTableType;
  typedef Notify_ScriptImplBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_CALLBACK_STR = 4
  };
CROSS_SCHEMA_API  const flatbuffers::String *callback_str() const;
  CROSS_SCHEMA_API  flatbuffers::String *mutable_callback_str();
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API Notify_ScriptImplT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(Notify_ScriptImplT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<Notify_ScriptImpl> Pack(flatbuffers::FlatBufferBuilder &_fbb, const Notify_ScriptImplT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API Notify_ScriptImplBuilder {
  typedef Notify_ScriptImpl Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_callback_str(flatbuffers::Offset<flatbuffers::String> callback_str);
  explicit Notify_ScriptImplBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<Notify_ScriptImpl> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<Notify_ScriptImpl>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<Notify_ScriptImpl> CreateNotify_ScriptImpl(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> callback_str = 0);
struct Notify_ScriptImpl::Traits {
  using type = Notify_ScriptImpl;
  static auto constexpr Create = CreateNotify_ScriptImpl;
};

CROSS_SCHEMA_API flatbuffers::Offset<Notify_ScriptImpl> CreateNotify_ScriptImplDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *callback_str = nullptr);
CROSS_SCHEMA_API flatbuffers::Offset<Notify_ScriptImpl> CreateNotify_ScriptImpl(flatbuffers::FlatBufferBuilder &_fbb, const Notify_ScriptImplT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct Notify_JumpSectionScriptImplT : public flatbuffers::NativeTable {
  typedef Notify_JumpSectionScriptImpl TableType;
  std::string callback_str{};
};

struct Notify_JumpSectionScriptImpl FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef Notify_JumpSectionScriptImplT NativeTableType;
  typedef Notify_JumpSectionScriptImplBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_CALLBACK_STR = 4
  };
CROSS_SCHEMA_API  const flatbuffers::String *callback_str() const;
  CROSS_SCHEMA_API  flatbuffers::String *mutable_callback_str();
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API Notify_JumpSectionScriptImplT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(Notify_JumpSectionScriptImplT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<Notify_JumpSectionScriptImpl> Pack(flatbuffers::FlatBufferBuilder &_fbb, const Notify_JumpSectionScriptImplT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API Notify_JumpSectionScriptImplBuilder {
  typedef Notify_JumpSectionScriptImpl Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_callback_str(flatbuffers::Offset<flatbuffers::String> callback_str);
  explicit Notify_JumpSectionScriptImplBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<Notify_JumpSectionScriptImpl> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<Notify_JumpSectionScriptImpl>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<Notify_JumpSectionScriptImpl> CreateNotify_JumpSectionScriptImpl(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> callback_str = 0);
struct Notify_JumpSectionScriptImpl::Traits {
  using type = Notify_JumpSectionScriptImpl;
  static auto constexpr Create = CreateNotify_JumpSectionScriptImpl;
};

CROSS_SCHEMA_API flatbuffers::Offset<Notify_JumpSectionScriptImpl> CreateNotify_JumpSectionScriptImplDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *callback_str = nullptr);
CROSS_SCHEMA_API flatbuffers::Offset<Notify_JumpSectionScriptImpl> CreateNotify_JumpSectionScriptImpl(flatbuffers::FlatBufferBuilder &_fbb, const Notify_JumpSectionScriptImplT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct Notify_JumpSectionFormulaImplT : public flatbuffers::NativeTable {
  typedef Notify_JumpSectionFormulaImpl TableType;
  std::string express_str{};
};

struct Notify_JumpSectionFormulaImpl FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef Notify_JumpSectionFormulaImplT NativeTableType;
  typedef Notify_JumpSectionFormulaImplBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_EXPRESS_STR = 4
  };
CROSS_SCHEMA_API  const flatbuffers::String *express_str() const;
  CROSS_SCHEMA_API  flatbuffers::String *mutable_express_str();
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API Notify_JumpSectionFormulaImplT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(Notify_JumpSectionFormulaImplT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<Notify_JumpSectionFormulaImpl> Pack(flatbuffers::FlatBufferBuilder &_fbb, const Notify_JumpSectionFormulaImplT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API Notify_JumpSectionFormulaImplBuilder {
  typedef Notify_JumpSectionFormulaImpl Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_express_str(flatbuffers::Offset<flatbuffers::String> express_str);
  explicit Notify_JumpSectionFormulaImplBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<Notify_JumpSectionFormulaImpl> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<Notify_JumpSectionFormulaImpl>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<Notify_JumpSectionFormulaImpl> CreateNotify_JumpSectionFormulaImpl(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> express_str = 0);
struct Notify_JumpSectionFormulaImpl::Traits {
  using type = Notify_JumpSectionFormulaImpl;
  static auto constexpr Create = CreateNotify_JumpSectionFormulaImpl;
};

CROSS_SCHEMA_API flatbuffers::Offset<Notify_JumpSectionFormulaImpl> CreateNotify_JumpSectionFormulaImplDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *express_str = nullptr);
CROSS_SCHEMA_API flatbuffers::Offset<Notify_JumpSectionFormulaImpl> CreateNotify_JumpSectionFormulaImpl(flatbuffers::FlatBufferBuilder &_fbb, const Notify_JumpSectionFormulaImplT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct ImportAnimNotifyT : public flatbuffers::NativeTable {
  typedef ImportAnimNotify TableType;
  std::string notify_type{};
  CrossSchema::ImportAnimNotifyImplUnion notify_impl{};
};

struct ImportAnimNotify FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef ImportAnimNotifyT NativeTableType;
  typedef ImportAnimNotifyBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NOTIFY_TYPE = 4,
    VT_NOTIFY_IMPL_TYPE = 6,
    VT_NOTIFY_IMPL = 8
  };
CROSS_SCHEMA_API  const flatbuffers::String *notify_type() const;
  CROSS_SCHEMA_API  flatbuffers::String *mutable_notify_type();
CROSS_SCHEMA_API  CrossSchema::ImportAnimNotifyImpl notify_impl_type() const;
CROSS_SCHEMA_API  const void *notify_impl() const;
  template<typename T>    CROSS_SCHEMA_API  const T *notify_impl_as() const;
 CROSS_SCHEMA_API  const CrossSchema::Notify_ScriptImpl * notify_impl_as_Notify_ScriptImpl() const;
 CROSS_SCHEMA_API  const CrossSchema::Notify_JumpSectionScriptImpl * notify_impl_as_Notify_JumpSectionScriptImpl() const;
 CROSS_SCHEMA_API  const CrossSchema::Notify_JumpSectionFormulaImpl * notify_impl_as_Notify_JumpSectionFormulaImpl() const;
  CROSS_SCHEMA_API  void *mutable_notify_impl();
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API ImportAnimNotifyT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(ImportAnimNotifyT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<ImportAnimNotify> Pack(flatbuffers::FlatBufferBuilder &_fbb, const ImportAnimNotifyT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API ImportAnimNotifyBuilder {
  typedef ImportAnimNotify Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_notify_type(flatbuffers::Offset<flatbuffers::String> notify_type);
  void add_notify_impl_type(CrossSchema::ImportAnimNotifyImpl notify_impl_type);
  void add_notify_impl(flatbuffers::Offset<void> notify_impl);
  explicit ImportAnimNotifyBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<ImportAnimNotify> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<ImportAnimNotify>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<ImportAnimNotify> CreateImportAnimNotify(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> notify_type = 0,
    CrossSchema::ImportAnimNotifyImpl notify_impl_type = CrossSchema::ImportAnimNotifyImpl::NONE,
    flatbuffers::Offset<void> notify_impl = 0);
struct ImportAnimNotify::Traits {
  using type = ImportAnimNotify;
  static auto constexpr Create = CreateImportAnimNotify;
};

CROSS_SCHEMA_API flatbuffers::Offset<ImportAnimNotify> CreateImportAnimNotifyDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *notify_type = nullptr,
    CrossSchema::ImportAnimNotifyImpl notify_impl_type = CrossSchema::ImportAnimNotifyImpl::NONE,
    flatbuffers::Offset<void> notify_impl = 0);
CROSS_SCHEMA_API flatbuffers::Offset<ImportAnimNotify> CreateImportAnimNotify(flatbuffers::FlatBufferBuilder &_fbb, const ImportAnimNotifyT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct ImportAnimNotifyEventT : public flatbuffers::NativeTable {
  typedef ImportAnimNotifyEvent TableType;
  float trigger_time = 0.0f;
  float end_trigger_time = 0.0f;
  float trigger_weight_thres = 0.0f;
  int32_t lod_thres = 0;
  std::string name{};
  std::unique_ptr<CrossSchema::ImportAnimNotifyT> notify{};
};

struct ImportAnimNotifyEvent FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef ImportAnimNotifyEventT NativeTableType;
  typedef ImportAnimNotifyEventBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TRIGGER_TIME = 4,
    VT_END_TRIGGER_TIME = 6,
    VT_TRIGGER_WEIGHT_THRES = 8,
    VT_LOD_THRES = 10,
    VT_NAME = 12,
    VT_NOTIFY = 14
  };
CROSS_SCHEMA_API  float trigger_time() const;
  CROSS_SCHEMA_API  bool mutate_trigger_time(float _trigger_time);
CROSS_SCHEMA_API  float end_trigger_time() const;
  CROSS_SCHEMA_API  bool mutate_end_trigger_time(float _end_trigger_time);
CROSS_SCHEMA_API  float trigger_weight_thres() const;
  CROSS_SCHEMA_API  bool mutate_trigger_weight_thres(float _trigger_weight_thres);
CROSS_SCHEMA_API  int32_t lod_thres() const;
  CROSS_SCHEMA_API  bool mutate_lod_thres(int32_t _lod_thres);
CROSS_SCHEMA_API  const flatbuffers::String *name() const;
  CROSS_SCHEMA_API  flatbuffers::String *mutable_name();
CROSS_SCHEMA_API  const CrossSchema::ImportAnimNotify *notify() const;
  CROSS_SCHEMA_API  CrossSchema::ImportAnimNotify *mutable_notify();
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API ImportAnimNotifyEventT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(ImportAnimNotifyEventT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<ImportAnimNotifyEvent> Pack(flatbuffers::FlatBufferBuilder &_fbb, const ImportAnimNotifyEventT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API ImportAnimNotifyEventBuilder {
  typedef ImportAnimNotifyEvent Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_trigger_time(float trigger_time);
  void add_end_trigger_time(float end_trigger_time);
  void add_trigger_weight_thres(float trigger_weight_thres);
  void add_lod_thres(int32_t lod_thres);
  void add_name(flatbuffers::Offset<flatbuffers::String> name);
  void add_notify(flatbuffers::Offset<CrossSchema::ImportAnimNotify> notify);
  explicit ImportAnimNotifyEventBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<ImportAnimNotifyEvent> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<ImportAnimNotifyEvent>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<ImportAnimNotifyEvent> CreateImportAnimNotifyEvent(
    flatbuffers::FlatBufferBuilder &_fbb,
    float trigger_time = 0.0f,
    float end_trigger_time = 0.0f,
    float trigger_weight_thres = 0.0f,
    int32_t lod_thres = 0,
    flatbuffers::Offset<flatbuffers::String> name = 0,
    flatbuffers::Offset<CrossSchema::ImportAnimNotify> notify = 0);
struct ImportAnimNotifyEvent::Traits {
  using type = ImportAnimNotifyEvent;
  static auto constexpr Create = CreateImportAnimNotifyEvent;
};

CROSS_SCHEMA_API flatbuffers::Offset<ImportAnimNotifyEvent> CreateImportAnimNotifyEventDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    float trigger_time = 0.0f,
    float end_trigger_time = 0.0f,
    float trigger_weight_thres = 0.0f,
    int32_t lod_thres = 0,
    const char *name = nullptr,
    flatbuffers::Offset<CrossSchema::ImportAnimNotify> notify = 0);
CROSS_SCHEMA_API flatbuffers::Offset<ImportAnimNotifyEvent> CreateImportAnimNotifyEvent(flatbuffers::FlatBufferBuilder &_fbb, const ImportAnimNotifyEventT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct ImportAnimSyncMarkerT : public flatbuffers::NativeTable {
  typedef ImportAnimSyncMarker TableType;
  std::string marker_name{};
  float time = 0.0f;
};

struct ImportAnimSyncMarker FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef ImportAnimSyncMarkerT NativeTableType;
  typedef ImportAnimSyncMarkerBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MARKER_NAME = 4,
    VT_TIME = 6
  };
CROSS_SCHEMA_API  const flatbuffers::String *marker_name() const;
  CROSS_SCHEMA_API  flatbuffers::String *mutable_marker_name();
CROSS_SCHEMA_API  float time() const;
  CROSS_SCHEMA_API  bool mutate_time(float _time);
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API ImportAnimSyncMarkerT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(ImportAnimSyncMarkerT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<ImportAnimSyncMarker> Pack(flatbuffers::FlatBufferBuilder &_fbb, const ImportAnimSyncMarkerT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API ImportAnimSyncMarkerBuilder {
  typedef ImportAnimSyncMarker Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_marker_name(flatbuffers::Offset<flatbuffers::String> marker_name);
  void add_time(float time);
  explicit ImportAnimSyncMarkerBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<ImportAnimSyncMarker> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<ImportAnimSyncMarker>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<ImportAnimSyncMarker> CreateImportAnimSyncMarker(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> marker_name = 0,
    float time = 0.0f);
struct ImportAnimSyncMarker::Traits {
  using type = ImportAnimSyncMarker;
  static auto constexpr Create = CreateImportAnimSyncMarker;
};

CROSS_SCHEMA_API flatbuffers::Offset<ImportAnimSyncMarker> CreateImportAnimSyncMarkerDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *marker_name = nullptr,
    float time = 0.0f);
CROSS_SCHEMA_API flatbuffers::Offset<ImportAnimSyncMarker> CreateImportAnimSyncMarker(flatbuffers::FlatBufferBuilder &_fbb, const ImportAnimSyncMarkerT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct ImportAnimTrackPerBoneT : public flatbuffers::NativeTable {
  typedef ImportAnimTrackPerBone TableType;
  std::vector<CrossSchema::float3> pos{};
  std::vector<float> pos_t{};
  std::vector<CrossSchema::float4> rot{};
  std::vector<float> rot_t{};
  std::vector<CrossSchema::float3> scl{};
  std::vector<float> scl_t{};
};

struct ImportAnimTrackPerBone FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef ImportAnimTrackPerBoneT NativeTableType;
  typedef ImportAnimTrackPerBoneBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_POS = 4,
    VT_POS_T = 6,
    VT_ROT = 8,
    VT_ROT_T = 10,
    VT_SCL = 12,
    VT_SCL_T = 14
  };
CROSS_SCHEMA_API  const flatbuffers::Vector<const CrossSchema::float3 *> *pos() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<const CrossSchema::float3 *> *mutable_pos();
CROSS_SCHEMA_API  const flatbuffers::Vector<float> *pos_t() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<float> *mutable_pos_t();
CROSS_SCHEMA_API  const flatbuffers::Vector<const CrossSchema::float4 *> *rot() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<const CrossSchema::float4 *> *mutable_rot();
CROSS_SCHEMA_API  const flatbuffers::Vector<float> *rot_t() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<float> *mutable_rot_t();
CROSS_SCHEMA_API  const flatbuffers::Vector<const CrossSchema::float3 *> *scl() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<const CrossSchema::float3 *> *mutable_scl();
CROSS_SCHEMA_API  const flatbuffers::Vector<float> *scl_t() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<float> *mutable_scl_t();
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API ImportAnimTrackPerBoneT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(ImportAnimTrackPerBoneT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<ImportAnimTrackPerBone> Pack(flatbuffers::FlatBufferBuilder &_fbb, const ImportAnimTrackPerBoneT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API ImportAnimTrackPerBoneBuilder {
  typedef ImportAnimTrackPerBone Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_pos(flatbuffers::Offset<flatbuffers::Vector<const CrossSchema::float3 *>> pos);
  void add_pos_t(flatbuffers::Offset<flatbuffers::Vector<float>> pos_t);
  void add_rot(flatbuffers::Offset<flatbuffers::Vector<const CrossSchema::float4 *>> rot);
  void add_rot_t(flatbuffers::Offset<flatbuffers::Vector<float>> rot_t);
  void add_scl(flatbuffers::Offset<flatbuffers::Vector<const CrossSchema::float3 *>> scl);
  void add_scl_t(flatbuffers::Offset<flatbuffers::Vector<float>> scl_t);
  explicit ImportAnimTrackPerBoneBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<ImportAnimTrackPerBone> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<ImportAnimTrackPerBone>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<ImportAnimTrackPerBone> CreateImportAnimTrackPerBone(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<const CrossSchema::float3 *>> pos = 0,
    flatbuffers::Offset<flatbuffers::Vector<float>> pos_t = 0,
    flatbuffers::Offset<flatbuffers::Vector<const CrossSchema::float4 *>> rot = 0,
    flatbuffers::Offset<flatbuffers::Vector<float>> rot_t = 0,
    flatbuffers::Offset<flatbuffers::Vector<const CrossSchema::float3 *>> scl = 0,
    flatbuffers::Offset<flatbuffers::Vector<float>> scl_t = 0);
struct ImportAnimTrackPerBone::Traits {
  using type = ImportAnimTrackPerBone;
  static auto constexpr Create = CreateImportAnimTrackPerBone;
};

CROSS_SCHEMA_API flatbuffers::Offset<ImportAnimTrackPerBone> CreateImportAnimTrackPerBoneDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<CrossSchema::float3> *pos = nullptr,
    const std::vector<float> *pos_t = nullptr,
    const std::vector<CrossSchema::float4> *rot = nullptr,
    const std::vector<float> *rot_t = nullptr,
    const std::vector<CrossSchema::float3> *scl = nullptr,
    const std::vector<float> *scl_t = nullptr);
CROSS_SCHEMA_API flatbuffers::Offset<ImportAnimTrackPerBone> CreateImportAnimTrackPerBone(flatbuffers::FlatBufferBuilder &_fbb, const ImportAnimTrackPerBoneT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct ImportAnimationT : public flatbuffers::NativeTable {
  typedef ImportAnimation TableType;
  std::string name{};
  std::string ref_path{};
  float duration_sec = 0.0f;
  int32_t frame_num = 0;
  int32_t skelt_num = 0;
  CrossSchema::ImportAnimCompressType cpr_type = CrossSchema::ImportAnimCompressType::LinearReduction;
  std::unique_ptr<CrossSchema::ImportRefSkeletonT> ref_skelt{};
  std::vector<std::unique_ptr<CrossSchema::ImportAnimTrackPerBoneT>> tracks_ani_buffer{};
  std::vector<int8_t> cpr_ani_buffer{};
  std::vector<std::unique_ptr<CrossSchema::ImportAnimNotifyEventT>> notifies{};
  std::vector<std::unique_ptr<CrossSchema::ImportAnimSyncMarkerT>> sync_markers{};
  bool has_rootmotion = false;
  CrossSchema::ImportRootMotionLockType root_lock_type = CrossSchema::ImportRootMotionLockType::AnimFirstFrame;
  std::string additive_path{};
  CrossSchema::ImportAdditiveSpaceType additive_animspace = CrossSchema::ImportAdditiveSpaceType::None;
  CrossSchema::ImportAdditiveBaseType additive_basepose = CrossSchema::ImportAdditiveBaseType::None;
  int32_t additive_baseframe = 0;
  std::vector<uint8_t> curve_set{};
};

struct ImportAnimation FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef ImportAnimationT NativeTableType;
  typedef ImportAnimationBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NAME = 4,
    VT_REF_PATH = 6,
    VT_DURATION_SEC = 8,
    VT_FRAME_NUM = 10,
    VT_SKELT_NUM = 12,
    VT_CPR_TYPE = 14,
    VT_REF_SKELT = 16,
    VT_TRACKS_ANI_BUFFER = 18,
    VT_CPR_ANI_BUFFER = 20,
    VT_NOTIFIES = 22,
    VT_SYNC_MARKERS = 24,
    VT_HAS_ROOTMOTION = 26,
    VT_ROOT_LOCK_TYPE = 28,
    VT_ADDITIVE_PATH = 30,
    VT_ADDITIVE_ANIMSPACE = 32,
    VT_ADDITIVE_BASEPOSE = 34,
    VT_ADDITIVE_BASEFRAME = 36,
    VT_CURVE_SET = 38
  };
CROSS_SCHEMA_API  const flatbuffers::String *name() const;
  CROSS_SCHEMA_API  flatbuffers::String *mutable_name();
CROSS_SCHEMA_API  const flatbuffers::String *ref_path() const;
  CROSS_SCHEMA_API  flatbuffers::String *mutable_ref_path();
CROSS_SCHEMA_API  float duration_sec() const;
  CROSS_SCHEMA_API  bool mutate_duration_sec(float _duration_sec);
CROSS_SCHEMA_API  int32_t frame_num() const;
  CROSS_SCHEMA_API  bool mutate_frame_num(int32_t _frame_num);
CROSS_SCHEMA_API  int32_t skelt_num() const;
  CROSS_SCHEMA_API  bool mutate_skelt_num(int32_t _skelt_num);
CROSS_SCHEMA_API  CrossSchema::ImportAnimCompressType cpr_type() const;
  CROSS_SCHEMA_API  bool mutate_cpr_type(CrossSchema::ImportAnimCompressType _cpr_type);
CROSS_SCHEMA_API  const CrossSchema::ImportRefSkeleton *ref_skelt() const;
  CROSS_SCHEMA_API  CrossSchema::ImportRefSkeleton *mutable_ref_skelt();
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportAnimTrackPerBone>> *tracks_ani_buffer() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportAnimTrackPerBone>> *mutable_tracks_ani_buffer();
CROSS_SCHEMA_API  const flatbuffers::Vector<int8_t> *cpr_ani_buffer() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<int8_t> *mutable_cpr_ani_buffer();
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportAnimNotifyEvent>> *notifies() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportAnimNotifyEvent>> *mutable_notifies();
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportAnimSyncMarker>> *sync_markers() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportAnimSyncMarker>> *mutable_sync_markers();
CROSS_SCHEMA_API  bool has_rootmotion() const;
  CROSS_SCHEMA_API  bool mutate_has_rootmotion(bool _has_rootmotion);
CROSS_SCHEMA_API  CrossSchema::ImportRootMotionLockType root_lock_type() const;
  CROSS_SCHEMA_API  bool mutate_root_lock_type(CrossSchema::ImportRootMotionLockType _root_lock_type);
CROSS_SCHEMA_API  const flatbuffers::String *additive_path() const;
  CROSS_SCHEMA_API  flatbuffers::String *mutable_additive_path();
CROSS_SCHEMA_API  CrossSchema::ImportAdditiveSpaceType additive_animspace() const;
  CROSS_SCHEMA_API  bool mutate_additive_animspace(CrossSchema::ImportAdditiveSpaceType _additive_animspace);
CROSS_SCHEMA_API  CrossSchema::ImportAdditiveBaseType additive_basepose() const;
  CROSS_SCHEMA_API  bool mutate_additive_basepose(CrossSchema::ImportAdditiveBaseType _additive_basepose);
CROSS_SCHEMA_API  int32_t additive_baseframe() const;
  CROSS_SCHEMA_API  bool mutate_additive_baseframe(int32_t _additive_baseframe);
CROSS_SCHEMA_API  const flatbuffers::Vector<uint8_t> *curve_set() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<uint8_t> *mutable_curve_set();
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API ImportAnimationT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(ImportAnimationT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<ImportAnimation> Pack(flatbuffers::FlatBufferBuilder &_fbb, const ImportAnimationT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API ImportAnimationBuilder {
  typedef ImportAnimation Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_name(flatbuffers::Offset<flatbuffers::String> name);
  void add_ref_path(flatbuffers::Offset<flatbuffers::String> ref_path);
  void add_duration_sec(float duration_sec);
  void add_frame_num(int32_t frame_num);
  void add_skelt_num(int32_t skelt_num);
  void add_cpr_type(CrossSchema::ImportAnimCompressType cpr_type);
  void add_ref_skelt(flatbuffers::Offset<CrossSchema::ImportRefSkeleton> ref_skelt);
  void add_tracks_ani_buffer(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportAnimTrackPerBone>>> tracks_ani_buffer);
  void add_cpr_ani_buffer(flatbuffers::Offset<flatbuffers::Vector<int8_t>> cpr_ani_buffer);
  void add_notifies(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportAnimNotifyEvent>>> notifies);
  void add_sync_markers(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportAnimSyncMarker>>> sync_markers);
  void add_has_rootmotion(bool has_rootmotion);
  void add_root_lock_type(CrossSchema::ImportRootMotionLockType root_lock_type);
  void add_additive_path(flatbuffers::Offset<flatbuffers::String> additive_path);
  void add_additive_animspace(CrossSchema::ImportAdditiveSpaceType additive_animspace);
  void add_additive_basepose(CrossSchema::ImportAdditiveBaseType additive_basepose);
  void add_additive_baseframe(int32_t additive_baseframe);
  void add_curve_set(flatbuffers::Offset<flatbuffers::Vector<uint8_t>> curve_set);
  explicit ImportAnimationBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<ImportAnimation> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<ImportAnimation>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<ImportAnimation> CreateImportAnimation(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> name = 0,
    flatbuffers::Offset<flatbuffers::String> ref_path = 0,
    float duration_sec = 0.0f,
    int32_t frame_num = 0,
    int32_t skelt_num = 0,
    CrossSchema::ImportAnimCompressType cpr_type = CrossSchema::ImportAnimCompressType::LinearReduction,
    flatbuffers::Offset<CrossSchema::ImportRefSkeleton> ref_skelt = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportAnimTrackPerBone>>> tracks_ani_buffer = 0,
    flatbuffers::Offset<flatbuffers::Vector<int8_t>> cpr_ani_buffer = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportAnimNotifyEvent>>> notifies = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportAnimSyncMarker>>> sync_markers = 0,
    bool has_rootmotion = false,
    CrossSchema::ImportRootMotionLockType root_lock_type = CrossSchema::ImportRootMotionLockType::AnimFirstFrame,
    flatbuffers::Offset<flatbuffers::String> additive_path = 0,
    CrossSchema::ImportAdditiveSpaceType additive_animspace = CrossSchema::ImportAdditiveSpaceType::None,
    CrossSchema::ImportAdditiveBaseType additive_basepose = CrossSchema::ImportAdditiveBaseType::None,
    int32_t additive_baseframe = 0,
    flatbuffers::Offset<flatbuffers::Vector<uint8_t>> curve_set = 0);
struct ImportAnimation::Traits {
  using type = ImportAnimation;
  static auto constexpr Create = CreateImportAnimation;
};

CROSS_SCHEMA_API flatbuffers::Offset<ImportAnimation> CreateImportAnimationDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *name = nullptr,
    const char *ref_path = nullptr,
    float duration_sec = 0.0f,
    int32_t frame_num = 0,
    int32_t skelt_num = 0,
    CrossSchema::ImportAnimCompressType cpr_type = CrossSchema::ImportAnimCompressType::LinearReduction,
    flatbuffers::Offset<CrossSchema::ImportRefSkeleton> ref_skelt = 0,
    const std::vector<flatbuffers::Offset<CrossSchema::ImportAnimTrackPerBone>> *tracks_ani_buffer = nullptr,
    const std::vector<int8_t> *cpr_ani_buffer = nullptr,
    const std::vector<flatbuffers::Offset<CrossSchema::ImportAnimNotifyEvent>> *notifies = nullptr,
    const std::vector<flatbuffers::Offset<CrossSchema::ImportAnimSyncMarker>> *sync_markers = nullptr,
    bool has_rootmotion = false,
    CrossSchema::ImportRootMotionLockType root_lock_type = CrossSchema::ImportRootMotionLockType::AnimFirstFrame,
    const char *additive_path = nullptr,
    CrossSchema::ImportAdditiveSpaceType additive_animspace = CrossSchema::ImportAdditiveSpaceType::None,
    CrossSchema::ImportAdditiveBaseType additive_basepose = CrossSchema::ImportAdditiveBaseType::None,
    int32_t additive_baseframe = 0,
    const std::vector<uint8_t> *curve_set = nullptr);
CROSS_SCHEMA_API flatbuffers::Offset<ImportAnimation> CreateImportAnimation(flatbuffers::FlatBufferBuilder &_fbb, const ImportAnimationT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

inline const flatbuffers::TypeTable *ImportAnimCompressTypeTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::ImportAnimCompressTypeTypeTable
  };
  static const char * const names[] = {
    "LinearReduction",
    "UniformSample",
    "Raw"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_ENUM, 3, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ImportRootMotionLockTypeTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::ImportRootMotionLockTypeTypeTable
  };
  static const char * const names[] = {
    "RefPose",
    "AnimFirstFrame",
    "Zero"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_ENUM, 3, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ImportAdditiveSpaceTypeTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::ImportAdditiveSpaceTypeTypeTable
  };
  static const char * const names[] = {
    "None",
    "LocalSpaceBase",
    "RotationOffsetRootSpace"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_ENUM, 3, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ImportAdditiveBaseTypeTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_CHAR, 0, 0 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::ImportAdditiveBaseTypeTypeTable
  };
  static const char * const names[] = {
    "None",
    "SkeletonRefPose",
    "AnimScaled",
    "AnimFrame"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_ENUM, 4, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ImportAnimNotifyImplTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_SEQUENCE, 0, -1 },
    { flatbuffers::ET_SEQUENCE, 0, 0 },
    { flatbuffers::ET_SEQUENCE, 0, 1 },
    { flatbuffers::ET_SEQUENCE, 0, 2 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::Notify_ScriptImplTypeTable,
    CrossSchema::Notify_JumpSectionScriptImplTypeTable,
    CrossSchema::Notify_JumpSectionFormulaImplTypeTable
  };
  static const char * const names[] = {
    "NONE",
    "Notify_ScriptImpl",
    "Notify_JumpSectionScriptImpl",
    "Notify_JumpSectionFormulaImpl"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_UNION, 4, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *Notify_ScriptImplTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_STRING, 0, -1 }
  };
  static const char * const names[] = {
    "callback_str"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 1, type_codes, nullptr, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *Notify_JumpSectionScriptImplTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_STRING, 0, -1 }
  };
  static const char * const names[] = {
    "callback_str"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 1, type_codes, nullptr, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *Notify_JumpSectionFormulaImplTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_STRING, 0, -1 }
  };
  static const char * const names[] = {
    "express_str"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 1, type_codes, nullptr, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ImportAnimNotifyTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_STRING, 0, -1 },
    { flatbuffers::ET_UTYPE, 0, 0 },
    { flatbuffers::ET_SEQUENCE, 0, 0 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::ImportAnimNotifyImplTypeTable
  };
  static const char * const names[] = {
    "notify_type",
    "notify_impl_type",
    "notify_impl"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 3, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ImportAnimNotifyEventTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_FLOAT, 0, -1 },
    { flatbuffers::ET_FLOAT, 0, -1 },
    { flatbuffers::ET_FLOAT, 0, -1 },
    { flatbuffers::ET_INT, 0, -1 },
    { flatbuffers::ET_STRING, 0, -1 },
    { flatbuffers::ET_SEQUENCE, 0, 0 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::ImportAnimNotifyTypeTable
  };
  static const char * const names[] = {
    "trigger_time",
    "end_trigger_time",
    "trigger_weight_thres",
    "lod_thres",
    "name",
    "notify"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 6, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ImportAnimSyncMarkerTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_STRING, 0, -1 },
    { flatbuffers::ET_FLOAT, 0, -1 }
  };
  static const char * const names[] = {
    "marker_name",
    "time"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 2, type_codes, nullptr, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ImportAnimTrackPerBoneTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_SEQUENCE, 1, 0 },
    { flatbuffers::ET_FLOAT, 1, -1 },
    { flatbuffers::ET_SEQUENCE, 1, 1 },
    { flatbuffers::ET_FLOAT, 1, -1 },
    { flatbuffers::ET_SEQUENCE, 1, 0 },
    { flatbuffers::ET_FLOAT, 1, -1 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::float3TypeTable,
    CrossSchema::float4TypeTable
  };
  static const char * const names[] = {
    "pos",
    "pos_t",
    "rot",
    "rot_t",
    "scl",
    "scl_t"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 6, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ImportAnimationTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_STRING, 0, -1 },
    { flatbuffers::ET_STRING, 0, -1 },
    { flatbuffers::ET_FLOAT, 0, -1 },
    { flatbuffers::ET_INT, 0, -1 },
    { flatbuffers::ET_INT, 0, -1 },
    { flatbuffers::ET_CHAR, 0, 0 },
    { flatbuffers::ET_SEQUENCE, 0, 1 },
    { flatbuffers::ET_SEQUENCE, 1, 2 },
    { flatbuffers::ET_CHAR, 1, -1 },
    { flatbuffers::ET_SEQUENCE, 1, 3 },
    { flatbuffers::ET_SEQUENCE, 1, 4 },
    { flatbuffers::ET_BOOL, 0, -1 },
    { flatbuffers::ET_CHAR, 0, 5 },
    { flatbuffers::ET_STRING, 0, -1 },
    { flatbuffers::ET_CHAR, 0, 6 },
    { flatbuffers::ET_CHAR, 0, 7 },
    { flatbuffers::ET_INT, 0, -1 },
    { flatbuffers::ET_UCHAR, 1, -1 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::ImportAnimCompressTypeTypeTable,
    CrossSchema::ImportRefSkeletonTypeTable,
    CrossSchema::ImportAnimTrackPerBoneTypeTable,
    CrossSchema::ImportAnimNotifyEventTypeTable,
    CrossSchema::ImportAnimSyncMarkerTypeTable,
    CrossSchema::ImportRootMotionLockTypeTypeTable,
    CrossSchema::ImportAdditiveSpaceTypeTypeTable,
    CrossSchema::ImportAdditiveBaseTypeTypeTable
  };
  static const char * const names[] = {
    "name",
    "ref_path",
    "duration_sec",
    "frame_num",
    "skelt_num",
    "cpr_type",
    "ref_skelt",
    "tracks_ani_buffer",
    "cpr_ani_buffer",
    "notifies",
    "sync_markers",
    "has_rootmotion",
    "root_lock_type",
    "additive_path",
    "additive_animspace",
    "additive_basepose",
    "additive_baseframe",
    "curve_set"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 18, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

CROSS_SCHEMA_API const CrossSchema::ImportAnimation *GetImportAnimation(const void *buf);
CROSS_SCHEMA_API const CrossSchema::ImportAnimation *GetSizePrefixedImportAnimation(const void *buf);
CROSS_SCHEMA_API ImportAnimation *GetMutableImportAnimation(void *buf);
CROSS_SCHEMA_API bool VerifyImportAnimationBuffer(flatbuffers::Verifier &verifier);
CROSS_SCHEMA_API bool VerifySizePrefixedImportAnimationBuffer(flatbuffers::Verifier &verifier);
CROSS_SCHEMA_API void FinishImportAnimationBuffer(flatbuffers::FlatBufferBuilder &fbb,flatbuffers::Offset<CrossSchema::ImportAnimation> root);
CROSS_SCHEMA_API void FinishSizePrefixedImportAnimationBuffer(flatbuffers::FlatBufferBuilder &fbb,flatbuffers::Offset<CrossSchema::ImportAnimation> root);
CROSS_SCHEMA_API std::unique_ptr<CrossSchema::ImportAnimationT> UnPackImportAnimation(const void *buf,const flatbuffers::resolver_function_t *res = nullptr);
CROSS_SCHEMA_API std::unique_ptr<CrossSchema::ImportAnimationT> UnPackSizePrefixedImportAnimation(const void *buf,const flatbuffers::resolver_function_t *res = nullptr);
}  // namespace CrossSchema

#endif  // FLATBUFFERS_GENERATED_IMPORTANIMATION_CROSSSCHEMA_H_
