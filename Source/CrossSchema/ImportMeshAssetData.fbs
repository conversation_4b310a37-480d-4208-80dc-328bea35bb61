include "BasicStruct.fbs";
include "ImportMesh.fbs";
include "ImportSkeleton.fbs";

namespace CrossSchema;

//---------------------------------  add for submesh  --------------------------------------
table PhysicsBoxCollision
{
	position:float3;
	rotate:float4;
	halfextents:float3;
}

table PhysicsSphereCollision
{
	position:float3;
	radius:float;
}

table PhysicsCapsuleCollision
{
	position:float3;
	rotate:float4;
	radius:float;
	halfHeight:float;
}

table PhysicsConvexCollision
{
	position:float3;
	rotate:float4;
	data:[ubyte];
}

table PhysicsMeshCollision
{
	position:float3;
	rotate:float4;
	data:[ubyte];
}

table PhysicsCollision
{
	boxcollision:[PhysicsBoxCollision];
	spherecollision:[PhysicsSphereCollision];
	capsulecollision:[PhysicsCapsuleCollision];
	convexcollision:[PhysicsConvexCollision];
	meshcollision:[PhysicsMeshCollision];
}

table UVRangeInfo
{
    mMin:[int];
    mGapStart:[int];
    mGapLength:[int];
    mPrecision:uint = 0;
    mBitsU:uint = 0;
    mBitsV:uint = 0;
}

table MeshClusterInfo
{
    mIndexStart:uint = 0;
	mIndexCount:uint = 0;
    mVertexStart:uint = 0;
    mVertexCount:uint = 0;
	mBoundingSphere:float4;
    mNormalCone:float4;
    mApexOffset:float;
    mPosStart:[int];
    mPosPrecision:uint;
    mPosBits:[uint];
    mUVInfo:[UVRangeInfo];
    mUVChannelCount:uint;
    mBitsPerVertex:uint;
    mIndexBit:uint;
    mIndexOffset:uint;
    mDataOffset:uint;
	mEncodeData:[ubyte];
}

table ClusterGroupInfo
{
	boundingSphere:float4;
    clusterStartIndex:uint = 0;
	clusterCount:uint = 0;
    mipLevel:uint = 0;
    parentIndex:uint = 0;
    childStartIndex:uint = 0;
    childCount:uint = 0;
}

table IndirectDrawCMD
{
    indexCount:uint = 0;
    instanceCount:uint = 0;
    firstIndex:uint = 0;
    vertexOffset:int = 0;

    firstInstance:uint = 0;
    apexOffset:float;
    padding:float2;

    boundingSphere:float4;

    normalCone:float4;
}

table CustomAttributeInfo
{
	fkeynamehash:uint = 0;
	fdataflag:uint = 0;
	fdatasizeinbyte:uint = 0;
	fdataoffset:uint = 0;
}

table VertexChannelAssetData
{
	fstride:ushort = 0;
	ffrequency:ushort = 1;
	fstream:short = -1;
	fstreamoffset:short = -1;
	fmiscflag:ushort = 0;
	freserve0:ushort = 0;
	freserve1:ushort = 0;
	fvertexchannel:uint = 0;
	fdataformat:uint = 0;
	fdata:[ubyte];
}

table IndexStreamAssetData
{
	fcount:uint = 0;
	fdata:[ubyte];
	fis16bitindex:bool;
}

table MeshBound
{
    fmin:float3;
    fmax:float3;
}

table ImportDeltaShapeInfo
{
	fvertexstart:uint = 0;       // 当前 DeltaShape 对应整个 Blend Shape 顶点数据中的起始顶点索引
	fvertexcount:uint = 0;
}

table ImportBlendShapeChannelInfo
{
    fname:string;
    fnormalizedfullweights:[float];
    fdeltashapes:[ImportDeltaShapeInfo];
}

table ImportBlendShapeInfo
{
    fvertexchannelsemanticmask:uint = 0;
    fvertexchanneldata:[VertexChannelAssetData]; // Position, Normal, Tangent, InfluencedVertexID
    fchannelinfos:[ImportBlendShapeChannelInfo];
}

// Match to the MeshPartAssetInfo(submesh) in MeshData.h, 
table ImportMeshPartAssetInfo
{
    fnameindex:short = -1;       //当前mesh part名称
	fmaterialindex:short = -1;   //当前mesh part使用的材质索引，对应MeshAssetData.mMaterialNames数组中的位置，不同mesh part可以使用同一个material
	fvertexstart:uint = 0;       //当前mesh part对应整个mesh顶点数据中的起始顶点索引
	fvertexcount:uint = 0;
	findexstart:uint = 0;        //当前mesh part对应整个mesh索引数据中的起始索引
	findexcount:uint = 0;
	fprimitivecount:uint = 0;
    fbindinginfo:MeshBound;      //aabb包围盒信息
    fmiscflag:ulong = 0;         //预留的标记
    fshadowbias:float = 0;       //美术或者ta设置的值 
    fshadownormalbias:float = 0; //美术或者ta设置的值 
    fprimitivetype:uint = 0;     //submesh的图元类型
    frenderpriority:ubyte = 0;   //当前mesh part的渲染优先级 
    fcollisiontree:[CollisionNode];
    fcustomattributeinfo:CustomAttributeInfo;
    fcustomattributedata:[ubyte];
    mClusters:[MeshClusterInfo];
    mClusterGroups:[ClusterGroupInfo];
    mIndirectDrawCMDs:[IndirectDrawCMD];
    fblendshape:ImportBlendShapeInfo;
}

table ImportMeshAssetData
{
    fversion:uint = 0;
    fname:string;
    fvertexcount:uint = 0;
    fprimitivecount:uint = 0;
    fvertexchannelsemanticmask:uint = 0;
    findexstream:IndexStreamAssetData;
    fvertexchanneldata:[VertexChannelAssetData];
    fmeshpartinfo:[ImportMeshPartAssetInfo];
    fmeshpartlodstartindex:[uint];
    fmaterialnames:[string];
    fmeshpartnames:[string];
    faabb:MeshBound;
    frefskeleton:ImportRefSkeleton;
	fbindposeinv:[invmatrix];
    fphysicscollision:PhysicsCollision;
    fcustomattributeversion:ushort = 0;
    fcustomattributeversionflag:ushort = 0;
    fcustomattributeinfo:CustomAttributeInfo;
    fcustomattributedata:[ubyte];
}

root_type ImportMeshAssetData;