#include "BasicStruct_generated.h"
namespace CrossSchema {
const flatbuffers::TypeTable* float3::MiniReflectTypeTable(){
   return float3TypeTable();
  }
 float3::float3()
      : x_(0),
        y_(0),
        z_(0) {
  }
float3::float3(float _x, float _y, float _z)
      : x_(flatbuffers::EndianScalar(_x)),
        y_(flatbuffers::EndianScalar(_y)),
        z_(flatbuffers::EndianScalar(_z)) {
  }
float  float3::x() const{
    return flatbuffers::EndianScalar(x_);
  }
void float3::mutate_x(float _x) {
    flatbuffers::WriteScalar(&x_, _x);
  }
float  float3::y() const{
    return flatbuffers::EndianScalar(y_);
  }
void float3::mutate_y(float _y) {
    flatbuffers::WriteScalar(&y_, _y);
  }
float  float3::z() const{
    return flatbuffers::EndianScalar(z_);
  }
void float3::mutate_z(float _z) {
    flatbuffers::WriteScalar(&z_, _z);
  }
const flatbuffers::TypeTable* float2::MiniReflectTypeTable(){
   return float2TypeTable();
  }
 float2::float2()
      : x_(0),
        y_(0) {
  }
float2::float2(float _x, float _y)
      : x_(flatbuffers::EndianScalar(_x)),
        y_(flatbuffers::EndianScalar(_y)) {
  }
float  float2::x() const{
    return flatbuffers::EndianScalar(x_);
  }
void float2::mutate_x(float _x) {
    flatbuffers::WriteScalar(&x_, _x);
  }
float  float2::y() const{
    return flatbuffers::EndianScalar(y_);
  }
void float2::mutate_y(float _y) {
    flatbuffers::WriteScalar(&y_, _y);
  }
const flatbuffers::TypeTable* float4::MiniReflectTypeTable(){
   return float4TypeTable();
  }
 float4::float4()
      : x_(0),
        y_(0),
        z_(0),
        w_(0) {
  }
float4::float4(float _x, float _y, float _z, float _w)
      : x_(flatbuffers::EndianScalar(_x)),
        y_(flatbuffers::EndianScalar(_y)),
        z_(flatbuffers::EndianScalar(_z)),
        w_(flatbuffers::EndianScalar(_w)) {
  }
float  float4::x() const{
    return flatbuffers::EndianScalar(x_);
  }
void float4::mutate_x(float _x) {
    flatbuffers::WriteScalar(&x_, _x);
  }
float  float4::y() const{
    return flatbuffers::EndianScalar(y_);
  }
void float4::mutate_y(float _y) {
    flatbuffers::WriteScalar(&y_, _y);
  }
float  float4::z() const{
    return flatbuffers::EndianScalar(z_);
  }
void float4::mutate_z(float _z) {
    flatbuffers::WriteScalar(&z_, _z);
  }
float  float4::w() const{
    return flatbuffers::EndianScalar(w_);
  }
void float4::mutate_w(float _w) {
    flatbuffers::WriteScalar(&w_, _w);
  }
const flatbuffers::TypeTable* uint3::MiniReflectTypeTable(){
   return uint3TypeTable();
  }
 uint3::uint3()
      : x_(0),
        y_(0),
        z_(0) {
  }
uint3::uint3(uint32_t _x, uint32_t _y, uint32_t _z)
      : x_(flatbuffers::EndianScalar(_x)),
        y_(flatbuffers::EndianScalar(_y)),
        z_(flatbuffers::EndianScalar(_z)) {
  }
uint32_t  uint3::x() const{
    return flatbuffers::EndianScalar(x_);
  }
void uint3::mutate_x(uint32_t _x) {
    flatbuffers::WriteScalar(&x_, _x);
  }
uint32_t  uint3::y() const{
    return flatbuffers::EndianScalar(y_);
  }
void uint3::mutate_y(uint32_t _y) {
    flatbuffers::WriteScalar(&y_, _y);
  }
uint32_t  uint3::z() const{
    return flatbuffers::EndianScalar(z_);
  }
void uint3::mutate_z(uint32_t _z) {
    flatbuffers::WriteScalar(&z_, _z);
  }
const flatbuffers::TypeTable* uint4::MiniReflectTypeTable(){
   return uint4TypeTable();
  }
 uint4::uint4()
      : x_(0),
        y_(0),
        z_(0),
        w_(0) {
  }
uint4::uint4(uint32_t _x, uint32_t _y, uint32_t _z, uint32_t _w)
      : x_(flatbuffers::EndianScalar(_x)),
        y_(flatbuffers::EndianScalar(_y)),
        z_(flatbuffers::EndianScalar(_z)),
        w_(flatbuffers::EndianScalar(_w)) {
  }
uint32_t  uint4::x() const{
    return flatbuffers::EndianScalar(x_);
  }
void uint4::mutate_x(uint32_t _x) {
    flatbuffers::WriteScalar(&x_, _x);
  }
uint32_t  uint4::y() const{
    return flatbuffers::EndianScalar(y_);
  }
void uint4::mutate_y(uint32_t _y) {
    flatbuffers::WriteScalar(&y_, _y);
  }
uint32_t  uint4::z() const{
    return flatbuffers::EndianScalar(z_);
  }
void uint4::mutate_z(uint32_t _z) {
    flatbuffers::WriteScalar(&z_, _z);
  }
uint32_t  uint4::w() const{
    return flatbuffers::EndianScalar(w_);
  }
void uint4::mutate_w(uint32_t _w) {
    flatbuffers::WriteScalar(&w_, _w);
  }
const flatbuffers::TypeTable* GUID::MiniReflectTypeTable(){
   return GUIDTypeTable();
  }
 GUID::GUID()
      : low_(0),
        high_(0) {
  }
GUID::GUID(uint64_t _low, uint64_t _high)
      : low_(flatbuffers::EndianScalar(_low)),
        high_(flatbuffers::EndianScalar(_high)) {
  }
uint64_t  GUID::low() const{
    return flatbuffers::EndianScalar(low_);
  }
void GUID::mutate_low(uint64_t _low) {
    flatbuffers::WriteScalar(&low_, _low);
  }
uint64_t  GUID::high() const{
    return flatbuffers::EndianScalar(high_);
  }
void GUID::mutate_high(uint64_t _high) {
    flatbuffers::WriteScalar(&high_, _high);
  }
 const flatbuffers::TypeTable * matrix4x4::MiniReflectTypeTable(){
    return matrix4x4TypeTable();
  }
const flatbuffers::Vector<float> * matrix4x4::matrix_value() const{
    return GetPointer<const flatbuffers::Vector<float> *>(VT_MATRIX_VALUE);
  }
  flatbuffers::Vector<float> * matrix4x4::mutable_matrix_value() {
    return GetPointer<flatbuffers::Vector<float> *>(VT_MATRIX_VALUE);
  }
 bool matrix4x4::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_MATRIX_VALUE) &&
           verifier.VerifyVector(matrix_value()) &&
           verifier.EndTable();
  }
  void matrix4x4Builder::add_matrix_value(flatbuffers::Offset<flatbuffers::Vector<float>> matrix_value) {
    fbb_.AddOffset(matrix4x4::VT_MATRIX_VALUE, matrix_value);
  }
flatbuffers::Offset<matrix4x4> Creatematrix4x4(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<float>> matrix_value){
    matrix4x4Builder builder_(_fbb);
  builder_.add_matrix_value(matrix_value);
  return builder_.Finish();
}

flatbuffers::Offset<matrix4x4> Creatematrix4x4Direct(
    flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<float> *matrix_value) {
  auto matrix_value__ = matrix_value ? _fbb.CreateVector<float>(*matrix_value) : 0;
  return CrossSchema::Creatematrix4x4(
      _fbb,
      matrix_value__);
}

 const flatbuffers::TypeTable * transform::MiniReflectTypeTable(){
    return transformTypeTable();
  }
const CrossSchema::float4 * transform::translate() const{
    return GetStruct<const CrossSchema::float4 *>(VT_TRANSLATE);
  }
  CrossSchema::float4 * transform::mutable_translate() {
    return GetStruct<CrossSchema::float4 *>(VT_TRANSLATE);
  }
const CrossSchema::float4 * transform::rotation() const{
    return GetStruct<const CrossSchema::float4 *>(VT_ROTATION);
  }
  CrossSchema::float4 * transform::mutable_rotation() {
    return GetStruct<CrossSchema::float4 *>(VT_ROTATION);
  }
const CrossSchema::float4 * transform::scale() const{
    return GetStruct<const CrossSchema::float4 *>(VT_SCALE);
  }
  CrossSchema::float4 * transform::mutable_scale() {
    return GetStruct<CrossSchema::float4 *>(VT_SCALE);
  }
 bool transform::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyField<CrossSchema::float4>(verifier, VT_TRANSLATE) &&
           VerifyField<CrossSchema::float4>(verifier, VT_ROTATION) &&
           VerifyField<CrossSchema::float4>(verifier, VT_SCALE) &&
           verifier.EndTable();
  }
  void transformBuilder::add_translate(const CrossSchema::float4 *translate) {
    fbb_.AddStruct(transform::VT_TRANSLATE, translate);
  }
  void transformBuilder::add_rotation(const CrossSchema::float4 *rotation) {
    fbb_.AddStruct(transform::VT_ROTATION, rotation);
  }
  void transformBuilder::add_scale(const CrossSchema::float4 *scale) {
    fbb_.AddStruct(transform::VT_SCALE, scale);
  }
flatbuffers::Offset<transform> Createtransform(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::float4 *translate,
    const CrossSchema::float4 *rotation,
    const CrossSchema::float4 *scale){
    transformBuilder builder_(_fbb);
  builder_.add_scale(scale);
  builder_.add_rotation(rotation);
  builder_.add_translate(translate);
  return builder_.Finish();
}

 const flatbuffers::TypeTable * ImportBoneNode::MiniReflectTypeTable(){
    return ImportBoneNodeTypeTable();
  }
const flatbuffers::String * ImportBoneNode::name() const{
    return GetPointer<const flatbuffers::String *>(VT_NAME);
  }
  flatbuffers::String * ImportBoneNode::mutable_name() {
    return GetPointer<flatbuffers::String *>(VT_NAME);
  }
uint32_t  ImportBoneNode::boneid() const{
    return GetField<uint32_t>(VT_BONEID, 0);
  }
  bool ImportBoneNode::mutate_boneid (uint32_t _boneid) {
    return SetField<uint32_t>(VT_BONEID, _boneid, 0);
  }
int32_t  ImportBoneNode::bonetype() const{
    return GetField<int32_t>(VT_BONETYPE, 0);
  }
  bool ImportBoneNode::mutate_bonetype (int32_t _bonetype) {
    return SetField<int32_t>(VT_BONETYPE, _bonetype, 0);
  }
uint32_t  ImportBoneNode::parentid() const{
    return GetField<uint32_t>(VT_PARENTID, 0);
  }
  bool ImportBoneNode::mutate_parentid (uint32_t _parentid) {
    return SetField<uint32_t>(VT_PARENTID, _parentid, 0);
  }
CrossSchema::ImportBoneTransRetgtMode  ImportBoneNode::retarget() const{
    return static_cast<CrossSchema::ImportBoneTransRetgtMode>(GetField<int8_t>(VT_RETARGET, 0));
  }
  bool ImportBoneNode::mutate_retarget (CrossSchema::ImportBoneTransRetgtMode _retarget) {
    return SetField<int8_t>(VT_RETARGET, static_cast<int8_t>(_retarget), 0);
  }
const flatbuffers::Vector<float> * ImportBoneNode::bindposeinv() const{
    return GetPointer<const flatbuffers::Vector<float> *>(VT_BINDPOSEINV);
  }
  flatbuffers::Vector<float> * ImportBoneNode::mutable_bindposeinv() {
    return GetPointer<flatbuffers::Vector<float> *>(VT_BINDPOSEINV);
  }
const flatbuffers::Vector<float> * ImportBoneNode::bindposedef() const{
    return GetPointer<const flatbuffers::Vector<float> *>(VT_BINDPOSEDEF);
  }
  flatbuffers::Vector<float> * ImportBoneNode::mutable_bindposedef() {
    return GetPointer<flatbuffers::Vector<float> *>(VT_BINDPOSEDEF);
  }
const flatbuffers::Vector<float> * ImportBoneNode::worldmatrix() const{
    return GetPointer<const flatbuffers::Vector<float> *>(VT_WORLDMATRIX);
  }
  flatbuffers::Vector<float> * ImportBoneNode::mutable_worldmatrix() {
    return GetPointer<flatbuffers::Vector<float> *>(VT_WORLDMATRIX);
  }
 bool ImportBoneNode::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyField<uint32_t>(verifier, VT_BONEID) &&
           VerifyField<int32_t>(verifier, VT_BONETYPE) &&
           VerifyField<uint32_t>(verifier, VT_PARENTID) &&
           VerifyField<int8_t>(verifier, VT_RETARGET) &&
           VerifyOffset(verifier, VT_BINDPOSEINV) &&
           verifier.VerifyVector(bindposeinv()) &&
           VerifyOffset(verifier, VT_BINDPOSEDEF) &&
           verifier.VerifyVector(bindposedef()) &&
           VerifyOffset(verifier, VT_WORLDMATRIX) &&
           verifier.VerifyVector(worldmatrix()) &&
           verifier.EndTable();
  }
  void ImportBoneNodeBuilder::add_name(flatbuffers::Offset<flatbuffers::String> name) {
    fbb_.AddOffset(ImportBoneNode::VT_NAME, name);
  }
  void ImportBoneNodeBuilder::add_boneid(uint32_t boneid) {
    fbb_.AddElement<uint32_t>(ImportBoneNode::VT_BONEID, boneid, 0);
  }
  void ImportBoneNodeBuilder::add_bonetype(int32_t bonetype) {
    fbb_.AddElement<int32_t>(ImportBoneNode::VT_BONETYPE, bonetype, 0);
  }
  void ImportBoneNodeBuilder::add_parentid(uint32_t parentid) {
    fbb_.AddElement<uint32_t>(ImportBoneNode::VT_PARENTID, parentid, 0);
  }
  void ImportBoneNodeBuilder::add_retarget(CrossSchema::ImportBoneTransRetgtMode retarget) {
    fbb_.AddElement<int8_t>(ImportBoneNode::VT_RETARGET, static_cast<int8_t>(retarget), 0);
  }
  void ImportBoneNodeBuilder::add_bindposeinv(flatbuffers::Offset<flatbuffers::Vector<float>> bindposeinv) {
    fbb_.AddOffset(ImportBoneNode::VT_BINDPOSEINV, bindposeinv);
  }
  void ImportBoneNodeBuilder::add_bindposedef(flatbuffers::Offset<flatbuffers::Vector<float>> bindposedef) {
    fbb_.AddOffset(ImportBoneNode::VT_BINDPOSEDEF, bindposedef);
  }
  void ImportBoneNodeBuilder::add_worldmatrix(flatbuffers::Offset<flatbuffers::Vector<float>> worldmatrix) {
    fbb_.AddOffset(ImportBoneNode::VT_WORLDMATRIX, worldmatrix);
  }
flatbuffers::Offset<ImportBoneNode> CreateImportBoneNode(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> name,
    uint32_t boneid,
    int32_t bonetype,
    uint32_t parentid,
    CrossSchema::ImportBoneTransRetgtMode retarget,
    flatbuffers::Offset<flatbuffers::Vector<float>> bindposeinv,
    flatbuffers::Offset<flatbuffers::Vector<float>> bindposedef,
    flatbuffers::Offset<flatbuffers::Vector<float>> worldmatrix){
    ImportBoneNodeBuilder builder_(_fbb);
  builder_.add_worldmatrix(worldmatrix);
  builder_.add_bindposedef(bindposedef);
  builder_.add_bindposeinv(bindposeinv);
  builder_.add_parentid(parentid);
  builder_.add_bonetype(bonetype);
  builder_.add_boneid(boneid);
  builder_.add_name(name);
  builder_.add_retarget(retarget);
  return builder_.Finish();
}

flatbuffers::Offset<ImportBoneNode> CreateImportBoneNodeDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *name,
    uint32_t boneid,
    int32_t bonetype,
    uint32_t parentid,
    CrossSchema::ImportBoneTransRetgtMode retarget,
    const std::vector<float> *bindposeinv,
    const std::vector<float> *bindposedef,
    const std::vector<float> *worldmatrix) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto bindposeinv__ = bindposeinv ? _fbb.CreateVector<float>(*bindposeinv) : 0;
  auto bindposedef__ = bindposedef ? _fbb.CreateVector<float>(*bindposedef) : 0;
  auto worldmatrix__ = worldmatrix ? _fbb.CreateVector<float>(*worldmatrix) : 0;
  return CrossSchema::CreateImportBoneNode(
      _fbb,
      name__,
      boneid,
      bonetype,
      parentid,
      retarget,
      bindposeinv__,
      bindposedef__,
      worldmatrix__);
}

matrix4x4T *matrix4x4::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<matrix4x4T>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void matrix4x4::UnPackTo(matrix4x4T *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = matrix_value(); if (_e) { _o->matrix_value.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->matrix_value[_i] = _e->Get(_i); } } }
}

CROSS_SCHEMA_API flatbuffers::Offset<matrix4x4> matrix4x4::Pack(flatbuffers::FlatBufferBuilder &_fbb, const matrix4x4T* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return Creatematrix4x4(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<matrix4x4> Creatematrix4x4(flatbuffers::FlatBufferBuilder &_fbb, const matrix4x4T *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const matrix4x4T* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _matrix_value = _o->matrix_value.size() ? _fbb.CreateVector(_o->matrix_value) : 0;
  return CrossSchema::Creatematrix4x4(
      _fbb,
      _matrix_value);
}

transformT *transform::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<transformT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void transform::UnPackTo(transformT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = translate(); if (_e) _o->translate = std::unique_ptr<CrossSchema::float4>(new CrossSchema::float4(*_e)); }
  { auto _e = rotation(); if (_e) _o->rotation = std::unique_ptr<CrossSchema::float4>(new CrossSchema::float4(*_e)); }
  { auto _e = scale(); if (_e) _o->scale = std::unique_ptr<CrossSchema::float4>(new CrossSchema::float4(*_e)); }
}

CROSS_SCHEMA_API flatbuffers::Offset<transform> transform::Pack(flatbuffers::FlatBufferBuilder &_fbb, const transformT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return Createtransform(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<transform> Createtransform(flatbuffers::FlatBufferBuilder &_fbb, const transformT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const transformT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _translate = _o->translate ? _o->translate.get() : 0;
  auto _rotation = _o->rotation ? _o->rotation.get() : 0;
  auto _scale = _o->scale ? _o->scale.get() : 0;
  return CrossSchema::Createtransform(
      _fbb,
      _translate,
      _rotation,
      _scale);
}

ImportBoneNodeT *ImportBoneNode::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<ImportBoneNodeT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void ImportBoneNode::UnPackTo(ImportBoneNodeT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = name(); if (_e) _o->name = _e->str(); }
  { auto _e = boneid(); _o->boneid = _e; }
  { auto _e = bonetype(); _o->bonetype = _e; }
  { auto _e = parentid(); _o->parentid = _e; }
  { auto _e = retarget(); _o->retarget = _e; }
  { auto _e = bindposeinv(); if (_e) { _o->bindposeinv.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->bindposeinv[_i] = _e->Get(_i); } } }
  { auto _e = bindposedef(); if (_e) { _o->bindposedef.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->bindposedef[_i] = _e->Get(_i); } } }
  { auto _e = worldmatrix(); if (_e) { _o->worldmatrix.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->worldmatrix[_i] = _e->Get(_i); } } }
}

CROSS_SCHEMA_API flatbuffers::Offset<ImportBoneNode> ImportBoneNode::Pack(flatbuffers::FlatBufferBuilder &_fbb, const ImportBoneNodeT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateImportBoneNode(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<ImportBoneNode> CreateImportBoneNode(flatbuffers::FlatBufferBuilder &_fbb, const ImportBoneNodeT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const ImportBoneNodeT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _name = _o->name.empty() ? 0 : _fbb.CreateString(_o->name);
  auto _boneid = _o->boneid;
  auto _bonetype = _o->bonetype;
  auto _parentid = _o->parentid;
  auto _retarget = _o->retarget;
  auto _bindposeinv = _o->bindposeinv.size() ? _fbb.CreateVector(_o->bindposeinv) : 0;
  auto _bindposedef = _o->bindposedef.size() ? _fbb.CreateVector(_o->bindposedef) : 0;
  auto _worldmatrix = _o->worldmatrix.size() ? _fbb.CreateVector(_o->worldmatrix) : 0;
  return CrossSchema::CreateImportBoneNode(
      _fbb,
      _name,
      _boneid,
      _bonetype,
      _parentid,
      _retarget,
      _bindposeinv,
      _bindposedef,
      _worldmatrix);
}

}  // namespace CrossSchema
