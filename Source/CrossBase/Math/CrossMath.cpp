#include "CrossMath.h"
namespace cross
{
Axis::Type cross::Axis::Operate(const std::string& str)
{
    static const std::unordered_map<std::string, Axis::Type> sOperateStrs{{"X", Axis::X},
                                                                          {"Y", Axis::Y},
                                                                          {"Z", Axis::Z},

                                                                          {"Screen", Axis::Screen},

                                                                          {"XY", Axis::XY},
                                                                          {"XZ", Axis::XZ},
                                                                          {"YZ", Axis::YZ},
                                                                          {"XYZ", Axis::XYZ},

                                                                          {"All", Axis::All}};

    auto itrOperate = sOperateStrs.find(str);
    if (itrOperate != sOperateStrs.end())
        return itrOperate->second;
    else
        return Axis::None;
}

float Float2::Distance(const Float2& v1, const Float2& v2)
{
    float deltaX = v1.x - v2.x;
    float deltaY = v1.y - v2.y;
    return sqrt(deltaX * deltaX + deltaY * deltaY);
}
bool Float2::NearEqual(const Float2& v, const Float2& epsilon) const
{
    float dx = fabsf(x - v.x);
    float dy = fabsf(y - v.y);
    return ((dx <= epsilon.x) && (dy <= epsilon.y));
}

Float2 Float2::CatmullRom(Float2 v1, Float2 v2, Float2 v3, Float2 v4, float t)
{
    using namespace DirectX;
    XMVECTOR p1 = XMLoadFloat2(reinterpret_cast<const XMFLOAT2*>(&v1));
    XMVECTOR p2 = XMLoadFloat2(reinterpret_cast<const XMFLOAT2*>(&v2));
    XMVECTOR p3 = XMLoadFloat2(reinterpret_cast<const XMFLOAT2*>(&v3));
    XMVECTOR p4 = XMLoadFloat2(reinterpret_cast<const XMFLOAT2*>(&v4));

    Float2 result;
    DirectX::XMStoreFloat2(reinterpret_cast<XMFLOAT2*>(&result), DirectX::XMVectorCatmullRom(p1, p2, p3, p4, t));

    return result;
}
Float3 Float3::ProjectOnPlane(const Float3& n_normalized) const
{
    const Float3& v_normalized = *this;

    float v_dot_n = v_normalized.Dot(n_normalized);
    Float3 t = {v_normalized.x - n_normalized.x * v_dot_n, v_normalized.y - n_normalized.y * v_dot_n, v_normalized.z - n_normalized.z * v_dot_n};
    return t.Normalized();
}
void Float3::Normalize()
{
    float length = Length();
    if (length > 0)
        length = 1.0f / length;

    x *= length;
    y *= length;
    z *= length;
}
Float3 Float3::Normalized() const
{
    float length = Length();
    if (length > 0)
        length = 1.0f / length;

    return Float3(x * length, y * length, z * length);
}
Float3 Float3::StableNormalized(float& invMag)
{
    float a = DirectX::XMMax(DirectX::XMMax(abs(x), abs(y)), abs(z));
    float b = ((*this) / a).LengthSquared();
    if (b > 0.0f)
    {
        invMag = 1.0f / (sqrt(b) * a);
        return (*this) / (sqrt(b) * a);
    }
    else
    {
        invMag = 1.0f;
        return *this;
    }
}
Float3 Float3::StableNormalized()
{
    float a = DirectX::XMMax(DirectX::XMMax(abs(x), abs(y)), abs(z));
    float b = ((*this) / a).LengthSquared();
    if (b > 0.0f)
    {
        return (*this) / (sqrt(b) * a);
    }
    else
    {
        return *this;
    }
}
void Float3::StableNormalize()
{
    float a = DirectX::XMMax(DirectX::XMMax(abs(x), abs(y)), abs(z));
    float b = ((*this) / a).LengthSquared();
    if (b > 0.0f)
    {
        x /= (sqrt(b) * a);
        y /= (sqrt(b) * a);
        z /= (sqrt(b) * a);
    }
}
 Float3 Float3::SafeNormalXZ(float tolerance) const
{
    float square = x * x + z * z;
    if (square < tolerance)
    {
        return Float3::Zero();
    }
    float scale = 1.0f / std::sqrt(square);
    return Float3(x * scale, 0.f, z * scale);
}

Float3 Float3::SafeNormal(float tolerance) const
{
    float square = x * x + y * y + z * z;
    if (square < tolerance)
    {
        return Float3::Zero();
    }
    float scale = 1.0f / sqrt(square);
    return Float3(x * scale, y * scale, z * scale);
}

void Float3::ClampToMaxSize(float maxSize)
{
    if (maxSize < 1e-4f)
    {
        x = y = z = 0.f;
        return;
    }

    float len = Length();
    if (len > maxSize)
    {
        float scale = maxSize * 1.0f / len;
        x *= scale;
        y *= scale;
        z *= scale;
    }
}

Float3 Float3::ClampToMaxSize(float maxSize) const
{
    if (maxSize < 1e-4f)
    {
        return Float3::Zero();
    }

    float len = Length();
    if (len > maxSize)
    {
        float scale = maxSize * 1.0f / len;
        return Float3(x * scale, y * scale, z * scale);
    }
    else
    {
        return *this;
    }
}

void Float3::Clamp(const Float3& vmin, const Float3& vmax)
{
    x = (x >= vmin.x) ? x : vmin.x;
    x = (x <= vmax.x) ? x : vmax.x;
    y = (y >= vmin.y) ? y : vmin.y;
    y = (y <= vmax.y) ? y : vmax.y;
    z = (z >= vmin.z) ? z : vmin.z;
    z = (z <= vmax.z) ? z : vmax.z;
}
Float3 Float3::SmoothStep(const Float3& v1, const Float3& v2, float t)
{
    t = (t > 1.0f) ? 1.0f : ((t < 0.0f) ? 0.0f : t);   // Clamp value to 0 to 1
    t = t * t * (3.f - 2.f * t);
    Float3 result(v1.x + ((v2.x - v1.x) * t), v1.y + ((v2.y - v1.y) * t), v1.z + ((v2.z - v1.z) * t));
    return result;
}
Float3 Float3::Barycentric(const Float3& v1, const Float3& v2, const Float3& v3, float f, float g)
{
    Float3 result((v1.x + (f * (v2.x - v1.x))) + (g * (v3.x - v1.x)), (v1.y + (f * (v2.y - v1.y))) + (g * (v3.y - v1.y)), (v1.z + (f * (v2.z - v1.z))) + (g * (v3.z - v1.z)));
    return result;
}
Float3 Float3::CatmullRom(const Float3& v1, const Float3& v2, const Float3& v3, const Float3& v4, float t)
{
    using namespace DirectX;
    XMVECTOR p1 = XMLoadFloat3(reinterpret_cast<const XMFLOAT3*>(&v1));
    XMVECTOR p2 = XMLoadFloat3(reinterpret_cast<const XMFLOAT3*>(&v2));
    XMVECTOR p3 = XMLoadFloat3(reinterpret_cast<const XMFLOAT3*>(&v3));
    XMVECTOR p4 = XMLoadFloat3(reinterpret_cast<const XMFLOAT3*>(&v4));

    Float3 result;
    DirectX::XMStoreFloat3(reinterpret_cast<XMFLOAT3*>(&result), DirectX::XMVectorCatmullRom(p1, p2, p3, p4, t));

    return result;
}
Float3 Float3::Hermite(const Float3& v1, const Float3& t1, const Float3& v2, const Float3& t2, float t)
{
    using namespace DirectX;
    XMVECTOR p1 = XMLoadFloat3(reinterpret_cast<const XMFLOAT3*>(&v1));
    XMVECTOR tg1 = XMLoadFloat3(reinterpret_cast<const XMFLOAT3*>(&t1));
    XMVECTOR p2 = XMLoadFloat3(reinterpret_cast<const XMFLOAT3*>(&v2));
    XMVECTOR tg2 = XMLoadFloat3(reinterpret_cast<const XMFLOAT3*>(&t2));

    Float3 result;
    DirectX::XMStoreFloat3(reinterpret_cast<XMFLOAT3*>(&result), DirectX::XMVectorHermite(p1, tg1, p2, tg2, t));

    return result;
}
Float3 Float3::Reflect(const Float3& ivec, const Float3& nvec)
{
    using namespace DirectX;
    XMVECTOR i = XMLoadFloat3(reinterpret_cast<const XMFLOAT3*>(&ivec));
    XMVECTOR n = XMLoadFloat3(reinterpret_cast<const XMFLOAT3*>(&nvec));

    Float3 result;
    DirectX::XMStoreFloat3(reinterpret_cast<XMFLOAT3*>(&result), DirectX::XMVector4Reflect(i, n));

    return result;
}
Float3 Float3::Refract(const Float3& ivec, const Float3& nvec, float refractionIndex)
{
    using namespace DirectX;
    XMVECTOR i = XMLoadFloat3(reinterpret_cast<const XMFLOAT3*>(&ivec));
    XMVECTOR n = XMLoadFloat3(reinterpret_cast<const XMFLOAT3*>(&nvec));

    Float3 result;
    DirectX::XMStoreFloat3(reinterpret_cast<XMFLOAT3*>(&result), DirectX::XMVector4Refract(i, n, refractionIndex));

    return result;
}
Float4 Float4::BaryCentric(const Float4& v1, const Float4& v2, const Float4& v3, float f, float g)
{
    using namespace DirectX;
    XMVECTOR p1 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&v1));
    XMVECTOR p2 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&v2));
    XMVECTOR p3 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&v3));

    Float4 result;
    DirectX::XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&result), DirectX::XMVectorBaryCentric(p1, p2, p3, f, g));
    return result;
}

Float4 Float4::CatmullRom(const Float4& v1, const Float4& v2, const Float4& v3, const Float4& v4, float t)
{
    using namespace DirectX;
    XMVECTOR p1 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&v1));
    XMVECTOR p2 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&v2));
    XMVECTOR p3 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&v3));
    XMVECTOR p4 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&v4));

    Float4 result;
    DirectX::XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&result), DirectX::XMVectorCatmullRom(p1, p2, p3, p4, t));
    return result;
}

Float4 Float4::Hermite(const Float4& v1, const Float4& t1, const Float4& v2, const Float4& t2, float t)
{
    using namespace DirectX;
    XMVECTOR p1 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&v1));
    XMVECTOR tg1 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&t1));
    XMVECTOR p2 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&v2));
    XMVECTOR tg2 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&t2));

    Float4 result;
    DirectX::XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&result), DirectX::XMVectorHermite(p1, tg1, p2, tg2, t));
    return result;
}

Float4 Float4::Reflect(const Float4& ivec, const Float4& nvec)
{
    using namespace DirectX;
    XMVECTOR i = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&ivec));
    XMVECTOR n = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&nvec));

    Float4 result;
    DirectX::XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&result), DirectX::XMVector2Reflect(i, n));
    return result;
}

Float4 Float4::Refract(const Float4& ivec, const Float4& nvec, float refractionIndex)
{
    using namespace DirectX;
    XMVECTOR i = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&ivec));
    XMVECTOR n = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&nvec));

    Float4 result;
    DirectX::XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&result), DirectX::XMVector2Refract(i, n, refractionIndex));
    return result;
}
bool Float4x4::Decompose(Float3& scale, Quaternion& rotation, Float3& translation) const
{
    using namespace DirectX;
    FXMMATRIX M = DirectX::XMLoadFloat4x4(reinterpret_cast<const XMFLOAT4X4*>(this));

    XMVECTOR s, r, t;
    DirectX::XMMatrixDecompose(&s, &r, &t, M);

    DirectX::XMStoreFloat3(reinterpret_cast<XMFLOAT3*>(&scale), s);
    DirectX::XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&rotation), r);
    DirectX::XMStoreFloat3(reinterpret_cast<XMFLOAT3*>(&translation), t);

    return true;
}
bool Float4x4::Decompose(Float3A& scale, QuaternionA& rotation, Float3A& translation) const
{
    using namespace DirectX;
    FXMMATRIX M = DirectX::XMLoadFloat4x4(reinterpret_cast<const XMFLOAT4X4*>(this));

    XMVECTOR s, r, t;
    DirectX::XMMatrixDecompose(&s, &r, &t, M);

    DirectX::XMStoreFloat3(reinterpret_cast<XMFLOAT3*>(&scale), s);
    DirectX::XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&rotation), r);
    DirectX::XMStoreFloat3(reinterpret_cast<XMFLOAT3*>(&translation), t);

    return true;
}
bool Float4x4::Decompose(Float4A& scale, QuaternionA& rotation, Float4A& translation) const
{
    using namespace DirectX;
    FXMMATRIX M = DirectX::XMLoadFloat4x4(reinterpret_cast<const XMFLOAT4X4*>(this));

    XMVECTOR s, r, t;
    DirectX::XMMatrixDecompose(&s, &r, &t, M);

    DirectX::XMStoreFloat4A(reinterpret_cast<XMFLOAT4A*>(&scale), s);
    DirectX::XMStoreFloat4A(reinterpret_cast<XMFLOAT4A*>(&rotation), r);
    DirectX::XMStoreFloat4A(reinterpret_cast<XMFLOAT4A*>(&translation), t);

    return true;
}
Float4x4& Float4x4::operator /= (float S)
{
    using namespace DirectX;
    assert(S != 0.f);
    XMVECTOR x1 = XMLoadFloat4(reinterpret_cast<XMFLOAT4*>(&m00));
    XMVECTOR x2 = XMLoadFloat4(reinterpret_cast<XMFLOAT4*>(&m10));
    XMVECTOR x3 = XMLoadFloat4(reinterpret_cast<XMFLOAT4*>(&m20));
    XMVECTOR x4 = XMLoadFloat4(reinterpret_cast<XMFLOAT4*>(&m30));

    float rs = 1.f / S;

    x1 = XMVectorScale(x1, rs);
    x2 = XMVectorScale(x2, rs);
    x3 = XMVectorScale(x3, rs);
    x4 = XMVectorScale(x4, rs);

    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&m00), x1);
    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&m10), x2);
    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&m20), x3);
    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&m30), x4);
    return *this;
}
Float4x4& Float4x4::operator /= (const Float4x4& M)
{
    using namespace DirectX;
    XMVECTOR x1 = XMLoadFloat4(reinterpret_cast<XMFLOAT4*>(&m00));
    XMVECTOR x2 = XMLoadFloat4(reinterpret_cast<XMFLOAT4*>(&m10));
    XMVECTOR x3 = XMLoadFloat4(reinterpret_cast<XMFLOAT4*>(&m20));
    XMVECTOR x4 = XMLoadFloat4(reinterpret_cast<XMFLOAT4*>(&m30));

    XMVECTOR y1 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M.m00));
    XMVECTOR y2 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M.m10));
    XMVECTOR y3 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M.m20));
    XMVECTOR y4 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M.m30));

    x1 = XMVectorDivide(x1, y1);
    x2 = XMVectorDivide(x2, y2);
    x3 = XMVectorDivide(x3, y3);
    x4 = XMVectorDivide(x4, y4);

    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&m00), x1);
    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&m10), x2);
    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&m20), x3);
    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&m30), x4);
    return *this;
}
Float4x4& Float4x4::Mirror(Axis::Type mirrorAxis, Axis::Type flipAxis)
{
    if (mirrorAxis != Axis::Type::None)
    {
        if (mirrorAxis == Axis::Type::X)
        {
            m00 *= -1.f;
            m10 *= -1.f;
            m20 *= -1.f;

            m30 *= -1.f;
        }
        else if (mirrorAxis == Axis::Type::Y)
        {
            m01 *= -1.f;
            m11 *= -1.f;
            m21 *= -1.f;

            m31 *= -1.f;
        }
        else if (mirrorAxis == Axis::Type::Z)
        {
            m02 *= -1.f;
            m12 *= -1.f;
            m22 *= -1.f;

            m32 *= -1.f;
        }
    }

    if (flipAxis != Axis::Type::None)
    {
        if (flipAxis == Axis::Type::X)
        {
            m00 *= -1.f;
            m01 *= -1.f;
            m02 *= -1.f;
        }
        else if (flipAxis == Axis::Type::Y)
        {
            m10 *= -1.f;
            m11 *= -1.f;
            m12 *= -1.f;
        }
        else if (flipAxis == Axis::Type::Z)
        {
            m20 *= -1.f;
            m21 *= -1.f;
            m22 *= -1.f;
        }
    }

    return *this;
}
Float4x4 Float4x4::CalculationRotationMatrix(const Float3& vectorBefore, const Float3& vectorAfter)
{
    Float3 axis;
    vectorBefore.Cross(vectorAfter, axis);
    float angle = acos(vectorBefore.Dot(vectorAfter) / vectorBefore.Length() / vectorAfter.Length());
    // rotationMatrix = RotationMatrix(rotationAngle, rotationAxis);

    axis.Normalize();
    Float4x4 rotatinMatrix;
    rotatinMatrix = Float4x4::Identity();

    rotatinMatrix.m00 = cos(angle) + axis.x * axis.x * (1 - cos(angle));
    rotatinMatrix.m10 = axis.x * axis.y * (1 - cos(angle) - axis.z * sin(angle));
    rotatinMatrix.m20 = axis.y * sin(angle) + axis.x * axis.z * (1 - cos(angle));

    rotatinMatrix.m01 = axis.z * sin(angle) + axis.x * axis.y * (1 - cos(angle));
    rotatinMatrix.m11 = cos(angle) + axis.y * axis.y * (1 - cos(angle));
    rotatinMatrix.m21 = -axis.x * sin(angle) + axis.y * axis.z * (1 - cos(angle));

    rotatinMatrix.m02 = -axis.y * sin(angle) + axis.x * axis.z * (1 - cos(angle));
    rotatinMatrix.m12 = axis.x * sin(angle) + axis.y * axis.z * (1 - cos(angle));
    rotatinMatrix.m22 = cos(angle) + axis.z * axis.z * (1 - cos(angle));

    return rotatinMatrix;
}
Float4x4 Float4x4::CreateScale(const Float3& scales)
{
    Float4x4 result;
    result.m00 = scales.x;
    result.m01 = 0.0f;
    result.m02 = 0.0f;
    result.m03 = 0.0f;

    result.m10 = 0.0f;
    result.m11 = scales.y;
    result.m12 = 0.0f;
    result.m13 = 0.0f;

    result.m20 = 0.0f;
    result.m21 = 0.0f;
    result.m22 = scales.z;
    result.m23 = 0.0f;

    result.m30 = 0.0f;
    result.m31 = 0.0f;
    result.m32 = 0.0f;
    result.m33 = 1.0f;
    return result;
}
Float4x4 Float4x4::CreateRotationX(float radians)
{
    float sin;
    float cos;
    MathUtils::SinCos(radians, sin, cos);

    Float4x4 result;
    result.m00 = 1.0f;
    result.m01 = 0.0f;
    result.m02 = 0.0f;
    result.m03 = 0.0f;

    result.m10 = 0.0f;
    result.m11 = cos;
    result.m12 = sin;
    result.m13 = 0.0f;

    result.m20 = 0.0f;
    result.m21 = -sin;
    result.m22 = cos;
    result.m23 = 0.0f;

    result.m30 = 0.0f;
    result.m31 = 0.0f;
    result.m32 = 0.0f;
    result.m33 = 1.0f;
    return result;
}

Float4x4 Float4x4::CreateRotationY(float radians)
{
    float sin;
    float cos;
    MathUtils::SinCos(radians, sin, cos);

    Float4x4 result;
    result.m00 = cos;
    result.m01 = 0.0f;
    result.m02 = -sin;
    result.m03 = 0.0f;

    result.m10 = 0.0f;
    result.m11 = 1.0f;
    result.m12 = 0.0f;
    result.m13 = 0.0f;

    result.m20 = sin;
    result.m21 = 0.0f;
    result.m22 = cos;
    result.m23 = 0.0f;

    result.m30 = 0.0f;
    result.m31 = 0.0f;
    result.m32 = 0.0f;
    result.m33 = 1.0f;
    return result;
}

Float4x4 Float4x4::CreateRotationZ(float radians)
{
    float sin;
    float cos;
    MathUtils::SinCos(radians, sin, cos);

    Float4x4 result;
    result.m00 = cos;
    result.m01 = sin;
    result.m02 = 0.0f;
    result.m03 = 0.0f;

    result.m10 = -sin;
    result.m11 = cos;
    result.m12 = 0.0f;
    result.m13 = 0.0f;

    result.m20 = 0.0f;
    result.m21 = 0.0f;
    result.m22 = 1.0f;
    result.m23 = 0.0f;

    result.m30 = 0.0f;
    result.m31 = 0.0f;
    result.m32 = 0.0f;
    result.m33 = 1.0f;
    return result;
}


Float4x4 Float4x4::CreateFromAxisAngle(const Float3& axis, float angle)
{
    auto v = DirectX::XMLoadFloat3(reinterpret_cast<const DirectX::XMFLOAT3*>(&axis));
    auto mat = DirectX::XMMatrixRotationAxis(v, angle);
    Float4x4 result;
    DirectX::XMStoreFloat4x4(reinterpret_cast<DirectX::XMFLOAT4X4*>(&result), DirectX::XMMatrixRotationAxis(v, angle));
    return result;
}


Float4x4 Float4x4::CreatePerspectiveFieldOfView(float fov, float aspectRatio, float nearPlane, float farPlane, bool ndcGL)
{
    using namespace DirectX;
    Float4x4 R;
    XMStoreFloat4x4(reinterpret_cast<XMFLOAT4X4*>(&R), DirectX::XMMatrixPerspectiveFovLH(fov, aspectRatio, nearPlane, farPlane));
    if (ndcGL)
    {
        R.m02 *= 2;
        R.m12 *= 2;
        R.m22 *= 2;
        R.m32 *= 2;
        R.m02 -= R.m03;
        R.m12 -= R.m13;
        R.m22 -= R.m23;
        R.m32 -= R.m33;
    }
    return R;
}


Float4x4 Float4x4::CreatePerspective(float width, float height, float nearPlane, float farPlane, bool ndcGL)
{
    using namespace DirectX;
    Float4x4 R;
    XMStoreFloat4x4(reinterpret_cast<XMFLOAT4X4*>(&R), XMMatrixPerspectiveLH(width, height, nearPlane, farPlane));
    if (ndcGL)
    {
        R.m02 *= 2;
        R.m12 *= 2;
        R.m22 *= 2;
        R.m32 *= 2;
        R.m02 -= R.m03;
        R.m12 -= R.m13;
        R.m22 -= R.m23;
        R.m32 -= R.m33;
    }
    return R;
}


Float4x4 Float4x4::CreatePerspectiveOffCenter(float left, float right, float bottom, float top, float nearPlane, float farPlane, bool ndcGL)
{
    using namespace DirectX;
    Float4x4 R;
    XMStoreFloat4x4(reinterpret_cast<XMFLOAT4X4*>(&R), XMMatrixPerspectiveOffCenterLH(left, right, bottom, top, nearPlane, farPlane));
    if (ndcGL)
    {
        R.m02 *= 2;
        R.m12 *= 2;
        R.m22 *= 2;
        R.m32 *= 2;
        R.m02 -= R.m03;
        R.m12 -= R.m13;
        R.m22 -= R.m23;
        R.m32 -= R.m33;
    }
    return R;
}


Float4x4 Float4x4::CreateOrthographic(float width, float height, float zNearPlane, float zFarPlane, bool ndcGL)
{
    using namespace DirectX;
    Float4x4 R;
    XMStoreFloat4x4(reinterpret_cast<XMFLOAT4X4*>(&R), XMMatrixOrthographicLH(width, height, zNearPlane, zFarPlane));
    if (ndcGL)
    {
        R.m02 *= 2;
        R.m12 *= 2;
        R.m22 *= 2;
        R.m32 *= 2;
        R.m02 -= R.m03;
        R.m12 -= R.m13;
        R.m22 -= R.m23;
        R.m32 -= R.m33;
    }
    return R;
}


Float4x4 Float4x4::CreateOrthographicOffCenter(float left, float right, float bottom, float top, float zNearPlane, float zFarPlane, bool ndcGL)
{
    using namespace DirectX;
    Float4x4 R;
    XMStoreFloat4x4(reinterpret_cast<XMFLOAT4X4*>(&R), XMMatrixOrthographicOffCenterLH(left, right, bottom, top, zNearPlane, zFarPlane));
    if (ndcGL)
    {
        R.m02 *= 2;
        R.m12 *= 2;
        R.m22 *= 2;
        R.m32 *= 2;
        R.m02 -= R.m03;
        R.m12 -= R.m13;
        R.m22 -= R.m23;
        R.m32 -= R.m33;
    }
    return R;
}


Float4x4 Float4x4::CreateLookAt(const Float3& eye, const Float3& target, const Float3& up)
{
    const Float3 dir = (target - eye).Normalized();
    Float3 x;

    if (MathUtils::Abs(up.Dot(dir)) > 1.0f - MathUtils::MathEps)
    {
        x = dir.Orthogonal();
    }
    else
    {
        x = (up.Cross(dir)).Normalized();
    }

    const Float3 y = (dir.Cross(x)).Normalized();
    return Float4x4{x.x, y.x, dir.x, 0, x.y, y.y, dir.y, 0, x.z, y.z, dir.z, 0, -x.Dot(eye), -y.Dot(eye), -dir.Dot(eye), 1};
}

Float4x4 Float4x4::QuaternionToRotationMatrix(const Quaternion& rot)
{
    Float3 eulerAngles = Quaternion::QuaternionToEuler(rot);
    Float4x4 res = Float4x4::Identity();
    // float c1 = cos(eulerAngles.y / 180.0f * MathUtils::MathPi);
    float c1 = cos(eulerAngles.y);
    float s1 = sin(eulerAngles.y);
    float c2 = cos(eulerAngles.x);
    float s2 = sin(eulerAngles.x);
    float c3 = cos(eulerAngles.z);
    float s3 = sin(eulerAngles.z);
    res.m00 = c1 * c3 + s1 * s2 * s3;
    res.m10 = c2 * s3;
    res.m20 = c1 * s2 * s3 - s1 * c3;
    res.m01 = c3 * s1 * s2 - c1 * s3;
    res.m11 = c2 * c3;
    res.m21 = s1 * s3 + c1 * c3 * s2;
    res.m02 = c2 * s1;
    res.m12 = -s2;
    res.m22 = c1 * c2;
    return res;
}


 Float4x4 Float4x4::CreateWorld(const Float3& position, const Float3& forward, const Float3& up)
{
    using namespace DirectX;
    XMVECTOR zaxis = XMVector3Normalize(XMLoadFloat3(reinterpret_cast<const XMFLOAT3*>(&forward)));
    XMVECTOR yaxis = XMLoadFloat3(reinterpret_cast<const XMFLOAT3*>(&up));
    XMVECTOR xaxis = XMVector3Normalize(XMVector3Cross(yaxis, zaxis));
    yaxis = XMVector3Cross(zaxis, xaxis);

    Float4x4 R;
    XMStoreFloat3(reinterpret_cast<XMFLOAT3*>(&R.m00), xaxis);
    XMStoreFloat3(reinterpret_cast<XMFLOAT3*>(&R.m10), yaxis);
    XMStoreFloat3(reinterpret_cast<XMFLOAT3*>(&R.m20), zaxis);
    R.m03 = R.m13 = R.m23 = 0.f;
    R.m30 = position.x;
    R.m31 = position.y;
    R.m32 = position.z;
    R.m33 = 1.f;
    return R;
}
void Float4x4::Lerp(const Float4x4& M1, const Float4x4& M2, float t, Float4x4& result)
{
    using namespace DirectX;
    XMVECTOR x1 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M1.m00));
    XMVECTOR x2 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M1.m10));
    XMVECTOR x3 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M1.m20));
    XMVECTOR x4 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M1.m30));

    XMVECTOR y1 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M2.m00));
    XMVECTOR y2 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M2.m10));
    XMVECTOR y3 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M2.m20));
    XMVECTOR y4 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M2.m30));

    x1 = XMVectorLerp(x1, y1, t);
    x2 = XMVectorLerp(x2, y2, t);
    x3 = XMVectorLerp(x3, y3, t);
    x4 = XMVectorLerp(x4, y4, t);

    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&result.m00), x1);
    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&result.m10), x2);
    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&result.m20), x3);
    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&result.m30), x4);
}


 Float4x4 Float4x4::Lerp(const Float4x4& M1, const Float4x4& M2, float t)
{
    using namespace DirectX;
    XMVECTOR x1 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M1.m00));
    XMVECTOR x2 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M1.m10));
    XMVECTOR x3 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M1.m20));
    XMVECTOR x4 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M1.m30));

    XMVECTOR y1 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M2.m00));
    XMVECTOR y2 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M2.m10));
    XMVECTOR y3 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M2.m20));
    XMVECTOR y4 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M2.m30));

    x1 = XMVectorLerp(x1, y1, t);
    x2 = XMVectorLerp(x2, y2, t);
    x3 = XMVectorLerp(x3, y3, t);
    x4 = XMVectorLerp(x4, y4, t);

    Float4x4 result;
    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&result.m00), x1);
    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&result.m10), x2);
    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&result.m20), x3);
    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&result.m30), x4);
    return result;
}
}   // namespace cross