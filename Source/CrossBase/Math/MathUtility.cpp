#include "CrossMath.h"

namespace cross {
Float3 MathUtils::VInterpTo(const Float3& Current, const Float3& Target, float DeltaTime, float InterpSpeed)
{
    return VInterpToImpl(Current, Target, DeltaTime, InterpSpeed);
}

Float3A MathUtils::VInterpTo(const Float3A& Current, const Float3A& Target, float DeltaTime, float InterpSpeed)
{
    return VInterpToImpl(Current, Target, DeltaTime, InterpSpeed);
}

Double3 MathUtils::VInterpTo(const Double3& Current, const Double3& Target, float DeltaTime, float InterpSpeed)
{
    return VInterpToImpl(Current, Target, DeltaTime, InterpSpeed);
}

Double3A MathUtils::VInterpTo(const Double3A& Current, const Double3A& Target, float DeltaTime, float InterpSpeed)
{
    return VInterpToImpl(Current, Target, DeltaTime, InterpSpeed);
}

Float3 MathUtils::RInterpTo(const Float3& Current, const Float3& Target, float DeltaTime, float InterpSpeed)
{
    // if DeltaTime is 0, do not perform any interpolation (Location was already calculated for that frame)
    if (DeltaTime == 0.f || Current == Target)
    {
        return Current;
    }

    // If no interp speed, jump to target value
    if (InterpSpeed <= 0.f)
    {
        return Target;
    }

    const float DeltaInterpSpeed = InterpSpeed * DeltaTime;

    const Float3 Delta = (Target - Current).Normalized();

    // If steps are too small, just return Target and assume we have reached our destination.
    if (Delta.IsNearlyZero())
    {
        return Target;
    }

    // Delta Move, Clamp so we do not over shoot.
    const Float3 DeltaMove = Delta * Clamp<float>(DeltaInterpSpeed, 0.f, 1.f);
    return (Current + DeltaMove).Normalized();
}

Quaternion MathUtils::QInterpTo(const Quaternion& Current, const Quaternion& Target, float DeltaTime, float InterpSpeed)
{
    return QInterpToImpl(Current, Target, DeltaTime, InterpSpeed);
}

QuaternionA MathUtils::QInterpTo(const QuaternionA& Current, const QuaternionA& Target, float DeltaTime, float InterpSpeed)
{
    return QInterpToImpl(Current, Target, DeltaTime, InterpSpeed);
}

Quaternion64 MathUtils::QInterpTo(const Quaternion64& Current, const Quaternion64& Target, float DeltaTime, float InterpSpeed)
{
    return QInterpToImpl(Current, Target, DeltaTime, InterpSpeed);
}

Quaternion64A MathUtils::QInterpTo(const Quaternion64A& Current, const Quaternion64A& Target, float DeltaTime, float InterpSpeed)
{
    return QInterpToImpl(Current, Target, DeltaTime, InterpSpeed);
}

float MathUtils::FInterpTo(const float& Current, const float& Target, float DeltaTime, float InterpSpeed) 
{
    return FInterpToImpl(Current, Target, DeltaTime, InterpSpeed);
}
cross::Float4x4 cross::math::MatrixUtil::Math_MatrixInverse(cross::Float4x4 matrix)
{
    cross::Float4x4 matrix2 = matrix.Inverted();
    return matrix2;
 }

cross::Double4x4 cross::math::MatrixUtil::Math_MatrixInverse_d(cross::Double4x4 matrix)
 {
    cross::Double4x4 matrix2 = matrix.Inverted();
    return matrix2;
}

cross::Float4x4 cross::math::MatrixUtil::Math_MatrixCompose(cross::Float3 scaling, cross::Quaternion rotation, cross::Float3 translation)
{
    cross::Float4x4 matrix;
    matrix = cross::Float4x4::Compose(scaling, rotation, translation);
    return matrix;
}

cross::math::TRS cross::math::MatrixUtil::Math_MatrixDecompose(cross::Float4x4 matrix)
{
    cross::math::TRS trs;
    cross::Quaternion q;
    matrix.Decompose(trs.scaling, q, trs.translation);
    trs.rotation.x = q.x;
    trs.rotation.y = q.y;
    trs.rotation.z = q.z;
    trs.rotation.w = q.w;
    return trs;
}

cross::Double4x4 cross::math::MatrixUtil::Math_MatrixComposeD(cross::Double3 scaling, cross::Quaternion64 rotation, cross::Double3 translation)
{
    cross::Double4x4 matrix;
    matrix = cross::Double4x4::Compose(scaling, rotation, translation);
    return matrix;
}

cross::math::TRSD  cross::math::MatrixUtil::Math_MatrixDecomposeD(cross::Double4x4 matrix)
{
    TRSD trs;
    cross::Quaternion64 q;
    matrix.Decompose(trs.scaling, q, trs.translation);
    trs.rotation.x = q.x;
    trs.rotation.y = q.y;
    trs.rotation.z = q.z;
    trs.rotation.w = q.w;
    return trs;
}
}   // namespace cross
