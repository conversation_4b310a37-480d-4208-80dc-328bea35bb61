#pragma once
#if defined(ANDROID) || defined(__ANDROID__)
#include "PCH/AndroidPrefix.h"
#endif
#include <unordered_map>
#include "CrossBase/Template/TypeCast.hpp"
#include "DirectXMath/Inc/DirectXMath.h"
#include "DirectXMath/Inc/DirectXCollision.h"
#include "DirectXMath/SHMath/DirectXSH.h"
#include "Serialization/SerializeNode.h"
#include "DXMathDoubleExt.h"
#include "CrossBase/CEMetaMacros.h"

#define ENABLE_SIMD_NODE_TRANSFORM 1
namespace cross {
#define MATH_CALL XM_CALLCONV
#pragma warning(push)
#pragma warning(disable : 4324 4459)
/** Generic axis enum */
namespace Axis {
    enum CEMeta(Reflect) Type
    {
        None = 0,
        X = 1,
        Y = 2,
        Z = 4,

        Screen = 8,
        XY = X | Y,
        XZ = X | Z,
        YZ = Y | Z,
        XYZ = X | Y | Z,
        All = XYZ | Screen,

        // alias over Axis YZ since it isn't used when the z-rotation widget is being used
        ZRotation = YZ,

        // alias over Screen since it isn't used when the 2d translate rotate widget is being used
        Rotate2D = Screen,
    };

    static Axis::Type Operate(const std::string& str);
    
}   // namespace Axis

struct Float3;
struct Float3A;
struct Quaternion;
struct QuaternionA;

struct Double3;
struct Double3A;
struct Quaternion64;
struct Quaternion64A;

class CROSS_BASE_API MathUtils
{
public:
    static constexpr float MathPi = 3.141592654f;
    static constexpr float Math2Pi = 6.283185307f;
    static constexpr float Math1DivPi = 0.318309886f;
    static constexpr float Math1Div2Pi = 0.159154943f;
    static constexpr float MathPiDiv2 = 1.570796327f;
    static constexpr float MathPiDiv4 = 0.785398163f;
    static constexpr float MathEps = 1.192092896e-7f;
    static constexpr float MathSmallNumber = 1e-6f;
    static constexpr double MathPiD = DirectX::XM_PI_D;
    static constexpr double Math2PiD = DirectX::XM_2PI_D;
    static constexpr double Math1DivPiD = DirectX::XM_1DIVPI_D;
    static constexpr double Math1Div2PiD = DirectX::XM_1DIV2PI_D;
    static constexpr double MathPiDiv2D = DirectX::XM_PIDIV2_D;
    static constexpr double MathPiDiv4D = DirectX::XM_PIDIV4_D;
    static constexpr double MathEpsD = 2.2204460492503131e-016;
    static constexpr double MathSmallNumberD = 1e-6;

    static inline float ConvertToRadians(float fDegrees) noexcept
    {
        return DirectX::XMConvertToRadians(fDegrees);
    }
    static inline float ConvertToDegrees(float fRadians) noexcept
    {
        return DirectX::XMConvertToDegrees(fRadians);
    }

    static inline double ConvertToRadians(double fDegrees) noexcept
    {
        return DirectX::XMConvertToRadians(fDegrees);
    }
    static inline double ConvertToDegrees(double fRadians) noexcept
    {
        return DirectX::XMConvertToDegrees(fRadians);
    }

    static inline void SinCos(float radian, float& sin, float& cos)
    {
        return DirectX::XMScalarSinCos(&sin, &cos, radian);
    }

    static inline void SinCos(double radian, double& sin, double& cos)
    {
        return DirectX::XM64ScalarSinCos(&sin, &cos, radian);
    }

    CEFunction(Editor, Script)
    static bool IsNearlyEqual(float a, float b, float tolerance = 1.e-8f)
    {
        return Abs<float>(a - b) <= tolerance;
    }

    CEFunction(Editor, Script)
    static bool IsNearlyZero(float value, float tolerance = 1.e-8f)
    {
        return Abs<float>(value) <= tolerance;
    }

    CEFunction(Editor, Script)
    static float Lerp(float A, float B, float Alpha)
    {
        return A + Alpha * (B - A);
    }

    CEFunction(Editor, Script)
    static float Fmod(float A, float B)
    {
        return fmod(A, B);
    }

    template<class T>
    static constexpr FORCEINLINE T Square(const T x)
    {
        return x * x;
    }

    template<class T>
    static constexpr FORCEINLINE T Abs(const T x)
    {
        return (x >= (T)0) ? x : -x;
    }

    template<class T>
    static constexpr FORCEINLINE T Sign(const T x)
    {
        return (x > (T)0) ? (T)1 : ((x < (T)0) ? (T)-1 : (T)0);
    }

    template<class T>
    static constexpr FORCEINLINE T Min(const T x, const T y)
    {
        return (x >= y) ? y : x;
    }

    template<class T>
    static constexpr FORCEINLINE T Max(const T x, const T y)
    {
        return (x >= y) ? x : y;
    }

    template<class T>
    static constexpr FORCEINLINE T Clamp(const T x, const T A, const T B)
    {
        return x < A ? A : x > B ? B : x;
    }

    template<class T>
    static constexpr FORCEINLINE bool IsPowerOfTwo(T value)
    {
        return ((value & (value - 1)) == (T)0);
    }

    // Performs a linear interpolation between two values, Alpha ranges from 0-1. Handles full numeric range of T
    template<class T>
    static constexpr FORCEINLINE T LerpStable(const T& A, const T& B, float Alpha)
    {
        return (T)((A * (1.0f - Alpha)) + (B * Alpha));
    }
    enum class AngleType
    {
        Degree,
        Radian,
    };

    template<AngleType type, class T = double>
    struct Angle
    {
        static constexpr T PI_VALUE() noexcept
        {
            return T();
        }
    };

    template<class T>
    struct Angle<AngleType::Degree, T>
    {
        static constexpr T PI_VALUE() noexcept
        {
            return 180.0;
        }
    };

    template<class T>
    struct Angle<AngleType::Radian, T>
    {
        static constexpr T PI_VALUE() noexcept
        {
            return MathPi;
        }
    };

    static inline float UnwindRadians(float A)
    {
        while (A > MathPi)
        {
            A -= (MathPi * 2.0f);
        }

        while (A < -MathPi)
        {
            A += (MathPi * 2.0f);
        }

        return A;
    }

    static inline double UnwindRadians(double A)
    {
        while (A > MathPiD)
        {
            A -= (MathPiD * 2.0);
        }

        while (A < -MathPiD)
        {
            A += (MathPiD * 2.0);
        }

        return A;
    }
    // get delta angle between A1 and A2, map it to [-PI/-180,PI/180]
    template<AngleType angle, class T = double>
    static T FindDeltaAngle(T A1, T A2)
    {
        // Find the difference
        T Delta = A2 - A1;

        T Pi_value = Angle<angle, T>::PI_VALUE();
        // If change is larger than 180
        if (Delta > Pi_value)
        {
            // Flip to negative equivalent
            Delta = Delta - T(2.0) * Pi_value;
        }
        else if (Delta < -Pi_value)
        {
            // Otherwise, if change is smaller than -180
            // Flip to positive equivalent
            Delta = Delta + T(2.0) * Pi_value;
        }

        // Return delta in [-180/-PI,180/PI] range
        return Delta;
    }

    /// <summary>
    ///  Lerp between two angles and map result to [-PI,PI] or [-180,180]
    /// </summary>
    /// <param name="AngleA"></param>
    /// <param name="AngleB"></param>
    /// <param name="Progress"></param>
    /// <returns></returns>

    template<AngleType angle, class T = double>
    static T LerpAngle(T AngleA, T AngleB, float Progress)
    {
        T Max = T(2.0) * Angle<angle, T>::PI_VALUE();
        T DeltaAngle = fmod((AngleB - AngleA), Max);

        return AngleA + (fmod(T(2.0) * DeltaAngle, Max) - DeltaAngle) * Progress;
    }

    // Interpolate vector from Current to Target. Scaled by distance to Target, so it has a strong start speed and ease out.
    CEFunction(Editor, Script) static Float3 VInterpTo(const Float3& Current, const Float3& Target, float DeltaTime, float InterpSpeed);
    CEFunction(Editor, Script) static Float3A VInterpTo(const Float3A& Current, const Float3A& Target, float DeltaTime, float InterpSpeed);
    CEFunction(Editor, Script) static Double3 VInterpTo(const Double3& Current, const Double3& Target, float DeltaTime, float InterpSpeed);
    CEFunction(Editor, Script) static Double3A VInterpTo(const Double3A& Current, const Double3A& Target, float DeltaTime, float InterpSpeed);

    // Interpolate rotator from Current to Target. Scaled by distance to Target, so it has a strong start speed and ease out.
    CEFunction(Editor, Script) static Float3 RInterpTo(const Float3& Current, const Float3& Target, float DeltaTime, float InterpSpeed);

    // Interpolate quaternion from Current to Target. Scaled by angle to Target, so it has a strong start speed and ease out.
    CEFunction(Editor, Script) static Quaternion QInterpTo(const Quaternion& Current, const Quaternion& Target, float DeltaTime, float InterpSpeed);
    CEFunction(Editor, Script) static QuaternionA QInterpTo(const QuaternionA& Current, const QuaternionA& Target, float DeltaTime, float InterpSpeed);
    CEFunction(Editor, Script) static Quaternion64 QInterpTo(const Quaternion64& Current, const Quaternion64& Target, float DeltaTime, float InterpSpeed);
    CEFunction(Editor, Script) static Quaternion64A QInterpTo(const Quaternion64A& Current, const Quaternion64A& Target, float DeltaTime, float InterpSpeed);

    // Interpolate float from Current to Target. Scaled by distance to Target, so it has a strong start speed and ease out.
    CEFunction(Editor, Script) static float FInterpTo(const float& Current, const float& Target, float DeltaTime, float InterpSpeed);

    template<typename T>
    static T VInterpToImpl(const T& Current, const T& Target, float DeltaTime, float InterpSpeed)
    {
        static constexpr float KINDA_SMALL_NUMBER = (1.e-4f);
        // If no interp speed, jump to target value
        if (InterpSpeed <= 0.f)
        {
            return Target;
        }

        // Distance to reach
        const T Dist = Target - Current;

        // If distance is too small, just set the desired location
        if (Dist.LengthSquared() < KINDA_SMALL_NUMBER)
        {
            return Target;
        }

        // Delta Move, Clamp so we do not over shoot.
        const T DeltaMove = Dist * Clamp<float>(DeltaTime * InterpSpeed, 0.f, 1.f);

        return Current + DeltaMove;
    }

    template<typename T>
    static T QInterpToImpl(const T& Current, const T& Target, float DeltaTime, float InterpSpeed)
    {
        // If no interp speed, jump to target value
        if (InterpSpeed <= 0.f)
        {
            return Target;
        }

        // If the values are nearly equal, just return Target and assume we have reached our destination.
        if (Current == Target)
        {
            return Target;
        }

        return T::Slerp(Current, Target, Clamp<float>(InterpSpeed * DeltaTime, 0.f, 1.f));
    }

    template<typename T1, typename T2 = T1, typename T3 = T2, typename T4 = T3>
    static auto FInterpToImpl(T1 Current, T2 Target, T3 DeltaTime, T4 InterpSpeed)
    {
        using RetType = decltype(T1() * T2() * T3() * T4());
        static constexpr float SMALL_NUMBER = (1.e-8f);

        // If no interp speed, jump to target value
        if (InterpSpeed <= 0.f)
        {
            return static_cast<RetType>(Target);
        }

        // Distance to reach
        const RetType Dist = Target - Current;

        // If distance is too small, just set the desired location
        if (Square(Dist) < SMALL_NUMBER)
        {
            return static_cast<RetType>(Target);
        }

        // Delta Move, Clamp so we do not over shoot.
        const RetType DeltaMove = Dist * Clamp<RetType>(DeltaTime * InterpSpeed, 0.0f, 1.0f);

        return Current + DeltaMove;
    }
};

inline constexpr bool ComparisonAllTrue(uint32_t CR) noexcept
{
    return DirectX::XMComparisonAllTrue(CR);
}
inline constexpr bool ComparisonAnyTrue(uint32_t CR) noexcept
{
    return DirectX::XMComparisonAnyTrue(CR);
}
inline constexpr bool ComparisonAllFalse(uint32_t CR) noexcept
{
    return DirectX::XMComparisonAllFalse(CR);
}
inline constexpr bool ComparisonAnyFalse(uint32_t CR) noexcept
{
    return DirectX::XMComparisonAnyFalse(CR);
}
inline constexpr bool ComparisonMixed(uint32_t CR) noexcept
{
    return DirectX::XMComparisonMixed(CR);
}
inline constexpr bool ComparisonAllInBounds(uint32_t CR) noexcept
{
    return DirectX::XMComparisonAllInBounds(CR);
}
inline constexpr bool ComparisonAnyOutOfBounds(uint32_t CR) noexcept
{
    return DirectX::XMComparisonAnyOutOfBounds(CR);
}

using SIMDVector4 = DirectX::XMVECTOR;
using SIMDVec4Param1 = DirectX::FXMVECTOR;
using SIMDVec4Param4 = DirectX::GXMVECTOR;
using SIMDVec4Param5 = DirectX::HXMVECTOR;
using SIMDVec4Param7 = DirectX::CXMVECTOR;

using VectorF4A = DirectX::XMVECTORF32;
using VectorI32A = DirectX::XMVECTORI32;
using VectorU32A = DirectX::XMVECTORU32;
using VectorU8A = DirectX::XMVECTORU8;

using SIMDMatrix = DirectX::XMMATRIX;
using SIMDMatrixParam0 = DirectX::FXMMATRIX;
using SIMDMatrixParam1 = DirectX::CXMMATRIX;

struct Float4x4;
struct Quaternion;
struct Quaternion64;

//****************************************************************************
// Vec2
//****************************************************************************
template<class Element>
struct Vec2
{
    Element x{0};
    Element y{0};

    Vec2() = default;
    Vec2(const Vec2&) = default;
    Vec2& operator=(const Vec2&) = default;
    Vec2(Vec2&&) = default;
    Vec2& operator=(Vec2&&) = default;
    constexpr Vec2(Element _x, Element _y) noexcept
        : x(_x)
        , y(_y)
    {}
    explicit Vec2(const Element* pArray) noexcept
        : x(pArray[0])
        , y(pArray[1])
    {}
    const Element* data() const
    {
        return &x;
    }

    Element* data()
    {
        return &x;
    }

    static constexpr size_t Dim()
    {
        return 2;
    }
};

typedef Vec2<UInt32> UInt2;
typedef Vec2<SInt32> Int2;
typedef Vec2<UInt16> UShort2;
typedef Vec2<SInt16> Short2;

//****************************************************************************
// Vec3
//****************************************************************************
template<class Element>
struct Vec3
{
    Element x{0};
    Element y{0};
    Element z{0};

    Vec3() = default;
    Vec3(const Vec3&) = default;
    Vec3& operator=(const Vec3&) = default;
    Vec3(Vec3&&) = default;
    Vec3& operator=(Vec3&&) = default;
    constexpr Vec3(Element _x, Element _y, Element _z) noexcept
        : x(_x)
        , y(_y)
        , z(_z)
    {}

    template<typename T>
    explicit Vec3(const Vec3<T>& v): x(static_cast<Element>(v.x)), y(static_cast<Element>(v.y)), z(static_cast<Element>(v.z)) {}

    explicit Vec3(const Element* pArray) noexcept
        : x(pArray[0])
        , y(pArray[1])
        , z(pArray[2])
    {}
    const Element* data() const
    {
        return &x;
    }
    Element* data()
    {
        return &x;
    }
    static constexpr size_t Dim()
    {
        return 3;
    }
};

typedef Vec3<UInt32> UInt3;
typedef Vec3<SInt32> Int3;
typedef Vec3<UInt16> UShort3;
typedef Vec3<SInt16> Short3;
typedef Vec3<UInt8> UChar3;
typedef Vec3<SInt8> Char3;

//****************************************************************************
// Vec4
//****************************************************************************
template<class Element>
struct Vec4
{
    Element x{0};
    Element y{0};
    Element z{0};
    Element w{0};

    Vec4() = default;
    Vec4(const Vec4&) = default;
    Vec4& operator=(const Vec4&) = default;
    Vec4(Vec4&&) = default;
    Vec4& operator=(Vec4&&) = default;
    constexpr Vec4(Element _x, Element _y, Element _z, Element _w) noexcept
        : x(_x)
        , y(_y)
        , z(_z)
        , w(_w)
    {}
    explicit Vec4(const Element* pArray) noexcept
        : x(pArray[0])
        , y(pArray[1])
        , z(pArray[2])
        , w(pArray[3])
    {}
    const Element* data() const
    {
        return &x;
    }

    Element* data()
    {
        return &x;
    }

    static constexpr size_t Dim()
    {
        return 4;
    }
};

typedef Vec4<int> Int4;
typedef Vec4<SInt16> Short4;
typedef Vec4<UInt8> UChar4;
typedef Vec4<SInt8> Char4;
typedef Vec4<UInt16> UShort4;
typedef Vec4<UInt32> UInt4;

//****************************************************************************
// Float2
//****************************************************************************
struct Float2;
Float2 operator+(const Float2& v1, const Float2& v2);
Float2 operator+(const Float2& v1, float v2);
Float2 operator-(const Float2& v1, const Float2& v2);
Float2 operator-(const Float2& v1, float v2);
Float2 operator*(const Float2& v1, const Float2& v2);
Float2 operator*(const Float2& v, float S);
Float2 operator/(const Float2& v1, const Float2& v2);
Float2 operator/(const Float2& v1, float S);
struct CROSS_BASE_API CEMeta(Cli, Puerts, WorkflowType) Float2
{
    CEProperty(Editor, ScriptReadWrite)
    float x{0.0f}, y{0.0f};

    CEMeta(Editor, ScriptCallable) Float2() = default;
    CEMeta(Editor, ScriptCallable) Float2(const Float2&) = default;
    Float2& operator=(const Float2&) = default;
    Float2(Float2&&) = default;
    Float2& operator=(Float2&&) = default;

    CEMeta(Editor, ScriptCallable) constexpr Float2(float _x, float _y) noexcept
        : x(_x)
        , y(_y)
    {}
    explicit Float2(const float* pArray) noexcept
        : x(pArray[0])
        , y(pArray[1])
    {}
    float* data()
    {
        return &x;
    }
    const float* data() const
    {
        return &x;
    }

    CEMeta(Editor, ScriptCallable) void Set(float _x, float _y)
    {
        x = _x;
        y = _y;
    }

    float& operator[](int i)
    {
        return reinterpret_cast<float*>(&x)[i];
    }

    inline bool operator==(const Float2& v) const
    {
        return (x == v.x) && (y == v.y);
    }

    inline bool operator!=(const Float2& v) const
    {
        return (x != v.x) || (y != v.y);
    }

    inline Float2& operator+=(const Float2& v)
    {
        x += v.x;
        y += v.y;
        return *this;
    }

    inline Float2& operator-=(const Float2& v)
    {
        x -= v.x;
        y -= v.y;
        return *this;
    }

    inline Float2& operator*=(const Float2& v)
    {
        x *= v.x;
        y *= v.y;
        return *this;
    }

    inline Float2& operator*=(float s)
    {
        x *= s;
        y *= s;
        return *this;
    }

    inline Float2& operator/=(const Float2& v)
    {
        x /= v.x;
        y /= v.y;
        return *this;
    }

    inline Float2& operator/=(float s)
    {
        float inv = 1.0f / s;
        x *= inv;
        y *= inv;
        return *this;
    }

    inline uint32_t EqualR(const Float2& v2) const
    {
        uint32_t CR = 0;
        if ((x == v2.x) && (y == v2.y))
        {
            CR = DirectX::XM_CRMASK_CR6TRUE;
        }
        else if ((x != v2.x) && (y != v2.y))
        {
            CR = DirectX::XM_CRMASK_CR6FALSE;
        }
        return CR;
    }

    CEMeta(Editor, ScriptCallable)
    static float Distance(const Float2& v1, const Float2& v2);

    bool NearEqual(const Float2& v, const Float2& epsilon) const;
   
    static inline uint32_t GreaterR(const Float2& v1, const Float2& v2)
    {
        uint32_t CR = 0;
        if ((v1.x > v2.x) && (v1.y > v2.y))
        {
            CR = DirectX::XM_CRMASK_CR6TRUE;
        }
        else if ((v1.x <= v2.x) && (v1.y <= v2.y))
        {
            CR = DirectX::XM_CRMASK_CR6FALSE;
        }
        return CR;
    }

    static inline uint32_t GreaterOrEqualR(const Float2& v1, const Float2& v2)
    {
        uint32_t CR = 0;
        if ((v1.x >= v2.x) && (v1.y >= v2.y))
        {
            CR = DirectX::XM_CRMASK_CR6TRUE;
        }
        else if ((v1.x < v2.x) && (v1.y < v2.y))
        {
            CR = DirectX::XM_CRMASK_CR6FALSE;
        }
        return CR;
    }

    inline SerializeNode Serialize() const
    {
        return SerializeNode{"x"_k = x, "y"_k = y};
    }

    inline void Deserialize(const DeserializeNode& json)
    {
        // todo(xtnwang), remove array json in the future
        if (json.IsArray())
        {
            this->x = json[0].AsFloat();
            this->y = json[1].AsFloat();
        }
        else
        {
            this->x = json["x"].AsFloat();
            this->y = json["y"].AsFloat();
        }
    }

    std::string ToString() const
    {
        std::string ret = "(" + std::to_string(x) + "," + std::to_string(y) + ")";
        return ret;
    }

    CEMeta(Editor, ScriptCallable) inline bool IsNaN() const
    {
        return (isnan(x) || isnan(y));
    }

    CEMeta(Editor, ScriptCallable) inline bool IsInfinite() const
    {
        return (isinf(x) || isinf(y));
    }

    CEMeta(Editor, ScriptCallable) inline bool InBounds(const Float2& bounds) const
    {
        return ((x <= bounds.x && x >= -bounds.x) && (y <= bounds.y && y >= -bounds.y));
    }

    CEMeta(Editor, ScriptCallable) inline float Length() const
    {
        return sqrtf(x * x + y * y);
    }

    CEMeta(Editor, ScriptCallable) inline float LengthSq() const
    {
        return (x * x + y * y);
    }

    CEMeta(Editor, ScriptCallable) inline float Dot(const Float2& v) const
    {
        return (x * v.x + y * v.y);
    }

    CEMeta(Editor, ScriptCallable) float Cross(const Float2& v) const
    {
        return x * v.y - y * v.x;
    }

    CEMeta(Editor, ScriptCallable) inline void Normalize()
    {
        float length = Length();
        if (length > 0)
            length = 1.0f / length;

        x *= length;
        y *= length;
    }

    CEMeta(Editor, ScriptCallable) inline Float2 Normalized() const
    {
        float length = Length();
        if (length > 0)
            length = 1.0f / length;

        return Float2(x * length, y * length);
    }

    CEMeta(Editor, ScriptCallable) inline void Clamp(const Float2& vmin, const Float2& vmax)
    {
        x = (x >= vmin.x) ? x : vmin.x;
        x = (x <= vmax.x) ? x : vmax.x;
        y = (y >= vmin.y) ? y : vmin.y;
        y = (y <= vmax.y) ? y : vmax.y;
    }

    CEMeta(Editor, ScriptCallable) inline Float2 Clamp(const Float2& vmin, const Float2& vmax) const
    {
        Float2 result;
        float temp = x;
        temp = (temp >= vmin.x) ? temp : vmin.x;
        temp = (temp <= vmax.x) ? temp : vmax.x;
        result.x = temp;

        temp = y;
        temp = (y >= vmin.y) ? y : vmin.y;
        temp = (y <= vmax.y) ? y : vmax.y;
        result.y = temp;
        return result;
    }

    CEMeta(Editor, ScriptCallable) static inline Float2 Min(const Float2& v1, const Float2& v2)
    {
        Float2 result((v1.x <= v2.x) ? v1.x : v2.x, (v1.y <= v2.y) ? v1.y : v2.y);
        return result;
    }

    CEMeta(Editor, ScriptCallable) static inline Float2 Max(const Float2& v1, const Float2& v2)
    {
        Float2 result((v1.x >= v2.x) ? v1.x : v2.x, (v1.y >= v2.y) ? v1.y : v2.y);
        return result;
    }

    CEMeta(Editor, ScriptCallable) static inline Float2 Zero()
    {
        return {0.f, 0.f};
    }

    CEMeta(Editor, ScriptCallable) inline Float2 Add(const Float2& other) const
    {
        return *this + other;
    }

    CEMeta(Editor, ScriptCallable) inline Float2 Add(float other) const
    {
        return *this + other;
    }

    CEMeta(Editor, ScriptCallable) inline Float2 Sub(const Float2& other) const
    {
        return *this - other;
    }

    CEMeta(Editor, ScriptCallable) inline Float2 Sub(float other) const
    {
        return *this - other;
    }

    CEMeta(Editor, ScriptCallable) inline Float2 Mul(const Float2& other) const
    {
        return *this * other;
    }

    CEMeta(Editor, ScriptCallable) inline Float2 Mul(float other) const
    {
        return *this * other;
    }

    CEMeta(Editor, ScriptCallable) inline Float2 Div(const Float2& other) const
    {
        return *this / other;
    }

    CEMeta(Editor, ScriptCallable) inline Float2 Div(float other) const
    {
        return *this / other;
    }

    CEMeta(Editor, ScriptCallable)
    static Float2 CatmullRom(Float2 v1, Float2 v2, Float2 v3, Float2 v4, float t);
    

    // todo:
    // static inline _Float2 Reflect(const _Float2& incident, const _Float2& Normal) {}
    // static inline _Float2    Refract(const _Float2& Incident, const _Float2& Normal, float RefractionIndex) {}
    // static inline _Float2    RefractV(const _Float2& Incident, const _Float2& Normal, const _Float2& RefractionIndex)
    // {} static inline _Float2    Orthogonal(const _Float2& v) {} static inline _Float2    AngleBetweenNormalsEst(const
    // _Float2& N1, const _Float2& N2) {} static inline _Float2    AngleBetweenNormals(const _Float2& N1, const _Float2&
    // N2) {} static inline _Float2    AngleBetweenVectors(const _Float2& v1, const _Float2& v2) {} static inline
    // _Float2    LinePointDistance(const _Float2& LinePoint1, const _Float2& LinePoint2, _Float2 Point) {} static
    // inline _Float2    IntersectLine(const _Float2& Line1Point1, const _Float2& Line1Point2, _Float2 Line2Point1,
    // SIMDVec4Param4 Line2Point2) { } static inline _Float2    Transform(const _Float2& v, SIMDMatrixParam0 M) { }
    // static inline _Float2    Vector2TransformCoord(const _Float2& v, SIMDMatrixParam0 M) {}
    // static inline _Float2    Vector2TransformNormal(const _Float2& v, SIMDMatrixParam0 M) {}
};

// 2D Vector; 32 bit floating point components aligned on a 16 byte boundary
XM_ALIGNED_STRUCT(16) CEMeta(Puerts)
Float2A : public Float2
{
    Float2A() = default;

    CEMeta(Ignore)
    Float2A(const Float2A&) = default;
    Float2A& operator=(const Float2A&) = default;

    Float2A(Float2A &&) = default;
    Float2A& operator=(Float2A&&) = default;

    CEMeta(Ignore)
    Float2A(const Float2& vec)
        : Float2(vec)
    {}
    CEMeta(ScriptCallable)
    constexpr Float2A(float _x, float _y) noexcept
        : Float2(_x, _y)
    {}
    explicit Float2A(const float* pArray) noexcept
        : Float2(pArray)
    {}
};

inline Float2 operator+(const Float2& v1, const Float2& v2)
{
    return Float2(v1.x + v2.x, v1.y + v2.y);
}
inline Float2 operator+(const Float2& v1, float v2)
{
    return Float2(v1.x + v2, v1.y + v2);
}
inline Float2 operator-(const Float2& v1, const Float2& v2)
{
    return Float2(v1.x - v2.x, v1.y - v2.y);
}
inline Float2 operator-(const Float2& v1, float v2)
{
    return Float2(v1.x - v2, v1.y - v2);
}
inline Float2 operator*(const Float2& v1, const Float2& v2)
{
    return Float2(v1.x * v2.x, v1.y * v2.y);
}
inline Float2 operator*(const Float2& v, float S)
{
    return Float2(v.x * S, v.y * S);
}
inline Float2 operator*(float S, const Float2& v)
{
    return Float2(v.x * S, v.y * S);
}
inline Float2 operator/(const Float2& v1, const Float2& v2)
{
    return Float2(v1.x / v2.x, v1.y / v2.y);
}
inline Float2 operator/(const Float2& v1, float S)
{
    return Float2(v1.x / S, v1.y / S);
}
inline bool operator>(const Float2& v1, const Float2& v2)
{
    return ((v1.x > v2.x) && (v1.y > v2.y));
}
inline bool operator>=(const Float2& v1, const Float2& v2)
{
    return ((v1.x >= v2.x) && (v1.y >= v2.y));
}
inline bool operator<(const Float2& v1, const Float2& v2)
{
    return ((v1.x < v2.x) && (v1.y < v2.y));
}
inline bool operator<=(const Float2& v1, const Float2& v2)
{
    return ((v1.x <= v2.x) && (v1.y <= v2.y));
}

//****************************************************************************
// Float3
//****************************************************************************
struct Quaternion;
struct Float3;
Float3 operator+(const Float3& v1, const Float3& v2);
Float3 operator-(const Float3& v1, const Float3& v2);
Float3 operator*(const Float3& v1, const Float3& v2);
Float3 operator*(const Float3& v, float S);
Float3 operator/(const Float3& v1, const Float3& v2);
Float3 operator*(float S, const Float3& v);

struct CROSS_BASE_API CEMeta(Cli, Puerts, WorkflowType) Float3
{
    CEProperty(Editor, Reflect, ScriptReadWrite)
    float x{0.0f}, y{0.0f}, z{0.0f};

    CEMeta(Editor, ScriptCallable) Float3() = default;

    CEMeta(Editor, ScriptCallable) Float3(const Float3&) = default;
    Float3& operator=(const Float3&) = default;
    Float3(Float3&&) = default;
    Float3& operator=(Float3&&) = default;

    CEMeta(Editor, ScriptCallable)
    constexpr Float3(float _x, float _y, float _z) noexcept
        : x(_x)
        , y(_y)
        , z(_z)
    {}
    explicit Float3(const float* pArray) noexcept
        : x(pArray[0])
        , y(pArray[1])
        , z(pArray[2])
    {}

    CEMeta(Editor, ScriptCallable) bool Equal(const Float3& v) const
    {
        return *this == v;
    }

    inline bool operator==(const Float3& v) const
    {
        return (x == v.x) && (y == v.y) && (z == v.z);
    }
    inline bool operator!=(const Float3& v) const
    {
        return (x != v.x) || (y != v.y) || (z != v.z);
    }

    float operator[](int index) const
    {
        return (&x)[index];
    }
    float& operator[](int i)
    {
        return reinterpret_cast<float*>(&x)[i];
    }

    float* data()
    {
        return &x;
    }
    const float* data() const
    {
        return &x;
    }

    inline SerializeNode Serialize() const
    {
        SerializeNode node = {"x"_k = x, "y"_k = y, "z"_k = z};
        return node;
    }

    inline void Deserialize(const DeserializeNode& json)
    {
        this->x = json["x"].AsFloat();
        this->y = json["y"].AsFloat();
        this->z = json["z"].AsFloat();
    }

    std::string ToString() const
    {
        std::string ret = "(" + std::to_string(x) + "," + std::to_string(y) + "," + std::to_string(z) + ")";
        return ret;
    }

    CEMeta(Editor, ScriptCallable) Float3 ToRadian() const
    {
        return {MathUtils::ConvertToRadians(x), MathUtils::ConvertToRadians(y), MathUtils::ConvertToRadians(z)};
    }

    CEMeta(Editor, ScriptCallable) Float3 ToDegree() const
    {
        return {MathUtils::ConvertToDegrees(x), MathUtils::ConvertToDegrees(y), MathUtils::ConvertToDegrees(z)};
    }

    CEMeta(Editor, ScriptCallable) Float3 Abs() const
    {
        return {abs(x), abs(y), abs(z)};
    }

    CEMeta(Editor, ScriptCallable) Float3 Add(const Float3& v) const
    {
        return *this + v;
    }

    inline Float3& operator+=(const Float3& v)
    {
        x += v.x;
        y += v.y;
        z += v.z;
        return *this;
    }

    CEMeta(Editor, ScriptCallable) Float3 Subtract(const Float3& v) const
    {
        return *this - v;
    }

    inline Float3& operator-=(const Float3& v)
    {
        x -= v.x;
        y -= v.y;
        z -= v.z;
        return *this;
    }

    CEMeta(Editor, ScriptCallable) Float3 Mult(const Float3& v) const
    {
        return *this * v;
    }

    inline Float3& operator*=(const Float3& v)
    {
        x *= v.x;
        y *= v.y;
        z *= v.z;
        return *this;
    }

    CEMeta(Editor, ScriptCallable) Float3 Mult(float S) const
    {
        return *this * S;
    }

    inline Float3& operator*=(float S)
    {
        x *= S;
        y *= S;
        z *= S;
        return *this;
    }

    CEMeta(Editor, ScriptCallable) Float3 Divide(float S) const
    {
        return *this / S;
    }

    CEMeta(Editor, ScriptCallable) Float3 Divide(const Float3& v) const
    {
        return *this / v;
    }

    inline Float3& operator/=(float S)
    {
        //assert(S != 0.0f);
        S = 1 / S;
        x *= S;
        y *= S;
        z *= S;
        return *this;
    }

    inline Float3 operator/(float S) const
    {
       // assert(S != 0.0f);
        S = 1 / S;
        return Float3(x * S, y * S, z * S);
    }

    inline Float3 operator-() const
    {
        return Float3(-x, -y, -z);
    }

    inline Float3 operator^(const Float3 v) const
    {
        return Float3(y * v.z - z * v.y, z * v.x - x * v.z, x * v.y - y * v.x);
    }

    inline bool InBounds(const Float3& Bounds) const
    {
        return ((x <= Bounds.x) && (x >= -Bounds.x) && (y <= Bounds.y) && (y >= -Bounds.y) && (z <= Bounds.z) && (z >= -Bounds.z));
    }

    CEMeta(Editor, ScriptCallable) inline float Length() const
    {
        return sqrt(x * x + y * y + z * z);
    }

    CEMeta(Editor, ScriptCallable) inline float LengthXZ() const
    {
        return sqrt(x * x + z * z);
    }

    CEMeta(Editor, ScriptCallable) inline float LengthSquared() const
    {
        return (x * x + y * y + z * z);
    }

    CEMeta(Editor, ScriptCallable) inline float LengthSquaredXZ() const
    {
        return (x * x + z * z);
    }

    CEMeta(Editor, ScriptCallable) inline float Dot(const Float3& v) const
    {
        return (x * v.x + y * v.y + z * v.z);
    }

    inline void Cross(const Float3& v, Float3& result) const
    {
        result.x = (y * v.z) - (z * v.y);
        result.y = (z * v.x) - (x * v.z);
        result.z = (x * v.y) - (y * v.x);
    }

    CEMeta(Editor, ScriptCallable) inline Float3 Cross(const Float3& v) const
    {
        Float3 result;
        result.x = (y * v.z) - (z * v.y);
        result.y = (z * v.x) - (x * v.z);
        result.z = (x * v.y) - (y * v.x);
        return result;
    }

    CEMeta(Editor, ScriptCallable) Float3 Orthogonal() const
    {
        static const Float3 ZAxis(0, 0, 1);
        static const Float3 YAxis(0, 1, 0);
        static const Float3 XAxis(1, 0, 0);

        if (std::abs(Normalized().Dot(ZAxis)) > 1 - MathUtils::MathEps)
        {
            return Cross(XAxis);
        }
        else
        {
            return Cross(ZAxis);
        }
    }

    CEMeta(Editor, ScriptCallable)
    Float3 ProjectOnPlane(const Float3& n_normalized) const;
   

    CEMeta(Editor, ScriptCallable)
    void Normalize();
    

    CEMeta(Editor, ScriptCallable) 
    Float3 Normalized() const;
    

    Float3 StableNormalized(float& invMag);


    Float3 StableNormalized();
   
    void StableNormalize();
    

    Float3 SafeNormalXZ(float tolerance = 0.00001f) const;


    Float3 SafeNormal(float tolerance = 0.00001f) const;


    void ClampToMaxSize(float maxSize);


    Float3 ClampToMaxSize(float maxSize) const;


   void Clamp(const Float3& vmin, const Float3& vmax);

    CEMeta(Editor, ScriptCallable) inline Float3 Clamp(const Float3& vmin, const Float3& vmax) const
    {
        return Float3((vmin.x >= x) ? vmin.x : (vmax.x <= x ? vmax.x : x), (vmin.y >= y) ? vmin.y : (vmax.y <= y ? vmax.y : y), (vmin.z >= z) ? vmin.z : (vmax.z <= z ? vmax.z : z));
    }

    inline Float2 GetXY() const
    {
        return Float2(x, y);
    }

    inline const bool IsNaN() const
    {
        return isnan(x) || isnan(y) || isnan(z);
    }

    inline const bool IsInfinite() const
    {
        return isinf(x) || isinf(y) || isinf(z);
    }

    inline const bool IsNearlyZero(float tolerance = 1.e-4f) const
    {
        return MathUtils::Abs(x) <= tolerance && MathUtils::Abs(y) <= tolerance && MathUtils::Abs(z) <= tolerance;
    }

    inline const bool IsValid() const
    {
        return !IsNaN() && !IsInfinite();
    }
    
    CEMeta(Editor, ScriptCallable) static inline float Dot(const Float3& v1, const Float3& v2)
    {
        return (v1.x * v2.x + v1.y * v2.y + v1.z * v2.z);
    }

    CEMeta(Editor, ScriptCallable) static inline float Distance(const Float3& v1, const Float3& v2)
    {
        float deltaX = v1.x - v2.x;
        float deltaY = v1.y - v2.y;
        float deltaZ = v1.z - v2.z;
        return sqrt(deltaX * deltaX + deltaY * deltaY + deltaZ * deltaZ);
    }

    CEMeta(Editor, ScriptCallable) static inline float DistanceSquared(const Float3& v1, const Float3& v2)
    {
        float deltaX = v1.x - v2.x;
        float deltaY = v1.y - v2.y;
        float deltaZ = v1.z - v2.z;
        return (deltaX * deltaX + deltaY * deltaY + deltaZ * deltaZ);
    }

    CEMeta(Editor, ScriptCallable) static inline Float3 Min(const Float3& v1, const Float3& v2)
    {
        Float3 result((v1.x <= v2.x) ? v1.x : v2.x, (v1.y <= v2.y) ? v1.y : v2.y, (v1.z <= v2.z) ? v1.z : v2.z);
        return result;
    }

    CEMeta(Editor, ScriptCallable) static inline Float3 Max(const Float3& v1, const Float3& v2)
    {
        Float3 result((v1.x >= v2.x) ? v1.x : v2.x, (v1.y >= v2.y) ? v1.y : v2.y, (v1.z >= v2.z) ? v1.z : v2.z);
        return result;
    }

    CEMeta(Editor, ScriptCallable) static inline Float3 Lerp(const Float3& v1, const Float3& v2, float t)
    {
        Float3 result(v1.x + ((v2.x - v1.x) * t), v1.y + ((v2.y - v1.y) * t), v1.z + ((v2.z - v1.z) * t));
        return result;
    }

    CEMeta(Editor, ScriptCallable) static inline Float3 Zero()
    {
        return Float3(0.0f, 0.0f, 0.0f);
    }

    CEMeta(Editor, ScriptCallable) static inline Float3 One()
    {
        return Float3(1.0f, 1.0f, 1.0f);
    }

    CEMeta(Editor, ScriptCallable) 
    static Float3 SmoothStep(const Float3& v1, const Float3& v2, float t);
 

    CEMeta(Editor, ScriptCallable) 
    static Float3 Barycentric(const Float3& v1, const Float3& v2, const Float3& v3, float f, float g);

    CEMeta(Editor, ScriptCallable)
    static Float3 CatmullRom(const Float3& v1, const Float3& v2, const Float3& v3, const Float3& v4, float t);


    CEMeta(Editor, ScriptCallable)
    static Float3 Hermite(const Float3& v1, const Float3& t1, const Float3& v2, const Float3& t2, float t);


    CEMeta(Editor, ScriptCallable) 
    static  Float3 Reflect(const Float3& ivec, const Float3& nvec);
    

    CEMeta(Editor, ScriptCallable)
    static Float3 Refract(const Float3& ivec, const Float3& nvec, float refractionIndex);

    CEMeta(Editor, ScriptCallable) static inline Float3 Transform(const Float3& v, const Quaternion& quat)
    {
        using namespace DirectX;
        XMVECTOR V3 = XMLoadFloat3(reinterpret_cast<const XMFLOAT3*>(&v));
        XMVECTOR Q = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&quat));

        Float3 result;
        DirectX::XMStoreFloat3(reinterpret_cast<XMFLOAT3*>(&result), DirectX::XMVector3Rotate(V3, Q));

        return result;
    }

    CEMeta(Editor, ScriptCallable) static inline Float3 Transform(const Float3& v, const Float4x4& m)
    {
        using namespace DirectX;
        XMVECTOR V3 = XMLoadFloat3(reinterpret_cast<const XMFLOAT3*>(&v));
        XMMATRIX M4 = XMLoadFloat4x4(reinterpret_cast<const XMFLOAT4X4*>(&m));

        Float3 result;
        DirectX::XMStoreFloat3(reinterpret_cast<XMFLOAT3*>(&result), DirectX::XMVector3Transform(V3, M4));

        return result;
    }

    CEMeta(Editor, ScriptCallable) static inline Float3 TransformNormal(const Float3& v, const Float4x4& m)
    {
        using namespace DirectX;
        XMVECTOR V3 = XMLoadFloat3(reinterpret_cast<const XMFLOAT3*>(&v));
        XMMATRIX M4 = XMLoadFloat4x4(reinterpret_cast<const XMFLOAT4X4*>(&m));

        Float3 result;
        DirectX::XMStoreFloat3(reinterpret_cast<XMFLOAT3*>(&result), DirectX::XMVector3TransformNormal(V3, M4));

        return result;
    }

    // Computes the radian angle between two 3D vectors.
    CEMeta(Editor, ScriptCallable) static inline float AngleBetweenVectors(const Float3& v1, const Float3& v2)
    {
        using namespace DirectX;
        XMVECTOR V1 = XMLoadFloat3(reinterpret_cast<const XMFLOAT3*>(&v1));
        XMVECTOR V2 = XMLoadFloat3(reinterpret_cast<const XMFLOAT3*>(&v2));

        auto re = XMVector3AngleBetweenVectors(V1, V2);
        return XMVectorGetX(re);
    }

    CEMeta(Editor, ScriptCallable) static inline Float3 PointPlaneProject(const Float3& point, const Float3& planeBase, const Float3& planeNormal)
    {
        return point - ((point - planeBase).Dot(planeNormal)) * planeNormal;
    }
};

// 3D Vector; 32 bit floating point components aligned on a 16 byte boundary
XM_ALIGNED_STRUCT(16) CEMeta(Puerts)
Float3A : public Float3
{
    CEMetaInternal(Reflect)

    Float3A() = default;

    CEMeta(Ignore)
    Float3A(const Float3A&) = default;
    Float3A& operator=(const Float3A&) = default;
    Float3A(Float3A &&) = default;
    Float3A& operator=(Float3A&&) = default;

    CEMeta(Ignore)
    Float3A(const Float3& vec)
        : Float3(vec)
    {}
    Float3A& operator=(const Float3& v)
    {
        this->x = v.x;
        this->y = v.y;
        this->z = v.z;
        return *this;
    }
    CEMeta(Editor, ScriptCallable) float X()
    {
        return x;
    }
    CEMeta(Editor, ScriptCallable) float Y()
    {
        return y;
    }
    CEMeta(Editor, ScriptCallable) float Z()
    {
        return z;
    }
    CEMeta(Editor, ScriptCallable) bool Equal(const Float3A& other) const
    {
        return *this == other;
    }
    CEMeta(Editor, ScriptCallable)
    constexpr Float3A(float _x, float _y, float _z) noexcept
        : Float3(_x, _y, _z)
    {}
    explicit Float3A(const float* pArray) noexcept
        : Float3(pArray)
    {}
};

inline Float3 operator+(const Float3& v1, const Float3& v2)
{
    return Float3(v1.x + v2.x, v1.y + v2.y, v1.z + v2.z);
}

inline Float3 operator-(const Float3& v1, const Float3& v2)
{
    return Float3(v1.x - v2.x, v1.y - v2.y, v1.z - v2.z);
}

inline Float3 operator*(const Float3& v1, const Float3& v2)
{
    return Float3(v1.x * v2.x, v1.y * v2.y, v1.z * v2.z);
}

inline Float3 operator*(const Float3& v, float S)
{
    return Float3(v.x * S, v.y * S, v.z * S);
}

inline Float3 operator/(const Float3& v1, const Float3& v2)
{
    return Float3(v1.x / v2.x, v1.y / v2.y, v1.z / v2.z);
}

inline Float3 operator*(float S, const Float3& v)
{
    return Float3(v.x * S, v.y * S, v.z * S);
}


//****************************************************************************
// Float4
//****************************************************************************
struct Float4A;

struct CROSS_BASE_API CEMeta(Cli, Puerts, WorkflowType) Float4
{
    CEProperty(Editor, Reflect, ScriptReadWrite)
    float x{0.0f}, y{0.0f}, z{0.0f}, w{0.0f};

    CEMeta(Editor, ScriptCallable) Float4() = default;

    CEMeta(Editor, ScriptCallable) Float4(const Float4&) = default;

    Float4& operator=(const Float4&) = default;

    Float4(Float4&&) = default;

    Float4& operator=(Float4&&) = default;

    Float4(const Float3& _xyz, float _w) noexcept
        : x(_xyz.x)
        , y(_xyz.y)
        , z(_xyz.z)
        , w(_w)
    {}
    CEMeta(Editor, ScriptCallable) constexpr Float4(float _x, float _y, float _z, float _w) noexcept
        : x(_x)
        , y(_y)
        , z(_z)
        , w(_w)
    {}

    Float4(float _x) noexcept
        : x(_x)
        , y(_x)
        , z(_x)
        , w(_x)
    {}

    explicit Float4(_In_reads_(4) const float* pArray) noexcept
        : x(pArray[0])
        , y(pArray[1])
        , z(pArray[2])
        , w(pArray[3])
    {}

    inline void Set(float _x, float _y, float _z, float _w)
    {
        x = _x;
        y = _y;
        z = _z;
        w = _w;
    }

    float* data()
    {
        return &x;
    }
    const float* data() const
    {
        return &x;
    }

    inline SerializeNode Serialize() const
    {
        return SerializeNode{"x"_k = x, "y"_k = y, "z"_k = z, "w"_k = w};
    }
    inline void Deserialize(const DeserializeNode& json)
    {
        this->x = json["x"].AsFloat();
        this->y = json["y"].AsFloat();
        this->z = json["z"].AsFloat();
        this->w = json["w"].AsFloat();
    }

    float& operator[](int i)
    {
        return reinterpret_cast<float*>(&x)[i];
    }

    float operator[](int i) const
    {
        return reinterpret_cast<const float*>(&x)[i];
    }

    inline bool operator==(const Float4& v) const
    {
        return (x == v.x && y == v.y && z == v.z && w == v.w);
    }

    inline bool operator!=(const Float4& v) const
    {
        return (x != v.x || y != v.y || z != v.z || w != v.w);
    }

    inline Float4& operator+=(const Float4& v)
    {
        x += v.x;
        y += v.y;
        z += v.z;
        w += v.w;
        return *this;
    }

    inline Float4& operator-=(const Float4& v)
    {
        x -= v.x;
        y -= v.y;
        z -= v.z;
        w -= v.w;
        return *this;
    }

    inline Float4& operator*=(const Float4& v)
    {
        x *= v.x;
        y *= v.y;
        z *= v.z;
        w *= v.w;
        return *this;
    }

    inline Float4& operator*=(float s)
    {
        x *= s;
        y *= s;
        z *= s;
        w *= s;
        return *this;
    }

    inline Float4& operator/=(float s)
    {
        x /= s;
        y /= s;
        z /= s;
        w /= s;
        return *this;
    }

    inline Float4 operator-() const
    {
        return Float4(-x, -y, -z, -w);
    }

    inline bool InBounds(const Float4& Bounds) const
    {
        return (((x <= Bounds.x && x >= -Bounds.x) && (y <= Bounds.y && y >= -Bounds.y) && (z <= Bounds.z && z >= -Bounds.z) && (w <= Bounds.w && w >= -Bounds.w)) != 0);
    }

    CEMeta(Editor, ScriptCallable) inline float Length() const
    {
        return sqrt(x * x + y * y + z * z + w * w);
    }

    inline float LengthSquared() const
    {
        return (x * x + y * y + z * z + w * w);
    }

    CEMeta(Editor, ScriptCallable) inline float Dot(const Float4& v) const
    {
        return (x * v.x + y * v.y + z * v.z + w * v.w);
    }

    CEMeta(Editor, ScriptCallable) inline void Normalize()
    {
        float length = Length();
        if (length > 0)
            length = 1.0f / length;

        x *= length;
        y *= length;
        z *= length;
        w *= length;
    }

    inline Float4 Normalized() const
    {
        float length = Length();
        if (length > 0)
            length = 1.0f / length;

        return Float4(x * length, y * length, z * length, w * length);
    }

    inline Float4 Clamp(const Float4& vmin, const Float4& vmax) const
    {
        return Float4((vmin.x >= x) ? vmin.x : (vmax.x <= x ? vmax.x : x), (vmin.y >= y) ? vmin.y : (vmax.y <= y ? vmax.y : y), (vmin.z >= z) ? vmin.z : (vmax.z <= z ? vmax.z : z), (vmin.w >= w) ? vmin.w : (vmax.w <= w ? vmax.w : w));
    }

    inline void Clamp(const Float4& vmin, const Float4& vmax)
    {
        x = (vmin.x >= x) ? vmin.x : (vmax.x <= x ? vmax.x : x);
        y = (vmin.y >= y) ? vmin.y : (vmax.y <= y ? vmax.y : y);
        z = (vmin.z >= z) ? vmin.z : (vmax.z <= z ? vmax.z : z);
        w = (vmin.w >= w) ? vmin.w : (vmax.w <= w ? vmax.w : w);
    }

    inline Float3 XYZ() const
    {
        return Float3(x, y, z);
    }

    CEMeta(Editor, ScriptCallable) inline bool IsNaN()
    {
        auto qua = DirectX::XMLoadFloat4(reinterpret_cast<const DirectX::XMFLOAT4*>(this));
        return DirectX::XMVector4IsNaN(qua);
    }

    static inline float Distance(const Float4& v1, const Float4& v2)
    {
        Float4 delta(v1.x - v2.x, v1.y - v2.y, v1.z - v2.z, v1.w - v2.w);
        return delta.Length();
    }

    static inline float DistanceSquared(const Float4& v1, const Float4& v2)
    {
        Float4 delta(v1.x - v2.x, v1.y - v2.y, v1.z - v2.z, v1.w - v2.w);
        return delta.Dot(delta);
    }

    static inline Float4 Min(const Float4& v1, const Float4& v2)
    {
        Float4 result((v1.x <= v2.x) ? v1.x : v2.x, (v1.y <= v2.y) ? v1.y : v2.y, (v1.z <= v2.z) ? v1.z : v2.z, (v1.w <= v2.w) ? v1.w : v2.w);
        return result;
    }

    static inline Float4 Max(const Float4& v1, const Float4& v2)
    {
        Float4 result((v1.x >= v2.x) ? v1.x : v2.x, (v1.y >= v2.y) ? v1.y : v2.y, (v1.z >= v2.z) ? v1.z : v2.z, (v1.w >= v2.w) ? v1.w : v2.w);
        return result;
    }

    static inline Float4 Lerp(const Float4& v1, const Float4& v2, float t)
    {
        Float4 result(v1.x + ((v2.x - v1.x) * t), v1.y + ((v2.y - v1.y) * t), v1.z + ((v2.z - v1.z) * t), v1.w + ((v2.w - v1.w) * t));
        return result;
    }

    static inline Float4 SmoothStep(const Float4& v1, const Float4& v2, float t)
    {
        t = (t > 1.0f) ? 1.0f : ((t < 0.0f) ? 0.0f : t);   // Clamp value to 0 to 1
        t = t * t * (3.f - 2.f * t);
        Float4 result(v1.x + ((v2.x - v1.x) * t), v1.y + ((v2.y - v1.y) * t), v1.z + ((v2.z - v1.z) * t), v1.w + ((v2.w - v1.w) * t));
        return result;
    }

    static inline Float4 Zero()
    {
        return Float4(0.0f, 0.0f, 0.0f, 0.0f);
    }

    static Float4 BaryCentric(const Float4& v1, const Float4& v2, const Float4& v3, float f, float g);
    

    static Float4 CatmullRom(const Float4& v1, const Float4& v2, const Float4& v3, const Float4& v4, float t);
   

    static Float4 Hermite(const Float4& v1, const Float4& t1, const Float4& v2, const Float4& t2, float t);
  

    static Float4 Reflect(const Float4& ivec, const Float4& nvec);


    static Float4 Refract(const Float4& ivec, const Float4& nvec, float refractionIndex);
    
};

// 4D Vector; 32 bit floating point components aligned on a 16 byte boundary
XM_ALIGNED_STRUCT(16) CEMeta(Puerts) Float4A : public Float4
{
    CEMetaInternal(Reflect)

    Float4A() = default;

    CEMeta(Ignore)
    Float4A(const Float4A&) = default;
    Float4A& operator=(const Float4A&) = default;

    Float4A(Float4A &&) = default;
    Float4A& operator=(Float4A&&) = default;

    CEMeta(Ignore)
    Float4A(const Float4& vec)
        : Float4(vec)
    {}

    CEMeta(Editor, ScriptCallable) float X()
    {
        return x;
    }
    CEMeta(Editor, ScriptCallable) float Y()
    {
        return y;
    }
    CEMeta(Editor, ScriptCallable) float Z()
    {
        return z;
    }
    CEMeta(Editor, ScriptCallable) float W()
    {
        return w;
    }
    CEMeta(Editor, ScriptCallable) Float4A(float _x, float _y, float _z, float _w) noexcept
        : Float4(_x, _y, _z, _w)
    {}
    explicit Float4A(_In_reads_(4) const float* pArray) noexcept
        : Float4(pArray)
    {}
};

inline Float4 operator+(const Float4& V1, const Float4& V2)
{
    return Float4(V1.x + V2.x, V1.y + V2.y, V1.z + V2.z, V1.w + V2.w);
}

inline Float4 operator-(const Float4& V1, const Float4& V2)
{
    return Float4(V1.x - V2.x, V1.y - V2.y, V1.z - V2.z, V1.w - V2.w);
}

inline Float4 operator*(const Float4& V1, const Float4& V2)
{
    return Float4(V1.x * V2.x, V1.y * V2.y, V1.z * V2.z, V1.w * V2.w);
}

inline Float4 operator*(const Float4& v, float S)
{
    return Float4(v.x * S, v.y * S, v.z * S, v.w * S);
}

inline Float4 operator/(const Float4& V1, const Float4& V2)
{
    return Float4(V1.x / V2.x, V1.y / V2.y, V1.z / V2.z, V1.w / V2.w);
}

inline Float4 operator*(float S, const Float4& v)
{
    return Float4(v.x * S, v.y * S, v.z * S, v.w * S);
}

inline bool operator==(const Float4A& v1, const Float4& v2)
{
    return v1.x == v2.x && v1.y == v2.y && v1.z == v2.z && v1.w == v2.w;
}

inline bool operator==(const Float4& v1, const Float4A& v2)
{
    return v1.x == v2.x && v1.y == v2.y && v1.z == v2.z && v1.w == v2.w;
}

//****************************************************************************
// Quaternion
//****************************************************************************
inline Quaternion operator+(const Quaternion& Q1, const Quaternion& Q2);
inline Quaternion operator-(const Quaternion& Q1, const Quaternion& Q2);
inline Quaternion operator*(const Quaternion& Q1, const Quaternion& Q2);
inline Quaternion operator*(const Quaternion& Q, float S);
inline Quaternion operator/(const Quaternion& Q, float S);
inline Quaternion operator/(const Quaternion& Q1, const Quaternion& Q2);
inline Quaternion operator*(float S, const Quaternion& Q);
struct CROSS_BASE_API CEMeta(Cli, Puerts, WorkflowType) Quaternion
{
    CEProperty(Editor, Reflect, ScriptReadWrite)
    float x{0.0f}, y{0.0f}, z{0.0f}, w{1.0f};

    CEMeta(Editor, ScriptCallable) Quaternion() = default;

    CEMeta(Editor, ScriptCallable) Quaternion(const Quaternion&) = default;
    Quaternion& operator=(const Quaternion&) = default;
    Quaternion(Quaternion&&) = default;
    Quaternion& operator=(Quaternion&&) = default;

    CEMeta(Editor, ScriptCallable) constexpr Quaternion(float _x, float _y, float _z, float _w) noexcept
        : x(_x)
        , y(_y)
        , z(_z)
        , w(_w)
    {}
    explicit Quaternion(const float* pArray) noexcept
        : x(pArray[0])
        , y(pArray[1])
        , z(pArray[2])
        , w(pArray[3])
    {}

    /*
        extract pitch(x), yaw(y), and roll(z) from given quaternion in radian.
    */
    CEMeta(Editor, ScriptCallable) static inline Float3 QuaternionToEuler(const Quaternion& rot)
    {
        float x = rot.x, y = rot.y, z = rot.z, w = rot.w;
        float temp = 2 * (y * z - x * w);
        if (temp >= 1 - MathUtils::MathSmallNumber)
        {
            return Float3(-MathUtils::MathPiDiv2, atan2(y, w), atan2(z, w));
        }
        else if (-temp >= 1 - MathUtils::MathSmallNumber)
        {
            return Float3(MathUtils::MathPiDiv2, atan2(y, w), atan2(z, w));
        }
        else
        {
            return Float3(-asin(temp), atan2(x * z + y * w, 0.5f - x * x - y * y), atan2(x * y + z * w, 0.5f - x * x - z * z));
        }
    }

    const float* data()
    {
        return &x;
    }

    /*
        extract quaternion from given pitch(x), yaw(y), and roll(z) in radian.
    */
    CEMeta(Editor, ScriptCallable) static inline Quaternion EulerToQuaternion(const Float3& rot)
    {
        return CreateFromYawPitchRoll(rot.y, rot.x, rot.z);
    }

    // Log and exp of Quaternion
    // https://en.wikipedia.org/wiki/Quaternion#Exponential,_logarithm,_and_power_functions
    CEMeta(Editor, ScriptCallable) static inline Float3 Log(const Quaternion& q, float eps = 1e-6)
    {
        float length = sqrtf(q.x * q.x + q.y * q.y + q.z * q.z);

        if (length < eps)
        {
            return Float3(q.x, q.y, q.z);
        }
        else
        {
            float halfangle = acosf(std::clamp(q.w, -1.0f, 1.0f));
            return halfangle * (Float3(q.x, q.y, q.z) / length);
        }
    }

    CEMeta(Editor, ScriptCallable) static inline Quaternion Exp(const Float3& v, float eps = 1e-6)
    {
        float halfangle = sqrtf(v.x * v.x + v.y * v.y + v.z * v.z);

        if (halfangle < eps)
        {
            return Quaternion(v.x, v.y, v.z, 1.0f).Normalized();
        }
        else
        {
            float c = cosf(halfangle);
            float s = sinf(halfangle) / halfangle;
            return Quaternion(s * v.x, s * v.y, s * v.z, c);
        }
    }

    CEMeta(Editor, ScriptCallable) static inline Quaternion Identity()
    {
        return Quaternion(0.0f, 0.0f, 0.0f, 1.0f);
    }

    inline SerializeNode Serialize() const
    {
        return SerializeNode{"x"_k = x, "y"_k = y, "z"_k = z, "w"_k = w};
    }

    inline void Deserialize(const DeserializeNode& json)
    {
        this->x = json["x"].AsFloat();
        this->y = json["y"].AsFloat();
        this->z = json["z"].AsFloat();
        this->w = json["w"].AsFloat();
    }

    CEMeta(Editor, ScriptCallable) bool Equal(const Quaternion& other) const
    {
        return *this == other;
    }

    inline bool operator==(const Quaternion& q) const
    {
        return (x == q.x && y == q.y && z == q.z && w == q.w);
    }

    inline bool operator!=(const Quaternion& q) const
    {
        return (x != q.x || y != q.y || z != q.z || w != q.w);
    }

    CEMeta(Editor, ScriptCallable) Quaternion Add(const Quaternion& other) const
    {
        return *this + other;
    }

    inline Quaternion& operator+=(const Quaternion& q)
    {
        x += q.x;
        y += q.y;
        z += q.z;
        w += q.w;
        return *this;
    }

    inline Float3 XYZ() const
    {
        return {x, y, z};
    }

    inline float AngularDistance(const Quaternion& other) const
    {
        Quaternion qua = (*this) * other.Conjugate();
        return 2.0f * std::atan2(qua.XYZ().Length(), std::abs(qua.w));
    }

    CEMeta(Editor, ScriptCallable) Quaternion Subtract(const Quaternion& other) const
    {
        return *this - other;
    }

    inline Quaternion& operator-=(const Quaternion& q)
    {
        x -= q.x;
        y -= q.y;
        z -= q.z;
        w -= q.w;
        return *this;
    }

    CEMeta(Editor, ScriptCallable) Quaternion Mult(const Quaternion& other) const
    {
        return *this * other;
    }

    inline Quaternion& operator*=(const Quaternion& q)
    {
        Quaternion Result{(q.w * x) + (q.x * w) + (q.y * z) - (q.z * y), (q.w * y) - (q.x * z) + (q.y * w) + (q.z * x), (q.w * z) + (q.x * y) - (q.y * x) + (q.z * w), (q.w * w) - (q.x * x) - (q.y * y) - (q.z * z)};
        *this = Result;
        return *this;
    }

    CEMeta(Editor, ScriptCallable) Quaternion Mult(float S) const
    {
        return *this * S;
    }

    inline Quaternion& operator*=(float S)
    {
        x *= S;
        y *= S;
        z *= S;
        w *= S;
        return *this;
    }

    inline Quaternion operator-() const
    {
        return Quaternion(-x, -y, -z, -w);
    }

    CEMeta(Editor, ScriptCallable) inline Quaternion Abs()
    {
        return w < 0 ? -(*this) : *this;
    }

    CEMeta(Editor, ScriptCallable) inline float LengthSquared() const
    {
        return x * x + y * y + z * z + w * w;
    }

    CEMeta(Editor, ScriptCallable) inline float Length() const
    {
        return sqrt(LengthSquared());
    }

    CEMeta(Editor, ScriptCallable) inline void Normalize()
    {
        float length = Length();

        // Prevent divide by zero
        if (length > 0)
        {
            length = 1.0f / length;
        }

        x *= length;
        y *= length;
        z *= length;
        w *= length;
    }

    CEMeta(Editor, ScriptCallable) inline Quaternion Normalized() const
    {
        Quaternion Result(x, y, z, w);
        Result.Normalize();
        return Result;
    }

    CEMeta(Editor, ScriptCallable) inline Quaternion Conjugate() const
    {
        return Quaternion(-x, -y, -z, w);
    }

    CEMeta(Editor, ScriptCallable) inline Quaternion Inverse() const
    {
        float length = Length();
        // Prevent divide by zero
        if (length > 0)
        {
            length = 1.0f / length;
        }
        Quaternion Result = Conjugate();

        return Quaternion(Result.x * length, Result.y * length, Result.z * length, Result.w * length);
    }

    CEMeta(Editor, ScriptCallable) inline Float3 GetForwardVector() const
    {
        return Float3Rotate(Float3(0.0f, 0.0f, 1.0f));
    }

    CEMeta(Editor, ScriptCallable) inline Float3 GetUpVector() const
    {
        return Float3Rotate(Float3(0.0f, 1.0f, 0.0f));
    }

    CEMeta(Editor, ScriptCallable) inline Float3 GetRightVector() const
    {
        return Float3Rotate(Float3(1.0f, 0.0f, 0.0f));
    }

    CEMeta(Editor, ScriptCallable) inline float GetAngleRad() const
    {
        return 2.0f * DirectX::XMScalarACos(w);
    }

    CEMeta(Editor, ScriptCallable) inline Float3 GetAxis() const
    {
        const float sin = std::sqrtf(MathUtils::Max(1.0f - w * w, 0.f));

        if (sin >= 0.0001f)
            return Float3(x / sin, y / sin, z / sin);
        else
            return Float3(1.0f, 0.f, 0.f);
    }

    CEMeta(Editor, ScriptCallable) inline Float3 Float3Rotate(const Float3& v) const;

    inline Float4 Float4Rotate(const Float4& v) const;

    CEMeta(Editor, ScriptCallable) inline bool IsNaN()
    {
        auto qua = DirectX::XMLoadFloat4(reinterpret_cast<const DirectX::XMFLOAT4*>(this));
        return DirectX::XMQuaternionIsNaN(qua);
    }

    CEMeta(Editor, ScriptCallable) inline bool IsIdentity() const
    {
        return IsEqual(*this, Quaternion::Identity());
    }

    CEMeta(Editor, ScriptCallable) static inline Quaternion CreateFromAxisAngle(const Float3& axis, float angle)
    {
        using namespace DirectX;
        FXMVECTOR A = DirectX::XMLoadFloat3(reinterpret_cast<const XMFLOAT3*>(&axis));

        Quaternion result;
        DirectX::XMStoreFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&result), DirectX::XMQuaternionRotationAxis(A, angle));
        return result;
    }

    CEMeta(Editor, ScriptCallable) static inline Quaternion CreateFromYawPitchRollInDegrees(float yaw, float pitch, float roll)
    {
        pitch = MathUtils::ConvertToRadians(pitch);
        yaw = MathUtils::ConvertToRadians(yaw);
        roll = MathUtils::ConvertToRadians(roll);
        return CreateFromYawPitchRoll(yaw, pitch, roll);
    }

    CEMeta(Editor, ScriptCallable) static inline Quaternion CreateFromYawPitchRoll(float yaw, float pitch, float roll)
    {
        float rollOver2 = roll * 0.5f;
        float sinRollOver2, cosRollOver2;
        MathUtils::SinCos(rollOver2, sinRollOver2, cosRollOver2);

        float pitchOver2 = pitch * 0.5f;
        float sinPitchOver2, cosPitchOver2;
        MathUtils::SinCos(pitchOver2, sinPitchOver2, cosPitchOver2);

        float yawOver2 = yaw * 0.5f;
        float sinYawOver2, cosYawOver2;
        MathUtils::SinCos(yawOver2, sinYawOver2, cosYawOver2);

        Quaternion result;
        result.w = cosYawOver2 * cosPitchOver2 * cosRollOver2 + sinYawOver2 * sinPitchOver2 * sinRollOver2;
        result.x = cosYawOver2 * sinPitchOver2 * cosRollOver2 + sinYawOver2 * cosPitchOver2 * sinRollOver2;
        result.y = sinYawOver2 * cosPitchOver2 * cosRollOver2 - cosYawOver2 * sinPitchOver2 * sinRollOver2;
        result.z = cosYawOver2 * cosPitchOver2 * sinRollOver2 - sinYawOver2 * sinPitchOver2 * cosRollOver2;

        return result;
    }

    CEMeta(Editor, ScriptCallable) static inline Quaternion CreateFromRotationMatrix(const Float4x4& M)
    {
        using namespace DirectX;
        FXMMATRIX MX = DirectX::XMLoadFloat4x4(reinterpret_cast<const XMFLOAT4X4*>(&M));
        Quaternion result;
        DirectX::XMStoreFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&result), DirectX::XMQuaternionRotationMatrix(MX));
        return result;
    }

    CEMeta(Editor, ScriptCallable) static inline Quaternion Slerp(const Quaternion& q1, const Quaternion& q2, float t)
    {
        auto v1 = DirectX::XMLoadFloat4(reinterpret_cast<const DirectX::XMFLOAT4*>(&q1));
        auto v2 = DirectX::XMLoadFloat4(reinterpret_cast<const DirectX::XMFLOAT4*>(&q2));
        Quaternion result;
        DirectX::XMStoreFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&result), DirectX::XMQuaternionSlerp(v1, v2, t));
        return result;
    }

    CEMeta(Editor, ScriptCallable) static inline Quaternion Concatenate(const Quaternion& q1, const Quaternion& q2)
    {
        auto v1 = DirectX::XMLoadFloat4(reinterpret_cast<const DirectX::XMFLOAT4*>(&q1));
        auto v2 = DirectX::XMLoadFloat4(reinterpret_cast<const DirectX::XMFLOAT4*>(&q2));
        Quaternion result;
        DirectX::XMStoreFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&result), DirectX::XMQuaternionMultiply(v1, v2));
        return result;
    }

    CEMeta(Editor, ScriptCallable) static inline Quaternion CreateFrom2Vectors(const Float3& from, const Float3& to, const Float3& rotateAxis = Float3(0, 1, 0))
    {
        auto from_n = from.Normalized();
        auto to_n = to.Normalized();

        float cos = from_n.Dot(to_n);
        // from and to are in the same direction
        if (cos > 1 - MathUtils::MathEps)
        {
            return Quaternion::Identity();
        }
        // from and to are in the opposite direction
        else if (cos < -(1 - MathUtils::MathEps))
        {
            return Quaternion::CreateFromAxisAngle(rotateAxis.Normalized(), MathUtils::MathPi);
        }
        else
        {
            auto sin = from_n.Cross(to_n);
            Quaternion ret(sin.x, sin.y, sin.z, cos + 1.0f);
            return ret.Normalized();
        }
    }

    CEMeta(Editor, ScriptCallable) static inline float Dot(const Quaternion& q1, const Quaternion& q2)
    {
        auto v1 = DirectX::XMLoadFloat4(reinterpret_cast<const DirectX::XMFLOAT4*>(&q1));
        auto v2 = DirectX::XMLoadFloat4(reinterpret_cast<const DirectX::XMFLOAT4*>(&q2));

        auto re = DirectX::XMQuaternionDot(v1, v2);
        return DirectX::XMVectorGetX(re);
    }

    CEMeta(Editor, ScriptCallable) static inline bool IsEqual(const Quaternion& q1, const Quaternion& q2)
    {
        auto v1 = DirectX::XMLoadFloat4(reinterpret_cast<const DirectX::XMFLOAT4*>(&q1));
        auto v2 = DirectX::XMLoadFloat4(reinterpret_cast<const DirectX::XMFLOAT4*>(&q2));

        return DirectX::XMQuaternionEqual(v1, v2);
    }
};

XM_ALIGNED_STRUCT(16) CEMeta(Puerts)
QuaternionA : public Quaternion
{
    CEMetaInternal(Reflect)

    QuaternionA() = default;
    QuaternionA(const QuaternionA&) = default;
    QuaternionA& operator=(const QuaternionA&) = default;
    QuaternionA(QuaternionA &&) = default;
    QuaternionA& operator=(QuaternionA&&) = default;
    QuaternionA(const Quaternion& q)
        : Quaternion(q)
    {}
    CEMeta(Editor, ScriptCallable) constexpr QuaternionA(float _x, float _y, float _z, float _w) noexcept
        : Quaternion(_x, _y, _z, _w)
    {}
    explicit QuaternionA(const float* pArray) noexcept
        : Quaternion(pArray)
    {}
    CEMeta(Editor, ScriptCallable) static inline QuaternionA EulerToQuaternion(const Float3A& rot)
    {
        return Quaternion::CreateFromYawPitchRoll(rot.y, rot.x, rot.z);
    }
    CEMeta(Editor, ScriptCallable) static Float3A QuaternionToEuler(const QuaternionA& rot)
    {
        return Quaternion::QuaternionToEuler(rot);
    }
    CEMeta(Editor, ScriptCallable) bool Equal(const QuaternionA& other)
    {
        return *this == other;
    }
};

inline Quaternion operator+(const Quaternion& Q1, const Quaternion& Q2)
{
    return Quaternion(Q1.x + Q2.x, Q1.y + Q2.y, Q1.z + Q2.z, Q1.w + Q2.w);
}

inline Quaternion operator-(const Quaternion& Q1, const Quaternion& Q2)
{
    return Quaternion(Q1.x - Q2.x, Q1.y - Q2.y, Q1.z - Q2.z, Q1.w - Q2.w);
}

inline Quaternion operator*(const Quaternion& Q1, const Quaternion& Q2)
{
    // Generic Quaternion1 * Quaternion2 product
    Quaternion q;
    q.w = (Q2.w * Q1.w) - (Q2.x * Q1.x) - (Q2.y * Q1.y) - (Q2.z * Q1.z);
    q.x = (Q2.w * Q1.x) + (Q2.x * Q1.w) + (Q2.y * Q1.z) - (Q2.z * Q1.y);
    q.y = (Q2.w * Q1.y) - (Q2.x * Q1.z) + (Q2.y * Q1.w) + (Q2.z * Q1.x);
    q.z = (Q2.w * Q1.z) + (Q2.x * Q1.y) - (Q2.y * Q1.x) + (Q2.z * Q1.w);
    return q;
}

inline Quaternion operator*(const Quaternion& Q, float S)
{
    return Quaternion(Q.x * S, Q.y * S, Q.z * S, Q.w * S);
}

inline Quaternion operator/(const Quaternion& Q, float S)
{
    float recip = 1.0f / S;
    return Quaternion(Q.x * recip, Q.y * recip, Q.z * recip, Q.w * recip);
}

inline Quaternion operator/(const Quaternion& Q1, const Quaternion& Q2)
{
    return Q1 * Q2.Inverse();
}

inline Quaternion operator*(float S, const Quaternion& Q)
{
    return Q * S;
}

Float4x4 operator+(const Float4x4& M1, const Float4x4& M2);
Float4x4 operator-(const Float4x4& M1, const Float4x4& M2);
Float4x4 operator*(const Float4x4& M1, const Float4x4& M2);
Float4x4 operator*(const Float4x4& M1, float S);
Float4x4 operator/(const Float4x4& M1, float S);
Float4x4 operator/(const Float4x4& M1, const Float4x4& M2);
//****************************************************************************
// Matrix
// row major
//****************************************************************************
struct CROSS_BASE_API CEMeta(Cli, Puerts, WorkflowType) Float4x4
{
    CEProperty(Editor, Reflect, ScriptReadWrite)
    float m00{1.0f}, m01{0.0f}, m02{0.0f}, m03{0.0f};
    CEProperty(Editor, Reflect, ScriptReadWrite)
    float m10{0.0f}, m11{1.0f}, m12{0.0f}, m13{0.0f};
    CEProperty(Editor, Reflect, ScriptReadWrite)
    float m20{0.0f}, m21{0.0f}, m22{1.0f}, m23{0.0f};
    CEProperty(Editor, Reflect, ScriptReadWrite)
    float m30{0.0f}, m31{0.0f}, m32{0.0f}, m33{1.0f};

    CEMeta(Editor, ScriptCallable) Float4x4() = default;

    CEMeta(Editor, ScriptCallable) Float4x4(const Float4x4&) = default;

    Float4x4(Float4x4&&) = default;

    Float4x4& operator=(const Float4x4&) = default;

    Float4x4& operator=(Float4x4&&) = default;

    float& operator[](int i)
    {
        return reinterpret_cast<float*>(&m00)[i];
    }

    inline SerializeNode Serialize() const
    {
        return SerializeNode{"m00"_k = m00,
                             "m01"_k = m01,
                             "m02"_k = m02,
                             "m03"_k = m03,
                             "m10"_k = m10,
                             "m11"_k = m11,
                             "m12"_k = m12,
                             "m13"_k = m13,
                             "m20"_k = m20,
                             "m21"_k = m21,
                             "m22"_k = m22,
                             "m23"_k = m23,
                             "m30"_k = m30,
                             "m31"_k = m31,
                             "m32"_k = m32,
                             "m33"_k = m33};
    }

    inline bool Validate()
    {
        for (int i = 0; i < 16; i++)
        {
            if (isnan(data()[i]))
            {
                Assert(!isnan(data()[i]) && "matrix item is nan");
                return false;
            }
            else if (isinf(data()[i]))
            {
                Assert(!isinf(data()[i]) && "matrix item is inf");
                return false;
            }
        }
        return true;
    }

    inline void Deserialize(const DeserializeNode& json)
    {
        m00 = json["m00"].AsFloat();
        m01 = json["m01"].AsFloat();
        m02 = json["m02"].AsFloat();
        m03 = json["m03"].AsFloat();

        m10 = json["m10"].AsFloat();
        m11 = json["m11"].AsFloat();
        m12 = json["m12"].AsFloat();
        m13 = json["m13"].AsFloat();

        m20 = json["m20"].AsFloat();
        m21 = json["m21"].AsFloat();
        m22 = json["m22"].AsFloat();
        m23 = json["m23"].AsFloat();

        m30 = json["m30"].AsFloat();
        m31 = json["m31"].AsFloat();
        m32 = json["m32"].AsFloat();
        m33 = json["m33"].AsFloat();
    }

    CEMeta(Editor, ScriptCallable) constexpr Float4x4(float m00, float m01, float m02, float m03, float m10, float m11, float m12, float m13, float m20, float m21, float m22, float m23, float m30, float m31, float m32, float m33) noexcept
        : m00(m00)
        , m01(m01)
        , m02(m02)
        , m03(m03)
        , m10(m10)
        , m11(m11)
        , m12(m12)
        , m13(m13)
        , m20(m20)
        , m21(m21)
        , m22(m22)
        , m23(m23)
        , m30(m30)
        , m31(m31)
        , m32(m32)
        , m33(m33)
    {}

    CEMeta(Editor, ScriptCallable) constexpr Float4x4(Float4 v0, Float4 v1, Float4 v2, Float4 v3) noexcept
        : m00(v0.x)
        , m01(v0.y)
        , m02(v0.z)
        , m03(v0.w)
        , m10(v1.x)
        , m11(v1.y)
        , m12(v1.z)
        , m13(v1.w)
        , m20(v2.x)
        , m21(v2.y)
        , m22(v2.z)
        , m23(v2.w)
        , m30(v3.x)
        , m31(v3.y)
        , m32(v3.z)
        , m33(v3.w)
    {}

    explicit Float4x4(_In_reads_(16) const float* pArray) noexcept
        : m00(pArray[0])
        , m01(pArray[1])
        , m02(pArray[2])
        , m03(pArray[3])
        , m10(pArray[4])
        , m11(pArray[5])
        , m12(pArray[6])
        , m13(pArray[7])
        , m20(pArray[8])
        , m21(pArray[9])
        , m22(pArray[10])
        , m23(pArray[11])
        , m30(pArray[12])
        , m31(pArray[13])
        , m32(pArray[14])
        , m33(pArray[15])
    {}

    float operator()(uint32_t Row, uint32_t Column) const noexcept
    {
        return reinterpret_cast<const float*>(&m00)[Row * 4 + Column];
    }

    float& operator()(uint32_t Row, uint32_t Column) noexcept
    {
        return reinterpret_cast<float*>(&m00)[Row * 4 + Column];
    }

    const float* data() const
    {
        return &m00;
    }

    CEMeta(Editor, ScriptCallable) bool Equal(const Float4x4& M) const
    {
        return *this == M;
    }

    inline bool operator==(const Float4x4& M) const
    {
        using namespace DirectX;
        XMVECTOR x1 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&m00));
        XMVECTOR x2 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&m10));
        XMVECTOR x3 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&m20));
        XMVECTOR x4 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&m30));

        XMVECTOR y1 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M.m00));
        XMVECTOR y2 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M.m10));
        XMVECTOR y3 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M.m20));
        XMVECTOR y4 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M.m30));

        return (XMVector4Equal(x1, y1) && XMVector4Equal(x2, y2) && XMVector4Equal(x3, y3) && XMVector4Equal(x4, y4)) != 0;
    }

    inline bool operator!=(const Float4x4& M) const
    {
        using namespace DirectX;
        XMVECTOR x1 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&m00));
        XMVECTOR x2 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&m10));
        XMVECTOR x3 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&m20));
        XMVECTOR x4 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&m30));

        XMVECTOR y1 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M.m00));
        XMVECTOR y2 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M.m10));
        XMVECTOR y3 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M.m20));
        XMVECTOR y4 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M.m30));

        return (XMVector4NotEqual(x1, y1) || XMVector4NotEqual(x2, y2) || XMVector4NotEqual(x3, y3) || XMVector4NotEqual(x4, y4)) != 0;
    }

    CEMeta(Editor, ScriptCallable) Float4x4 Add(const Float4x4& M) const
    {
        return *this + M;
    }

    inline Float4x4& operator+=(const Float4x4& M)
    {
        using namespace DirectX;
        XMVECTOR x1 = XMLoadFloat4(reinterpret_cast<XMFLOAT4*>(&m00));
        XMVECTOR x2 = XMLoadFloat4(reinterpret_cast<XMFLOAT4*>(&m10));
        XMVECTOR x3 = XMLoadFloat4(reinterpret_cast<XMFLOAT4*>(&m20));
        XMVECTOR x4 = XMLoadFloat4(reinterpret_cast<XMFLOAT4*>(&m30));

        XMVECTOR y1 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M.m00));
        XMVECTOR y2 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M.m10));
        XMVECTOR y3 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M.m20));
        XMVECTOR y4 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M.m30));

        x1 = XMVectorAdd(x1, y1);
        x2 = XMVectorAdd(x2, y2);
        x3 = XMVectorAdd(x3, y3);
        x4 = XMVectorAdd(x4, y4);

        XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&m00), x1);
        XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&m10), x2);
        XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&m20), x3);
        XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&m30), x4);
        return *this;
    }

    CEMeta(Editor, ScriptCallable) Float4x4 Subtract(const Float4x4& M) const
    {
        return *this - M;
    }

    inline Float4x4& operator-=(const Float4x4& M)
    {
        using namespace DirectX;
        XMVECTOR x1 = XMLoadFloat4(reinterpret_cast<XMFLOAT4*>(&m00));
        XMVECTOR x2 = XMLoadFloat4(reinterpret_cast<XMFLOAT4*>(&m10));
        XMVECTOR x3 = XMLoadFloat4(reinterpret_cast<XMFLOAT4*>(&m20));
        XMVECTOR x4 = XMLoadFloat4(reinterpret_cast<XMFLOAT4*>(&m30));

        XMVECTOR y1 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M.m00));
        XMVECTOR y2 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M.m10));
        XMVECTOR y3 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M.m20));
        XMVECTOR y4 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M.m30));

        x1 = XMVectorSubtract(x1, y1);
        x2 = XMVectorSubtract(x2, y2);
        x3 = XMVectorSubtract(x3, y3);
        x4 = XMVectorSubtract(x4, y4);

        XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&m00), x1);
        XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&m10), x2);
        XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&m20), x3);
        XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&m30), x4);
        return *this;
    }

    CEMeta(Editor, ScriptCallable) Float4x4 Mult(const Float4x4& M) const
    {
        return *this * M;
    }

    inline Float4x4& operator*=(const Float4x4& M)
    {
        using namespace DirectX;
        XMMATRIX M1 = XMLoadFloat4x4(reinterpret_cast<const XMFLOAT4X4*>(this));
        XMMATRIX M2 = XMLoadFloat4x4(reinterpret_cast<const XMFLOAT4X4*>(&M));
        XMMATRIX X = XMMatrixMultiply(M1, M2);
        XMStoreFloat4x4(reinterpret_cast<XMFLOAT4X4*>(this), X);
        return *this;
    }

    CEMeta(Editor, ScriptCallable) Float4x4 Mult(float S) const
    {
        return *this * S;
    }

    inline Float4x4& operator*=(float S)
    {
        using namespace DirectX;
        XMVECTOR x1 = XMLoadFloat4(reinterpret_cast<XMFLOAT4*>(&m00));
        XMVECTOR x2 = XMLoadFloat4(reinterpret_cast<XMFLOAT4*>(&m10));
        XMVECTOR x3 = XMLoadFloat4(reinterpret_cast<XMFLOAT4*>(&m20));
        XMVECTOR x4 = XMLoadFloat4(reinterpret_cast<XMFLOAT4*>(&m30));

        x1 = XMVectorScale(x1, S);
        x2 = XMVectorScale(x2, S);
        x3 = XMVectorScale(x3, S);
        x4 = XMVectorScale(x4, S);

        XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&m00), x1);
        XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&m10), x2);
        XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&m20), x3);
        XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&m30), x4);
        return *this;
    }

    CEMeta(Editor, ScriptCallable) Float4x4 Divide(float S) const
    {
        return *this / S;
    }

    Float4x4& operator/=(float S);

    CEMeta(Editor, ScriptCallable) Float4x4 Divide(const Float4x4& M) const
    {
        return *this / M;
    }

    Float4x4& operator/=(const Float4x4& M);

    inline Float4x4 operator-() const
    {
        using namespace DirectX;
        XMVECTOR v1 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&m00));
        XMVECTOR v2 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&m10));
        XMVECTOR v3 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&m20));
        XMVECTOR v4 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&m30));

        v1 = XMVectorNegate(v1);
        v2 = XMVectorNegate(v2);
        v3 = XMVectorNegate(v3);
        v4 = XMVectorNegate(v4);

        Float4x4 R;
        XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&R.m00), v1);
        XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&R.m10), v2);
        XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&R.m20), v3);
        XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&R.m30), v4);
        return R;
    }

    CEMeta(Editor, ScriptCallable)
    bool Decompose(Float3 & scale, Quaternion & rotation, Float3 & translation) const;
    
    CEMeta(Editor, ScriptCallable)
    bool Decompose(Float3A & scale, QuaternionA & rotation, Float3A & translation) const;

    CEMeta(Editor, ScriptCallable)
    bool Decompose(Float4A & scale, QuaternionA & rotation, Float4A & translation) const;

    CEMeta(Editor, ScriptCallable) inline Float4x4 Transpose() const
    {
        using namespace DirectX;
        FXMMATRIX M = DirectX::XMLoadFloat4x4(reinterpret_cast<const XMFLOAT4X4*>(this));
        Float4x4 R;
        DirectX::XMStoreFloat4x4(reinterpret_cast<XMFLOAT4X4*>(&R), DirectX::XMMatrixTranspose(M));
        return R;
    }

    Float4x4& Mirror(Axis::Type mirrorAxis = Axis::Type::None, Axis::Type flipAxis = Axis::Type::None);
    

    CEMeta(Editor, ScriptCallable) inline Float4x4 Inverted() const
    {
        using namespace DirectX;
        FXMMATRIX M = DirectX::XMLoadFloat4x4(reinterpret_cast<const XMFLOAT4X4*>(this));
        Float4x4 R;
        DirectX::XMStoreFloat4x4(reinterpret_cast<XMFLOAT4X4*>(&R), DirectX::XMMatrixInverse(nullptr, M));
        return R;
    }

    CEMeta(Editor, ScriptCallable) inline void Inverse()
    {
        using namespace DirectX;
        FXMMATRIX M = DirectX::XMLoadFloat4x4(reinterpret_cast<const XMFLOAT4X4*>(this));
        DirectX::XMStoreFloat4x4(reinterpret_cast<XMFLOAT4X4*>(this), DirectX::XMMatrixInverse(nullptr, M));
    }

    CEMeta(Editor, ScriptCallable) inline float Determinant() const
    {
        using namespace DirectX;
        FXMMATRIX M = DirectX::XMLoadFloat4x4(reinterpret_cast<const XMFLOAT4X4*>(this));
        XMFLOAT4 F;
        DirectX::XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&F), DirectX::XMMatrixDeterminant(M));
        return F.x;
    }

    CEMeta(Editor, ScriptCallable)
    static Float4x4 CalculationRotationMatrix(const Float3& vectorBefore, const Float3& vectorAfter);


    CEMeta(Editor, ScriptCallable) static inline Float4x4 Identity()
    {
        return Float4x4(1.f, 0, 0, 0, 0, 1.f, 0, 0, 0, 0, 1.f, 0, 0, 0, 0, 1.f);
    }

    CEMeta(Editor, ScriptCallable) static inline Float4x4 Zeros()
    {
        return Float4x4(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
    }

    CEMeta(Editor, ScriptCallable) static inline Float4x4 CreateTranslation(float x, float y, float z)
    {
        Float4x4 result;
        result.m00 = 1.0f;
        result.m01 = 0.0f;
        result.m02 = 0.0f;
        result.m03 = 0.0f;

        result.m10 = 0.0f;
        result.m11 = 1.0f;
        result.m12 = 0.0f;
        result.m13 = 0.0f;

        result.m20 = 0.0f;
        result.m21 = 0.0f;
        result.m22 = 1.0f;
        result.m23 = 0.0f;

        result.m30 = x;
        result.m31 = y;
        result.m32 = z;
        result.m33 = 1.0f;
        return result;
    }

    CEMeta(Editor, ScriptCallable) static inline Float4x4 CreateTranslation(const Float3& t)
    {
        return CreateTranslation(t.x, t.y, t.z);
    }

    CEMeta(Editor, ScriptCallable)
    static Float4x4 CreateScale(const Float3& scales);

    CEMeta(Editor, ScriptCallable) static inline Float4x4 CreateScale(float xs, float ys, float zs)
    {
        return CreateScale(Float3(xs, ys, zs));
    }

    CEMeta(Editor, ScriptCallable) static inline Float4x4 CreateScale(float scale)
    {
        return CreateScale(Float3(scale, scale, scale));
    }

    CEMeta(Editor, ScriptCallable)
    static Float4x4 CreateRotationX(float radians);


    CEMeta(Editor, ScriptCallable)
    static Float4x4 CreateRotationY(float radians);


    CEMeta(Editor, ScriptCallable)
    static Float4x4 CreateRotationZ(float radians);

    CEMeta(Editor, ScriptCallable)
    static Float4x4 CreateFromAxisAngle(const Float3& axis, float angle);

    CEMeta(Editor, ScriptCallable)
    static Float4x4 CreatePerspectiveFieldOfView(float fov, float aspectRatio, float nearPlane, float farPlane, bool ndcGL = false);


    CEMeta(Editor, ScriptCallable)
    static Float4x4 CreatePerspective(float width, float height, float nearPlane, float farPlane, bool ndcGL = false);


    CEMeta(Editor, ScriptCallable)
    static Float4x4 CreatePerspectiveOffCenter(float left, float right, float bottom, float top, float nearPlane, float farPlane, bool ndcGL = false);


    CEMeta(Editor, ScriptCallable)
    static Float4x4 CreateOrthographic(float width, float height, float zNearPlane, float zFarPlane, bool ndcGL = false);


    CEMeta(Editor, ScriptCallable)
    static Float4x4 CreateOrthographicOffCenter(float left, float right, float bottom, float top, float zNearPlane, float zFarPlane, bool ndcGL = false);


    // Normalized Up vector
    CEMeta(Editor, ScriptCallable)
    static Float4x4 CreateLookAt(const Float3& eye, const Float3& target, const Float3& up);
   

    static Float4x4 QuaternionToRotationMatrix(const Quaternion& rot);


    CEMeta(Editor, ScriptCallable)
    static Float4x4 CreateWorld(const Float3& position, const Float3& forward, const Float3& up);

    CEMeta(Editor, ScriptCallable) static inline Float4x4 CreateFromQuaternion(const Quaternion& rotation)
    {
        using namespace DirectX;
        Float4x4 R;
        DirectX::XMVECTOR quatv = DirectX::XMLoadFloat4(reinterpret_cast<const DirectX::XMFLOAT4*>(&rotation));
        DirectX::XMStoreFloat4x4(reinterpret_cast<XMFLOAT4X4*>(&R), DirectX::XMMatrixRotationQuaternion(quatv));
        return R;
    }

    CEMeta(Editor, ScriptCallable) static inline Float4x4 CreateFromYawPitchRoll(float yaw, float pitch, float roll)
    {
        using namespace DirectX;
        Float4x4 R;
        XMStoreFloat4x4(reinterpret_cast<XMFLOAT4X4*>(&R), DirectX::XMMatrixRotationRollPitchYaw(pitch, yaw, roll));
        return R;
    }

    CEMeta(Editor, ScriptCallable)
    static void Lerp(const Float4x4& M1, const Float4x4& M2, float t, Float4x4& result);


    CEMeta(Editor, ScriptCallable)
    static Float4x4 Lerp(const Float4x4& M1, const Float4x4& M2, float t);

    CEMeta(Editor, ScriptCallable) static inline Float4x4 Transform(const Float4x4& M, const Quaternion& rotation)
    {
        using namespace DirectX;
        XMVECTOR quatv = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&rotation));
        XMMATRIX M0 = XMLoadFloat4x4(reinterpret_cast<const XMFLOAT4X4*>(&M));
        XMMATRIX M1 = XMMatrixRotationQuaternion(quatv);
        Float4x4 result;
        XMStoreFloat4x4(reinterpret_cast<XMFLOAT4X4*>(&result), XMMatrixMultiply(M0, M1));
        return result;
    }

    // Point * Matrix
    CEMeta(Editor, ScriptCallable) static inline Float3 TransformPointF3(const Float4x4& m, const Float3& v)
    {
        float fX = (m.m00 * v.x) + (m.m10 * v.y) + (m.m20 * v.z) + (m.m30 * 1.0f);
        float fY = (m.m01 * v.x) + (m.m11 * v.y) + (m.m21 * v.z) + (m.m31 * 1.0f);
        float fZ = (m.m02 * v.x) + (m.m12 * v.y) + (m.m22 * v.z) + (m.m32 * 1.0f);
        return Float3{fX, fY, fZ};
    }

    // Vector * Matrix
    CEMeta(Editor, ScriptCallable) static inline Float3 TransformVectorF3(const Float4x4& m, const Float3& v)
    {
        float fX = (m.m00 * v.x) + (m.m10 * v.y) + (m.m20 * v.z);
        float fY = (m.m01 * v.x) + (m.m11 * v.y) + (m.m21 * v.z);
        float fZ = (m.m02 * v.x) + (m.m12 * v.y) + (m.m22 * v.z);
        return Float3{fX, fY, fZ};
    }

    // V * M
    CEMeta(Editor, ScriptCallable) static inline Float4 Transform(const Float4x4& m, const Float4& v)
    {
        float fX = (m.m00 * v.x) + (m.m10 * v.y) + (m.m20 * v.z) + (m.m30 * v.w);
        float fY = (m.m01 * v.x) + (m.m11 * v.y) + (m.m21 * v.z) + (m.m31 * v.w);
        float fZ = (m.m02 * v.x) + (m.m12 * v.y) + (m.m22 * v.z) + (m.m32 * v.w);
        float fW = (m.m03 * v.x) + (m.m13 * v.y) + (m.m23 * v.z) + (m.m33 * v.w);
        return Float4{fX, fY, fZ, fW};
    }

    CEMeta(Editor, ScriptCallable) static inline Float4x4 Compose(const Float3& scale, const Quaternion& rotation, const Float3& translation)
    {
        Float4x4 result = CreateScale(scale);
        result *= CreateFromQuaternion(rotation);
        result *= CreateTranslation(translation);
        return result;
    }

    CEMeta(Editor, ScriptCallable)
        /** Asssume Float4 & Float3 memory strictly follow the layout x, y, z **/
        static inline Float4x4 Compose(const Float4& scale, const Quaternion& rotation, const Float4& translation)
    {
        if constexpr ((sizeof(Float4) - sizeof(Float3)) != sizeof(float))
            return Float4x4::Identity();

        return Compose(*reinterpret_cast<const Float3*>(&scale), rotation, *reinterpret_cast<const Float3*>(&translation));
    }

    CEMeta(Editor, ScriptCallable)
    inline Float3 GetForwardVector() const
    {
        return TransformVectorF3(*this, Float3(0.0, 0.0, 1.0));
    }

    CEMeta(Editor, ScriptCallable)
    inline Float3 GetUpVector() const
    {
        return TransformVectorF3(*this, Float3(0.0, 1.0, 0.0));
    }

    CEMeta(Editor, ScriptCallable)
    inline Float3 GetRightVector() const
    {
        return TransformVectorF3(*this, Float3(1.0, 0.0, 0.0));
    }
};

// 4x4 Matrix: 32 bit floating point components aligned on a 16 byte boundary
XM_ALIGNED_STRUCT(16) Float4x4A : public Float4x4
{
    Float4x4A() = default;

    Float4x4A(const Float4x4A&) = default;
    Float4x4A& operator=(const Float4x4A&) = default;

    Float4x4A(Float4x4A &&) = default;
    Float4x4A& operator=(Float4x4A&&) = default;

    Float4x4A(const Float4x4& mat)
        : Float4x4(mat)
    {}

    constexpr Float4x4A(float m00, float m01, float m02, float m03, float m10, float m11, float m12, float m13, float m20, float m21, float m22, float m23, float m30, float m31, float m32, float m33) noexcept
        : Float4x4(m00, m01, m02, m03, m10, m11, m12, m13, m20, m21, m22, m23, m30, m31, m32, m33)
    {}

    explicit Float4x4A(_In_reads_(16) const float* pArray) noexcept
        : Float4x4(pArray)
    {}

    constexpr Float4x4A(Float4 v0, Float4 v1, Float4 v2, Float4 v3) noexcept
        : Float4x4(v0, v1, v2, v3)
    {}

    CEMeta(Editor, ScriptCallable) bool Equal(const Float4x4A& other) const
    {
        return *this == other;
    }
};

inline Float4x4 operator+(const Float4x4& M1, const Float4x4& M2)
{
    using namespace DirectX;
    XMVECTOR x1 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M1.m00));
    XMVECTOR x2 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M1.m10));
    XMVECTOR x3 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M1.m20));
    XMVECTOR x4 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M1.m30));

    XMVECTOR y1 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M2.m00));
    XMVECTOR y2 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M2.m10));
    XMVECTOR y3 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M2.m20));
    XMVECTOR y4 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M2.m30));

    x1 = XMVectorAdd(x1, y1);
    x2 = XMVectorAdd(x2, y2);
    x3 = XMVectorAdd(x3, y3);
    x4 = XMVectorAdd(x4, y4);

    Float4x4 R;
    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&R.m00), x1);
    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&R.m10), x2);
    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&R.m20), x3);
    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&R.m30), x4);
    return R;
}

inline Float4x4 operator-(const Float4x4& M1, const Float4x4& M2)
{
    DirectX::XMVECTOR x1 = DirectX::XMLoadFloat4(reinterpret_cast<const DirectX::XMFLOAT4*>(&M1.m00));
    DirectX::XMVECTOR x2 = DirectX::XMLoadFloat4(reinterpret_cast<const DirectX::XMFLOAT4*>(&M1.m10));
    DirectX::XMVECTOR x3 = DirectX::XMLoadFloat4(reinterpret_cast<const DirectX::XMFLOAT4*>(&M1.m20));
    DirectX::XMVECTOR x4 = DirectX::XMLoadFloat4(reinterpret_cast<const DirectX::XMFLOAT4*>(&M1.m30));

    DirectX::XMVECTOR y1 = DirectX::XMLoadFloat4(reinterpret_cast<const DirectX::XMFLOAT4*>(&M2.m00));
    DirectX::XMVECTOR y2 = DirectX::XMLoadFloat4(reinterpret_cast<const DirectX::XMFLOAT4*>(&M2.m10));
    DirectX::XMVECTOR y3 = DirectX::XMLoadFloat4(reinterpret_cast<const DirectX::XMFLOAT4*>(&M2.m20));
    DirectX::XMVECTOR y4 = DirectX::XMLoadFloat4(reinterpret_cast<const DirectX::XMFLOAT4*>(&M2.m30));

    x1 = DirectX::XMVectorSubtract(x1, y1);
    x2 = DirectX::XMVectorSubtract(x2, y2);
    x3 = DirectX::XMVectorSubtract(x3, y3);
    x4 = DirectX::XMVectorSubtract(x4, y4);

    Float4x4 R;
    DirectX::XMStoreFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&R.m00), x1);
    DirectX::XMStoreFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&R.m10), x2);
    DirectX::XMStoreFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&R.m20), x3);
    DirectX::XMStoreFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&R.m30), x4);
    return R;
}

inline Float4x4 operator*(const Float4x4& M1, const Float4x4& M2)
{
    DirectX::XMMATRIX m1 = DirectX::XMLoadFloat4x4(reinterpret_cast<const DirectX::XMFLOAT4X4*>(&M1));
    DirectX::XMMATRIX m2 = DirectX::XMLoadFloat4x4(reinterpret_cast<const DirectX::XMFLOAT4X4*>(&M2));
    DirectX::XMMATRIX X = DirectX::XMMatrixMultiply(m1, m2);

    Float4x4 R;
    DirectX::XMStoreFloat4x4(reinterpret_cast<DirectX::XMFLOAT4X4*>(&R), X);
    return R;
}

inline Float4x4 operator*(const Float4x4& M, float S)
{
    using namespace DirectX;
    XMVECTOR x1 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M.m00));
    XMVECTOR x2 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M.m10));
    XMVECTOR x3 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M.m20));
    XMVECTOR x4 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M.m30));

    x1 = XMVectorScale(x1, S);
    x2 = XMVectorScale(x2, S);
    x3 = XMVectorScale(x3, S);
    x4 = XMVectorScale(x4, S);

    Float4x4 R;
    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&R.m00), x1);
    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&R.m10), x2);
    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&R.m20), x3);
    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&R.m30), x4);
    return R;
}

inline Float4x4 operator/(const Float4x4& M, float S)
{
    using namespace DirectX;
    assert(S != 0.f);

    XMVECTOR x1 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M.m00));
    XMVECTOR x2 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M.m10));
    XMVECTOR x3 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M.m20));
    XMVECTOR x4 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M.m30));

    float rs = 1.f / S;

    x1 = XMVectorScale(x1, rs);
    x2 = XMVectorScale(x2, rs);
    x3 = XMVectorScale(x3, rs);
    x4 = XMVectorScale(x4, rs);

    Float4x4 R;
    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&R.m00), x1);
    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&R.m10), x2);
    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&R.m20), x3);
    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&R.m30), x4);
    return R;
}

inline Float4x4 operator/(const Float4x4& M1, const Float4x4& M2)
{
    using namespace DirectX;
    XMVECTOR x1 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M1.m00));
    XMVECTOR x2 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M1.m10));
    XMVECTOR x3 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M1.m20));
    XMVECTOR x4 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M1.m30));

    XMVECTOR y1 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M2.m00));
    XMVECTOR y2 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M2.m10));
    XMVECTOR y3 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M2.m20));
    XMVECTOR y4 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M2.m30));

    x1 = XMVectorDivide(x1, y1);
    x2 = XMVectorDivide(x2, y2);
    x3 = XMVectorDivide(x3, y3);
    x4 = XMVectorDivide(x4, y4);

    Float4x4 R;
    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&R.m00), x1);
    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&R.m10), x2);
    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&R.m20), x3);
    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&R.m30), x4);
    return R;
}

inline Float4x4 operator*(float S, const Float4x4& M)
{
    using namespace DirectX;

    XMVECTOR x1 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M.m00));
    XMVECTOR x2 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M.m10));
    XMVECTOR x3 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M.m20));
    XMVECTOR x4 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&M.m30));

    x1 = XMVectorScale(x1, S);
    x2 = XMVectorScale(x2, S);
    x3 = XMVectorScale(x3, S);
    x4 = XMVectorScale(x4, S);

    Float4x4 R;
    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&R.m00), x1);
    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&R.m10), x2);
    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&R.m20), x3);
    XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&R.m30), x4);
    return R;
}

inline Float4 operator*(const Float4x4& M, const Float4& V);
inline Float4 operator*(const Float4& V, const Float4x4& M);

inline Float4x4 OuterProduct(const Float4& U, const Float4& V)
{
    Float4x4 result;
    for (int i = 0; i < 4; i++)
    {
        for (int j = 0; j < 4; j++)
        {
            result[i * 4 + j] = U[i] * V[j];
        }
    }

    return result;
}

struct Random
{
public:
    static inline float GetHaltonValue(SInt32 index, SInt32 base)
    {
        float Result = 0.0f;
        float InvBase = 1.0f / base;
        float Fraction = InvBase;
        while (index > 0)
        {
            Result += (index % base) * Fraction;
            index /= base;
            Fraction *= InvBase;
        }
        return Result;
    }
};

enum ContainmentType
{
    ContainmentDisjoint = DirectX::ContainmentType::DISJOINT,
    ContainmentIntersect = DirectX::ContainmentType::INTERSECTS,
    ContainmentContain = DirectX::ContainmentType::CONTAINS,
};

enum PlaneIntersectionType
{
    PlaneIntersectionFront = DirectX::PlaneIntersectionType::FRONT,
    PlaneIntersectionIntersecting = DirectX::PlaneIntersectionType::INTERSECTING,
    PlaneIntersectionBack = DirectX::PlaneIntersectionType::BACK,
};

struct BoundingBox;
struct BoundingOrientedBox;
struct BoundingFrustum;

// plane definition: normal * x + d = 0

struct CEMeta(Cli) Plane2
{
    CEMeta(Cli)
    Float3 mNormal;
    CEMeta(Cli)
    float mDistance{0.0f};
    Plane2() {}
    CEMeta(Cli)
    Plane2(float a, float b, float c, float d)
        : mNormal(a, b, c)
        , mDistance(d)
    {}

    inline float Distance()
    {
        return mDistance;
    }

    inline const float& a() const
    {
        return mNormal.x;
    }
    inline const float& b() const
    {
        return mNormal.y;
    }
    inline const float& c() const
    {
        return mNormal.z;
    }
    inline const float& d() const
    {
        return mDistance;
    }

    inline const Float3& GetNormal() const
    {
        return mNormal;
    }

    inline Plane2& operator*=(float scale)
    {
        mNormal *= scale;
        mDistance *= scale;
        return *this;
    }

    inline bool operator==(const Plane2& p) const
    {
        return mNormal == p.mNormal && mDistance == p.mDistance;
    }
    inline bool operator!=(const Plane2& p) const
    {
        return mNormal != p.mNormal || mDistance != p.mDistance;
    }

    void SetInvalid()
    {
        mNormal = Float3::Zero();
        mDistance = 0.0f;
    }

    inline void SetABCD(const float a, const float b, const float c, const float d)
    {
        mNormal = {a, b, c};
        mDistance = d;
    }

    inline void Set3Points(const Float3& a, const Float3& b, const Float3& c)
    {
        mNormal = (b - a).Cross(c - a);
        mNormal.Normalize();
        mDistance = -mNormal.Dot(a);
    }

    inline void Set3PointsUnnormalized(const Float3& a, const Float3& b, const Float3& c)
    {
        mNormal = (b - a).Cross(c - a);
        mDistance = -mNormal.Dot(a);
    }

    inline void SetNormalAndPosition(const Float3& inNormal, const Float3& inPoint)
    {
        mNormal = inNormal;
        mDistance = -mNormal.Dot(inPoint);
    }

    inline float GetDistanceToPoint(const Float3& inPt) const
    {
        return mNormal.Dot(inPt) + mDistance;
    }

    // ingore inPt.w for Float4
    inline float GetDistanceToPoint(const Float4& inPt) const
    {
        // Dot3(mNormal, inPt) + mDistance
        return mNormal.x * inPt.x + mNormal.y * inPt.y + mNormal.z * inPt.z + mDistance;
    }

    inline bool GetSide(const Float3& inPt) const
    {
        return GetDistanceToPoint(inPt) > 0.0f;
    }

    inline bool SameSide(const Float3& inPt0, const Float3& inPt1)
    {
        float d0 = GetDistanceToPoint(inPt0);
        float d1 = GetDistanceToPoint(inPt1);
        return (d0 * d1 > 0.0f) || (d0 == 0.0f && d1 == 0.0f);
    }

    // This function handles zero and extremely small normal, but can be slow.
    inline void NormalizeRobust()
    {
        float invMag;
        mNormal = mNormal.StableNormalized(invMag);
        mDistance *= invMag;
    }

    // This function don't handles zero or extremely small normal, remember to check for division by zero with zero
    // vectors.
    inline void NormalizeUnsafe()
    {
        float invMag = 1.0f / mNormal.Length();
        mNormal *= invMag;
        mDistance *= invMag;
    }
};

struct CROSS_BASE_API BoundingSphere
{
    friend struct BoundingBox;
    friend struct BoundingOrientedBox;
    friend struct BoundingFrustum;

private:
    DirectX::BoundingSphere mSphere;

public:
    // Creators
    BoundingSphere() noexcept
        : mSphere(){};
    BoundingSphere(const BoundingSphere&) = default;
    BoundingSphere& operator=(const BoundingSphere&) = default;
    BoundingSphere(BoundingSphere&&) = default;
    BoundingSphere& operator=(BoundingSphere&&) = default;

    BoundingSphere(const Float3& center, float radius) noexcept
        : mSphere(*(reinterpret_cast<const DirectX::XMFLOAT3*>(&center)), radius)
    {}

    inline Float3 GetCenter() const
    {
        return Float3(mSphere.Center.x, mSphere.Center.y, mSphere.Center.z);
    }

    inline float GetRadius() const
    {
        return mSphere.Radius;
    }

    // Methods
    // Transform the sphere
    inline void Transform(BoundingSphere& Out, Float4x4& M) const noexcept;
    inline void Transform(BoundingSphere& Out, float Scale, Quaternion& Rotation, Float3& Translation) const noexcept;
    inline void Transform(BoundingSphere& Out, const Float3& Translation) const noexcept;

    inline ContainmentType Contains(Float3 Point) const noexcept;

    // Contains with other shapes
    inline ContainmentType Contains(Float3 V0, Float3 V1, Float3 V2) const noexcept;

    inline ContainmentType Contains(const BoundingSphere& sh) const noexcept;
    inline ContainmentType Contains(const BoundingBox& box) const noexcept;
    inline ContainmentType Contains(const BoundingOrientedBox& box) const noexcept;
    inline ContainmentType Contains(const BoundingFrustum& fr) const noexcept;

    // Intersects with other shapes
    inline bool Intersects(const BoundingSphere& sh) const noexcept;
    inline bool Intersects(const BoundingBox& box) const noexcept;
    inline bool Intersects(const BoundingOrientedBox& box) const noexcept;
    inline bool Intersects(const BoundingFrustum& fr) const noexcept;

    // Triangle sphere test
    inline bool Intersects(Float3 V0, Float3 V3, Float3 V2) const noexcept;

    // Plane-sphere test
    inline PlaneIntersectionType Intersects(Float4 Plane) const noexcept;

    // Ray-sphere test
    inline bool Intersects(Float4 Origin, Float4 Direction, float& Dist) const noexcept;

    // Test sphere against six planes (see BoundingFrustum::GetPlanes)
    inline ContainmentType ContainedBy(Float4 plane0, Float4 plane1, Float4 plane2, Float4 plane3, Float4 plane4, Float4 plane5) const noexcept;

    // static methods
    static  void CreateMerged(BoundingSphere& Out, const BoundingSphere& S1, const BoundingSphere& S2) noexcept;

    static  void CreateFromBoundingBox(BoundingSphere& Out, const BoundingBox& box) noexcept;
    static  void CreateFromBoundingBox(BoundingSphere& Out, const BoundingOrientedBox& box) noexcept;

    static  void CreateFromPoints(BoundingSphere& Out, size_t Count, _In_reads_bytes_(sizeof(Float3) + Stride * (Count - 1)) const Float3* pPoints, size_t Stride) noexcept;

    static  void CreateFromFrustum(BoundingSphere& Out, const BoundingFrustum& fr) noexcept;
};
//-------------------------------------------------------------------------------------
// Axis-aligned bounding box
// AABB
//-------------------------------------------------------------------------------------

struct CROSS_BASE_API BoundingBox
{
    friend struct BoundingSphere;
    friend struct BoundingOrientedBox;
    friend struct BoundingFrustum;

    enum class Flags
    {
        MergeIdentity
    };

private:
    DirectX::BoundingBox mBox;

public:
    // Creators
    BoundingBox() noexcept
        : mBox(){};
    BoundingBox(const BoundingBox&) = default;
    BoundingBox& operator=(const BoundingBox&) = default;
    BoundingBox(BoundingBox&&) = default;
    BoundingBox& operator=(BoundingBox&&) = default;

    BoundingBox(const Float3& center, const Float3& extent) noexcept
        : mBox(*(reinterpret_cast<const DirectX::XMFLOAT3*>(&center)), *(reinterpret_cast<const DirectX::XMFLOAT3*>(&extent)))
    {}

    BoundingBox(Flags)
        : BoundingBox(Float3::Zero(), Float3(-std::numeric_limits<float>::infinity(), -std::numeric_limits<float>::infinity(), -std::numeric_limits<float>::infinity()))
    {}

    // Methods
    // Transform the sphere
    inline void Transform(BoundingBox& Out, const Float4x4& M) const noexcept;
    inline void Transform(BoundingBox& Out, float Scale, const Quaternion& Rotation, const Float3& Translation) const noexcept;
    inline void Transform(BoundingBox& Out, const Float3& Translation) const noexcept;

    inline void GetCorners(_Out_writes_(8) Float3* Corners) const noexcept;
    inline void GetCenter(Float3* Out) const noexcept;
    inline const Float3& GetCenter() const noexcept;
    inline void GetExtent(Float3* Out) const noexcept;
    inline const Float3& GetExtent() const noexcept;
    inline void GetMinMax(Float3* OutMin, Float3* OutMax) const noexcept;
    inline void Encapsulate(const Float3& point) noexcept;
    inline ContainmentType Contains(Float3 Point) const noexcept;

    // Contains with other shapes
    inline ContainmentType Contains(Float3 V0, Float3 V1, Float3 V2) const noexcept;

    inline ContainmentType Contains(const BoundingSphere& sh) const noexcept;
    inline ContainmentType Contains(const BoundingBox& box) const noexcept;
    inline ContainmentType Contains(const BoundingOrientedBox& box) const noexcept;
    inline ContainmentType Contains(const BoundingFrustum& fr) const noexcept;
    // Intersects with other shapes
    inline bool Intersects(const BoundingSphere& sh) const noexcept;
    inline bool Intersects(const BoundingBox& box) const noexcept;
    inline bool Intersects(const BoundingOrientedBox& box) const noexcept;
    inline bool Intersects(const BoundingFrustum& fr) const noexcept;
    inline bool Intersects(Float4 Origin, Float4 Direction, float& Dist) const noexcept;   // Ray-AABB test
    inline bool Intersects(Float3 V0, Float3 V1, Float3 V2) const noexcept;                // Triangle AABB test
    inline PlaneIntersectionType Intersects(Float4 Plane) const noexcept;                  // Plane-AABB test

    // Test sphere against six planes (see BoundingFrustum::GetPlanes)
    inline ContainmentType ContainedBy(Float4 plane0, Float4 plane1, Float4 plane2, Float4 plane3, Float4 plane4, Float4 plane5) const noexcept;

    // Distance between surfaces
    inline float Distance(const BoundingSphere& sh) const noexcept;

    // Adds to bounding box
    inline BoundingBox& operator+=(const BoundingBox& box) noexcept;
    inline BoundingBox& operator+=(const Float3& point) noexcept;

    // Static methods
    static  void CreateMerged(BoundingBox& Out, const BoundingBox& b1, const BoundingBox& b2) noexcept;

    static void CreateFromSphere(BoundingBox& Out, const BoundingSphere& sh) noexcept;

    static void CreateFromPoints(BoundingBox& Out, Float3 pt1, Float3 pt2) noexcept;

    static void CreateFromPoints(BoundingBox& Out, size_t Count, _In_reads_bytes_(sizeof(Float3) + Stride * (Count - 1)) const Float3* pPoints, size_t Stride) noexcept;

    static void CreateIntersection(BoundingBox& Out, const BoundingBox& b1, const BoundingBox& b2) noexcept;
};

//-------------------------------------------------------------------------------------
// Oriented bounding box
// OBB
//-------------------------------------------------------------------------------------

struct CROSS_BASE_API BoundingOrientedBox
{
    friend struct BoundingBox;
    friend struct BoundingSphere;
    friend struct BoundingFrustum;

private:
    DirectX::BoundingOrientedBox mOBox;

public:
    // Creators
    BoundingOrientedBox() noexcept
        : mOBox(){};
    BoundingOrientedBox(const BoundingOrientedBox&) = default;
    BoundingOrientedBox& operator=(const BoundingOrientedBox&) = default;
    BoundingOrientedBox(BoundingOrientedBox&&) = default;
    BoundingOrientedBox& operator=(BoundingOrientedBox&&) = default;

    BoundingOrientedBox(const Float3& center, const Float3& extent, const Quaternion& orientation) noexcept
        : mOBox(*(reinterpret_cast<const DirectX::XMFLOAT3*>(&center)), *(reinterpret_cast<const DirectX::XMFLOAT3*>(&extent)), *(reinterpret_cast<const DirectX::XMFLOAT4*>(&orientation)))
    {}
    // Methods
    // Transform the sphere
    inline void Transform(BoundingOrientedBox& Out, const Float4x4& M) const noexcept;
    inline void Transform(BoundingOrientedBox& Out, float Scale, const Quaternion& Rotation, const Float3& Translation) const noexcept;
    inline void Transform(BoundingOrientedBox& Out, const Float3& Translation) const noexcept;

    inline void GetCorners(_Out_writes_(8) Float3* Corners) const noexcept;

    inline void GetCenter(Float3* Out) const noexcept;
    inline const Float3& GetCenter() const noexcept;
    inline void GetExtent(Float3* Out) const noexcept;
    inline const Float3& GetExtent() const noexcept;
    inline Float3& GetExtent() noexcept;
    inline void GetOrientation(Quaternion* Out) const noexcept;
    inline const Quaternion& GetOrientation() const noexcept;
    inline void SetOrientation(Quaternion in) noexcept;

    inline ContainmentType Contains(Float3 Point) const noexcept;

    // Contains with other shapes
    inline ContainmentType Contains(Float3 V0, Float3 V1, Float3 V2) const noexcept;

    inline ContainmentType Contains(const BoundingSphere& sh) const noexcept;
    inline ContainmentType Contains(const BoundingBox& box) const noexcept;
    inline ContainmentType Contains(const BoundingOrientedBox& box) const noexcept;

    inline ContainmentType Contains(const BoundingFrustum& fr) const noexcept;

    // Intersects with other shapes
    inline bool Intersects(const BoundingSphere& sh) const noexcept;
    inline bool Intersects(const BoundingBox& box) const noexcept;
    inline bool Intersects(const BoundingOrientedBox& box) const noexcept;
    inline bool Intersects(const BoundingFrustum& fr) const noexcept;

    // Triangle OBB test
    inline bool Intersects(Float3 V0, Float3 V1, Float3 V2) const noexcept;

    // Plane-OBB test
    inline PlaneIntersectionType Intersects(Float4 Plane) const noexcept;

    // Ray-OBB test
    inline bool Intersects(Float4 Origin, Float4 Direction, float& Dist) const noexcept;

    // Test sphere against six planes (see BoundingFrustum::GetPlanes)
    inline ContainmentType ContainedBy(Float4 plane0, Float4 plane1, Float4 plane2, Float4 plane3, Float4 plane4, Float4 plane5) const noexcept;

    static inline void CreateFromBoundingBox(BoundingOrientedBox& Out, const BoundingBox& box) noexcept;

    static inline void CreateFromPoints(BoundingOrientedBox& Out, size_t Count, _In_reads_bytes_(sizeof(Float3) + Stride * (Count - 1)) const Float3* pPoints, size_t Stride) noexcept;
};

struct CROSS_BASE_API BoundingFrustum
{
    friend struct BoundingBox;
    friend struct BoundingOrientedBox;
    friend struct BoundingSphere;

private:
    DirectX::BoundingFrustum mFrustum;

public:
    BoundingFrustum() {}
    BoundingFrustum(const Float4x4& projection, bool rhcoords = false)
        : mFrustum(DirectX::XMLoadFloat4x4(reinterpret_cast<const DirectX::XMFLOAT4X4*>(&projection)), rhcoords)
    {}
    BoundingFrustum(const BoundingFrustum&) = default;
    BoundingFrustum& operator=(const BoundingFrustum&) = default;
    BoundingFrustum(BoundingFrustum&&) = default;
    BoundingFrustum& operator=(BoundingFrustum&&) = default;
    BoundingFrustum(const Float3& origin, const Float4& orientation, float rightSlope, float leftSlope, float topSlope, float bottomSlope, float _near, float _far) noexcept
        : mFrustum(*(reinterpret_cast<const DirectX::XMFLOAT3*>(&origin)), *(reinterpret_cast<const DirectX::XMFLOAT4*>(&orientation)), rightSlope, leftSlope, topSlope, bottomSlope, _near, _far)
    {}

    // Methods// Transform the frustum
    inline void Transform(BoundingFrustum& Out, const Float4x4& M) const noexcept;
    inline void Transform(BoundingFrustum& Out, float Scale, const Quaternion& Rotation, const Float3& Translation) const noexcept;

    inline void GetCorners(_Out_writes_(8) Float3* Corners) const noexcept;

    inline ContainmentType Contains(Float3 Point) const noexcept;

    // Contains with other shapes
    inline ContainmentType Contains(Float3 V0, Float3 V1, Float3 V2) const noexcept;

    inline ContainmentType Contains(const BoundingSphere& sh) const noexcept;
    inline ContainmentType Contains(const BoundingBox& box) const noexcept;
    inline ContainmentType Contains(const BoundingOrientedBox& box) const noexcept;

    inline ContainmentType Contains(const BoundingFrustum& fr) const noexcept;

    // Intersects with other shapes
    inline bool Intersects(const BoundingSphere& sh) const noexcept;
    inline bool Intersects(const BoundingBox& box) const noexcept;
    inline bool Intersects(const BoundingOrientedBox& box) const noexcept;
    inline bool Intersects(const BoundingFrustum& fr) const noexcept;

    // Triangle Frustum test
    inline bool Intersects(Float3 V0, Float3 V1, Float3 V2) const noexcept;

    // Plane-Frustum test
    inline PlaneIntersectionType Intersects(Float4 Plane) const noexcept;

    // Ray-Frustum test
    inline bool Intersects(Float4 Origin, Float4 Direction, float& Dist) const noexcept;

    // Test sphere against six planes (see BoundingFrustum::GetPlanes)
    inline ContainmentType ContainedBy(Float4 plane0, Float4 plane1, Float4 plane2, Float4 plane3, Float4 plane4, Float4 plane5) const noexcept;

    // Test frustum against six planes (see BoundingFrustum::GetPlanes)

    inline void GetPlanes(Float4* NearPlane, Float4* FarPlane, Float4* RightPlane, Float4* LeftPlane, Float4* TopPlane, Float4* BottomPlane) const noexcept;

    // CTG Begin: Double Dong, create frustum without projection matrix
    // static methods
    static void CreateFromCameraInfo(BoundingFrustum& Out, const float AspectRatioOrWidth, const float FovOrHeight, const float NearPlane, const float FarPlane, const bool bPerspectiveCamera = true) noexcept;
    // CTG End
};

struct Double2;
struct Double3;
struct Double4x4;

Double2 operator+(const Double2& v1, const Double2& v2);
Double2 operator+(const Double2& v1, double v2);
Double2 operator-(const Double2& v1, const Double2& v2);
Double2 operator-(const Double2& v1, double v2);
Double2 operator*(const Double2& v1, const Double2& v2);
Double2 operator*(const Double2& v, double S);
Double2 operator/(const Double2& v1, const Double2& v2);
Double2 operator*(double S, const Double2& v);
struct CEMeta(Cli, Puerts, WorkflowType, Reflect) Double2
{
    CEMeta(Editor, ScriptReadWrite)
    double x{0.0};
    CEMeta(Editor, ScriptReadWrite)
    double y{0.0};

    CEMeta(Editor, ScriptCallable) Double2() = default;
    CEMeta(Editor, ScriptCallable) Double2(const Double2&) = default;
    CEMeta(Editor, ScriptCallable) explicit Double2(const Float2& v)
    {
        x = static_cast<double>(v.x);
        y = static_cast<double>(v.y);
    }
    CEMeta(Editor, ScriptCallable) constexpr Double2(double _x, double _y) noexcept
        : x(_x)
        , y(_y)
    {}
    explicit inline operator Float2() const noexcept
    {
        return Float2(static_cast<float>(x), static_cast<float>(y));
    }
    CEMeta(Editor, ScriptCallable) Float2 ToFloat2() const
    {
        return Float2(static_cast<float>(x), static_cast<float>(y));
    }
    inline bool operator==(const Double2& v) const
    {
        return (x == v.x) && (y == v.y);
    }
    inline bool operator!=(const Double2& v) const
    {
        return (x != v.x) || (y != v.y);
    }
    Double2& operator=(const Double2&) = default;
    Double2(Double2&&) = default;
    Double2& operator=(Double2&&) = default;

    CEMeta(Editor, ScriptCallable) inline double Length() const
    {
        return sqrt(x * x + y * y);
    }
    CEMeta(Editor, ScriptCallable) inline void Normalize()
    {
        double length = Length();
        if (length > 0)
            length = 1.0 / length;

        x *= length;
        y *= length;
    }
    CEMeta(Editor, ScriptCallable) inline double Dot(const Double2& v) const
    {
        return (x * v.x + y * v.y);
    }
    CEMeta(Editor, ScriptCallable) double Cross(const Double2& v) const
    {
        return x * v.y - y * v.x;
    }
    inline Double2 operator-() const
    {
        return Double2(-x, -y);
    }
    CEMeta(Editor, ScriptCallable)
    static inline Double2 Zero()
    {
        return Double2(0.0, 0.0);
    }
    inline const bool IsNearlyZero(double tolerance = 1.e-14) const
    {
        return MathUtils::Abs(x) <= tolerance && MathUtils::Abs(y) <= tolerance;
    }

    double* data()
    {
        return &x;
    }
    const double* data() const
    {
        return &x;
    }

    inline SerializeNode Serialize() const
    {
        SerializeNode node = {"x"_k = x, "y"_k = y};
        return node;
    }
    inline void Deserialize(const DeserializeNode& json)
    {
        this->x = json["x"].AsDouble();
        this->y = json["y"].AsDouble();
    }
    
    CEMeta(Editor, ScriptCallable) static inline double Distance(const Double2& v1, const Double2& v2)
    {
        double deltaX = v1.x - v2.x;
        double deltaY = v1.y - v2.y;
        return sqrt(deltaX * deltaX + deltaY * deltaY);
    }

    CEMeta(Editor, ScriptCallable)
    inline Double2 Sub(const Double2& other) const
    {
        return *this - other;
    }

    CEMeta(Editor, ScriptCallable)
    inline Double2 Sub(double other) const
    {
        return *this - other;
    }
    CEMeta(ScriptCallable)
    inline Double2 Add(const Double2& v) const
    {
        return *this + v;
    }
    CEMeta(ScriptCallable)
    inline Double2 Mult(double S) const
    {
        return *this * S;
    }
    CEMeta(Editor, ScriptCallable)
    static inline Double2 CatmullRom(Double2 v1, Double2 v2, Double2 v3, Double2 v4, float t)
    {
        using namespace DirectX;
        XMVECTOR256D x1 = XMLoadDouble2(reinterpret_cast<const XMDOUBLE2*>(&v1));
        XMVECTOR256D x2 = XMLoadDouble2(reinterpret_cast<const XMDOUBLE2*>(&v2));
        XMVECTOR256D x3 = XMLoadDouble2(reinterpret_cast<const XMDOUBLE2*>(&v3));
        XMVECTOR256D x4 = XMLoadDouble2(reinterpret_cast<const XMDOUBLE2*>(&v4));

        XMVECTOR p1 = XMConvertVectorDoubleToFloat(x1);
        XMVECTOR p2 = XMConvertVectorDoubleToFloat(x2);
        XMVECTOR p3 = XMConvertVectorDoubleToFloat(x3);
        XMVECTOR p4 = XMConvertVectorDoubleToFloat(x4);

        Float2 result;
        DirectX::XMStoreFloat2(reinterpret_cast<XMFLOAT2*>(&result), DirectX::XMVectorCatmullRom(p1, p2, p3, p4, t));

        return Double2{result};
    }
};
inline Double2 operator+(const Double2& v1, const Double2& v2)
{
    return Double2(v1.x + v2.x, v1.y + v2.y);
}

inline Double2 operator+(const Double2& v1, double v2)
{
    return Double2(v1.x + v2, v1.y + v2);
}

inline Double2 operator-(const Double2& v1, const Double2& v2)
{
    return Double2(v1.x - v2.x, v1.y - v2.y);
}

inline Double2 operator-(const Double2& v1, double v2)
{
    return Double2(v1.x - v2, v1.y - v2);
}

inline Double2 operator*(const Double2& v1, const Double2& v2)
{
    return Double2(v1.x * v2.x, v1.y * v2.y);
}

inline Double2 operator*(const Double2& v, double S)
{
    return Double2(v.x * S, v.y * S);
}

inline Double2 operator/(const Double2& v1, const Double2& v2)
{
    return Double2(v1.x / v2.x, v1.y / v2.y);
}

inline Double2 operator*(double S, const Double2& v)
{
    return Double2(v.x * S, v.y * S);
}


Double3 operator+(const Double3& v1, const Double3& v2);
Double3 operator+(const Double3& v1, double v2);
Double3 operator-(const Double3& v1, const Double3& v2);
Double3 operator-(const Double3& v1, double v2);
Double3 operator*(const Double3& v1, const Double3& v2);
Double3 operator*(const Double3& v, double S);
Double3 operator/(const Double3& v1, const Double3& v2);
Double3 operator*(double S, const Double3& v);
struct CEMeta(Cli, Puerts, WorkflowType) Double3
{
    CEMeta(Editor, ScriptReadWrite)
    double x{0.0};
    CEMeta(Editor, ScriptReadWrite)
    double y{0.0};
    CEMeta(Editor, ScriptReadWrite)
    double z{0.0};

    CEMeta(Editor, ScriptCallable) Double3() = default;
    CEMeta(Editor, ScriptCallable) Double3(const Double3&) = default;
    CEMeta(Editor, ScriptCallable) explicit Double3(const Float3& v)
    {
        x = static_cast<double>(v.x);
        y = static_cast<double>(v.y);
        z = static_cast<double>(v.z);
    }
    Double3& operator=(const Double3&) = default;
    Double3(Double3&&) = default;
    Double3& operator=(Double3&&) = default;

    explicit inline operator Float3() const noexcept
    {
        return Float3(static_cast<float>(x), static_cast<float>(y), static_cast<float>(z));
    }

    explicit inline operator Float3A() const noexcept
    {
        return Float3A(static_cast<float>(x), static_cast<float>(y), static_cast<float>(z));
    }

    CEMeta(Editor, ScriptCallable) constexpr Double3(double _x, double _y, double _z) noexcept
        : x(_x)
        , y(_y)
        , z(_z)
    {}
    explicit Double3(const double* pArray) noexcept
        : x(pArray[0])
        , y(pArray[1])
        , z(pArray[2])
    {}

    CEMeta(Editor, ScriptCallable) Float3 ToFloat3() const
    {
        return Float3(static_cast<float>(x), static_cast<float>(y), static_cast<float>(z));
    }

    CEMeta(Editor, ScriptCallable) inline Double2 GetXY() const
    {
        return Double2(x, y);
    }

    CEMeta(Editor, ScriptCallable) bool Equal(const Double3& v) const
    {
        return *this == v;
    }

    inline bool operator==(const Double3& v) const
    {
        return (x == v.x) && (y == v.y) && (z == v.z);
    }

    inline bool operator!=(const Double3& v) const
    {
        return (x != v.x) || (y != v.y) || (z != v.z);
    }

    double* data()
    {
        return &x;
    }
    const double* data() const
    {
        return &x;
    }

    inline SerializeNode Serialize() const
    {
        SerializeNode node = {"x"_k = x, "y"_k = y, "z"_k = z};
        return node;
    }

    inline void Deserialize(const DeserializeNode& json)
    {
        this->x = json["x"].AsDouble();
        this->y = json["y"].AsDouble();
        this->z = json["z"].AsDouble();
    }

    std::string ToString() const
    {
        std::string ret = "(" + std::to_string(x) + "," + std::to_string(y) + "," + std::to_string(z) + ")";
        return ret;
    }

    CEMeta(Editor, ScriptCallable) Double3 ToRadian() const
    {
        return {MathUtils::ConvertToRadians(x), MathUtils::ConvertToRadians(y), MathUtils::ConvertToRadians(z)};
    }

    CEMeta(Editor, ScriptCallable) Double3 ToDegree() const
    {
        return {MathUtils::ConvertToDegrees(x), MathUtils::ConvertToDegrees(y), MathUtils::ConvertToDegrees(z)};
    }

    CEMeta(Editor, ScriptCallable) Double3 Add(const Double3& v) const
    {
        return *this + v;
    }

    inline Double3& operator+=(const Double3& v)
    {
        x += v.x;
        y += v.y;
        z += v.z;
        return *this;
    }

    CEMeta(Editor, ScriptCallable) Double3 Subtract(const Double3& v) const
    {
        return *this - v;
    }

    inline Double3& operator-=(const Double3& v)
    {
        x -= v.x;
        y -= v.y;
        z -= v.z;
        return *this;
    }

    CEMeta(Editor, ScriptCallable) Double3 Mult(const Double3& v) const
    {
        return *this * v;
    }

    inline Double3& operator*=(const Double3& v)
    {
        x *= v.x;
        y *= v.y;
        z *= v.z;
        return *this;
    }

    CEMeta(Editor, ScriptCallable) Double3 Mult(double S) const
    {
        return *this * S;
    }

    inline Double3& operator*=(double S)
    {
        x *= S;
        y *= S;
        z *= S;
        return *this;
    }

    CEMeta(Editor, ScriptCallable) Double3 Divide(double S) const
    {
        return *this / S;
    }

    CEMeta(Editor, ScriptCallable) Double3 Divide(const Double3& v) const
    {
        return *this / v;
    }

    CEMeta(Editor, ScriptCallable)
    inline Double3 Sub(const Double3& other) const
    {
        return *this - other;
    }

    CEMeta(Editor, ScriptCallable)
    inline Double3 Sub(double other) const
    {
        return *this - other;
    }

    inline Double3& operator/=(double S)
    {
        assert(S != 0.0);
        S = 1. / S;
        x *= S;
        y *= S;
        z *= S;
        return *this;
    }

    inline Double3 operator/(double S) const
    {
        assert(S != 0.0);
        S = 1. / S;
        return Double3(x * S, y * S, z * S);
    }

    inline Double3 operator-() const
    {
        return Double3(-x, -y, -z);
    }

    inline Double3 operator^(const Double3 v) const
    {
        return Double3(y * v.z - z * v.y, z * v.x - x * v.z, x * v.y - y * v.x);
    }

    CEMeta(Editor, ScriptCallable) Double3 Abs() const
    {
        return {abs(x), abs(y), abs(z)};
    }

    CEMeta(Editor, ScriptCallable) inline bool InBounds(const Double3& Bounds) const
    {
        return ((x <= Bounds.x) && (x >= -Bounds.x) && (y <= Bounds.y) && (y >= -Bounds.y) && (z <= Bounds.z) && (z >= -Bounds.z));
    }

    CEMeta(Editor, ScriptCallable) inline double Length() const
    {
        return sqrt(x * x + y * y + z * z);
    }

    CEMeta(Editor, ScriptCallable) inline double LengthXZ() const
    {
        return sqrt(x * x + z * z);
    }

    CEMeta(Editor, ScriptCallable) inline double LengthSquared() const
    {
        return (x * x + y * y + z * z);
    }

    CEMeta(Editor, ScriptCallable) inline double LengthSquaredXZ() const
    {
        return (x * x + z * z);
    }

    CEMeta(Editor, ScriptCallable) inline double Dot(const Double3& v) const
    {
        return (x * v.x + y * v.y + z * v.z);
    }

    inline void Cross(const Double3& v, Double3& result) const
    {
        result.x = (y * v.z) - (z * v.y);
        result.y = (z * v.x) - (x * v.z);
        result.z = (x * v.y) - (y * v.x);
    }

    CEMeta(Editor, ScriptCallable) inline Double3 Cross(const Double3& v) const
    {
        Double3 result;
        result.x = (y * v.z) - (z * v.y);
        result.y = (z * v.x) - (x * v.z);
        result.z = (x * v.y) - (y * v.x);
        return result;
    }

    CEMeta(Editor, ScriptCallable) Double3 Orthogonal() const
    {
        static const Double3 ZAxis(0, 0, 1);
        static const Double3 YAxis(0, 1, 0);
        static const Double3 XAxis(1, 0, 0);

        if (std::abs(Normalized().Dot(ZAxis)) > 1 - MathUtils::MathEpsD)
        {
            return Cross(XAxis);
        }
        else
        {
            return Cross(ZAxis);
        }
    }

    CEMeta(Editor, ScriptCallable) Double3 ProjectOnPlane(const Double3& n_normalized) const
    {
        const Double3& v_normalized = *this;

        double v_dot_n = v_normalized.Dot(n_normalized);
        Double3 t = {v_normalized.x - n_normalized.x * v_dot_n, v_normalized.y - n_normalized.y * v_dot_n, v_normalized.z - n_normalized.z * v_dot_n};
        return t.Normalized();
    }

    CEMeta(Editor, ScriptCallable) inline void Normalize()
    {
        double length = Length();
        if (length > 0)
            length = 1.0 / length;

        x *= length;
        y *= length;
        z *= length;
    }

    CEMeta(Editor, ScriptCallable) inline Double3 Normalized() const
    {
        double length = Length();
        if (length > 0)
            length = 1.0 / length;

        return Double3(x * length, y * length, z * length);
    }

    CEMeta(Editor, ScriptCallable) inline Double3 StableNormalized(double& invMag)
    {
        double a = DirectX::XMMax(DirectX::XMMax(abs(x), abs(y)), abs(z));
        if (a > 0.0)
        {
            double b = ((*this) / a).LengthSquared();
            if (b > 0.0)
            {
                invMag = 1.0 / (sqrt(b) * a);
                return (*this) / (sqrt(b) * a);
            }
        }

        invMag = 1.0;
        return *this;
    }

    CEMeta(Editor, ScriptCallable) inline Double3 StableNormalized()
    {
        double a = DirectX::XMMax(DirectX::XMMax(abs(x), abs(y)), abs(z));
        if (a > 0.0)
        {
            double b = ((*this) / a).LengthSquared();
            if (b > 0.0)
            {
                return (*this) / (sqrt(b) * a);
            }
        }
        return *this;
    }

    CEMeta(Editor, ScriptCallable) inline void StableNormalize()
    {
        double a = DirectX::XMMax(DirectX::XMMax(abs(x), abs(y)), abs(z));
        if (a > 0.0)
        {
            double b = ((*this) / a).LengthSquared();
            if (b > 0.0)
            {
                x /= (sqrt(b) * a);
                y /= (sqrt(b) * a);
                z /= (sqrt(b) * a);
            }
        }
    }

    inline Double3 SafeNormalXZ(double tolerance = 1e-14) const
    {
        double square = x * x + z * z;
        if (square < tolerance)
        {
            return Double3::Zero();
        }
        double scale = 1.0 / std::sqrt(square);
        return Double3(x * scale, 0., z * scale);
    }

    inline Double3 SafeNormal(double tolerance = 1e-14) const
    {
        double square = x * x + y * y + z * z;
        if (square < tolerance)
        {
            return Double3::Zero();
        }
        double scale = 1.0 / sqrt(square);
        return Double3(x * scale, y * scale, z * scale);
    }

    inline void ClampToMaxSize(double maxSize)
    {
        if (maxSize < 1e-4)
        {
            x = y = z = 0.;
            return;
        }

        double len = Length();
        if (len > maxSize)
        {
            double scale = maxSize * 1.0 / len;
            x *= scale;
            y *= scale;
            z *= scale;
        }
    }

    inline Double3 ClampToMaxSize(double maxSize) const
    {
        if (maxSize < 1e-4)
        {
            return Double3::Zero();
        }

        double len = Length();
        if (len > maxSize)
        {
            double scale = maxSize * 1.0 / len;
            return Double3(x * scale, y * scale, z * scale);
        }
        else
        {
            return *this;
        }
    }

    inline void Clamp(const Double3& vmin, const Double3& vmax)
    {
        x = (x >= vmin.x) ? x : vmin.x;
        x = (x <= vmax.x) ? x : vmax.x;
        y = (y >= vmin.y) ? y : vmin.y;
        y = (y <= vmax.y) ? y : vmax.y;
        z = (z >= vmin.z) ? z : vmin.z;
        z = (z <= vmax.z) ? z : vmax.z;
    }

    CEMeta(Editor, ScriptCallable) inline Double3 Clamp(const Double3& vmin, const Double3& vmax) const
    {
        return Double3((vmin.x >= x) ? vmin.x : (vmax.x <= x ? vmax.x : x), (vmin.y >= y) ? vmin.y : (vmax.y <= y ? vmax.y : y), (vmin.z >= z) ? vmin.z : (vmax.z <= z ? vmax.z : z));
    }

    inline const bool IsNaN() const
    {
        return isnan(x) || isnan(y) || isnan(z);
    }

    inline const bool IsInfinite() const
    {
        return isinf(x) || isinf(y) || isinf(z);
    }

    CEMeta(ScriptCallable)inline const bool IsNearlyZero(double tolerance = 1.e-14) const
    {
        return MathUtils::Abs(x) <= tolerance && MathUtils::Abs(y) <= tolerance && MathUtils::Abs(z) <= tolerance;
    }

    inline const bool IsValid() const
    {
        return !IsNaN() && !IsInfinite();
    }

    CEMeta(Editor, ScriptCallable) static inline double Distance(const Double3& v1, const Double3& v2)
    {
        double deltaX = v1.x - v2.x;
        double deltaY = v1.y - v2.y;
        double deltaZ = v1.z - v2.z;
        return sqrt(deltaX * deltaX + deltaY * deltaY + deltaZ * deltaZ);
    }

    CEMeta(Editor, ScriptCallable) static inline double DistanceSquared(const Double3& v1, const Double3& v2)
    {
        double deltaX = v1.x - v2.x;
        double deltaY = v1.y - v2.y;
        double deltaZ = v1.z - v2.z;
        return (deltaX * deltaX + deltaY * deltaY + deltaZ * deltaZ);
    }

    CEMeta(Editor, ScriptCallable) static inline Double3 Min(const Double3& v1, const Double3& v2)
    {
        Double3 result((v1.x <= v2.x) ? v1.x : v2.x, (v1.y <= v2.y) ? v1.y : v2.y, (v1.z <= v2.z) ? v1.z : v2.z);
        return result;
    }

    CEMeta(Editor, ScriptCallable) static inline Double3 Max(const Double3& v1, const Double3& v2)
    {
        Double3 result((v1.x >= v2.x) ? v1.x : v2.x, (v1.y >= v2.y) ? v1.y : v2.y, (v1.z >= v2.z) ? v1.z : v2.z);
        return result;
    }

    CEMeta(Editor, ScriptCallable) static inline Double3 Lerp(const Double3& v1, const Double3& v2, double t)
    {
        Double3 result(v1.x + ((v2.x - v1.x) * t), v1.y + ((v2.y - v1.y) * t), v1.z + ((v2.z - v1.z) * t));
        return result;
    }

    CEMeta(Editor, ScriptCallable) static inline Double3 Zero()
    {
        return Double3(0.0, 0.0, 0.0);
    }

    CEMeta(Editor, ScriptCallable) static inline Double3 One()
    {
        return Double3(1.0, 1.0, 1.0);
    }

    CEMeta(Editor, ScriptCallable) static inline Double3 SmoothStep(const Double3& v1, const Double3& v2, double t)
    {
        t = t > 1.0 ? 1.0 : ((t < 0.0) ? 0.0 : t);   // Clamp value to 0 to 1
        t = t * t * (3. - 2. * t);
        Double3 result(v1.x + ((v2.x - v1.x) * t), v1.y + ((v2.y - v1.y) * t), v1.z + ((v2.z - v1.z) * t));
        return result;
    }

    CEMeta(Editor, ScriptCallable) static inline Double3 Barycentric(const Double3& v1, const Double3& v2, const Double3& v3, double f, double g)
    {
        Double3 result((v1.x + (f * (v2.x - v1.x))) + (g * (v3.x - v1.x)), (v1.y + (f * (v2.y - v1.y))) + (g * (v3.y - v1.y)), (v1.z + (f * (v2.z - v1.z))) + (g * (v3.z - v1.z)));
        return result;
    }

    CEMeta(Editor, ScriptCallable) static inline Double3 Transform(const Double3& v, const Quaternion64& quat)
    {
        using namespace DirectX;
        XMVECTOR256D V3 = XMLoadDouble3(reinterpret_cast<const XMDOUBLE3*>(&v));
        XMVECTOR256D Q = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&quat));

        Double3 result;
        DirectX::XMStoreDouble3(reinterpret_cast<XMDOUBLE3*>(&result), DirectX::XMVector64x3Rotate(V3, Q));

        return result;
    }

    CEMeta(Editor, ScriptCallable) static inline Double3 Transform(const Double3& v, const Double4x4& m)
    {
        using namespace DirectX;
        XMVECTOR256D V3 = XMLoadDouble3(reinterpret_cast<const XMDOUBLE3*>(&v));
        XMMATRIX64 M4 = XMLoadDouble4x4(reinterpret_cast<const XMDOUBLE4X4*>(&m));

        Double3 result;
        DirectX::XMStoreDouble3(reinterpret_cast<XMDOUBLE3*>(&result), DirectX::XMVector64x3Transform(V3, M4));

        return result;
    }

    CEMeta(Editor, ScriptCallable) static inline Double3 TransformNormal(const Double3& v, const Double4x4& m)
    {
        using namespace DirectX;
        XMVECTOR256D V3 = XMLoadDouble3(reinterpret_cast<const XMDOUBLE3*>(&v));
        XMMATRIX64 M4 = XMLoadDouble4x4(reinterpret_cast<const XMDOUBLE4X4*>(&m));

        Double3 result;
        DirectX::XMStoreDouble3(reinterpret_cast<XMDOUBLE3*>(&result), DirectX::XMVector64x3TransformNormal(V3, M4));

        return result;
    }

    // Computes the radian angle between two 3D vectors.
    CEMeta(Editor, ScriptCallable) static inline double AngleBetweenVectors(const Double3& v1, const Double3& v2)
    {
        using namespace DirectX;
        XMVECTOR256D V1 = XMLoadDouble3(reinterpret_cast<const XMDOUBLE3*>(&v1));
        XMVECTOR256D V2 = XMLoadDouble3(reinterpret_cast<const XMDOUBLE3*>(&v2));

        auto re = XMVector64x3AngleBetweenVectors(V1, V2);
        return XMVector256GetX(re);
    }

    CEMeta(Editor, ScriptCallable) static inline Double3 PointPlaneProject(const Double3& point, const Double3& planeBase, const Double3& planeNormal)
    {
        return point - ((point - planeBase).Dot(planeNormal)) * planeNormal;
    }
};
inline Float4 operator*(const Float4x4& M, const Float4& V);

inline Float4 operator*(const Float4& v, const Float4x4& m);
// 3D Vector; 32 bit floating point components aligned on a 16 byte boundary
XM_ALIGNED_STRUCT(32) CEMeta(Puerts)
Double3A : public Double3
{
    Double3A() = default;

    Double3A(const Double3A&) = default;
    Double3A& operator=(const Double3A&) = default;
    Double3A(Double3A &&) = default;
    Double3A& operator=(Double3A&&) = default;
    Double3A(const Double3& vec)
        : Double3(vec)
    {}
    explicit Double3A(const Float3& in)
        : Double3(in)
    {}
    explicit Double3A(const Float3A& in)
        : Double3(in)
    {}
    Double3A& operator=(const Double3& v)
    {
        this->x = v.x;
        this->y = v.y;
        this->z = v.z;
        return *this;
    }
    CEMeta(Editor, ScriptCallable) double X()
    {
        return x;
    }
    CEMeta(Editor, ScriptCallable) double Y()
    {
        return y;
    }
    CEMeta(Editor, ScriptCallable) double Z()
    {
        return z;
    }
    CEMeta(Editor, ScriptCallable) bool Equal(const Double3A& other) const
    {
        return *this == other;
    }
    CEMeta(Editor, ScriptCallable) constexpr Double3A(double _x, double _y, double _z) noexcept
        : Double3(_x, _y, _z)
    {}
    explicit Double3A(const double* pArray) noexcept
        : Double3(pArray)
    {}
};

inline Double3 operator+(const Double3& v1, const Double3& v2)
{
    return Double3(v1.x + v2.x, v1.y + v2.y, v1.z + v2.z);
}

inline Double3 operator+(const Double3& v1, double v2)
{
    return Double3(v1.x + v2, v1.y + v2, v1.z + v2);
}

inline Double3 operator-(const Double3& v1, const Double3& v2)
{
    return Double3(v1.x - v2.x, v1.y - v2.y, v1.z - v2.z);
}

inline Double3 operator-(const Double3& v1, double v2)
{
    return Double3(v1.x - v2, v1.y - v2, v1.z - v2);
}

inline Double3 operator*(const Double3& v1, const Double3& v2)
{
    return Double3(v1.x * v2.x, v1.y * v2.y, v1.z * v2.z);
}

inline Double3 operator*(const Double3& v, double S)
{
    return Double3(v.x * S, v.y * S, v.z * S);
}

inline Double3 operator/(const Double3& v1, const Double3& v2)
{
    return Double3(v1.x / v2.x, v1.y / v2.y, v1.z / v2.z);
}

inline Double3 operator*(double S, const Double3& v)
{
    return Double3(v.x * S, v.y * S, v.z * S);
}

//****************************************************************************
// Double4
//****************************************************************************
struct Double4A;

struct CEMeta(Cli, Puerts, WorkflowType) Double4
{
    CEMeta(Editor, ScriptReadWrite)
    double x{0.0}, y{0.0}, z{0.0}, w{0.0};

    CEMeta(Editor, ScriptCallable) Double4() = default;

    CEMeta(Editor, ScriptCallable) Double4(const Double4&) = default;

    Double4& operator=(const Double4&) = default;

    Double4(Double4&&) = default;

    Double4& operator=(Double4&&) = default;

    Double4(const Double3& _xyz, double _w) noexcept
        : x(_xyz.x)
        , y(_xyz.y)
        , z(_xyz.z)
        , w(_w)
    {}
    CEMeta(Editor, ScriptCallable) Double4(double _x, double _y, double _z, double _w) noexcept
        : x(_x)
        , y(_y)
        , z(_z)
        , w(_w)
    {}

    Double4(double _x) noexcept
        : x(_x)
        , y(_x)
        , z(_x)
        , w(_x)
    {}

    explicit Double4(_In_reads_(4) const double* pArray) noexcept
        : x(pArray[0])
        , y(pArray[1])
        , z(pArray[2])
        , w(pArray[3])
    {}

    explicit Double4(const Float4& in)
    {
        x = static_cast<double>(in.x);
        y = static_cast<double>(in.y);
        z = static_cast<double>(in.z);
        w = static_cast<double>(in.w);
    }

    explicit inline operator Float4() const noexcept
    {
        return Float4(static_cast<float>(x), static_cast<float>(y), static_cast<float>(z), static_cast<float>(w));
    }

    explicit inline operator Float4A() const noexcept
    {
        return Float4A(static_cast<float>(x), static_cast<float>(y), static_cast<float>(z), static_cast<float>(w));
    }

    inline void Set(double _x, double _y, double _z, double _w)
    {
        x = _x;
        y = _y;
        z = _z;
        w = _w;
    }

    double* data()
    {
        return &x;
    }
    const double* data() const
    {
        return &x;
    }

    std::string ToString() const
    {
        std::string ret = "(" + std::to_string(x) + "," + std::to_string(y) + "," + std::to_string(z) + "," + std::to_string(w) + ")";
        return ret;
    }

    inline SerializeNode Serialize() const
    {
        return SerializeNode{"x"_k = x, "y"_k = y, "z"_k = z, "w"_k = w};
    }
    inline void Deserialize(const DeserializeNode& json)
    {
        this->x = json["x"].AsDouble();
        this->y = json["y"].AsDouble();
        this->z = json["z"].AsDouble();
        this->w = json["w"].AsDouble();
    }

    double& operator[](int i)
    {
        return reinterpret_cast<double*>(&x)[i];
    }

    double operator[](int i) const
    {
        return reinterpret_cast<const double*>(&x)[i];
    }

    inline bool operator==(const Double4& v) const
    {
        return (x == v.x && y == v.y && z == v.z && w == v.w);
    }

    inline bool operator!=(const Double4& v) const
    {
        return (x != v.x || y != v.y || z != v.z || w != v.w);
    }

    inline Double4& operator+=(const Double4& v)
    {
        x += v.x;
        y += v.y;
        z += v.z;
        w += v.w;
        return *this;
    }

    inline Double4& operator-=(const Double4& v)
    {
        x -= v.x;
        y -= v.y;
        z -= v.z;
        w -= v.w;
        return *this;
    }

    inline Double4& operator*=(const Double4& v)
    {
        x *= v.x;
        y *= v.y;
        z *= v.z;
        w *= v.w;
        return *this;
    }

    inline Double4& operator*=(double s)
    {
        x *= s;
        y *= s;
        z *= s;
        w *= s;
        return *this;
    }

    inline Double4& operator/=(double s)
    {
        x /= s;
        y /= s;
        z /= s;
        w /= s;
        return *this;
    }

    inline Double4 operator-() const
    {
        return Double4(-x, -y, -z, -w);
    }

    CEMeta(Editor, ScriptCallable) Double4 Abs() const
    {
        return {abs(x), abs(y), abs(z), abs(w)};
    }

    inline bool InBounds(const Double4& Bounds) const
    {
        return (((x <= Bounds.x && x >= -Bounds.x) && (y <= Bounds.y && y >= -Bounds.y) && (z <= Bounds.z && z >= -Bounds.z) && (w <= Bounds.w && w >= -Bounds.w)) != 0);
    }

    CEMeta(Editor, ScriptCallable) inline double Length() const
    {
        return sqrt(x * x + y * y + z * z + w * w);
    }

    inline double LengthSquared() const
    {
        return (x * x + y * y + z * z + w * w);
    }

    CEMeta(Editor, ScriptCallable) inline double Dot(const Double4& v) const
    {
        return (x * v.x + y * v.y + z * v.z + w * v.w);
    }

    CEMeta(Editor, ScriptCallable) inline void Normalize()
    {
        double length = Length();
        if (length > 0)
            length = 1.0 / length;

        x *= length;
        y *= length;
        z *= length;
        w *= length;
    }

    inline Double4 Normalized() const
    {
        double length = Length();
        if (length > 0)
            length = 1.0 / length;

        return Double4(x * length, y * length, z * length, w * length);
    }

    inline Double4 Clamp(const Double4& vmin, const Double4& vmax) const
    {
        return Double4((vmin.x >= x) ? vmin.x : (vmax.x <= x ? vmax.x : x), (vmin.y >= y) ? vmin.y : (vmax.y <= y ? vmax.y : y), (vmin.z >= z) ? vmin.z : (vmax.z <= z ? vmax.z : z), (vmin.w >= w) ? vmin.w : (vmax.w <= w ? vmax.w : w));
    }

    inline void Clamp(const Double4& vmin, const Double4& vmax)
    {
        x = (vmin.x >= x) ? vmin.x : (vmax.x <= x ? vmax.x : x);
        y = (vmin.y >= y) ? vmin.y : (vmax.y <= y ? vmax.y : y);
        z = (vmin.z >= z) ? vmin.z : (vmax.z <= z ? vmax.z : z);
        w = (vmin.w >= w) ? vmin.w : (vmax.w <= w ? vmax.w : w);
    }

    inline Double3 XYZ() const
    {
        return Double3(x, y, z);
    }

    CEMeta(Editor, ScriptCallable) inline bool IsNaN()
    {
        auto v = DirectX::XMLoadDouble4(reinterpret_cast<const DirectX::XMDOUBLE4*>(this));
        return DirectX::XMVector64x4HasNaN(v);
    }

    static inline double Distance(const Double4& v1, const Double4& v2)
    {
        Double4 delta(v1.x - v2.x, v1.y - v2.y, v1.z - v2.z, v1.w - v2.w);
        return delta.Length();
    }

    static inline double DistanceSquared(const Double4& v1, const Double4& v2)
    {
        Double4 delta(v1.x - v2.x, v1.y - v2.y, v1.z - v2.z, v1.w - v2.w);
        return delta.Dot(delta);
    }

    static inline Double4 Min(const Double4& v1, const Double4& v2)
    {
        Double4 result((v1.x <= v2.x) ? v1.x : v2.x, (v1.y <= v2.y) ? v1.y : v2.y, (v1.z <= v2.z) ? v1.z : v2.z, (v1.w <= v2.w) ? v1.w : v2.w);
        return result;
    }

    static inline Double4 Max(const Double4& v1, const Double4& v2)
    {
        Double4 result((v1.x >= v2.x) ? v1.x : v2.x, (v1.y >= v2.y) ? v1.y : v2.y, (v1.z >= v2.z) ? v1.z : v2.z, (v1.w >= v2.w) ? v1.w : v2.w);
        return result;
    }

    static inline Double4 Lerp(const Double4& v1, const Double4& v2, double t)
    {
        Double4 result(v1.x + ((v2.x - v1.x) * t), v1.y + ((v2.y - v1.y) * t), v1.z + ((v2.z - v1.z) * t), v1.w + ((v2.w - v1.w) * t));
        return result;
    }

    static inline Double4 SmoothStep(const Double4& v1, const Double4& v2, double t)
    {
        t = (t > 1.0) ? 1.0 : ((t < 0.0) ? 0.0 : t);   // Clamp value to 0 to 1
        t = t * t * (3.0 - 2.0 * t);
        Double4 result(v1.x + ((v2.x - v1.x) * t), v1.y + ((v2.y - v1.y) * t), v1.z + ((v2.z - v1.z) * t), v1.w + ((v2.w - v1.w) * t));
        return result;
    }

    static inline Double4 Zero()
    {
        return Double4(0.0, 0.0, 0.0, 0.0);
    }

    CEMeta(Editor, ScriptCallable)
    static inline Double4 CatmullRom(Double4 v1, Double4 v2, Double4 v3, Double4 v4, float t)
    {
        using namespace DirectX;
        XMVECTOR256D x1 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&v1));
        XMVECTOR256D x2 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&v2));
        XMVECTOR256D x3 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&v3));
        XMVECTOR256D x4 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&v4));

        XMVECTOR p1 = XMConvertVectorDoubleToFloat(x1);
        XMVECTOR p2 = XMConvertVectorDoubleToFloat(x2);
        XMVECTOR p3 = XMConvertVectorDoubleToFloat(x3);
        XMVECTOR p4 = XMConvertVectorDoubleToFloat(x4);

        Float4 result;
        DirectX::XMStoreFloat4(reinterpret_cast<XMFLOAT4*>(&result), DirectX::XMVectorCatmullRom(p1, p2, p3, p4, t));

        return Double4{result};
    }

    // static inline Double4 BaryCentric(const Double4& v1, const Double4& v2, const Double4& v3, double f, double g);
    //
    // static inline Double4 CatmullRom(const Double4& v1, const Double4& v2, const Double4& v3, const Double4& v4, double t);
    //
    // static inline Double4 Hermite(const Double4& v1, const Double4& t1, const Double4& v2, const Double4& t2, double t);
    //
    // static inline Double4 Reflect(const Double4& ivec, const Double4& nvec);
    //
    // static inline Double4 Refract(const Double4& ivec, const Double4& nvec, double refractionIndex);
};

// 4D Vector; 32 bit floating point components aligned on a 16 byte boundary
XM_ALIGNED_STRUCT(32) CEMeta(Puerts)
Double4A : public Double4
{
    Double4A() = default;

    Double4A(const Double4A&) = default;
    Double4A& operator=(const Double4A&) = default;

    Double4A(Double4A &&) = default;
    Double4A& operator=(Double4A&&) = default;

    Double4A(const Double4& vec)
        : Double4(vec)
    {}

    Double4A(double _x, double _y, double _z, double _w) noexcept
        : Double4(_x, _y, _z, _w)
    {}
    explicit Double4A(_In_reads_(4) const double* pArray) noexcept
        : Double4(pArray)
    {}
    explicit Double4A(const Float4& in)
        : Double4(in)
    {}
    explicit Double4A(const Float4A& in)
        : Double4(in)
    {}
};

inline Double4 operator+(const Double4& V1, const Double4& V2)
{
    return Double4(V1.x + V2.x, V1.y + V2.y, V1.z + V2.z, V1.w + V2.w);
}

inline Double4 operator-(const Double4& V1, const Double4& V2)
{
    return Double4(V1.x - V2.x, V1.y - V2.y, V1.z - V2.z, V1.w - V2.w);
}

inline Double4 operator*(const Double4& V1, const Double4& V2)
{
    return Double4(V1.x * V2.x, V1.y * V2.y, V1.z * V2.z, V1.w * V2.w);
}

inline Double4 operator*(const Double4& v, double S)
{
    return Double4(v.x * S, v.y * S, v.z * S, v.w * S);
}

inline Double4 operator/(const Double4& V1, const Double4& V2)
{
    return Double4(V1.x / V2.x, V1.y / V2.y, V1.z / V2.z, V1.w / V2.w);
}

inline Double4 operator*(double S, const Double4& v)
{
    return Double4(v.x * S, v.y * S, v.z * S, v.w * S);
}

inline bool operator==(const Double4A& v1, const Double4& v2)
{
    return v1.x == v2.x && v1.y == v2.y && v1.z == v2.z && v1.w == v2.w;
}

inline bool operator==(const Double4& v1, const Double4A& v2)
{
    return v1.x == v2.x && v1.y == v2.y && v1.z == v2.z && v1.w == v2.w;
}

//****************************************************************************
// Quaternion
//****************************************************************************
inline Quaternion64 operator+(const Quaternion64& Q1, const Quaternion64& Q2);
inline Quaternion64 operator-(const Quaternion64& Q1, const Quaternion64& Q2);
inline Quaternion64 operator*(const Quaternion64& Q1, const Quaternion64& Q2);
inline Quaternion64 operator*(const Quaternion64& Q, double S);
inline Quaternion64 operator/(const Quaternion64& Q1, const Quaternion64& Q2);
inline Quaternion64 operator*(double S, const Quaternion64& Q);

struct CROSS_BASE_API CEMeta(Cli, Puerts, WorkflowType) Quaternion64
{
    CEMeta(Editor, ScriptReadWrite)
    double x{0.0f}, y{0.0f}, z{0.0f}, w{1.0f};

    CEMeta(Editor, ScriptCallable) Quaternion64() = default;

    CEMeta(Editor, ScriptCallable) Quaternion64(const Quaternion64&) = default;
    Quaternion64& operator=(const Quaternion64&) = default;
    Quaternion64(Quaternion64&&) = default;
    Quaternion64& operator=(Quaternion64&&) = default;

    CEMeta(Editor, ScriptCallable) constexpr Quaternion64(double _x, double _y, double _z, double _w) noexcept
        : x(_x)
        , y(_y)
        , z(_z)
        , w(_w)
    {}
    explicit Quaternion64(const double* pArray) noexcept
        : x(pArray[0])
        , y(pArray[1])
        , z(pArray[2])
        , w(pArray[3])
    {}

    explicit Quaternion64(const Quaternion& in)
    {
        x = in.x;
        y = in.y;
        z = in.z;
        w = in.w;
    }

    explicit inline operator Quaternion() const noexcept
    {
        return Quaternion(static_cast<float>(x), static_cast<float>(y), static_cast<float>(z), static_cast<float>(w));
    }

    explicit inline operator QuaternionA() const noexcept
    {
        return QuaternionA(static_cast<float>(x), static_cast<float>(y), static_cast<float>(z), static_cast<float>(w));
    }

    /*
        extract pitch(x), yaw(y), and roll(z) from given Quaternion64 in radian.
    */
    CEMeta(Editor, ScriptCallable) static inline Double3 Quaternion64ToEuler(const Quaternion64& rot)
    {
        double x = rot.x, y = rot.y, z = rot.z, w = rot.w;
        double temp = 2 * (y * z - x * w);
        if (temp >= 1 - MathUtils::MathSmallNumberD)
        {
            return Double3(-MathUtils::MathPiDiv2D, atan2(y, w), atan2(z, w));
        }
        else if (-temp >= 1 - MathUtils::MathSmallNumberD)
        {
            return Double3(MathUtils::MathPiDiv2D, atan2(y, w), atan2(z, w));
        }
        else
        {
            return Double3(-asin(temp), atan2(x * z + y * w, 0.5 - x * x - y * y), atan2(x * y + z * w, 0.5 - x * x - z * z));
        }
    }

    const double* data()
    {
        return &x;
    }

    // Log and exp of Quaternion
    /*
        extract Quaternion64 from given pitch(x), yaw(y), and roll(z) in radian.
    */
    CEMeta(Editor, ScriptCallable) static inline Quaternion64 EulerToQuaternion64(const Double3& rot)
    {
        return CreateFromYawPitchRoll(rot.y, rot.x, rot.z);
    }

    // https://en.wikipedia.org/wiki/Quaternion#Exponential,_logarithm,_and_power_functions
    CEMeta(Editor, ScriptCallable) static inline Double3 Log(const Quaternion64& q, double eps = 1e-6)
    {
        double length = sqrt(q.x * q.x + q.y * q.y + q.z * q.z);

        if (length < eps)
        {
            return Double3(q.x, q.y, q.z);
        }
        else
        {
            double halfangle = acos(std::clamp(q.w, -1.0, 1.0));
            return halfangle * (Double3(q.x, q.y, q.z) / length);
        }
    }

    CEMeta(Editor, ScriptCallable) static inline Quaternion64 Exp(const Double3& v, double eps = 1e-6)
    {
        double halfangle = sqrt(v.x * v.x + v.y * v.y + v.z * v.z);

        if (halfangle < eps)
        {
            return Quaternion64(v.x, v.y, v.z, 1.0).Normalized();
        }
        else
        {
            double c = cos(halfangle);
            double s = sin(halfangle) / halfangle;
            return Quaternion64(s * v.x, s * v.y, s * v.z, c);
        }
    }

    CEMeta(Editor, ScriptCallable)
    static inline Quaternion64 Scale(const Quaternion64& v, double s) 
    { 
        return Quaternion64(v.x * s, v.y * s, v.z * s, v.w * s);
    }

    CEMeta(Editor, ScriptCallable)
    static inline Quaternion64 Add(const Quaternion64& v1, const Quaternion64& v2) 
    { 
        return Quaternion64(v1.x + v2.x, v1.y + v2.y, v1.z + v2.z, v1.w + v2.w); 
    }

    CEMeta(Editor, ScriptCallable) static inline Quaternion64 Identity()
    {
        return Quaternion64(0.0, 0.0, 0.0, 1.0);
    }

    inline SerializeNode Serialize() const
    {
        return SerializeNode{"x"_k = x, "y"_k = y, "z"_k = z, "w"_k = w};
    }

    inline void Deserialize(const DeserializeNode& json)
    {
        this->x = json["x"].AsDouble();
        this->y = json["y"].AsDouble();
        this->z = json["z"].AsDouble();
        this->w = json["w"].AsDouble();
    }

    CEMeta(Editor, ScriptCallable) bool Equal(const Quaternion64& other) const
    {
        return *this == other;
    }

    inline bool operator==(const Quaternion64& q) const
    {
        return (x == q.x && y == q.y && z == q.z && w == q.w);
    }

    inline bool operator!=(const Quaternion64& q) const
    {
        return (x != q.x || y != q.y || z != q.z || w != q.w);
    }

    inline Double3 XYZ() const
    {
        return {x, y, z};
    }

    inline double AngularDistance(const Quaternion64& other) const
    {
        Quaternion64 qua = (*this) * other.Conjugate();
        return 2.0 * std::atan2(qua.XYZ().Length(), std::abs(qua.w));
    }

    CEMeta(Editor, ScriptCallable) Quaternion64 Mult(const Quaternion64& other) const
    {
        return *this * other;
    }

    inline Quaternion64& operator*=(const Quaternion64& q)
    {
        Quaternion64 Result{(q.w * x) + (q.x * w) + (q.y * z) - (q.z * y), (q.w * y) - (q.x * z) + (q.y * w) + (q.z * x), (q.w * z) + (q.x * y) - (q.y * x) + (q.z * w), (q.w * w) - (q.x * x) - (q.y * y) - (q.z * z)};
        *this = Result;
        return *this;
    }

    inline Quaternion64 operator-() const
    {
        return Quaternion64(-x, -y, -z, -w);
    }

    CEMeta(Editor, ScriptCallable) inline Quaternion64 Abs()
    {
        return w < 0 ? -(*this) : *this;
    }

    CEMeta(Editor, ScriptCallable) inline double LengthSquared() const
    {
        return x * x + y * y + z * z + w * w;
    }

    CEMeta(Editor, ScriptCallable) inline double Length() const
    {
        return sqrt(LengthSquared());
    }

    CEMeta(Editor, ScriptCallable) inline void Normalize()
    {
        double length = Length();

        // Prevent divide by zero
        if (length > 0)
        {
            length = 1.0 / length;
        }

        x *= length;
        y *= length;
        z *= length;
        w *= length;
    }

    CEMeta(Editor, ScriptCallable) inline Quaternion64 Normalized() const
    {
        Quaternion64 Result(x, y, z, w);
        Result.Normalize();
        return Result;
    }

    CEMeta(Editor, ScriptCallable) inline Quaternion64 Conjugate() const
    {
        return Quaternion64(-x, -y, -z, w);
    }

    CEMeta(Editor, ScriptCallable) inline Quaternion64 Inverse() const
    {
        double length = Length();
        // Prevent divide by zero
        if (length > 0)
        {
            length = 1.0 / length;
        }
        Quaternion64 Result = Conjugate();

        return Quaternion64(Result.x * length, Result.y * length, Result.z * length, Result.w * length);
    }

    CEMeta(Editor, ScriptCallable) inline Double3 GetForwardVector() const
    {
        return Double3Rotate(Double3(0.0, 0.0, 1.0));
    }

    CEMeta(Editor, ScriptCallable) inline Double3 GetUpVector() const
    {
        return Double3Rotate(Double3(0.0, 1.0, 0.0));
    }

    CEMeta(Editor, ScriptCallable) inline Double3 GetRightVector() const
    {
        return Double3Rotate(Double3(1.0, 0.0, 0.0));
    }

    CEMeta(Editor, ScriptCallable) inline double GetAngleRad() const
    {
        return 2.0 * std::acos(w);
    }

    CEMeta(Editor, ScriptCallable) inline Double3 GetAxis() const
    {
        const double sin = std::sqrt(MathUtils::Max(1.0 - w * w, 0.));

        if (sin >= 0.0001)
            return Double3(x / sin, y / sin, z / sin);
        else
            return Double3(1.0, 0., 0.);
    }

    CEMeta(Editor, ScriptCallable) inline Double3 Double3Rotate(const Double3& v) const;

    inline Double4 Double4Rotate(const Double4& v) const;

    CEMeta(Editor, ScriptCallable) inline bool IsNaN()
    {
        auto v = DirectX::XMLoadDouble4(reinterpret_cast<const DirectX::XMDOUBLE4*>(this));
        return DirectX::XMVector64x4HasNaN(v);
    }

    CEMeta(Editor, ScriptCallable) inline bool IsIdentity() const
    {
        return IsEqual(*this, Quaternion64::Identity());
    }

    CEMeta(Editor, ScriptCallable) static inline Quaternion64 CreateFromAxisAngle(const Double3& axis, double angle)
    {
        using namespace DirectX;
        FXMVECTOR256D A = DirectX::XMLoadDouble3(reinterpret_cast<const XMDOUBLE3*>(&axis));

        Quaternion64 result;
        DirectX::XMStoreDouble4(reinterpret_cast<DirectX::XMDOUBLE4*>(&result), DirectX::XMQuaternion64RotationAxis(A, angle));
        return result;
    }

    CEMeta(Editor, ScriptCallable) static inline Quaternion64 CreateFromYawPitchRollInDegrees(double yaw, double pitch, double roll)
    {
        pitch = MathUtils::ConvertToRadians(pitch);
        yaw = MathUtils::ConvertToRadians(yaw);
        roll = MathUtils::ConvertToRadians(roll);
        return CreateFromYawPitchRoll(yaw, pitch, roll);
    }

    CEMeta(Editor, ScriptCallable) static inline Quaternion64 CreateFromYawPitchRoll(double yaw, double pitch, double roll)
    {
        double rollOver2 = roll * 0.5;
        double sinRollOver2, cosRollOver2;
        MathUtils::SinCos(rollOver2, sinRollOver2, cosRollOver2);

        double pitchOver2 = pitch * 0.5;
        double sinPitchOver2, cosPitchOver2;
        MathUtils::SinCos(pitchOver2, sinPitchOver2, cosPitchOver2);

        double yawOver2 = yaw * 0.5;
        double sinYawOver2, cosYawOver2;
        MathUtils::SinCos(yawOver2, sinYawOver2, cosYawOver2);

        Quaternion64 result;
        result.w = cosYawOver2 * cosPitchOver2 * cosRollOver2 + sinYawOver2 * sinPitchOver2 * sinRollOver2;
        result.x = cosYawOver2 * sinPitchOver2 * cosRollOver2 + sinYawOver2 * cosPitchOver2 * sinRollOver2;
        result.y = sinYawOver2 * cosPitchOver2 * cosRollOver2 - cosYawOver2 * sinPitchOver2 * sinRollOver2;
        result.z = cosYawOver2 * cosPitchOver2 * sinRollOver2 - sinYawOver2 * sinPitchOver2 * cosRollOver2;

        return result;
    }

    CEMeta(Editor, ScriptCallable) static inline Quaternion64 CreateFromRotationMatrix(const Double4x4& M)
    {
        using namespace DirectX;
        FXMMATRIX64 MX = DirectX::XMLoadDouble4x4(reinterpret_cast<const XMDOUBLE4X4*>(&M));
        Quaternion64 result;
        DirectX::XMStoreDouble4(reinterpret_cast<DirectX::XMDOUBLE4*>(&result), DirectX::XMQuaternion64RotationMatrix(MX));
        return result;
    }

    CEMeta(Editor, ScriptCallable) static inline Quaternion64 Slerp(const Quaternion64& q1, const Quaternion64& q2, double t)
    {
        auto v1 = DirectX::XMLoadDouble4(reinterpret_cast<const DirectX::XMDOUBLE4*>(&q1));
        auto v2 = DirectX::XMLoadDouble4(reinterpret_cast<const DirectX::XMDOUBLE4*>(&q2));
        Quaternion64 result;
        DirectX::XMStoreDouble4(reinterpret_cast<DirectX::XMDOUBLE4*>(&result), DirectX::XMQuaternion64Slerp(v1, v2, t));
        return result;
    }

    CEMeta(Editor, ScriptCallable) static inline Quaternion64 Concatenate(const Quaternion64& q1, const Quaternion64& q2)
    {
        auto v1 = DirectX::XMLoadDouble4(reinterpret_cast<const DirectX::XMDOUBLE4*>(&q1));
        auto v2 = DirectX::XMLoadDouble4(reinterpret_cast<const DirectX::XMDOUBLE4*>(&q2));
        Quaternion64 result;
        DirectX::XMStoreDouble4(reinterpret_cast<DirectX::XMDOUBLE4*>(&result), DirectX::XMQuaternion64Multiply(v1, v2));
        return result;
    }

    CEMeta(Editor, ScriptCallable) static inline Quaternion64 CreateFrom2Vectors(const Double3& from, const Double3& to, const Double3& rotateAxis = Double3(0, 1, 0))
    {
        auto from_n = from.Normalized();
        auto to_n = to.Normalized();

        double cos = from_n.Dot(to_n);
        // from and to are in the same direction
        if (cos > 1 - MathUtils::MathEpsD)
        {
            return Quaternion64::Identity();
        }
        // from and to are in the opposite direction
        else if (cos < -(1 - MathUtils::MathEpsD))
        {
            return Quaternion64::CreateFromAxisAngle(rotateAxis.Normalized(), MathUtils::MathPi);
        }
        else
        {
            auto sin = from_n.Cross(to_n);
            Quaternion64 ret(sin.x, sin.y, sin.z, cos + 1.0);
            return ret.Normalized();
        }
    }

    CEMeta(Editor, ScriptCallable) static inline double Dot(const Quaternion64& q1, const Quaternion64& q2)
    {
        auto v1 = DirectX::XMLoadDouble4(reinterpret_cast<const DirectX::XMDOUBLE4*>(&q1));
        auto v2 = DirectX::XMLoadDouble4(reinterpret_cast<const DirectX::XMDOUBLE4*>(&q2));

        auto re = DirectX::XMQuaternion64Dot(v1, v2);
        return DirectX::XMVector256GetX(re);
    }

    CEMeta(Editor, ScriptCallable) static inline bool IsEqual(const Quaternion64& q1, const Quaternion64& q2)
    {
        return q1 == q2;
    }

    CEMeta(Editor, ScriptCallable) static inline double Distance(const Quaternion64& q1, const Quaternion64& q2) 
    {
        auto re = Dot(q1, q2);
        return (std::clamp)(1 - re * re, 0.0, 1.0);
    }
};

XM_ALIGNED_STRUCT(32) CEMeta(Puerts)
Quaternion64A : public Quaternion64
{
    Quaternion64A() = default;
    Quaternion64A(const Quaternion64A&) = default;
    Quaternion64A& operator=(const Quaternion64A&) = default;
    Quaternion64A(Quaternion64A &&) = default;
    Quaternion64A& operator=(Quaternion64A&&) = default;
    Quaternion64A(const Quaternion64& q)
        : Quaternion64(q)
    {}
    CEMeta(Editor, ScriptCallable) constexpr Quaternion64A(double _x, double _y, double _z, double _w) noexcept
        : Quaternion64(_x, _y, _z, _w)
    {}
    explicit Quaternion64A(const double* pArray) noexcept
        : Quaternion64(pArray)
    {}
    explicit Quaternion64A(const Quaternion& in)
        : Quaternion64(in)
    {}
    explicit Quaternion64A(const QuaternionA& in)
        : Quaternion64(in)
    {}
    CEMeta(Editor, ScriptCallable) static inline Quaternion64A EulerToQuaternion64(const Float3A& rot)
    {
        return Quaternion64::CreateFromYawPitchRoll(rot.y, rot.x, rot.z);
    }
    CEMeta(Editor, ScriptCallable) static Double3A Quaternion64ToEuler(const Quaternion64A& rot)
    {
        return Quaternion64::Quaternion64ToEuler(rot);
    }
    CEMeta(Editor, ScriptCallable) bool Equal(const Quaternion64A& other)
    {
        return *this == other;
    }
};

inline Quaternion64 operator*(const Quaternion64& Q1, const Quaternion64& Q2)
{
    // Generic Quaternion1 * Quaternion2 product
    Quaternion64 q;
    q.w = (Q2.w * Q1.w) - (Q2.x * Q1.x) - (Q2.y * Q1.y) - (Q2.z * Q1.z);
    q.x = (Q2.w * Q1.x) + (Q2.x * Q1.w) + (Q2.y * Q1.z) - (Q2.z * Q1.y);
    q.y = (Q2.w * Q1.y) - (Q2.x * Q1.z) + (Q2.y * Q1.w) + (Q2.z * Q1.x);
    q.z = (Q2.w * Q1.z) + (Q2.x * Q1.y) - (Q2.y * Q1.x) + (Q2.z * Q1.w);
    return q;
}

inline Quaternion64 operator/(const Quaternion64& Q1, const Quaternion64& Q2)
{
    return Q1 * Q2.Inverse();
}

Double4x4 operator+(const Double4x4& M1, const Double4x4& M2);
Double4x4 operator-(const Double4x4& M1, const Double4x4& M2);
Double4x4 operator*(const Double4x4& M1, const Double4x4& M2);
Double4x4 operator*(const Double4x4& M1, double S);
Double4x4 operator/(const Double4x4& M1, double S);
Double4x4 operator/(const Double4x4& M1, const Double4x4& M2);
//****************************************************************************
// Matrix
// row major
//****************************************************************************
#pragma warning(push)
#pragma warning(disable : 4702)
struct CROSS_BASE_API CEMeta(Puerts, WorkflowType, Cli) Double4x4
{
    CEMeta(Editor, ScriptReadWrite, Cli)
    double m00{1.0f}, m01{0.0f}, m02{0.0f}, m03{0.0f};
    CEMeta(Editor, ScriptReadWrite, Cli)
    double m10{0.0f}, m11{1.0f}, m12{0.0f}, m13{0.0f};
    CEMeta(Editor, ScriptReadWrite, Cli)
    double m20{0.0f}, m21{0.0f}, m22{1.0f}, m23{0.0f};
    CEMeta(Editor, ScriptReadWrite, Cli)
    double m30{0.0f}, m31{0.0f}, m32{0.0f}, m33{1.0f};

    CEMeta(Editor, ScriptCallable) Double4x4() = default;

    CEMeta(Editor, ScriptCallable) Double4x4(const Double4x4&) = default;

    Double4x4(Double4x4&&) = default;

    Double4x4& operator=(const Double4x4&) = default;

    Double4x4& operator=(Double4x4&&) = default;

    double& operator[](int i)
    {
        return reinterpret_cast<double*>(&m00)[i];
    }
    CEMeta(Editor, ScriptCallable)
    explicit Double4x4(const Float4x4& v)
    {
        using namespace DirectX;
        XMVECTOR x1 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&v.m00));
        XMVECTOR x2 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&v.m10));
        XMVECTOR x3 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&v.m20));
        XMVECTOR x4 = XMLoadFloat4(reinterpret_cast<const XMFLOAT4*>(&v.m30));
        XMVECTOR256D y1 = XMConvertVectorFloatToDouble(x1);
        XMVECTOR256D y2 = XMConvertVectorFloatToDouble(x2);
        XMVECTOR256D y3 = XMConvertVectorFloatToDouble(x3);
        XMVECTOR256D y4 = XMConvertVectorFloatToDouble(x4);
        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&m00), y1);
        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&m10), y2);
        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&m20), y3);
        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&m30), y4);
    }
    explicit operator Float4x4() const noexcept;
    explicit operator Float4x4A() const noexcept;

    inline SerializeNode Serialize() const
    {
        return SerializeNode{"m00"_k = m00,
                             "m01"_k = m01,
                             "m02"_k = m02,
                             "m03"_k = m03,
                             "m10"_k = m10,
                             "m11"_k = m11,
                             "m12"_k = m12,
                             "m13"_k = m13,
                             "m20"_k = m20,
                             "m21"_k = m21,
                             "m22"_k = m22,
                             "m23"_k = m23,
                             "m30"_k = m30,
                             "m31"_k = m31,
                             "m32"_k = m32,
                             "m33"_k = m33};
    }

    inline bool Validate()
    {
        for (int i = 0; i < 16; i++)
        {
            if (isnan(data()[i]))
            {
                Assert(!isnan(data()[i]) && "matrix item is nan");
                return false;
            }
            else if (isinf(data()[i]))
            {
                Assert(!isinf(data()[i]) && "matrix item is inf");
                return false;
            }
        }
        return true;
    }

    inline void Deserialize(const DeserializeNode& json)
    {
        m00 = json["m00"].AsDouble();
        m01 = json["m01"].AsDouble();
        m02 = json["m02"].AsDouble();
        m03 = json["m03"].AsDouble();

        m10 = json["m10"].AsDouble();
        m11 = json["m11"].AsDouble();
        m12 = json["m12"].AsDouble();
        m13 = json["m13"].AsDouble();

        m20 = json["m20"].AsDouble();
        m21 = json["m21"].AsDouble();
        m22 = json["m22"].AsDouble();
        m23 = json["m23"].AsDouble();

        m30 = json["m30"].AsDouble();
        m31 = json["m31"].AsDouble();
        m32 = json["m32"].AsDouble();
        m33 = json["m33"].AsDouble();
    }

    CEMeta(Editor, ScriptCallable) constexpr Double4x4(double m00, double m01, double m02, double m03, double m10, double m11, double m12, double m13, double m20, double m21, double m22, double m23, double m30, double m31, double m32, double m33) noexcept
        : m00(m00)
        , m01(m01)
        , m02(m02)
        , m03(m03)
        , m10(m10)
        , m11(m11)
        , m12(m12)
        , m13(m13)
        , m20(m20)
        , m21(m21)
        , m22(m22)
        , m23(m23)
        , m30(m30)
        , m31(m31)
        , m32(m32)
        , m33(m33)
    {}

    CEMeta(Editor, ScriptCallable) constexpr Double4x4(Double4 v0, Double4 v1, Double4 v2, Double4 v3) noexcept
        : m00(v0.x)
        , m01(v0.y)
        , m02(v0.z)
        , m03(v0.w)
        , m10(v1.x)
        , m11(v1.y)
        , m12(v1.z)
        , m13(v1.w)
        , m20(v2.x)
        , m21(v2.y)
        , m22(v2.z)
        , m23(v2.w)
        , m30(v3.x)
        , m31(v3.y)
        , m32(v3.z)
        , m33(v3.w)
    {}

    CEMeta(Editor, ScriptCallable) Float4x4 ToFloat4x4() const
    {
        return (Float4x4)*this;
    }

    explicit Double4x4(_In_reads_(16) const double* pArray) noexcept
        : m00(pArray[0])
        , m01(pArray[1])
        , m02(pArray[2])
        , m03(pArray[3])
        , m10(pArray[4])
        , m11(pArray[5])
        , m12(pArray[6])
        , m13(pArray[7])
        , m20(pArray[8])
        , m21(pArray[9])
        , m22(pArray[10])
        , m23(pArray[11])
        , m30(pArray[12])
        , m31(pArray[13])
        , m32(pArray[14])
        , m33(pArray[15])
    {}

    double operator()(uint32_t Row, uint32_t Column) const noexcept
    {
        return reinterpret_cast<const double*>(&m00)[Row * 4 + Column];
    }

    double& operator()(uint32_t Row, uint32_t Column) noexcept
    {
        return reinterpret_cast<double*>(&m00)[Row * 4 + Column];
    }

    const double* data() const
    {
        return &m00;
    }

    CEMeta(Editor, ScriptCallable) bool Equal(const Double4x4& M) const
    {
        return *this == M;
    }

    inline bool operator==(const Double4x4& M) const
    {
        using namespace DirectX;
        XMVECTOR256D x1 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&m00));
        XMVECTOR256D x2 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&m10));
        XMVECTOR256D x3 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&m20));
        XMVECTOR256D x4 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&m30));

        XMVECTOR256D y1 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M.m00));
        XMVECTOR256D y2 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M.m10));
        XMVECTOR256D y3 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M.m20));
        XMVECTOR256D y4 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M.m30));

        return (XMVector64x4Equal(x1, y1) && XMVector64x4Equal(x2, y2) && XMVector64x4Equal(x3, y3) && XMVector64x4Equal(x4, y4)) != 0;
    }

    inline bool operator!=(const Double4x4& M) const
    {
        using namespace DirectX;
        XMVECTOR256D x1 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&m00));
        XMVECTOR256D x2 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&m10));
        XMVECTOR256D x3 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&m20));
        XMVECTOR256D x4 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&m30));

        XMVECTOR256D y1 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M.m00));
        XMVECTOR256D y2 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M.m10));
        XMVECTOR256D y3 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M.m20));
        XMVECTOR256D y4 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M.m30));

        return (XMVector64x4NotEqual(x1, y1) || XMVector64x4NotEqual(x2, y2) || XMVector64x4NotEqual(x3, y3) || XMVector64x4NotEqual(x4, y4)) != 0;
    }

    CEMeta(Editor, ScriptCallable) Double4x4 Add(const Double4x4& M) const
    {
        return *this + M;
    }

    inline Double4x4& operator+=(const Double4x4& M)
    {
        using namespace DirectX;
        XMVECTOR256D x1 = XMLoadDouble4(reinterpret_cast<XMDOUBLE4*>(&m00));
        XMVECTOR256D x2 = XMLoadDouble4(reinterpret_cast<XMDOUBLE4*>(&m10));
        XMVECTOR256D x3 = XMLoadDouble4(reinterpret_cast<XMDOUBLE4*>(&m20));
        XMVECTOR256D x4 = XMLoadDouble4(reinterpret_cast<XMDOUBLE4*>(&m30));

        XMVECTOR256D y1 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M.m00));
        XMVECTOR256D y2 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M.m10));
        XMVECTOR256D y3 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M.m20));
        XMVECTOR256D y4 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M.m30));

        x1 = XMVector256Add(x1, y1);
        x2 = XMVector256Add(x2, y2);
        x3 = XMVector256Add(x3, y3);
        x4 = XMVector256Add(x4, y4);

        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&m00), x1);
        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&m10), x2);
        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&m20), x3);
        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&m30), x4);
        return *this;
    }

    CEMeta(Editor, ScriptCallable) Double4x4 Subtract(const Double4x4& M) const
    {
        return *this - M;
    }

    inline Double4x4& operator-=(const Double4x4& M)
    {
        using namespace DirectX;
        XMVECTOR256D x1 = XMLoadDouble4(reinterpret_cast<XMDOUBLE4*>(&m00));
        XMVECTOR256D x2 = XMLoadDouble4(reinterpret_cast<XMDOUBLE4*>(&m10));
        XMVECTOR256D x3 = XMLoadDouble4(reinterpret_cast<XMDOUBLE4*>(&m20));
        XMVECTOR256D x4 = XMLoadDouble4(reinterpret_cast<XMDOUBLE4*>(&m30));

        XMVECTOR256D y1 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M.m00));
        XMVECTOR256D y2 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M.m10));
        XMVECTOR256D y3 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M.m20));
        XMVECTOR256D y4 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M.m30));

        x1 = XMVector256Subtract(x1, y1);
        x2 = XMVector256Subtract(x2, y2);
        x3 = XMVector256Subtract(x3, y3);
        x4 = XMVector256Subtract(x4, y4);

        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&m00), x1);
        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&m10), x2);
        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&m20), x3);
        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&m30), x4);
        return *this;
    }

    CEMeta(Editor, ScriptCallable) Double4x4 Mult(const Double4x4& M) const
    {
        return *this * M;
    }

    inline Double4x4& operator*=(const Double4x4& M)
    {
        using namespace DirectX;
        XMMATRIX64 M1 = XMLoadDouble4x4(reinterpret_cast<const XMDOUBLE4X4*>(this));
        XMMATRIX64 M2 = XMLoadDouble4x4(reinterpret_cast<const XMDOUBLE4X4*>(&M));
        XMMATRIX64 X = XMMatrix64Multiply(M1, M2);
        XMStoreDouble4x4(reinterpret_cast<XMDOUBLE4X4*>(this), X);
        return *this;
    }

    CEMeta(Editor, ScriptCallable) Double4x4 Mult(double S) const
    {
        return *this * S;
    }

    inline Double4x4& operator*=(double S)
    {
        using namespace DirectX;
        XMVECTOR256D x1 = XMLoadDouble4(reinterpret_cast<XMDOUBLE4*>(&m00));
        XMVECTOR256D x2 = XMLoadDouble4(reinterpret_cast<XMDOUBLE4*>(&m10));
        XMVECTOR256D x3 = XMLoadDouble4(reinterpret_cast<XMDOUBLE4*>(&m20));
        XMVECTOR256D x4 = XMLoadDouble4(reinterpret_cast<XMDOUBLE4*>(&m30));

        x1 = XMVector256Scale(x1, S);
        x2 = XMVector256Scale(x2, S);
        x3 = XMVector256Scale(x3, S);
        x4 = XMVector256Scale(x4, S);

        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&m00), x1);
        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&m10), x2);
        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&m20), x3);
        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&m30), x4);
        return *this;
    }

    CEMeta(Editor, ScriptCallable) Double4x4 Divide(double S) const
    {
        return *this / S;
    }

    inline Double4x4& operator/=(double S)
    {
        using namespace DirectX;
        assert(S != 0.);
        XMVECTOR256D x1 = XMLoadDouble4(reinterpret_cast<XMDOUBLE4*>(&m00));
        XMVECTOR256D x2 = XMLoadDouble4(reinterpret_cast<XMDOUBLE4*>(&m10));
        XMVECTOR256D x3 = XMLoadDouble4(reinterpret_cast<XMDOUBLE4*>(&m20));
        XMVECTOR256D x4 = XMLoadDouble4(reinterpret_cast<XMDOUBLE4*>(&m30));

        double rs = 1. / S;

        x1 = XMVector256Scale(x1, rs);
        x2 = XMVector256Scale(x2, rs);
        x3 = XMVector256Scale(x3, rs);
        x4 = XMVector256Scale(x4, rs);

        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&m00), x1);
        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&m10), x2);
        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&m20), x3);
        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&m30), x4);
        return *this;
    }

    CEMeta(Editor, ScriptCallable) Double4x4 Divide(const Double4x4& M) const
    {
        return *this / M;
    }

    inline Double4x4& operator/=(const Double4x4& M)
    {
        using namespace DirectX;
        XMVECTOR256D x1 = XMLoadDouble4(reinterpret_cast<XMDOUBLE4*>(&m00));
        XMVECTOR256D x2 = XMLoadDouble4(reinterpret_cast<XMDOUBLE4*>(&m10));
        XMVECTOR256D x3 = XMLoadDouble4(reinterpret_cast<XMDOUBLE4*>(&m20));
        XMVECTOR256D x4 = XMLoadDouble4(reinterpret_cast<XMDOUBLE4*>(&m30));

        XMVECTOR256D y1 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M.m00));
        XMVECTOR256D y2 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M.m10));
        XMVECTOR256D y3 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M.m20));
        XMVECTOR256D y4 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M.m30));

        x1 = XMVector256Divide(x1, y1);
        x2 = XMVector256Divide(x2, y2);
        x3 = XMVector256Divide(x3, y3);
        x4 = XMVector256Divide(x4, y4);

        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&m00), x1);
        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&m10), x2);
        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&m20), x3);
        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&m30), x4);
        return *this;
    }

    inline Double4x4 operator-() const
    {
        using namespace DirectX;
        XMVECTOR256D v1 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&m00));
        XMVECTOR256D v2 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&m10));
        XMVECTOR256D v3 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&m20));
        XMVECTOR256D v4 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&m30));

        v1 = XMVector256Negate(v1);
        v2 = XMVector256Negate(v2);
        v3 = XMVector256Negate(v3);
        v4 = XMVector256Negate(v4);

        Double4x4 R;
        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&R.m00), v1);
        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&R.m10), v2);
        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&R.m20), v3);
        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&R.m30), v4);
        return R;
    }

    CEMeta(Editor, ScriptCallable) inline bool Decompose(Double3& scale, Quaternion64& rotation, Double3& translation) const
    {
        using namespace DirectX;
        FXMMATRIX64 M = DirectX::XMLoadDouble4x4(reinterpret_cast<const XMDOUBLE4X4*>(this));

        XMVECTOR256D s, r, t;
        DirectX::XMMatrix64Decompose(&s, &r, &t, M);

        DirectX::XMStoreDouble3(reinterpret_cast<XMDOUBLE3*>(&scale), s);
        DirectX::XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&rotation), r);
        DirectX::XMStoreDouble3(reinterpret_cast<XMDOUBLE3*>(&translation), t);

        return true;
    }
    CEMeta(Editor, ScriptCallable) inline bool Decompose(Double3A& scale, Quaternion64A& rotation, Double3A& translation) const
    {
        using namespace DirectX;
        FXMMATRIX64 M = DirectX::XMLoadDouble4x4(reinterpret_cast<const XMDOUBLE4X4*>(this));

        XMVECTOR256D s, r, t;
        DirectX::XMMatrix64Decompose(&s, &r, &t, M);

        DirectX::XMStoreDouble3(reinterpret_cast<XMDOUBLE3*>(&scale), s);
        DirectX::XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&rotation), r);
        DirectX::XMStoreDouble3(reinterpret_cast<XMDOUBLE3*>(&translation), t);

        return true;
    }
    CEMeta(Editor, ScriptCallable) inline bool Decompose(Double4A& scale, Quaternion64A& rotation, Double4A& translation) const
    {
        using namespace DirectX;
        FXMMATRIX64 M = DirectX::XMLoadDouble4x4(reinterpret_cast<const XMDOUBLE4X4*>(this));

        XMVECTOR256D s, r, t;
        DirectX::XMMatrix64Decompose(&s, &r, &t, M);

        DirectX::XMStoreDouble4A(reinterpret_cast<XMDOUBLE4A*>(&scale), s);
        DirectX::XMStoreDouble4A(reinterpret_cast<XMDOUBLE4A*>(&rotation), r);
        DirectX::XMStoreDouble4A(reinterpret_cast<XMDOUBLE4A*>(&translation), t);

        return true;
    }

    CEMeta(Editor, ScriptCallable) inline Double4x4 Transpose() const
    {
        using namespace DirectX;
        FXMMATRIX64 M = DirectX::XMLoadDouble4x4(reinterpret_cast<const XMDOUBLE4X4*>(this));
        Double4x4 R;
        DirectX::XMStoreDouble4x4(reinterpret_cast<XMDOUBLE4X4*>(&R), DirectX::XMMatrix64Transpose(M));
        return R;
    }

    inline Double4x4& Mirror(Axis::Type mirrorAxis = Axis::Type::None, Axis::Type flipAxis = Axis::Type::None)
    {
        if (mirrorAxis != Axis::Type::None)
        {
            if (mirrorAxis == Axis::Type::X)
            {
                m00 *= -1.0;
                m10 *= -1.0;
                m20 *= -1.0;

                m30 *= -1.0;
            }
            else if (mirrorAxis == Axis::Type::Y)
            {
                m01 *= -1.0;
                m11 *= -1.0;
                m21 *= -1.0;

                m31 *= -1.0;
            }
            else if (mirrorAxis == Axis::Type::Z)
            {
                m02 *= -1.0;
                m12 *= -1.0;
                m22 *= -1.0;

                m32 *= -1.0;
            }
        }

        if (flipAxis != Axis::Type::None)
        {
            if (flipAxis == Axis::Type::X)
            {
                m00 *= -1.0;
                m01 *= -1.0;
                m02 *= -1.0;
            }
            else if (flipAxis == Axis::Type::Y)
            {
                m10 *= -1.0;
                m11 *= -1.0;
                m12 *= -1.0;
            }
            else if (flipAxis == Axis::Type::Z)
            {
                m20 *= -1.0;
                m21 *= -1.0;
                m22 *= -1.0;
            }
        }

        return *this;
    }

    CEMeta(Editor, ScriptCallable) inline Double4x4 Inverted() const
    {
        using namespace DirectX;
        FXMMATRIX64 M = DirectX::XMLoadDouble4x4(reinterpret_cast<const XMDOUBLE4X4*>(this));
        Double4x4 R;
        DirectX::XMStoreDouble4x4(reinterpret_cast<XMDOUBLE4X4*>(&R), DirectX::XMMatrix64Inverse(nullptr, M));
        return R;
    }

    CEMeta(Editor, ScriptCallable) inline void Inverse()
    {
        using namespace DirectX;
        FXMMATRIX64 M = DirectX::XMLoadDouble4x4(reinterpret_cast<const XMDOUBLE4X4*>(this));
        DirectX::XMStoreDouble4x4(reinterpret_cast<XMDOUBLE4X4*>(this), DirectX::XMMatrix64Inverse(nullptr, M));
    }

    CEMeta(Editor, ScriptCallable) inline double Determinant() const
    {
        using namespace DirectX;
        FXMMATRIX64 M = DirectX::XMLoadDouble4x4(reinterpret_cast<const XMDOUBLE4X4*>(this));
        XMDOUBLE4 F;
        DirectX::XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&F), DirectX::XMMatrix64Determinant(M));
        return F.x;
    }

    CEMeta(Editor, ScriptCallable) static inline Double4x4 CalculationRotationMatrix(const Double3& vectorBefore, const Double3& vectorAfter)
    {
        return CreateFromQuaternion(Quaternion64::CreateFrom2Vectors(vectorBefore, vectorAfter));
    }

    CEMeta(Editor, ScriptCallable) static inline Double4x4 Identity()
    {
        return Double4x4(1., 0, 0, 0, 0, 1., 0, 0, 0, 0, 1., 0, 0, 0, 0, 1.);
    }

    CEMeta(Editor, ScriptCallable) static inline Double4x4 Zeros()
    {
        return Double4x4(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
    }

    CEMeta(Editor, ScriptCallable) static inline Double4x4 CreateTranslation(double x, double y, double z)
    {
        Double4x4 result;
        result.m00 = 1.0;
        result.m01 = 0.0;
        result.m02 = 0.0;
        result.m03 = 0.0;

        result.m10 = 0.0;
        result.m11 = 1.0;
        result.m12 = 0.0;
        result.m13 = 0.0;

        result.m20 = 0.0;
        result.m21 = 0.0;
        result.m22 = 1.0;
        result.m23 = 0.0;

        result.m30 = x;
        result.m31 = y;
        result.m32 = z;
        result.m33 = 1.0;
        return result;
    }

    CEMeta(Editor, ScriptCallable) static inline Double4x4 CreateTranslation(const Double3& t)
    {
        return CreateTranslation(t.x, t.y, t.z);
    }

    CEMeta(Editor, ScriptCallable) static inline Double4x4 CreateScale(const Double3& scales)
    {
        Double4x4 result;
        result.m00 = scales.x;
        result.m01 = 0.0;
        result.m02 = 0.0;
        result.m03 = 0.0;

        result.m10 = 0.0;
        result.m11 = scales.y;
        result.m12 = 0.0;
        result.m13 = 0.0;

        result.m20 = 0.0;
        result.m21 = 0.0;
        result.m22 = scales.z;
        result.m23 = 0.0;

        result.m30 = 0.0;
        result.m31 = 0.0;
        result.m32 = 0.0;
        result.m33 = 1.0;
        return result;
    }

    CEMeta(Editor, ScriptCallable) static inline Double4x4 CreateScale(double xs, double ys, double zs)
    {
        return CreateScale(Double3(xs, ys, zs));
    }

    CEMeta(Editor, ScriptCallable) static inline Double4x4 CreateScale(double scale)
    {
        return CreateScale(Double3(scale, scale, scale));
    }

    CEMeta(Editor, ScriptCallable) static inline Double4x4 CreateRotationX(double radians)
    {
        double sin;
        double cos;
        MathUtils::SinCos(radians, sin, cos);

        Double4x4 result;
        result.m00 = 1.0;
        result.m01 = 0.0;
        result.m02 = 0.0;
        result.m03 = 0.0;

        result.m10 = 0.0;
        result.m11 = cos;
        result.m12 = sin;
        result.m13 = 0.0;

        result.m20 = 0.0;
        result.m21 = -sin;
        result.m22 = cos;
        result.m23 = 0.0;

        result.m30 = 0.0;
        result.m31 = 0.0;
        result.m32 = 0.0;
        result.m33 = 1.0;
        return result;
    }

    CEMeta(Editor, ScriptCallable) static inline Double4x4 CreateRotationY(double radians)
    {
        double sin;
        double cos;
        MathUtils::SinCos(radians, sin, cos);

        Double4x4 result;
        result.m00 = cos;
        result.m01 = 0.0f;
        result.m02 = -sin;
        result.m03 = 0.0f;

        result.m10 = 0.0f;
        result.m11 = 1.0f;
        result.m12 = 0.0f;
        result.m13 = 0.0f;

        result.m20 = sin;
        result.m21 = 0.0f;
        result.m22 = cos;
        result.m23 = 0.0f;

        result.m30 = 0.0f;
        result.m31 = 0.0f;
        result.m32 = 0.0f;
        result.m33 = 1.0f;
        return result;
    }

    CEMeta(Editor, ScriptCallable) static inline Double4x4 CreateRotationZ(double radians)
    {
        double sin;
        double cos;
        MathUtils::SinCos(radians, sin, cos);

        Double4x4 result;
        result.m00 = cos;
        result.m01 = sin;
        result.m02 = 0.0;
        result.m03 = 0.0;

        result.m10 = -sin;
        result.m11 = cos;
        result.m12 = 0.0;
        result.m13 = 0.0;

        result.m20 = 0.0;
        result.m21 = 0.0;
        result.m22 = 1.0;
        result.m23 = 0.0;

        result.m30 = 0.0;
        result.m31 = 0.0;
        result.m32 = 0.0;
        result.m33 = 1.0;
        return result;
    }

    CEMeta(Editor, ScriptCallable) static inline Double4x4 CreateFromAxisAngle(const Double3& axis, double angle)
    {
        auto v = DirectX::XMLoadDouble3(reinterpret_cast<const DirectX::XMDOUBLE3*>(&axis));
        auto mat = DirectX::XMMatrix64RotationAxis(v, angle);
        Double4x4 result;
        DirectX::XMStoreDouble4x4(reinterpret_cast<DirectX::XMDOUBLE4X4*>(&result), DirectX::XMMatrix64RotationAxis(v, angle));
        return result;
    }

    CEMeta(Editor, ScriptCallable) static inline Double4x4 CreatePerspectiveFieldOfView(double fov, double aspectRatio, double nearPlane, double farPlane, bool ndcGL = false)
    {
        assert(false);
        return Identity();
    }

    CEMeta(Editor, ScriptCallable) static inline Double4x4 CreatePerspective(double width, double height, double nearPlane, double farPlane, bool ndcGL = false)
    {
        assert(false);
        return Identity();
    }

    CEMeta(Editor, ScriptCallable) static inline Double4x4 CreatePerspectiveOffCenter(double left, double right, double bottom, double top, double nearPlane, double farPlane, bool ndcGL = false)
    {
        assert(false);
        return Identity();
    }

    CEMeta(Editor, ScriptCallable) static inline Double4x4 CreateOrthographic(double width, double height, double zNearPlane, double zFarPlane, bool ndcGL = false)
    {
        assert(false);
        return Identity();
    }

    CEMeta(Editor, ScriptCallable) static inline Double4x4 CreateOrthographicOffCenter(double left, double right, double bottom, double top, double zNearPlane, double zFarPlane, bool ndcGL = false)
    {
        assert(false);
        return Identity();
    }

    // Normalized Up vector
    CEMeta(Editor, ScriptCallable) static inline Double4x4 CreateLookAt(const Double3& eye, const Double3& target, const Double3& up)
    {
        const Double3 dir = (target - eye).Normalized();
        Double3 x;

        if (MathUtils::Abs(up.Dot(dir)) > 1.0 - MathUtils::MathEpsD)
        {
            x = dir.Orthogonal();
        }
        else
        {
            x = (up.Cross(dir)).Normalized();
        }

        const Double3 y = (dir.Cross(x)).Normalized();
        return Double4x4{x.x, y.x, dir.x, 0, x.y, y.y, dir.y, 0, x.z, y.z, dir.z, 0, -x.Dot(eye), -y.Dot(eye), -dir.Dot(eye), 1};
    }

    CEMeta(Editor, ScriptCallable) static inline Double4x4 CreateWorld(const Double3& position, const Double3& forward, const Double3& up)
    {
        using namespace DirectX;
        XMVECTOR256D zaxis = XMVector64x3Normalize(XMLoadDouble3(reinterpret_cast<const XMDOUBLE3*>(&forward)));
        XMVECTOR256D yaxis = XMLoadDouble3(reinterpret_cast<const XMDOUBLE3*>(&up));
        XMVECTOR256D xaxis = XMVector64x3Normalize(XMVector64x3Cross(yaxis, zaxis));
        yaxis = XMVector64x3Cross(zaxis, xaxis);

        Double4x4 R;
        XMStoreDouble3(reinterpret_cast<XMDOUBLE3*>(&R.m00), xaxis);
        XMStoreDouble3(reinterpret_cast<XMDOUBLE3*>(&R.m10), yaxis);
        XMStoreDouble3(reinterpret_cast<XMDOUBLE3*>(&R.m20), zaxis);
        R.m03 = R.m13 = R.m23 = 0.0;
        R.m30 = position.x;
        R.m31 = position.y;
        R.m32 = position.z;
        R.m33 = 1.0;
        return R;
    }

    CEMeta(Editor, ScriptCallable) static inline Double4x4 CreateFromQuaternion(const Quaternion64& rotation)
    {
        using namespace DirectX;
        Double4x4 R;
        DirectX::XMVECTOR256D quatv = DirectX::XMLoadDouble4(reinterpret_cast<const DirectX::XMDOUBLE4*>(&rotation));
        DirectX::XMStoreDouble4x4(reinterpret_cast<XMDOUBLE4X4*>(&R), DirectX::XMMatrix64RotationQuaternion(quatv));
        return R;
    }

    CEMeta(Editor, ScriptCallable) static inline Double4x4 CreateFromYawPitchRoll(double yaw, double pitch, double roll)
    {
        using namespace DirectX;
        Double4x4 R;
        XMStoreDouble4x4(reinterpret_cast<XMDOUBLE4X4*>(&R), DirectX::XMMatrix64RotationRollPitchYaw(pitch, yaw, roll));
        return R;
    }

    CEMeta(Editor, ScriptCallable) static inline void Lerp(const Double4x4& M1, const Double4x4& M2, double t, Double4x4& result)
    {
        using namespace DirectX;
        XMVECTOR256D x1 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M1.m00));
        XMVECTOR256D x2 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M1.m10));
        XMVECTOR256D x3 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M1.m20));
        XMVECTOR256D x4 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M1.m30));

        XMVECTOR256D y1 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M2.m00));
        XMVECTOR256D y2 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M2.m10));
        XMVECTOR256D y3 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M2.m20));
        XMVECTOR256D y4 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M2.m30));

        x1 = XMVector256Lerp(x1, y1, t);
        x2 = XMVector256Lerp(x2, y2, t);
        x3 = XMVector256Lerp(x3, y3, t);
        x4 = XMVector256Lerp(x4, y4, t);

        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&result.m00), x1);
        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&result.m10), x2);
        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&result.m20), x3);
        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&result.m30), x4);
    }

    CEMeta(Editor, ScriptCallable) static inline Double4x4 Lerp(const Double4x4& M1, const Double4x4& M2, double t)
    {
        using namespace DirectX;
        XMVECTOR256D x1 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M1.m00));
        XMVECTOR256D x2 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M1.m10));
        XMVECTOR256D x3 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M1.m20));
        XMVECTOR256D x4 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M1.m30));

        XMVECTOR256D y1 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M2.m00));
        XMVECTOR256D y2 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M2.m10));
        XMVECTOR256D y3 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M2.m20));
        XMVECTOR256D y4 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M2.m30));

        x1 = XMVector256Lerp(x1, y1, t);
        x2 = XMVector256Lerp(x2, y2, t);
        x3 = XMVector256Lerp(x3, y3, t);
        x4 = XMVector256Lerp(x4, y4, t);

        Double4x4 result;
        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&result.m00), x1);
        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&result.m10), x2);
        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&result.m20), x3);
        XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&result.m30), x4);
        return result;
    }

    CEMeta(Editor, ScriptCallable) static inline Double4x4 Transform(const Double4x4& M, const Quaternion64& rotation)
    {
        using namespace DirectX;
        XMVECTOR256D quatv = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&rotation));
        XMMATRIX64 M0 = XMLoadDouble4x4(reinterpret_cast<const XMDOUBLE4X4*>(&M));
        XMMATRIX64 M1 = XMMatrix64RotationQuaternion(quatv);
        Double4x4 result;
        XMStoreDouble4x4(reinterpret_cast<XMDOUBLE4X4*>(&result), XMMatrix64Multiply(M0, M1));
        return result;
    }

    // Point * Matrix
    CEMeta(Editor, ScriptCallable) static inline Double3 TransformPointF3(const Double4x4& m, const Double3& v)
    {
        double X = (m.m00 * v.x) + (m.m10 * v.y) + (m.m20 * v.z) + (m.m30 * 1.0);
        double Y = (m.m01 * v.x) + (m.m11 * v.y) + (m.m21 * v.z) + (m.m31 * 1.0);
        double Z = (m.m02 * v.x) + (m.m12 * v.y) + (m.m22 * v.z) + (m.m32 * 1.0);
        return Double3{X, Y, Z};
    }

    // Vector * Matrix
    CEMeta(Editor, ScriptCallable) static inline Double3 TransformVectorF3(const Double4x4& m, const Double3& v)
    {
        double X = (m.m00 * v.x) + (m.m10 * v.y) + (m.m20 * v.z);
        double Y = (m.m01 * v.x) + (m.m11 * v.y) + (m.m21 * v.z);
        double Z = (m.m02 * v.x) + (m.m12 * v.y) + (m.m22 * v.z);
        return Double3{X, Y, Z};
    }

    // V * M
    CEMeta(Editor, ScriptCallable) static inline Double4 Transform(const Double4x4& m, const Double4& v)
    {
        double X = (m.m00 * v.x) + (m.m10 * v.y) + (m.m20 * v.z) + (m.m30 * v.w);
        double Y = (m.m01 * v.x) + (m.m11 * v.y) + (m.m21 * v.z) + (m.m31 * v.w);
        double Z = (m.m02 * v.x) + (m.m12 * v.y) + (m.m22 * v.z) + (m.m32 * v.w);
        double W = (m.m03 * v.x) + (m.m13 * v.y) + (m.m23 * v.z) + (m.m33 * v.w);
        return Double4{X, Y, Z, W};
    }

    CEMeta(Editor, ScriptCallable) static inline Double4x4 Compose(const Double3& scale, const Quaternion64& rotation, const Double3& translation)
    {
        Double4x4 result = CreateScale(scale);
        result *= CreateFromQuaternion(rotation);
        result *= CreateTranslation(translation);
        return result;
    }

    CEMeta(Editor, ScriptCallable)
        /** Asssume Double4 & Float3 memory strictly follow the layout x, y, z **/
        static inline Double4x4 Compose(const Double4& scale, const Quaternion64& rotation, const Double4& translation)
    {
        if constexpr ((sizeof(Double4) - sizeof(Double3)) != sizeof(double))
            return Double4x4::Identity();

        return Compose(*reinterpret_cast<const Double3*>(&scale), rotation, *reinterpret_cast<const Double3*>(&translation));
    }

    CEMeta(Editor, ScriptCallable)
    inline Double3 GetForwardVector() const
    {
        return TransformVectorF3(*this, Double3(0.0, 0.0, 1.0));
    }

    CEMeta(Editor, ScriptCallable)
    inline Double3 GetUpVector() const
    {
        return TransformVectorF3(*this, Double3(0.0, 1.0, 0.0));
    }

    CEMeta(Editor, ScriptCallable)
    inline Double3 GetRightVector() const
    {
        return TransformVectorF3(*this, Double3(1.0, 0.0, 0.0));
    }
};

// 4x4 Matrix: 32 bit floating point components aligned on a 16 byte boundary
XM_ALIGNED_STRUCT(32) Double4x4A : public Double4x4
{
    CEMeta(Editor, ScriptCallable) Double4x4A() = default;

    CEMeta(Editor, ScriptCallable) Double4x4A(const Double4x4A&) = default;
    Double4x4A& operator=(const Double4x4A&) = default;

    Double4x4A(Double4x4A &&) = default;
    Double4x4A& operator=(Double4x4A&&) = default;

    CEMeta(Editor, ScriptCallable) Double4x4A(const Double4x4& mat)
        : Double4x4(mat)
    {}

    CEMeta(Editor, ScriptCallable) constexpr Double4x4A(double m00, double m01, double m02, double m03, double m10, double m11, double m12, double m13, double m20, double m21, double m22, double m23, double m30, double m31, double m32, double m33) noexcept
        : Double4x4(m00, m01, m02, m03, m10, m11, m12, m13, m20, m21, m22, m23, m30, m31, m32, m33)
    {}

    CEMeta(Editor, ScriptCallable) explicit Double4x4A(_In_reads_(16) const double* pArray) noexcept
        : Double4x4(pArray)
    {}

    CEMeta(Editor, ScriptCallable) explicit Double4x4A(const Float4x4& in)
        : Double4x4(in)
    {}
    CEMeta(Editor, ScriptCallable) explicit Double4x4A(const Float4x4A& in)
        : Double4x4(in)
    {}
    CEMeta(Editor, ScriptCallable) bool Equal(const Double4x4A& other) const
    {
        return *this == other;
    }
};

inline Double4x4 operator+(const Double4x4& M1, const Double4x4& M2)
{
    using namespace DirectX;
    XMVECTOR256D x1 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M1.m00));
    XMVECTOR256D x2 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M1.m10));
    XMVECTOR256D x3 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M1.m20));
    XMVECTOR256D x4 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M1.m30));

    XMVECTOR256D y1 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M2.m00));
    XMVECTOR256D y2 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M2.m10));
    XMVECTOR256D y3 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M2.m20));
    XMVECTOR256D y4 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M2.m30));

    x1 = XMVector256Add(x1, y1);
    x2 = XMVector256Add(x2, y2);
    x3 = XMVector256Add(x3, y3);
    x4 = XMVector256Add(x4, y4);

    Double4x4 R;
    XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&R.m00), x1);
    XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&R.m10), x2);
    XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&R.m20), x3);
    XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&R.m30), x4);
    return R;
}

inline Double4x4 operator-(const Double4x4& M1, const Double4x4& M2)
{
    DirectX::XMVECTOR256D x1 = DirectX::XMLoadDouble4(reinterpret_cast<const DirectX::XMDOUBLE4*>(&M1.m00));
    DirectX::XMVECTOR256D x2 = DirectX::XMLoadDouble4(reinterpret_cast<const DirectX::XMDOUBLE4*>(&M1.m10));
    DirectX::XMVECTOR256D x3 = DirectX::XMLoadDouble4(reinterpret_cast<const DirectX::XMDOUBLE4*>(&M1.m20));
    DirectX::XMVECTOR256D x4 = DirectX::XMLoadDouble4(reinterpret_cast<const DirectX::XMDOUBLE4*>(&M1.m30));

    DirectX::XMVECTOR256D y1 = DirectX::XMLoadDouble4(reinterpret_cast<const DirectX::XMDOUBLE4*>(&M2.m00));
    DirectX::XMVECTOR256D y2 = DirectX::XMLoadDouble4(reinterpret_cast<const DirectX::XMDOUBLE4*>(&M2.m10));
    DirectX::XMVECTOR256D y3 = DirectX::XMLoadDouble4(reinterpret_cast<const DirectX::XMDOUBLE4*>(&M2.m20));
    DirectX::XMVECTOR256D y4 = DirectX::XMLoadDouble4(reinterpret_cast<const DirectX::XMDOUBLE4*>(&M2.m30));

    x1 = DirectX::XMVector256Subtract(x1, y1);
    x2 = DirectX::XMVector256Subtract(x2, y2);
    x3 = DirectX::XMVector256Subtract(x3, y3);
    x4 = DirectX::XMVector256Subtract(x4, y4);

    Double4x4 R;
    DirectX::XMStoreDouble4(reinterpret_cast<DirectX::XMDOUBLE4*>(&R.m00), x1);
    DirectX::XMStoreDouble4(reinterpret_cast<DirectX::XMDOUBLE4*>(&R.m10), x2);
    DirectX::XMStoreDouble4(reinterpret_cast<DirectX::XMDOUBLE4*>(&R.m20), x3);
    DirectX::XMStoreDouble4(reinterpret_cast<DirectX::XMDOUBLE4*>(&R.m30), x4);
    return R;
}

inline Double4x4 operator*(const Double4x4& M1, const Double4x4& M2)
{
    DirectX::XMMATRIX64 m1 = DirectX::XMLoadDouble4x4(reinterpret_cast<const DirectX::XMDOUBLE4X4*>(&M1));
    DirectX::XMMATRIX64 m2 = DirectX::XMLoadDouble4x4(reinterpret_cast<const DirectX::XMDOUBLE4X4*>(&M2));
    DirectX::XMMATRIX64 X = DirectX::XMMatrix64Multiply(m1, m2);

    Double4x4 R;
    DirectX::XMStoreDouble4x4(reinterpret_cast<DirectX::XMDOUBLE4X4*>(&R), X);
    return R;
}

inline Double4x4 operator*(const Double4x4& M, double S)
{
    using namespace DirectX;
    XMVECTOR256D x1 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M.m00));
    XMVECTOR256D x2 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M.m10));
    XMVECTOR256D x3 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M.m20));
    XMVECTOR256D x4 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M.m30));

    x1 = XMVector256Scale(x1, S);
    x2 = XMVector256Scale(x2, S);
    x3 = XMVector256Scale(x3, S);
    x4 = XMVector256Scale(x4, S);

    Double4x4 R;
    XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&R.m00), x1);
    XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&R.m10), x2);
    XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&R.m20), x3);
    XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&R.m30), x4);
    return R;
}

inline Double4x4 operator/(const Double4x4& M, double S)
{
    using namespace DirectX;
    assert(S != 0.);

    XMVECTOR256D x1 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M.m00));
    XMVECTOR256D x2 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M.m10));
    XMVECTOR256D x3 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M.m20));
    XMVECTOR256D x4 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M.m30));

    double rs = 1. / S;

    x1 = XMVector256Scale(x1, rs);
    x2 = XMVector256Scale(x2, rs);
    x3 = XMVector256Scale(x3, rs);
    x4 = XMVector256Scale(x4, rs);

    Double4x4 R;
    XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&R.m00), x1);
    XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&R.m10), x2);
    XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&R.m20), x3);
    XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&R.m30), x4);
    return R;
}

inline Double4x4 operator/(const Double4x4& M1, const Double4x4& M2)
{
    using namespace DirectX;
    XMVECTOR256D x1 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M1.m00));
    XMVECTOR256D x2 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M1.m10));
    XMVECTOR256D x3 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M1.m20));
    XMVECTOR256D x4 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M1.m30));

    XMVECTOR256D y1 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M2.m00));
    XMVECTOR256D y2 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M2.m10));
    XMVECTOR256D y3 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M2.m20));
    XMVECTOR256D y4 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M2.m30));

    x1 = XMVector256Divide(x1, y1);
    x2 = XMVector256Divide(x2, y2);
    x3 = XMVector256Divide(x3, y3);
    x4 = XMVector256Divide(x4, y4);

    Double4x4 R;
    XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&R.m00), x1);
    XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&R.m10), x2);
    XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&R.m20), x3);
    XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&R.m30), x4);
    return R;
}

inline Double4x4 operator*(double S, const Double4x4& M)
{
    using namespace DirectX;

    XMVECTOR256D x1 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M.m00));
    XMVECTOR256D x2 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M.m10));
    XMVECTOR256D x3 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M.m20));
    XMVECTOR256D x4 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&M.m30));

    x1 = XMVector256Scale(x1, S);
    x2 = XMVector256Scale(x2, S);
    x3 = XMVector256Scale(x3, S);
    x4 = XMVector256Scale(x4, S);

    Double4x4 R;
    XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&R.m00), x1);
    XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&R.m10), x2);
    XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&R.m20), x3);
    XMStoreDouble4(reinterpret_cast<XMDOUBLE4*>(&R.m30), x4);
    return R;
}

inline Double4 operator*(const Double4x4& M, const Double4& V);
inline Double4 operator*(const Double4& V, const Double4x4& M);

inline Double4x4 OuterProduct(const Double4& U, const Double4& V)
{
    Double4x4 result;
    for (int i = 0; i < 4; i++)
    {
        for (int j = 0; j < 4; j++)
        {
            result[i * 4 + j] = U[i] * V[j];
        }
    }

    return result;
}
inline Double4 operator*(const Double4x4& M, const Double4& V);
inline Double4 operator*(const Double4& v, const Double4x4& m);
#pragma warning(pop)
//****************************************************************************
// Load operations
//****************************************************************************
class SIMD
{
public:
    static inline SIMDVector4 MATH_CALL LoadFloat(const float* pSource) noexcept
    {
        return DirectX::XMLoadFloat(pSource);
    }
    static inline SIMDVector4 MATH_CALL LoadFloat2(const Float2* pSource) noexcept
    {
        return DirectX::XMLoadFloat2(reinterpret_cast<const DirectX::XMFLOAT2*>(pSource));
    }
    static inline SIMDVector4 MATH_CALL LoadFloat2A(const Float2A* pSource) noexcept
    {
        return DirectX::XMLoadFloat2A(reinterpret_cast<const DirectX::XMFLOAT2A*>(pSource));
    }
    static inline SIMDVector4 MATH_CALL LoadFloat3(const Float3* pSource) noexcept
    {
        return DirectX::XMLoadFloat3(reinterpret_cast<const DirectX::XMFLOAT3*>(pSource));
    }
    static inline SIMDVector4 MATH_CALL LoadFloat3A(const Float3A* pSource) noexcept
    {
        return DirectX::XMLoadFloat3A(reinterpret_cast<const DirectX::XMFLOAT3A*>(pSource));
    }
    static inline SIMDVector4 MATH_CALL LoadFloat3A_W1(const Float3A* pSource) noexcept
    {
        return DirectX::XMVectorSetW(LoadFloat3A(pSource), 1.0f);
    }
    static inline SIMDVector4 MATH_CALL LoadFloat3A_W0(const Float3A* pSource) noexcept
    {
        return DirectX::XMVectorSetW(LoadFloat3A(pSource), 0.0f);
    }
    static inline SIMDVector4 MATH_CALL LoadFloat4(const Float4* pSource) noexcept
    {
        return DirectX::XMLoadFloat4(reinterpret_cast<const DirectX::XMFLOAT4*>(pSource));
    }
    static inline SIMDVector4 MATH_CALL LoadFloat4A(const Float4A* pSource) noexcept
    {
        return DirectX::XMLoadFloat4A(reinterpret_cast<const DirectX::XMFLOAT4A*>(pSource));
    }
    static inline SIMDVector4 MATH_CALL LoadFloat4A(const float* pSource) noexcept
    {
        return DirectX::XMLoadFloat4A(reinterpret_cast<const DirectX::XMFLOAT4A*>(pSource));
    }
    static inline SIMDVector4 MATH_CALL LoadQuaternion(const Quaternion* pSource) noexcept
    {
        return DirectX::XMLoadFloat4(reinterpret_cast<const DirectX::XMFLOAT4*>(pSource));
    }
    static inline SIMDVector4 MATH_CALL LoadQuaternionA(const QuaternionA* pSource) noexcept
    {
        return DirectX::XMLoadFloat4A(reinterpret_cast<const DirectX::XMFLOAT4A*>(pSource));
    }
    static inline SIMDMatrix MATH_CALL LoadFloat4x4(const Float4x4* pSource) noexcept
    {
        return DirectX::XMLoadFloat4x4(reinterpret_cast<const DirectX::XMFLOAT4X4*>(pSource));
    }
    static inline SIMDMatrix MATH_CALL LoadFloat4x4A(const Float4x4A* pSource) noexcept
    {
        return DirectX::XMLoadFloat4x4A(reinterpret_cast<const DirectX::XMFLOAT4X4A*>(pSource));
    }

    //****************************************************************************
    // Store operations
    //****************************************************************************/
    static void MATH_CALL StoreFloat(float* pDestination, SIMDVec4Param1 v) noexcept
    {
        DirectX::XMStoreFloat(pDestination, v);
    }
    static void MATH_CALL StoreFloat2(Float2* pDestination, SIMDVec4Param1 v) noexcept
    {
        DirectX::XMStoreFloat2(reinterpret_cast<DirectX::XMFLOAT2*>(pDestination), v);
    }
    static void MATH_CALL StoreFloat2A(Float2A* pDestination, SIMDVec4Param1 v) noexcept
    {
        DirectX::XMStoreFloat2A(reinterpret_cast<DirectX::XMFLOAT2A*>(pDestination), v);
    }
    static void MATH_CALL StoreFloat3(Float3* pDestination, SIMDVec4Param1 v) noexcept
    {
        DirectX::XMStoreFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(pDestination), v);
    }
    static void MATH_CALL StoreFloat3A(Float3A* pDestination, SIMDVec4Param1 v) noexcept
    {
        DirectX::XMStoreFloat3A(reinterpret_cast<DirectX::XMFLOAT3A*>(pDestination), v);
    }
    static void MATH_CALL StoreFloat4(Float4* pDestination, SIMDVec4Param1 v) noexcept
    {
        DirectX::XMStoreFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(pDestination), v);
    }
    static void MATH_CALL StoreFloat4A(Float4A* pDestination, SIMDVec4Param1 v) noexcept
    {
        DirectX::XMStoreFloat4A(reinterpret_cast<DirectX::XMFLOAT4A*>(pDestination), v);
    }
    static void MATH_CALL StoreFloat4(Quaternion* pDestination, SIMDVec4Param1 v) noexcept
    {
        DirectX::XMStoreFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(pDestination), v);
    }
    static void MATH_CALL StoreFloat4A(QuaternionA* pDestination, SIMDVec4Param1 v) noexcept
    {
        DirectX::XMStoreFloat4A(reinterpret_cast<DirectX::XMFLOAT4A*>(pDestination), v);
    }
    static void MATH_CALL StoreFloat4x4(Float4x4* pDestination, SIMDMatrix M) noexcept
    {
        DirectX::XMStoreFloat4x4(reinterpret_cast<DirectX::XMFLOAT4X4*>(pDestination), M);
    }
    static void MATH_CALL StoreFloat4x4A(Float4x4A* pDestination, SIMDMatrix M) noexcept
    {
        DirectX::XMStoreFloat4x4A(reinterpret_cast<DirectX::XMFLOAT4X4A*>(pDestination), M);
    }
};

//****************************************************************************
// * General vector operations
//****************************************************************************
#ifndef _MANAGED
static inline SIMDVector4 const sVectorEpsilon = DirectX::g_XMEpsilon;
static inline SIMDVector4 const sVectorZero = DirectX::g_XMZero;
class MathSIMD
{
public:
    static inline SIMDVector4 MATH_CALL VectorZero()
    {
        return DirectX::XMVectorZero();
    }
    static inline SIMDVector4 MATH_CALL VectorOne()
    {
        return DirectX::g_XMOne;
    }
    static inline SIMDVector4 MATH_CALL VectorOne3()
    {
        return DirectX::g_XMOne3;
    }
    static inline SIMDVector4 MATH_CALL Vector0001()
    {
        return DirectX::g_XMIdentityR3;
    }
    static inline SIMDVector4 MATH_CALL VectorSet(float x, float y, float z, float w)
    {
        return DirectX::XMVectorSet(x, y, z, w);
    }
    static inline SIMDVector4 MATH_CALL VectorSetInt(uint32_t x, uint32_t y, uint32_t z, uint32_t w)
    {
        return DirectX::XMVectorSetInt(x, y, z, w);
    }
    static inline SIMDVector4 MATH_CALL VectorReplicate(float Value)
    {
        return DirectX::XMVectorReplicate(Value);
    }
    static inline SIMDVector4 MATH_CALL VectorReplicatePtr(const float* pValue)
    {
        return DirectX::XMVectorReplicatePtr(pValue);
    }
    static inline SIMDVector4 MATH_CALL VectorReplicateInt(uint32_t Value)
    {
        return DirectX::XMVectorReplicateInt(Value);
    }
    static inline SIMDVector4 MATH_CALL VectorReplicateIntPtr(const uint32_t* pValue)
    {
        return DirectX::XMVectorReplicateIntPtr(pValue);
    }
    static inline SIMDVector4 MATH_CALL VectorTrueInt()
    {
        return DirectX::XMVectorTrueInt();
    }
    static inline SIMDVector4 MATH_CALL VectorFalseInt()
    {
        return DirectX::XMVectorFalseInt();
    }
    static inline SIMDVector4 MATH_CALL VectorSplatX(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorSplatX(v);
    }
    static inline SIMDVector4 MATH_CALL VectorSplatY(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorSplatY(v);
    }
    static inline SIMDVector4 MATH_CALL VectorSplatZ(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorSplatZ(v);
    }
    static inline SIMDVector4 MATH_CALL VectorSplatW(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorSplatW(v);
    }
    static inline SIMDVector4 MATH_CALL VectorSplatOne()
    {
        return DirectX::XMVectorSplatOne();
    }
    static inline SIMDVector4 MATH_CALL VectorSplatInfinity()
    {
        return DirectX::XMVectorSplatInfinity();
    }
    static inline SIMDVector4 MATH_CALL VectorSplatQNaN()
    {
        return DirectX::XMVectorSplatQNaN();
    }
    static inline SIMDVector4 MATH_CALL VectorSplatEpsilon()
    {
        return DirectX::XMVectorSplatEpsilon();
    }
    static inline SIMDVector4 MATH_CALL VectorSplatSignMask()
    {
        return DirectX::XMVectorSplatSignMask();
    }
    static inline float MATH_CALL VectorGetByIndex(SIMDVec4Param1 v, size_t i)
    {
        return DirectX::XMVectorGetByIndex(v, i);
    }
    static inline float MATH_CALL VectorGetX(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorGetX(v);
    }
    static inline float MATH_CALL VectorGetY(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorGetY(v);
    }
    static inline float MATH_CALL VectorGetZ(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorGetZ(v);
    }
    static inline float MATH_CALL VectorGetW(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorGetW(v);
    }
    static inline void MATH_CALL VectorGetByIndexPtr(float* f, SIMDVec4Param1 v, size_t i)
    {
        DirectX::XMVectorGetByIndexPtr(f, v, i);
    }
    static inline void MATH_CALL VectorGetXPtr(float* x, SIMDVec4Param1 v)
    {
        DirectX::XMVectorGetXPtr(x, v);
    }
    static inline void MATH_CALL VectorGetYPtr(float* y, SIMDVec4Param1 v)
    {
        DirectX::XMVectorGetYPtr(y, v);
    }
    static inline void MATH_CALL VectorGetZPtr(float* z, SIMDVec4Param1 v)
    {
        DirectX::XMVectorGetZPtr(z, v);
    }
    static inline void MATH_CALL VectorGetWPtr(float* w, SIMDVec4Param1 v)
    {
        DirectX::XMVectorGetWPtr(w, v);
    }
    static inline uint32_t MATH_CALL VectorGetIntByIndex(SIMDVec4Param1 v, size_t i)
    {
        return DirectX::XMVectorGetIntByIndex(v, i);
    }
    static inline uint32_t MATH_CALL VectorGetIntX(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorGetIntX(v);
    }
    static inline uint32_t MATH_CALL VectorGetIntY(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorGetIntY(v);
    }
    static inline uint32_t MATH_CALL VectorGetIntZ(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorGetIntZ(v);
    }
    static inline uint32_t MATH_CALL VectorGetIntW(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorGetIntW(v);
    }
    static inline void MATH_CALL VectorGetIntByIndexPtr(uint32_t* x, SIMDVec4Param1 v, size_t i)
    {
        return DirectX::XMVectorGetIntByIndexPtr(x, v, i);
    }
    static inline void MATH_CALL VectorGetIntXPtr(uint32_t* x, SIMDVec4Param1 v)
    {
        DirectX::XMVectorGetIntXPtr(x, v);
    }
    static inline void MATH_CALL VectorGetIntYPtr(uint32_t* y, SIMDVec4Param1 v)
    {
        DirectX::XMVectorGetIntYPtr(y, v);
    }
    static inline void MATH_CALL VectorGetIntZPtr(uint32_t* z, SIMDVec4Param1 v)
    {
        DirectX::XMVectorGetIntZPtr(z, v);
    }
    static inline void MATH_CALL VectorGetIntWPtr(uint32_t* w, SIMDVec4Param1 v)
    {
        DirectX::XMVectorGetIntWPtr(w, v);
    }
    static inline SIMDVector4 MATH_CALL VectorSetByIndex(SIMDVec4Param1 v, float f, size_t i)
    {
        return DirectX::XMVectorSetByIndex(v, f, i);
    }
    static inline SIMDVector4 MATH_CALL VectorSetX(SIMDVec4Param1 v, float x)
    {
        return DirectX::XMVectorSetX(v, x);
    }
    static inline SIMDVector4 MATH_CALL VectorSetY(SIMDVec4Param1 v, float y)
    {
        return DirectX::XMVectorSetY(v, y);
    }
    static inline SIMDVector4 MATH_CALL VectorSetZ(SIMDVec4Param1 v, float z)
    {
        return DirectX::XMVectorSetZ(v, z);
    }
    static inline SIMDVector4 MATH_CALL VectorSetW(SIMDVec4Param1 v, float w)
    {
        return DirectX::XMVectorSetW(v, w);
    }
    static inline SIMDVector4 MATH_CALL VectorSetByIndexPtr(SIMDVec4Param1 v, const float* f, size_t i)
    {
        return DirectX::XMVectorSetByIndexPtr(v, f, i);
    }
    static inline SIMDVector4 MATH_CALL VectorSetXPtr(SIMDVec4Param1 v, const float* x)
    {
        return DirectX::XMVectorSetXPtr(v, x);
    }
    static inline SIMDVector4 MATH_CALL VectorSetYPtr(SIMDVec4Param1 v, const float* y)
    {
        return DirectX::XMVectorSetYPtr(v, y);
    }
    static inline SIMDVector4 MATH_CALL VectorSetZPtr(SIMDVec4Param1 v, const float* z)
    {
        return DirectX::XMVectorSetZPtr(v, z);
    }
    static inline SIMDVector4 MATH_CALL VectorSetWPtr(SIMDVec4Param1 v, const float* w)
    {
        return DirectX::XMVectorSetWPtr(v, w);
    }
    static inline SIMDVector4 MATH_CALL VectorSetIntByIndex(SIMDVec4Param1 v, uint32_t x, size_t i)
    {
        return DirectX::XMVectorSetIntByIndex(v, x, i);
    }
    static inline SIMDVector4 MATH_CALL VectorSetIntX(SIMDVec4Param1 v, uint32_t x)
    {
        return DirectX::XMVectorSetIntX(v, x);
    }
    static inline SIMDVector4 MATH_CALL VectorSetIntY(SIMDVec4Param1 v, uint32_t y)
    {
        return DirectX::XMVectorSetIntY(v, y);
    }
    static inline SIMDVector4 MATH_CALL VectorSetIntZ(SIMDVec4Param1 v, uint32_t z)
    {
        return DirectX::XMVectorSetIntZ(v, z);
    }
    static inline SIMDVector4 MATH_CALL VectorSetIntW(SIMDVec4Param1 v, uint32_t w)
    {
        return DirectX::XMVectorSetIntW(v, w);
    }
    static inline SIMDVector4 MATH_CALL VectorSetIntByIndexPtr(SIMDVec4Param1 v, const uint32_t* x, size_t i)
    {
        return DirectX::XMVectorSetIntByIndexPtr(v, x, i);
    }
    static inline SIMDVector4 MATH_CALL VectorSetIntXPtr(SIMDVec4Param1 v, const uint32_t* x)
    {
        return DirectX::XMVectorSetIntXPtr(v, x);
    }
    static inline SIMDVector4 MATH_CALL VectorSetIntYPtr(SIMDVec4Param1 v, const uint32_t* y)
    {
        return DirectX::XMVectorSetIntYPtr(v, y);
    }
    static inline SIMDVector4 MATH_CALL VectorSetIntZPtr(SIMDVec4Param1 v, const uint32_t* z)
    {
        return DirectX::XMVectorSetIntZPtr(v, z);
    }
    static inline SIMDVector4 MATH_CALL VectorSetIntWPtr(SIMDVec4Param1 v, const uint32_t* w)
    {
        return DirectX::XMVectorSetIntWPtr(v, w);
    }
    static inline SIMDVector4 MATH_CALL VectorSwizzle(SIMDVec4Param1 v, uint32_t E0, uint32_t E1, uint32_t E2, uint32_t E3)
    {
        return DirectX::XMVectorSwizzle(v, E0, E1, E2, E3);
    }
    static inline SIMDVector4 MATH_CALL VectorPermute(SIMDVec4Param1 v1, SIMDVec4Param1 v2, uint32_t PermuteX, uint32_t PermuteY, uint32_t PermuteZ, uint32_t PermuteW)
    {
        return DirectX::XMVectorPermute(v1, v2, PermuteX, PermuteY, PermuteZ, PermuteW);
    }
    static inline SIMDVector4 MATH_CALL VectorSelectControl(uint32_t VectorIndex0, uint32_t VectorIndex1, uint32_t VectorIndex2, uint32_t VectorIndex3)
    {
        return DirectX::XMVectorSelectControl(VectorIndex0, VectorIndex1, VectorIndex2, VectorIndex3);
    }
    static inline SIMDVector4 MATH_CALL VectorSelect(SIMDVec4Param1 v1, SIMDVec4Param1 v2, SIMDVec4Param1 Control)
    {
        return DirectX::XMVectorSelect(v1, v2, Control);
    }
    static inline SIMDVector4 MATH_CALL VectorMergeXY(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVectorMergeXY(v1, v2);
    }
    static inline SIMDVector4 MATH_CALL VectorMergeZW(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVectorMergeZW(v1, v2);
    }
    static inline SIMDVector4 MATH_CALL VectorShiftLeft(SIMDVec4Param1 v1, SIMDVec4Param1 v2, uint32_t Elements)
    {
        return DirectX::XMVectorShiftLeft(v1, v2, Elements);
    }
    static inline SIMDVector4 MATH_CALL VectorRotateLeft(SIMDVec4Param1 v, uint32_t Elements)
    {
        return DirectX::XMVectorRotateLeft(v, Elements);
    }
    static inline SIMDVector4 MATH_CALL VectorRotateRight(SIMDVec4Param1 v, uint32_t Elements)
    {
        return DirectX::XMVectorRotateRight(v, Elements);
    }
    static inline SIMDVector4 MATH_CALL VectorInsert(SIMDVec4Param1 VD, SIMDVec4Param1 VS, uint32_t VSLeftRotateElements, uint32_t Select0, uint32_t Select1, uint32_t Select2, uint32_t Select3)
    {
        return DirectX::XMVectorInsert(VD, VS, VSLeftRotateElements, Select0, Select1, Select2, Select3);
    }
    static inline SIMDVector4 MATH_CALL VectorEqual(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVectorEqual(v1, v2);
    }
    static inline SIMDVector4 MATH_CALL VectorEqualR(uint32_t* pCR, SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVectorEqualR(pCR, v1, v2);
    }
    static inline SIMDVector4 MATH_CALL VectorEqualInt(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVectorEqualInt(v1, v2);
    }
    static inline SIMDVector4 MATH_CALL VectorEqualIntR(uint32_t* pCR, SIMDVec4Param1 v, SIMDVec4Param1 v2)
    {
        return DirectX::XMVectorEqualIntR(pCR, v, v2);
    }
    static inline SIMDVector4 MATH_CALL VectorNearEqual(SIMDVec4Param1 v1, SIMDVec4Param1 v2, SIMDVec4Param1 Epsilon)
    {
        return DirectX::XMVectorNearEqual(v1, v2, Epsilon);
    }
    static inline SIMDVector4 MATH_CALL VectorNotEqual(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVectorNotEqual(v1, v2);
    }
    static inline SIMDVector4 MATH_CALL VectorNotEqualInt(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVectorNotEqualInt(v1, v2);
    }
    static inline SIMDVector4 MATH_CALL VectorGreater(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVectorGreater(v1, v2);
    }
    static inline SIMDVector4 MATH_CALL VectorGreaterR(uint32_t* pCR, SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVectorGreaterR(pCR, v1, v2);
    }
    static inline SIMDVector4 MATH_CALL VectorGreaterOrEqual(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVectorGreaterOrEqual(v1, v2);
    }
    static inline SIMDVector4 MATH_CALL VectorGreaterOrEqualR(uint32_t* pCR, SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVectorGreaterOrEqualR(pCR, v1, v2);
    }
    static inline SIMDVector4 MATH_CALL VectorLess(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVectorLess(v1, v2);
    }
    static inline SIMDVector4 MATH_CALL VectorLessOrEqual(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVectorLessOrEqual(v1, v2);
    }
    static inline SIMDVector4 MATH_CALL VectorInBounds(SIMDVec4Param1 v, SIMDVec4Param1 Bounds)
    {
        return DirectX::XMVectorInBounds(v, Bounds);
    }
    static inline SIMDVector4 MATH_CALL VectorInBoundsR(uint32_t* pCR, SIMDVec4Param1 v, SIMDVec4Param1 Bounds)
    {
        return DirectX::XMVectorInBoundsR(pCR, v, Bounds);
    }
    static inline SIMDVector4 MATH_CALL VectorIsNaN(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorIsNaN(v);
    }
    static inline SIMDVector4 MATH_CALL VectorIsInfinite(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorIsInfinite(v);
    }
    static inline SIMDVector4 MATH_CALL VectorMin(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVectorMin(v1, v2);
    }
    static inline SIMDVector4 MATH_CALL VectorMax(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVectorMax(v1, v2);
    }
    static inline SIMDVector4 MATH_CALL VectorRound(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorRound(v);
    }
    static inline SIMDVector4 MATH_CALL VectorTruncate(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorTruncate(v);
    }
    static inline SIMDVector4 MATH_CALL VectorFloor(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorFloor(v);
    }
    static inline SIMDVector4 MATH_CALL VectorCeiling(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorCeiling(v);
    }
    static inline SIMDVector4 MATH_CALL VectorClamp(SIMDVec4Param1 v, SIMDVec4Param1 Min, SIMDVec4Param1 Max)
    {
        return DirectX::XMVectorClamp(v, Min, Max);
    }
    static inline SIMDVector4 MATH_CALL VectorSaturate(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorSaturate(v);
    }
    static inline SIMDVector4 MATH_CALL VectorSetW0(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorAndInt(v, DirectX::g_XMMask3);
    }
    static inline SIMDVector4 MATH_CALL VectorAndInt(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVectorAndInt(v1, v2);
    }
    static inline SIMDVector4 MATH_CALL VectorAndCInt(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVectorAndCInt(v1, v2);
    }
    static inline SIMDVector4 MATH_CALL VectorOrInt(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVectorOrInt(v1, v2);
    }
    static inline SIMDVector4 MATH_CALL VectorNorInt(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVectorNorInt(v1, v2);
    }
    static inline SIMDVector4 MATH_CALL VectorXorInt(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVectorXorInt(v1, v2);
    }
    static inline SIMDVector4 MATH_CALL VectorNegate(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorNegate(v);
    }
    static inline SIMDVector4 MATH_CALL VectorAdd(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVectorAdd(v1, v2);
    }
    static inline SIMDVector4 MATH_CALL VectorSum(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorSum(v);
    }
    static inline SIMDVector4 MATH_CALL VectorAddAngles(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVectorAddAngles(v1, v2);
    }
    static inline SIMDVector4 MATH_CALL VectorSubtract(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVectorSubtract(v1, v2);
    }
    static inline SIMDVector4 MATH_CALL VectorSubtractAngles(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVectorSubtractAngles(v1, v2);
    }
    static inline SIMDVector4 MATH_CALL VectorMultiply(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVectorMultiply(v1, v2);
    }
    static inline SIMDVector4 MATH_CALL VectorMultiplyAdd(SIMDVec4Param1 v1, SIMDVec4Param1 v2, SIMDVec4Param1 V3)
    {
        return DirectX::XMVectorMultiplyAdd(v1, v2, V3);
    }
    static inline SIMDVector4 MATH_CALL VectorDivide(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVectorDivide(v1, v2);
    }
    static inline SIMDVector4 MATH_CALL VectorNegativeMultiplySubtract(SIMDVec4Param1 v1, SIMDVec4Param1 v2, SIMDVec4Param1 V3)
    {
        return DirectX::XMVectorNegativeMultiplySubtract(v1, v2, V3);
    }
    static inline SIMDVector4 MATH_CALL VectorScale(SIMDVec4Param1 v, float ScaleFactor)
    {
        return DirectX::XMVectorScale(v, ScaleFactor);
    }
    static inline SIMDVector4 MATH_CALL VectorReciprocalEst(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorReciprocalEst(v);
    }
    static  inline CROSS_BASE_API SIMDVector4 MATH_CALL VectorReciprocalEstAccurate(SIMDVec4Param1 v);
    static inline CROSS_BASE_API SIMDVector4 MATH_CALL VectorReciprocalSafe(SIMDVec4Param1 v);
    static inline CROSS_BASE_API SIMDVector4 MATH_CALL VectorReciprocal(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorReciprocal(v);
    }
    static inline SIMDVector4 MATH_CALL VectorSqrtEst(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorSqrtEst(v);
    }
    static inline SIMDVector4 MATH_CALL VectorSqrt(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorSqrt(v);
    }
    static inline SIMDVector4 MATH_CALL VectorReciprocalSqrtEst(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorReciprocalSqrtEst(v);
    }
    static inline SIMDVector4 MATH_CALL VectorReciprocalSqrt(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorReciprocalSqrt(v);
    }
    static inline SIMDVector4 MATH_CALL VectorExp2(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorExp2(v);
    }
    static inline SIMDVector4 MATH_CALL VectorExpE(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorExpE(v);
    }
    static inline SIMDVector4 MATH_CALL VectorExp(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorExp(v);
    }
    static inline SIMDVector4 MATH_CALL VectorLog2(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorLog2(v);
    }
    static inline SIMDVector4 MATH_CALL VectorLogE(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorLogE(v);
    }
    static inline SIMDVector4 MATH_CALL VectorLog(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorLog(v);
    }
    static inline SIMDVector4 MATH_CALL VectorPow(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVectorPow(v1, v2);
    }
    static inline SIMDVector4 MATH_CALL VectorAbs(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorAbs(v);
    }
    static inline SIMDVector4 MATH_CALL VectorMod(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVectorMod(v1, v2);
    }
    static inline SIMDVector4 MATH_CALL VectorModAngles(SIMDVec4Param1 Angles)
    {
        return DirectX::XMVectorModAngles(Angles);
    }
    static inline SIMDVector4 MATH_CALL VectorSin(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorSin(v);
    }
    static inline SIMDVector4 MATH_CALL VectorSinEst(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorSinEst(v);
    }
    static inline SIMDVector4 MATH_CALL VectorCos(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorCos(v);
    }
    static inline SIMDVector4 MATH_CALL VectorCosEst(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorCosEst(v);
    }
    static inline void MATH_CALL VectorSinCos(SIMDVector4* pSin, SIMDVector4* pCos, SIMDVec4Param1 v)
    {
        DirectX::XMVectorSinCos(pSin, pCos, v);
    }
    static inline void MATH_CALL VectorSinCosEst(SIMDVector4* pSin, SIMDVector4* pCos, SIMDVec4Param1 v)
    {
        DirectX::XMVectorSinCosEst(pSin, pCos, v);
    }
    static inline SIMDVector4 MATH_CALL VectorTan(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorTan(v);
    }
    static inline SIMDVector4 MATH_CALL VectorTanEst(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorTanEst(v);
    }
    static inline SIMDVector4 MATH_CALL VectorSinH(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorSinH(v);
    }
    static inline SIMDVector4 MATH_CALL VectorCosH(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorCosH(v);
    }
    static inline SIMDVector4 MATH_CALL VectorTanH(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorTanH(v);
    }
    static inline SIMDVector4 MATH_CALL VectorASin(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorASin(v);
    }
    static inline SIMDVector4 MATH_CALL VectorASinEst(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorASinEst(v);
    }
    static inline SIMDVector4 MATH_CALL VectorACos(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorACos(v);
    }
    static inline SIMDVector4 MATH_CALL VectorACosEst(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorACosEst(v);
    }
    static inline SIMDVector4 MATH_CALL VectorATan(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorATan(v);
    }
    static inline SIMDVector4 MATH_CALL VectorATanEst(SIMDVec4Param1 v)
    {
        return DirectX::XMVectorATanEst(v);
    }
    static inline SIMDVector4 MATH_CALL VectorATan2(SIMDVec4Param1 Y, SIMDVec4Param1 X)
    {
        return DirectX::XMVectorATan2(Y, X);
    }
    static inline SIMDVector4 MATH_CALL VectorATan2Est(SIMDVec4Param1 Y, SIMDVec4Param1 X)
    {
        return DirectX::XMVectorATan2Est(Y, X);
    }
    static inline SIMDVector4 MATH_CALL VectorLerp(SIMDVec4Param1 V0, SIMDVec4Param1 v1, float t)
    {
        return DirectX::XMVectorLerp(V0, v1, t);
    }
    static inline SIMDVector4 MATH_CALL VectorLerpV(SIMDVec4Param1 V0, SIMDVec4Param1 v1, SIMDVec4Param1 T)
    {
        return DirectX::XMVectorLerpV(V0, v1, T);
    }
    static inline SIMDVector4 MATH_CALL VectorHermite(SIMDVec4Param1 Position0, SIMDVec4Param1 Tangent0, SIMDVec4Param1 Position1, SIMDVec4Param4 Tangent1, float t)
    {
        return DirectX::XMVectorHermite(Position0, Tangent0, Position1, Tangent1, t);
    }
    static inline SIMDVector4 MATH_CALL VectorHermiteV(SIMDVec4Param1 Position0, SIMDVec4Param1 Tangent0, SIMDVec4Param1 Position1, SIMDVec4Param4 Tangent1, SIMDVec4Param5 T)
    {
        return DirectX::XMVectorHermiteV(Position0, Tangent0, Position1, Tangent1, T);
    }
    static inline SIMDVector4 MATH_CALL VectorCatmullRom(SIMDVec4Param1 Position0, SIMDVec4Param1 Position1, SIMDVec4Param1 Position2, SIMDVec4Param4 Position3, float t)
    {
        return DirectX::XMVectorCatmullRom(Position0, Position1, Position2, Position3, t);
    }
    static inline SIMDVector4 MATH_CALL VectorCatmullRomV(SIMDVec4Param1 Position0, SIMDVec4Param1 Position1, SIMDVec4Param1 Position2, SIMDVec4Param4 Position3, SIMDVec4Param5 T)
    {
        return DirectX::XMVectorCatmullRomV(Position0, Position1, Position2, Position3, T);
    }
    static inline SIMDVector4 MATH_CALL VectorBaryCentric(SIMDVec4Param1 Position0, SIMDVec4Param1 Position1, SIMDVec4Param1 Position2, float f, float g)
    {
        return DirectX::XMVectorBaryCentric(Position0, Position1, Position2, f, g);
    }
    static inline SIMDVector4 MATH_CALL VectorBaryCentricV(SIMDVec4Param1 Position0, SIMDVec4Param1 Position1, SIMDVec4Param1 Position2, SIMDVec4Param4 F, SIMDVec4Param5 G)
    {
        return DirectX::XMVectorBaryCentricV(Position0, Position1, Position2, F, G);
    }

    //****************************************************************************
    // 2D vector operations
    //****************************************************************************
    static inline bool MATH_CALL Vector2Equal(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector2Equal(v1, v2);
    }
    static inline uint32_t MATH_CALL Vector2EqualR(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector2EqualR(v1, v2);
    }
    static inline bool MATH_CALL Vector2EqualInt(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector2EqualInt(v1, v2);
    }
    static inline uint32_t MATH_CALL Vector2EqualIntR(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector2EqualIntR(v1, v2);
    }
    static inline bool MATH_CALL Vector2NearEqual(SIMDVec4Param1 v1, SIMDVec4Param1 v2, SIMDVec4Param1 Epsilon)
    {
        return DirectX::XMVector2NearEqual(v1, v2, Epsilon);
    }
    static inline bool MATH_CALL Vector2NotEqual(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector2NotEqual(v1, v2);
    }
    static inline bool MATH_CALL Vector2NotEqualInt(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector2NotEqualInt(v1, v2);
    }
    static inline bool MATH_CALL Vector2Greater(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector2Greater(v1, v2);
    }
    static inline uint32_t MATH_CALL Vector2GreaterR(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector2GreaterR(v1, v2);
    }
    static inline bool MATH_CALL Vector2GreaterOrEqual(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector2GreaterOrEqual(v1, v2);
    }
    static inline uint32_t MATH_CALL Vector2GreaterOrEqualR(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector2GreaterOrEqualR(v1, v2);
    }
    static inline bool MATH_CALL Vector2Less(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector2Less(v1, v2);
    }
    static inline bool MATH_CALL Vector2LessOrEqual(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector2LessOrEqual(v1, v2);
    }
    static inline bool MATH_CALL Vector2InBounds(SIMDVec4Param1 v, SIMDVec4Param1 Bounds)
    {
        return DirectX::XMVector2InBounds(v, Bounds);
    }
    static inline bool MATH_CALL Vector2IsNaN(SIMDVec4Param1 v)
    {
        return DirectX::XMVector2IsNaN(v);
    }
    static inline bool MATH_CALL Vector2IsInfinite(SIMDVec4Param1 v)
    {
        return DirectX::XMVector2IsInfinite(v);
    }
    static inline SIMDVector4 MATH_CALL Vector2Dot(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector2Dot(v1, v2);
    }
    static inline SIMDVector4 MATH_CALL Vector2Cross(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector2Cross(v1, v2);
    }
    static inline SIMDVector4 MATH_CALL Vector2LengthSq(SIMDVec4Param1 v)
    {
        return DirectX::XMVector2LengthSq(v);
    }
    static inline SIMDVector4 MATH_CALL Vector2ReciprocalLengthEst(SIMDVec4Param1 v)
    {
        return DirectX::XMVector2ReciprocalLengthEst(v);
    }
    static inline SIMDVector4 MATH_CALL Vector2ReciprocalLength(SIMDVec4Param1 v)
    {
        return DirectX::XMVector2ReciprocalLength(v);
    }
    static inline SIMDVector4 MATH_CALL Vector2LengthEst(SIMDVec4Param1 v)
    {
        return DirectX::XMVector2LengthEst(v);
    }
    static inline SIMDVector4 MATH_CALL Vector2Length(SIMDVec4Param1 v)
    {
        return DirectX::XMVector2Length(v);
    }
    static inline SIMDVector4 MATH_CALL Vector2NormalizeEst(SIMDVec4Param1 v)
    {
        return DirectX::XMVector2NormalizeEst(v);
    }
    static inline SIMDVector4 MATH_CALL Vector2Normalize(SIMDVec4Param1 v)
    {
        return DirectX::XMVector2Normalize(v);
    }
    static inline SIMDVector4 MATH_CALL Vector2ClampLength(SIMDVec4Param1 v, float LengthMin, float LengthMax)
    {
        return DirectX::XMVector2ClampLength(v, LengthMin, LengthMax);
    }
    static inline SIMDVector4 MATH_CALL Vector2ClampLengthV(SIMDVec4Param1 v, SIMDVec4Param1 LengthMin, SIMDVec4Param1 LengthMax)
    {
        return DirectX::XMVector2ClampLengthV(v, LengthMin, LengthMax);
    }
    static inline SIMDVector4 MATH_CALL Vector2Reflect(SIMDVec4Param1 Incident, SIMDVec4Param1 Normal)
    {
        return DirectX::XMVector2Reflect(Incident, Normal);
    }
    static inline SIMDVector4 MATH_CALL Vector2Refract(SIMDVec4Param1 Incident, SIMDVec4Param1 Normal, float RefractionIndex)
    {
        return DirectX::XMVector2Refract(Incident, Normal, RefractionIndex);
    }
    static inline SIMDVector4 MATH_CALL Vector2RefractV(SIMDVec4Param1 Incident, SIMDVec4Param1 Normal, SIMDVec4Param1 RefractionIndex)
    {
        return DirectX::XMVector2RefractV(Incident, Normal, RefractionIndex);
    }
    static inline SIMDVector4 MATH_CALL Vector2Orthogonal(SIMDVec4Param1 v)
    {
        return DirectX::XMVector2Orthogonal(v);
    }
    static inline SIMDVector4 MATH_CALL Vector2AngleBetweenNormalsEst(SIMDVec4Param1 N1, SIMDVec4Param1 N2)
    {
        return DirectX::XMVector2AngleBetweenNormalsEst(N1, N2);
    }
    static inline SIMDVector4 MATH_CALL Vector2AngleBetweenNormals(SIMDVec4Param1 N1, SIMDVec4Param1 N2)
    {
        return DirectX::XMVector2AngleBetweenNormals(N1, N2);
    }
    static inline SIMDVector4 MATH_CALL Vector2AngleBetweenVectors(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector2AngleBetweenVectors(v1, v2);
    }
    static inline SIMDVector4 MATH_CALL Vector2LinePointDistance(SIMDVec4Param1 LinePoint1, SIMDVec4Param1 LinePoint2, SIMDVec4Param1 Point)
    {
        return DirectX::XMVector2LinePointDistance(LinePoint1, LinePoint2, Point);
    }
    static inline SIMDVector4 MATH_CALL Vector2IntersectLine(SIMDVec4Param1 Line1Point1, SIMDVec4Param1 Line1Point2, SIMDVec4Param1 Line2Point1, SIMDVec4Param4 Line2Point2)
    {
        return DirectX::XMVector2IntersectLine(Line1Point1, Line1Point2, Line2Point1, Line2Point2);
    }
    static inline SIMDVector4 MATH_CALL Vector2Transform(SIMDVec4Param1 v, SIMDMatrixParam0 M)
    {
        return DirectX::XMVector2Transform(v, M);
    }
    static inline Float4* MATH_CALL Vector2TransformStream(Float4* pOutputStream, size_t OutputStride, const Float2* pInputStream, size_t InputStride, size_t VectorCount, SIMDMatrixParam0 M)
    {
        return reinterpret_cast<Float4*>(DirectX::XMVector2TransformStream(reinterpret_cast<DirectX::XMFLOAT4*>(pOutputStream), OutputStride, reinterpret_cast<const DirectX::XMFLOAT2*>(pInputStream), InputStride, VectorCount, M));
    }
    static inline SIMDVector4 MATH_CALL Vector2TransformCoord(SIMDVec4Param1 v, SIMDMatrixParam0 M)
    {
        return DirectX::XMVector2TransformCoord(v, M);
    }
    static inline Float2* MATH_CALL Vector2TransformCoordStream(Float2* pOutputStream, size_t OutputStride, const Float2* pInputStream, size_t InputStride, size_t VectorCount, SIMDMatrixParam0 M)
    {
        return reinterpret_cast<Float2*>(DirectX::XMVector2TransformCoordStream(reinterpret_cast<DirectX::XMFLOAT2*>(pOutputStream), OutputStride, (DirectX::XMFLOAT2*)pInputStream, InputStride, VectorCount, M));
    }
    static inline SIMDVector4 MATH_CALL Vector2TransformNormal(SIMDVec4Param1 v, SIMDMatrixParam0 M)
    {
        return DirectX::XMVector2TransformNormal(v, M);
    }
    static inline Float2* MATH_CALL Vector2TransformNormalStream(Float2* pOutputStream, size_t OutputStride, const Float2* pInputStream, size_t InputStride, size_t VectorCount, SIMDMatrixParam0 M)
    {
        return reinterpret_cast<Float2*>(DirectX::XMVector2TransformNormalStream(reinterpret_cast<DirectX::XMFLOAT2*>(pOutputStream), OutputStride, reinterpret_cast<const DirectX::XMFLOAT2*>(pInputStream), InputStride, VectorCount, M));
    }

    //****************************************************************************
    // 3D vector operations
    //****************************************************************************
    static inline bool MATH_CALL Vector3Equal(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector3Equal(v1, v2);
    }
    static inline uint32_t MATH_CALL Vector3EqualR(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector3EqualR(v1, v2);
    }
    static inline bool MATH_CALL Vector3EqualInt(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector3EqualInt(v1, v2);
    }
    static inline uint32_t MATH_CALL Vector3EqualIntR(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector3EqualIntR(v1, v2);
    }
    static inline bool MATH_CALL Vector3NearEqual(SIMDVec4Param1 v1, SIMDVec4Param1 v2, SIMDVec4Param1 Epsilon)
    {
        return DirectX::XMVector3NearEqual(v1, v2, Epsilon);
    }
    static inline bool MATH_CALL Vector3NotEqual(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector3NotEqual(v1, v2);
    }
    static inline bool MATH_CALL Vector3NotEqualInt(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector3NotEqualInt(v1, v2);
    }
    static inline bool MATH_CALL Vector3Greater(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector3Greater(v1, v2);
    }
    static inline uint32_t MATH_CALL Vector3GreaterR(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector3GreaterR(v1, v2);
    }
    static inline bool MATH_CALL Vector3GreaterOrEqual(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector3GreaterOrEqual(v1, v2);
    }
    static inline uint32_t MATH_CALL Vector3GreaterOrEqualR(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector3GreaterOrEqualR(v1, v2);
    }
    static inline bool MATH_CALL Vector3Less(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector3Less(v1, v2);
    }
    static inline bool MATH_CALL Vector3LessOrEqual(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector3LessOrEqual(v1, v2);
    }
    static inline bool MATH_CALL Vector3InBounds(SIMDVec4Param1 v, SIMDVec4Param1 Bounds)
    {
        return DirectX::XMVector3InBounds(v, Bounds);
    }
    static inline bool MATH_CALL Vector3IsNaN(SIMDVec4Param1 v)
    {
        return DirectX::XMVector3IsNaN(v);
    }
    static inline bool MATH_CALL Vector3IsInfinite(SIMDVec4Param1 v)
    {
        return DirectX::XMVector3IsInfinite(v);
    }
    static inline SIMDVector4 MATH_CALL Vector3Dot(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector3Dot(v1, v2);
    }
    static inline SIMDVector4 MATH_CALL Vector3Cross(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector3Cross(v1, v2);
    }
    static inline SIMDVector4 MATH_CALL Vector3LengthSq(SIMDVec4Param1 v)
    {
        return DirectX::XMVector3LengthSq(v);
    }
    static inline SIMDVector4 MATH_CALL Vector3ReciprocalLengthEst(SIMDVec4Param1 v)
    {
        return DirectX::XMVector3ReciprocalLengthEst(v);
    }
    static inline SIMDVector4 MATH_CALL Vector3ReciprocalLength(SIMDVec4Param1 v)
    {
        return DirectX::XMVector3ReciprocalLength(v);
    }
    static inline SIMDVector4 MATH_CALL Vector3LengthEst(SIMDVec4Param1 v)
    {
        return DirectX::XMVector3LengthEst(v);
    }
    static inline SIMDVector4 MATH_CALL Vector3Length(SIMDVec4Param1 v)
    {
        return DirectX::XMVector3Length(v);
    }
    static inline SIMDVector4 MATH_CALL Vector3NormalizeEst(SIMDVec4Param1 v)
    {
        return DirectX::XMVector3NormalizeEst(v);
    }
    static inline SIMDVector4 MATH_CALL Vector3Normalize(SIMDVec4Param1 v)
    {
        return DirectX::XMVector3Normalize(v);
    }
    static inline SIMDVector4 MATH_CALL Vector3ClampLength(SIMDVec4Param1 v, float LengthMin, float LengthMax)
    {
        return DirectX::XMVector3ClampLength(v, LengthMin, LengthMax);
    }
    static inline SIMDVector4 MATH_CALL Vector3ClampLengthV(SIMDVec4Param1 v, SIMDVec4Param1 LengthMin, SIMDVec4Param1 LengthMax)
    {
        return DirectX::XMVector3ClampLengthV(v, LengthMin, LengthMax);
    }
    static inline SIMDVector4 MATH_CALL Vector3Reflect(SIMDVec4Param1 Incident, SIMDVec4Param1 Normal)
    {
        return DirectX::XMVector3Reflect(Incident, Normal);
    }
    static inline SIMDVector4 MATH_CALL Vector3Refract(SIMDVec4Param1 Incident, SIMDVec4Param1 Normal, float RefractionIndex)
    {
        return DirectX::XMVector3Refract(Incident, Normal, RefractionIndex);
    }
    static inline SIMDVector4 MATH_CALL Vector3RefractV(SIMDVec4Param1 Incident, SIMDVec4Param1 Normal, SIMDVec4Param1 RefractionIndex)
    {
        return DirectX::XMVector3RefractV(Incident, Normal, RefractionIndex);
    }
    static inline SIMDVector4 MATH_CALL Vector3Orthogonal(SIMDVec4Param1 v)
    {
        return DirectX::XMVector3Orthogonal(v);
    }
    static inline SIMDVector4 MATH_CALL Vector3AngleBetweenNormalsEst(SIMDVec4Param1 N1, SIMDVec4Param1 N2)
    {
        return DirectX::XMVector3AngleBetweenNormalsEst(N1, N2);
    }
    static inline SIMDVector4 MATH_CALL Vector3AngleBetweenNormals(SIMDVec4Param1 N1, SIMDVec4Param1 N2)
    {
        return DirectX::XMVector3AngleBetweenNormals(N1, N2);
    }
    static inline SIMDVector4 MATH_CALL Vector3AngleBetweenVectors(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector3AngleBetweenVectors(v1, v2);
    }
    static inline SIMDVector4 MATH_CALL Vector3LinePointDistance(SIMDVec4Param1 LinePoint1, SIMDVec4Param1 LinePoint2, SIMDVec4Param1 Point)
    {
        return DirectX::XMVector3LinePointDistance(LinePoint1, LinePoint2, Point);
    }
    static inline void MATH_CALL Vector3ComponentsFromNormal(SIMDVector4* pParallel, SIMDVector4* pPerpendicular, SIMDVec4Param1 v, SIMDVec4Param1 Normal)
    {
        DirectX::XMVector3ComponentsFromNormal(pParallel, pPerpendicular, v, Normal);
    }
    static inline SIMDVector4 MATH_CALL Vector3Rotate(SIMDVec4Param1 v, SIMDVec4Param1 RotationQuaternion)
    {
        return DirectX::XMVector3Rotate(v, RotationQuaternion);
    }
    static inline SIMDVector4 MATH_CALL Vector3InverseRotate(SIMDVec4Param1 v, SIMDVec4Param1 RotationQuaternion)
    {
        return DirectX::XMVector3InverseRotate(v, RotationQuaternion);
    }
    static inline SIMDVector4 MATH_CALL Vector3Transform(SIMDVec4Param1 v, SIMDMatrixParam0 M)
    {
        return DirectX::XMVector3Transform(v, M);
    }
    static inline Float4* MATH_CALL Vector3TransformStream(Float4* pOutputStream, size_t OutputStride, const Float3* pInputStream, size_t InputStride, size_t VectorCount, SIMDMatrixParam0 M)
    {
        return reinterpret_cast<Float4*>(DirectX::XMVector3TransformStream(reinterpret_cast<DirectX::XMFLOAT4*>(pOutputStream), OutputStride, reinterpret_cast<const DirectX::XMFLOAT3*>(pInputStream), InputStride, VectorCount, M));
    }
    static inline SIMDVector4 MATH_CALL Vector3TransformCoord(SIMDVec4Param1 v, SIMDMatrixParam0 M)
    {
        return DirectX::XMVector3TransformCoord(v, M);
    }
    static inline Float3* MATH_CALL Vector3TransformCXMStoreFloat3oordStream(Float3* pOutputStream, size_t OutputStride, const Float3* pInputStream, size_t InputStride, size_t VectorCount, SIMDMatrixParam0 M)
    {
        return reinterpret_cast<Float3*>(DirectX::XMVector3TransformCoordStream(reinterpret_cast<DirectX::XMFLOAT3*>(pOutputStream), OutputStride, reinterpret_cast<const DirectX::XMFLOAT3*>(pInputStream), InputStride, VectorCount, M));
    }
    static inline SIMDVector4 MATH_CALL Vector3TransformNormal(SIMDVec4Param1 v, SIMDMatrixParam0 M)
    {
        return DirectX::XMVector3TransformNormal(v, M);
    }
    static inline Float3* MATH_CALL Vector3TransformNormalStream(Float3* pOutputStream, size_t OutputStride, const Float3* pInputStream, size_t InputStride, size_t VectorCount, SIMDMatrixParam0 M)
    {
        return reinterpret_cast<Float3*>(DirectX::XMVector3TransformNormalStream(reinterpret_cast<DirectX::XMFLOAT3*>(pOutputStream), OutputStride, reinterpret_cast<const DirectX::XMFLOAT3*>(pInputStream), InputStride, VectorCount, M));
    }
    static inline SIMDVector4 MATH_CALL Vector3Project(SIMDVec4Param1 v, float ViewportX, float ViewportY, float ViewportWidth, float ViewportHeight, float ViewportMinZ, float ViewportMaxZ, SIMDMatrixParam0 Projection,
                                                       SIMDMatrixParam1 View, SIMDMatrixParam1 World)
    {
        return DirectX::XMVector3Project(v, ViewportX, ViewportY, ViewportWidth, ViewportHeight, ViewportMinZ, ViewportMaxZ, Projection, View, World);
    }
    static inline Float3* MATH_CALL Vector3ProjectStream(Float3* pOutputStream, size_t OutputStride, const Float3* pInputStream, size_t InputStride, size_t VectorCount, float ViewportX, float ViewportY, float ViewportWidth,
                                                         float ViewportHeight, float ViewportMinZ, float ViewportMaxZ, SIMDMatrixParam0 Projection, SIMDMatrixParam1 View, SIMDMatrixParam1 World)
    {
        return reinterpret_cast<Float3*>(DirectX::XMVector3ProjectStream(reinterpret_cast<DirectX::XMFLOAT3*>(pOutputStream),
                                                                         OutputStride,
                                                                         reinterpret_cast<const DirectX::XMFLOAT3*>(pInputStream),
                                                                         InputStride,
                                                                         VectorCount,
                                                                         ViewportX,
                                                                         ViewportY,
                                                                         ViewportWidth,
                                                                         ViewportHeight,
                                                                         ViewportMinZ,
                                                                         ViewportMaxZ,
                                                                         Projection,
                                                                         View,
                                                                         World));
    }
    static inline SIMDVector4 MATH_CALL Vector3Unproject(SIMDVec4Param1 v, float ViewportX, float ViewportY, float ViewportWidth, float ViewportHeight, float ViewportMinZ, float ViewportMaxZ, SIMDMatrixParam0 Projection,
                                                         SIMDMatrixParam1 View, SIMDMatrixParam1 World)
    {
        return DirectX::XMVector3Unproject(v, ViewportX, ViewportY, ViewportWidth, ViewportHeight, ViewportMinZ, ViewportMaxZ, Projection, View, World);
    }
    static inline Float3* MATH_CALL Vector3UnprojectStream(Float3* pOutputStream, size_t OutputStride, const Float3* pInputStream, size_t InputStride, size_t VectorCount, float ViewportX, float ViewportY, float ViewportWidth,
                                                           float ViewportHeight, float ViewportMinZ, float ViewportMaxZ, SIMDMatrixParam0 Projection, SIMDMatrixParam1 View, SIMDMatrixParam1 World)
    {
        return reinterpret_cast<Float3*>(DirectX::XMVector3UnprojectStream(reinterpret_cast<DirectX::XMFLOAT3*>(pOutputStream),
                                                                           OutputStride,
                                                                           reinterpret_cast<const DirectX::XMFLOAT3*>(pInputStream),
                                                                           InputStride,
                                                                           VectorCount,
                                                                           ViewportX,
                                                                           ViewportY,
                                                                           ViewportWidth,
                                                                           ViewportHeight,
                                                                           ViewportMinZ,
                                                                           ViewportMaxZ,
                                                                           Projection,
                                                                           View,
                                                                           World));
    }

    //****************************************************************************
    // 4D vector operations
    //****************************************************************************/
    static inline bool MATH_CALL Vector4Equal(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector4Equal(v1, v2);
    }
    static inline uint32_t MATH_CALL Vector4EqualR(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector4EqualR(v1, v2);
    }
    static inline bool MATH_CALL Vector4EqualInt(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector4EqualInt(v1, v2);
    }
    static inline uint32_t MATH_CALL Vector4EqualIntR(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector4EqualIntR(v1, v2);
    }
    static inline bool MATH_CALL Vector4NearEqual(SIMDVec4Param1 v1, SIMDVec4Param1 v2, SIMDVec4Param1 Epsilon)
    {
        return DirectX::XMVector4NearEqual(v1, v2, Epsilon);
    }
    static inline bool MATH_CALL Vector4NotEqual(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector4NotEqual(v1, v2);
    }
    static inline bool MATH_CALL Vector4NotEqualInt(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector4NotEqualInt(v1, v2);
    }
    static inline bool MATH_CALL Vector4Greater(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector4Greater(v1, v2);
    }
    static inline uint32_t MATH_CALL Vector4GreaterR(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector4GreaterR(v1, v2);
    }
    static inline bool MATH_CALL Vector4GreaterOrEqual(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector4GreaterOrEqual(v1, v2);
    }
    static inline uint32_t MATH_CALL Vector4GreaterOrEqualR(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector4GreaterOrEqualR(v1, v2);
    }
    static inline bool MATH_CALL Vector4Less(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector4Less(v1, v2);
    }
    static inline bool MATH_CALL Vector4LessOrEqual(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector4LessOrEqual(v1, v2);
    }
    static inline bool MATH_CALL Vector4InBounds(SIMDVec4Param1 v, SIMDVec4Param1 Bounds)
    {
        return DirectX::XMVector4InBounds(v, Bounds);
    }
    static inline bool MATH_CALL Vector4IsNaN(SIMDVec4Param1 v)
    {
        return DirectX::XMVector4IsNaN(v);
    }
    static inline bool MATH_CALL Vector4IsInfinite(SIMDVec4Param1 v)
    {
        return DirectX::XMVector4IsInfinite(v);
    }
    static inline SIMDVector4 MATH_CALL Vector4Dot(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector4Dot(v1, v2);
    }
    static inline SIMDVector4 MATH_CALL Vector4Cross(SIMDVec4Param1 v1, SIMDVec4Param1 v2, SIMDVec4Param1 V3)
    {
        return DirectX::XMVector4Cross(v1, v2, V3);
    }
    static inline SIMDVector4 MATH_CALL Vector4LengthSq(SIMDVec4Param1 v)
    {
        return DirectX::XMVector4LengthSq(v);
    }
    static inline SIMDVector4 MATH_CALL Vector4ReciprocalLengthEst(SIMDVec4Param1 v)
    {
        return DirectX::XMVector4ReciprocalLengthEst(v);
    }
    static inline SIMDVector4 MATH_CALL Vector4ReciprocalLength(SIMDVec4Param1 v)
    {
        return DirectX::XMVector4ReciprocalLength(v);
    }
    static inline SIMDVector4 MATH_CALL Vector4LengthEst(SIMDVec4Param1 v)
    {
        return DirectX::XMVector4LengthEst(v);
    }
    static inline SIMDVector4 MATH_CALL Vector4Length(SIMDVec4Param1 v)
    {
        return DirectX::XMVector4Length(v);
    }
    static inline SIMDVector4 MATH_CALL Vector4NormalizeEst(SIMDVec4Param1 v)
    {
        return DirectX::XMVector4NormalizeEst(v);
    }
    static inline SIMDVector4 MATH_CALL Vector4Normalize(SIMDVec4Param1 v)
    {
        return DirectX::XMVector4Normalize(v);
    }
    static inline SIMDVector4 MATH_CALL Vector4ClampLength(SIMDVec4Param1 v, float LengthMin, float LengthMax)
    {
        return DirectX::XMVector4ClampLength(v, LengthMin, LengthMax);
    }
    static inline SIMDVector4 MATH_CALL Vector4ClampLengthV(SIMDVec4Param1 v, SIMDVec4Param1 LengthMin, SIMDVec4Param1 LengthMax)
    {
        return DirectX::XMVector4ClampLengthV(v, LengthMin, LengthMax);
    }
    static inline SIMDVector4 MATH_CALL Vector4Reflect(SIMDVec4Param1 Incident, SIMDVec4Param1 Normal)
    {
        return DirectX::XMVector4Reflect(Incident, Normal);
    }
    static inline SIMDVector4 MATH_CALL Vector4Refract(SIMDVec4Param1 Incident, SIMDVec4Param1 Normal, float RefractionIndex)
    {
        return DirectX::XMVector4Refract(Incident, Normal, RefractionIndex);
    }
    static inline SIMDVector4 MATH_CALL Vector4RefractV(SIMDVec4Param1 Incident, SIMDVec4Param1 Normal, SIMDVec4Param1 RefractionIndex)
    {
        return DirectX::XMVector4RefractV(Incident, Normal, RefractionIndex);
    }
    static inline SIMDVector4 MATH_CALL Vector4Orthogonal(SIMDVec4Param1 v)
    {
        return DirectX::XMVector4Orthogonal(v);
    }
    static inline SIMDVector4 MATH_CALL Vector4AngleBetweenNormalsEst(SIMDVec4Param1 N1, SIMDVec4Param1 N2)
    {
        return DirectX::XMVector4AngleBetweenNormalsEst(N1, N2);
    }
    static inline SIMDVector4 MATH_CALL Vector4AngleBetweenNormals(SIMDVec4Param1 N1, SIMDVec4Param1 N2)
    {
        return DirectX::XMVector4AngleBetweenNormals(N1, N2);
    }
    static inline SIMDVector4 MATH_CALL Vector4AngleBetweenVectors(SIMDVec4Param1 v1, SIMDVec4Param1 v2)
    {
        return DirectX::XMVector4AngleBetweenVectors(v1, v2);
    }
    static inline SIMDVector4 MATH_CALL Vector4Transform(SIMDVec4Param1 v, SIMDMatrixParam0 M)
    {
        return DirectX::XMVector4Transform(v, M);
    }
    static inline Float4* MATH_CALL Vector4TransformStream(Float4* pOutputStream, size_t OutputStride, const Float4* pInputStream, size_t InputStride, size_t VectorCount, SIMDMatrixParam0 M)
    {
        return reinterpret_cast<Float4*>(DirectX::XMVector4TransformStream(reinterpret_cast<DirectX::XMFLOAT4*>(pOutputStream), OutputStride, reinterpret_cast<const DirectX::XMFLOAT4*>(pInputStream), InputStride, VectorCount, M));
    }

    //****************************************************************************
    // Matrix operations
    //****************************************************************************
    static inline bool MATH_CALL MatrixIsNaN(SIMDMatrixParam0 M)
    {
        return DirectX::XMMatrixIsNaN(M);
    }
    static inline bool MATH_CALL MatrixIsInfinite(SIMDMatrixParam0 M)
    {
        return DirectX::XMMatrixIsInfinite(M);
    }
    static inline bool MATH_CALL MatrixIsIdentity(SIMDMatrixParam0 M)
    {
        return DirectX::XMMatrixIsIdentity(M);
    }
    static inline SIMDMatrix MATH_CALL MatrixMultiply(SIMDMatrixParam0 M1, SIMDMatrixParam1 M2)
    {
        return DirectX::XMMatrixMultiply(M1, M2);
    }
    static inline SIMDMatrix MATH_CALL MatrixMultiply(SIMDMatrixParam0 M1, float s)
    {
        return M1 * s;
    }
    static inline SIMDMatrix MATH_CALL MatrixMultiplyTranspose(SIMDMatrixParam0 M1, SIMDMatrixParam1 M2)
    {
        return DirectX::XMMatrixMultiplyTranspose(M1, M2);
    }
    static inline SIMDMatrix MATH_CALL MatrixTranspose(SIMDMatrixParam0 M)
    {
        return DirectX::XMMatrixTranspose(M);
    }
    static inline SIMDMatrix MATH_CALL MatrixInverse(SIMDVector4* pDeterminant, SIMDMatrixParam0 M)
    {
        return DirectX::XMMatrixInverse(pDeterminant, M);
    }
    static inline SIMDVector4 MATH_CALL MatrixDeterminant(SIMDMatrixParam0 M)
    {
        return DirectX::XMMatrixDeterminant(M);
    }
    static inline bool MATH_CALL MatrixDecompose(SIMDVector4* outScale, SIMDVector4* outRotQuat, SIMDVector4* outTrans, SIMDMatrixParam0 M)
    {
        return DirectX::XMMatrixDecompose(outScale, outRotQuat, outTrans, M);
    }
    static inline SIMDMatrix MATH_CALL MatrixIdentity()
    {
        return DirectX::XMMatrixIdentity();
    }
    static inline SIMDMatrix MATH_CALL MatrixSet(float m00, float m01, float m02, float m03, float m10, float m11, float m12, float m13, float m20, float m21, float m22, float m23, float m30, float m31, float m32, float m33)
    {
        return DirectX::XMMatrixSet(m00, m01, m02, m03, m10, m11, m12, m13, m20, m21, m22, m23, m30, m31, m32, m33);
    }
    static inline SIMDMatrix MATH_CALL MatrixTranslation(float OffsetX, float OffsetY, float OffsetZ)
    {
        return DirectX::XMMatrixTranslation(OffsetX, OffsetY, OffsetZ);
    }
    static inline SIMDMatrix MATH_CALL MatrixTranslationFromVector(SIMDVec4Param1 Offset)
    {
        return DirectX::XMMatrixTranslationFromVector(Offset);
    }
    static inline SIMDMatrix MATH_CALL MatrixScaling(float ScaleX, float ScaleY, float ScaleZ)
    {
        return DirectX::XMMatrixScaling(ScaleX, ScaleY, ScaleZ);
    }
    static inline SIMDMatrix MATH_CALL MatrixScalingFromVector(SIMDVec4Param1 Scale)
    {
        return DirectX::XMMatrixScalingFromVector(Scale);
    }
    static inline SIMDMatrix MATH_CALL MatrixRotationX(float Angle)
    {
        return DirectX::XMMatrixRotationX(Angle);
    }
    static inline SIMDMatrix MATH_CALL MatrixRotationY(float Angle)
    {
        return DirectX::XMMatrixRotationY(Angle);
    }
    static inline SIMDMatrix MATH_CALL MatrixRotationZ(float Angle)
    {
        return DirectX::XMMatrixRotationZ(Angle);
    }
    static inline SIMDMatrix MATH_CALL MatrixRotationRollPitchYaw(float Pitch, float Yaw, float Roll)
    {
        return DirectX::XMMatrixRotationRollPitchYaw(Pitch, Yaw, Roll);
    }
    static inline SIMDMatrix MATH_CALL MatrixRotationRollPitchYawFromVector(SIMDVec4Param1 Angles)
    {
        return DirectX::XMMatrixRotationRollPitchYawFromVector(Angles);
    }
    static inline SIMDMatrix MATH_CALL MatrixRotationNormal(SIMDVec4Param1 NormalAxis, float Angle)
    {
        return DirectX::XMMatrixRotationNormal(NormalAxis, Angle);
    }
    static inline SIMDMatrix MATH_CALL MatrixRotationAxis(SIMDVec4Param1 Axis, float Angle)
    {
        return DirectX::XMMatrixRotationAxis(Axis, Angle);
    }
    static inline SIMDMatrix MATH_CALL MatrixRotationQuaternion(SIMDVec4Param1 Quaternion)
    {
        return DirectX::XMMatrixRotationQuaternion(Quaternion);
    }
    static inline SIMDMatrix MATH_CALL MatrixTransformation2D(SIMDVec4Param1 ScalingOrigin, float ScalingOrientation, SIMDVec4Param1 Scaling, SIMDVec4Param1 RotationOrigin, float Rotation, SIMDVec4Param4 Translation)
    {
        return DirectX::XMMatrixTransformation2D(ScalingOrigin, ScalingOrientation, Scaling, RotationOrigin, Rotation, Translation);
    }
    static inline SIMDMatrix MATH_CALL MatrixTransformation(SIMDVec4Param1 ScalingOrigin, SIMDVec4Param1 ScalingOrientationQuaternion, SIMDVec4Param1 Scaling, SIMDVec4Param4 RotationOrigin, SIMDVec4Param5 RotationQuaternion,
                                                            SIMDVec4Param5 Translation)
    {
        return DirectX::XMMatrixTransformation(ScalingOrigin, ScalingOrientationQuaternion, Scaling, RotationOrigin, RotationQuaternion, Translation);
    }
    static inline SIMDMatrix MATH_CALL MatrixAffineTransformation2D(SIMDVec4Param1 Scaling, SIMDVec4Param1 RotationOrigin, float Rotation, SIMDVec4Param1 Translation)
    {
        return DirectX::XMMatrixAffineTransformation2D(Scaling, RotationOrigin, Rotation, Translation);
    }
    static inline SIMDMatrix MATH_CALL MatrixAffineTransformation(SIMDVec4Param1 Scaling, SIMDVec4Param1 RotationOrigin, SIMDVec4Param1 RotationQuaternion, SIMDVec4Param4 Translation)
    {
        return DirectX::XMMatrixAffineTransformation(Scaling, RotationOrigin, RotationQuaternion, Translation);
    }
    static inline SIMDMatrix MATH_CALL MatrixReflect(SIMDVec4Param1 ReflectionPlane)
    {
        return DirectX::XMMatrixReflect(ReflectionPlane);
    }
    static inline SIMDMatrix MATH_CALL MatrixShadow(SIMDVec4Param1 ShadowPlane, SIMDVec4Param1 LightPosition)
    {
        return DirectX::XMMatrixShadow(ShadowPlane, LightPosition);
    }
    static inline SIMDMatrix MATH_CALL MatrixLookAtLH(SIMDVec4Param1 EyePosition, SIMDVec4Param1 FocusPosition, SIMDVec4Param1 UpDirection)
    {
        return DirectX::XMMatrixLookAtLH(EyePosition, FocusPosition, UpDirection);
    }
    static inline SIMDMatrix MATH_CALL MatrixLookAtRH(SIMDVec4Param1 EyePosition, SIMDVec4Param1 FocusPosition, SIMDVec4Param1 UpDirection)
    {
        return DirectX::XMMatrixLookAtRH(EyePosition, FocusPosition, UpDirection);
    }
    static inline SIMDMatrix MATH_CALL MatrixLookToLH(SIMDVec4Param1 EyePosition, SIMDVec4Param1 EyeDirection, SIMDVec4Param1 UpDirection)
    {
        return DirectX::XMMatrixLookToLH(EyePosition, EyeDirection, UpDirection);
    }
    static inline SIMDMatrix MATH_CALL MatrixLookToRH(SIMDVec4Param1 EyePosition, SIMDVec4Param1 EyeDirection, SIMDVec4Param1 UpDirection)
    {
        return DirectX::XMMatrixLookToRH(EyePosition, EyeDirection, UpDirection);
    }
    static inline SIMDMatrix MATH_CALL MatrixPerspectiveLH(float ViewWidth, float ViewHeight, float NearZ, float FarZ)
    {
        return DirectX::XMMatrixPerspectiveLH(ViewWidth, ViewHeight, NearZ, FarZ);
    }
    static inline SIMDMatrix MATH_CALL MatrixPerspectiveRH(float ViewWidth, float ViewHeight, float NearZ, float FarZ)
    {
        return DirectX::XMMatrixPerspectiveRH(ViewWidth, ViewHeight, NearZ, FarZ);
    }
    static inline SIMDMatrix MATH_CALL MatrixPerspectiveFovLH(float FovAngleY, float AspectRatio, float NearZ, float FarZ)
    {
        return DirectX::XMMatrixPerspectiveFovLH(FovAngleY, AspectRatio, NearZ, FarZ);
    }
    static inline SIMDMatrix MATH_CALL MatrixPerspectiveFovRH(float FovAngleY, float AspectRatio, float NearZ, float FarZ)
    {
        return DirectX::XMMatrixPerspectiveFovRH(FovAngleY, AspectRatio, NearZ, FarZ);
    }
    static inline SIMDMatrix MATH_CALL MatrixPerspectiveOffCenterLH(float ViewLeft, float ViewRight, float ViewBottom, float ViewTop, float NearZ, float FarZ)
    {
        return DirectX::XMMatrixPerspectiveOffCenterLH(ViewLeft, ViewRight, ViewBottom, ViewTop, NearZ, FarZ);
    }
    static inline SIMDMatrix MATH_CALL MatrixPerspectiveOffCenterRH(float ViewLeft, float ViewRight, float ViewBottom, float ViewTop, float NearZ, float FarZ)
    {
        return DirectX::XMMatrixPerspectiveOffCenterRH(ViewLeft, ViewRight, ViewBottom, ViewTop, NearZ, FarZ);
    }
    static inline SIMDMatrix MATH_CALL MatrixOrthographicLH(float ViewWidth, float ViewHeight, float NearZ, float FarZ)
    {
        return DirectX::XMMatrixOrthographicLH(ViewWidth, ViewHeight, NearZ, FarZ);
    }
    static inline SIMDMatrix MATH_CALL MatrixOrthographicRH(float ViewWidth, float ViewHeight, float NearZ, float FarZ)
    {
        return DirectX::XMMatrixOrthographicRH(ViewWidth, ViewHeight, NearZ, FarZ);
    }
    static inline SIMDMatrix MATH_CALL MatrixOrthographicOffCenterLH(float ViewLeft, float ViewRight, float ViewBottom, float ViewTop, float NearZ, float FarZ)
    {
        return DirectX::XMMatrixOrthographicOffCenterLH(ViewLeft, ViewRight, ViewBottom, ViewTop, NearZ, FarZ);
    }
    static inline SIMDMatrix MATH_CALL MatrixOrthographicOffCenterRH(float ViewLeft, float ViewRight, float ViewBottom, float ViewTop, float NearZ, float FarZ)
    {
        return DirectX::XMMatrixOrthographicOffCenterRH(ViewLeft, ViewRight, ViewBottom, ViewTop, NearZ, FarZ);
    }

    //****************************************************************************
    // Quaternion operations
    //****************************************************************************
    static inline bool MATH_CALL QuaternionEqual(SIMDVec4Param1 Q1, SIMDVec4Param1 Q2)
    {
        return DirectX::XMQuaternionEqual(Q1, Q2);
    }
    static inline bool MATH_CALL QuaternionNotEqual(SIMDVec4Param1 Q1, SIMDVec4Param1 Q2)
    {
        return DirectX::XMQuaternionNotEqual(Q1, Q2);
    }
    static inline bool MATH_CALL QuaternionIsNaN(SIMDVec4Param1 Q)
    {
        return DirectX::XMQuaternionIsNaN(Q);
    }
    static inline bool MATH_CALL QuaternionIsInfinite(SIMDVec4Param1 Q)
    {
        return DirectX::XMQuaternionIsInfinite(Q);
    }
    static inline bool MATH_CALL QuaternionIsIdentity(SIMDVec4Param1 Q)
    {
        return DirectX::XMQuaternionIsIdentity(Q);
    }
    static inline SIMDVector4 MATH_CALL QuaternionDot(SIMDVec4Param1 Q1, SIMDVec4Param1 Q2)
    {
        return DirectX::XMQuaternionDot(Q1, Q2);
    }
    static inline SIMDVector4 MATH_CALL QuaternionMultiply(SIMDVec4Param1 Q1, SIMDVec4Param1 Q2)
    {
        return DirectX::XMQuaternionMultiply(Q1, Q2);
    }
    static inline SIMDVector4 MATH_CALL QuaternionLengthSq(SIMDVec4Param1 Q)
    {
        return DirectX::XMQuaternionLengthSq(Q);
    }
    static inline SIMDVector4 MATH_CALL QuaternionReciprocalLength(SIMDVec4Param1 Q)
    {
        return DirectX::XMQuaternionReciprocalLength(Q);
    }
    static inline SIMDVector4 MATH_CALL QuaternionLength(SIMDVec4Param1 Q)
    {
        return DirectX::XMQuaternionLength(Q);
    }
    static inline SIMDVector4 MATH_CALL QuaternionNormalizeEst(SIMDVec4Param1 Q)
    {
        return DirectX::XMQuaternionNormalizeEst(Q);
    }
    static inline SIMDVector4 MATH_CALL QuaternionNormalize(SIMDVec4Param1 Q)
    {
        return DirectX::XMQuaternionNormalize(Q);
    }
    static inline SIMDVector4 MATH_CALL QuaternionConjugate(SIMDVec4Param1 Q)
    {
        return DirectX::XMQuaternionConjugate(Q);
    }
    static inline SIMDVector4 MATH_CALL QuaternionInverse(SIMDVec4Param1 Q)
    {
        return DirectX::XMQuaternionInverse(Q);
    }
    static inline SIMDVector4 MATH_CALL QuaternionLn(SIMDVec4Param1 Q)
    {
        return DirectX::XMQuaternionLn(Q);
    }
    static inline SIMDVector4 MATH_CALL QuaternionExp(SIMDVec4Param1 Q)
    {
        return DirectX::XMQuaternionExp(Q);
    }
    static inline SIMDVector4 MATH_CALL QuaternionSlerp(SIMDVec4Param1 Q0, SIMDVec4Param1 Q1, float t)
    {
        return DirectX::XMQuaternionSlerp(Q0, Q1, t);
    }
    static inline SIMDVector4 MATH_CALL QuaternionSlerpV(SIMDVec4Param1 Q0, SIMDVec4Param1 Q1, SIMDVec4Param1 T)
    {
        return DirectX::XMQuaternionSlerpV(Q0, Q1, T);
    }
    static inline SIMDVector4 MATH_CALL QuaternionSquad(SIMDVec4Param1 Q0, SIMDVec4Param1 Q1, SIMDVec4Param1 Q2, SIMDVec4Param4 Q3, float t)
    {
        return DirectX::XMQuaternionSquad(Q0, Q1, Q2, Q3, t);
    }
    static inline SIMDVector4 MATH_CALL QuaternionSquadV(SIMDVec4Param1 Q0, SIMDVec4Param1 Q1, SIMDVec4Param1 Q2, SIMDVec4Param4 Q3, SIMDVec4Param5 T)
    {
        return DirectX::XMQuaternionSquadV(Q0, Q1, Q2, Q3, T);
    }
    static inline void MATH_CALL QuaternionSquadSetup(SIMDVector4* pA, SIMDVector4* pB, SIMDVector4* pC, SIMDVec4Param1 Q0, SIMDVec4Param1 Q1, SIMDVec4Param1 Q2, SIMDVec4Param4 Q3)
    {
        DirectX::XMQuaternionSquadSetup(pA, pB, pC, Q0, Q1, Q2, Q3);
    }
    static inline SIMDVector4 MATH_CALL QuaternionBaryCentric(SIMDVec4Param1 Q0, SIMDVec4Param1 Q1, SIMDVec4Param1 Q2, float f, float g)
    {
        return DirectX::XMQuaternionBaryCentric(Q0, Q1, Q2, f, g);
    }
    static inline SIMDVector4 MATH_CALL QuaternionBaryCentricV(SIMDVec4Param1 Q0, SIMDVec4Param1 Q1, SIMDVec4Param1 Q2, SIMDVec4Param4 F, SIMDVec4Param5 G)
    {
        return DirectX::XMQuaternionBaryCentricV(Q0, Q1, Q2, F, G);
    }
    static inline SIMDVector4 MATH_CALL QuaternionIdentity()
    {
        return DirectX::XMQuaternionIdentity();
    }
    static inline SIMDVector4 MATH_CALL QuaternionRotationRollPitchYaw(float Pitch, float Yaw, float Roll)
    {
        return DirectX::XMQuaternionRotationRollPitchYaw(Pitch, Yaw, Roll);
    }
    static inline SIMDVector4 MATH_CALL QuaternionRotationRollPitchYawFromVector(SIMDVec4Param1 Angles)
    {
        return DirectX::XMQuaternionRotationRollPitchYawFromVector(Angles);
    }
    static inline SIMDVector4 MATH_CALL QuaternionRotationNormal(SIMDVec4Param1 NormalAxis, float Angle)
    {
        return DirectX::XMQuaternionRotationNormal(NormalAxis, Angle);
    }
    static inline SIMDVector4 MATH_CALL QuaternionRotationAxis(SIMDVec4Param1 Axis, float Angle)
    {
        return DirectX::XMQuaternionRotationAxis(Axis, Angle);
    }
    static inline SIMDVector4 MATH_CALL QuaternionRotationMatrix(SIMDMatrixParam0 M)
    {
        return DirectX::XMQuaternionRotationMatrix(M);
    }
    static inline void MATH_CALL QuaternionToAxisAngle(SIMDVector4* pAxis, float* pAngle, SIMDVec4Param1 Q)
    {
        DirectX::XMQuaternionToAxisAngle(pAxis, pAngle, Q);
    }

    //****************************************************************************
    // Plane operations
    //****************************************************************************
    static inline bool MATH_CALL PlaneEqual(SIMDVec4Param1 P1, SIMDVec4Param1 P2)
    {
        return DirectX::XMPlaneEqual(P1, P2);
    }
    static inline bool MATH_CALL PlaneNearEqual(SIMDVec4Param1 P1, SIMDVec4Param1 P2, SIMDVec4Param1 Epsilon)
    {
        return DirectX::XMPlaneNearEqual(P1, P2, Epsilon);
    }
    static inline bool MATH_CALL PlaneNotEqual(SIMDVec4Param1 P1, SIMDVec4Param1 P2)
    {
        return DirectX::XMPlaneNotEqual(P1, P2);
    }
    static inline bool MATH_CALL PlaneIsNaN(SIMDVec4Param1 P)
    {
        return DirectX::XMPlaneIsNaN(P);
    }
    static inline bool MATH_CALL PlaneIsInfinite(SIMDVec4Param1 P)
    {
        return DirectX::XMPlaneIsInfinite(P);
    }
    static inline SIMDVector4 MATH_CALL PlaneDot(SIMDVec4Param1 P, SIMDVec4Param1 v)
    {
        return DirectX::XMPlaneDot(P, v);
    }
    static inline SIMDVector4 MATH_CALL PlaneDotCoord(SIMDVec4Param1 P, SIMDVec4Param1 v)
    {
        return DirectX::XMPlaneDotCoord(P, v);
    }
    static inline SIMDVector4 MATH_CALL PlaneDotNormal(SIMDVec4Param1 P, SIMDVec4Param1 v)
    {
        return DirectX::XMPlaneDotNormal(P, v);
    }
    static inline SIMDVector4 MATH_CALL PlaneNormalizeEst(SIMDVec4Param1 P)
    {
        return DirectX::XMPlaneNormalizeEst(P);
    }
    static inline SIMDVector4 MATH_CALL PlaneNormalize(SIMDVec4Param1 P)
    {
        return DirectX::XMPlaneNormalize(P);
    }
    static inline SIMDVector4 MATH_CALL PlaneIntersectLine(SIMDVec4Param1 P, SIMDVec4Param1 LinePoint1, SIMDVec4Param1 LinePoint2)
    {
        return DirectX::XMPlaneIntersectLine(P, LinePoint1, LinePoint2);
    }
    static inline void MATH_CALL PlaneIntersectPlane(SIMDVector4* pLinePoint1, SIMDVector4* pLinePoint2, SIMDVec4Param1 P1, SIMDVec4Param1 P2)
    {
        DirectX::XMPlaneIntersectPlane(pLinePoint1, pLinePoint2, P1, P2);
    }
    static inline SIMDVector4 MATH_CALL PlaneTransform(SIMDVec4Param1 P, SIMDMatrixParam0 M)
    {
        return DirectX::XMPlaneTransform(P, M);
    }
    static inline Float4* MATH_CALL PlaneTransformStream(Float4* pOutputStream, size_t OutputStride, const Float4* pInputStream, size_t InputStride, size_t PlaneCount, SIMDMatrixParam0 M)
    {
        return reinterpret_cast<Float4*>(DirectX::XMPlaneTransformStream(reinterpret_cast<DirectX::XMFLOAT4*>(pOutputStream), OutputStride, reinterpret_cast<const DirectX::XMFLOAT4*>(pInputStream), InputStride, PlaneCount, M));
    }
    static inline SIMDVector4 MATH_CALL PlaneFromPointNormal(SIMDVec4Param1 Point, SIMDVec4Param1 Normal)
    {
        return DirectX::XMPlaneFromPointNormal(Point, Normal);
    }
    static inline SIMDVector4 MATH_CALL PlaneFromPoints(SIMDVec4Param1 Point1, SIMDVec4Param1 Point2, SIMDVec4Param1 Point3)
    {
        return DirectX::XMPlaneFromPoints(Point1, Point2, Point3);
    }

    //****************************************************************************
    // Color operations
    //****************************************************************************
    static inline bool MATH_CALL ColorEqual(SIMDVec4Param1 C1, SIMDVec4Param1 C2)
    {
        return DirectX::XMColorEqual(C1, C2);
    }
    static inline bool MATH_CALL ColorNotEqual(SIMDVec4Param1 C1, SIMDVec4Param1 C2)
    {
        return DirectX::XMColorNotEqual(C1, C2);
    }
    static inline bool MATH_CALL ColorGreater(SIMDVec4Param1 C1, SIMDVec4Param1 C2)
    {
        return DirectX::XMColorGreater(C1, C2);
    }
    static inline bool MATH_CALL ColorGreaterOrEqual(SIMDVec4Param1 C1, SIMDVec4Param1 C2)
    {
        return DirectX::XMColorGreaterOrEqual(C1, C2);
    }
    static inline bool MATH_CALL ColorLess(SIMDVec4Param1 C1, SIMDVec4Param1 C2)
    {
        return DirectX::XMColorLess(C1, C2);
    }
    static inline bool MATH_CALL ColorLessOrEqual(SIMDVec4Param1 C1, SIMDVec4Param1 C2)
    {
        return DirectX::XMColorLessOrEqual(C1, C2);
    }
    static inline bool MATH_CALL ColorIsNaN(SIMDVec4Param1 C)
    {
        return DirectX::XMColorIsNaN(C);
    }
    static inline bool MATH_CALL ColorIsInfinite(SIMDVec4Param1 C)
    {
        return DirectX::XMColorIsInfinite(C);
    }
    static inline SIMDVector4 MATH_CALL ColorNegative(SIMDVec4Param1 C)
    {
        return DirectX::XMColorNegative(C);
    }
    static inline SIMDVector4 MATH_CALL ColorModulate(SIMDVec4Param1 C1, SIMDVec4Param1 C2)
    {
        return DirectX::XMColorModulate(C1, C2);
    }
    static inline SIMDVector4 MATH_CALL ColorAdjustSaturation(SIMDVec4Param1 C, float Saturation)
    {
        return DirectX::XMColorAdjustSaturation(C, Saturation);
    }
    static inline SIMDVector4 MATH_CALL ColorAdjustContrast(SIMDVec4Param1 C, float Contrast)
    {
        return DirectX::XMColorAdjustContrast(C, Contrast);
    }
    static inline SIMDVector4 MATH_CALL ColorRGBToHSL(SIMDVec4Param1 rgb)
    {
        return DirectX::XMColorRGBToHSL(rgb);
    }
    static inline SIMDVector4 MATH_CALL ColorHSLToRGB(SIMDVec4Param1 hsl)
    {
        return DirectX::XMColorHSLToRGB(hsl);
    }
    static inline SIMDVector4 MATH_CALL ColorRGBToHSV(SIMDVec4Param1 rgb)
    {
        return DirectX::XMColorRGBToHSV(rgb);
    }
    static inline SIMDVector4 MATH_CALL ColorHSVToRGB(SIMDVec4Param1 hsv)
    {
        return DirectX::XMColorHSVToRGB(hsv);
    }
    static inline SIMDVector4 MATH_CALL ColorRGBToYUV(SIMDVec4Param1 rgb)
    {
        return DirectX::XMColorRGBToYUV(rgb);
    }
    static inline SIMDVector4 MATH_CALL ColorYUVToRGB(SIMDVec4Param1 yuv)
    {
        return DirectX::XMColorYUVToRGB(yuv);
    }
    static inline SIMDVector4 MATH_CALL ColorRGBToYUV_HD(SIMDVec4Param1 rgb)
    {
        return DirectX::XMColorRGBToYUV_HD(rgb);
    }
    static inline SIMDVector4 MATH_CALL ColorYUVToRGB_HD(SIMDVec4Param1 yuv)
    {
        return DirectX::XMColorYUVToRGB_HD(yuv);
    }
    static inline SIMDVector4 MATH_CALL ColorRGBToXYZ(SIMDVec4Param1 rgb)
    {
        return DirectX::XMColorRGBToXYZ(rgb);
    }
    static inline SIMDVector4 MATH_CALL ColorXYZToRGB(SIMDVec4Param1 xyz)
    {
        return DirectX::XMColorXYZToRGB(xyz);
    }
    static inline SIMDVector4 MATH_CALL ColorXYZToSRGB(SIMDVec4Param1 xyz)
    {
        return DirectX::XMColorXYZToSRGB(xyz);
    }
    static inline SIMDVector4 MATH_CALL ColorSRGBToXYZ(SIMDVec4Param1 srgb)
    {
        return DirectX::XMColorSRGBToXYZ(srgb);
    }
    static inline SIMDVector4 MATH_CALL ColorRGBToSRGB(SIMDVec4Param1 rgb)
    {
        return DirectX::XMColorRGBToSRGB(rgb);
    }
    static inline SIMDVector4 MATH_CALL ColorSRGBToRGB(SIMDVec4Param1 srgb)
    {
        return DirectX::XMColorSRGBToRGB(srgb);
    }
};
#endif
#pragma warning(pop)
static_assert(sizeof(Float2) == sizeof(DirectX::XMFLOAT2));
static_assert(sizeof(Float3) == sizeof(DirectX::XMFLOAT3));
static_assert(sizeof(Float4) == sizeof(DirectX::XMFLOAT4));
static_assert(sizeof(Float4x4) == sizeof(DirectX::XMFLOAT4X4));

/**
 * Loads 1 float from unaligned memory and replicates it to all 4 elements.
 *
 * @param Ptr	Unaligned memory pointer to the float
 * @return		VectorRegister(Ptr[0], Ptr[0], Ptr[0], Ptr[0])
 */
#define VectorLoadFloat1(Ptr) DirectX::XMVectorReplicatePtr((const float*)(Ptr))

/**
 * Loads XYZ and sets W=0
 *
 * @param Vector	VectorRegister
 * @return		VectorRegister(X, Y, Z, 0.0f)
 */
#define VectorSet_W0(Vec) DirectX::XMVectorAndInt(Vec, DirectX::g_XMMask3)

 /**
 * Replicates one element into all four elements and returns the new vector.
 *
 * @param Vec			Source vector
 * @param ElementIndex	Index (0-3) of the element to replicate
 * @return				VectorRegister( Vec[ElementIndex], Vec[ElementIndex], Vec[ElementIndex], Vec[ElementIndex] )
 */
#define VectorReplicateIndex(Vec, ElementIndex) DirectX::XMVectorSwizzle<ElementIndex, ElementIndex, ElementIndex, ElementIndex>(Vec)

/**
 * Returns non-zero if any element in Vec1 is greater than the corresponding element in Vec2, otherwise 0.
 *
 * @param Vec1			1st source vector
 * @param Vec2			2nd source vector
 * @return				Non-zero integer if (Vec1.x > Vec2.x) || (Vec1.y > Vec2.y) || (Vec1.z > Vec2.z) || (Vec1.w > Vec2.w)
 */
FORCEINLINE UInt32 VectorAnyGreaterThan(const DirectX::XMVECTOR& Vec1, const DirectX::XMVECTOR& Vec2)
{
    using namespace DirectX;
    // Returns a comparison value that can be examined using functions such as XMComparisonAllTrue
    uint32_t comparisonValue = XMVector4GreaterR(Vec1, Vec2);

    // Returns true if any of the compared components are true
    return (UInt32)XMComparisonAnyTrue(comparisonValue);
}

/**
 * Returns an integer bit-mask (0x00 - 0x0f) based on the sign-bit for each component in a vector.
 *
 * @param VecMask		Vector
 * @return				Bit 0 = sign(VecMask.x), Bit 1 = sign(VecMask.y), Bit 2 = sign(VecMask.z), Bit 3 = sign(VecMask.w)
 */
FORCEINLINE int VectorMaskBits(const DirectX::XMVECTOR& v)
{
#if defined(_XM_NO_INTRINSICS_)
    return (v.vector4_f32[0] >= 0 ? 0x1 : 0 + v.vector4_f32[1] >= 0 ? 0x2 : 0 + v.vector4_f32[2] >= 0 ? 0x4 : 0 + v.vector4_f32[3] >= 0 ? 0x8 : 0);
#elif defined(_XM_ARM_NEON_INTRINSICS_)
    constexpr const uint32_t cMaskElementIndex[4]{1, 2, 4, 8};
    uint32x4_t mmA = vandq_u32(vreinterpretq_u32_f32(v), vld1q_u32(cMaskElementIndex));   // [0 1 2 3]
    uint32x4_t mmB = vextq_u32(mmA, mmA, 2);                                              // [2 3 0 1]
    uint32x4_t mmC = vorrq_u32(mmA, mmB);                                                 // [0+2 1+3 0+2 1+3]
    uint32x4_t mmD = vextq_u32(mmC, mmC, 3);                                              // [1+3 0+2 1+3 0+2]
    uint32x4_t mmE = vorrq_u32(mmC, mmD);                                                 // [0+1+2+3 ...]
    return vgetq_lane_u32(mmE, 0);
#elif defined(_XM_SSE_INTRINSICS_)
    return _mm_movemask_ps(v);
#endif
}

}   // namespace cross
#include "CrossBase/TileBasedCoordinates.h"

namespace cross::math
{
    /** Divides two integers and rounds up */
    template<class T>
    inline T DivideAndRoundUp(T dividend, T divisor)
    {
        return (dividend + divisor - 1) / divisor;
    }

    inline UInt2 DivideAndRoundUp(UInt2 lhs, UInt2 divisor)
    {
        return UInt2(DivideAndRoundUp(lhs.x, divisor.x), DivideAndRoundUp(lhs.y, divisor.y));
    }

    inline UInt3 DivideAndRoundUp(UInt3 lhs, UInt3 divisor)
    {
        return UInt3(DivideAndRoundUp(lhs.x, divisor.x), DivideAndRoundUp(lhs.y, divisor.y), DivideAndRoundUp(lhs.z, divisor.z));
    }

    template<class T>
    inline T DivideAndRoundNearest(T dividend, T divisor)
    {
        return (dividend >= 0) ? (dividend + divisor / 2) / divisor : (dividend - divisor / 2 + 1) / divisor;
    }

    template<typename T>
    auto InBetween(T a, T b, T c)
    {
        return a <= b && b <= c;
    };

    //template<class T>
    static double Mean(const std::vector<double>& data)
    {
        double sum = 0.0;

        if (data.size() == 0) { return sum; }

        for (auto& itr : data)
        {
            sum += itr;
        }
        return sum / data.size();
    }
    //template<class T>
    static std::pair<double, double> MeanAndVariance(const std::vector<double>& data)
    {
        if (data.size() == 0)
        {
            return { 0., 0. };
        }
        auto mean = Mean(data);

        double variance_sum = 0.;
        for (auto& itr : data)
        {
            variance_sum += (itr - mean) * (itr - mean);
        }

        return { mean, variance_sum / data.size() };
    }
    struct CEMeta(Cli) EditorQuaternion
    {
        CEMeta(Cli)
        float x;
        CEMeta(Cli)
        float y;
        CEMeta(Cli)
        float z;
        CEMeta(Cli)
        float w;
    };
    struct CEMeta(Cli) EditorQuaternionD
    {
        CEMeta(Cli)
        double x;
        CEMeta(Cli)
        double y;
        CEMeta(Cli)
        double z;
        CEMeta(Cli)
        double w;
    };
    struct CEMeta(Cli) TRS
    {
        CEMeta(Cli)
        cross::Float3 scaling;
        CEMeta(Cli)
        EditorQuaternion rotation;
        CEMeta(Cli)
        cross::Float3 translation;
    };

    struct CEMeta(Cli) TRSD
    {
        CEMeta(Cli)
        cross::Double3 scaling;
        CEMeta(Cli)
        EditorQuaternionD rotation;
        CEMeta(Cli)
        cross::Double3 translation;
    };
    class CROSS_BASE_API CEMeta(Cli) MatrixUtil
    {
    public:
        CEMeta(Cli)
        static cross::Float4x4 Math_MatrixInverse(cross::Float4x4 matrix);
        CEMeta(Cli)
        static cross::Double4x4 Math_MatrixInverse_d(cross::Double4x4 matrix);
        CEMeta(Cli)
        static cross::Float4x4 Math_MatrixCompose(cross::Float3 scaling, cross::Quaternion rotation, cross::Float3 translation);
        CEMeta(Cli)
        static TRS Math_MatrixDecompose(cross::Float4x4 matrix);
        CEMeta(Cli)
        static cross::Double4x4 Math_MatrixComposeD(cross::Double3 scaling, cross::Quaternion64 rotation, cross::Double3 translation);
        CEMeta(Cli)
        static TRSD Math_MatrixDecomposeD(cross::Double4x4 matrix);
    };
    template<typename T, typename = void>
    struct HasLength : std::false_type
    {};
    template<typename T>
    struct HasLength<T, std::void_t<decltype(std::declval<T>().Length())>> : std::true_type
    {};
    template<typename T>
    inline constexpr bool HasLengthV = HasLength<T>::value;
    template<class T, class = std::enable_if_t<HasLength<T>::value, bool> >
    static bool isApprox(const T& lhs, const T& rhs, float precision = MathUtils::MathEps)
    {
        return (lhs - rhs).Length() <= precision;
    }
}
#include "CrossMath.inl"