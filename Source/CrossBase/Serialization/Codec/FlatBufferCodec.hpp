#pragma once

#include "Template/TypeTraits.hpp"
#include "Serialization/SerializationForward.hpp"
#include "Serialization/Archive/Archive.h"
#include "Serialization/Archive/BinaryArchive.h"
#include "Serialization/Archive/FileArchive.h"
#include "Serialization/Serialization.hpp"
#include "MathLib.h"
#include "BasicStruct_generated.h"
#include "flatbuffers/reflection.h"
#include "stduuid/gsl/span"
#include "Serialization/Archive/MemoryMappingArchive.h"
namespace cross {
struct FlatBufferCodec
{
    class Writer
    {
        template<typename T>
        auto Write(T& t) const noexcept -> std::enable_if_t<TIsArithmaticV<T>, bool>
        {
            static_assert("not implemented");
        }
    };
    class Reader
    {
    public:
        static auto GetOptionalOffsetOfTable(const flatbuffers::Table* table, uint16_t offset)
        {
            return table->GetOptionalFieldOffset(offset);
        }

        template<typename T>
        auto Read(const flatbuffers::Table* table, T& t, uint16_t offset) const noexcept -> std::enable_if_t<TIsArithmaticV<T>, bool>
        {
            if (!table)
                return false;

            t = std::move(table->GetField<T>(offset, 0));
            return true;
        }
        template<typename T>
        auto Read(const flatbuffers::Table* table, T& t, uint16_t offset) const noexcept -> std::enable_if_t<TIsBoolV<T>, bool>
        {
            if (!table)
                return false;

            t = std::move(table->GetField<uint8_t>(offset, 0) != 0);
            return true;
        }
        template<typename T>
        auto Read(const flatbuffers::Table* table, T& t, uint16_t offset) const noexcept -> std::enable_if_t<std::is_enum_v<T>, bool>
        {
            if (!table)
                return false;
            t = std::move(static_cast<T>(table->GetField<uint8_t>(offset, 0)));
            return true;
        }
        template<typename T>
        auto Read(const flatbuffers::Table* table, T& t, uint16_t offset) const noexcept -> std::enable_if_t<TIsSimpleFBStructV<T>, bool>
        {
            if (!table)
                return false;
            auto tPtr = table->GetStruct<const T*>(offset);
            if (tPtr)
            {
                t = *tPtr;
                return true;
            }
            return false;
        }

        template<typename T>
        auto Read(const flatbuffers::Table* table, gsl::span<T>& span, uint16_t offset) const
        {
            if (!table)
                return false;

            if constexpr (TIsStrictIntegralV<T> || TIsArithmaticV<T>)
            {
                auto fbvector = table->GetPointer<flatbuffers::Vector<T>*>(offset);
                if (!fbvector)
                    return false;
                auto* data = fbvector->data();
                auto size = fbvector->size();
                span.assign(data, size);
                return true;
            }
            else if constexpr (std::is_same_v<cross::data::Vec4u, T>)
            {
                auto fbvector = table->GetPointer<const flatbuffers::Vector<const CrossSchema::uint4*>*>(offset);
                if (!fbvector)
                    return false;
                auto* data = reinterpret_cast<const cross::data::Vec4u*>(fbvector->data());
                auto size = fbvector->size();
                span.assign(data, size);
                return true;
            }
            else if constexpr (std::is_same_v<cross::data::Vec4f, T>)
            {
                auto fbvector = table->GetPointer<const flatbuffers::Vector<const CrossSchema::float4*>*>(offset);
                if (!fbvector)
                    return false;
                auto* data = reinterpret_cast<const cross::data::Vec4f*>(fbvector->data());
                auto size = fbvector->size();
                span.assign(data, size);
                return true;
            }
        }

        template<typename T, typename Alloc>
        auto Read(const flatbuffers::Table* table, std::vector<T, Alloc>& vec, uint16_t offset) const
        {
            if (!table)
                return false;

            if constexpr (TIsStrictIntegralV<T> || TIsArithmaticV<T>)
            {
                auto fbvector = table->GetPointer<const flatbuffers::Vector<T>*>(offset);
                if (!fbvector)
                    return false;
                auto size = fbvector->size();
                vec = std::move(std::vector<T>(fbvector->data(), fbvector->data() + size));
                return true;
            }
            else if constexpr (std::is_same_v<cross::data::Vec4u, T>)
            {
                auto fbvector = table->GetPointer<const flatbuffers::Vector<const CrossSchema::uint4*>*>(offset);
                if (!fbvector)
                    return false;
                auto size = fbvector->size();
                vec = std::move(std::vector<cross::data::Vec4u>(reinterpret_cast<const cross::data::Vec4u*>(fbvector->data()), reinterpret_cast<const cross::data::Vec4u*>(fbvector->data()) + size));
                return true;
            }
            else if constexpr (std::is_same_v<cross::data::Vec4f, T>)
            {
                auto fbvector = table->GetPointer<const flatbuffers::Vector<const CrossSchema::float4*>*>(offset);
                if (!fbvector)
                    return false;
                auto size = fbvector->size();
                vec = std::move(std::vector<cross::data::Vec4f>(reinterpret_cast<const cross::data::Vec4f*>(fbvector->data()), reinterpret_cast<const cross::data::Vec4f*>(fbvector->data()) + size));
                return true;
            }

            else if constexpr (TIsSimpleFBStructV<T>)
            {
                auto fbvector = table->GetPointer<const flatbuffers::Vector<const T*>*>(offset);
                auto size = fbvector->size();
                vec.resize(size);
                for (flatbuffers::uoffset_t _i = 0; _i < size; _i++)
                {
                    vec[_i] = *(fbvector->Get(_i));
                }
                return true;
            }

            else if constexpr (TIsStdStringV<T>)
            {
                auto fbvector = table->GetPointer<const flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>*>(offset);
                vec.resize(fbvector->size());
                for (flatbuffers::uoffset_t _i = 0; _i < vec.size(); _i++)
                {
                    vec[_i] = fbvector->Get(_i)->str();
                }
                return true;
            }
        }
        template<typename T>
        auto Read(const flatbuffers::Table* table, T& t, uint16_t offset) const -> std::enable_if_t<TIsStdStringV<T>, bool>
        {
            if (!table)
                return false;

            auto fbstring = table->GetPointer<const flatbuffers::String*>(offset);
            if (fbstring)
            {
                t = std::move(fbstring->str());
                return true;
            }
            else
            {
                t = std::string("");
                return false;
            }
        }
    };
};
using FBSerializer = TSerializer<FlatBufferCodec>;
}   // namespace cross
