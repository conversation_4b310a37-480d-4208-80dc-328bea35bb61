#pragma once
#include <vector>
#include "Platform/PlatformTypes.h"
#include "CrossBase/Log.h"
#include <optional>
#include <string>
#include <string_view>
#include <variant>
#define USE_RAPID_JSON 1
#define USE_NLOHMANN_JSON 0

#if USE_RAPID_JSON
#include "document.h"
#include "prettywriter.h"
#include "stringbuffer.h"
#elif USE_NLOHMANN_JSON
#include "NlohmannJSON/single_include/nlohmann/json.hpp"
#endif

#include "DangoBinaryJson.h"
#include "CEMetaMacros.h"
#include "memory/allocator/range_allocator.hpp"
namespace cross {
// Nested Types

struct TLSFPoolInfo
{
    bool IsContain(const void* ptr) const noexcept { return ptr >= poolSegmentBegin && ptr < poolSegmentEnd; }

    void Free(void* ptr);

    void* Allocate(size_t bytes, size_t alignment);

    bool IsEmpty() const noexcept { return poolAllocatedSize == 0; }

    void* tlsfPool = nullptr;
    uint8_t* poolSegmentBegin = nullptr;
    uint8_t* poolSegmentEnd = nullptr;
    uint32_t poolReservedSize = 0;
    uint32_t totalAllocationCount = 0;

    uint32_t poolAllocatedSize = 0;

    ////uint32 maxBlockSize = 0;
};

class CROSS_BASE_API TLSFAllocator
{
public:
    static const bool kNeedFree = true;
    TLSFAllocator();
    TLSFAllocator(const TLSFAllocator&) = delete;
    ~TLSFAllocator();

    void* Malloc(size_t bytes);

    static void Free(void* mem);

    void* Realloc(void* mem, size_t originalSize, size_t newSize) ;

protected:
    void OnChunkPreAllocate(size_t size);
    size_t GetChunkSize(void* mem) const;
    TLSFPoolInfo CreateTLSFPool(uint32_t expectSize);
    void DestroyTLSFPool(TLSFPoolInfo& poolInfo);

    void* TryAllocateChunkInExistPools(size_t bytes, size_t alignment, int& exceptPoolIndex, std::vector<TLSFPoolInfo>& poolarray);

    void* RequestNewPoolAndAllocateOneChunk(size_t poolSize, size_t bytes, size_t alignment, std::vector<TLSFPoolInfo>& poolarrays, int& pooldex);
    std::vector<TLSFPoolInfo> TLSFPoolArray;
    std::vector<TLSFPoolInfo> TLSFPoolArrayBig;
    void OnChunkFreed(size_t poolIndex, std::vector<TLSFPoolInfo>& poolarrays, int& poolindex);

protected:
    size_t mStartWarningSize = 128 * 1024;

    int mMaxChunkSize = 0;
    int mNowUsePoolIndex = 0;
    int mNowUsePoolIndexBig = 0;
    std::mutex mMtx;
};
class SerializeNode;
class DeserializeNode;

struct UniqueJsonNodeKey;
using TLSFDocument = rapidjson::GenericDocument<rapidjson::UTF8<>, TLSFAllocator>;
struct UniqueJsonNodeIStream
{
    typedef char Ch;

    virtual ~UniqueJsonNodeIStream() {}
    virtual Ch Peek() const = 0; // Read first char without forward current position
    virtual Ch Take() = 0;       // Read and forward position
    virtual size_t Tell() = 0;   // Current position
};

// Iterator Usage:
// for(auto it = node.begin(); it != node.end(); ++it)
//{
//  std::string_view key = it.Key();
//  SerializeNode val = it.Value();
//  if(key == "ComponentA")
//  {
//      LOG_INFO("{}", val.AsInt32();
//  }
//}
// Or you can use:
// for(auto[key, val] : node)
//{
//  //key is std::string_view
//  //val is SerializeNode
//}
CROSS_BASE_API TLSFDocument& GetStaticDoc();
CROSS_BASE_API TLSFAllocator& GetTLSFAllocator();

template<bool IsConst>
struct UniqueJsonNodeIt
{
    friend class DeserializeNode;
    friend class SerializeNode;
    using ValueType = std::conditional_t<IsConst, DeserializeNode, SerializeNode>;

    std::string_view Key() const;
    ValueType Value() const;
    bool operator==(const UniqueJsonNodeIt<IsConst>& other) const;
    bool operator!=(const UniqueJsonNodeIt<IsConst>& other) const;
    UniqueJsonNodeIt& operator++();
    UniqueJsonNodeIt operator++(int);
    std::tuple<std::string_view, ValueType> operator*() const;

protected:
    using JsonIter = rapidjson::GenericValue<rapidjson::UTF8<>, TLSFAllocator>::MemberIterator;


    UniqueJsonNodeIt(std::variant<JsonIter, dango::BinJsonIt> it)
        : it(std::move(it))
    {
    }

    std::variant<JsonIter, dango::BinJsonIt> it;

    bool IsJson() const;
    const JsonIter& JsonIt() const;
    JsonIter& JsonIt();
    const dango::BinJsonIt& BinIt() const;
    dango::BinJsonIt& BinIt();
};

//SerializeNode
// A key-val pair to store data for Serialize
//Example:                                  
// SerializeNode a = 123;                    // int/float/double/bool/nullptr
//
// SerializeNode b = "abc";                  // string literal( !!will NOT copy!! )
//
// const char* str = "abc";                 
// SerializeNode b_with_copy = str;          // const char* ( !!will copy!! Try your best not to write like this, use string literal instead)
//
// std::string str2 = "abc";
// SerializeNode b_with_copy2 = str2;        // std::string ( !!will copy!! )
//
// SerializeNode c = {"name"_k = "dango"};   // json style key-val pair
// std::string cs = c.FormatToJson();        // {"name" : "dango"}
//
// SerializeNode d = {                      
//      "ComponentA"_k = std::move(a),       // Copy constructor is deleted, you need to use std::move
//      "ComponentB"_k = std::move(b),      
//      "ComponentC"_k = std::move(c)       
// };                                       
// std::string ds = d.FormatToJson();        // { "ComponentA" : 123, "ComponentB" : "abc", "ComponentC" : {"name" : "dango"} }
class CEMeta(Editor) DeserializeNode
    {
    friend struct UniqueJsonNodeKey;
    friend class SerializeNode;
    friend struct UniqueJsonNodeIt<true>;

protected:
#if USE_RAPID_JSON
    using JsonNode =  rapidjson::GenericValue<rapidjson::UTF8<>, TLSFAllocator>;
#elif USE_NLOHMANN_JSON
        using JsonNode = nlohmann::json;
#endif

public:
    //////////////////////////////////Interface declare//////////////////////////////
    bool IsNull() const;
    bool IsBoolean() const;
    bool IsIntegral() const;
    bool IsFloatingPoint() const;
    bool IsNumber() const;
    bool IsString() const;
    bool IsObject() const;
    bool IsArray() const;

    bool AsBoolean() const;
    UInt8 AsUInt8() const;
    UInt16 AsUInt16() const;
    UInt32 AsUInt32() const;
    UInt64 AsUInt64() const;
    SInt8 AsInt8() const;
    SInt16 AsInt16() const;
    SInt32 AsInt32() const;
    SInt64 AsInt64() const;
    float AsFloat() const;
    double AsDouble() const;

    std::string AsString() const;
    std::string_view AsStringView() const;
    template<typename T, typename = std::enable_if_t<std::disjunction_v<
                 std::is_enum<T>,
                 std::is_arithmetic<T>,
                 std::is_same<T, bool>,
                 std::is_same<T, std::string>,
                 std::is_same<T, std::string_view>
             >>>
    T As() const;

    size_t Size() const;

    DeserializeNode Clone() const;

    /////////////////Array only interface////////////////
    template<typename T, typename = std::enable_if_t<std::is_integral_v<T>>>
    DeserializeNode operator[](T index) const;

    template<typename T, typename = std::enable_if_t<std::is_integral_v<T>>>
    DeserializeNode At(T index) const;
    ////////////////End array only//////////////////////

    ///////////////////Object only interface/////////////////
    template<size_t StrLen>
    DeserializeNode operator[](const char (&name)[StrLen]) const;
    DeserializeNode operator[](std::string_view name) const;

    template<size_t StrLen>
    std::optional<DeserializeNode> HasMember(const char (&name)[StrLen]) const;
    std::optional<DeserializeNode> HasMember(std::string_view name) const;

    template<size_t StrLen>
    std::optional<DeserializeNode> At(const char (&name)[StrLen]) const;
    std::optional<DeserializeNode> At(std::string_view name) const;

    //Try to find a member in Object, if not found, return defaultValue rather than throw error
    template<typename KeyType, typename ValType>
    ValType Value(KeyType name, const ValType& defaultValue) const;
    template<typename KeyType>
    const char* Value(KeyType name, const char* defaultValue) const;

    UniqueJsonNodeIt<true> begin() const;
    UniqueJsonNodeIt<true> end() const;
    ////////////////////End object only///////////////////////////
    
public:
    DeserializeNode();
    DeserializeNode(const DeserializeNode& node) = delete;
    
    //object type: another node
    DeserializeNode(DeserializeNode&& node) noexcept;

    ~DeserializeNode();
public:
    DeserializeNode& operator=(const DeserializeNode& node) = delete;
    DeserializeNode& operator=(DeserializeNode&& node) noexcept;

    bool operator==(const DeserializeNode& other) const;
    bool operator!=(const DeserializeNode& other) const;
public:
    std::string FormatToJson(bool pretty = true) const;
    std::vector<UInt8> FormatToBin() const;

    static DeserializeNode ParseFromBin(const UInt8* bin, size_t size);
    static DeserializeNode ParseFromJson(const std::string& json, bool* outSuccess = nullptr);
    static DeserializeNode ParseFromJsonStream(UniqueJsonNodeIStream & stream, bool* outSuccess = nullptr);

    //Compare the diff with this and base.
    //Return a node that contains the diff info on base parameter
    //SerializeNode node_a = {"a"_k = 1, "b"_k = 2, "c"_k = 3, "d"_k = 4, "arr"_k = {1,3}};
    //SerializeNode node_b = {"a"_k = 2, "c"_k = 3, "d"_k = 4, "e"_k = 5, "arr"_k = {1,2,3}};
    //SerializeNode node_c = a.DiffWith(b);
    // node_c is {
    //  "a" : 1,   //node_a has changed "a" to 1 based on node_b.
    //  "+b" : 2,  //node_a has added "b" based on node_b. "+" means add.
    //  "-e" : 5   //node_a has deleted "e" based on node_b. "-" means delete.
    //  "arr" : {"1" = 3, "-2" = 3} //node_a has changed "arr[1]" to 3 and deleted "arr[2]" based on node_b
    // }
    SerializeNode DiffWith(const DeserializeNode& base) const;

    //Is this diff an empty diff
    bool IsEmptyDiff() const;

    //If now I'm holding BinJson, returns true
    bool IsBinMode() const;

protected:
    enum NoAlloc_t { kNoAlloc };
    DeserializeNode(NoAlloc_t) : mData(std::in_place_type<JsonNode*>, nullptr), mOwnData(false) {}

    static constexpr auto& NO_DIFF_STR = "@@__There_is_no_diff_#";
    std::variant<JsonNode*, const dango::BinJson*> mData;
    bool mOwnData = true;

    DeserializeNode(JsonNode * node, bool ownData)
        : mData(node), mOwnData(ownData) 
    {
    }

    DeserializeNode(const dango::BinJson* node, int)
        : mData(node)
        , mOwnData(false) 
    {
    }


    JsonNode* JsonData() const;
    const dango::BinJson* BinData() const;
};

////////////////////////SerializeNode/////////////////////
class CEMeta(Editor) SerializeNode : public DeserializeNode
{
    friend struct UniqueJsonNodeKey;
    friend struct UniqueJsonNodeIt<false>;

public:
    SerializeNode Clone() const;

    /////////////////Array only interface////////////////
    template<typename T, typename = std::enable_if_t<std::is_integral_v<T>>>
    DeserializeNode operator[](T index) const;
    template<typename T, typename = std::enable_if_t<std::is_integral_v<T>>>
    SerializeNode operator[](T index);

    template<typename T, typename = std::enable_if_t<std::is_integral_v<T>>>
    DeserializeNode At(T index) const;
    template<typename T, typename = std::enable_if_t<std::is_integral_v<T>>>
    SerializeNode At(T index);

    // SerializeNode arr;
    // arr.PushBack(123);                    //int/float/double/bool/nullptr
    // SerializeNode some_node = "abc";
    // arr.PushBack(std::move(some_node));   //Use std::move
    void PushBack(SerializeNode&& node);
    void PopBack();
    ////////////////End array only//////////////////////

    
    ///////////////////Object only interface/////////////////
    template<size_t StrLen>
    DeserializeNode operator[](const char (&name)[StrLen]) const;
    template<size_t StrLen>
    SerializeNode operator[](const char (&name)[StrLen]);

    DeserializeNode operator[](std::string_view name) const;
    SerializeNode operator[](std::string_view name);

    
    template<size_t StrLen>
    std::optional<DeserializeNode> HasMember(const char (&name)[StrLen]) const;
    template<size_t StrLen>
    std::optional<SerializeNode> HasMember(const char (&name)[StrLen]);

    std::optional<DeserializeNode> HasMember(std::string_view name) const;
    std::optional<SerializeNode> HasMember(std::string_view name);
    std::optional<SerializeNode> HasMembers(std::vector<std::string_view> keys);

    template<size_t StrLen>
    std::optional<DeserializeNode> At(const char (&name)[StrLen]) const;
    template<size_t StrLen>
    std::optional<SerializeNode> At(const char (&name)[StrLen]);

    std::optional<DeserializeNode> At(std::string_view name) const;
    std::optional<SerializeNode> At(std::string_view name);

    void RemoveMember(std::string_view name);

    UniqueJsonNodeIt<true> begin() const;
    UniqueJsonNodeIt<false> begin();
    UniqueJsonNodeIt<true> end() const;
    UniqueJsonNodeIt<false> end();
    ////////////////////End object only///////////////////////////
public:
    SerializeNode()
        : DeserializeNode()
    {}
    SerializeNode(const SerializeNode& node);
    // basic type: number, bool, nullptr, const char*, char*
    template<typename T,
        typename = std::enable_if_t<std::disjunction_v<
            std::is_enum<T>,
            std::is_arithmetic<T>,
            std::is_same<T, bool>,
            std::is_same<T, std::nullptr_t>,
            std::is_same<T, std::string>,
            std::is_same<T, std::string_view>,
            std::is_same<T, const char*>>>>
    SerializeNode(T val);

    // basic type: constant string
    template<size_t StrLen>
    SerializeNode(const char (&str)[StrLen]);

    // object type: another node
    SerializeNode(SerializeNode&& node) noexcept;

    // object type: key-val pair
    SerializeNode(std::initializer_list<UniqueJsonNodeKey> list);

    // array type: array of nodes
    SerializeNode(std::initializer_list<SerializeNode> list);

    ~SerializeNode() = default;

    static SerializeNode EmptyArray();
    static SerializeNode EmptyObject();

    // Create a non const node from a const node.
    // If this is BinMode, convert BinJson to rapidjson, it's very expensive.
    // If this is JsonMode, clone the underlying rapidjson node. Same expensive as Clone()
    static SerializeNode CreateFromDeserializeNode(const DeserializeNode& deserializeNode);   // Notice!! May be EXPENSIVE!

public:
    SerializeNode& operator=(const SerializeNode& node) = delete;

    template<typename T, typename = std::enable_if_t<std::disjunction_v<
        std::is_enum<T>,
        std::is_arithmetic<T>,
        std::is_same<T, bool>,
        std::is_same<T, std::nullptr_t>,
        std::is_same<T, std::string>,
        std::is_same<T, std::string_view>,
        std::is_same<T, const char*>>>>
    SerializeNode& operator=(T val) noexcept;

    template<size_t StrLen>
    SerializeNode& operator=(const char (&val)[StrLen]);

    SerializeNode& operator=(SerializeNode&& node) noexcept;
public:
    static SerializeNode ParseFromJson(const std::string& json, bool* outSuccess = nullptr);
    static SerializeNode ParseFromJsonStream(UniqueJsonNodeIStream& stream, bool* outSuccess = nullptr);
    // Apply a diff to this.
    // SerializeNode node_a = ...
    // SerializeNode node_b = ...;
    // SerializeNode node_c = a.DiffWith(b);
    // SerializeNode node_d = node_b.Clone();
    // node_d.ApplyDiff(node_c);
    // Assert(node_d == node_a);
    void ApplyDiff(const DeserializeNode& diff);

    void ApplyDiffSafety(const DeserializeNode& diff);

    // If base changed a property, and I haven't overwritten that property, then I apply the change.
    // For example:
    // SerializeNode base = {"a"_k = 1, "b"_k = 2, "c"_k = 3, "d"_k = 4};
    // SerializeNode child ={"a"_k = 1, "b"_k = 999, "c"_k = 3, "d"_k = 455}; //b and d are overwritten
    // /*Now I make changes*/
    // SerializeNode newBase = {"a"_k = 233, "b"_k = 2, "c"_k = 3, "d"_k = 987}; //base.a and base.d change
    // child.PullChangeFromBase(base, newBase.DiffWith(base));
    // Now child is {a:233, b:999, c:3, d:455}
    // Why:
    // a is changed, and are not overwritten by child, so a also changes in result
    // b was overwritten by child, so it keeps it's value
    // c is not changed
    // d is overwritten by child, so even though it changes, it keeps it's value
    void PullChangeFromBase(const DeserializeNode& base, const DeserializeNode& baseDiff);
protected:
    SerializeNode(JsonNode * node, bool ownData)
        :DeserializeNode(node, ownData)
    {}

    SerializeNode(const dango::BinJson* node, int)
        : DeserializeNode(node, 0)
    {}
};

struct CEMeta(Editor) SerializeContext
{
public:
    SerializeNode data = {};

 public:
    SerializeContext() = default;
    SerializeContext(const SerializeContext&) = delete;
    SerializeContext& operator=(const SerializeContext& node) = delete;
};


////////////////////////////////////IMPLEMENTATIONS key///////////////////////////////////////////

struct UniqueJsonNodeKey
{
    // will not copy
    explicit UniqueJsonNodeKey(const char* name, size_t size)
#if USE_NLOHMANN_JSON
        : key(name)
#elif USE_RAPID_JSON
        : key(new SerializeNode::JsonNode(rapidjson::StringRef(name, size)), true)
#endif
    {}

    // will copy
    explicit UniqueJsonNodeKey(const char* name, size_t size, bool)
#if USE_NLOHMANN_JSON
        : key(name)
#endif
    {
#if USE_RAPID_JSON
        key = SerializeNode(new SerializeNode::JsonNode(rapidjson::StringRef(name, size), GetTLSFAllocator()), true);
#endif
    }

    UniqueJsonNodeKey(UniqueJsonNodeKey&& other) noexcept
        : key(std::move(other.key))
        , val(std::move(other.val))
    {}

    UniqueJsonNodeKey(UniqueJsonNodeKey& other)
        : key(std::move(other.key))
        , val(std::move(other.val))
    {}

    UniqueJsonNodeKey& operator=(SerializeNode&& node)
    {
        val = std::move(node);
        return *this;
    }

    UniqueJsonNodeKey& operator=(UniqueJsonNodeKey& other)
    {
        val = {other};
        return *this;
    }

    SerializeNode key;
    SerializeNode val;
};

inline UniqueJsonNodeKey operator""_k(const char* str, size_t size)
{
    return UniqueJsonNodeKey{str, size};
}

inline UniqueJsonNodeKey _N(std::string_view name, SerializeNode&& val)
{
    UniqueJsonNodeKey key{name.data(), name.length(), true};
    key = std::move(val);
    return key;
}

////////////////////////////////////////////////////////////////////////////

inline bool DeserializeNode::IsNull() const
{
    if (IsBinMode())
    {
        return BinData()->IsNull();
    }
    else
    {
#if USE_NLOHMANN_JSON
        return JsonData()->is_null();
#elif USE_RAPID_JSON
        return JsonData()->IsNull();
#endif
    }
}

inline bool DeserializeNode::IsBoolean() const
{
    if (IsBinMode())
    {
        return BinData()->IsBoolean();
    }
    else
    {
#if USE_NLOHMANN_JSON
        return JsonData()->is_boolean();
#elif USE_RAPID_JSON
        return JsonData()->IsBool();
#endif
    }
}

inline bool DeserializeNode::IsIntegral() const
{
    if (IsBinMode())
    {
        return BinData()->IsIntegral();
    }
    else
    {
#if USE_NLOHMANN_JSON
        return JsonData()->is_number_integer();
#elif USE_RAPID_JSON
        return JsonData()->IsInt() || JsonData()->IsInt64() || JsonData()->IsUint() || JsonData()->IsUint64();
#endif
    }
}

inline bool DeserializeNode::IsFloatingPoint() const
{
    if (IsBinMode())
    {
        return BinData()->IsFloatingPoint();
    }
    else
    {
#if USE_NLOHMANN_JSON
        return JsonData()->is_number_float();
#elif USE_RAPID_JSON
        return JsonData()->IsDouble();
#endif
    }
}

inline bool DeserializeNode::IsNumber() const
{
    if (IsBinMode())
    {
        return BinData()->IsNumber();
    }
    else
    {
#if USE_NLOHMANN_JSON
        return JsonData()->is_number();
#elif USE_RAPID_JSON
        return JsonData()->IsNumber();
#endif
    }
}

inline bool DeserializeNode::IsString() const
{
    if (IsBinMode())
    {
        return BinData()->IsString();
    }
    else
    {
#if USE_NLOHMANN_JSON
        return JsonData()->is_string();
#elif USE_RAPID_JSON
        return JsonData()->IsString();
#endif
    }
}

inline bool DeserializeNode::IsObject() const
{
    if (IsBinMode())
    {
        return BinData()->IsObject();
    }
    else
    {
#if USE_NLOHMANN_JSON
        return JsonData()->is_object();
#elif USE_RAPID_JSON
        return JsonData()->IsObject();
#endif
    }
}

inline bool DeserializeNode::IsArray() const
{
    if (IsBinMode())
    {
        return BinData()->IsArray();
    }
    else
    {
#if USE_NLOHMANN_JSON
        return JsonData()->is_array();
#elif USE_RAPID_JSON
        return JsonData()->IsArray();
#endif
    }
}

inline bool DeserializeNode::AsBoolean() const
{
    return As<bool>();
}

inline UInt8 DeserializeNode::AsUInt8() const
{
    return As<UInt8>();
}

inline UInt16 DeserializeNode::AsUInt16() const
{
    return As<UInt16>();
}

inline UInt32 DeserializeNode::AsUInt32() const
{
    return As<UInt32>();
}

inline UInt64 DeserializeNode::AsUInt64() const
{
    return As<UInt64>();
}

inline SInt8 DeserializeNode::AsInt8() const
{
    return As<SInt8>();
}

inline SInt16 DeserializeNode::AsInt16() const
{
    return As<SInt16>();
}

inline SInt32 DeserializeNode::AsInt32() const
{
    return As<SInt32>();
}

inline SInt64 DeserializeNode::AsInt64() const
{
    return As<SInt64>();
}

inline float DeserializeNode::AsFloat() const
{
    return As<float>();
}

inline double DeserializeNode::AsDouble() const
{
    return As<double>();
}

inline std::string DeserializeNode::AsString() const
{
    return As<std::string>();
}

inline std::string_view DeserializeNode::AsStringView() const
{
    return As<std::string_view>();
}

template<typename T, typename>
T DeserializeNode::As() const
{
    Assert(!IsNull());
    if (IsBinMode())
    {
        if constexpr (std::is_enum_v<T>)
        {
            return static_cast<T>(As<std::underlying_type_t<T>>());
        }
        else
        {
            return BinData()->template As<T>();
        }
    }
    else
    {
#if USE_NLOHMANN_JSON
        if constexpr (std::is_same_v<T, std::string_view>)
        {
            std::string* ptr = JsonData()->get_ptr<std::string*>();
            return std::string_view{ptr->c_str(), ptr->length()};
        }
        else
        {
            return JsonData()->get<T>();
        }
#elif USE_RAPID_JSON
        if constexpr (std::is_same_v<T, std::string>)
        {
            return std::string(JsonData()->GetString(), static_cast<size_t>(JsonData()->GetStringLength()));
        }
        else if constexpr (std::is_same_v<T, std::string_view>)
        {
            return std::string_view(JsonData()->GetString(), static_cast<size_t>(JsonData()->GetStringLength()));
        }
        else if constexpr (std::is_integral_v<T>)
        {
            if (JsonData()->IsBool())
                return static_cast<T>(JsonData()->GetBool());
            if (JsonData()->IsInt())
                return static_cast<T>(JsonData()->GetInt());
            if (JsonData()->IsUint())
                return static_cast<T>(JsonData()->GetUint());
            if (JsonData()->IsInt64())
                return static_cast<T>(JsonData()->GetInt64());
            if (JsonData()->IsUint64())
                return static_cast<T>(JsonData()->GetUint64());
            Assert(false);
            return 0;
        }
        else if constexpr (std::is_floating_point_v<T>)
        {
            return static_cast<T>(JsonData()->GetDouble());
        }
        else if constexpr (std::is_enum_v<T>)
        {
            return static_cast<T>(As<std::underlying_type_t<T>>());
        }
        else
        {
            return JsonData()->template Get<T>();
        }
#endif
    }
}

inline size_t DeserializeNode::Size() const
{
    if (IsBinMode())
    {
        return BinData()->Size();
    }
    else
    {
#if USE_NLOHMANN_JSON
        return JsonData()->size();
#elif USE_RAPID_JSON
        return JsonData()->Size();
#endif
    }
}

inline DeserializeNode DeserializeNode::Clone() const
{
    if (IsBinMode())
    {
        return DeserializeNode(BinData(), 0);
    }
    else
    {
        DeserializeNode ret(new JsonNode(), true);
#if USE_NLOHMANN_JSON
        *ret.JsonData() = *JsonData();
#elif USE_RAPID_JSON
        ret.JsonData()->CopyFrom(*JsonData(), GetTLSFAllocator());
#endif
        return ret;
    }
}

template<typename T, typename>
DeserializeNode DeserializeNode::operator[](T index) const
{
    Assert(IsArray());
    if (IsBinMode())
    {
        return DeserializeNode(&BinData()->At(index), 0);
    }
    else
    {
#if USE_RAPID_JSON
        auto& a = (*JsonData())[static_cast<rapidjson::SizeType>(index)];
#else
        auto& a = (*JsonData())[index];
#endif
        return DeserializeNode(&a, false);
    }
}

template<typename T, typename>
DeserializeNode DeserializeNode::At(T index) const
{
    return (*this)[index];
}

template<size_t StrLen>
DeserializeNode DeserializeNode::operator[](const char(& name)[StrLen]) const
{
    return (*this)[std::string_view{name, StrLen - 1}];
}

inline DeserializeNode DeserializeNode::operator[](std::string_view name) const
{
    auto result = HasMember(name);
    Assert(result.has_value());
    return std::move(*result);
}

template<size_t StrLen>
std::optional<DeserializeNode> DeserializeNode::HasMember(const char(& name)[StrLen]) const
{
    return HasMember(std::string_view{name, StrLen - 1});
}

inline std::optional<DeserializeNode> DeserializeNode::HasMember(std::string_view name) const
{
    if (IsNull())
        return {};

    if (IsBinMode())
    {
        Assert(IsObject());
        const auto it = BinData()->FindMember(name);
        if (it != BinData()->end())
            return DeserializeNode(&it.Value(), 0);
        return {};
    }
    else
    {
#if USE_NLOHMANN_JSON
        auto it = JsonData()->find(name.data());
        if (it != JsonData()->end())
        {
            return DeserializeNode(&(*it), false);
        }
        return {};
#elif USE_RAPID_JSON
        Assert(IsObject());
        const JsonNode n(name.data(), static_cast<rapidjson::SizeType>(name.length()));
        auto memberIt = JsonData()->FindMember(n);
        if (memberIt != JsonData()->MemberEnd())
        {
            return DeserializeNode(&memberIt->value, false);
        }
        return {};
#endif
    }
}

template<size_t StrLen>
std::optional<DeserializeNode> DeserializeNode::At(const char(& name)[StrLen]) const
{
    return (*this)[name];
}

inline std::optional<DeserializeNode> DeserializeNode::At(std::string_view name) const
{
    return (*this)[name];
}

template<typename KeyType, typename ValType>
ValType DeserializeNode::Value(KeyType name, const ValType& defaultValue) const
{
    auto hasVal = HasMember(std::move(name));
    if (hasVal.has_value())
    {
        return hasVal->template As<ValType>();
    }
    return defaultValue;
}

template<typename KeyType>
const char* DeserializeNode::Value(KeyType name, const char* defaultValue) const
{
    auto hasVal = HasMember(std::move(name));
    if (hasVal.has_value())
    {
        const std::string_view val = hasVal->AsStringView();
        return val.data();
    }
    return defaultValue;
}

inline UniqueJsonNodeIt<true> DeserializeNode::begin() const
{
    Assert(IsObject());
    if (IsBinMode())
    {
        return UniqueJsonNodeIt<true>{BinData()->begin()};
    }
    else
    {
#if USE_NLOHMANN_JSON
        return UniqueJsonNodeIt<true>{JsonData()->begin()};
#elif USE_RAPID_JSON
        return UniqueJsonNodeIt<true>{JsonData()->MemberBegin()};
#endif
    }
}

inline UniqueJsonNodeIt<true> DeserializeNode::end() const
{
    Assert(IsObject());
    if (IsBinMode())
    {
        return UniqueJsonNodeIt<true>{BinData()->end()};
    }
    else
    {
#if USE_NLOHMANN_JSON
        return UniqueJsonNodeIt<true>{JsonData()->end()};
#elif USE_RAPID_JSON
        return UniqueJsonNodeIt<true>{JsonData()->MemberEnd()};
#endif
    }
}

inline DeserializeNode::DeserializeNode()
{
    mData = new JsonNode();
}
inline DeserializeNode::DeserializeNode(DeserializeNode&& node) noexcept
{
    mOwnData = node.mOwnData;
    node.mOwnData = false;
    mData = node.mData;
    node.mData = static_cast<JsonNode*>(nullptr);
}

inline DeserializeNode::~DeserializeNode()
{
    if (mOwnData)
    {
        Assert(!IsBinMode());
        delete JsonData();
    }
    mOwnData = false;
    mData = static_cast<JsonNode*>(nullptr);
}

inline DeserializeNode& DeserializeNode::operator=(DeserializeNode&& node) noexcept
{
    if (mOwnData)
    {
        Assert(!IsBinMode());
        delete JsonData();
    }
    mOwnData = node.mOwnData;
    node.mOwnData = false;
    mData = node.mData;
    node.mData = static_cast<JsonNode*>(nullptr);
    return *this;
}

inline bool DeserializeNode::operator==(const DeserializeNode& other) const
{
    Assert((IsBinMode() && other.IsBinMode()) || (!IsBinMode() && !other.IsBinMode()));   // You can't compare between a BinNode and a JsonNode
    if (IsBinMode() && other.IsBinMode())
    {
        return *BinData() == *other.BinData();
    }
    else   //! a.IsBinMode() && !b.IsBinMode()
    {
        return *JsonData() == *other.JsonData();
    }
}

inline bool DeserializeNode::operator!=(const DeserializeNode& other) const
{
    return !(*this == other);
}

inline std::string DeserializeNode::FormatToJson(bool pretty) const
{
    Assert(!IsBinMode()); // This is a bin node, you can't format to json. Call SerializeNode::CreateFromDeserializeNode first
#if USE_NLOHMANN_JSON
    return JsonData()->dump();
#elif USE_RAPID_JSON
    rapidjson::StringBuffer buffer;
    if (pretty)
    {
        rapidjson::PrettyWriter<rapidjson::StringBuffer, rapidjson::UTF8<>, rapidjson::UTF8<>, rapidjson::CrtAllocator, rapidjson::kWriteNanAndInfFlag> writer(buffer);
        JsonData()->Accept(writer);
    }
    else
    {
        rapidjson::Writer<rapidjson::StringBuffer, rapidjson::UTF8<>, rapidjson::UTF8<>, rapidjson::CrtAllocator, rapidjson::kWriteNanAndInfFlag> writer(buffer);
        JsonData()->Accept(writer);
    }
    return std::string{buffer.GetString(), buffer.GetLength()};
#endif
}

inline std::vector<UInt8> DeserializeNode::FormatToBin() const
{
#if USE_NLOHMANN_JSON
    Assert(false);
    return {};
#elif USE_RAPID_JSON
    Assert(!IsBinMode());   // You are already a bin node, just use your data directly
    return dango::BinJson::SerializeFromRapidJson(*JsonData());
#endif
}

inline DeserializeNode DeserializeNode::ParseFromBin(const UInt8* bin, size_t size)
{
   return DeserializeNode(&dango::BinJson::FromBin(bin), 0);
}
inline DeserializeNode DeserializeNode::ParseFromJson(const std::string& json, bool* outSuccess)
{
    std::shared_ptr<TLSFDocument> doc1;
    doc1 = std::make_shared<TLSFDocument>(TLSFDocument(&GetTLSFAllocator()));       
    DeserializeNode ret;
    doc1->Parse<rapidjson::kParseNanAndInfFlag>(json.c_str(), json.length());
    if (!doc1->GetParseError())
    {
        *ret.JsonData() = doc1->Move();
        if (outSuccess)
            *outSuccess = true;
        return ret;
    }
    if (outSuccess)
        *outSuccess = false;
    return ret;
}

inline DeserializeNode DeserializeNode::ParseFromJsonStream(UniqueJsonNodeIStream& stream, bool* outSuccess)
{
    std::shared_ptr<TLSFDocument> doc1;

   doc1 = std::make_shared<TLSFDocument>(TLSFDocument(&GetTLSFAllocator()));

    DeserializeNode ret;
    struct RapidJsonStream
    {
        RapidJsonStream(UniqueJsonNodeIStream& stream)
            : stream(stream)
        {}

        typedef char Ch;

        Ch Peek() const
        {
            return stream.Peek();
        }

        Ch Take()
        {
            return stream.Take();
        }

        size_t Tell()
        {
            return stream.Tell();
        }

        Ch* PutBegin()   // no use for read stream
        {
            Assert(false);
            return nullptr;
        }

        void Put(Ch c)   // no use for read stream
        {
            Assert(false);
        }

        void Flush()   // no use for read stream
        {
            Assert(false);
        }

        size_t PutEnd(Ch* begin)   // no use for read stream
        {
            Assert(false);
            return 0;
        }

        UniqueJsonNodeIStream& stream;
    };
    RapidJsonStream rpStream{stream};
    doc1->ParseStream<rapidjson::kParseStopWhenDoneFlag | rapidjson::kParseNanAndInfFlag>(rpStream);
    if (!doc1->GetParseError())
    {
        *ret.JsonData() = doc1->Move();
        if (outSuccess)
            *outSuccess = true;
        return ret;
    }
    if (outSuccess)
        *outSuccess = false;
    return ret;
}

inline class SerializeNode DeserializeNode::DiffWith(const DeserializeNode& base) const
{
    SerializeNode diff;
    if (IsObject() && base.IsObject())   // object, go on traverse member
    {
        std::string tmpName;
        for (auto [key, val] : *this)
        {
            auto baseVal = base.HasMember(key);
            if (!baseVal)   // added
            {
                tmpName = "+";
                tmpName.append(key.data(), key.length());
                diff[tmpName] = SerializeNode::CreateFromDeserializeNode(val);
            }
            else   // changed
            {
                SerializeNode objDiff = val.DiffWith(*baseVal);
                if (!objDiff.IsEmptyDiff())
                {
                    diff[key] = std::move(objDiff);
                }
            }
        }
        for (auto [key, baseVal] : base)
        {
            auto val = this->HasMember(key);
            if (!val)   // deleted
            {
                tmpName = "-";
                tmpName.append(key.data(), key.length());
                diff[tmpName] = nullptr;
            }
        }
        if (diff.IsNull())
        {
            diff = NO_DIFF_STR;
        }
    }
    else if (IsArray() && base.IsArray())   // array, go on traverse item
    {
        std::string tmpName;
        const size_t size = Size() > base.Size() ? Size() : base.Size();
        for (int i = 0; i < static_cast<int>(size); ++i)
        {
            if (i >= static_cast<int>(base.Size()))   // added
            {
                tmpName = "+";
                tmpName += std::to_string(i);
                diff.PushBack(SerializeNode({"i"_k = tmpName, "v"_k = SerializeNode::CreateFromDeserializeNode((*this)[i])}));
            }
            else if (i >= static_cast<int>(Size()))   // deleted
            {
                tmpName = "-";
                tmpName += std::to_string(i);
                diff.PushBack(SerializeNode({"i"_k = tmpName, "v"_k = nullptr}));
                break;   // from this i on, all remain elements are removed
            }
            else
            {
                SerializeNode itemDiff = (*this)[i].DiffWith(base[i]);
                if (!itemDiff.IsEmptyDiff())
                {
                    diff.PushBack(SerializeNode({"i"_k = std::to_string(i), "v"_k = std::move(itemDiff)}));
                }
            }
        }
        if (diff.IsNull())
        {
            diff = NO_DIFF_STR;
        }
    }
    else
    {
        if (*this != base)
        {
            diff = SerializeNode::CreateFromDeserializeNode(*this);
        }
        else
        {
            diff = NO_DIFF_STR;
        }
    }
    return diff;
}

inline bool DeserializeNode::IsEmptyDiff() const
{
    return IsString() && AsStringView() == NO_DIFF_STR;
}

inline bool DeserializeNode::IsBinMode() const
{
    return mData.index() == 1;
}

inline DeserializeNode::JsonNode* DeserializeNode::JsonData() const
{
    Assert(mData.index() == 0);
    return std::get<0>(mData);
}

inline const dango::BinJson* DeserializeNode::BinData() const
{
    Assert(mData.index() == 1);
    return std::get<1>(mData);
}

inline SerializeNode SerializeNode::Clone() const
{
    SerializeNode ret(new JsonNode(), true);
#if USE_NLOHMANN_JSON
    *ret.JsonData() = *JsonData();
#elif USE_RAPID_JSON
    ret.JsonData()->CopyFrom(*JsonData(), GetTLSFAllocator());
#endif
    return ret;
}

template<typename T, typename>
DeserializeNode SerializeNode::operator[](T index) const
{
    return DeserializeNode::operator[](index);
}

template<typename T, typename>
SerializeNode SerializeNode::operator[](T index)
{
    Assert(!IsBinMode());
    Assert(IsArray());
#if USE_RAPID_JSON
    auto& a = (*JsonData())[static_cast<rapidjson::SizeType>(index)];
#else
    auto& a = (*JsonData())[index];
#endif
    return SerializeNode(&a, false);
}

template<typename T, typename>
DeserializeNode SerializeNode::At(T index) const
{
    return (*this)[index];
}

template<typename T, typename>
SerializeNode SerializeNode::At(T index)
{
    return (*this)[index];
}

inline void SerializeNode::PushBack(SerializeNode&& node)
{
    Assert(!IsBinMode());
#if USE_NLOHMANN_JSON
    JsonData()->push_back(std::move(*node.mData));
#elif USE_RAPID_JSON
    if (JsonData()->IsNull())
    {
        JsonData()->SetArray();
    }
    Assert(IsArray());
    JsonData()->PushBack(node.JsonData()->Move(), GetTLSFAllocator());
#endif
}
inline void SerializeNode::PopBack()
{
    Assert(!IsBinMode());
#if USE_NLOHMANN_JSON
    Assert(JsonData()->is_array() && JsonData()->size() > 0);
    JsonData()->erase(JsonData()->size() - 1);
#elif USE_RAPID_JSON
    Assert(JsonData()->IsArray() && Size() > 0);
    JsonData()->PopBack();
#endif
}

inline DeserializeNode SerializeNode::operator[](std::string_view name) const
{
    return DeserializeNode::operator[](name);
}

inline SerializeNode SerializeNode::operator[](std::string_view name)
{
    auto result = HasMember(name);
    if (!result)
    {
        Assert(!IsBinMode());
#if USE_NLOHMANN_JSON
        auto& a = (*JsonData())[name.data()];
        return SerializeNode(&a, false);
#elif USE_RAPID_JSON
        if (JsonData()->IsNull())
        {
            JsonData()->SetObject();
        }
        Assert(JsonData()->IsObject());
        {
            JsonNode nCopy(name.data(), static_cast<rapidjson::SizeType>(name.length()), GetTLSFAllocator());
            JsonData()->AddMember(nCopy, JsonNode{}, GetTLSFAllocator());
        }
        // TODO bug rapidjson::StringRef does not support std::string_view that is not null-terminated.
        auto memberIt = JsonData()->FindMember(JsonNode(name.data(), static_cast<rapidjson::SizeType>(name.length()), GetTLSFAllocator()));
        Assert(memberIt != JsonData()->MemberEnd());
        return SerializeNode{&memberIt->value, false};
#endif
    }
    else
    {
        return std::move(*result);
    }
}

template<size_t StrLen>
DeserializeNode SerializeNode::operator[](const char(& name)[StrLen]) const
{
    return (*this)[std::string_view{name, StrLen - 1}];
}

template<size_t StrLen>
SerializeNode SerializeNode::operator[](const char (&name)[StrLen])
{
    return (*this)[std::string_view{name, StrLen - 1}];
}

template<size_t StrLen>
std::optional<DeserializeNode> SerializeNode::HasMember(const char(& name)[StrLen]) const
{
    return HasMember(std::string_view{name, StrLen - 1});
}

template<size_t StrLen>
std::optional<SerializeNode> SerializeNode::HasMember(const char(& name)[StrLen])
{
    return HasMember(std::string_view{name, StrLen - 1});
}

inline std::optional<DeserializeNode> SerializeNode::HasMember(std::string_view name) const
{
    return DeserializeNode::HasMember(name);
}

inline std::optional<SerializeNode> SerializeNode::HasMember(std::string_view name)
{
    if (IsNull())
        return {};

    Assert(!IsBinMode());
#if USE_NLOHMANN_JSON
    auto it = JsonData()->find(name.data());
    if (it != JsonData()->end())
    {
        return SerializeNode(&(*it), false);
    }
    return {};
#elif USE_RAPID_JSON
    Assert(IsObject());
    const JsonNode n(name.data(), static_cast<rapidjson::SizeType>(name.length()));
    auto memberIt = JsonData()->FindMember(n);
    if (memberIt != JsonData()->MemberEnd())
    {
        return SerializeNode(&memberIt->value, false);
    }
    return {};
#endif
}

inline std::optional<SerializeNode> SerializeNode::HasMembers(std::vector<std::string_view> keys){
    auto node = HasMember(keys[0]);
    for (size_t i = 1; i < keys.size(); i++)
    {
        if (!node)
            break;
        node = node->HasMember(keys[i]);
    }
    return node;
}

inline std::optional<DeserializeNode> SerializeNode::At(std::string_view name) const
{
    return (*this)[name];
}

inline std::optional<SerializeNode> SerializeNode::At(std::string_view name)
{
    return (*this)[name];
}

inline void SerializeNode::RemoveMember(std::string_view name)
{
#if USE_NLOHMANN_JSON
    Assert(IsObject());
    JsonData()->erase(name.data());
#elif USE_RAPID_JSON
    Assert(IsObject());
    JsonData()->EraseMember(rapidjson::StringRef(name.data(), name.length()));
#endif
}

template<size_t StrLen>
std::optional<DeserializeNode> SerializeNode::At(const char(& name)[StrLen]) const
{
    return (*this)[name];
}

template<size_t StrLen>
std::optional<SerializeNode> SerializeNode::At(const char (&name)[StrLen])
{
    return (*this)[name];
}

inline UniqueJsonNodeIt<false> SerializeNode::begin()
{
    Assert(!IsBinMode());
#if USE_NLOHMANN_JSON
    return UniqueJsonNodeIt<false>{JsonData()->begin()};
#elif USE_RAPID_JSON
    return UniqueJsonNodeIt<false>{JsonData()->MemberBegin()};
#endif
}

inline UniqueJsonNodeIt<false> SerializeNode::end()
{
    Assert(!IsBinMode());
#if USE_NLOHMANN_JSON
    return UniqueJsonNodeIt<false>{JsonData()->end()};
#elif USE_RAPID_JSON
    return UniqueJsonNodeIt<false>{JsonData()->MemberEnd()};
#endif
}

template<typename T, typename>
SerializeNode::SerializeNode(T val)
    : DeserializeNode(kNoAlloc)
{
    if constexpr (std::is_same_v<T, std::nullptr_t>)
    {
        mData = new JsonNode(rapidjson::kNullType);
    }
    else if constexpr (std::is_same_v<T, std::string> || std::is_same_v<T, std::string_view>)
    {
        mData = new JsonNode(val.data(), static_cast<rapidjson::SizeType>(val.size()), GetTLSFAllocator());
    }
    else if constexpr (std::is_same_v<T, const char*>)
    {
        mData = new JsonNode(val, GetTLSFAllocator());
    }
    else if constexpr (std::is_enum_v<T>)
    {
         mData = new JsonNode(static_cast<std::underlying_type_t<T>>(val));
    }
    else
    {
       mData = new JsonNode(val);
    }
    mOwnData = true;
}

template<size_t StrLen>
SerializeNode::SerializeNode(const char(& str)[StrLen])
    : DeserializeNode(kNoAlloc)
{
#if USE_NLOHMANN_JSON
    mData = new JsonNode(str);
#elif USE_RAPID_JSON
    mData = new JsonNode(rapidjson::StringRef(str, StrLen - 1));
#endif
    mOwnData = true;
}

inline SerializeNode::SerializeNode(SerializeNode&& node) noexcept
{
    mOwnData = node.mOwnData;
    node.mOwnData = false;
    mData = node.mData;
    node.mData = static_cast<JsonNode*>(nullptr);
}
inline SerializeNode::SerializeNode(const SerializeNode& node) 
    : DeserializeNode(kNoAlloc)
{
    mOwnData = true;
    mData = new JsonNode();
#if USE_NLOHMANN_JSON
    *JsonData() = *node.JsonData();
#elif USE_RAPID_JSON
    JsonData()->CopyFrom(*node.JsonData(), GetTLSFAllocator());
#endif
}
inline SerializeNode::SerializeNode(std::initializer_list<UniqueJsonNodeKey> list)
    : DeserializeNode(kNoAlloc)
    {
#if USE_NLOHMANN_JSON
    mData = new JsonNode();
#elif USE_RAPID_JSON
    mData = new JsonNode(rapidjson::kObjectType);
#endif
    mOwnData = true;
    for (const auto& node : list)
    {
#if USE_NLOHMANN_JSON
        (*JsonData())[node.key.AsStringView().data()] = std::move(*node.val.JsonData());
#elif USE_RAPID_JSON
       JsonData()->AddMember(*node.key.JsonData(), *node.val.JsonData(), GetTLSFAllocator());
#endif
    }
}

inline SerializeNode::SerializeNode(std::initializer_list<SerializeNode> list)
    : DeserializeNode(kNoAlloc)
{
#if USE_NLOHMANN_JSON
    mData = new JsonNode();
#elif USE_RAPID_JSON
    mData = new JsonNode(rapidjson::kArrayType);
#endif
    mOwnData = true;
    for (const auto& node : list)
    {
#if USE_NLOHMANN_JSON
        JsonData()->emplace_back(std::move(*node.JsonData()));
#elif USE_RAPID_JSON
    JsonData()->PushBack(node.JsonData()->Move(), GetTLSFAllocator());
#endif
    }
}

inline SerializeNode SerializeNode::EmptyArray()
{
#if USE_NLOHMANN_JSON
    JsonNode* data = new JsonNode(nlohmann::detail::value_t::array);
#elif USE_RAPID_JSON
    JsonNode* data = new JsonNode(rapidjson::kArrayType);
#endif
    return SerializeNode(data, true);
}

inline SerializeNode SerializeNode::EmptyObject()
{
#if USE_NLOHMANN_JSON
    JsonNode* data = new JsonNode(nlohmann::detail::value_t::object);
#elif USE_RAPID_JSON
    JsonNode* data = new JsonNode(rapidjson::kObjectType);
#endif
    return SerializeNode(data, true);
}

inline SerializeNode SerializeNode::CreateFromDeserializeNode(const DeserializeNode& deserializeNode)
{
    if (deserializeNode.IsBinMode())
    {
        SerializeNode ret;
#if USE_NLOHMANN_JSON
        Assert(false);   // not implement
#elif USE_RAPID_JSON
        *ret.JsonData() = deserializeNode.BinData()->ConvertToRapidJson<JsonNode::EncodingType, JsonNode::AllocatorType>(GetTLSFAllocator());
#endif
        return ret;
    }
    else
    {
        SerializeNode ret;
#if USE_NLOHMANN_JSON
        *ret.JsonData() = *deserializeNode.JsonData();
#elif USE_RAPID_JSON
        ret.JsonData()->CopyFrom(*deserializeNode.JsonData(), GetTLSFAllocator());
#endif
        return ret;
    }
}

template<typename T, typename>
SerializeNode& SerializeNode::operator=(T val) noexcept
{
    Assert(!IsBinMode());
    if (!JsonData())
    {
        mData = new JsonNode();
        mOwnData = true;
    }
    if constexpr (std::is_same_v<T, const char*> || std::is_same_v<T, char*>)
    {
#if USE_NLOHMANN_JSON
        *JsonData() = val;
#elif USE_RAPID_JSON
        JsonData()->SetString(val, GetTLSFAllocator());
#endif
    }
    else if constexpr (std::is_same_v<T, std::string> || std::is_same_v<T, std::string_view>)
    {
#if USE_NLOHMANN_JSON
        *JsonData() = val.data();
#elif USE_RAPID_JSON
       JsonData()->SetString(val.data(), static_cast<rapidjson::SizeType>(val.size()), GetTLSFAllocator());
#endif
    }
    else if constexpr (std::is_same_v<T, std::nullptr_t>)
    {
#if USE_NLOHMANN_JSON
        *JsonData() = nullptr;
#elif USE_RAPID_JSON
        JsonData()->SetNull();
#endif
    }
    else if constexpr (std::is_enum_v<T>)
    {
        *JsonData() = static_cast<std::underlying_type_t<T>>(val);
    }
    else
    {
        *JsonData() = val;
    }
    return *this;
}

template<size_t StrLen>
SerializeNode& SerializeNode::operator=(const char(& val)[StrLen])
{
    Assert(!IsBinMode());
    if (!JsonData())
    {
        mData = new JsonNode();
        mOwnData = true;
    }
    *JsonData() = val;
    return *this;
}

inline SerializeNode& SerializeNode::operator=(SerializeNode&& node) noexcept
{
    Assert(!IsBinMode() && !node.IsBinMode());
    if (!JsonData())
    {
        mData = new JsonNode();
        mOwnData = true;
    }
    *JsonData() = std::move(*node.JsonData());
    return *this;
}

inline SerializeNode SerializeNode::ParseFromJson(const std::string& json, bool* outSuccess)
{
    DeserializeNode des = DeserializeNode::ParseFromJson(json, outSuccess);
    Assert(!des.IsBinMode() && des.JsonData() && des.mOwnData);
    SerializeNode ser(des.JsonData(), true);
    des.mOwnData = false;
    des.mData = static_cast<JsonNode*>(nullptr);
    return ser;
}

inline SerializeNode SerializeNode::ParseFromJsonStream(UniqueJsonNodeIStream& stream, bool* outSuccess)
{
    DeserializeNode des = DeserializeNode::ParseFromJsonStream(stream, outSuccess);
    Assert(!des.IsBinMode() && des.JsonData() && des.mOwnData);
    SerializeNode ser(des.JsonData(), true);
    des.mOwnData = false;
    des.mData = static_cast<JsonNode*>(nullptr);
    return ser;
}

inline void SerializeNode::ApplyDiff(const DeserializeNode& diff)
{
    //I'm basic type, so I change to diff directly
    //I'm an object but diff is not, which means type changing
    //I'm an array but diff is not, which means type changing 
    if (!(IsObject() && diff.IsObject()) && !(IsArray() && diff.IsArray()))
    {
        if (!diff.IsEmptyDiff())
        {
            *this = SerializeNode::CreateFromDeserializeNode(diff);
        }
    }
    else
    {
        Assert(IsArray() || IsObject());
        if (IsObject())
        {
            Assert(diff.IsObject());
            for (auto [key, val] : diff)
            {
                if (key[0] == '+')
                {
                    std::string_view name(key.data() + 1, key.length() - 1);
                    Assert(!HasMember(name).has_value());
                    (*this)[name] = SerializeNode::CreateFromDeserializeNode(val);
                }
                else if (key[0] == '-')
                {
                    std::string_view name(key.data() + 1, key.length() - 1);
                    Assert(HasMember(name).has_value());
                    RemoveMember(name);
                }
                else
                {
                    std::string_view name(key.data(), key.length());
                    auto myNode = HasMember(name);
                    Assert(myNode.has_value());
                    myNode->ApplyDiff(std::move(val));
                }
            }
        }
        else //(IsArray())
        {
            Assert(diff.IsArray());
            static auto stoi = [](const char* str) -> int {
                int ret = 0;
                for (; *str; ++str)
                {
                    ret = ret * 10 + (*str - '0');
                }
                return ret;
            };
            for (int i = 0; i < static_cast<int>(diff.Size()); ++i)
            {
                DeserializeNode currentNode = diff[i];
                Assert(currentNode.IsObject());
                auto arrIOpti = currentNode.HasMember("i");
                auto arrVOpti = currentNode.HasMember("v");
                Assert(arrIOpti.has_value() && arrVOpti.has_value());
                std::string_view arrIStrRef = arrIOpti->AsStringView();
                if (arrIStrRef[0] == '+')
                {
                    int arrI = stoi(arrIStrRef.data() + 1);
                    Assert(Size() == static_cast<size_t>(arrI));
                    PushBack(SerializeNode::CreateFromDeserializeNode(*arrVOpti));
                }
                else if (arrIStrRef[0] == '-')
                {
                    int arrI = stoi(arrIStrRef.data() + 1);
                    Assert(Size() > static_cast<size_t>(arrI));
#if USE_NLOHMANN_JSON
                        JsonData()->erase(JsonData()->begin() + arrI, JsonData()->end());
#elif USE_RAPID_JSON
                    JsonData()->Erase(JsonData()->Begin() + arrI, JsonData()->End());
#endif
                    //'-' means all later elements are removed, so it should be the last one in diff
                    Assert(static_cast<size_t>(i + 1) == diff.Size());
                }
                else
                {
                    int arrI = stoi(arrIStrRef.data());
                    Assert(Size() > static_cast<size_t>(arrI));
                    (*this)[arrI].ApplyDiff(*arrVOpti);
                }
            }
        }
    }
}

inline void SerializeNode::ApplyDiffSafety(const DeserializeNode& diff)
{
    // I'm basic type, so I change to diff directly
    // I'm an object but diff is not, which means type changing
    // I'm an array but diff is not, which means type changing
    if (!(IsObject() && diff.IsObject()) && !(IsArray() && diff.IsArray()))
    {
        if (!diff.IsEmptyDiff())
        {
            *this = SerializeNode::CreateFromDeserializeNode(diff);
        }
    }
    else
    {
        Assert(IsArray() || IsObject());
        if (IsObject())
        {
            Assert(diff.IsObject());
            for (auto [key, val] : diff)
            {
                if (key[0] == '+')
                {
                    std::string_view name(key.data() + 1, key.length() - 1);
                    (*this)[name] = SerializeNode::CreateFromDeserializeNode(val);
                }
                else if (key[0] == '-')
                {
                    std::string_view name(key.data() + 1, key.length() - 1);
                    if (HasMember(name))
                    {
                        RemoveMember(name);
                    }
                }
                else
                {
                    std::string_view name(key.data(), key.length());
                    auto myNode = HasMember(name);
                    if (myNode.has_value())
                    {
                        myNode->ApplyDiffSafety(std::move(val));
                    }
                    else
                    {
                        (*this)[name] = SerializeNode::CreateFromDeserializeNode(val);
                    }
                }
            }
        }
        else   //(IsArray())
        {
            Assert(diff.IsArray());
            static auto stoi = [](const char* str) -> int {
                int ret = 0;
                for (; *str; ++str)
                {
                    ret = ret * 10 + (*str - '0');
                }
                return ret;
            };
            for (int i = 0; i < static_cast<int>(diff.Size()); ++i)
            {
                DeserializeNode currentNode = diff[i];
                Assert(currentNode.IsObject());
                auto arrIOpti = currentNode.HasMember("i");
                auto arrVOpti = currentNode.HasMember("v");
                Assert(arrIOpti.has_value() && arrVOpti.has_value());
                std::string_view arrIStrRef = arrIOpti->AsStringView();
                if (arrIStrRef[0] == '+')
                {
                    PushBack(SerializeNode::CreateFromDeserializeNode(*arrVOpti));
                }
                else if (arrIStrRef[0] == '-')
                {
                    int arrI = stoi(arrIStrRef.data() + 1);
                    if (Size() > static_cast<size_t>(arrI))
                    {
#if USE_NLOHMANN_JSON
                        JsonData()->erase(JsonData()->begin() + arrI, JsonData()->end());
#elif USE_RAPID_JSON
                        JsonData()->Erase(JsonData()->Begin() + arrI, JsonData()->End());
#endif
                    }
                }
                else
                {
                    int arrI = stoi(arrIStrRef.data());
                    if (Size() > static_cast<size_t>(arrI))
                    {
                        (*this)[arrI].ApplyDiffSafety(*arrVOpti);
                    }
                }
            }
        }
    }
}


inline void SerializeNode::PullChangeFromBase(const DeserializeNode& base, const DeserializeNode& baseDiff)
{
    if (!(IsArray() && base.IsArray() && baseDiff.IsArray()) && !(IsObject() && base.IsObject() && baseDiff.IsObject()))
    {
        // if I == base, I change to diff directly.If not, I keep my value
        if ((*this) == base && !baseDiff.IsEmptyDiff())
        {
            *this = SerializeNode::CreateFromDeserializeNode(baseDiff);
        }
    }
    else
    {
        if (IsObject())
        {
            Assert(base.IsObject() && baseDiff.IsObject());
            for (auto [key, val] : baseDiff)
            {
                if (key[0] == '+')
                {
                    std::string_view name(key.data() + 1, key.length() - 1);
                    if (!HasMember(name).has_value())
                    {
                        (*this)[name] = SerializeNode::CreateFromDeserializeNode(val);
                    }
                }
                else if (key[0] == '-')
                {
                    std::string_view name(key.data() + 1, key.length() - 1);
                    auto baseMember = base.HasMember(name);
                    Assert(baseMember.has_value());
                    auto member = HasMember(name);
                    if (member.has_value() && *member == *baseMember)
                    {
                        RemoveMember(name);
                    }
                }
                else
                {
                    std::string_view name(key.data(), key.length());
                    auto baseNode = base.HasMember(name);
                    Assert(baseNode.has_value());
                    auto myNode = HasMember(name);
                    if (myNode.has_value())
                    {
                        myNode->PullChangeFromBase(*baseNode, SerializeNode::CreateFromDeserializeNode(val));
                    }
                }
            }
        }
        else //(IsArray)
        {
            Assert(base.IsArray() && baseDiff.IsArray());
            if ((*this) == base) // to avoid situation that: base=[1,2,3] child=[1,3,4]. I can't distinguish 3
            {
                static auto stoi = [](const char* str) -> int {
                    int ret = 0;
                    for (; *str; ++str)
                    {
                        ret = ret * 10 + (*str - '0');
                    }
                    return ret;
                };
                for (int i = 0; i < static_cast<int>(baseDiff.Size()); ++i)
                {
                    auto currentNode = baseDiff[i];
                    Assert(currentNode.IsObject());
                    auto arrIOpti = currentNode.HasMember("i");
                    auto arrVOpti = currentNode.HasMember("v");
                    Assert(arrIOpti.has_value() && arrVOpti.has_value());
                    std::string_view arrIStrRef = arrIOpti->AsStringView();
                    if (arrIStrRef[0] == '+')
                    {
                        int arrI = stoi(arrIStrRef.data() + 1);
                        Assert(Size() == static_cast<size_t>(arrI));
                        PushBack(SerializeNode::CreateFromDeserializeNode(*arrVOpti));
                    }
                    else if (arrIStrRef[0] == '-')
                    {
                        int arrI = stoi(arrIStrRef.data() + 1);
                        Assert(Size() > static_cast<size_t>(arrI));
#if USE_NLOHMANN_JSON
                            JsonData()->erase(JsonData()->begin() + arrI, JsonData()->end());
#elif USE_RAPID_JSON
                        JsonData()->Erase(JsonData()->Begin() + arrI, JsonData()->End());
#endif
                        //'-' means all later elements are removed, so it should be the last one in diff
                        Assert(static_cast<size_t>(i + 1) == baseDiff.Size());
                    }
                    else
                    {
                        int arrI = stoi(arrIStrRef.data());
                        Assert(Size() > static_cast<size_t>(arrI));
                        (*this)[arrI].PullChangeFromBase(base[arrI], *arrVOpti);
                    }
                }
            }
        }
    }
}

///////////////////////////////////////////////UniqueJsonNodeIt//////////////////////////////////////

template<bool IsConst>
bool UniqueJsonNodeIt<IsConst>::IsJson() const
{
    return it.index() == 0;
}

template<bool IsConst>
const typename UniqueJsonNodeIt<IsConst>::JsonIter& UniqueJsonNodeIt<IsConst>::JsonIt() const
{
    Assert(IsJson());
    return std::get<0>(it);
}

template<bool IsConst>
typename UniqueJsonNodeIt<IsConst>::JsonIter& UniqueJsonNodeIt<IsConst>::JsonIt()
{
    Assert(IsJson());
    return std::get<0>(it);
}

template<bool IsConst>
const dango::BinJsonIt& UniqueJsonNodeIt<IsConst>::BinIt() const
{
    Assert(!IsJson());
    return std::get<1>(it);
}

template<bool IsConst>
dango::BinJsonIt& UniqueJsonNodeIt<IsConst>::BinIt()
{
    Assert(!IsJson());
    return std::get<1>(it);
}


template<bool IsConst>
std::string_view UniqueJsonNodeIt<IsConst>::Key() const
{
    if (IsJson())
    {
#if USE_NLOHMANN_JSON
        const std::string& str = JsonIt().key();
        return str;
#elif USE_RAPID_JSON
        return std::string_view{JsonIt()->name.GetString(), JsonIt()->name.GetStringLength()};
#endif
    }
    else
    {
        return BinIt().Key();
    }
}

template<bool IsConst>
typename UniqueJsonNodeIt<IsConst>::ValueType UniqueJsonNodeIt<IsConst>::Value() const
{
    if (IsJson())
    {
#if USE_NLOHMANN_JSON
        return ValueType(&JsonIt().value(), 0);
#elif USE_RAPID_JSON
        return ValueType(&JsonIt()->value, false);
#endif
    }
    else
    {
        return ValueType(&BinIt().Value(), 0);
    }
}

template<bool IsConst>
bool UniqueJsonNodeIt<IsConst>::operator==(const UniqueJsonNodeIt<IsConst>& other) const
{
    Assert((IsJson() && other.IsJson()) || (!IsJson() && !other.IsJson()));

    if (IsJson())
        return JsonIt() == other.JsonIt();
    return BinIt() == other.BinIt();
}

template<bool IsConst>
bool UniqueJsonNodeIt<IsConst>::operator!=(const UniqueJsonNodeIt<IsConst>& other) const
{
    return !(*this == other);
}

template<bool IsConst>
UniqueJsonNodeIt<IsConst>& UniqueJsonNodeIt<IsConst>::operator++()
{
    if (IsJson())
        ++JsonIt();
    else
        ++BinIt();
    return *this;
}

template<bool IsConst>
UniqueJsonNodeIt<IsConst> UniqueJsonNodeIt<IsConst>::operator++(int a)
{
    UniqueJsonNodeIt old = *this;
    ++(*this);
    return old;
}

template<bool IsConst>
std::tuple<std::string_view, typename UniqueJsonNodeIt<IsConst>::ValueType> UniqueJsonNodeIt<IsConst>::operator*() const
{
    return std::tuple<std::string_view, ValueType>(Key(), Value());
}
} 