#include "PCH/CrossBasePCHPrivate.h"
#include "Locale/UTF8.h"

#include <locale>
#include <codecvt>

namespace cross
{
    typedef unsigned long	UTF32;	/* at least 32 bits */
    typedef unsigned short	UTF16;	/* at least 16 bits */
    typedef unsigned char	UTF8;	/* typically 8 bits */
    typedef unsigned char	<PERSON><PERSON><PERSON>; /* 0 or 1 */

    typedef enum
    {
        strictConversion = 0,
        lenientConversion
    } ConversionFlags;

    typedef enum
    {
        conversionOK, 		/* conversion successful */
        sourceExhausted,	/* partial character in source, but hit end */
        targetExhausted,	/* insuff. room in target for conversion */
        sourceIllegal		/* source sequence is illegal/malformed */
    } ConversionResult;

    /* Some fundamental constants */
#define UNI_REPLACEMENT_CHAR (UTF32)0x0000FFFD
#define UNI_MAX_BMP (UTF32)0x0000FFFF
#define UNI_MAX_UTF16 (UTF32)0x0010FFFF
#define UNI_MAX_UTF32 (UTF32)0x7FFFFFFF
#define UNI_MAX_LEGAL_UTF32 (UTF32)0x0010FFFF

    ConversionResult ConvertUTF8toUTF16(
        const UTF8** sourceStart, const UTF8* sourceEnd,
        UTF16** targetStart, UTF16* targetEnd, ConversionFlags flags);

    /*
    * Copyright 2001-2004 Unicode, Inc.
    *
    * Disclaimer
    *
    * This source code is provided as is by Unicode, Inc. No claims are
    * made as to fitness for any particular purpose. No warranties of any
    * kind are expressed or implied. The recipient agrees to determine
    * applicability of information provided. If this file has been
    * purchased on magnetic or optical media from Unicode, Inc., the
    * sole remedy for any claim will be exchange of defective media
    * within 90 days of receipt.
    *
    * Limitations on Rights to Redistribute This Code
    *
    * Unicode, Inc. hereby grants the right to freely use the information
    * supplied in this file in the creation of products supporting the
    * Unicode Standard, and to make copies of this file in any form
    * for internal or external distribution as long as this notice
    * remains attached.
    */

    /* ---------------------------------------------------------------------

    Conversions between UTF32, UTF-16, and UTF-8. Source code file.
    Author: Mark E. Davis, 1994.
    Rev History: Rick McGowan, fixes & updates May 2001.
    Sept 2001: fixed const & error conditions per
    mods suggested by S. Parent & A. Lillich.
    June 2002: Tim Dodd added detection and handling of incomplete
    source sequences, enhanced error detection, added casts
    to eliminate compiler warnings.
    July 2003: slight mods to back out aggressive FFFE detection.
    Jan 2004: updated switches in from-UTF8 conversions.
    Oct 2004: updated to use UNI_MAX_LEGAL_UTF32 in UTF-32 conversions.

    See the header file "ConvertUTF.h" for complete documentation.

    ------------------------------------------------------------------------ */

#ifdef CVTUTF_DEBUG
#include <stdio.h>
#endif

    static const int halfShift = 10; /* used for shifting by 10 bits */

    static const UTF32 halfBase = 0x0010000UL;
    static const UTF32 halfMask = 0x3FFUL;

#define UNI_SUR_HIGH_START  (UTF32)0xD800
#define UNI_SUR_HIGH_END    (UTF32)0xDBFF
#define UNI_SUR_LOW_START   (UTF32)0xDC00
#define UNI_SUR_LOW_END     (UTF32)0xDFFF


    /* --------------------------------------------------------------------- */

    /*
    * Index into the table below with the first byte of a UTF-8 sequence to
    * get the number of trailing bytes that are supposed to follow it.
    * Note that *legal* UTF-8 values can't have 4 or 5-bytes. The table is
    * left as-is for anyone who may want to do such conversion, which was
    * allowed in earlier algorithms.
    */
    static const char trailingBytesForUTF8[256] = {
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
        2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5
    };

    /*
    * Magic values subtracted from a buffer value during UTF8 conversion.
    * This table contains as many values as there might be trailing bytes
    * in a UTF-8 sequence.
    */
    static const UTF32 offsetsFromUTF8[6] = { 0x00000000UL, 0x00003080UL, 0x000E2080UL,
    0x03C82080UL, 0xFA082080UL, 0x82082080UL };

    /*
    * Once the bits are split out into bytes of UTF-8, this is a mask OR-ed
    * into the first byte, depending on how many bytes follow.  There are
    * as many entries in this table as there are UTF-8 sequence types.
    * (I.e., one byte sequence, two byte... etc.). Remember that sequencs
    * for *legal* UTF-8 will be 4 or fewer bytes total.
    */
    static const UTF8 firstByteMark[7] = { 0x00, 0x00, 0xC0, 0xE0, 0xF0, 0xF8, 0xFC };


    /* --------------------------------------------------------------------- */

    /*
    * Utility routine to tell whether a sequence of bytes is legal UTF-8.
    * This must be called with the length pre-determined by the first byte.
    * If not calling this from ConvertUTF8to*, then the length can be set by:
    *  length = trailingBytesForUTF8[*source]+1;
    * and the sequence is illegal right away if there aren't that many bytes
    * available.
    * If presented with a length > 4, this returns false.  The Unicode
    * definition of UTF-8 goes up to 4-byte sequences.
    */

    static Boolean isLegalUTF8(const UTF8 *source, int length) {
        UTF8 a;
        const UTF8 *srcptr = source + length;
        switch (length) {
        default: return false;
            /* Everything else falls through when "true"... */
        case 4: if ((a = (*--srcptr)) < 0x80 || a > 0xBF) return false;
        case 3: if ((a = (*--srcptr)) < 0x80 || a > 0xBF) return false;
        case 2: if ((a = (*--srcptr)) > 0xBF) return false;

            switch (*source) {
                /* no fall-through in this inner switch */
            case 0xE0: if (a < 0xA0) return false; break;
            case 0xED: if (a > 0x9F) return false; break;
            case 0xF0: if (a < 0x90) return false; break;
            case 0xF4: if (a > 0x8F) return false; break;
            default:   if (a < 0x80) return false;
            }

        case 1: if (*source >= 0x80 && *source < 0xC2) return false;
        }
        if (*source > 0xF4) return false;
        return true;
    }


    /* --------------------------------------------------------------------- */

    ConversionResult ConvertUTF8toUTF16(
        const UTF8** sourceStart, const UTF8* sourceEnd,
        UTF16** targetStart, UTF16* targetEnd, ConversionFlags flags) {
        ConversionResult result = conversionOK;
        const UTF8* source = *sourceStart;
        UTF16* target = *targetStart;
        while (source < sourceEnd) {
            UTF32 ch = 0;
            unsigned short extraBytesToRead = trailingBytesForUTF8[*source];
            if (source + extraBytesToRead >= sourceEnd) {
                result = sourceExhausted; break;
            }
            /* Do this check whether lenient or strict */
            if (!isLegalUTF8(source, extraBytesToRead + 1)) {
                result = sourceIllegal;
                break;
            }
            /*
            * The cases all fall through. See "Note A" below.
            */
            switch (extraBytesToRead) {
            case 5: ch += *source++; ch <<= 6; /* remember, illegal UTF-8 */
            case 4: ch += *source++; ch <<= 6; /* remember, illegal UTF-8 */
            case 3: ch += *source++; ch <<= 6;
            case 2: ch += *source++; ch <<= 6;
            case 1: ch += *source++; ch <<= 6;
            case 0: ch += *source++;
            }
            ch -= offsetsFromUTF8[extraBytesToRead];

            assert(target < targetEnd);

            if (ch <= UNI_MAX_BMP) { /* Target is a character <= 0xFFFF */
                /* UTF-16 surrogate values are illegal in UTF-32 */
                if (ch >= UNI_SUR_HIGH_START && ch <= UNI_SUR_LOW_END) {
                    if (flags == strictConversion) {
                        source -= (extraBytesToRead + 1); /* return to the illegal value itself */
                        result = sourceIllegal;
                        break;
                    }
                    else {
                        *target++ = UNI_REPLACEMENT_CHAR;
                    }
                }
                else {
                    *target++ = (UTF16)ch; /* normal case */
                }
            }
            else if (ch > UNI_MAX_UTF16) {
                if (flags == strictConversion) {
                    result = sourceIllegal;
                    source -= (extraBytesToRead + 1); /* return to the start */
                    break; /* Bail out; shouldn't continue */
                }
                else {
                    *target++ = UNI_REPLACEMENT_CHAR;
                }
            }
            else {
                /* target is a character in range 0xFFFF - 0x10FFFF. */
                if (target + 1 >= targetEnd) {
                    source -= (extraBytesToRead + 1); /* Back up source pointer! */
                    result = targetExhausted; break;
                }
                ch -= halfBase;
                *target++ = (UTF16)((ch >> halfShift) + UNI_SUR_HIGH_START);
                *target++ = (UTF16)((ch & halfMask) + UNI_SUR_LOW_START);
            }
        }
        *sourceStart = source;
        *targetStart = target;
        return result;
    }

    /* ---------------------------------------------------------------------

    Note A.
    The fall-through switches in UTF-8 reading code save a
    temp variable, some decrements & conditionals.  The switches
    are equivalent to the following loop:
    {
    int tmpBytesToRead = extraBytesToRead+1;
    do {
    ch += *source++;
    --tmpBytesToRead;
    if (tmpBytesToRead) ch <<= 6;
    } while (tmpBytesToRead > 0);
    }
    In UTF-8 writing code, the switches on "bytesToWrite" are
    similarly unrolled loops.

    --------------------------------------------------------------------- */

    ConversionResult ConvertUTF16toUTF8(
        const UTF16** sourceStart, const UTF16* sourceEnd,
        UTF8** targetStart, UTF8* targetEnd, ConversionFlags flags) {
        ConversionResult result = conversionOK;
        const UTF16* source = *sourceStart;
        UTF8* target = *targetStart;
        while (source < sourceEnd) {
            UTF32 ch;
            unsigned short bytesToWrite = 0;
            const UTF32 byteMask = 0xBF;
            const UTF32 byteMark = 0x80;
            const UTF16* oldSource = source; /* In case we have to back up because of target overflow. */
            ch = *source++;
            /* If we have a surrogate pair, convert to UTF32 first. */
            if (ch >= UNI_SUR_HIGH_START && ch <= UNI_SUR_HIGH_END && source < sourceEnd) {
                UTF32 ch2 = *source;
                if (ch2 >= UNI_SUR_LOW_START && ch2 <= UNI_SUR_LOW_END) {
                    ch = ((ch - UNI_SUR_HIGH_START) << halfShift)
                        + (ch2 - UNI_SUR_LOW_START) + halfBase;
                    ++source;
                }
                else if (flags == strictConversion) { /* it's an unpaired high surrogate */
                    --source; /* return to the illegal value itself */
                    result = sourceIllegal;
                    break;
                }
            }
            else if ((flags == strictConversion) && (ch >= UNI_SUR_LOW_START && ch <= UNI_SUR_LOW_END)) {
                --source; /* return to the illegal value itself */
                result = sourceIllegal;
                break;
            }
            /* Figure out how many bytes the result will require */
            if (ch < (UTF32)0x80) {
                bytesToWrite = 1;
            }
            else if (ch < (UTF32)0x800) {
                bytesToWrite = 2;
            }
            else if (ch < (UTF32)0x10000) {
                bytesToWrite = 3;
            }
            else if (ch < (UTF32)0x200000) {
                bytesToWrite = 4;
            }
            else {
                bytesToWrite = 2;
                ch = UNI_REPLACEMENT_CHAR;
            }

            target += bytesToWrite;
            if (target > targetEnd) {
                source = oldSource; /* Back up source pointer! */
                target -= bytesToWrite; result = targetExhausted; break;
            }
            switch (bytesToWrite) {	/* note: everything falls through. */
            case 4:	*--target = (ch | byteMark) & byteMask; ch >>= 6;
            case 3:	*--target = (ch | byteMark) & byteMask; ch >>= 6;
            case 2:	*--target = (ch | byteMark) & byteMask; ch >>= 6;
            case 1:	*--target = UTF8(ch | firstByteMark[bytesToWrite]);
            }
            target += bytesToWrite;
        }
        *sourceStart = source;
        *targetStart = target;
        return result;
    }


    bool ConvertUTF8toUTF16(const char* source, int srcLength, UnicodeChar* output, int& outlength)
    {
        UInt16* newoutput = output;
        const UTF8* src = (UTF8*)source;
        if (ConvertUTF8toUTF16(&src, src + srcLength, &newoutput, newoutput + srcLength, lenientConversion) != sourceIllegal)
        {
            outlength = int(newoutput - output);
            return true;
        }
        else
        {
            outlength = 0;
            return false;
        }
    }

    bool ConvertUTF16toUTF8(const UInt16* source, int srcLength, char* output, int& outlength)
    {
        UTF8* newoutput = (UTF8*)output;
        const UTF16* src = (UTF16*)source;
        if (ConvertUTF16toUTF8(&src, src + srcLength, &newoutput, newoutput + (srcLength * 4), lenientConversion) != sourceIllegal)
        {
            outlength = int(newoutput - (UTF8*)output);
            return true;
        }
        else
        {
            outlength = 0;
            return false;
        }
    }

    bool ConvertUTF16toUTF8(const UInt16 utf16character, std::string& utf8)
    {
        int len;
        char character[5];
        if (!ConvertUTF16toUTF8(&utf16character, 1, character, len))
            return false;
        character[len] = 0;
        utf8 = std::string(character);
        return true;
    }

    std::wstring ConvertUTF8toUTF16(std::string const& str)
    {
#if WIN32
        int length = MultiByteToWideChar(CP_UTF8, 0, str.c_str(), -1, nullptr, 0);
        std::wstring result(length - 1, L'\0');
        MultiByteToWideChar(CP_UTF8, 0, str.c_str(), -1, &result[0], length);
        return result;
#else
        std::wstring_convert<std::codecvt_utf8_utf16<wchar_t>, wchar_t> conv;
        std::wstring result = conv.from_bytes(str);
        return result;
#endif
    }

    std::string ConvertUTF16toUTF8(std::wstring const& str)
    {
#if WIN32
        int length = WideCharToMultiByte(CP_UTF8, 0, str.c_str(), -1, nullptr, 0, nullptr, nullptr);
        std::string result(length - 1, '\0');
        WideCharToMultiByte(CP_UTF8, 0, str.c_str(), -1, &result[0], length, nullptr, nullptr);
        return result;
#else
        std::wstring_convert<std::codecvt_utf8_utf16<wchar_t>, wchar_t> conv;
        std::string result = conv.to_bytes(str);
        return result;
#endif
    }

    std::string ConvertUTF32toUTF8(std::u32string const& u32str)
    {
#if WIN32
        std::wstring wstr(u32str.size(), L'\0');
        for (size_t i = 0; i < u32str.size(); ++i)
        {
            wstr[i] = static_cast<wchar_t>(u32str[i]);
        }
        return ConvertUTF16toUTF8(wstr);
#else
        std::wstring_convert<std::codecvt_utf8<char32_t>, char32_t> conv;
        std::string result = conv.to_bytes(u32str);
        return result;
#endif
    }

    std::wstring ConvertUTF32toUTF16(std::u32string const& u32str)
    {
        std::wstring_convert<std::codecvt_utf16<char32_t>, char32_t> conv;
        std::string bytes = conv.to_bytes(u32str);
        return ConvertUTF8toUTF16(bytes);
    }
}