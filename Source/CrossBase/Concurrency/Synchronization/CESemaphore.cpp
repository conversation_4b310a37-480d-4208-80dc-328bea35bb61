#include "PCH/CrossBasePCHPrivate.h"
#include "Concurrency/Synchronization/CESemaphore.h"

#if CROSSENGINE_WIN
#include "Concurrency/Synchronization/Windows/PlatformSemaphore.hpp"
#elif (CROSSENGINE_OSX || CROSSENGINE_IOS)
#include "Concurrency/Synchronization/Apple/PlatformSemaphore.hpp"
#elif (CROSSENGINE_ANDROID || CROSSENGINE_LINUX || CROSSENGINE_WASM)
#include "Concurrency/Synchronization/Posix/PlatformSemaphore.hpp"
#else
#error Unsupported platform
#endif

namespace cross
{
    Semaphore::Semaphore(UInt32 defaultValue)
        : mPImpl(new PlatformSemaphore{ defaultValue })
    {}

    Semaphore::~Semaphore()
    {
        delete mPImpl;
        mPImpl = nullptr;
    }

    void Semaphore::Wait() const
    {
        Assert(mPImpl);
        mPImpl->Wait();
    }

    bool Semaphore::TryWait() const
    {
        Assert(mPImpl);
        return mPImpl->TryWait();
    }

    void Semaphore::Notify()
    {
        Assert(mPImpl);
        mPImpl->Notify();
    }

    void* Semaphore::GetNativeHandle() const
    {
        Assert(mPImpl);
        return mPImpl->GetNativeHandle();
    }

    bool Semaphore::WaitForImpl(MillionSecondCount const& duration) const
    {
        Assert(mPImpl);
        return mPImpl->WaitFor(duration);
    }

    bool Semaphore::WaitUntilImpl(std::chrono::system_clock::time_point const& timePoint) const
    {
        Assert(mPImpl);
        return mPImpl->WaitUntil(timePoint);
    }
}
