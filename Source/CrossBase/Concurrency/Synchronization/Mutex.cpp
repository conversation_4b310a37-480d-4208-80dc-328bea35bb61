#include "PCH/CrossBasePCHPrivate.h"
#include "Concurrency/Synchronization/Mutex.h"

#ifndef MUTEX_API_PTHREAD
#define MUTEX_API_PTHREAD (CROSSENGINE_OSX || CROSSENGINE_IOS || CROSSENGINE_ANDROID || CROSSENGINE_LINUX || CROSSENGINE_WASM)
#endif

#ifndef MUTEX_API_WINAPI
#define MUTEX_API_WINAPI CROSSENGINE_WIN
#endif

#if CROSSENGINE_WIN
#include "Concurrency/Synchronization/Windows/PlatformMutex.hpp"
#elif (CROSSENGINE_OSX || CROSSENGINE_IOS || CROSSENGINE_ANDROID || CROSSENGINE_LINUX || CROSSENGINE_WASM)
#include "Concurrency/Synchronization/Posix/PlatformMutex.hpp"
#else
#error Unsupported platform
#endif

namespace cross
{
    Mutex::Mutex()
        : mMutex(new PlatformMutex{})
    {
    }

    Mutex::~Mutex()
    {
        assert(mMutex);
        delete mMutex;
        mMutex = nullptr;
    }

    void Mutex::Lock()
    {
        assert(mMutex);
        mMutex->Lock();
    }

    void Mutex::Unlock()
    {
        assert(mMutex);
        mMutex->Unlock();
    }

    bool Mutex::TryLock()
    {
        assert(mMutex);
        return mMutex->TryLock();
    }

    void* Mutex::GetNativeHandle() const
    {
        assert(mMutex);
        return mMutex->GetNativeHandle();
    }

    RWMutex::RWMutex()
        : mMutex(new PlatformRWMutex{})
    {
    }

    RWMutex::~RWMutex()
    {
        assert(mMutex);
        delete mMutex;
        mMutex = nullptr;
    }

    void RWMutex::Lock()
    {
        assert(mMutex);
        mMutex->Lock();
    }

    void RWMutex::Unlock()
    {
        assert(mMutex);
        mMutex->Unlock();
    }

    bool RWMutex::TryLock()
    {
        assert(mMutex);
        return mMutex->TryLock();
    }

    void RWMutex::LockRead()
    {
        assert(mMutex);
        mMutex->LockRead();
    }

    void RWMutex::UnlockRead()
    {
        assert(mMutex);
        mMutex->UnlockRead();
    }

    bool RWMutex::TryLockRead()
    {
        assert(mMutex);
        return mMutex->TryLockRead();
    }

    void* RWMutex::GetNativeHandle() const
    {
        assert(mMutex);
        return mMutex->GetNativeHandle();
    }
}
