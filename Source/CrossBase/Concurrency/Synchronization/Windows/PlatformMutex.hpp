#pragma once

#include <process.h>

namespace cross
{
    class PlatformMutexBase
    {
    protected:
        using MutexType = SRWLOCK;

    protected:
        PlatformMutexBase()
        {
            ::InitializeSRWLock(&mMutex);
        }

        ~PlatformMutexBase() = default;

        PlatformMutexBase(PlatformMutexBase const&) = delete;
        PlatformMutexBase& operator=(PlatformMutexBase const&) = delete;

    protected:
        void Lock()
        {
            ::AcquireSRWLockExclusive(&mMutex);
        }
        void Unlock()
        {
            ::ReleaseSRWLockExclusive(&mMutex);
        }
        bool TryLock() { return ::TryAcquireSRWLockExclusive(&mMutex) != 0; }
        void LockRead() { ::AcquireSRWLockShared(&mMutex); }
        void UnlockRead() { ::ReleaseSRWLockShared(&mMutex); }
        bool TryLockRead() { return ::TryAcquireSRWLockShared(&mMutex) != 0; }
        void* GetNativeHandle() const noexcept { return mMutex.Ptr; }
        MutexType* GetSRWLock() noexcept { return &mMutex; }

    protected:
        MutexType           mMutex;
    };

    class PlatformMutex : public PlatformMutexBase
    {
        using BaseType = PlatformMutexBase;
    public:
        PlatformMutex() : PlatformMutexBase() {}
        ~PlatformMutex() = default;
        using BaseType::Lock;
        using BaseType::Unlock;
        using BaseType::TryLock;
        using BaseType::GetNativeHandle;
        using BaseType::GetSRWLock;
    };

    class PlatformRWMutex : public PlatformMutexBase
    {
        using BaseType = PlatformMutexBase;
    public:
        PlatformRWMutex() : PlatformMutexBase() {}
        ~PlatformRWMutex() = default;
        using BaseType::Lock;
        using BaseType::Unlock;
        using BaseType::TryLock;
        using BaseType::LockRead;
        using BaseType::UnlockRead;
        using BaseType::TryLockRead;
        using BaseType::GetNativeHandle;
        using BaseType::GetSRWLock;
    };
}