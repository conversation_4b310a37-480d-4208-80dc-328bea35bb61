#pragma once

#define CROSS_REQUIRE_IF_II(...) typename = std::enable_if_t<std::conjunction_v<__VA_ARGS__>>
#define CROSS_REQUIRE_II(...) typename = std::void_t<__VA_ARGS__>
#define CROSS_REQUIRE_TYPENAME_T_I(t) typename t
#define CROSS_REQUIRE_TYPENAME() typename
#define CROSS_REQUIRE_TYPENAME_T(r, data, i, t) CROSS_REQUIRE_TYPENAME_T_I(t) BOOST_PP_COMMA()

// require if
#define CROSS_REQUIRE_IF_I(Seq, ...) \
BOOST_PP_SEQ_FOR_EACH_I(CROSS_REQUIRE_TYPENAME_T, _, Seq) CROSS_REQUIRE_IF_II(__VA_ARGS__)
#define CROSS_REQUIRE_IF(<PERSON><PERSON>, ...) \
CROSS_REQUIRE_IF_I(BOOST_PP_TUPLE_TO_SEQ(<PERSON><PERSON>), __VA_ARGS__)

// require
#define CROSS_REQUIRE_I(Seq, ...) \
BOOST_PP_SEQ_FOR_EACH_I(CROSS_REQUIRE_TYPENAME_T, _, Seq) CROSS_REQUIRE_II(__VA_ARGS__)
#define CROSS_REQUIRE(Tuple, ...) \
CROSS_REQUIRE_I(BOOST_PP_TUPLE_TO_SEQ(Tuple), __VA_ARGS__)

// require impl
#define CROSS_REQUIRE_IMPL_I(Seq) \
BOOST_PP_SEQ_FOR_EACH_I(CROSS_REQUIRE_TYPENAME_T, _, Seq) CROSS_REQUIRE_TYPENAME()
#define CROSS_REQUIRE_IMPL(Tuple) \
CROSS_REQUIRE_IMPL_I(BOOST_PP_TUPLE_TO_SEQ(Tuple))

namespace cross
{
    /// forward
    namespace launch
    {
        constexpr struct PostType {} Post;
        constexpr struct DispatchType {} Dispatch;
        constexpr struct DeferType {} Defer;
    }
    
    template <typename T>
    class TFuture;
    
    template <typename Impl>
    class TPromiseContext;
    
    template <typename T>
    class TPromise;
    
    template <typename T>
    class TTry;
    
    /// meta functions
    template <typename T>
    using TIsFuture = TIsInstanceOf<T, TFuture>;
    template <typename T>
    constexpr bool TIsFutureV = TIsFuture<T>::value;
    
    template <typename T>
    using TIsTry = TIsInstanceOf<T, TTry>;
    template <typename T>
    constexpr bool TIsTryV = TIsTry<T>::value;
    
    // traits type of try
    template <typename T>
    struct TTryType { using Type = TTry<T>; };
    template <typename T>
    struct TTryType<TTry<T>> { using Type = TTry<T>; };
    template <typename T>
    using TTryTypeT = typename TTryType<T>::Type;

    // traits for launch
    template <typename T>
    using TIsLaunchPolicy = TAmongTypes<T, launch::PostType, launch::DispatchType, launch::DeferType>;
    template <typename T>
    constexpr bool TIsLaunchPolicyV = TIsLaunchPolicy<T>::value;
    
    // traits for executor
    // An exectuor must implement the 'Post' interface.
    // 'Dispatch' & 'Defer' are optional, which shall decay to the 'Post' when not implemented.
    template <typename T, typename F, typename = void>
    struct TIsExecutorOf : std::false_type {};
    template <typename T, typename F>
    struct TIsExecutorOf<T, F, std::void_t<
        decltype(std::declval<T&>().Post(std::declval<std::decay_t<F>>()))
    >> : std::true_type {};
    template <typename T, typename F>
    constexpr bool TIsExecutorOfV = TIsExecutorOf<T, F>::value;
 
    template <typename T, typename F, typename = void>
    struct TIsExecutorDispatchableOf : std::false_type {};
    template <typename T, typename F>
    struct TIsExecutorDispatchableOf<T, F, std::void_t<
        decltype(std::declval<T&>().Dispatch(std::declval<std::decay_t<F>>()))
    >> : std::true_type {};
    template <typename T, typename F>
    constexpr bool TIsExecutorDispatchableOfV = TIsExecutorDispatchableOf<T, F>::value;
    
    template <typename T, typename F, typename = void>
    struct TIsExecutorDeferableOf : std::false_type {};
    template <typename T, typename F>
    struct TIsExecutorDeferableOf<T, F, std::void_t<
        decltype(std::declval<T&>().Defer(std::declval<std::decay_t<F>>()))
    >> : std::true_type {};

    template <typename T, typename Type = void>
    using TEnableIfFutureIterator = std::enable_if_t<
        TIsInstanceOfV<typename std::iterator_traits<T>::value_type, TFuture>, Type
    >;

    namespace detail
    {
        template <typename Pattern, typename = void>
        class TWhenAnyContext;

        template <typename Pattern, typename = void>
        class TWhenAllContext;
    }

    template <typename T, typename = void>
    struct TPackFuturePattern;
    template <typename ... Args>
    struct TPackFuturePattern<std::tuple<TFuture<Args>...>, void> { using Type = std::tuple<TFuture<Args>...>; };
    template <typename Arg>
    struct TPackFuturePattern<std::tuple<Arg, Arg>, std::enable_if_t<std::conjunction_v<
        TIsIterator<Arg>, TIsInstanceOf<typename std::iterator_traits<Arg>::value_type, TFuture>
    >>>
    {
        using Type = Arg;
    };

    template <typename ... Args>
    using TEnableIfMatchFuturePattern = std::void_t<typename TPackFuturePattern<std::tuple<Args...>>::Type>;

    template <typename Func, typename StorageType, typename = void>
    struct TTraitsThenResultType;
    template <typename Func, typename StorageType>
    struct TTraitsThenResultType<Func, StorageType, std::enable_if_t<std::is_invocable_v<Func>>>
    {
        using Type = std::invoke_result_t<Func>;
        using PassStorage = std::false_type;
    };
    template <typename Func, typename StorageType>
    struct TTraitsThenResultType<Func, StorageType, std::enable_if_t<std::is_invocable_v<Func, StorageType>>>
    {
        using Type = std::invoke_result_t<Func, StorageType>;
        using PassStorage = std::true_type;
    };
}
