#pragma once

namespace cross
{
    template <typename T>
    class TPromise final
    {
    public:
        using SharedState = TSharedState<T>;
        using StatePtr = std::shared_ptr<SharedState>;
        using FutureType = TFuture<T>;
        using ForwardType = typename SharedState::ForwardType;

    public:
        TPromise()
            : mState(std::make_shared<SharedState>())
            , mFutureRetrived(false)
        {
        }

        ~TPromise()
        {
            if(mState && !mState->IsReady())
            {
                mState->SetException(std::runtime_error{ "TimeOut" });
            }
        }

        TPromise(TPromise const&) = default;
        TPromise& operator=(TPromise const&) = default;
        TPromise(TPromise&&) = default;
        TPromise& operator=(TPromise&&) = default;

    public:
        FutureType GetFuture()
        {
            if(mFutureRetrived)
            {
                LOG_ERROR("{}", "Future already retrieved");
                throw std::runtime_error{ "Future already retrieved." };
            }

            Assert(mState);
            mFutureRetrived = true;
            return FutureType{ mState };
        }

        void SetValue(ForwardType value)
        {
            Assert(mState);
            mState->SetValue(std::forward<ForwardType>(value));
        }

        void SetException(std::exception_ptr e)
        {
            Assert(mState);
            mState->SetException(e);
        }

        bool IsReady() const
        {
            Assert(mState);
            return mState->IsReady();
        }

    private:
        StatePtr mState;
        bool     mFutureRetrived;
    };

    template <>
    class TPromise<void> final
    {
    public:
        using SharedState = TSharedState<void>;
        using StatePtr = std::shared_ptr<SharedState>;
        using FutureType = TFuture<void>;

    public:
        CROSS_BASE_API TPromise();
        CROSS_BASE_API ~TPromise();
        TPromise(TPromise const&) = default;
        TPromise& operator=(TPromise const&) = default;
        TPromise(TPromise&&) = default;
        TPromise& operator=(TPromise&&) = default;

    public:
        CROSS_BASE_API FutureType GetFuture();
        CROSS_BASE_API void SetValue();
        CROSS_BASE_API void SetException(std::exception_ptr e);

    private:
        StatePtr mState;
        bool     mFutureRetrived;
    };

    namespace detail
    {
        template <typename PassStorage, typename T, typename Func>
        decltype(auto) ApplyContinuation([[maybe_unused]]std::shared_ptr<TSharedState<T>>& state, Func const& continuation)
        {
            if constexpr(PassStorage::value)
            {
                return continuation(state->GetStorage());
            }
            else
            {
                return continuation();
            }
        }

        template <typename PassStorage, typename T, typename P, typename Func>
        void ThenSetPromiseValue(TPromise<T>& promise, std::shared_ptr<TSharedState<P>>& state, Func const& continuation)
        {
            try
            {
                if constexpr (std::is_void_v<T>)
                {
                    ApplyContinuation<PassStorage>(state, continuation);
                    promise.SetValue();
                }
                else
                {
                    decltype(auto) result = ApplyContinuation<PassStorage>(state, continuation);
                    promise.SetValue(std::forward<decltype(result)>(result));
                }
            }
            catch (...)
            {
                promise.SetException(std::current_exception());
            }
        }

        template <typename PassStorage, typename T, typename P, typename Launch, typename Executor, typename Func>
        void ThenImpl(TPromise<T>& promise,
                      std::shared_ptr<TSharedState<P>>& state,
                      Launch,
                      Executor& executor,
                      Func& continuation)
        {
            auto task = [p = std::move(promise), s = std::move(state), f = std::move(continuation)]() mutable
            {
                ThenSetPromiseValue<PassStorage>(p, s, f);
            };
        
            if constexpr(std::conjunction_v<
                std::is_same<Launch, launch::DispatchType>,
                TIsExecutorDispatchableOf<Executor, decltype(task)>>)
            {
                executor.Dispatch(std::move(task));
            }
            else if constexpr(std::conjunction_v<
                std::is_same<Launch, launch::DeferType>,
                TIsExecutorDeferableOf<Executor, decltype(task)>>)
            {
                executor.Defer(std::move(task));
            }
            else
            {
                executor.Post(std::move(task));
            }
        }
    }

    template <typename T>
    template <CROSS_REQUIRE_IMPL((Launch, Executor, Func))>
    auto TFutureBase<T>::Then(Launch, Executor& executor, Func&& continuation)
    {
        using ResultType = typename TTraitsThenResultType<Func, StorageType>::Type;
        using PassStorage = typename TTraitsThenResultType<Func, StorageType>::PassStorage;
        TPromise<ResultType> promise{};
        TFuture<ResultType> future = promise.GetFuture();
    
        mState->AddContinuation([
            p = std::move(promise),                 // move capture promise
            &executor,                              // capture executor
            f = std::forward<Func>(continuation),   // forward capture functions
            s = mState                              // value capture this shared state
        ]() mutable                                 // mutable for p.SetValue(...)
        {
            detail::ThenImpl<PassStorage>(p, s, Launch{}, executor, f);
        });

        return future;
    }

    template <typename Impl>
    class TPromiseContext : public std::enable_shared_from_this<Impl>
    {
    protected:
        template <typename T>
        static decltype(auto) GetSharedState(TFutureBase<T>& futureBase)
        {
            return futureBase.mState;
        }
    };
}
