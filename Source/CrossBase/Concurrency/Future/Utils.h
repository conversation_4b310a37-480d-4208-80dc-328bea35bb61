#pragma once

namespace cross
{
    template <typename T>
    inline TFuture<T> MakeReadyFuture(T&& value)
    {
        TPromise<T> promise;
        promise.SetValue(std::forward<T>(value));
        return promise.GetFuture();
    }

    CROSS_BASE_API TFuture<void> MakeReadyFuture();
 
    namespace future
    {
        template <typename T>
        inline TFuture<T> Just(T&& value)
        {
            return MakeReadyFuture(std::forward<T>(value));
        }

        inline TFuture<void> Just()
        {
            return MakeReadyFuture();
        }
    }
}