// Used for shader property name string comparisons.

#include "EnginePrefix.h"
#include "NameID.h"
#include <shared_mutex>
#include "CrossBase/Containers/HashMap/HashMap.hpp"

void cross::NameID::AdditionalSerialize(SerializeNode& node, SerializeContext& context) const
{
    node = mDebugName;
}

void cross::NameID::AdditionalDeserialize(const DeserializeNode& inNode, SerializeContext& context)
{
    mPropertyID = PropertyToID(inNode.AsStringView(), &mDebugName);
}

size_t cross::NameID::PropertyToID(std::string_view name, const char** ppInternalName)
{
    static CEHashMap<std::string_view, size_t> gDataSet;

    static std::shared_mutex gMutex;

    size_t propertyID = 0;
    const char* propertyName = nullptr;
    {
        std::shared_lock<std::shared_mutex> lock{ gMutex };

        if (gDataSet.count(name))
        {
            if (auto i = gDataSet.find(name); i != gDataSet.end())
            {
                propertyName = i->first.data();
                propertyID = i->second;
            }

            if (ppInternalName)
            {
                *ppInternalName = propertyName;
            }
            return propertyID;
        }
    }
    
    std::unique_lock<std::shared_mutex> lock{ gMutex };
    {
        static size_t gPropertyID = 1;
        propertyID = gPropertyID++;

        auto length = name.length();
        propertyName = new char[length + 1]{};
        strncpy(const_cast<char*>(propertyName), name.data(), length);
        gDataSet.emplace(propertyName, propertyID);
    }

    if (ppInternalName)
    {
        *ppInternalName = propertyName;
    }

    return propertyID;
}