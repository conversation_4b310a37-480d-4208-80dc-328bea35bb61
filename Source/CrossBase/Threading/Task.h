#pragma once

#include "TaskSystem.h"
#include "Profiling/Profiling.h"
#include <atomic>
#include <variant>
#ifdef __cpp_lib_coroutine
#include <coroutine>
#endif

#ifdef __cpp_lib_concepts
#include <concepts>
#endif
#ifndef _MANAGED
#include <tracy/Tracy.hpp>
DECLARE_CPU_TIMING_GROUP(GroupParallelFor);
DECLARE_CPU_TIMING_GROUP(GroupAsync);
DECLARE_CPU_TIMING_GROUP(GroupDispatch);
#endif
namespace cross::threading
{
    namespace memory
    {
        CROSS_BASE_API void* Allocate(std::uint64_t size);

        CROSS_BASE_API void Deallocate(void* address, std::uint64_t size);

        template<typename T, typename... Ts>
        T* CreateInstance(Ts&&... args)
        {
            static_assert(alignof(T) <= 64U);
            return new (Allocate(sizeof(T))) T(std::forward<Ts>(args)...);
        }

        template<typename T>
        void DestroyInstance(T* address, std::uint64_t size)
        {
            std::destroy_at(address);
            Deallocate(address, size);
        }

        template<typename T>
        void DestroyInstance(T* address)
        {
            static_assert(alignof(T) <= 64U);
            DestroyInstance(address, sizeof(T));
        }

        template<typename T>
        class TTaskList
        {
        public:
            TTaskList() : mHead(nullptr)
            {
            }

            TTaskList(std::initializer_list<T> list) : TTaskList()
            {
                for (const auto& item : list)
                {
                    Push(item);
                }
            }

            ~TTaskList()
            {
                Reset();
            }

            TTaskList(TTaskList&& other) noexcept
            {
                mHead = other.mHead;
                other.mHead = nullptr;
            }

            TTaskList& operator=(TTaskList&& other) noexcept
            {
                TTaskList(std::move(other)).Swap(*this);
                return *this;
            }

            TTaskList(const TTaskList&) = delete;
            TTaskList& operator=(const TTaskList&) = delete;

            bool Empty() const noexcept
            {
                return !mHead;
            }

            void Push(T value)
            {
                mHead = memory::CreateInstance<Node>(std::move(value), mHead);
            }

            T Pop()
            {
                auto node = mHead;
                auto copy = std::move(node->mValue);
                mHead = node->mNext;
                DestroyInstance(node);
                return copy;
            }

            void Reset()
            {
                while (!Empty())
                {
                    Pop();
                }
            }

            template<typename TLambda>
            void Traverse(TLambda&& lambda)
            {
                auto node = mHead;
                while (node && !lambda(node->mValue))
                {
                    node = node->mNext;
                }
            }

            template<typename TLambda>
            void Traverse(TLambda&& lambda) const
            {
                auto node = mHead;
                while (node && !lambda(node->mValue))
                {
                    node = node->mNext;
                }
            }

        private:
            struct Node
            {
                Node(T value, Node* next)
                    : mValue(std::move(value)), mNext(next)
                {
                }

                T mValue;
                Node* mNext;
            };

            void Swap(TTaskList& other)
            {
                std::swap(mHead, other.mHead);
            }

            Node* mHead;
        };
    }

    class TaskBase
    {
        friend class TaskEvent;

    public:
        virtual ~TaskBase() = default;

        TaskBase(const TaskBase&) = delete;
        TaskBase& operator=(const TaskBase&) = delete;

        void DoExecute();

        void ConditionalQueueTask(ThreadID currentThreadID)
        {
            if (mCounter.fetch_sub(1U, std::memory_order_release) == 1U)
            {
                std::atomic_thread_fence(std::memory_order_acquire);
                TaskSystem::Get()->QueueTask(this, mThreadToExecuteOn, mTaskPriority, currentThreadID);
            }
        }

        void QueueTask(ThreadID currentThreadID)
        {
            TaskSystem::Get()->QueueTask(this, mThreadToExecuteOn, mTaskPriority, currentThreadID);
        }

        ThreadID GetThreadToExecuteOn() const
        {
            return mThreadToExecuteOn;
        }

        Priority GetTaskPriority() const
        {
            return mTaskPriority;
        }

    protected:
        TaskBase(ThreadID threadToExecuteOn, Priority taskPriority) : mThreadToExecuteOn(threadToExecuteOn), mTaskPriority(taskPriority)
        {
            mCounter.store(0U, std::memory_order_relaxed);
        }

        virtual std::byte* GetReturnValueStorage() = 0;
        virtual const std::byte* GetReturnValueStorage() const = 0;

        virtual std::uint64_t Size() const = 0;

        ThreadID mThreadToExecuteOn;
        Priority mTaskPriority;
        std::atomic_uint64_t mCounter;

    private:
        virtual void Execute() = 0;
    };

#if !(CROSSENGINE_OSX || CROSSENGINE_IOS)
    inline void TaskBase::DoExecute()
    {
        Execute();
    }
#endif

    class TaskEventBase
    {
        friend class TaskEventPtr;

    public:
        TaskEventBase() noexcept
        {
            mCounter.store(0U, std::memory_order_relaxed);
        }

    private:
        std::uint64_t IncRefCount() noexcept
        {
            return mCounter.fetch_add(1U, std::memory_order_relaxed);
        }

        std::uint64_t DecRefCount() noexcept
        {
            return mCounter.fetch_sub(1U, std::memory_order_release);
        }

        std::atomic_uint64_t mCounter;
    };

    class TaskEvent;

    class TaskEventPtr
    {
    public:
        TaskEventPtr(TaskEvent* event = nullptr);

        ~TaskEventPtr();

        TaskEventPtr(const TaskEventPtr& other);

        TaskEventPtr& operator=(const TaskEventPtr& other) noexcept
        {
            TaskEventPtr(other).Swap(*this);
            return *this;
        }

        TaskEventPtr(TaskEventPtr&& other) noexcept
        {
            mTaskEvent = other.mTaskEvent;
            other.mTaskEvent = nullptr;
        }

        TaskEventPtr& operator=(TaskEventPtr&& other) noexcept
        {
            TaskEventPtr(std::move(other)).Swap(*this);
            return *this;
        }

        TaskEvent& operator*() noexcept
        {
            return *mTaskEvent;
        }

        const TaskEvent& operator*() const noexcept
        {
            return *mTaskEvent;
        }

        TaskEvent* operator->() noexcept
        {
            return mTaskEvent;
        }

        const TaskEvent* operator->() const noexcept
        {
            return mTaskEvent;
        }

        operator bool() const noexcept
        {
            return mTaskEvent;
        }

        bool operator==(nullptr_t) const noexcept
        {
            return mTaskEvent == nullptr;
        }

        void Reset() noexcept
        {
            TaskEventPtr().Swap(*this);
        }

    private:
        void Swap(TaskEventPtr& other) noexcept
        {
            std::swap(mTaskEvent, other.mTaskEvent);
        }

        TaskEvent* mTaskEvent;
    };

    class TaskEvent : public TaskEventBase
    {
        template<typename, typename>
        friend class TTaskBase;

        template<typename, typename>
        friend class TTask;

    public:
        ~TaskEvent()
        {
            if (mTask)
            {
                memory::DestroyInstance(mTask, mTask->Size());
            }
        }

        TaskEvent(const TaskEvent&) = delete;
        TaskEvent& operator=(const TaskEvent&) = delete;
        TaskEvent(TaskEvent&&) = delete;
        TaskEvent& operator=(TaskEvent&&) = delete;

        bool Complete() const
        {
            std::scoped_lock lock(mCompletionMutex);
            return mComplete;
        }

        void WaitForCompletion();

        void ReleaseTask(ThreadID currentThreadID = ThreadID::AnyThread)
        {
            assert(mIsHoldingTask == true);

            if (mIsHoldingTask)
            {
                if (currentThreadID == ThreadID::AnyThread)
                {
                    currentThreadID = TaskSystem::GetCurrentThreadID();
                }

                if (mTask->mCounter.fetch_sub(1U, std::memory_order_release) == 1U)
                {
                    std::atomic_thread_fence(std::memory_order_acquire);
                    mTask->QueueTask(currentThreadID);
                }
            }

            mIsHoldingTask = false;
        }

        template<typename TReturn>
        TReturn& GetReturnValue()
        {
            assert(Complete() && mTask && mTask->GetReturnValueStorage());

            return *std::launder(reinterpret_cast<TReturn*>(mTask->GetReturnValueStorage()));
        }

        template<typename TReturn>
        const TReturn& GetReturnValue() const
        {
            assert(Complete() && mTask && mTask->GetReturnValueStorage());

            return *std::launder(reinterpret_cast<const TReturn*>(mTask->GetReturnValueStorage()));
        }

        static TaskEventPtr Create(TaskBase* task);

    private:
        TaskEvent() : mTask(nullptr), mIsHoldingTask(false), mComplete(true) {}

        TaskEvent(TaskBase* task) : mTask(task), mIsHoldingTask(false), mComplete(false) {}

        bool AddSuccessor(TaskBase* task)
        {
            std::scoped_lock lock(mCompletionMutex);
            if (!mComplete)
            {
                mSuccessors.Push(task);
                return true;
            }
            return false;
        }

        void DispatchSuccessors(ThreadID currentThreadID)
        {
            memory::TTaskList<TaskBase*> localList;
            {
                std::scoped_lock lock(mCompletionMutex);
                mComplete = true;
                localList = std::move(mSuccessors);
            }

            while (!localList.Empty())
            {
                localList.Pop()->ConditionalQueueTask(currentThreadID);
            }
        }

        TaskBase* mTask;
        std::uint64_t mIsHoldingTask : 1;
        std::uint64_t mComplete : 1;
        memory::TTaskList<TaskBase*> mSuccessors;
        mutable std::mutex mCompletionMutex;
    };

    inline TaskEventPtr::TaskEventPtr(TaskEvent* event) : mTaskEvent(event)
    {
        if (mTaskEvent)
        {
            mTaskEvent->IncRefCount();
        }
    }

    inline TaskEventPtr::~TaskEventPtr()
    {
        if (mTaskEvent && mTaskEvent->DecRefCount() == 1U)
        {
            std::atomic_thread_fence(std::memory_order_acquire);
            memory::DestroyInstance(mTaskEvent);
        }
    }

    inline TaskEventPtr::TaskEventPtr(const TaskEventPtr& other)
    {
        if (other.mTaskEvent)
        {
            other.mTaskEvent->IncRefCount();
        }
        mTaskEvent = other.mTaskEvent;
    }

    class TaskEventArray
    {
        template<typename, typename>
        friend class TTaskBase;

    public:
        TaskEventArray() : mSize(0U) {}
        TaskEventArray(std::initializer_list<TaskEventPtr> list) : mTaskEvents(list), mSize(list.size()) {}

        TaskEventArray(const TaskEventArray&) = delete;
        TaskEventArray& operator=(const TaskEventArray&) = delete;

        TaskEventArray(TaskEventArray&&) = default;
        TaskEventArray& operator=(TaskEventArray&&) = default;

        bool Empty() const
        {
            return mTaskEvents.Empty();
        }

        void Add(const TaskEventPtr& taskEventPtr)
        {
            mTaskEvents.Push(taskEventPtr);
            mSize++;
        }

        void Reset()
        {
            mTaskEvents.Reset();
            mSize = 0U;
        }

        void WaitForCompletion()
        {
            TaskSystem::Get()->WaitForTasks(*this, TaskSystem::GetCurrentThreadID());
        }

        template<typename TLambda>
        void Traverse(TLambda&& lambda)
        {
            mTaskEvents.Traverse(std::forward<TLambda>(lambda));
        }

        template<typename TLambda>
        void Traverse(TLambda&& lambda) const
        {
            mTaskEvents.Traverse(std::forward<TLambda>(lambda));
        }

        std::uint64_t Size() const
        {
            return mSize;
        }

        std::uint64_t GetNumCompletedTasks() const
        {
            std::uint64_t numCompletedTasks{};
            Traverse([&numCompletedTasks](const auto& taskEventPtr)
            {
                if (taskEventPtr->Complete())
                {
                    numCompletedTasks++;
                }
                return false;
            });
            return numCompletedTasks;
        }

        bool Complete() const
        {
            auto complete = true;
            Traverse([&complete](const auto& taskEventPtr)
            {
                if (!taskEventPtr->Complete())
                {
                    complete = false;
                    return true;
                }
                return false;
            });
            return complete;
        }

        void ReleaseTasks(ThreadID currentThreadID = ThreadID::AnyThread)
        {
            Traverse([currentThreadID](auto& taskEventPtr)
            {
                taskEventPtr->ReleaseTask(currentThreadID);
                return false;
            });
        }

    private:
        memory::TTaskList<TaskEventPtr> mTaskEvents;
        std::uint64_t mSize;
    };

    inline void TaskEvent::WaitForCompletion()
    {
        TaskSystem::Get()->WaitForTasks({ TaskEventPtr(this) }, TaskSystem::GetCurrentThreadID());
    }

    inline TaskEventPtr TaskEvent::Create(TaskBase* task)
    {
        class TaskEventDerived : public TaskEvent
        {
        public:
            TaskEventDerived(TaskBase* task) : TaskEvent(task) {}
        };

        return TaskEventPtr(memory::CreateInstance<TaskEventDerived>(task));
    }

    template<typename TUserTask, typename TReturn>
    class TTaskBase : public TaskBase
    {
    public:
        TTaskBase(ThreadID threadToExecuteOn, Priority taskPriority) : TaskBase(threadToExecuteOn, taskPriority), mTaskStorage{} {}

        class Constructor
        {
            friend class TTaskBase<TUserTask, TReturn>;

        public:
            Constructor(const Constructor&) = delete;
            Constructor& operator=(const Constructor&) = delete;
            Constructor(Constructor&&) = delete;
            Constructor& operator=(Constructor&&) = delete;

            template<typename... Ts>
            TaskEventPtr DispatchTask(Ts&&... args) const;

            template<typename... Ts>
            TaskEventPtr HoldTask(Ts&&... args) const;

        private:
            Constructor(ThreadID currentThreadID, const TaskEventArray& predecessors) : mCurrentThreadID(currentThreadID), mPredecessors(predecessors) {}

            ThreadID mCurrentThreadID;
            const TaskEventArray& mPredecessors;
        };

        static Constructor Create(const TaskEventArray& predecessors = {}, ThreadID currentThreadID = ThreadID::AnyThread)
        {
            if (currentThreadID == ThreadID::AnyThread)
            {
                currentThreadID = TaskSystem::GetCurrentThreadID();
            }

            return Constructor{ currentThreadID, predecessors };
        }

    protected:
        TaskEventPtr mEvent;
        alignas(TUserTask) std::byte mTaskStorage[sizeof(TUserTask)];
    };

    template<typename TUserTask, typename TReturn = void>
    class TTask final : public TTaskBase<TUserTask, TReturn>
    {
    public:
        TTask() : TTaskBase<TUserTask, TReturn>::TTaskBase(ThreadID::AnyThread, Priority::Default), mReturnStorage{} {}

        virtual ~TTask() override
        {
            auto returnStorage = std::launder(reinterpret_cast<TReturn*>(mReturnStorage));
            std::destroy_at(returnStorage);
        }

        TTask(const TTask&) = delete;
        TTask& operator=(const TTask&) = delete;
        TTask(TTask&&) = delete;
        TTask& operator=(TTask&&) = delete;

        virtual void Execute() override
        {
            auto userTask = std::launder(reinterpret_cast<TUserTask*>(this->mTaskStorage));
            new (mReturnStorage) TReturn(userTask->Execute(this->mEvent));
            std::destroy_at(userTask);

            this->mEvent->DispatchSuccessors(this->mThreadToExecuteOn);
            this->mEvent.Reset();
        }

    private:
        virtual std::byte* GetReturnValueStorage() override
        {
            return mReturnStorage;
        }

        virtual const std::byte* GetReturnValueStorage() const override
        {
            return mReturnStorage;
        }

        std::uint64_t Size() const override
        {
            return static_cast<std::uint64_t>(sizeof(*this));
        }

        alignas(TReturn) std::byte mReturnStorage[sizeof(TReturn)];
    };

    template<typename TUserTask>
    class TTask<TUserTask, void> final : public TTaskBase<TUserTask, void>
    {
    public:
        TTask() : TTaskBase<TUserTask, void>::TTaskBase(ThreadID::AnyThread, Priority::Default) {}

        TTask(const TTask&) = delete;
        TTask& operator=(const TTask&) = delete;
        TTask(TTask&&) = delete;
        TTask& operator=(TTask&&) = delete;

        virtual void Execute() override
        {
            auto userTask = std::launder(reinterpret_cast<TUserTask*>(this->mTaskStorage));
            userTask->Execute(this->mEvent);
            std::destroy_at(userTask);

            this->mEvent->DispatchSuccessors(this->mThreadToExecuteOn);
            this->mEvent->mTask = nullptr;
            memory::DestroyInstance(this);
        }

    private:
        virtual std::byte* GetReturnValueStorage() override
        {
            return nullptr;
        }

        virtual const std::byte* GetReturnValueStorage() const override
        {
            return nullptr;
        }

        std::uint64_t Size() const override
        {
            return static_cast<std::uint64_t>(sizeof(*this));
        }
    };

    template<typename TUserTask, typename TReturn>
    template<typename ...Ts>
    inline TaskEventPtr TTaskBase<TUserTask, TReturn>::Constructor::DispatchTask(Ts&& ...args) const
    {
        using TaskType = TTask<TUserTask, TReturn>;
        auto task = memory::CreateInstance<TaskType>();

        auto userTask = new (task->mTaskStorage) TUserTask(std::forward<Ts>(args)...);
        task->mThreadToExecuteOn = userTask->GetThreadToExecuteOn();
        task->mTaskPriority = userTask->GetTaskPriority();

        auto taskEvent = TaskEvent::Create(task);
        task->mEvent = taskEvent;

        if (mPredecessors.Empty())
        {
            task->QueueTask(mCurrentThreadID);
        }
        else
        {
            task->mCounter.store(static_cast<int>(mPredecessors.Size()) + 1U, std::memory_order_relaxed);

            std::uint64_t numCompletedTasks = 0U;
            mPredecessors.Traverse([&numCompletedTasks, task](auto& taskEventPtr)
            {
                numCompletedTasks += !taskEventPtr->AddSuccessor(task);
                return false;
            });

            if (task->mCounter.fetch_sub(numCompletedTasks + 1U, std::memory_order_release) == numCompletedTasks + 1U)
            {
                std::atomic_thread_fence(std::memory_order_acquire);
                task->QueueTask(mCurrentThreadID);
            }
        }

        return taskEvent;
    }

    template<typename TUserTask, typename TReturn>
    template<typename ...Ts>
    inline TaskEventPtr TTaskBase<TUserTask, TReturn>::Constructor::HoldTask(Ts&& ...args) const
    {
        using TaskType = TTask<TUserTask, TReturn>;
        auto task = memory::CreateInstance<TaskType>();

        auto userTask = new (task->mTaskStorage) TUserTask(std::forward<Ts>(args)...);
        task->mThreadToExecuteOn = userTask->GetThreadToExecuteOn();
        task->mTaskPriority = userTask->GetTaskPriority();

        auto taskEvent = TaskEvent::Create(task);
        taskEvent->mIsHoldingTask = true;
        task->mEvent = taskEvent;
        task->mCounter.store(static_cast<int>(mPredecessors.Size()) + 1U, std::memory_order_relaxed);

        std::uint64_t numCompletedTasks = 0U;
        mPredecessors.Traverse([&numCompletedTasks, task](auto& taskEventPtr)
        {
            numCompletedTasks += !taskEventPtr->AddSuccessor(task);
            return false;
        });
        task->mCounter.fetch_sub(numCompletedTasks, std::memory_order_relaxed);

        return taskEvent;
    }

    template<ThreadID ThreadToExecuteOn = ThreadID::AnyThread, typename TLambda>
    void ParallelFor(int32_t numIterations, Priority taskPriority, TLambda&& body)
    {
        assert(0 <= numIterations);

        class ParallelForTask
        {
        public:
            ParallelForTask(TLambda&& body, std::atomic_int32_t& nextIndex, int32_t endIndex, Priority taskPriority)
                : mBody(body)
                , mNextIndex(nextIndex)
                , mEndIndex(endIndex)
                , mTaskPriority(taskPriority)
            {}

            void Execute(const TaskEventPtr&)
            {
                SCOPED_CPU_TIMING(GroupParallelFor, "ParallelForTask");

                for (auto localNextIndex = mNextIndex.fetch_add(1, std::memory_order_relaxed);
                    localNextIndex < mEndIndex;
                    localNextIndex = mNextIndex.fetch_add(1, std::memory_order_relaxed))
                {
                    mBody(localNextIndex);
                }
            }

            ThreadID GetThreadToExecuteOn() const
            {
                return ThreadToExecuteOn;
            }

            Priority GetTaskPriority() const
            {
                return mTaskPriority;
            }

        private:
            TLambda& mBody;
            std::atomic_int32_t& mNextIndex;
            int32_t mEndIndex;
            Priority mTaskPriority;
        };

        SCOPED_CPU_TIMING(GroupParallelFor, "ParallelFor");

        const auto currentThreadID = TaskSystem::GetCurrentThreadID();
        const auto numTasksToDispatch = (std::max)(0, (std::min)(numIterations, TaskSystem::Get()->GetNumMasterThreads() + TaskSystem::Get()->GetNumTaskThreads()) - 1);

        std::atomic_int32_t nextIndex = 0;
        TaskEventArray taskEvents;
        for (int32_t i = 0; i != numTasksToDispatch; i++)
        {
            taskEvents.Add(TTask<ParallelForTask>::Create({}, currentThreadID).DispatchTask(std::forward<TLambda>(body), nextIndex, numIterations, taskPriority));
        }

        {
            SCOPED_CPU_TIMING(GroupParallelFor, "ParallelForLoop");

            for (auto localNextIndex = nextIndex.fetch_add(1, std::memory_order_relaxed);
                localNextIndex < numIterations;
                localNextIndex = nextIndex.fetch_add(1, std::memory_order_relaxed))
            {
                body(localNextIndex);
            }
        }

        if (numTasksToDispatch)
        {
            TaskSystem::Get()->WaitForTasks(taskEvents, currentThreadID);
        }
    }

    template<ThreadID ThreadToExecuteOn = ThreadID::AnyThread, typename TLambda>
    void ParallelFor(int32_t numIterations, TLambda&& body)
    {
        return ParallelFor<ThreadToExecuteOn>(numIterations, Priority::High, std::forward<TLambda>(body));
    }

    template<int32_t BlockSize = 8, typename TIterator, typename TLambda>
    void ParallelFor(TIterator begin, TIterator end, TLambda&& body)
    {
        struct LoopBlock
        {
            TIterator mBegin;
            int32_t mCount;
        };

        class ParallelForTask
        {
        public:
            ParallelForTask(TLambda&& body, std::atomic_int32_t& nextIndex, const std::vector<LoopBlock>& blocks) : mBody(body), mNextIndex(nextIndex), mLoopBlocks(blocks) {}

            void Execute(const TaskEventPtr&)
            {
                SCOPED_CPU_TIMING(GroupParallelFor, "ParallelForTask");

                auto numBlocks = static_cast<int32_t>(mLoopBlocks.size());
                for (auto localNextIndex = mNextIndex.fetch_add(1, std::memory_order_relaxed);
                    localNextIndex < numBlocks;
                    localNextIndex = mNextIndex.fetch_add(1, std::memory_order_relaxed))
                {
                    auto iter = mLoopBlocks[localNextIndex].mBegin;
                    for (int32_t i = 0; i != mLoopBlocks[localNextIndex].mCount; i++)
                    {
                        mBody(iter++);
                    }
                }
            }

            ThreadID GetThreadToExecuteOn() const
            {
                return ThreadID::AnyThread;
            }

            Priority GetTaskPriority() const
            {
                return Priority::High;
            }

        private:
            TLambda& mBody;
            std::atomic_int32_t& mNextIndex;
            const std::vector<LoopBlock>& mLoopBlocks;
        };

        SCOPED_CPU_TIMING(GroupParallelFor, "ParallelFor");

        const auto currentThreadID = TaskSystem::GetCurrentThreadID();
        const auto numWorkerThreads = TaskSystem::Get()->GetNumMasterThreads() + TaskSystem::Get()->GetNumTaskThreads();
        const auto numIterations = static_cast<int32_t>(std::distance(begin, end));
        const auto actualBlockSize = (std::max)(1, (std::min)((numIterations + numWorkerThreads - 1) / numWorkerThreads, BlockSize));
        const auto numBlocks = (numIterations + actualBlockSize - 1) / actualBlockSize;
        const auto numTasksToDispatch = (std::max)(0, (std::min)(numBlocks, numWorkerThreads) - 1);

        std::vector<LoopBlock> blocks;
        {
            blocks.reserve(numBlocks);
            TIterator iter = begin;
            int32_t count = 0;
            while (begin != end)
            {
                if (count == actualBlockSize)
                {
                    blocks.push_back({ iter, actualBlockSize });
                    iter = begin;
                    count = 0;
                }
                count++;
                begin++;
            }
            if (count)
            {
                blocks.push_back({ iter, count });
            }
        }

        std::atomic_int32_t nextIndex = 0;
        TaskEventArray taskEvents;
        for (int32_t i = 0; i != numTasksToDispatch; i++)
        {
            taskEvents.Add(TTask<ParallelForTask>::Create({}, currentThreadID).DispatchTask(std::forward<TLambda>(body), nextIndex, blocks));
        }

        {
            SCOPED_CPU_TIMING(GroupParallelFor, "ParallelForLoop");

            for (auto localNextIndex = nextIndex.fetch_add(1, std::memory_order_relaxed);
                localNextIndex < numBlocks;
                localNextIndex = nextIndex.fetch_add(1, std::memory_order_relaxed))
            {
                auto iter = blocks[localNextIndex].mBegin;
                for (int32_t i = 0; i != blocks[localNextIndex].mCount; i++)
                {
                    body(iter++);
                }
            }
        }

        if (numTasksToDispatch)
        {
            TaskSystem::Get()->WaitForTasks(taskEvents, currentThreadID);
        }
    }

    template<ThreadID ThreadToExecuteOn = ThreadID::AnyThread, typename TReturn = void, typename TLambda = void>
    TaskEventPtr Dispatch(const TaskEventArray& predecessors, Priority taskPriority, TLambda&& lambda)
    {
        //static_assert(ThreadToExecuteOn == ThreadID::AnyThread || ThreadToExecuteOn == ThreadID::TaskThread);

        class DispatchTask
        {
        public:
            DispatchTask(TLambda&& lambda, Priority taskPriority) : mLambda(std::forward<TLambda>(lambda)), mTaskPriority(taskPriority) {}

            TReturn Execute(const TaskEventPtr& selfTaskEvent)
            {
                QUICK_SCOPED_CPU_TIMING("DispatchTask::Execute");
                return mLambda(selfTaskEvent);
            }

            ThreadID GetThreadToExecuteOn() const
            {
                return ThreadToExecuteOn;
            }

            Priority GetTaskPriority() const
            {
                return mTaskPriority;
            }

        private:
            TLambda mLambda;
            Priority mTaskPriority;
        };

        return TTask<DispatchTask, TReturn>::Create(predecessors).DispatchTask(std::forward<TLambda>(lambda), taskPriority);
    }

    template<ThreadID ThreadToExecuteOn = ThreadID::AnyThread, typename TReturn = void, typename TLambda = void>
    TaskEventPtr Dispatch(TLambda&& lambda)
    {
        return Dispatch<ThreadToExecuteOn, TReturn>({}, Priority::Default, std::forward<TLambda>(lambda));
    }

    template<ThreadID ThreadToExecuteOn = ThreadID::AnyThread, typename TReturn = void, typename TLambda = void>
    TaskEventPtr Dispatch(const TaskEventArray& predecessors, TLambda&& lambda)
    {
        return Dispatch<ThreadToExecuteOn, TReturn>(predecessors, Priority::Default, std::forward<TLambda>(lambda));
    }

    template<ThreadID ThreadToExecuteOn = ThreadID::AnyThread, typename TReturn = void, typename TLambda = void>
    TaskEventPtr Dispatch(Priority taskPrioirty, TLambda&& lambda)
    {
        return Dispatch<ThreadToExecuteOn, TReturn>({}, taskPrioirty, std::forward<TLambda>(lambda));
    }

    template<typename TReturn, typename TLambda = void>
    TaskEventPtr Dispatch(TLambda&& lambda)
    {
        return Dispatch<ThreadID::AnyThread, TReturn>({}, Priority::Default, std::forward<TLambda>(lambda));
    }

    template<typename TReturn, typename TLambda = void>
    TaskEventPtr Dispatch(const TaskEventArray& predecessors, TLambda&& lambda)
    {
        return Dispatch<ThreadID::AnyThread, TReturn>(predecessors, Priority::Default, std::forward<TLambda>(lambda));
    }

    template<typename TReturn, typename TLambda = void>
    TaskEventPtr Dispatch(Priority taskPrioirty, TLambda&& lambda)
    {
        return Dispatch<ThreadID::AnyThread, TReturn>({}, taskPrioirty, std::forward<TLambda>(lambda));
    }

    template<typename TReturn = void, typename TLambda = void>
    TaskEventPtr Async(const TaskEventArray& predecessors, Priority taskPriority, TLambda&& lambda)
    {
        class AsyncTask
        {
        public:
            AsyncTask(TLambda&& lambda, Priority taskPriority) : mLambda(std::forward<TLambda>(lambda)), mTaskPriority(taskPriority) {}

            TReturn Execute(const TaskEventPtr& selfTaskEvent)
            {
                SCOPED_CPU_TIMING(GroupAsync, "AsyncTask");

                return mLambda(selfTaskEvent);
            }

            ThreadID GetThreadToExecuteOn() const
            {
                return ThreadID::AsyncThread;
            }

            Priority GetTaskPriority() const
            {
                return mTaskPriority;
            }

        private:
            TLambda mLambda;
            Priority mTaskPriority;
        };

        return TTask<AsyncTask, TReturn>::Create(predecessors).DispatchTask(std::forward<TLambda>(lambda), taskPriority);
    }

    template<typename TReturn = void, typename TLambda = void>
    TaskEventPtr Async(TLambda&& lambda)
    {
        return Async<TReturn>({}, Priority::Default, std::forward<TLambda>(lambda));
    }

    template<typename TReturn = void, typename TLambda = void>
    TaskEventPtr Async(const TaskEventArray& predecessors, TLambda&& lambda)
    {
        return Async<TReturn>(predecessors, Priority::Default, std::forward<TLambda>(lambda));
    }

    template<typename TReturn = void, typename TLambda = void>
    TaskEventPtr Async(Priority taskPrioirty, TLambda&& lambda)
    {
        return Async<TReturn>({}, taskPrioirty, std::forward<TLambda>(lambda));
    }

    template<ThreadID ThreadToExecuteOn = ThreadID::AnyThread, typename TReturn = void, typename TLambda = void>
    TaskEventPtr DispatchAndHold(const TaskEventArray& predecessors, Priority taskPriority, TLambda&& lambda)
    {
        class DispatchTask
        {
        public:
            DispatchTask(TLambda&& lambda, Priority taskPriority) : mLambda(std::forward<TLambda>(lambda)), mTaskPriority(taskPriority) {}

            TReturn Execute(const TaskEventPtr& selfTaskEvent)
            {
                SCOPED_CPU_TIMING(GroupDispatch, "DispatchTask");
                return mLambda(selfTaskEvent);
            }

            ThreadID GetThreadToExecuteOn() const
            {
                return ThreadToExecuteOn;
            }

            Priority GetTaskPriority() const
            {
                return mTaskPriority;
            }

        private:
            TLambda mLambda;
            Priority mTaskPriority;
        };

        return TTask<DispatchTask, TReturn>::Create(predecessors).HoldTask(std::forward<TLambda>(lambda), taskPriority);
    }

    template<ThreadID ThreadToExecuteOn = ThreadID::AnyThread, typename TReturn = void, typename TLambda = void>
    TaskEventPtr DispatchAndHold(TLambda&& lambda)
    {
        return DispatchAndHold<ThreadToExecuteOn, TReturn>({}, Priority::Default, std::forward<TLambda>(lambda));
    }

    template<ThreadID ThreadToExecuteOn = ThreadID::AnyThread, typename TReturn = void, typename TLambda = void>
    TaskEventPtr DispatchAndHold(const TaskEventArray& predecessors, TLambda&& lambda)
    {
        return DispatchAndHold<ThreadToExecuteOn, TReturn>(predecessors, Priority::Default, std::forward<TLambda>(lambda));
    }

    template<ThreadID ThreadToExecuteOn = ThreadID::AnyThread, typename TReturn = void, typename TLambda = void>
    TaskEventPtr DispatchAndHold(Priority taskPriority, TLambda&& lambda)
    {
        return DispatchAndHold<ThreadToExecuteOn, TReturn>({}, taskPriority, std::forward<TLambda>(lambda));
    }

    template<typename TReturn, typename TLambda = void>
    TaskEventPtr DispatchAndHold(TLambda&& lambda)
    {
        return DispatchAndHold<ThreadID::AnyThread, TReturn>({}, Priority::Default, std::forward<TLambda>(lambda));
    }

    template<typename TReturn, typename TLambda = void>
    TaskEventPtr DispatchAndHold(const TaskEventArray& predecessors, TLambda&& lambda)
    {
        return DispatchAndHold<ThreadID::AnyThread, TReturn>(predecessors, Priority::Default, std::forward<TLambda>(lambda));
    }

    template<typename TReturn, typename TLambda = void>
    TaskEventPtr DispatchAndHold(Priority taskPriority, TLambda&& lambda)
    {
        return DispatchAndHold<ThreadID::AnyThread, TReturn>({}, taskPriority, std::forward<TLambda>(lambda));
    }

    template<typename TReturn = void, typename TLambda = void>
    TaskEventPtr AsyncAndHold(const TaskEventArray& predecessors, Priority taskPriority, TLambda&& lambda)
    {
        class AsyncTask
        {
        public:
            AsyncTask(TLambda&& lambda, Priority taskPriority) : mLambda(std::forward<TLambda>(lambda)), mTaskPriority(taskPriority) {}

            TReturn Execute(const TaskEventPtr& selfTaskEvent)
            {
                SCOPED_CPU_TIMING(GroupAsync, "AsyncTask");

                return mLambda(selfTaskEvent);
            }

            ThreadID GetThreadToExecuteOn() const
            {
                return ThreadID::AsyncThread;
            }

            Priority GetTaskPriority() const
            {
                return mTaskPriority;
            }

        private:
            TLambda mLambda;
            Priority mTaskPriority;
        };

        return TTask<AsyncTask, TReturn>::Create(predecessors).HoldTask(std::forward<TLambda>(lambda), taskPriority);
    }

    template<typename TReturn = void, typename TLambda = void>
    TaskEventPtr AsyncAndHold(TLambda&& lambda)
    {
        return AsyncAndHold<TReturn>({}, Priority::Default, std::forward<TLambda>(lambda));
    }

    template<typename TReturn = void, typename TLambda = void>
    TaskEventPtr AsyncAndHold(const TaskEventArray& predecessors, TLambda&& lambda)
    {
        return AsyncAndHold<TReturn>(predecessors, Priority::Default, std::forward<TLambda>(lambda));
    }

    template<typename TReturn = void, typename TLambda = void>
    TaskEventPtr AsyncAndHold(Priority taskPriority, TLambda&& lambda)
    {
        return AsyncAndHold<TReturn>({}, taskPriority, std::forward<TLambda>(lambda));
    }

#if defined(__cpp_lib_coroutine) && defined(__cpp_lib_concepts)
    template<typename... TEvents>
    class Coros
    {
    public:
        Coros(TEvents&&... events) noexcept : mEvents(std::forward<TEvents>(events)...)
        {
            mCounter.store(std::tuple_size_v<decltype(mEvents)>, std::memory_order_relaxed);
        }

        Coros(const Coros&) = delete;
        Coros& operator=(const Coros&) = delete;

        bool await_ready() const noexcept
        {
            return false;
        }

        template<typename TPromiseType>
        void await_suspend(std::coroutine_handle<TPromiseType> handle) noexcept
        {
            ForEachEvent<std::tuple_size_v<decltype(mEvents)> - 1U>(handle);
        }

        void await_resume() const noexcept
        {
        }

    private:
        template<std::size_t index, typename TPromiseType>
        void ForEachEvent(std::coroutine_handle<TPromiseType> handle)
        {
            auto eventHandle = std::get<index>(mEvents).mHandle;
            auto& promise = eventHandle.promise();
            auto& continuation = promise.mContinuation;
            promise.mEventCounter = &mCounter;

            using EventPromiseType = std::tuple_element_t<index, std::tuple<TEvents...>>::promise_type;
            constexpr auto threadID = EventPromiseType::GetThreadID();
            constexpr auto priority = EventPromiseType::GetPriority();
            constexpr auto otherThreadID = TPromiseType::GetThreadID();
            constexpr auto otherPriority = TPromiseType::GetPriority();
            if constexpr (otherThreadID == threadID && otherPriority == priority)
            {
                continuation = handle;
            }
            else
            {
                if constexpr (otherThreadID == ThreadID::AnyThread || otherThreadID == ThreadID::TaskThread)
                {
                    continuation = DispatchAndHold<otherThreadID>(otherPriority, [handle](auto)
                    {
                        handle.resume();
                    });
                }
                else
                {
                    continuation = AsyncAndHold(otherPriority, [handle](auto)
                    {
                        handle.resume();
                    });
                }
            }

            if constexpr (threadID == ThreadID::AnyThread || threadID == ThreadID::TaskThread)
            {
                Dispatch<threadID>(priority, [eventHandle](auto)
                {
                    eventHandle.resume();
                });
            }
            else if constexpr (threadID == ThreadID::AsyncThread)
            {
                Async(priority, [eventHandle](auto)
                {
                    eventHandle.resume();
                });
            }
            else
            {
                // to-do
            }

            if constexpr (index != 0U)
            {
                ForEachEvent<index - 1U>(handle);
            }
        }

        std::tuple<TEvents...> mEvents;
        std::atomic_uint64_t mCounter;
    };

    template<ThreadID threadID, typename TReturn = void, Priority priority = Priority::Default>
    class CoroEventPtr
    {
        template<typename...>
        friend class Coros;
    public:
        class PromiseTypeBase
        {
            template<typename...>
            friend class Coros;
            friend CoroEventPtr;
        public:
            struct FinalAwaiter
            {
                bool await_ready() const noexcept
                {
                    return false;
                }

                template<typename TPromiseType>
                void await_suspend(std::coroutine_handle<TPromiseType> handle) const noexcept
                {
                    auto& promise = handle.promise();
                    auto ready = [&promise]()
                    {
                        if (!promise.mEventCounter)
                        {
                            return true;
                        }
                        else
                        {
                            if (promise.mEventCounter->fetch_sub(1U, std::memory_order_release) == 1U)
                            {
                                std::atomic_thread_fence(std::memory_order_acquire);
                                return true;
                            }
                            else
                            {
                                return false;
                            }
                        }
                    };

                    if (ready())
                    {
                        if (auto continuation = std::get_if<std::coroutine_handle<>>(&promise.mContinuation))
                        {
                            if (*continuation)
                            {
                                continuation->resume();
                            }
                        }
                        else
                        {
                            (*std::get_if<TaskEventPtr>(&promise.mContinuation))->ReleaseTask();
                        }
                    }

                    if (promise.DecRefCount() == 1U)
                    {
                        std::atomic_thread_fence(std::memory_order_acquire);
                        handle.destroy();
                    }
                }

                void await_resume() const noexcept
                {
                }
            };

            // to-do
            //void* operator new(std::size_t size)
            //{
            //    LOG_INFO("PromiseTypeBase operator new called\n");
            //    return ::operator new(size);
            //}

            // to-do
            //void operator delete(void* address)
            //{
            //    LOG_INFO("PromiseTypeBase operator delete called\n");
            //    ::operator delete(address);
            //}

            PromiseTypeBase() noexcept : mEventCounter(nullptr)
            {
                mCounter.store(1U, std::memory_order_relaxed);
            }

            std::suspend_always initial_suspend() const noexcept
            {
                return {};
            }

            void unhandled_exception() const noexcept
            {
                std::terminate();
            }

            FinalAwaiter final_suspend() const noexcept
            {
                return {};
            }

            //template<typename TEvent, typename = std::enable_if_t<std::is_same_v<std::std::decay_t<TEvent>, CoroEventPtr>>>
            //auto await_transform(TEvent&& event) const noexcept
            //{
            //    return Awaiter(std::move(event));
            //}

            //template<typename... TEvents>
            //auto await_transform(Coros<TEvents...>&& events) const noexcept
            //{
            //    return Awaiter(std::move(events));
            //}

            std::uint64_t IncRefCount() noexcept
            {
                return mCounter.fetch_add(1U, std::memory_order_relaxed);
            }

            std::uint64_t DecRefCount() noexcept
            {
                return mCounter.fetch_sub(1U, std::memory_order_release);
            }

            static constexpr ThreadID GetThreadID() noexcept
            {
                return threadID;
            }

            static constexpr Priority GetPriority() noexcept
            {
                return priority;
            }

        private:
            std::atomic_uint64_t mCounter;
            std::atomic_uint64_t* mEventCounter;
            std::variant<std::coroutine_handle<>, TaskEventPtr> mContinuation;
        };

        template<typename TResult>
        class PromiseType final : public PromiseTypeBase
        {
        public:
            PromiseType() noexcept : mResultStorage{}
            {
            }

            ~PromiseType() noexcept
            {
                std::destroy_at(std::launder(reinterpret_cast<TResult*>(mResultStorage)));
            }

            CoroEventPtr get_return_object() noexcept
            {
                return CoroEventPtr(std::coroutine_handle<PromiseType>::from_promise(*this));
            }

            template<std::convertible_to<TResult> TValue>
            void return_value(TValue&& value) noexcept
            {
                new (mResultStorage) TResult(std::forward<TValue>(value));
            }

            TResult& GetResult() noexcept
            {
                return *std::launder(reinterpret_cast<TResult*>(mResultStorage));
            }

            const TResult& GetResult() const noexcept
            {
                return *std::launder(reinterpret_cast<const TResult*>(mResultStorage));
            }

        private:
            alignas(TResult) std::byte mResultStorage[sizeof(TResult)];
        };

        template<>
        class PromiseType<void> : public PromiseTypeBase
        {
        public:
            CoroEventPtr get_return_object() noexcept
            {
                return CoroEventPtr(std::coroutine_handle<PromiseType>::from_promise(*this));
            }

            void return_void() noexcept
            {
            }

            void GetResult() const noexcept
            {
            }
        };

        using promise_type = PromiseType<TReturn>;

        class Awaiter
        {
            friend CoroEventPtr;
        public:
            bool await_ready() const noexcept
            {
                return false;
            }

            template<typename TPromiseType>
            void await_suspend(std::coroutine_handle<TPromiseType> handle) noexcept
            {
                auto& continuation = mHandle.promise().mContinuation;
                constexpr auto otherThreadID = TPromiseType::GetThreadID();
                constexpr auto otherPriority = TPromiseType::GetPriority();
                if constexpr (otherThreadID == threadID && otherPriority == priority)
                {
                    continuation = handle;
                }
                else
                {
                    if constexpr (otherThreadID == ThreadID::AnyThread || otherThreadID == ThreadID::TaskThread)
                    {
                        continuation = DispatchAndHold<otherThreadID>(otherPriority, [handle](auto)
                        {
                            handle.resume();
                        });
                    }
                    else
                    {
                        continuation = AsyncAndHold(otherPriority, [handle](auto)
                        {
                            handle.resume();
                        });
                    }
                }

                if constexpr (threadID == ThreadID::AnyThread || threadID == ThreadID::TaskThread)
                {
                    Dispatch<threadID>(priority, [this](auto)
                    {
                        mHandle.resume();
                    });
                }
                else if constexpr (threadID == ThreadID::AsyncThread)
                {
                    Async(priority, [this](auto)
                    {
                        mHandle.resume();
                    });
                }
                else
                {
                    // to-do
                }
            }

            decltype(auto) await_resume() const noexcept
            {
                return mHandle.promise().GetResult();
            }

        private:
            Awaiter(std::coroutine_handle<promise_type> handle) noexcept : mHandle(handle)
            {
            }

            std::coroutine_handle<promise_type> mHandle;
        };

        CoroEventPtr(std::coroutine_handle<promise_type>&& handle) noexcept : mHandle(std::exchange(handle, {}))
        {
            mHandle.promise().IncRefCount();
        }

        ~CoroEventPtr()
        {
            if (mHandle && mHandle.promise().DecRefCount() == 1U)
            {
                std::atomic_thread_fence(std::memory_order_acquire);
                mHandle.destroy();
            }
        }

        CoroEventPtr(const CoroEventPtr& other)
        {
            if (other.mHandle)
            {
                other.mHandle.promise().IncRefCount();
            }
            mHandle = other.mHandle;
        }

        CoroEventPtr& operator=(const CoroEventPtr& other) noexcept
        {
            CoroEventPtr(other).Swap(*this);
            return *this;
        }

        CoroEventPtr(CoroEventPtr&& other) noexcept : mHandle(std::exchange(other.mHandle, nullptr))
        {
        }

        CoroEventPtr& operator=(CoroEventPtr&& other) noexcept
        {
            CoroEventPtr(std::move(other)).Swap(*this);
            return *this;
        }

        Awaiter operator co_await() const && noexcept
        {
            return { mHandle };
        }

        void Resume() const noexcept
        {
            mHandle.resume();
        }

        void Destroy() const noexcept
        {
            mHandle.destroy();
        }

        TReturn GetReturnValue() const noexcept
        {
            return mHandle.promise().GetResult();
        }

    private:
        void Swap(CoroEventPtr& other) noexcept
        {
            std::swap(mHandle, other.mHandle);
        }

        std::coroutine_handle<promise_type> mHandle;
    };

    template<typename TReturn = void, ThreadID threadID = ThreadID::AnyThread, Priority priority = Priority::Default>
    using CoroDispatchPtr = CoroEventPtr<threadID, TReturn, priority>;

    template<typename TReturn = void, Priority priority = Priority::Default>
    using CoroAsyncPtr = CoroEventPtr<ThreadID::AsyncThread, TReturn, priority>;

    template<std::invocable TLambda>
    auto Coro(TLambda&& lambda) -> decltype(lambda())
    {
        auto copy = std::forward<TLambda>(lambda);
        co_return co_await copy();
    }

    template<std::invocable TLambda>
    auto Coro(TaskEventArray&& predecessors, TLambda&& lambda) -> decltype(lambda())
    {
        class Predecessors
        {
        public:
            Predecessors(TaskEventArray&& predecessors) noexcept : mPredecessors(std::move(predecessors))
            {
            }

            Predecessors(const Predecessors&) = delete;
            Predecessors& operator=(const Predecessors&) = delete;

            bool await_ready() const noexcept
            {
                return mPredecessors.Complete();
            }

            void await_suspend(std::coroutine_handle<> handle) noexcept
            {
                Dispatch(mPredecessors, Priority::High, [handle](auto)
                {
                    handle.resume();
                });
            }

            void await_resume() const noexcept
            {
            }

        private:
            TaskEventArray mPredecessors;
        };

        auto copy = std::forward<TLambda>(lambda);
        co_await Predecessors(std::move(predecessors));
        co_return co_await copy();
    }

    template<std::invocable TLambda>
    auto Coro(const TaskEventArray& predecessors, TLambda&& lambda) -> decltype(lambda())
    {
        class Predecessors
        {
        public:
            Predecessors(const TaskEventArray& predecessors) noexcept : mPredecessors(predecessors)
            {
            }

            Predecessors(const Predecessors&) = delete;
            Predecessors& operator=(const Predecessors&) = delete;

            bool await_ready() const noexcept
            {
                return mPredecessors.Complete();
            }

            void await_suspend(std::coroutine_handle<> handle) noexcept
            {
                Dispatch(mPredecessors, Priority::High, [handle](auto)
                {
                    handle.resume();
                });
            }

            void await_resume() const noexcept
            {
            }

        private:
            const TaskEventArray& mPredecessors;
        };

        auto copy = std::forward<TLambda>(lambda);
        co_await Predecessors(predecessors);
        co_return co_await copy();
    }
#endif
}
