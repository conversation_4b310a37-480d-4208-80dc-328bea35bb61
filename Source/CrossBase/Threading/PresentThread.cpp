#include "PCH/CrossBasePCHPrivate.h"
#include "PresentThread.h"

#include <chrono>
#include <thread>
#include <math.h>

namespace cross::threading {
static RunnableThread* gPresentThread;
static class PresentThreadRunnable* gPresentThreadRunnable;

class PresentThreadRunnable final : public IRunnable
{
public:
    PresentThreadRunnable() : IRunnable(ThreadID::PresentThread)
    {
        mThreadIndex = static_cast<int32_t>(ThreadID::PresentThread);
    }

    virtual bool Init() override
    {
        TaskSystem::Get()->AttachThread(ThreadID::PresentThread);

        constexpr auto threadName = "Present Thread";
        RunnableThread::SetThreadName(threadName);
        profiling::OnThreadCreation(threadName);

        RunnableThread::SetThreadPriority(ThreadID::PresentThread);

        return true;
    }

    virtual void Run() override
    {
        TaskSystem::Get()->ExecuteTasks(ThreadID::PresentThread);
    }

    virtual void Exit() override
    {
        profiling::OnThreadExit();

        TaskSystem::Get()->DetachThread(ThreadID::PresentThread);
    }
};

void StartPresentThread()
{
    gPresentThreadRunnable = new PresentThreadRunnable();
    gPresentThread = RunnableThread::CreateRunnableThread(gPresentThreadRunnable);

    PresentCommandFence fence;
    fence.Begin();
    fence.Wait();
}

void StopPresentThread()
{
    class RequestReturnTask
    {
    public:
        void Execute(const TaskEventPtr&)
        {
            TaskSystem::Get()->QuitExecution(ThreadID::PresentThread);
        }

        ThreadID GetThreadToExecuteOn() const
        {
            return ThreadID::PresentThread;
        }

        Priority GetTaskPriority() const
        {
            return Priority::Default;
        }
    };

    TaskSystem::Get()->WaitForTasks({TTask<RequestReturnTask>::Create({}, ThreadID::RenderingThread).DispatchTask()}, ThreadID::RenderingThread);

    delete gPresentThread;
    delete gPresentThreadRunnable;
}

void FlushPresentCommands()
{
    Assert(TaskSystem::IsInRenderingThread());

    TaskSystem::Get()->DrainTasks(ThreadID::RenderingThreadLocal);

    PresentCommandFence fence;
    fence.Begin();
    fence.Wait();
}

void PresentCommandFence::Wait(bool stall)
{
    Assert(TaskSystem::IsInRenderingThread());
    TaskSystem::Get()->DrainTasks(ThreadID::RenderingThreadLocal);
    if (mFenceTaskEvent && !mFenceTaskEvent->Complete())
    {
        if (stall)
        {
            mStallEvent.Wait();
        }
        else
        {
            TaskSystem::Get()->WaitForTasks({mFenceTaskEvent}, ThreadID::RenderingThread);
        }
    }
    mStallEvent.Reset();
}
}   // namespace cross::threading
