#pragma once

#include "Task.h"
#include "CECommon/Common/FrameParam.h"
#include "CrossBase/Template/TypeSafeHandle.hpp"
#include "NativeGraphicsInterface/NGI.h"

DECLARE_CPU_TIMING_GROUP(RenderingThread);

namespace cross::threading
{
    void CROSS_BASE_API StartRenderingThread();

    void CROSS_BASE_API StopRenderingThread();

    void CROSS_BASE_API FlushRenderingCommands();

    class CROSS_BASE_API RenderingCommandFence
    {
    public:
        void Begin()
        {
            Assert(TaskSystem::IsInGameThread());

            mFenceTaskEvent = TTask<FenceTask>::Create({}, ThreadID::GameThread).DispatchTask(mStallEvent);
        }

        bool Complete() const
        {
            Assert(TaskSystem::IsInGameThread());

            return mFenceTaskEvent && mFenceTaskEvent->Complete();
        }

        void Wait(bool stall = false);

    private:
        class FenceTask
        {
        public:
            FenceTask(StallEvent& stallEvent) : mStallEvent(stallEvent) {}

            void Execute(const TaskEventPtr&)
            {
                mStallEvent.Trigger();
            }

            ThreadID GetThreadToExecuteOn() const
            {
                return ThreadID::RenderingThread;
            }

            Priority GetTaskPriority() const
            {
                return Priority::Default;
            }

        private:
            StallEvent& mStallEvent;
        };

        TaskEventPtr mFenceTaskEvent;

        StallEvent mStallEvent;
    };

    class CROSS_BASE_API FrameFrequencyRestriction
    {
    public:
        FrameFrequencyRestriction(const double inFrequency)
            : mFrequency(inFrequency)
            , mDurationInUs(1000.0 * 1000.0 / mFrequency)
        {
            Assert(mFrequency > 0);
        }

        void SetFrameRate(const double inFrequency)
        {
            mFrequency = inFrequency;
            mDurationInUs = (1000.0 * 1000.0 / mFrequency);
        }

        const double GetFrameRate() const
        {
            return mFrequency;
        }

        void Sync() const;

        inline double FrameDurationUs() const
        {
            return mDurationInUs;
        }

    public:
        mutable std::chrono::steady_clock::time_point a = std::chrono::steady_clock::now();
        mutable std::chrono::steady_clock::time_point b = std::chrono::steady_clock::now();
        mutable std::chrono::duration<double, std::micro> Delta = std::chrono::duration<double, std::micro>(0);

    private:
        inline std::chrono::duration<double, std::micro> WorkTimeUs() const
        {
            return a - b;
        }
        inline std::chrono::duration<double, std::micro> SleepTimeUs() const
        {
            return b - a;
        }

        void Sleep(double seconds) const;

    private:
        double mFrequency = 60.0f;
        double mDurationInUs = 1000.0 * 1000.0 / 60.0;

        mutable double mEstimate = 5e-3;
        mutable double mMean = 5e-3;
        mutable double mM2 = 0;
        mutable UInt64 mCount = 1;
    };

    template<typename TLambda>
    inline TaskEventPtr DispatchRenderingCommand(TLambda&& lambda, const char* token = nullptr)
    {
        class RenderingCommand
        {
        public:
#ifdef ENABLE_RENDERING_COMMAND_TOKEN
            RenderingCommand(TLambda&& lambda, const char* token = nullptr) : mLambda(std::forward<TLambda>(lambda)), mToken(token) {}
#else
            RenderingCommand(TLambda&& lambda, const char* = nullptr) : mLambda(std::forward<TLambda>(lambda)) {}
#endif

            void Execute(const TaskEventPtr&)
            {
#ifdef ENABLE_RENDERING_COMMAND_TOKEN
                if (mToken)
                {
                    SCOPED_CPU_TIMING_DYNAMIC(RenderingThread, fmt::format("RenderingCommand <- {}", mToken));

                    mLambda();
                }
                else
#endif
                {
#ifndef CROSSENGINE_RELEASE
                    SCOPED_CPU_TIMING(RenderingThread, "RenderingCommand");
#endif

                    mLambda();
                }
            }

            ThreadID GetThreadToExecuteOn() const
            {
                return ThreadID::RenderingThread;
            }

            Priority GetTaskPriority() const
            {
                return Priority::Default;
            }

        private:
            TLambda mLambda;
#ifdef ENABLE_RENDERING_COMMAND_TOKEN
            const char* mToken;
#endif
        };
        
        switch (TaskSystem::Get()->GetRenderingThreadScheduling())
        {
        case MasterThreadScheduling::Separated:
            //Assert(TaskSystem::GetCurrentThreadID() != threading::ThreadID::RenderingThread);
            return TTask<RenderingCommand>::Create().DispatchTask(std::forward<TLambda>(lambda), token);
        case MasterThreadScheduling::Inlined:
            lambda();
            return TaskEvent::Create(nullptr);
        case MasterThreadScheduling::Disabled:
        default:
            return TaskEvent::Create(nullptr);
        }
    }
}

#ifdef ENABLE_RENDERING_COMMAND_TOKEN
#define DispatchRenderingCommandWithToken(...) cross::threading::DispatchRenderingCommand(__VA_ARGS__, __func__)
#else
#define DispatchRenderingCommandWithToken(...) cross::threading::DispatchRenderingCommand(__VA_ARGS__)
#endif
