#pragma once

namespace cross::detail
{
    template <typename T, typename = TEnableIfSupportBit<T>>
    inline /*constexpr*/ int CountZeroL(T value) noexcept
    {
        if constexpr (sizeof(T) == sizeof(std::uint16_t))
        {
            return static_cast<int>(__lzcnt16(value));
        }
        else if constexpr (sizeof(T) == sizeof(std::uint32_t))
        {
            return static_cast<int>(__lzcnt(value));
        }
        else if constexpr (sizeof(T) == sizeof(std::uint64_t))
        {
            return static_cast<int>(__lzcnt64(value));
        }
        else
        {
            static_assert(sizeof(T) < sizeof(std::uint16_t));
            constexpr auto offset = std::numeric_limits<T>::digits - std::numeric_limits<std::uint16_t>::digits;
            return static_cast<int>(__lzcnt16(value)) + offset;
        }
    }

    template <typename T, typename = TEnableIfSupportBit<T>>
    inline /*constexpr*/ int CountZeroR(T value) noexcept
    {
        if constexpr (sizeof(T) <= sizeof(std::uint32_t))
        {
            std::uint32_t result;
            _BitScanForward(&result, value);
            return static_cast<int>(result);
        }
        else
        {
            static_assert(sizeof(T) <= sizeof(std::uint64_t));
            std::uint32_t result;
            _BitScanForward64(&result, value);
            return static_cast<int>(result);
        }
    }

    template <typename T, typename = TEnableIfSupportBit<T>>
    inline /*constexpr*/ int PopCount(T value) noexcept
    {
        if constexpr (sizeof(T) <= sizeof(std::uint16_t))
        {
            return static_cast<int>(__popcnt16(value));
        }
        else if constexpr (sizeof(T) <= sizeof(std::uint32_t))
        {
            return static_cast<int>(__popcnt(value));
        }
        else
        {
            static_assert(sizeof(T) <= sizeof(std::uint64_t));
            return static_cast<int>(__popcnt64(value));
        }
    }
}