#pragma once
#include <vector>
#include "CrossBase/Math/CrossMath.h"

namespace cross::editor {

enum class ImportVertexChannel : UInt32
{
    POSITION = 0x01,
    NORMAL = 0x02,
    TANGENT = 0x04,
    COLOR = 0x08,
    UV0 = 0x10,
    UV1 = 0x20,
    UV2 = 0x40,
    UV3 = 0x80
};

inline UInt32 operator&(ImportVertexChannel lhs, ImportVertexChannel rhs)
{
    return static_cast<UInt32>(lhs) & static_cast<UInt32>(rhs);
}

inline ImportVertexChannel& operator|=(ImportVertexChannel& lhs, ImportVertexChannel rhs)
{
    UInt32 ltmp = static_cast<UInt32>(lhs);
    UInt32 rtmp = static_cast<UInt32>(rhs);
    return lhs = static_cast<ImportVertexChannel>(ltmp |= rtmp);
}

struct ASSET_API ShapeDesc
{
    // [0.0, 1.0]
    float NormalizedFullWeight{1.f};

    ImportVertexChannel VertexChannels = ImportVertexChannel::POSITION;

    std::vector<Float3> Positions;
    std::vector<Float3> Normals;
    std::vector<Float4> Tangents;
};

struct ASSET_API DeltaShapeDesc : public ShapeDesc
{
    // Vertex Id to index mMeshBuildVertices array in MeshDescription
    std::vector<UInt32> InfluencedVertexIDs;
};

struct ASSET_API BlendShapeChannel
{
    std::string Name;

    // Each shape has all vertices's info one MeshDescription has.
    std::vector<ShapeDesc> TargetShapes;
    // Each delta shape only has vertices's info it actually influences, and influence has been calculated as delta to base shape.
    std::vector<DeltaShapeDesc> DeltaShapes;
};

struct BlendShapeDeformer
{
    void Reserve(UInt32 channelCount)
    {
        Channels.resize(channelCount);
        ChannelValidFlags.resize(channelCount, false);
    }

    bool HasBlendShape() const
    {
        for (auto flag : ChannelValidFlags)
        {
            if (flag) 
            {
                return true;
            }
        }
        return false;
    }

    std::vector<bool> ChannelValidFlags;
    std::vector<BlendShapeChannel> Channels;
};

}   // namespace cross::editor