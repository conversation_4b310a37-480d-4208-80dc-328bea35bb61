#pragma once
#include "AssetPipeline/Import/ModelImporter/ModelImportSettings.h"
#include "AssetPipeline/Import/ModelImporter/ModelImporter.h"
#include "AssetPipeline/Protocol/Model/ImportMeshAssetData.h"
#include "CrossSchema/ImportMeshAssetData_generated.h"

namespace cross { namespace editor {
    class OBJImporter : public ModelImporter
    {
    public:
        OBJImporter();
        ~OBJImporter() = default;

        void ImportAsset(const std::string& assetFilename, const std::string& ndaSavePath, ImportSetting* setting) override;
        bool CheckAssetName(const char* name) const override;

    private:
        bool                    IfExistChannel(VertexChannel channel);
        UInt32                  GetVertexCount(VertexChannel posChannel);
        VertexChannelAssetData& GetChannelAssetData(VertexChannel channel)
        {
            for (int i = 0; i < mVertexChannelData.size(); i++)
            {
                if (mVertexChannelData[i].mVertexChannel == (UInt32)channel)
                    return mVertexChannelData[i];
            }
            throw std::runtime_error(
                "GetChannelAssetData cannot find the channel in mVertexChannelData\n");
        }
        void CalculateWholeAABB();
        void AddSubMeshBegin();
        void AddSubMeshEnd();
        bool LoadObjAssetDataFromFile();
        bool AssembleImportMeshAssetDataT();
        bool ToImportMeshAssetDataT();
        bool ExportNdaFile();

        template<class T1, class T2>
        bool AddChannelData(VertexSemantic sem, VertexChannel channel, VertexFormat format,
                            std::vector<T1>& inVec)
        {
            if (!IfExistChannel(channel))
            {
                mVertexChannelData.emplace_back();
                VertexChannelAssetData& vertexChannel = mVertexChannelData.back();
                vertexChannel.mStride                 = sizeof(T2);
                vertexChannel.mVertexChannel          = (UInt32)channel;
                vertexChannel.mDataFormat             = (UInt32)format;
                mVertexChannelSemanticMask |= (UInt32)sem;
                vertexChannel.mData.clear();
            }

            VertexChannelAssetData& vertexChannel = GetChannelAssetData(channel);
            unsigned int            orgSize       = (unsigned int)vertexChannel.mData.size();
            vertexChannel.mData.resize(orgSize + vertexChannel.mStride * inVec.size());
            assert(vertexChannel.mData.size() == orgSize + inVec.size() * sizeof(T2));
            memcpy(vertexChannel.mData.data() + orgSize, inVec.data(), inVec.size() * sizeof(T2));

            return true;
        }

        CrossSchema::ImportMeshAssetDataT mMeshAssetDataT;

        std::string         mAssetFileName;
        std::string         mNdaSavePath;

        std::vector<UInt32>   mIdxLoad;
        std::vector<AssetMath::Vector3f> mVtxLoad;
        std::vector<AssetMath::Vector4f> mVcolLoad;
        std::vector<AssetMath::Vector3f> mNmlLoad;
        std::vector<AssetMath::Vector2f> mUVLoad;
        std::vector<AssetMath::Vector2f> mUV2Load;
        std::vector<AssetMath::Vector4f> mUV3Load;
        std::vector<AssetMath::Vector3f> mUV4Load;
        std::vector<AssetMath::Vector4f> mTanLoad;

        int                                mSubMeshCnt;
        bool                               mSubMeshMode;
        std::vector<UInt32>                mIdxOffset;
        std::vector<std::vector<UInt32>>   mIdxBuffer;
        std::vector<std::vector<AssetMath::Vector3f>> mVtxBuffer;
        std::vector<std::vector<AssetMath::Vector4f>> mVcolBuffer;
        std::vector<std::vector<AssetMath::Vector3f>> mNmlBuffer;
        std::vector<std::vector<AssetMath::Vector2f>> mUVBuffer;
        std::vector<std::vector<AssetMath::Vector2f>> mUV2Buffer;
        std::vector<std::vector<AssetMath::Vector4f>> mUV3Buffer;
        std::vector<std::vector<AssetMath::Vector3f>> mUV4Buffer;
        std::vector<std::vector<AssetMath::Vector4f>> mTanBuffer;

        UInt32      mVersion{0};
        std::string mName;
        UInt32      mVertexCount{0};
        UInt32      mPrimitiveCount{0};
        UInt32      mVertexChannelSemanticMask{0};

        IndexStreamAssetData                mIndexStream;
        std::vector<VertexChannelAssetData> mVertexChannelData;

        std::vector<MeshPartAssetInfo> mMeshPartInfo;

        std::vector<UInt32> mMeshPartLodStartIndex;

        struct MeshLODParam
        {
            float screenRelativeTransitionHeight;
            float fadeTransitionWidth;
        };

        std::vector<MeshLODParam> mMeshPartLodParams;

        std::vector<std::string>             mMaterialNames;
        std::vector<std::string>             mMeshPartNames;
        std::vector<ImportMeshCollisionNode> mCollisionTree;
        MeshBound                            mAABB;
        SkeletonDesc                       mRefSkeleton;
        std::vector<std::array<float, 16>>   mBindPoseInv;

        UInt16              mCustomAttributeVersion{0};
        UInt16              mCustomAttributeVersionFlag{0};
        CustomAttributeInfo mCustomAttributeInfo;
        std::vector<UInt8>  mCustomAttribute;
    };
}}   // namespace cross::editor
