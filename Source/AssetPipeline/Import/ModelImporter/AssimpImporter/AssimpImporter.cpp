
/*

#include "AssetPipeline/PCH/AssetPipelinePCH.h"
#include "AssimpImporter.h"
#include <assimp/Importer.hpp>
#include <assimp/postprocess.h>
#include <assimp/scene.h>

//static_assert(sizeof(cross::ImportNode) == sizeof(cross::Node), "Node size not match!!");			// TODO

namespace cross { namespace editor 
{
    bool AssimpImporter::ImportAsset(const std::string& assetFilename, ModelImportSettings& settings, const std::string& ndaSavePath)
    {
        unsigned int ppsteps = 0
            | aiProcess_CalcTangentSpace // Calculates the tangents and bitangents for the imported meshes.
            | aiProcess_Triangulate // triangulate polygons with more than 3 edges
            //| aiProcess_JoinIdenticalVertices // join identical vertices/ optimize indexing
            //| aiProcess_MakeLeftHanded // convert everything to D3D left handed space
            //| aiProcess_FixInfacingNormals // find normals facing inwards and inverts them
            | aiProcess_GenSmoothNormals // generate smooth normal vectors if not existing
            | aiProcess_ConvertToLeftHanded // convert everything to D3D left handed space
            //| aiProcess_PreTransformVertices // which could lead bone & animation missing
            | aiProcess_RemoveRedundantMaterials
            | aiProcess_OptimizeMeshes
            | aiProcess_SortByPType  // make 'clean' meshes which consist of a single typ of primitives)
            | aiProcess_SplitLargeMeshes; // split large, unrenderable meshes into submeshes

        Assimp::Importer imp;
        //imp.SetPropertyBool(AI_CONFIG_PP_PTV_KEEP_HIERARCHY, true);
        const aiScene* scene = imp.ReadFile(assetFilename, ppsteps);
        if (scene == nullptr)
        {
            LOG_EDITOR_ERROR("[AssimpImporter] Load model [ {} ] failed.", assetFilename);
            return false;
        }

		ImportScene importScene;
		ImportNodeData(*scene, importScene.RootNode, assetFilename);
		ImportMeshData(*scene, importScene.Meshes);
		ImportSkeletonData(*scene, importScene.Skeleton);
		ImportAnimationData(*scene, importScene.Animations);

        if (!ImportAssetImpl(importScene, assetFilename, ndaSavePath))
        {
			LOG_EDITOR_ERROR("[AssimpImporter] Import model [ {} ] failed.", assetFilename);
            return false;
        }

        return true;
    }

    bool AssimpImporter::CheckAssetName(const char * name) const
    {
        if (name == nullptr) return false;
        const char* extension = strrchr(name, '.');
        return Assimp::Importer().IsExtensionSupported(extension);
    }

    //void TransformCoord(aiVector3D* pout, const aiVector3D* pIn, const aiMatrix4x4* pmatrix)
    //{
    //    pout->x = pIn->x * pmatrix->a1 + pIn->y * pmatrix->b1 + pIn->z * pmatrix->c1 + pmatrix->d1;
    //    pout->y = pIn->x * pmatrix->a2 + pIn->y * pmatrix->b2 + pIn->z * pmatrix->c2 + pmatrix->d2;
    //    pout->z = pIn->x * pmatrix->a3 + pIn->y * pmatrix->b3 + pIn->z * pmatrix->c3 + pmatrix->d3;
    //    float w = pmatrix->d4;

    //    pout->x /= w;
    //    pout->y /= w;
    //    pout->z /= w;
    //}

    //int CalculateBounds(const aiScene* pcScene, aiNode* piNode, aiVector3D* p_avOut, const aiMatrix4x4& piMatrix)
    //{
    //    aiMatrix4x4 mTemp = piNode->mTransformation;
    //    mTemp.Transpose();
    //    aiMatrix4x4 aiMe = mTemp * piMatrix;

    //    for (unsigned int i = 0; i < piNode->mNumMeshes; ++i)
    //    {
    //        for (unsigned int a = 0; a < pcScene->mMeshes[piNode->mMeshes[i]]->mNumVertices; ++a)
    //        {
    //            aiVector3D pc = pcScene->mMeshes[piNode->mMeshes[i]]->mVertices[a];

    //            aiVector3D pc1;
    //            //D3DXVec3TransformCoord((D3DXVECTOR3*)&pc1, (D3DXVECTOR3*)&pc,
    //            //	(D3DXMATRIX*)&aiMe);
    //            TransformCoord(&pc1, &pc, &aiMe);

    //            p_avOut[0].x = min(p_avOut[0].x, pc1.x);
    //            p_avOut[0].y = min(p_avOut[0].y, pc1.y);
    //            p_avOut[0].z = min(p_avOut[0].z, pc1.z);
    //            p_avOut[1].x = max(p_avOut[1].x, pc1.x);
    //            p_avOut[1].y = max(p_avOut[1].y, pc1.y);
    //            p_avOut[1].z = max(p_avOut[1].z, pc1.z);
    //        }
    //    }
    //    for (unsigned int i = 0; i < piNode->mNumChildren; ++i)
    //    {
    //        CalculateBounds(pcScene, piNode->mChildren[i], p_avOut, aiMe);
    //    }
    //    return 1;
    //}

    bool AssimpImporter::ImportNodeData(aiScene const& aiScene, ImportNode& root, const std::string& assetFilename)
    {
        if (!aiScene.mRootNode)
            return false;

		auto& aiRootNode = *aiScene.mRootNode;

		// assign name
		if (aiRootNode.mName.length > 0)
		{
			root.Name = aiRootNode.mName.data;
		}
		else
		{
			root.Name = PathHelper::GetBaseFileName(assetFilename, true);
		}

		// assign transform
		ConvertMatrix(aiRootNode.mTransformation, root.LocalTransform);

		auto meshCount = aiRootNode.mNumMeshes;
		auto childCount = aiRootNode.mNumChildren;
		auto splitOffset = meshCount;               // used for split
		if (meshCount > 1)
		{
			// split node by meshes
			auto totalCount = childCount + meshCount;
			root.Children.resize(totalCount);

			// split node dosen`t have child node
			for (auto loop = 0u; loop < meshCount; ++loop)
			{
				if (!ImportNodeImpl(aiRootNode, *aiScene.mMeshes[aiRootNode.mMeshes[loop]], root, root.Children[loop], loop))
					return false;
			}
		}
		else
		{
			if (meshCount != 0u)
			{
				root.MeshIndex = aiRootNode.mMeshes[0u];
			}

			splitOffset = 0u;
			root.Children.resize(childCount);
		}

		for (auto loop = 0u; loop < childCount; ++loop)
		{
			auto importChildIndex = loop + splitOffset;
			if (!ImportNodeImpl(aiScene, *aiRootNode.mChildren[loop], root.Children[importChildIndex]))
				return false;
		}

		return true;
    }

    namespace
    {
        static void ImportVerticesFromAssimpMesh(aiMesh const* mesh, ImportMeshVertexDescription& vertices)
        {
            size_t const verticesNumber = mesh->mNumVertices;

            for (auto loop = 0u; loop < verticesNumber; ++loop)
            {

                // assign position
                auto Pos = reinterpret_cast<Vector3f&>(mesh->mVertices[loop]);
				vertices.GetAttributes()->Attribute<data::Vec3f>(vertices.mPos).datavector().push_back(Pos);
                // assign normal
                if (mesh->mNormals)
                {
                    auto Normal = reinterpret_cast<Vector3f&>(mesh->mNormals[loop]);
					vertices.GetAttributes()->Attribute<data::Vec3f>(vertices.mNormal).datavector().push_back(Normal);
                }

                // assign color
                if (mesh->HasVertexColors(0))
                {
                    ColorRGBAf color = reinterpret_cast<ColorRGBAf&>(mesh->mColors[0][loop]);
					vertices.GetAttributes()->Attribute<UInt32>(vertices.mCol).datavector().push_back(color.GetHex());
                }

                // assign UV
                if (mesh->HasTextureCoords(0))
                {
                   auto TexCoord = reinterpret_cast<Vector2f&>(mesh->mTextureCoords[0][loop]);
				   vertices.GetAttributes()->Attribute<data::Vec2f>(vertices.mUV1).datavector().push_back(TexCoord);
                }

                 if (mesh->HasTextureCoords(1))
                 {
                     auto TexCoord2 = reinterpret_cast<Vector2f&>(mesh->mTextureCoords[1][loop]);
					 vertices.GetAttributes()->Attribute<data::Vec2f>(vertices.mUV2).datavector().push_back(TexCoord2);
                 }

                // assign Tangent
                // TODO ... WHY so Complex
				///WTF
                if (mesh->HasTangentsAndBitangents())
                {
                    auto& t = reinterpret_cast<Vector3f&>(mesh->mTangents[loop]);
                    auto& b = reinterpret_cast<Vector3f&>(mesh->mBitangents[loop]);
					auto& normal = vertices.GetAttributes()->Attribute<data::Vec3f>(vertices.mNormal).datavector()[loop];
                    data::Vec4f Tangent =
                    {
                        t.x(), t.y(), t.z(),
                        t.cross(b).dot(normal) > 0.0f ? 1.0f : -1.0f
                    };
					vertices.GetAttributes()->Attribute<data::Vec4f>(vertices.mTangent).datavector().push_back(Tangent);

                }
            }
        }
    }

    bool AssimpImporter::ImportMeshData(aiScene const& aiScene, ImportMeshes& meshes)
    {
        auto meshCount = aiScene.mNumMeshes;
        meshes.MeshesData.resize(meshCount);

        // normal pass to import assets data
        for (auto loop = 0u; loop < meshCount; ++loop)
        {
            auto mesh = aiScene.mMeshes[loop];
            auto& importMesh = meshes.MeshesData[loop];

            // import name
            if (mesh->mName.length)
            {
                importMesh.Name = mesh->mName.data;
            }
            else
            {
                importMesh.Name = Format("Mesh{}", loop);
            }

            // import indices
            auto indicesNumber = mesh->mNumFaces * 3;

            importMesh.Indices.Init(indicesNumber, mesh->mNumVertices);
            if (mesh->mNumVertices > (std::numeric_limits<UInt16>::max)())
            {
                auto ptr = importMesh.Indices.GetData<UInt32>();
                for (auto innerLoop = 0u; innerLoop < mesh->mNumFaces; ++innerLoop, ptr += 3)
                {
                    auto& face = mesh->mFaces[innerLoop];
                    Assert(face.mNumIndices == 3);
                    std::memcpy(ptr, face.mIndices, 3 * sizeof(UInt32));
                }
            }
            else
            {
                auto ptr = importMesh.Indices.GetData<UInt16>();
                for (auto innerLoop = 0u; innerLoop < mesh->mNumFaces; ++innerLoop, ptr += 3)
                {
                    auto& face = mesh->mFaces[innerLoop];
                    Assert(face.mNumIndices == 3);
                    *reinterpret_cast<std::array<UInt16, 3>*>(ptr) = 
                    {
                        static_cast<UInt16>(face.mIndices[0]),
                        static_cast<UInt16>(face.mIndices[1]),
                        static_cast<UInt16>(face.mIndices[2])
                    };
                }
            }

            // import vertices
            //auto& verticesData = importMesh.Vertices;
            //verticesData.resize(verticesNumber);

                ImportVerticesFromAssimpMesh(mesh, importMesh.VertexData);
        }

        return true;
    }

    bool AssimpImporter::ImportSkeletonData(aiScene const& aiScene, ImportSkeletonDep& skeleton)
    {
        if (!aiScene.HasMeshes())
            return false;

        std::vector<std::vector<ImportBone>> boneses;
        auto meshCount = aiScene.mNumMeshes;
        boneses.reserve(meshCount);

        // map to vector of vector
        std::transform(aiScene.mMeshes, aiScene.mMeshes + meshCount, std::back_inserter(boneses),
            [this](auto const mesh) -> std::vector<ImportBone> 
        {
            if (!mesh->HasBones())
                return {};

            auto boneCount = mesh->mNumBones;
            std::vector<ImportBone> bones;
            bones.reserve(boneCount);
            std::transform(mesh->mBones, mesh->mBones + boneCount, std::back_inserter(bones),
                [this](auto const aiBone) -> ImportBone
            {
                ImportBone bond;

                // assign bone name
                bond.BoneName = aiBone->mName.data;
                // assign weights count
                //TODO: atuxili delete
//				bond.NumWeights = aiBone->mNumWeights;
                // assign offset transform matrix
//                ConvertMatrix(aiBone->mOffsetMatrix, bond.OffsetMatrix);
                // assign weights
//                auto weightCount = aiBone->mNumWeights;
                //bond.Weights.reserve(weightCount);
                //std::transform(aiBone->mWeights, aiBone->mWeights + weightCount, std::back_inserter(bond.Weights),
                //    [](auto const& weight) -> ImportVertexWeight
                //{
                //    return { weight.mVertexId, weight.mWeight };
                //});

                return bond;
            });

            return bones;
        });

        // reduce to vector
        auto result = std::accumulate(boneses.begin(), boneses.end(), std::vector<ImportBone>{},
            [](std::vector<ImportBone> acc, std::vector<ImportBone> bones)
        {
            std::vector<ImportBone> result = std::move(acc);
            result.insert(result.cend(), std::make_move_iterator(bones.begin()), std::make_move_iterator(bones.end()));
            return result;
        });

        skeleton.Bones = std::move(result);

        // result true if we have got some bones
        return skeleton.Bones.size() > 0;
    }

    bool AssimpImporter::ImportAnimationData(aiScene const& scene, ImportAnimations& animations)
    {
        if (!scene.HasAnimations())
            return false;

        //auto animationCount = scene.mNumAnimations;
        //std::vector<ImportAnimation> animations;
        //animations.NodeAnimationSequences.reserve(animationCount);
        //std::transform(scene.mAnimations, scene.mAnimations + animationCount, std::back_inserter(animations.NodeAnimationSequences),
        //    [this](aiAnimation const* aiAnimation) -> ImportAnimationSequence
        //{
        //    ImportAnimationSequence animation;
        //    // assign animation name
        //    if (aiAnimation->mName.length > 0)
        //    {
        //        animation.Name = aiAnimation->mName.data;
        //    }
        //    // assign node animation
        //    auto channelCount = aiAnimation->mNumChannels;
        //    animation.NodeAnimations.reserve(channelCount);
//            std::transform(aiAnimation->mChannels, aiAnimation->mChannels + channelCount, std::back_inserter(animation.NodeAnimations),
//                [this](aiNodeAnim const* aiNodeAnim) -> ImportNodeAnimation
//            {
//                ImportNodeAnimation nodeAnim;
				//TODO: atuxili delete
//                 // assign name
//                 if (aiNodeAnim->mNodeName.length > 0)
//                 {
//                     nodeAnim.NodeName = aiNodeAnim->mNodeName.data;
//                 }
//                 // assign pre state
//                 nodeAnim.PreState = GetAnimationBehaviour(aiNodeAnim->mPreState);
//                 // assign post state
//                 nodeAnim.PostState = GetAnimationBehaviour(aiNodeAnim->mPostState);
                // assign position keys
                //nodeAnim.PositionKeys.reserve(aiNodeAnim->mNumPositionKeys);
                //std::transform(aiNodeAnim->mPositionKeys, aiNodeAnim->mPositionKeys + aiNodeAnim->mNumPositionKeys, 
                //    std::back_inserter(nodeAnim.PositionKeys), [](auto const& position)
                //{
                //    return VectorKey{ position };
                //});
                //// assign rotation keys
                //nodeAnim.RotationKeys.reserve(aiNodeAnim->mNumRotationKeys);
                //std::transform(aiNodeAnim->mRotationKeys, aiNodeAnim->mRotationKeys + aiNodeAnim->mNumRotationKeys,
                //    std::back_inserter(nodeAnim.RotationKeys), [](auto const& rotation) 
                //{
                //    return QuaternionKey{ rotation };
                //});
                //// assign scale keys
                //nodeAnim.ScaleKeys.reserve(aiNodeAnim->mNumScalingKeys);
                //std::transform(aiNodeAnim->mScalingKeys, aiNodeAnim->mScalingKeys + aiNodeAnim->mNumScalingKeys,
                //    std::back_inserter(nodeAnim.ScaleKeys), [](auto const& scale)
                //{
                //    return VectorKey{ scale };
                //});
//                return nodeAnim;
//            });
//            return animation;
        //});
        return true;
    }

    bool AssimpImporter::ImportNodeImpl(aiScene const& aiScene, aiNode const& aiNode, ImportNode& node)
    {
        // assign name
        if (aiNode.mName.length > 0)
        {
            node.Name = aiNode.mName.data;
        }
        else
        {
            // TODO ... better name
            node.Name = "Unknown";
        }

        // assign transform
        ConvertMatrix(aiNode.mTransformation, node.LocalTransform);

        auto meshCount = aiNode.mNumMeshes;
        auto childCount = aiNode.mNumChildren;
        auto splitOffset = meshCount;               // used for split
        if (meshCount > 1)
        { 
            // split node by meshes
            auto totalCount = childCount + meshCount;
            node.Children.resize(totalCount);

            // split node dosen`t have child node
            for (auto loop = 0u; loop < meshCount; ++loop)
            {
                if (!ImportNodeImpl(aiNode, *aiScene.mMeshes[aiNode.mMeshes[loop]], node, node.Children[loop], loop))
                    return false;
            }
        }
        else
        {
            if (meshCount != 0u)
            {
                node.MeshIndex = aiNode.mMeshes[0u];
            }

            splitOffset = 0u;
            node.Children.resize(childCount);
        }

        for (auto loop = 0u; loop < childCount; ++loop)
        {
            auto importChildIndex = loop + splitOffset;
            if (!ImportNodeImpl(aiScene, *aiNode.mChildren[loop], node.Children[importChildIndex]))
                return false;
        }

        return true;
    }

    bool AssimpImporter::ImportNodeImpl(aiNode const& aiNode, aiMesh const& aiMesh, ImportNode const& parent, ImportNode& child, UInt32 index)
    {
        // assign Name
        if (aiMesh.mName.length > 0)
        {
            child.Name = aiMesh.mName.data;
        }
        else
        {
            // TODO ... better name
            Format("{}_{}", parent.Name, index);
        }

        // split node doesn`t have a child
        // leave LocalTransform as identity
        // assign index
        child.MeshIndex = aiNode.mMeshes[index];

        return true;
    }

	void AssimpImporter::ConvertMatrix(const aiMatrix4x4& aiMatrix, Matrix4x4f& matrix)
	{
        matrix = *reinterpret_cast<Matrix4x4f const*>(aiMatrix[0]);
        matrix.transpose();
	}

	AnimBehaviour AssimpImporter::GetAnimationBehaviour(aiAnimBehaviour behaviour)
	{
		switch (behaviour)
		{
		case aiAnimBehaviour_DEFAULT:
			return AnimBehaviour_Default;
		case aiAnimBehaviour_CONSTANT:
			return AnimBehaviour_Constant;
			break;
		case aiAnimBehaviour_LINEAR:
			return AnimBehaviour_Linear;
		case aiAnimBehaviour_REPEAT:
			return AnimBehaviour_Repeat;
		default:
			throw std::invalid_argument{ "Unexpected aiAnimBehaviour value" };
		}
	}

} }
*/
