#include <queue>
#include "EnginePrefix.h"
#include "FBXImporter.h"
#include "FBXConversion.h"
#include "FBXHelper.h"
#include "FBXImportData.h"
#include "FBXImportUtility.h"
#include "CECommon/Common/EngineGlobal.h"
#include "PhysicsEngine/PhysicsEngine.h"
#include "PhysicsEngine/PhysicsCooker.h"
#include "CrossBase/Log.h"
#include "CrossBase/String/StringHelper.h"
#include "AssetPipeline/Import/AssetImporterManager.h"
#include "AssetPipeline/Import/TextureImporter/TextureImporter.h"
#include "AssetPipeline/Cook/AssetCookerManager.h"
#include "Resource/Material.h"
#include "Resource/ResourceManager.h"
#include "Resource/ResourceConvertManager.h"
#include "Resource/AssetStreaming.h"
#include "Resource/Prefab/PrefabResource.h"
#include "Resource/resourceasset.h"
#include "Resource/Texture/Texture2D.h"
#include "Resource/Texture/Texture2DArray.h"
#include "Resource/HLODResource.h"
#include "CECommon/Utilities/TransformConvert.h"
#include "AssetPipeline/MeshSimplifier/meshoptimizer/meshoptimizer.h"

namespace cross { namespace editor {
    static constexpr SInt32 INDEX_NONE = -1;
    static constexpr SInt32 MAX_STATIC_MESH_LODS = 8;
    static constexpr float DEFAULT_SAMPLERATE = 30.0f;
    static constexpr std::array<UInt32, 3> TRIANGLE_CCW_INDEX = {0, 2, 1};
    static constexpr float TRIANGLE_AREA_THRESHOLD = 1.e-8f;

    static constexpr std::string_view M_DIFFUSE_KEY = "color_texture";
    static constexpr std::string_view M_NORMAL_KEY = "normal_texture";
    static constexpr std::string_view M_SPECULAR_KEY = "specular_texture";
    static constexpr std::string_view M_EMISSIVE_KEY = "emissive_texture";

    static inline bool IsValidIndex(SInt32 index)
    {
        return index >= 0 && index < 3;
    }

    FBXImporter::FBXImporter()
    {
        // mImportSettings = setting;
        // mSdkManager = FbxManager::Create();
        // mSdkManager->SetIOSettings(FbxIOSettings::Create(mSdkManager, IOSROOT));
        // mGeometryConverter = std::make_unique<FbxGeometryConverter>(mSdkManager);
    }

    FBXImporter::~FBXImporter()
    {
        CleanUp();
    }

    void FBXImporter::ImportAsset(const std::string& readPath, const std::string& writePath, ImportSetting* setting)
    {
        SetProgress(0.01f);
        mImportSettings = static_cast<ModelImportSettings*>(setting);

        if (mImportSettings->UseHLODData)
        {
            // If current asset is HLOD asset, force it to be streamable
            mImportSettings->UseHLODData = false;
            mImportSettings->IsStreamable = true;
            ImportHLODAssets(readPath, writePath, setting);
            return;
        }

        mSdkManager = FbxManager::Create();
        mSdkManager->SetIOSettings(FbxIOSettings::Create(mSdkManager, IOSROOT));
        mGeometryConverter = std::make_unique<FbxGeometryConverter>(mSdkManager);
        mImportResult.bSuccess = true;
        do
        {
            if (!OpenFile(readPath))
            {
                LOG_EDITOR_ERROR("[FBXImporter] Open file [ {} ] failed.", readPath);
                mImportResult = AssetImportState::OpenFileFail;
                break;
            }

            SetProgress(0.05f);

            if (!ImportFile(readPath))
            {
                LOG_EDITOR_ERROR("[FBXImporter] Import file [ {} ] failed.", readPath);
                mImportResult = AssetImportState::ImportFail;
                break;
            }
            SetProgress(0.1f);

            mWritePath = writePath;
            ImportScene importScene;
            importScene.Name = PathHelper::GetBaseFileName(readPath, true);

            // Import Curve Data Before Convert Scene
            if (mImportSettings->ImportCurveDataAsSequence)
            {
                TransferCurveData(importScene.ImportCurveData);
            }

            ConvertScene();
            // TODO(xujh) [5] reset invalid 3dsmax value
            // TODO(xujh) [6] frame rate
            SetProgress(0.2f);
            if (auto bTISState = TransferImportScene(importScene); !bTISState)
            {
                LOG_EDITOR_ERROR("[FBXImporter] TransferImportScene [ {} ] failed.", readPath);
                mImportResult = AssetImportState::ImportFail;
                SetProgress(1.0f);
                break;
            }
            SetProgress(0.5f);
            ExtractSceneInfo();
            SetProgress(0.55f);
            PostImport(importScene);
            SetProgress(0.6f);
            // only import texture!!!
            SerializeAllAsset(importScene, readPath, writePath);
            SetProgress(1.0f);
        } while (false);
        SetProgress(0.0f);
    }

    void FBXImporter::ImportHLODAssets(const std::string& readPath, const std::string& writePath, ImportSetting* setting)
    {
        constexpr std::string_view HLODConfigFileName = "HLODConfig.json";

        std::string directory = PathHelper::GetDirectoryFromAbsolutePath(writePath);
        std::string configPath = PathHelper::Combine(directory.data(), HLODConfigFileName.data());
        auto* importSettings = static_cast<ModelImportSettings*>(setting);

        // If config exists, import all assets listed by config
        if (PathHelper::IsFileExist(configPath))
        {
            std::string configContent = "";
            FileHelper::LoadFileToString(configContent, configPath.data());
            DeserializeNode configNode = DeserializeNode::ParseFromJson(configContent);

            auto hlodResource = gResourceMgr.CreateResourceAs<resource::HLODResource>();
            hlodResource->LoadFromConfig(configNode);
            std::string hlodResourceName = hlodResource->GetPackageName();
            std::string hlodResourcePath = PathHelper::Combine(directory.data(), hlodResourceName.data(), ".nda");
            hlodResource->CreateAsset(hlodResourcePath);
            hlodResource->GetAsset()->EnsureGuid();
            const auto& hlodPackageDesc = hlodResource->GetPackageDesc();

            // Create an overall prefab to store all prefabs imported by HLOD config
            std::string hlodPackagePrefabPath = PathHelper::Combine(directory.data(), hlodResourceName.data(), ".prefab");
            PrefabResourcePtr hlodPackagePrefab;
            SerializeNode hlodPackagePrefabJson;
            std::string hlodPackageEuid = "";
            if (PathHelper::IsFileExist(hlodPackagePrefabPath))
            {
                resource::ResourceLoadError loadError = resource::ResourceLoadError::Succeeded;
                if (auto ptr = gResourceAssetMgr.LoadNDAFile(PathHelper::GetRelativePath(hlodPackagePrefabPath).c_str(), loadError); ptr)
                {
                    hlodPackagePrefab = TypeCast<resource::PrefabResource>(ResourcePtr(ptr));
                    hlodPackagePrefab->ClearReference();
                    hlodPackagePrefabJson = hlodPackagePrefab->GetJsonData().Clone();
                    hlodPackageEuid = hlodPackagePrefabJson[WorldSerializeConst::ECSDict][WorldSerializeConst::RootNode][WorldSerializeConst::Euid].AsString();
                    // Clear prefab content and re-fill them
                    hlodPackagePrefabJson[WorldSerializeConst::ECSDict][WorldSerializeConst::Entites] = SerializeNode::EmptyObject();
                }
            }
            else
            {
                hlodPackagePrefab = gResourceMgr.CreateResourceAs<resource::PrefabResource>();
                hlodPackagePrefab->CreateAsset(hlodPackagePrefabPath);
                hlodPackagePrefab->GetAsset()->EnsureGuid();
                hlodPackageEuid = CrossUUID::GenerateCrossUUID().ToString();
            }

            auto&& ecsNode = hlodPackagePrefabJson[WorldSerializeConst::ECSDict];
            auto&& rootNode = ecsNode[WorldSerializeConst::RootNode];
            auto&& entitiesNode = ecsNode[WorldSerializeConst::Entites];
            auto&& hlodPackageEntityNode = entitiesNode[hlodPackageEuid];
            hlodPackageEntityNode = std::move(LoadJsonFile("EngineResource/Model/ModelEmpty.json"));

            rootNode[WorldSerializeConst::Euid] = hlodPackageEuid;
            hlodPackageEntityNode[WorldSerializeConst::Euid] = hlodPackageEuid;
            hlodPackageEntityNode[WorldSerializeConst::Name] = hlodResourceName;

            auto&& gameObjectNode = hlodPackageEntityNode[WorldSerializeConst::ComponentList]["cross::GOSerializerComponentG"]["GameObject"];
            gameObjectNode["MetaClassType"] = "cegf::HLODProxyObject";
            gameObjectNode["ObjectName"] = hlodResourceName;
            gameObjectNode["GameComponents"].PushBack({
                "ComponentType"_k = "cegf::HLODComponent",
                "mHLODSettingAsset"_k = hlodResource->GetGuid_Str(),
            });

            for (const auto& [hlodAsset, hlodAssetDesc] : hlodPackageDesc.HLODAssets)
            {
                ImportHLODAsset(directory, hlodPackageEuid, hlodAssetDesc, hlodPackagePrefabJson, setting);
            }

            gResourceMgr.AddResourceDependencies(hlodPackagePrefabJson, TypeCast<Resource>(hlodPackagePrefab));
            hlodPackagePrefab->DelReferenceResource(hlodPackagePrefab->GetGuid_Str());
            hlodPackagePrefab->Serialize(std::move(hlodPackagePrefabJson), hlodPackagePrefabPath);

            hlodResource->PostDeserialize();
            hlodResource->Serialize({}, hlodResourcePath);
            mImportResult = AssetImportState::Success;
        }
        // Otherwise, just import current asset as usual
        else
        {
            LOG_EDITOR_WARNING("{} is missing! Import single FBX file", HLODConfigFileName);
            ImportAsset(readPath, writePath, setting);
        }

        CleanUp();
    }

    void FBXImporter::ImportHLODAsset(const std::string& baseDirectory, const std::string& parentEuid, const HLODAssetDescription& hlodAssetDesc, SerializeNode& hlodPackage, ImportSetting* setting)
    {
        auto&& ecsNode = hlodPackage[WorldSerializeConst::ECSDict];
        auto&& rootNode = ecsNode[WorldSerializeConst::RootNode];
        auto&& entitiesNode = ecsNode[WorldSerializeConst::Entites];

        std::string hlodAssetReadPath = PathHelper::Combine(baseDirectory.data(), hlodAssetDesc.Name.data(), ".fbx");
        std::string hlodAssetWritePath = PathHelper::Combine(baseDirectory.data(), hlodAssetDesc.Name.data(), ".nda");
        std::string hlodAssetPrefabPath = PathHelper::Combine(baseDirectory.data(), hlodAssetDesc.Name.data(), ".model");
        auto* importSettings = static_cast<ModelImportSettings*>(setting);

        ImportAsset(hlodAssetReadPath, hlodAssetWritePath, setting);
        CleanUp();

        auto hlodAssetPrefab = TypeCast<resource::PrefabResource>(gResourceMgr.GetResource(hlodAssetPrefabPath.data()));
        auto hlodAssetPrefabJson = hlodAssetPrefab->GetJsonData().Clone();
        auto&& hlodAssetPrefabEcsNode = hlodAssetPrefabJson[WorldSerializeConst::ECSDict];
        std::string hlodAssetPrefabEuid = hlodAssetPrefabEcsNode[WorldSerializeConst::RootNode][WorldSerializeConst::Euid].AsString();
        std::string hlodAssetEuid = CrossUUID::GenerateCrossUUID().ToString();
        auto&& hlodAssetEntityNode = hlodAssetPrefabEcsNode[WorldSerializeConst::Entites][hlodAssetPrefabEuid];

        auto&& gameObjectNode = hlodAssetEntityNode[WorldSerializeConst::ComponentList]["cross::GOSerializerComponentG"]["GameObject"];
        gameObjectNode["ObjectName"] = hlodAssetEntityNode[WorldSerializeConst::Name].AsString();

        // HLOD assets
        if (hlodAssetDesc.HLODLevel != 0)
        {
            gameObjectNode["MetaClassType"] = "cegf::HLODObject";
        }
        // Finest models
        else
        {
            gameObjectNode["MetaClassType"] = "cegf::GameObject";
        }

        // hlodAssetEntityNode[WorldSerializeConst::PrefabID] = hlodAssetPrefab->GetGuid_Str();
        // hlodAssetEntityNode[WorldSerializeConst::PrefabEUID] = hlodAssetPrefabEuid;
        hlodAssetEntityNode[WorldSerializeConst::Euid] = hlodAssetEuid;

        entitiesNode[hlodAssetEuid] = std::move(hlodAssetEntityNode);

        for (const auto& subAsset : hlodAssetDesc.SubAssets)
        {
            ImportHLODAsset(baseDirectory, hlodAssetEuid, subAsset, hlodPackage, setting);
        }

        auto&& parentEntityNode = entitiesNode[parentEuid];
        parentEntityNode[WorldSerializeConst::Children].PushBack(std::move(hlodAssetEuid));
    }

    bool FBXImporter::UpdateAsset(const std::string& readPath, const std::string& writePath)
    {
        if (!PathHelper::IsFileExist(writePath))
            return false;
        resource::LoadNDAFileInfo fileInfo;
        if (!gResourceAssetMgr.GetLoadNDAInfo(writePath.c_str(), fileInfo))
            return false;
        if (fileInfo.GetImportSet() == "")
            return false;
        ModelImportSettings setting;
        setting.DeserializeFromString(fileInfo.GetImportSet());
        ImportAsset(readPath, writePath, &setting);
        return true;
    }

    bool FBXImporter::CheckAssetName(const char* name) const
    {
        return (HasExtension(name, ".fbx") || HasExtension(name, ".obj"));
    }

    void FBXImporter::ReleaseScene()
    {
        if (mImporter)
        {
            mImporter->Destroy();
            mImporter = nullptr;
        }

        if (mScene)
        {
            mScene->Destroy();
            mScene = nullptr;
        }
    }

    void FBXImporter::CleanUp()
    {
        ReleaseScene();

        if (mSdkManager)
        {
            mSdkManager->Destroy();
        }
        mSdkManager = nullptr;

        mBoneNodeToIDMap.clear();
        mFbxMeshSharedInfoMap.clear();
        mSkeletonArray.clear();
        mClusterLinks.clear();
        mMeshDescSrcFbxMeshes.clear();
        mUniqueLOD0FbxMeshes.clear();
        mLodMaterialNameMap.clear();
        mLodMaterialCacheDirty = false;
        mMaterailPathMap.clear();
    }

    void FBXImporter::ConvertScene()
    {
        FbxAxisSystem AxisSystem = mScene->GetGlobalSettings().GetAxisSystem();
        FbxAxisSystem EngineAxisSystem(FbxAxisSystem::eYAxis, FbxAxisSystem::eParityOdd, FbxAxisSystem::eRightHanded);
        if (AxisSystem != EngineAxisSystem)
        {
            EngineAxisSystem.DeepConvertScene(mScene);
        }

        fbxsdk::FbxSystemUnit SystemUnit = mScene->GetGlobalSettings().GetSystemUnit();
        double sceneScale = SystemUnit.GetScaleFactor();
        double newScale = sceneScale;
        switch (mImportSettings->ModelSystemUnit)
        {
        case UNIT_MM:
            newScale = fbxsdk::FbxSystemUnit::mm.GetScaleFactor();
            break;
        case UNIT_CM:
            newScale = fbxsdk::FbxSystemUnit::cm.GetScaleFactor();
            break;
        case UNIT_DM:
            newScale = fbxsdk::FbxSystemUnit::dm.GetScaleFactor();
            break;
        case UNIT_M:
            newScale = fbxsdk::FbxSystemUnit::m.GetScaleFactor();
            break;
        case UNIT_KM:
            newScale = fbxsdk::FbxSystemUnit::km.GetScaleFactor();
            break;
        case UNIT_INCH:
            newScale = fbxsdk::FbxSystemUnit::Inch.GetScaleFactor();
            break;
        case UNIT_FOOT:
            newScale = fbxsdk::FbxSystemUnit::Foot.GetScaleFactor();
            break;
        case UNIT_MILE:
            newScale = fbxsdk::FbxSystemUnit::Mile.GetScaleFactor();
            break;
        case UNIT_YARD:
            newScale = fbxsdk::FbxSystemUnit::Yard.GetScaleFactor();
            break;
        default:
            break;
        }
        fbxsdk::FbxSystemUnit EngineSystemUnit(newScale);
        EngineSystemUnit.ConvertScene(mScene);

        // Triangulate all node's mesh in current scene.
        // Cost a lot of time
        //mGeometryConverter->Triangulate(mScene, true);
    }

    void FBXImporter::RecomputeNormals(MeshDescription& meshDescription)
    {
        const UInt32 faceCount = meshDescription.GetTriangleNum();
        const UInt32 vertexCount = meshDescription.GetMeshBuildVertexCount();

        for (UInt32 vertexID = 0u; vertexID < vertexCount; ++vertexID)
        {
            auto& vertex = meshDescription.GetMeshBuildVertex(vertexID);
            vertex.Normal = Float3::Zero();
        }

        for (UInt32 faceIndex = 0u; faceIndex < faceCount; ++faceIndex)
        {
            auto& v0 = meshDescription.GetMeshBuildVertex(faceIndex, TRIANGLE_CCW_INDEX[0]);
            auto& v1 = meshDescription.GetMeshBuildVertex(faceIndex, TRIANGLE_CCW_INDEX[1]);
            auto& v2 = meshDescription.GetMeshBuildVertex(faceIndex, TRIANGLE_CCW_INDEX[2]);

            Float3 normal = ((v2.Position - v0.Position).Cross(v1.Position - v0.Position)).Normalized();
            v0.Normal += normal;
            v1.Normal += normal;
            v2.Normal += normal;
        }

        for (UInt32 vertexID = 0u; vertexID < vertexCount; ++vertexID)
        {
            auto& vertex = meshDescription.GetMeshBuildVertex(vertexID);
            vertex.Normal.Normalize();
            vertex.Channels |= ImportVertexChannel::NORMAL;
        }
    }

    bool FBXImporter::OpenFile(const std::string& path)
    {
        mImporter = FbxImporter::Create(mSdkManager, "");

        if (mSdkManager->GetIOSettings())
        {
            mSdkManager->GetIOSettings()->SetBoolProp(IMP_RELAXED_FBX_CHECK, true);
        }

        const bool ImportStatus = mImporter->Initialize(path.c_str(), -1, mSdkManager->GetIOSettings());

        SInt32 SDKMajor, SDKMinor, SDKRevision;
        FbxManager::GetFileFormatVersion(SDKMajor, SDKMinor, SDKRevision);

        if (!ImportStatus)
        {
            if (mImporter->GetStatus().GetCode() == fbxsdk::FbxStatus::eInvalidFileVersion)
            {
                LOG_EDITOR_ERROR("Fbx Initialize Fail, SDK Version {} {} {}", SDKMajor, SDKMinor, SDKRevision);
            }
            return false;
        }

        // Version out of date
        SInt32 FileMajor, FileMinor, FileRevision;
        mImporter->GetFileVersion(FileMajor, FileMinor, FileRevision);
        SInt32 FileVersion = (FileMajor << 16 | FileMinor << 8 | FileRevision);
        SInt32 SDKVersion = (SDKMajor << 16 | SDKMinor << 8 | SDKRevision);
        if (FileVersion != SDKVersion)
        {
            LOG_EDITOR_WARNING("An out of date FBX has been detected, File Version: {}.{}.{}, SDK Version: {}.{}.{}", FileMajor, FileMinor, FileRevision, SDKMajor, SDKMinor, SDKRevision);
        }

        return true;
    }

    bool FBXImporter::ImportFile(const std::string& path)
    {
        mScene = FbxScene::Create(mSdkManager, "");
        const bool success = mImporter->Import(mScene);

        if (!success)
        {
            ReleaseScene();
            return false;
        }

        return success;
    }

    void FBXImporter::ExtractSceneInfo() {}

    void FBXImporter::PostImport(ImportScene& importScene)
    {
        UInt32 meshPartCount = static_cast<UInt32>(importScene.MeshDescriptions.size());


        if (importScene.LodCount > 1)
        {
            meshPartCount = importScene.LodStartIndexArray[1];
        }

        if (mImportSettings->CollisionSetting.CollisionGenerationType == GenerateCollisionType::BoxCollision)
        {
            Float3 minVertex(std::numeric_limits<float>::infinity(), std::numeric_limits<float>::infinity(), std::numeric_limits<float>::infinity());
            Float3 maxVertex = -minVertex;
            for (UInt32 meshPartIdx = 0; meshPartIdx < meshPartCount; ++meshPartIdx)
            {
                auto& meshDescription = importScene.MeshDescriptions[meshPartIdx];

                const UInt32 vertexCnt = meshDescription.GetMeshBuildVertexCount();
                for (UInt32 i = 0u; i < vertexCnt; ++i)
                {
                    auto position = meshDescription.GetMeshBuildVertex(i).Position;
                    minVertex.x = std::min(minVertex.x, position.x);
                    minVertex.y = std::min(minVertex.y, position.y);
                    minVertex.z = std::min(minVertex.z, position.z);

                    maxVertex.x = std::max(maxVertex.x, position.x);
                    maxVertex.y = std::max(maxVertex.y, position.y);
                    maxVertex.z = std::max(maxVertex.z, position.z);
                }
            }
            Float3 center = (minVertex + maxVertex) / 2.0f;
            Float3 halfExtents = (maxVertex - minVertex) / 2.0f;
            PhysicsBoxCollisionImport box = {center, Quaternion::Identity(), halfExtents};
            importScene.PhyCollision.boxCollision.emplace_back(std::move(box));
        }
        else if (mImportSettings->CollisionSetting.CollisionGenerationType == GenerateCollisionType::ComplexCollision)
        {
            // auto meshSimplifier = CreateMeshSimplifierSDK("FFSProject");

            for (UInt32 meshPartIdx = 0; meshPartIdx < meshPartCount; ++meshPartIdx)
            {
                auto& meshDescription = importScene.MeshDescriptions[meshPartIdx];
                // Physics collision: mesh collision
                PhysicsEngine* physicsEngine = EngineGlobal::GetPhysicsEngine();
                const auto& indices = meshDescription.GetVertexInstanceVertices();
                const UInt32 vertexCnt = meshDescription.GetMeshBuildVertexCount();
                std::vector<Float3> vertices;
                vertices.reserve(vertexCnt);
                for (UInt32 i = 0u; i < vertexCnt; ++i)
                {
                    auto position = meshDescription.GetMeshBuildVertex(i).Position;
                    vertices.emplace_back(position);
                }

                std::shared_ptr<PhysicsTriangleMesh> tran;
                if (mImportSettings->CollisionSetting.ComplexCollisionSimplifyError < 1e-3f)
                {
                    auto& out_vb = vertices;
                    auto& out_ib = indices;
                    tran = physicsEngine->GetCooker()->BuildTriangleMesh(
                        reinterpret_cast<const UInt8*>(out_vb.data()),
                        static_cast<UInt32>(out_vb.size()),
                        static_cast<UInt16>(sizeof(out_vb[0])),
                        reinterpret_cast<const UInt8*>(out_ib.data()),
                        static_cast<UInt32>(out_ib.size()),
                        static_cast<UInt16>(sizeof(out_ib[0]))
                    );
                }
                else
                {
                    std::vector<UInt32> out_ib;
                    std::vector<Float3> out_vb;

                    auto& vb = vertices;
                    auto& ib = indices;
                    float threshold = 0.1f;

                    std::size_t target_index_count = static_cast<std::size_t>(ib.size() * threshold);
                    float target_error = MathUtils::Clamp(mImportSettings->CollisionSetting.ComplexCollisionSimplifyError, 1e-3f, 10.0f);
                    float result_error = 0;

                    out_ib.resize(ib.size());
                    out_ib.resize(meshopt_simplifySloppy(&out_ib[0], &ib[0], ib.size(), &vb[0].x, vb.size(), sizeof(vb[0]), target_index_count, target_error, &result_error));

                    out_vb.resize(std::min(out_ib.size(), vb.size()));
                    out_vb.resize(meshopt_optimizeVertexFetch(&out_vb[0], &out_ib[0], out_ib.size(), &vb[0], vb.size(), sizeof(vb[0])));

                    LOG_INFO("result_error: {}, vertexCount: {} -> {}", result_error, vb.size(), out_vb.size());
                    tran = physicsEngine->GetCooker()->BuildTriangleMesh(
                        reinterpret_cast<const UInt8*>(out_vb.data()),
                        static_cast<UInt32>(out_vb.size()),
                        static_cast<UInt16>(sizeof(out_vb[0])),
                        reinterpret_cast<const UInt8*>(out_ib.data()),
                        static_cast<UInt32>(out_ib.size()),
                        static_cast<UInt16>(sizeof(out_ib[0]))
                    );
                }
                // if (0){
                //    static constexpr UInt32 UEVerticesAtrributeCount = 32;

                //    InputSubMeshParams meshParams = {0.5f, 0.5f};
                //    InputSubMeshBuffer inBuffer;
                //    inBuffer.originalTriangleCount = static_cast<int>(indices.size() / 3);
                //    inBuffer.originalVertexCount = static_cast<int>(vertices.size());
                //    inBuffer.indices = indices;
                //    inBuffer.materialGroup.resize(indices.size() / 3, 0);
                //    inBuffer.vertices.resize(indices.size() * UEVerticesAtrributeCount, 0.0f);

                //    const auto vertexCount = indices.size();
                //    vertices.resize(vertexCount * UEVerticesAtrributeCount);

                //    for (size_t index = 0; index < indices.size(); index++)
                //    {
                //        std::uint32_t vertexIndex = indices[index];
                //        inBuffer.vertices[index * UEVerticesAtrributeCount + 0] = vertices[vertexIndex].x;
                //        inBuffer.vertices[index * UEVerticesAtrributeCount + 1] = vertices[vertexIndex].y;
                //        inBuffer.vertices[index * UEVerticesAtrributeCount + 2] = vertices[vertexIndex].z;
                //        inBuffer.indices[index] = static_cast<UInt32>(index);
                //    }

                //    OutSubMeshData outBuffer;
                //    meshSimplifier->SubMeshReduce(meshParams, inBuffer, &outBuffer);

                //    tran = physicsEngine->GetCooker()->BuildTriangleMesh(
                //        reinterpret_cast<const UInt8*>(outBuffer.vertexBuffer),
                //        static_cast<UInt32>(outBuffer.vertexCount),
                //        static_cast<UInt16>(sizeof(float) * UEVerticesAtrributeCount),

                //        reinterpret_cast<const UInt8*>(outBuffer.indexBuffer),
                //        static_cast<UInt32>(outBuffer.indexCount),
                //        static_cast<UInt16>(sizeof(outBuffer.indexBuffer[0]))
                //    );
                //}
                if (tran)
                {
                    std::vector<UInt8> serializeData = physicsEngine->GetCooker()->SerializeTriangleMesh(tran.get());
                    PhysicsMeshCollisionImport coll({0.0, 0.0, 0.0}, Quaternion::Identity(), std::move(serializeData));
                    importScene.PhyCollision.meshCollision.emplace_back(std::move(coll));
                }
            }
            // meshSimplifier->Release();
        }
    }

    void FBXImporter::SerializeAllAsset(ImportScene& importScene, const std::string& readPath, const std::string& writePath)
    {
        bool importSkeleton = !importScene.ImportSkeltData.Bones.empty();
        // Serialize Skeleton
        if (importSkeleton)
        {
            SerializeSkeleton(importScene.ImportSkeltData, readPath, writePath);
        }

        // Serialize Animation
        if (!importScene.ImportAnimations.empty())
        {
            SerializeAnimations(importScene.ImportAnimations, readPath, writePath, importScene.ImportSkeltData);
        }

        SetProgress(0.65f);

        if (mImportSettings->CreateCombineMaterials)
        {
            SerializeCombineMaterials(importScene, importScene.MaterialDescriptions, readPath, writePath, importScene.MaterialMeshBindings, importScene.MeshDescriptions);
        }
        else if (mImportSettings->CreateDefaultMaterials)
        {
            SerializeDefaultMaterials(importScene, importScene.MaterialDescriptions, readPath, writePath, importScene.MaterialMeshBindings, importScene.MeshDescriptions);
        }

        SetProgress(0.80f);
        // Serialize Geometry At Last
        if (!importScene.MeshDescriptions.empty())
        {
            auto meshAssetData = std::make_shared<MeshAssetData>();
            meshAssetData->SetName(importScene.Name);

            MeshAssetData::MeshCompileSettings mesh_compile_setting;
            mesh_compile_setting.UseFullPrecisionTangent = mImportSettings->ImportUseFullPrecisionNormal;
            mesh_compile_setting.UseFullPrecisionUV = mImportSettings->ImportUseFullPrecisionUV;
            mesh_compile_setting.UseQTangents = mImportSettings->ImportUseQTangents;
            mesh_compile_setting.GenCollisionTree = mImportSettings->CanPickInEditor;
            mesh_compile_setting.UseTextureArray = mImportSettings->CreateCombineMaterials;

            meshAssetData->InitByImportScene(const_cast<ImportScene&>(importScene), mesh_compile_setting);

            if (mImportSettings->GenerateClusters)
            {
                meshAssetData->GenerateMeshPartCluster();
            }
            if (mImportSettings->UseMeshCurvation)
            {
                meshAssetData->AddVersionFlag(static_cast<UInt32>(MeshAssetVersionFlags::VersionMeshCurvation));
            }
            if (mImportSettings->CollisionSetting.CollisionGenerationType != GenerateCollisionType::None)
            {
                meshAssetData->AddVersionFlag(static_cast<UInt32>(MeshAssetVersionFlags::VersionPhysicsCurvation));
            }
            if (mImportSettings->IsStreamable)
            {
                meshAssetData->SetMeshStreamable(true);
            }

            if (!meshAssetData->SerializeToFlatbufferFile(writePath, mImportSettings->SerializeToString()))
            {
                LOG_EDITOR_ERROR("[FBXImporter] Serialize file [ {} ] failed.", readPath);
                return;
            }
            else if (mImportSettings->IsCookMesh)
            {
                AssetCookerManager::Instance().CookImportAsset(writePath.c_str());
            }

            if (mImportSettings->ImportAsModel)
            {
                SerializeModel(importScene, writePath, importSkeleton);
            }
        }
        SetProgress(0.90f);
        if (importScene.ImportCurveData.Res.get())
        {
            auto savePath = importScene.ImportCurveData.Staging + "/" + importScene.ImportCurveData.Refference;
            importScene.ImportCurveData.Res->Serialize({}, savePath);
        }
    }

    void FBXImporter::SerializeDefaultMaterials(const ImportScene& importScene, std::vector<MaterialDescription>& materialDescs, const std::string& assetFilePath, const std::string& ndaSavePath, std::vector<MaterialMeshBind>& bindings,
                                                std::vector<MeshDescription>& meshDescs)
    {
        std::unordered_set<FbxSurfaceMaterial*> fbxMaterials;
        for (auto materialDescIndex = 0u; materialDescIndex < materialDescs.size(); ++materialDescIndex)
        {
            const MaterialDescription& desc = materialDescs[materialDescIndex];
            desc.FetchFbxMatrials(fbxMaterials);
        }

        for (auto iter = fbxMaterials.begin(); iter != fbxMaterials.end(); ++iter)
        {
            FbxSurfaceMaterial* fbxMatPtr = *iter;
            auto diffuseTexture = ImportTexture(fbxMatPtr, FbxSurfaceMaterial::sDiffuse, ndaSavePath, assetFilePath, TextureCompression::BC3);
            auto normalTexture = ImportTexture(fbxMatPtr, FbxSurfaceMaterial::sNormalMap, ndaSavePath, assetFilePath, TextureCompression::BC5);
            auto specularTexture = ImportTexture(fbxMatPtr, FbxSurfaceMaterial::sSpecular, ndaSavePath, assetFilePath, TextureCompression::BC3);
            auto emissiveTexture = ImportTexture(fbxMatPtr, FbxSurfaceMaterial::sEmissive, ndaSavePath, assetFilePath, TextureCompression::BC3);

            bool needCreateMaterial = (diffuseTexture != nullptr || normalTexture != nullptr || specularTexture != nullptr || emissiveTexture != nullptr);
            if (needCreateMaterial)
            {
                FxPtr fx;
                const std::string& path = gResourceMgr.ConvertGuidToPath(mImportSettings->ImportMaterialFx);
                if (ResourcePtr res = gResourceMgr.Find(path.c_str()))
                {
                    fx = TypeCast<resource::Fx>(res);
                }
                else
                {
                    fx = TypeCast<resource::Fx>(gResourceMgr.GetResource(path.c_str()));
                }

                if (fx != nullptr)
                {
                    std::unordered_map<std::string_view, NameID> relatedMapping;
                    std::regex diffuseRegex("^(?!.*vt)(?!.*array).*(basemap|diffusemap|albedomap).*$");
                    std::regex normalRegex("^(?!.*vt)(?!.*array).*normalmap.*$");
                    std::regex specularRegex("^(?!.*vt)(?!.*array).*specularmap.*$");
                    std::regex emissiveRegex("^(?!.*vt)(?!.*array).*emissivemap.*$");
                    for (const auto& [propID, prop] : fx->GetProperties())
                    {
                        if (std::holds_alternative<TexturePtr>(prop.mValue))
                        {
                            std::string lowerName = StringHelper::ToLower(propID.GetName());
                            if (std::regex_search(lowerName, diffuseRegex))
                            {
                                relatedMapping.try_emplace(M_DIFFUSE_KEY, propID);
                            }
                            else if (std::regex_search(lowerName, normalRegex))
                            {
                                relatedMapping.try_emplace(M_NORMAL_KEY, propID);
                            }
                            else if (std::regex_search(lowerName, specularRegex))
                            {
                                relatedMapping.try_emplace(M_SPECULAR_KEY, propID);
                            }
                            else if (std::regex_search(lowerName, emissiveRegex))
                            {
                                relatedMapping.try_emplace(M_EMISSIVE_KEY, propID);
                            }
                        }
                    }
                    auto newMaterial = resource::Material::CreateMaterialTempInstance(fx);

                    if (diffuseTexture && relatedMapping.find(M_DIFFUSE_KEY) != relatedMapping.end())
                    {
                        newMaterial->SetTextureProp(relatedMapping[M_DIFFUSE_KEY], diffuseTexture);
                    }
                    if (normalTexture && relatedMapping.find(M_NORMAL_KEY) != relatedMapping.end())
                    {
                        newMaterial->SetTextureProp(relatedMapping[M_NORMAL_KEY], normalTexture);
                    }
                    if (specularTexture && relatedMapping.find(M_SPECULAR_KEY) != relatedMapping.end())
                    {
                        newMaterial->SetTextureProp(relatedMapping[M_SPECULAR_KEY], specularTexture);
                    }
                    if (emissiveTexture && relatedMapping.find(M_EMISSIVE_KEY) != relatedMapping.end())
                    {
                        newMaterial->SetTextureProp(relatedMapping[M_EMISSIVE_KEY], emissiveTexture);
                    }

                    std::string matSavePath = ndaSavePath;
                    matSavePath.replace(ndaSavePath.length() - 4, ndaSavePath.length(), "_" + std::string(fbxMatPtr->GetName()) + ".mtl.nda");
                    mMaterailPathMap.try_emplace(fbxMatPtr, matSavePath);
                    newMaterial->SetProp("_SubsurfaceColor", std::vector<float>({1.0, 1.0, 1.0, 1.0}));
                    newMaterial->Serialize({}, matSavePath);

                    // TODO(jihuixu): Fix crash bug when newMaterial's render material was destructed after RefreshRenderData(), wait for support cpp's coroutine, handle it in GameThread.
                    std::mutex mtx;
                    std::unique_lock lock(mtx);
                    std::condition_variable cv;
                    threading::DispatchRenderingCommand([&]() { 
                        cv.notify_one(); 
                    });
                    cv.wait(lock);
                }
                else
                {
                    Assert(false);
                }
            }
            else
            {
                mMaterailPathMap.try_emplace(fbxMatPtr, "");
                mImportResult = AssetImportState::MissImportTextureFile;
                LOG_ERROR("Can't find texture file while import fbx to create default material: {}", assetFilePath);
            }
        }

        for (auto materialDescIndex = 0u; materialDescIndex < materialDescs.size(); ++materialDescIndex)
        {
            const MaterialDescription& desc = materialDescs[materialDescIndex];
            const auto& materialIDs = desc.GetMaterialIDs();
            for (auto iter = materialIDs.begin(); iter != materialIDs.end(); ++iter)
            {
                FbxSurfaceMaterial* fbxMaterial = iter->first;
                auto search = mMaterailPathMap.find(fbxMaterial);
                if (search != mMaterailPathMap.end())
                {
                    bindings[materialDescIndex].AddMaterialPath(search->second);
                }
            }
        }

        for (auto bindingIndex = 0u; bindingIndex < bindings.size(); ++bindingIndex)
        {
            const auto& binding = bindings[bindingIndex];
            const auto& meshDescIndices = binding.GetMeshDescIndices();
            for (auto i = 0u; i < meshDescIndices.size(); ++i)
            {
                const MeshDescIndex meshDescIndex = meshDescIndices[i];
                MeshDescription& meshDesc = meshDescs[meshDescIndex];
                meshDesc.SetMaterialPath(binding.GetMaterialPath(i));
            }
        }
    }

    void FBXImporter::SerializeCombineMaterials(ImportScene& importScene, std::vector<MaterialDescription>& materialDescs, const std::string& assetFilePath, const std::string& ndaSavePath, std::vector<MaterialMeshBind>& bindings,
                                                std::vector<MeshDescription>& meshDescs)
    {
        if (importScene.MaterilGlobalIndexMap.size() <= 0)
            return;

        const std::string& path = gResourceMgr.ConvertGuidToPath(mImportSettings->ImportMaterialFx);
        auto fx = TypeCast<resource::Fx>(gResourceMgr.GetResource(path.c_str()));
        auto newMaterial = resource::Material::CreateMaterialTempInstance(fx);
        std::string newMaterialPath = ndaSavePath;
        newMaterialPath.replace(ndaSavePath.length() - 4, ndaSavePath.length(), ".mtl.nda");
        newMaterial->CreateAsset(newMaterialPath);

        std::vector<std::pair<std::string, std::string>> matPropes = {
            {FbxSurfaceMaterial::sDiffuse, "_BaseMapArray"}, {FbxSurfaceMaterial::sNormalMap, "_NormalMapArray"}, {"ShininessExponent", "_MaskMapArray"}, {FbxSurfaceMaterial::sEmissive, "_EmissiveMapArray"}};

        std::vector<TextureCompression> matTextCompression = {TextureCompression::BC1, TextureCompression::BC1, TextureCompression::BC1, TextureCompression::BC1};
        std::vector<UInt32> matTexDefualtColor = {0xffffffff, 0xffff8080, 0xff000000, 0xff000000};

        importScene.MatIdxToTexIds.resize(importScene.MaterilGlobalIndexMap.size(), {-1, -1, -1, -1});
        std::vector<float> enableFlag = {-1, -1, -1, -1};

        std::map<UInt32, std::vector<TexturePtr>> matTextureArray;
        std::vector<std::pair<bool, TexturePtr>> matBlackTexs(matPropes.size());
       
        for (const auto& [voidPtr, matId] : importScene.MaterilGlobalIndexMap)
        {
            auto& matTexs = matTextureArray[matId];
            matTexs.resize(matPropes.size());
            for (int pindex = 0; pindex < matPropes.size(); pindex++)
            {
                const FbxSurfaceMaterial* fbxMat = reinterpret_cast<FbxSurfaceMaterial*>(voidPtr);
                auto tex = ImportTexture(fbxMat, matPropes[pindex].first, ndaSavePath, assetFilePath, matTextCompression[pindex]);
                if (tex && tex->GetClassID() == ClassID(Texture2D))
                {
                    matTexs[pindex] = tex;
                    matBlackTexs[pindex].second = tex;
                    enableFlag[pindex] = 1.f;
                }
                else
                {
                    matBlackTexs[pindex].first = true;
                }
            }
        }

        if (mImportSettings->UseSameUVIndex)
        {
            for (int pindex = 0; pindex < matPropes.size(); pindex++)
            {
                if (!matBlackTexs[pindex].first || enableFlag[pindex] <= 0.f)
                    continue;
                std::string savePath = ndaSavePath;
                savePath.replace(ndaSavePath.rfind('/') + 1, ndaSavePath.length(), "");
                auto blackTex = GenSolidTexture(matBlackTexs[pindex].second->GetTextureInfo(), savePath, matTextCompression[pindex], matTexDefualtColor[pindex], matPropes[pindex].second);
                Assert(blackTex);
                blackTex->CreateGPUResource();
                matBlackTexs[pindex].second = blackTex;
            }
        }

        for (int pindex = 0; pindex < matPropes.size(); pindex++)
        {
            if (enableFlag[pindex] <= 0.0f)
                continue;
            const std::string& propName = matPropes[pindex].second;
            std::string propTex = ndaSavePath;
            propTex.replace(ndaSavePath.length() - 3, ndaSavePath.length(), propName + ".nda");
            auto textArrayPtr = gResourceMgr.CreateResourceAs<resource::Texture2DArray>();
            textArrayPtr->CreateAsset(propTex);
            for (const auto& [matId, matTexs] : matTextureArray)
            {
                bool ret = false;
                if (mImportSettings->UseSameUVIndex)
                {
                    ret = textArrayPtr->AddTexture(matTexs[pindex] ? matTexs[pindex] : matBlackTexs[pindex].second);
                    AssertMsg(ret, "Error Texture: matId: {} pindex: {}", matId, pindex);
                    importScene.MatIdxToTexIds[matId][pindex] = (UInt32)textArrayPtr->GetTextureCount() - 1u;
                }
                else
                {
                    if (matTexs[pindex])
                    {
                        const auto& texPath = matTexs[pindex]->GetGuid_Str();
                        if (!textArrayPtr->HasTexture(texPath))
                            ret = textArrayPtr->AddTexture(matTexs[pindex]);
                        importScene.MatIdxToTexIds[matId][pindex] = textArrayPtr->GetTextureIndex(texPath);
                    }                    
                }
            }
            //textArrayPtr->FinishAddTexture();
            textArrayPtr->Serialize();
            newMaterial->SetTextureProp(propName, TypeCast<resource::Texture>(textArrayPtr));
        }
        newMaterial->SetProp("TEXTURE_ARRAY_ENABLE", true);
        newMaterial->SetProp("_EnableMapArrayFlags", enableFlag);

        auto newRelMaterialPath = EngineGlobal::GetFileSystem()->GetRelativePath(newMaterialPath);
        for (auto& binding : bindings)
        {
            binding.AddMaterialPath(newRelMaterialPath);
            for (const auto& meshDescIndex : binding.GetMeshDescIndices())
            {
                meshDescs[meshDescIndex].SetMaterialPath(newRelMaterialPath);
            }
        }
        // save mat
        newMaterial->Serialize({}, newMaterialPath);
        newMaterial.reset();
    }

    void FBXImporter::SerializeModel(const ImportScene& importScene, const std::string& writePath, bool importSkeleton)
    {
        std::string writeDir = PathHelper::GetDirectoryFromAbsolutePath(writePath);
        std::string baseName = PathHelper::GetBaseFileName(writePath);
        std::string baseRelPath = PathHelper::GetRelativePath(writeDir);
        baseRelPath.append(baseName);
        std::string modelPath = baseRelPath + ".model";
        std::string meshPath = baseRelPath + ".nda";
        std::string skeletonPath = baseRelPath + "_SK.nda";
        std::string modelSavePath = writeDir + baseName + ".model";
        // generate prefab resource
        PrefabResourcePtr prefab;
        SerializeNode prefabJson;
        std::string entityEuid = "";
        if (PathHelper::IsFileExist(modelSavePath))
        {
            resource::ResourceLoadError loadError = resource::ResourceLoadError::Succeeded;
            if (auto ptr = gResourceAssetMgr.LoadNDAFile(PathHelper::GetRelativePath(modelSavePath).c_str(), loadError); ptr)
            {
                prefab = TypeCast<resource::PrefabResource>(ResourcePtr(ptr));
                prefab->ClearReference();
                prefabJson = prefab->GetJsonData().Clone();
                entityEuid = prefabJson[WorldSerializeConst::ECSDict][WorldSerializeConst::RootNode][WorldSerializeConst::Euid].AsString();
            }
        }
        if (!prefab)
        {
            auto ptr = gResourceMgr.CreateResource<resource::PrefabResource>(modelSavePath);
            prefab = TypeCast<resource::PrefabResource>(ptr);
            prefab->CreateAsset(modelSavePath);
            prefab->GetAsset()->EnsureGuid();
            SerializeNode entityNode = LoadJsonFile(importSkeleton ? "EngineResource/Model/ModelTemplateWithSkeleton.json" : "EngineResource/Model/ModelTemplate.json");
            entityEuid = CrossUUID::GenerateCrossUUID().ToString();
            entityNode[WorldSerializeConst::Euid] = entityEuid;
            entityNode[WorldSerializeConst::Name] = baseName;
            SerializeNode entitiesNode;
            entitiesNode[entityEuid] = std::move(entityNode);
            SerializeNode rootNode;
            rootNode[WorldSerializeConst::Euid] = entityEuid;
            SerializeNode ecsNode;
            ecsNode[WorldSerializeConst::RootNode] = std::move(rootNode);
            ecsNode[WorldSerializeConst::Entites] = std::move(entitiesNode);
            //prefabJson[WorldSerializeConst::PrefabID] = prefab->GetGuid_Str();
            prefabJson[WorldSerializeConst::ECSDict] = std::move(ecsNode);
        }
        auto&& componentNode = prefabJson[WorldSerializeConst::ECSDict][WorldSerializeConst::Entites][entityEuid][WorldSerializeConst::ComponentList];
        auto&& modelNode = componentNode["cross::ModelComponentG"]["mModels"][0];
        modelNode["mAsset"] = gResourceMgr.ConvertPathToGuid(meshPath);
        modelNode["mSubModelProperties"] = SerializeNode::EmptyArray();

        auto&& gameObjectNode = componentNode["cross::GOSerializerComponentG"]["GameObject"];
        gameObjectNode["ObjectName"] = baseName;

        std::string defaultMat = EngineGlobal::GetSettingMgr()->GetRenderPipelineSetting()->DefaultMaterial;
        for (auto i = 0u; i < importScene.MaterialMeshBindings.size(); ++i)
        {
            const auto& binding = importScene.MaterialMeshBindings[i];
            const UInt32 materialCount = binding.GetMaterialCount();
            for (UInt32 index = 0; index < binding.GetMeshDescIndices().size(); index++)
            {
                SerializeNode subModelProp;
                subModelProp["mMaterial"] = gResourceMgr.ConvertPathToGuid(index < materialCount ? binding.GetMaterialPath(index) : defaultMat);
                subModelProp["mVisible"] = true;
                modelNode["mSubModelProperties"].PushBack(std::move(subModelProp));
            }
        }
        if (importSkeleton)
        {
            if (auto skeletonNode = componentNode.HasMember("cross::SkeletonComponentG"); skeletonNode)
            {
                (*skeletonNode)["Path"] = gResourceMgr.ConvertPathToGuid(skeletonPath);
            }
            else
            {
                componentNode["cross::SkeletonComponentG"] = {"Path"_k = gResourceMgr.ConvertPathToGuid(skeletonPath), "PhysicsPath"_k = "", "componentHash"_k = "2294777672"};
            }
        }
        // Add a PhysicsComponentG if an imported model has collision generation type
        if (mImportSettings->CollisionSetting.CollisionGenerationType != GenerateCollisionType::None)
        {
            componentNode["cross::PhysicsComponentG"] = {
                "mEnable"_k = true,
                "mEnableSimulate"_k = false,
                "mEnableGravity"_k = true,
                "mIsTrigger"_k = false,
                "mIsKinematic"_k = false,
                "mOnlyUseExtraCollision"_k = false,
                "mStartAsleep"_k = false,
                "mLinearDamping"_k = 0.01,
                "mMass"_k = 0.0,
                "mMaxDepenetrationVelocity"_k = 0.0,
                "mMassSpaceInertiaTensorMultiplier"_k = {"x"_k = 1.0, "y"_k = 1.0, "z"_k = 1.0},
                "mCollisionType"_k = 0,
                "mCollisionMask"_k = 0,
                "mMaterialType"_k = 0,
                "mEnableCollisionEvent"_k = false,
                "mExtraCollision"_k = nullptr,
                "componentHash"_k = 1482413578,
            };

            gameObjectNode["GameComponents"].PushBack({
                "ComponentType"_k = "cegf::PhysicsComponent",
            });
        }
        gResourceMgr.AddResourceDependencies(prefabJson, TypeCast<Resource>(prefab));
        prefab->DelReferenceResource(prefab->GetGuid_Str());
        prefab->Serialize(std::move(prefabJson), modelSavePath);
    }

    bool FBXImporter::TransferImportScene(ImportScene& outImportScene)
    {
        TransferGlobalMaterial(outImportScene);
        const bool needImportSkeleton = mImportSettings->ImportContentType != SkinImportType::Geometry || mImportSettings->AnimImport != None;
        if (needImportSkeleton)
        {
            if (auto bTNHState = TransferNodeHierarchy(outImportScene); !bTNHState)
                return false;
            bool skImportResult = ImportSkeleton(outImportScene.Name, outImportScene.ImportSkeltData);
            if (!skImportResult)
                LOG_EDITOR_ERROR("Skeleton importing failed for [{}]", outImportScene.Name);

            if (mImportSettings->ImportContentType != SkinImportType::Geometry && skImportResult)
            {
                TransferSkin(outImportScene);
            }

            if (mImportSettings->AnimImport != None && skImportResult)
            {
                UInt32 lod0MeshPartCount = 0;
                if (outImportScene.LodCount <= 1U)
                {
                    lod0MeshPartCount = static_cast<UInt32>(outImportScene.MeshDescriptions.size());
                }
                else
                {
                    lod0MeshPartCount = outImportScene.LodStartIndexArray[1];
                }

                // Collect Unique LOD0 FbxMeshes
                mUniqueLOD0FbxMeshes.clear();
                for (UInt32 meshIdx = 0; meshIdx < lod0MeshPartCount; ++meshIdx)
                {
                    mUniqueLOD0FbxMeshes.insert(mMeshDescSrcFbxMeshes[meshIdx]);
                }

                TransferAnimations(outImportScene.ImportSkeltData, outImportScene.ImportAnimations);
            }
        }
        else
        {
            if (needToBeMerged || mImportSettings->CombineMesh)
            {
                fbxsdk::FbxNode* fbxRoot = mScene->GetRootNode();
                fbxsdk::FbxNode* fbxNode = fbxRoot->GetChildCount() == 1 ? fbxRoot->GetChild(0) : fbxRoot;
                CollectMeshNodeRecursively(fbxNode, ComputeTotalMatrix(fbxNode), 0, outImportScene, true);
            }
            else
            {
                if (auto bTNHState = TransferNodeHierarchy(outImportScene); !bTNHState)
                    return false;
            }
        }
        return true;
    }

    bool FBXImporter::TransferNodeHierarchy(ImportScene& importScene)
    {
        fbxsdk::FbxNode* fbxRoot = mScene->GetRootNode();

        AssertMsg(fbxRoot->GetChildCount() > 0, "root node cild is null!!");
        fbxsdk::FbxNode* fbxNode = fbxRoot->GetChildCount() == 1 ? fbxRoot->GetChild(0) : fbxRoot;
        return RecursiveImportNode(fbxNode, importScene);
    }

    void FBXImporter::TransferGlobalMaterial(ImportScene& importScene)
    {
        auto matCount = mScene->GetMaterialCount();
        for (int matIndex = 0; matIndex < matCount; matIndex++)
        {
            auto fbxMat = mScene->GetMaterial(matIndex);
            importScene.MaterilGlobalIndexMap.try_emplace(fbxMat, matIndex);
        }
    }

    bool FBXImporter::TransferMesh(fbxsdk::FbxMesh* fbxMesh, const FbxAMatrix& totalMatrix, const MaterialDescription& materialDesc, MaterialMeshBind& matMeshBind, std::vector<MeshDescription>& outMeshDescs)
    {
        auto mainLayer = fbxMesh->GetLayer(0);
        if (mainLayer == nullptr)
        {
            return false;
        }

        std::vector<int> smoothinGroups;
        if (mainLayer && fbxMesh->GetShapeCount() > 0)
        {
            if (FbxLayerElementSmoothing* smoothingInfo = mainLayer->GetSmoothing())
            {
                if (smoothingInfo->GetMappingMode() == FbxLayerElement::eByEdge)
                {
                    if (!mGeometryConverter->ComputePolygonSmoothingFromEdgeSmoothing(fbxMesh))
                    {
                        LOG_EDITOR_ERROR("Failed to convert smoothing information from by-edge, to by-polygon on mesh '{}'. Please report this bug.", fbxMesh->GetName());
                    }
                }

                UInt32 wedgeIndex = 0;
                for (auto polygonIndex = 0; polygonIndex < fbxMesh->GetPolygonCount(); ++polygonIndex)
                {
                    for (auto cornerIndex = 0; cornerIndex < fbxMesh->GetPolygonSize(polygonIndex); ++cornerIndex)
                    {
                        smoothinGroups.emplace_back(smoothingInfo->GetDirectArray().GetAt(wedgeIndex++));
                    }
                }
            }
        }

        const auto& attachPolygons = materialDesc.GetAttachPolygons();
        if (attachPolygons.empty() || !mImportSettings->SplitByMaterial)
        {
            MeshDescription& meshDescription = outMeshDescs.emplace_back(std::string(fbxMesh->GetNode()->GetNameWithoutNameSpacePrefix()));
            if (!BuildMeshDescription(fbxMesh, materialDesc, totalMatrix, smoothinGroups, std::vector<UInt32>(), meshDescription))
            {
                outMeshDescs.pop_back();
                LOG_ERROR("Fbximporter buildMeshDescription error");
                return false;
            }
            mMeshDescSrcFbxMeshes.emplace_back(fbxMesh);
            matMeshBind.AddMeshDescIndex(static_cast<MeshDescIndex>(outMeshDescs.size() - 1));
        }
        else
        {
            matMeshBind.Reserve(static_cast<UInt32>(attachPolygons.size()));
            for (const auto& iter : attachPolygons)
            {
                const MaterialID materialID = iter.first;
                const std::vector<UInt32> polygons = iter.second;
                std::string meshName = static_cast<const char*>(fbxMesh->GetNode()->GetNameWithoutNameSpacePrefix());
                MeshDescription& meshDescription = outMeshDescs.emplace_back(meshName + std::string("_Part") + std::to_string(materialID));

                if (!BuildMeshDescription(fbxMesh, materialDesc, totalMatrix, smoothinGroups, polygons, meshDescription))
                {
                    outMeshDescs.pop_back();
                    LOG_ERROR("Fbximporter buildMeshDescription error");
                    return false;
                    //continue;
                }
                mMeshDescSrcFbxMeshes.emplace_back(fbxMesh);
                matMeshBind.AddMeshDescIndex(static_cast<MeshDescIndex>(outMeshDescs.size() - 1));
            }
        }

        if (mImportSettings->ImportBlendShapes)
        {
            const UInt32 originSize = static_cast<UInt32>(outMeshDescs.size());
            TransferBlendShape(fbxMesh, totalMatrix, outMeshDescs, originSize, static_cast<UInt32>(outMeshDescs.size()));
        }
        return true;
    }

    void FBXImporter::TransferMaterial(const ImportScene& importScene, fbxsdk::FbxNode* fbxNode, fbxsdk::FbxMesh* fbxMesh, MaterialDescription& outMaterialDesc)
    {
        const SInt32 materialCount = fbxNode->GetMaterialCount();

        for (SInt32 materialID = 0; materialID < materialCount; ++materialID)
        {
            FbxSurfaceMaterial* fbxMaterial = fbxNode->GetMaterial(materialID);
            const std::string materialName = fbxMaterial->GetName();
            outMaterialDesc.InitAttachPolygon(materialID);

            if (mLodMaterialCacheDirty)
            {
                auto iter = mLodMaterialNameMap.find(materialName);
                if (iter != mLodMaterialNameMap.end())
                {
                    const SInt32 realMaterialID = iter->second;
                    outMaterialDesc.AddMaterialID(fbxMaterial, realMaterialID);
                }
                else
                {
                    outMaterialDesc.AddMaterialID(fbxMaterial, materialID);
                }
            }
            else
            {
                mLodMaterialNameMap.try_emplace(materialName, materialID);
                outMaterialDesc.AddMaterialID(fbxMaterial, materialID);
            }
        }

        if (!mLodMaterialCacheDirty)
        {
            mLodMaterialCacheDirty = true;
        }

        const UInt32 polygonNums = fbxMesh->GetPolygonCount();

        const int layerCount = fbxMesh->GetLayerCount();
        for (auto layerIndex = 0; layerIndex < layerCount; ++layerIndex)
        {
            FbxLayerElementMaterial* materialLayer = fbxMesh->GetLayer(layerIndex)->GetMaterials();

            if (materialLayer == nullptr)
                continue;

            if (materialLayer->GetMappingMode() == FbxLayerElement::eByPolygon)
            {
                for (UInt32 faceIndex = 0u; faceIndex < polygonNums; ++faceIndex)
                {
                    UInt32 realIndex = materialLayer->GetReferenceMode() == FbxLayerElement::eDirect ? faceIndex : materialLayer->GetIndexArray().GetAt(faceIndex);
                    if (FbxSurfaceMaterial* fbxMaterial = fbxNode->GetMaterial(realIndex))
                    {
                        MaterialID materialID = outMaterialDesc.FindMaterialID(fbxMaterial);
                        outMaterialDesc.AddAttachPolygon(materialID, faceIndex);
                        if (auto it = importScene.MaterilGlobalIndexMap.find(fbxMaterial); it != importScene.MaterilGlobalIndexMap.end())
                        {
                            outMaterialDesc.AddPolygonMatIndex(it->second, faceIndex);
                        }
                    }
                }
            }
        }
    }

    void FBXImporter::TransferBlendShape(fbxsdk::FbxMesh* fbxMesh, const FbxAMatrix& totalMatrix, std::vector<MeshDescription>& outMeshDescs, UInt32 prevSize, UInt32 newSize)
    {
        // Import vertex positions from FbxShape
        auto ImportVerticesFromFBXShape = [&](const FbxMesh* fbxMesh, const FbxShape* fbxShape, std::vector<Float3>& outShapeVertices) 
        {
            const int rawVertexCnt = fbxShape->GetControlPointsCount();
            Assert(fbxMesh->GetControlPointsCount() == rawVertexCnt);
            outShapeVertices.resize(rawVertexCnt);

            for (int vertexIdx = 0; vertexIdx < rawVertexCnt; ++vertexIdx)
            {
                fbxsdk::FbxVector4 controlPoint = fbxShape->GetControlPointAt(vertexIdx);
                fbxsdk::FbxVector4 fbxPos = totalMatrix.MultT(controlPoint);

                outShapeVertices[vertexIdx] = FBXPointToFloat3Remap(fbxPos);
            }
        };

        // Import Fbx blend shapes
        Assert(fbxMesh);

        std::vector<Float3> RemappedShapeVertices(fbxMesh->GetControlPointsCount());

        if (fbxMesh->GetDeformerCount(FbxDeformer::eBlendShape) > 0)
        {
            FbxBlendShape* curFbxBlendShape = static_cast<FbxBlendShape*>(fbxMesh->GetDeformer(0, FbxDeformer::eBlendShape));
            if (curFbxBlendShape)
            {
                const UInt32 blendShapeChannelCount = curFbxBlendShape->GetBlendShapeChannelCount();
                std::string blendShapeName = static_cast<const char*>(curFbxBlendShape->GetNameWithoutNameSpacePrefix());

                const bool bMightBeBadMAXFile = (blendShapeName == "Morpher");

                for (UInt32 meshIdx = prevSize; meshIdx < newSize; ++meshIdx)
                {
                    outMeshDescs[meshIdx].GetBlendShapeDeformerRef().Reserve(blendShapeChannelCount);
                }

                for (UInt32 channelIdx = 0; channelIdx < blendShapeChannelCount; ++channelIdx)
                {
                    FbxBlendShapeChannel* curFbxBlendShapeChannel = curFbxBlendShape->GetBlendShapeChannel(channelIdx);
                    if (curFbxBlendShapeChannel)
                    {
                        std::string channelName = static_cast<const char*>(curFbxBlendShapeChannel->GetNameWithoutNameSpacePrefix());
                        const UInt32 targetShapeCount = curFbxBlendShapeChannel->GetTargetShapeCount();
                        const double* targetWeights = curFbxBlendShapeChannel->GetTargetShapeFullWeights();

                        // Maya adds the name of the blend shape and an underscore or point to the front of the channel name, so remove it.
                        // Also avoid ending up with a empty name, we prefer having the BlendShapeName instead of nothing.
                        if (StartWith(channelName, blendShapeName) && channelName.length() > blendShapeName.length())
                        {
                            channelName = channelName.substr(blendShapeName.length() + 1);
                        }

                        if (bMightBeBadMAXFile)
                        {
                            FbxShape* targetShape = targetShapeCount > 0 ? curFbxBlendShapeChannel->GetTargetShape(0) : nullptr;
                            if (targetShape)
                            {
                                std::string targetShapeName = static_cast<const char*>(targetShape->GetNameWithoutNameSpacePrefix());
                                channelName = targetShapeName.empty() ? channelName : targetShapeName;
                            }
                        }

                        for (UInt32 meshIdx = prevSize; meshIdx < newSize; ++meshIdx)
                        {
                            auto& curBlendShape = outMeshDescs[meshIdx].GetBlendShapeDeformerRef();
                            curBlendShape.Channels[channelIdx].Name = channelName;
                            curBlendShape.Channels[channelIdx].TargetShapes.resize(targetShapeCount);
                            curBlendShape.Channels[channelIdx].DeltaShapes.resize(targetShapeCount);
                        }

                        for (UInt32 shapeIdx = 0; shapeIdx < targetShapeCount; ++shapeIdx)
                        {
                            FbxShape* curFbxShape = curFbxBlendShapeChannel->GetTargetShape(shapeIdx);
                            // Import remapped vertices from current target shape
                            ImportVerticesFromFBXShape(fbxMesh, curFbxShape, RemappedShapeVertices);

                            for (UInt32 meshIdx = prevSize; meshIdx < newSize; ++meshIdx)
                            {
                                auto& curTargetShape = outMeshDescs[meshIdx].GetTargetShape(channelIdx, shapeIdx);
                                curTargetShape.NormalizedFullWeight = static_cast<float>(targetWeights[shapeIdx] / targetWeights[targetShapeCount - 1]);

                                BuildShapeDescription(RemappedShapeVertices, outMeshDescs[meshIdx], channelIdx, shapeIdx);
                            }
                        }
                    }
                }
            }
        }
    }

    bool FBXImporter::BuildShapeDescription(const std::vector<Float3>& fbxShapePoints, MeshDescription& meshDescription, UInt32 channelIndex, UInt32 shapeIndex)
    {
        const bool needAdjustByFaceOffset = mImportSettings->FaceOffset != 0.f;
        const static float PointNearThresSquare = THRESH_POINTS_ARE_NEAR * THRESH_POINTS_ARE_NEAR;
        const auto& meshBuildVertices = meshDescription.GetMeshBuildVerticesConstRef();
        const UInt32 meshVertexCount = meshDescription.GetMeshBuildVertexCount();
        auto& curBlendShape = meshDescription.GetBlendShapeDeformerRef();

        auto& targetShape = meshDescription.GetTargetShape(channelIndex, shapeIndex);

        targetShape.Positions.resize(meshVertexCount);
        for (UInt32 meshVertexIdx = 0; meshVertexIdx < meshVertexCount; ++meshVertexIdx)
        {
            const auto controlPtID = meshBuildVertices[meshVertexIdx].ControlPointID;
            targetShape.Positions[meshVertexIdx] = fbxShapePoints[controlPtID];
        }

        if (mImportSettings->ImportNormals)
        {
            targetShape.Normals.resize(meshVertexCount);

            auto CalculateNormals = [&]()
            {
                const UInt32 faceCount = meshDescription.GetTriangleNum();

                for (UInt32 faceIndex = 0u; faceIndex < faceCount; ++faceIndex)
                {
                    const auto vertexID0 = meshDescription.GetVertexInstanceVertex(faceIndex, TRIANGLE_CCW_INDEX[0]);
                    const auto vertexID1 = meshDescription.GetVertexInstanceVertex(faceIndex, TRIANGLE_CCW_INDEX[1]);
                    const auto vertexID2 = meshDescription.GetVertexInstanceVertex(faceIndex, TRIANGLE_CCW_INDEX[2]);

                    Float3 normal = ((targetShape.Positions[vertexID2] - targetShape.Positions[vertexID0]).Cross(targetShape.Positions[vertexID1] - targetShape.Positions[vertexID0])).Normalized();
                    targetShape.Normals[vertexID0] += normal;
                    targetShape.Normals[vertexID1] += normal;
                    targetShape.Normals[vertexID2] += normal;
                }

                for (auto& normal : targetShape.Normals)
                {
                    normal.Normalize();
                }
            };

            CalculateNormals();
            targetShape.VertexChannels |= ImportVertexChannel::NORMAL;
        }

        if (mImportSettings->ImportTangents)
        {
            targetShape.Tangents.resize(meshVertexCount);

            meshDescription.GenerateMikkTSpaceForTargetShape(targetShape);
            targetShape.VertexChannels |= ImportVertexChannel::TANGENT;
        }

        // Adjust blend shape vertex by face offset
        if (needAdjustByFaceOffset)
        {
            const Float4x4 faceOffset = Float4x4::CreateRotationY(mImportSettings->FaceOffset / 180.0f * MathUtils::MathPi);

            for (auto& position : targetShape.Positions)
            {
                position = Float4x4::TransformPointF3(faceOffset, position);
            }
            for (auto& normal : targetShape.Normals)
            {
                normal = Float4x4::TransformVectorF3(faceOffset, normal);
            }
            for (auto& tangent : targetShape.Tangents)
            {
                tangent = Float4x4::Transform(faceOffset, tangent);
            }
        }

        // Calculate delta shapes
        auto& deltaShape = meshDescription.GetDeltaShape(channelIndex, shapeIndex);
        deltaShape.NormalizedFullWeight = targetShape.NormalizedFullWeight;
        deltaShape.VertexChannels = targetShape.VertexChannels;

        auto CalculateDeltaShapeFromMeshToTargetShape = [&](const std::vector<MeshBuildVertex>& baseMeshVertices, const ShapeDesc& targetShape, DeltaShapeDesc& outDeltaShape) 
        {
            for (UInt32 meshVertexIdx = 0; meshVertexIdx < meshVertexCount; ++meshVertexIdx)
            {
                auto deltaPos = targetShape.Positions[meshVertexIdx] - baseMeshVertices[meshVertexIdx].Position;
                auto deltaNormal = targetShape.Normals[meshVertexIdx] - baseMeshVertices[meshVertexIdx].Normal;
                auto deltaTangent = targetShape.Tangents[meshVertexIdx] - baseMeshVertices[meshVertexIdx].Tangent;

                if (deltaPos.LengthSquared() > PointNearThresSquare)
                {
                    outDeltaShape.Positions.emplace_back(deltaPos);
                    outDeltaShape.Normals.emplace_back(deltaNormal);
                    outDeltaShape.Tangents.emplace_back(deltaTangent);
                    outDeltaShape.InfluencedVertexIDs.emplace_back(meshVertexIdx);
                }
            }
        };

        auto CalculateDeltaShapeBetweenShapes = [&](const ShapeDesc& baseShape, const ShapeDesc& targetShape, DeltaShapeDesc& outDeltaShape) 
        {
            for (UInt32 meshVertexIdx = 0; meshVertexIdx < meshVertexCount; ++meshVertexIdx)
            {
                auto deltaPos = targetShape.Positions[meshVertexIdx] - baseShape.Positions[meshVertexIdx];
                auto deltaNormal = targetShape.Normals[meshVertexIdx] - baseShape.Normals[meshVertexIdx];
                auto deltaTangent = targetShape.Tangents[meshVertexIdx] - baseShape.Tangents[meshVertexIdx];

                if (deltaPos.LengthSquared() > PointNearThresSquare)
                {
                    outDeltaShape.Positions.emplace_back(deltaPos);
                    outDeltaShape.Normals.emplace_back(deltaNormal);
                    outDeltaShape.Tangents.emplace_back(deltaTangent);
                    outDeltaShape.InfluencedVertexIDs.emplace_back(meshVertexIdx);
                }
            }
        };

        if (shapeIndex == 0)
        {
            CalculateDeltaShapeFromMeshToTargetShape(meshBuildVertices, targetShape, deltaShape);

            if (!deltaShape.InfluencedVertexIDs.empty())
            {
                curBlendShape.ChannelValidFlags[channelIndex] = true;
            }
        }
        else
        {
            const auto& prevTargetShape = meshDescription.GetTargetShape(channelIndex, shapeIndex - 1);
            CalculateDeltaShapeBetweenShapes(prevTargetShape, targetShape, deltaShape);
        }

        return true;
    }

    void FBXImporter::AdjustMeshDescByFaceOffset(MeshDescription& outMeshDesc)
    {
        const Float4x4 faceOffset = Float4x4::CreateRotationY(mImportSettings->FaceOffset / 180.0f * MathUtils::MathPi);

        auto& meshVertices = outMeshDesc.GetMeshBuildVerticesRef();
        for (auto& meshVertex : meshVertices)
        {
            meshVertex.Position = Float4x4::TransformPointF3(faceOffset, meshVertex.Position);
            meshVertex.Normal = Float4x4::TransformVectorF3(faceOffset, meshVertex.Normal);
            meshVertex.Tangent = Float4x4::Transform(faceOffset, meshVertex.Tangent);
        }
    }

    void FBXImporter::TransferSkin(ImportScene& importScene)
    {
        const UInt32 meshPartCount = static_cast<UInt32>(importScene.MeshDescriptions.size());

        for (UInt32 i = 0U; i < meshPartCount; ++i)
        {
            FbxMesh* pFbxMesh = mMeshDescSrcFbxMeshes[i];
            Assert(pFbxMesh);

            ImportSkinData(pFbxMesh, importScene.MeshDescriptions[i]);
        }
    }

    void FBXImporter::TransferAnimations(const SkeletonDesc& skeleton, std::vector<AnimationDesc>& outAnimations)
    {
        // No bones
        if (skeleton.Bones.empty())
        {
            return;
        }

        int animStackCount = mScene->GetSrcObjectCount<fbxsdk::FbxAnimStack>();
        for (int stackIdx = 0; stackIdx < animStackCount; stackIdx++)
        {
            fbxsdk::FbxAnimStack* curAnimStack = mScene->GetSrcObject<fbxsdk::FbxAnimStack>(stackIdx);
            Assert(curAnimStack);
            Assert(curAnimStack->GetSrcObjectCount<fbxsdk::FbxAnimLayer>() != 0);

            // Bake multi animation layers into one layer in stack
            if (curAnimStack->GetSrcObjectCount<fbxsdk::FbxAnimLayer>() > 1)
            {
                FbxTime bakeStartTime, bakeEndTime, fbxSampleRate;
                FbxTimeSpan bakeTimeSpan = curAnimStack->GetLocalTimeSpan();

                bakeStartTime = bakeTimeSpan.GetStart();
                bakeEndTime = bakeTimeSpan.GetStop();

                const double sampleRate = 1.0f / FbxTime::GetFrameRate(mScene->GetGlobalSettings().GetTimeMode());
                fbxSampleRate.SetSecondDouble(sampleRate);

                if (!curAnimStack->BakeLayers(mScene->GetAnimationEvaluator(), bakeStartTime, bakeEndTime, fbxSampleRate))
                {
                    LOG_EDITOR_WARNING("Failed to bake layers.\n");
                }
            }
            Assert(curAnimStack->GetSrcObjectCount<fbxsdk::FbxAnimLayer>() == 1);

            FbxTimeSpan animTimeSpan = curAnimStack->GetLocalTimeSpan();
            bool validAnimStack = ValidateAnimStack(curAnimStack, animTimeSpan);

            // No valid animation
            if (!validAnimStack)
            {
                continue;
            }

            AnimationDesc curAnim;
            if (ImportAnimation(curAnimStack, animTimeSpan, skeleton, curAnim))
            {
                outAnimations.emplace_back(std::move(curAnim));
            }
        }
    }

    void FBXImporter::TransferCurveData(CurveDataDesc& outCurveData)
    {
        if (mScene == nullptr)
            return;

        // merge animation layer at first
        FbxAnimStack* AnimStack = mScene->GetMember<fbxsdk::FbxAnimStack>(0);
        if (!AnimStack)
            return;

        FbxAnimLayer* AnimLayer = AnimStack->GetMember<fbxsdk::FbxAnimLayer>(0);
        if (AnimLayer == NULL)
            return;       

        FbxNode* RootNode = mScene->GetRootNode();
        if (RootNode == nullptr || RootNode->GetChildCount() == 0)
            return;
        
        outCurveData.Reset();

        std::string saveDirectory = PathHelper::GetDirectoryFromAbsolutePath(mWritePath);
        std::string baseName = PathHelper::GetBaseFileName(mWritePath);
        std::string savePath = saveDirectory + baseName;
        
        outCurveData.Staging = PathHelper::GetInternalRootDirectory(savePath);
        outCurveData.Refference = PathHelper::GetRelativePath(savePath) + ".cinematic.nda";
        
        using namespace CEAssetExchange;
        //auto AESDK = CreateAssetExchangeSDKAutoPtr(outCurveData.Staging.c_str());
        IWorldAssemble* prefabOrWorld = nullptr;

        //if (mImportSettings->SequenceCurveDataBindingsType != SequenceCurveDataBindingsType::NoGenerate)
        //{
        //    bool asPrefab = mImportSettings->SequenceCurveDataBindingsType == SequenceCurveDataBindingsType::Prefab;

        //    outCurveData.RefferencePrefab = PathHelper::GetRelativePath(savePath) + (asPrefab ? ".cinematic.prefab" : ".cinematic.world");

        //    CEAssetExchange::SDKSettings sdkSettings{};
        //    AESDK->BeginAssemble(&sdkSettings);

        //    auto GUID = gResourceConvertMgr.GetGuid(outCurveData.RefferencePrefab);
        //    if (GUID != "")
        //        AESDK->AddResourceGUID(outCurveData.RefferencePrefab.c_str(), GUID.c_str());
        //    AESDK->AddResourceGUID(outCurveData.RefferencePrefab.c_str(), GUID.c_str());        
        //        AESDK->AddResourceGUID(outCurveData.RefferencePrefab.c_str(), GUID.c_str());

        //    prefabOrWorld = AESDK->CreateWorldAssemble(outCurveData.RefferencePrefab.c_str());

        //    CEAssetExchange::WorldSettings worldSettings{};
        //    worldSettings.mAsPrefab = asPrefab;
        //    worldSettings.mAutoChangeExtension = false;
        //    prefabOrWorld->SetSettings(&worldSettings);
        //}

        LoadNodeKeyframeAnimationRecursively(outCurveData, RootNode, prefabOrWorld);
    }

    void FBXImporter::LoadNodeKeyframeAnimationRecursively(CurveDataDesc& data, FbxNode* NodeToQuery, CEAssetExchange::IWorldAssemble* prefabOrWorld)
    {
        LoadNodeKeyframeAnimation(NodeToQuery, data, prefabOrWorld);
        int NodeCount = NodeToQuery->GetChildCount();
        for (int NodeIndex = 0; NodeIndex < NodeCount; ++NodeIndex)
        {
            FbxNode* ChildNode = NodeToQuery->GetChild(NodeIndex);
            LoadNodeKeyframeAnimationRecursively(data, ChildNode, prefabOrWorld);
        }
    }

    Quaternion UEFbx2CE_Rotation(Double3 fbxRotation, bool RotateLookAt)
    {
        auto x = RotateLookAt ? fbxRotation.x - 90 : fbxRotation.x;
        auto y = -fbxRotation.y;
        auto z = -fbxRotation.z + 90;
        Double3 rotator = TransformConvert::UE2CE_Rotator(Double3{x, y, z});
        return Quaternion(TransformConvert::UE2CE_Quaternion(TransformConvert::UE_RotatorToQuat(rotator)));
    }

    Quaternion UEFbx2CE_Rotation(fbxsdk::FbxVector4 fbxRotation, bool RotateLookAt)
    {
        return UEFbx2CE_Rotation(Double3{fbxRotation.mData[0], fbxRotation.mData[1], fbxRotation.mData[2]}, RotateLookAt);
    }

    void FBXImporter::LoadNodeKeyframeAnimation(FbxNode* NodeToQuery, CurveDataDesc& data, CEAssetExchange::IWorldAssemble* prefabOrWorld)
    {
        using namespace fbxsdk;
        SetupTransformForNode(NodeToQuery);

        bool RotateLookAt = false;
        FbxNodeAttribute::EType NodeType = FbxNodeAttribute::eUnknown;
        FbxNodeAttribute* NodeAttribute = NodeToQuery->GetNodeAttribute();
        
        if (NodeToQuery->GetParent() && NodeAttribute != nullptr)
        {
            NodeType = NodeAttribute->GetAttributeType();

            if (NodeType == FbxNodeAttribute::eCamera || NodeType == FbxNodeAttribute::eLight)
                RotateLookAt = true;
        }

        if (prefabOrWorld)
        {
            CEAssetExchange::IEntity* entity = nullptr;
            if (NodeAttribute != nullptr)
            {
                CEAssetExchange::Transform transform{};
                CEAssetExchange::Hierarchy hierarchy{};
                hierarchy.mTransform = &transform;
                hierarchy.mParent = reinterpret_cast<CEAssetExchange::IEntity*>(data.EntityMap[NodeToQuery->GetParent()->GetUniqueID()]);

                Float3 t = FBXPointToFloat3Remap(TransformConvert::UE2CE_Translation(FBXPointToDouble3(mScene->GetAnimationEvaluator()->GetNodeLocalTranslation(NodeToQuery))));
                Float3 s = Float3(TransformConvert::UE2CE_Scale(FBXPointToDouble3(mScene->GetAnimationEvaluator()->GetNodeLocalScaling(NodeToQuery))));

                transform.mScale[0] = s.x, transform.mScale[1] = s.y, transform.mScale[2] = s.z;
                transform.mTranslation[0] = t.x, transform.mTranslation[1] = t.y, transform.mTranslation[2] = t.z;

                fbxsdk::FbxVector4 fbxRotation = mScene->GetAnimationEvaluator()->GetNodeLocalRotation(NodeToQuery);

                Quaternion q = UEFbx2CE_Rotation(fbxRotation, RotateLookAt);
                transform.mQuarternion[0] = q.x, transform.mQuarternion[1] = q.y, transform.mQuarternion[2] = q.z, transform.mQuarternion[3] = q.w;

                switch (NodeAttribute->GetAttributeType())
                {
                case FbxNodeAttribute::eCamera:
                {
                    double ConversionFactor = mScene->GetGlobalSettings().GetSystemUnit().GetScaleFactor();
                    FbxCamera* CameraAttribute = reinterpret_cast<FbxCamera*>(NodeAttribute);
                    
                    CEAssetExchange::CameraInfo CameraInfo{};

                    auto INCH_TO_MM = [](float value) {return value * 25.4f;};
                    
                    // These always come in inches according to the FBX SDK
                    CameraInfo.mSensorWidth = INCH_TO_MM(static_cast<float>(CameraAttribute->FilmWidth.Get()));
                    CameraInfo.mSensorHeight = INCH_TO_MM(static_cast<float>(CameraAttribute->FilmHeight.Get()));
                    CameraInfo.mAspectRatio = static_cast<float>(CameraInfo.mSensorWidth / CameraInfo.mSensorHeight);

                    // These are always in mm according to the FBX SDK
                    CameraInfo.mCurrentFocalLength = static_cast<float>(CameraAttribute->FocalLength.Get());
                    //CameraInfo. = Camera->FocusDistance.Get();

                    // These come in VRED's scale units, which need to be converted to cm
                    // They don't seem to actually be used for anything though
                    CameraInfo.mNearPlane = static_cast<float>(CameraAttribute->NearPlane.Get() * ConversionFactor);
                    CameraInfo.mFarPlane = static_cast<float>(CameraAttribute->FarPlane.Get() * ConversionFactor);
                    CameraInfo.mOrthNearPlane = CameraInfo.mNearPlane;
                    CameraInfo.mOrthFarPlane = CameraInfo.mFarPlane;

                    if (CameraAttribute->ProjectionType.Get() == fbxsdk::FbxCamera::EProjectionType::eOrthogonal)
                    {
                        CameraInfo.mProjectionMode = CEAssetExchange::EProjectionMode::Orthogonal;
                    }
                    else
                    {
                        CameraInfo.mProjectionMode = CEAssetExchange::EProjectionMode::Perspective;
                    }

                    CEAssetExchange::ControllableUnitInfo ControllableUnitInfo{};
                    CameraInfo.mControllableUnitInfo = &ControllableUnitInfo;
                    ControllableUnitInfo.mControllableUnitType = CEAssetExchange::EControllableUnitType::CurveController;
                    ControllableUnitInfo.mCurveCtrResPath = data.Refference.c_str();
                    entity = prefabOrWorld->AddCamera(NodeToQuery->GetName(), &CameraInfo, &hierarchy);
                }
                break;
                case FbxNodeAttribute::eLight:
                {
                    // FbxLight* LightAttribute = (FbxLight*)NodeAttribute;
                    CEAssetExchange::LightData Light{};
                    entity = prefabOrWorld->AddLight(NodeToQuery->GetName(), &Light, &hierarchy);
                }
                break;
                default:
                {
                    entity = prefabOrWorld->AddEntity(NodeToQuery->GetName(), &hierarchy);
                }
                break;
                }
            }

            data.EntityMap[NodeToQuery->GetUniqueID()] = entity;
        }

        int NumAnimations = mScene->GetSrcObjectCount<FbxAnimStack>();

        for (int AnimationIndex = 0; AnimationIndex < NumAnimations; AnimationIndex++)
        {
            FbxAnimStack* animStack = reinterpret_cast<FbxAnimStack*>(mScene->GetSrcObject<FbxAnimStack>(AnimationIndex));
            FbxTimeSpan animTimeSpan = animStack->GetLocalTimeSpan();
            bool validAnimStack = ValidateAnimStack(animStack, animTimeSpan);
            if (!validAnimStack)
                continue;

            // Bake multi animation layers into one layer in stack
            if (animStack->GetSrcObjectCount<fbxsdk::FbxAnimLayer>() > 1)
            {
                FbxTime bakeStartTime, bakeEndTime, fbxSampleRate;
                FbxTimeSpan bakeTimeSpan = animStack->GetLocalTimeSpan();

                bakeStartTime = bakeTimeSpan.GetStart();
                bakeEndTime = bakeTimeSpan.GetStop();

                const double sampleRate = 1.0f / FbxTime::GetFrameRate(mScene->GetGlobalSettings().GetTimeMode());
                fbxSampleRate.SetSecondDouble(sampleRate);

                if (!animStack->BakeLayers(mScene->GetAnimationEvaluator(), bakeStartTime, bakeEndTime, fbxSampleRate))
                {
                    LOG_EDITOR_WARNING("Failed to bake layers.\n");
                }
            }
            Assert(animStack->GetSrcObjectCount<fbxsdk::FbxAnimLayer>() == 1);

            SInt32 startFrame = 0;
            float Duration = 0;
            bool keepStartTime = false;

            if (mImportSettings->AnimFramgeRange == StartToEnd)
            {
                Duration = static_cast<float>(animTimeSpan.GetDuration().GetSecondDouble());
                startFrame = static_cast<int>(std::ceil(static_cast<float>(animTimeSpan.GetStart().GetSecondDouble()) * DEFAULT_SAMPLERATE));
                keepStartTime = false;
            }
            else   // mImportSettings->AnimFramgeRange != StartToEnd
            {
                Duration = static_cast<float>(animTimeSpan.GetStop().GetSecondDouble());
                startFrame = 0;
                keepStartTime = true;
            }

            // At least one frame when anim duration > 0.0
            const SInt32 frameCount = static_cast<int>(std::ceil(Duration * DEFAULT_SAMPLERATE));

            if (frameCount <= 0)
                return;

            Assert("Start frame is not 0!" && startFrame >= 0);

            CurveDataDesc::SystemType SystemType = CurveDataDesc::SystemType::Unkonw;
            int numLayers = animStack->GetMemberCount();
            for (int layerIndex = 0; layerIndex < numLayers; layerIndex++)
            {
                FbxAnimLayer* AnimLayer = reinterpret_cast<FbxAnimLayer*>(animStack->GetMember(layerIndex));
                // Display curves specific to properties
                FbxObject* ObjectToQuery = reinterpret_cast<FbxObject*>(NodeToQuery);

                FbxAnimCurve* TransformCurves[9] = {};
                TransformCurves[0] = NodeToQuery->LclTranslation.GetCurve(AnimLayer, FBXSDK_CURVENODE_COMPONENT_X, false);
                TransformCurves[1] = NodeToQuery->LclTranslation.GetCurve(AnimLayer, FBXSDK_CURVENODE_COMPONENT_Y, false);
                TransformCurves[2] = NodeToQuery->LclTranslation.GetCurve(AnimLayer, FBXSDK_CURVENODE_COMPONENT_Z, false);
                TransformCurves[3] = NodeToQuery->LclRotation.GetCurve(AnimLayer, FBXSDK_CURVENODE_COMPONENT_X, false);
                TransformCurves[4] = NodeToQuery->LclRotation.GetCurve(AnimLayer, FBXSDK_CURVENODE_COMPONENT_Y, false);
                TransformCurves[5] = NodeToQuery->LclRotation.GetCurve(AnimLayer, FBXSDK_CURVENODE_COMPONENT_Z, false);
                TransformCurves[6] = NodeToQuery->LclScaling.GetCurve(AnimLayer, FBXSDK_CURVENODE_COMPONENT_X, false);
                TransformCurves[7] = NodeToQuery->LclScaling.GetCurve(AnimLayer, FBXSDK_CURVENODE_COMPONENT_Y, false);
                TransformCurves[8] = NodeToQuery->LclScaling.GetCurve(AnimLayer, FBXSDK_CURVENODE_COMPONENT_Z, false);

                bool hasTransformCurves = std::find_if(std::begin(TransformCurves), std::end(TransformCurves), [](FbxAnimCurve* curve) { return curve != nullptr; }) != std::end(TransformCurves);

                auto& ResData = data.Res->GetCurveControllerData();                
                CurveControllerData* CurveDataEntity = nullptr;

                auto addCurves = [&](FbxAnimCurve* curve, std::string name, FloatCurveList& curveData, bool removeDupKey = true, bool keepStart = true) {
                    if (curve)
                        ImportCurveToAnimSequence(curve, name, animTimeSpan, curveData, CurveUseType::Common, removeDupKey, keepStart);
                };

                if (hasTransformCurves)
                {
                    if (!CurveDataEntity)
                        CurveDataEntity = &ResData.emplace_back();
                    
                    if (NodeToQuery->LclTranslation.IsAnimated() && (TransformCurves[0] || TransformCurves[1] || TransformCurves[2]))
                    {
                        auto& DataItem = CurveDataEntity->mDataItems.emplace_back();
                        DataItem.Type = CurveControllerDataItem::ItemType::ComponentProperty;
                        CurveDataDesc::SetupSystemProperty(CurveDataDesc::SystemType::Transform, DataItem);
                        DataItem.PropertyName = "Translation";
                        DataItem.CurveType = "Float3";
                        DataItem.GenCurveDataObject();
                        auto& CurveList = *DataItem.GetCurveData().get();
                        addCurves(TransformCurves[0], "x", CurveList, false, keepStartTime);
                        addCurves(TransformCurves[1], "y", CurveList, false, keepStartTime);
                        addCurves(TransformCurves[2], "z", CurveList, false, keepStartTime);

                        auto& trackX = CurveList.FindTrackByName("x");
                        if (int n = trackX.GetNumKeys())
                        {
                            auto& trackY = CurveList.FindTrackByName("y");
                            auto& trackZ = CurveList.FindTrackByName("z");
                            Assert(n == trackY.Keys.size() && n == trackZ.Keys.size());

                            for (int i = 0; i < n; i++)
                            {
                                auto t = FBXPointToFloat3Remap(TransformConvert::UE2CE_Translation(Double3{trackX.Keys[i].Value, trackY.Keys[i].Value, trackZ.Keys[i].Value}));
                                trackX.Keys[i].Value = t.x, trackY.Keys[i].Value = t.y, trackZ.Keys[i].Value = t.z;
                            }

                            trackX.RemoveDuplicateKeys(), trackY.RemoveDuplicateKeys(), trackZ.RemoveDuplicateKeys();
                        }
                    }

                    if (NodeToQuery->LclRotation.IsAnimated() && (TransformCurves[3] || TransformCurves[4] || TransformCurves[5]))
                    {
                        auto& DataItem = CurveDataEntity->mDataItems.emplace_back();
                        DataItem.Type = CurveControllerDataItem::ItemType::ComponentProperty;
                        CurveDataDesc::SetupSystemProperty(CurveDataDesc::SystemType::Transform, DataItem);
                        DataItem.PropertyName = "Rotation";
                        DataItem.CurveType = "RotEuler";
                        DataItem.GenCurveDataObject();
                        auto& CurveList = *DataItem.GetCurveData().get();
                        addCurves(TransformCurves[3], "x", CurveList, false, keepStartTime);
                        addCurves(TransformCurves[4], "y", CurveList, false, keepStartTime);
                        addCurves(TransformCurves[5], "z", CurveList, false, keepStartTime);
                        
                        auto& trackX = CurveList.FindTrackByName("x");
                        if (int n = trackX.GetNumKeys())
                        {
                            auto& trackY = CurveList.FindTrackByName("y");
                            auto& trackZ = CurveList.FindTrackByName("z");
                            Assert(n == trackY.Keys.size() && n == trackZ.Keys.size());

                            auto& trackW = CurveList.FindTrackByName("w");
                            trackW.EnterType = CurveRepeatType::CRT_CONSTANT;
                            trackW.LeaveType = CurveRepeatType::CRT_CONSTANT;
                            trackW.UseType = CurveUseType::Common;
                            trackW.Keys.resize(n);
                            
                            for (int i = 0; i < n; i++)
                            {
                                trackW.Keys[i] = trackX.Keys[i];
                                Quaternion q = UEFbx2CE_Rotation(Double3{trackX.Keys[i].Value, trackY.Keys[i].Value, trackZ.Keys[i].Value}, RotateLookAt);
                                trackW.Keys[i].Time = trackX.Keys[i].Time;
                                trackX.Keys[i].Value = q.x, trackY.Keys[i].Value = q.y, trackZ.Keys[i].Value = q.z, trackW.Keys[i].Value = q.w;
                            }
                        }
                    }

                    if (NodeToQuery->LclScaling.IsAnimated() && (TransformCurves[6] || TransformCurves[7] || TransformCurves[8]))
                    {
                        auto& DataItem = CurveDataEntity->mDataItems.emplace_back();
                        DataItem.Type = CurveControllerDataItem::ItemType::ComponentProperty;
                        CurveDataDesc::SetupSystemProperty(CurveDataDesc::SystemType::Transform, DataItem);
                        DataItem.PropertyName = "Scale";
                        DataItem.CurveType = "Float3";
                        DataItem.GenCurveDataObject();
                        auto& CurveList = *DataItem.GetCurveData().get();
                        addCurves(TransformCurves[6], "x", CurveList, false, keepStartTime);
                        addCurves(TransformCurves[7], "y", CurveList, false, keepStartTime);
                        addCurves(TransformCurves[8], "z", CurveList, false, keepStartTime);

                        auto& trackX = CurveList.FindTrackByName("x");
                        if (int n = trackX.GetNumKeys())
                        {
                            auto& trackY = CurveList.FindTrackByName("y");
                            auto& trackZ = CurveList.FindTrackByName("z");
                            Assert(n == trackY.Keys.size() && n == trackZ.Keys.size());

                            for (int i = 0; i < n; i++)
                            {
                                auto t = Float3(TransformConvert::UE2CE_Scale(Double3{trackX.Keys[i].Value, trackY.Keys[i].Value, trackZ.Keys[i].Value}));
                                trackX.Keys[i].Value = t.x, trackY.Keys[i].Value = t.y, trackZ.Keys[i].Value = t.z;
                            }

                            trackX.RemoveDuplicateKeys(), trackY.RemoveDuplicateKeys(), trackZ.RemoveDuplicateKeys();
                        }
                    }
                }

                auto IsNotTransformCurves = [&](FbxAnimCurve* animCurve) {
                    for (auto curve : TransformCurves)
                    {
                        if (curve == animCurve)
                            return false;
                    }
                    return true;
                };

                //bool IsNodeProperty = true;
                FbxProperty CurrentProperty = ObjectToQuery->GetFirstProperty();
                while (CurrentProperty.IsValid())
                {
                    //if (NodeType == )

                    FbxAnimCurve* AnimCurve = nullptr;

                    FbxAnimCurveNode* CurveNode = CurrentProperty.GetCurveNode(AnimLayer);
                    if (CurveNode != nullptr)
                    {
                        auto addDataItem = [&](const char* CurveType) 
                        {
                            const char* channel[4] = {"x", "y", "z", "w"};

                            for (int c = 0; c < CurveNode->GetCurveCount(0U); c++)
                            {
                                AnimCurve = CurveNode->GetCurve(0U, c);
                                if (ShouldImportCurve(AnimCurve) && IsNotTransformCurves(AnimCurve))
                                {
                                    if (!CurveDataEntity)
                                        CurveDataEntity = &ResData.emplace_back();

                                    auto& DataItem = CurveDataEntity->mDataItems.emplace_back();
                                    DataItem.Type = CurveControllerDataItem::ItemType::ComponentProperty;
                                    CurveDataDesc::SetupSystemProperty(SystemType, DataItem);
                                    DataItem.PropertyName = CurrentProperty.GetNameAsCStr();
                                    DataItem.CurveType = CurveType;
                                    DataItem.GenCurveDataObject();
                                    auto& CurveList = *DataItem.GetCurveData().get();
                                    addCurves(AnimCurve, channel[c], CurveList, true, keepStartTime);
                                }
                            }
                        };

                        FbxDataType DataType = CurrentProperty.GetPropertyDataType();
                        if (DataType.GetType() == fbxsdk::eFbxDouble || DataType.GetType() == fbxsdk::eFbxFloat)
                        {
                            addDataItem("Float1");
                        }
                        else if (DataType.GetType() == fbxsdk::eFbxDouble3)
                        {
                            addDataItem("Float3");
                        }
                        else if (DataType.GetType() == fbxsdk::eFbxDouble4)
                        {
                            addDataItem("Float4");
                        }
                        else
                        {
                            LOG_EDITOR_WARNING("[FBX Curve Import] Property {} With Unkown dtat type {}.\n", CurrentProperty.GetNameAsCStr(), DataType.GetName());
                        }
                    }

                    CurrentProperty = ObjectToQuery->GetNextProperty(CurrentProperty);
                    if (!CurrentProperty.IsValid() && ObjectToQuery->GetUniqueID() == NodeToQuery->GetUniqueID())
                    {
                        if (NodeAttribute != nullptr)
                        {
                            switch (NodeAttribute->GetAttributeType())
                            {
                            case FbxNodeAttribute::eCamera:
                            {
                                FbxCamera* CameraAttribute = reinterpret_cast<FbxCamera*>(NodeAttribute);
                                CurrentProperty = CameraAttribute->GetFirstProperty();
                                SystemType = CurveDataDesc::SystemType::Camera;
                            }
                            break;
                            case FbxNodeAttribute::eLight:
                            {
                                FbxLight* LightAttribute = reinterpret_cast<FbxLight*>(NodeAttribute);
                                CurrentProperty = LightAttribute->GetFirstProperty();
                                SystemType = CurveDataDesc::SystemType::Light;
                            }
                            break;
                            }
                            ObjectToQuery = NodeAttribute;
                        }
                    }
                }   // while

                
                if (CurveDataEntity)
                {
                    if (prefabOrWorld)
                        CurveDataEntity->mEntityID = prefabOrWorld->GetEntityUUID(reinterpret_cast<CEAssetExchange::IEntity*>(data.EntityMap[NodeToQuery->GetUniqueID()]));

                    CurveDataEntity->mDuration = Duration;
                }
            }
        }
    }

    // This function now clears out all pivots, post and pre rotations and set's the
    // RotationOrder to XYZ.
    // Updated per the latest documentation
    // https://help.autodesk.com/view/FBX/2017/ENU/?guid=__files_GUID_C35D98CB_5148_4B46_82D1_51077D8970EE_htm
    void FBXImporter::SetupTransformForNode(FbxNode* Node)
    {
        // Activate pivot converting
        Node->SetPivotState(FbxNode::eSourcePivot, FbxNode::ePivotActive);
        Node->SetPivotState(FbxNode::eDestinationPivot, FbxNode::ePivotActive);

        FbxVector4 Zero(0, 0, 0);

        // We want to set all these to 0 and bake them into the transforms.
        Node->SetPostRotation(FbxNode::eDestinationPivot, Zero);
        Node->SetPreRotation(FbxNode::eDestinationPivot, Zero);
        Node->SetRotationOffset(FbxNode::eDestinationPivot, Zero);
        Node->SetScalingOffset(FbxNode::eDestinationPivot, Zero);
        Node->SetRotationPivot(FbxNode::eDestinationPivot, Zero);
        Node->SetScalingPivot(FbxNode::eDestinationPivot, Zero);

        Node->SetRotationOrder(FbxNode::eDestinationPivot, eEulerXYZ);
        // When  we support other orders do this.
        // EFbxRotationOrder ro;
        // Node->GetRotationOrder(FbxNode::eSourcePivot, ro);
        // Node->SetRotationOrder(FbxNode::eDestinationPivot, ro);

        // Most DCC's don't have this but 3ds Max does
        Node->SetGeometricTranslation(FbxNode::eDestinationPivot, Zero);
        Node->SetGeometricRotation(FbxNode::eDestinationPivot, Zero);
        Node->SetGeometricScaling(FbxNode::eDestinationPivot, Zero);
        // NOTE THAT ConvertPivotAnimationRecursive did not seem to work when getting the local transform values!!!
        Node->ResetPivotSetAndConvertAnimation(FbxTime::GetFrameRate(mScene->GetGlobalSettings().GetTimeMode()));
    }

    void FBXImporter::TransferCollider(const std::string& name, FbxNode* fbxNode, ImportScene& output)
    {
        if (FbxMesh* fbxMesh = fbxNode->GetMesh())
        {
            fbxsdk::FbxVector4* controlPoints = fbxMesh->GetControlPoints();
            const SInt32 vertexCount = fbxMesh->GetControlPointsCount();
            std::vector<Float3> vertex;
            vertex.reserve(vertexCount);
            for (auto i = 0; i < vertexCount; ++i)
            {
                const fbxsdk::FbxVector4& point = ComputeTotalMatrix(fbxNode).MultT(controlPoints[i]);
                vertex.emplace_back(FBXPointToFloat3Remap(point));
            }

            BoundingBox aabb;
            BoundingBox::CreateFromPoints(aabb, vertex.size(), vertex.data(), sizeof(Float3));
            Float3 center;
            Float3 extent;
            aabb.GetCenter(&center);
            aabb.GetExtent(&extent);

            // simple collision
            if (FBXHelper::SubmeshNameIsPhysicsCollisionBox(name))
            {
                output.PhyCollision.boxCollision.emplace_back(center, Quaternion::Identity(), extent);
            }
            else if (FBXHelper::SubmeshNameIsPhysicsCollisionSphere(name))
            {
                if (abs(extent.x - extent.y) > 0.001 || abs(extent.x - extent.z) > 0.001)
                {
                    LOG_EDITOR_ERROR("FBXImporter: Import physics collision error: {} AABB is not square", name);
                }
                output.PhyCollision.sphereCollision.emplace_back(center, extent.x);
            }
            else if (FBXHelper::SubmeshNameIsPhysicsCollisionCapsule(name))
            {
                float radius = 0;
                float halfExtent = 0;
                if (abs(extent.x - extent.y) < 0.001)
                {
                    radius = extent.x;
                    halfExtent = extent.z * 0.5f;
                }
                else if (abs(extent.x - extent.z) < 0.001)
                {
                    radius = extent.x;
                    halfExtent = extent.y * 0.5f;
                }
                else if (abs(extent.y - extent.z) < 0.001)
                {
                    radius = extent.y;
                    halfExtent = extent.x * 0.5f;
                }
                else
                {
                    LOG_EDITOR_ERROR("FBXImporter: Import physics collision error: {} is not a capsule", name);
                }
                output.PhyCollision.capsuleCollision.emplace_back(center, Quaternion::Identity(), radius, halfExtent);
            }
            else if (FBXHelper::SubmeshNameIsPhysicsCollisionConvex(name))
            {
                int* indice = fbxMesh->GetPolygonVertices();
                const int indiceCount = fbxMesh->GetPolygonVertexCount();
                PhysicsEngine* physicsEngine = EngineGlobal::GetPhysicsEngine();
                std::shared_ptr<PhysicsConvexMesh> convex = physicsEngine->GetCooker()->BuildConvexMesh(
                    reinterpret_cast<UInt8*>(vertex.data()), static_cast<UInt32>(vertex.size()), static_cast<UInt16>(sizeof(Float3)), reinterpret_cast<UInt8*>(indice), static_cast<UInt32>(indiceCount), static_cast<UInt16>(sizeof(UInt32)));
                std::vector<UInt8> serializeData = physicsEngine->GetCooker()->SerializeConvexMesh(convex.get());
                output.PhyCollision.convexCollision.emplace_back(PhysicsConvexCollisionImport{{0.0f, 0.0f, 0.0f}, Quaternion::Identity(), std::move(serializeData)});
            }
        }
    }

    void FBXImporter::FindAllMeshNodeInCurrentLOD(FbxNode* lodGroupNode, SInt32 lodIndex, std::vector<FbxNode*>& output)
    {
        Assert(lodGroupNode->GetChildCount() > lodIndex);
        if (FbxNode* childNode = lodGroupNode->GetChild(lodIndex))
        {
            RecursiveGetAllMeshNode(childNode, output);
        }
    }

    void FBXImporter::RecursiveGetAllMeshNode(FbxNode* fbxNode, std::vector<FbxNode*>& output)
    {
        if (fbxNode == nullptr)
            return;

        if (fbxNode->GetMesh() != nullptr)
        {
            output.emplace_back(fbxNode);
            return;
        }

        for (SInt32 index = 0; index < fbxNode->GetChildCount(); ++index)
        {
            RecursiveGetAllMeshNode(fbxNode->GetChild(index), output);
        }
    }

    void FBXImporter::RecordMeshSharedInfo(FbxNode* fbxNode, FbxMesh* fbxMesh)
    {
        auto sharedMeshInfo = std::make_unique<SharedFbxMeshInfo>();
        sharedMeshInfo->FbxNodes.emplace_back(fbxNode);
        mFbxMeshSharedInfoMap.insert(std::make_pair(fbxMesh, std::move(sharedMeshInfo)));
    }

    bool FBXImporter::RecursiveImportNode(FbxNode* fbxNode, ImportScene& scene)
    {
        std::string name = static_cast<const char*>(fbxNode->GetNameWithoutNameSpacePrefix());

        if (FBXHelper::SubmeshNameIsPhysicsCollision(name))
        {
            TransferCollider(name, fbxNode, scene);
            return true;
        }

        auto TransferNode = [this, &scene](FbxNode* node) ->bool 
        {
            if (auto fbxMesh = node->GetMesh())
            {
                auto iter = mFbxMeshSharedInfoMap.find(fbxMesh);
                if (iter != mFbxMeshSharedInfoMap.end())
                {
                    auto sharedMeshInfo = iter->second.get();
                    sharedMeshInfo->FbxNodes.emplace_back(node);

                    // Multi FbxNode shared a FbxMesh, don't need transfer mesh again (On which situation?)
                    // scene.MeshDescriptions.emplace_back(scene.MeshDescriptions[sharedMeshInfo->nIndex]);
                }
                else
                {
                    if (fbxsdk::FbxMesh* newMesh = PrepareFbxMesh(fbxMesh))
                    {
                        auto& materialDesc = scene.MaterialDescriptions.emplace_back();
                        TransferMaterial(scene, node, newMesh, materialDesc);

                        auto& materialMeshBind = scene.MaterialMeshBindings.emplace_back();
                        if (auto bTransferMeshState = TransferMesh(newMesh, ComputeTotalMatrix(node), materialDesc, materialMeshBind, scene.MeshDescriptions); !bTransferMeshState)
                            return false;
                        RecordMeshSharedInfo(node, fbxMesh);
                    }
                }
            }
            mClusterLinks.insert(node);
            return true;
        };

        const SInt32 childCount = fbxNode->GetChildCount();
        const bool isLodGroupNode = fbxNode->GetNodeAttribute() && fbxNode->GetNodeAttribute()->GetAttributeType() == FbxNodeAttribute::eLODGroup;

        // Current node is LODGroup node and lod count > 0
        if (isLodGroupNode && childCount > 0)
        {
            std::vector<FbxNode*> curLODMeshNodes;

            if (mImportSettings->ImportMeshLODs)
            {
                for (SInt32 lodIndex = 0; lodIndex < childCount; ++lodIndex)
                {
                    if (lodIndex > MAX_STATIC_MESH_LODS)
                    {
                        LOG_EDITOR_ERROR("Reached the maximum number of LODs for Mesh({})", fbxNode->GetChild(lodIndex)->GetName());
                        break;
                    }

                    UInt32 originMeshDescCount = static_cast<UInt32>(scene.MeshDescriptions.size());

                    curLODMeshNodes.clear();
                    FindAllMeshNodeInCurrentLOD(fbxNode, lodIndex, curLODMeshNodes);

                    for (FbxNode* node : curLODMeshNodes)
                    {
                        if (auto bTransferNodeState = TransferNode(node); !bTransferNodeState)
                        {
                            return false;
                        }
                    }

                    UInt32 newMeshDescCount = static_cast<UInt32>(scene.MeshDescriptions.size());
                    // Current LOD mesh imported successfully
                    if (newMeshDescCount > originMeshDescCount)
                    {
                        // Record current lod mesh part start index
                        scene.LodStartIndexArray.emplace_back(originMeshDescCount);
                        scene.LodCount++;
                    }
                }
            }
            else
            {
                // Only transfer lod[0] mesh when ImportMeshLODs is closed.
                FindAllMeshNodeInCurrentLOD(fbxNode, 0, curLODMeshNodes);

                for (auto& node : curLODMeshNodes)
                {
                    if (auto bTransferNodeState = TransferNode(node); !bTransferNodeState)
                    {
                        return false;
                    }
                }
            }
        }
        // Current node is not LODGroup node
        else
        {
            if (auto bTransferNodeState = TransferNode(fbxNode); !bTransferNodeState)
                return false;

            for (SInt32 i = 0; i < childCount; ++i)
            {
                fbxsdk::FbxNode* fbxChild = fbxNode->GetChild(i);
                if (auto bRINState = RecursiveImportNode(fbxChild, scene); !bRINState)
                    return false;
            }
        }
        return true;
    }

    bool FBXImporter::BuildMeshDescription(fbxsdk::FbxMesh* fbxMesh, const MaterialDescription& materialDesc, const FbxAMatrix& totalMatrix,
        const std::vector<int>& smoothingGroups, const std::vector<UInt32>& materialPolygons, MeshDescription& meshDescription)
    {
        FbxLayer* baseLayer = fbxMesh->GetLayer(0);
        if (baseLayer == nullptr)
        {
            return false;
        }

        const auto polygonCnt = materialPolygons.size() > 0 ? materialPolygons.size() : fbxMesh->GetPolygonCount();
        meshDescription.Init(static_cast<UInt32>(polygonCnt));

        std::vector<SInt32> indices;
        std::vector<UInt32> triangles(polygonCnt);
        std::vector<SInt32> triangleMatIds(polygonCnt);
        std::set<VertexID> vertices;

        auto& triangleInstance = meshDescription.GetTriangleInstancesRef();
        bool bIsMeshComplete = true;
        if (materialPolygons.size() > 0)
        {
            indices.reserve(polygonCnt * 3);
            VertexInstanceID vertexInstanceID = 0;
            for (UInt32 index = 0; index < materialPolygons.size(); index++)
            {
                UInt32 faceIndex = materialPolygons[index];

                triangles[index] = faceIndex;
                triangleMatIds[index] = materialDesc.FindMaterialIndex(faceIndex);
                auto& triangleView = triangleInstance.emplace_back(faceIndex);
                // polygonVertexCnt is actually 3, since triangulation has already been done
                const SInt32 polygonVertexCnt = fbxMesh->GetPolygonSize(faceIndex);
                if (polygonVertexCnt != 3)
                {
                    LOG_ERROR("polygonVertexCnt is {} not 3, triangulation failed", polygonVertexCnt);
                    bIsMeshComplete = false;
                    break;
                }
                for (SInt32 cornerIndex = 0; cornerIndex < polygonVertexCnt; ++cornerIndex, ++vertexInstanceID)
                {
                    triangleView[cornerIndex] = vertexInstanceID;
                    const SInt32 vertexID = fbxMesh->GetPolygonVertex(faceIndex, TRIANGLE_CCW_INDEX[cornerIndex]);
                    indices.emplace_back(vertexID);
                    vertices.insert(vertexID);
                    meshDescription.SetVertexInstanceVertex(vertexInstanceID, vertexID);
                }
            }
        }
        else
        {
            for (SInt32 polygonIndex = 0, vertexInstanceID = 0; polygonIndex < polygonCnt; ++polygonIndex)
            {
                triangles[polygonIndex] = polygonIndex;
                triangleMatIds[polygonIndex] = materialDesc.FindMaterialIndex(polygonIndex);

                auto& triangleView = triangleInstance.emplace_back(polygonIndex);

                // polygonVertexCnt is actually 3, since triangulation has already been done
                const SInt32 polygonVertexCnt = fbxMesh->GetPolygonSize(polygonIndex);
                if (polygonVertexCnt != 3)
                {
                    LOG_ERROR("polygonVertexCnt is {} not 3, triangulation failed", polygonVertexCnt);
                    bIsMeshComplete = false;
                    break;
                }
                for (SInt32 cornerIndex = 0; cornerIndex < polygonVertexCnt; ++cornerIndex, ++vertexInstanceID)
                {
                    triangleView[cornerIndex] = vertexInstanceID;
                    const SInt32 vertexID = fbxMesh->GetPolygonVertex(polygonIndex, TRIANGLE_CCW_INDEX[cornerIndex]);
                    indices.emplace_back(vertexID);
                    vertices.insert(vertexID);
                    meshDescription.SetVertexInstanceVertex(vertexInstanceID, vertexID);
                }
            }
        }
        if (!bIsMeshComplete)
        {
            return false;
        }
        meshDescription.RecordRawIndices(indices.data(), static_cast<UInt32>(indices.size()));
        Assert(polygonCnt > 0);

        FillMeshRemappedVertex(*fbxMesh, totalMatrix, meshDescription, vertices);

        FbxUVs fbxUVs(fbxMesh);
        RemeshTopologySmooth(*fbxMesh, totalMatrix, triangles, fbxUVs, smoothingGroups, triangleMatIds, meshDescription);

        // If FBX has no normal info but need to import, then recompute it
        if (mImportSettings->CalculateNormals || (!FBXHelper::HasNormal(meshDescription)))
        {
            RecomputeNormals(meshDescription);
        }

        // If FBX has no tangent info but need to import, then GenerateMikkTSpace tangent
        if (mImportSettings->ImportTangents && !(baseLayer->GetTangents() && baseLayer->GetBinormals()))
        {
            meshDescription.GenerateMikkTSpace();
        }

        // Check validation of normal & tangent
        if (mImportSettings->ImportNormals && mImportSettings->ImportTangents)
        {
            if (FBXHelper::HasBadNTB(meshDescription))
            {
                mImportResult = AssetImportState::HasBadTangent;
                LOG_EDITOR_ERROR("Mesh {} has some nearly zero normal/tangent which can create some issues.", meshDescription.GetName());
            }
        }

        // Adjust Mesh Vertex by face offset
        if (mImportSettings->FaceOffset != 0.f)
        {
            AdjustMeshDescByFaceOffset(meshDescription);
        }

        return true;
    }

    FbxAMatrix FBXImporter::ComputeTotalMatrix(FbxNode* node)
    {
        FbxAMatrix geometry = GetGeometryOffset(node);
        // For Single Matrix situation, obtain transform matrix from eDESTINATION_SET, which include pivot offsets and pre/post rotations.
        FbxAMatrix& globalTransform = mScene->GetAnimationEvaluator()->GetNodeGlobalTransform(node);

        // We can bake the pivot only if we don't transform the vertex to the absolute position
        if (!mImportSettings->TransformVertexToAbsolute)
        {
            if (mImportSettings->BakePivotInVertex)
            {
                FbxAMatrix pivotGeometry;
                FbxVector4 rotationPivot = node->GetRotationPivot(FbxNode::eSourcePivot);
                FbxVector4 fullPivot;
                fullPivot[0] = -rotationPivot[0];
                fullPivot[1] = -rotationPivot[1];
                fullPivot[2] = -rotationPivot[2];
                pivotGeometry.SetT(fullPivot);
                geometry = geometry * pivotGeometry;
            }
            else
            {
                geometry.SetIdentity();
            }
        }

        FbxAMatrix totalMatrix = mImportSettings->TransformVertexToAbsolute ? globalTransform * geometry : geometry;
        return totalMatrix;
    }

    void FBXImporter::RemeshTopologySmooth(const FbxMesh& fbxMesh, const FbxAMatrix& totalMatrix, const std::vector<UInt32>& triangles, 
        const FbxUVs& fbxUVs, const std::vector<int>& smoothingGroups, const std::vector<SInt32>& triangleMatIds,
                                           MeshDescription& meshDescription)
    {
        std::vector<Float3> smoothNormals;
        if (!smoothingGroups.empty())
        {
            //TODO(xujh): some bug need fix later.
            //FBXHelper::GenerateSmoothNormals(meshDescription, smoothingGroups, smoothNormals);
        }

        OverlappingCorners overlappingCorners;
        FBXHelper::FindOverlappingCorners(meshDescription, THRESH_POINTS_ARE_SAME, overlappingCorners);
        std::vector<SInt32> remapVerts(triangles.size() * 3, INDEX_NONE);

        SInt32 curVertexInstanceIndex = 0;
        bool hasVertexColor = meshDescription.HasVertexColor() || mImportSettings->ImportVertexColor;
        for (UInt32 index = 0; index < triangles.size(); index++)
        {
            UInt32 triangleIndex = triangles[index];
            const int beginIndex = fbxMesh.GetPolygonVertexIndex(triangleIndex);
            for (SInt32 cornerIndex = 0; cornerIndex < 3; ++cornerIndex)
            {
                // Fbx use CW vertex index while mapping mode is FbxLayerElement::eByPolygonVertex, so get the CW index here as realFbxVertexIndex
                const SInt32 realFbxVertexIndex = beginIndex + TRIANGLE_CCW_INDEX[cornerIndex];
                const SInt32 vertexInstanceID = curVertexInstanceIndex + cornerIndex;
                const SInt32 vertexID = meshDescription.GetVertexInstanceVertex(vertexInstanceID);
                const auto& position = meshDescription.GetRemappedVertexPosition(vertexID);

                MeshBuildVertex meshVertex(position);
                meshVertex.ControlPointID = vertexID;
                FillMeshBuildVertex(*fbxMesh.GetLayer(0), fbxUVs, totalMatrix, vertexID, realFbxVertexIndex, triangleMatIds[index], hasVertexColor, meshVertex);

                if (!hasVertexColor && (meshVertex.Channels & ImportVertexChannel::COLOR) && mImportSettings->ImportVertexColor)
                {
                    hasVertexColor = true;
                    meshDescription.RecordVertexColor(true);
                    mImportResult = AssetImportState::PaddingVertexColor;
                }

                if (vertexInstanceID < smoothNormals.size())
                {
                    meshVertex.Normal = smoothNormals[vertexInstanceID];
                }

                // Never add duplicated MeshBuildVertex
                // Use vertexInstanceID as key since OverlappingCorners has been built based on that
                const auto& dupVerts = overlappingCorners.FindIfOverlapping(vertexInstanceID);

                SInt32 meshBuildVertexIndex = INDEX_NONE;
                for (SInt32 k = 0; k < dupVerts.size(); ++k)
                {
                    SInt32 location = dupVerts[k] < remapVerts.size() ? remapVerts[dupVerts[k]] : INDEX_NONE;
                    if (location != INDEX_NONE)
                    {
                        if (mImportSettings->MergeNormals)
                        {
                            const auto& vertex = meshDescription.GetMeshBuildVertex(location);
                            if (FBXHelper::AreVerticesPosEqual(meshVertex, vertex, THRESH_POINTS_ARE_SAME))
                            {
                                meshVertex.Normal = meshVertex.Normal + vertex.Normal;
                                meshVertex.Normal.Normalized();
                                meshBuildVertexIndex = location;
                                break;
                            }
                        }
                        else
                        {
                            if (FBXHelper::AreVerticesEqual(meshVertex, meshDescription.GetMeshBuildVertex(location), THRESH_POINTS_ARE_SAME))
                            {
                                meshBuildVertexIndex = location;
                                break;
                            }
                        }
                    }
                }

                if (meshBuildVertexIndex == INDEX_NONE)
                {
                    meshBuildVertexIndex = meshDescription.AddMeshBuildVertex(meshVertex);
                }

                meshDescription.SetVertexInstanceVertex(vertexInstanceID, meshBuildVertexIndex);
                remapVerts[vertexInstanceID] = meshBuildVertexIndex;
            }

            curVertexInstanceIndex += 3;
        }
    }

    void FBXImporter::FillMeshRemappedVertex(const FbxMesh& mesh, const FbxAMatrix& totalMatrix, MeshDescription& output, std::set<VertexID>& outVertices)
    {
        const int rawVertexCnt = mesh.GetControlPointsCount();
        output.ReserveNewVertices(rawVertexCnt);
        for (const VertexID vertexID : outVertices)
        {
            fbxsdk::FbxVector4 controlPoint = mesh.GetControlPointAt(vertexID);
            fbxsdk::FbxVector4 fbxPos = totalMatrix.MultT(controlPoint);
            output.CreateRemappedVertex(vertexID, FBXPointToFloat3Remap(fbxPos));
        }
    }

    void FBXImporter::FillMeshBuildVertex(const FbxLayer& layer, const FbxUVs& uvs, const FbxAMatrix& totalMatrix, VertexID vertexID, SInt32 realIndex, const MaterialID& matId, bool hasVertexColor, MeshBuildVertex& output)
    {
        // Fill UV
        const SInt32 uvCount = std::min<SInt32>(MAX_UV_SUPPORT, uvs.mUVCount);
        for (SInt32 layerIndex = 0; layerIndex < uvCount; ++layerIndex)
        {
            if (FbxLayerElementUV const* element = uvs.mLayerElementUV[layerIndex])
            {
                SInt32 uvMapIndex = (uvs.mUVMappingMode[layerIndex] == FbxLayerElement::eByControlPoint) ? vertexID : realIndex;
                SInt32 uvIndex = (uvs.mUVReferenceMode[layerIndex] == FbxLayerElement::eDirect) ? uvMapIndex : element->GetIndexArray().GetAt(uvMapIndex);

                FbxVector2 uvVector = element->GetDirectArray().GetAt(uvIndex);
                output.UVs[layerIndex] = Float2(static_cast<float>(uvVector[0]), static_cast<float>(uvVector[1]));

                output.UVChannelNum += 1;
                // UV channel 0 mask is 16(1<<4)
                output.Channels |= static_cast<ImportVertexChannel>(1 << (3 + output.UVChannelNum));
            }
        }
        output.MatID = matId;

        // Fill Color
        if (mImportSettings->ImportVertexColor)
        {
            if (const FbxLayerElementVertexColor* layerElementColor = layer.GetVertexColors())
            {
                FbxLayerElement::EMappingMode mappingMode = layerElementColor->GetMappingMode();
                FbxLayerElement::EReferenceMode referenceMode = layerElementColor->GetReferenceMode();
                SInt32 mappingIndex = (mappingMode == FbxLayerElement::eByControlPoint) ? vertexID : realIndex;
                SInt32 colorIndex = (referenceMode == FbxLayerElement::eDirect) ? mappingIndex : layerElementColor->GetIndexArray().GetAt(mappingIndex);

                fbxsdk::FbxColor vertexColor = layerElementColor->GetDirectArray().GetAt(colorIndex);
                output.RawColor.x = static_cast<float>(vertexColor.mRed);
                output.RawColor.y = static_cast<float>(vertexColor.mGreen);
                output.RawColor.z = static_cast<float>(vertexColor.mBlue);
                output.RawColor.w = static_cast<float>(vertexColor.mAlpha);

                output.Color.r = static_cast<UInt8>(255.f * vertexColor.mRed);
                output.Color.g = static_cast<UInt8>(255.f * vertexColor.mGreen);
                output.Color.b = static_cast<UInt8>(255.f * vertexColor.mBlue);
                output.Color.a = static_cast<UInt8>(255.f * vertexColor.mAlpha);
                output.Channels |= ImportVertexChannel::COLOR;
            }
            else if (hasVertexColor)
            {
                output.Color.Set(255, 255, 255, 255);
                output.Channels |= ImportVertexChannel::COLOR;
            }
        }

        // Fill Normal, Tangent
        if (const FbxLayerElementNormal* layerElementNormal = layer.GetNormals())
        {
            FbxAMatrix totalNormalMatrix = totalMatrix.Inverse().Transpose();
            // Normals may have different reference and mapping mode than tangents and binormals
            FbxLayerElement::EMappingMode normalMapMode = layerElementNormal->GetMappingMode();
            FbxLayerElement::EReferenceMode normalRefMode = layerElementNormal->GetReferenceMode();
            SInt32 normalMapIndex = (normalMapMode == FbxLayerElement::eByControlPoint) ? vertexID : realIndex;
            SInt32 normalIndex = (normalRefMode == FbxLayerElement::eDirect) ? normalMapIndex : layerElementNormal->GetIndexArray().GetAt(normalMapIndex);

            FbxVector4 fbxNormal = layerElementNormal->GetDirectArray().GetAt(normalIndex);
            fbxNormal = totalNormalMatrix.MultT(fbxNormal);
            output.Normal = ConvertDir(fbxNormal).SafeNormal();
            output.Channels |= ImportVertexChannel::NORMAL;

            const FbxLayerElementTangent* layerElementTangent = layer.GetTangents();
            const FbxLayerElementBinormal* layerElementBinormal = layer.GetBinormals();
            if (layerElementTangent && layerElementBinormal)
            {
                FbxLayerElement::EMappingMode tangentMapMode = layerElementTangent->GetMappingMode();
                FbxLayerElement::EReferenceMode tangentRefMode = layerElementTangent->GetReferenceMode();
                SInt32 tangentMapIndex = (tangentMapMode == FbxLayerElement::eByControlPoint) ? vertexID : realIndex;
                SInt32 tangentIndex = (tangentRefMode == FbxLayerElement::eDirect) ? tangentMapIndex : layerElementTangent->GetIndexArray().GetAt(tangentMapIndex);

                FbxVector4 fbxTangent = layerElementTangent->GetDirectArray().GetAt(tangentIndex);
                fbxTangent = totalNormalMatrix.MultT(fbxTangent);
                Float3 tangent = ConvertDir(fbxTangent).SafeNormal();

                FbxLayerElement::EMappingMode binormalMapMode = layerElementBinormal->GetMappingMode();
                FbxLayerElement::EReferenceMode binormalRefMode = layerElementBinormal->GetReferenceMode();
                SInt32 binormalMapIndex = (binormalMapMode == FbxLayerElement::eByControlPoint) ? vertexID : realIndex;
                SInt32 binormalIndex = (binormalRefMode == FbxLayerElement::eDirect) ? binormalMapIndex : layerElementBinormal->GetIndexArray().GetAt(binormalMapIndex);

                FbxVector4 fbxBinormal = layerElementBinormal->GetDirectArray().GetAt(binormalIndex);
                fbxBinormal = totalNormalMatrix.MultT(fbxBinormal);
                Float3 binormal = ConvertDir(fbxBinormal).SafeNormal();

                Float3 computedBinormal = output.Normal.Cross(tangent);
                float sign = computedBinormal.Dot(binormal);

                output.Tangent = Float4(tangent.x, tangent.y, tangent.z, sign > 0 ? 1.f : -1.f);
                output.Channels |= ImportVertexChannel::TANGENT;
            }
        }
    }
    
    fbxsdk::FbxMesh* FBXImporter::PrepareFbxMesh(fbxsdk::FbxMesh* fbxMesh)
    {
        Assert(fbxMesh);
        // Must do this before triangulating the mesh due to an FBX bug in TriangulateMeshAdvance
        auto layerSmoothingCount = fbxMesh->GetLayerCount(FbxLayerElement::eSmoothing);
        for (auto i = 0; i < layerSmoothingCount; i++)
        {
            FbxLayerElementSmoothing const* SmoothingInfo = fbxMesh->GetLayer(0)->GetSmoothing();
            if (SmoothingInfo && SmoothingInfo->GetMappingMode() != FbxLayerElement::eByPolygon)
            {
                mGeometryConverter->ComputePolygonSmoothingFromEdgeSmoothing(fbxMesh, i);
            }
        }

        if (!fbxMesh->IsTriangleMesh())
        {
            FbxNodeAttribute* convertedNode = mGeometryConverter->Triangulate(fbxMesh, true);
            if (convertedNode != nullptr && convertedNode->GetAttributeType() == FbxNodeAttribute::eMesh)
            {
                fbxMesh = static_cast<fbxsdk::FbxMesh*>(convertedNode);
            }
            else
            {
                LOG_INFO("convert fbx mesh fail : {}", fbxMesh->GetNameWithNameSpacePrefix().Buffer());
            }
        }

        // Remove the bad polygons before getting any data from mesh
        fbxMesh->RemoveBadPolygons();

        return fbxMesh;
    }

    cross::TexturePtr FBXImporter::ImportTexture(const FbxSurfaceMaterial* fbxMaterial, const std::string& texProperty, const std::string& savePath, const std::string& assetPath, TextureCompression compression)
    {
        TextureImportSetting texSeting;
        texSeting.ImportSize = mImportSettings->MatTexImportSize;
        texSeting.Compression = compression;
        texSeting.ColorSpace = compression == TextureCompression::BC4 || compression == TextureCompression::BC5 || compression == TextureCompression::BC6H ? ImportColorSpace::Linear : ImportColorSpace::SRGB;
        texSeting.SaveRawData = false;

        TexturePtr texture{nullptr};
        if (FbxProperty fbxProperty = fbxMaterial->FindProperty(texProperty.c_str()); fbxProperty.IsValid())
        {
            UInt32 textureCount = static_cast<UInt32>(fbxProperty.GetSrcObjectCount<FbxTexture>());
            if (textureCount > 0)
            {
                for (auto textureIndex = 0u; textureIndex < textureCount; ++textureIndex)
                {
                    FbxFileTexture* fbxTexture = fbxProperty.GetSrcObject<FbxFileTexture>(textureIndex);
                    if (fbxTexture)
                    {
                        std::string texSavePath = savePath;
                        std::string texName = fbxTexture->GetRelativeFileName();
                        PathHelper::Normalize(texName);
                        texName = texName.substr(0, texName.rfind("."));
                        texSavePath.replace(texSavePath.find_last_of("/") + 1, texSavePath.find_last_of(".") - texSavePath.find_last_of("/") - 1, texName);
                        PathHelper::CollapseRelativeDirectories(texSavePath);
                        if (!PathHelper::IsFileExist(fbxTexture->GetFileName()))
                        {
                            std::filesystem::path dir(assetPath);
                            dir.remove_filename();
                            std::filesystem::path texPath(fbxTexture->GetFileName());
                            std::string fileName = texPath.filename().string();
                            std::filesystem::path newPath = dir / fileName;
                            fbxTexture->SetFileName(newPath.string().c_str());
                        }
                        texture = ImportTexture(fbxTexture, texSavePath, texSeting);
                    }
                    break;
                }
                if (!texture)
                {
                    LOG_WARN("{} {} Property Import Texture Error\n", fbxMaterial->GetName(), texProperty);
                }
            }
            else
            {
                LOG_WARN("{} {} Property Texture Count Is Zero\n", fbxMaterial->GetName(), texProperty);
            }
        }
        else
        {
            LOG_WARN("{} Not Have Property {}\n", fbxMaterial->GetName(), texProperty);
        }
        return texture;
    }

    cross::TexturePtr FBXImporter::ImportTexture(const FbxFileTexture* fbxTexture, const std::string& savePath, cross::editor::TextureImportSetting settings)
    {
        auto dir = PathHelper::GetDirectoryFromAbsolutePath(savePath);
        if (!PathHelper::IsDirectoryExist(dir))
        {
            PathHelper::CheckDirectory(dir);
        }

        auto textureImporter = TextureImporter();
        std::string texturePath = fbxTexture->GetFileName();
        if (HasExtension(texturePath.c_str(), ".rgba") || HasExtension(texturePath.c_str(), ".rgb"))
        {
            // FFS need change rgb/a image to png
            texturePath = texturePath.substr(0, texturePath.rfind(".")) + ".tiff";
        }
        const std::string& relativePath = PathHelper::GetRelativePath(savePath);
        if (PathHelper::IsFileExist(savePath))
        {
            EngineGlobal::GetFileSystem()->RemoveFile(relativePath);
        }

        textureImporter.ImportAsset(texturePath, savePath, &settings);

        TexturePtr texture{nullptr};
        if (textureImporter.GetImportResult().bSuccess && PathHelper::IsFileExist(savePath))
        {
            //const std::string& relativePath = PathHelper::GetRelativePath(savePath);
            texture = TypeCast<resource::Texture>(gAssetStreamingManager->LoadSynchronously(relativePath.c_str(), false));
        }
        return texture;
    }

    cross::TexturePtr FBXImporter::GenSolidTexture(const TextureInfo& info, const std::string& savePath, TextureCompression compression, UInt32 rgba, const std::string namePrefix, bool createNew)
    {
        std::stringstream ss;
        ss << rgba;
        ss << (UInt8)info.Format;
        ss << (UInt8)info.ColorSpace;
        ss << (UInt8)info.Width;
        ss << (UInt8)info.Height;
        ss << (UInt8)info.MipCount;
        ss << (UInt8)info.EnableVirtualTextureStreaming;
        auto textHash = std::hash<std::string>{}(ss.str());

        std::string texname = "Default_" + namePrefix + "(" + std::to_string(textHash) + ")";
        std::string texSavePath = PathHelper::Combine(savePath.c_str(), texname.c_str(), ".nda");
        if (createNew || !PathHelper::IsFileExist(texSavePath))
        {
            cmft::Image src;
            cmft::imageCreate(src, info.Width, info.Height, rgba, (UInt8)info.MipCount, (UInt8)info.ArraySize);

            TextureImportSetting setting;
            setting.Compression = compression;
            setting.ColorSpace = static_cast<ImportColorSpace>(info.ColorSpace);
            setting.SaveRawData = false;

            CrossSchema::TextureAssetT texture;

            LoadImageBC(src, texture, setting);

            texture.colorspace = (setting.ColorSpace == ImportColorSpace::Linear) ? CrossSchema::ColorSpace::Linear : CrossSchema::ColorSpace::SRGB;
            texture.vtstreaming = false;
            texture.texturegroup = static_cast<CrossSchema::TextureGroup>(setting.ImportTextureGroup);
            texture.vtstreaming = info.EnableVirtualTextureStreaming;

            Texture2DPtr tex2d;
            tex2d = gResourceMgr.CreateResourceAs<resource::Texture2D>();
            tex2d->CreateAsset(texSavePath);
            tex2d->Serialize(texture);
        }
        return TypeCast<resource::Texture>(gAssetStreamingManager->GetResource(texSavePath.c_str()));
    }

    /*
        We need two steps to import skeleton data. In first go, we use DFS to iterate the fbx scene, and extract all nodes whose
        eType is eSkeleton and store their parent-child relationship. In second go, we iterate our cluster array to extract the bindpose
        of bones. We cannot simply do these two steps in one go because there exists some bones without a bindpose.
    */
    bool FBXImporter::ImportSkeleton(const std::string meshName, SkeletonDesc& outSkeleton)
    {
        outSkeleton.Name = meshName + "_SK";

        ImportSkeletonHierarchy(outSkeleton, mSkeletonArray);

        std::vector<FbxCluster*> clusterArray;
        ImportSkeletonCluster(clusterArray);

        ImportSkeletonBindPose(mSkeletonArray, clusterArray, outSkeleton);

        return !outSkeleton.Bones.empty();
    }

    void FBXImporter::ImportSkeletonCluster(std::vector<FbxCluster*>& output)
    {
        fbxsdk::FbxNode* rootNode = mScene->GetRootNode();
        std::stack<fbxsdk::FbxNode*> fbxpNodeStack;
        fbxpNodeStack.push(rootNode);
        while (!fbxpNodeStack.empty())
        {
            auto pNode = fbxpNodeStack.top();
            fbxpNodeStack.pop();

            for (int i = 0; i < pNode->GetChildCount(); i++)
            {
                fbxpNodeStack.push(pNode->GetChild(i));
            }

            if (pNode->GetNodeAttribute() != nullptr && pNode->GetNodeAttribute()->GetAttributeType() == fbxsdk::FbxNodeAttribute::EType::eMesh)
            {
                FbxMesh* pFbxMesh = pNode->GetMesh();

                if (pFbxMesh == nullptr)
                    continue;

                if (FbxSkin* pFbxSkin = reinterpret_cast<FbxSkin*>(pFbxMesh->GetDeformer(0, fbxsdk::FbxDeformer::eSkin)))
                {
                    const auto nCluster = pFbxSkin->GetClusterCount();

                    for (int i = 0; i < nCluster; i++)
                    {
                        FbxCluster* pFbxCluster = pFbxSkin->GetCluster(i);
                        if (pFbxCluster == nullptr)
                            throw std::runtime_error("null cluster find\n");
                        output.emplace_back(pFbxCluster);
                    }
                }
            }
        }

        for (int i = 0; i < output.size(); i++)
        {
            auto& pFbxCluster = output[i];
            auto pBoneNode = pFbxCluster->GetLink();

            if (mBoneNodeToIDMap.find(pBoneNode) == mBoneNodeToIDMap.end())
            {
                auto pair = std::find_if(mBoneNodeToIDMap.begin(), mBoneNodeToIDMap.end(), [=](std::pair<fbxsdk::FbxNode*, int> const& elem) {
                    auto pCursorNode = elem.first;
                    auto pCheckerNode = pBoneNode;

                    while (strcmp(pCursorNode->GetName(), pCheckerNode->GetName()) == 0)
                    {
                        pCursorNode = pCursorNode->GetParent();
                        pCheckerNode = pCheckerNode->GetParent();

                        if (pCursorNode == pCheckerNode && pCursorNode == nullptr)
                            return true;
                    }

                    return false;
                });

                if (pair != mBoneNodeToIDMap.end())
                    mBoneNodeToIDMap.emplace(pBoneNode, pair->second);
            }
        }
    }

    void FBXImporter::ImportSkeletonHierarchy(SkeletonDesc& outSkeleton, std::vector<FbxNode*>& outSkeletonArray)
    {
        fbxsdk::FbxNode* pFbxSceneRootNode = mScene->GetRootNode();

        // depth first search using a stack
        std::queue<fbxsdk::FbxNode*> fbxNodeQueue;
        fbxNodeQueue.push(pFbxSceneRootNode);

        fbxsdk::FbxAnimStack* curAnimStack = mScene->GetSrcObject<fbxsdk::FbxAnimStack>(0);
        FbxAnimLayer* curAnimLayer = curAnimStack != nullptr ? curAnimStack->GetMember<FbxAnimLayer>(0) : nullptr;

        int SKRootNodeCount = 0;

        while (!fbxNodeQueue.empty())
        {
            auto pNode = fbxNodeQueue.front();
            fbxNodeQueue.pop();

            bool isContainSkeleton = IsChildrenContain(fbxsdk::FbxNodeAttribute::EType::eSkeleton, pNode);

            bool isAnimated = IsNodeAnimated(pNode, curAnimLayer);

            bool isStaticMesh = pNode->GetNodeAttribute() != nullptr && pNode->GetNodeAttribute()->GetAttributeType() == fbxsdk::FbxNodeAttribute::EType::eMesh && pNode->GetMesh()->GetDeformerCount(FbxDeformer::eSkin) == 0;

            bool isCamera = pNode->GetNodeAttribute() != nullptr && pNode->GetNodeAttribute()->GetAttributeType() == fbxsdk::FbxNodeAttribute::EType::eCamera;

            bool isExistSkeleton = outSkeleton.Bones.size() > 0;

            // current node's attribute is skeleton node, record it as part of skeleton
            bool isSkeletonNode = pNode->GetNodeAttribute() != nullptr && pNode->GetNodeAttribute()->GetAttributeType() == fbxsdk::FbxNodeAttribute::EType::eSkeleton;

            // current node contains child which is skeleton node, while current node's parent exist skeleton node.
            // we mark this gap node as part of skeleton
            bool isSkGapNode = !isSkeletonNode && !isStaticMesh && isContainSkeleton && isExistSkeleton;

            // current node is static mesh & animation curve grabbed successfully,
            // mark this mesh node as part of skeleton while the node is immutable leaf in skeleton hierarchy
            bool isFrameAnimNode = isAnimated && !isContainSkeleton;

            if (isSkeletonNode || isSkGapNode || isFrameAnimNode || isCamera)
            {
                SkeletonDesc::Bone curBone;
                curBone.BoneName = pNode->GetNameWithoutNameSpacePrefix();

                // try to find the missed eNull type nodes from the first SkeletonPartBone to the Root node
                if (mBoneNodeToIDMap.find(pNode->GetParent()) == mBoneNodeToIDMap.end() && pNode->GetParent() != pFbxSceneRootNode)
                {
                    static std::vector<fbxsdk::FbxNode*> childToRootSkArray;
                    childToRootSkArray.clear();

                    // find missedNodes from bottom to top
                    auto missedNode = pNode->GetParent();
                    while (missedNode != nullptr && missedNode->GetParent() != pFbxSceneRootNode && mBoneNodeToIDMap.find(missedNode) == mBoneNodeToIDMap.end())
                    {
                        childToRootSkArray.emplace_back(missedNode);
                        missedNode = missedNode->GetParent();
                    }

                    // insert all missedNodes from top to left using a reverse_iterator
                    for (auto riter = childToRootSkArray.rbegin(); riter != childToRootSkArray.rend(); riter++)
                    {
                        auto node = *riter;
                        SkeletonDesc::Bone missedBone;
                        missedBone.BoneName = node->GetNameWithoutNameSpacePrefix();
                        missedBone.BoneID = static_cast<UInt32>(outSkeleton.Bones.size());

                        if (mBoneNodeToIDMap.find(node->GetParent()) == mBoneNodeToIDMap.end())
                        {
                            missedBone.ParentID = SK_BONE_INDEX_NONE;
                            SKRootNodeCount++;
                            Assert(SKRootNodeCount <= 1 && "More than one root node.");
                        }
                        else
                        {
                            missedBone.ParentID = mBoneNodeToIDMap.at(node->GetParent());
                        }

                        mBoneNodeToIDMap.insert(std::pair(node, missedBone.BoneID));
                        outSkeletonArray.emplace_back(node);
                        outSkeleton.Bones.emplace_back(missedBone);
                    }
                }

                // set current bone's parent id
                if (mBoneNodeToIDMap.find(pNode->GetParent()) == mBoneNodeToIDMap.end())
                {
                    curBone.ParentID = SK_BONE_INDEX_NONE;
                    SKRootNodeCount++;
                    Assert(SKRootNodeCount <= 1 && "More than one root node.");
                }
                else
                {
                    curBone.ParentID = mBoneNodeToIDMap.at(pNode->GetParent());
                }

                curBone.BoneID = static_cast<UInt32>(outSkeleton.Bones.size());
                mBoneNodeToIDMap.insert(std::pair(pNode, curBone.BoneID));
                outSkeletonArray.emplace_back(pNode);

                outSkeleton.Bones.emplace_back(curBone);
            }

            // Record fbx node hierarchy by level order traversal, which memory arrangement's
            // is more efficient in calculate bone transform without Recursion
            for (int i = 0; i < pNode->GetChildCount(); i++)
            {
                fbxNodeQueue.push(pNode->GetChild(i));
            }
        }

        if (mImportSettings->NormalizeBoneName)
        {
            std::vector<SInt32> LevelNodeCountArray;
            LevelNodeCountArray.resize(outSkeleton.Bones.size(), 0);

            for (SkeletonDesc::Bone& bone : outSkeleton.Bones)
            {
                if (bone.ParentID == SK_BONE_INDEX_NONE)
                {
                    bone.HierarchyDepth = 0;
                    bone.BoneName = "#0_#0";
                }
                else
                {
                    SkeletonDesc::Bone& ParentBone = outSkeleton.Bones[bone.ParentID];
                    SInt32 parentDepth = ParentBone.HierarchyDepth;
                    Assert(parentDepth != -1);
                    bone.HierarchyDepth = parentDepth + 1;
                    bone.BoneName = "#" + std::to_string(bone.HierarchyDepth) + "_#" + std::to_string(LevelNodeCountArray[bone.HierarchyDepth]);
                    LevelNodeCountArray[bone.HierarchyDepth] += 1;
                }
            }
        }
    }

    /*
    Here is the formula of a mesh point's final position in engine during an animation.

    Define:
    Pt_Init. This control point position, which is the position of the original control point in the FbxMesh.
    G = Geometry offset transform of the FbxMesh node.
    M = fbxCluster->GetTransformMatrix(). This is the offset of the FbxMesh to the FbxScene origin.
    L = fbxCluster->GetTransformLinkMatrix(). This is the Cluster's initial world transform.
    A = fbxCluster->GetLink()->EvaluateGlobalTransform(time). This is the bone world transform at current time in the Animation.
    Pt_Final. This is the final vertex position in engine world.

    Then:
    Pt_Init * G * M * L^{-1} * A = Pt_Final

    In the import data, Pt_Init * G * M is baked into the vertex position of the model( TotalMatrix * ControlPointPos, TotalMatrix = GlobalTransform * GeometryOffset )
    L^{-1} is represented as bindpose_inverse.
    A is the animation data.


    Go a step further:

        Considering Face Offset, we want mesh vertex to be transformed by RotationOffsetMatrix whether it is skeletal mesh or not,
        So for static mesh, its imported vertex is VertexPos' = VertexPos * RotationOffsetMatrix = (Pt_Init * G * M) * RotationOffsetMatrix.

        And for skeletal Mesh, we want final vertex pos also be transformed by RotationOffsetMatrix after animation,
        So Pt_Final' = Pt_Final * RotationOffsetMatrix.

        Then for formula: VertexPos' * BPInv' * A' = Pt_Final',

        We have, (Pt_Init * G * M) * L^{-1} * A * RotationOffsetMatrix = (Pt_Init * G * M) * RotationOffsetMatrix * BPInv' * A'
        ==>
        RotationOffsetMatrix * BPInv' = L^{-1},  BPInv' = RotationOffsetMatrix^{-1} * L^{-1};
        A' (actually bone's world transform) = A * RotationOffsetMatrix

    */
    void FBXImporter::ImportSkeletonBindPose(const std::vector<FbxNode*>& skeletonArray, const std::vector<FbxCluster*>& clusterArray, SkeletonDesc& outSkeleton)
    {
        const bool needAdjustByFaceOffset = mImportSettings->FaceOffset != 0.f;
        Float4x4 faceOffset = Float4x4::CreateRotationY(mImportSettings->FaceOffset / 180.0f * MathUtils::MathPi);
        Float4x4 faceOffsetInv = faceOffset.Inverted();

        // Extract skeleton local 2 world matrix from fbx nodes
        for (int i = 0; i < skeletonArray.size(); ++i)
        {
            auto pSkNode = skeletonArray[i];

            int curBoneID = mBoneNodeToIDMap.at(pSkNode);
            auto& bone = outSkeleton.Bones[curBoneID];
            // hack by mindalv, need optimization
            const FbxAMatrix& boneGlobalMatrix = pSkNode->EvaluateGlobalTransform(0);
            bone.RefPoseWorld = FBXMatrixToFloat4x4(boneGlobalMatrix);
            // Adjust by face offset
            if (needAdjustByFaceOffset)
            {
                bone.RefPoseWorld = bone.RefPoseWorld * faceOffset;
            }
        }

        for (int i = 0; i < clusterArray.size(); i++)
        {
            auto& pFbxCluster = clusterArray[i];

            FbxAMatrix linkTransformation;
            pFbxCluster->GetTransformLinkMatrix(linkTransformation);

            FbxAMatrix bindPoseInv = linkTransformation.Inverse();
            std::string curBoneName = static_cast<const char*>(pFbxCluster->GetLink()->GetNameWithoutNameSpacePrefix());
            if (mBoneNodeToIDMap.size() > 0 && curBoneName != "")
            {
                auto linkNode = pFbxCluster->GetLink();
                int curBoneID = mBoneNodeToIDMap.at(linkNode);
                auto& bone = outSkeleton.Bones[curBoneID];

                bone.BindPoseInv = FBXMatrixToFloat4x4(bindPoseInv);
                bone.RefPoseBind = FBXMatrixToFloat4x4(linkTransformation);

                // Adjust by face offset
                if (needAdjustByFaceOffset)
                {
                    bone.BindPoseInv = faceOffsetInv * bone.BindPoseInv;
                    bone.RefPoseBind = bone.RefPoseBind * faceOffset;
                }
            }
            else
            {
                LOG_EDITOR_WARNING("Bone is empty.");
            }
        }
    }

    void FBXImporter::ImportSkinData(FbxMesh* pFbxMesh, MeshDescription& meshDescription)
    {
        FbxSkin* skin = nullptr;
        UInt32 clusterCount = 0;
        if (pFbxMesh)
        {
            if (auto skinDeformerCount = pFbxMesh->GetDeformerCount(FbxDeformer::eSkin))
            {
                skin = static_cast<FbxSkin*>(pFbxMesh->GetDeformer(0, FbxDeformer::eSkin));
                clusterCount = skin->GetClusterCount();
            }
        }

        // Current mesh has no skin data, return
        if (clusterCount < 1)
        {
            return;
        }

        // Ignore the skin if the only bone links to the node itself
        if (clusterCount == 1)
        {
            auto it = mFbxMeshSharedInfoMap.find(pFbxMesh);
            if (it != mFbxMeshSharedInfoMap.end())
            {
                const SharedFbxMeshInfo* sharedFbxMesh = it->second.get();
                FbxCluster* cluster = skin->GetCluster(0);
                if (sharedFbxMesh->FbxNodes.size() == 1 && cluster != nullptr && cluster->GetLink() == sharedFbxMesh->FbxNodes[0])
                {
                    skin = nullptr;
                }
            }
            else
            {
                Assert(false);
            }
        }

        if (skin != nullptr)
        {
            meshDescription.ReserveSkinVertices();
            SkinDescription& skinDescription = meshDescription.GetSkinDescriptionRef();

            std::vector<SkinBuildVertex>& influences = skinDescription.GetSkinVerticesRef();
            if (influences.size() < 1)
            {
                return;
            }
            std::set<FbxNode*> duplicates;

            SkinDescription::BoneID currentBoneID = 0;
            for (auto clusterIdx = 0u; clusterIdx < clusterCount; ++clusterIdx)
            {
                FbxCluster* cluster = skin->GetCluster(clusterIdx);
                if (!cluster || mClusterLinks.find(cluster->GetLink()) == mClusterLinks.end())
                {
                    continue;
                }

                bool isDuplicate = (duplicates.count(cluster->GetLink()) != 0);
                FbxString boneName = cluster->GetLink()->GetNameWithoutNameSpacePrefix();

                auto clusterLink = cluster->GetLink();
                int boneIndex = SK_BONE_INDEX_NONE;
                auto iterNode = mBoneNodeToIDMap.find(clusterLink);
                if (mBoneNodeToIDMap.end() != iterNode)
                {
                    boneIndex = iterNode->second;
                }

                duplicates.insert(cluster->GetLink());

                // Weights and indices
                const double* weights = cluster->GetControlPointWeights();
                const int* indices = cluster->GetControlPointIndices();
                UInt32 controlPointCnt = cluster->GetControlPointIndicesCount();
                bool importWeights = true;

                if (isDuplicate || !weights || controlPointCnt <= 0)
                {
                    ReportWarning("Skipping bone because it is duplicate %s\n", boneName.Buffer());
                    importWeights = false;
                }

                // TODO(maxwan): we dont support eAdditive ( the mode used for deformers)
                FbxCluster::ELinkMode linkMode = cluster->GetLinkMode();
                if (linkMode == FbxCluster::eAdditive)
                {
                    ReportWarning("Skipping influences for '%s' because it is marked as ADDITIVE. This most likely means that "
                                  "you are using a blend shape or custom deformer, which is not supported.\n",
                                  boneName.Buffer());
                    importWeights = false;
                }

                if (importWeights)
                {
                    auto FetchWeight = [weights, indices, controlPointCnt](const UInt32 vertexID, float& output) -> bool {
                        for (auto idx = 0u; idx < controlPointCnt; ++idx)
                        {
                            if (vertexID == static_cast<UInt32>(indices[idx]))
                            {
                                output = static_cast<float>(weights[idx]);
                                return true;
                            }
                        }
                        return false;
                    };

                    // Go through all control points
                    // Find and replace the smallest weight with the current weight!
                    for (auto& skinVertex : influences)
                    {
                        float weight = 0.0f;
                        if (FetchWeight(skinVertex.ControlPointID, weight))
                        {
                            for (int j = 0; j < 4; j++)
                            {
                                if (weight >= skinVertex.Weights[j])
                                {
                                    // Push back old weights!
                                    for (int k = 2; k >= j; k--)
                                    {
                                        skinVertex.Weights[k + 1] = skinVertex.Weights[k];
                                        skinVertex.BoneIDs[k + 1] = skinVertex.BoneIDs[k];
                                    }
                                    // Replace the weights!
                                    skinVertex.Weights[j] = weight;
                                    skinVertex.BoneIDs[j] = boneIndex;
                                    break;
                                }
                            }
                        }
                    }
                }

                currentBoneID++;
            }

            std::vector<int> invalidWeights, invalidBones;
            invalidWeights.reserve(influences.size());
            invalidBones.reserve(influences.size());

            // TODO(maxwan): NORMALIZE WEIGHTS IF WE SHOULD!
            // TODO(maxwan): we dont support eTOTAL1 - Is this true? It seems that it would already be normalized.
            // Normalize the skin weights!
            for (UInt32 i = 0; i < influences.size(); i++)
            {
                float weightsum = 0.0F;

                for (int j = 0; j < 4; j++)
                {
                    weightsum += influences[i].Weights[j];
                }

                const float kWeigthEpsilon = 0.00000001F;
                if (weightsum < kWeigthEpsilon)
                {
                    for (int j = 0; j < 4; j++)
                    {
                        if (influences[i].BoneIDs[j] != SK_BONE_INDEX_NONE)
                        {
                            influences[i].Weights[j] = 1.0F;
                        }
                    }

                    weightsum = 0.0F;
                    for (int j = 0; j < 4; j++)
                    {
                        weightsum += influences[i].Weights[j];
                    }

                    if (weightsum < kWeigthEpsilon)
                    {
                        invalidBones.push_back(i);

                        influences[i].Weights[0] = weightsum = 1;
                        influences[i].BoneIDs[0] = 0;
                    }
                    else
                    {
                        invalidWeights.push_back(i);
                    }
                }

                weightsum = 1.0F / weightsum;
                for (int j = 0; j < 4; j++)
                {
                    influences[i].Weights[j] *= weightsum;

                    if (influences[i].BoneIDs[j] == SK_BONE_INDEX_NONE)
                    {
                        influences[i].BoneIDs[j] = 0;
                    }
                }
            }

            FBXHelper::ValidateSkin(influences, clusterCount, pFbxMesh->GetNameWithoutNameSpacePrefix());
        }
    }

    bool FBXImporter::ValidateAnimStack(FbxAnimStack* curAnimStack, FbxTimeSpan& animTimeSpan)
    {
        // Set current anim stack
        mScene->SetCurrentAnimationStack(curAnimStack);

        bool bValidAnimStack = true;

        // If no duration is found, return false
        if (animTimeSpan.GetDuration() <= 0)
        {
            return false;
        }

        // Add blend shape time if import blend shape
        if (mImportSettings->ImportBlendShapes)
        {
            for (FbxMesh* fbxMesh : mUniqueLOD0FbxMeshes)
            {
                // Consider blend shape animation curve
                if (fbxMesh->GetDeformerCount(FbxDeformer::eBlendShape) > 0)
                {
                    FbxBlendShape* curFbxBlendShape = static_cast<FbxBlendShape*>(fbxMesh->GetDeformer(0, FbxDeformer::eBlendShape));
                    if (curFbxBlendShape)
                    {
                        const UInt32 blendShapeChannelCount = curFbxBlendShape->GetBlendShapeChannelCount();

                        for (UInt32 channelIdx = 0; channelIdx < blendShapeChannelCount; ++channelIdx)
                        {
                            FbxBlendShapeChannel* curFbxBlendShapeChannel = curFbxBlendShape->GetBlendShapeChannel(channelIdx);
                            if (curFbxBlendShapeChannel)
                            {
                                // Get the percentage of influence of the shape.
                                FbxAnimLayer* animLayer = curAnimStack->GetSrcObject<fbxsdk::FbxAnimLayer>(0);
                                FbxAnimCurve* animCurve = fbxMesh->GetShapeChannel(0, channelIdx, animLayer);
                                if (animCurve && animCurve->KeyGetCount() > 0)
                                {
                                    FbxTimeSpan shapeAnimCurveSpan;

                                    if (animCurve->GetTimeInterval(shapeAnimCurveSpan))
                                    {
                                        bValidAnimStack = true;
                                        // Update animation interval to include blend shape range
                                        animTimeSpan.UnionAssignment(shapeAnimCurveSpan);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        return bValidAnimStack;
    }

    bool FBXImporter::ImportAnimation(FbxAnimStack* animStack, const FbxTimeSpan& animTimeSpan, const SkeletonDesc& skeleton, AnimationDesc& outAnimation)
    {
        // Set animation name
        outAnimation.Name = animStack->GetNameWithoutNameSpacePrefix();
        SInt32 startFrame = 0;

        if (mImportSettings->AnimFramgeRange == StartToEnd)
        {
            // Set animation duration
            outAnimation.Duration = static_cast<float>(animTimeSpan.GetDuration().GetSecondDouble());
            startFrame = static_cast<int>(std::ceil(static_cast<float>(animTimeSpan.GetStart().GetSecondDouble()) * DEFAULT_SAMPLERATE));
        }
        else // mImportSettings->AnimFramgeRange != StartToEnd
        {
            // Set animation duration
            outAnimation.Duration = static_cast<float>(animTimeSpan.GetStop().GetSecondDouble());
            startFrame = 0;
        }

        // Import blend shape curves
        if (mImportSettings->ImportBlendShapes)
        {
            ImportBlendShapeCurves(animStack, animTimeSpan, outAnimation);
        }

        // At least one frame when anim duration > 0.0
        const SInt32 frameCount = static_cast<int>(std::ceil(outAnimation.Duration * DEFAULT_SAMPLERATE));

        if (frameCount <= 0)
        {
            return false;
        }

        Assert("Start frame is not 0!" && startFrame >= 0);

        // Import bone tracks
        ImportBoneTracks(frameCount, startFrame, skeleton, outAnimation.TracksForAllBones);

        return true;
    }

    void FBXImporter::ImportBlendShapeCurves(FbxAnimStack* animStack, const FbxTimeSpan& animTimeSpan, AnimationDesc& outAnimation)
    {
        for (FbxMesh* fbxMesh : mUniqueLOD0FbxMeshes)
        {
            if (fbxMesh->GetDeformerCount(FbxDeformer::eBlendShape) > 0)
            {
                FbxBlendShape* curFbxBlendShape = static_cast<FbxBlendShape*>(fbxMesh->GetDeformer(0, FbxDeformer::eBlendShape));
                if (curFbxBlendShape)
                {
                    const UInt32 blendShapeChannelCount = curFbxBlendShape->GetBlendShapeChannelCount();
                    std::string blendShapeName = static_cast<const char*>(curFbxBlendShape->GetNameWithoutNameSpacePrefix());

                    const bool bMightBeBadMAXFile = (blendShapeName == "Morpher");

                    for (UInt32 channelIdx = 0; channelIdx < blendShapeChannelCount; ++channelIdx)
                    {
                        FbxBlendShapeChannel* curFbxBlendShapeChannel = curFbxBlendShape->GetBlendShapeChannel(channelIdx);
                        if (curFbxBlendShapeChannel)
                        {
                            std::string channelName = static_cast<const char*>(curFbxBlendShapeChannel->GetNameWithoutNameSpacePrefix());
                            const UInt32 targetShapeCount = curFbxBlendShapeChannel->GetTargetShapeCount();

                            // Maya adds the name of the blend shape and an underscore or point to the front of the channel name, so remove it.
                            // Also avoid ending up with a empty name, we prefer having the BlendShapeName instead of nothing.
                            if (StartWith(channelName, blendShapeName) && channelName.length() > blendShapeName.length())
                            {
                                channelName = channelName.substr(blendShapeName.length() + 1);
                            }

                            if (bMightBeBadMAXFile)
                            {
                                FbxShape* targetShape = targetShapeCount > 0 ? curFbxBlendShapeChannel->GetTargetShape(0) : nullptr;
                                if (targetShape)
                                {
                                    std::string targetShapeName = static_cast<const char*>(targetShape->GetNameWithoutNameSpacePrefix());
                                    channelName = targetShapeName.empty() ? channelName : targetShapeName;
                                }
                            }

                            FbxAnimLayer* animLayer = animStack->GetSrcObject<fbxsdk::FbxAnimLayer>(0);
                            FbxAnimCurve* animCurve = fbxMesh->GetShapeChannel(0, channelIdx, animLayer);
                            if (ShouldImportCurve(animCurve))
                            {
                                ImportCurveToAnimSequence(animCurve, channelName, animTimeSpan, outAnimation.CurveList);
                            }
                        }
                    }
                }
            }
        }
    }

    void FBXImporter::ImportCurveToAnimSequence(FbxAnimCurve* fbxAnimCurve, const std::string& curveName, const FbxTimeSpan& animTimeSpan, FloatCurveList& CurveList, CurveUseType UseType, bool removeDuplicateKey, bool keepStartTime)
    {
        // We import channel weights from [0.0f, 100.0f] to [0.0f, 1.0f], So here we need scale it
        const static float sValueScaleFactor = 0.01f;

        if (fbxAnimCurve)
        {
            FloatCurveTrack tmpCurve{};
            FloatCurveTrack* curve = &tmpCurve;
            bool hasTrack = CurveList.HasTrack(curveName.c_str());
            if (hasTrack)
                curve = &CurveList.FindTrackByName(curveName.c_str());
            
            curve->Name = curveName.c_str();
            curve->EnterType = CurveRepeatType::CRT_CONSTANT;
            curve->LeaveType = CurveRepeatType::CRT_CONSTANT;
            curve->UseType = UseType;
            curve->Keys.resize(0);

            if (ImportCurve(fbxAnimCurve, animTimeSpan, *curve, sValueScaleFactor, false, removeDuplicateKey, keepStartTime))
            {
                if (!hasTrack)
                    CurveList.mTracks.emplace_back(std::move(*curve));
            }
        }
    }

    void FBXImporter::ImportBoneTracks(SInt32 frameCount, SInt32 startFrame, const SkeletonDesc& skeleton, std::vector<AnimationDesc::Track>& outBoneTracks)
    {
        const auto boneCount = skeleton.Bones.size();

        // Set animation sample result
        std::vector<std::vector<Float4x4>> AllBonesGlobalTransformations;
        AllBonesGlobalTransformations.resize(boneCount);
        // Set bone tracks count
        outBoneTracks.resize(boneCount);

        FbxTime timeStep = 0;
        timeStep.SetSecondDouble(1.0 / static_cast<double>(DEFAULT_SAMPLERATE));

        const bool needAdjustByFaceOffset = mImportSettings->FaceOffset != 0.f;
        Float4x4 faceOffset = Float4x4::CreateRotationY(mImportSettings->FaceOffset / 180.0f * MathUtils::MathPi);

        // Scene 1:
        // If skeleton exists bones not include in any skin_node, the bone nodes attribute "maybe" not be EType::eSkeleton
        // Ex. Bone0 - Bone1 - StaticMesh, if Bone0 not include by any skin_node in fbx file while bone0 ctrl StaticMesh's transform
        // We would still make the Bone0 as part of skeleton, but the matrix should calculated by cluster need grab manually by below code
        // Scene 2:
        // If only skeleton node exists in fbx file, we need calculated bone world matrix manually instead of grabbed by cluster
        static Float4x4 curBoneTransform;
        static std::vector<Float4x4> curBoneGlobalTransformations;
        for (auto& pFbxBone : mSkeletonArray)
        {
            auto boneId = mBoneNodeToIDMap.at(pFbxBone);
            if (boneId < 0)
            {
                continue;
            }

            curBoneGlobalTransformations.resize(frameCount);

            FbxTime timeStamp = 0;
            timeStamp.SetSecondDouble(startFrame * 1.0 / static_cast<double>(DEFAULT_SAMPLERATE));

            for (int frameIdx = 0; frameIdx < frameCount; ++frameIdx)
            {
                auto& boneCononicalTransform = pFbxBone->EvaluateGlobalTransform(timeStamp);
                curBoneTransform = FBXMatrixToFloat4x4(boneCononicalTransform);

                // Adjust by face offset
                if (needAdjustByFaceOffset)
                {
                    curBoneTransform = curBoneTransform * faceOffset;
                }

                curBoneGlobalTransformations[frameIdx] = curBoneTransform;

                timeStamp += timeStep;
            }

            AllBonesGlobalTransformations[boneId] = std::move(curBoneGlobalTransformations);
        }

        ConvertBonesGlobalTransformToTracks(AllBonesGlobalTransformations, skeleton, outBoneTracks);
    }

    void FBXImporter::ConvertBonesGlobalTransformToTracks(const std::vector<std::vector<Float4x4>>& allBonesGlobalTransformations, const SkeletonDesc& skeleton, std::vector<AnimationDesc::Track>& outBoneTracks)
    {
        const auto boneCount = allBonesGlobalTransformations.size();
        Assert(boneCount > 0);
        outBoneTracks.resize(boneCount);

        const auto frameCount = allBonesGlobalTransformations[0].size();
        Assert(frameCount > 0);

        std::vector<Float4x4> curBoneLocalTransformations;
        curBoneLocalTransformations.resize(frameCount);

        for (int boneIdx = 0; boneIdx < boneCount; ++boneIdx)
        {
            const auto& curBoneGlobalTransformations = allBonesGlobalTransformations[boneIdx];

            for (int frameIdx = 0; frameIdx < frameCount; ++frameIdx)
            {
                UInt32 curBoneParentID = skeleton.Bones[boneIdx].ParentID;
                // Root bone
                if (curBoneParentID == SK_BONE_INDEX_NONE)
                {
                    curBoneLocalTransformations[frameIdx] = curBoneGlobalTransformations[frameIdx];
                }
                else
                {
                    curBoneLocalTransformations[frameIdx] = curBoneGlobalTransformations[frameIdx] * allBonesGlobalTransformations[curBoneParentID][frameIdx].Inverted();
                }
            }

            SplitBoneLocalTransformationsToTrack(curBoneLocalTransformations, outBoneTracks[boneIdx]);
        }
    }

    void FBXImporter::SplitBoneLocalTransformationsToTrack(const std::vector<Float4x4>& curBoneLocalTransformations, AnimationDesc::Track& outBoneTrack)
    {
        const auto frameCount = curBoneLocalTransformations.size();
        const float timeStep = 1.0f / static_cast<float>(DEFAULT_SAMPLERATE);

        outBoneTrack.TranslationKeys.resize(frameCount);
        outBoneTrack.RotationKeys.resize(frameCount);
        outBoneTrack.ScaleKeys.resize(frameCount);

        float timeStamp = 0.0f;
        for (int frameIdx = 0; frameIdx < frameCount; ++frameIdx)
        {
            outBoneTrack.TranslationKeys[frameIdx].Time = timeStamp;
            outBoneTrack.RotationKeys[frameIdx].Time = timeStamp;
            outBoneTrack.ScaleKeys[frameIdx].Time = timeStamp;

            curBoneLocalTransformations[frameIdx].Decompose(outBoneTrack.ScaleKeys[frameIdx].Scale, outBoneTrack.RotationKeys[frameIdx].Rotation, outBoneTrack.TranslationKeys[frameIdx].Translation);

            timeStamp += timeStep;
        }
    }

    SerializeNode FBXImporter::LoadJsonFile(std::string filename, std::shared_ptr<TLSFDocument> doc)
    {
        std::string fileContent;
        filesystem::IFilePtr file = EngineGlobal::GetFileSystem()->Open(filename);
        size_t fileSize = file->GetSize();
        fileContent.resize(fileSize);
        size_t readFileSize = file->Read(fileContent.data(), fileSize);
        Assert(fileSize == readFileSize);
        return SerializeNode::ParseFromJson(fileContent);
    }

    FbxAMatrix FBXImporter::GetGeometryOffset(const FbxNode* pNode)
    {
        FbxVector4 translation = pNode->GetGeometricTranslation(FbxNode::eSourcePivot);
        FbxVector4 rotation = pNode->GetGeometricRotation(FbxNode::eSourcePivot);
        FbxVector4 scale = pNode->GetGeometricScaling(FbxNode::eSourcePivot);

        return FbxAMatrix(translation, rotation, scale);
    }

    bool FBXImporter::IsChildrenContain(fbxsdk::FbxNodeAttribute::EType searchType, fbxsdk::FbxNode* pRoot)
    {
        if (pRoot == nullptr)
            return false;

        if (pRoot->GetNodeAttribute() != nullptr && pRoot->GetNodeAttribute()->GetAttributeType() == searchType)
            return true;

        bool ret = false;
        for (int i = 0; i < pRoot->GetChildCount(); i++)
        {
            auto child = pRoot->GetChild(i);
            ret = ret || IsChildrenContain(searchType, child);

            if (ret)
                break;
        }

        return ret;
    }

    bool FBXImporter::IsNodeAnimated(const FbxNode* pNode, const FbxAnimLayer* pAnimLayer)
    {
        if (pAnimLayer == nullptr)
            return false;

        // Verify that the node is animated.
        bool bIsAnimated = false;
        FbxTimeSpan animTimeSpan(FBXSDK_TIME_INFINITE, FBXSDK_TIME_MINUS_INFINITE);

        // Translation animation
        FbxProperty transProp = pNode->LclTranslation;
        for (int i = 0; i < transProp.GetSrcObjectCount<FbxAnimCurveNode>(); i++)
        {
            FbxAnimCurveNode* curveNode = transProp.GetSrcObject<FbxAnimCurveNode>(i);
            if (curveNode && pAnimLayer->IsConnectedSrcObject(curveNode))
            {
                bIsAnimated |= static_cast<bool>(curveNode->GetAnimationInterval(animTimeSpan));
                break;
            }
        }

        // Rotation animation
        FbxProperty rotProp = pNode->LclRotation;
        for (int i = 0; bIsAnimated == false && i < rotProp.GetSrcObjectCount<FbxAnimCurveNode>(); i++)
        {
            FbxAnimCurveNode* curveNode = rotProp.GetSrcObject<FbxAnimCurveNode>(i);
            if (curveNode && pAnimLayer->IsConnectedSrcObject(curveNode))
            {
                bIsAnimated |= static_cast<bool>(curveNode->GetAnimationInterval(animTimeSpan));
            }
        }

        return bIsAnimated;
    }

    bool FBXImporter::ImportCurve(const FbxAnimCurve* fbxAnimCurve, const FbxTimeSpan& animTimeSpan, FloatCurveTrack& outCurve, float valueScale, bool negative, bool removeDuplicateKey, bool keepStartTime)
    {
        const static float sDefaultCurveWeight = FbxAnimCurveDef::sDEFAULT_WEIGHT;

        if (fbxAnimCurve)
        {
            const UInt32 keyCount = static_cast<UInt32>(fbxAnimCurve->KeyGetCount());
            const float realValueScale = negative ? -valueScale : valueScale;

            for (UInt32 keyIdx = 0; keyIdx < keyCount; ++keyIdx)
            {
                // Fbx curve key info
                FbxAnimCurveKey curFbxKey = fbxAnimCurve->KeyGet(keyIdx);
                FbxTime curKeyTime = curFbxKey.GetTime() - animTimeSpan.GetStart();
                float curFbxKeyTime = static_cast<float>(curKeyTime.GetSecondDouble());
                float curFbxKeyValue = curFbxKey.GetValue();

                const bool includeOverrides = true;
                FbxAnimCurveDef::ETangentMode curFbxKeyTangentMode = curFbxKey.GetTangentMode(includeOverrides);
                FbxAnimCurveDef::EInterpolationType curFbxKeyInterpMode = curFbxKey.GetInterpolation();
                FbxAnimCurveDef::EWeightedMode curFbxKeyTangentWeightMode = curFbxKey.GetTangentWeightMode();

                const bool prevFbxKeyValid = keyIdx > 0U;
                const bool nextFbxKeyValid = keyIdx < keyCount - 1U;
                float prevFbxKeyTime = 0.f, nextFbxKeyTime = 0.f;
                float prevFbxKeyValue = 0.f, nextFbxKeyValue = 0.f;

                // Our curve key info
                FloatCurveKey ourCurveKey = FloatCurveKey{};
                KeyInterpType ourKeyInterpMode = KeyInterpType::KIT_LINEAR;
                KeySmoothMode ourKeyTangentMode = KeySmoothMode::KSM_AUTO;

                // Info of current key
                float keyArriveTangent = const_cast<FbxAnimCurve*>(fbxAnimCurve)->KeyGetLeftDerivative(keyIdx) * realValueScale;
                float keyLeaveTangent = const_cast<FbxAnimCurve*>(fbxAnimCurve)->KeyGetRightDerivative(keyIdx) * realValueScale;
                float keyArriveWeight = sDefaultCurveWeight, keyLeaveWeight = sDefaultCurveWeight;
                bool keyArriveWeightValid = false, keyLeaveWeightValid = false;

                if (prevFbxKeyValid)
                {
                    FbxAnimCurveKey prevFbxKey = fbxAnimCurve->KeyGet(keyIdx - 1);
                    FbxTime prevKeyTime = prevFbxKey.GetTime() - animTimeSpan.GetStart();
                    prevFbxKeyTime = static_cast<float>(prevKeyTime.GetSecondDouble());
                    prevFbxKeyValue = prevFbxKey.GetValue() * realValueScale;

                    // The left tangent is driven by the previous key. If the previous key have a the NextLeftweight or both flag weighted mode, it mean the next key is weighted on the left side
                    keyArriveWeightValid = (prevFbxKey.GetTangentWeightMode() & FbxAnimCurveDef::eWeightedNextLeft) > 0;
                    if (keyArriveWeightValid)
                    {
                        keyArriveWeight = prevFbxKey.GetDataFloat(FbxAnimCurveDef::eNextLeftWeight);
                    }
                }
                if (nextFbxKeyValid)
                {
                    FbxAnimCurveKey nextFbxKey = fbxAnimCurve->KeyGet(keyIdx + 1);
                    FbxTime nextKeyTime = nextFbxKey.GetTime() - animTimeSpan.GetStart();
                    nextFbxKeyTime = static_cast<float>(nextKeyTime.GetSecondDouble());
                    nextFbxKeyValue = nextFbxKey.GetValue() * realValueScale;

                    // The right tangent weight should be use only if we are not the last key since the last key do not have a right tangent.
                    // Use the current key to gather the right tangent weight
                    keyLeaveWeightValid = (curFbxKeyTangentWeightMode & FbxAnimCurveDef::eWeightedRight) > 0;
                    if (keyLeaveWeightValid)
                    {
                        keyLeaveWeight = curFbxKey.GetDataFloat(FbxAnimCurveDef::eRightWeight);
                    }
                }

                // When this flag is true, the tangent is flat if the value has the same value as the previous or next key.
                const bool bTangentGenericClamp = (curFbxKeyTangentMode & FbxAnimCurveDef::eTangentGenericClamp);

                // Time independent tangent this is consider has a spline tangent key
                const bool bTangentGenericTimeIndependent = (curFbxKeyTangentMode & FbxAnimCurveDef::ETangentMode::eTangentGenericTimeIndependent);

                // When this flag is true, the tangent is flat if the value is outside of the [previous key, next key] value range.
                // Clamp progressive is (eTangentGenericClampProgressive |eTangentGenericTimeIndependent)
                const bool bTangentGenericClampProgressive = (curFbxKeyTangentMode & FbxAnimCurveDef::ETangentMode::eTangentGenericClampProgressive) == FbxAnimCurveDef::ETangentMode::eTangentGenericClampProgressive;

                // Our key smooth mode
                if (curFbxKeyTangentMode & FbxAnimCurveDef::eTangentUser)
                {
                    ourKeyTangentMode = KeySmoothMode::KSM_FLAT;
                }
                else if (curFbxKeyTangentMode & FbxAnimCurveDef::eTangentGenericBreak)
                {
                    ourKeyTangentMode = KeySmoothMode::KSM_BREAK;
                }

                // Our key interpolation mode
                switch (curFbxKeyInterpMode)
                {
                case FbxAnimCurveDef::eInterpolationConstant:   // Constant value until next key.
                    ourKeyInterpMode = KeyInterpType::KIT_CONSTANT;
                    break;
                case FbxAnimCurveDef::eInterpolationLinear:   // Linear progression to next key.
                    ourKeyInterpMode = KeyInterpType::KIT_LINEAR;
                    break;
                case FbxAnimCurveDef::eInterpolationCubic:   // Cubic progression to next key.
                    ourKeyInterpMode = KeyInterpType::KIT_SMOOTH;
                    // Get Tangents
                    {
                        bool bIsFlatTangent = false;
                        if (bTangentGenericClampProgressive)
                        {
                            if (prevFbxKeyValid && nextFbxKeyValid)
                            {
                                const float PreviousNextHalfDelta = (nextFbxKeyValue - prevFbxKeyValue) * 0.5f;
                                const float PreviousNextAverage = prevFbxKeyValue + PreviousNextHalfDelta;
                                // If the value is outside of the previous-next value range, the tangent is flat.
                                bIsFlatTangent = MathUtils::Abs(curFbxKeyValue - PreviousNextAverage) >= MathUtils::Abs(PreviousNextHalfDelta);
                            }
                            else
                            {
                                // Start/End tangent with the ClampProgressive flag are flat.
                                bIsFlatTangent = true;
                            }
                        }
                        else if (bTangentGenericClamp && (prevFbxKeyValid || nextFbxKeyValid))
                        {
                            if (prevFbxKeyValid && prevFbxKeyValue == curFbxKeyValue)
                            {
                                bIsFlatTangent = true;
                            }
                            if (nextFbxKeyValid)
                            {
                                bIsFlatTangent |= (curFbxKeyValue == nextFbxKeyValue);
                            }
                        }
                        else if (bTangentGenericTimeIndependent)
                        {
                            // Spline tangent key, because bTangentGenericClampProgressive include bTangentGenericTimeIndependent, we must treat this case after bTangentGenericClampProgressive
                            if (keyCount == 1)
                            {
                                bIsFlatTangent = true;
                            }
                            else
                            {
                                // Spline tangent key must be Flat mode since we want to keep the tangents provide by the fbx key left and right derivatives
                                ourKeyTangentMode = KeySmoothMode::KSM_FLAT;
                            }
                        }

                        if (bIsFlatTangent)
                        {
                            keyArriveTangent = 0;
                            keyLeaveTangent = 0;
                            // To force flat tangent we need to set the tangent mode to Flat
                            ourKeyTangentMode = KeySmoothMode::KSM_FLAT;
                        }
                    }
                    break;
                default:
                    break;
                }

                // Auto with weighted give the wrong result, so when auto is weighted we set user mode and set the Right tangent equal to the left tangent.
                // Auto has only the left tangent set
                if (ourKeyTangentMode == KeySmoothMode::KSM_AUTO && (keyArriveWeightValid || keyLeaveWeightValid))
                {
                    ourKeyTangentMode = KeySmoothMode::KSM_FLAT;
                    keyLeaveTangent = keyArriveTangent;
                }

                if (ourKeyTangentMode != KeySmoothMode::KSM_AUTO)
                {
                    const bool bEqualTangents = MathUtils::IsNearlyZero(keyLeaveTangent - keyArriveTangent);
                    // If tangents are different then broken.
                    if (bEqualTangents)
                    {
                        ourKeyTangentMode = KeySmoothMode::KSM_FLAT;
                    }
                    else
                    {
                        ourKeyTangentMode = KeySmoothMode::KSM_BREAK;
                    }
                }

                // Only cubic interpolation allow weighted tangents
                if (curFbxKeyInterpMode == FbxAnimCurveDef::eInterpolationCubic)
                {
                    if (!keyArriveWeightValid && !keyLeaveWeightValid)
                    {
                        keyLeaveWeight = sDefaultCurveWeight;
                        keyArriveWeight = sDefaultCurveWeight;
                    }
                    else if (keyArriveWeightValid)
                    {
                        keyLeaveWeight = sDefaultCurveWeight;
                    }
                    else if (keyLeaveWeightValid)
                    {
                        keyArriveWeight = sDefaultCurveWeight;
                    }

                    auto ComputeWeightInternal = [](float timeA, float timeB, const float tangentSlope, const float tangentWeight) 
                    {
                        const float x = timeA - timeB;
                        const float y = tangentSlope * x;
                        return std::sqrt(x * x + y * y) * tangentWeight;
                    };

                    if (!MathUtils::IsNearlyZero(keyArriveWeight))
                    {
                        if (prevFbxKeyValid)
                        {
                            keyArriveWeight = ComputeWeightInternal(curFbxKeyTime, prevFbxKeyTime, keyArriveTangent, keyArriveWeight);
                        }
                        else
                        {
                            keyArriveWeight = 0.0f;
                        }
                    }

                    if (!MathUtils::IsNearlyZero(keyLeaveWeight))
                    {
                        if (nextFbxKeyValid)
                        {
                            keyLeaveWeight = ComputeWeightInternal(nextFbxKeyTime, curFbxKeyTime, keyLeaveTangent, keyLeaveWeight);
                        }
                        else
                        {
                            keyLeaveWeight = 0.0f;
                        }
                    }
                }

                ourCurveKey.Time = keepStartTime ? curFbxKeyTime + static_cast<float>(animTimeSpan.GetStart().GetSecondDouble()) : curFbxKeyTime;
                ourCurveKey.Value = curFbxKeyValue;
                ourCurveKey.InterpType = ourKeyInterpMode;
                ourCurveKey.SmoothType = ourKeyTangentMode;

                ourCurveKey.ArriveTangent = keyArriveTangent;
                ourCurveKey.LeaveTangent = keyLeaveTangent;
                ourCurveKey.ArriveWeight = keyArriveWeight;
                ourCurveKey.LeaveWeight = keyLeaveWeight;

                if (removeDuplicateKey && outCurve.Keys.size())
                {                        
                    if (outCurve.Keys.back() != ourCurveKey)
                    {
                        outCurve.Keys.emplace_back(std::move(ourCurveKey));
                    }
                }
                else
                {
                    outCurve.Keys.emplace_back(std::move(ourCurveKey));
                }
            }

            return true;
        }

        return false;
    }

    bool FBXImporter::ShouldImportCurve(const FbxAnimCurve* fbxAnimCurve, bool bDoNotImportCurveOnlyWithZero)
    {
        if (fbxAnimCurve && fbxAnimCurve->KeyGetCount() > 0)
        {
            if (bDoNotImportCurveOnlyWithZero)
            {
                for (auto keyIdx = 0; keyIdx < fbxAnimCurve->KeyGetCount(); ++keyIdx)
                {
                    if (!MathUtils::IsNearlyZero(fbxAnimCurve->KeyGetValue(keyIdx)))
                    {
                        return true;
                    }
                }
            }
            else
            {
                return true;
            }
        }

        return false;
    }

    void FBXImporter::CollectMeshNodeRecursively(FbxNode* mergeRoot, FbxAMatrix rootMatix, SInt32 mergeRootIndex, ImportScene& scene, bool includeRoot)
    {
        materialSet.clear();
        if (includeRoot)
        {
            RecursiveMergeNodes(mergeRoot, rootMatix, 0, scene);
        }
        else
        {
            const SInt32 childCount = mergeRoot->GetChildCount();
            for (SInt32 i = 0; i < childCount; ++i)
            {
                fbxsdk::FbxNode* fbxChild = mergeRoot->GetChild(i);
                FbxAMatrix maTrix = rootMatix * mScene->GetAnimationEvaluator()->GetNodeLocalTransform(fbxChild);
                RecursiveMergeNodes(fbxChild, ComputeTotalMatrix(fbxChild), mergeRootIndex, scene);
            }
        }
    }
    // no skeleton
    void FBXImporter::RecursiveMergeNodes(FbxNode* fbxNode, FbxAMatrix toMergeRootMatrix, SInt32 mergeRootIndex, ImportScene& scene)
    {
        std::string name = static_cast<const char*>(fbxNode->GetNameWithoutNameSpacePrefix());
        if (FBXHelper::SubmeshNameIsPhysicsCollision(name))
        {
            TransferCollider(name, fbxNode, scene);
            return;
        }
        if (auto fbxMesh = fbxNode->GetMesh())
        {
            mImportSettings->SplitByMaterial = false;
            MaterialDescription materialDescCheck;
            FBXImporter::TransferMaterial(scene, fbxNode, fbxMesh, materialDescCheck);
            auto fbxMaterialptr = materialDescCheck.GetMaterialIDs().begin()->first;
            if (mImportSettings->CreateCombineMaterials)
            {
                if (scene.MeshDescriptions.size() > 0)
                    MergeMeshDescription(nullptr, fbxMesh, fbxNode, materialDescCheck, toMergeRootMatrix, mergeRootIndex, scene);
                else
                    AddNodeWithNewMat(nullptr, fbxMesh, fbxNode, materialDescCheck, toMergeRootMatrix, mergeRootIndex, scene);
            }
            else if(materialSet.count(fbxMaterialptr))
            {
                MergeMeshDescription(fbxMaterialptr, fbxMesh, fbxNode, materialDescCheck, toMergeRootMatrix, mergeRootIndex, scene);
            }
            else
            {
                AddNodeWithNewMat(fbxMaterialptr, fbxMesh, fbxNode, materialDescCheck, toMergeRootMatrix, mergeRootIndex, scene);
            }
        }
        const SInt32 childCount = fbxNode->GetChildCount();
        for (SInt32 i = 0; i < childCount; ++i)
        {
            fbxsdk::FbxNode* fbxChild = fbxNode->GetChild(i);
            FbxAMatrix maTrix = toMergeRootMatrix * mScene->GetAnimationEvaluator()->GetNodeLocalTransform(fbxChild);
            RecursiveMergeNodes(fbxChild, maTrix, mergeRootIndex, scene);
        }
    }

    void FBXImporter::AddNodeWithNewMat(FbxSurfaceMaterial* fbxMaterialptr, FbxMesh* fbxMesh, FbxNode* fbxNode, MaterialDescription& materialDescCheck, FbxAMatrix toMergeRootMatrix, SInt32 mergeRootIndex, ImportScene& scene)
    {
        auto& materialMeshBind = scene.MaterialMeshBindings.emplace_back();
        TransferMesh(fbxMesh, toMergeRootMatrix * GetGeometryOffset(fbxNode), materialDescCheck, materialMeshBind, scene.MeshDescriptions);
        if (fbxMaterialptr)
        {
            auto index = scene.MeshDescriptions.size() - 1;
            mdset.insert(std::pair<FbxSurfaceMaterial*, SInt32>(fbxMaterialptr, static_cast<SInt32>(index)));
            materialSet.insert(fbxMaterialptr);
        }
        return;
    }

    void FBXImporter::MergeMeshDescription(FbxSurfaceMaterial* fbxMaterialptr, FbxMesh* fbxMesh, FbxNode* fbxNode, MaterialDescription& materialDescCheck, FbxAMatrix toMergeRootMatrix, SInt32 mergeRootIndex, ImportScene& scene)
    {
        SInt32 index = fbxMaterialptr ? mdset[fbxMaterialptr] : 0;
        std::vector<MaterialMeshBind> tmpMaterialMeshBind;
        std::vector<MeshDescription> tmpMeshDescriptions;
        MaterialMeshBind materialMeshBind;
        TransferMesh(fbxMesh, toMergeRootMatrix * GetGeometryOffset(fbxNode), materialDescCheck, materialMeshBind, tmpMeshDescriptions);
        MeshDescription mergedMesh = tmpMeshDescriptions[0];
        scene.MeshDescriptions[index].mergeMeshDescription(mergedMesh);
        return;
    }
}}   // namespace cross::editor