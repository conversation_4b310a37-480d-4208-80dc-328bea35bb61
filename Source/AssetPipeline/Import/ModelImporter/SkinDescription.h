#pragma once

#include <vector>
#include <unordered_map>
#include "CrossBase/Math/CrossMath.h"

namespace cross::editor {

    struct SkinBuildVertex
    {
        SkinBuildVertex(UInt32 id)
            : ControlPointID(id)
        {
        }

        // Raw control point index in Fbx for this SkinBuildVertex
        UInt32 ControlPointID;
        std::array<float, 4> Weights{0.0f, 0.0f, 0.0f, 0.0f};
        std::array<UInt32, 4> BoneIDs{0, 0, 0, 0};
    };

    struct MeshBuildVertex;

    class SkinDescription
    {
    public:
        using BoneID = SInt32;

        SkinDescription() = default;
        ~SkinDescription() = default;

    public:
        void Init(const std::vector<MeshBuildVertex>& vertices);

        const SkinBuildVertex& GetSkinBuildVertex(UInt32 index) const
        {
            return mSkinVertices[index];
        }

        std::vector<SkinBuildVertex>& GetSkinVerticesRef() 
        {
            return mSkinVertices;
        };

        bool HasSkin() const
        {
            return !mSkinVertices.empty();
        }

    private:
        std::vector<SkinBuildVertex> mSkinVertices;
    };
}