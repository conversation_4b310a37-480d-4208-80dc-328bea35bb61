#include "EnginePrefix.h"
#include "ShaderImporter.h"

#include "AssetPipeline/PCH/AssetPipelinePCH.h"
#include "AssetPipeline/Utils/AssetIO.h"
#include "AssetPipeline/Import/ShaderImporter/IncludeHandler.h"
#include "AssetPipeline/Import/ShaderImporter/DirectXShaderCompiler.h"
#include "AssetPipeline/Import/ShaderImporter/ShaderReflection.h"
#include "AssetPipeline/Import/ShaderImporter/ShaderImportSettings.h"
#include "CrossBase/uuid/CrossUUID.h"

#include "Threading/Task.h"

#include "CrossSchema/ShaderAsset_generated.h"
#include "flatbuffers/flatbuffers.h"

#include <fstream>
#include <algorithm>
#include <codecvt>
#include <sys/stat.h>
#include <sys/types.h>
#include <ranges>

#include "Resource/Shader.h"
#include "Resource/ResourceManager.h"

/*
 * simple grammar example
 *
 * graphics shader
 * #pragma vertex vert
 * #pragma pixel pixel
 * #pragma keyword WHAT
 * #pragma keyword THE
 * #pragma keyword FUCK
 *
 * compute shader
 * #pragma compute kernel0
 * #pragma compute kernel1
 * #pragma compute kernel2
 */

#if 0
#    include <chrono>
#    define TICK(x) auto bench_##x = std::chrono::steady_clock::now();
#    define TOCK(x) std::cout << #x ": " << std::chrono::duration_cast<std::chrono::duration<double>>(std::chrono::steady_clock::now() - bench_##x).count() << "s" << std::endl;
#else
#    define TICK(x)
#    define TOCK(x)
#endif

using CrossSchema::ShaderCodeFormat;
using CrossSchema::ShaderCodeT;
using CrossSchema::ShaderLayoutT;
using CrossSchema::ShaderVersion;

#define SequentialCompilation CROSSENGINE_OSX

namespace cross::editor {
GraphicsShaderImporter::GraphicsShaderImporter()
    : ShaderImporter(AssetType::Shader)
{}

ComputeShaderImporter::ComputeShaderImporter()
    : ShaderImporter(AssetType::ComputeShader)
{
}

inline ShaderImporter::ShaderImporter(AssetType type)
    : mImportSettings(&ShaderImportSettings::gShaderImportSettings)
    , AssetImporter(type)
{}

void ShaderImporter::ParseShaderCompileCommand(std::shared_ptr<std::string> originalShaderStr, ShaderCompileCommand& cmd)
{
    ParseShaderCompileCommand(*originalShaderStr.get(), cmd);
}

void ShaderImporter::ParseShaderCompileCommand(const std::string& originalShaderStr, ShaderCompileCommand& cmd)
{
    using namespace CrossSchema;
    static std::regex gPragmaPattern{R"""(^\s*#pragma\s+(\w+)\s+(\w+))"""};

    static const std::unordered_map<std::string_view, ShaderStageBit> gShaderStages{{"vertex", ShaderStageBit::Vertex},
                                                                                    {"hull", ShaderStageBit::Hull},
                                                                                    {"domain", ShaderStageBit::Domain},
                                                                                    {"geometry", ShaderStageBit::Geometry},
                                                                                    {"pixel", ShaderStageBit::Pixel},
                                                                                    {"task", ShaderStageBit::Task},
                                                                                    {"mesh", ShaderStageBit::Mesh},
                                                                                    {"compute", ShaderStageBit::Compute},
                                                                                    {"raygen", ShaderStageBit::RayGen},
                                                                                    {"closesthit", ShaderStageBit::ClosestHit},
                                                                                    {"anyhit", ShaderStageBit::AnyHit},
                                                                                    {"miss", ShaderStageBit::Miss},
                                                                                    {"callable", ShaderStageBit::Callable},
                                                                                    {"intersection", ShaderStageBit::InterSection}};

    bool Compute = false;
    bool RayTracing = false;

    ShaderCompileCommand::UsageInfo* curtUsageInfo = nullptr;

    for (std::sregex_iterator pragmaItr{originalShaderStr.begin(), originalShaderStr.end(), gPragmaPattern}, end{}; pragmaItr != end; ++pragmaItr)
    {
        auto& match = *pragmaItr;
        std::string_view key{&(*match[1].first), static_cast<size_t>(match[1].length())};
        if (auto ret = gShaderStages.find(key); ret != gShaderStages.end())
        {
            auto stage = ret->second;
            std::string entry = match[2];
            cmd.stages.emplace_back(stage, std::move(entry), UTF8toUTF16(entry));

            if (stage == ShaderStageBit::Compute)
            {
                Compute = true;
            }
            if (stage == ShaderStageBit::RayGen || stage == ShaderStageBit::ClosestHit || stage == ShaderStageBit::AnyHit ||
                stage == ShaderStageBit::Miss || stage == ShaderStageBit::Callable || stage == ShaderStageBit::InterSection)
            {
                RayTracing = true;
            }
        }
        else if (key == "keyword")
        {
            cmd.keywords.emplace_back(match[2]);
        }
        else if (key == "usage")
        {
            auto usage = magic_enum::enum_cast<MaterialUsage>(match[2].str());
            if (usage)
            {
                curtUsageInfo = &cmd.usages.emplace_back(*usage);
            }
        }
        else if (key == "usage_keyword")
        {
            Assert(curtUsageInfo);
            curtUsageInfo->Keywords.emplace_back(match[2]);
        }
        else if (key == "enable")
        {
            if (match[2] == "debug_symbol")
                cmd.debugMode = DebugMode::SourceCode;
            if (match[2] == "compatible_symbol")
                cmd.debugMode = DebugMode::Compatible;
        }
        else if (key == "only_renderers")
        {
            std::string_view onlyFormatsStr{&(*match[2].first), static_cast<size_t>(50)};
            size_t left = 0;
            auto right = onlyFormatsStr.find("\n");

            while (left < right)
            {
                auto temp_end = onlyFormatsStr.find(' ', left);
                if (temp_end > right)
                {
                    temp_end = right;
                }
                auto onlyFormat = onlyFormatsStr.substr(left, temp_end - left);
                if (auto res = mShaderPlatform.find(onlyFormat); res != mShaderPlatform.end())
                {
                    cmd.curShaderFormats.emplace_back(res->second);
                }
                left = temp_end + 1;
            }
            cmd.onDemandCompilation = true;
        }
        else if (key == "exclude_renderers")
        {
            std::string_view excludeFormatsStr{&(*match[2].first), static_cast<size_t>(50)};
            std::unordered_set<CrossSchema::ShaderCodeFormat> excludeFormatsSet;
            size_t left = 0;
            auto right = excludeFormatsStr.find("\n");

            while (left < right)
            {
                auto temp_end = excludeFormatsStr.find(' ', left);
                if (temp_end > right)
                {
                    temp_end = right;
                }
                auto& excludeFormat = std::get<0>(mShaderPlatform.find(excludeFormatsStr.substr(left, temp_end - left))->second);
                excludeFormatsSet.insert(excludeFormat.format());
                left = temp_end + 1;
            }

            for (auto& format : mShaderPlatform | std::views::values)
            {
                auto cur_scf = std::get<0>(format);
                if (auto res = excludeFormatsSet.find(cur_scf.format()); res == excludeFormatsSet.end())
                {
                    cmd.curShaderFormats.emplace_back(format);
                }
            }
            cmd.onDemandCompilation = true;
        }
    }

    if (!cmd.onDemandCompilation)
    {
        // Convert mShaderPlatform to curShaderFormats
        for (auto& format : mShaderPlatform | std::views::values)
        {
            cmd.curShaderFormats.emplace_back(format);
        }
    }

    if (!Compute && !RayTracing)
    {
        if (std::none_of(cmd.stages.begin(), cmd.stages.end(), [](auto& stage) { return std::get<0>(stage) == ShaderStageBit::Vertex; }))
        {
            cmd.stages.emplace_back(ShaderStageBit::Vertex, "VSMain", L"VSMain");
        }
        if (std::none_of(cmd.stages.begin(), cmd.stages.end(), [](auto& stage) { return std::get<0>(stage) == ShaderStageBit::Pixel; }))
        {
            cmd.stages.emplace_back(ShaderStageBit::Pixel, "PSMain", L"PSMain");
        }

        std::sort(cmd.stages.begin(), cmd.stages.end(), [](auto& a, auto& b) {
            ShaderStageBit sa = std::get<0>(a);
            ShaderStageBit sb = std::get<0>(b);
            return ToUnderlying(sa) < ToUnderlying(sb);
        });
    }
}

cross::threading::TaskEventPtr cross::editor::GraphicsShaderImporter::ImportAssetAsync(const std::string& sourcePath, const std::string& ndaSavePath)
{
    std::shared_ptr<IncludeHandler> headerDataBase;
    std::shared_ptr<CrossSchema::GraphicsShaderAssetT> output;
    auto mergeShaderTaskEvents = CompileShaderAsyncInner(sourcePath, headerDataBase, output);

    if (!mergeShaderTaskEvents)
        return nullptr;

    return threading::Async<bool>(*mergeShaderTaskEvents, [mergeShaderTaskEvents, headerDataBase, sourcePath, ndaSavePath, output, this](const auto&) {
        auto allTrue = true;
        mergeShaderTaskEvents->Traverse([&allTrue](const auto& taskEventPtr)
        {
            if (!taskEventPtr->GetReturnValue<bool>())
            {
                allTrue = false;
                return true;
            }
            return false;
        });
        LOG_INFO("Shader: {} Compilation {}", sourcePath, allTrue ? "Succeeded" : "Failed");

        if (allTrue)
        {
            auto typedPath = ndaSavePath;
            ShaderPtr shader = gResourceMgr.CreateResourceAs<resource::Shader>();
            shader->CreateAsset(typedPath);
            shader->Serialize(*output);
            if (mImportSettings->GenShaderMaps)
            {
                headerDataBase->RecordFinish(shader->GetGuid_Str(), true, mImportSettings->GenShaderMapsDebugInfo);
            }
        }
        else if (mImportSettings->GenShaderMaps)
        {
            auto fileSystem = EngineGlobal::GetFileSystem();
            const std::string ndaPath = fileSystem->GetAbsolutePath(ndaSavePath);
            const std::string guidStr = gResourceMgr.GetGuidByPath(fileSystem->GetRelativePath(ndaPath));
            if (!guidStr.empty())
            {
                headerDataBase->RecordFinish(guidStr, true, mImportSettings->GenShaderMapsDebugInfo);
            }
        }

        SetProgress(0.f);
        return allTrue;
    });
}

threading::TaskEventPtr GraphicsShaderImporter::CompileShaderAsync(const std::string& sourcePath, std::vector<UInt8>& outShaderBinary)
{
    std::shared_ptr<IncludeHandler> includeHeader;
    std::shared_ptr<CrossSchema::GraphicsShaderAssetT> graphicsShaderAssetT;
    auto mergeShaderTaskEvents = CompileShaderAsyncInner(sourcePath, includeHeader, graphicsShaderAssetT);

    return threading::Async<bool>(*mergeShaderTaskEvents, [mergeShaderTaskEvents, graphicsShaderAssetT, sourcePath, &outShaderBinary, this](const auto&) {
        auto allTrue = true;
        mergeShaderTaskEvents->Traverse([&allTrue](const auto& taskEventPtr)
        {
            if (!taskEventPtr->GetReturnValue<bool>())
            {
                allTrue = false;
                return true;
            }
            return false;
        });
        LOG_INFO("Shader: {} Compilation {}", sourcePath, allTrue ? "Succeeded" : "Failed");

        if (allTrue)
        {
            flatbuffers::FlatBufferBuilder graphicsShaderBuilder{4096};

            ClassIDType classID = ClassID(Shader);
            CrossSchema::ResourceHeader header(ASSET_MAGIC_NUMBER, 0, classID, 0, 0);

            flatbuffers::Offset<CrossSchema::GraphicsShaderAsset> mloc = CreateGraphicsShaderAsset(graphicsShaderBuilder, graphicsShaderAssetT.get());
            flatbuffers::Offset<CrossSchema::ResourceAsset> mloc2 = CreateResourceAsset(graphicsShaderBuilder, &header, graphicsShaderBuilder.CreateString(""), CrossSchema::ResourceType::GraphicsShaderAsset, mloc.Union());
            CrossSchema::FinishResourceAssetBuffer(graphicsShaderBuilder, mloc2);

            auto binaryDataSize = graphicsShaderBuilder.GetSize();
            outShaderBinary.resize(binaryDataSize);
            memcpy(outShaderBinary.data(), graphicsShaderBuilder.GetBufferPointer(), binaryDataSize);
        }

        SetProgress(0.f);
        return allTrue;
    });
}

static bool CheckConstantBuffersRedefine(std::string_view sourcePath, ShaderLayoutT* shaderLayout)
{
    std::unordered_map<std::string_view, int> count_map;
    std::vector<std::string_view> repeated_elements;

    for (const auto& cb : shaderLayout->constant_buffers)
    {
        count_map[cb->name]++;
    }

    for (const auto& pair : count_map)
    {
        if (pair.second > 1)
        {
            repeated_elements.push_back(pair.first);
        }
    }

    if (repeated_elements.empty())
        return true;

    LOG_EDITOR_ERROR("sourcePath:{}, constant buffers name redefine for {}", sourcePath, fmt::join(repeated_elements, ", "));
    return false;
}

bool cross::editor::GraphicsShaderImporter::GenVariant(CrossSchema::GraphicsShaderCodeT* shaderVariant, const std::time_t mtime, const std::string& shaderCode, const CrossSchema::ShaderVersion& version,
                                                       const std::vector<std::wstring>& defines, const UInt64 keywordMask, const std::vector<std::tuple<CrossSchema::ShaderStageBit, std::string, std::wstring>>& stages,
                                                       IncludeHandler* includer, DebugMode debugMode)
{
    using namespace CrossSchema;
    {
        shaderVariant->mtime = mtime;
        shaderVariant->active_keywords = keywordMask;
        auto guid = CrossUUID::GenerateCrossUUID();
        shaderVariant->guid = std::make_unique<CrossSchema::GUID>(guid.low, guid.high);
    }

    const auto sourcePath = includer->GetFilePath().string();
    const auto* formatName = ShaderCodeFormatTypeTable()->names[static_cast<UInt32>(version.format())];

    auto stageLayoutVec = std::vector<std::shared_ptr<ShaderLayoutT>>();

    TICK(CompileStage);
    {
        for (std::size_t i = 0; i < stages.size(); i++)
        {
            const auto& stage = std::get<0>(stages[i]);
            const auto& entryPoint = std::get<2>(stages[i]);

            auto stageCode = std::make_unique<ShaderCodeT>();
            ShaderCodeT* shaderCodeP = stageCode.get();
            ShaderLayoutT* stageLayoutP = stageLayoutVec.emplace_back(std::make_shared<ShaderLayoutT>()).get();

            switch (stage)
            {
            case CrossSchema::ShaderStageBit::Vertex:
                shaderVariant->vertex_shader = std::move(stageCode);
                break;
            case CrossSchema::ShaderStageBit::Hull:
                shaderVariant->hull_shader = std::move(stageCode);
                break;
            case CrossSchema::ShaderStageBit::Domain:
                shaderVariant->domain_shader = std::move(stageCode);
                break;
            case CrossSchema::ShaderStageBit::Geometry:
                shaderVariant->geometry_shader = std::move(stageCode);
                break;
            case CrossSchema::ShaderStageBit::Pixel:
                shaderVariant->pixel_shader = std::move(stageCode);
                break;
            case CrossSchema::ShaderStageBit::Task:
                shaderVariant->task_shader = std::move(stageCode);
                break;
            case CrossSchema::ShaderStageBit::Mesh:
                shaderVariant->mesh_shader = std::move(stageCode);
                break;
            default:
                LOG_EDITOR_ERROR("[ShaderCompile] Unsupported shader stage {}", stage);
                return false;
            }

            const auto* stageName = ShaderStageBitTypeTable()->names[static_cast<UInt32>(std::log2(static_cast<UInt32>(stage))) + 1];

            LOG_INFO("Threading: {}, Compile shader: {}, keyword: {}, format: {}_{}_{}, stage: {}, entry point: {}",
                     threading::TaskSystem::GetCurrentThreadID(),
                     sourcePath.c_str(),
                     keywordMask,
                     formatName,
                     version.major_version(),
                     version.minor_version(),
                     stageName,
                     UTF16toUTF8(entryPoint).c_str());

            shaderCodeP->stage = stage;
            shaderCodeP->entry_point = UTF16toUTF8(entryPoint);

            std::vector<DxcDefine> defComb{defines.size(), DxcDefine{}};
            std::transform(defines.begin(), defines.end(), defComb.begin(), [](auto& kwl) {
                return DxcDefine{
                    kwl.c_str(),
                    L"1",
                };
            });

            /*if (version.format() == ShaderCodeFormat::DXIL)
            {
                DirectXShaderCompiler dxc{includer};
                auto result = dxc.Compile(shaderCode, entryPoint, UTF8toUTF16(sourcePath), defComb, stage, version, false, debugSymbol);

                if (result)
                {
                    shaderCodeP->code_data.resize(result->GetBufferSize());
                    std::memcpy(shaderCodeP->code_data.data(), result->GetBufferPointer(), result->GetBufferSize());
                    if (!ReflectForDXIL(result, shaderCodeP, stageLayoutP, nullptr))
                        return false;
                }
            }
            else*/
            {
                DirectXShaderCompiler dxc{includer};

                // Use fixed shader model 6_6 to compile SPIR_V
                CrossSchema::ShaderVersion compileVersion{
                    ShaderCodeFormat::DXIL,
                    6,
                    6
                };

                if (stage == ShaderStageBit::Pixel && (version.format() == ShaderCodeFormat::SPIR_V || version.format() == ShaderCodeFormat::MSL_IOS || version.format() == ShaderCodeFormat::MTLLIB_IOS))
                {
                    defComb.push_back({L"CROSS_NGI_SUPPORT_SUBPASS_INPUT", L"1"});
                }

                TICK(DXC_OTHER);
                auto result = dxc.Compile(shaderCode, entryPoint, UTF8toUTF16(sourcePath), defComb, stage, ShaderVersion{version.format(), version.major_version(), version.minor_version()}, false, debugMode);
                TOCK(DXC_OTHER);

                TICK(REFLECTION);
                if (result)
                {
                    auto dxilStageLayout = std::make_unique<ShaderLayoutT>();

                    // CrossEngine don't support constant buffer name redefined, but dxc support it.
                    if (!CheckConstantBuffersRedefine(sourcePath, dxilStageLayout.get()))
                        return false;

                    shaderCodeP->code_data.resize(result->GetBufferSize());
                    shaderCodeP->debug_symbol = debugMode != DebugMode::None;
                    std::memcpy(shaderCodeP->code_data.data(), result->GetBufferPointer(), result->GetBufferSize());

                    ReflectForOtherFormat(shaderCodeP, version, dxilStageLayout.get(), stageLayoutP, nullptr);
                }
                TOCK(REFLECTION);
            }

            if (shaderCodeP->code_data.empty())
            {
                LOG_EDITOR_ERROR("Compile shader failed: {} format: {}_{}_{} stage: {}", sourcePath.c_str(), formatName, version.major_version(), version.minor_version(), stageName);
                return false;
            }
        }
    }
    TOCK(CompileStage);

    shaderVariant->layout = std::make_unique<ShaderLayoutT>();
    ShaderLayoutT* shaderLayout = shaderVariant->layout.get();

    MergeShaderLayouts(stageLayoutVec, shaderLayout, version);

    return true;
}

std::shared_ptr<threading::TaskEventArray> GraphicsShaderImporter::CompileShaderAsyncInner(const std::string& sourcePath, std::shared_ptr<IncludeHandler>& outHeaderDataBase,
                                                                                           std::shared_ptr<CrossSchema::GraphicsShaderAssetT>& outGraphicsShaderAssetT)
{
    using namespace CrossSchema;

    QUICK_SCOPED_CPU_TIMING("CompileShader");

    // Load File
    std::ifstream file(sourcePath);
    if (!file)
    {
        LOG_EDITOR_ERROR("[ShaderCompile] Fail to open file: {}", sourcePath);
        return nullptr;
    }

    SetProgress(0.01f);

    outGraphicsShaderAssetT = std::make_shared<GraphicsShaderAssetT>();

    // The source file modify time stamp
    const std::time_t mtime = outGraphicsShaderAssetT->mtime = [&sourcePath]() {
        struct stat st;
        if (stat(sourcePath.c_str(), &st) == 0)
        {
            return st.st_mtime;
        }
        return std::time_t{0};
    }();

    auto originalShaderStr = std::make_shared<std::string>((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());

    ShaderCompileCommand compileCommand{};
    ParseShaderCompileCommand(originalShaderStr, compileCommand);
    const auto& [curShaderFormats, stages, keywords, usages, debugMode, onDemandCompilation] = compileCommand;

    auto allKeywords = keywords;
    for (auto& usage : usages)
    {
        allKeywords.emplace_back(magic_enum::enum_name(usage.Usage));
        for (auto& subkeyword : usage.Keywords)
        {
            if (std::ranges::find(allKeywords, subkeyword) == allKeywords.end())
            {
                allKeywords.emplace_back(subkeyword);
            }
        }
    }
    
    const auto targetVersion = *reinterpret_cast<CrossSchema::ShaderVersion*>(&mImportSettings->Version);
    auto formatIterator = std::find_if(mShaderPlatform.begin(), mShaderPlatform.end(), [&targetVersion](const auto& pair) {
        return targetVersion.format() == std::get<0>(pair.second).format() &&
            targetVersion.major_version() == std::get<0>(pair.second).major_version() &&
                targetVersion.minor_version() == std::get<0>(pair.second).minor_version();
    });

    if (formatIterator == mShaderPlatform.end())
    {
        LOG_EDITOR_ERROR("[ShaderCompile] Unsupported shader format: {}_{}_{}", EnumNameShaderCodeFormat(targetVersion.format()), targetVersion.major_version(), targetVersion.minor_version());
        return nullptr;
    }
    
    const auto& [version, basicDefines] = formatIterator->second;

    outHeaderDataBase = std::make_shared<IncludeHandler>(sourcePath.c_str());

    auto& platformShader =  outGraphicsShaderAssetT->platform_shaders.emplace_back(std::make_unique<PlatformGraphicsShaderT>());
    platformShader->version = std::make_unique<ShaderVersion>(version);
    platformShader->keywords = allKeywords;

    std::vector<UInt64> kwBitsVec{};
    std::vector<std::vector<std::wstring>> macroDefines{};

    if (mImportSettings->GenAllVariants)
    {
        if (usages.empty())
        {
            const UInt64 keywordVariantCount = static_cast<UInt64>(std::pow(2LL, keywords.size()));

            for (UInt64 i = 0; i < keywordVariantCount; i++)
            {
                auto keywordBits = std::bitset<64>(i);

                kwBitsVec.emplace_back(i);

                auto& defines = macroDefines.emplace_back(basicDefines);

                for (int j = 0; j < keywords.size(); j++)
                {
                    if (keywordBits[j])
                    {
                        defines.emplace_back(UTF8toUTF16(keywords[j]));
                    }
                }
            }
        }
        else
        {
            auto SetBitByAllKeywords = [&](std::string_view keyword, std::bitset<64>& bits)
            {
                if (auto ret = std::ranges::find(allKeywords, keyword); ret != allKeywords.end())
                {
                    bits[std::distance(allKeywords.begin(), ret)] = true;
                }
                else
                {
                    Assert(false);
                }
            };

            for (auto& usage : usages)
            {
                auto keywordsForUsage = keywords;
                keywordsForUsage.insert(keywordsForUsage.end(), usage.Keywords.begin(), usage.Keywords.end());

                const UInt64 variantsOfKeywordsForUsageCount = static_cast<UInt64>(std::pow(2LL, keywordsForUsage.size()));
                for (UInt64 i = 0; i < variantsOfKeywordsForUsageCount; i++)
                {
                    auto& defines = macroDefines.emplace_back(basicDefines);
                    defines.emplace_back(UTF8toUTF16(std::string(magic_enum::enum_name(usage.Usage))));

                    std::bitset<64> variantFromAllKeywordBits;
                    SetBitByAllKeywords(magic_enum::enum_name(usage.Usage), variantFromAllKeywordBits);

                    std::bitset<64> variantFromKeywordForUsageBits{i};
                    for (UInt64 j = 0; j < keywordsForUsage.size(); ++j)
                    {
                        if (variantFromKeywordForUsageBits[j])
                        {
                            SetBitByAllKeywords(keywordsForUsage[j], variantFromAllKeywordBits);
                            defines.emplace_back(UTF8toUTF16(keywordsForUsage[j]));
                        }
                    }

                    kwBitsVec.emplace_back(variantFromAllKeywordBits.to_ulong());
                }
            }
        }
    }
    else if (const auto& variants = mImportSettings->Variants; variants.size())
    {
        for (std::size_t i = 0; i < variants.size(); i++)
        {
            std::bitset<64> kwBits{0};
            auto& variantDefines = macroDefines.emplace_back(basicDefines);

            const auto& userDefs = variants[i].Defines;
            for (auto define : userDefs)
            {
                variantDefines.emplace_back(UTF8toUTF16(define));

                auto iterator = std::find(keywords.begin(), keywords.end(), define);

                if (iterator != keywords.end())
                    kwBits[std::distance(keywords.begin(), iterator)] = 1;
            }

            kwBitsVec.emplace_back(kwBits.to_ullong());
        }
    }
    else
    {
        kwBitsVec.emplace_back(0);
        macroDefines.emplace_back(basicDefines);
    }

    SetProgress(0.1f);

    auto variantEvents = std::make_shared<threading::TaskEventArray>();
    threading::TaskEventPtr lastCompilationEvent = nullptr;

    for (int i = 0; i < kwBitsVec.size(); i++)
    {
        const auto bitMask = kwBitsVec[i];
        const auto& allDefines = macroDefines[i];

        platformShader->variants.emplace_back(std::make_unique<CrossSchema::GraphicsShaderCodeT>());
        auto shaderVariant = platformShader->variants.back().get();

        threading::TaskEventArray dependencies;
        if (SequentialCompilation && lastCompilationEvent)
            dependencies.Add(lastCompilationEvent);

        const auto _version = version;
        lastCompilationEvent = threading::Async<bool>(dependencies, [=](const auto&) {
            auto ret = GenVariant(shaderVariant, mtime, *originalShaderStr, _version, allDefines, bitMask, stages, outHeaderDataBase.get(), debugMode);
            AddProgress(1.0f / kwBitsVec.size() * 0.8f);
            return ret;
        });

        variantEvents->Add(lastCompilationEvent);
    }

    return variantEvents;
}

cross::threading::TaskEventPtr cross::editor::ComputeShaderImporter::ImportAssetAsync(const std::string& sourcePath, const std::string& ndaSavePath)
{
    using namespace CrossSchema;   // safe

    auto output = std::make_shared<ComputeShaderAssetT>();

    QUICK_SCOPED_CPU_TIMING("CompileShader");

    // Load File
    std::ifstream file(sourcePath);
    if (!file)
    {
        return threading::Async<bool>([=](const auto&) {
            LOG_EDITOR_ERROR("Fail to open file: {}", sourcePath);
            return false;
        });
    }
    SetProgress(0.01f);

    auto originalShaderStr = std::make_shared<std::string>((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());

    static std::regex gPragmaPattern{R"""(^\s*#pragma\s+(\w+)\s+(\w+))"""};

    ShaderCompileCommand compileCommand{};
    ParseShaderCompileCommand(originalShaderStr, compileCommand);
    auto& [curShaderFormats, entryPoints, keywords, usages, debugSymbol, onDemandCompilation] = compileCommand;

    auto headerDataBase = std::make_shared<IncludeHandler>(sourcePath.c_str());

    auto sourcePathL = UTF8toUTF16(sourcePath);

    SetProgress(0.1f);

    auto kernelCompilationTaskEvents = std::make_shared<threading::TaskEventArray>();
    threading::TaskEventPtr lastCompilationEvent;

    auto taskNums = curShaderFormats.size() * entryPoints.size();
    output->platform_shaders.reserve(curShaderFormats.size());
    for (const auto& [version, defs] : curShaderFormats)
    {
        auto* formatName = ShaderCodeFormatTypeTable()->names[static_cast<UInt32>(version.format())];

        auto platformShader = std::make_unique<PlatformComputeShaderT>();
        platformShader->version = std::make_unique<ShaderVersion>(version);
        platformShader->code_list.reserve(entryPoints.size());

        for (const auto& [stageBit, entryPoint, entryPointL] : entryPoints)
        {
            auto uuid = CrossUUID::GenerateCrossUUID();
            auto& code = platformShader->code_list.emplace_back(std::make_unique<ComputeShaderCodeT>());
            code->guid = std::make_unique<CrossSchema::GUID>(uuid.low, uuid.high);
            code->group_size = std::make_unique<uint3>();
            code->layout = std::make_unique<ShaderLayoutT>();
            code->compute_shader = std::make_unique<ShaderCodeT>();
            code->compute_shader->entry_point = entryPoint;
            code->compute_shader->stage = ShaderStageBit::Compute;

            threading::TaskEventArray depends;
            if (SequentialCompilation && lastCompilationEvent)
            {
                depends.Add(lastCompilationEvent);
            }
            auto compilationEvent = threading::Async<bool>(
                depends, [headerDataBase, sourcePath, sourcePathL, originalShaderStr, version = version, formatName, debugSymbol, entryPoint = entryPoint, entryPointL = entryPointL, &code, this, taskNums](const auto&) {
                    QUICK_SCOPED_CPU_TIMING("CompileShaderStageTask");
                    LOG_INFO("[thread: {}] Compile shader: {}, format: {}_{}_{}, stage: {}, entry point: {}",
                             threading::TaskSystem::GetCurrentThreadID(),
                             sourcePath,
                             formatName,
                             version.major_version(),
                             version.minor_version(),
                             "Compute",
                             entryPoint);

                    DirectXShaderCompiler dxc{headerDataBase.get()};

                    auto& stageCode = code->compute_shader;

                    if (version.format() == ShaderCodeFormat::DXIL)
                    {
                        auto result = dxc.Compile(*originalShaderStr, entryPointL, sourcePathL, {}, ShaderStageBit::Compute, version, false, debugSymbol);
                        AddProgress(1.0f / taskNums * 0.8f);
                        if (result)
                        {
                            stageCode->code_data.resize(result->GetBufferSize());
                            std::memcpy(stageCode->code_data.data(), result->GetBufferPointer(), result->GetBufferSize());
                            stageCode->debug_symbol = debugSymbol != DebugMode::None;

                            if (!ReflectForDXIL(result, stageCode.get(), code->layout.get(), code->group_size.get()))
                                return false;
                        }
                        else
                        {
                            return false;
                        }
                    }
                    else
                    {
                        // Use fixed shader model 6_6 to compile SPIR_V
                        CrossSchema::ShaderVersion compileVersion{
                            ShaderCodeFormat::DXIL,
                            6,
                            6
                        };
                        
                        auto result = dxc.Compile(*originalShaderStr, entryPointL, sourcePathL, {}, ShaderStageBit::Compute, ShaderVersion{version.format(), compileVersion.major_version(), compileVersion.minor_version()}, false, debugSymbol);
                        AddProgress(1.0f / taskNums * 0.8f);
                        if (result)
                        {
                            auto dxilStageLayout = std::make_unique<ShaderLayoutT>();

                            auto size = result->GetBufferSize();
                            stageCode->code_data.resize(result->GetBufferSize());
                            std::memcpy(stageCode->code_data.data(), result->GetBufferPointer(), result->GetBufferSize());
                            stageCode->debug_symbol = debugSymbol != DebugMode::None;

                            uint3 groupSize{};
                            ReflectForOtherFormat(stageCode.get(), version, dxilStageLayout.get(), code->layout.get(), &groupSize);
                            code->group_size->mutate_x(groupSize.x());
                            code->group_size->mutate_y(groupSize.y());
                            code->group_size->mutate_z(groupSize.z());
                        }
                        else
                        {
                            return false;
                        }
                    }

                    if (auto& specConsts = code->layout->specialization_constants; specConsts)
                    {
                        specConsts->name = "__SpecializationConstants__";
                        // sort specialization constants and calculate packed size, for spirv / essl / msl
                        if (specConsts->size == 0)
                        {
                            if (auto maxIndexcb = std::max_element(code->layout->constant_buffers.cbegin(), code->layout->constant_buffers.cend(), [](auto& a, auto& b) { return a->index < b->index; });
                                maxIndexcb != code->layout->constant_buffers.cend())
                            {
                                specConsts->index = (*maxIndexcb)->index + 1;
                            }

                            Assert(specConsts->struct_type);
                            std::sort(specConsts->struct_type->members.begin(), specConsts->struct_type->members.end(), [](auto& a, auto& b) { return a->index < b->index; });
                            if (version.format() == ShaderCodeFormat::SPIR_V)
                            {
                                auto end = std::unique(specConsts->struct_type->members.begin(), specConsts->struct_type->members.end(), [](auto& a, auto& b) { return a->index == b->index; });
                                if (end != specConsts->struct_type->members.end())
                                {
                                    LOG_EDITOR_ERROR("Duplicated shader constant index!");
                                    return false;
                                }
                            }
                            UInt32 offset = 0;
                            for (const auto& member : specConsts->struct_type->members)
                            {
                                member->offset = offset;
                                offset += member->size;
                            }
                            specConsts->size = offset;
                            specConsts->struct_type->size = offset;
                        }
                    }

                    return true;
                });

            kernelCompilationTaskEvents->Add(compilationEvent);
            lastCompilationEvent = compilationEvent;
        }

        output->platform_shaders.emplace_back(std::move(platformShader));
    }

    return threading::Async<bool>(*kernelCompilationTaskEvents, [output, headerDataBase, kernelCompilationTaskEvents, sourcePath, ndaSavePath, this](const auto&) {
        auto allTrue = true;
        kernelCompilationTaskEvents->Traverse([&allTrue](const auto& taskEventPtr)
        {
            if (!taskEventPtr->GetReturnValue<bool>())
            {
                allTrue = false;
                return true;
            }
            return false;
        });
        LOG_INFO("Shader: {} Compilation {}", sourcePath, allTrue ? "Succeeded" : "Failed");

        if (allTrue)
        {
            auto typedPath = ndaSavePath;
            ComputeShaderPtr shader = gResourceMgr.CreateResourceAs<resource::ComputeShader>();
            shader->CreateAsset(typedPath);
            shader->Serialize(*output);
            if (mImportSettings->GenShaderMaps)
            {
                headerDataBase->RecordFinish(shader->GetGuid_Str(), true, mImportSettings->GenShaderMapsDebugInfo);
            }
        }
        else if (mImportSettings->GenShaderMaps)
        {
            auto fileSystem = EngineGlobal::GetFileSystem();
            const std::string ndaPath = fileSystem->GetAbsolutePath(ndaSavePath);
            const std::string guidStr = gResourceMgr.GetGuidByPath(fileSystem->GetRelativePath(ndaPath));
            if (!guidStr.empty())
            {
                headerDataBase->RecordFinish(guidStr, true, mImportSettings->GenShaderMapsDebugInfo);
            }
        }

        SetProgress(0.f);
        return allTrue;
    });
}

void cross::editor::ComputeShaderImporter::ImportAssetOnDemand(const std::string& sourcePath, const std::string& ndaSavePath)
{
    using namespace CrossSchema;

    QUICK_SCOPED_CPU_TIMING("CompileShader");
    TICK(CompileShader);

    // Load File
    std::ifstream file(sourcePath);
    if (!file)
    {
        LOG_EDITOR_ERROR("[ShaderCompile] Fail to open file: {}", sourcePath);
        return;
    }

    SetProgress(0.01f);

    bool hasCache = false;
    const auto typedPath = ndaSavePath;
    // const auto typedPath = std::filesystem::path{sourcePath}.replace_extension(".compute.nda").string();

    std::vector<char> cacheBuff{0};
    std::unique_ptr<ComputeShaderAssetT> shader = std::make_unique<ComputeShaderAssetT>();

    // Try to use cache if exist
    {
        std::ifstream ndaFile{typedPath, std::ios::binary | std::ios::in};
        if (ndaFile)
        {
            cross::resource::LoadNDAFileInfo info;
            gResourceAssetMgr.GetLoadNDAInfo(typedPath.c_str(), info);

            hasCache = true;

            ndaFile.seekg(0, std::ios::end);
            const auto bufSize = ndaFile.tellg();
            const auto headerSize = info.HasMetaHeader() ? info.GetMetaHeader().mJsonStringLength : 0;
            const auto dataSize = bufSize - std::streampos{headerSize};

            cacheBuff.resize(dataSize);
            ndaFile.seekg(headerSize, std::ios::beg);
            ndaFile.read(cacheBuff.data(), dataSize);
            ndaFile.close();

            flatbuffers::Verifier verifier(reinterpret_cast<UInt8*>(cacheBuff.data()), dataSize);
            if (CrossSchema::VerifyResourceAssetBuffer(verifier))
            {
                auto* res = CrossSchema::GetResourceAsset(cacheBuff.data());
                shader.reset(res->resource_as_ComputeShaderAsset()->UnPack());
            }
        }
    }

    // The source file modify time stamp
    const std::time_t mtime = [&sourcePath]() {
        struct stat st;
        if (stat(sourcePath.c_str(), &st) == 0)
        {
            return st.st_mtime;
        }
        return std::time_t{0};
    }();

    // Compare file modify time
    auto hasSameMtime = [&hasCache, &mtime, &sourcePath](const std::time_t mtimeInVariant) { return hasCache && mtime == mtimeInVariant; };

    auto originalShaderStr = std::make_shared<std::string>((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());

    TICK(ParseStage_Compute);

    ShaderCompileCommand compileCommand{};
    ParseShaderCompileCommand(originalShaderStr, compileCommand);
    auto& [curShaderFormats, entryPoints, keywords, usages, debugMode, onDemandCompilation] = compileCommand;

    TOCK(ParseStage_Compute);

    SetProgress(0.2f);
    // Compare shader version
    auto compareShaderVersion = [](const CrossSchema::ShaderVersion& left, const CrossSchema::ShaderVersion& right) {
        return left.format() == right.format() && left.major_version() == right.major_version() && left.minor_version() == right.minor_version();
    };

    const auto targetVersion = *reinterpret_cast<CrossSchema::ShaderVersion*>(&mImportSettings->Version);
    auto formatIterator = std::find_if(mShaderPlatform.begin(), mShaderPlatform.end(), [&targetVersion](const auto& pair) {
        return targetVersion.format() == std::get<0>(pair.second).format() &&
            targetVersion.major_version() == std::get<0>(pair.second).major_version() &&
                targetVersion.minor_version() == std::get<0>(pair.second).minor_version();
    });

    if (formatIterator == mShaderPlatform.end())
    {
        LOG_EDITOR_ERROR("[ShaderCompile] Unsupported shader format: {}_{}_{}", EnumNameShaderCodeFormat(targetVersion.format()), targetVersion.major_version(), targetVersion.minor_version());
        return;
    }

    SetProgress(0.5f);
    if (formatIterator != mShaderPlatform.end())
    {
        const auto& [version, defs] = formatIterator->second;

        CrossSchema::PlatformComputeShaderT* platformShader = nullptr;

        if (hasCache)
        {
            for (auto begin = shader->platform_shaders.begin(); begin != shader->platform_shaders.end(); begin++)
            {
                const auto v = begin->get()->version.get();
                if (compareShaderVersion(*v, version))
                {
                    platformShader = begin->get();
                    break;
                }
            }
        }

        if (!platformShader)
        {
            hasCache = false;
            platformShader = shader->platform_shaders.emplace_back(std::make_unique<PlatformComputeShaderT>()).get();
            platformShader->version = std::make_unique<ShaderVersion>(version);
        }

        if (platformShader->code_list.size() && hasSameMtime(platformShader->code_list[0]->mtime))
        {
            LOG_INFO("[ShaderCompile] Found compute shader cache, Skip compile {}", sourcePath.c_str());
            return;
        }
        else
        {
            platformShader->code_list.clear();
        }

        auto headerDataBase = std::make_shared<IncludeHandler>(sourcePath.c_str());

        bool ret = GenKernels(platformShader, mtime, *originalShaderStr, version, entryPoints, headerDataBase.get(), debugMode);

        SetProgress(0.8f);

        LOG_EDITOR_INFORMATION("Shader: {} Compilation {}", sourcePath, ret ? "Succeeded" : "Failed");

        TICK(SerializeStage_Compute);

        if (ret)
        {
            ComputeShaderPtr compute = gResourceMgr.CreateResourceAs<resource::ComputeShader>();
            compute->CreateAsset(typedPath);
            compute->Serialize(*shader);
            if (mImportSettings->GenShaderMaps)
            {
                headerDataBase->RecordFinish(compute->GetGuid_Str(), true, mImportSettings->GenShaderMapsDebugInfo);
            }
        }
        else if (mImportSettings->GenShaderMaps)
        {
            auto fileSystem = EngineGlobal::GetFileSystem();
            const std::string ndaPath = fileSystem->GetAbsolutePath(ndaSavePath);
            const std::string guidStr = gResourceMgr.GetGuidByPath(fileSystem->GetRelativePath(ndaPath));
            if (!guidStr.empty())
            {
                headerDataBase->RecordFinish(guidStr, true, mImportSettings->GenShaderMapsDebugInfo);
            }
        }

        TOCK(SerializeStage_Compute);
    }
    else
    {
        LOG_EDITOR_ERROR("[ShaderCompile] Unsupported compute shader format: {}_{}_{}", EnumNameShaderCodeFormat(targetVersion.format()), targetVersion.major_version(), targetVersion.minor_version());
    }
    SetProgress(0.0f);
    TOCK(CompileShader);
}

void cross::editor::ComputeShaderImporter::GenShaderOnDemand(const std::string& code, const std::string& ndaSavePath)
{
    using namespace CrossSchema;

    QUICK_SCOPED_CPU_TIMING("CompileShader");
    TICK(CompileShader);

    SetProgress(0.01f);

    bool hasCache = false;
    const auto typedPath = ndaSavePath;
    // const auto typedPath = std::filesystem::path{sourcePath}.replace_extension(".compute.nda").string();

    std::vector<char> cacheBuff{0};
    std::unique_ptr<ComputeShaderAssetT> shader = std::make_unique<ComputeShaderAssetT>();

    // Try to use cache if exist
    {
        std::ifstream ndaFile{typedPath, std::ios::binary | std::ios::in};
        if (ndaFile)
        {
            cross::resource::LoadNDAFileInfo info;
            gResourceAssetMgr.GetLoadNDAInfo(typedPath.c_str(), info);

            hasCache = true;

            ndaFile.seekg(0, std::ios::end);
            const auto bufSize = ndaFile.tellg();
            const auto headerSize = info.HasMetaHeader() ? info.GetMetaHeader().mJsonStringLength : 0;
            const auto dataSize = bufSize - std::streampos{headerSize};

            cacheBuff.resize(dataSize);
            ndaFile.seekg(headerSize, std::ios::beg);
            ndaFile.read(cacheBuff.data(), dataSize);
            ndaFile.close();

            flatbuffers::Verifier verifier(reinterpret_cast<UInt8*>(cacheBuff.data()), dataSize);
            if (CrossSchema::VerifyResourceAssetBuffer(verifier))
            {
                auto* res = CrossSchema::GetResourceAsset(cacheBuff.data());
                shader.reset(res->resource_as_ComputeShaderAsset()->UnPack());
            }
        }
    }

    TICK(ParseStage_Compute);

    ShaderCompileCommand compileCommand{};
    ParseShaderCompileCommand(code, compileCommand);
    auto& [curShaderFormats, entryPoints, keywords, usages, debugMode, onDemandCompilation] = compileCommand;

    TOCK(ParseStage_Compute);

    SetProgress(0.2f);
    // Compare shader version
    auto compareShaderVersion = [](const CrossSchema::ShaderVersion& left, const CrossSchema::ShaderVersion& right) {
        return left.format() == right.format() && left.major_version() == right.major_version() && left.minor_version() == right.minor_version();
    };

    const auto targetVersion = *reinterpret_cast<CrossSchema::ShaderVersion*>(&mImportSettings->Version);
    auto formatIterator = std::find_if(mShaderPlatform.begin(), mShaderPlatform.end(), [&targetVersion](const auto& pair) {
        return targetVersion.format() == std::get<0>(pair.second).format() &&
            targetVersion.major_version() == std::get<0>(pair.second).major_version() &&
                targetVersion.minor_version() == std::get<0>(pair.second).minor_version();
    });
    if (formatIterator == mShaderPlatform.end())
    {
        LOG_EDITOR_ERROR("[ShaderCompile] Unsupported shader format: {}_{}_{}", EnumNameShaderCodeFormat(targetVersion.format()), targetVersion.major_version(), targetVersion.minor_version());
        return;
    }

    SetProgress(0.5f);
    if (formatIterator != mShaderPlatform.end())
    {
        const auto& [version, defs] = formatIterator->second;

        CrossSchema::PlatformComputeShaderT* platformShader = nullptr;

        if (hasCache)
        {
            for (auto begin = shader->platform_shaders.begin(); begin != shader->platform_shaders.end(); begin++)
            {
                const auto v = begin->get()->version.get();
                if (compareShaderVersion(*v, version))
                {
                    platformShader = begin->get();
                    break;
                }
            }
        }

        if (!platformShader)
        {
            hasCache = false;
            platformShader = shader->platform_shaders.emplace_back(std::make_unique<PlatformComputeShaderT>()).get();
            platformShader->version = std::make_unique<ShaderVersion>(version);
        }

        platformShader->code_list.clear();

        auto headerDataBase = std::make_shared<IncludeHandler>(ndaSavePath.c_str());
        bool ret = GenKernels(platformShader, std::time_t{0}, code.c_str(), version, entryPoints, headerDataBase.get(), debugMode);

        SetProgress(0.8f);

        LOG_EDITOR_INFORMATION("Compute shader nda: {} Compilation {}", ndaSavePath, ret ? "Succeeded" : "Failed");

        TICK(SerializeStage_Compute);

        if (ret)
        {
            ComputeShaderPtr compute = gResourceMgr.CreateResourceAs<resource::ComputeShader>();
            compute->CreateAsset(typedPath);
            compute->Serialize(*shader);
            if (mImportSettings->GenShaderMaps)
            {
                headerDataBase->RecordFinish(compute->GetGuid_Str(), true, mImportSettings->GenShaderMapsDebugInfo);
            }
        }
        else if (mImportSettings->GenShaderMaps)
        {
            auto fileSystem = EngineGlobal::GetFileSystem();
            const std::string ndaPath = fileSystem->GetAbsolutePath(ndaSavePath);
            const std::string guidStr = gResourceMgr.GetGuidByPath(fileSystem->GetRelativePath(ndaPath));

            LOG_INFO("{} {}", ndaPath, fileSystem->GetRelativePath(ndaPath));
            if (!guidStr.empty())
            {
                headerDataBase->RecordFinish(guidStr, true, mImportSettings->GenShaderMapsDebugInfo);
            }
        }

        TOCK(SerializeStage_Compute);
    }
    else
    {
        LOG_EDITOR_ERROR("[ShaderCompile] Unsupported compute shader format: {}_{}_{}", EnumNameShaderCodeFormat(targetVersion.format()), targetVersion.major_version(), targetVersion.minor_version());
    }
    SetProgress(0.0f);
    TOCK(CompileShader);
}

bool cross::editor::ComputeShaderImporter::GenKernels(CrossSchema::PlatformComputeShaderT* shader, const std::time_t mtime, const std::string& shaderCode, const CrossSchema::ShaderVersion& version,
                                                      const std::vector<std::tuple<CrossSchema::ShaderStageBit, std::string, std::wstring>>& entrys, IncludeHandler* includer, const DebugMode debugSymbol)
{
    TICK(GenKernelsStage);
    using namespace CrossSchema;
    const auto sourcePath = includer->GetFilePath().string();
    const auto* formatName = ShaderCodeFormatTypeTable()->names[static_cast<UInt32>(version.format())];
    auto stageLayoutVec = std::vector<std::unique_ptr<ShaderLayoutT>>();
    for (const auto& [stageBit, entryPoint, entryPointL] : entrys)
    {
        LOG_INFO("[ShaderCompile] Compile shader: {}, format: {}_{}_{}, stage: Compute, entry point: {}", sourcePath.c_str(), formatName, version.major_version(), version.minor_version(), UTF16toUTF8(entryPointL).c_str());

        auto& code = shader->code_list.emplace_back(std::make_unique<ComputeShaderCodeT>());
        // auto code = std::make_unique<ComputeShaderCodeT>();
        {
            auto uuid = CrossUUID::GenerateCrossUUID();
            code->mtime = mtime;
            code->guid = std::make_unique<CrossSchema::GUID>(uuid.low, uuid.high);
            code->group_size = std::make_unique<uint3>();
            code->layout = std::make_unique<ShaderLayoutT>();
            code->compute_shader = std::make_unique<ShaderCodeT>();
            code->compute_shader->entry_point = UTF16toUTF8(entryPointL);
            code->compute_shader->stage = ShaderStageBit::Compute;
        }

        auto& stageCode = code->compute_shader;
        auto sourcePathL = UTF8toUTF16(sourcePath);
        if (version.format() == ShaderCodeFormat::DXIL)
        {
            DirectXShaderCompiler dxc{includer};
            auto result = dxc.Compile(shaderCode.c_str(), entryPointL, sourcePathL, {}, ShaderStageBit::Compute, version, false, debugSymbol);

            if (result)
            {
                stageCode->code_data.resize(result->GetBufferSize());
                std::memcpy(stageCode->code_data.data(), result->GetBufferPointer(), result->GetBufferSize());
                stageCode->debug_symbol = debugSymbol != DebugMode::None;

                if (!ReflectForDXIL(result, stageCode.get(), code->layout.get(), code->group_size.get()))
                    return false;
            }
            else
            {
                return false;
            }
        }
        else
        {
            DirectXShaderCompiler dxc{includer};
            auto result = dxc.Compile(shaderCode.c_str(), entryPointL, sourcePathL, {}, ShaderStageBit::Compute, ShaderVersion{version.format(), version.major_version(), version.minor_version()}, false, debugSymbol);

            if (result)
            {
                auto dxilStageLayout = std::make_unique<ShaderLayoutT>();
                stageCode->code_data.resize(result->GetBufferSize());
                std::memcpy(stageCode->code_data.data(), result->GetBufferPointer(), result->GetBufferSize());
                stageCode->debug_symbol = debugSymbol != DebugMode::None;

                uint3 groupSize{};
                ReflectForOtherFormat(stageCode.get(), version, dxilStageLayout.get(), code->layout.get(), &groupSize);
                code->group_size->mutate_x(groupSize.x());
                code->group_size->mutate_y(groupSize.y());
                code->group_size->mutate_z(groupSize.z());
            }
            else
            {
                return false;
            }
        }

        if (auto& specConsts = code->layout->specialization_constants; specConsts)
        {
            specConsts->name = "__SpecializationConstants__";
            // sort specialization constants and calculate packed size, for spirv / essl / msl
            if (specConsts->size == 0)
            {
                if (auto maxIndexcb = std::max_element(code->layout->constant_buffers.cbegin(), code->layout->constant_buffers.cend(), [](auto& a, auto& b) { return a->index < b->index; });
                    maxIndexcb != code->layout->constant_buffers.cend())
                {
                    specConsts->index = (*maxIndexcb)->index + 1;
                }

                std::sort(specConsts->members.begin(), specConsts->members.end(), [](auto& a, auto& b) { return a->index < b->index; });
                if (version.format() == ShaderCodeFormat::SPIR_V)
                {
                    auto end = std::unique(specConsts->members.begin(), specConsts->members.end(), [](auto& a, auto& b) { return a->index == b->index; });
                    if (end != specConsts->members.end())
                    {
                        LOG_EDITOR_ERROR("Duplicated shader constant index!");
                        return false;
                    }
                }
                UInt32 offset = 0;
                for (auto& member : specConsts->members)
                {
                    member->offset = offset;
                    offset += member->size;
                }
                specConsts->size = offset;
            }
        }
    }

    TOCK(GenKernelsStage);
    return true;
}

RayTracingShaderImporter::RayTracingShaderImporter(): ShaderImporter(AssetType::RayTracingShader)
{
    
}

void RayTracingShaderImporter::ImportAssetOnDemand(const std::string& sourcePath, const std::string& ndaSavePath)
{
    using namespace CrossSchema;

    QUICK_SCOPED_CPU_TIMING("CompileRayTracingShader");
    TICK(CompileRayTracingShader);

    std::ifstream file(sourcePath);
    if (!file)
    {
        LOG_EDITOR_ERROR("[ShaderCompile] Fail to open file: {}", sourcePath);
        return;
    }
    SetProgress(0.01f);

    bool hasCache = false;
    const auto typedPath = ndaSavePath;

    std::vector<char> cacheBuff{0};
    std::unique_ptr<RayTracingShaderAssetT> shader = std::make_unique<RayTracingShaderAssetT>();

    // Try to use cache if exist
    {
        std::ifstream ndaFile{typedPath, std::ios::binary | std::ios::in};
        if (ndaFile)
        {
            cross::resource::LoadNDAFileInfo info;
            gResourceAssetMgr.GetLoadNDAInfo(typedPath.c_str(), info);

            hasCache = true;

            ndaFile.seekg(0, std::ios::end);
            const auto bufSize = ndaFile.tellg();
            const auto headerSize = info.HasMetaHeader() ? info.GetMetaHeader().mJsonStringLength : 0;
            const auto dataSize = bufSize - std::streampos{headerSize};

            cacheBuff.resize(dataSize);
            ndaFile.seekg(headerSize, std::ios::beg);
            ndaFile.read(cacheBuff.data(), dataSize);
            ndaFile.close();

            flatbuffers::Verifier verifier(reinterpret_cast<UInt8*>(cacheBuff.data()), dataSize);
            if (VerifyResourceAssetBuffer(verifier))
            {
                auto* res = GetResourceAsset(cacheBuff.data());
                shader.reset(res->resource_as_RayTracingShaderAsset()->UnPack());
            }
        }
    }

    // The source file modify time stamp
    const std::time_t mtime = [&sourcePath]() {
        struct stat st;
        if (stat(sourcePath.c_str(), &st) == 0)
        {
            return st.st_mtime;
        }
        return std::time_t{0};
    }();

    // Compare file modify time
    auto hasSameMtime = [&hasCache, &mtime, &sourcePath](const std::time_t mtimeInVariant) { return hasCache && mtime == mtimeInVariant; };
    
    auto originalShaderStr = std::make_shared<std::string>((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());

    TICK(ParseStage_RayTracing);
    ShaderCompileCommand compileCommand{};
    ParseShaderCompileCommand(originalShaderStr, compileCommand);
    auto& [curShaderFormats, entryPoints, keywords, usages, debugMode, onDemandCompilation] = compileCommand;
    TOCK(ParseStage_RayTracing);
    
    SetProgress(0.2f);

    // Compare shader version
    auto compareShaderVersion = [](const CrossSchema::ShaderVersion& left, const CrossSchema::ShaderVersion& right) {
        return left.format() == right.format() && left.major_version() == right.major_version() && left.minor_version() == right.minor_version();
    };

    const auto targetVersion = *reinterpret_cast<CrossSchema::ShaderVersion*>(&mImportSettings->Version);
    auto formatIterator = std::find_if(mShaderPlatform.begin(), mShaderPlatform.end(), [&targetVersion](const auto& pair) {
        return targetVersion.format() == std::get<0>(pair.second).format() &&
            targetVersion.major_version() == std::get<0>(pair.second).major_version() &&
                targetVersion.minor_version() == std::get<0>(pair.second).minor_version();
    });

    if (formatIterator == mShaderPlatform.end())
    {
        LOG_EDITOR_ERROR("[ShaderCompile] Unsupported shader format: {}_{}_{}", EnumNameShaderCodeFormat(targetVersion.format()), targetVersion.major_version(), targetVersion.minor_version());
        return;
    }

    SetProgress(0.5f);

    if (formatIterator != mShaderPlatform.end())
    {
        const auto& [version, defs] = formatIterator->second;
        PlatformRayTracingShaderT* platformShader = nullptr;

        if (hasCache)
        {
            for (auto begin = shader->platform_shaders.begin(); begin != shader->platform_shaders.end(); ++begin)
            {
                if (compareShaderVersion(*begin->get()->version.get(), version))
                {
                    platformShader = begin->get();
                    break;
                }
            }
        }

        if (!platformShader)
        {
            hasCache = false;
            platformShader = shader->platform_shaders.emplace_back(std::make_unique<PlatformRayTracingShaderT>()).get();
            platformShader->version = std::make_unique<ShaderVersion>(version);
        }
        
        if (platformShader->shader_code && hasSameMtime(platformShader->shader_code->mtime))
        {
            LOG_INFO("[ShaderCompile] Found raytracing shader cache, Skip compile {}", sourcePath.c_str());
            return;
        }
        else
        {
            platformShader->shader_code = std::make_unique<RayTracingShaderCodeT>();
        }

        auto headerDataBase = std::make_unique<IncludeHandler>(sourcePath.c_str());

        bool ret = GenKernels(platformShader, mtime, *originalShaderStr, version, entryPoints, headerDataBase.get(), debugMode);
        SetProgress(0.8f);

        LOG_EDITOR_INFORMATION("RayTracing shader nda: {} Compilation {}", ndaSavePath, ret ? "Succeeded" : "Failed");
        
        TICK(SerializeStage_RayTracing);

        if (ret)
        {
            RayTracingShaderPtr raytracing = gResourceMgr.CreateResourceAs<resource::RayTracingShader>();
            raytracing->CreateAsset(typedPath);
            raytracing->Serialize(*shader);
            if (mImportSettings->GenShaderMaps)
            {
                headerDataBase->RecordFinish(raytracing->GetGuid_Str(), true, mImportSettings->GenShaderMapsDebugInfo);
            }
        }
        else if (mImportSettings->GenShaderMaps)
        {
            auto fileSystem = EngineGlobal::GetFileSystem();
            const std::string ndaPath = fileSystem->GetAbsolutePath(ndaSavePath);
            const std::string guidStr = gResourceMgr.GetGuidByPath(fileSystem->GetRelativePath(ndaPath));
            
            LOG_INFO("{} {}", ndaPath, fileSystem->GetRelativePath(ndaPath));
            if (!guidStr.empty())
            {
                headerDataBase->RecordFinish(guidStr, true, mImportSettings->GenShaderMapsDebugInfo);
            }
        }
        
        TOCK(SerializeStage_RayTracing);
    }
    else
    {
        LOG_EDITOR_ERROR("[ShaderCompile] Unsupported RayTracing shader format: {}_{}_{}", EnumNameShaderCodeFormat(targetVersion.format()), targetVersion.major_version(), targetVersion.minor_version());
    }
    
    SetProgress(0.0f);
    TOCK(CompileRayTracingShader);
}

bool RayTracingShaderImporter::GenKernels(CrossSchema::PlatformRayTracingShaderT* shader, std::time_t mtime, const std::string& shaderCode, const CrossSchema::ShaderVersion& version,
    const std::vector<std::tuple<CrossSchema::ShaderStageBit, std::string, std::wstring>>& entrys, IncludeHandler* includer, DebugMode debugSymbol)
{
    // TODO(scolu): shader layouts are not merged in this function
    Assert(false);
    
    // TICK(GenKernelStage);
    // using namespace CrossSchema;
    //
    // const auto sourcePath = includer->GetFilePath().string();
    // const auto* formatName = ShaderCodeFormatTypeTable()->names[static_cast<UInt32>(version.format())];
    //
    // auto RTStageToString = [](ShaderStageBit stage) -> std::string {
    //     switch (stage)
    //     {
    //     case ShaderStageBit::RayGen:
    //         return "RayGen";
    //     case ShaderStageBit::ClosestHit:
    //         return "ClosestHit";
    //     case ShaderStageBit::Miss:
    //         return "Miss";
    //     case ShaderStageBit::AnyHit:
    //         return "AnyHit";
    //     case ShaderStageBit::Callable:
    //         return "Callable";
    //     case ShaderStageBit::InterSection:
    //         return "Intersection";
    //     default:
    //         AssertMsg(false, "Incorrect Shader Stage!");
    //         return "Error";
    //     }
    // };
    //
    // auto& rt_shader_code = shader->shader_code;
    // rt_shader_code->mtime = mtime;
    // auto uuid = CrossUUID::GenerateCrossUUID();
    // rt_shader_code->guid = std::make_unique<CrossSchema::GUID>(uuid.low, uuid.high);
    // rt_shader_code->layout = std::make_unique<ShaderLayoutT>();
    //
    // rt_shader_code->code = std::make_unique<ShaderCodeT>();
    // rt_shader_code->code->stage = ShaderStageBit::AllRayTracing;
    //
    // if (version.format() == ShaderCodeFormat::DXIL)
    // {
    //     DirectXShaderCompiler dxc{includer};
    //     auto result = dxc.Compile(shaderCode.c_str(), "", sourcePathL, {},
    //         stageBit, version, false, debugSymbol);
    //
    //     if (result)
    //     {
    //         shader_code->code_data.resize(result->GetBufferSize());
    //         std::memcpy(shader_code->code_data.data(), result->GetBufferPointer(), result->GetBufferSize());
    //         shader_code->debug_symbol = debugSymbol != DebugMode::None;
    //
    //         uint3 groupSize{};
    //         if (!ReflectForDXIL(result, shader_code, rt_shader_code->layout.get(), &groupSize))
    //         {
    //             return false;
    //         }
    //     }
    //     else
    //     {
    //         return false;
    //     }
    // }
    // else
    // {
    //     // Use fixed shader model 6_6 to compile SPIR_V
    //     CrossSchema::ShaderVersion compileVersion{
    //         ShaderCodeFormat::DXIL,
    //         6,
    //         6
    //     };
    //     DirectXShaderCompiler dxc{includer};
    //     auto sourcePathL = UTF8toUTF16(sourcePath);
    //     auto result = dxc.Compile(shaderCode.c_str(), "", sourcePathL, {}, ShaderStageBit::AllRayTracing, ShaderVersion{version.format(), compileVersion.major_version(), compileVersion.minor_version()}, false, debugSymbol);
    //
    //     if (result)
    //     {
    //         auto dxilStageLayout = std::make_unique<ShaderLayoutT>();
    //         
    //         rt_shader_code->code->code_data.resize(result->GetBufferSize());
    //         std::memcpy(rt_shader_code->code->code_data.data(), result->GetBufferPointer(), result->GetBufferSize());
    //         rt_shader_code->code->debug_symbol = debugSymbol != DebugMode::None;
    //
    //         uint3 groupSize{};
    //         ReflectForOtherFormat(rt_shader_code->code.get(), version, dxilStageLayout.get(), rt_shader_code->layout.get(), &groupSize);
    //     }
    //     else
    //     {
    //         return false;
    //     }
    // }
    //
    // for (const auto& [stageBit, entryPoint, entryPointL] : entrys)
    // {
    //     LOG_INFO("[ShaderCompile] Compile shader: {}, format: {}_{}_{}, stage: {}, entry point: {}", sourcePath.c_str(), formatName,
    //         version.major_version(), version.minor_version(), RTStageToString(stageBit), UTF16toUTF8(entryPointL).c_str());
    //     
    //     switch (stageBit)
    //     {
    //     case ShaderStageBit::RayGen:
    //         rt_shader_code->raygen_shader_entry = UTF16toUTF8(entryPointL);
    //         break;
    //     case ShaderStageBit::ClosestHit:
    //         rt_shader_code->closesthit_shader_entries.emplace_back(UTF16toUTF8(entryPointL));
    //         break;
    //     case ShaderStageBit::Miss:
    //         rt_shader_code->miss_shader_entries.emplace_back(UTF16toUTF8(entryPointL));
    //         break;
    //     case ShaderStageBit::AnyHit:
    //         rt_shader_code->anyhit_shader_entries.emplace_back(UTF16toUTF8(entryPointL));
    //         break;
    //     case ShaderStageBit::Callable:
    //         rt_shader_code->callable_shader_entries.emplace_back(UTF16toUTF8(entryPointL));
    //         break;
    //     case ShaderStageBit::InterSection:
    //         rt_shader_code->intersection_shader_entries.emplace_back(UTF16toUTF8(entryPointL));
    //         break;
    //     default:
    //         AssertMsg(false, "Incorrect Shader Stage!");
    //         break;
    //     }
    //     shader_code->stage = stageBit;
    //     
    //     shader_code->entry_point = UTF16toUTF8(entryPointL);
    //     auto sourcePathL = UTF8toUTF16(sourcePath);
    //     if (version.format() == ShaderCodeFormat::DXIL)
    //     {
    //         DirectXShaderCompiler dxc{includer};
    //         auto result = dxc.Compile(shaderCode.c_str(), entryPointL, sourcePathL, {},
    //             stageBit, version, false, debugSymbol);
    //
    //         if (result)
    //         {
    //             shader_code->code_data.resize(result->GetBufferSize());
    //             std::memcpy(shader_code->code_data.data(), result->GetBufferPointer(), result->GetBufferSize());
    //             shader_code->debug_symbol = debugSymbol != DebugMode::None;
    //
    //             uint3 groupSize{};
    //             if (!ReflectForDXIL(result, shader_code, rt_shader_code->layout.get(), &groupSize))
    //             {
    //                 return false;
    //             }
    //         }
    //         else
    //         {
    //             return false;
    //         }
    //     }
    //     else
    //     {
    //         // Use fixed shader model 6_6 to compile SPIR_V
    //         CrossSchema::ShaderVersion compileVersion{
    //             ShaderCodeFormat::DXIL,
    //             6,
    //             6
    //         };
    //         DirectXShaderCompiler dxc{includer};
    //         auto result = dxc.Compile(shaderCode.c_str(), entryPointL, sourcePathL, {},
    //             stageBit, ShaderVersion{version.format(), compileVersion.major_version(), compileVersion.minor_version()}, false, debugSymbol);
    //
    //         if (result)
    //         {
    //             auto dxilStageLayout = std::make_unique<ShaderLayoutT>();
    //             
    //             shader_code->code_data.resize(result->GetBufferSize());
    //             std::memcpy(shader_code->code_data.data(), result->GetBufferPointer(), result->GetBufferSize());
    //             shader_code->debug_symbol = debugSymbol != DebugMode::None;
    //
    //             uint3 groupSize{};
    //             ReflectForOtherFormat(shader_code, version, dxilStageLayout.get(), rt_shader_code->layout.get(), &groupSize);
    //         }
    //         else
    //         {
    //             return false;
    //         }
    //     }
    // }
    //
    // if (auto& specConsts = rt_shader_code->layout->specialization_constants; specConsts)
    // {
    //     specConsts->name = "__SpecializationConstants__";
    //     // sort specialization constants and calculate packed size, for spirv / essl / msl
    //     if (specConsts->size == 0)
    //     {
    //         if (auto maxIndexcb = std::max_element(rt_shader_code->layout->constant_buffers.cbegin(),
    //             rt_shader_code->layout->constant_buffers.cend(), [](auto& a, auto& b) { return a->index < b->index; });
    //             maxIndexcb != rt_shader_code->layout->constant_buffers.cend())
    //         {
    //             specConsts->index = (*maxIndexcb)->index + 1;
    //         }
    //
    //         std::sort(specConsts->members.begin(), specConsts->members.end(), [](auto& a, auto& b) { return a->index < b->index; });
    //         if (version.format() == ShaderCodeFormat::SPIR_V)
    //         {
    //             auto end = std::unique(specConsts->members.begin(), specConsts->members.end(), [](auto& a, auto& b) { return a->index == b->index; });
    //             if (end != specConsts->members.end())
    //             {
    //                 LOG_EDITOR_ERROR("Duplicated shader constant index!");
    //                 return false;
    //             }
    //         }
    //         UInt32 offset = 0;
    //         for (auto& member : specConsts->members)
    //         {
    //             member->offset = offset;
    //             offset += member->size;
    //         }
    //         specConsts->size = offset;
    //     }
    // }
    //
    // TOCK(GenKernelStage);
    return true;
}

threading::TaskEventPtr RayTracingShaderImporter::ImportAssetAsync(const std::string& sourcePath, const std::string& ndaSavePath)
{
    using namespace CrossSchema;

    auto output = std::make_shared<RayTracingShaderAssetT>();

    QUICK_SCOPED_CPU_TIMING("CompileRayTracingShader");

    std::ifstream file(sourcePath);
    if (!file)
    {
        return threading::Async<bool>([sourcePath](const auto&)->bool {
            LOG_EDITOR_ERROR("Fail to open file: {}", sourcePath);
            return false;
        });
    }
    SetProgress(0.01f);

    auto originalShaderStr = std::make_shared<std::string>(std::istreambuf_iterator<char>(file), std::istreambuf_iterator<char>());

    static std::regex gPragmaPattern{R"""(^\s*#pragma\s+(\w+)\s+(\w+))"""};

    ShaderCompileCommand compileCommand{};
    ParseShaderCompileCommand(originalShaderStr, compileCommand);
    auto& [curShaderFormats, entryPoints, keywords, usage, debugSymbol, onDemandCompilation] = compileCommand;

    auto headerDataBase = std::make_shared<IncludeHandler>(sourcePath.c_str());
    auto sourcePathL = UTF8toUTF16(sourcePath);
    SetProgress(0.1f);
    
    auto taskNums = curShaderFormats.size() * entryPoints.size();
    output->platform_shaders.reserve(curShaderFormats.size());

    auto depends = std::make_shared<threading::TaskEventArray>();
    for (const auto& [version, defs] : curShaderFormats)
    {
        auto* formatName = ShaderCodeFormatTypeTable()->names[static_cast<UInt32>(version.format())];
        auto platformShader = std::make_unique<PlatformRayTracingShaderT>();
        platformShader->version = std::make_unique<ShaderVersion>(version);
        platformShader->shader_code = std::make_unique<RayTracingShaderCodeT>();
        auto& rt_shader_code = platformShader->shader_code;
        
        auto compilationEvent = threading::Async<bool>([formatName, headerDataBase, sourcePath, sourcePathL, originalShaderStr, version = version,
            debugSymbol, entryPoints, &rt_shader_code, this](const auto&) {
            LOG_INFO("[thread: {}] Compile shader: {}, format: {}_{}_{}, stage: {}, entry point: {}",
            threading::TaskSystem::GetCurrentThreadID(),
            sourcePath,
            formatName,
            version.major_version(),
            version.minor_version(),
            "AllRayTracing",
            "AllRayTracingShaders");

            auto uuid = CrossUUID::GenerateCrossUUID();
            rt_shader_code->guid = std::make_unique<CrossSchema::GUID>(uuid.low, uuid.high);
            rt_shader_code->layout = std::make_unique<ShaderLayoutT>();

            rt_shader_code->code = std::make_unique<ShaderCodeT>();
            rt_shader_code->code->stage = ShaderStageBit::AllRayTracing;

            if (version.format() == ShaderCodeFormat::DXIL)
            {
                DirectXShaderCompiler dxc(headerDataBase.get());
                auto result = dxc.Compile(*originalShaderStr,L"", sourcePathL, {}, ShaderStageBit::AllRayTracing, version, false, debugSymbol);

                if (result)
                {
                    rt_shader_code->code->code_data.resize(result->GetBufferSize());
                    std::memcpy(rt_shader_code->code->code_data.data(), result->GetBufferPointer(), result->GetBufferSize());
                    rt_shader_code->code->debug_symbol = debugSymbol != DebugMode::None;

                    uint3 groupSize{};
                    if (!ReflectForDXIL(result, rt_shader_code->code.get(), rt_shader_code->layout.get(), &groupSize))
                    {
                        return false;
                    }
                }
                else
                {
                    return false;
                }
            }
            else
            {
                // Use fixed shader model 6_6 to compile SPIR_V
                CrossSchema::ShaderVersion compileVersion{
                    ShaderCodeFormat::DXIL,
                    6,
                    6
                };
                DirectXShaderCompiler dxc(headerDataBase.get());
                auto result = dxc.Compile(*originalShaderStr, L"", sourcePathL, {}, ShaderStageBit::AllRayTracing, version, false, debugSymbol);
                
                if (result)
                {
                    auto dxilStageLayout = std::make_unique<ShaderLayoutT>();
                    
                    rt_shader_code->code->code_data.resize(result->GetBufferSize());
                    std::memcpy(rt_shader_code->code->code_data.data(), result->GetBufferPointer(), result->GetBufferSize());

                    // write rt_shader_code->code->code_data to file
                    std::ofstream ostrm("D:\\test.spv", std::ios::out | std::ios::binary);
                    ostrm.write(reinterpret_cast<char*>(rt_shader_code->code->code_data.data()), rt_shader_code->code->code_data.size());
                    ostrm.close();
                    
                    rt_shader_code->code->debug_symbol = debugSymbol != DebugMode::None;

                    uint3 groupSize{};
                    ReflectForOtherFormat(rt_shader_code->code.get(), version, dxilStageLayout.get(), rt_shader_code->layout.get(), &groupSize);
                }
                else
                {
                    return false;
                }
            }

            if (auto& specConsts = rt_shader_code->layout->specialization_constants; specConsts)
            {
                specConsts->name = "__SpecializationConstants__";
                // sort specialization constants and calculate packed size, for spirv / essl / msl
                if (specConsts->size == 0)
                {
                    if (auto maxIndexcb = std::max_element(rt_shader_code->layout->constant_buffers.cbegin(),
                        rt_shader_code->layout->constant_buffers.cend(), [](auto& a, auto& b) { return a->index < b->index; });
                        maxIndexcb != rt_shader_code->layout->constant_buffers.cend())
                    {
                        specConsts->index = (*maxIndexcb)->index + 1;
                    }

                    std::sort(specConsts->members.begin(), specConsts->members.end(), [](auto& a, auto& b) { return a->index < b->index; });
                    if (version.format() == ShaderCodeFormat::SPIR_V)
                    {
                        auto end = std::unique(specConsts->members.begin(), specConsts->members.end(), [](auto& a, auto& b) { return a->index == b->index; });
                        if (end != specConsts->members.end())
                        {
                            LOG_EDITOR_ERROR("Duplicated shader constant index!");
                            return false;
                        }
                    }
                    UInt32 offset = 0;
                    for (auto& member : specConsts->members)
                    {
                        member->offset = offset;
                        offset += member->size;
                    }
                    specConsts->size = offset;
                }
            }

            for (const auto& [stageBit, entryPoint, entryPointL] : entryPoints)
            {
                switch (stageBit)
                {
                case ShaderStageBit::RayGen:
                    rt_shader_code->raygen_shader_entry = UTF16toUTF8(entryPointL);
                    break;
                case ShaderStageBit::ClosestHit:
                    rt_shader_code->closesthit_shader_entries.emplace_back(UTF16toUTF8(entryPointL));
                    break;
                case ShaderStageBit::Miss:
                    rt_shader_code->miss_shader_entries.emplace_back(UTF16toUTF8(entryPointL));
                    break;
                case ShaderStageBit::AnyHit:
                    rt_shader_code->anyhit_shader_entries.emplace_back(UTF16toUTF8(entryPointL));
                    break;
                case ShaderStageBit::Callable:
                    rt_shader_code->callable_shader_entries.emplace_back(UTF16toUTF8(entryPointL));
                    break;
                case ShaderStageBit::InterSection:
                    rt_shader_code->intersection_shader_entries.emplace_back(UTF16toUTF8(entryPointL));
                    break;
                default:
                    AssertMsg(false, "Incorrect Shader Stage!");
                    break;
                }
            }

            return true;
        });

        depends->Add(compilationEvent);

        output->platform_shaders.emplace_back(std::move(platformShader));
    }

    return threading::Async<bool>(*depends, [output, headerDataBase, sourcePath, ndaSavePath, this, depends](const auto&) -> bool {
        bool allTrue = true;
        depends->Traverse([&allTrue](const auto& taskEventPtr) {
            if (!taskEventPtr->GetReturnValue<bool>())
            {
                allTrue = false;
                return true;
            }
            return false;
        });
        LOG_INFO("Shader: {} Compilation {}", sourcePath, allTrue ? "Succeed" : "Failed");
        
        if (allTrue)
        {
            auto typedPath = ndaSavePath;
            RayTracingShaderPtr raytracing = gResourceMgr.CreateResourceAs<resource::RayTracingShader>();
            raytracing->CreateAsset(typedPath);
            raytracing->Serialize(*output);
            if (mImportSettings->GenShaderMaps)
            {
                headerDataBase->RecordFinish(raytracing->GetGuid_Str(), true, mImportSettings->GenShaderMapsDebugInfo);
            }
        }
        else if (mImportSettings->GenShaderMaps)
        {
            auto fileSystem = EngineGlobal::GetFileSystem();
            const std::string ndaPath = fileSystem->GetAbsolutePath(ndaSavePath);
            const std::string guidStr = gResourceMgr.GetGuidByPath(fileSystem->GetRelativePath(ndaPath));
            
            LOG_INFO("{} {}", ndaPath, fileSystem->GetRelativePath(ndaPath));
            if (!guidStr.empty())
            {
                headerDataBase->RecordFinish(guidStr, true, mImportSettings->GenShaderMapsDebugInfo);
            }
        }

        SetProgress(0.f);
        return allTrue;
    });
}

}   // namespace cross::editor

