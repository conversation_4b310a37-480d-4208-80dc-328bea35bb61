#include "EnginePrefix.h"
#ifdef max
#    undef max
#endif
#include "ShaderReflection.h"
#include "DXILReflection.h"
//#include "dxc/DxilContainer/DxilContainer.h"
#include <regex>
#include <cstdio>
#include <fstream>
#include <filesystem>
#include <cerrno>


using CrossSchema::CombinedSamplerT;
using CrossSchema::DXILReflectionCode;
using CrossSchema::DXILReflectionCodeT;
using CrossSchema::ShaderCodeFormat;
using CrossSchema::ShaderCodeT;
using CrossSchema::ShaderConstantBufferT;
using CrossSchema::ShaderLayoutT;
using CrossSchema::ShaderResourceT;
using CrossSchema::ShaderResourceType;
using CrossSchema::ShaderStageBit;
using CrossSchema::ShaderVariableT;
using CrossSchema::ShaderVariableType;
using CrossSchema::ShaderVariableExT;
using CrossSchema::ShaderStructTypeT;

namespace cross::editor {

inline void CHECK(HRESULT ret)
{
    if (ret != S_OK)
    {
        Assert(false);
        LOG_EDITOR_ERROR("Failure in shader compliation");
    }
}

auto Copy(const std::unique_ptr<ShaderConstantBufferT>& src)
{
    auto dst = std::make_unique<ShaderConstantBufferT>();
    dst->name = src->name;
    dst->type = src->type;
    dst->space = src->space;
    dst->index = src->index;
    dst->size = src->size;
    dst->stage_mask = src->stage_mask;
    dst->members.reserve(src->members.size());
    return dst;
}

auto Copy(const std::unique_ptr<ShaderResourceT>& src)
{
    auto dst = std::make_unique<ShaderResourceT>();
    dst->name = src->name;
    dst->type = src->type;
    dst->array_size = src->array_size;
    dst->space = src->space;
    dst->index = src->index;
    dst->subpass_index = src->subpass_index;
    dst->stage_mask = src->stage_mask;
    dst->return_type = src->return_type;
    dst->depth_texture = src->depth_texture;
    return dst;
}

ShaderVariableType MapVariableType(spirv_cross::SPIRType::BaseType type)
{
    switch (type)
    {
    case spirv_cross::SPIRType::BaseType::Boolean:
        return ShaderVariableType::Bool;
    case spirv_cross::SPIRType::BaseType::SByte:
        return ShaderVariableType::Int8;
    case spirv_cross::SPIRType::BaseType::UByte:
        return ShaderVariableType::UInt8;
    case spirv_cross::SPIRType::BaseType::Short:
        return ShaderVariableType::Int16;
    case spirv_cross::SPIRType::BaseType::UShort:
        return ShaderVariableType::UInt16;
    case spirv_cross::SPIRType::BaseType::Int:
        return ShaderVariableType::Int32;
    case spirv_cross::SPIRType::BaseType::UInt:
        return ShaderVariableType::UInt32;
    case spirv_cross::SPIRType::BaseType::Int64:
        return ShaderVariableType::Int64;
    case spirv_cross::SPIRType::BaseType::UInt64:
        return ShaderVariableType::UInt64;
    case spirv_cross::SPIRType::BaseType::Half:
        return ShaderVariableType::Half;
    case spirv_cross::SPIRType::BaseType::Float:
        return ShaderVariableType::Float;
    case spirv_cross::SPIRType::BaseType::Double:
        return ShaderVariableType::Double;
    case spirv_cross::SPIRType::BaseType::Struct:
        return ShaderVariableType::Struct;
    default:
        LOG_EDITOR_ERROR("Can't map required VariableType: ", type);
        Assert(false);
        return ShaderVariableType::Unknown;
    }
}

struct CompilerEx : public spirv_cross::Compiler
{
    using Compiler::Compiler;

    std::string compile() override
    {
        analyze_image_and_sampler_usage();
        return "";
    }

    bool is_depth_image(const spirv_cross::SPIRType& type, uint32_t id) const
    {
        return (type.image.depth && type.image.format == spv::ImageFormat::ImageFormatUnknown) || comparison_ids.count(id);
    }
};

bool ReflectForSPIR_V(ShaderCodeT* code, const CrossSchema::ShaderVersion& version, ShaderLayoutT* HLSLLayout, ShaderLayoutT* dstLayout, CrossSchema::uint3* groupSize)
{
    using namespace CrossSchema;
    using namespace spirv_cross;

    auto stageMask = ToUnderlying(code->stage);

    CompilerEx compiler{reinterpret_cast<uint32_t*>(code->code_data.data()), code->code_data.size() / 4};
    
    auto ReflectConstantBuffer = [&](auto& bufferObject, std::string_view name, ShaderLayoutT* layout) {
        auto cbufRefRet = std::find_if(HLSLLayout->constant_buffers.begin(), HLSLLayout->constant_buffers.end(), [&](auto& cb) { return cb->name == name; });
        // if (cbufRefRet != HLSLLayout->constant_buffers.end())
        {
            auto cbuffer = std::make_unique<ShaderConstantBufferT>();
            auto& type = compiler.get_type(bufferObject.base_type_id);
            cbuffer->name = compiler.get_name(bufferObject.id);
            cbuffer->type = ShaderResourceType::ConstantBuffer;
            cbuffer->size = static_cast<UInt32>(compiler.get_declared_struct_size(type));
            cbuffer->space = compiler.get_decoration(bufferObject.id, spv::DecorationDescriptorSet);
            cbuffer->index = compiler.get_decoration(bufferObject.id, spv::DecorationBinding);
            cbuffer->stage_mask |= stageMask;
            switch (type.array.size())
            {
            case 0:
                cbuffer->array_size = 1;
                break;
            case 1:
                if (!type.array_size_literal[0])
                {
                    LOG_EDITOR_ERROR("Using shader const as array size was not supported");
                    return false;
                }
                cbuffer->array_size = type.array[0];
                break;
            default:
                return false;
            }

            auto varStructType = std::make_unique<ShaderStructTypeT>();
            for (auto i = 0u; i < type.member_types.size(); i++)
            {
                auto variable = std::make_unique<ShaderVariableExT>();
                variable->name = compiler.get_member_name(bufferObject.base_type_id, i);
                auto& memberType = compiler.get_type(type.member_types[i]);
                variable->type = MapVariableType(memberType.basetype);
                // Boolean type is reflected to BaseType::UInt
                if (cbufRefRet != HLSLLayout->constant_buffers.end())
                {
                    auto& cbufferRef = *cbufRefRet;
                    if (cbufferRef && memberType.basetype == spirv_cross::SPIRType::BaseType::UInt)
                    {
                        // Get variable type from hlsl layout to distinguish boolean type
                        auto varRefRet = std::find_if(cbufferRef->members.begin(), cbufferRef->members.end(), [&](auto& var) { return var->name == variable->name; });
                        if (varRefRet != cbufferRef->members.end())
                        {
                            auto& varRef = *varRefRet;
                            if (varRef && varRef->type == ShaderVariableType::Bool)
                                variable->type = ShaderVariableType::Bool;
                        }
                    }
                }
                variable->offset = compiler.type_struct_member_offset(type, i);
                variable->row_count = memberType.columns;
                variable->col_count = memberType.vecsize;
                variable->size = static_cast<UInt32>(compiler.get_declared_struct_member_size(type, i));
                varStructType->members.emplace_back(std::move(variable));
            }
            varStructType->size = cbuffer->size;
            cbuffer->struct_type = std::move(varStructType);

            layout->constant_buffers.emplace_back(std::move(cbuffer));
            return true;
        }
    };

    auto ReflectResource = [&](const spirv_cross::Resource& resourceObject, std::string_view name, ShaderLayoutT* layout) {
        auto& type = compiler.get_type(resourceObject.type_id);
        auto& baseType = compiler.get_type(resourceObject.base_type_id);

        auto res = std::make_unique<ShaderResourceT>();
        res->name = compiler.get_name(resourceObject.id);
        
        // TODO(peterwjma): not finished yet, uniform bufffer/texture buffer
        auto typeName = compiler.get_name(resourceObject.base_type_id);
        if (typeName.find("RWStructuredBuffer") != std::string::npos)
        {
            res->type = ShaderResourceType::RWStructuredBuffer;
        }
        else if (typeName.find("StructuredBuffer") != std::string::npos)
        {
            res->type = ShaderResourceType::StructuredBuffer;
        }
        else if (typeName.find("RWByteAddressBuffer") != std::string::npos)
        {
            res->type = ShaderResourceType::RWByteAddressBuffer;
        }
        else if (typeName.find("ByteAddressBuffer") != std::string::npos)
        {
            res->type = ShaderResourceType::ByteAddressBuffer;
        }
        /*else if (baseType.basetype == spirv_cross::SPIRType::BaseType::Struct)
        {
            res->type = ShaderResourceType::TextureBuffer;
        }
        else if (baseType.basetype == spirv_cross::SPIRType::BaseType::SampledImage)
        {

        }*/
        else if (baseType.basetype == spirv_cross::SPIRType::BaseType::Image)
        {
            if (baseType.image.sampled == 1)
            {
                switch (baseType.image.dim)
                {
                case spv::Dim1D:
                {
                    res->type = baseType.image.arrayed ? ShaderResourceType::Texture1DArray : ShaderResourceType::Texture1D;
                    break;
                }
                case spv::Dim2D:
                {
                    if (baseType.image.ms)
                    {
                        res->type = baseType.image.arrayed ? ShaderResourceType::Texture2DMSArray : ShaderResourceType::Texture2DMS;
                    }
                    else
                    {
                        res->type = baseType.image.arrayed ? ShaderResourceType::Texture2DArray : ShaderResourceType::Texture2D;
                    }
                    break;
                }
                case spv::Dim3D:
                {
                    res->type = ShaderResourceType::Texture3D;
                    break;
                }
                case spv::DimCube:
                {
                    res->type = baseType.image.arrayed ? ShaderResourceType::TextureCubeArray : ShaderResourceType::TextureCube;
                    break;
                }
                case spv::DimBuffer:
                {
                    res->type = ShaderResourceType::TexelBuffer;
                    break;
                }
                default:
                    Assert(false);
                    break;
                }
            }
            else
            {
                switch (baseType.image.dim)
                {
                case spv::Dim1D:
                {
                    res->type = baseType.image.arrayed ? ShaderResourceType::RWTexture1DArray : ShaderResourceType::RWTexture1D;
                    break;
                }
                case spv::Dim2D:
                {
                    res->type = baseType.image.arrayed ? ShaderResourceType::RWTexture2DArray : ShaderResourceType::RWTexture2D;
                    break;
                }
                case spv::Dim3D:
                {
                    res->type = ShaderResourceType::RWTexture3D;
                    break;
                }
                case spv::DimBuffer:
                {
                    res->type = ShaderResourceType::RWTexelBuffer;
                    break;
                }
                default:
                    Assert(false);
                    break;
                }
            }
        }
        else if (baseType.basetype == spirv_cross::SPIRType::BaseType::Sampler)
        {
            res->type = ShaderResourceType::Sampler;
        }
        else if (baseType.basetype == spirv_cross::SPIRType::BaseType::AccelerationStructure)
        {
            res->type = ShaderResourceType::AccelStruct;
        }

        bool is_bindless = type.array.size() == 1 && type.array[0] == 0;

        res->array_size = type.array.size() ? std::max(type.array[0], type.vecsize) : type.vecsize;
        if (is_bindless) 
        {
            res->array_size = 0;
        }
        
        res->space = compiler.get_decoration(resourceObject.id, spv::DecorationDescriptorSet);
        res->index = compiler.get_decoration(resourceObject.id, spv::DecorationBinding);
        res->stage_mask |= stageMask;

        switch (baseType.basetype)
        {
        case spirv_cross::SPIRType::BaseType::Image:
        {
            auto& returnType = compiler.get_type(baseType.image.type);
            res->return_type = MapVariableType(returnType.basetype);
            res->depth_texture = compiler.is_depth_image(baseType, resourceObject.id);
            break;
        }
        default:
            res->return_type = ShaderVariableType::Unknown;
            res->depth_texture = false;
            break;
        }

        if (res->type == ShaderResourceType::StructuredBuffer || res->type == ShaderResourceType::RWStructuredBuffer)
        {
            auto shaderStructType = std::make_unique<ShaderStructTypeT>();
            shaderStructType->size = static_cast<uint32_t>(compiler.get_declared_struct_size_runtime_array(baseType, 1) - compiler.get_declared_struct_size_runtime_array(baseType, 0));

            // fetch first member of base type
            Assert(!baseType.member_types.empty());
            auto& runtimeArrType = compiler.get_type(baseType.member_types.back());
            auto& structType = compiler.get_type(runtimeArrType.parent_type);

            if (structType.basetype == spirv_cross::SPIRType::BaseType::Struct)
            {
                std::function<std::unique_ptr<ShaderVariableExT>(const SPIRType&, UInt32)> ReflectVariable = [&](const SPIRType& type, UInt32 memberIdx) -> std::unique_ptr<ShaderVariableExT> {
                    auto var = std::make_unique<ShaderVariableExT>();

                    auto memberType = &compiler.get_type(type.member_types[memberIdx]);
                    var->name = compiler.get_member_name(type.self, memberIdx);
                    var->offset = compiler.type_struct_member_offset(type, memberIdx);
                    var->size = static_cast<uint32_t>(compiler.get_declared_struct_member_size(type, memberIdx));

                    if (!memberType->array.empty())
                    {
                        Assert(memberType->array.size() == 1);
                        // only support one dim array with literial size
                        var->array_size = memberType->array[0];
                        var->array_stride = compiler.type_struct_member_array_stride(type, memberIdx);

                        memberType = &compiler.get_type(memberType->parent_type);
                    }

                    if (memberType->member_types.empty())
                    {
                        var->type = MapVariableType(memberType->basetype);
                        // use hlsl row major rule
                        var->row_count = memberType->columns;
                        var->col_count = memberType->vecsize;
                    }
                    else
                    {
                        var->type = ShaderVariableType::Struct;
                        for (UInt32 i = 0; i < memberType->member_types.size(); ++i)
                        {
                            var->members.emplace_back(ReflectVariable(*memberType, i));
                        }
                    }

                    return std::move(var);
                };

                for (UInt32 i = 0; i < structType.member_types.size(); ++i)
                {
                    shaderStructType->members.emplace_back(ReflectVariable(structType, i));
                }

                res->struct_type = std::move(shaderStructType);
            }
        }

        layout->resources.emplace_back(std::move(res));
        return true;
    };

    std::vector<std::shared_ptr<CrossSchema::ShaderLayoutT>> srcLayouts;
    auto all_entries = compiler.get_entry_points_and_stages();
    
    bool isFirstCompile = true;
    for (UInt32 entryIndex = 0; entryIndex < all_entries.size(); entryIndex++)
    {
        auto& entry = all_entries[entryIndex];
        compiler.set_entry_point(entry.name, entry.execution_model);
        compiler.compile();

        if (isFirstCompile && code->stage == ShaderStageBit::Compute)
        {
            groupSize->mutate_x(compiler.get_execution_mode_argument(spv::ExecutionModeLocalSize, 0));
            groupSize->mutate_y(compiler.get_execution_mode_argument(spv::ExecutionModeLocalSize, 1));
            groupSize->mutate_z(compiler.get_execution_mode_argument(spv::ExecutionModeLocalSize, 2));
        }
        isFirstCompile = false;

        auto resources = compiler.get_shader_resources();

        ShaderLayoutT* layout = srcLayouts.emplace_back(std::make_shared<ShaderLayoutT>()).get();

        for (auto& input : resources.stage_inputs)
        {
            auto inputVar = std::make_unique<ShaderVariableT>();
            inputVar->name = input.name;
            inputVar->index = compiler.get_decoration(input.id, spv::DecorationLocation);
            auto& type = compiler.get_type(input.base_type_id);
            // HLSL为行向量，SPIRV为列向量，我们统一用HLSL规则，因此这里需要翻一下
            inputVar->col_count = type.vecsize;
            inputVar->row_count = type.columns;
            inputVar->type = MapVariableType(type.basetype);
            code->stage_inputs.emplace_back(std::move(inputVar));
        }

        // resources
        for (auto& ubo : resources.uniform_buffers)
        {
            std::string_view name = ubo.name;
            if (auto prefix = "type."; name.compare(0, strlen(prefix), prefix) == 0)
            {
                name = name.substr(strlen(prefix));
                if (!ReflectConstantBuffer(ubo, name, layout))
                {
                    LOG_EDITOR_ERROR("Can't find required UBO: {} from hlsl reflection", name);
                    Assert(false);
                    return false;
                }
            }
            else
            {
                LOG_EDITOR_ERROR("Uniform buffer's name didn't start with {}", prefix);
                return false;
            }
        }

        for (auto& ssbo : resources.storage_buffers)
        {
            if (!ReflectResource(ssbo, ssbo.name, layout))
            {
                LOG_EDITOR_ERROR("Can't find required SSBO: {} from hlsl reflection", ssbo.name);
                return false;
            }
        }

        for (auto& as : resources.acceleration_structures)
        {
            if (!ReflectResource(as, as.name, layout))
            {
                LOG_EDITOR_ERROR("Can't find required AccelerationStructure: {} from hlsl reflection", as.name);
                return false;
            }
        }

        for (auto& subpassInput : resources.subpass_inputs)
        {
            // auto& type = compiler.get_type(subpassInput.type_id);
            auto& baseType = compiler.get_type(subpassInput.base_type_id);
            auto res = std::make_unique<ShaderResourceT>();
            res->name = compiler.get_name(subpassInput.id);
            res->index = compiler.get_decoration(subpassInput.id, spv::DecorationBinding);
            res->space = compiler.get_decoration(subpassInput.id, spv::DecorationDescriptorSet);
            res->subpass_index = compiler.get_decoration(subpassInput.id, spv::DecorationInputAttachmentIndex);
            res->stage_mask |= stageMask;
            res->type = baseType.image.ms ? ShaderResourceType::SubpassInputMS : ShaderResourceType::SubpassInput;
            layout->resources.emplace_back(std::move(res));
        }
        /*
        for (auto& subpassInput : resources.subpass_inputs)
        {
            auto ret = std::find_if(HLSLLayout->resources.begin(), HLSLLayout->resources.end(), [&](auto& sr) { return sr->name == subpassInput.name; });
            Assert(ret != HLSLLayout->resources.end());

            auto res = Copy(*ret);

            res->index = compiler.get_decoration(subpassInput.id, spv::DecorationBinding);
            switch (res->type)
            {
            case ShaderResourceType::Texture2D:
                res->type = ShaderResourceType::SubpassInput;
                break;
            case ShaderResourceType::Texture2DMS:
                res->type = ShaderResourceType::SubpassInputMS;
                break;
            default:
                Assert(false);
                break;
            }
            res->subpass_index = compiler.get_decoration(subpassInput.id, spv::DecorationInputAttachmentIndex);

            layout->resources.emplace_back(std::move(res));
        }
        */

        for (auto& image : resources.storage_images)
        {
            if (!ReflectResource(image, image.name, layout))
            {
                return false;
            }
        }

        for (auto& texture : resources.separate_images)
        {
            if (!ReflectResource(texture, texture.name, layout))
            {
                return false;
            }
        }

        for (auto& sampler : resources.separate_samplers)
        {
            if (!ReflectResource(sampler, sampler.name, layout))
            {
                return false;
            }
        }

        /*if (auto constInfos = compiler.get_specialization_constants(); !constInfos.empty())
        {
            auto specConsts = std::make_unique<ShaderConstantBufferT>();
            Assert(HLSLLayout->specialization_constants);
            specConsts->stage_mask = HLSLLayout->specialization_constants->stage_mask;

            for (auto& constInfo : constInfos)
            {
                auto& name = compiler.get_name(constInfo.id);

                auto ret = std::find_if(HLSLLayout->specialization_constants->members.begin(), HLSLLayout->specialization_constants->members.end(), [&](auto& member) { return member->name == name; });
                Assert(ret != HLSLLayout->specialization_constants->members.end());

                auto member = std::make_unique<ShaderVariableT>(**ret);
                member->offset = 0;
                member->index = constInfo.constant_id;
                specConsts->members.emplace_back(std::move(member));
            }

            layout->specialization_constants = std::move(specConsts);

        }*/
        if (auto constInfos = compiler.get_specialization_constants(); !constInfos.empty())
        {
            auto specConsts = std::make_unique<ShaderConstantBufferT>();
            specConsts->type = CrossSchema::ShaderResourceType::ConstantBuffer;
            specConsts->stage_mask |= stageMask;

            auto varStructType = std::make_unique<ShaderStructTypeT>();
            for (auto& constInfo : constInfos)
            {
                auto& name = compiler.get_name(constInfo.id);

                auto& constValue = compiler.get_constant(constInfo.id);
                auto variable = std::make_unique<ShaderVariableExT>();
                variable->name = compiler.get_name(constInfo.id);
                variable->offset = 0;
                variable->index = constInfo.constant_id;
                variable->row_count = constValue.columns();
                variable->col_count = constValue.vector_size();
                auto& memberType = compiler.get_type(constValue.constant_type);
                variable->type = MapVariableType(memberType.basetype);
                // the spec_cosnt only have 3 types : boolean, int, float,in HLSLlayout, size of anytype is 4
                variable->size = variable->row_count * variable->col_count * 4;
                variable->stage_mask |= stageMask;

                if (constValue.is_used_as_array_length)
                {
                    variable->array_size = static_cast<UInt32>(constValue.subconstants.size());
                }
                varStructType->members.emplace_back(std::move(variable));
            }
            specConsts->struct_type = std::move(varStructType);
            layout->specialization_constants = std::move(specConsts);
        }
    }

    MergeShaderLayouts(srcLayouts, dstLayout, version);
    
    return true;
}

spv::ExecutionModel MapExecutionModel(ShaderStageBit stage)
{
    switch (stage)
    {
    case ShaderStageBit::Vertex:
        return spv::ExecutionModelVertex;
    case ShaderStageBit::Hull:
        return spv::ExecutionModelTessellationControl;
    case ShaderStageBit::Domain:
        return spv::ExecutionModelTessellationEvaluation;
    case ShaderStageBit::Geometry:
        return spv::ExecutionModelGeometry;
    case ShaderStageBit::Pixel:
        return spv::ExecutionModelFragment;
    case ShaderStageBit::Compute:
        return spv::ExecutionModelGLCompute;
    default:
        Assert(false);
        return spv::ExecutionModelMax;
    }
}

bool ReflectForESSL(ShaderCodeT* code, const CrossSchema::ShaderVersion& version, ShaderLayoutT* HLSLLayout, ShaderLayoutT* layout, CrossSchema::uint3* groupSize)
{
    spirv_cross::CompilerGLSL compiler{reinterpret_cast<uint32_t*>(code->code_data.data()), code->code_data.size() / 4};

    if (code->stage == ShaderStageBit::Compute)
    {
        groupSize->mutate_x(compiler.get_execution_mode_argument(spv::ExecutionModeLocalSize, 0));
        groupSize->mutate_y(compiler.get_execution_mode_argument(spv::ExecutionModeLocalSize, 1));
        groupSize->mutate_z(compiler.get_execution_mode_argument(spv::ExecutionModeLocalSize, 2));
    }

    compiler.set_entry_point(code->entry_point, MapExecutionModel(code->stage));
    auto options = compiler.get_common_options();
    options.version = version.major_version() * 100 + version.minor_version() * 10;
    options.es = true;
    options.force_temporary = false;
    options.flatten_multidimensional_arrays = false;
    options.enable_420pack_extension = false;
    options.vulkan_semantics = false;
    options.vertex.fixup_clipspace = false;
    options.vertex.flip_vert_y = false;
    options.vertex.support_nonzero_base_instance = true;
    compiler.set_common_options(options);

    if (UInt32 sampler = compiler.build_dummy_sampler_for_combined_images(); sampler != 0)
    {
        compiler.set_decoration(sampler, spv::DecorationDescriptorSet, 0);
        compiler.set_decoration(sampler, spv::DecorationBinding, 0);
    }
    compiler.build_combined_image_samplers();
    for (auto& remap : compiler.get_combined_image_samplers())
    {
        compiler.set_name(remap.combined_id, Format("SPIRV_Cross_Combined{}{}", compiler.get_name(remap.image_id), compiler.get_name(remap.sampler_id)));
    }

    try
    {
        auto esslStr = compiler.compile();
        if (options.version <= 300)
        {
            static const std::regex vsPattern{"out_var"};
            static const std::regex psPattern{"in_var"};
            switch (code->stage)
            {
            case ShaderStageBit::Vertex:
                esslStr = std::regex_replace(esslStr, vsPattern, "v2f_var");
                break;
            case ShaderStageBit::Pixel:
                esslStr = std::regex_replace(esslStr, psPattern, "v2f_var");
                break;
            default:
                Assert(false);
                break;
            }
        }
        code->code_data.resize(esslStr.length());
        memcpy(code->code_data.data(), esslStr.c_str(), esslStr.length());
    }
    catch (spirv_cross::CompilerError& e)
    {
        LOG_EDITOR_ERROR("{}", e.what());
        return false;
    }

    auto stageMask = static_cast<UInt32>(code->stage);

    // use reflection from hlsl for resources except uniform buffer(cbuffer) and storage buffer(tbuffer)
    layout->resources.reserve(HLSLLayout->resources.size());
    std::transform(HLSLLayout->resources.begin(), HLSLLayout->resources.end(), std::back_inserter(layout->resources), [](auto& src) { return Copy(src); });

    auto resources = compiler.get_shader_resources();

    for (auto& input : resources.stage_inputs)
    {
        auto inputVar = std::make_unique<ShaderVariableT>();
        inputVar->name = input.name;
        inputVar->index = compiler.get_decoration(input.id, spv::DecorationLocation);
        auto& type = compiler.get_type(input.base_type_id);
        inputVar->col_count = type.columns;
        inputVar->row_count = type.vecsize;
        inputVar->type = MapVariableType(type.basetype);
        code->stage_inputs.emplace_back(std::move(inputVar));
    }

    auto ReflectConstantBuffer = [&](auto& bufferObject) {
        std::string_view cbName = bufferObject.name;
        if (auto prefix = "type."; cbName.compare(0, strlen(prefix), prefix) == 0)
        {
            cbName = cbName.substr(strlen(prefix));
        }
        auto cbufRefRet = std::find_if(HLSLLayout->constant_buffers.begin(), HLSLLayout->constant_buffers.end(), [&](auto& cb) { return cb->name == cbName; });
        if (cbufRefRet == HLSLLayout->constant_buffers.end())
        {
            return false;
        }
        auto& cbufferRef = *cbufRefRet;

        auto cbuffer = Copy(cbufferRef);
        auto& type = compiler.get_type(bufferObject.base_type_id);
        // cbuffer->space = 0;
        // cbuffer->index = 0;
        cbuffer->size = static_cast<UInt32>(compiler.get_declared_struct_size(type));
        cbuffer->size = AlignSize(cbuffer->size, sizeof(float[4]));

        auto varStructType = std::make_unique<ShaderStructTypeT>();

        for (auto i = 0u; i < type.member_types.size(); i++)
        {
            auto& name = compiler.get_member_name(bufferObject.base_type_id, i);

            if (cbufferRef->struct_type)
            {
                auto varRefRet = std::find_if(cbufferRef->struct_type->members.begin(), cbufferRef->struct_type->members.end(), [&](auto& var) { return var->name == name; });
                Assert(varRefRet != cbufferRef->struct_type->members.end());

                auto& variableRef = *varRefRet;
                auto variable = std::make_unique<ShaderVariableExT>();
                variable->name = variableRef->name;
                variable->type = variableRef->type;
                variable->row_count = variableRef->row_count;
                variable->col_count = variableRef->col_count;
                variable->offset = compiler.type_struct_member_offset(type, i);
                variable->size = static_cast<UInt32>(compiler.get_declared_struct_member_size(type, i));
                varStructType->members.emplace_back(std::move(variable));
            }
            else
            {
                auto varRefRet = std::find_if(cbufferRef->members.begin(), cbufferRef->members.end(), [&](auto& var) { return var->name == name; });
                Assert(varRefRet != cbufferRef->members.end());
                auto& variableRef = *varRefRet;
                
                auto variable = std::make_unique<ShaderVariableExT>();
                variable->name = variableRef->name;
                variable->type = variableRef->type;
                variable->row_count = variableRef->row_count;
                variable->col_count = variableRef->col_count;
                variable->offset = compiler.type_struct_member_offset(type, i);
                variable->size = static_cast<UInt32>(compiler.get_declared_struct_member_size(type, i));
                varStructType->members.emplace_back(std::move(variable));
            }
        }
        cbuffer->struct_type = std::move(varStructType);

        layout->constant_buffers.emplace_back(std::move(cbuffer));
        return true;
    };

    auto ReflectResource = [&](auto& resourceObject) {
        auto ret = std::find_if(HLSLLayout->resources.begin(), HLSLLayout->resources.end(), [&](auto& sr) { return sr->name == resourceObject.name; });
        if (ret == HLSLLayout->resources.end())
        {
            return false;
        }
        auto& resourceRef = *ret;

        auto resource = Copy(resourceRef);
        // resource->space = 0;
        // resource->index = 0;
        resource->subpass_index = 0;

        layout->resources.emplace_back(std::move(resource));
        return true;
    };

    if (version.major_version() >= 3)
    {
        for (auto& ubo : resources.uniform_buffers)
        {
            if (!ReflectConstantBuffer(ubo))
            {
                LOG_EDITOR_ERROR("Can't find required UBO: {} from hlsl reflection", ubo.name);
                Assert(false);
                return false;
            }
        }
    }
    else
    {
        for (auto& cbuffer : HLSLLayout->constant_buffers)
        {
            auto cb = Copy(cbuffer);
            // cb->space = 0;
            // cb->index = 0;
            cb->size = 0;
            for (auto& variable : cbuffer->members)
            {
                auto var = std::make_unique<ShaderVariableT>(*variable);
                var->index = 0;
                var->offset = cb->size;
                cb->size += var->size;
                cb->members.emplace_back(std::move(var));
            }
            layout->constant_buffers.emplace_back(std::move(cb));
        }
    }

    for (auto& ssbo : resources.storage_buffers)
    {
        if (!ReflectConstantBuffer(ssbo))
        {
            if (!ReflectResource(ssbo))
            {
                LOG_EDITOR_ERROR("Can't find required SSBO: {} from hlsl reflection", ssbo.name);
                Assert(false);
                return false;
            }
        }
    }

    for (auto& combinedImageSampler : compiler.get_combined_image_samplers())
    {
        auto combinedSampler = std::make_unique<CombinedSamplerT>();
        combinedSampler->name = compiler.get_name(combinedImageSampler.combined_id);
        combinedSampler->texture_name = compiler.get_name(combinedImageSampler.image_id);
        combinedSampler->sampler_name = compiler.get_name(combinedImageSampler.sampler_id);
        combinedSampler->stage_mask = stageMask;
        layout->combined_samplers.emplace_back(std::move(combinedSampler));
    }

    if (auto constInfos = compiler.get_specialization_constants(); !constInfos.empty())
    {
        auto specConsts = std::make_unique<ShaderConstantBufferT>();
        Assert(HLSLLayout->specialization_constants);
        specConsts->stage_mask = HLSLLayout->specialization_constants->stage_mask;

        auto varStructType = std::make_unique<ShaderStructTypeT>();

        for (auto& constInfo : constInfos)
        {
            auto& name = compiler.get_name(constInfo.id);

            if (HLSLLayout->specialization_constants->struct_type)
            {
                auto ret = std::find_if(HLSLLayout->specialization_constants->struct_type->members.begin(), HLSLLayout->specialization_constants->struct_type->members.end(), [&](auto& member) { return member->name == name; });
                Assert(ret != HLSLLayout->specialization_constants->struct_type->members.end());

                auto variable = std::make_unique<ShaderVariableExT>();
                variable->name = (*ret)->name;
                variable->type = (*ret)->type;
                variable->row_count = (*ret)->row_count;
                variable->col_count = (*ret)->col_count;
                variable->offset = 0;
                variable->index = constInfo.constant_id;
                varStructType->members.emplace_back(std::move(variable));
            }
            else
            {
                auto ret = std::find_if(HLSLLayout->specialization_constants->members.begin(), HLSLLayout->specialization_constants->members.end(), [&](auto& member) { return member->name == name; });
                Assert(ret != HLSLLayout->specialization_constants->members.end());
                auto variable = std::make_unique<ShaderVariableExT>();
                variable->name = (*ret)->name;
                variable->type = (*ret)->type;
                variable->row_count = (*ret)->row_count;
                variable->col_count = (*ret)->col_count;
                variable->offset = 0;
                variable->index = constInfo.constant_id;
                varStructType->members.emplace_back(std::move(variable));
            }
        }
        specConsts->struct_type = std::move(varStructType);

        layout->specialization_constants = std::move(specConsts);
    }

    return true;
}

bool ReflectForMSL(ShaderCodeT* code, const CrossSchema::ShaderVersion& version, ShaderLayoutT* HLSLLayout, ShaderLayoutT* layout, CrossSchema::uint3* groupSize)
{
    spirv_cross::CompilerMSL compiler{reinterpret_cast<uint32_t*>(code->code_data.data()), code->code_data.size() / 4};

    if (code->stage == ShaderStageBit::Compute)
    {
        groupSize->mutate_x(compiler.get_execution_mode_argument(spv::ExecutionModeLocalSize, 0));
        groupSize->mutate_y(compiler.get_execution_mode_argument(spv::ExecutionModeLocalSize, 1));
        groupSize->mutate_z(compiler.get_execution_mode_argument(spv::ExecutionModeLocalSize, 2));
    }

    compiler.set_entry_point(code->entry_point, MapExecutionModel(code->stage));
    auto options = compiler.get_common_options();
    options.es = false;
    options.force_temporary = false;
    options.separate_shader_objects = true;
    options.flatten_multidimensional_arrays = false;
    options.enable_420pack_extension = false;
    options.vulkan_semantics = false;
    options.vertex.fixup_clipspace = false;
    options.vertex.flip_vert_y = false;
    options.vertex.support_nonzero_base_instance = true;
    compiler.set_common_options(options);

    auto mslOpts = compiler.get_msl_options();
    if (version.format() == ShaderCodeFormat::MSL_OSX)
    {
        mslOpts.platform = spirv_cross::CompilerMSL::Options::macOS;
        mslOpts.use_framebuffer_fetch_subpasses = false;
    }
    else
    {
        mslOpts.platform = spirv_cross::CompilerMSL::Options::iOS;
        mslOpts.use_framebuffer_fetch_subpasses = true;
    }
    mslOpts.set_msl_version(version.major_version(), version.minor_version());
    mslOpts.swizzle_texture_samples = false;
    mslOpts.argument_buffers = true;
    mslOpts.enable_base_index_zero = true;
    mslOpts.ios_support_base_vertex_instance = true;
    mslOpts.force_active_argument_buffer_resources = true;
    mslOpts.texture_buffer_native = true;
    compiler.set_msl_options(mslOpts);

    // argument buffer setting
    static const int MaxArgumentBufferNum = 16;
    static const int MaxVertexBufferNum = 16;
    static_assert(SPIRV_CROSS_NAMESPACE::kMaxArgumentBuffers <= MaxArgumentBufferNum);

    spv::ExecutionModel exeModel = spv::ExecutionModelMax;
    switch (code->stage)
    {
    case ShaderStageBit::Vertex:
        exeModel = spv::ExecutionModelVertex;
        break;
    case ShaderStageBit::Pixel:
        exeModel = spv::ExecutionModelFragment;
        break;
    case ShaderStageBit::Compute:
        exeModel = spv::ExecutionModelGLCompute;
        break;
    default:
        Assert(false);
        break;
    }

    for (auto i = 0u; i < SPIRV_CROSS_NAMESPACE::kMaxArgumentBuffers; i++)
    {
        SPIRV_CROSS_NAMESPACE::MSLResourceBinding bind;
        bind.stage = exeModel;
        bind.binding = SPIRV_CROSS_NAMESPACE::kArgumentBufferBinding;
        bind.desc_set = i;
        bind.msl_buffer = i + MaxVertexBufferNum;
        compiler.add_msl_resource_binding(bind);
    }

    try
    {
        auto mslCode = compiler.compile();
        code->code_data.resize(mslCode.length());
        memcpy(code->code_data.data(), mslCode.c_str(), mslCode.length());
    }
    catch (spirv_cross::CompilerError& e)
    {
        LOG_EDITOR_ERROR("{}", e.what());
        return false;
    }

    // note: todo reflect [[buffer]] of shader vertex buffer  and argument buffer.we only use one vertex buffer(stream, id:0) and  only one argument buffer(id:16)

    auto resources = compiler.get_shader_resources();

    // msl : [[stage_in]]
    for (auto& input : resources.stage_inputs)
    {
        // msl: param name and [[attribute]]
        auto var = std::make_unique<ShaderVariableT>();
        var->name = input.name;
        var->index = static_cast<uint16_t>(compiler.get_decoration(input.id, spv::DecorationLocation));
        auto& type = compiler.get_type(input.base_type_id);
        var->col_count = type.columns;
        var->row_count = type.vecsize;
        var->type = MapVariableType(type.basetype);
        code->stage_inputs.emplace_back(std::move(var));
    }

    auto ReflectConstantBuffer = [&](auto& bufferObject, std::string_view name) {
        auto cbufRefRet = std::find_if(HLSLLayout->constant_buffers.begin(), HLSLLayout->constant_buffers.end(), [&](auto& cb) {
            if (name.length() == cb->name.length())
            {
                return name == cb->name;
            }
            else if (name.length() > cb->name.length())
            {
                return false;
            }
            else
            {
                // cb->name is ends with name and the rest characters were "_" ?
                if (std::equal(name.rbegin(), name.rend(), cb->name.rbegin()))
                {
                    return std::all_of(cb->name.begin(), cb->name.begin() + cb->name.length() - name.length(), [](auto c) { return c == '_'; });
                }
                else
                {
                    return false;
                }
            }
        });
        if (cbufRefRet == HLSLLayout->constant_buffers.end())
        {
            LOG_EDITOR_ERROR("Can't find required cosntant buffer: {} from hlsl reflection", name);
            return false;
        }
        auto& cbufferRef = *cbufRefRet;

        auto cbuffer = Copy(cbufferRef);

        auto& type = compiler.get_type(bufferObject.base_type_id);
        // cbuffer->space = 0;
        // cbuffer->index = compiler.get_automatic_msl_resource_binding(bufferObject.id);
        cbuffer->index = compiler.get_decoration(bufferObject.id, spv::DecorationBinding);
        cbuffer->size = static_cast<UInt32>(compiler.get_declared_struct_size(type));

        auto varStructType = std::make_unique<ShaderStructTypeT>();
        for (auto i = 0u; i < type.member_types.size(); i++)
        {
            auto& memberName = compiler.get_member_name(bufferObject.base_type_id, i);

            if (cbufferRef->struct_type)
            {
                auto varRefRet = std::find_if(cbufferRef->struct_type->members.begin(), cbufferRef->struct_type->members.end(), [&](auto& var) { return var->name == memberName; });
                Assert(varRefRet != cbufferRef->struct_type->members.end());

                auto& variableRef = *varRefRet;
                auto variable = std::make_unique<ShaderVariableExT>();
                variable->name = variableRef->name;
                variable->type = variableRef->type;
                variable->row_count = variableRef->row_count;
                variable->col_count = variableRef->col_count;
                variable->offset = compiler.type_struct_member_offset(type, i);
                variable->size = static_cast<UInt32>(compiler.get_declared_struct_member_size(type, i));
                varStructType->members.emplace_back(std::move(variable));
            }
            else
            {
                auto varRefRet = std::find_if(cbufferRef->members.begin(), cbufferRef->members.end(), [&](auto& var) { return var->name == memberName; });
                Assert(varRefRet != cbufferRef->members.end());
                auto& variableRef = *varRefRet;

                auto variable = std::make_unique<ShaderVariableExT>();
                variable->name = variableRef->name;
                variable->type = variableRef->type;
                variable->row_count = variableRef->row_count;
                variable->col_count = variableRef->col_count;
                variable->offset = compiler.type_struct_member_offset(type, i);
                variable->size = static_cast<UInt32>(compiler.get_declared_struct_member_size(type, i));
                varStructType->members.emplace_back(std::move(variable));
            }
        }
        cbuffer->struct_type = std::move(varStructType);

        layout->constant_buffers.emplace_back(std::move(cbuffer));

        return true;
    };

    auto ReflectResource = [&](auto& resourceObject, std::string_view name) {
        auto ret = std::find_if(HLSLLayout->resources.begin(), HLSLLayout->resources.end(), [&](auto& sr) { return sr->name == name; });
        if (ret == HLSLLayout->resources.end())
        {
            LOG_EDITOR_ERROR("Can't find required resource: {} from hlsl reflection", resourceObject.name);
            Assert(false);
            return false;
        }
        auto& resourceRef = *ret;

        auto resource = Copy(resourceRef);
        // resource->space = 0;
        // resource->index = static_cast<UInt32>(compiler.get_automatic_msl_resource_binding(resourceObject.id));
        resource->index = compiler.get_decoration(resourceObject.id, spv::DecorationBinding);

        layout->resources.emplace_back(std::move(resource));
        return true;
    };

    for (auto& ubo : resources.uniform_buffers)
    {
        std::string_view name = ubo.name;
        if (auto prefix = "type."; name.compare(0, strlen(prefix), prefix) == 0)
        {
            name = name.substr(strlen(prefix));
            if (ReflectConstantBuffer(ubo, name))
            {
                continue;
            }
            else
            {
                LOG_EDITOR_ERROR("Can't find required UBO: {} from hlsl reflection", name);
                Assert(false);
                return false;
            }
        }
        else
        {
            LOG_EDITOR_ERROR("Uniform buffer's name didn't start with {}", prefix);
            return false;
        }
    }

    for (auto& ssbo : resources.storage_buffers)
    {
        const std::string_view name = ssbo.name;
        if (auto prefix = "type."; name.compare(0, strlen(prefix), prefix) == 0)
        {
            if (ReflectConstantBuffer(ssbo, name.substr(strlen(prefix))))
            {
                continue;
            }
        }

        auto dotPos = name.find('.');
        if (dotPos != std::string::npos)
        {
            if (ReflectResource(ssbo, name.substr(dotPos + 1, name.length() - dotPos - 2)))
            {
                continue;
            }
        }

        LOG_EDITOR_ERROR("Can't find required SSBO: {} from hlsl reflection", ssbo.name);
        Assert(false);
        return false;
    }

    for (auto& image : resources.storage_images)
    {
        if (!ReflectResource(image, image.name))
        {
            return false;
        }
    }

    for (auto& texture : resources.separate_images)
    {
        if (!ReflectResource(texture, texture.name))
        {
            return false;
        }
    }

    for (auto& sampler : resources.separate_samplers)
    {
        if (!ReflectResource(sampler, sampler.name))
        {
            return false;
        }
    }

    if (auto constInfos = compiler.get_specialization_constants(); !constInfos.empty())
    {
        auto specConsts = std::make_unique<ShaderConstantBufferT>();
        Assert(HLSLLayout->specialization_constants);
        specConsts->stage_mask = HLSLLayout->specialization_constants->stage_mask;

        auto varStructType = std::make_unique<ShaderStructTypeT>();

        for (auto& constInfo : constInfos)
        {
            auto& name = compiler.get_name(constInfo.id);

            if (HLSLLayout->specialization_constants->struct_type)
            {
                auto ret = std::find_if(HLSLLayout->specialization_constants->struct_type->members.begin(), HLSLLayout->specialization_constants->struct_type->members.end(), [&](auto& member) { return member->name == name; });
                Assert(ret != HLSLLayout->specialization_constants->struct_type->members.end());

                auto variable = std::make_unique<ShaderVariableExT>();
                variable->name = (*ret)->name;
                variable->type = (*ret)->type;
                variable->row_count = (*ret)->row_count;
                variable->col_count = (*ret)->col_count;
                variable->offset = 0;
                variable->index = constInfo.constant_id;
                varStructType->members.emplace_back(std::move(variable));
            }
            else
            {
                auto ret = std::find_if(HLSLLayout->specialization_constants->members.begin(), HLSLLayout->specialization_constants->members.end(), [&](auto& member) { return member->name == name; });
                Assert(ret != HLSLLayout->specialization_constants->members.end());
                auto variable = std::make_unique<ShaderVariableExT>();
                variable->name = (*ret)->name;
                variable->type = (*ret)->type;
                variable->row_count = (*ret)->row_count;
                variable->col_count = (*ret)->col_count;
                variable->offset = 0;
                variable->index = constInfo.constant_id;
                varStructType->members.emplace_back(std::move(variable));
            }
        }
        specConsts->struct_type = std::move(varStructType);

        layout->specialization_constants = std::move(specConsts);
    }

    return true;
}

}   // namespace cross::editor

#define DXIL_FOURCC(ch0, ch1, ch2, ch3) ((uint32_t)(uint8_t)(ch0) | (uint32_t)(uint8_t)(ch1) << 8 | (uint32_t)(uint8_t)(ch2) << 16 | (uint32_t)(uint8_t)(ch3) << 24)

bool cross::editor::ReflectForDXIL(IDxcBlob* binary, CrossSchema::ShaderCodeT* code, ShaderLayoutT* layout, CrossSchema::uint3* groupSize)
{
#if CROSSENGINE_WIN
    using namespace CrossSchema;

    CComPtr<IDxcContainerReflection> pContainerReflection;
    CHECK(DxcCreateInstance(CLSID_DxcContainerReflection, __uuidof(IDxcContainerReflection), reinterpret_cast<void**>(&pContainerReflection)));
    CHECK(pContainerReflection->Load(binary));
    UInt32 shaderIndex;
    CHECK(pContainerReflection->FindFirstPartKind(DXIL_FOURCC('D', 'X', 'I', 'L'), &shaderIndex));

    CComPtr<ID3D12ShaderReflection> pReflection;
    CHECK(pContainerReflection->GetPartReflection(shaderIndex, __uuidof(ID3D12ShaderReflection), reinterpret_cast<void**>(&pReflection)));

    ReflectionDXIL(pReflection, code->stage, layout, groupSize);

    return true;
#else
    auto tempDirPath = std::filesystem::temp_directory_path();
    auto tempFullName = tempDirPath / std::tmpnam(nullptr);
    auto tempFullNameS = tempFullName.string();
    {
        std::ofstream ostrm(tempFullNameS, std::ios::out | std::ios::binary);
        ostrm.write(reinterpret_cast<char*>(binary->GetBufferPointer()), binary->GetBufferSize());
        ostrm.close();
        // LOG_INFO("[DXIL] Create Temp File: {}", tempFullName.string());
    }
    const auto stageBitStr = std::to_string(static_cast<std::underlying_type_t<ShaderStageBit> >(code->stage));
    // The path relative was editorBin/${configuration}/netcore2.1/engineBin/DXILReflection
    const auto cdDXILBin = "cd " + PathHelper::GetEngineBinaryDirectoryPath() + "/../../DXILReflection";
#    if WIN32
    const std::string command = cdDXILBin + " && DXILReflection.exe " + tempFullNameS + " " + stageBitStr;
#    else
    const std::string command = cdDXILBin + " && WINEPREFIX=~/.wine.ce ../Wine/bin/wine64 DXILReflection.exe " + tempFullNameS + " " + stageBitStr;
#    endif
    LOG_INFO("[DXIL] Call Command With \"{}\"", command);
    const int ret = std::system(command.c_str());

    std::remove(tempFullNameS.c_str());

    if (ret < 0)
    {
        LOG_EDITOR_ERROR("[DXIL] Create Process Failed");
        return false;
    }
    else if (ret != 0)
    {
        LOG_EDITOR_ERROR("[DXIL] Process Return {} And Get Error {}", ret, std::strerror(errno));
        return false;
    }

    // UnPack
    {
        const auto tempFlat = tempFullNameS + ".flat";
        std::ifstream infile;
        infile.open(tempFlat, std::ios::binary | std::ios::in);
        if (!infile.is_open())
        {
            LOG_EDITOR_ERROR("[DXIL] The Genearted Flat File {} Was Not Exist", tempFlat);
            return false;
        }

        infile.seekg(0, std::ios::end);
        auto length = infile.tellg();
        infile.seekg(0, std::ios::beg);
        char* data = new char[length];
        infile.read(data, length);
        infile.close();
        std::remove(tempFlat.c_str());

        auto dxilReflectionCode = flatbuffers::GetRoot<DXILReflectionCode>(reinterpret_cast<void*>(data));

        if (groupSize)
            new (groupSize) CrossSchema::uint3(*dxilReflectionCode->group_size());

        if (layout)
            dxilReflectionCode->layout()->UnPackTo(layout);

        delete[] data;
    }

    LOG_INFO("[DXIL] Generate Reflection Info Succeed");

    return true;
#endif
}

bool cross::editor::ReflectForOtherFormat(CrossSchema::ShaderCodeT* code, const CrossSchema::ShaderVersion& version, CrossSchema::ShaderLayoutT* pHLSLLayout, CrossSchema::ShaderLayoutT* layout, CrossSchema::uint3* groupSize)
{
    switch (version.format())
    {
    case ShaderCodeFormat::SPIR_V:
        return ReflectForSPIR_V(code, version, pHLSLLayout, layout, groupSize);
    case ShaderCodeFormat::ESSL:
        return ReflectForESSL(code, version, pHLSLLayout, layout, groupSize);
    case ShaderCodeFormat::MSL_OSX:
    case ShaderCodeFormat::MSL_IOS:
        return ReflectForMSL(code, version, pHLSLLayout, layout, groupSize);
    default:
        return false;
    }
}

bool cross::editor::MergeShaderLayouts(const std::vector<std::shared_ptr<CrossSchema::ShaderLayoutT>>& srcLayouts, CrossSchema::ShaderLayoutT* dstLayout, CrossSchema::ShaderVersion version)
{
    auto& mergedcbs = dstLayout->constant_buffers;
    auto& mergedreses = dstLayout->resources;
    auto& mergedCombSmps = dstLayout->combined_samplers;

    auto MergeConstantBuffer = [](auto& mergedcb, auto& cb) {
        mergedcb->size = std::max(mergedcb->size, cb->size);
        mergedcb->stage_mask |= cb->stage_mask;

        if (cb->struct_type)
        {
            mergedcb->stage_mask |= 0;
        }

        if (!mergedcb->struct_type && cb->struct_type)
        {
            mergedcb->struct_type = std::make_unique<ShaderStructTypeT>();
            mergedcb->struct_type->size = std::max(cb->struct_type->size, cb->size);
            for (const auto& srcMember : cb->struct_type->members)
            {
                auto clone = std::make_unique<ShaderVariableExT>();
                clone->name = srcMember->name;
                clone->type = srcMember->type;
                clone->offset = srcMember->offset;
                clone->size = srcMember->size;
                clone->row_count = srcMember->row_count;
                clone->col_count = srcMember->col_count;
                clone->array_size = srcMember->array_size;
                clone->array_stride = srcMember->array_stride;
                clone->stage_mask = srcMember->stage_mask;
                clone->index = srcMember->index;

                mergedcb->struct_type->members.push_back(std::move(clone));
            }
        }
        else if (mergedcb->struct_type && cb->struct_type)
        {
            mergedcb->struct_type->size = std::max(cb->struct_type->size, cb->size);
            mergedcb->size = mergedcb->struct_type->size;
            for (const auto& srcMember : cb->struct_type->members)
            {
                auto mergedMemberIt = std::find_if(mergedcb->struct_type->members.begin(), mergedcb->struct_type->members.end(), [&](const auto& v) { return v->name == srcMember->name; });

                if (mergedMemberIt != mergedcb->struct_type->members.end())
                {
                    (*mergedMemberIt)->stage_mask |= cb->stage_mask;
                }
                else
                {
                    auto newVarEx = std::make_unique<ShaderVariableExT>();
                    newVarEx->name = srcMember->name;
                    newVarEx->type = srcMember->type;
                    newVarEx->offset = srcMember->offset;
                    newVarEx->size = srcMember->size;
                    newVarEx->row_count = srcMember->row_count;
                    newVarEx->col_count = srcMember->col_count;
                    newVarEx->array_size = srcMember->array_size;
                    newVarEx->array_stride = srcMember->array_stride;
                    newVarEx->stage_mask = srcMember->stage_mask;
                    newVarEx->index = srcMember->index;

                    mergedcb->struct_type->members.push_back(std::move(newVarEx));
                }
            }
        }
    };

    for (auto& layout : srcLayouts)
    {
        for (auto& cb : layout->constant_buffers)
        {
            if (auto mergedcb = std::find_if(mergedcbs.begin(), mergedcbs.end(), [&](auto& v) { return v->name == cb->name; }); mergedcb != mergedcbs.end())
            {
                MergeConstantBuffer(*mergedcb, cb);
            }
            else
            {
                mergedcbs.emplace_back(std::move(cb));
            }
        }

        for (auto& res : layout->resources)
        {
            if (auto mergedres = std::find_if(mergedreses.begin(), mergedreses.end(), [&](auto& v) { return v->name == res->name; }); mergedres != mergedreses.end())
            {
                (*mergedres)->stage_mask |= res->stage_mask;
            }
            else
            {
                mergedreses.emplace_back(std::move(res));
            }
        }

        for (auto& combSmp : layout->combined_samplers)
        {
            if (auto mergedCombSmp = std::find_if(mergedCombSmps.begin(), mergedCombSmps.end(), [&](auto& v) { return v->name == combSmp->name; }); mergedCombSmp != mergedCombSmps.end())
            {
                (*mergedCombSmp)->stage_mask |= combSmp->stage_mask;
            }
            else
            {
                mergedCombSmps.emplace_back(std::move(combSmp));
            }
        }

        if (auto& specConsts = layout->specialization_constants; specConsts)
        {
            if (dstLayout->specialization_constants)
            {
                Assert(dstLayout->specialization_constants->size == specConsts->size);
                MergeConstantBuffer(dstLayout->specialization_constants, specConsts);
            }
            else
            {
                dstLayout->specialization_constants = std::move(specConsts);
            }
        }
    }

    if (auto& specConsts = dstLayout->specialization_constants; specConsts)
    {
        specConsts->name = "__SpecializationConstants__";
        // sort specialization constants and calculate packed size, for spirv / essl / msl
        if (specConsts->size == 0)
        {
            if (auto maxIndexcb = std::max_element(mergedcbs.cbegin(), mergedcbs.cend(), [](auto& a, auto& b) { return a->index < b->index; }); maxIndexcb != mergedcbs.cend())
            {
                specConsts->index = (*maxIndexcb)->index + 1;
            }

            
            UInt32 offset = 0;
            if (specConsts->struct_type)
            {
                std::sort(specConsts->struct_type->members.begin(), specConsts->struct_type->members.end(), [](auto& a, auto& b) { return a->index < b->index; });
                if (version.format() == ShaderCodeFormat::SPIR_V)
                {
                    auto end = std::unique(specConsts->struct_type->members.begin(), specConsts->struct_type->members.end(), [](auto& a, auto& b) { return a->index == b->index; });
                    if (end != specConsts->struct_type->members.end())
                    {
                        LOG_EDITOR_ERROR("[ShaderCompile] Duplicated shader constant index!");
                        return false;
                    }
                }

                for (auto& member : specConsts->struct_type->members)
                {
                    member->offset = offset;
                    offset += member->size;
                }
            }
            else
            {
                std::sort(specConsts->members.begin(), specConsts->members.end(), [](auto& a, auto& b) { return a->index < b->index; });
                if (version.format() == ShaderCodeFormat::SPIR_V)
                {
                    auto end = std::unique(specConsts->members.begin(), specConsts->members.end(), [](auto& a, auto& b) { return a->index == b->index; });
                    if (end != specConsts->members.end())
                    {
                        LOG_EDITOR_ERROR("[ShaderCompile] Duplicated shader constant index!");
                        return false;
                    }
                }

                for (auto& member : specConsts->members)
                {
                    member->offset = offset;
                    offset += member->size;
                }
            }
            specConsts->size = offset;
        }
    }
    return true;
}
