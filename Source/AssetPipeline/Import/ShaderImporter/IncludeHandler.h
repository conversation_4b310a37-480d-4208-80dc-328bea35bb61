#pragma once
#include <mutex>
#include <filesystem>
#include <tuple>
#include <functional>
#include <CrossSchema/ShaderMaps_generated.h>

namespace cross::editor
{

struct FileCache
{
    std::vector<UInt8> data;
    int index;
    std::string relPath;
};

class IncludeHandler
{
public:
    IncludeHandler(const char* filePath);

    static const std::vector<std::string> GetShaderImportIncludes(const char* filePath);

    void RecordFinish(std::string shader_guid, bool saveToDisk, bool genDebugInfo = false);

    // Return size of buffer, 0 if not found.
    std::tuple<SizeType, const void*> OpenPath(const std::filesystem::path& relPath);
    std::tuple<SizeType, const void*> OpenAbsolutePath(const std::filesystem::path& dirPath, int dirIndex, const std::filesystem::path& relPath);

    const std::filesystem::path& GetFilePath();
    const std::filesystem::path& GetFileDirectoryPath();
    const std::vector<std::filesystem::path>& GetIncludePaths() { return mIncludePaths; }

private:
    std::vector<std::filesystem::path> mIncludePaths;
    std::map<std::filesystem::path, FileCache> mFileCache;
    std::mutex mIncludeMutex;
    std::filesystem::path mFilePath;
    std::filesystem::path mFileDirectoryPath;
    CrossSchema::ShaderSourceMapT mSourceMap;

private:
    // Use fixed include path order
    void AddHeaderDirectory(const std::filesystem::path& includeDirectory);
};
}   // namespace cross::editor