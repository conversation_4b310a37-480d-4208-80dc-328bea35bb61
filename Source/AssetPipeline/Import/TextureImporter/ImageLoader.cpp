#include "AssetPipeline/PCH/AssetPipelinePCH.h"
#include "ImageLoader.h"
#include "TextureImportSetting.h"
#include "basis_export.h"
#include "nvtt/nvtt.h"
#include "imageio.h"
#include "image.h"

#include "transcoder/basisu_transcoder.h"
#include "cmft/cubemapfilter.h"
#include "cmft/cubemaputils.h"
#include "cmft/image.h"
//#include "CrossImage/compress.cc"

namespace cross::editor
{
    void cmft_to_image(cmft::Image& src, imageio::image& dst)
    {
        cmft::imageConvert(src, cmft::TextureFormat::RGBA8);
        dst.resize(src.m_width, src.m_height);
        memcpy(dst.get_pixels().data(), src.m_data, dst.get_pixels().size() * 4);
        for (size_t i = 0; i < dst.get_pixels().size(); i+=1) 
        {
            Assert(dst.get_pixels().data()[i].r == 0);
            Assert(dst.get_pixels().data()[i].g == 0);
            Assert(dst.get_pixels().data()[i].b == 0);
            //std::cout << "Test" + (dst.get_pixels().data()[i]).r << std::endl;
        }
    }

    bool HasExtension(const char* cszFileName, const char* cszEx)
    {
        if (cszFileName == nullptr || cszEx == nullptr)
            return false;
        unsigned int nLen = (UInt32)strlen(cszFileName);
        unsigned int nExLen = (UInt32)strlen(cszEx);
        if (nLen <= nExLen)
            return false;
        const char* cszCmp = cszFileName + (nLen - nExLen);
    #if CROSSENGINE_OSX
        return strcasecmp(cszCmp, cszEx) == 0;
    #else
        return _stricmp(cszCmp, cszEx) == 0;
    #endif
    }

    basisu::EncodeParameter ConvertSetting(TextureImportSetting setting)
    {
        basisu::EncodeParameter parameter;
        parameter.mipmap = setting.GenerateMipmap;
        parameter.srgb = setting.ColorSpace == static_cast<cross::editor::ImportColorSpace>(CrossSchema::ColorSpace::SRGB);
        parameter.normal_map = setting.Type == TextureType::NormalTexture;
        if (setting.Compression == TextureCompression::CompressedBasisLQ) {
            parameter.compression_level = 1;
        } else if (setting.Compression == TextureCompression::CompressedBasisHQ) {
            parameter.compression_level = 6;
        }
        return parameter;
    }

    nvtt::Format ConvertToNvttFormat(TextureImportSetting setting) 
    {
        nvtt::Format format = nvtt::Format::Format_Count;
        if (setting.Type == TextureType::NormalTexture) 
        {
            format = nvtt::Format::Format_BC5;
        }
        else if (setting.Compression == TextureCompression::BC1)
        {
            format = nvtt::Format::Format_BC1;
        }
        else if (setting.Compression == TextureCompression::BC3)
        {
            format = nvtt::Format::Format_BC3;
        }
        else if (setting.Compression == TextureCompression::BC4)
        {
            format = nvtt::Format::Format_BC4;
        }
        else if (setting.Compression == TextureCompression::BC5)
        {
            format = nvtt::Format::Format_BC5;
        }
        else if (setting.Compression == TextureCompression::BC7)
        {
            format = nvtt::Format::Format_BC7;
        }
        else if (setting.Compression == TextureCompression::BC6H)
        {
            format = nvtt::Format::Format_BC6U;
        }
        return format;
    }

    bool ToBasis(CrossSchema::TextureAssetT &texture)
    {
        if (texture.format != TextureFormat::RGBA32 || texture.format == TextureFormat::BASIS_UNIVERSAL)
        {
            return false;
        }

        basisu::EncodeParameter parameter;
        parameter.srgb = texture.colorspace == CrossSchema::ColorSpace::SRGB;
        parameter.mipmap = true;

        auto& images = texture.images;
        std::vector<basisu::EncodedImage> encodeds;

        size_t offset = 0;
        size_t imageCount = images.size();
        for (size_t i = 0; i < imageCount; ++i)
        {
            auto& image = images[i];
            UInt32 width      = image.width();
            UInt32 height     = image.height();

            basisu::EncodedImage encoded;
#if !CROSSENGINE_OSX
            if (!basisu::BasisEncodeRGBA(texture.data.data() + image.dataoffset(), image.databytesize(), width, height, encoded, parameter))
            {
                return false;
            }
#else
            Assert(0);
#endif

            encodeds.emplace_back(encoded);
            image.mutate_dataoffset(static_cast<UInt32>(offset));
            image.mutate_databytesize(encoded.data_size);
            offset += encoded.data_size;
        }

        texture.data.resize(offset);
        offset = 0;
        for (size_t i = 0; i < imageCount; ++i)
        {
            auto& encoded = encodeds[i];
            memcpy(texture.data.data() + offset, encoded.data, encoded.data_size);
#if !CROSSENGINE_OSX
            basisu::BasisEncodeFree(encoded);
#else
            Assert(0);
#endif
            offset += encoded.data_size;
        }
        texture.format = TextureFormat::BASIS_UNIVERSAL;
        return true;
    }
    CrossSchema::TextureFormat MapToCEFormat(cmft::TextureFormat::Enum importformat) 
    {
        if (importformat == cmft::TextureFormat::RGBA16F)
            return CrossSchema::TextureFormat::RGBAHalf;
        else if (importformat == cmft::TextureFormat::RGBA32F)
            return CrossSchema::TextureFormat::RGBAFloat;
        else
            return CrossSchema::TextureFormat::RGBA32;
    }
    bool LoadImageRaw(cmft::Image &src, CrossSchema::TextureAssetT &texture, TextureImportSetting setting)
    {
        uint32_t total_levels = 1;
        uint32_t w = src.m_width, h = src.m_height;
        while (std::max(w, h) > 1)
        {
            w = std::max(w >> 1U, 1U);
            h = std::max(h >> 1U, 1U);
            total_levels++;
        }
        std::vector<cmft::Image> mips;
        mips.reserve(total_levels);
        mips.push_back(src);  

        if (src.m_format == cmft::TextureFormat::RGBA16F || src.m_format == cmft::TextureFormat::RGBA32F)
        {
            cmft::imageToRgba32f(src);
        }
        else
        {
            cmft::imageConvert(src, cmft::TextureFormat::RGBA8);
        }
        if (setting.GenerateMipmap) {
            cmft::imageGenerateMipMapChain(src);
        }

        uint32_t offsets[CUBE_FACE_NUM][MAX_MIP_NUM];
        cmft::imageGetMipOffsets(offsets, src);

        std::vector<CrossSchema::TextureAssetImage> texImages;
        size_t total_size = 0;

        auto image_info = cmft::getImageDataInfo(src.m_format);
        uint32_t bytes_pre_pixel = image_info.m_bytesPerPixel;
        for (uint8_t i = 0; i < src.m_numMips; ++i) {
            const uint8_t mip = std::min(i, uint8_t(src.m_numMips - 1));
            const uint32_t mip_width = std::max(UINT32_C(1), src.m_width >> mip);
            const uint32_t mip_height = std::max(UINT32_C(1), src.m_height >> mip);
            const uint32_t mip_pitch = mip_width * bytes_pre_pixel;
            const uint8_t* mip_data = (const uint8_t*)src.m_data + offsets[0][mip];
            const uint32_t mip_data_size = mip_pitch * mip_height;

            auto& image = texImages.emplace_back();
            image.mutate_width(mip_width);
            image.mutate_height(mip_height);
            image.mutate_dataoffset(static_cast<uint32_t>(total_size));
            image.mutate_databytesize(mip_data_size);
            image.mutate_depth(1);
            image.mutate_rowpitch(mip_pitch);

            size_t offset = total_size;
            total_size += static_cast<size_t>(mip_data_size);
            texture.data.resize(total_size);
            memcpy(texture.data.data() + offset, mip_data, mip_data_size);
        }

        texture.images = texImages;
        texture.mipcount = static_cast<uint32_t>(texImages.size());
        if (setting.Type == TextureType::LUT_Cube)
            texture.format = TextureFormat::RGBAFloat;
        else 
            texture.format = MapToCEFormat(src.m_format);
        texture.dimension = TextureDimension::Tex2D;
        texture.colorspace = static_cast<CrossSchema::ColorSpace>(setting.ColorSpace);
        texture.flags = 0;
        return true;
    }

    Float4 HSVToLinearRGB(const Float4& inRGBA)
    {
        const float Hue = inRGBA.x;
        const float Saturation = inRGBA.y;
        const float Value = inRGBA.z;

        const float HDiv60 = Hue / 60.0f;
        const float HDiv60_Floor = floorf(HDiv60);
        const float HDiv60_Fraction = HDiv60 - HDiv60_Floor;

        const float RGBValues[4] = {
            Value,
            Value * (1.0f - Saturation),
            Value * (1.0f - (HDiv60_Fraction * Saturation)),
            Value * (1.0f - ((1.0f - HDiv60_Fraction) * Saturation)),
        };

        const UInt32 RGBSwizzle[6][3] = {
            {0, 3, 1},
            {2, 0, 1},
            {1, 0, 3},
            {1, 2, 0},
            {3, 1, 0},
            {0, 1, 2},
        };
        const UInt32 SwizzleIndex = ((UInt32)HDiv60_Floor) % 6;

        return Float4(RGBValues[RGBSwizzle[SwizzleIndex][0]], RGBValues[RGBSwizzle[SwizzleIndex][1]], RGBValues[RGBSwizzle[SwizzleIndex][2]], inRGBA.w);
    }

    Float4 LinearRGBToHSV(Float4& inRGBA)
    {
        const float RGBMin = cross::FloatMin(cross::FloatMin(inRGBA.x, inRGBA.y), inRGBA.z);
        const float RGBMax = cross::FloatMax(cross::FloatMax(inRGBA.x, inRGBA.y), inRGBA.z);
        const float RGBRange = RGBMax - RGBMin;

        const float Hue = (RGBMax == RGBMin     ? 0.0f
                           : RGBMax == inRGBA.x ? fmod((((inRGBA.y - inRGBA.z) / RGBRange) * 60.0f) + 360.0f, 360.0f)
                           : RGBMax == inRGBA.y ? (((inRGBA.z - inRGBA.x) / RGBRange) * 60.0f) + 120.0f
                           : RGBMax == inRGBA.z ? (((inRGBA.x - inRGBA.y) / RGBRange) * 60.0f) + 240.0f
                                                : 0.0f);

        const float Saturation = (RGBMax == 0.0f ? 0.0f : RGBRange / RGBMax);
        const float Value = RGBMax;

        // In the new color, R = H, G = S, B = V, A = A
        return Float4(Hue, Saturation, Value, inRGBA.w);
    }

    void GetLinearColorValue(cmft::Image& src, TextureResourceInfo info)
    {
        auto head = reinterpret_cast<UInt8*>(src.m_data);
        const UInt64 numPixels = (UInt64)src.m_width * src.m_height;
        UInt64 numPixelsEachJob;
        UInt32 numJobs = ImageParallelForComputeNumJobsForPixels(numPixelsEachJob, numPixels);
        threading::ParallelFor(numJobs, [&](UInt32 index) {
            UInt64 startIndex = index * numPixelsEachJob;
            UInt64 endIndex = std::min(startIndex + numPixelsEachJob, numPixels);
            for (UInt64 curPixelIndex = startIndex; curPixelIndex < endIndex; ++curPixelIndex)
            {
                ProcessColorAdjustments(head[curPixelIndex * 4], head[(curPixelIndex * 4) + 1], head[(curPixelIndex * 4) + 2], head[(curPixelIndex * 4) + 3], info);
            }
        });
    }

    void ProcessColorAdjustments(UInt8& x, UInt8& y, UInt8& z, UInt8& w, TextureResourceInfo info) 
    {
        const float constValue = 1 / 255.0f;
        float X = static_cast<float>(x * constValue);
        float Y = static_cast<float>(y * constValue);
        float Z = static_cast<float>(z * constValue);
        float W = static_cast<float>(w * constValue);

        Adjustments adjust = info.Adjustments;
        const bool bShouldClampValue = (X <= 1.0f && Y <= 1.0f && Z <= 1.0f);

        Float4 temp{X, Y, Z, W};
        Float4 hsvColor = LinearRGBToHSV(temp);
        float& pixelHue = hsvColor.x;
        float& pixelSaturation = hsvColor.y;
        float& pixelValue = hsvColor.z;

        pixelValue *= adjust.Brightness;
        if (!(abs(adjust.BrightnessCurve - 1.0f) < 1.e-4f) && adjust.BrightnessCurve != 0.0f) 
        {
            pixelValue = pow(pixelValue, adjust.BrightnessCurve);
        }

        if (!(abs(adjust.Vibrance < 1.e-4f))) 
        {
            const float satRaisePow = 5.0f;
            const float InvSatRaised = static_cast<float>(pow(1.0 - pixelSaturation, satRaisePow));

            const float clampedVibrance = cross::editor::clamp(adjust.Vibrance, 0.0f, 1.0f);
            const float halfVibrance = clampedVibrance * 0.5f;

            const float satProduct = halfVibrance * InvSatRaised;

            pixelSaturation += satProduct;
        }

        pixelSaturation *= adjust.Saturation;

        pixelHue += adjust.Hue;

        {
            pixelHue = fmod(pixelHue, 360.0f);
            if (pixelHue < 0.0f) 
            {
                pixelHue += 360.0f;
            }
            pixelSaturation = cross::editor::clamp(pixelSaturation, 0.0f, 1.0f);

            if (bShouldClampValue) 
            {
                pixelValue = cross::editor::clamp(pixelValue, 0.0f, 1.0f);
            }
        }

        Float4 linearColor = HSVToLinearRGB(hsvColor);
        linearColor.w = Lerp(adjust.MinAlpha, adjust.MaxAlpha, W);
        x = static_cast<UInt8>(linearColor.x * 255);
        y = static_cast<UInt8>(linearColor.y * 255);
        z = static_cast<UInt8>(linearColor.z * 255);
        w = static_cast<UInt8>(linearColor.w * 255);
    }

    bool LoadImageBC(cmft::Image& src, CrossSchema::TextureAssetT& texture, TextureImportSetting setting) 
    {
#if CROSSENGINE_WIN
        imageio::GPUImage gpu_image;
        nvtt::Format format = ConvertToNvttFormat(setting);
        if (format >= nvtt::Format::Format_BC1 && format <= nvtt::Format::Format_BC3_RGBM)
        {
            bool srgb = false;
            if (setting.Type == TextureType::NormalTexture || format == nvtt::Format::Format_BC6U) 
            {
                srgb = false;
            }
            else
            {
                srgb = setting.ColorSpace == cross::editor::ImportColorSpace::SRGB;
            }
            bool result = imageio::compress2dxbcnvtt(src, gpu_image, false, setting.GenerateMipmap, setting.GeneratePrefilterMipmap, format, false);
            if (result) 
            {
                std::vector<CrossSchema::TextureAssetImage> texImages;
                texture.data.resize(gpu_image.m_total_size);
                int mip_count = gpu_image.m_mip_count;
                for (int i = 0; i < gpu_image.m_mipmaps.size(); ++i)
                {
                    int mip = i % mip_count;
                    auto& mipmap = gpu_image.m_mipmaps[i];
                    auto& image = texImages.emplace_back();
                    image.mutate_width(mipmap.m_width);
                    image.mutate_height(mipmap.m_height);
                    image.mutate_databytesize(mipmap.m_data_size);
                    image.mutate_depth(1);
                    image.mutate_rowpitch(mipmap.m_pitch);
                    uint32_t offset = gpu_image.m_mip_offsets[0][mip];
                    image.mutate_dataoffset(offset);
                    memcpy(texture.data.data() + offset, mipmap.m_data, mipmap.m_data_size);

                    free(mipmap.m_data);
                    mipmap.m_data = nullptr;
                }

                texture.images = texImages;
                texture.mipcount = static_cast<UInt32>(mip_count);
                if (format == nvtt::Format::Format_BC1)
                {
                    texture.format = TextureFormat::BC1;
                }
                else if (format == nvtt::Format::Format_BC3)
                {
                    texture.format = TextureFormat::BC3;
                }
                else if (format == nvtt::Format::Format_BC4)
                {
                    texture.format = TextureFormat::BC4;
                }
                else if (format == nvtt::Format::Format_BC5)
                {
                    texture.format = TextureFormat::BC5;
                }
                else if (format == nvtt::Format::Format_BC7)
                {
                    texture.format = TextureFormat::BC7;
                }
                else if (format == nvtt::Format::Format_BC6U)
                {
                    texture.format = TextureFormat::BC6H;
                }
                texture.dimension = TextureDimension::Tex2D;
                //BC4 only has unorm
                if (format == nvtt::Format::Format_BC4 || format == nvtt::Format::Format_BC6U) 
                {
                    texture.colorspace = CrossSchema::ColorSpace::Linear;
                }
                else
                {
                    texture.colorspace = static_cast<CrossSchema::ColorSpace>(setting.ColorSpace);
                }
                texture.flags = 0;

                return true;
            }
        }
        return false;
#else
        return false;
#endif
    }

    bool LoadImageBasis(cmft::Image& src, CrossSchema::TextureAssetT& texture, TextureImportSetting setting)
    {
        constexpr UInt32 BlockSize = 4;
        imageio::image img;
        cmft_to_image(src, img);

        if (setting.OpenGLESCompatible)
        {
            auto width = img.get_width();
            auto height = img.get_height();
            if (width >= BlockSize && width % BlockSize != 0)
            {
                width = ((width - 1) / BlockSize + 1) * BlockSize;
            }
            if (height >= BlockSize && height % BlockSize != 0)
            {
                height = ((height - 1) / BlockSize + 1) * BlockSize;
            }

            img.resize(width, height);
        }

        uint32_t width = img.get_width();
        uint32_t height = img.get_height();
        uint32_t pixelSize = img.get_total_pixels() * sizeof(imageio::color_rgba);

        std::vector<uint8_t> data;
        data.resize(pixelSize);
        memcpy(data.data(), img.get_pixels().data(), pixelSize);
    
        basisu::EncodedImage encoded;
#if !CROSSENGINE_OSX
        if (!basisu::BasisEncodeRGBA(data.data(), pixelSize, img.get_pitch(), img.get_height(), encoded, ConvertSetting(setting)))
        {
          return false;
        }
#else
        Assert(0);
#endif

        std::vector<CrossSchema::TextureAssetImage> texImages;
        texture.data.resize(encoded.data_size);
        memcpy(texture.data.data(), encoded.data, encoded.data_size);

        auto& image = texImages.emplace_back();
        image.mutate_width(width);
        image.mutate_height(height);
        image.mutate_dataoffset(0);
        image.mutate_databytesize(encoded.data_size);
        image.mutate_depth(1);
        image.mutate_rowpitch(width * sizeof(imageio::color_rgba));

        texture.images = texImages;
        texture.mipcount = static_cast<UInt32>(texImages.size());
        texture.format = TextureFormat::BASIS_UNIVERSAL;
        texture.dimension = TextureDimension::Tex2D;
        texture.colorspace = static_cast<CrossSchema::ColorSpace>(setting.ColorSpace);
        texture.flags = 0;
        return true;
    }

    bool LoadImageCubeRaw(cmft::Image& src, CrossSchema::TextureAssetT& texture, TextureImportSetting setting, std::vector<Float3>& outShCoefs)
    {
        //cmft::imageToCubemap(src);
        cmft::imageToRgba32f(src);
        cmft::imageToCubemap(src);

        if (setting.GenerateMipmap)
        {
            cmft::imageGenerateMipMapChain(src);
        }
        if (setting.GeneratePrefilterMipmap) 
        {
            cmft::imageRadianceFilter(src,
                                      0,
                                      cmft::LightingModel::Enum::Phong,
                                      true,
                                      UINT8_MAX,
                                      static_cast<uint8_t>(src.m_numMips + setting.GlossBias, 0),
                                      static_cast<uint8_t>(setting.GlossBias),
                                      cmft::EdgeFixup::Warp,
                                      static_cast<uint8_t>(std::thread::hardware_concurrency()));
        }

        double shCoef[SH_COEFF_NUM][3];
        if (!cmft::imageShCoeffs(shCoef, src)) {
            return false;
        }
        outShCoefs.resize(SH_COEFF_NUM);
        for (auto i = 0; i < outShCoefs.size(); i++)
        {
            outShCoefs[i].x = static_cast<float>(shCoef[i][0]);
            outShCoefs[i].y = static_cast<float>(shCoef[i][1]);
            outShCoefs[i].z = static_cast<float>(shCoef[i][2]);
        }

        uint32_t offsets[CUBE_FACE_NUM][MAX_MIP_NUM];
        cmft::imageGetMipOffsets(offsets, src);

        auto image_info = cmft::getImageDataInfo(src.m_format);
        uint32_t bytes_pre_pixel = image_info.m_bytesPerPixel;

        std::vector<CrossSchema::TextureAssetImage> texImages;

        size_t total_size = 0;
        for (uint8_t face = 0; face < src.m_numFaces; ++face)
        {
            for (uint8_t mip = 0; mip < src.m_numMips; ++mip)
            {
                const uint8_t mip_level = std::min(mip, uint8_t(src.m_numMips - 1));
                const uint32_t mip_width = std::max(UINT32_C(1), src.m_width >> mip_level);
                const uint32_t mip_height = std::max(UINT32_C(1), src.m_height >> mip_level);
                const uint32_t mip_pitch = mip_width * bytes_pre_pixel;
                const uint8_t* mip_data = reinterpret_cast<const uint8_t*>(src.m_data) + offsets[face][mip_level];
                const uint32_t mip_data_size = mip_pitch * mip_height;

                auto& image = texImages.emplace_back();
                image.mutate_width(mip_width);
                image.mutate_height(mip_height);
                image.mutate_dataoffset(static_cast<uint32_t>(total_size));
                image.mutate_databytesize(mip_data_size);
                image.mutate_depth(1);
                image.mutate_rowpitch(mip_pitch);

                size_t offset = total_size;
                total_size += static_cast<size_t>(mip_data_size);
                texture.data.resize(total_size);
                memcpy(texture.data.data() + offset, mip_data, mip_data_size);
            }
        }

        texture.images     = texImages;
        texture.mipcount   = src.m_numMips;
        texture.format     = TextureFormat::RGBA32;
        texture.dimension  = TextureDimension::TexCube;
        texture.colorspace = static_cast<CrossSchema::ColorSpace>(setting.ColorSpace);
        texture.flags      = 0;

        return true;
    }
    
    bool LoadImageCubeBC(cmft::Image& src, CrossSchema::TextureAssetT& texture, TextureImportSetting setting, std::vector<Float3>& outShCoefs) {
#if CROSSENGINE_WIN

        //TransformCubeMap(src);

        imageio::GPUImage gpu_image;
        nvtt::Format format = ConvertToNvttFormat(setting);
        if (format >= nvtt::Format::Format_BC1 && format <= nvtt::Format::Format_BC3_RGBM)
        {
            bool srgb = false;
            if (setting.Type == TextureType::NormalTexture || format == nvtt::Format::Format_BC6U)
            {
                srgb = false;
            }
            else
            {
                srgb = setting.ColorSpace == cross::editor::ImportColorSpace::SRGB;
            }
            bool result = imageio::compress2dxbcnvtt(src, gpu_image, true, setting.GenerateMipmap, setting.GeneratePrefilterMipmap, format, false, setting.GlossBias);
            if (result)
            {
                double shCoef[SH_COEFF_NUM][3];
                if (!cmft::imageShCoeffs(shCoef, src))
                {
                    return false;
                }
                outShCoefs.resize(SH_COEFF_NUM);
                for (auto i = 0; i < outShCoefs.size(); i++)
                {
                    outShCoefs[i].x = static_cast<float>(shCoef[i][0]);
                    outShCoefs[i].y = static_cast<float>(shCoef[i][1]);
                    outShCoefs[i].z = static_cast<float>(shCoef[i][2]);
                }

                std::vector<CrossSchema::TextureAssetImage> texImages;
                texture.data.resize(gpu_image.m_total_size);
                int mip_count = gpu_image.m_mip_count;
                for (int i = 0; i < gpu_image.m_mipmaps.size(); ++i)
                {
                    int face = static_cast<int>(floor(i / mip_count));
                    int mip = i % mip_count;
                    auto& mipmap = gpu_image.m_mipmaps[i];
                    auto& image = texImages.emplace_back();
                    image.mutate_width(mipmap.m_width);
                    image.mutate_height(mipmap.m_height);
                    image.mutate_databytesize(mipmap.m_data_size);
                    image.mutate_depth(1);
                    image.mutate_rowpitch(mipmap.m_pitch);
                    uint32_t offset = gpu_image.m_mip_offsets[face][mip];
                    image.mutate_dataoffset(offset);
                    memcpy(texture.data.data() + offset, mipmap.m_data, mipmap.m_data_size);
                }

                texture.images = texImages;
                texture.mipcount = static_cast<UInt32>(mip_count);
                if (format == nvtt::Format::Format_BC1)
                {
                    texture.format = TextureFormat::BC1;
                }
                else if (format == nvtt::Format::Format_BC3)
                {
                    texture.format = TextureFormat::BC3;
                }
                else if (format == nvtt::Format::Format_BC4)
                {
                    texture.format = TextureFormat::BC4;
                }
                else if (format == nvtt::Format::Format_BC7)
                {
                    texture.format = TextureFormat::BC7;
                }
                else if (format == nvtt::Format::Format_BC6U)
                {
                    texture.format = TextureFormat::BC6H;
                }
                texture.dimension = TextureDimension::Tex2D;
                // BC4 only has unorm
                if (format == nvtt::Format::Format_BC4 || format == nvtt::Format::Format_BC6U)
                {
                    texture.colorspace = CrossSchema::ColorSpace::Linear;
                }
                else
                {
                    texture.colorspace = static_cast<CrossSchema::ColorSpace>(setting.ColorSpace);
                }
                texture.dimension = TextureDimension::TexCube;
                texture.flags = 0;

                return true;
            }
        }
        return false;
#else
        return false;
#endif
    }

    bool LoadImage2DArrayRaw(cmft::Image& src, CrossSchema::TextureAssetT& texture, TextureImportSetting setting)
    {
        return false;
    }

    bool LoadImage2DArrayBC(cmft::Image& src, CrossSchema::TextureAssetT& texture, TextureImportSetting setting)
    {
        return false;
    }

#if CROSSENGINE_WIN
    bool LoadImageCubeHDR(cmft::Image& src, CrossSchema::TextureAssetT& texture, TextureImportSetting setting, std::vector<Float3>& outShCoefs)
    {
        imageio::GPUImage gpu_image;

        double shCoef[SH_COEFF_NUM][3];
        if (imageio::compress_hdr(src, gpu_image, shCoef) == false)
        {
            return false;
        }
        outShCoefs.resize(SH_COEFF_NUM);
        for (auto i = 0; i < outShCoefs.size(); i++)
        {
            outShCoefs[i].x = static_cast<float>(shCoef[i][0]);
            outShCoefs[i].y = static_cast<float>(shCoef[i][1]);
            outShCoefs[i].z = static_cast<float>(shCoef[i][2]);
        }

        std::vector<CrossSchema::TextureAssetImage> texImages;
        texture.data.resize(gpu_image.m_total_size);
        int mip_count = gpu_image.m_mip_count;

        for (int i = 0; i < gpu_image.m_mipmaps.size(); ++i)
        {
            int face = static_cast<int>(floor(i / mip_count));
            int mip = i % mip_count;
            auto& mipmap = gpu_image.m_mipmaps[i];
            auto& image = texImages.emplace_back();
            image.mutate_width(mipmap.m_width);
            image.mutate_height(mipmap.m_height);
            image.mutate_databytesize(mipmap.m_data_size);
            image.mutate_depth(1);
            image.mutate_rowpitch(mipmap.m_pitch);
            uint32_t offset = gpu_image.m_mip_offsets[face][mip];
            image.mutate_dataoffset(offset);
            memcpy(texture.data.data() + offset, mipmap.m_data, mipmap.m_data_size);
        }

        texture.images = texImages;
        texture.mipcount = static_cast<UInt32>(mip_count);
        texture.format = TextureFormat::BC6H;
        texture.dimension = TextureDimension::TexCube;
        texture.colorspace = CrossSchema::ColorSpace::Linear; // float texture force linear
        texture.flags = 0;

        return true;
    }
    
    void ProcessBCFormatNVTT(std::vector<cmft::Image>& images, imageio::GPUImage& gpu_image, TextureImportSetting setting)
    {
        using namespace nvtt;
        bool useGPUAccel = nvtt::isCudaSupported();
        nvtt::Format dxformat = ConvertToNvttFormat(setting);
        auto& mipmaps = gpu_image.m_mipmaps;
        uint32_t offset = 0;

        gpu_image.m_mip_count = static_cast<int>(images.size());
        gpu_image.m_width = images[0].m_width;
        gpu_image.m_height = images[0].m_height;
        gpu_image.m_cubemap = false;

        for (size_t i = 0; i < images.size(); i++)
        {
            // Get src data ptr for current mip.
            cmft::Image curImage;
            cmft::imageFromRgba32f(curImage, cmft::TextureFormat::RGBA8, images[i]);
            const uint8_t* mip_data = (const uint8_t*)curImage.m_data;
            const uint32_t mip_width = curImage.m_width;
            const uint32_t mip_height = curImage.m_height;

            RefImage nv_image;
            nv_image.width = mip_width;
            nv_image.height = mip_height;
            nv_image.num_channels = 4;
            nv_image.depth = 1;
            nv_image.data = mip_data;
            void* output_buffer = nullptr;
            uint32_t output_size = 0;

            nvtt::CPUInputBuffer cpu_buffer(&nv_image, nvtt::ValueType::UINT8);
            output_size = imageio::compute_compressed_size(dxformat, mip_width, mip_height);
            output_buffer = malloc(output_size);
            if (dxformat == nvtt::Format::Format_BC1)
            {
                nvtt::nvtt_encode_bc1(cpu_buffer, false, output_buffer, useGPUAccel, false);
            }
            else if (dxformat == nvtt::Format::Format_BC3)
            {
                nvtt::nvtt_encode_bc3(cpu_buffer, false, output_buffer, useGPUAccel, false);
            }
            else if (dxformat == nvtt::Format::Format_BC4)
            {
                nvtt::nvtt_encode_bc4(cpu_buffer, false, output_buffer, useGPUAccel, false);
            }
            else if (dxformat == nvtt::Format::Format_BC5)
            {
                nvtt::nvtt_encode_bc5(cpu_buffer, false, output_buffer, useGPUAccel, false);
            }
            else if (dxformat == nvtt::Format::Format_BC7)
            {
                nvtt::nvtt_encode_bc7(cpu_buffer, false, true, output_buffer, useGPUAccel, false);
            }

            imageio::GPUImageData& image_data = mipmaps.emplace_back();
            image_data.m_width = mip_width;
            image_data.m_height = mip_height;
            image_data.m_format = dxformat;
            image_data.m_data = output_buffer;
            image_data.m_data_size = output_size;
            image_data.m_pitch = imageio::compute_pitch(image_data.m_format, mip_width);

            gpu_image.m_total_size += output_size;
            gpu_image.m_mip_offsets[0][i] = offset;
            offset += output_size;
        }
    }

    void NVTTTransformTexture(CrossSchema::TextureAssetT& texture, TextureImportSetting setting, imageio::GPUImage& gpu_image)
    {
        nvtt::Format format = ConvertToNvttFormat(setting);
        std::vector<CrossSchema::TextureAssetImage> texImages;
        texture.data.resize(gpu_image.m_total_size);
        int mip_count = gpu_image.m_mip_count;
        for (int i = 0; i < gpu_image.m_mipmaps.size(); ++i)
        {
            int face = static_cast<int>(floor(i / mip_count));
            int mip = i % mip_count;
            auto& mipmap = gpu_image.m_mipmaps[i];
            auto& image = texImages.emplace_back();
            image.mutate_width(mipmap.m_width);
            image.mutate_height(mipmap.m_height);
            image.mutate_databytesize(mipmap.m_data_size);
            image.mutate_depth(1);
            image.mutate_rowpitch(mipmap.m_pitch);
            uint32_t offset = gpu_image.m_mip_offsets[face][mip];
            image.mutate_dataoffset(offset);
            memcpy(texture.data.data() + offset, mipmap.m_data, mipmap.m_data_size);
        }

        texture.images = texImages;
        texture.mipcount = static_cast<UInt32>(mip_count);
        if (format == nvtt::Format::Format_BC1)
        {
            texture.format = TextureFormat::BC1;
        }
        else if (format == nvtt::Format::Format_BC3)
        {
            texture.format = TextureFormat::BC3;
        }
        else if (format == nvtt::Format::Format_BC4)
        {
            texture.format = TextureFormat::BC4;
        }
        else if (format == nvtt::Format::Format_BC5)
        {
            texture.format = TextureFormat::BC5;
        }
        else if (format == nvtt::Format::Format_BC7)
        {
            texture.format = TextureFormat::BC7;
        }
        else if (format == nvtt::Format::Format_BC6U)
        {
            texture.format = TextureFormat::BC6H;
        }
        texture.dimension = TextureDimension::Tex2D;
        // BC4 only has unorm
        if (format == nvtt::Format::Format_BC4 || format == nvtt::Format::Format_BC6U)
        {
            texture.colorspace = CrossSchema::ColorSpace::Linear;
        }
        else
        {
            texture.colorspace = static_cast<CrossSchema::ColorSpace>(setting.ColorSpace);
        }
        texture.dimension = TextureDimension::Tex2D;
        texture.flags = 0;
    }

#endif

    void Transcode(CrossSchema::TextureAssetT& texture, TextureFormat format)
    {
        basist::transcoder_texture_format transcode_format = basist::transcoder_texture_format::cTFBC7_RGBA;
        if (format == TextureFormat::BC7)
        {
            transcode_format = basist::transcoder_texture_format::cTFBC7_RGBA;
        }
        else if (format == TextureFormat::ASTC_4x4) {
            transcode_format = basist::transcoder_texture_format::cTFASTC_4x4_RGBA;
        }
        else if (format == TextureFormat::PVRTC_RGBA4)
        {
            transcode_format = basist::transcoder_texture_format::cTFPVRTC2_4_RGBA;
        } else if (format == TextureFormat::ETC2_RGBA8)
        {
            transcode_format = basist::transcoder_texture_format::cTFETC2_RGBA;
        }
        else
        {
            Assert(false);
        }

        UInt32 size = 0;
        UInt32 imageCount = 0;
        std::vector<basist::TranscodedImage> transcodedImages;
        for (UInt32 i = 0, l = UInt32(texture.images.size()); i < l; ++i)
        {
            const auto& img = texture.images[i];

            UInt8* data = texture.data.data() + img.dataoffset();
            uint32_t dataSize = img.databytesize();
            
            basist::TranscodedImage transcoded;
            basist::BasisTranscode(data, dataSize, static_cast<uint32_t>(transcode_format), transcoded);

            transcodedImages.emplace_back(transcoded);
            size += transcoded.total_data_size;
            imageCount += transcoded.level_count;
        }

        texture.data.resize(size);
        std::vector<CrossSchema::TextureAssetImage> images;
        images.resize(imageCount);

        UInt32 imageIndex = 0;
        UInt32 offset = 0;
        UInt32 levelCount = 1;
        for (UInt32 i = 0, il = UInt32(transcodedImages.size()); i < il; ++i)
        {
            auto& transcodedImage = transcodedImages[i];
            for (UInt32 l = 0; l < transcodedImage.level_count; ++l)
            {
                auto& img = images[imageIndex++];
                auto& level = transcodedImage.levels[l];
                memcpy(texture.data.data() + offset, level.data, level.data_size);

                img.mutate_width(level.width);
                img.mutate_height(level.height);
                img.mutate_rowpitch(level.pitch);
                img.mutate_dataoffset(offset);
                img.mutate_depth(1);
                img.mutate_databytesize(level.data_size);

                offset += level.data_size;
            }
            levelCount = std::max(transcodedImage.level_count, levelCount);
            basist::BasisTranscodeFree(transcodedImage);
        }

        texture.images = images;
        texture.format = format;
        texture.data.resize(size);
        texture.mipcount = static_cast<UInt32>(images.size());
    }

    class ImageKernel2D
    {
    public:
        ImageKernel2D()
            : mFilterTableSize(0)
        {
        }

        void BuildSeparatableGaussWithSharpen(uint32_t tableSize1D, float sharpenFactor = 0.0f) 
        {
            //float tempFactor = sharpenFactor;
            if (tableSize1D > maxKernelExtend)
            {
                tableSize1D = maxKernelExtend;
            }

            float Table1D[maxKernelExtend];
            float NegativeTable1D[maxKernelExtend];

            mFilterTableSize = tableSize1D;

            if (sharpenFactor < 0.0f)
            {
                // blur only
                BuildGaussian1D(Table1D, tableSize1D, 1.0f, -sharpenFactor);
                BuildFilterTable2DFrom1D(mKernelWeights, Table1D, tableSize1D);
                return;
            }
            else if (tableSize1D == 2)
            {
                // 2x2 kernel: simple average
                mKernelWeights[0] = mKernelWeights[1] = mKernelWeights[2] = mKernelWeights[3] = 0.25f;
                return;
            }
            else if (tableSize1D == 4)
            {
                // 4x4 kernel with sharpen or blur: can alias a bit
                BuildFilterTable1DBase(Table1D, tableSize1D, 1.0f + sharpenFactor);
                BuildFilterTable1DBase(NegativeTable1D, tableSize1D, -sharpenFactor);
                BlurFilterTable1D(NegativeTable1D, tableSize1D, 1);
            }
            else if (tableSize1D == 6)
            {
                // 6x6 kernel with sharpen or blur: still can alias
                BuildFilterTable1DBase(Table1D, tableSize1D, 1.0f + sharpenFactor);
                BuildFilterTable1DBase(NegativeTable1D, tableSize1D, -sharpenFactor);
                BlurFilterTable1D(NegativeTable1D, tableSize1D, 2);
            }
            else if (tableSize1D == 8)
            {
                // 8x8 kernel with sharpen or blur

                // * 2 to get similar appearance as for TableSize 6
                sharpenFactor = sharpenFactor * 2.0f;
                //tempFactor *= 2;

                BuildFilterTable1DBase(Table1D, tableSize1D, 1.0f + sharpenFactor);
                // positive lobe is blurred a bit for better quality
                BlurFilterTable1D(Table1D, tableSize1D, 1);
                BuildFilterTable1DBase(NegativeTable1D, tableSize1D, -sharpenFactor);
                BlurFilterTable1D(NegativeTable1D, tableSize1D, 3);
            }
            else
            {
                // not yet supported
            }

            AddFilterTable1D(Table1D, NegativeTable1D, tableSize1D);
            BuildFilterTable2DFrom1D(mKernelWeights, Table1D, tableSize1D);
        }

        inline int GetFilterTableSize() const 
        {
            return mFilterTableSize;
        }

        inline float GetAt(int x, int y) const
        {
            return mKernelWeights[x + y * mFilterTableSize];
        }

    public:
        inline static float NormalDistribution(float x, float variance) 
        {
            const float standardDeviation = sqrt(variance);
            return exp(-(x * x) / (2.0f * variance)) / (standardDeviation * sqrt(2.0f * PI));
        }

        static void BuildGaussian1D(float* inOutTable, uint32_t TableSize, float Sum, float Variance)
        {
            float Center = TableSize * 0.5f;
            float CurrentSum = 0;
            for (uint32_t i = 0; i < TableSize; ++i)
            {
                float Actual = NormalDistribution(i - Center + 0.5f, Variance);
                inOutTable[i] = Actual;
                CurrentSum += Actual;
            }
            // Normalize
            float InvSum = Sum / CurrentSum;
            for (uint32_t i = 0; i < TableSize; ++i)
            {
                inOutTable[i] *= InvSum;
            }
        }

        static void BlurFilterTable1D(float* InOutTable, uint32_t TableSize, uint32_t Times)
        {
            float Intermediate[32];

            for (uint32_t Pass = 0; Pass < Times; ++Pass)
            {
                for (uint32_t x = 0; x < TableSize; ++x)
                {
                    Intermediate[x] = InOutTable[x];
                }

                for (uint32_t x = 0; x < TableSize; ++x)
                {
                    float sum = Intermediate[x];

                    if (x)
                    {
                        sum += Intermediate[x - 1];
                    }
                    if (x < TableSize - 1)
                    {
                        sum += Intermediate[x + 1];
                    }

                    InOutTable[x] = sum / 3.0f;
                }
            }
        }

        static void BuildFilterTable1DBase(float* InOutTable, uint32_t TableSize, float Sum)
        {
            // we require a even sized filter
            float Inner = 0.5f * Sum;
            uint32_t Center = TableSize / 2;
            for (uint32_t x = 0; x < TableSize; ++x)
            {
                if (x == Center || x == Center - 1)
                {
                    // center elements
                    InOutTable[x] = Inner;
                }
                else
                {
                    // outer elements
                    InOutTable[x] = 0.0f;
                }
            }
        }

        static void BuildFilterTable2DFrom1D(float* OutTable2D, float* InTable1D, uint32_t TableSize) 
        {
            for (uint32_t y = 0; y < TableSize; ++y)
            {
                for (uint32_t x = 0; x < TableSize; ++x)
                {
                    OutTable2D[x + y * TableSize] = InTable1D[y] * InTable1D[x];
                }
            }
        }

        static void AddFilterTable1D(float* InOutTable, float* InTable, uint32_t TableSize)
        {
            for (uint32_t x = 0; x < TableSize; ++x)
            {
                InOutTable[x] += InTable[x];
            }
        }

        const static int maxKernelExtend = 12;
        int mFilterTableSize;
        float mKernelWeights[maxKernelExtend * maxKernelExtend];
    };

    Float4 LookupSourceMip(const cmft::Image& sourceData, uint32_t x, uint32_t y) 
    {
        x = (int32_t)((uint32_t)x) & (sourceData.m_width - 1);
        y = (int32_t)((uint32_t)y) & (sourceData.m_height - 1);

        cross::Float4* curptr = static_cast<cross::Float4*>(sourceData.m_data) + (y * sourceData.m_width + x) /** 4*/;
        return Float4(curptr->x, curptr->y, curptr->z, curptr->w);
    }

    void GenerateSharpenedMipB8G8R8A8Templ(const cmft::Image& sourceImageData, cmft::Image& destImageData, const ImageKernel2D& kernel)
    {
        uint32_t width = destImageData.m_width, height = destImageData.m_height;
        uint32_t scaleFactor = 2;
        const uint32_t kernelCenter = kernel.GetFilterTableSize() / 2 - 1;

        Float4 alphaScale(1.0f, 1.0f, 1.0f, 1.0f);

        UInt32 numRowEachJob;
        UInt32 numJobs = ImageParallelForComputeNumJobsForRows(numRowEachJob, width, height);

        threading::ParallelFor(numJobs, [&](UInt32 Index) {
            uint32_t startIndex = Index * numRowEachJob;
            uint32_t endIndex = std::min(startIndex + numRowEachJob, height);
            for (uint32_t destY = startIndex; destY < endIndex; destY++)
            {
                for (uint32_t destX = 0; destX < width; destX++)
                {
                    const uint32_t sourceX = destX * scaleFactor;
                    const uint32_t sourceY = destY * scaleFactor;

                    Float4 linearcolor(0.0f, 0.0f, 0.0f, 0.0f);

                    for (uint32_t kernelY = 0; kernelY < (uint32_t)kernel.GetFilterTableSize(); ++kernelY)
                    {
                        for (uint32_t kernelX = 0; kernelX < (uint32_t)kernel.GetFilterTableSize(); ++kernelX)
                        {
                            float weight = kernel.GetAt(kernelX, kernelY);
                            Float4 sample = LookupSourceMip(sourceImageData, sourceX + kernelX - kernelCenter, sourceY + kernelY - kernelCenter);
                            linearcolor += weight * sample;
                        }
                    }

                    linearcolor.x *= alphaScale.x;
                    linearcolor.y *= alphaScale.y;
                    linearcolor.z *= alphaScale.z;
                    linearcolor.w *= alphaScale.w;

                    float* curptr = static_cast<float*>(destImageData.m_data) + (destY * destImageData.m_width + destX) * 4;
                    curptr[0] = linearcolor.x;
                    curptr[1] = linearcolor.y;
                    curptr[2] = linearcolor.z;
                    curptr[3] = linearcolor.w;
                }
            }
        });
    }
    
    void GenerateMipChain(cmft::Image& src, CrossSchema::TextureAssetT& texture, std::vector<cmft::Image>& mipmaps, const TextureResourceInfo info) 
    {
        uint32_t mipChainDepth = 1;
        uint32_t width = src.m_width, height = src.m_height;
        while (std::max(width, height) > 1) 
        {
            width  = std::max(width >> 1U, 1U);
            height = std::max(height >> 1U, 1U);
            ++mipChainDepth;
        }

        cmft::imageToRgba32f(src);

        float mipSharpening = 0.0f;
        int SharpenMipKernelSize = 8;
        if (info.MipmapGenerateSetting >= MipmapGenerateSetting::Sharpen0 && info.MipmapGenerateSetting <= MipmapGenerateSetting::Sharpen10) 
        {
            mipSharpening = (info.MipmapGenerateSetting - MipmapGenerateSetting::Sharpen0) * 0.02f; 
        }
        else if (info.MipmapGenerateSetting >= MipmapGenerateSetting::Blur1 && info.MipmapGenerateSetting <= MipmapGenerateSetting::Blur5)
        {
            mipSharpening = (info.MipmapGenerateSetting - MipmapGenerateSetting::Sharpen10) * -2.0f;
            SharpenMipKernelSize = (info.MipmapGenerateSetting - MipmapGenerateSetting::Sharpen10) * 2 + 2;
        }
        ImageKernel2D kernelSimpleAverage;
        ImageKernel2D kernelDownSample;
        kernelSimpleAverage.BuildSeparatableGaussWithSharpen(2);
        kernelDownSample.BuildSeparatableGaussWithSharpen(SharpenMipKernelSize, mipSharpening);

        cmft::Image source = src;
        cmft::Image dst;
        cmft::Image intermediatedst;
        cmft::Image firstmip;
        for (uint8_t i = 0; i < mipChainDepth; ++i) 
        {
            if (i == 0) 
            {
                cmft::imageCreate(firstmip, src.m_width, src.m_height);
                firstmip.m_dataSize = src.m_dataSize;
                firstmip.m_format = src.m_format;
                firstmip.m_numFaces = src.m_numFaces;
                firstmip.m_numMips = src.m_numMips;
                memcpy(firstmip.m_data, source.m_data, firstmip.m_dataSize);
                mipmaps.emplace_back(firstmip);
                continue;
            }
            else
            {
                const uint8_t currentmip = std::min((uint8_t)(i), (uint8_t)mipChainDepth);
                const uint32_t curmipwidth = std::max(UINT32_C(1), src.m_width >> currentmip);
                const uint32_t curmipheight = std::max(UINT32_C(1), src.m_height >> currentmip);

                cmft::imageCreate(dst, curmipwidth, curmipheight);
                cmft::imageCreate(intermediatedst, curmipwidth, curmipheight);

                GenerateSharpenedMipB8G8R8A8Templ(source, dst, kernelDownSample);
                GenerateSharpenedMipB8G8R8A8Templ(source, intermediatedst, kernelSimpleAverage);

                cmft::imageCopy(source, intermediatedst);
            }
            mipmaps.emplace_back(dst);
            dst.m_data = NULL;
            intermediatedst.m_data = NULL;
        }
    }

    UInt32 ParallelForComputeNumJobs(UInt64& outNumberPerJob, UInt64 numItems, UInt64 minNumItemsPerJobs, UInt64 minNumItemsForAnyJobs)
    {
        if (numItems <= std::max(minNumItemsPerJobs, minNumItemsForAnyJobs))
        {
            outNumberPerJob = numItems;
            return 1;
        }

        // due to no task graph,temp set workers is 14.
        const UInt32 numWorkers = 14;

        UInt32 numJobs = (UInt32)(numItems / minNumItemsPerJobs);
        numJobs = std::clamp(numJobs, UInt32(1), numWorkers);

        outNumberPerJob = (numItems + numJobs - 1) / numJobs;

        return numJobs;
    }

    UInt32 ImageParallelForComputeNumJobsForRows(UInt32& outNumberPerJob, UInt32 sizeX, UInt32 sizeY)
    {
        UInt64 numPixels = UInt64(sizeX) * sizeY;
        UInt64 numPixelsPerJob;
        UInt32 numJobs = ParallelForComputeNumJobs(numPixelsPerJob, numPixels, MinPixelsPerJob, MinPixelsForAnyJob);
        outNumberPerJob = (sizeX + numJobs - 1) / numJobs;
        return numJobs;
    }

    UInt32 ImageParallelForComputeNumJobsForPixels(UInt64& outNumPixelsPerJob, UInt64 numPixels)
    {
        return ParallelForComputeNumJobs(outNumPixelsPerJob, numPixels, MinPixelsPerJob, MinPixelsForAnyJob);
    }
}
