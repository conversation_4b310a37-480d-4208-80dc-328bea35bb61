#include "TextureImporter.h"
#include "ImageLoader.h"
#include "CECommon/Utilities/NDACommon.h"
#include "AssetPipeline/Utils/AssetIO.h"
#include "CrossBase/Serialization/ResourceMetaHeader.h"
#include "imageio.h"
#include <regex>
#include "Runtime/Interface/CrossEngineImp.h"
#include "FileSystem/filesystem.h"
#include "Resource/Resource.h"
#include "Resource/Texture/Texture2D.h"
#include "Resource/Texture/Texture3D.h"
#include "Resource/Texture/TextureCube.h"
#include "Resource/Texture/Texture2DArray.h"
#include "Resource/Texture/TextureUDIM.h"
#include "Resource/ResourceManager.h"
#include "AssetPipeline/Cook/AssetCookerManager.h"
#include "CrossImage/loadlutcube.h"
#include <encoder/basisu_enc.h>
#include <Resource/AssetStreaming.h>

namespace cross::editor {

bool TextureImporter::LoadImageFile(const std::string& path, cmft::Image& src, const TextureImportSetting& setting)
{
#if CROSSENGINE_WIN
    if (HasExtension(path.c_str(), ".exr")) {
        if (!imageio::load_exr(path.c_str(), src, setting.ColorSpace == ImportColorSpace::SRGB))
        {
            return false;
        }
        else
        {
            return true;
        }
    }
#endif
        
    if (HasExtension(path.c_str(), ".hdr") ||
        HasExtension(path.c_str(), ".dds") ||
        HasExtension(path.c_str(), ".ktx") ||
        HasExtension(path.c_str(), ".tga"))
    {
        if (!cmft::imageLoad(src, path.c_str()))
        {
            return false;
        }
    }
    else
    {
        if (!cmft::imageLoadStb(src, path.c_str()))
        {
            return false;
        }
    }
    return true;
}

TextureImporter::TextureImporter()
    : AssetImporter(AssetType::Texture)
{
    //mImportSetting = &gTextureImportSetting;
}

TextureImporter::~TextureImporter()
{
    // SAFE_DELETE(mOutput.mTextrure);
}

void TextureImporter::ImportAsset(const std::string& assetFilename, const std::string& ndaSavePath, ImportSetting* setting)
{
    SetProgress(0.01f);
    // The member importSetting has a potential problem of been changged by other thread (like import thumbnail.png)
    mImportSetting = *static_cast<TextureImportSetting*>(setting);
    auto tempSetting = mImportSetting;
    auto tempResult = mImportResult;
    tempResult = AssetImportState::Success;

    if (tempSetting.Type == TextureType::RectangularCubeMap)
    {
        if (!ImportTextureCubeFile(assetFilename, ndaSavePath, tempSetting))
        {
            tempResult = AssetImportState::OpenFileFail;
        }
    }
    else if (tempSetting.Type == TextureType::LUT_Cube)
    {
        if (!ImportTexture3DFile(assetFilename, ndaSavePath, tempSetting)) 
        {
            tempResult = AssetImportState::OpenFileFail;
        }
    }
    else if (tempSetting.isUDIM)
    {
        tempResult = ImportUDIMTextures(assetFilename, ndaSavePath, tempSetting);
    }
    else {
        if (!ImportTexture2DFile(assetFilename, ndaSavePath, tempSetting))
        {
            tempResult = AssetImportState::OpenFileFail;
        }
    }
    
    mImportResult = tempResult;
    if (tempResult.bSuccess && tempSetting.IsCook)
    {
        AssetCookerManager::Instance().CookImportAsset(ndaSavePath.c_str());
    }
    mProgress = 0.0f;
}

bool TextureImporter::UpdateAsset(const std::string& assetFilename, const std::string& ndaSavePath)
{
    if (!PathHelper::IsFileExist(ndaSavePath))
        return false;
    resource::LoadNDAFileInfo fileInfo;
    if (!gResourceAssetMgr.GetLoadNDAInfo(ndaSavePath.c_str(), fileInfo))
        return false;
    if (fileInfo.GetImportSet() == "")
        return false;
    TextureImportSetting setting;
    setting.DeserializeFromString(fileInfo.GetImportSet());
    ImportAsset(assetFilename, ndaSavePath, &setting);
    return true;
}

bool TextureImporter::CheckAssetName(const char* name) const
{
    return HasExtension(name, ".tif") || HasExtension(name, ".tiff") || HasExtension(name, ".png") || HasExtension(name, ".jpg") || HasExtension(name, ".jpeg") || HasExtension(name, ".jpeg") || HasExtension(name, ".jfif") ||
           HasExtension(name, ".tga") || HasExtension(name, ".exr") ||
           HasExtension(name, ".hdr") || HasExtension(name, ".cube");
}

uint32_t GetMemPos(uint32_t rowPitch, uint32_t x, uint32_t y)
{
    return y * rowPitch + x;
}

uint32_t GetRowPos(uint32_t src_rowPitch, uint32_t dimWidth, uint32_t dimHeight, uint32_t d, uint32_t h) 
{
    uint32_t slicePerRow = src_rowPitch / dimWidth;

    uint32_t slice_y = (d / slicePerRow) * dimHeight;

    uint32_t slice_x = (d % slicePerRow) * dimWidth;

    return GetMemPos(src_rowPitch, slice_x, slice_y + h);
}
// Claude4 aided vibe coding
bool LoadImage3D(cmft::Image src, CrossSchema::TextureAssetT& texture, TextureImportSetting setting)
{
    uint32_t pixelCount = src.m_width * src.m_height;

    auto image_info = cmft::getImageDataInfo(src.m_format);
    uint32_t srcBytesPerPixel = image_info.m_bytesPerPixel;

    uint32_t width = 0;
    uint32_t height = 0;
    uint32_t depth = 0;

    if (setting.TileXY.x != 0 && setting.TileXY.y != 0)
    {
        width = static_cast<uint32_t>(setting.TileXY.x);
        height = static_cast<uint32_t>(setting.TileXY.y);
        Assert(pixelCount > static_cast<uint32_t>(setting.TileXY.x * setting.TileXY.y));
        depth = std::max(pixelCount / (width * height), 1u);
    }
    else
    {
        uint32_t voxelSize = static_cast<uint32_t>(std::cbrt(pixelCount));
        width = voxelSize;
        height = voxelSize;
        depth = std::max(pixelCount / (width * height), 1u);
    }

    // Calculate mip levels for 3D texture
    uint32_t mipLevels = 1;
    if (setting.GenerateMipmap)
    {
        uint32_t maxDim = std::max({width, height, depth});
        while (maxDim > 1)
        {
            maxDim >>= 1;
            mipLevels++;
        }
    }

    // Calculate total data size for all mip levels
    std::vector<CrossSchema::TextureAssetImage> texImages;
    uint32_t totalDataSize = 0;
    uint32_t currentOffset = 0;

    // Create texture images for each mip level
    for (uint32_t mip = 0; mip < mipLevels; ++mip)
    {
        uint32_t mipWidth = std::max(width >> mip, 1u);
        uint32_t mipHeight = std::max(height >> mip, 1u);
        uint32_t mipDepth = std::max(depth >> mip, 1u);
        uint32_t mipPixelCount = mipWidth * mipHeight * mipDepth;
        uint32_t mipDataSize = mipPixelCount * srcBytesPerPixel;

        CrossSchema::TextureAssetImage mipImage;
        mipImage.mutate_width(mipWidth);
        mipImage.mutate_height(mipHeight);
        mipImage.mutate_depth(mipDepth);
        mipImage.mutate_dataoffset(currentOffset);
        mipImage.mutate_databytesize(mipDataSize);
        mipImage.mutate_rowpitch(mipWidth * srcBytesPerPixel);

        texImages.push_back(mipImage);
        totalDataSize += mipDataSize;
        currentOffset += mipDataSize;
    }

    // Allocate total data buffer
    texture.data.resize(totalDataSize);
    uint8_t* srcData = reinterpret_cast<uint8_t*>(src.m_data);

    // Generate base mip level (mip 0)
    uint8_t* dstData = texture.data.data();
    for (uint32_t d = 0; d < depth; d++)
    {
        for (uint32_t h = 0; h < height; h++)
        {
            uint32_t memPos = GetRowPos(src.m_width, width, height, d, h);
            uint32_t dstPos = width * height * d + h * width;

            for (uint32_t w = 0; w < width; w++)
            {
                for (uint32_t pixOffset = 0; pixOffset < srcBytesPerPixel; pixOffset++)
                {
                    *(dstData + (dstPos + w) * srcBytesPerPixel + pixOffset) = srcData[(memPos + w) * srcBytesPerPixel + pixOffset];
                }
            }
        }
    }

    // Generate additional mip levels if requested
    if (setting.GenerateMipmap && mipLevels > 1)
    {
        for (uint32_t mip = 1; mip < mipLevels; ++mip)
        {
            uint32_t prevMipWidth = std::max(width >> (mip - 1), 1u);
            uint32_t prevMipHeight = std::max(height >> (mip - 1), 1u);
            uint32_t prevMipDepth = std::max(depth >> (mip - 1), 1u);

            uint32_t mipWidth = std::max(width >> mip, 1u);
            uint32_t mipHeight = std::max(height >> mip, 1u);
            uint32_t mipDepth = std::max(depth >> mip, 1u);

            uint8_t* prevMipData = texture.data.data() + texImages[mip - 1].dataoffset();
            uint8_t* currentMipData = texture.data.data() + texImages[mip].dataoffset();

            // Simple box filter downsampling for 3D texture
            for (uint32_t d = 0; d < mipDepth; ++d)
            {
                for (uint32_t h = 0; h < mipHeight; ++h)
                {
                    for (uint32_t w = 0; w < mipWidth; ++w)
                    {
                        // Sample 2x2x2 region from previous mip level
                        uint32_t srcD0 = std::min(d * 2, prevMipDepth - 1);
                        uint32_t srcD1 = std::min(d * 2 + 1, prevMipDepth - 1);
                        uint32_t srcH0 = std::min(h * 2, prevMipHeight - 1);
                        uint32_t srcH1 = std::min(h * 2 + 1, prevMipHeight - 1);
                        uint32_t srcW0 = std::min(w * 2, prevMipWidth - 1);
                        uint32_t srcW1 = std::min(w * 2 + 1, prevMipWidth - 1);

                        uint32_t dstIndex = (d * mipHeight * mipWidth + h * mipWidth + w) * srcBytesPerPixel;

                        // Average 8 samples (2x2x2 cube)
                        for (uint32_t channel = 0; channel < srcBytesPerPixel; ++channel)
                        {
                            uint32_t sum = 0;
                            uint32_t sampleCount = 0;

                            // Sample all 8 corners of the 2x2x2 cube
                            for (uint32_t dd = 0; dd < 2; ++dd)
                            {
                                for (uint32_t hh = 0; hh < 2; ++hh)
                                {
                                    for (uint32_t ww = 0; ww < 2; ++ww)
                                    {
                                        uint32_t sampleD = (dd == 0) ? srcD0 : srcD1;
                                        uint32_t sampleH = (hh == 0) ? srcH0 : srcH1;
                                        uint32_t sampleW = (ww == 0) ? srcW0 : srcW1;

                                        uint32_t sampleIndex = (sampleD * prevMipHeight * prevMipWidth + 
                                                              sampleH * prevMipWidth + sampleW) * srcBytesPerPixel + channel;
                                        sum += prevMipData[sampleIndex];
                                        sampleCount++;
                                    }
                                }
                            }

                            currentMipData[dstIndex + channel] = static_cast<uint8_t>(sum / sampleCount);
                        }
                    }
                }
            }
        }
    }

    texture.images = texImages;
    texture.mipcount = mipLevels;
   
    if (setting.Type == TextureType::LUT_Cube)
        texture.format = TextureFormat::RGBAFloat;
    else
        texture.format = MapToCEFormat(src.m_format);

    texture.dimension = TextureDimension::Tex3D;
    texture.colorspace = static_cast<CrossSchema::ColorSpace>(setting.ColorSpace);
    texture.flags = 0;
    return true;
}

bool LoadImage3D_RGBAFloat(cmft::Image src, CrossSchema::TextureAssetT& texture, TextureImportSetting setting)
{
    CrossSchema::TextureAssetImage texImages;
    uint32_t pixelCount = src.m_width * src.m_height;
    uint32_t dst_bytesPerPixel = 16;
    uint32_t voxelSize = static_cast<uint32_t>(std::cbrt(pixelCount));
    uint32_t width = voxelSize;
    uint32_t height = voxelSize;
    uint32_t depth = voxelSize;

    texImages.mutate_width(width);
    texImages.mutate_height(height);
    texImages.mutate_depth(depth);
    texImages.mutate_dataoffset(0);
    texImages.mutate_databytesize(pixelCount * dst_bytesPerPixel);
    texImages.mutate_rowpitch(width * dst_bytesPerPixel);

    auto image_info = cmft::getImageDataInfo(src.m_format);
    texture.data.resize(pixelCount * dst_bytesPerPixel);
    float* data = reinterpret_cast<float*>(src.m_data);
    float* dst = reinterpret_cast<float*>(texture.data.data());

    for (uint32_t d = 0; d < voxelSize; d++)
    {
        for (uint32_t h = 0; h < voxelSize; h++)
        {
            for (uint32_t w = 0; w < voxelSize; w++)
            {
                uint32_t posNumDst = width * height * d + h * width + w;
                uint32_t basePosDst = posNumDst * 4;
                float* dstPix = dst + basePosDst;

                uint32_t posNumSrc = width * depth * h + width * d + w;
                uint32_t basePosSrc = posNumSrc * 4;
                float* srcPix = data + basePosSrc;
                dstPix[0] = srcPix[0];
                dstPix[1] = srcPix[1];
                dstPix[2] = srcPix[2];
                dstPix[3] = srcPix[3];
            }
        }
    }

    texture.images = {texImages};
    texture.mipcount = 1;
    texture.format = TextureFormat::RGBAFloat;
    texture.dimension = TextureDimension::Tex3D;
    texture.colorspace = static_cast<CrossSchema::ColorSpace>(setting.ColorSpace);
    texture.flags = 0;

    return true;
}


TexturePtr TextureImporter::ImportTexture2DFile(const std::string& assetFilename, const std::string& ndaSavePath, const TextureImportSetting& setting)
{
    CrossSchema::TextureAssetT texture;
#if CROSSENGINE_EDITOR
    if (setting.SaveRawData) {
        SaveRawData(assetFilename, texture, setting.ColorSpace == ImportColorSpace::SRGB);
    }
#endif
    cmft::Image src;

    SetProgress(0.3f);
        
    const char* name = assetFilename.c_str();
    if (HasExtension(name, ".tif") || HasExtension(name, ".tiff"))
    {
#if CROSSENGINE_WIN
        if (!imageio::load_tiff(name, src))
            return TexturePtr{nullptr};
#else
        return nullptr;
#endif
    }
    else
    {
        if (!LoadImageFile(assetFilename, src, setting))
        {
            return TexturePtr{nullptr};
        }
    }

    SetProgress(0.7f);

    if (setting.ImportSize != ImportTexureSize::None)
    {
        cmft::imageResize(src, static_cast<UInt32>(setting.ImportSize), static_cast<UInt32>(setting.ImportSize));
    }

    if (setting.FlipUV)
    {
        // our flipY mean y = 1 - y
        // it seems cmft flipx ,means different.
        cmft::imageTransform(src, cmft::IMAGE_OP_FLIP_X);
    }
    SetProgress(0.8f);

    texture.brightness = setting.SaveRawData      ? 1.0f : 0.0f;
    texture.brightnesscurve = setting.SaveRawData ? 1.0f : 0.0f; 
    texture.vibrance = setting.SaveRawData        ? 0.0f : 0.0f;
    texture.saturation = setting.SaveRawData      ? 1.0f : 0.0f;
    texture.hue = setting.SaveRawData             ? 0.0f : 0.0f;
    texture.minalpha = setting.SaveRawData        ? 0.0f : 0.0f;
    texture.maxalpha = setting.SaveRawData        ? 1.0f : 0.0f;
    TexturePtr result = ImportTexture2D(src, texture, ndaSavePath, setting);
    cmft::imageUnload(src);
    SetProgress(1.0f);
    return result;
}

TexturePtr TextureImporter::ImportTexture3DFile(const std::string& assetFilename, const std::string& ndaSavePath, const TextureImportSetting& setting)
{
    CrossSchema::TextureAssetT texture;
#if CROSSENGINE_EDITOR
    if (setting.SaveRawData)
    {
        SaveRawData(assetFilename, texture, setting.ColorSpace == ImportColorSpace::SRGB);
    }
#endif
    cmft::Image src;

    const char* name = assetFilename.c_str();

#if CROSSENGINE_WIN
    if (HasExtension(name, ".cube"))
    {
        if (!imageio::load_lut_cube(name, src))
            return TexturePtr{};
    }
#else
    return false;
#endif

    if (setting.ImportSize != ImportTexureSize::None)
    {
        cmft::imageResize(src, static_cast<UInt32>(setting.ImportSize));
    }
    
    auto result = ImportTexture3D(src, texture, ndaSavePath, setting);
    cmft::imageUnload(src);
    return result;
}

TexturePtr TextureImporter::ImportTextureCubeFile(const std::string& assetFilename, const std::string& ndaSavePath, const TextureImportSetting& setting)
{
    CrossSchema::TextureAssetT texture;
#if CROSSENGINE_EDITOR
    if (setting.SaveRawData) {
        SaveRawData(assetFilename, texture, setting.ColorSpace == ImportColorSpace::SRGB);
    }
#endif


#if CROSSENGINE_WIN
    cmft::Image src;
    if (!LoadImageFile(assetFilename, src, setting))
    {
        return TexturePtr{};
    }

    if (setting.ImportSize != ImportTexureSize::None)
    {
        cmft::imageResize(src, static_cast<UInt32>(setting.ImportSize));
    }
    else if (setting.GeneratePrefilterMipmap && std::min(src.m_width, src.m_height) > static_cast<UInt32>(ImportTexureSize::SQUARE_1024))
    {
        cmft::imageResize(src, static_cast<UInt32>(ImportTexureSize::SQUARE_1024));
    }

    auto result = ImportTextureCube(src, texture, ndaSavePath, setting);
    cmft::imageUnload(src);
    return result;
#else
    return TexturePtr{};
#endif
}

TexturePtr TextureImporter::ImportTexture2DArrayFile(const std::string& assetFilename, const std::string& ndaSavePath, const TextureImportSetting& setting)
{
    CrossSchema::TextureAssetT texture;
#if CROSSENGINE_EDITOR
    if (setting.SaveRawData)
    {
        SaveRawData(assetFilename, texture, setting.ColorSpace == ImportColorSpace::SRGB);
    }
#endif

#if CROSSENGINE_WIN
    cmft::Image src;
    if (!LoadImageFile(assetFilename, src, setting))
    {
        return TexturePtr{};
    }

    if (setting.ImportSize != ImportTexureSize::None)
    {
        cmft::imageResize(src, static_cast<UInt32>(setting.ImportSize));
    }

    auto result = ImportTexture2DArray(src, texture, ndaSavePath, setting);
    cmft::imageUnload(src);
    return result;
#else
    return TexturePtr{};
#endif
}

AssetImportState TextureImporter::ImportUDIMTextures(const std::string& assetFilename, const std::string& ndaSavePath, const TextureImportSetting& setting)
{
    // first, check the suffix with 10xx pattern
    std::string base_file;
    int blockx = -1, blocky = -1; 

    if (resource::TextureUDIM::UDIMCal(assetFilename, base_file, blockx, blocky) == false)
    {
        return AssetImportState::ExtensionError;
    }

    // second, find all candidate that at same directory, same base_file ,same ext and match udim regex;
    auto base_directory = PathHelper::GetParentPath(assetFilename);
    auto ext = PathHelper::GetExtension(assetFilename);

    std::vector<std::string> all_udim_textures;
    std::vector<std::string> all_save_files;
    std::vector<std::pair<TexturePtr, std::pair<int, int>>> textureInfo;

    PathHelper::TraverseFiles(
        base_directory, [&](std::string path) {
            auto cur_ext = PathHelper::GetExtension(path);
            
            std::string cur_base_file;
            int x = 0, y = 0;
            if (!resource::TextureUDIM::UDIMCal(path, cur_base_file, x, y))
                return;

            if (ext != cur_ext || cur_base_file != base_file)
                return;

             all_udim_textures.push_back(path);
        }, false);

    // get the nda directory;
    auto base_save_directory = PathHelper::GetParentPath(ndaSavePath);
    // load textures;
    auto override_settings = setting;
    override_settings.VirtualTextureStreaming = true;
    override_settings.FlipUV = true;

    int maxMipLevels = 0;
    TexturePtr baseTexture{nullptr};

    for (auto& itr : all_udim_textures)
    {        
        std::string new_ndaSave_path = base_save_directory + "/" + PathHelper::GetBaseFileName(itr) + ".nda";
        auto texture = ImportTexture2DFile(itr, new_ndaSave_path, override_settings);
        if (texture)
        {
            gResourceMgr.ReloadResource(TypeCast<Resource>(texture));
            std::string cur_base_file;
            int x = 0, y = 0;
            resource::TextureUDIM::UDIMCal(itr, cur_base_file, x, y);
            textureInfo.push_back({texture, {x, y}});
            maxMipLevels = std::max(maxMipLevels, static_cast<int>(texture->GetNumMips()));
            if (!baseTexture) baseTexture = texture;
        }
        all_save_files.push_back(PathHelper::GetRelativePath(PathHelper::GetCurrentDirectoryPath(), new_ndaSave_path));
    }

    // Find the mipmap level where dimensions become < 128
    int startMipLevel = 0;
    if (baseTexture)
    {
        for (int level = 0; level < maxMipLevels; ++level) {
            int width = baseTexture->GetWidth() >> level;
            int height = baseTexture->GetHeight() >> level;
            if (width < 128 || height < 128) {
                startMipLevel = level;
                break;
            }
        }
    }

    // Calculate merged texture dimensions
    int maxBlockX = 0, maxBlockY = 0;
    for (const auto& info : textureInfo)
    {
        maxBlockX = std::max(maxBlockX, info.second.first);
        maxBlockY = std::max(maxBlockY, info.second.second + 1);
    }

    TexturePtr udim_mip_texture;
    // Create merged texture for small mipmaps
    if (baseTexture && startMipLevel < maxMipLevels)
    {
        std::string mergedMipmapPath = base_save_directory + "/" + base_file + "_merged_mips.nda";
        CrossSchema::TextureAssetT mergedTexture;
        
        // Set merged texture properties
        mergedTexture.brightness = setting.SaveRawData ? 1.0f : 0.0f;
        mergedTexture.brightnesscurve = setting.SaveRawData ? 1.0f : 0.0f;
        mergedTexture.vibrance = setting.SaveRawData ? 0.0f : 0.0f;
        mergedTexture.saturation = setting.SaveRawData ? 1.0f : 0.0f;
        mergedTexture.hue = setting.SaveRawData ? 0.0f : 0.0f;
        mergedTexture.minalpha = setting.SaveRawData ? 0.0f : 0.0f;
        mergedTexture.maxalpha = setting.SaveRawData ? 1.0f : 0.0f;
        mergedTexture.dimension = TextureDimension::Tex2D;
        mergedTexture.flags = 0;
        mergedTexture.format = static_cast<CrossSchema::TextureFormat>(baseTexture->GetTextureInfo().Format);
        mergedTexture.colorspace = baseTexture->GetTextureInfo().ColorSpace == cross::ColorSpace::Linear ? 
            CrossSchema::ColorSpace::Linear : CrossSchema::ColorSpace::SRGB;
        //mergedTexture.width = baseTexture->GetWidth() >> startMipLevel * maxBlockX;
        //mergedTexture.height = baseTexture->GetHeight() >> startMipLevel * maxBlockY;
        mergedTexture.mipcount = maxMipLevels - startMipLevel;
        mergedTexture.vtstreaming = override_settings.VirtualTextureStreaming;
        mergedTexture.texturegroup = static_cast<CrossSchema::TextureGroup>(setting.ImportTextureGroup);

        mergedTexture.data.reserve(maxBlockX * maxBlockY * 128 * 128 * 4 * 4);// assuming worst cases, rgba float
        // Merge mipmaps
        std::vector<CrossSchema::TextureAssetImage> texImages;
        for (int mipLevel = startMipLevel; mipLevel < maxMipLevels; ++mipLevel)
        {
            int mipWidth = baseTexture->GetWidth() >> mipLevel;
            int mipHeight = baseTexture->GetHeight() >> mipLevel;
            int mergedWidth = mipWidth * maxBlockX;
            int mergedHeight = mipHeight * maxBlockY;

            int base_row_pitch = baseTexture->GetTextureDataPtr()->GetImagePitchByteSize(mipLevel);
            int base_cololumn_number = baseTexture->GetTextureDataPtr()->GetImageDataByteSize(mipLevel) / base_row_pitch;

            int merged_row_pitch_bytes = base_row_pitch * maxBlockX;
            int total_bytes = merged_row_pitch_bytes * base_cololumn_number * maxBlockY;


            //std::vector<UInt8> mergedData(mergedWidth*pixel_bytes);   // Assuming RGBA format

            

            // Create and setup TextureAssetImage for this mipmap level
            CrossSchema::TextureAssetImage mipImage;
            mipImage.mutate_width(mergedWidth);
            mipImage.mutate_height(mergedHeight);
            mipImage.mutate_depth(1); // 2D texture, depth is 1
            mipImage.mutate_databytesize(total_bytes);
            mipImage.mutate_rowpitch(merged_row_pitch_bytes);

            int data_offset = static_cast<int>(mergedTexture.data.size());

            mergedTexture.data.resize(data_offset + total_bytes);

            // Copy data from each UDIM texture
            for (const auto& info : textureInfo)
            {
                auto texture = info.first;
                if (mipLevel < static_cast<int>(texture->GetNumMips()))
                {
                    int destX = (info.second.first - 1);
                    int destY = (info.second.second) * base_cololumn_number;

                    // Copy mipmap data
                    //auto srcData = texture->GetTextureDataPtr()->GetImageData(mipLevel);

                    auto stream = texture->GetTextureDataPtr()->StreaminRequestIO(mipLevel);
                    
                    auto srcData = stream.mData.data();
                    auto size = texture->GetTextureDataPtr()->GetImageDataByteSize(mipLevel);


                    if (srcData)
                    {
                        for (int y = 0; y < base_cololumn_number; ++y)
                        {
                            int y_in_udim = destY + y;
                            int x_in_udim = destX;
                            memcpy(mergedTexture.data.data() + data_offset + (y_in_udim)*merged_row_pitch_bytes + x_in_udim * base_row_pitch, srcData + y * base_row_pitch, base_row_pitch);
                        }
                    }
                }
            }

            mipImage.mutate_dataoffset(data_offset);
            
            // Store mipmap data and image info in TextureAssetT
            mergedTexture.images.push_back(mipImage);
        }

        // Serialize the merged texture
        udim_mip_texture = SerializeTexture2D(mergedTexture, mergedMipmapPath, override_settings);
    }

    // write UDIM asset;
    std::string udim_save_path = base_save_directory + "/" + base_file + ".UDIM.nda";
    auto texUDIM = gResourceMgr.CreateResourceAs<resource::TextureUDIM>();
    texUDIM->CreateAsset(udim_save_path);
    bool flag = texUDIM->Serialize(all_save_files, udim_mip_texture ? PathHelper::GetRelativePath(PathHelper::GetCurrentDirectoryPath(), udim_mip_texture->GetName()) : "", udim_save_path);
    //auto generate material
    std::string material_save_path = base_save_directory + "/" + base_file + ".nda";
    std::string udimFx = "PipelineResource/FFSRP/Shader/VT/VTFx.nda";
    if (flag && PathHelper::IsFileExist(PathHelper::GetEngineResourceDirectoryPath() + "/" + udimFx))
    {
        auto fx = TypeCast<resource::Fx>(gAssetStreamingManager->LoadSynchronously(udimFx));
        cross::MaterialPtr material = TypeCast<cross::resource::Material>(cross::resource::Material::CreateMaterialInstance(fx));
        material->SetTexture("_BaseMap_VT_0", cross::TypeCast<cross::resource::Texture>(gAssetStreamingManager->LoadSynchronously(udim_save_path)));
        cross::SerializeNode emptyNode = {};
        material->Serialize(std::move(emptyNode), material_save_path);
    }
    return flag == true ? AssetImportState::Success : AssetImportState::WriteNdaFail;
}

TexturePtr TextureImporter::ImportTexture2D(cmft::Image& src, CrossSchema::TextureAssetT& texture, const std::string& ndaSavePath, const TextureImportSetting& setting, bool isExist)
{
    TexturePtr returnValue{};
    if (ToUnderlying(setting.Type) >= 4) // 3DSlice or LUT
    {
        if (!LoadImage3D(src, texture, setting))
        {
            return returnValue;
        }
    }
    else if (setting.Compression == TextureCompression::Uncompressed)
    {
        if (!LoadImageRaw(src, texture, setting))
        {
            return returnValue;
        }
    }
    else
    {
        if (setting.Compression == TextureCompression::CompressedBasisLQ || setting.Compression == TextureCompression::CompressedBasisHQ)
        {
            if (!LoadImageBasis(src, texture, setting))
            {
                return returnValue;
            }
        }
        else
        {
            // use basis transcode
            TextureImportSetting importSetting = setting;

            if (importSetting.Compression == TextureCompression::BC1 || importSetting.Compression == TextureCompression::BC3 || importSetting.Compression == TextureCompression::BC4 ||
                importSetting.Compression == TextureCompression::BC5 || importSetting.Compression == TextureCompression::BC6H || importSetting.Compression == TextureCompression::BC7) 
            {
                LoadImageBC(src, texture, importSetting);
            }
            else
            {
                Assert(false);
            }
        }
    }

    texture.colorspace = (setting.ColorSpace == ImportColorSpace::Linear) ? CrossSchema::ColorSpace::Linear : CrossSchema::ColorSpace::SRGB;
    texture.vtstreaming = setting.VirtualTextureStreaming;
    texture.texturegroup = static_cast<CrossSchema::TextureGroup>(setting.ImportTextureGroup);
    if (!isExist)
    {
        return SerializeTexture2D(texture, ndaSavePath, setting, isExist);
    }
    return returnValue;
}

TexturePtr TextureImporter::ImportTexture3D(cmft::Image& src, CrossSchema::TextureAssetT& texture, const std::string& ndaSavePath, const TextureImportSetting& setting)
{
    if (setting.Type == TextureType::LUT_Cube)
    {
        if (!LoadImage3D_RGBAFloat(src, texture, setting))
        {
            return TexturePtr{};
        }
    }
    else
    {
        if (!LoadImage3D(src, texture, setting))
        {
            return TexturePtr{};
        }
    }
    
    texture.colorspace = (setting.ColorSpace == ImportColorSpace::Linear) ? CrossSchema::ColorSpace::Linear : CrossSchema::ColorSpace::SRGB;
    texture.texturegroup = static_cast<CrossSchema::TextureGroup>(setting.ImportTextureGroup);
    return SerializeTexture3D(texture, ndaSavePath, setting);
}

TexturePtr TextureImporter::ImportTextureCube(cmft::Image &src, CrossSchema::TextureAssetT& texture, const std::string& ndaSavePath, const TextureImportSetting& setting)
{
#if CROSSENGINE_WIN
    std::vector<Float3> shCoefs;

    TextureImportSetting importSetting = setting;
    texture.texturegroup = static_cast<CrossSchema::TextureGroup>(importSetting.ImportTextureGroup);
    if (importSetting.Compression == TextureCompression::Uncompressed) {
        if (!LoadImageCubeRaw(src, texture, importSetting, shCoefs))
        {
            return TexturePtr{};
        }
    }
    else
    {
        if (!LoadImageCubeBC(src, texture, importSetting, shCoefs))
        {
            return TexturePtr{};
        }
    }

    return SerializeTextureCube(texture, ndaSavePath, shCoefs, setting);
#endif
    return TexturePtr{};
}

TexturePtr TextureImporter::ImportTexture2DArray(cmft::Image& src, CrossSchema::TextureAssetT& texture, const std::string& ndaSavePath, const TextureImportSetting& setting)
{
#if !CROSSENGINE_WIN
    TextureImportSetting importSetting = setting;

    if (importSetting.Compression == TextureCompression::Uncompressed)
    {
    }
    else
    {
    }
    return  SerializeTexture2DArray(texture, ndaSavePath, setting);
#endif
    return TexturePtr{};
}


// seems a hack by maxwan, the texture cook seems do noting and replaced by limitcai's cook
bool TextureImporter::Compress(const std::string& assetFilename, const std::string& ndaSavePath, const TextureImportSetting& setting)
{
    if (ndaSavePath.find("Contents") != std::string::npos && ndaSavePath.find(".thumb") == std::string::npos)
    {
        // get the texture compression nda file path
        std::string texturePathWithoutExt = PathHelper::GetBaseFileName(assetFilename, true);
        std::string cookPath = ndaSavePath;
        cookPath = cookPath.replace(ndaSavePath.find("Contents"), std::strlen("Contents"), "Intermediate/Cook/TextureCompression");
        std::string cookNdaFilePath = PathHelper::Combine(cookPath.c_str(), texturePathWithoutExt.c_str(), ".nda");

        CrossSchema::TextureAssetT texture;
        std::string ndaFilePath = Resource::GetNDAFilePath(assetFilename, ndaSavePath);
        {
            filesystem::FileSystem* fileSystem = EngineGlobal::Inst().GetFileSystem();
            Assert(fileSystem);

            filesystem::IFilePtr srcfile = fileSystem->Open(ndaFilePath);
            Assert(srcfile);

            // read nda texture
            AssetIO::DeserializeTextureAsset(texture, srcfile);
            // texture compression and save to nda file
            CrossSchema::TextureAssetT cookedTexture;


            if (mTextureCooker.CookTexture(texture, cookedTexture))
            {
                flatbuffers::FlatBufferBuilder texturebuilder(1024);
                auto mloc = CrossSchema::CreateTextureAsset(texturebuilder, &cookedTexture);
                CrossSchema::ResourceHeader header;
                auto classID = ClassID(NullType);
                switch (cookedTexture.dimension)
                {
                case TextureDimension::Tex2D:
                    classID = ClassID(Texture2D);
                    break;
                case TextureDimension::Tex3D:
                    classID = ClassID(Texture3D);
                    break;
                case TextureDimension::TexCube:
                    classID = ClassID(TextureCube);
                    break;
                default:
                    return false;
                }
                header.mutate_classid(classID);
                header.mutate_magicnumber(ASSET_MAGIC_NUMBER);
                header.mutate_datasize((int32_t)cookedTexture.data.size());
                auto ma = CrossSchema::CreateResourceAsset(texturebuilder, &header, texturebuilder.CreateString(assetFilename), CrossSchema::ResourceType::TextureAsset, mloc.Union());
                FinishResourceAssetBuffer(texturebuilder, ma);
                DropAssetAsSingleFile(cookNdaFilePath, [this, classID, &texturebuilder](filesystem::IFilePtr file) -> bool { return AssetIO::SerializeAsset(texturebuilder, classID, file); });
            }
            return true;
        }
    }
    return false;
}

TexturePtr TextureImporter::SerializeTexture2D(CrossSchema::TextureAssetT& texture, const std::string& ndaSavePath, const TextureImportSetting& setting, bool isExist)
{
    if (!setting.VirtualTextureStreaming)
    {
        Texture2DPtr tex2d;
        if (isExist)
        {
            tex2d = TypeCast<resource::Texture2D>(gResourceMgr.GetResource(ndaSavePath.c_str()));
        }
        else
        {
            tex2d = gResourceMgr.CreateResourceAs<resource::Texture2D>();
            tex2d->CreateAsset(ndaSavePath);
            tex2d->SetImportSet(setting.SerializeToString());
        }
        return tex2d->Serialize(texture) ? TypeCast<resource::Texture>(tex2d) : TexturePtr{nullptr};
    }
    else
    {
        // virtual texture
        TextureVirtualPtr tex2dVirtual;
        if (isExist)
        {
            tex2dVirtual = TypeCast<resource::Texture2DVirtual>(gResourceMgr.GetResource(ndaSavePath.c_str()));
        }
        else
        {
            tex2dVirtual = gResourceMgr.CreateResourceAs<resource::Texture2DVirtual>();
            tex2dVirtual->CreateAsset(ndaSavePath);
            tex2dVirtual->SetImportSet(setting.SerializeToString());
        }
        return tex2dVirtual->Serialize(texture) ? TypeCast<resource::Texture>(tex2dVirtual) : TexturePtr{nullptr};
    }
}

TexturePtr TextureImporter::SerializeTexture3D(CrossSchema::TextureAssetT& texture, const std::string& ndaSavePath, const TextureImportSetting& setting)
{
    Texture3DPtr tex3d = gResourceMgr.CreateResourceAs<resource::Texture3D>();
    tex3d->CreateAsset(ndaSavePath);
    tex3d->SetImportSet(setting.SerializeToString());
    return tex3d->Serialize(texture) ? TypeCast<resource::Texture>(tex3d) : TexturePtr{nullptr};
}

TexturePtr TextureImporter::SerializeTextureCube(CrossSchema::TextureAssetT& texture, const std::string& ndaSavePath, std::vector<Float3>& shCoefs, const TextureImportSetting& setting)
{
    TextureCubePtr tex = gResourceMgr.CreateResourceAs<cross::resource::TextureCube>();
    tex->SetImgSHCoefs(std::move(shCoefs));
    tex->CreateAsset(ndaSavePath);
    tex->SetImportSet(setting.SerializeToString());
    return tex->Serialize(texture) ? TypeCast<resource::Texture>(tex) : TexturePtr{nullptr};
}

TexturePtr TextureImporter::SerializeTexture2DArray(CrossSchema::TextureAssetT& texture, const std::string& ndaSavePath, const TextureImportSetting& setting)
{
    Texture2DArrayPtr tex = gResourceMgr.CreateResourceAs<cross::resource::Texture2DArray>();
    tex->CreateAsset(ndaSavePath);
    tex->SetImportSet(setting.SerializeToString());
    return tex->Serialize(texture) ? TypeCast<resource::Texture>(tex) : TexturePtr{nullptr};
}

bool TextureImporter::ByPathGetTextureRawData(const std::string& ndaPath, cmft::Image& src, CrossSchema::TextureAssetT& texture)
{
    auto dir = PathHelper::GetDirectoryFromAbsolutePath(ndaPath);
    if (!PathHelper::IsDirectoryExist(dir))
    {
        // create all the folders needed
        std::lock_guard<std::mutex> lockGurad(mMutex);
        PathHelper::CheckDirectory(dir);
    }

    BinaryArchive* fileData = gResourceAssetMgr.ReadAssetFile(PathHelper::GetRelativePath(ndaPath).c_str());
    resource::LoadNDAFileInfo fileInfo;
    if (gResourceAssetMgr.GetLoadNDAInfo(fileData, fileInfo) == false)
        return false;

    int id = fileInfo.GetClassID();
    if (!(id == ClassID(Texture2D) || id == ClassID(Texture3D) || id == ClassID(TextureCube)))
        return false;

    std::vector<UInt8> data;
    std::unique_ptr<CrossSchema::TextureAssetT> assetData = std::make_unique<CrossSchema::TextureAssetT>();
    auto* res = CrossSchema::GetResourceAsset(fileData->Data() + fileInfo.GetOffet());
    res->resource_as_TextureAsset()->UnPackTo(assetData.get());

    data = assetData->rawdata;
    texture = *assetData;
    if (data.size() == 0)
        return false;

    uint32_t* head = static_cast<uint32_t*>(static_cast<void*>(data.data()));
    if (*head == 1) 
    {
        texture.rawdata.resize(data.size());
        int width = *(head + 1);
        int height = *(head + 2);

        cross::Float4* target = reinterpret_cast<cross::Float4*>(head + 3);
        cmft::imageCreate(src, width, height);
        const int stride = 4;
        for (int y = 0; y < height; ++y)
        {
            for (int x = 0; x < width; ++x)
            {
                float* outPixel = static_cast<float*>(src.m_data) + (y * width + x) * stride;
                outPixel[0] = target->x;
                outPixel[1] = target->y;
                outPixel[2] = target->z;
                outPixel[3] = target->w;
                target++;
            }
        }
#if CROSSENGINE_EDITOR
        memcpy(texture.rawdata.data(), data.data(), data.size());
#endif
    }
    else
    {
        cmft::imageLoadStb(src, data.data(), static_cast<uint32_t>(data.size()));
#if CROSSENGINE_EDITOR
        texture.rawdata.resize(data.size());
        memcpy(texture.rawdata.data(), data.data(), data.size());
#endif
    }
    return true;
}


void TextureImporter::UpdateTextureAsset(const std::string& ndaPath, const TextureImportSetting& setting, const TextureResourceInfo& info)
{
    cmft::Image src;
    CrossSchema::TextureAssetT texture;
    bool flag = ByPathGetTextureRawData(ndaPath, src, texture);
    if (!flag)
    {
        LOG_DEBUG("No raw texture data, just update settings");
        CopyAttributesToTexture(texture, info);
        SerializeTexture2D(texture, ndaPath, setting, false);
    }
    else
    {
        GetLinearColorValue(src, info);

        if (info.TextureSize != TextureSize::None)
        {
            cmft::imageResize(src, static_cast<UInt32>(info.TextureSize));
        }

        if (mImportSetting.Type == TextureType::RectangularCubeMap)
        {
            ImportTextureCube(src, texture, ndaPath, setting);
        }
        /*else if (mImportSetting->Type == TextureType::LUT_Cube)
        {
            ImportTexture3D(src, texture, ndaPath, setting);
        }*/
        else
        {
            CopyAttributesToTexture(texture, info);
            ImportTexture2D(src, texture, ndaPath, setting);
        }
    }
    cmft::imageUnload(src);
}

void TextureImporter::GenerateSharpenedMip(const std::string& ndaPath, const TextureImportSetting setting, const TextureResourceInfo info)
{
#if CROSSENGINE_WIN
    cmft::Image src;
    CrossSchema::TextureAssetT texture;
    ByPathGetTextureRawData(ndaPath, src, texture);
    cmft::imageResize(src, info.Width, info.Height);
    if (info.MipmapGenerateSetting == MipmapGenerateSetting::Initial) 
    {
        CopyAttributesToTexture(texture, info);
        bool result = ImportTexture2D(src, texture, ndaPath, setting);
        std::cout << "result:" << result <<std::endl;
    }
    else
    {
        std::vector<cmft::Image> mipmaps;
        cross::editor::GenerateMipChain(src, texture, mipmaps, info);

        imageio::GPUImage gpu_image;
        cross::editor::ProcessBCFormatNVTT(mipmaps, gpu_image, setting);
        cross::editor::NVTTTransformTexture(texture, setting, gpu_image);
        CopyAttributesToTexture(texture, info);
        SerializeTexture2D(texture, ndaPath, setting, true);
    }
    //cmft::imageUnload(src);
#endif
}

void TextureImporter::SaveRawData(const std::string& assetFilename, CrossSchema::TextureAssetT& texture, bool isSRGB)
{
    auto* system = EngineGlobal::GetFileSystem();
    auto* name = assetFilename.c_str();
    auto file = system->Open(assetFilename);
    if (file)
    {
#if CROSSENGINE_WIN
        if (HasExtension(name, ".exr"))
        {
            cmft::Image src;
            if (imageio::load_exr(name, src, isSRGB))
            {
                texture.rawdata.resize(src.m_dataSize + 12);
                uint32_t* head = static_cast<uint32_t*>(static_cast<void*>(texture.rawdata.data()));
                *head = 1;
                *(head + 1) = src.m_width;
                *(head + 2) = src.m_height;
                memcpy(texture.rawdata.data() + 12, src.m_data, src.m_dataSize);
                return;
            }
        }
#endif
        auto size = file->GetSize();
        texture.rawdata.resize(size);
        file->Read(reinterpret_cast<char*>(texture.rawdata.data()), size);
    }
}

void TextureImporter::AddDependency(std::string& dep)
{
    mDependencies.push_back(dep.c_str());
}

void TextureImporter::EvaluateSubBlock(SInt32 left, SInt32 top, SInt32 width, SInt32 height, Float4& averageColor, SInt32& numSamplesTaken, SInt32& numSamplesRejected, imageio::image& image)
{
    for (SInt32 y = top; y != (top + height); y++)
    {
        for (SInt32 x = left; x != (left + width); x++)
        {
            imageio::color_rgba sample = image(x, y);

            Float4 ColorSample = Float4(sample.r, sample.g, sample.b, sample.a);
            if (std::abs(ColorSample.w) <= AlphaComponentNearlyZeroThreshold || (cross::Squre(ColorSample.x) < CE_DELTA && cross::Squre(ColorSample.y) < CE_DELTA && cross::Squre(ColorSample.z) < CE_DELTA))
            {
                continue;
            }

            float vx = static_cast<float>(ColorSample.x / 255 * 2.0 - 1.0);
            float vy = static_cast<float>(ColorSample.y / 255 * 2.0 - 1.0);
            float vz = static_cast<float>(ColorSample.z / 255 * 2.0 - 1.0);

            const float length = std::sqrt(vx * vx + vy * vy + vz * vz);

            if (length < ColorComponentNearlyZeroThreshold)
            {
                continue;
            }

            if (std::abs(length - 1.0f) > NormalVectorUnitLengthDeltaThreshold)
            {
                numSamplesRejected++;
                continue;
            }

            if (vz < 0.0f)
            {
                numSamplesRejected++;
                continue;
            }

            averageColor += ColorSample;
            numSamplesTaken++;
        }
    }
}

bool TextureImporter::DoesTexture2DLookLikelyToBeANormalMap(cmft::Image& image, bool& isAlpha)
{
    imageio::image dst;
    cmft::imageConvert(image, cmft::TextureFormat::RGBA8);

    dst.resize(image.m_width, image.m_height);
    memcpy(dst.get_pixels().data(), image.m_data, dst.get_pixels().size() * 4);
    isAlpha = dst.has_alpha();

    SInt32 numSamplesTaken = 0;
    SInt32 numSamplesRejected = 0;
    SInt32 numSamplesThreshold = 0;
    Float4 averageColor = Float4(0.0f, 0.0f, 0.0f, 0.0f);

    SInt32 textureSizeX = image.m_width;
    SInt32 textureSizeY = image.m_height;

    SInt32 numTilesX = std::min(textureSizeX / SampleTileEdgeLength, MaxTilesPerAxis);
    SInt32 numTilesY = std::min(textureSizeY / SampleTileEdgeLength, MaxTilesPerAxis);

    if ((numTilesX > 0) && (numTilesY > 0))
    {
        numSamplesThreshold = (numTilesX * numTilesY) * 4;
        for (SInt32 tileY = 0; tileY < numTilesY; tileY++)
        {
            int top = (textureSizeY / numTilesY) * tileY;

            for (SInt32 tileX = 0; tileX < numTilesX; tileX++)
            {
                int left = (textureSizeX / numTilesX) * tileX;

                EvaluateSubBlock(left, top, SampleTileEdgeLength, SampleTileEdgeLength, averageColor, numSamplesTaken, numSamplesRejected, dst);
            }
        }
    }
    else
    {
        numSamplesThreshold = (textureSizeX * textureSizeY) / 4;
        EvaluateSubBlock(0, 0, textureSizeX, textureSizeY, averageColor, numSamplesTaken, numSamplesRejected, dst);
    }

    if (numSamplesTaken >= numSamplesThreshold)
    {
        const float rejectdToTakenRatio = static_cast<float>(numSamplesRejected) / static_cast<float>(numSamplesTaken);
        if (rejectdToTakenRatio >= RejectedToTakenRatioThreshold)
        {
            return false;
        }

        averageColor /= static_cast<float>(numSamplesTaken);

        float vx = static_cast<float>(averageColor.x / 255 * 2.0 - 1.0);
        float vy = static_cast<float>(averageColor.y / 255 * 2.0 - 1.0);
        float vz = static_cast<float>(averageColor.z / 255 * 2.0 - 1.0);

        float manitude = std::sqrt(vx * vx + vy * vy + vz * vz);

        float normalizedZ = vz / manitude;

        return ((manitude >= NormalMapMinLengthConfidenceThreshold) && (manitude < NormalMapMaxLengthConfidenceThreshold) && (normalizedZ >= NormalMapDeviationThreshold));
    }
    return false;
}

TextureImportSetting TextureImporter::UpdateDefaultSetting(const std::string& ndaPath, TextureImportSetting setting, bool AutoFFS) 
{
    cmft::Image src;

    const char* name = ndaPath.c_str();
    if (HasExtension(name, ".tif") || HasExtension(name, ".tiff"))
    {
#if CROSSENGINE_WIN
        if (!imageio::load_tiff(name, src))
            return setting;
#else
        return setting;
#endif
    }
    else
    {
        if (!LoadImageFile(ndaPath, src, setting))
        {
            return setting;
        }
    }

    if (src.m_format == cmft::TextureFormat::RGBE)
    {
        setting.Type = TextureType::RectangularCubeMap;
        setting.ColorSpace = ImportColorSpace::Linear;
        setting.Compression = TextureCompression::BC6H;
        return setting;
    }

    bool isAlpha = false;
    bool isNormal = DoesTexture2DLookLikelyToBeANormalMap(src, isAlpha);

    if (isNormal)
    {
        setting.ColorSpace = ImportColorSpace::Linear;
        setting.Compression = TextureCompression::BC5;
        setting.Type = TextureType::NormalTexture;
    }
    else
    {
        if (isAlpha)
        {
            setting.Compression = TextureCompression::BC3;
        }
        else
        {
            setting.Compression = TextureCompression::BC1;
        }

        if (ndaPath.find("_BaseColor") != std::string::npos)
        {
            setting.ColorSpace = ImportColorSpace::SRGB;
        }
        else if (ndaPath.find("_MRO") != std::string::npos)
        {
            setting.ColorSpace = ImportColorSpace::Linear;
        }
        else
        {
            setting.ColorSpace = ImportColorSpace::SRGB;
        }
        setting.Type = TextureType::ImageTexture;
    }
   
    return setting;
}

bool TextureImporter::GenerateCurveGradientTex(const std::string& fileName, const TextureImportSetting& setting, UInt32* stopColors, int n)
{
    cmft::Image src;
    CrossSchema::TextureAssetT texture;

    //fill raw data to 256*256
    std::vector<UInt32> colors;
    for (int i = 0; i < n; ++i)
    {
        colors.emplace_back(stopColors[i]);
    }

    int width = 256;
    int height = 256;

    std::vector<UInt32> firstLineColor;
    cmft::imageCreate(src, width, height);
    src.m_dataSize = width * height * 4;
    src.m_format = cmft::TextureFormat::RGBA8;
    for (int y = 0; y < 1; ++y)
    {
        for (int x = 0; x < width; ++x)
        {
            int k = static_cast<int>(x * 1.0f / width * colors.size());
            UInt32 color = colors[k];
            firstLineColor.emplace_back(color);
            UInt32* outPixel = static_cast<UInt32*>(src.m_data) + (y * width + x);
            *outPixel = color;
        }
    }

    for (int y = 1; y < height; ++y)
    {
        for (int x = 0; x < width; ++x)
        {
            UInt32 color = firstLineColor[x];
            UInt32* outPixel = static_cast<UInt32*>(src.m_data) + (y * width + x);
            *outPixel = color;
        }
    }

    // save texture direct to nda file
    flatbuffers::FlatBufferBuilder texturebuilder(1024);
    auto mloc = CrossSchema::CreateTextureAsset(texturebuilder, &texture);

    auto classID = ClassID(Texture2D);
    CrossSchema::ResourceHeader header(ASSET_MAGIC_NUMBER, 0, classID, (int32_t)texture.data.size(), (int32_t)texture.data.size());
    auto mloc2 = CrossSchema::CreateResourceAsset(texturebuilder, &header, texturebuilder.CreateString(fileName), CrossSchema::ResourceType::TextureAsset, mloc.Union());
    FinishResourceAssetBuffer(texturebuilder, mloc2);

    //compress and serialize
    ImportTexture2D(src, texture, fileName, setting);
    cmft::imageUnload(src);
    return true;
}

void TextureImporter::CopyAttributesToTexture(CrossSchema::TextureAssetT& texture, const TextureResourceInfo info)
{
    cross::Adjustments crossadj = info.Adjustments;
    texture.brightness = crossadj.Brightness;
    texture.brightnesscurve = crossadj.BrightnessCurve;
    texture.vibrance = crossadj.Vibrance;
    texture.saturation = crossadj.Saturation;
    texture.hue = crossadj.Hue;
    texture.minalpha = crossadj.MinAlpha;
    texture.maxalpha = crossadj.MaxAlpha;
    texture.mipmapgeneratesetting = static_cast<CrossSchema::MipmapGenerateSetting>(info.MipmapGenerateSetting);
    texture.texturegroup = static_cast<CrossSchema::TextureGroup>(info.TextureGroup);
    texture.texturesize = static_cast<CrossSchema::TextureSize>(info.TextureSize);
}

bool TextureImporter::GetTextureRawData(const std::string& ndaPath) 
{
    auto dir = PathHelper::GetDirectoryFromAbsolutePath(ndaPath);
    if (!PathHelper::IsDirectoryExist(dir))
    {
        // create all the folders needed
        std::lock_guard<std::mutex> lockGurad(mMutex);
        PathHelper::CheckDirectory(dir);
    }

    BinaryArchive* fileData = gResourceAssetMgr.ReadAssetFile(PathHelper::GetRelativePath(ndaPath).c_str());
    resource::LoadNDAFileInfo fileInfo;
    if (gResourceAssetMgr.GetLoadNDAInfo(fileData, fileInfo) == false)
        return false;

    int id = fileInfo.GetClassID();
    if (!(id == ClassID(Texture2D) || id == ClassID(Texture3D) || id == ClassID(TextureCube)))
        return false;

    std::vector<UInt8> data;
    std::unique_ptr<CrossSchema::TextureAssetT> assetData = std::make_unique<CrossSchema::TextureAssetT>();
    auto* res = CrossSchema::GetResourceAsset(fileData->Data() + fileInfo.GetOffet());
    res->resource_as_TextureAsset()->UnPackTo(assetData.get());

    data = assetData->rawdata;
    if (data.size() == 0)
    {
        return false;
    }
    return true;
}
}   // namespace cross::editor
