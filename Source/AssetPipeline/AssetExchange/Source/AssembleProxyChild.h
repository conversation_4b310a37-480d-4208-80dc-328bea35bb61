#pragma once

#include "AssembleProxy.h"
#include "TextureAssemble.h"
#include "MaterialAssemble.h"
#include "MeshAssemble.h"
#include "WorldAssemble.h"

namespace CEAssetExchange {

struct MeshAssembleProxy : virtual public IMeshAssemble, virtual public AssembleProxy
{
    explicit MeshAssembleProxy(std::string& reference, MeshAssemble* a)
        : AssembleProxy(reference, dynamic_cast<IMeshAssemble*>(this))
        , mAssemble(a)
    {}

    virtual void SetRawHeaderBuffers(const std::uint8_t* buffer, const std::uintmax_t size) override
    {
        mAssemble->SetRawHeaderBuffers(buffer, size);
    }

    virtual void SetRawFlatBuffers(const std::uint8_t* buffer, const std::uintmax_t size) override
    {
        mAssemble->SetRawFlatBuffers(buffer, size);
    }

    virtual std::uint32_t GetMeshPartCount() override
    {
        return mAssemble->GetMeshPartCount();
    }

    virtual void GetMeshPartRange(std::uint32_t beginLOD, std::uint32_t endLOD, std::uint32_t* beginMeshPart, std::uint32_t* endMeshPart) override
    {
        mAssemble->GetMeshPartRange(beginLOD, endLOD, beginMeshPart, endMeshPart);
    }

    virtual std::uint32_t GetLODCount() override
    {
        return mAssemble->GetLODCount();
    }

    virtual void GetLODMeshPartRange(std::uint32_t LOD, std::uint32_t* beginMeshPart, std::uint32_t* endMeshPart) override
    {
        mAssemble->GetLODMeshPartRange(LOD, beginMeshPart, endMeshPart);
    }

    virtual void RemoveLODRange(std::uint32_t beginLOD, std::uint32_t endLOD) override
    {
        mAssemble->RemoveLODRange(beginLOD, endLOD);
    }

    virtual void RemoveLOD(std::uint32_t LOD) override
    {
        mAssemble->RemoveLOD(LOD);
    }

    virtual void UpdateMeshPart(std::uint32_t Index, const MeshSplitBuffer* m) override 
    {
        mAssemble->UpdateMeshPart(Index, m);
    }

    virtual void UpdateMeshPart(std::uint32_t Index, const MeshFlatBuffer* m) override 
    {
        mAssemble->UpdateMeshPart(Index, m);
    }

    virtual std::uint32_t GetLODVertexCount(std::uint32_t LOD) override
    {
        return mAssemble->GetLODVertexCount(LOD);
    }

    virtual std::uint32_t GetLODIndexCount(std::uint32_t LOD) override
    {
        return mAssemble->GetLODIndexCount(LOD);
    }

    virtual std::uint32_t GetMeshPartVertexCount(std::uint32_t MeshPart) override
    {
        return mAssemble->GetMeshPartVertexCount(MeshPart);
    }

    virtual std::uint32_t GetMeshPartIndexCount(std::uint32_t MeshPart) override
    {
        return mAssemble->GetMeshPartIndexCount(MeshPart);
    }

    virtual void SaveToFile() override
    {
        mAssemble->SaveToFile();
    }

    virtual void SetSettings(const MeshSettings* s) override
    {
        mAssemble->SetSettings(s);
    }

    virtual bool HasBeenDestroyed() override
    {
        return mAssemble == nullptr;
    };

    virtual void EndAssemble() override
    {
        mAssemble->EndAssemble();
    }

    virtual bool HasEndAssemble() override
    {
        return mAssemble->HasEndAssemble();
    }

    virtual void AddMeshLODs(const MeshLODs* LODs) override
    {
        mAssemble->AddMeshLODs(LODs);
    }

    virtual void AddMeshLODSetting(MeshLODSetting LODSetting) override
    {
        mAssemble->AddMeshLODSetting(std::move(LODSetting));
    }

    virtual void AddIndexStream(const std::uint32_t* data, const std::uintmax_t size) override
    {
        mAssemble->AddIndexStream(data, size);
    }

    virtual void AddVertexBuffer(const MeshVertexBuffer* vb) override
    {
        mAssemble->AddVertexBuffer(vb);
    }

    virtual void AddVertexPosition(const VertexChannel c, const float* data, const std::uintmax_t size) override
    {
        mAssemble->AddVertexPosition(c, data, size);
    }

    virtual void AddVertexTexCoord(const VertexChannel c, const float* data, const std::uintmax_t size) override
    {
        mAssemble->AddVertexTexCoord(c, data, size);
    }

    virtual void AddVertexColor(const VertexChannel c, const std::uint32_t* data, const std::uintmax_t size) override
    {
        mAssemble->AddVertexColor(c, data, size);
    }

    virtual void AddVertexNormal(const VertexChannel c, const float* data, const std::uintmax_t size) override
    {
        mAssemble->AddVertexNormal(c, data, size);
    }

    virtual void AddVertexTangent(const VertexChannel c, const float* data, const std::uintmax_t size) override
    {
        mAssemble->AddVertexTangent(c, data, size);
    }

    virtual void AddVertexBiNormal(const VertexChannel c, const float* data, const std::uintmax_t size) override
    {
        mAssemble->AddVertexBiNormal(c, data, size);
    }

    virtual void AddVertexBoneWeights(const VertexChannel c, const float* data, const std::uintmax_t size) override
    {
        mAssemble->AddVertexBoneWeights(c, data, size);
    }

    virtual void AddVertexBoneIds(const VertexChannel c, const std::int16_t* data, const std::uintmax_t size) override
    {
        mAssemble->AddVertexBoneIds(c, data, size);
    }

    virtual void AddVertexQuatTangent(const VertexChannel c, const float* data, const std::uintmax_t size) override
    {
        mAssemble->AddVertexQuatTangent(c, data, size);
    }

    virtual void AddRefSkeleton(const MeshBoneNode* nodes, const std::uintmax_t numNodes, const char* name) override
    {
        mAssemble->AddRefSkeleton(nodes, numNodes, name);
    }

    virtual void AddBoxCollision(const BoxCollision* data, const std::uintmax_t size) override 
    { mAssemble->AddBoxCollision(data,size); }

    virtual void AddSphereCollision(const SphereCollision* data, const std::uintmax_t size) override { mAssemble->AddSphereCollision(data,size); }
    virtual void AddCapsuleCollision(const CapsuleCollision* data, const std::uintmax_t size) override { mAssemble->AddCapsuleCollision(data,size); }
    virtual void AddConvexCollision(const ConvexCollision* data) override { mAssemble->AddConvexCollision(data); }
    virtual void AddTriMeshCollision(const TriMeshCollision* data) override { mAssemble->AddTriMeshCollision(data); }
    MeshAssemble* mAssemble = nullptr;
};

struct FxAssembleProxy : virtual public IFxAssemble, virtual public AssembleProxy
{
    explicit FxAssembleProxy(std::string& reference, FxAssemble* a)
        : AssembleProxy(reference, dynamic_cast<IFxAssemble*>(this))
        , mAssemble(a)
    {}

    virtual void SaveToFile() override { mAssemble->SaveToFile(); };

    virtual void SetupFromJsonString(const char* jsonStr, Type type) override { mAssemble->SetupFromJsonString(jsonStr, type); }

    virtual void AddDependencies(const char* textureRP) override { mAssemble->AddDependencies(textureRP); }

    virtual bool HasBeenDestroyed() override { return mAssemble == nullptr; };

    virtual void EndAssemble() override { mAssemble->EndAssemble(); };

    virtual bool HasEndAssemble() override { return mAssemble->HasEndAssemble(); };
    virtual const char* GetCompileErrorMessage() override { return mAssemble->GetCompileErrorMessage(); }
    FxAssemble* mAssemble = nullptr;
};

struct TextureAssembleProxy : virtual public ITextureAssemble, virtual public AssembleProxy
{
    explicit TextureAssembleProxy(std::string& reference, TextureAssemble* a)
        : AssembleProxy(reference, dynamic_cast<ITextureAssemble*>(this))
        , mAssemble(a)
    {}

    virtual void SaveToFile() override
    {
        mAssemble->SaveToFile();
    }

    virtual bool HasBeenDestroyed() override
    {
        return mAssemble == nullptr;
    };

    virtual void EndAssemble() override
    {
        mAssemble->EndAssemble();
    }

    virtual bool HasEndAssemble() override
    {
        return mAssemble->HasEndAssemble();
    }

    virtual void SetAdjustmentSettings(AdjustmentSettings* m) override
    {
        mAssemble->SetAdjustmentSettings(m);
    }

    virtual void AsMipMapArray(MipMapArray* m, bool vtstreaming) override
    {
        mAssemble->AsMipMapArray(m, vtstreaming);
    }

    virtual void AsUDIM(UDIM* m) override
    {
        mAssemble->AsUDIM(m);
    }
    virtual void SetTextureImportSetting(TextureImportSettingWrapper setting) override 
    { 
        mAssemble->SetTextureImportSetting(setting);
    }

    TextureAssemble* mAssemble = nullptr;
};

struct WorldAssembleProxy : virtual public IWorldAssemble, virtual public AssembleProxy
{
    explicit WorldAssembleProxy(std::string& reference, WorldAssemble* a)
        : AssembleProxy(reference, dynamic_cast<IWorldAssemble*>(this))
        , mAssemble(a)
    {}

    virtual void SaveToFile() override
    {
        mAssemble->SaveToFile();
    }

    virtual void SetSettings(WorldSettings* s) override
    {
        mAssemble->SetSettings(s);
    }

    virtual bool HasBeenDestroyed() override
    {
        return mAssemble == nullptr;
    };

    virtual void EndAssemble() override
    {
        mAssemble->EndAssemble();
    }

    virtual bool HasEndAssemble() override
    {
        return mAssemble->HasEndAssemble();
    }

    virtual IEntity* AddEntity(const char* name, Hierarchy* h) override
    {
        return mAssemble->AddEntity(name, h);
    }

    virtual IEntity* AddModel(const char* name, ModelData* d, Hierarchy* h) override
    {
        return mAssemble->AddModel(name, d, h);
    }

    virtual IEntity* AddLight(const char* name, LightData* l, Hierarchy* h) override
    {
        return mAssemble->AddLight(name, l, h);
    }

    virtual IEntity* AddPrefab(const char* name, IWorldAssemble* a, Hierarchy* h) override
    {
        return mAssemble->AddPrefab(name, a, h);
    }

    virtual IEntity* AddInstancedFoliage(const char* name, InstancedFoliage* a, Hierarchy* h) override
    {
        return mAssemble->AddInstancedFoliage(name, a, h);
    }

    virtual IEntity* AddCamera(const char* name, CameraInfo* a, Hierarchy* h) override
    {
        return mAssemble->AddCamera(name, a, h);
    }

    virtual IEntity* AddControllableUnit(const char* name, ControllableUnitInfo* a, Hierarchy* h) override
    {
        return mAssemble->AddControllableUnit(name, a, h);
    }

    virtual IEntity* AddSkyAtmosphere(const char* name, SkyAtmosphere* a, Hierarchy* h) override
    {
        return mAssemble->AddSkyAtmosphere(name, a, h);
    }

    virtual IEntity* AddPostProcessVolume(const char* name, PostProcessVolumeInfo* a, Hierarchy* h) override
    {
        return mAssemble->AddPostProcessVolume(name, a, h);
    }

    virtual IEntity* AddTerrain(const char* name, TerrainDesc* d, Hierarchy* h) override
    {
        return mAssemble->AddTerrain(name, d, h);
    }

    virtual const char* GetEntityUUID(IEntity* entity) override
    {
        return mAssemble->GetEntityUUID(entity);
    }

    WorldAssemble* mAssemble = nullptr;
};

}   // namespace CEAssetExchange
