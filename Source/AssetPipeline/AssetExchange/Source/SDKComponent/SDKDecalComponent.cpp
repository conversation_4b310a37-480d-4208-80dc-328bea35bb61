#include "SDKComponent/SDKDecalComponent.h"
#include "GameFramework/Components/DecalComponent.h"
namespace cesdk::cegf {

static ::cegf::DecalComponent* GetDecalComponent(const SDKDecalComponent* component)
{
    if (!component || !component->componentInstance)
    {
        return nullptr;
    }

    return static_cast<::cegf::DecalComponent*>(component->componentInstance);
}

bool SDKDecalComponent::GetDecalEnable() const
{
    ::cegf::DecalComponent* decalComponent = GetDecalComponent(this);
    if (decalComponent)
    {
        return decalComponent->GetDecalEnable();
    }
    return false;
}

void SDKDecalComponent::SetDecalEnable(bool enable)
{
    ::cegf::DecalComponent* decalComponent = GetDecalComponent(this);
    if (decalComponent)
    {
        decalComponent->SetDecalEnable(enable);
    }
}

void SDKDecalComponent::SetDecalVisible(bool visible)
{
    ::cegf::DecalComponent* decalComponent = GetDecalComponent(this);
    if (decalComponent)
    {
        decalComponent->SetDecalVisible(visible);
    }
}

const char* SDKDecalComponent::GetDecalMaterial() const
{
    ::cegf::DecalComponent* decalComponent = GetDecalComponent(this);
    if (decalComponent)
    {
        return decalComponent->GetDecalMaterial().c_str();
    }
    return "";
}

void SDKDecalComponent::SetDecalMaterial(const char* material)
{
    ::cegf::DecalComponent* decalComponent = GetDecalComponent(this);
    if (decalComponent && material)
    {
        decalComponent->SetDecalMaterial(std::string(material));
    }
}

uint32_t SDKDecalComponent::GetSortOrder() const
{
    ::cegf::DecalComponent* decalComponent = GetDecalComponent(this);
    if (decalComponent)
    {
        return decalComponent->GetSortOrder();
    }
    return 0;
}

void SDKDecalComponent::SetSortOrder(uint32_t sortOrder)
{
    ::cegf::DecalComponent* decalComponent = GetDecalComponent(this);
    if (decalComponent)
    {
        decalComponent->SetSortOrder(sortOrder);
    }
}

float SDKDecalComponent::GetFadeScreenSize() const
{
    ::cegf::DecalComponent* decalComponent = GetDecalComponent(this);
    if (decalComponent)
    {
        return decalComponent->GetFadeScreenSize();
    }
    return 0.01f;
}

void SDKDecalComponent::SetFadeScreenSize(float fadeScreenSize)
{
    ::cegf::DecalComponent* decalComponent = GetDecalComponent(this);
    if (decalComponent)
    {
        decalComponent->SetFadeScreenSize(fadeScreenSize);
    }
}

float SDKDecalComponent::GetFadeStartDelay() const
{
    ::cegf::DecalComponent* decalComponent = GetDecalComponent(this);
    if (decalComponent)
    {
        return decalComponent->GetFadeStartDelay();
    }
    return 0.0f;
}

void SDKDecalComponent::SetFadeStartDelay(float fadeStartDelay)
{
    ::cegf::DecalComponent* decalComponent = GetDecalComponent(this);
    if (decalComponent)
    {
        decalComponent->SetFadeStartDelay(fadeStartDelay);
    }
}

float SDKDecalComponent::GetFadeDuration() const
{
    ::cegf::DecalComponent* decalComponent = GetDecalComponent(this);
    if (decalComponent)
    {
        return decalComponent->GetFadeDuration();
    }
    return 0.0f;
}

void SDKDecalComponent::SetFadeDuration(float fadeDuration)
{
    ::cegf::DecalComponent* decalComponent = GetDecalComponent(this);
    if (decalComponent)
    {
        decalComponent->SetFadeDuration(fadeDuration);
    }
}

float SDKDecalComponent::GetFadeInDuration() const
{
    ::cegf::DecalComponent* decalComponent = GetDecalComponent(this);
    if (decalComponent)
    {
        return decalComponent->GetFadeInDuration();
    }
    return 0.0f;
}

void SDKDecalComponent::SetFadeInDuration(float fadeInDuration)
{
    ::cegf::DecalComponent* decalComponent = GetDecalComponent(this);
    if (decalComponent)
    {
        decalComponent->SetFadeInDuration(fadeInDuration);
    }
}

float SDKDecalComponent::GetFadeInStartDelay() const
{
    ::cegf::DecalComponent* decalComponent = GetDecalComponent(this);
    if (decalComponent)
    {
        return decalComponent->GetFadeInStartDelay();
    }
    return 0.0f;
}

void SDKDecalComponent::SetFadeInStartDelay(float fadeInStartDelay)
{
    ::cegf::DecalComponent* decalComponent = GetDecalComponent(this);
    if (decalComponent)
    {
        decalComponent->SetFadeInStartDelay(fadeInStartDelay);
    }
}

void SDKDecalComponent::GetDecalSize(float& x, float& y, float& z) const
{
    ::cegf::DecalComponent* decalComponent = GetDecalComponent(this);
    if (decalComponent)
    {
        ::cross::Float3 size = decalComponent->GetDecalSize();
        x = size.x;
        y = size.y;
        z = size.z;
    }
    else
    {
        x = 256.0f;
        y = 256.0f;
        z = 128.0f;
    }
}

void SDKDecalComponent::SetDecalSize(float x, float y, float z)
{
    ::cegf::DecalComponent* decalComponent = GetDecalComponent(this);
    if (decalComponent)
    {
        ::cross::Float3 size(x, y, z);
        decalComponent->SetDecalSize(size);
    }
}

} 