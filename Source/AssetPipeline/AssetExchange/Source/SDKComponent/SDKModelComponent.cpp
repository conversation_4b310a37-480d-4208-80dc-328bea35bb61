#include "SDKComponent/SDKModelComponent.h"
#include "GameFramework/Components/ModelComponent.h" // 包含原始 C++ ModelComponent 定义
#include <string> // 用于 std::string

namespace cesdk {
    namespace cegf {
        /**
         * @brief SDKModelComponent 构造函数实现
         * 
         * @param modelComponentInstance 指向内部引擎 ModelComponent 实例的指针
         * @details 通过基类 SDKGameObjectComponent 初始化列表传递引擎组件实例
         */
        SDKModelComponent::SDKModelComponent(::cegf::GameObjectComponent* modelComponentInstance)
            : SDKGameObjectComponent(modelComponentInstance) {
            // 构造函数体可以为空，或者进行必要的初始化
        }

        /**
         * @brief 为指定的模型索引设置模型资源路径
         * 
         * @param assetPath 网格资源数据的路径
         * @return 设置成功返回 true，否则返回 false
         * @details 将字符串路径传递给内部 ModelComponent 的同名方法
         */
        bool SDKModelComponent::SetModelAssetPath(const char* assetPath) {
            // 将 SDKGameObjectComponent 的实例指针转换为 ModelComponent 指针
            auto* modelComp = static_cast<::cegf::ModelComponent*>(componentInstance);
            if (modelComp && assetPath) {
                // 调用 C++ ModelComponent 的方法，将 C 风格字符串转换为 std::string
                return modelComp->SetModelAssetPath(std::string(assetPath));
            }
            return false; // 如果转换失败或 assetPath 为空，则返回 false
        }

        /**
         * @brief 设置指定模型索引的可见性
         * 
         * @param isVisible true 表示使模型可见，false 表示隐藏
         * @details 直接调用内部 ModelComponent 的同名方法
         */
        void SDKModelComponent::SetModelVisibility(bool isVisible) {
            // 将 SDKGameObjectComponent 的实例指针转换为 ModelComponent 指针
            auto* modelComp = static_cast<::cegf::ModelComponent*>(componentInstance);
            if (modelComp) {
                // 调用 C++ ModelComponent 的方法
                modelComp->SetModelVisible(isVisible);
            }
        }

        /**
         * @brief 为指定的子模型、LOD 和模型索引设置材质资源路径
         * 
         * @param assetPath 材质资源的路径
         * @param subModelIndex 子模型的索引，用于指定模型的特定部分
         * @param lodIndex 细节层次(LOD)的索引，控制模型在不同距离的精细度
         * @details 直接调用内部 ModelComponent 的同名方法
         */
        void SDKModelComponent::SetModelMaterialPath(const char* assetPath, int32_t subModelIndex, uint32_t lodIndex) {
            // 将 SDKGameObjectComponent 的实例指针转换为 ModelComponent 指针
            auto* modelComp = static_cast<::cegf::ModelComponent*>(componentInstance);
            if (modelComp && assetPath)
            {
                modelComp->SetModelMaterialPath(assetPath, subModelIndex, lodIndex);
            }
        }

        /**
         * @brief 添加运行时渲染效果标签
         * 
         * @param renderEffectTag 要添加的渲染效果标签
         * @details 为模型添加高亮、轮廓等特殊渲染效果
         */
        void SDKModelComponent::AddRuntimeRenderEffect(cross::RenderEffectTag renderEffectTag)
        {
            auto* modelComp = static_cast<::cegf::ModelComponent*>(componentInstance);
            if (modelComp)
            {
                // 将 SDK 中的枚举值转换为引擎内部的枚举值
                modelComp->AddRuntimeRenderEffect(::cross::RenderEffectTag(renderEffectTag));
            }
        }

        /**
         * @brief 移除运行时渲染效果标签
         * 
         * @param renderEffectTag 要移除的渲染效果标签
         * @details 移除之前添加的特殊渲染效果
         */
        void SDKModelComponent::RemoveRuntimeRenderEffect(cross::RenderEffectTag renderEffectTag)
        {
            auto* modelComp = static_cast<::cegf::ModelComponent*>(componentInstance);
            if (modelComp)
            {
                // 将 SDK 中的枚举值转换为引擎内部的枚举值
                modelComp->RemoveRuntimeRenderEffect(::cross::RenderEffectTag(renderEffectTag));
            }
        }

        /**
         * @brief 设置运行时渲染效果标志
         * 
         * @param flags 要设置的渲染效果标志位
         * @details 直接设置整个标志位，可以一次性设置多个效果
         */
        void SDKModelComponent::SetRuntimeRenderEffect(uint32_t flags)
        {
            auto* modelComp = static_cast<::cegf::ModelComponent*>(componentInstance);
            if (modelComp)
            {
                modelComp->SetRuntimeRenderEffect(flags);
            }
        }

        /**
         * @brief 检查是否存在指定的运行时渲染效果标签
         * 
         * @param renderEffectTag 要检查的渲染效果标签
         * @return 如果存在指定的渲染效果则返回 true，否则返回 false
         * @details 用于查询模型当前是否应用了某种特定的渲染效果
         */
        bool SDKModelComponent::HasRuntimeRenderEffect(cross::RenderEffectTag renderEffectTag) const
        {
            auto* modelComp = static_cast<::cegf::ModelComponent*>(componentInstance);
            if (modelComp)
            {
                return modelComp->HasRuntimeRenderEffect(::cross::RenderEffectTag(renderEffectTag));
            }
            return false;
        }

        /**
         * @brief 获取当前渲染效果标签
         * 
         * @return 当前的渲染效果标签
         * @details 获取当前模型的全部渲染效果配置
         * @note 如果组件实例无效，则返回默认效果标签
         */
        cross::RenderEffectTag SDKModelComponent::GetRenderEffectTag() const
        {
            auto* modelComp = static_cast<::cegf::ModelComponent*>(componentInstance);
            if (modelComp)
            {
                return cross::RenderEffectTag(modelComp->GetRenderEffectTag());
            }
            return cross::RenderEffectTag::DefaultEffect;
        }

    } // namespace cegf
} // namespace cesdk
