#include "SDKGameObject/SDKCharacterObject.h"

#include "CEGameFramework/GameFramework/GameEngine.h"
#include "GameFramework/GameObjects/GameObject.h"
#include "GameFramework/Components/Component.h"
#include "GameFramework/GameObjects/Character.h"

namespace cesdk::cegf {
SDKCharacterObject CreateCharacterObject(SDKGameWorld* gameWorld, const char* name, SDKGameObject parent, const cross::TRSVector3Type& localLocation, const cross::TRSQuaternionType& localRotation, const cross::TRSVector3Type& localScale)
{
    ::cross::TRSVector3Type location = {localLocation.x, localLocation.y, localLocation.z};
    ::cross::TRSQuaternionType rotation = {localRotation.x, localRotation.y, localRotation.z, localRotation.w};
    ::cross::TRSVector3Type scale = {localScale.x, localScale.y, localScale.z};

    auto metaClassName = ::cegf::Character::MetaClassName();
    auto character = gameWorld->gameWorldInstance->CreateGameObjectByMetaClassName(name, metaClassName, parent.gameObjectInstance, location, rotation, scale);
    character->GetMetaClass();
    return SDKCharacterObject(character);
}
}   // namespace cesdk::cegf
