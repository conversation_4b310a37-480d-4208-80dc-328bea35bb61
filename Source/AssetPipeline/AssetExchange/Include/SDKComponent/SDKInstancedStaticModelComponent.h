#pragma once
#include "../CrossEngineSDK.h" // 包含 SDKGameObjectComponent 和 AssetExchange_API 定义
#include "SDKModelComponent.h"

namespace cesdk {
    namespace cegf {


        class AssetExchange_API SDKInstancedStaticModelComponent : public SDKModelComponent {
        public:

            SDKInstancedStaticModelComponent(::cegf::GameObjectComponent* instancedStaticModelComponentInstance);

            bool SetInstanceDataResourcePath(const char* resourcePath);
            const char* GetInstanceDataResourcePath() const;
        };
    } // namespace cegf
} // namespace cesdk
