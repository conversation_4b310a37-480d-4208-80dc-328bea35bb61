#pragma once
#include "../CrossEngineSDK.h" 
#include "../IMeshAssemble.h"
namespace cesdk { 
	namespace cegf {
        /**
         * @class SDKPhysicsComponent
         * @brief 引擎 PhysicsComponent 的包装类
         *
         * 提供操作物理属性的接口。
         */
    enum class CollisionType: uint16_t
    {
        NoCollision = 0,
        WorldStatic = 1,
        WorldDynamic = 1<<1,
        Actor = 1<<2
    };
    class AssetExchange_API SDKPhysicsComponent : public SDKGameObjectComponent
    {
    public:

        SDKPhysicsComponent(::cegf::GameObjectComponent* physicsComponentInstance);

        void SetUseMeshCollision(bool useMeshCollision);

        void SetCollisionType(CollisionType type);

        void SetCollisionMask(uint16_t mask);

        void SetIsDynamic(bool isDynamic);

        void SetIsKinematic(bool isKinematic);

        void SetIsTrigger(bool isTrigger);

        void AddBoxCollision(const CEAssetExchange::BoxCollision* data, const std::uintmax_t size);

        void AddSphereCollision(const CEAssetExchange::SphereCollision* data, const std::uintmax_t size);

        void AddCapsuleCollision(const CEAssetExchange::CapsuleCollision* data, const std::uintmax_t size);

        void AddConvexCollision(const CEAssetExchange::ConvexCollision* data);
    };
    }
}  // namespace cesdk::cegf