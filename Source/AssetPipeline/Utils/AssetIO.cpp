#include "AssetPipeline/PCH/AssetPipelinePCH.h"
#include "AssetPipeline/Utils/AssetIO.h"
#include "Resource/BaseClasses/ClassIDs.h"
#include "flatbuffers/reflection.h"
#include <stdlib.h>
#include <stdio.h>

#if CROSSENGINE_LINUX
#    include <unistd.h>
#    include <dirent.h>
#endif
#if CROSSENGINE_WIN
#    include <io.h>
#    include <corecrt_io.h>
#endif

namespace cross::editor::AssetIO {
void LoadAssetHeader(AssetHeader& header, cross::SimpleSerializer& serializer)
{
    serializer.Read(header);
}
//These functions are deprecated
void LoadAssetHeader(AssetHeader& header, File& file)
{
    cross::FileArchive archive{file};
    cross::SimpleSerializer serializer{archive};
    cross::FBSerializer fbserializer{archive};
    CrossSchema::ResourceHeader header1;
    auto verifier = flatbuffers::Verifier(archive.Data(), archive.Size());
    auto root = flatbuffers::GetAnyRoot(archive.Data());
    // Only verify header
    bool ok = root->VerifyTableStart(verifier) && root->template VerifyField<CrossSchema::ResourceAsset>(verifier, CrossSchema::ResourceAsset::VT_HEADER);
    if (ok)
    {
        // auto table = CrossSchema::GetResourceAsset(fbserializer.GetArchive().Data());
        auto table = CrossSchema::GetResourceAsset(fbserializer.GetCachedBuffer().data());
        fbserializer.Read(table, header1, CrossSchema::ResourceAsset::VT_HEADER);
    }
    ///
    if (header1.classid() == ClassID(Texture2D))
    {
        header = *reinterpret_cast<AssetHeader*>(&header1);
    }
    else
    {
        file.SeekRead(0, File::SeekFrom::Begin);
        LoadAssetHeader(header, serializer);
    }
}
// These functions are deprecated
void LoadAssetHeader(AssetHeader& header, filesystem::IFilePtr inFile)
{
    cross::FileArchive archive{inFile};
    cross::SimpleSerializer serializer{archive};
    cross::FBSerializer fbserializer{archive};
    CrossSchema::ResourceHeader header1;
    auto verifier = flatbuffers::Verifier(archive.Data(), archive.Size());
    auto root = flatbuffers::GetAnyRoot(archive.Data());
    // Only verify header
    bool ok = root->VerifyTableStart(verifier) && root->template VerifyField<CrossSchema::ResourceAsset>(verifier, CrossSchema::ResourceAsset::VT_HEADER);
    if (ok)
    {
        // auto table = CrossSchema::GetResourceAsset(fbserializer.GetArchive().Data());
        auto table = CrossSchema::GetResourceAsset(fbserializer.GetCachedBuffer().data());
        fbserializer.Read(table, header1, CrossSchema::ResourceAsset::VT_HEADER);
    }
    ///
    if (header1.classid() == ClassID(Texture2D))
    {
        header = *reinterpret_cast<AssetHeader*>(&header1);
    }
    else
    {
        inFile->Seek(0, filesystem::SEEK_OPTION_BEGIN);
        LoadAssetHeader(header, serializer);
    }
}

bool DeserializeTextureAsset(CrossSchema::TextureAssetT& tex, File& file)
{
    return DeserializeAsset(tex, file, [](CrossSchema::ResourceType type) { return type == CrossSchema::ResourceType::TextureAsset; });
}

bool DeserializeTextureAsset(CrossSchema::TextureAssetT& tex, filesystem::IFilePtr inFile)
{
    return DeserializeAsset(tex, inFile, [](CrossSchema::ResourceType type) { return type == CrossSchema::ResourceType::TextureAsset; });
}

bool GetFiles(std::string dir, std::vector<std::string>& dirs, std::vector<std::string>& files)
{
    bool ret = false;
#if CROSSENGINE_WIN
    dir = dir + "/*";
    intptr_t fp = 0;
    struct _finddata_t fileinfo;
    if ((fp = _findfirst(dir.c_str(), &fileinfo)) >= 0)
    {
        while (_findnext(fp, &fileinfo) == 0)
        {
            if (strcmp(fileinfo.name, ".") == 0 || strcmp(fileinfo.name, "..") == 0)
                continue;
            if ((fileinfo.attrib & _A_SUBDIR))
                dirs.push_back(fileinfo.name);
            else
                files.push_back(fileinfo.name);
        }
        ret = true;
    }
    _findclose(fp);
#endif

#if CROSSENGINE_LINUX
    DIR* dir;
    struct dirent* ptr;
    char base[1000];

    if ((dir = opendir(cate_dir.c_str())) != NULL)
    {
        while ((ptr = readdir(dir)) != NULL)
        {
            if (strcmp(ptr->d_name, ".") == 0 || strcmp(ptr->d_name, "..") == 0)   /// current dir OR parent dir
                continue;
            else if (ptr->d_type == 8)   /// file
                files.push_back(ptr->d_name);
            else if (ptr->d_type == 10)   /// link file
                continue;
            else if (ptr->d_type == 4)   /// dir
            {
                dirs.push_back(ptr->d_name);
            }
        }
        ret = true;
    }
    closedir(dir);
#endif
    return ret;
}
}   // namespace cross::editor::AssetIO
