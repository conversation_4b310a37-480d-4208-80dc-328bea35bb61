#pragma once

#if WIN32
#    ifdef MeshSimplifier_EXPORTS
#        define MeshSimplifier_API __declspec(dllexport)
#    else
#        define MeshSimplifier_API __declspec(dllimport)
#    endif
#else
#    if defined(__GNUC__)
#        define MeshSimplifier_API __attribute__((visibility("default")))
#    else
#        define MeshSimplifier_API
#    endif
#    define __stdcall
#endif

#define MESHOPTIMIZER_API MeshSimplifier_API