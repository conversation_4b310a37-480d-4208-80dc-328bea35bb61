#pragma once

#include <fbxsdk.h>
#include "AssetPipeline/Utils/AssetMath.h"
#ifndef MAX_PATH
#    define MAX_PATH 260
#endif

namespace cross::editor {
struct IEntityBuffer;

struct SubMeshInfo
{
    UInt32 uMeshID;
    UInt32 uSubMeshID;
    AssetMath::Matrix4x4f localToWorld;
};

struct MeshStructureInfo
{
    std::vector<AssetMath::Vector3f> vertices;
    std::vector<AssetMath::Vector3f> normals;
    std::vector<UInt32> indices;
    std::vector<std::vector<AssetMath::Vector2f>> texCoords;
    std::vector<AssetMath::Vector4f> tangents;
    std::vector<AssetMath::Vector4f> binormals;
};

class FBXExporter
{
public:
    FBXExporter();
    ~FBXExporter();

public:
    bool ExportSelectModelsAsFBX(const char* pcszFBXFilename, IEntityBuffer* pEntityBuffer);
    bool ExportSelectModelsAsFBX(const char* inputNdaFilename, const char* outputFbxFilename);
    bool CheckNdaIsValid(const char* inputNdaFilename);
    void ExportSingleMesh(std::vector<MeshStructureInfo>& info, int meshId);
    bool MergeSelectModelsAsFBX(std::vector<std::string> fbxPath, std::vector<cross::Float3> fbxPosition, std::vector<cross::Float3> fbxRotation, std::vector<cross::Float3> fbxScale, const char* fbxpath);
    void MeshAssetDataTToMeshStructure(CrossSchema::ImportMeshAssetDataT& dataT, std::vector<MeshStructureInfo>& info);
    void FbxAddVertices(std::vector<AssetMath::Vector3f>& vertices, int index);
    void FbxAddNormals(std::vector<AssetMath::Vector3f>& normals, int index);
    void FbxAddTexCoords(std::vector<AssetMath::Vector2f>& uvs, int uvLayer, const char* channelName, int index);
    void FbxAddIndices(std::vector<UInt32>& indices, int index, int Material, int size = 0);
    void FbxAddTangents(std::vector<AssetMath::Vector4f>& tangents, int index);
    void FbxAddBiNormals(std::vector<AssetMath::Vector4f>& binormals, int index);
    void FbxAddMaterial(int index);
    Float3 CalLeftToRightRotation(Float3 rotation);
    int FbxCreateDefaultMaterial(int index);
    void FbxEndMesh(int i, Float3 position = Float3(0.0f, 0.0f, 0.0f), Float3 rotation = Float3(0.0f, 0.0f, 0.0f), Float3 scale = Float3(1.0f, 1.0f, 1.0f));
    void FbxEndMeshMultiNode(int i, Float3 position = Float3(0.0f, 0.0f, 0.0f));
    bool SaveScene(const char* exportFbxFilename, int pFileFormat = 0, bool pEmbedMedia = false);

private:
    void InitializeFbxSdkObjects();
    bool CreateScene(IEntityBuffer* pEntityBuffer);
    bool SaveScene(int nFileFormat = -1, bool bEmbedMedia = false);
    void DestroyFbxSdkObjects(FbxManager* fbxSdkManager, bool bExitStatus);
    void Clear();

private:
    void AxisSystemConvert();

    void ExtractModelInfo();

    void ExportNode();

    void SetGeometryInfo(FbxMesh* pFbxMesh, SubMeshInfo& subMeshInfo);

    void CreateDefaultMaterial(FbxMesh* pFbxMesh, FbxNode* pFbxNode, FbxSurfacePhong* pFbxMaterial, int nGroupIndex, const char* pcszTextureFilename);

    void CreateCustomMaterial(FbxNode* pFbxNode);

    void CreateTableEntry(FbxBindingTable* pBindingTable, FbxProperty& pProperty);

    void SetNodeTransform(FbxNode* pFbxNode, SubMeshInfo& subMeshInfo);

private:
    FbxManager* m_pFbxSdkManager;
    FbxScene* m_pFbxScene;
    FbxExporter* mExporter;
    std::vector<FbxMesh*> mFbxMeshs;
    std::vector<FbxNode*> meshNodes;
    char m_pszFBXFilename[MAX_PATH];
    std::map<std::string, std::vector<SubMeshInfo>> m_mapTextureToSubMeshInfo;
    const char* m_pcszDiffuseElementName;
    const char* m_pcszLightMapElementName;
    bool m_bUseCustomMaterial;
};
}   // namespace cross::editor