#include "EnginePrefix.h"
#include "Font.h"

#include "Resource/Texture/Texture.h"
#include "Resource/Texture/Texture2D.h"
#include "Resource/ResourceManager.h"
#include "Resource/AssetStreaming.h"
#include "Resource/Resource.h"

#include "image.h"

namespace cross::ui {
const char* DefaultFont = "EngineResource/Font/Lato-Regular.json";
ResourceFuturePtr<resource::Texture> load_texture(const char* url, UInt32& width, UInt32& height)
{
    imageio::image local_image;
    imageio::load_image(url, local_image);

    width = local_image.get_width();
    height = local_image.get_height();

    auto extension = PathHelper::GetExtension(url);
    if (extension == "png" || extension == "jpg")
    {
        if (width == 0 || height == 0)
        {
            auto texture = TypeCast<resource::Texture>(gAssetStreamingManager->LoadSynchronously("EngineResource/Texture/DefaultTexture.nda"));
            width = texture->GetWidth();
            height = texture->GetHeight();
            return texture;
        }
        else
        {
            UInt32 size = local_image.get_total_pixels() * sizeof(imageio::color_rgba);
            UInt32 pitch = local_image.get_pitch() * sizeof(imageio::color_rgba);
            auto texture = gResourceMgr.CreateResourceAs<resource::Texture2D>(TextureFormat::RGBA32, ColorSpace::Linear, width, height, 1, url);
            texture->UploadImage(0, reinterpret_cast<const UInt8*>(local_image.get_pixels().data()), size, pitch);
            return TypeCast<resource::Texture>(texture);
        }
    }
    else
    {
        auto texture = TypeCast<resource::Texture>(gAssetStreamingManager->LoadSynchronously(url));
        width = texture->GetWidth();
        height = texture->GetHeight();
        return texture;
    }
}

std::string ReadFile(const std::string filename)
{
    std::string file_content;
    {
        UInt64 size = 0;
        //std::string absolute_path = PathHelper::GetAbsolutePath(filename);
        filesystem::FileSystem* fileSystem = cross::EngineGlobal::Inst().GetFileSystem();
        Assert(fileSystem);
        filesystem::IFilePtr file = fileSystem->Open(filename);
        if (!file)
            file = fileSystem->Open(DefaultFont);
        size = file->GetSize();
        file_content.resize(size);
        file->Read(file_content.data(), size);
        file->Close();
    }

    return file_content;
}

SDFFont::SDFFont(const char* source)
{
    auto source_str = ReadFile(source);
    DeserializeNode doc = DeserializeNode::ParseFromJson(source_str);

    std::string absolutePath = PathHelper::GetAbsolutePath(source);
    PathHelper::IsFileExist(absolutePath);
    auto dirPath = PathHelper::GetDirectoryFromAbsolutePath(absolutePath);

    auto texturePath = doc["pages"][0].AsString();
    auto textureAbsolutePath = PathHelper::Combine(dirPath.c_str(), texturePath.c_str());
    mTexture = load_texture(textureAbsolutePath.c_str(), mTextureWidth, mTextureHeight);

    mTextureWidth = doc["common"]["scaleW"].AsUInt32();
    mTextureHeight = doc["common"]["scaleH"].AsUInt32();
    mBase = doc["common"]["base"].AsInt32();
    mSize = doc["info"]["size"].AsInt32();

    auto chars = doc["chars"];
    mAscent = -INT_MAX;
    mDescent = INT_MAX;
    mLineHeight = doc["common"]["lineHeight"].AsInt32();

    for (size_t i = 0, l = chars.Size(); i < l; ++i)
    {
        auto char_doc = chars[static_cast<int>(i)];
        SDFChar charNode;
        charNode.index = char_doc["index"].AsInt32();
        charNode.char_code = char_doc["char"].AsString();
        charNode.id = char_doc["id"].AsInt32();
        charNode.x = char_doc["x"].AsInt32();
        charNode.y = char_doc["y"].AsInt32();
        charNode.x_offset = char_doc["xoffset"].AsInt32();
        charNode.y_offset = char_doc["yoffset"].AsInt32();
        charNode.x_advance = char_doc["xadvance"].AsInt32();
        charNode.width = char_doc["width"].AsInt32();
        charNode.height = char_doc["height"].AsInt32();
        mCharMap[charNode.id] = charNode;
        int upper = charNode.height + charNode.y_offset - mBase;
        mAscent = mAscent > upper ? mAscent : upper;
        mDescent = mDescent < (charNode.y_offset - mBase) ? mDescent : (charNode.y_offset - mBase);
    }

    auto kernings = doc["kernings"];
    for (size_t i = 0, l = kernings.Size(); i < l; ++i)
    {
        auto kerning_doc = kernings[static_cast<int>(i)];
        auto first = kerning_doc["first"].AsInt32();
        auto second = kerning_doc["second"].AsInt32();
        auto amount = kerning_doc["amount"].AsInt32();
        mKerningMap[KerningHash{first, second}.hash()] = amount;
    }
}

int SDFFont::ComputeKerning(int previous_id, int next_id)
{
    auto it = mKerningMap.find(KerningHash{previous_id, next_id}.hash());
    if (it == mKerningMap.end())
        return 0;
    return it->second;
}

SDFFont::SDFChar SDFFont::GetChar(int char_code)
{
    auto it = mCharMap.find(char_code);
    if (it == mCharMap.end())
    {
        return mCharMap[32];
    }
    return it->second;
}

void Font::SetSize(const int size)
{
    mSize = size;
    mRatio = static_cast<float>(size) / static_cast<float>(mSDFFont->mSize);
}

std::vector<UInt16> _TmpChars;
float Font::ComputeWidth(const char* content, float* offset)
{
    int textLength = static_cast<int>(strlen(content));
    if (_TmpChars.size() < textLength)
    {
        _TmpChars.resize(textLength);
    }
    int size;
    ConvertUTF8toUTF16(content, textLength, _TmpChars.data(), size);

    int length = 0;
    int preId = -1;
    for (int i = 0; i < size; ++i)
    {
        auto char_node = mSDFFont->GetChar(_TmpChars[i]);
        auto kerning = mSDFFont->ComputeKerning(preId, char_node.id);
        length += char_node.x_advance + kerning;
        preId = char_node.id;
        if (offset != nullptr) offset[i] = static_cast<float>(length) * mRatio;
    }
    return static_cast<float>(length + 4) * mRatio;   // padding
}
static Font* _font = nullptr;
Font* SetDefaultFont(Font* font)
{
    _font = font;
    return _font;
}

Font* GetDefaultFont() {
    if (_font == nullptr) {
        _font = new ui::Font(std::make_shared<SDFFont>("EngineResource/Font/consola.json"));
        _font->SetSize(14);
    }

    return _font;
}


}   // namespace cross::ui
