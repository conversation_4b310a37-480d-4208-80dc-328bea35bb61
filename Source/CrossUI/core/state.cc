#include "state.h"
#include "primitive_buffer.h"
#include "../uimath.h"

namespace oui {

UIState::UIState()
{
    for (int i = 0; i < SWAP_BUFFER_COUNT; ++i) {
        primitive_buffer_create(&buffers[i]);
    }

    key_press = new std::set<Keycode>();
    key_pressed = new std::set<Keycode>();
}

UIState::~UIState() {
    delete key_press;
    delete key_pressed;
}

void UIState::RequestUpdate()
{
    _defer_update_frame_index = _defer_update_frame_count;
}

void UIState::ClearMouseState()
{
    left_mouse_press = false;
    left_mouse_release = false;
    right_mouse_press = false;
    right_mouse_release = false;
    middle_mouse_press = false;
    middle_mouse_release = false;
}

bool UIState::Update()
{
    key_press->clear();

    hover = next_hover;
    hover_layer = next_hover_layer_index;
    next_hover = -1;
    next_hover_layer_index = -1;

    if (double_click_id != -1) {
        if (double_click_frame_index < double_click_defer_frame) {
            double_click_frame_index++;
        } else {
            double_click_id = -1;
        }
    }

    mouse_wheel = 1.0;
    mouse_wheel_raw = 0.f;

    primitive_buffer_update(&buffers[swap_index]);
    ClearMouseState();

    last_swap_index = swap_index;
    swap_index = (swap_index + 1) % SWAP_BUFFER_COUNT;

    bool updated = _defer_update_frame_index > 0;
    if (updated) _defer_update_frame_index--;
    return updated;
}

bool UIState::IsHovering(ui_rect test_rect)
{
    return ui_rect_contains(test_rect, mouse_location);
}

void UIState::SetActive(u32 id) {
    last_active = active;
    active = id;
}

void UIState::ClearActive()
{
    last_active = active;
    active = -1;
    double_click_frame_index = 0;
}

}