#include "EnginePrefix.h"
#include "PhysXEngineImpl.h"

#include "CECommon/Common/EngineGlobal.h"
#include "PhysicsEngine/PhysXImpl/PhysXQuery.h"
#include "PhysicsEngine/PhysXImpl/PhysXJoint.h"
#include "PhysicsEngine/PhysXImpl/PhysXCooker.h"
#include "PhysicsEngine/PhysXImpl/PhysXScene.h"
#include "PhysicsEngine/PhysXImpl/PhysXMaterial.h"
#include "PhysicsEngine/PhysXImpl/PhysXUtility.h"
#include "PhysicsEngine/PhysXImpl/PhysXActor.h"
#include "PhysicsEngine/PhysXImpl/PhysXCpuDispatcher.h"
#include "PhysicsEngine/PhysXImpl/PhysXGeometry.h"
#include "CECommon/Common/SettingsManager.h"

namespace cross {
#define PHYSX_SAFE_RELEASE(PTR) do{if(PTR)PTR->release();PTR=nullptr;}while(0)
#define PHYSX_SAFE_DELETE(PTR) do{if(PTR) delete PTR;PTR=nullptr;}while(0)

constexpr char PVD_HOST[] = "127.0.0.1";

void PhysXErrorCallback::reportError(physx::PxErrorCode::Enum code, const char* message, const char* file, int line)
{
    std::string_view fileName = PathHelper::GetCleanFileName(std::string_view{file});
    switch (code)
    {
    case physx::PxErrorCode::eDEBUG_INFO:
        LOG_INFO("{}:{} PhysX Info: {}", fileName.data(), line, message);
        break;
    case physx::PxErrorCode::eDEBUG_WARNING:
        LOG_WARN("{}:{} PhysX Warn: {}", fileName.data(), line, message);
        break;
    case physx::PxErrorCode::eINVALID_PARAMETER:
        LOG_ERROR("{}:{} PhysX Invalid Parameter: {}", fileName.data(), line, message);
        break;
    case physx::PxErrorCode::eINVALID_OPERATION:
        LOG_ERROR("{}:{} PhysX Invalid Operation: {}", fileName.data(), line, message);
        break;
    case physx::PxErrorCode::eOUT_OF_MEMORY:
        LOG_ERROR("{}:{} PhysX Out of Memory: {}", fileName.data(), line, message);
        break;
    case physx::PxErrorCode::eINTERNAL_ERROR:
        LOG_ERROR("{}:{} PhysX internal error: {}", fileName.data(), line, message);
        break;
    case physx::PxErrorCode::eABORT:
        LOG_ERROR("{}:{} PhysX abort: {}", fileName.data(), line, message);
        break;
    case physx::PxErrorCode::ePERF_WARNING:
        LOG_ERROR("{}:{} PhysX perf warning: {}", fileName.data(), line, message);
        break;
    default:
        //NO error
        break;
    }
}

void PhysXEngineImpl::Init()
{
    Assert(!gPhysXEngineImpl);
    gPhysXEngineImpl = this; //I can ensure this is OK because physx::PxFoundation* can only exist one at the same time.

    mFoundation = PxCreateFoundation(PX_PHYSICS_VERSION, mAllocator, mErrorCallback);
    bool pvdSaveToFile = false;
    EngineGlobal::GetSettingMgr()->GetValue("Physics.Pvd.SaveToFile", pvdSaveToFile);

    if (GetEnablePVD())
    {
        mPvd = physx::PxCreatePvd(*mFoundation);
        physx::PxPvdTransport* transport = nullptr;
        if (pvdSaveToFile)
        {
            std::string saveDir = PathHelper::GetCurrentDirectoryPath() + "/Saved/pvd/";
            std::string pvdPath = fmt::format("{}/{}.pxd2", saveDir, time::TimeStamp());
            if (!PathHelper::IsDirectoryExist(saveDir))
                PathHelper::MakeDirectory(saveDir);
            transport = physx::PxDefaultPvdFileTransportCreate(pvdPath.c_str());
        }
        else
        {
            transport = physx::PxDefaultPvdSocketTransportCreate(PVD_HOST, 5425, 10);
            LOG_DEBUG("Connecting PVD with {}", PVD_HOST);
        }
        mPvd->connect(*transport, physx::PxPvdInstrumentationFlag::eALL);
    }

    auto scale = physx::PxTolerancesScale();
    scale.length = 100;
    scale.speed = 981;
    mPhysics = PxCreatePhysics(PX_PHYSICS_VERSION, *mFoundation, scale, true, mPvd);
    PxInitExtensions(*mPhysics, mPvd);
    //mDispatcher = physx::PxDefaultCpuDispatcherCreate(2);
    mDispatcher = CreatePhysXCpuDispatcher();
    mCooker = new PhysXCooker(mPhysics, mFoundation, scale);
}

void PhysXEngineImpl::CleanUp()
{
    Assert(gPhysXEngineImpl);
    gPhysXEngineImpl = nullptr;

    PxCloseExtensions();
    PHYSX_SAFE_DELETE(mCooker);
    DestroyPhysXCpuDispatcher(mDispatcher);
    PHYSX_SAFE_RELEASE(mPhysics);
    if (mPvd)
    {
        physx::PxPvdTransport* transport = mPvd->getTransport();
        PHYSX_SAFE_RELEASE(mPvd);
        PHYSX_SAFE_RELEASE(transport);
    }
    PHYSX_SAFE_RELEASE(mFoundation);
}

PhysicsScene* PhysXEngineImpl::CreatePhysicsScene(bool requireRWLock)
{
    return PhysXScene::Create(requireRWLock);
}

void PhysXEngineImpl::ReleasePhysicsScene(PhysicsScene* scene)
{
    PhysXScene::Destroy(static_cast<PhysXScene*>(scene));
}

PhysicsActor* PhysXEngineImpl::CreateRigidStatic(const Float3& pos, const Quaternion& rotation)
{
    return PhysXActor::CreateRigidStatic(pos, rotation);
}

PhysicsActor* PhysXEngineImpl::CreateRigidDynamic(const Float3& pos, const Quaternion& rotation)
{
    return PhysXActor::CreateRigidDynamic(pos, rotation);
}

void PhysXEngineImpl::ReleasePhysicsActor(PhysicsActor* actor)
{
    PhysXActor::Destroy(static_cast<PhysXActor*>(actor));
}

PhysicsMaterial* PhysXEngineImpl::CreateMaterial(float staticFriction, float dynamicFriction, float restitution)
{
    return PhysXMaterial::Create(staticFriction, dynamicFriction, restitution);
}

void PhysXEngineImpl::ReleaseMaterial(PhysicsMaterial* material)
{
    PhysXMaterial::Destroy(static_cast<PhysXMaterial*>(material));
}

PhysicsMaterial* PhysXEngineImpl::GetDefaultMaterial()
{
    static PhysicsMaterial* phyMat = CreateMaterial(0.5f, 0.5f, 0.6f);
    return phyMat;
}

PhysicsJoint* PhysXEngineImpl::CreatePhysicsJoint(PhysicsActor* actor0, PhysicsActor* actor1, const PhysicsJointConfig& config)
{
    return PhysXJoint::Create(actor0, actor1, config);
}

void PhysXEngineImpl::ReleaseJoint(PhysicsJoint* joint)
{
    delete joint;
}

bool PhysXEngineImpl::GetEnablePVD() const
{
  //  if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() != cross::AppStartUpType::AppStartUpTypeCrossEditor)
        return true;
  //  return false;
}

PhysXEngineImpl* gPhysXEngineImpl = nullptr;
}
