#include "EnginePrefix.h"
#include "PhysXMaterial.h"


namespace cross
{
    PhysXMaterial* PhysXMaterial::Create(float staticFriction, float dynamicFriction, float restitution)
    {
		physx::PxMaterial* mat = PxGetPhysics().createMaterial(staticFriction, dynamicFriction, restitution);
		return new PhysXMaterial{ mat };
    }

    void PhysXMaterial::Destroy(PhysXMaterial* mat)
    {
		delete mat;
    }

    PhysXMaterial::~PhysXMaterial()
    {
		if (mPxMaterial)
			mPxMaterial->release();
    }
}
