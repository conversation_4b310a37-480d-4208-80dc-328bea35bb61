#pragma once
#include "PhysicsEngine/CrossPhysics.h"
#include <tuple>

#include "PxPhysicsAPI.h"

#include "PhysicsEngine/PhysicsActor.h"
#include "PhysicsEngine/PhysXImpl/PhysXShape.h"

namespace cross
{
    class PhysXActor : public PhysicsActor
    {
    public:
        PhysXActor(physx::PxRigidActor* pxActor);
        PhysXActor(physx::PxRigidActor* pxActor, bool ownPxActor);
        ~PhysXActor() override;
    public:
        static PhysXActor* CreateRigidStatic(Float3A pos, QuaternionA rotation);
        static PhysXActor* CreateRigidDynamic(Float3A pos, QuaternionA rotation);
        static void Destroy(PhysXActor* actor);
    public:
        bool IsDynamicActor() const override;
        void SetScene(PhysicsScene* scene) override;
        PhysicsShape* AddTriangleMeshShape(const PhysicsGeometryMesh& meshGeo, PhysicsMaterial* material) override;
        PhysicsShape* AddConvexMeshShape(const PhysicsGeometryConvex& convexGeo, PhysicsMaterial* material) override;
        PhysicsShape* AddBoxShape(const PhysicsGeometryBox& boxGeo, PhysicsMaterial* material) override;
        PhysicsShape* AddSphereShape(const PhysicsGeometrySphere& sphereGeo, PhysicsMaterial* material) override;
        PhysicsShape* AddCapsuleShape(const PhysicsGeometryCapsule& capsuleGeo, PhysicsMaterial* material) override;
        PhysicsShape* AddPlaneShape(const PhysicsGeometryPlane& planeGeo, PhysicsMaterial* material) override;
        PhysicsShape* AddTerrainShape(const PhysicsGeometryTerrain& terrainGeo, PhysicsMaterial* material) override;
        void ClearShape(PhysicsShape* shape) override;
        void ClearAllShapes() override;

        size_t GetShapeCount() const override;
        PhysicsShape* GetShape(size_t index) override;
        const PhysicsShape* GetShape(size_t index) const override;

        void SetOnlyUsedForSceneQuery(bool onlyForSceneQuery) override;
        bool GetOnlyUsedForSceneQuery() const override;

        void SetMass(float mass) override;
        float GetMass() const override;

        Float3A GetMassSpaceInertiaTensor() const override;
        void SetMassSpaceInertiaTensor(const Float3A& tensor) override;

        void UpdateMassAndInertia(float density, const Float3A& massLocalPose, bool includeNonSimShapes) override;
        void SetMassAndUpdateInertia(float mass, const Float3A& massLocalPose, bool includeNonSimShapes) override;

        void SetRigidDynamicFlags(RigidDynamicFlag flags, bool value) override;

        physx::PxRigidActor* GetUnderlay() const { return mPxActor; }
        void SetDebugName(const char* name) override;
        std::string GetDebugName() override;

        void SetTransform(const Float3& pos, const Quaternion& rotation) override;
        void SetTransformAssumeSceneLocked(const Float3& pos, const Quaternion& rotation) override;
        std::pair<Float3, Quaternion> GetTransform() const override;

        void SetScale(const Float3A& scale) override;
        const Float3& GetScale() const override;

        void AddForce(const Float3A& force) override;
        void AddImpulse(const Float3A& impulse) override;
        void AddLinearVelocity(const Float3A& velocity) override;
        void AddLinearAcceleration(const Float3A& acceleration) override;
        void ClearForce() override;
        void ClearImpulse() override;
        void ClearLinearVelocity() override;
        void ClearLinearAcceleration() override;

        void AddTorque(const Float3& torque) override;
        void ClearTorque() override;
        void AddImpulseTorque(const Float3& impulse) override;
        void ClearImpulseTorque() override;
        void AddAngularVelocity(const Float3& velocity) override;
        void ClearAngularVelocity() override;
        void AddAngularAcceleration(const Float3& acceleration) override;
        void ClearAngularAcceleration() override;

        Float3 GetAngularVelocity() const override;
        Float3 GetLinearVelocity() const override;

        void SetCollisionEnter(CollisionCallBack callback, bool reportCollisionPoint) override;
        const CollisionCallBack& GetCollisionEnter() const { return mCollisionEnter; }
        void SetCollisionStay(CollisionCallBack callback, bool reportCollisionPoint) override;
        const CollisionCallBack& GetCollisionStay() const { return mCollisionStay; }
        void SetCollisionExit(CollisionCallBack callback, bool reportCollisionPoint) override;
        const CollisionCallBack& GetCollisionExit() const { return mCollisionExit; }

        void SetEnableGravity(bool enable) override;

        float GetLinearDamping() const override;
        void SetLinearDamping(float linearDamping) override;

        // We don't use PhysX trigger in fact. Because triangleMesh and HeightField geometries are not supported for trigger shapes.
        // So we use kinematic actor and filterData to do this.
        // For this reason, we need trigger to be a RigidDynamic
        void SetIsTrigger(bool isTrigger) override;
        bool GetIsTrigger() const override;

        bool GetIsKinematic() const override;
        void SetIsKinematic(bool isKinematic, bool wakeUp = true) override;

        void SetMaxDepenetrationVelocity(float velocity) override;
        float GetMaxDepenetrationVelocity() const override;

        void PutToSleep() override;

        void SetKinematicTarget(const Float3A& position, const Quaternion& rotation) override;
        void SetKinematicTargetAssumeSceneLocked(const Float3A& position, const Quaternion& rotation) override;

        void SetCollisionType(CollisionType type) override;
        void SetCollisionMask(CollisionMask mask) override;
    protected:
        void AddShape(PhysXShape* shape);
        void UpdateFilterData();
    protected:
        physx::PxRigidActor* mPxActor = nullptr;
        PhysicsScene* mScene = nullptr;
   
        CollisionCallBack mCollisionEnter = nullptr;
        CollisionCallBack mCollisionStay = nullptr;
        CollisionCallBack mCollisionExit = nullptr;

        struct ShapeDeleter
        {
            void operator()(PhysXShape* shape) const
            {
                PhysXShape::DestroyShape(shape);
            }
        };
        using ShapePtr = std::unique_ptr<PhysXShape, ShapeDeleter>;
        std::vector<ShapePtr> mShapes;
        Float3 mScale{1.0f, 1.0f, 1.0f};
        bool mIsTrigger = false;
        bool mIsKinematic = false;
        bool mOwnPxActor = true;
    };
}
