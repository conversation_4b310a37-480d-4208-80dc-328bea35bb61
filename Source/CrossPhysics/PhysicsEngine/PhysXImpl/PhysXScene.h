#pragma once
#include "PhysicsEngine/CrossPhysics.h"
#include <functional>
#include "PxPhysicsAPI.h"
#include "PhysicsEngine/PhysicsScene.h"
namespace cross {
class PhysXScene;
class PhysXActor;

class PhysXSimulationEventCallback : public physx::PxSimulationEventCallback
{
public:
    PhysXSimulationEventCallback(PhysXScene* scene)
        : mScene(scene)
    {}

public:
    void onConstraintBreak(physx::PxConstraintInfo* constraints, physx::PxU32 count) override {}
    void onWake(physx::PxActor** actors, physx::PxU32 count) override {}
    void onSleep(physx::PxActor** actors, physx::PxU32 count) override {}
    void onContact(const physx::PxContactPairHeader& pairHeader, const physx::PxContactPair* pairs, physx::PxU32 nbPairs) override;
    void onTrigger(physx::PxTriggerPair* pairs, physx::PxU32 count) override;
    void onAdvance(const physx::PxRigidBody* const* bodyBuffer, const physx::PxTransform* poseBuffer, const physx::PxU32 count) override {}

protected:
    PhysXScene* mScene;
};

struct PhysXDebugVisualization : public PhysicsDebugVisualization
{
    PhysXDebugVisualization(const physx::PxRenderBuffer& pxBuffer);

    const UInt32 GetNbPoints() override;
    const Point* GetPoints() const override;
    const UInt32 GetNbLines() override;
    const Line* GetLines() const override;
    const UInt32 GetNbTriangles() override;
    const Triangle* GetTriangles() const override;

    const physx::PxRenderBuffer& mBuffer;
};


class PhysXScene : public PhysicsScene
{
    PhysXScene(bool requireRWLock);
    ~PhysXScene() override;

public:
    static PhysXScene* Create(bool requireRWLock);
    static void Destroy(PhysXScene* scene);

    void AddActor(PhysicsActor* actor) override;
    void RemoveActor(PhysicsActor* actor) override;

    void TraverseActiveActors(const std::function<void(PhysicsActor*)>& functor) override;
    void TraverseAllActors(const std::function<void(PhysicsActor*)>& functor) override;

    void Simulate(float step) override;
    void FetchResult() override;
    void BeforeUpdate() override;

    UInt32 RayCast(const TRSVector3Type& origin, const Float3& unitDir, float maxDistance, CollisionMask mask, HitFlag flag = HitFlag::Default, UInt32 maxHit = 1, const PhysicsActor* self = nullptr,
                   PhysicsHitResult* outResults = nullptr) override;
    UInt32 Sweep(PhysicsGeometryBase* geometry, const TRSVector3Type& position, const TRSQuaternionType& rotation, const TRSVector3Type& scale, const Float3& unitDir, float maxDistance, CollisionMask mask, HitFlag flag = HitFlag::Default,
                 UInt32 maxHit = 1u,
                 const PhysicsActor* self = nullptr, PhysicsHitResult* outResults = nullptr) override;
    UInt32 Overlap(PhysicsGeometryBase* geometry, const TRSVector3Type& position, const TRSQuaternionType& rotation, const TRSVector3Type& scale, CollisionMask mask, HitFlag flag = HitFlag::Default, UInt32 maxHit = 1u,
                   const PhysicsActor* self = nullptr,
                   PhysicsHitResult* outResults = nullptr) override;

    void SetGravity(const Float3& gravity) override;


    PhysXSimulationEventCallback* GetEventCallback() { return &mEventCallback; }

    void SetDebugViewOption(const PhysicsSceneDebugViewOption& option) override;
    std::unique_ptr<PhysicsDebugVisualization> RefreshDebugVisualization() override;

    //PhysXSceneWriteLocker LockWriteWithScopeLocker();
    physx::PxScene* GetUnderlay() const { return mPxScene; }
    void SetUnderlay(physx::PxScene* scene);

    virtual void LockWrite() override;
    virtual void UnlockWrite() override;
    virtual void LockRead() override;
    virtual void UnlockRead() override;
    void ShiftOriginAssumeSceneLocked(const Double3& origin);
    //Internal use, don't touch these functions
public:
    void PushCollisionEnterCmd(PhysXActor* actor, std::shared_ptr<CollisionInfo>&& info);
    void PushCollisionStayCmd(PhysXActor* actor, std::shared_ptr<CollisionInfo>&& info);
    void PushCollisionExitCmd(PhysXActor* actor, std::shared_ptr<CollisionInfo>&& info);
    void ExecuteCollisionCmd();
    

protected:
    std::unique_ptr<physx::PxGeometry> GenerateSweepGeo(PhysicsGeometryBase* geometry, const Float3& scale);
protected:
    PhysXSimulationEventCallback mEventCallback{this};
    physx::PxScene* mPxScene = nullptr;
    physx::PxQueryCache mRayCastCache;
    physx::PxQueryCache mSweepCache;
    std::vector<std::pair<PhysXActor*, std::shared_ptr<CollisionInfo>>> mCollisionEnterCmds;   // It's not valid to write pxscene during callback. So delay the onCollision callbacks
    std::vector<std::pair<PhysXActor*, std::shared_ptr<CollisionInfo>>> mCollisionStayCmds;
    std::vector<std::pair<PhysXActor*, std::shared_ptr<CollisionInfo>>> mCollisionExitCmds;
    PhysicsSceneDebugViewOption mDebugViewOption;


    Double3 mOrigin = Double3::Zero();
};

class PhysXQueryFilterCallback : public physx::PxQueryFilterCallback
{
public:
    PhysXQueryFilterCallback(CollisionMask mask, bool hitBuffers, const PhysXActor* self);
    virtual ~PhysXQueryFilterCallback() = default;

    virtual physx::PxQueryHitType::Enum preFilter(const physx::PxFilterData& filterData, const physx::PxShape* shape, const physx::PxRigidActor* actor, physx::PxHitFlags& queryFlags) override;
    virtual physx::PxQueryHitType::Enum postFilter(const physx::PxFilterData& filterData, const physx::PxQueryHit& hit) override;

private:
    CollisionMask mMask;
    const PhysXActor* mSelf;
    /*Whether to discard hit eTouch hit type, false means discard eTouch*/
    bool mHitBuffers;
};
}   // namespace cross
