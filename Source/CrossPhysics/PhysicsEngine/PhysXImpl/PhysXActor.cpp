#include "EnginePrefix.h"
#include "PhysXActor.h"

#include "PhysicsEngine/PhysXImpl/PhysXMaterial.h"
#include "PhysicsEngine/PhysXImpl/PhysXEngineImpl.h"
#include "PhysicsEngine/PhysXImpl/PhysXUtility.h"
#include "PhysicsEngine/PhysXImpl/PhysXGeometry.h"



namespace cross
{
    constexpr int DEFAULT_SOLVER_ITERATIONS = 8;

    PhysXActor::PhysXActor(physx::PxRigidActor* pxActor)
        : mOwnPxActor(true)
    {
        pxActor->userData = this;
        mPxActor = pxActor;
    }

    PhysXActor::PhysXActor(physx::PxRigidActor* pxActor, bool ownPxActor)
        : mOwnPxActor(ownPxActor)
    {
        pxActor->userData = this;
        mPxActor = pxActor;
    }

    PhysXActor* PhysXActor::CreateRigidStatic(Float3A pos, QuaternionA rotation)
    {
        physx::PxTransform transform{ CrossVec3ToPxVec3(pos), CrossQuatToPxQuat(rotation)};
        if (!transform.q.isSane())
        {
            LOG_ERROR("Quaternion is not valid when creating a new PhysicsActor. I'll reset the quaternion to identity to avoid crashing.");
            Assert(false);
            transform.q = physx::PxQuat(physx::PxIdentity);
        }
        physx::PxRigidStatic* pxRigidStatic = PxGetPhysics().createRigidStatic(transform);
        Assert(pxRigidStatic);
        return new PhysXActor(pxRigidStatic);
    }

    PhysXActor* PhysXActor::CreateRigidDynamic(Float3A pos, QuaternionA rotation)
    {
        physx::PxTransform transform{CrossVec3ToPxVec3(pos), CrossQuatToPxQuat(rotation)};
        if (!transform.q.isSane())
        {
            LOG_ERROR("Quaternion is not valid when creating a new PhysicsActor. I'll reset the quaternion to identity to avoid crashing.");
            Assert(false);
            transform.q = physx::PxQuat(physx::PxIdentity);
        }
        physx::PxRigidDynamic* pxRigidDynamic = PxGetPhysics().createRigidDynamic(transform);
        Assert(pxRigidDynamic);
        pxRigidDynamic->setRigidBodyFlag(physx::PxRigidBodyFlag::eUSE_KINEMATIC_TARGET_FOR_SCENE_QUERIES, true);
        pxRigidDynamic->setSolverIterationCounts(DEFAULT_SOLVER_ITERATIONS);

        return new PhysXActor(pxRigidDynamic);
    }

    void PhysXActor::Destroy(PhysXActor* actor)
    {
        delete actor;
    }

    bool PhysXActor::IsDynamicActor() const
    {
        if (mPxActor)
        {
            return mPxActor->is<physx::PxRigidDynamic>() != nullptr;
        }
        return false;
    }
    void PhysXActor::SetScene(PhysicsScene* scene) {
        mScene = scene;
    }
    PhysXActor::~PhysXActor()
    {
        if (mOwnPxActor)
        {
            ClearAllShapes();
            if (mPxActor)
                mPxActor->release();
        }
        mScene = nullptr;
    }

    PhysicsShape* PhysXActor::AddTriangleMeshShape(const PhysicsGeometryMesh& meshGeo, PhysicsMaterial* material)
    {
        PhysXShape* shape = PhysXShape::CreateTriangleMeshShape(meshGeo, mScale, material);
        Assert(shape != nullptr);
        AddShape(shape);
        return shape;
    }

    PhysicsShape* PhysXActor::AddConvexMeshShape(const PhysicsGeometryConvex& convexGeo, PhysicsMaterial* material)
    {
        PhysXShape* shape = PhysXShape::CreateConvexMeshShape(convexGeo, mScale, material);
        Assert(shape != nullptr);
        AddShape(shape);
        return shape;
    }

    PhysicsShape* PhysXActor::AddBoxShape(const PhysicsGeometryBox& boxGeo, PhysicsMaterial* material)
    {
        PhysXShape* shape = PhysXShape::CreateBoxShape(boxGeo, mScale, material);
        Assert(shape != nullptr);
        AddShape(shape);
        return shape;
    }

    PhysicsShape* PhysXActor::AddSphereShape(const PhysicsGeometrySphere& sphereGeo, PhysicsMaterial* material)
    {
        PhysXShape* shape = PhysXShape::CreateSphereShape(sphereGeo, mScale, material);
        Assert(shape != nullptr);
        AddShape(shape);
        return shape;
    }

    PhysicsShape* PhysXActor::AddCapsuleShape(const PhysicsGeometryCapsule& capsuleGeo, PhysicsMaterial* material)
    {
        PhysXShape* shape = PhysXShape::CreateCapsuleShape(capsuleGeo, mScale, material);
        Assert(shape != nullptr);
        AddShape(shape);
        return shape;
    }

    PhysicsShape* PhysXActor::AddPlaneShape(const PhysicsGeometryPlane& planeGeo, PhysicsMaterial* material)
    {
        PhysXShape* shape = PhysXShape::CreatePlaneShape(planeGeo, mScale, material);
        Assert(shape != nullptr);
        AddShape(shape);
        return shape;
    }
    PhysicsShape* PhysXActor::AddTerrainShape(const PhysicsGeometryTerrain& terrainGeo, PhysicsMaterial* material)
    {
        auto levelScale = terrainGeo.levelScale;
        PhysXShape* shape = PhysXShape::CreateTerrainShape(terrainGeo, {mScale.x * levelScale,mScale.y,mScale.z*levelScale}, material);
        Assert(shape != nullptr);
        Float3 offset(float(terrainGeo.offsetX), 0.0f, float(terrainGeo.offsetZ));
        shape->SetLocalPos(offset * mScale, Quaternion::Identity());
        AddShape(shape);
        return shape;
    }
    void PhysXActor::ClearShape(PhysicsShape* shape)
    {
        PhysXShape* phyXShape = static_cast<PhysXShape*>(shape);
        auto it = std::find_if(mShapes.begin(), mShapes.end(), [phyXShape](const ShapePtr& ptr) {return ptr.get() == phyXShape; });
        Assert(it != mShapes.end());
        mPxActor->detachShape(*phyXShape->GetUnderlay());
        mShapes.erase(it);
    }

    size_t PhysXActor::GetShapeCount() const
    {
        Assert(mPxActor->getNbShapes() == mShapes.size());
        return mShapes.size();
    }

    PhysicsShape* PhysXActor::GetShape(size_t index)
    {
        Assert(mPxActor->getNbShapes() == mShapes.size());
        return mShapes[index].get();
    }

    const PhysicsShape* PhysXActor::GetShape(size_t index) const
    {
        Assert(mPxActor->getNbShapes() == mShapes.size());
        return mShapes[index].get();
    }

    void PhysXActor::SetOnlyUsedForSceneQuery(bool onlyForSceneQuery)
    {
        mPxActor->setActorFlag(physx::PxActorFlag::eDISABLE_SIMULATION, onlyForSceneQuery);
    }

    bool PhysXActor::GetOnlyUsedForSceneQuery() const
    {
        return mPxActor->getActorFlags() & physx::PxActorFlag::eDISABLE_SIMULATION;
    }

    void PhysXActor::ClearAllShapes()
    {
        Assert(mPxActor->getNbShapes() == mShapes.size());
        for (auto& ptr : mShapes)
        {
            mPxActor->detachShape(*ptr->GetUnderlay());
        }
        mShapes.clear();
        Assert(mPxActor->getNbShapes() == mShapes.size());
    }

    void PhysXActor::SetMass(float mass)
    {
        physx::PxRigidBody* body = mPxActor->is<physx::PxRigidBody>();
        Assert(body);
        body->setMass(mass);
    }

    float PhysXActor::GetMass() const
    {
        physx::PxRigidBody* body = mPxActor->is<physx::PxRigidBody>();
        Assert(body);
        return body->getMass();
    }
    
    Float3A PhysXActor::GetMassSpaceInertiaTensor() const
    {
        physx::PxRigidBody* body = mPxActor->is<physx::PxRigidBody>();
        Assert(body);
        return PxVec3ToCrossVec3(body->getMassSpaceInertiaTensor());
    }

    void PhysXActor::SetMassSpaceInertiaTensor(const Float3A& tensor)
    {
        physx::PxRigidBody* body = mPxActor->is<physx::PxRigidBody>();
        Assert(body);
        body->setMassSpaceInertiaTensor(CrossVec3ToPxVec3(tensor));
    }

    void PhysXActor::UpdateMassAndInertia(float density, const Float3A& massLocalPose, bool includeNonSimShapes)
    {
        physx::PxVec3 massLocal = CrossVec3ToPxVec3(massLocalPose);
        physx::PxRigidBody* body = mPxActor->is<physx::PxRigidBody>();
        Assert(body);
        physx::PxRigidBodyExt::updateMassAndInertia(*body, density, &massLocal, includeNonSimShapes);
    }

    void PhysXActor::SetMassAndUpdateInertia(float mass, const Float3A& massLocalPose, bool includeNonSimShapes)
    {
        physx::PxVec3 massLocal = CrossVec3ToPxVec3(massLocalPose);
        physx::PxRigidBody* body = mPxActor->is<physx::PxRigidBody>();
        Assert(body);
        physx::PxRigidBodyExt::setMassAndUpdateInertia(*body, mass, &massLocal, includeNonSimShapes);
    }

    void PhysXActor::SetRigidDynamicFlags(RigidDynamicFlag flags, bool value)
    {
        physx::PxRigidBody* body = mPxActor->is<physx::PxRigidBody>();
        Assert(body);
        body->setRigidBodyFlag(static_cast<physx::PxRigidBodyFlag::Enum>(flags), value);
    }

    void PhysXActor::SetDebugName(const char* name)
    {
        mPxActor->setName(name);
    }

    std::string PhysXActor::GetDebugName()
    {
        return mPxActor->getName();
    }

    void PhysXActor::SetTransform(const Float3& pos, const Quaternion& rotation)
    {
        mScene->LockWrite();
        SetTransformAssumeSceneLocked(pos, rotation);
        mScene->UnlockWrite();
    }

    void PhysXActor::SetTransformAssumeSceneLocked(const Float3& pos, const Quaternion& rotation)
    {
        physx::PxTransform trans(CrossVec3ToPxVec3(pos), CrossQuatToPxQuat(rotation));
        mPxActor->setGlobalPose(trans);
    }

    std::pair<Float3, Quaternion> PhysXActor::GetTransform() const
    {
        mScene->LockRead();
        physx::PxScene* pxScene = mPxActor->getScene();
        physx::PxTransform trans = mPxActor->getGlobalPose();
        mScene->UnlockRead();
        return { PxVec3ToCrossVec3(trans.p), PxQuatToCrossQuat(trans.q) };
    }

    void PhysXActor::SetScale(const Float3A& scale)
    {
        if(mScale != scale)
        {
            mScale = scale;
            for (auto& shape : mShapes)
            {
                auto [pos, rot] = shape->GetLocalPos();
                pos *= scale / shape->GetScale();
                shape->SetLocalPos(pos, rot);
                shape->SetScale(scale);
            }
        }
    }

    const Float3& PhysXActor::GetScale() const
    {
        return mScale;
    }

    void PhysXActor::AddForce(const Float3A& force)
    {
        physx::PxRigidBody* body = mPxActor->is<physx::PxRigidBody>();
        Assert(body);
        body->addForce(CrossVec3ToPxVec3(force), physx::PxForceMode::eFORCE);
    }

    void PhysXActor::AddImpulse(const Float3A& impulse)
    {
        physx::PxRigidBody* body = mPxActor->is<physx::PxRigidBody>();
        Assert(body);
        body->addForce(CrossVec3ToPxVec3(impulse), physx::PxForceMode::eIMPULSE);
    }

    void PhysXActor::AddLinearVelocity(const Float3A& velocity)
    {
        physx::PxRigidBody* body = mPxActor->is<physx::PxRigidBody>();
        Assert(body);
        body->addForce(CrossVec3ToPxVec3(velocity), physx::PxForceMode::eVELOCITY_CHANGE);
    }

    void PhysXActor::AddLinearAcceleration(const Float3A& acceleration)
    {
        physx::PxRigidBody* body = mPxActor->is<physx::PxRigidBody>();
        Assert(body);
        body->addForce(CrossVec3ToPxVec3(acceleration), physx::PxForceMode::eACCELERATION);
    }

    void PhysXActor::ClearForce()
    {
        physx::PxRigidBody* body = mPxActor->is<physx::PxRigidBody>();
        Assert(body);
        body->clearForce(physx::PxForceMode::eFORCE);
    }

    void PhysXActor::ClearImpulse()
    {
        physx::PxRigidBody* body = mPxActor->is<physx::PxRigidBody>();
        Assert(body);
        body->clearForce(physx::PxForceMode::eIMPULSE);
    }

    void PhysXActor::ClearLinearVelocity()
    {
        physx::PxRigidBody* body = mPxActor->is<physx::PxRigidBody>();
        Assert(body);
        body->clearForce(physx::PxForceMode::eVELOCITY_CHANGE);
        body->setLinearVelocity(physx::PxVec3{ 0, 0, 0 });
    }

    void PhysXActor::ClearLinearAcceleration()
    {
        physx::PxRigidBody* body = mPxActor->is<physx::PxRigidBody>();
        Assert(body);
        body->clearForce(physx::PxForceMode::eACCELERATION);
    }

    void PhysXActor::AddTorque(const Float3& torque)
    {
        physx::PxRigidBody* body = mPxActor->is<physx::PxRigidBody>();
        Assert(body);
        body->addTorque(CrossVec3ToPxVec3(torque), physx::PxForceMode::eFORCE);
    }
    void PhysXActor::ClearTorque()
    {
        physx::PxRigidBody* body = mPxActor->is<physx::PxRigidBody>();
        Assert(body);
        body->clearTorque(physx::PxForceMode::eFORCE);
    }
    void PhysXActor::AddImpulseTorque(const Float3& impulse)
    {
        physx::PxRigidBody* body = mPxActor->is<physx::PxRigidBody>();
        Assert(body);
        body->addTorque(CrossVec3ToPxVec3(impulse), physx::PxForceMode::eIMPULSE);
    }
    void PhysXActor::ClearImpulseTorque()
    {
        physx::PxRigidBody* body = mPxActor->is<physx::PxRigidBody>();
        Assert(body);
        body->clearTorque(physx::PxForceMode::eIMPULSE);
    }
    void PhysXActor::AddAngularVelocity(const Float3& velocity)
    {
        physx::PxRigidBody* body = mPxActor->is<physx::PxRigidBody>();
        Assert(body);
        body->addTorque(CrossVec3ToPxVec3(velocity), physx::PxForceMode::eVELOCITY_CHANGE);
    }
    void PhysXActor::ClearAngularVelocity()
    {
        physx::PxRigidBody* body = mPxActor->is<physx::PxRigidBody>();
        Assert(body);
        body->clearTorque(physx::PxForceMode::eVELOCITY_CHANGE);
        body->setAngularVelocity(physx::PxVec3{0, 0, 0});
    }
    void PhysXActor::AddAngularAcceleration(const Float3& acceleration)
    {
        physx::PxRigidBody* body = mPxActor->is<physx::PxRigidBody>();
        Assert(body);
        body->addTorque(CrossVec3ToPxVec3(acceleration), physx::PxForceMode::eACCELERATION);
    }
    void PhysXActor::ClearAngularAcceleration()
    {
        physx::PxRigidBody* body = mPxActor->is<physx::PxRigidBody>();
        Assert(body);
        body->clearTorque(physx::PxForceMode::eACCELERATION);
    }

    Float3 PhysXActor:: GetAngularVelocity() const
    {
        physx::PxRigidBody* body = mPxActor->is<physx::PxRigidBody>();
        Assert(body);
        return PxVec3ToCrossVec3(body->getAngularVelocity());
    }

    Float3 PhysXActor::GetLinearVelocity() const
    {
        physx::PxRigidBody* body = mPxActor->is<physx::PxRigidBody>();
        Assert(body);
        return PxVec3ToCrossVec3(body->getLinearVelocity());
    }

    void PhysXActor::SetCollisionEnter(CollisionCallBack callback, bool reportCollisionPoint)
    {
        mCollisionEnter = std::move(callback);
        //bool enable = mCollisionEnter != nullptr;

        for (auto& shape : mShapes)
        {
            shape->SetEnableReportCollision(reportCollisionPoint);
        }
    }
    void PhysXActor::SetCollisionStay(CollisionCallBack callback, bool reportCollisionPoint)
    {
        mCollisionStay = std::move(callback);
        // bool enable = mCollisionExit != nullptr;

        for (auto& shape : mShapes)
        {
            shape->SetEnableReportCollision(reportCollisionPoint);
        }
    }
    void PhysXActor::SetCollisionExit(CollisionCallBack callback, bool reportCollisionPoint)
    {
        mCollisionExit = std::move(callback);
        //bool enable = mCollisionExit != nullptr;

        for (auto& shape : mShapes)
        {
            shape->SetEnableReportCollision(reportCollisionPoint);
        }
    }

    void PhysXActor::SetEnableGravity(bool enable)
    {
        mPxActor->setActorFlag(physx::PxActorFlag::eDISABLE_GRAVITY, !enable);
    }

    float PhysXActor::GetLinearDamping() const
    {
        physx::PxRigidBody* body = mPxActor->is<physx::PxRigidBody>();
        Assert(body);
        return body->getLinearDamping();
    }

    void PhysXActor::SetLinearDamping(float linearDamping)
    {
        physx::PxRigidBody* body = mPxActor->is<physx::PxRigidBody>();
        Assert(body);
        body->setLinearDamping(linearDamping);
    }

    void PhysXActor::SetIsTrigger(bool isTrigger)
    {
        mIsTrigger = isTrigger;

         for (size_t i = 0; i < mShapes.size(); ++i)
        {
             mShapes[i]->SetIsTrigger(mIsTrigger);
        }
    }

    bool PhysXActor::GetIsTrigger() const
    {
        return mIsTrigger;
    }

    bool PhysXActor::GetIsKinematic() const
    {
        return mIsKinematic;
    }

    void PhysXActor::SetIsKinematic(bool isKinematic, bool wakeUp)
    {
        mIsKinematic = isKinematic;
        physx::PxRigidDynamic* body = mPxActor->is<physx::PxRigidDynamic>();
        Assert(body);
        body->setRigidBodyFlag(physx::PxRigidBodyFlag::eKINEMATIC, mIsKinematic);
        if (wakeUp && !isKinematic && body->getScene() != nullptr)
        {
            body->wakeUp();
        }
    }

    void PhysXActor::SetMaxDepenetrationVelocity(float velocity)
    {
        physx::PxRigidBody* body = mPxActor->is<physx::PxRigidBody>();
        Assert(body);
        body->setMaxDepenetrationVelocity(velocity);
    }

    float PhysXActor::GetMaxDepenetrationVelocity() const
    {
        physx::PxRigidBody* body = mPxActor->is<physx::PxRigidBody>();
        Assert(body);
        return body->getMaxDepenetrationVelocity();
    }

    void PhysXActor::PutToSleep()
    {
        physx::PxRigidDynamic* dyn = mPxActor->is<physx::PxRigidDynamic>();
        Assert(dyn);
        dyn->putToSleep();
    }

    void PhysXActor::SetKinematicTarget(const Float3A& position, const Quaternion& rotation)
    {
        physx::PxRigidDynamic* dyn = mPxActor->is<physx::PxRigidDynamic>();
        Assert(dyn);

        mScene->LockWrite();
        dyn->setKinematicTarget({CrossVec3ToPxVec3(position), CrossQuatToPxQuat(rotation)});
        mScene->UnlockWrite();
    }

    void PhysXActor::SetKinematicTargetAssumeSceneLocked(const Float3A& position, const Quaternion& rotation)
    {
        physx::PxRigidDynamic* dyn = mPxActor->is<physx::PxRigidDynamic>();
        Assert(dyn);
        dyn->setKinematicTarget({CrossVec3ToPxVec3(position), CrossQuatToPxQuat(rotation)});
    }

    void PhysXActor::AddShape(PhysXShape* shape)
    {
        if (shape)
        {
            shape->SetCollisionInfo(mCollisionType, mCollisionMask);
            shape->SetIsTrigger(mIsTrigger);
            shape->SetEnableReportCollision(mCollisionEnter != nullptr);
            mPxActor->attachShape(*shape->GetUnderlay());
            mShapes.emplace_back(shape);
        }
    }

    void PhysXActor::UpdateFilterData()
    {
        for (size_t i = 0; i < mShapes.size(); ++i)
        {
            mShapes[i]->SetCollisionInfo(mCollisionType, mCollisionMask);
        }
    }

    void PhysXActor::SetCollisionType(CollisionType type)
    {
        if (type != mCollisionType)
        {
            mCollisionType = type;
            UpdateFilterData();
        }
    }

    void PhysXActor::SetCollisionMask(CollisionMask mask)
    {
        if (mask != mCollisionMask)
        {
            mCollisionMask = mask;
            UpdateFilterData();
        }
    }
}   // namespace cross
