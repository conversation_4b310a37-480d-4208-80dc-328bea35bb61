#pragma once
#include "PhysicsEngine/CrossPhysics.h"
#include "Math/CrossMath.h"
#include "CECommon/Common/TRS.h"
#include "PxPhysicsAPI.h"
#include "Template/EnumClassFlags.h"
#include "PhysicsEngine/PhysXImpl/PhysXScene.h"
#include "PhysicsEngine/PhysicsShape.h"
#include "PhysicsEngine/PhysicsJoint.h"

namespace cross {
inline physx::PxVec3 CrossVec3ToPxVec3(Float3 vec)
{
    return {vec.x, vec.y, vec.z};
}

inline Float3 PxVec3ToCrossVec3(physx::PxVec3 vec)
{
    return {vec.x, vec.y, vec.z};
}

inline Double3 PxVec3ToCrossDouble3(physx::PxVec3 vec)
{
    return {vec.x, vec.y, vec.z};
}

inline physx::PxQuat CrossQuatToPxQuat(Quaternion quat)
{
    return {quat.x, quat.y, quat.z, quat.w};
}

inline Quaternion PxQuatToCrossQuat(physx::PxQuat quat)
{
    return {quat.x, quat.y, quat.z, quat.w};
}

inline physx::PxTransform CrossTransToPxTrans(const Transform& trans)
{
    return {CrossVec3ToPxVec3(trans.translation), CrossQuatToPxQuat(trans.rotation)};
}

inline Transform PxTransToCrossTrans(const physx::PxTransform& trans)
{
    return Transform{PxVec3ToCrossVec3(trans.p), Float3{1, 1, 1}, PxQuatToCrossQuat(trans.q)};
}

inline physx::PxHitFlags CrossHitFlagToPxHitFlag(HitFlag flag)
{
    physx::PxHitFlags ret{0};
    if (EnumHasAnyFlags(flag, HitFlag::Position))
        ret |= physx::PxHitFlag::ePOSITION;
    if (EnumHasAnyFlags(flag, HitFlag::Normal))
        ret |= physx::PxHitFlag::eNORMAL;
    if (EnumHasAnyFlags(flag, HitFlag::UV))
        ret |= physx::PxHitFlag::eUV;
    if (EnumHasAnyFlags(flag, HitFlag::AssumeNoInitialOverlap))
        ret |= physx::PxHitFlag::eASSUME_NO_INITIAL_OVERLAP;
    if (EnumHasAnyFlags(flag, HitFlag::MeshBothSides))
        ret |= physx::PxHitFlag::eMESH_BOTH_SIDES;
    if (EnumHasAnyFlags(flag, HitFlag::MTD))
        ret |= physx::PxHitFlag::eMTD;
    return ret;
}

inline HitFlag PxHitFlagToCrossHitFlag(physx::PxHitFlags flag)
{
    HitFlag ret{0};
    if (flag & physx::PxHitFlag::ePOSITION)
        EnumAddFlags(ret, HitFlag::Position);
    if (flag & physx::PxHitFlag::eNORMAL)
        EnumAddFlags(ret, HitFlag::Normal);
    if (flag & physx::PxHitFlag::eUV)
        EnumAddFlags(ret, HitFlag::UV);
    if (flag & physx::PxHitFlag::eASSUME_NO_INITIAL_OVERLAP)
        EnumAddFlags(ret, HitFlag::AssumeNoInitialOverlap);
    if (flag & physx::PxHitFlag::eMESH_BOTH_SIDES)
        EnumAddFlags(ret, HitFlag::MeshBothSides);
    if (flag & physx::PxHitFlag::eMTD)
        EnumAddFlags(ret, HitFlag::MTD);
    return ret;
}

inline UInt32 EncodeCollisionWord(CollisionType collisionType, CollisionMask blockMask)
{
    // |-- 16 --|-- 16 --|
    return static_cast<UInt32>(blockMask.Value()) << 16 | static_cast<UInt32>(collisionType);
}

inline std::tuple<CollisionType, CollisionMask> DecodeCollisionWord(UInt32 collisionWord)
{
    return { static_cast<CollisionType>(collisionWord & 0xffff), static_cast<CollisionMask>(collisionWord >> 16 & 0xffff)};
}

inline physx::PxD6Motion::Enum CrossMotionToPxMotion(JointMotionType motion)
{
    switch (motion)
    {
    case JointMotionType::Free:
        return physx::PxD6Motion::eFREE;
    case JointMotionType::Limited:
        return physx::PxD6Motion::eLIMITED;
    case JointMotionType::Locked:
        return physx::PxD6Motion::eLOCKED;
    }
    Assert(false);
    return {};
}
}
