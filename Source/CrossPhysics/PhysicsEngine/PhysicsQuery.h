#pragma once
#include "CrossPhysics.h"
#include "CECommon/Common/TRS.h"
#include "PhysicsEngine/PhysicsScene.h"

namespace cross {
// HitFlag should match the define in lua


class PhysicsScene;
class CrossPhysics_API PhysicsQuery
{
public:
    static const UInt32 RaycastMaxHits = 128;
    static const UInt32 SweepMaxHits = 128;
    static const UInt32 OverlapMaxHits = 128;

public:
    /* 
        Attention! 
        If use double transform, the contact "position" in PhysicsHitResult is the offset in camera tile. You can calculate the absolute position use "tile" in PhysicsHitResult.
     */

    /** Trace a ray against the world and return if a blocking hit is found **/
    bool RayCastAny(const TRSVector3Type& origin, const Float3& unitDir, float maxDistance, CollisionMask blockMask) const;
    /** Trace a ray against the world and return the closest blocking hit **/
    bool RayCast(const TRSVector3Type& origin, const Float3& unitDir, float maxDistance, CollisionMask blockMask, PhysicsHitResult& hitResult) const;
    /** Trace a ray against the world and return touching hits. Results are NOT sorted **/
    UInt32 RayCastMulti(const TRSVector3Type& origin, const Float3& unitDir, float maxDistance, CollisionMask blockMask, PhysicsHitResult* hitResults, UInt32 maxHit = RaycastMaxHits) const;

    /** Sweeping a supplied shape against the world and return if a blocking hit is found **/
    bool SweepAny(PhysicsGeometryBase& geo, const TRS& transform, const Float3& unitDir, float maxDistance, CollisionMask blockMask) const;
    /** Sweeping a supplied shape against the world and return the closest blocking hit **/
    bool Sweep(PhysicsGeometryBase& geo, const TRS& transform, const Float3& unitDir, float maxDistance, CollisionMask blockMask, PhysicsHitResult& hitResult) const;
    /** Sweeping a ray against the world and return touching hits. Results are NOT sorted **/
    bool SweepMulti(PhysicsGeometryBase& geo, const TRS& transform, const Float3& unitDir, float maxDistance, CollisionMask blockMask, std::vector<PhysicsHitResult>& hitResults, UInt32 maxHit = SweepMaxHits) const;

    /** Overlapping a supplied shape against the world and return if a blocking hit is found **/
    bool OverlapAny(PhysicsGeometryBase& geo, const TRS& transform, CollisionMask blockMask) const;
    /** Overlapping a supplied shape against return touching hits. Results are NOT sorted  **/
    bool OverlapMulti(PhysicsGeometryBase& geo, const TRS& transform, CollisionMask blockMask, std::vector<PhysicsHitResult>& hitResults, UInt32 maxHit = OverlapMaxHits) const;

private:
    PhysicsQuery(PhysicsScene* scene);
    void SetPhysicsScene(PhysicsScene* scene);

private:
    PhysicsScene* mScene;
    friend class PhysicsSystemG;
};
}   // namespace cross
