#pragma once
#include "CrossPhysics.h"
#include "PhysicsActor.h"
#include "CrossBase/Template/EnumClassFlags.h"

// #include "PhysicsScene.h"

namespace physx {
class PxCapsuleController;
class PxControllerManager;
class PxCapsuleControllerDesc;
class PxControllerFilters;
class PxShape;
}   // namespace physx

namespace cross {
using PhysicsCapsuleControllerDesc = physx::PxCapsuleControllerDesc;

class PhysicsScene;

enum class PhysicsControllerCollisionFlag
{
    None = 0,
    Sides = (1 << 0),   //!< Character is colliding to the sides.
    Up = (1 << 1),      //!< Character has collision above.
    Down = (1 << 2)     //!< Character has collision below.
};

ENUM_CLASS_FLAGS(PhysicsControllerCollisionFlag)

class CrossPhysics_API PhysicsCapsuleController
{
private:
    PhysicsCapsuleController(physx::PxCapsuleController* controller);
public:
    ~PhysicsCapsuleController();

    PhysicsControllerCollisionFlag Move(const Float3& disp, float minDist, float elapsedTime, const physx::PxControllerFilters& filters);
    void SetPosition(const Double3& position);
    Double3 GetPosition() const;
    Double3 GetFootPosition() const;

    void SetRadius(float radius);
    float GetRadius() const;
    void SetHalfHeight(float height);
    float GetHalfHeight() const;

    void SetUp(const Float3& up);

    PhysicsActor* GetActor();
    void SetCollisionInfo(CollisionType collisionType, CollisionMask mask);
    physx::PxShape* GetPxShape();

private:
    PhysicsActor* mActor = nullptr;
    physx::PxCapsuleController* mPxController = nullptr;

    friend class PhysicsControllerManager;
};

class CrossPhysics_API PhysicsControllerManager
{
public:
    PhysicsControllerManager(PhysicsScene* scene);
    ~PhysicsControllerManager();
    PhysicsCapsuleController* CreateCapsuleController(PhysicsCapsuleControllerDesc& desc);

private:
    physx::PxControllerManager* mPxManager;
};
}   // namespace cross