#include "EnginePrefix.h"

#include "PhysicsQuery.h"

#include "PhysicsEngine/PhysXImpl/PhysXUtility.h"
#include "PhysicsEngine/PhysXImpl/PhysXActor.h"
#include "PhysicsEngine/PhysXImpl/PhysXGeometry.h"
#include "PhysicsEngine/PhysXImpl/PhysXEngineImpl.h"
#include "PhysicsEngine/PhysXImpl/PhysXCpuDispatcher.h"

namespace cross {

template<typename RayCastResult>
    requires std::is_same_v<RayCastResult, PhysicsHitResult>
static void ConvertPxHitResultToCross(const physx::PxRaycastHit& pxHit, RayCastResult& outHit, const Double3& origin)
{
    outHit.flags = PxHitFlagToCrossHitFlag(pxHit.flags);
    if constexpr (std::is_same_v<RayCastResult, PhysicsHitResult>)
        outHit.position = Float3A(PxVec3ToCrossDouble3(pxHit.position) + origin);
    else
        outHit.position = PxVec3ToCrossDouble3(pxHit.position) + origin;
    outHit.uv = Float2{pxHit.u, pxHit.v};
    outHit.normal = PxVec3ToCrossVec3(pxHit.normal);
    outHit.distance = pxHit.distance;
    outHit.hitActor = static_cast<PhysXActor*>(pxHit.actor->userData);
    outHit.hitShape = static_cast<PhysXShape*>(pxHit.shape->userData);
}

template<typename RayCastResult>
    requires std::is_same_v<RayCastResult, PhysicsHitResult>
static void ConvertPxHitResultToCross(const physx::PxSweepHit& pxHit, RayCastResult& outHit, const Double3& origin)
{
    outHit.flags = PxHitFlagToCrossHitFlag(pxHit.flags);
    if constexpr (std::is_same_v<RayCastResult, PhysicsHitResult>)
        outHit.position = Float3A(PxVec3ToCrossDouble3(pxHit.position) + origin);
    else
        outHit.position = PxVec3ToCrossDouble3(pxHit.position) + origin;
    outHit.normal = PxVec3ToCrossVec3(pxHit.normal);
    outHit.distance = pxHit.distance;
    outHit.hitActor = static_cast<PhysXActor*>(pxHit.actor->userData);
    outHit.hitShape = static_cast<PhysXShape*>(pxHit.shape->userData);
}

template<typename RayCastResult>
    requires std::is_same_v<RayCastResult, PhysicsHitResult>
static void ConvertPxHitResultToCross(const physx::PxOverlapHit& pxHit, RayCastResult& outHit)
{
    outHit.hitActor = static_cast<PhysXActor*>(pxHit.actor->userData);
    outHit.hitShape = static_cast<PhysXShape*>(pxHit.shape->userData);
}

static std::unique_ptr<physx::PxGeometry> GeneratePhysXGeo(const PhysicsGeometryBase* geometry, const Float3& scale)
{
    std::unique_ptr<physx::PxGeometry> pxGeo = nullptr;

    switch (geometry->GetGeometryType())
    {
    case PhysicsGeometryBase::Type::Box:
    {
        auto* box = static_cast<const PhysicsGeometryBox*>(geometry);
        pxGeo = std::make_unique<physx::PxBoxGeometry>(CrossVec3ToPxVec3(box->halfExtents * scale));
        break;
    }
    case PhysicsGeometryBase::Type::Sphere:
    {
        auto* sphere = static_cast<const PhysicsGeometrySphere*>(geometry);
        Assert(FloatEqual(scale.x, scale.y, 0.00001f) && FloatEqual(scale.y, scale.z, 0.00001f));
        pxGeo = std::make_unique<physx::PxSphereGeometry>(sphere->radius * scale.x);
        break;
    }
    case PhysicsGeometryBase::Type::Capsule:
    {
        auto* capsule = static_cast<const PhysicsGeometryCapsule*>(geometry);
        Assert(FloatEqual(scale.z, scale.x, 0.00001f));
        pxGeo = std::make_unique<physx::PxCapsuleGeometry>(capsule->radius * scale.x, capsule->halfHeight * scale.y);
        break;
    }
    case PhysicsGeometryBase::Type::Convex:
    {
        auto* convex = static_cast<const PhysicsGeometryConvex*>(geometry);
        PhysXConvexMesh* mesh = static_cast<PhysXConvexMesh*>(convex->mesh.get());
        pxGeo = std::make_unique<physx::PxConvexMeshGeometry>(mesh->GetUnderlay(), physx::PxMeshScale{CrossVec3ToPxVec3(scale)});
        break;
    }
    case PhysicsGeometryBase::Type::Mesh:
    case PhysicsGeometryBase::Type::Plane:
    default:
        Assert(false);
    }
    return pxGeo;
}

static UInt32 RaycastInternal(PhysicsScene* scene, const TRSVector3Type& origin, const Float3& unitDir, float maxDistance, CollisionMask blockMask, PhysicsHitResult* hitResult, HitFlag flags = HitFlag::Default, UInt32 maxHit = 1u,
                              const PhysicsActor* self = nullptr)
{
    UInt32 result = scene->RayCast(origin, unitDir, maxDistance, blockMask, flags, maxHit, self, hitResult);
    return result;
}

static bool SweepInternal(PhysicsScene* scene, PhysicsGeometryBase& geometry, const TRSVector3Type& origin, const TRSQuaternionType& rotation, const TRSVector3Type& scale, const Float3& unitDir, float maxDistance, CollisionMask blockMask,
                          PhysicsHitResult* hitResult, HitFlag flags = HitFlag::Default, UInt32 maxHit = 1u, const PhysicsActor* self = nullptr)
{
    bool result = scene->Sweep(&geometry, origin, rotation, scale, unitDir, maxDistance, blockMask, flags, maxHit, self, hitResult);

    return result;
}

static bool OverlapInternal(PhysicsScene* scene, PhysicsGeometryBase& geometry, const TRSVector3Type& origin, const TRSQuaternionType& rotation, const TRSVector3Type& scale, CollisionMask blockMask, PhysicsHitResult* hitResult,
                            HitFlag flags = HitFlag::Default,
                            UInt32 maxHit = 1u, const PhysicsActor* self = nullptr)
{
    bool result = scene->Overlap(&geometry, origin, rotation, scale, blockMask, flags, maxHit, self, hitResult);

    return result;
}

PhysicsQuery::PhysicsQuery(PhysicsScene* scene)
    : mScene(scene)
{}

void PhysicsQuery::SetPhysicsScene(PhysicsScene* scene)
{
    mScene = scene;
}



bool PhysicsQuery::RayCastAny(const TRSVector3Type& origin, const Float3& unitDir, float maxDistance, CollisionMask blockMask) const
{
    bool hasAnyHit = RaycastInternal(mScene, origin, unitDir, maxDistance, blockMask, nullptr, HitFlag::None);
    return hasAnyHit;
}

bool PhysicsQuery::RayCast(const TRSVector3Type& origin, const Float3& unitDir, float maxDistance, CollisionMask blockMask, PhysicsHitResult& hitResult) const
{
    UInt32 hasAnyHit = RaycastInternal(mScene, origin, unitDir, maxDistance, blockMask, &hitResult);
    return hasAnyHit > 0;
}

UInt32 PhysicsQuery::RayCastMulti(const TRSVector3Type& origin, const Float3& unitDir, float maxDistance, CollisionMask blockMask, PhysicsHitResult* hitResults, UInt32 maxHit) const
{
    UInt32 hitCount = RaycastInternal(mScene, origin, unitDir, maxDistance, blockMask, hitResults, HitFlag::Default, maxHit);

    return hitCount;
}

bool PhysicsQuery::SweepAny(PhysicsGeometryBase& geo, const TRS& transform, const Float3& unitDir, float maxDistance, CollisionMask blockMask) const
{
    bool hasAnyHit = SweepInternal(mScene, geo, transform.mTranslation, transform.mRotation, transform.mScale, unitDir, maxDistance, blockMask, nullptr, HitFlag::None);
    return hasAnyHit;
}

bool PhysicsQuery::Sweep(PhysicsGeometryBase& geo, const TRS& transform, const Float3& unitDir, float maxDistance, CollisionMask blockMask, PhysicsHitResult& hitResult) const
{
    bool hasAnyHit = SweepInternal(mScene, geo, transform.mTranslation, transform.mRotation, transform.mScale, unitDir, maxDistance, blockMask, &hitResult);

    return hasAnyHit;
}

bool PhysicsQuery::SweepMulti(PhysicsGeometryBase& geo, const TRS& transform, const Float3& unitDir, float maxDistance, CollisionMask blockMask, std::vector<PhysicsHitResult>& hitResults, UInt32 maxHit) const
{
    hitResults.resize(1);
    hitResults.reserve(maxHit); 

    bool hasAnyHit = SweepInternal(mScene, geo, transform.mTranslation, transform.mRotation, transform.mScale, unitDir, maxDistance, blockMask, &(hitResults[0]), HitFlag::Default, maxHit);
    if (!hasAnyHit)
        hitResults.clear();
    return hasAnyHit;
}

bool PhysicsQuery::OverlapAny(PhysicsGeometryBase& geo, const TRS& transform, CollisionMask blockMask) const
{
    bool hasAnyHit = OverlapInternal(mScene, geo, transform.mTranslation, transform.mRotation, transform.mScale, blockMask, nullptr,HitFlag::None);

    return hasAnyHit;
}

bool PhysicsQuery::OverlapMulti(PhysicsGeometryBase& geo, const TRS& transform, CollisionMask blockMask, std::vector<PhysicsHitResult>& hitResults, UInt32 maxHit) const
{
    hitResults.resize(1);
    hitResults.reserve(maxHit); 

    bool hasAnyHit = OverlapInternal(mScene, geo, transform.mTranslation, transform.mRotation, transform.mScale, blockMask, &(hitResults[0]),HitFlag::Default,maxHit);
    if (!hasAnyHit)
        hitResults.clear();
    return hasAnyHit;
}
}   // namespace cross
