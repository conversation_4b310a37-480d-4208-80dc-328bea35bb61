#pragma once
#include "EnginePrefix.h"
#include "Math/CrossMath.h"

#if CROSSENGINE_WIN && defined(PHYSICS_ENGINE_DYNAMIC)
#ifdef CrossPhysics_EXPORTS
#define CrossPhysics_API __declspec(dllexport)
#else
#define CrossPhysics_API __declspec(dllimport)
#endif
#else
#ifdef __GNUC__
#define CrossPhysics_API __attribute__((visibility("default")))
#else
#define CrossPhysics_API
#endif
#endif

DECLARE_CPU_TIMING_GROUP(GroupPhysX);