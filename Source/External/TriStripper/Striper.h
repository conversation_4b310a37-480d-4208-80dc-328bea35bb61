#ifndef STRIPER_H
#define STRIPER_H
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// Source code for "Creating Efficient Triangle Strips"
// (C) 2000, <PERSON> (<EMAIL>)
//
// Version is 2.0.
//
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

#include "StriperIncludes.h"

class CustomArray;
namespace TriStripper { class Adjacencies; }

	///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
	//
	//																Class Striper
	//
	///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

	struct STRIPERCREATE{
				STRIPERCREATE()
				{
					DFaces				= nullptr;
					WFaces				= nullptr;
					NbFaces				= 0;
				}
				UInt32					NbFaces;			// #faces in source topo
				UInt32*					DFaces;				// list of faces (dwords) or null
				UInt16*					WFaces;				// list of faces (words) or null
	};

	struct STRIPERRESULT{
				UInt32					NbStrips;			// #strips created
				UInt32*					StripLengths;		// Lengths of the strips (NbStrips values)
				UInt32*					StripRuns;			// The strips
	};

	// cache for reducing allocated&released memory size
	struct BestStripCache
	{
		struct Cache
		{
			std::vector<UInt32> strip;
			std::vector<UInt32> faces;			
		};

		Cache caches[3];

		// this should be std::vector<bool>, but std::vector<bool> doesn't work as other vector templates
		std::vector<char> tags;
	};

	class CROSS_BASE_API Striper
	{
	private:
				Striper&				FreeUsedRam();
				UInt32					ComputeBestStrip(UInt32 face, BestStripCache& cache);
				UInt32					TrackStrip(UInt32 face, UInt32 oldest, UInt32 middle, UInt32* strip, UInt32* faces, bool* tags, bool reversedEdgeDirection);
				bool					ConnectAllStrips(STRIPERRESULT& result);

				TriStripper::Adjacencies*		mAdj;				// Adjacency structures
				bool*					mTags;				// Face markers

				UInt32					mNbStrips;			// The number of strips created for the mesh
				CustomArray*			mStripLengths;		// Array to store strip lengths
				CustomArray*			mStripRuns;			// Array to store strip indices

				UInt32					mTotalLength;		// The length of the single strip
				CustomArray*			mSingleStrip;		// Array to store the single strip

	public:
				Striper();
				~Striper();

				bool					Init(STRIPERCREATE& create);
				bool					Compute(STRIPERRESULT& result);
	};

#endif
