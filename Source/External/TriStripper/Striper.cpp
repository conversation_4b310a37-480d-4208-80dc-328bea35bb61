///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// Source code for "Creating Efficient Triangle Strips"
// (C) 2000, <PERSON> (<EMAIL>)
//
// Version is 2.0.
//
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
#include "PCH/CrossBasePCHPrivate.h"
#include "Striper.h"
#include "StriperIncludes.h"
#include "Adjacency.h"
#include "CustomArray.h"
#include "RevisitedRadix.h"

using namespace TriStripper;

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
//																	Striper Class Implementation
//
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// Constructor
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
Striper::Striper() : mAdj(nullptr), mTags(nullptr), mStripLengths(nullptr), mStripRuns(nullptr), mSingleStrip(nullptr)
{
}

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// Destructor
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
Striper::~Striper()
{
	FreeUsedRam();
}

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// A method to free possibly used ram
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// Input	:	-
// Output	:	-
// Return	:	Self-reference
// Exception:	-
// Remark	:	-
Striper& Striper::FreeUsedRam()
{
	SAFE_DELETE(mSingleStrip);
	SAFE_DELETE(mStripRuns);
	SAFE_DELETE(mStripLengths);
	RELEASEARRAY(mTags);
	SAFE_DELETE(mAdj);
	return *this;
}

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// A method to initialize the striper
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// Input	:	create,		the creation structure
// Output	:	-
// Return	:	true if success
// Exception:	-
// Remark	:	-
bool Striper::Init(STRIPERCREATE& create)
{
	// Release possibly already used ram
	FreeUsedRam();

	// Create adjacencies
	{
		mAdj = new Adjacencies;
		if(!mAdj)	return false;

		bool Status = mAdj->Init(create.WFaces, create.DFaces, create.NbFaces);
		if(!Status)	{ SAFE_DELETE(mAdj); return false; }
	}

	return true;
}

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// A method to create the triangle strips
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// Input	:	result,		the result structure
// Output	:	-
// Return	:	true if success
// Exception:	-
// Remark	:	-
bool Striper::Compute(STRIPERRESULT& result)
{
	// You must call Init() first
	if(!mAdj)	return false;

	// Get some bytes
	mStripLengths			= new CustomArray;				if(!mStripLengths)	return false;
	mStripRuns				= new CustomArray;				if(!mStripRuns)		return false;
	mTags					= new bool[mAdj->mNbFaces];		if(!mTags)			return false;
	UInt32* Connectivity	= new UInt32[mAdj->mNbFaces];	if(!Connectivity)	return false;

	// mTags contains one bool/face. True=>the face has already been included in a strip
	ZeroMemory_(mTags, mAdj->mNbFaces*sizeof(bool));

	// Compute the number of connections for each face. This buffer is further recycled into
	// the insertion order, ie contains face indices in the order we should treat them
	ZeroMemory_(Connectivity, mAdj->mNbFaces*sizeof(UInt32));
	{
		// Compute number of adjacent triangles for each face
		for(UInt32 i=0;i<mAdj->mNbFaces;i++)
		{
			AdjTriangle* Tri = &mAdj->mFaces[i];
			if(!IS_BOUNDARY(Tri->ATri[0]))	Connectivity[i]++;
			if(!IS_BOUNDARY(Tri->ATri[1]))	Connectivity[i]++;
			if(!IS_BOUNDARY(Tri->ATri[2]))	Connectivity[i]++;
		}

		// Sort by number of neighbors
		RadixSorter RS;
		UInt32* Sorted = RS.Sort(Connectivity, mAdj->mNbFaces).GetIndices();

		// The sorted indices become the order of insertion in the strips
		CopyMemory_(Connectivity, Sorted, mAdj->mNbFaces*sizeof(UInt32));
	}

	mNbStrips			= 0;	// #strips created
	UInt32 TotalNbFaces	= 0;	// #faces already transformed into strips
	UInt32 Index		= 0;	// Index of first face

	BestStripCache cache;

	while(TotalNbFaces!=mAdj->mNbFaces)
	{
		// Look for the first face [could be optimized]
		while(mTags[Connectivity[Index]])	Index++;
		UInt32 FirstFace = Connectivity[Index];

		// Compute the three possible strips from this face and take the best
		TotalNbFaces += ComputeBestStrip(FirstFace, cache);

		// Let's wrap
		mNbStrips++;
	}

	// Free now useless ram
	RELEASEARRAY(Connectivity);
	RELEASEARRAY(mTags);

	// Fill result structure and exit
	result.NbStrips		= mNbStrips;
	result.StripLengths	= (UInt32*)	mStripLengths	->Collapse();
	result.StripRuns	= (UInt32*) mStripRuns		->Collapse();

	ConnectAllStrips(result);

	return true;
}

template <typename T>
T* GetDataPtr(std::vector<T>& data) 
{
	return data.empty() ? 0 : &data.front();
}

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// A method to compute the three possible strips starting from a given face
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// Input	:	face,		the first face
// Output	:	-
// Return	:	UInt32,		the #faces included in the strip
// Exception:	-
// Remark	:	mStripLengths and mStripRuns are filled with strip data
UInt32 Striper::ComputeBestStrip(UInt32 face, BestStripCache& bestStripCache)
{ 
	UInt32* Strip[3];		// Strips computed in the 3 possible directions
	UInt32* Faces[3];		// Faces involved in the 3 previous strips
	UInt32 Length[3];		// Lengths of the 3 previous strips

	UInt32 FirstLength[3];	// Lengths of the first parts of the strips are saved for culling

	// Starting references
	UInt32 Refs0[3];
	UInt32 Refs1[3];
	Refs0[0] = mAdj->mFaces[face].VRef[0];
	Refs1[0] = mAdj->mFaces[face].VRef[1];

	// Bugfix by Eric Malafeew!
	Refs0[1] = mAdj->mFaces[face].VRef[2];
	Refs1[1] = mAdj->mFaces[face].VRef[0];

	Refs0[2] = mAdj->mFaces[face].VRef[1];
	Refs1[2] = mAdj->mFaces[face].VRef[2];

	// Compute 3 strips
	for(UInt32 j=0;j<3;j++)
	{
		BestStripCache::Cache& cache = bestStripCache.caches[j];

		// Get some bytes for the strip and its faces
		cache.strip.resize(mAdj->mNbFaces+2+1+2); // max possible length is NbFaces+2, 1 more if the first index gets replicated
		Strip[j] = GetDataPtr(cache.strip);
		cache.faces.resize(mAdj->mNbFaces+2);
		Faces[j] = GetDataPtr(cache.faces);
		FillMemory_(Strip[j], (mAdj->mNbFaces+2+1+2)*sizeof(UInt32), 0xff);
		FillMemory_(Faces[j], (mAdj->mNbFaces+2)*sizeof(UInt32), 0xff);

		// Create a local copy of the tags
		bestStripCache.tags.resize(mAdj->mNbFaces);
		bool* Tags = reinterpret_cast<bool*>(GetDataPtr(bestStripCache.tags));
		CopyMemory_(Tags, mTags, mAdj->mNbFaces*sizeof(bool));

		// Track first part of the strip
		Length[j] = TrackStrip(face, Refs0[j], Refs1[j], &Strip[j][0], &Faces[j][0], Tags, false);

		// Save first length for culling
		FirstLength[j] = Length[j];
//		if(j==1)	FirstLength[j]++;	// ...because the first face is written in reverse order for j==1

		// Reverse first part of the strip
		for(UInt32 i=0;i<Length[j]/2;i++)
		{
			Strip[j][i]				^= Strip[j][Length[j]-i-1];
			Strip[j][Length[j]-i-1]	^= Strip[j][i];
			Strip[j][i]				^= Strip[j][Length[j]-i-1];
		}
		for(UInt32 i=0;i<(Length[j]-2)/2;i++)
		{
			Faces[j][i]				^= Faces[j][Length[j]-i-3];
			Faces[j][Length[j]-i-3]	^= Faces[j][i];
			Faces[j][i]				^= Faces[j][Length[j]-i-3];
		}

		// Track second part of the strip
		UInt32 NewRef0 = Strip[j][Length[j]-3];
		UInt32 NewRef1 = Strip[j][Length[j]-2];
		// This edge has reverse order, so we pass true to last argument
		UInt32 ExtraLength = TrackStrip(face, NewRef0, NewRef1, &Strip[j][Length[j]-3], &Faces[j][Length[j]-3], Tags, true);
		Length[j]+=ExtraLength-3;
	}

	// Look for the best strip among the three
	UInt32 Longest	= Length[0];
	UInt32 Best		= 0;
	if(Length[1] > Longest)	{	Longest = Length[1];	Best = 1;	}
	if(Length[2] > Longest)	{	Longest = Length[2];	Best = 2;	}

	UInt32 NbFaces = Longest-2;

	// Update global tags
	for(UInt32 j=0;j<Longest-2;j++)	mTags[Faces[Best][j]] = true;

	// Flip strip if needed ("if the length of the first part of the strip is odd, the strip must be reversed")
	if(FirstLength[Best]&1)
	{
		// Here the strip must be flipped. I hardcoded a special case for triangles and quads.
		if(Longest==3 || Longest==4)
		{
			// Flip isolated triangle or quad
			Strip[Best][1] ^= Strip[Best][2];
			Strip[Best][2] ^= Strip[Best][1];
			Strip[Best][1] ^= Strip[Best][2];
		}
		else
		{
			// "to reverse the strip, write it in reverse order"
			for(UInt32 j=0;j<Longest/2;j++)
			{
				Strip[Best][j]				^= Strip[Best][Longest-j-1];
				Strip[Best][Longest-j-1]	^= Strip[Best][j];
				Strip[Best][j]				^= Strip[Best][Longest-j-1];
			}

			// "If the position of the original face in this new reversed strip is odd, you're done"
			UInt32 NewPos = Longest-FirstLength[Best];
			if(NewPos&1)
			{
				// "Else replicate the first index"
				for(UInt32 j=0;j<Longest;j++)	Strip[Best][Longest-j] = Strip[Best][Longest-j-1];
				Longest++;
			}
		}
	}

	// Copy best strip in the strip buffers
	for(UInt32 j=0;j<Longest;j++)
	{
		UInt32 Ref = Strip[Best][j];
		mStripRuns->StoreU32(Ref);
	}
	mStripLengths->StoreU32(Longest);

	// Returns #faces involved in the strip
	return NbFaces;
}

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// A method to extend a strip in a given direction, starting from a given face
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// Input	:	face,				the starting face
//				oldest, middle,		the two first indices of the strip == a starting edge == a direction
//				reversedEdgeDirection direction of first edge (pass true if the order of oldest and middle indices is reversed compated to the order on triangle)
// Output	:	strip,				a buffer to store the strip
//				faces,				a buffer to store the faces of the strip
//				tags,				a buffer to mark the visited faces
// Return	:	UInt32,				the strip length
// Exception:	-
// Remark	:	-
UInt32 Striper::TrackStrip(UInt32 face, UInt32 oldest, UInt32 middle, UInt32* strip, UInt32* faces, bool* tags, bool reversedEdgeDirection)
{
	UInt32 Length = 2;														// Initial length is 2 since we have 2 indices in input
	strip[0] = oldest;														// First index of the strip
	strip[1] = middle;														// Second index of the strip

	bool DoTheStrip = true;
	while(DoTheStrip)
	{
		// We need to invert it, because order of indices is alternating between CCW and CW
		reversedEdgeDirection = !reversedEdgeDirection;

		UInt32 Newest = mAdj->mFaces[face].OppositeVertex(oldest, middle);	// Get the third index of a face given two of them
		strip[Length++] = Newest;											// Extend the strip,...
		*faces++ = face;													// ...keep track of the face,...
		tags[face] = true;													// ...and mark it as "done".

		ubyte CurEdge = mAdj->mFaces[face].FindEdge(middle, Newest);		// Get the edge ID...

		UInt32 Link = mAdj->mFaces[face].ATri[CurEdge];						// ...and use it to catch the link to adjacent face.
		if(IS_BOUNDARY(Link))	DoTheStrip = false;							// If the face is no more connected, we're done...
		else
		{
			face = MAKE_ADJ_TRI(Link);										// ...else the link gives us the new face index.
			// we try to find an edge with appropriate direction on next triangle
			// this guarantees that we're not adding triangles with reversed vertex order to the strip
			const ubyte edge = mAdj->mFaces[face].FindEdgeDirectional(middle, Newest, reversedEdgeDirection);
			if (tags[face] || edge == 0xff)	DoTheStrip=false;				// Is the new face already done or egge with approriate direction doesn't exist?
		}
		oldest = middle;													// Shift the indices and wrap
		middle = Newest;
	}
	return Length;
}

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// A method to link all strips in a single one.
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// Input	:	result,		the result structure
// Output	:	the result structure is updated
// Return	:	true if success
// Exception:	-
// Remark	:	-
bool Striper::ConnectAllStrips(STRIPERRESULT& result)
{
	mSingleStrip = new CustomArray;
	if(!mSingleStrip) return false;

	//ANALYSIS_ASSUME(result.StripRuns);

	mTotalLength	= 0;
	UInt32* drefs	= result.StripRuns;

	// Loop over strips and link them together
	for(UInt32 k=0;k<result.NbStrips;k++)
	{
		// Nothing to do for the first strip, we just copy it
		if(k)
		{
			// This is not the first strip, so we must copy two void vertices between the linked strips
			UInt32 LastRef	= drefs[-1];
			UInt32 FirstRef	= drefs[0];
			mSingleStrip->StoreU32(LastRef).StoreU32(FirstRef);
			mTotalLength += 2;

			// Linking two strips may flip their culling. We must fix that
			// Culling has been inverted only if mTotalLength is odd
			if(mTotalLength&1)
			{
				// We can fix culling by replicating the first vertex once again...
				UInt32 SecondRef = drefs[1];
				if(FirstRef!=SecondRef)
				{
					mSingleStrip->StoreU32(FirstRef);
					mTotalLength++;
				}
				else
				{
					// ...but if flipped strip already begin with a replicated vertex, we just can skip it.
					result.StripLengths[k]--;
					drefs++;
				}
			}
		}

		// Copy strip
		for(UInt32 j=0;j<result.StripLengths[k];j++)
		{
			UInt32 Ref = drefs[j];
			mSingleStrip->StoreU32(Ref);
		}
		drefs += result.StripLengths[k];
		mTotalLength += result.StripLengths[k];
	}

	// Update result structure
	result.NbStrips		= 1;
	result.StripRuns	= (UInt32*)mSingleStrip->Collapse();
	result.StripLengths	= &mTotalLength;

	return true;
}
