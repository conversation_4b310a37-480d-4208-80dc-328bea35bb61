
#include "EnginePrefix.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "CECommon/Allocator/FrameAllocator.h"
#include "RenderEngine/RenderWorld.h"
#include "RenderEngine/CameraSystemR.h"
#include "RenderEngine/PrimitiveRenderSystemR.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderEngine/PrimitiveGenerator.h"
#include "RenderEngine/RenderPropertySystemR.h"
#include "RenderEngine/VisibilitySystemR.h"
#include "RenderEngine/RenderNodeSystemR.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RenderWindowR.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/RenderFactory.h"
#include "RenderEngine/AABBSystemR.h"
#include "RenderEngine/WindowSystemR.h"
#include "Resource/Material.h"
#include "CrossUI/Font.h"

namespace cross {
PrimitiveRenderSystemR::PrimitiveRenderSystemR()
{
    mRenderMesh.reset(new MeshR(nullptr));
}

PrimitiveRenderSystemR::~PrimitiveRenderSystemR() {}

void PrimitiveRenderSystemR::PreparaScreenTextData(ScreenTextData & textdata)
{
    QUICK_SCOPED_CPU_TIMING("ScreenTextBatch");
    auto& char_origin = textdata.char_origin;
    auto& content = textdata.content;
    auto& color = textdata.color;
    auto& fontsize = textdata.fontsize;
    FontInfo fontInfo = fontSet.at(textdata.fontType);
    auto font = fontInfo.font;
    auto sdf_font = font->mSDFFont;
    
    font->SetSize(fontsize);
    auto ratio = font->mRatio;
    //auto ratio = 1.0f;
    float font_map_width = static_cast<float>(sdf_font->mTextureWidth);
    float font_map_height = static_cast<float>(sdf_font->mTextureHeight);

    MaterialR* material = TYPE_CAST(MaterialR*, fontInfo.textMaterial->GetRenderMaterial());

    PrimitiveData data;
    std::vector<UnicodeChar> _unicode;
    {
        // get font info

        // convert unicode char buffer to UnicodeChar vector
        size_t text_length = content.size();
        if (_unicode.size() < text_length)
        {
            _unicode.resize(text_length);
        }
        int size;
        ConvertUTF8toUTF16(content.c_str(), static_cast<int>(text_length), _unicode.data(), size);

        // computer vertex data
        UInt16 vertex_count = static_cast<UInt16>(size) * 4;
        UInt16 index_count = static_cast<UInt16>(size) * 6;

        VertexStreamLayout&& layout = PrimitiveGenerator::GetStandardTriangleLayout();
        data.Resize(vertex_count, index_count, index_count / 3, layout, false);
        data.SetTopology(PrimitiveTopology::TriangleList);

        UInt32 offset = 0;
        UInt32 stride = layout.GetVertexStride();
        auto add_vertex = [vertex_data = data.GetVertexData(), &offset, stride](Float3* position, Float2* uv) {
            memcpy(static_cast<void*>(vertex_data + offset), position, 12);
            memcpy(static_cast<void*>(vertex_data + offset + 24), uv, 8);
            offset += stride;
        };

        UInt16 index_offset = 0;
        auto add_triangle = [index_data = data.GetIndexArray(), &index_offset](UInt16 a, UInt16 b, UInt16 c) {
            index_data[index_offset] = a;
            index_data[index_offset + 1] = b;
            index_data[index_offset + 2] = c;
            index_offset += 3;
        };

        Float2 origin = char_origin;
        Float2 char_min, char_max, char_offset, char_size;
        Float2 uv0, uv1, uv2, uv3;
        Float3 p0, p1, p2, p3;
        float origin_z = 0.f;
        int previous_id = -1;
        for (int i = 0; i < size; ++i)
        {
            int char_index = static_cast<int>(_unicode[i]);
            auto char_node = sdf_font->GetChar(char_index);

            float kerning = static_cast<float>(sdf_font->ComputeKerning(previous_id, char_node.id));

            char_offset = {(static_cast<float>(char_node.x_offset) + kerning) * ratio, static_cast<float>(char_node.y_offset) * ratio};
            char_size = {static_cast<float>(char_node.width) * ratio, static_cast<float>(char_node.height) * ratio};

            char_min.x = static_cast<float>(char_node.x) / font_map_width;
            char_min.y = static_cast<float>(char_node.y) / font_map_height;
            char_max.x = char_min.x + static_cast<float>(char_node.width) / font_map_width;
            char_max.y = char_min.y + static_cast<float>(char_node.height) / font_map_height;

            p0.x = (origin.x + char_offset.x);
            p0.y = (origin.y + char_size.y + char_offset.y);
            p0.z = origin_z;

            uv0.x = char_min.x;
            uv0.y = char_max.y;

            p1.x = (origin.x + char_size.x + char_offset.x);
            p1.y = (origin.y + char_size.y + char_offset.y);
            p1.z = origin_z;

            uv1.x = char_max.x;
            uv1.y = char_max.y;

            p2.x = (origin.x + char_size.x + char_offset.x);
            p2.y = (origin.y + char_offset.y);
            p2.z = origin_z;

            uv2.x = char_max.x;
            uv2.y = char_min.y;

            p3.x = (origin.x + char_offset.x);
            p3.y = (origin.y + char_offset.y);
            p3.z = origin_z;

            uv3.x = char_min.x;
            uv3.y = char_min.y;

            origin.x += char_node.x_advance * ratio;

            add_vertex(&p0, &uv0);
            add_vertex(&p1, &uv1);
            add_vertex(&p2, &uv2);
            add_vertex(&p3, &uv3);

            UInt16 vertex_offset = static_cast<UInt16>(i * 4);
            add_triangle(vertex_offset, vertex_offset + 2, vertex_offset + 1);
            add_triangle(vertex_offset, vertex_offset + 3, vertex_offset + 2);
        }
    }

    if (data.GetPrimitiveCount() == 0)
        return;

    // Copy index
    UInt32 indexDataSizeInByte = data.GetIndexCount() * (data.IsIndex32() ? sizeof(UInt32) : sizeof(UInt16));
    auto indexDataCopy = mCurFrameAllocator->CreateFrameContainer<FixedSizeFrameDataLot>(FRAME_STAGE_GAME_RENDER, indexDataSizeInByte);
    indexDataCopy->PushBack(reinterpret_cast<UInt8*>(data.GetIndexArray()), indexDataSizeInByte);

    // Copy and modify vertex
    auto& layout = data.GetVertexLayout();
    UInt32 vertexDataSizeInByte = data.GetVertexCount() * layout.GetVertexStride();

    auto vertexDataCopy = mCurFrameAllocator->CreateFrameContainer<FixedSizeFrameDataLot>(FRAME_STAGE_GAME_RENDER, vertexDataSizeInByte);
    vertexDataCopy->PushBack(data.GetVertexData(), vertexDataSizeInByte);

    auto topology = data.GetPrimitiveTopology();
    auto primitiveCount = data.GetPrimitiveCount();
    auto isIndex32 = data.IsIndex32();
    BatchPrimitive(vertexDataCopy, indexDataCopy, layout, topology, primitiveCount, isIndex32, material, {0.0f, 0.0f, 0.0f}, Float4x4A::Identity(), color);
}
void PrimitiveRenderSystemR::PreparaSceneTextData(SceneTextData& textdata)
{
    QUICK_SCOPED_CPU_TIMING("SceneTextBatch");
    auto& char_origin = textdata.char_offset;
    Float3 char_tile = textdata.char_tile;
    auto& content = textdata.content;
    auto& color = textdata.color;
    FontInfo fontInfo = fontSet.at(textdata.fontType);
    auto sdf_font = fontInfo.font->mSDFFont;
    Float2 ratio = textdata.scale;
    float font_map_width = static_cast<float>(sdf_font->mTextureWidth);
    float font_map_height = static_cast<float>(sdf_font->mTextureHeight);

    MaterialR* material = TYPE_CAST(MaterialR*, fontInfo.textMaterial->GetRenderMaterial());

    PrimitiveData data;
    std::vector<UnicodeChar> _unicode;
    {
        // get font info

        // convert unicode char buffer to UnicodeChar vector
        size_t text_length = content.size();
        if (_unicode.size() < text_length)
        {
            _unicode.resize(text_length);
        }
        int size;
        ConvertUTF8toUTF16(content.c_str(), static_cast<int>(text_length), _unicode.data(), size);

        // computer vertex data
        UInt16 vertex_count = static_cast<UInt16>(size) * 4;
        UInt16 index_count = static_cast<UInt16>(size) * 6;

        VertexStreamLayout&& layout = PrimitiveGenerator::GetStandardTriangleLayout();
        data.Resize(vertex_count, index_count, index_count / 3, layout, false);
        data.SetTopology(PrimitiveTopology::TriangleList);

        UInt32 offset = 0;
        UInt32 stride = layout.GetVertexStride();
        auto add_vertex = [vertex_data = data.GetVertexData(), &offset, stride](Float3* position, Float2* uv, Float3* tile) {
            memcpy(static_cast<void*>(vertex_data + offset), position, 12);
            memcpy(static_cast<void*>(vertex_data + offset + 12), tile, 12);
            memcpy(static_cast<void*>(vertex_data + offset + 24), uv, 8);            
            offset += stride;
        };

        UInt16 index_offset = 0;
        auto add_triangle = [index_data = data.GetIndexArray(), &index_offset](UInt16 a, UInt16 b, UInt16 c) {
            index_data[index_offset] = a;
            index_data[index_offset + 1] = b;
            index_data[index_offset + 2] = c;
            index_offset += 3;
        };

        Float3 origin = char_origin;
        Float2 char_min, char_max, char_offset, char_size;
        Float2 uv0, uv1, uv2, uv3;
        Float3 p0, p1, p2, p3;

        int previous_id = -1;
        for (int i = 0; i < size; ++i)
        {
            int char_id = static_cast<int>(_unicode[i]);
            auto char_node = sdf_font->GetChar(char_id);

            float kerning = static_cast<float>(sdf_font->ComputeKerning(previous_id, char_node.id));
            previous_id = char_node.id;
            char_offset = {(static_cast<float>(char_node.x_offset) + kerning) * ratio.x, static_cast<float>(char_node.y_offset) * ratio.y};
            char_size = {static_cast<float>(char_node.width) * ratio.x, static_cast<float>(char_node.height) * ratio.y};

            char_min.x = static_cast<float>(char_node.x) / font_map_width;
            char_min.y = static_cast<float>(char_node.y) / font_map_height;
            char_max.x = char_min.x + static_cast<float>(char_node.width) / font_map_width;
            char_max.y = char_min.y + static_cast<float>(char_node.height) / font_map_height;
            origin += char_offset.x * textdata.bitangent;

            p0 = origin;
            p0 += char_offset.y * textdata.tangent;

            uv0.x = char_min.x;
            uv0.y = char_min.y;

            p1 = origin + char_size.x * textdata.bitangent;
            p1 += char_offset.y * textdata.tangent;

            uv1.x = char_max.x;
            uv1.y = char_min.y;

            p2 = origin + char_size.x * textdata.bitangent;
            p2 += (char_size.y + char_offset.y) * textdata.tangent;

            uv2.x = char_max.x;
            uv2.y = char_max.y;
            
            p3 = origin;
            p3 += (char_size.y + char_offset.y) * textdata.tangent;

            uv3.x = char_min.x;
            uv3.y = char_max.y;

            origin += char_node.x_advance * ratio.x * textdata.bitangent;

            add_vertex(&p0, &uv0, &char_tile);
            add_vertex(&p1, &uv1, &char_tile);
            add_vertex(&p2, &uv2, &char_tile);
            add_vertex(&p3, &uv3, &char_tile);

            UInt16 vertex_offset = static_cast<UInt16>(i * 4);

            add_triangle(vertex_offset + 1, vertex_offset + 2, vertex_offset);
            add_triangle(vertex_offset + 2, vertex_offset + 3, vertex_offset);
        }
    }

    if (data.GetPrimitiveCount() == 0)
        return;

    // Copy index
    UInt32 indexDataSizeInByte = data.GetIndexCount() * (data.IsIndex32() ? sizeof(UInt32) : sizeof(UInt16));
    auto indexDataCopy = mCurFrameAllocator->CreateFrameContainer<FixedSizeFrameDataLot>(FRAME_STAGE_GAME_RENDER, indexDataSizeInByte);
    indexDataCopy->PushBack(reinterpret_cast<UInt8*>(data.GetIndexArray()), indexDataSizeInByte);

    // Copy and modify vertex
    auto& layout = data.GetVertexLayout();
    UInt32 vertexDataSizeInByte = data.GetVertexCount() * layout.GetVertexStride();

    auto vertexDataCopy = mCurFrameAllocator->CreateFrameContainer<FixedSizeFrameDataLot>(FRAME_STAGE_GAME_RENDER, vertexDataSizeInByte);
    vertexDataCopy->PushBack(data.GetVertexData(), vertexDataSizeInByte);

    auto topology = data.GetPrimitiveTopology();
    auto primitiveCount = data.GetPrimitiveCount();
    auto isIndex32 = data.IsIndex32();
    BatchPrimitive(vertexDataCopy, indexDataCopy, layout, topology, primitiveCount, isIndex32, material, {0.0f, 0.0f, 0.0f}, Float4x4A::Identity(), color);
}
PrimitiveRenderSystemR* PrimitiveRenderSystemR::CreateInstance()
{
    PrimitiveRenderSystemR* system = new PrimitiveRenderSystemR();
    return system;
}

void PrimitiveRenderSystemR::Release()
{
    delete this;
}

template <class T>
void copy_and_swap(std::pmr::vector<T>& data, std::pmr::polymorphic_allocator<T >& alloc)
{
    auto newTempData = std::move(std::pmr::vector<T>(alloc));
    newTempData.insert(newTempData.begin(), data.begin(), data.end());
    data = newTempData;
}

void PrimitiveRenderSystemR::OnBeginFrame(FrameParam* frameParam)
{
    QUICK_SCOPED_CPU_TIMING("PrimitiveBeginFrame");
    mCurFrameAllocator = frameParam->GetFrameAllocator();
    mPrimitiveDataBatches = mCurFrameAllocator->CreateFrameContainer<FrameVector<PrimitiveDataBatch>>(FRAME_STAGE_RENDER, 4);
    for (const auto& batch : mPrimitiveDataLaterUpdateSubmitted)
    {
        BatchPrimitive(batch.first, batch.second, false);
    }
    mPrimitiveDataLaterUpdateSubmitted.clear();
    auto pool = std::move(FrameAllocatorPool(mCurFrameAllocator, FRAME_STAGE_RENDER));
#if _WIN32    
    // need copy and swap so that previous call in begin frame can still remain
    mTextAlloc = std::make_unique<std::pmr::polymorphic_allocator<ScreenTextData>>(&(pool));
    copy_and_swap(mTextDatas, *mTextAlloc);
#endif
}

void PrimitiveRenderSystemR::OnEndFrame(FrameParam* frameParam)
{

    mTextDatas.clear();
    mSceneTextDatas.clear();

    mCurFrameAllocator = nullptr;
    mPrimitiveDataBatches = nullptr;

    mPoints.clear();
}

void PrimitiveRenderSystemR::BatchPrimitive(FixedSizeFrameDataLot* vertexData, FixedSizeFrameDataLot* indexData, VertexStreamLayout layout, PrimitiveTopology topology, UInt32 primitiveCount, bool isIndex32, MaterialR* renderMaterial,
                                            const Float3A& tilePosition, const Float4x4A& worldTransformMatrix, const ColorRGBAf& color)
{
    if (!mPrimitiveDataBatches && !mCurFrameAllocator)
    {
        mCurFrameAllocator = EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameAllocator();
        mPrimitiveDataBatches = mCurFrameAllocator->CreateFrameContainer<FrameVector<PrimitiveDataBatch>>(FRAME_STAGE_RENDER, 4);
    }
    PrimitiveData* tPrimitive = reinterpret_cast<PrimitiveData*>(mCurFrameAllocator->Allocate(sizeof(PrimitiveData), FRAME_STAGE_RENDER));
    if (isIndex32)
    {
        new (tPrimitive) PrimitiveData(vertexData->GetData(), (UInt32*)indexData->GetData(), vertexData->GetSize() / layout.GetVertexStride(), indexData->GetSize() / sizeof(UInt32), primitiveCount, layout, topology, color);
    }
    else
    {
        new (tPrimitive) PrimitiveData(vertexData->GetData(), (UInt16*)indexData->GetData(), vertexData->GetSize() / layout.GetVertexStride(), indexData->GetSize() / sizeof(UInt16), primitiveCount, layout, topology, color);
    }
    BatchPrimitive(tPrimitive, renderMaterial, false, false, tilePosition, worldTransformMatrix);
}

void PrimitiveRenderSystemR::BatchPrimitive(PrimitiveData* primitive, MaterialR* renderMaterial, bool isDataValidBetweenFrames, bool isLaterUpdate, Float3A const& tilePosition, const Float4x4A& worldTransformMatrix)
{
    Assert(primitive->HasData());
    std::lock_guard<std::mutex> lock(mMtx);
    if (isLaterUpdate)
    {
        mPrimitiveDataLaterUpdateSubmitted.push_back({primitive, renderMaterial});
        return;
    }

    PrimitiveDataBatch* properBatch = nullptr;
    bool needCopyPrimitive = false;
    // Get proper batch
    {
        if (!primitive->IsIndex32())   // 32-bit-index primitives do not join batch
        {
            for (UInt32 i = 0; i < mPrimitiveDataBatches->GetSize(); ++i)
            {
                auto& batch = mPrimitiveDataBatches->At(i);
                if (batch.mIndexCount + primitive->GetIndexCount() < 65535   // keep 16-bit index after batching
                    && batch.mVertexCount + primitive->GetVertexCount() < 65535 && batch.mPrimitiveTopology == primitive->GetPrimitiveTopology() && batch.mRenderMaterial == renderMaterial && batch.mColor.Equals(primitive->GetColor()) &&
                    batch.mVertexLayout.GetHash() == primitive->GetVertexLayout().GetHash() && batch.mWorldMatrix == Float4x4A::Identity() && batch.mTilePosition == Float3{0.0f, 0.0f, 0.0f})
                {
                    properBatch = &batch;
                    if (isDataValidBetweenFrames)
                        needCopyPrimitive = true;
                    break;
                }
            }
        }
        if (!properBatch)
        {
            mPrimitiveDataBatches->EmplaceBack();
            properBatch = &(mPrimitiveDataBatches->Back());
            properBatch->InitializeData(mCurFrameAllocator, mBatchInstanceCountReference);
            properBatch->mRenderMaterial = renderMaterial;
            properBatch->mColor = primitive->GetColor();
            properBatch->mPrimitiveTopology = primitive->GetPrimitiveTopology();
            properBatch->mVertexLayout = primitive->GetVertexLayout();
            properBatch->mTilePosition = tilePosition;
            properBatch->mWorldMatrix = worldTransformMatrix;
        }
    }
    // Add data to batch
    {
        properBatch->mVertexCount += primitive->GetVertexCount();
        properBatch->mIndexCount += primitive->GetIndexCount();
        properBatch->mPrimitiveCount += primitive->GetPrimitiveCount();

        UInt32 indexSizeInByte = primitive->GetIndexCount() * UInt32(primitive->IsIndex32() ? sizeof(UInt32) : sizeof(UInt16));
        if (needCopyPrimitive)
        {
            FixedSizeFrameDataLot* copyIndexData = mCurFrameAllocator->CreateFrameContainer<FixedSizeFrameDataLot>(FRAME_STAGE_RENDER, indexSizeInByte);
            copyIndexData->PushBack(reinterpret_cast<const UInt8*>(primitive->GetIndexArray()), indexSizeInByte);
            properBatch->mIndexData->EmplaceBack(reinterpret_cast<UInt16*>(copyIndexData->GetData()), indexSizeInByte);
        }
        else
        {
            properBatch->mIndexData->EmplaceBack(primitive->GetIndexArray(), indexSizeInByte);
        }

        UInt32 vertexSizeInByte = primitive->GetVertexCount() * primitive->GetVertexLayout().GetVertexStride();
        properBatch->mVertexData->EmplaceBack(primitive->GetVertexData(), vertexSizeInByte);
    }
}

void PrimitiveRenderSystemR::BatchPoint(Float3 const& position, float scale, ColorRGBAf const& color, MaterialR* renderMaterial, Float3 const& tilePosition, bool isScreenSize)
{
    std::lock_guard<std::mutex> lock(mPointsMtx);
    mPoints.emplace_back(color, position, scale, renderMaterial, tilePosition, isScreenSize);
}

void PrimitiveRenderSystemR::RenderBatches(FrameVector<PrimitiveDataBatch>* batches)
{
    while (mGeoPacks.size() < batches->GetSize())
    {
        mGeoPacks.emplace_back(RenderFactory::Instance().CreateGeometryPacket());
    }

    auto renderNodeSystem = mRenderWorld->GetRenderSystem<RenderNodeSystemR>();
    auto renderSystem = EngineGlobal::Inst().GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    const UInt16 batchCount = static_cast<UInt16>(batches->GetSize());

    // Prepare geoPack
    for (UInt32 i = 0; i < batchCount; ++i)
    {
        auto& batch = batches->At(i);
        auto geoPack = mGeoPacks[i];
        geoPack->Clear();

        UInt32 vertexSizeInByte = batch.mVertexCount * batch.mVertexLayout.GetVertexStride();
        auto vertexBufferNGIWrap = renderSystem->GetScratchBuffer()->AllocateScratch(NGIBufferUsage::VertexBuffer, vertexSizeInByte);
        size_t vertexDataNGIOffset = 0;
        UInt32 instanceCount = batch.mVertexData->GetSize();
        mBatchInstanceCountReference = std::max<UInt32>(instanceCount / 2, mBatchInstanceCountReference);
        auto* toAddIndex = mCurFrameAllocator->CreateFrameContainer<FrameArray<UInt16>>(FRAME_STAGE_RENDER, instanceCount);
        UInt16 lastVertexCount = 0;   // 32-bit-index primitives do not join batch
        for (UInt32 j = 0; j < instanceCount; ++j)
        {
            auto [data, size] = batch.mVertexData->At(j);
            vertexBufferNGIWrap.MemWrite(vertexDataNGIOffset, data, size);
            vertexDataNGIOffset = vertexDataNGIOffset + size;

            toAddIndex->EmplaceBack(j == 0 ? (UInt16)0 : (UInt16)(toAddIndex->At(j - 1) + lastVertexCount));
            lastVertexCount = UInt16(size / batch.mVertexLayout.GetVertexStride());
        }

        UInt32 indexSizeInByte = instanceCount > 1 ? batch.mIndexCount * sizeof(UInt16) : batch.mIndexData->At(0).second;   // 32-bit-index primitives do not join batch
        auto indexBufferNGIWrap = renderSystem->GetScratchBuffer()->AllocateScratch(NGIBufferUsage::IndexBuffer, indexSizeInByte);
        size_t indexBufferNGIWrapOffset = 0;
        for (UInt32 k = 0; k < instanceCount; ++k)
        {
            auto [toWriteIndexData, size] = batch.mIndexData->At(k);
            const UInt16 startIndex = toAddIndex->At(k);
            if (k > 0)
            {
                UInt16 indexCount = static_cast<UInt16>(size / sizeof(UInt16));   // 32-bit-index primitives do not join batch
                for (UInt16 ii = 0; ii < indexCount; ++ii)
                {
                    toWriteIndexData[ii] += startIndex;
                }
            }
            indexBufferNGIWrap.MemWrite(indexBufferNGIWrapOffset, toWriteIndexData, size);
            indexBufferNGIWrapOffset = indexBufferNGIWrapOffset + size;
        }

        geoPack->AddVertexStream(vertexBufferNGIWrap.GetNGIBuffer(), vertexSizeInByte, UInt32(vertexBufferNGIWrap.GetNGIOffset()), batch.mVertexLayout);
        geoPack->SetIndexStream(indexBufferNGIWrap.GetNGIBuffer(), indexSizeInByte, batch.mIndexCount, UInt32(indexBufferNGIWrap.GetNGIOffset()));
    }

    // Set geometry list
    mRenderMesh->ClearAndResize(batchCount);
    auto renderNodeH = mRenderWorld->GetComponent<RenderNodeComponentR>(mRenderEntity);
    //mRenderNode = std::make_shared<EditorPrimitiveRenderNode>();
    auto renderNode = mRenderNode.get();
    renderNode->mDrawData.clear();
    //renderNode->mDrawData.resize(batchCount);
    for (UInt32 i = 0; i < batchCount; ++i)
    {
        auto batch = batches->At(i);
        mRenderMesh->GetRenderGeometry(i).SetData(mGeoPacks[i].get(), batch.mVertexCount, 0, batch.mIndexCount, 0, batch.mPrimitiveCount, batch.mPrimitiveTopology);

        EditorPrimitiveRenderNode::DrawData drawData
        {&(mRenderMesh->GetRenderGeometry(i)),
            batch.mRenderMaterial,
            nullptr
        };
        drawData.mProperties.SetProperty(NAME_ID("Color"), Float4{batch.mColor.r, batch.mColor.g, batch.mColor.b, batch.mColor.a});
        drawData.mProperties.SetProperty(BuiltInProperty::ce_World, batch.mWorldMatrix);
#if defined(CE_USE_DOUBLE_TRANSFORM)
        drawData.mProperties.SetProperty(NAME_ID("CE_USE_DOUBLE_TRANSFORM"), true);
        drawData.mProperties.SetProperty(BuiltInProperty::ce_TilePosition, batch.mTilePosition);
#endif

        renderNode->mDrawData.push_back(std::move(drawData));
    }

    for (auto const& point : mPoints)
    {
        EditorPrimitiveRenderNode::DrawData drawData
        {
            &pointGeometry,
            point.mRenderMaterial,
            nullptr
        };
        TRSRenderMatrixType worldTransformationType;
        drawData.mProperties.SetProperty(NAME_ID("COLOR_FROM_CONST_BUFFER"), true);
        drawData.mProperties.SetProperty(NAME_ID("Color"), Float4{point.mColor.r, point.mColor.g, point.mColor.b, point.mColor.a});
        
        Float4x4 worldMatrix
        {
            point.mScale,      0.0f,               0.0f,              0.0f,
            0.0f,              point.mScale,       0.0f,              0.0f,
            0.0f,              0.0f,               point.mScale,      0.0f,
            point.mPosition.x, point.mPosition.y,  point.mPosition.z, 1.0f,
        };
        drawData.mProperties.SetProperty(BuiltInProperty::ce_World, worldMatrix);
#if defined(CE_USE_DOUBLE_TRANSFORM)
        drawData.mProperties.SetProperty(NAME_ID("CE_USE_DOUBLE_TRANSFORM"), true);
        drawData.mProperties.SetProperty(BuiltInProperty::ce_TilePosition, point.mTilePosition);
#endif
        drawData.mProperties.SetProperty(NAME_ID("IS_SCREEN_SIZE"), true);
        renderNode->mDrawData.push_back(std::move(drawData));
    }

    renderNodeH.Write()->SetRenderNode(mRenderNode);

    //renderNodeSystem->SetRenderMeshes(renderNodeH, tRenderMeshes);
}

void PrimitiveRenderSystemR::OnBuildUpdateTasks(FrameParam* frameParam)
{
    auto prepatask = CreateTaskFunction(FrameTickStage::Update, {}, [this] {
        for (auto& data : mTextDatas)
        {
            PreparaScreenTextData(data);
        }
        for (auto& data : mSceneTextDatas) {
            PreparaSceneTextData(data);
        }
    });
    CreateTaskFunction<threading::ThreadID::RenderingThreadLocal>(FrameTickStage::Update, {prepatask}, [this] {
        SCOPED_CPU_TIMING(GroupRendering, "PrimitiveRenderUpdateR");
        RenderBatches(mPrimitiveDataBatches);
    });
}

void PrimitiveRenderSystemR::NotifyEvent(const SystemEventBase& event, UInt32& flag)
{
    RenderSystemBase::NotifyEvent(event, flag);

    if (event.mEventType == OnSystemAddToRenderWorldEvent::sEventType)
    {
        mRenderEntity = mRenderWorld->CreateEntityID();
        mRenderWorld->CreateComponents<TransformComponentR, TilePositionComponentR, AABBComponentR, RenderNodeComponentR, RenderPropertyComponentR>(mRenderEntity);
    }
}

void PrimitiveRenderSystemR::OnFirstUpdate(FrameParam* frameParam)
{
    auto* renderPropertySystem = TYPE_CAST(RenderPropertySystemR*, mRenderWorld->GetRenderSystem<RenderPropertySystemR>());
    renderPropertySystem->SetCullingProperty(mRenderEntity, CullingProperty::CULLING_PROPERTY_ALWAYS_VISIBLE);

    auto preparePointGeometry = [&]{
        pointGeometryPacket = RenderFactory::Instance().CreateGeometryPacket();

        auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        auto* renderPrimitives = rendererSystem->GetRenderPrimitives();

        pointGeometryPacket->Clear();
        auto vb = renderPrimitives->GetUnitSphere();
        auto ib = renderPrimitives->GetUnitSphereIndex();
        auto ibCount = renderPrimitives->GetUnitSphereIndexCount();

        VertexStreamLayout layout{};
        layout.AddVertexChannelLayout({VertexChannel::Position0, VertexFormat::Float3, 0});
        layout.AddVertexChannelLayout({VertexChannel::Color0, VertexFormat::Float2, 12}); // hack for sphere uv data

        pointGeometryPacket->AddVertexStream(vb, static_cast<UInt32>(vb->GetDesc().Size), 0, layout);
        pointGeometryPacket->SetIndexStream(ib, static_cast<UInt32>(ib->GetDesc().Size), ibCount);
        pointGeometry.SetData(pointGeometryPacket.get(), 42, 0, ibCount, 0, ibCount / 3, PrimitiveTopology::TriangleList);
    };
    preparePointGeometry();
}

void PrimitiveDataBatch::InitializeData(FrameAllocator* curFrameAllocator, UInt32 capacity)
{
    mVertexData = curFrameAllocator->CreateFrameContainer<FrameVector<std::pair<UInt8*, UInt32>>>(FRAME_STAGE_RENDER, capacity);
    mIndexData = curFrameAllocator->CreateFrameContainer<FrameVector<std::pair<UInt16*, UInt32>>>(FRAME_STAGE_RENDER, capacity);
}

void PrimitiveRenderSystemR::DrawScreenText(const Float2& char_origin, std::string content, const ColorRGBAf& color, UInt32 fontsize, const std::string fontType)
{
    if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() != AppStartUpTypeThumbnailProcessor)
    {
        mTextDatas.emplace_back(ScreenTextData{char_origin, content, fontType, color, fontsize});
    }
}
void PrimitiveRenderSystemR::DrawSceneText(const SceneTextData& textData)
{
    if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() != AppStartUpTypeThumbnailProcessor)
    {
        mSceneTextDatas.emplace_back(textData);
    }
}
void PrimitiveRenderSystemR::PrepareTextMap(std::string fontType, FontInfo fontInfo) {
    if (fontSet.find(fontType) != fontSet.end())
    {
        return;
    }

    fontSet.emplace(fontType, fontInfo);
}
//FontInfo PrimitiveRenderSystemR::GetFontInfo(const std::string& fontType) {
//    if (fontSet.find(fontType) == fontSet.end())
//    {
//        return fontSet.at("Default");
//    }
//    return fontSet.at(fontType);
//}
//std::unordered_map<std::string, FontInfo>* PrimitiveRenderSystemR::GetFontSet()
//{
//    return &fontSet;
//}
//void PrimitiveRenderSystemR::SetFontInfo(std::string fontType, FontInfo fontInfo) {
//    if (fontSet.find(fontType)!=fontSet.end())
//    {
//        return;
//    }
//    fontSet.emplace(fontType, fontInfo);
//}
}   // namespace cross
