#include "EnginePrefix.h"
#include "RenderEngine/InstancedStaticModelSystemR.h"
#include "RenderEngine/HierachicalInstancedStaticModelSystemR.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "ECS/Develop/Framework.h"
#include "RenderEngine/MeshBuildSystemR.h"
#include "RenderEngine/RenderNodeSystemR.h"
#include "RenderEngine/RenderWorld.h"
#include "RenderEngine/RenderMaterial.h"
#include "RenderEngine/RenderPipelineSystemR.h"
#include "RenderEngine/RenderPropertySystemR.h"
#include "RenderEngine/GPUScene/GPUScene.h"
#include "RenderEngine/CameraSystemR.h"
#include "RenderEngine/RenderCamera.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderEngine/AABBSystemR.h"

namespace cross {
ecs::ComponentDesc* InstancedStaticModelComponentR::GetDesc()
{
    return EngineGlobal::GetECSFramework().CreateOrGetRenderComponentDesc<cross::InstancedStaticModelComponentR>(false);
}

InstancedStaticModelSystemR* InstancedStaticModelSystemR::CreateInstance()
{
    return new InstancedStaticModelSystemR();
}

void InstancedStaticModelSystemR::Release()
{
    delete this;
}

void InstancedStaticModelSystemR::OnBeginFrame(FrameParam* frameParam)
{
    mChangeList.GetContainer().clear();
}

void InstancedStaticModelSystemR::OnEndFrame(FrameParam* frameParam) {}

void InstancedStaticModelSystemR::OnBuildUpdateTasks(FrameParam* frameParam)
{
    constexpr UInt32 FrameCount = 30;

    CreateTaskFunction(FrameTickStage::Update, {}, [this] {
        SCOPED_CPU_TIMING(GroupRendering, "InstancedStaticModelUpdateR");

        auto* GPUScene = mRenderWorld->GetRenderSystem<RenderPipelineSystemR>()->GetWorldRenderPipeline()->GetGPUScene();

        // Pass InstancedRenderNode to RenderNodeComponent
        for (UInt32 eventIndex = 0, eventCount = mRenderWorld->GetRemainedEventCount<EntityCreateEvent>(true); eventIndex < eventCount; ++eventIndex)
        {
            auto& lifeEvent = mRenderWorld->GetRemainedEvent<EntityCreateEvent>(eventIndex);
            if (ecs::HasComponentMask<InstancedStaticModelComponentR>(lifeEvent.mData.mChangedComponentMask))
            {
                auto& entityID = lifeEvent.mData.mEntityID;
                if (mRenderWorld->IsEntityAlive(entityID))
                {
                    auto [instancedStaticModelComp, renderNodeComp] = mRenderWorld->GetComponent<InstancedStaticModelComponentR, RenderNodeComponentR>(entityID);
                    if (EnumHasAnyFlags(lifeEvent.mData.mEventFlag, EntityLifeCycleEventFlag::CreateComponent))
                    {
                        renderNodeComp.mComponent->SetRenderNode(instancedStaticModelComp.Write()->mRenderNode);
                    }
                }
            }
        }

        // Generate RenderNode
        for (auto iter = mChangeList.GetContainer().begin(); iter != mChangeList.GetContainer().end(); ++iter)
        {
            ecs::EntityID entity = (*iter);
            if (!mRenderWorld->HasComponent<HierachicalInstancedStaticModelComponentR>(entity))
            {
                if (!mRenderWorld->IsEntityAlive(entity)) [[unlikely]]
                {
                    continue;
                }

                auto [modelComp, renderNodeComp] = mRenderWorld->GetComponent<InstancedStaticModelComponentR, RenderNodeComponentR>(entity);
                if (!modelComp.IsValid()) [[unlikely]]
                {
                    continue;
                }

                auto modelCompReader = modelComp.Read();

                if (!modelCompReader->mVisible)
                {
                    continue;
                }

                MeshR* renderMesh = modelCompReader->mRenderMesh;
                if (!renderMesh)
                {
                    continue;   // Not built
                }

                if (renderMesh->GetState() != MeshR::State::Initialized)
                {
                    continue;
                }

                ModelRenderNode::RenderModel renderModel{};
                std::vector<const resource::MeshAssetLODSetting*> LODSettings;

                auto ConvertIndividualModelToRenderModel = [&] {
                    auto meshAssetData = modelCompReader->mAsset->GetAssetData();

                    renderModel.mReceiveDecals = modelCompReader->mReceiveDecals;

                    auto modelLODCount = modelCompReader->mLODModelProperties.size();
                    renderModel.mLODModels.resize(modelLODCount);
                    BoundingBox renderModelBoundingBox{BoundingBox::Flags::MergeIdentity};
                    // Lod0 BBox
                    BoundingBox renderModelLod0BoundingBox{BoundingBox::Flags::MergeIdentity};
                    {
                        auto const& singleLODModelProperty = modelCompReader->mLODModelProperties[0u];
                        auto& singleLODRenderModel = renderModel.mLODModels[0u];

                        auto subModelCount = singleLODModelProperty.mSubModelProperties.size();
                        singleLODRenderModel.mSubModels.resize(subModelCount);
                        for (auto subMeshIndex = 0u; subMeshIndex < subModelCount; subMeshIndex++)
                        {
                            auto const& subModelProperty = singleLODModelProperty.mSubModelProperties[subMeshIndex];

                            if (!subModelProperty.mVisible)
                                continue;

                            auto& subModel = singleLODRenderModel.mSubModels[subMeshIndex];

                            UInt32 subMeshStartIndex = 0;
                            UInt32 subMeshCount = 0;
                            meshAssetData->GetMeshLodInfo(0u, subMeshStartIndex, subMeshCount);
                            subModel.mGeometry = subMeshIndex < subMeshCount ? &(renderMesh->GetRenderGeometry(subMeshStartIndex + subMeshIndex)) : nullptr;
                            subModel.mMaterial = subModelProperty.mMaterial;
                            subModel.mBoundingBox = meshAssetData->GetMeshPartBoundingBox(subMeshStartIndex + subMeshIndex);

                            BoundingBox::CreateMerged(renderModelLod0BoundingBox, renderModelLod0BoundingBox, subModel.mBoundingBox);
                        }
                    }
                    for (auto lodIndex = 1u; lodIndex < modelLODCount; lodIndex++)
                    {
                        auto const& singleLODModelProperty = modelCompReader->mLODModelProperties[lodIndex];
                        auto& singleLODRenderModel = renderModel.mLODModels[lodIndex];

                        auto subModelCount = singleLODModelProperty.mSubModelProperties.size();
                        singleLODRenderModel.mSubModels.resize(subModelCount);
                        for (auto subMeshIndex = 0u; subMeshIndex < subModelCount; subMeshIndex++)
                        {
                            auto const& subModelProperty = singleLODModelProperty.mSubModelProperties[subMeshIndex];

                            if (!subModelProperty.mVisible)
                                continue;

                            auto& subModel = singleLODRenderModel.mSubModels[subMeshIndex];

                            UInt32 subMeshStartIndex = 0;
                            UInt32 subMeshCount = 0;
                            meshAssetData->GetMeshLodInfo(lodIndex, subMeshStartIndex, subMeshCount);
                            subModel.mGeometry = subMeshIndex < subMeshCount ? &(renderMesh->GetRenderGeometry(subMeshStartIndex + subMeshIndex)) : nullptr;
                            subModel.mMaterial = subModelProperty.mMaterial;
                            if (modelCompReader->mUseLod0Bbox)
                            {
                                subModel.mBoundingBox = std::move(renderModelLod0BoundingBox);
                            }
                            else
                            {
                                subModel.mBoundingBox = meshAssetData->GetMeshPartBoundingBox(subMeshStartIndex + subMeshIndex);
                                BoundingBox::CreateMerged(renderModelBoundingBox, renderModelBoundingBox, subModel.mBoundingBox);
                            }
                        }
                    }

                    renderModel.mBoundingBox = std::move(modelCompReader->mUseLod0Bbox ? renderModelLod0BoundingBox : renderModelBoundingBox);
                };

                ConvertIndividualModelToRenderModel();

                auto* renderNode = static_cast<InstancedStaticModelRenderNode*>(modelComp.Write()->mRenderNode.get());
                renderNode->SetLODSettings(LODSettings);
                renderNode->FreeGPUScene(*GPUScene);
                renderNode->SetRenderModelInstanced(std::move(renderModel), modelCompReader->mInstanceDataResource);
                renderNode->SetGlobalScale(modelCompReader->mGlobalScale);
                renderNode->SetDistnaceCulling(modelCompReader->mDistanceCulling);
                renderNode->AllocateGPUScene(*GPUScene);
                GPUScene->SetGPUSceneDirty(entity);
            }
        }
    });
}

void InstancedStaticModelSystemR::NotifyEvent(const SystemEventBase& event, UInt32& flag)
{
    RenderSystemBase::NotifyEvent(event, flag);
    if (event.mEventType == OnSystemAddToRenderWorldEvent::sEventType)
    {
        mRenderWorld->SubscribeRemainedEvent<EntityDestroyEvent>(this, true);
    }
    else if (event.mEventType == RemainedEventUpdatedEvent::sEventType)
    {
        const RemainedEventUpdatedEvent& e = static_cast<const RemainedEventUpdatedEvent&>(event);
        if (e.mData.mRemainedEventType == EntityDestroyEvent::sEventType)
        {
            auto* GPUScene = mRenderWorld->GetRenderSystem<RenderPipelineSystemR>()->GetWorldRenderPipeline()->GetGPUScene();
            for (UInt32 i = e.mData.mFirstIndex; i <= e.mData.mLastIndex; i++)
            {
                auto& lifeEvent = mRenderWorld->GetRemainedEvent<EntityDestroyEvent>(i);
                auto entity = lifeEvent.mData.mEntityID;

                if (ecs::HasComponentMask<InstancedStaticModelComponentR>(lifeEvent.mData.mChangedComponentMask) && EnumHasAnyFlags(lifeEvent.mData.mEventFlag, EntityLifeCycleEventFlag::DestroyComponent))
                {
                    auto& entityID = lifeEvent.mData.mEntityID;
                    auto modelComp = mRenderWorld->GetComponent<InstancedStaticModelComponentR>(entityID);
                    auto& renderNode = modelComp.Write()->mRenderNode;
                    if (renderNode)
                        renderNode->FreeGPUScene(*GPUScene);
                }
            }
        }
    }
}

void InstancedStaticModelSystemR::SetModelAsset(ecs::EntityID entity, MeshAssetDataResourcePtr asset)
{
    auto modelH = mRenderWorld->GetComponent<InstancedStaticModelComponentR>(entity);
    auto writer = modelH.Write();

    writer->mAsset = asset;
    writer->mRenderMesh = static_cast<MeshR*>(asset->GetAssetData()->GetRenderMesh());
    Assert(writer->mRenderMesh != nullptr);
    writer->mRenderMeshDirty = true;

    auto meshAssetData = asset->GetAssetData();

    writer->mLODModelProperties.resize(meshAssetData->GetLodCount());

    int lodIdx = 0;
    for (auto& singleLODProperty : writer->mLODModelProperties)
    {
        singleLODProperty.mSubModelProperties.resize(meshAssetData->GetMeshPartCount(lodIdx));
        lodIdx++;
    }

    writer->mIsStaticBuilt = false;
}

MaterialR* InstancedStaticModelSystemR::GetModelMaterial(const RenderInstancedStaticModelComponentReader& modelH, UInt32 subModelIndex, UInt32 lodIndex)
{
    if (modelH->mAsset)
    {
        if (lodIndex < static_cast<UInt32>(modelH->mLODModelProperties.size()) && subModelIndex < static_cast<UInt32>(modelH->mLODModelProperties[lodIndex].mSubModelProperties.size()))
        {
            return modelH->mLODModelProperties[lodIndex].mSubModelProperties[subModelIndex].mMaterial;
        }
    }

    return nullptr;
}

void InstancedStaticModelSystemR::SetModelMaterial(ecs::EntityID entity, MaterialR* material, SInt32 subModelIndex, SInt32 lodIndex)
{
    auto SetSubModelMaterial = [](InstancedStaticModelComponentR::StaticSingleLODModelProperty& singleLODModelProperty, MaterialR* material, SInt32 subModelIndex) {
        if (subModelIndex >= 0)
        {
            Assert(subModelIndex < singleLODModelProperty.mSubModelProperties.size());
            singleLODModelProperty.mSubModelProperties[subModelIndex].mMaterial = material;
        }
        else
        {
            for (auto& prop : singleLODModelProperty.mSubModelProperties)
            {
                prop.mMaterial = material;
            }
        }
    };
    
    auto modelH = mRenderWorld->GetComponent<InstancedStaticModelComponentR>(entity);
    auto writer = modelH.Write();

    auto& lodModelProperties = writer->mLODModelProperties;
    if (lodIndex >= 0)
    {
        if (lodIndex >= lodModelProperties.size())
        {
            lodModelProperties.resize(lodIndex + 1);
        }

        SetSubModelMaterial(lodModelProperties[lodIndex], material, subModelIndex);
    }
    else
    {
        for (auto& singleLODModelProperty : lodModelProperties)
        {
            SetSubModelMaterial(singleLODModelProperty, material, subModelIndex);
        }
    }

    SetModelDirty(entity);
}

EntityDistanceCulling InstancedStaticModelSystemR::GetModelEntityDistanceCulling(ecs::EntityID entity)
{
    auto modelH = mRenderWorld->GetComponent<InstancedStaticModelComponentR>(entity);
    return modelH.Read()->mDistanceCulling;
}

void InstancedStaticModelSystemR::SetModelEntityDistanceCulling(ecs::EntityID entity, const EntityDistanceCulling& entityCulling)
{
    auto modelH = mRenderWorld->GetComponent<InstancedStaticModelComponentR>(entity);
    modelH.Write()->mDistanceCulling = entityCulling;
    std::dynamic_pointer_cast<InstancedStaticModelRenderNode>(modelH.Write()->mRenderNode)->SetDistnaceCulling(entityCulling);
}
void InstancedStaticModelSystemR::SetGlobalScale(ecs::EntityID entity, const float globalScale, bool rebuildClusterTree)
{
    auto modelH = mRenderWorld->GetComponent<InstancedStaticModelComponentR>(entity);
    modelH.Write()->mGlobalScale = globalScale;
    if (rebuildClusterTree)
        modelH.Write()->mNeedRebuildClusterTree = true;
    SetModelDirty(entity);
}
bool InstancedStaticModelSystemR::GetModelVisible(ecs::EntityID entity)
{
    auto modelComp = mRenderWorld->GetComponent<InstancedStaticModelComponentR>(entity);
    auto renderPropertyComp = mRenderWorld->GetComponent<RenderPropertyComponentR>(entity);

    if (mRenderWorld->GetRenderSystem<RenderPropertySystemR>()->IsHide(renderPropertyComp.Read()))
    {
        return false;
    }

    return modelComp.Read()->mVisible;
}

void InstancedStaticModelSystemR::SetModelVisible(ecs::EntityID entity, bool isVisible)
{
    mRenderWorld->GetComponent<InstancedStaticModelComponentR>(entity).Write()->mVisible = isVisible;

    SetModelDirty(entity);
}

void InstancedStaticModelSystemR::SetModelReceiveDecals(ecs::EntityID entity, bool value)
{
    mRenderWorld->GetComponent<InstancedStaticModelComponentR>(entity).Write()->mReceiveDecals = value;

    SetModelDirty(entity);
}

void InstancedStaticModelSystemR::SetModelUseLod0Bbox(ecs::EntityID entity, bool value)
{
    mRenderWorld->GetComponent<InstancedStaticModelComponentR>(entity).Write()->mUseLod0Bbox = value;

    SetModelDirty(entity);
}

void InstancedStaticModelSystemR::SetSubModelVisible(ecs::EntityID entity, bool isVisible, UInt32 lodIndex, SInt32 subModelIndex)
{
    auto writer = mRenderWorld->GetComponent<InstancedStaticModelComponentR>(entity).Write();

    if (subModelIndex >= 0)
    {
        auto& singleLODModelProperty = writer->mLODModelProperties[lodIndex]; 
        singleLODModelProperty.mSubModelProperties[subModelIndex].mVisible = isVisible;
    }
    else
    {
        auto& singleLODModelProperty = writer->mLODModelProperties[lodIndex];
        for (auto& prop : singleLODModelProperty.mSubModelProperties)
        {
            prop.mVisible = isVisible;
        }    
    }

    SetModelDirty(entity);
}

RenderInstancedStaticModelComponentHandle InstancedStaticModelSystemR::GetModelHandle(ecs::EntityID entity)
{
    return mRenderWorld->GetComponent<InstancedStaticModelComponentR>(entity);
}

void InstancedStaticModelSystemR::SetModelDirty(ecs::EntityID entity)
{
    if (mChangeList.HasChangeData(entity))
        return;

    mChangeList.EmplaceChangeData(entity);
}

void InstancedStaticModelSystemR::SetInstanceDataResource(ecs::EntityID entity, InstanceDataResourcePtr instanceDataResource)
{
    mRenderWorld->GetComponent<InstancedStaticModelComponentR>(entity).Write()->mInstanceDataResource = instanceDataResource;

    SetModelDirty(entity);
}

void InstancedStaticModelSystemR::NotifyInstanceDataResourceChange(const resource::InstanceDataResource* instanceDataResource)
{
    auto ismComps = mRenderWorld->Query<InstancedStaticModelComponentR>();
    for (auto ismComp : ismComps)
    {
        if (ismComp.Read()->mInstanceDataResource.get() == instanceDataResource)
        {

            SetModelDirty(ismComp.GetEntityID());
        }
    }
}
}   // namespace cross
