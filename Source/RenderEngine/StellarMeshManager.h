#pragma once
#include "RenderMesh.h"
#include "Resource/Cluster/Cluster.h"
#include "Resource/Cluster/ClusterEncoder.h"
#include "NativeGraphicsInterface/NGI.h"
#include <mutex>
#include <queue>
#include <shared_mutex>

namespace cross::StellarMesh
{
    // 
    struct MeshConstant
    {
        UInt32 mClusterOffset{0};
        UInt32 mClusterCount {0};

        UInt32 mIndexOffset{0};
        UInt32 mIndexCount{0};
        UInt32 mVertexOffset{0};
        UInt32 mVertexCount{0};

        UInt32 mIndexBufferIndex   {CE_BINDLESS_INVALID_INDEX};
        UInt32 mPositionBufferIndex{CE_BINDLESS_INVALID_INDEX};
        UInt32 mNormalBufferIndex  {CE_BINDLESS_INVALID_INDEX};
        UInt32 mTangetBufferIndex  {CE_BINDLESS_INVALID_INDEX};
        UInt32 mColorBufferIndex   {CE_BINDLESS_INVALID_INDEX};
        UInt32 mUV0BufferIndex     {CE_BINDLESS_INVALID_INDEX};
        UInt32 mUV1BufferIndex     {CE_BINDLESS_INVALID_INDEX};

        UInt3 padding;
    };

    class StellarMeshManager
    {
    public:
        StellarMeshManager();
        ~StellarMeshManager();

        void AddRenderMesh(MeshR* meshR, GPUClusteredMeshData clusteredMeshData);

        // Note: We don't remove data from mClusters as this would invalidate offsets for other meshes
        // TODO(jahwang): A more complex memory management strategy might be needed
        void RemoveRenderMesh(MeshR* meshR);

        // Get buffer pointers for rendering thread
        NGIBuffer* GetMeshConstantBuffer() const;
        NGIBuffer* GetClusterBuffer() const;

        // Get buffer views for rendering thread
        NGIBufferView* GetMeshConstantBufferView() const;
        NGIBufferView* GetClusterBufferView() const;
        NGIBufferView* GetIndexBufferView() const;
        NGIBufferView* GetPositionBufferView() const;

    private:
        struct Impl;
        std::unique_ptr<Impl> pImpl;
    };
}
