#pragma once
#include "RenderEngineForward.h"
#include "CrossBase/Math/CrossMath.h"
#include "CECommon/Common/TRS.h"
#include "CECommon/Common/FrameStdContainer.h"
#include "CECommon/Common/RenderSystemBase.h"
#include "ECS/Develop/Framework/Types.h"

namespace cross
{

namespace ecs
{
struct ComponentDesc;
}

struct SystemDesc;

/*
*   Tile based coordinate system:
*       RelativePosition =  TransformComponentR.mTransform.Translation
*       AbsolutePosition = TilePositionComponentR.mTilePosition * LENGTH_PER_TILE + mTransform.Translation
*/

struct alignas(16) TransformComponentR : ecs::IComponent
{
    CEFunction(Reflect)
    RENDER_ENGINE_API static ecs::ComponentDesc* GetDesc();

protected:
    TRS_F_A mTransform;
    Float4x4A mWorldMatrix{ 1,0,0,0,
                            0,1,0,0,
                            0,0,1,0,
                            0,0,0,1 };
    Float4x4A mWorldAbsoMatrix{1, 0, 0, 0,
                                0,1,0,0,
                                0,0,1,0,
                                0,0,0,1 };
    Float4x4A mWorldInverseMatrix{  1,0,0,0,
                                    0,1,0,0,
                                    0,0,1,0,
                                    0,0,0,1 };
	friend class TransformSystemR;
};
#pragma warning(push)
#pragma warning(disable : 4324)
struct alignas(16) TilePositionComponentR : ecs::IComponent
{
    CEFunction(Reflect)
    RENDER_ENGINE_API static ecs::ComponentDesc* GetDesc();

protected:
    Float3 mTilePosition{0.f, 0.f, 0.f}; 

    friend class TransformSystemR;
};
#pragma warning(pop)
struct TransformChangeEventData
{
    TransformChangeEventData(ecs::EntityID entityID)
        : mEntityID(entityID){};

    ecs::EntityID mEntityID;
};
using TransformChangeEvent = SystemEvent<TransformChangeEventData>;

class TransformSystemR : public RenderSystemBase, public SystemEventManager<TransformChangeEvent>
{
    CEMetaInternal(Reflect)
public:
    using TransformChangeList = FrameEntityChangeList<TRSRenderMatrixType>;

    using TransformHandle = ecs::ComponentHandle<TransformComponentR>;
    using TransformReader = ecs::ScopedComponentRead<TransformComponentR>;
    using TilePositionHandle = ecs::ComponentHandle<TilePositionComponentR>;
    using TilePositionReader = ecs::ScopedComponentRead<TilePositionComponentR>;

    RENDER_ENGINE_API static SystemDesc& GetDesc();

    RENDER_ENGINE_API static TransformSystemR* CreateInstance();

    virtual void Release() override;

    virtual void OnBeginFrame(FrameParam* frameParam) override;

    virtual void OnEndFrame(FrameParam* frameParam) override;

public:
    RENDER_ENGINE_API void SetTransfromData(ecs::EntityID entity, const TRS_A& transform, const TRSMatrixType& worldMatrix, const TRSMatrixType& worldInverseMatrix);

    RENDER_ENGINE_API void SetTransfromDataDirty(ecs::EntityID entity);

    void CopyTransformData(ecs::EntityID fromEntity, ecs::EntityID toEntity);

    void SetWorldTranslation(ecs::EntityID entity, const Float3A& translation);

    void SetWorldRotation(ecs::EntityID entity, const QuaternionA& rotation);

    void SetWorldScale(ecs::EntityID entity, const Float3A& scale);
    
    RENDER_ENGINE_API Float3A GetWorldTranslation(ecs::EntityID entity) const;

    RENDER_ENGINE_API Float3A GetAbsWorldTranslation(ecs::EntityID entity) const;

    RENDER_ENGINE_API Float3A GetTilePosition(ecs::EntityID entity) const;

    RENDER_ENGINE_API Float4x4A GetWorldMatrix(ecs::EntityID entity) const;

    RENDER_ENGINE_API Float4x4A GetWorldInverseMatrix(ecs::EntityID entity) const;

    QuaternionA GetWorldRotation(ecs::EntityID entity) const;

    Float3A GetWorldScale(ecs::EntityID entity) const;

    inline const Quaternion& GetWorldRotation(const TransformReader& reader) const { return reader->mTransform.mRotation; }
    
    inline const Float4x4* GetWorldMatrix(const TransformReader& reader)const { return &reader->mWorldMatrix; }

    inline const Float4x4* GetWorldAbsoMatrixMatrix(const TransformReader& reader) const { return &reader->mWorldAbsoMatrix; };

    inline const Float3& GetWorldTranslation(const TransformReader& reader)const { return reader->mTransform.mTranslation; }

    inline const Float3& GetTilePosition(const TilePositionReader& reader)const { return reader->mTilePosition; }

    //hierarchy
    void CreateRootEntity(ecs::EntityID entity);

    inline ecs::EntityID GetRootEntity() { return mRootEntity; }

    const TransformChangeList& GetTransformChangeList() const { return mTransformChangeList; }

    std::vector<std::pair<ecs::EntityID, TileBasedMatrix>>& GetPreviousTransformPostRenderUpdateList() { return mPreviousTransformPostRenderUpdateList; }

    CEFunction(Reflect)
    void OnBuildPostUpdateTasks(FrameParam* frameParam) override;

private:
    ecs::EntityID mRootEntity;

protected:
	TransformSystemR();

	~TransformSystemR();

    TransformChangeList mTransformChangeList;

    std::vector<std::pair<ecs::EntityID, TileBasedMatrix>> mPreviousTransformPostRenderUpdateList;
};

}
