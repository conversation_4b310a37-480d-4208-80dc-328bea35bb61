#pragma once

#include "CrossBase/Math/Color.h"
#include "CrossBase/MathLib.h"

namespace cross
{

class SceneParam
{
public:
	Vector3f mViewerPosition;
	ColorRGBA32 mbackgroundColor{ 0,0,0,255 };
	ColorRGBA32 mAmbientLight{ 50,50,50,255 };
	ColorRGBA32 mLightmapTintColor{ 255,255,255,255 };

	//fog param
	float mFogStart{ 0 };
	float mFogEnd{ 0 };
	float mFogDensity{ 1 };
	float mFogBrightness{ 1 };
	float mFogExponent{ 1 };
	ColorRGBA32 mFogColor{ 255,255,255,255 };
	UInt32 mFogType;

	//height fog param
	float mHeightFogStart{ 0 };
	float mHeightFogEnd{ 0 };
	float mHeightFogDensity{ 1 };
	float mHeightFogBrightness{ 1 };
	float mHeightFogExponent{ 1 };
	ColorRGBA32 mHeightFogColor{ 255,255,255,255 };
	UInt32 mHeightFogType;

	//wind
	float mWindDirection[4] = { 1.0f,0,0,1.0f };
	float mWindSpeed{ 0 };
};

}