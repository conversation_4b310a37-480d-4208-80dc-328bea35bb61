#pragma once
#include "EnginePrefix.h"
#include "Resource/InstanceDataResource.h"

namespace cross {

struct ClusterTree
{
    std::vector<ClusterNode> Nodes;
    std::vector<SInt32> SortedInstances;
    std::vector<SInt32> InstanceReorderTable;
};

class ClusterBuilder
{
public:
    static constexpr UInt32 MAX_PARTITION_COUNT = 16;

    ClusterBuilder(InstanceDataResourcePtr instanceDataResource, const BoundingBox& instBox, SInt32 maxInstancesPerLeaf, SInt32 internalNodeBranchingFactor);

    void BuildTreeAndResource(float globalScale, SInt32 partitionCount = 1);

private:
    void Init(float globalScale);

    void BuildTree(float globalScale, const SInt32 partitionCount = 1);

    void OverrideInstanceDataResource();

    void Split(SInt32 num);

    void Split(SInt32 start, SInt32 end);

    void MergeRange(SInt32 index, std::vector<SInt32> parentFirstInstance, std::vector<SInt32> parentLastInstance, std::vector<SInt32>& sortedInstances);

private:
    std::unique_ptr<ClusterTree> mResult;
    InstanceDataResourcePtr mInstanceDataResource;

    std::vector<Float4x4> mInstanceTransforms;

    std::vector<SInt32> mSortIndex;
    std::vector<Float3> mSortPoints;

    struct RunPair
    {
        SInt32 Start;
        SInt32 Num;

        bool operator<(const RunPair& o) const { return Start < o.Start; }
    };
    std::vector<RunPair> mClusters;

    struct SortPair
    {
        float d;
        SInt32 Index;

        bool operator<(const SortPair& o) const { return d < o.d; }
    };
    std::vector<SortPair> mSortPairs;

    SInt32 mMaxInstancesPerLeaf = 16;
    SInt32 mBranchingFactor;
    SInt32 mNumRoot;
    BoundingBox mInstBox;
    SInt32 mInternalNodeBranchingFactor = 16;
    SInt32 mNum;
};
}   // namespace cross