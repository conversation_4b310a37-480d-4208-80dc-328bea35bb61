#include "RenderNode.h"

namespace cross 
{
void RenderNode::SetRenderEffect(RenderEffectTag renderEffect) 
{
    mRenderEffect = renderEffect;
}

UInt8 RenderNode::GetLoDBias() const {
    auto pipelineSetting = EngineGlobal::GetSettingMgr()->GetRenderPipelineSetting();
    return mLoDBias + static_cast<UInt8>(pipelineSetting->GlobalLODBias);
}

UInt8 RenderNode::GetLODIndex(float* screenRelativeHeight, UInt8 lodCount, const resource::MeshAssetLODSetting* lodSetting) const
{
    if (!lodSetting || !screenRelativeHeight)
    {
        return std::min(GetLoDBias(), static_cast<UInt8>(lodCount - 1));
    }

    const SInt32 lodSelected = mLodSelected;
    if (lodSelected < 0)
    {
        float height = *screenRelativeHeight;
        if (height <= lodSetting->mCulledHeight && lodSetting->mCulledHeight != 0.0)
        {
            return UINT8_MAX;
        }
        for (UInt8 lodIndex = 0; lodIndex < lodCount; ++lodIndex)
        {
            if (height > lodSetting->mLevelSettings[lodIndex].mScreenReleativeTransitionHeight)
            {
                return std::min(static_cast<UInt8>(lodIndex + GetLoDBias()), static_cast<UInt8>(lodCount - 1));
            }
        }
        return static_cast<UInt8>(lodCount - 1);
    }
    else
    {
        return std::min<UInt8>(static_cast<UInt8>(lodSelected) + GetLoDBias(), lodCount - 1);
    }
}

void RenderNode::SetReflectionProbeList(FrameVector<std::pair<ecs::EntityID, float>>* rpIdAndWeightList)
{
    UInt32 rpSize = rpIdAndWeightList->GetSize();
    mRefleProbeIDWeights.resize(rpSize);

    for (UInt32 i = 0; i < rpSize; ++i)
    {
        mRefleProbeIDWeights[i] = rpIdAndWeightList->At(i);
    }
}

float RenderNode::GetScaledCulledDistance(float distanceToCamera)
{
    return distanceToCamera / ((CmdSettings::Inst().gCullingDistanceScale == 0.0f) ? 1.0f : std::max(0.000001f, CmdSettings::Inst().gCullingDistanceScale));
}

bool RenderNode::GenerateDrawUnitsParams::IsDrawable(MaterialR* material, MaterialR*& finalMaterial, UInt16& renderGroup) const
{
    finalMaterial = drawUnitsDesc.OverrideMaterial ? drawUnitsDesc.OverrideMaterial : material;

    if (finalMaterial->GetFx()->HasPass(drawUnitsDesc.TagName) && finalMaterial->IsEnable(drawUnitsDesc.TagName) && finalMaterial->GetPassDataBlock().size())
    {
        if (renderGroup = finalMaterial->GetRenderGroup(drawUnitsDesc.TagName); math::InBetween(drawUnitsDesc.RenderGroupLow, renderGroup, drawUnitsDesc.RenderGroupHigh))
        {
            return true;
        }
    }
    return false;
}

}   // namespace cross