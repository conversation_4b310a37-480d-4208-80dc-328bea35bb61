#pragma once

#include "RendererSystemR.h"
#include "RenderNode.h"
#include "Resource/MeshAssetDataResource.h"

namespace cross {

class GPUScene;

class ModelRenderNode : public RenderNode
{
public:
    //struct RenderMesh
    //{
    //    std::array<RenderGeometry*, resource::MAX_MESH_LOD_NUM> mLODGeometries;
    //    UInt8 mLODCount{0};
    //    std::vector<MaterialR*> mLODMaterials;   // 0-LOD1
    //    MaterialR* mDefaultMaterial;
    //    bool mReceiveDecals{true};
    //    BoundingBox mBoundingBox;
    //};

    struct GPUSceneAlloc
    {
        SInt32 IndexStart = -1;
        UInt32 IndexCount = 0;
        const GPUSceneBufferView* BufferView = nullptr;
        const resource::Shader::ProtoType* ProtoType = nullptr;
    };

    struct GPUScenePassAlloc
    {
        GPUSceneAlloc* PrimitiveAlloc = nullptr;
        GPUSceneAlloc* ObjectAlloc = nullptr;
    };

    //struct SubModelLOD
    //{
    //    RenderGeometry* mGeometry;
    //    MaterialR* mMaterial;

    //    CEHashMap<NameID, GPUScenePassAlloc, std::hash<NameID>> PassAllocs; // for version 1
    //    GPUScenePassAlloc GPUSceneAlloc; // for version 2
    //};

    //struct SubModel
    //{
    //    auto& GetLODs() { return mLODs; }

    //    UInt8 GetLODCount() const { return static_cast<UInt8>(mLODs.size()); }

    //    bool IsReceiveDecals() const { return mReceiveDecals; }

    //    const RenderGeometry* GetRenderGeometry(UInt8 lodIndex) const;

    //    MaterialR* GetMaterial(RenderMaterialTag materialTag = 0) const;

    //public:
    //    std::vector<SubModelLOD> mLODs;
    //    BoundingBox mBoundingBox;
    //    bool mReceiveDecals{true};

    //    // used by StaticModelRenderNode and InstancedStaticModelRenderNode
    //    SInt32 mPrimitiveCullingGUIDStart = -1;
    //    SInt32 mObjectCullingGUIDStart = -1;
    //};

    struct SubModel
    {
        RenderGeometry* mGeometry{};
        MaterialR* mMaterial{};

        CEHashMap<NameID, GPUScenePassAlloc, std::hash<NameID>> PassAllocs{}; // for version 1
        GPUScenePassAlloc GPUSceneAlloc{};                                    // for version 2

        // For Culling
        BoundingBox mBoundingBox{};

        SInt32 mPrimitiveCullingGUIDStart = -1;
        SInt32 mObjectCullingGUIDStart = -1;
    };

    struct SingleLODModel
    {
        std::vector<SubModel> mSubModels{};
        MeshR* mParentMesh = nullptr;
        UInt32 mParentMeshLOD = 0;
        std::unique_ptr<NGIAccelStruct> mDynamicBLAS;  // For skeleton mesh
    };

    struct RenderModel
    {
        std::vector<SingleLODModel> mLODModels{};

        bool mReceiveDecals{false};

        // For lod Selection and Primitive buffer
        BoundingBox mBoundingBox{};

        bool mReadyForStreaming{false};
    };

    struct RenderStellarMesh
    {
        std::vector<MaterialR*> mMaterials{};
        BoundingBox mBoundingBox{};
        UInt32 mMeshConstantID{};
    };

public:
    virtual void AllocateGPUScene(GPUScene& GPUScene) = 0;

    virtual void FreeGPUScene(GPUScene& GPUScene);

    void AllocateCullingData(GPUScene& GPUScene, UInt32 instanceCount);

    void AllocateObjectAndPrimitiveData(GPUScene& GPUScene, UInt32 instanceCount);

public:
    template<typename T, typename = std::enable_if_t<std::is_rvalue_reference_v<T&&>>, typename = std::enable_if_t<std::is_same_v<std::remove_reference_t<T>, RenderModel>>>
    void SetRenderModel(T&& renderModel)
    {
        SetRenderModelImpl(std::forward<T>(renderModel));
    }

    const RenderModel& GetRenderModel() const { return mRenderModel; }

    auto& GetRenderModel() { return mRenderModel; }

    BoundingBox GetSubModelBoundingBox(UInt32 submodelIndex, UInt32 lodIndex = 0) const;

    SInt32 GetSubModelObjectGUID(UInt32 submodelIndex, UInt32 lodIndex = 0) const;
    
    // hot reload, fx changed
    virtual void NotifyMaterialChange(GPUScene& GPUScene, MaterialR* mtlChanged, RenderNodeSystemR* renderNodeSys, ecs::EntityID entity) override;

    RenderNodeType GetType() const override { return RenderNodeType::Model; }

    inline SInt32 GetLodSelected() const { return mLodSelected; }

    inline const resource::MeshAssetLODSetting* GetLODSetting() const { return mLodSetting; }

    void SetLODSettings(const std::vector<const resource::MeshAssetLODSetting*> lodSettings)
    {
        if (lodSettings.size())
        {
            mLodSetting = lodSettings.front();
        }
    }

    void SetLODSelected(SInt32 lodSelected) { mLodSelected = lodSelected; }

    Float2 GetBoundingBoxScreenRadiusAndDistance(const BoundingBox& boundingBox, const GenerateDrawUnitsParams& params) const;

    Float2 GetEntityScreenRadiusAndDistance(const GenerateDrawUnitsParams& params) const;

    // ---------------------------------------------For RayTracing---------------------------------------------
    UInt32 GetSubMeshCount() const override
    {
        return mRenderModel.mLODModels.size() == 0 ? 0 : static_cast<UInt32>(mRenderModel.mLODModels[0].mSubModels.size());
    }
    void BuildAccelStruct(RayTracingScene& RayTracingScene, RenderWorld* renderWorld, ecs::EntityID entity) override;
    bool IsValidForRayTracing() const override { return true; }
    NGIAccelStruct* GetAccelStruct() const override;
    RenderGeometry* GetRenderGeometry(UInt32 index) override { return mRenderModel.mLODModels[0].mSubModels[index].mGeometry; }
    
    // For StellarMesh
    // TODO(jahwang): A more elegant way to do this
    virtual void FreeStellarMeshScene(StellarMeshScene& StellarMeshScene, ecs::EntityID entity) {}
    virtual void SetRenderStellarMesh(RenderStellarMesh renderStellarMesh) { Assert(false); } 

protected:
    //std::vector<SubModel> mSubModels;

    virtual void SetRenderModelImpl(RenderModel&& renderModel);

    RenderModel mRenderModel;

    CEHashMap<GPUProtoType::ID, GPUSceneAlloc> mPrimitiveAllocs;

    struct Hasher
    {
        std::hash<UInt64> gHasher;

        size_t operator()(const std::tuple<GPUProtoType::ID, GPUProtoType::ID>& value) const { return gHasher(static_cast<UInt64>(std::get<0>(value)) << 32 | std::get<1>(value)); }
    };

    CEHashMap<std::tuple<GPUProtoType::ID, GPUProtoType::ID>, GPUSceneAlloc, Hasher> mObjectAllocs;

    GPUSceneAlloc* AllocatePrimitiveGPUScene(GPUScene& GPUScene, const resource::Shader::ProtoType* primitiveProtoType);

    GPUSceneAlloc* AllocateObjectGPUScene(GPUScene& GPUScene, const resource::Shader::ProtoType* primitiveProtoType, const resource::Shader::ProtoType* objectProtoType, UInt32 count);

    const resource::MeshAssetLODSetting* mLodSetting{nullptr};

    friend class RenderNodeSystemR;
};
}   // namespace cross