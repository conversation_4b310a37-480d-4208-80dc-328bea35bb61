#include "ParticleSystemRenderNode.h"
#include "RenderEngine/GPUScene/GPUScene.h"
#include "RenderEngine/GPUScene/GPUSceneData.h"
#include "RenderEngine/RenderPropertySystemR.h"
#include "RenderEngine/AABBSystemR.h"
#include "RenderEngine/TransformSystemR.h"

namespace cross {

DECLARE_CPU_TIMING_GROUP(GroupParticleSystemRenderNode);

void GPUParticleRenderUnit::DeallocateBuffers()
{
    if (mParticleSystemGpuDriven)
    {
        for (auto buffer : mParticleOriginStateBuffers)
        {
            mParticleSystemGpuDriven->DeallocateParticleOriginState(buffer);
        }

        for (auto buffer : mParticleSimulationStateBuffers)
        {
            mParticleSystemGpuDriven->DeallocateParticleSimulationState(buffer);
        }
    }
    else
    {
        REDResidentBuffer::Deleter deleter;

        for (auto buffer : mParticleOriginStateBuffers)
        {
            deleter(buffer);
        }

        for (auto buffer : mParticleSimulationStateBuffers)
        {
            deleter(buffer);
        }
    }

    mParticleOriginStateBuffers.clear();
    mParticleSimulationStateBuffers.clear();
}

void ParticleRenderUnit::Update(UInt32 emitterIndex, const fx::ParticleRenderData& renderData, bool bRenderDirty, const fx::ParticleGpuComputeContext* context)
{
    mInstanceCapacity = renderData.DataCapacity[emitterIndex];
    mUseLocalSpace = renderData.LocalSpace[emitterIndex];
    mRenderType = renderData.RenderType[emitterIndex] ? ParticleRendererType::Mesh : ParticleRendererType::Sprite;
    mPrimitivePropertySet = renderData.PrimitivePropertySets.at(emitterIndex);
    mEmitterIndex = emitterIndex;
}

void GPUParticleRenderUnit::Update(UInt32 emitterIndex, const fx::ParticleRenderData& renderData, bool bRenderDirty, const fx::ParticleGpuComputeContext* context)
{
    ParticleRenderUnit::Update(emitterIndex, renderData);

    if (emitterIndex < renderData.SimulationPropertySets.size())
    {
        mSimulationPropertySet = renderData.SimulationPropertySets[emitterIndex];
    }

    mComputeContext = context;
}

void GPUParticleRenderUnit::Reset()
{
    DeallocateBuffers();
    mParticleInstanceCountManager = nullptr;
}

void CPUParticleRenderUnit::Update(UInt32 emitterIndex, const fx::ParticleRenderData& renderData, bool bRenderDirty, const fx::ParticleGpuComputeContext* context)
{
    ParticleRenderUnit::Update(emitterIndex, renderData);

    const fx::ParticleDataSet& dataSet = renderData.DataSets[emitterIndex];
    mInstanceNum = dataSet.GetInstanceNum();
    if (bRenderDirty)
    {
        if (const fx::ParticleDataBuffer* dataBuffer = dataSet.GetDataBuffer())
        {
            if (auto dataBufferPtr = dataBuffer->GetData(); dataBufferPtr && dataBuffer->GetDataSize() > 0)
            {
                mParticleBuffer.assign(dataBufferPtr, dataBufferPtr + dataBuffer->GetDataSize());
                mInstanceStride = dataSet.GetStride();
            }
            else
            {
                mParticleBuffer.clear();
                mInstanceNum = 0u;
            }
        }
    }
    else
    {
        mParticleBuffer.clear();
        mInstanceNum = 0u;
    }
}

bool ParticleSystemRenderNode::CheckGpuSceneCapacity()
{
    bool needExpand = false;
    for (auto& renderUnit : mRenderUnits)
    {
        Assert(renderUnit);
        MaterialR* renderMaterial = TYPE_CAST(MaterialR*, renderUnit->mMaterials[0]->GetRenderMaterial());
        resource::Fx* fx = renderMaterial->GetFx();

        if (!renderUnit->mInstanceCapacity)
            continue;
        if (renderUnit->mAllocationInfo.mObjectCullingGUIDStart == -1 || renderUnit->mAllocationInfo.mPrimitiveCullingGUID == -1)
        {
            needExpand = true;
            return needExpand;
        }
        if (fx->GetVersion() == 2)
        {
            if (renderUnit->mAllocationInfo.mObjectAlloc.mObjectIndexStart == -1 || renderUnit->mAllocationInfo.mObjectAlloc.mLastAllocateSize != renderUnit->mInstanceCapacity)
            {
                needExpand = true;
                return needExpand;
            }
        }
        else
        {
            auto& indexStarts = renderUnit->mAllocationInfo.mObjectIndexStarts;
            if (indexStarts.empty())
            {
                needExpand = true;
                return needExpand;
            }
            for (auto iter = indexStarts.begin(); iter != indexStarts.end(); ++iter)
            {
                auto& passObjIdxStart = iter->second;
                if (passObjIdxStart.mObjectIndexStart == -1 || passObjIdxStart.mLastAllocateSize != renderUnit->mInstanceCapacity)
                {
                    needExpand = true;
                    return needExpand;
                }
            }
        }
    }
    return needExpand;
}

void ParticleSystemRenderNode::AllocateGpuScene(GPUScene& gpuScene)
{
    SCOPED_CPU_TIMING(GroupParticleSystemRenderNode, "ParticleSystemRenderNodeAllocateGPUScene");

    if (mIsAllocated)
        FreeGpuScene(gpuScene);

    for (auto& renderUnit : mRenderUnits)
    {
        if (renderUnit->mInstanceCapacity < 1)
        {
            continue;
        }
        Assert(renderUnit->mAllocationInfo.mPrimitiveCullingGUID == -1);
        if (renderUnit->mAllocationInfo.mPrimitiveCullingGUID == -1)
        {
            std::tie(renderUnit->mAllocationInfo.mPrimitiveCullingGUID, renderUnit->mAllocationInfo.mObjectCullingGUIDStart) = gpuScene.AllocateCullingData(renderUnit->mInstanceCapacity);
            LOG_DEBUG(
                "allocate culling data mPrimitiveCullingGUID[{}], mObjectCullingGUIDStart[{}], mCullingDataNum[{}]",
                renderUnit->mAllocationInfo.mPrimitiveCullingGUID,
                renderUnit->mAllocationInfo.mObjectCullingGUIDStart,
                renderUnit->mInstanceCapacity);
        }

        const UInt32 materailCount = static_cast<UInt32>(renderUnit->mMaterials.size());
        for (auto matIndex = 0u; matIndex < materailCount; ++matIndex)
        {
            MaterialR* renderMaterial = TYPE_CAST(MaterialR*, renderUnit->mMaterials[matIndex]->GetRenderMaterial());
            resource::Fx* fx = renderMaterial->GetFx();
            if (!fx)
            {
                LOG_ERROR("Fetch the null fx while allocate gpu scene. {}", renderMaterial->GetName());
                continue;
            }

            if (fx->GetVersion() == 2)
            {
                auto [primType, objType] = fx->GetPrimitiveAndObjectProtoType(renderUnit->mUseLocalSpace ? MaterialUsage::USED_WITH_LOCAL_SPACE_PARTICLE : MaterialUsage::USED_WITH_GLOBAL_SPACE_PARTICLE);
                Assert(primType && objType);

                // prim data
                {
                    auto byteStride = primType->Layout.ByteSize;
                    auto& allocation = renderUnit->mAllocationInfo.mPrimitiveAlloc;
                    if (allocation.mObjectIndexStart == -1)
                    {
                        auto [primIndexStart, primDataBufferView] = gpuScene.Allocate(byteStride, 1);
                        LOG_ERROR("primitive index start : {}\n", primIndexStart);
                        allocation = {
                            static_cast<SInt32>(primIndexStart),
                            byteStride,
                            1,
                            primDataBufferView,
                            primType
                        };
                    }
                }

                // obj data
                {
                    auto byteStride = objType->Layout.ByteSize;
                    auto& allocation = renderUnit->mAllocationInfo.mObjectAlloc;
                    if (allocation.mObjectIndexStart == -1 || allocation.mLastAllocateSize != renderUnit->mInstanceCapacity)
                    {
                        auto [objectIndexStart, objectDataBufferView] = gpuScene.Allocate(byteStride, renderUnit->mInstanceCapacity);
                        LOG_ERROR("object index start : {}, instance num : {}\n", objectIndexStart, renderUnit->mInstanceCapacity);
                        allocation = {
                            static_cast<SInt32>(objectIndexStart),
                            byteStride,
                            renderUnit->mInstanceCapacity,
                            objectDataBufferView,
                            objType
                        };
                    }
                }
            }
            else
            {
                for (auto& [passName, pass] : fx->GetAllPass())
                {
                    if (!pass.mShaderPtr)
                    {
                        LOG_ERROR("Current pass[{}] shader ptr is invalid from material {}", passName.GetName(), renderMaterial->GetName());
                        continue;
                    }

                    if (auto protoType = pass.mShaderPtr->GetObjectProtoType(); protoType)
                    {
                        auto byteStride = protoType->Layout.ByteSize;
                        auto& allocation = renderUnit->mAllocationInfo.mObjectIndexStarts[passName];
                        if (allocation.mObjectIndexStart == -1 || allocation.mLastAllocateSize != renderUnit->mInstanceCapacity)
                        {
                            auto [objectIndexStart, objectDataBufferView] = gpuScene.Allocate(byteStride, renderUnit->mInstanceCapacity);
                            LOG_ERROR("object index start : {}, instance num : {}\n", objectIndexStart, renderUnit->mInstanceCapacity);
                            allocation = {
                                static_cast<SInt32>(objectIndexStart),
                                byteStride,
                                renderUnit->mInstanceCapacity,
                                objectDataBufferView,
                            };
                        }
                    }
                }
            }
        }
    }

    mIsAllocated = true;
}

void ParticleSystemRenderNode::FreeGpuScene(GPUScene& gpuScene)
{
    SCOPED_CPU_TIMING(GroupParticleSystemRenderNode, "ParticleSystemRenderNodeFreeGPUScene");

    for (auto& renderUnit : mRenderUnits)
    {
        UInt32 lastAllocateSize = 0u;
        auto& indexStarts = renderUnit->mAllocationInfo.mObjectIndexStarts;
        for (auto iter = indexStarts.begin(); iter != indexStarts.end(); ++iter)
        {
            auto& passObjIdxStart = iter->second;
            if (passObjIdxStart.mObjectIndexStart != -1 && passObjIdxStart.mLastAllocateSize > 0)
            {
                lastAllocateSize = passObjIdxStart.mLastAllocateSize;
                gpuScene.Free(passObjIdxStart.mByteStride, passObjIdxStart.mObjectIndexStart, passObjIdxStart.mLastAllocateSize);
                passObjIdxStart.mObjectIndexStart = -1;
                passObjIdxStart.mLastAllocateSize = 0u;
                passObjIdxStart.mByteStride = 0u;
            }
        }
        indexStarts.clear();

        auto FreeAlloc = [&](GPUSceneAllocationInfo::GPUScenePassAllocationInfo& alloc) {
            if (alloc.mObjectIndexStart != -1)
            {
                gpuScene.Free(alloc.mByteStride, alloc.mObjectIndexStart, alloc.mLastAllocateSize);
                alloc = {-1, 0u, 0u, nullptr, nullptr};
            }
        };

        if (renderUnit->mAllocationInfo.mObjectAlloc.mObjectIndexStart != -1)
            lastAllocateSize = renderUnit->mAllocationInfo.mObjectAlloc.mLastAllocateSize;
        FreeAlloc(renderUnit->mAllocationInfo.mPrimitiveAlloc);
        FreeAlloc(renderUnit->mAllocationInfo.mObjectAlloc);

        

        if (renderUnit->mAllocationInfo.mPrimitiveCullingGUID != -1)
        {
            gpuScene.FreeCullingData(renderUnit->mAllocationInfo.mPrimitiveCullingGUID, renderUnit->mAllocationInfo.mObjectCullingGUIDStart, lastAllocateSize);
            LOG_DEBUG("Free GpuScene mPrimitiveCullingGUID[{}], mObjectCullingGUIDStart[{}], mCullingDataNum[{}]",
                        renderUnit->mAllocationInfo.mPrimitiveCullingGUID,
                        renderUnit->mAllocationInfo.mObjectCullingGUIDStart,
                        lastAllocateSize);
            renderUnit->mAllocationInfo.mPrimitiveCullingGUID = renderUnit->mAllocationInfo.mObjectCullingGUIDStart = -1;
        }
    }

    mIsAllocated = false;
}

void ParticleSystemRenderNode::GenerateDrawUnits(const GenerateDrawUnitsParams& params, REDDrawUnitCollector& collector) const
{
    if (!mIsAllocated)
        return;
    SCOPED_CPU_TIMING(GroupParticleSystemRenderNode, "ParticleSystemRenderNodeGenerateDrawUnits");
    auto& entityData = params.entityData;
    auto& cameraView = params.camera->GetCameraView();
    Float3 cameraWorldPos{cameraView.mInvertViewMatrix.m30, cameraView.mInvertViewMatrix.m31, cameraView.mInvertViewMatrix.m32};
    auto distanceToCamera = Float3::Distance(entityData.worldPosition, cameraWorldPos);

    for (const auto& renderUnit : mRenderUnits)
    {
        if (renderUnit->GetInstanceNumForUpload() == 0)
        {
            continue;
        }

        Assert(renderUnit->mEmitterIndex < mRenderProperties.size());
        auto& renderProperties = mRenderProperties[renderUnit->mEmitterIndex];
        
        for (auto index = 0; index < renderUnit->mMaterials.size(); ++index)
        {
            if (index >= renderUnit->mGeometryList.size())
                continue;
            const GPUSceneAllocationInfo& allocationInfo = renderUnit->mAllocationInfo;
            const auto& drawUnitsDesc = params.drawUnitsDesc;
            //const UInt16 worldRenderGroupLow = drawUnitsDesc.RenderGroupLow;
            //const UInt16 worldRenderGroupHigh = drawUnitsDesc.RenderGroupHigh;
            const auto& passName = drawUnitsDesc.TagName;

            MaterialR* renderMaterial = TYPE_CAST(MaterialR*, renderUnit->mMaterials[index]->GetRenderMaterial());
            MaterialR* finalMaterial = nullptr;
            UInt16 renderGroup;
            if (params.IsDrawable(renderMaterial, finalMaterial, renderGroup))
            {
                const RenderGeometry* geometry = &renderUnit->mGeometryList[index];
                if (!geometry || !geometry->GetGeometryPacket())
                {
                    continue;
                }
                REDDrawUnitFlag flags{};
                flags = mNeedReverseCullingFace ? flags | REDDrawUnitFlag::ReverseFaceOrder : flags;
                UInt32 stateBucketID = REDCulling::CalculateDrawUnitStateBucketID(geometry, finalMaterial, mObjectProperties, passName, flags, 0);

                const GPUSceneBufferView* typedPrimitiveDataBufferView = nullptr;
                const GPUSceneBufferView* typedObjectDataBufferView = nullptr;
                SInt32 objectIndexStart = -1;
                {
                    const auto& objectIndexStarts = allocationInfo.mObjectIndexStarts;
                    auto iter = objectIndexStarts.find(passName);
                    if (iter != objectIndexStarts.end())
                    {
                        objectIndexStart = iter->second.mObjectIndexStart;
                        typedObjectDataBufferView = iter->second.mTypedObjectDataBufferView;
                    }
                    else
                    {
                        typedPrimitiveDataBufferView = allocationInfo.mPrimitiveAlloc.mTypedObjectDataBufferView;
                        typedObjectDataBufferView = allocationInfo.mObjectAlloc.mTypedObjectDataBufferView;
                        objectIndexStart = allocationInfo.mObjectAlloc.mObjectIndexStart;
                    }
                }
                
                // the old version material do not support outlining
                if (objectIndexStart < 0)
                    continue;
                
                Assert(objectIndexStart >= 0);
                UInt32 instanceCount = renderUnit->GetInstanceNumForUpload();
                SInt32 globalInstanceCounterBufferOffset = renderUnit->GetGlobalInstanceCounterBufferOffset();
                
                if (math::InBetween(gRenderGroupOpaque, renderGroup, static_cast<UInt16>(gRenderGroupTransparent - 1u)))
                {
                    auto& drawUnit = collector.AddOpaqueBatchableDrawUnit(
                        renderGroup,
                        geometry,
                        finalMaterial,
                        stateBucketID,
                        typedObjectDataBufferView,
                        typedPrimitiveDataBufferView,
                        REDDrawUnitFlag::Isolated,
                        reinterpret_cast<const void*>(static_cast<uintptr_t>(globalInstanceCounterBufferOffset)),
                        &renderProperties);
                    drawUnit.mSingleRun = {instanceCount, renderUnit->mAllocationInfo.mObjectCullingGUIDStart, objectIndexStart};
                
                }
                else if (math::InBetween(gRenderGroupTransparent, renderGroup, static_cast<UInt16>(gRenderGroupUI - 1u)))
                {
                    auto& drawUnit = collector.AddTransparentBatchableDrawUnit(
                        distanceToCamera,
                        renderGroup,
                        geometry,
                        finalMaterial,
                        stateBucketID,
                        typedObjectDataBufferView,
                        typedPrimitiveDataBufferView,
                        REDDrawUnitFlag::Isolated,
                        reinterpret_cast<const void*>(static_cast<uintptr_t>(globalInstanceCounterBufferOffset)),
                        &renderProperties);
                    drawUnit.mSingleRun = {instanceCount, renderUnit->mAllocationInfo.mObjectCullingGUIDStart, objectIndexStart};
                }
            }
        }
    }
}

void GPUParticleRenderUnit::UploadParticleData(GPUScene& gpuScene, PropertySet& objProps)
{
    auto& allocation = mAllocationInfo.mObjectAlloc;
    Assert(allocation.mProtoType);
    UInt32 objectDataByteSize = allocation.mProtoType->Layout.ByteSize;
    Assert(objectDataByteSize % sizeof(float) == 0);
    UInt32 objectIndex = allocation.mObjectIndexStart;
    objProps.SetProperty(BuiltInProperty::ce_PrimitiveIndex, mAllocationInfo.mPrimitiveAlloc.mObjectIndexStart);

    UInt32 uploadBufferSize = objectDataByteSize * mInstanceCapacity;
    void* uploadBufferDataPtr = gpuScene.UploadData(uploadBufferSize, objectIndex * objectDataByteSize, false);

    for (auto instanceIndex = 0u; instanceIndex < mInstanceCapacity; ++instanceIndex)
    {
        objProps.FillBuffer(allocation.mProtoType->Layout, reinterpret_cast<UInt8*>(uploadBufferDataPtr) + instanceIndex * objectDataByteSize);
    }
}

void CPUParticleRenderUnit::UploadParticleData(GPUScene& gpuScene, PropertySet& objProps)
{
    auto& allocation = mAllocationInfo.mObjectAlloc;
    Assert(allocation.mProtoType);
    UInt32 objectDataByteSize = allocation.mProtoType->Layout.ByteSize;
    Assert(objectDataByteSize % sizeof(float) == 0);
    UInt32 objectIndex = allocation.mObjectIndexStart;
    objProps.SetProperty(BuiltInProperty::ce_PrimitiveIndex, mAllocationInfo.mPrimitiveAlloc.mObjectIndexStart);

    UInt32 uploadBufferSize = objectDataByteSize * mInstanceNum;
    void* uploadBufferDataPtr = gpuScene.UploadData(uploadBufferSize, objectIndex * objectDataByteSize, false);

    for (auto instanceIndex = 0u; instanceIndex < mInstanceNum; ++instanceIndex)
    {
        objProps.FillBuffer(allocation.mProtoType->Layout, reinterpret_cast<UInt8*>(uploadBufferDataPtr) + instanceIndex * objectDataByteSize);

        for (const NGIVariableDesc& member : allocation.mProtoType->Layout.Members)
        {
            if (auto iter = mParticleVariables.find(member.Name); iter != mParticleVariables.end())
            {
                const char* variableName = member.Name.GetName();
                // fill particle_XXX property into uploadBufferData
                if (strncmp(variableName, "ce_", 3) != 0 || !mUseLocalSpace && (strcmp(variableName, "ce_TilePosition") == 0 || strcmp(variableName, "ce_PreTilePosition") == 0))
                {
                    const fx::ParticleVariable& variable = iter->second;
                    Assert(member.Size == static_cast<UInt32>(variable.ParamType));
                    auto dstOffset = instanceIndex * allocation.mProtoType->Layout.ByteSize + member.Offset;
                    auto srcOffset = instanceIndex * mInstanceStride + variable.Offset;
                    memcpy(reinterpret_cast<UInt8*>(uploadBufferDataPtr) + dstOffset, mParticleBuffer.data() + srcOffset, member.Size);
                }
            }
        }
    }
}

void ParticleSystemRenderNode::UploadGPUScene(GPUScene& gpuScene, RenderWorld* renderWorld, ecs::EntityID entity)
{
    SCOPED_CPU_TIMING(GroupParticleSystemRenderNode, "ParticleSystemRenderNodeUploadGPUScene");
    const UInt32 totalInstanceCapacity = GetTotalInstanceCapacity();

    if (totalInstanceCapacity == 0 || !mIsAllocated)
    {
        return;
    }

    auto* transformSys = renderWorld->GetRenderSystem<TransformSystemR>();
    auto [renderPropertyComp, tilePositionComp] = renderWorld->GetComponent<RenderPropertyComponentR, TilePositionComponentR>(entity);
    UInt32 currEngineFrameCount = EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameCount();

    // Upload CullingData
    auto UploadCullingData = [renderWorld, &gpuScene, transformSys, renderPropertyComp, tilePositionComp, currEngineFrameCount](SInt32 primitiveCullingGUID, SInt32 objectCullingGUIDStart, UInt32 instanceNum) {
        
        if (primitiveCullingGUID != -1)
        {
            auto* renderPropertySys = renderWorld->GetRenderSystem<RenderPropertySystemR>();
            CullingProperty cullingProperty = renderPropertySys->GetCullingProperty(renderPropertyComp.Read());
            bool isAlwaysVisible = cullingProperty == CullingProperty::CULLING_PROPERTY_ALWAYS_VISIBLE;
            UInt32 flag = isAlwaysVisible ? 1u : 0u;

            {
                BoundingBox worldBoundingBox;
                Float3 tilePosition = transformSys->GetTilePosition(tilePositionComp.Read());

                PrimitiveCullingData primitiveCullingData{
                    worldBoundingBox.GetCenter(),
                    currEngineFrameCount,
                    worldBoundingBox.GetExtent(),
                    flag,
                    tilePosition,
                    0u,
                };

                ObjectCullingData objectCullingData{
                    Float4x4::Identity(),
                    static_cast<UInt32>(primitiveCullingGUID),
                };

                void* uploadBufferDataPtr = gpuScene.UploadData(sizeof(PrimitiveCullingData), primitiveCullingGUID * sizeof(PrimitiveCullingData));
                memcpy(uploadBufferDataPtr, &primitiveCullingData, sizeof(PrimitiveCullingData));

                const auto objectCullingDataStride = sizeof(ObjectCullingData);
                uploadBufferDataPtr = gpuScene.UploadData(objectCullingDataStride * instanceNum, objectCullingGUIDStart * objectCullingDataStride, false);
                //LOG_ERROR("update culling data : totalInstanceNum[{}], mCullingDataNum[{}], cullingDataStride[{}], mCullingGUIDStart[{}]", totalInstanceNum, mCullingDataNum, cullingDataStride, mCullingGUIDStart);
                for (auto i = 0u; i < instanceNum; ++i)
                {
                    memcpy(reinterpret_cast<UInt8*>(uploadBufferDataPtr) + i * objectCullingDataStride, &objectCullingData, objectCullingDataStride);
                }
            }
        }
    };

    for (const auto& renderUnit : mRenderUnits)
    {
        if (renderUnit->mInstanceCapacity)
        {
            Assert(renderUnit->mAllocationInfo.mPrimitiveCullingGUID >= 0 && renderUnit->mAllocationInfo.mObjectCullingGUIDStart >= 0);
            UploadCullingData(renderUnit->mAllocationInfo.mPrimitiveCullingGUID, renderUnit->mAllocationInfo.mObjectCullingGUIDStart, renderUnit->mInstanceCapacity);
        }

        if (!renderUnit->HasDataToUpload())
        {
            continue;
        }
        auto pool = EngineGlobal::Inst().GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetRenderingExecutionDescriptor()->GetREDFrameAllocator();
        PropertySet objProps{pool};
        const GPUSceneAllocationInfo& allocationInfo = renderUnit->mAllocationInfo;
        if (!allocationInfo.mObjectIndexStarts.empty())
        {
            auto cpuRenderUnit = dynamic_cast<CPUParticleRenderUnit*>(renderUnit.get());
            Assert(cpuRenderUnit);
            for (auto [passName, passObjIdxStart] : allocationInfo.mObjectIndexStarts)
            {
                for (MaterialInterfacePtr material : renderUnit->mMaterials)
                {
                    if (MaterialR* renderMaterial = TYPE_CAST(MaterialR*, material->GetRenderMaterial()))
                    {
                        const auto* protoType = renderMaterial->GetShader(passName)->GetObjectProtoType();
                        if (!protoType)
                        {
                            continue;
                        }
                        UInt32 objectDataByteSize = protoType->Layout.ByteSize;
                        Assert(objectDataByteSize % sizeof(float) == 0);
                        UInt32 uploadBufferSize = objectDataByteSize * cpuRenderUnit->mInstanceNum;

                        UInt32 objectIndex = passObjIdxStart.mObjectIndexStart;
                        void* uploadBufferDataPtr = gpuScene.UploadData(uploadBufferSize, objectIndex * objectDataByteSize, false);
                        //memset(uploadBufferDataPtr, 0, uploadBufferSize);

                        if (!cpuRenderUnit->mUseLocalSpace)
                        {
                            objProps.SetProperty(BuiltInProperty::ce_World, Float4x4::Identity());
                            objProps.SetProperty(BuiltInProperty::ce_InvWorld, Float4x4::Identity());
                            objProps.SetProperty(BuiltInProperty::ce_PreWorld, Float4x4::Identity());
                        #if defined(CE_USE_DOUBLE_TRANSFORM)
                            //objProps.SetProperty(BuiltInProperty::ce_TilePosition, mWorldTransform.TilePosition);
                            //objProps.SetProperty(BuiltInProperty::ce_PreTilePosition, mWorldTransform.PreTilePosition);
                        #endif
                            objProps.SetProperty(BuiltInProperty::ce_InvWorld, Float4x4::Identity());
                            objProps.SetProperty(BuiltInProperty::ce_InvTransposeWorld, Float4x4::Identity());
                            objProps.SetProperty(BuiltInProperty::ce_InvTransposeInvWorld, Float4x4::Identity());
                        }
                        else
                        {
                            objProps.SetProperty(BuiltInProperty::ce_World, mWorldTransform.RelativeMatrix);
                            objProps.SetProperty(BuiltInProperty::ce_InvWorld, mWorldTransform.RelativeMatrix.Inverted());
                            objProps.SetProperty(BuiltInProperty::ce_PreWorld, mWorldTransform.PreRelativeMatrix);
                         #if defined(CE_USE_DOUBLE_TRANSFORM)
                            objProps.SetProperty(BuiltInProperty::ce_TilePosition, mWorldTransform.TilePosition);
                            objProps.SetProperty(BuiltInProperty::ce_PreTilePosition, mWorldTransform.PreTilePosition);
                         #endif
                            objProps.SetProperty(BuiltInProperty::ce_InvWorld, mWorldTransform.RelativeMatrix.Inverted());
                            objProps.SetProperty(BuiltInProperty::ce_InvTransposeWorld, mWorldTransform.RelativeMatrix.Inverted().Transpose());
                            objProps.SetProperty(BuiltInProperty::ce_InvTransposeInvWorld, mWorldTransform.RelativeMatrix.Inverted().Transpose().Inverted());
                        }

                        for (auto instanceIndex = 0u; instanceIndex < cpuRenderUnit->mInstanceNum; ++instanceIndex)
                        {
                            objProps.FillBuffer(protoType->Layout, reinterpret_cast<UInt8*>(uploadBufferDataPtr) + instanceIndex * protoType->Layout.ByteSize);
                            for (const NGIVariableDesc& member : protoType->Layout.Members)
                            {
                                if (auto iter = cpuRenderUnit->mParticleVariables.find(member.Name); iter != cpuRenderUnit->mParticleVariables.end())
                                {
                                    const char* variableName = member.Name.GetName();
                                    // fill particle_XXX property into uploadBufferData
                                    if (strncmp(variableName, "ce_", 3) != 0 || !cpuRenderUnit->mUseLocalSpace && (strcmp(variableName, "ce_TilePosition") == 0 || strcmp(variableName, "ce_PreTilePosition") == 0))
                                    {
                                        const fx::ParticleVariable& variable = iter->second;
                                        Assert(member.Size == static_cast<UInt32>(variable.ParamType));
                                        auto dstOffset = instanceIndex * protoType->Layout.ByteSize + member.Offset;
                                        auto srcOffset = instanceIndex * cpuRenderUnit->mInstanceStride + variable.Offset;
                                        memcpy(reinterpret_cast<UInt8*>(uploadBufferDataPtr) + dstOffset, cpuRenderUnit->mParticleBuffer.data() + srcOffset, member.Size);
                                    }
                                }
                            }
                        }

                         //const UInt32 dataSetSize = static_cast<UInt32>(renderUnit.mParticleBuffer.size() / sizeof(float));
                         //std::vector<float> datasetView(dataSetSize);
                         //memcpy(datasetView.data(), renderUnit.mParticleBuffer.data(), dataSetSize * 4);
                         //LOG_DEBUG("uploadBufferDataPtr dataset view : {}", fmt::join(datasetView, ", "));

                         //std::vector<float> uploadView(uploadBufferSize / sizeof(float));
                         //memcpy(uploadView.data(), uploadBufferDataPtr, uploadBufferSize);
                         //LOG_DEBUG("uploadBufferDataPtr upload view : {}", fmt::join(uploadView, ", "));
                    }
                }
            }
        }
        else
        {
            {
                auto& allocation = renderUnit->mAllocationInfo.mPrimitiveAlloc;
                Assert(allocation.mProtoType);
                auto byteStride = allocation.mProtoType->Layout.ByteSize;
                PropertySet primitivePropertySet(pool);
                for (auto& [name, val] : renderUnit->mPrimitivePropertySet)
                {
                    primitivePropertySet.SetProperty(name, val);
                }
                if (!renderUnit->mUseLocalSpace)
                {
                    primitivePropertySet.SetProperty(BuiltInProperty::ce_World, Float4x4::Identity());
                    primitivePropertySet.SetProperty(BuiltInProperty::ce_InvWorld, Float4x4::Identity());
                    primitivePropertySet.SetProperty(BuiltInProperty::ce_PreWorld, Float4x4::Identity());
                }
                else
                {
                    primitivePropertySet.SetProperty(BuiltInProperty::ce_World, mWorldTransform.RelativeMatrix);
                    primitivePropertySet.SetProperty(BuiltInProperty::ce_InvWorld, mWorldTransform.RelativeMatrix.Inverted());
                    primitivePropertySet.SetProperty(BuiltInProperty::ce_PreWorld, mWorldTransform.PreRelativeMatrix);
#if defined(CE_USE_DOUBLE_TRANSFORM)
                    primitivePropertySet.SetProperty(BuiltInProperty::ce_TilePosition, mWorldTransform.TilePosition);
                    primitivePropertySet.SetProperty(BuiltInProperty::ce_PreTilePosition, mWorldTransform.PreTilePosition);
#endif
                }
                auto* data = gpuScene.UploadData(byteStride, byteStride * allocation.mObjectIndexStart);
                primitivePropertySet.FillBuffer(allocation.mProtoType->Layout, data);
            }

            renderUnit->UploadParticleData(gpuScene, objProps);
        }
    }
}

ParticleRenderUnit* ParticleSystemRenderNode::GetRenderUnit(UInt32 emitterIndex)
{
    return emitterIndex < mRenderUnits.size() ? mRenderUnits[emitterIndex].get() : nullptr;
}

void ParticleSystemRenderNode::ResizeRenderUnits(UInt32 size)
{
    if (mRenderUnits.size() != size)
    {
        mRenderUnits.resize(size, nullptr);
        mRenderProperties.resize(size, PropertySet(mObjectProperties));
    }
}

bool ParticleSystemRenderNode::UpdateRenderUnit(UInt32 emitterIndex, SimulationType simulationType, ParticleSystemGpuDriven* particleSystemGpuDriven)
{
    if (emitterIndex >= mRenderUnits.size())
        return false;
    if (mRenderUnits[emitterIndex] == nullptr || mRenderUnits[emitterIndex]->mSimulation != simulationType)
    {
        if (simulationType == SimulationType::CPU)
            mRenderUnits[emitterIndex] = std::make_shared<CPUParticleRenderUnit>();
        else
            mRenderUnits[emitterIndex] = std::make_shared<GPUParticleRenderUnit>(particleSystemGpuDriven);
        mRenderUnits[emitterIndex]->mEmitterIndex = emitterIndex;
    }
    return true;
}

void ParticleSystemRenderNode::PostUpdate()
{
    for (const auto& renderUnit : mRenderUnits)
    {
        Assert(renderUnit->mEmitterIndex < mRenderProperties.size());
        auto& renderProperties = mRenderProperties[renderUnit->mEmitterIndex];
        // For outline
        renderProperties.SetProperty(NameID("IS_PARTICLE_PASS"), true);
        if (renderUnit->mRenderType == ParticleRendererType::Sprite)
        {
            renderProperties.SetProperty(NameID("ENABLE_SPRITE_PARTICLE"), true);
            renderProperties.SetProperty(NameID("ENABLE_MESH_PARTICLE"), false);
            // For outline
            renderProperties.SetProperty(NameID("IS_MESH_PARTICLE"), false);
        }
        else
        {
            renderProperties.SetProperty(NameID("ENABLE_MESH_PARTICLE"), true);
            renderProperties.SetProperty(NameID("ENABLE_SPRITE_PARTICLE"), false);
            // For outline
            renderProperties.SetProperty(NameID("IS_MESH_PARTICLE"), true);
        }
        if (renderUnit->mUseLocalSpace)
        {
            renderProperties.SetProperty(magic_enum::enum_name(MaterialUsage::USED_WITH_LOCAL_SPACE_PARTICLE), true);
            renderProperties.SetProperty(magic_enum::enum_name(MaterialUsage::USED_WITH_GLOBAL_SPACE_PARTICLE), false);
            renderProperties.SetProperty(NameID("IS_GLOBAL_PARTICLE"), false);
        }
        else
        {
            renderProperties.SetProperty(magic_enum::enum_name(MaterialUsage::USED_WITH_GLOBAL_SPACE_PARTICLE), true);
            renderProperties.SetProperty(magic_enum::enum_name(MaterialUsage::USED_WITH_LOCAL_SPACE_PARTICLE), false);
            renderProperties.SetProperty(NameID("IS_GLOBAL_PARTICLE"), true);
        }
    }
}

void ParticleSystemRenderNode::Cleanup(GPUScene* gpuScene, fx::SimulationState state)
{
    Assert(gpuScene);
    if (gpuScene)
    {
        FreeGpuScene(*gpuScene);
    }

    switch (state)
    {
    case fx::SimulationState::STOPPED:
        mRenderUnits.clear();
        break;
    case fx::SimulationState::RESTART:
        ResetRenderUnitsBuffer();
        break;
    default:
        break;
    }

    mMaterialPtrs.clear();
    mLayer = 0u;
}

void ParticleSystemRenderNode::NotifyMaterialChange(GPUScene& gpuScene, MaterialR* mtlChanged, RenderNodeSystemR* renderNodeSys, ecs::EntityID entity)
{
    for (const auto& renderUnit : mRenderUnits)
    {
        for (MaterialInterfacePtr material : renderUnit->mMaterials)
        {
            if (MaterialR* renderMaterial = TYPE_CAST(MaterialR*, material->GetRenderMaterial()); renderMaterial == mtlChanged)
            {
                FreeGpuScene(gpuScene);
                AllocateGpuScene(gpuScene);
                gpuScene.SetGPUSceneDirty(entity);
            }
        }
    }
}

bool ParticleSystemRenderNode::AcquireReadbackBuffer(std::vector<REDResidentBuffer*>& output)
{
    for (const auto& renderUnit : mRenderUnits)
    {
        auto gpuRenderUnit = dynamic_cast<GPUParticleRenderUnit*>(renderUnit.get());
        if (!gpuRenderUnit)
            continue;
        
        if (auto mgr = gpuRenderUnit->mParticleInstanceCountManager)
        {
            output.emplace_back(mgr->GetInstanceCounterBuffer());
        }
    }
    return !output.empty();
}

UInt32 ParticleSystemRenderNode::GetTotalInstanceCapacity() const
{
    return std::accumulate(mRenderUnits.begin(), mRenderUnits.end(), 0, [](UInt32 sum, const std::shared_ptr<ParticleRenderUnit>& renderUnit) { return sum + renderUnit->mInstanceCapacity; });
}

void ParticleSystemRenderNode::ResetRenderUnitsBuffer()
{
    for (auto& renderUnit : mRenderUnits)
    {
        renderUnit->Reset();
    }
}

}   // namespace cross