#pragma once

#include "ModelRenderNode.h"
#include <utility>

namespace cross {

// Use AABB in Mesh for culling
class InstancedStaticModelRenderNode : public ModelRenderNode
{
public:
    InstancedStaticModelRenderNode() { mObjectProperties.SetProperty(magic_enum::enum_name<MaterialUsage::USED_WITH_DEFAULT>(), true); }

    template<typename T, typename = std::enable_if_t<std::is_rvalue_reference_v<T&&>>, typename = std::enable_if_t<std::is_same_v<std::remove_reference_t<T>, RenderModel>>>
    void SetRenderModelInstanced(T&& renderModel, InstanceDataResourcePtr instanceDataResource)
    {
        SetRenderModelInstancedImpl(std::forward<T>(renderModel), instanceDataResource);
    }

    virtual void AllocateGPUScene(GPUScene& GPUScene) override;

    virtual void FreeGPUScene(GPUScene& GPUScene) override;

    virtual void GenerateDrawUnits(const GenerateDrawUnitsParams& params, REDDrawUnitCollector& collector) const override;

    virtual void UploadGPUScene(GPUScene& GPUScene, RenderWorld* renderWorld, ecs::EntityID entity) override;

    void SetDistnaceCulling(const EntityDistanceCulling&);

    void SetGlobalScale(const float);


protected:
    virtual void SetRenderModelImpl(RenderModel&& renderModel) override
    {
        // Call SetRenderMeshesInstanced()
        Assert(false);
    }

    virtual void SetRenderModelInstancedImpl(RenderModel&& renderModel, InstanceDataResourcePtr instanceDataResource);

    // Resource
    InstanceDataResourcePtr mInstanceDataResource;

    // Copy from InstanceDataResource, used to FreeGPUScene
    UInt32 mInstanceCount = 0;
    EntityDistanceCulling mDistanceCulling;
    float mGlobalScale = 1.0f;
};
}   // namespace cross