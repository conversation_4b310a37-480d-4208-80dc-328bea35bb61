#include "EnginePrefix.h"
#include "HierachicalInstancedStaticModel.h"

namespace cross {
ClusterBuilder::ClusterBuilder(InstanceDataResourcePtr instanceDataResource, const BoundingBox& instBox, SInt32 maxInstancesPerLeaf, SInt32 internalNodeBranchingFactor)
    : mInstanceDataResource(instanceDataResource)
    , mInstBox(instBox)
    , mMaxInstancesPerLeaf(maxInstancesPerLeaf)
    , mInternalNodeBranchingFactor(internalNodeBranchingFactor)
{
    if (static_cast<SInt32>(instanceDataResource->mInstanceCount / maxInstancesPerLeaf) < internalNodeBranchingFactor)
    {
        mMaxInstancesPerLeaf = std::clamp(static_cast<SInt32>(instanceDataResource->mInstanceCount / internalNodeBranchingFactor), 1, 1024);
    }
}

void ClusterBuilder::BuildTreeAndResource(float globalScale, SInt32 partitionCount)
{
    BuildTree(globalScale, partitionCount);

    OverrideInstanceDataResource();
}

void ClusterBuilder::Init(float globalScale)
{
    UInt32 instanceCount = mInstanceDataResource->mInstanceCount;

    {
        const UInt8* tranlationDataPtr = mInstanceDataResource->GetInstanceMemberData("Translation").mData.data();
        const UInt32 tranlationDataStride = sizeof(Float3);

        const UInt8* rotationDataPtr = mInstanceDataResource->GetInstanceMemberData("Rotation").mData.data();
        const UInt32 rotationDataStride = sizeof(Float3);

        const UInt8* scaleDataPtr = mInstanceDataResource->GetInstanceMemberData("Scale").mData.data();
        const UInt32 scaleDataStride = sizeof(Float3);

        mInstanceTransforms.reserve(instanceCount);
        mSortPoints.reserve(instanceCount);

        for (UInt32 instanceIndex = 0; instanceIndex < instanceCount; instanceIndex++)
        {
            Float3 translation(reinterpret_cast<const float*>(tranlationDataPtr));
            tranlationDataPtr += tranlationDataStride;

            Float3 rotation(reinterpret_cast<const float*>(rotationDataPtr));
            rotationDataPtr += rotationDataStride;

            Float3 scale(reinterpret_cast<const float*>(scaleDataPtr));
            scaleDataPtr += scaleDataStride;

            Float4x4 instanceToLocal = Float4x4::Compose(scale * globalScale, Quaternion::EulerToQuaternion(rotation), translation);

            mInstanceTransforms.push_back(instanceToLocal);
            mSortIndex.push_back(instanceIndex);
            mSortPoints.push_back(translation);
        }
    }

    mNum = static_cast<SInt32>(mSortIndex.size());
}

void ClusterBuilder::BuildTree(float globalScale, const SInt32 partitionCount)
{
    Assert(partitionCount >= 1);

    Init(globalScale);

    mResult = std::make_unique<ClusterTree>();

    mBranchingFactor = mMaxInstancesPerLeaf;
    if (partitionCount > 1)
        mBranchingFactor *= 2;

    Split(mInstanceDataResource->mInstanceCount);

    auto& sortedInstances = mResult->SortedInstances;
    mResult->SortedInstances = mSortIndex;

    mNumRoot = static_cast<SInt32>(mClusters.size());
    mResult->Nodes.resize(mClusters.size(), ClusterNode(partitionCount));

    std::vector<int> partitionedSortedInstances;
    std::default_random_engine rng(static_cast<unsigned>(std::time(nullptr)));

    //Generator leaves bounding box
    for (SInt32 index = 0; index < mNumRoot; index++)
    {
        ClusterNode& node = mResult->Nodes[index];
        auto first = mClusters[index].Start;
        auto last = mClusters[index].Start + mClusters[index].Num;

        BoundingBox nodeBox(BoundingBox::Flags::MergeIdentity);
        for (SInt32 instanceIndex = first; instanceIndex < last; instanceIndex++)
        {
            const Float4x4& thisInstTrans = mInstanceTransforms[sortedInstances[instanceIndex]];

            BoundingBox thisInstBoxTransformed;
            mInstBox.Transform(thisInstBoxTransformed, thisInstTrans);

            BoundingBox::CreateMerged(nodeBox, nodeBox, thisInstBoxTransformed);
        }

        std::shuffle(sortedInstances.begin() + first, sortedInstances.begin() + last, rng);

        nodeBox.GetMinMax(&node.BoundMin, &node.BoundMax);
    }

    for (SInt32 i = 0; i < partitionCount; i++)
    {
        // auto preSize = partitionedSortedInstances.size();

        for (SInt32 index = 0; index < mNumRoot; index++)
        {
            ClusterNode& node = mResult->Nodes[index];
            auto start = 0;
            auto end = 0;

            if (partitionCount == 1)
            {
                end = mClusters[index].Num;
            }
            else if (partitionCount == 2)
            {
                if (i == 0)
                {
                    end = mClusters[index].Num / 2;
                }
                else
                {
                    start = mClusters[index].Num / 2;
                    end = mClusters[index].Num;
                }
            }
            else if (i == 0)
            {
                end = mClusters[index].Num / 2;
            }
            else if (i < partitionCount - 2)
            {
                auto tmp = 1 << i;
                start = mClusters[index].Num * (tmp - 1) / tmp;
                tmp <<= 1;
                end = mClusters[index].Num * (tmp - 1) / tmp;
            }
            else if (i == partitionCount - 2)
            {
                auto tmp = 1 << i;
                start = mClusters[index].Num * (tmp - 1) / tmp;
                tmp <<= 1;
                end = mClusters[index].Num * (tmp - 1) / tmp;

                if (end < mClusters[index].Num - 1)
                    end = mClusters[index].Num - 1;
            }
            else
            {
                auto tmp = 1 << i;
                start = mClusters[index].Num * (tmp - 1) / tmp;
                if (start < mClusters[index].Num - 1)
                    start = mClusters[index].Num - 1;
                end = mClusters[index].Num;
            }

            start += mClusters[index].Start;
            end = std::min(end, mClusters[index].Num) + mClusters[index].Start;

            if (start < end)
            {
                node.FirstInstance[i] = static_cast<SInt32>(partitionedSortedInstances.size());

                for (SInt32 instanceIndex = start; instanceIndex < end; instanceIndex++)
                {
                    partitionedSortedInstances.push_back(sortedInstances[instanceIndex]);
                }

                node.LastInstance[i] = static_cast<SInt32>(partitionedSortedInstances.size()) - 1;
            }
        }

        // LOG_INFO("{} : {}", i, partitionedSortedInstances.size() - preSize);
    }

    sortedInstances = std::move(partitionedSortedInstances);

    std::vector<SInt32> nodesPerLevel;
    nodesPerLevel.push_back(mNumRoot);

    std::vector<SInt32> inverseSortIndex;
    std::vector<SInt32> remapSortIndex;
    std::vector<SInt32> inverseInstanceIndex;
    std::vector<SInt32> oldInstanceIndex;
    std::vector<SInt32> levelStarts;
    std::vector<SInt32> inverseChildIndex;
    std::vector<ClusterNode> oldNodes;

    while (mNumRoot > 1)
    {
        mSortIndex.clear();
        mSortPoints.clear();
        mSortIndex.resize(mNumRoot);
        mSortPoints.resize(mNumRoot);

        for (SInt32 index = 0; index < mNumRoot; index++)
        {
            mSortIndex[index] = index;
            ClusterNode& node = mResult->Nodes[index];
            mSortPoints[index] = (node.BoundMin + node.BoundMin) * 0.5f;
        }

        mBranchingFactor = mInternalNodeBranchingFactor;
        Split(mNumRoot);

        inverseSortIndex.clear();
        inverseSortIndex.resize(mNumRoot);
        for (SInt32 index = 0; index < mNumRoot; index++)
        {
            inverseSortIndex[mSortIndex[index]] = index;
        }

        {
            // rearrange the instances to match the new order of the old roots
            remapSortIndex.clear();
            remapSortIndex.resize(mNum);
            SInt32 outIndex = 0;

            for (int i = 0; i < partitionCount; i++)
            {
                for (SInt32 index = 0; index < mNumRoot; index++)
                {
                    ClusterNode& node = mResult->Nodes[mSortIndex[index]];
                    if (node.FirstInstance[i] >= 0)
                    {
                        Assert(node.FirstInstance[i] <= node.LastInstance[i]);

                        for (SInt32 instanceIndex = node.FirstInstance[i]; instanceIndex <= node.LastInstance[i]; instanceIndex++)
                        {
                            remapSortIndex[outIndex++] = instanceIndex;
                        }
                    }
                }
            }

            Assert(outIndex == mNum);

            inverseInstanceIndex.clear();
            inverseInstanceIndex.resize(mNum);
            for (SInt32 index = 0; index < mNum; index++)
            {
                inverseInstanceIndex[remapSortIndex[index]] = index;
            }
            for (SInt32 index = 0; index < mResult->Nodes.size(); index++)
            {
                ClusterNode& node = mResult->Nodes[index];
                
                // Assert(node.FirstInstance.size() == partitionCount && node.LastInstance.size() == partitionCount);

                for (int i = 0; i < partitionCount; i++)
                {
                    if (node.FirstInstance[i] >= 0)
                    {
                        Assert(node.FirstInstance[i] <= node.LastInstance[i]);

                        node.FirstInstance[i] = inverseInstanceIndex[node.FirstInstance[i]];
                        node.LastInstance[i] = inverseInstanceIndex[node.LastInstance[i]];

                    }
                    else
                    {
                        Assert(node.FirstInstance[i] < 0 && node.LastInstance[i] < 0);
                    }
                }
            }

            oldInstanceIndex.clear();
            std::swap(oldInstanceIndex, sortedInstances);
            sortedInstances.resize(mNum);
            for (SInt32 index = 0; index < mNum; index++)
            {
                sortedInstances[index] = oldInstanceIndex[remapSortIndex[index]];
            }
        }

        {
            // rearrange the nodes to match the new order of the old roots
            remapSortIndex.clear();
            SInt32 newNum = static_cast<SInt32>(mResult->Nodes.size() + mClusters.size());
            remapSortIndex.resize(newNum);
            levelStarts.clear();
            levelStarts.push_back(static_cast<SInt32>(mClusters.size()));
            for (SInt32 index = 0; index < nodesPerLevel.size() - 1; index++)
            {
                levelStarts.push_back(levelStarts[index] + nodesPerLevel[index]);
            }

            for (SInt32 index = 0; index < mNumRoot; index++)
            {
                ClusterNode& node = mResult->Nodes[mSortIndex[index]];
                remapSortIndex[levelStarts[0]++] = mSortIndex[index];

                SInt32 leftIndex = node.FirstChild;
                SInt32 rightIndex = node.LastChild;
                SInt32 levelIndex = 1;

                while (rightIndex >= 0)
                {
                    SInt32 nextLeftIndex = INT_MAX;
                    SInt32 nextRightIndex = -1;

                    for (SInt32 childIndex = leftIndex; childIndex <= rightIndex; childIndex++)
                    {
                        remapSortIndex[levelStarts[levelIndex]++] = childIndex;
                        SInt32 leftChild = mResult->Nodes[childIndex].FirstChild;
                        SInt32 rightChild = mResult->Nodes[childIndex].LastChild;
                        if (leftChild >= 0 && leftChild < nextLeftIndex)
                        {
                            nextLeftIndex = leftChild;
                        }
                        if (rightChild >= 0 && rightChild > nextRightIndex)
                        {
                            nextRightIndex = rightChild;
                        }
                    }
                    leftIndex = nextLeftIndex;
                    rightIndex = nextRightIndex;
                    levelIndex++;
                }
            }

            inverseChildIndex.clear();
            inverseChildIndex.resize(newNum);
            for (SizeType index = mClusters.size(); index < newNum; index++)
            {
                inverseChildIndex[remapSortIndex[index]] = static_cast<SInt32>(index);
            }
            for (SizeType index = 0; index < mResult->Nodes.size(); index++)
            {
                ClusterNode& node = mResult->Nodes[index];
                if (node.FirstChild >= 0)
                {
                    node.FirstChild = inverseChildIndex[node.FirstChild];
                    node.LastChild = inverseChildIndex[node.LastChild];
                }
            }

            {
                std::swap(oldNodes, mResult->Nodes);
                mResult->Nodes.resize(newNum);
                for (SInt32 index = 0; index < mClusters.size(); index++)
                {
                    mResult->Nodes[index] = ClusterNode(partitionCount);
                    Assert(mResult->Nodes[index].LastInstance[0] < 0);
                }
                for (SInt32 index = 0; index < oldNodes.size(); index++)
                {
                    Assert(inverseChildIndex[index] >= mClusters.size());
                    mResult->Nodes[inverseChildIndex[index]] = oldNodes[index];
                }
            }

            SInt32 oldIndex = static_cast<SInt32>(mClusters.size());
            for (SInt32 index = 0; index < mClusters.size(); index++)
            {
                ClusterNode& node = mResult->Nodes[index];
                node.FirstChild = oldIndex;
                oldIndex += mClusters[index].Num;
                node.LastChild = oldIndex - 1;

                for (SInt32 partition = 0; partition < partitionCount; partition++)
                {
                    auto ptr = node.FirstChild;
                    while (ptr <= node.LastChild && mResult->Nodes[ptr].FirstInstance[partition] < 0)
                        ptr++;

                    if (ptr <= node.LastChild)
                    {
                        node.FirstInstance[partition] = mResult->Nodes[ptr].FirstInstance[partition];

                        ptr = node.LastChild;

                        Assert(node.LastInstance[partition] < 0);
                        while (ptr >= node.FirstChild && mResult->Nodes[ptr].LastInstance[partition] < 0)
                            ptr--;

                        if (ptr >= node.FirstChild)
                            node.LastInstance[partition] = mResult->Nodes[ptr].LastInstance[partition];

                        Assert(node.LastInstance[partition] >= 0 && node.LastInstance[partition] >= node.FirstInstance[partition]);
                    }
                    else
                    {
                        node.LastInstance[partition] = -1;
                    }
                }

                BoundingBox nodeBox(BoundingBox::Flags::MergeIdentity);
                for (SInt32 childIndex = node.FirstChild; childIndex <= node.LastChild; childIndex++)
                {
                    ClusterNode& childNode = mResult->Nodes[childIndex];
                    nodeBox.Encapsulate(childNode.BoundMin);
                    nodeBox.Encapsulate(childNode.BoundMax);
                }
                nodeBox.GetMinMax(&node.BoundMin, &node.BoundMax);
            }

            mNumRoot = static_cast<SInt32>(mClusters.size());
            nodesPerLevel.insert(nodesPerLevel.begin(), mNumRoot);
        }
    }

    mResult->InstanceReorderTable.resize(mNum);

    for (SInt32 index = 0; index < mNum; index++)
    {
        mResult->InstanceReorderTable[sortedInstances[index]] = index;
    }

    if (mResult->Nodes.size() && partitionCount > 1)
    {
        std::vector<SInt32> parentFirstInstance(partitionCount, -1);
        std::vector<SInt32> parentLastInstance(partitionCount, -1);

        MergeRange(0, parentFirstInstance, parentLastInstance, sortedInstances);
    }
}

void ClusterBuilder::OverrideInstanceDataResource()
{
    auto newInstanceMemberDatas = mInstanceDataResource->mInstanceMembers;

    UInt32 instanceCount = mInstanceDataResource->mInstanceCount;

    for (auto i = 0l; i < mInstanceDataResource->mInstanceMembers.size(); ++i)
    {
        auto& srcInstanceMemberData = mInstanceDataResource->mInstanceMembers[i];
        auto& dstInstanceMemberData = newInstanceMemberDatas[i];

        auto stride = srcInstanceMemberData.mData.size() / instanceCount;

        for (UInt32 index = 0; index < instanceCount; index++)
        {
            auto* srcPtr = srcInstanceMemberData.mData.data() + index * stride;
            auto* dstPtr = dstInstanceMemberData.mData.data() + mResult->InstanceReorderTable[index] * stride;
            memcpy(dstPtr, srcPtr, stride);
        }
    }

    mInstanceDataResource->mInstanceMembers = std::move(newInstanceMemberDatas);
    mInstanceDataResource->mClusterNodes = std::move(mResult->Nodes);

    //mInstanceDataResource->Serialize({}, mInstanceDataResource->GetName());
}

void ClusterBuilder::MergeRange(SInt32 index, std::vector<SInt32> parentFirstInstance, std::vector<SInt32> parentLastInstance, std::vector<SInt32>& sortedInstances)
{
    ClusterNode& node = mResult->Nodes[index];

    for (int i = 0; i < parentFirstInstance.size(); i++)
    {
        auto parentCount = parentFirstInstance[i] >= 0 ? parentLastInstance[i] - parentFirstInstance[i] + 1 : 0;
        auto count = node.FirstInstance[i] >= 0 ? node.LastInstance[i] - node.FirstInstance[i] + 1 : 0;
        if (count == 0)
            continue;

        // Ancestor nodes are not less than mMaxInstancesPerLeaf / 2
        if (parentCount == 0)
        {
            // The partition of current node will not divide in child nodes
            if (count < mMaxInstancesPerLeaf / 2)
            {
                parentFirstInstance[i] = node.FirstInstance[i];
                parentLastInstance[i] = node.LastInstance[i];
            }
        }
        else
        {
            // Accept the partition from an ancestor or clear the partition
            if (parentFirstInstance[i] == node.FirstInstance[i])
            {
                node.LastInstance[i] = parentLastInstance[i];
            }
            else
            {
                node.FirstInstance[i] = -1;
                node.LastInstance[i] = -1;
            }
        }
    }

    if (node.FirstChild >= 0)
    {
        for (SInt32 childIndex = node.FirstChild; childIndex <= node.LastChild; childIndex++)
        {
            MergeRange(childIndex, parentFirstInstance, parentLastInstance, sortedInstances);
        }

        BoundingBox nodeBox(BoundingBox::Flags::MergeIdentity);
        for (SInt32 childIndex = node.FirstChild; childIndex <= node.LastChild; childIndex++)
        {
            ClusterNode& childNode = mResult->Nodes[childIndex];
            nodeBox.Encapsulate(childNode.BoundMin);
            nodeBox.Encapsulate(childNode.BoundMax);
        }
        nodeBox.GetMinMax(&node.BoundMin, &node.BoundMax);
    }
    else
    {
        BoundingBox nodeBox(BoundingBox::Flags::MergeIdentity);

        for (SInt32 i = 0; i < node.FirstInstance.size(); i++)
        {
            if (node.FirstInstance[i] < 0)
                continue;
            
            for (SInt32 instanceIndex = node.FirstInstance[i]; instanceIndex <= node.LastInstance[i]; instanceIndex++)
            {
                const Float4x4& thisInstTrans = mInstanceTransforms[sortedInstances[instanceIndex]];

                BoundingBox thisInstBoxTransformed;
                mInstBox.Transform(thisInstBoxTransformed, thisInstTrans);

                BoundingBox::CreateMerged(nodeBox, nodeBox, thisInstBoxTransformed);
            }
        }

        nodeBox.GetMinMax(&node.BoundMin, &node.BoundMax);
    }
}

void ClusterBuilder::Split(SInt32 num)
{
    mClusters.clear();

    Split(0, num - 1);

    std::sort(mClusters.begin(), mClusters.end());
}

void ClusterBuilder::Split(SInt32 start, SInt32 end)
{
    SInt32 numRange = end - start + 1;
    if (numRange <= mBranchingFactor)
    {
        mClusters.push_back(RunPair(start, numRange));
        return;
    }

    BoundingBox clusterBound(BoundingBox::Flags::MergeIdentity);
    for (SizeType index = start; index <= end; index++)
    {
        clusterBound.Encapsulate(mSortPoints[mSortIndex[index]]);
    }

    mSortPairs.clear();

    SInt32 bestAxis = -1;
    float bestAxisValue = -1.0f;
    for (SInt32 axis = 0; axis < 3; axis++)
    {
        Float3 boundMin, boundMax;
        clusterBound.GetMinMax(&boundMin, &boundMax);

        float thisAxisValue = boundMax[axis] - boundMin[axis];
        if (bestAxis == -1 || thisAxisValue > bestAxisValue)
        {
            bestAxis = axis;
            bestAxisValue = thisAxisValue;
        }
    }

    for (SInt32 index = start; index <= end; index++)
    {
        SortPair pair;
        pair.Index = mSortIndex[index];
        pair.d = mSortPoints[pair.Index][bestAxis];
        mSortPairs.push_back(pair);
    }

    std::sort(mSortPairs.begin(), mSortPairs.end());

    for (SInt32 index = start; index <= end; index++)
    {
        mSortIndex[index] = mSortPairs[index - start].Index;
    }

    SInt32 half = numRange / 2;
    SInt32 endLeft = start + half - 1;
    SInt32 startRight = end - half + 1;

    if (numRange & 1)
    {
        if (mSortPairs[half].d - mSortPairs[half - 1].d < mSortPairs[half + 1].d - mSortPairs[half].d)
        {
            endLeft++;
        }
        else
        {
            startRight--;
        }
    }

    Split(start, endLeft);
    Split(startRight, end);
}
}   // namespace cross
