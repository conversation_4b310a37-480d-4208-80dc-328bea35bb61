#pragma once
#include "RenderNode.h"
#include "Runtime/UI/Px/Canvas.h"
#include "RenderEngine/RenderFactory.h"
#include "RenderEngine/RenderMesh.h"

namespace cross {

struct CanvasRenderNode : public RenderNode
{
    std::unique_ptr<MeshR> mRenderMesh;
    std::vector<CanvasItemBatch> mItemBatches;
    std::array<GeometryPacketPtr, CanvasItemTypeCount> mPackets;
    int mLayer{ 0 };

    CanvasRenderNode()
    {
        for (UInt32 i = 0; i < CanvasItemTypeCount; ++i)
        {
            mPackets[i] = RenderFactory::Instance().CreateGeometryPacket();
        }
        mRenderMesh.reset(new MeshR(nullptr));
    }

    void GenerateDrawUnits(const cross::RenderNode::GenerateDrawUnitsParams& params, REDDrawUnitCollector& collector) const override;

    RenderNodeType GetType() const override { return RenderNodeType::Canvas; }
};

}   // namespace cross