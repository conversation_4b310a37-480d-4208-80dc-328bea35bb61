#pragma once

#include "RenderNode.h"
#include "Resource/MeshAssetDataResource.h"
namespace cross {


class NormalRenderNode : public RenderNode
{
public:
    struct RenderNodeLOD
    {
        RenderGeometry* mGeometry;
        MaterialR* mMaterial;
    };

    struct SubMeshRenderNode
    {
        auto& GetLODs()
        {
            return mLODs;
        }

        UInt8 GetLODCount() const
        {
            return static_cast<UInt8>(mLODs.size());
        }

        bool IsReceiveDecals() const
        {
            return mReceiveDecals;
        }

        const RenderGeometry* GetRenderGeometry(UInt8 lodIndex) const
        {
            if (lodIndex >= GetLODCount())
            {
                return nullptr;
            }
            return mLODs[lodIndex].mGeometry;
        }

        MaterialR* GetMaterial(RenderMaterialTag materialTag = 0) const
        {
            size_t lodIndex = static_cast<size_t>(materialTag);
            if (lodIndex < mLODs.size())
            {
                return mLODs[lodIndex].mMaterial;
            }
            return nullptr;
        }

        Float4 GetBoundingSphere() const
        {
            return mBoundingSphere;
        }

        const PropertySet& GetProperties() const
        {
            return mProperties;
        }

        std::vector<RenderNodeLOD> mLODs;
        // need refactor
        Float4 mBoundingSphere;
        bool mReceiveDecals{ true };

        // deprecated, terrain only
        PropertySet mProperties = PropertySet(nullptr);
    };

    virtual void GenerateDrawUnits(const GenerateDrawUnitsParams& params, REDDrawUnitCollector& collector) const override;

    RenderNodeType GetType() const override
    {
        return mType;
    }

    UInt32 GetInstanceCount() const
    {
        return mInstanceCount;
    }

    inline SInt32 GetLodSelected() const
    {
        return mLodSelected;
    }

    inline const resource::MeshAssetLODSetting* GetLODSetting() const
    {
        return mLodSetting;
    }

    void SetLODSettings(const std::vector<const resource::MeshAssetLODSetting*> lodSettings)
    {
        if (lodSettings.size())
        {
            mLodSetting = lodSettings.front();
        }
    }

    void SetLODSelected(SInt32 lodSelected)
    {
        mLodSelected = lodSelected;
    }

    bool GetEnableCameraMask() const override
    {
        return mEnableCameraMask;
    }

private:
    std::vector<SubMeshRenderNode> mRenderNodes;

    // culling guid start
    SInt32 mCullingGUIDStart = -1;
    bool mGPUSceneAllocated{ false };

    // mesh render node only
    UInt8 mLoDBias{ 0 };
    const resource::MeshAssetLODSetting* mLodSetting{ nullptr };

    // foliage only
    RenderNodeType mType = RenderNodeType::Normal;
    UInt32 mInstanceCount;
    float mScreenSizeScale{ 1.f };
    bool mEnableCameraMask { true };

    friend class RenderNodeSystemR;
    friend class FoliageSystemR;
};

// Temp
struct FoliageInfo
{
    using ClusterKey = const RenderNode*;
    UInt32 LODCount;
    UInt8 LODBias;
    const resource::MeshAssetLODSetting* LODSetting;
    ClusterKey mFoliageClusterKey;
    Float4 BoundingSphere;
    const NormalRenderNode::SubMeshRenderNode* RenderNode;
    UInt32 LODIndex;
    SInt32 ObjectIndexStart;
};

}   // namespace cross