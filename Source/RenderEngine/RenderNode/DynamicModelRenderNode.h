#pragma once

#include "ModelRenderNode.h"

namespace cross {

// Use AABB in AABBComponent for culling
class DynamicModelRenderNode : public ModelRenderNode
{
public:
    DynamicModelRenderNode(const EntityDistanceCulling* distanceCulling = nullptr)
        : mDistanceCulling{distanceCulling}
    {
        mObjectProperties.SetProperty(NameID("WRITES_VELOCITY_TO_GBUFFER"), true);
    }

    virtual void AllocateGPUScene(GPUScene& GPUScene) override;

    virtual void FreeGPUScene(GPUScene& GPUScene) override;

    virtual void GenerateDrawUnits(const GenerateDrawUnitsParams& params, REDDrawUnitCollector& collector) const override;

    virtual void UploadGPUScene(GPUScene& GPUScene, RenderWorld* renderWorld, ecs::EntityID entity) override;

    virtual void MarkAsTransformed() override
    {
        const auto isStatic = IsStatic();
        if (isStatic)
            return;
        mObjectProperties.SetProperty(NameID("WRITES_VELOCITY_TO_GBUFFER"), true);
    }

    virtual void ClearTransformState() override
    {
        const auto isStatic = IsStatic();
        mObjectProperties.SetProperty(NameID("WRITES_VELOCITY_TO_GBUFFER"), !isStatic);
    }

    virtual void SetRenderEffect(RenderEffectTag renderEffect) override;

    // ---------------------------------------For RayTracing------------------------------------------
    bool IsValidForRayTracing() const override { return false; }

private:
    SInt32 mPrimitiveCullingGUIDStart = -1;
    SInt32 mObjectCullingGUIDStart = -1;
    const EntityDistanceCulling* mDistanceCulling = nullptr;
};
}   // namespace cross