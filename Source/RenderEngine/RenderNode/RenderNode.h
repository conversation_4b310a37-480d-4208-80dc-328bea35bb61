#pragma once
#include "RenderEngine/RenderingExecutionDescriptor/REDDrawUnitList.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDCulling.h"
#include "RenderEngine/StellarMeshScene/StellarMeshScene.h"
#include "Resource/InstanceDataResource.h"

#pragma warning(push)
#pragma warning(disable : 4100)
#pragma warning(pop)

namespace cross {
struct GPUSceneBufferView;
class GrowOnlySpanAllocator;
class ObjectIndexAllocator;
class ScatterBytesUploadBuffer;
class GPUScene;
class RenderNodeSystemR;
class RayTracingScene;
struct SubInstanceData;

struct RenderNode
{
    RenderNode() { mObjectProperties.SetProperty(NameID("WRITES_VELOCITY_TO_GBUFFER"), false); }

    virtual ~RenderNode() {}

    virtual RenderNodeType GetType() const = 0;

    virtual void SetRenderEffect(RenderEffectTag renderEffect);

    bool IsCastShadow() const
    {
        return EnumHasAnyFlags(mRenderEffect, RenderEffectTag::CastShadow);
    }

    bool IsStatic() const
    {
        return EnumHasAnyFlags(mRenderEffect, RenderEffectTag::IsStatic);
    }

    bool NeedReverseCullingFace() const { return mNeedReverseCullingFace; }

    UInt8 GetLoDBias() const;

    void SetLODBias(UInt8 bias) { mLoDBias = bias; }

    UInt8 GetLODIndex(float* screenRelativeHeight, UInt8 lodCount, const resource::MeshAssetLODSetting* lodSetting) const;

    auto& GetWorldTransform() const
    {
        return mWorldTransform;
    }

    auto& GetObjectProperties() const
    {
        return mObjectProperties;
    }

    auto& GetObjectProperties()
    { 
        return mObjectProperties; 
    }

    inline RenderEffectTag GetRenderEffect() const
    {
        return mRenderEffect;
    }

    auto IsEnabled() const { return mEnabled; }

    void SetEnabled(bool enabled) { mEnabled = enabled; }

    auto& GetLightIDs() const
    {
        return mLightIDs;
    }

    auto& GetReflectionProbeIDWeights() const
    {
        return mRefleProbeIDWeights;
    }

    void SetLightList(const std::vector<ecs::EntityID>& lightIdList)
    {
        mLightIDs = lightIdList;
    }

    template<class T>
    void SetLightList(FrameVector<std::pair<ecs::EntityID, T>>* lightIdAndDistanceList)
    {
        UInt32 lightSize = lightIdAndDistanceList->GetSize();
        mLightIDs.resize(lightSize);

        for (UInt32 i = 0; i < lightSize; ++i)
        {
            mLightIDs[i] = lightIdAndDistanceList->At(i).first;
        }
    }

    void SetReflectionProbeList(FrameVector<std::pair<ecs::EntityID, float>>* rpIdAndWeightList);

    static float GetScaledCulledDistance(float distanceToCamera);

    virtual void MarkAsTransformed()
    {
        mObjectProperties.SetProperty(NameID("WRITES_VELOCITY_TO_GBUFFER"), true);
    }

    virtual void ClearTransformState()
    {
        mObjectProperties.SetProperty(NameID("WRITES_VELOCITY_TO_GBUFFER"), false);
    }

public:
    struct GenerateDrawUnitsParams
    {
        RenderWorld* renderWorld;
        const RenderCamera* camera;
        const RenderCamera* cameraLODSel;
        const REDCullingResult::EntityData& entityData;
        const REDCullingResultDesc& cullingDesc;
        const REDDrawUnitsDesc& drawUnitsDesc;

        bool IsDrawable(MaterialR* material, MaterialR*& finalMaterial, UInt16& renderGroup) const;
    };

    virtual void GenerateDrawUnits(const GenerateDrawUnitsParams& params, REDDrawUnitCollector& collector) const = 0;

    virtual void UploadGPUScene(GPUScene& GPUScene, RenderWorld* renderWorld, ecs::EntityID entity) {}

    virtual void UploadStellarMeshScene(StellarMeshScene& StellarMeshScene, RenderWorld* renderWorld, ecs::EntityID entity) {}

    // --------------------------------------For RayTracing-------------------------------------
    virtual void BuildAccelStruct(RayTracingScene& RayTracingScene, RenderWorld* renderWorld, ecs::EntityID entity) {}
    virtual UInt32 GetSubMeshCount() const { return 0; }
    virtual bool IsValidForRayTracing() const { return false; }
    virtual NGIAccelStruct* GetAccelStruct() const { return nullptr; }
    virtual RenderGeometry* GetRenderGeometry(UInt32 index) { return nullptr; }
    
    // hot reload, fx changed
    virtual void NotifyMaterialChange(GPUScene& GPUScene, MaterialR* mtlChanged, RenderNodeSystemR* renderNodeSys, ecs::EntityID entity) {}

    virtual bool GetEnableCameraMask() const
    {
        return true;
    }

    virtual bool AcquireReadbackBuffer(std::vector<REDResidentBuffer*>& output) { return false; }

    virtual bool IsHeavyRenderNode() const { return false; }

protected:
    PropertySet mObjectProperties = PropertySet(nullptr);

    TRSRenderMatrixType mWorldTransform{Float4x4::Identity()};
    RenderEffectTag mRenderEffect{0};
    bool mNeedReverseCullingFace = false;

    std::vector<std::pair<ecs::EntityID, float>> mRefleProbeIDWeights;
    std::vector<ecs::EntityID> mLightIDs;

    bool mEnabled{ true };
    SInt32 mLodSelected{ -1 };
    UInt8 mLoDBias{0};


    friend class RenderNodeSystemR;
    friend class FoliageSystemR;
    friend class GPUScene;
};

}   // namespace cross