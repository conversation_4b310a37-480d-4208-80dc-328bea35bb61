#pragma once

#include "InstancedStaticModelSystemR.h"
#include "HierachicalInstancedStaticModel.h"

namespace cross {
class HierarchicalInstancedStaticModelRenderNode : public InstancedStaticModelRenderNode
{
public:
    ~HierarchicalInstancedStaticModelRenderNode();

    virtual void GenerateDrawUnits(const GenerateDrawUnitsParams& params, REDDrawUnitCollector& collector) const override;

    void BuildTree();

    void SetMaxInstancesPerLeaf(int value) { mMaxInstancesPerLeaf = value; }

    void SetInternalNodeBranchingFactor(int value) { mInternalNodeBranchingFactor = value; }

    void SetDensityLODCount(int count)
    {
        mPartitionCount = std::clamp((count + 1) >> 1, 1, static_cast<SInt32>(ClusterBuilder::MAX_PARTITION_COUNT));
    }

    void SetDensityLodDistanceScalar(float value)
    {
        mDensityLodDistanceScalar = value;
    }

    void UpdateNodeInstanceCountCache();

    virtual bool IsHeavyRenderNode() const override { return true; }


protected:
    virtual void SetRenderModelInstancedImpl(RenderModel&& renderModel, InstanceDataResourcePtr instanceDataResource) override;


private:
    struct TraverseNode
    {
        SInt32 index;
        SInt32 minLOD;
        SInt32 maxLOD;
        SInt32 minDensityLOD;
        SInt32 maxDensityLOD;
    };

    struct HISMCullInstanceParam
    {
        HISMCullInstanceParam()
        {
        }
        std::variant<BoundingFrustum, BoundingOrientedBox> bounding;
        CameraProjectionMode cameraMode = CameraProjectionMode::Perspective;
        // for each lod , each submodel, store instance range
        std::pmr::vector<std::pmr::vector<REDDrawUnit::Run>> Runs[resource::MAX_MESH_LOD_NUM];
        std::pmr::vector<float> LODDistancePlanes;
        std::pmr::vector<float> DensityLODDistancePlanes;
        std::pmr::vector<std::pair<SInt32, SInt32>> DrawUnitInfos[resource::MAX_MESH_LOD_NUM];
        std::pmr::vector<std::pair<REDDrawUnit*, const SubModel*>> DrawUnits;
        std::pmr::vector<TraverseNode> Stack;
        Float3 CameraRelPosition;

        ~HISMCullInstanceParam();
    };

    void Traverse(HISMCullInstanceParam& param, SInt32 index, SInt32 minLOD, SInt32 maxLOD, SInt32 minDensityLOD, SInt32 maxDensityLOD, bool isFullyContain) const;

    void TraverseLoop(HISMCullInstanceParam& param, TraverseNode rootNode, bool isFullyContain) const;

private:
    BoundingBox mInstanceBoundingBox;
    SInt32 mMaxInstancesPerLeaf = 16;
    SInt32 mInternalNodeBranchingFactor = 16;
    SInt32 mPartitionCount = 1;
    float mDensityLodDistanceScalar = 8;
    std::vector<UInt32> mNodeInstanceCountCache;
    size_t mStackSize = 0;
    using HISMCacheType = std::unordered_map<REDCullingResultDesc, std::unordered_map<REDDrawUnitsDesc, HISMCullInstanceParam, REDCullingResult::DrawUnitDescHasher, REDCullingResult::DrawUnitDescHasher>, REDCulling::Hasher, REDCulling::Hasher>;
    mutable std::mutex cacheMutex;
    mutable HISMCacheType hismCacheResults;
};
}   // namespace cross