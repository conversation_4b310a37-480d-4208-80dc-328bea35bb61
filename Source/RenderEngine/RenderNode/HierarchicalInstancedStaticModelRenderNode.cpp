#include "EnginePrefix.h"
#include "HierarchicalInstancedStaticModelRenderNode.h"

namespace cross {

HierarchicalInstancedStaticModelRenderNode::~HierarchicalInstancedStaticModelRenderNode()
{
    std::scoped_lock lock(cacheMutex);
    HISMCacheType tmp;
    hismCacheResults.swap(tmp);

    for (auto& [_, map] : tmp)
    {
        for (auto& [__, param] : map)
        {
            std::pmr::vector<std::pair<REDDrawUnit*, const SubModel*>> DrawUnits;
            std::pmr::vector<TraverseNode> Stack;
            std::pmr::vector<float> LODDistancePlanes;
            std::pmr::vector<float> DensityLODDistancePlanes;

            param.DrawUnits.swap(DrawUnits);
            param.Stack.swap(Stack);
            param.LODDistancePlanes.swap(LODDistancePlanes);
            param.DensityLODDistancePlanes.swap(DensityLODDistancePlanes);

            for (int i = 0; i < resource::MAX_MESH_LOD_NUM; i++)
            {
                std::pmr::vector<std::pmr::vector<REDDrawUnit::Run>> runs;
                std::pmr::vector<std::pair<SInt32, SInt32>> infos;
                param.Runs[i].swap(runs);
                param.DrawUnitInfos[i].swap(infos);
            }
        }
    }
}

void HierarchicalInstancedStaticModelRenderNode::SetRenderModelInstancedImpl(RenderModel&& renderModel, InstanceDataResourcePtr instanceDataResource)
{
    InstancedStaticModelRenderNode::SetRenderModelInstancedImpl(std::move(renderModel), instanceDataResource);

    // Combine submodel BoundingBox
    mInstanceBoundingBox = BoundingBox(BoundingBox::Flags::MergeIdentity);
    for (auto& singleLODModel : mRenderModel.mLODModels)
    {
        for (auto& subModel : singleLODModel.mSubModels)
        {
            BoundingBox::CreateMerged(mInstanceBoundingBox, mInstanceBoundingBox, subModel.mBoundingBox);
        }
    }

    if (instanceDataResource->mClusterNodes.empty())
    {
        BuildTree();
    }
}

void HierarchicalInstancedStaticModelRenderNode::UpdateNodeInstanceCountCache()
{
    mNodeInstanceCountCache.resize(mInstanceDataResource->mClusterNodes.size());
    for (int i = 0; i < mNodeInstanceCountCache.size(); i++)
        mNodeInstanceCountCache[i] = mInstanceDataResource->mClusterNodes[i].GetInstanceCount(mPartitionCount);

    auto allocator = EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameAllocator();
    std::pmr::vector<SInt32> stack(allocator);

    size_t size = 0;
    stack.push_back(0);
    while (!stack.empty())
    {
        size = std::max(size, stack.size());

        auto index = stack.back();
        stack.pop_back();
        const ClusterNode& node = mInstanceDataResource->mClusterNodes[index];

        if (node.FirstChild >= 0)
        {
            for (SInt32 childIndex = node.FirstChild; childIndex <= node.LastChild; childIndex++)
            {
                stack.push_back(childIndex);
            }
        }
    }

    mStackSize = size;
}

void HierarchicalInstancedStaticModelRenderNode::GenerateDrawUnits(const GenerateDrawUnitsParams& params, REDDrawUnitCollector& collector) const
{
    SCOPED_CPU_TIMING(GroupRendering ,"HISM::GenerateDrawUnits");
    //std::string  pass_desc = params.drawUnitsDesc.TagName.GetName();
    //QUICK_SCOPED_CPU_TIMING_DYNAMIC(pass_desc);

    if (mInstanceCount == 0)
    {
        return;
    }

    HISMCullInstanceParam* cache_params = nullptr;

    {
        std::scoped_lock lock(cacheMutex);
        auto outer_it = hismCacheResults.find(params.cullingDesc);
        if (outer_it == hismCacheResults.end())
        {
            outer_it = hismCacheResults.emplace(params.cullingDesc, std::unordered_map<REDDrawUnitsDesc, HISMCullInstanceParam, REDCullingResult::DrawUnitDescHasher, REDCullingResult::DrawUnitDescHasher>()).first;
        }

        auto& inner_map = outer_it->second;

        auto inner_it = inner_map.find(params.drawUnitsDesc);
        if (inner_it == inner_map.end())
        {
            inner_it = inner_map.emplace(params.drawUnitsDesc, HISMCullInstanceParam()).first;
        }

        cache_params = &inner_it->second;
    }

    HISMCullInstanceParam & hismParam = *cache_params;

    // We assume all SubModels have same LOD count
    const size_t lodCount = mRenderModel.mLODModels.size();
    const SInt32 densityLodCount = std::max(1, 2 * mPartitionCount - 1);

   

    // LOD distance planes
    {
        const float boundingSphereRadius = mInstanceBoundingBox.GetExtent().Length();
        auto& projMatrix = params.cameraLODSel->GetProjMatrix();

        hismParam.LODDistancePlanes.resize(lodCount);
        float finalCulling = mDistanceCulling.maxCullingDistance > 1e-5 ? mDistanceCulling.maxCullingDistance : FLT_MAX;
        for (size_t lodIndex = 1; lodIndex < lodCount; lodIndex++)
        {
            const float t = (std::max)(projMatrix.m00, projMatrix.m11) * 0.5f;

            // screenSize = t * boundingSphereRadius / max(1.0f, dist) * 2.0f
            const float lodScreenSize = mLodSetting->mLevelSettings[lodIndex-1].mScreenReleativeTransitionHeight;
            hismParam.LODDistancePlanes[lodIndex - 1] = MathUtils::Min(finalCulling, t * boundingSphereRadius * 2.0f / lodScreenSize);
        }
        hismParam.LODDistancePlanes[lodCount - 1] = finalCulling;

        hismParam.DensityLODDistancePlanes.resize(densityLodCount);

        for (size_t lodIndex = 1; lodIndex <= hismParam.DensityLODDistancePlanes.size(); lodIndex++)
        {
            const float t = (std::max)(projMatrix.m00, projMatrix.m11) * 0.5f;

            const float lodScreenSize = mLodSetting->mLevelSettings[lodIndex - 1].mScreenReleativeTransitionHeight;
            hismParam.DensityLODDistancePlanes[lodIndex - 1] = MathUtils::Min(finalCulling, t * boundingSphereRadius * mDensityLodDistanceScalar / lodScreenSize);
        }
    }

    //set cluster node bounding box test to hism local coordinate
    BoundingFrustum frustum = params.camera->GetFrustum();
    BoundingOrientedBox orthBox = params.camera->GetOrthBox();
    Float4x4 world2hism = mWorldTransform.RelativeMatrix.Inverted();
    if (params.camera->GetProjectionMode() == CameraProjectionMode::Perspective)
    {
        frustum.Transform(frustum, params.camera->GetInvertViewMatrix());
        frustum.Transform(frustum, 1, Quaternion::Identity(), (params.camera->GetTilePosition() - mWorldTransform.TilePosition) * LENGTH_PER_TILE);
        frustum.Transform(frustum, world2hism);
        hismParam.cameraMode = CameraProjectionMode::Perspective;
        hismParam.bounding = frustum;
    }
    else
    {
        orthBox.Transform(orthBox, params.camera->GetInvertViewMatrix());
        orthBox.Transform(orthBox, (params.camera->GetTilePosition() - mWorldTransform.TilePosition) * LENGTH_PER_TILE);
        orthBox.Transform(orthBox, world2hism);
        hismParam.cameraMode = CameraProjectionMode::Orthogonal;
        hismParam.bounding = orthBox;
    }  
    hismParam.CameraRelPosition = Float4x4::TransformPointF3(world2hism,
                                                          GetLargeCoordinateReltvPosition(params.cameraLODSel->GetCameraOrigin(), params.cameraLODSel->GetTilePosition(), mWorldTransform.TilePosition));

    for (UInt8 lodIndex = 0; lodIndex < lodCount; lodIndex++)
    {

        //hismParam.Runs[lodIndex].reserve(mRenderModel.mLODModels[lodIndex].mSubModels.size());
        hismParam.DrawUnitInfos[lodIndex].clear();

        int count = 0;

        for (auto& subModel : mRenderModel.mLODModels[lodIndex].mSubModels)
        {
            //auto instance_number ="InstanceCount"+ std::to_string(mInstanceCount);
            //SCOPED_CPU_TIMING_DYNAMIC(GroupRendering, instance_number);
            if (subModel.mGeometry && subModel.mMaterial)
            {
                MaterialR* finalMaterial;
                UInt16 renderGroup;
                if (params.IsDrawable(subModel.mMaterial, finalMaterial, renderGroup))
                {
                    hismParam.DrawUnitInfos[lodIndex].emplace_back(subModel.mObjectCullingGUIDStart, subModel.GPUSceneAlloc.ObjectAlloc->IndexStart);
                    if (hismParam.Runs[lodIndex].size() <= count)
                    {
                        hismParam.Runs[lodIndex].emplace_back();
                    }
                    // add a small value to avoid fequent allocation
                    // will permant this vector for better performance
                    hismParam.Runs[lodIndex][count].reserve(std::max(mInstanceCount / 2048, 8192u));
                    
                    hismParam.Runs[lodIndex][count].resize(0);
                    count += 1;
                }
            }
        }
    }

    {
        SCOPED_CPU_TIMING(GroupRendering, "HISM::Traverse");

        TraverseLoop(hismParam, TraverseNode{0, 0, static_cast<SInt32>(lodCount), 0, densityLodCount}, false);
        // Traverse(hismParam, 0, 0, static_cast<SInt32>(lodCount), 0, mPartitionCount, false);
    }

    REDDrawUnitFlag flags{};


    // hism need not instancing
    flags |= REDDrawUnitFlag::Isolated;

    if (mRenderModel.mReceiveDecals)
    {
        flags |= REDDrawUnitFlag::ReceiveDecal;
    }

    if (mNeedReverseCullingFace)
    {
        flags |= REDDrawUnitFlag::ReverseFaceOrder;
    }

    // for each render node in entity


    for (UInt8 lodIndex = 0; lodIndex < lodCount; lodIndex++)
    {
        hismParam.DrawUnits.clear();

        //std::string range_count_1 = "InstanceDrawUnit" + std::to_string(mRenderModel.mLODModels[lodIndex].mSubModels.size());
        //SCOPED_CPU_TIMING_DYNAMIC(GroupRendering, range_count_1);

        for (auto& subModel : mRenderModel.mLODModels[lodIndex].mSubModels)
        {
            if (subModel.mGeometry && subModel.mMaterial)
            {
                auto& drawUnitsDesc = params.drawUnitsDesc;
                const auto& passName = drawUnitsDesc.TagName;

                MaterialR* finalMaterial;
                UInt16 renderGroup;
                if (params.IsDrawable(subModel.mMaterial, finalMaterial, renderGroup))
                {
                    UInt32 stateBucketID = REDCulling::CalculateDrawUnitStateBucketID(subModel.mGeometry, finalMaterial, mObjectProperties, passName, flags, lodIndex);

                    auto& drawUnit = collector.AddOpaqueBatchableDrawUnit(renderGroup, 
                                                                          subModel.mGeometry, 
                                                                          finalMaterial, 
                                                                          stateBucketID, 
                                                                          subModel.GPUSceneAlloc.ObjectAlloc->BufferView, 
                                                                          subModel.GPUSceneAlloc.PrimitiveAlloc->BufferView, 
                                                                          flags, 
                                                                          this);
                    hismParam.DrawUnits.emplace_back(&drawUnit, &subModel);
                }
            }
        }

        if (hismParam.DrawUnits.size() > 0 && hismParam.Runs[lodIndex].size() > 0)
        {
            auto& lodRuns = hismParam.Runs[lodIndex];
            //std::string range_count = "Instance" + std::to_string(lodRuns.size());
            //SCOPED_CPU_TIMING_DYNAMIC(GroupRendering, range_count);

            //lodRuns.reserve(lodRuns.size() * drawUnits.size());

            for (int i = 0; i < hismParam.DrawUnits.size(); i++)
            {
                std::string run_size = std::to_string(hismParam.Runs[lodIndex][i].size());
                QUICK_SCOPED_CPU_TIMING_DYNAMIC(run_size);
                std::get<0>(hismParam.DrawUnits[i])->mRuns = &hismParam.Runs[lodIndex][i];
            }
        }
    }
}

void HierarchicalInstancedStaticModelRenderNode::BuildTree()
{
    if (mInstanceCount > 0)
    {
        ClusterBuilder builder(mInstanceDataResource, mInstanceBoundingBox, mMaxInstancesPerLeaf, mInternalNodeBranchingFactor);
        builder.BuildTreeAndResource(mGlobalScale, mPartitionCount);
    }
}

HierarchicalInstancedStaticModelRenderNode::HISMCullInstanceParam::~HISMCullInstanceParam()
{
    SCOPED_CPU_TIMING(GroupRendering,"Deconstructor");
}

void HierarchicalInstancedStaticModelRenderNode::Traverse(HISMCullInstanceParam& param, SInt32 index, SInt32 minLOD, SInt32 maxLOD, SInt32 minDensityLOD, SInt32 maxDensityLOD, bool isFullyContain) const
{
    const ClusterNode& node = mInstanceDataResource->mClusterNodes[index];

    const Float3 boundCenter = (node.BoundMin + node.BoundMax) * 0.5f;
    const Float3 boundExtent = (node.BoundMax - node.BoundMin) * 0.5f;
    const BoundingBox bound(boundCenter, boundExtent);

    if (!isFullyContain)
    {
        if (ContainmentType type = 
            (param.cameraMode == CameraProjectionMode::Perspective ? 
                std::get<BoundingFrustum>(param.bounding).Contains(bound) : 
                std::get<BoundingOrientedBox>(param.bounding).Contains(bound))
            )
        {
            isFullyContain = type == ContainmentType::ContainmentContain;
        }
        else
        {
            return;
        }
    }

    if (minLOD != maxLOD || minDensityLOD != maxDensityLOD)
    {
        const Float3 center = bound.GetCenter();
        const float distance = (center - param.CameraRelPosition).Length();
        const float radius = bound.GetExtent().Length();
        const float nearDist = distance - radius;
        const float farDist = distance + radius;

        if (minLOD != maxLOD)
        {
            while (minLOD < maxLOD && nearDist > param.LODDistancePlanes[minLOD])
            {
                minLOD++;
            }

            while (minLOD < maxLOD && farDist < param.LODDistancePlanes[maxLOD - 1])
            {
                maxLOD--;
            }
        }

        // a cpu culling based on cluster node;
        if (minLOD >= param.LODDistancePlanes.size())
        {
            return;
        }

        if (minLOD == maxLOD && minDensityLOD != maxDensityLOD)
        {
            while (minDensityLOD < maxDensityLOD && nearDist > param.DensityLODDistancePlanes[minDensityLOD])
                minDensityLOD++;

            while (minDensityLOD < maxDensityLOD && farDist < param.DensityLODDistancePlanes[maxDensityLOD - 1])
                maxDensityLOD--;

            if (minDensityLOD >= param.DensityLODDistancePlanes.size())
                return;
        }
    }

    const bool isShouldGroup = node.FirstChild < 0 || (mNodeInstanceCountCache[index] < 2);
    const bool isSplit = (!isFullyContain || minLOD < maxLOD || minDensityLOD < maxDensityLOD) && (!isShouldGroup);

    if (!isSplit)
    {
        auto partitionCount = mPartitionCount - minDensityLOD;   //((maxDensityLOD - minDensityLOD) >> 1);
        Assert(partitionCount <= node.FirstInstance.size());

        for (int i = 0; i < partitionCount; i++)
        {
            if (node.FirstInstance[i] >= 0)
            {
                Assert(node.FirstInstance[i] <= node.LastInstance[i]);
                //param.AddRun(minLOD, static_cast<UInt32>(node.FirstInstance[i]), static_cast<UInt32>(node.LastInstance[i]));
            }
            else
                break;
        }

        return;
    }

    assert(node.FirstChild >= 0);
    assert(node.LastChild >= 0);

    if (node.FirstChild >= 0)
    {
        for (SInt32 childIndex = node.FirstChild; childIndex <= node.LastChild; childIndex++)
        {
            Traverse(param, childIndex, minLOD, maxLOD, minDensityLOD, maxDensityLOD, isFullyContain);
        }
    }
}

void HierarchicalInstancedStaticModelRenderNode::TraverseLoop(HISMCullInstanceParam& param, TraverseNode rootNode, bool isFullyContain) const
{
    const SInt32 maxLod = static_cast<SInt32>(mRenderModel.mLODModels.size()) - 1;
    const auto lodBias = GetLoDBias();
    auto& stack = param.Stack;
    stack.clear();

    {
        //auto size = "StackSize" + std::to_string(mStackSize);
        stack.push_back(std::move(rootNode));
    }
    {
        QUICK_SCOPED_CPU_TIMING("LoopStack")
        while (!stack.empty())
        {
            auto traverseNode = std::move(stack.back());
            stack.pop_back();

            const ClusterNode& node = mInstanceDataResource->mClusterNodes[traverseNode.index];

            const Float3 boundCenter = (node.BoundMin + node.BoundMax) * 0.5f;
            const Float3 boundExtent = (node.BoundMax - node.BoundMin) * 0.5f;
            const BoundingBox bound(boundCenter, boundExtent);

            if (!isFullyContain)
            {
                if (ContainmentType type = (param.cameraMode == CameraProjectionMode::Perspective ? std::get<BoundingFrustum>(param.bounding).Contains(bound) : std::get<BoundingOrientedBox>(param.bounding).Contains(bound)))
                {
                    isFullyContain = type == ContainmentType::ContainmentContain;
                }
                else
                {
                    continue;
                }
            }

            if (traverseNode.minLOD != traverseNode.maxLOD || traverseNode.minDensityLOD != traverseNode.maxDensityLOD)
            {
                const Float3 center = bound.GetCenter();
                const float distance = (center - param.CameraRelPosition).Length();
                const float radius = bound.GetExtent().Length();
                const float nearDist = distance - radius;
                const float farDist = distance + radius;

                if (traverseNode.minLOD != traverseNode.maxLOD)
                {
                    while (traverseNode.minLOD < traverseNode.maxLOD && nearDist > param.LODDistancePlanes[traverseNode.minLOD])
                    {
                        traverseNode.minLOD++;
                    }

                    while (traverseNode.minLOD < traverseNode.maxLOD && farDist < param.LODDistancePlanes[traverseNode.maxLOD - 1])
                    {
                        traverseNode.maxLOD--;
                    }
                }

                // a cpu culling based on cluster node;
                if (traverseNode.minLOD >= param.LODDistancePlanes.size())
                {
                    continue;
                }

                if (traverseNode.minLOD == traverseNode.maxLOD && traverseNode.minDensityLOD != traverseNode.maxDensityLOD)
                {
                    while (traverseNode.minDensityLOD < traverseNode.maxDensityLOD && nearDist > param.DensityLODDistancePlanes[traverseNode.minDensityLOD])
                        traverseNode.minDensityLOD++;

                    while (traverseNode.minDensityLOD < traverseNode.maxDensityLOD && farDist < param.DensityLODDistancePlanes[traverseNode.maxDensityLOD - 1])
                        traverseNode.maxDensityLOD--;
                }
            }

            const bool isShouldGroup = node.FirstChild < 0 || (mNodeInstanceCountCache[traverseNode.index] < 2);
            const bool isSplit = (!isFullyContain || traverseNode.minLOD < traverseNode.maxLOD || traverseNode.minDensityLOD < traverseNode.maxDensityLOD) && (!isShouldGroup);

            if (!isSplit)
            {
                auto lod = std::min(traverseNode.minLOD + lodBias, maxLod);
                    
                auto AddRun = [&param](SInt32 lod, SInt32 start, SInt32 count) {
                    //QUICK_SCOPED_CPU_TIMING("AddRun");
                    UInt32 ptr = 0;
                    for (auto [cullingStart, indexStart] : param.DrawUnitInfos[lod])
                    {
                        param.Runs[lod][ptr].emplace_back(count, cullingStart + start, indexStart + start);
                        ptr++;
                    }
                };

                if (mPartitionCount == 1)
                {
                    AddRun(lod, static_cast<UInt32>(node.FirstInstance[0]), static_cast<UInt32>(node.LastInstance[0] - node.FirstInstance[0] + 1));
                    // param.AddRun(traverseNode.minLOD, static_cast<UInt32>(node.FirstInstance[0]), static_cast<UInt32>(node.LastInstance[0]));
                }
                else
                {
                    traverseNode.minDensityLOD = std::min(traverseNode.minDensityLOD, static_cast<SInt32>(param.DensityLODDistancePlanes.size()) - 1);

                    if (traverseNode.minDensityLOD >= mPartitionCount - 1)
                    {
                        auto partitionId = std::min(traverseNode.minDensityLOD - (mPartitionCount - 1), mPartitionCount - 1);
                        AddRun(lod, static_cast<UInt32>(node.FirstInstance[partitionId]), static_cast<UInt32>(node.LastInstance[partitionId] - node.FirstInstance[partitionId] + 1));

                        // param.AddRun(traverseNode.minLOD, static_cast<UInt32>(node.FirstInstance[partitionId]), static_cast<UInt32>(node.LastInstance[partitionId]));
                    }
                    else
                    {
                        auto partitionCount = mPartitionCount - traverseNode.minDensityLOD;
                        Assert(partitionCount <= node.FirstInstance.size());

                        for (int i = 0; i < partitionCount; i++)
                        {
                            if (node.FirstInstance[i] >= 0)
                            {
                                Assert(node.FirstInstance[i] <= node.LastInstance[i]);
                                AddRun(lod, static_cast<UInt32>(node.FirstInstance[i]), static_cast<UInt32>(node.LastInstance[i] - node.FirstInstance[i] + 1));
                                // param.AddRun(traverseNode.minLOD, static_cast<UInt32>(node.FirstInstance[i]), static_cast<UInt32>(node.LastInstance[i]));
                            }
                        }
                    }
                }

                continue;
            }

            assert(node.FirstChild >= 0);
            assert(node.LastChild >= 0);

            if (node.FirstChild >= 0)
            {
                for (SInt32 childIndex = node.FirstChild; childIndex <= node.LastChild; childIndex++)
                {
                    stack.emplace_back(childIndex, traverseNode.minLOD, traverseNode.maxLOD, traverseNode.minDensityLOD, traverseNode.maxDensityLOD);
                }
            }
        }
    }
    
}
}   // namespace cross