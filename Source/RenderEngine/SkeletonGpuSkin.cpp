#include "EnginePrefix.h"
#include "RenderEngine/SkeletonGpuSkin.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/VertexStreamLayoutPolicy.h"
#include "NativeGraphicsInterface/NGIManager.h"
#include "NativeGraphicsInterface/NGIUtils.h"
#include "CECommon/Common/EngineGlobal.h"

namespace cross 
{

PoseMatricesBufferHandle::PoseMatricesBufferHandle(VertexStreamLayoutSkinParameter const* inSkinParam)
    : Target(inSkinParam->Entity)
    , ModelHandle(inSkinParam->ModelIndex)
    , SkinParam(inSkinParam)
{
    std::string hashStr;
    hashStr.append(std::to_string(Target));
    hashStr.append(std::to_string(ModelHandle));

    mHash = HashFunction::HashString64(hashStr.c_str(), static_cast<SInt32>(hashStr.length()));
}

PoseMatricesBufferHandle::PoseMatricesBufferHandle(PoseMatricesBufferHandle const& other)
    : Target(other.Target)
    , ModelHandle(other.ModelHandle)
    , SkinParam(other.SkinParam)
    , mHash(other.mHash)
{}

std::array<NGIBufferView*, PoseMatricesFrameBuffer::minSkinnedMeshStreamCount> PoseMatricesFrameBuffer::AssembleComputeSkinnedVertexBufferView(ModelComponentR::IndividualModel const* inPrimaryModel, SInt32 inLodlevel)
{
    auto meshDataRes = inPrimaryModel->mAsset->GetAssetData();
    auto meshDataList = inPrimaryModel->mPrivateRenderMesh != nullptr ? inPrimaryModel->mPrivateRenderMesh.get() : inPrimaryModel->mSharedRenderMesh;

    UInt32 meshPartStartIndex = 0;
    UInt32 meshPartLodCount = 0;

    meshDataRes->GetMeshLodInfo(inLodlevel, meshPartStartIndex, meshPartLodCount);
    Assert(meshDataRes->GetAllLodMeshPartCount() >= (meshPartStartIndex + meshPartLodCount));

    cross::RenderGeometry const* lodStartMesh   = &meshDataList->GetRenderGeometry(meshPartStartIndex);
    cross::RenderGeometry const* lodEndMesh     = &meshDataList->GetRenderGeometry(meshPartStartIndex + meshPartLodCount - 1); 
    
    SInt32 vertexIndexStart = lodStartMesh->GetVertexStart();
    SInt32 vertexIndexEnd = lodEndMesh->GetVertexStart() + lodEndMesh->GetVertexCount() - 1;

    return AssembleComputeSkinnedVertexBufferView(inPrimaryModel, vertexIndexStart, vertexIndexEnd);
}

//cross::NGIBufferView* PoseMatricesFrameBuffer::AssembleComputeSkinnedVertexBufferView(ModelComponentR::IndividualModel const* inPrimaryModel, SecondaryModelCompH inSubModelH)
//{
//    auto meshDataList = inPrimaryModel->mPrivateRenderMesh != nullptr ? inPrimaryModel->mPrivateRenderMesh.get() : inPrimaryModel->mSharedRenderMesh;
//    
//    // All RenderGeometry in one IndividualModel should hold same GeometryPacket
//    cross::RenderGeometry const* mesh = &meshDataList->GetRenderGeometry((UInt32)inSubModelH);
//    Assert(mesh->GetGeometryPacket() == meshDataList->GetRenderGeometry(0).GetGeometryPacket());
//
//    SInt32 vertexIndexStart = mesh->GetVertexStart();
//    SInt32 vertexIndexEnd = mesh->GetVertexStart() + mesh->GetVertexCount() - 1;
//
//    return AssembleComputeSkinnedVertexBufferView(inPrimaryModel, vertexIndexStart, vertexIndexEnd);
//}

std::array<NGIBufferView*, PoseMatricesFrameBuffer::minSkinnedMeshStreamCount> PoseMatricesFrameBuffer::AssembleComputeSkinnedVertexBufferView(ModelComponentR::IndividualModel const* inPrimaryModel, UInt32 inVertexStart, UInt32 inVertexEnd)
{
    Assert(inVertexEnd > inVertexStart);

    auto meshDataList = inPrimaryModel->mPrivateRenderMesh != nullptr ? inPrimaryModel->mPrivateRenderMesh.get() : inPrimaryModel->mSharedRenderMesh;

    // All sub model's vertex buffer view for specify lod level
    auto geomPacketPtr = meshDataList->GetRenderGeometry(0).GetGeometryPacket();

    // GPU skinned mesh has at least 5 streams, each stream stores one channel with fixed format
    // 0 for float3 pos, 1 for float3 normal, 2 for float4 tangent, 3 for uint16_4 skeletonID, 4 for float4 skeletonWeight
    // Assert(geomPacketPtr->GetInputLayout().GetStreamCount() >= minSkinnedMeshStreamCount);
    // static const std::unordered_map<UInt8, GraphicsFormat> streamFormatMap = {
    //     {0, GraphicsFormat::R32G32B32_SFloat},
    //     {1, GraphicsFormat::R32G32B32_SFloat},
    //     {2, GraphicsFormat::R32G32B32A32_SFloat},
    //     {3, GraphicsFormat::R16G16B16A16_UInt},
    //     {4, GraphicsFormat::R32G32B32A32_SFloat}
    // };

    std::array<NGIBufferView*, PoseMatricesFrameBuffer::minSkinnedMeshStreamCount> retBufferViews;
    auto* rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    for (UInt8 streamIndex = 0; streamIndex < minSkinnedMeshStreamCount; streamIndex++)
    {
        const auto& streamLayout = geomPacketPtr->GetInputLayout().GetVertexStreamLayout(streamIndex);
        const BufferStream* bufferStream =  geomPacketPtr->GetVertexStream(streamIndex);
        // One channel per stream for bindless access
        Assert(streamLayout.GetChannelCount() == 1);
        AssertMsg(streamLayout.GetVertexStride() > 0, "Failed for create buffer view with invalidate VertexStreamLayout");
        
        NGIBufferViewDesc vertexBufferView;
        vertexBufferView.Usage = NGIBufferUsage::RWStructuredBuffer;
        vertexBufferView.BufferLocation = bufferStream->GetStreamOffset() + streamLayout.GetVertexStride() * inVertexStart;
        vertexBufferView.SizeInBytes = streamLayout.GetVertexStride() * ((UInt64)inVertexEnd - inVertexStart + 1);
        vertexBufferView.StructureByteStride = streamLayout.GetVertexStride();
        retBufferViews[streamIndex] = rdrSys->GetTransientResourceManager()->AllocateBufferView(vertexBufferView, bufferStream->GetGpuBuffer());
    }
    
    return retBufferViews;
}

std::array<NGIBufferView*, PoseMatricesFrameBuffer::minSkinnedMeshStreamCount> PoseMatricesFrameBuffer::AssembleComputeSkinnedVertexBufferView(ModelComponentR::IndividualModel const* inPrimaryModel, UInt32 inMeshPartStart, UInt32 inMeshPartCount, bool isMeshPart) 
{
    auto meshDataRes = inPrimaryModel->mAsset->GetAssetData();
    auto meshDataList = inPrimaryModel->mPrivateRenderMesh != nullptr ? inPrimaryModel->mPrivateRenderMesh.get() : inPrimaryModel->mSharedRenderMesh;

    UInt32 meshPartStartIndex = inMeshPartStart;
    UInt32 meshPartLodCount = inMeshPartCount;

    Assert(meshDataRes->GetAllLodMeshPartCount() >= (meshPartStartIndex + meshPartLodCount) && isMeshPart);

    cross::RenderGeometry const* lodStartMesh = &meshDataList->GetRenderGeometry(meshPartStartIndex);
    cross::RenderGeometry const* lodEndMesh = &meshDataList->GetRenderGeometry(meshPartStartIndex + meshPartLodCount - 1);

    SInt32 vertexIndexStart = lodStartMesh->GetVertexStart();
    SInt32 vertexIndexEnd = lodEndMesh->GetVertexStart() + lodEndMesh->GetVertexCount() - 1;

    return AssembleComputeSkinnedVertexBufferView(inPrimaryModel, vertexIndexStart, vertexIndexEnd);
}

cross::NGIBufferView* PoseMatricesFrameBuffer::AssembleComputeSkinnedPoseBufferView(VertexStreamLayoutSkinParameter const* inSkinParameter, PoseMatricesBufferOffset inPoseBufferOffset, NGIBuffer* inPoseBuffer)
{
    AssertMsg(inSkinParameter != nullptr && (inSkinParameter->SkinningMatrices->GetSize() > 0), 
        "Failed for create pose buffer view with unfit RenderGeometryList");

    NGIBufferViewDesc poseBufferView;
    poseBufferView.SizeInBytes          = inSkinParameter->SkinningMatrices->GetSize() * PoseMatricesFrameBuffer::sMatrixStrideInFloat * sizeof(float);
    poseBufferView.Usage                = NGIBufferUsage::CopyDst | NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::VertexBuffer;
    poseBufferView.BufferLocation       = inPoseBufferOffset;
    poseBufferView.StructureByteStride  = PoseMatricesFrameBuffer::sMatrixStrideInFloat * sizeof(float); 

    auto* rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    return rdrSys->GetTransientResourceManager()->AllocateBufferView(poseBufferView, inPoseBuffer);
}

PoseMatricesFrameBuffer::PoseMatricesFrameBuffer()
    : mCurFrameCount(0)
{}

PoseMatricesFrameBuffer::~PoseMatricesFrameBuffer()
{
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

    for (auto& ptr : mPoseStorageBuffers)
        rendererSystem->DestroyNGIObject(std::move(ptr));

    for (auto& ptr : mPoseStorageBufferViews)
        rendererSystem->DestroyNGIObject(std::move(ptr));
}

void PoseMatricesFrameBuffer::Initialize()
{
    NGIBufferDesc descForBuffer;
    descForBuffer.Size = sPoseBufferArraySize;
    descForBuffer.Usage = NGIBufferUsage::CopyDst | NGIBufferUsage::RWStructuredBuffer;

    NGIBufferViewDesc descForBufferView;
    descForBufferView.SizeInBytes = sPoseBufferArraySize;
    descForBufferView.Usage = NGIBufferUsage::CopyDst | NGIBufferUsage::RWStructuredBuffer;
    descForBufferView.BufferLocation = 0;
    descForBufferView.StructureByteStride = sMatrixStrideInFloat * sizeof(float);

    for (SInt32 index = 0; index < sRingSize; ++index)
    {
        mPoseStorageBuffers[index].reset(GetNGIDevice().CreateBuffer(descForBuffer, "PoseMatricesBuffer"));
        mPoseStorageBufferViews[index].reset(GetNGIDevice().CreateBufferView(mPoseStorageBuffers[index].get(), descForBufferView));
    }
}

void PoseMatricesFrameBuffer::OnRenderFrameBegin()
{
    mCurFrameCount++;

    // Create a staging buffer for skin mesh builder padding mapped pose data before render system updating
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    mStagingBuffer.BufferWrap = rendererSystem->GetScratchBuffer()->AllocateStaging(NGIBufferUsage::CopySrc, sPoseBufferArraySize);
}

void PoseMatricesFrameBuffer::OnRenderFrameEnd()
{
    // Reset staging buffer ptrs
    mStagingBuffer.BufferWrap = {};
    mStagingBuffer.Cursor = {0};
}

PoseStagingBufferH PoseMatricesFrameBuffer::UpdatePoseStagingBuffer(FrameVector<SIMDMatrix>* inPose, FrameVector<MaterialR*>* inAllSubModelMaterials, GeomertySkinnedMode::Type inMode, threading::TaskEventArray& outTaskEventArray)
{
    SCOPED_CPU_TIMING(GroupRendering, "PoseMatricesFrameBuffer::UpdatePoseStagingBuffer");
    UInt32 poseByteSize = inPose->GetSize() * PoseMatricesFrameBuffer::sMatrixStrideInFloat * sizeof(float);
    UInt64 requireByteSize = mStagingBuffer.Cursor * sizeof(float) + poseByteSize;

    outTaskEventArray.Add(
        threading::Dispatch([
            materials = inAllSubModelMaterials, 
            pose = inPose, 
            skinMode = inMode, 
            &BufferWrap = mStagingBuffer.BufferWrap, 
            offsetPerTask = mStagingBuffer.Cursor, 
            rangePerTask = inPose->GetSize() * sMatrixStrideInFloat * sizeof(float),
            floatsPerMatrix = sMatrixStrideInFloat] (auto) 
        {
        SCOPED_CPU_TIMING(GroupRendering, "store pose");
            //
            // GpuGraphicSkin Deprecated 
            /*if (skinMode == GeomertySkinnedMode::Type::GpuGraphicSkin)
            {
                for (auto material = materials->begin(); material != materials->end(); material++)
                {
                    (*material)->SetValueProp(
                        Skin_Senamtic_Graphic_PoseStorageBufferOffset.GetCString(), reinterpret_cast<const UInt8*>(&offsetPerTask), sizeof(UInt32));
                
                    (*material)->SetValueProp(
                        Skin_Senamtic_Graphic_PoseStorageBufferRange.GetCString(), reinterpret_cast<const UInt8*>(&rangePerTask), sizeof(UInt32));
                }
            }*/

            //
            for (UInt32 iMatrix = 0; iMatrix < pose->GetSize(); iMatrix++)
            {
                const auto& currentMat = pose->At(iMatrix);
                BufferWrap.MemWrite(offsetPerTask * sizeof(float) + iMatrix * floatsPerMatrix * sizeof(float), &currentMat, sizeof(currentMat));
            }
        }));

    auto previousUploadBufferOffset = mStagingBuffer.Cursor;
    mStagingBuffer.Cursor = {requireByteSize / sizeof(float)};
    return previousUploadBufferOffset;
}
}
