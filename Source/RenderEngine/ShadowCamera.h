#pragma once
#include "RenderEngine/RenderCamera.h"
#include "CECommon/Geometry/ConvexVolumn.h"
#include "CECommon/Geometry/Sphere.h"

namespace cross {
class DirectionalLightShadowCamera : public RenderCamera
{
public:
    inline LightType GetLightType() const
    {
        return mLightType;
    }

    inline float GetWorldSpaceTexelScale() const
    {
        return mWorldSpaceTexelScale;
    }

    inline float GetSplitFar() const
    {
        return mSplitFar;
    }

    inline float GetFadePlaneOffset() const
    {
        return mFadePlaneOffset;
    }

    inline Float3 GetLightDirection() const
    {
        return mLightDirection;
    }

    inline const Sphere& GetBounds() const
    {
        return mBounds;
    }

    inline const ConvexVolumn& GetBoundsAccurate() const
    {
        return mBoundsAccurate;
    }

    inline float GetShadowBias() const
    {
        return mShadowBias;
    }

    inline float GetShadowSlopeBias() const
    {
        return mShadowSlopeBias;
    }

    inline float GetMaxShadowSlopeBias() const
    {
        return mMaxShadowSlopeBias;
    }

    inline float GetMaxSubjectZ() const
    {
        return mMaxSubjectZ;
    }

    inline float GetMinSubjectZ() const
    {
        return mMinSubjectZ;
    }

    inline const RenderCamera* GetCachedViewCamera() const
    {
        return mCachedViewCamera.has_value() ? &mCachedViewCamera.value() : nullptr;
    }

    inline const Float4x4& GetCachedShadowMatrix() const
    {
        return mCachedPrevShadowMatrix;
    }

    inline const Float3& GetCachedLightDirection() const
    {
        return mCachedLightDirection;
    }

    void CalculateOrthBox();

    float ComputeTransitionSize() const;

    float GetShadowReceiverBias() const;

    void CacheViewCamera(const RenderCamera& viewCamera)
    {
        mCachedViewCamera = viewCamera;
        mCachedPrevShadowMatrix = GetViewProjMatrix();
        mCachedLightDirection = mLightDirection;
    }

    void SetupWholeSceneProjection(Float4x4 lightWorldMatrix, UInt32 resolution);

private:
    void UpdateShadowDepthBias();

    bool IsBoundingBoxVisible(const BoundingBox& boundingBox) const override;

private:
    LightType mLightType;
    Float3 mLightDirection;
    float mLightShadowBias;
    float mLightShadowSlopeBias;

    std::optional<RenderCamera> mCachedViewCamera;
    Float4x4 mCachedPrevShadowMatrix;
    Float3 mCachedLightDirection;

    float mSplitNear;
    float mSplitFar;
    float mFadePlaneOffset;
    float mFadePlaneLength;
    float mCascadeBiasDistribution;
    float mMaxSubjectZ;
    float mMinSubjectZ;
    float mWorldSpaceTexelScale;
    bool isDirectionalLight;

    Sphere mBounds;
    ConvexVolumn mBoundsAccurate;

    float mShadowBias;
    float mShadowSlopeBias;
    float mMaxShadowSlopeBias;

    friend class ShadowSystemR;
};

}   // namespace cross