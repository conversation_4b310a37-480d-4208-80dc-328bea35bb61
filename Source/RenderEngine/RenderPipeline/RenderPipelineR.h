#pragma once
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDCulling.h"
#include "RenderEngine/RenderContext.h"
#include "RenderEngine/RenderWorld.h"
#include "RenderEngine/RenderCamera.h"
#include "CECommon/Common/RenderPipelineSetting.h"
#include "RenderEngine/RenderPipeline/RenderPipelineBaseSetting.h"
#include "RenderEngine/PostProcessVolumeSetting.h"
#include "RenderEngine/RenderPipeline/Effects/PassBase.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RendererSystemR.h"

namespace cross {

class RenderCamera;
class FoliageGpuDriven;
class SkyLightSystemR;
class WorldRenderPipeline;


class RenderPipelineR
{
public:
    void SetCamera(ecs::EntityID camera)
    {
        mCamera = camera;
    }

    ecs::EntityID GetCamera() const
    {
        return mCamera;
    }

    void SetRenderCamera(const RenderCamera* cameraR)
    {
        mRenderCamera = cameraR;
    }

    const RenderCamera* GetRenderCamera() const
    {
        return mRenderCamera;
    }

    void SetCullingRenderCamera(const RenderCamera* cameraCullR)
    {
        mCullingRenderCamera = cameraCullR;
    }

    const RenderCamera* GetCullingRenderCamera() const
    {
        return mCullingRenderCamera;
    }

    const std::vector<ecs::EntityID>& GetLightList() const
    {
        return mLightList;
    }

    const std::vector<UInt32>& GetLightIndexList() const
    {
        return mLightIndexList;
    }

    const std::vector<float>& GetLightScreenRatio() const
    {
        return mLightScreenRatio;
    }

    const std::vector<std::pair<Float3, Float3>>& GetlightViewBound() const
    {
        return mLightViewBound;
    }

    void ClearRefleProbeList();

    void AddVisibleRefleProbe(ecs::EntityID rpEntity);

    const std::vector<ecs::EntityID>& GetRefleProbeList() const
    {
        return mReflecProbeList;
    }

    MaterialR* GetPostProcessMtl() const
    {
        return mPostProcessMtl;
    }

protected:
    ecs::EntityID mCamera{ecs::EntityID::InvalidHandle()};
    const RenderCamera* mRenderCamera{nullptr};
    const RenderCamera* mCullingRenderCamera{nullptr};
    MaterialR* mPostProcessMtl{nullptr};
    std::vector<ecs::EntityID> mLightList;                  // visible lights to this camera
    std::vector<UInt32> mLightIndexList;                    // visible lights' indices in all lights
    std::vector<float> mLightScreenRatio;                   // the screen ratio of visible lights to this camera, same length as mLightList
    std::vector<std::pair<Float3, Float3>> mLightViewBound; // xy: light uv bound, z: linear depth bounding
    std::vector<ecs::EntityID> mReflecProbeList;            // visible rps to this camera
};

enum class ViewType
{
    // Views may not be rendered continuously

    // Used for prefab proxy
    PrefabProxy,
    // only need lighting and tonemapping
    Thumbnail,
    // HDR result without a lot of costly effect
    ReflectionProbe,
    // with most effects, excluding tone mapping and ui
    HDRSceneCapture,
    // with most effects, excluding ui
    SceneCapture,
    // GTAO（后期优化SSAO后替换），SmartGI，Shadow, FSR2
    MaterialEditorPreview,

    // Views be rendered continuously

    // standard result
    GameView,
    // For Editor, no ui, with some gizmo
    SceneView,
    // For selected camera in editor
    ScenePreview,
};

enum class PrimitiveMeshType
{
    FullScreenTriangle,
    FullScreenQuad,
    UnitSphere,
    UnitHemisphere,
    UnitCube,
};

enum class PostProcessFlag
{
    ClearColor = 0x1,
    ClearDepth = 0x2,
    ClearStencil = 0x4,
    ClearAll = ClearColor | ClearDepth | ClearStencil,
};

// PostProcess(mMtl, passName, "paran1", value, "param2", value, rt0, rt1, rt2, depthstencilrt, meshType, flag);

template<typename TArg, typename... TArgs>
void PostProcessUnpack(const NameID& ID, TArg&& arg, TArgs&&... args)
{}

template<typename... TArgs>
void PostProcess(MaterialR* mtl, NameID passName, TArgs&&... args)
{
    PostProcessUnpack(std::forward<TArgs>(args)...);
}

struct ShadowProperties;
using PassInoutValue = std::variant<REDTextureView*, REDBufferView*>;
enum class PassSemanticName
{
    DisplayColor,
    DisplayColorProxy,
    DepthOnlyBeforeUpScale,
    DepthStencilBeforeUpScale,
    DisplayDepthOnly,
    DisplayDepthStencil,
    LastPassColor,
    LastPassDepthStencil,
    GBufferViews,
    LastFrameEyeAdpatationTex,
    ExposureTex,
    ExposureTexView,
    SeperateTranslucencyView,
    DepthMapAfterGPassView,
    FSRReactiveMaskView,
    LastFrameDepthStencil,
    DepthStencilTexRenderResolution,
    SceneColorBeforeTransparent,
    LastFrameSceneColorView,
    FSRTransparentReactiveMaskView,
    DirectLightingSceneColor,
    SceneColorBeforeVisualization,
    DBufferViews,
    VisibilityBufferView,
    ExposureTexResult,
    VFogVolumeCutOffDistance
};

struct PipelineBuiltInValue
{
    REDTextureView* mDisplayColor = nullptr;
    REDTextureView* mDisplayColorProxy = nullptr;
    REDTextureView* mDepthOnlyBeforeUpScale = nullptr;
    REDTextureView* mDepthStencilBeforeUpScale = nullptr;
    REDTextureView* mDisplayDepthOnly = nullptr;
    REDTextureView* mDisplayDepthStencil = nullptr;
    REDTextureView* mLastPassColor = nullptr;
    REDTextureView* mLastPassDepthStencil = nullptr;
    std::array<REDTextureView*, 4> mGBufferViews;
    REDTextureView* mLastFrameEyeAdpatationTex = nullptr;
    REDUniquePtr<REDResidentTexture> mExposureTex = nullptr;
    REDUniquePtr<REDResidentTextureView> mExposureTexView = nullptr;
    // REDTexture* mExposureTex = nullptr;
    REDTextureView* mSeperateTranslucencyView = nullptr;
    REDTextureView* mDepthMapAfterGPassView = nullptr;
    REDTextureView* mReactiveMaskView = nullptr;
    REDTexture* mLastFrameDepthStencil = nullptr;
    REDTexture* mDepthStencilTex = nullptr;
    REDTextureView* mSceneColorBeforeTransparentView = nullptr;
    REDTextureView* mLastFrameSceneColorView = nullptr;
    REDTextureView* mTransparentReactiveMaskView = nullptr;
    REDTextureView* mDirectLightingSceneColor = nullptr;
    //REDTextureView* mWireframeColorView = nullptr;
    REDTextureView* mSceneColorBeforeVisualization = nullptr;
    std::array<REDTextureView*, 3> mDBufferViews;
    REDTextureView* mVisibilityBufferView = nullptr;
    Float4 mExposureTexResult;
    float mVFogVolumeCutOffDistance;
};

struct RENDER_ENGINE_API IRenderPipeline : public RenderPipelineR
{
    IRenderPipeline();

    virtual ~IRenderPipeline();

    struct JitterData
    {
        Float2 jitter;         // in pixel space
        Float2 jitterInProj;   // in projection space
        Float4x4 viewMat;
        Float4x4 jitterProjMat;

        Float2 lastJitter;
        Float2 lastJitterInProj;   // in projection space
        Float4x4 lastViewMat;
        Float4x4 lastProjMat;
        Float4x4 lastJitterProjMat;

#ifdef CE_USE_DOUBLE_TRANSFORM
        Float3 tilePosition;
        Float3 lastTilePosition;
#endif

        uint32_t m_index;

        void Initialize()
        {
            m_index = 0;

            jitter = jitterInProj = Float2{0, 0};
            viewMat = Float4x4::Identity();
            jitterProjMat = Float4x4::Identity();

            lastJitter = lastJitterInProj = Float2{0, 0};
            lastViewMat = Float4x4::Identity();
            lastProjMat = Float4x4::Identity();
            lastJitterProjMat = Float4x4::Identity();

#ifdef CE_USE_DOUBLE_TRANSFORM
            tilePosition = {0, 0, 0};
            lastTilePosition = {0, 0, 0};
#endif
        };

        Float4x4 GetReprojectionMatrixNoJitter() const
        {
            auto preProjMat = lastProjMat;
            auto curProjMat = jitterProjMat;
            curProjMat.m20 -= jitterInProj.x, curProjMat.m21 -= jitterInProj.y;

#ifdef CE_USE_DOUBLE_TRANSFORM
            Float4x4 OffsetMat = Float4x4::CreateTranslation((tilePosition - lastTilePosition) * LENGTH_PER_TILE_F);
            return curProjMat.Inverted() * viewMat.Inverted() * OffsetMat * lastViewMat * preProjMat;
#else
            return curProjMat.Inverted() * viewMat.Inverted() * lastViewMat * preProjMat;
#endif
        }

        Float2 GetProjectionOffset() const
        {
            return jitterInProj;
        }

        Float2 GetLastProjectionOffset() const
        {
            return lastJitterInProj;
        }

        Float2 GetOffsetInUVSpace() const
        {
            return Float2{jitterInProj.x * 0.5f, jitterInProj.y * -0.5f};
        }

        Float2 GetLastOffsetInUVSpace() const
        {
            return Float2{lastJitterInProj.x * 0.5f, lastJitterInProj.y * -0.5f};
        }

        void ClearJitter()
        {
            m_index = 0;

            jitterProjMat.m20 -= jitterInProj.x, jitterProjMat.m21 -= jitterInProj.y;
            jitter = jitterInProj = Float2{0, 0};

            lastJitterProjMat.m20 -= lastJitterInProj.x, lastJitterProjMat.m21 -= lastJitterInProj.y;
            lastJitter = lastJitterInProj = Float2{0, 0};
        }
    };

    virtual void Initialize(WorldRenderPipeline* worldRenderPipeline, RenderWorld* world, RenderingExecutionDescriptor* RED)
    {
        mWorldRenderPipeline = worldRenderPipeline;
        mWorld = world;
        mRED = RED;
        mJitterData.Initialize();
        mGameContext.mRenderWorld = mWorld;
        mGameContext.mRenderPipeline = this;
    }

    WorldRenderPipeline* GetWorldRenderPipeline()
    {
        return mWorldRenderPipeline;
    }

    RenderingExecutionDescriptor* GetRenderingExecutionDescriptor() const
    {
        return mRED;
    }

    const RenderWorld* GetWorld() const
    {
        return mWorld;
    }

    // !!!duplicated functions, if you are the author, please remove it
    RenderingExecutionDescriptor* RED() const
    {
        return mRED;
    }

    // !!!duplicated functions, if you are the author, please remove it
    RenderWorld* GetRenderWorld() const
    {
        return mWorld;
    }

    virtual void FreeFGRes()
    {
        
    }

    virtual bool IsEnable() const;

    virtual void PreAssemble();

    virtual void PrepareAssembleRenderContext();

    virtual void Assemble();

    virtual void PrepareRenderData();

    virtual void UpdateRenderContext();

    bool IsMainCamera() const;

    /*
     * colorView: result of render pipeline
     */
    virtual void Assemble(REDTextureView* targetView){};

    void SetTargetView(REDTextureView* view)
    {
        mTargetView = view;
    }
    REDTextureView* GetTargetView()
    {
        return mTargetView;
    }

    UInt2 GetRenderSize() const
    {
        return UInt2{mTargetView->GetWidth(), mTargetView->GetHeight()};
    }


    bool IsUpscaleRender() const
    {
        return mUpscale;
    }

    float GetUpscaleValue() const
    {
        return mUpscaleValue;
    }

    const JitterData* GetJitterData() const
    {
        return &mJitterData;
    }

    void SetRenderPipelineType(ViewType type)
    {
        mType = type;
    }

    ViewType GetRenderPipelineType() const
    {
        return mType;
    }

    bool IsReflectionProbePipeline() const
    {
        return GetRenderPipelineType() == ViewType::ReflectionProbe;
    }

    bool UseReverseZ() const
    {
        return mUseReverseZ;
    }

    // keep it for now, for UI
    PostProcessVolumeSettingR* GetPostProcessVolumeSetting()
    {
        return mPostProcessSetting;
    }

    // keep it for now, for UI
    void UpdateContext(RenderContext& context, RenderWorld* world, MaterialR* mtl = nullptr, bool UIPass = false);

    virtual void UpdateSetting(const RenderPipelineSetting* setting)
    {
        mSetting = setting;
    }

    virtual const RenderPipelineBaseSetting* GetSetting() const
    {
        return dynamic_cast<const RenderPipelineBaseSetting*>(mSetting);
    }

    virtual const LocalLightShadowCacheSettings* GetLocalLightShadowCacheSetting() const
    { 
        return nullptr;
    }

    virtual void UpdatePostProcessSetting(PostProcessVolumeSettingR* setting)
    {
        mPostProcessSetting = setting;
    }

    void SetEditorEffectMaterial(MaterialR* material, MaterialR* materialForGPUSkin)
    {
        mEditorEffectMtl = material;
        mEditorEffectMtlGPUSkin = materialForGPUSkin;
    }

    void SetEditorWireFrameMaterial(MaterialR* material)
    {
        mEditorWireFrameMtl = material;
    }

    void SetUIProcessMaterial(MaterialR* material)
    {
        mUIProcessMtl = material;
    }
    // !!!Deprecated
    void PostProcess(RenderingExecutionDescriptor* red, std::vector<REDTextureView*>& dstViews, MaterialR* material, NameID const& passID, RenderContext&& ctx, bool clearTarget = true);

    // Problem:
    // targets can't exceed 1, crash
    template<typename TFunc, typename... TArgs>
    void PostProcess(TFunc&& onConfig, MaterialR* material, NameID const& passID, bool clearTarget, TArgs&&... targets)
    {
        constexpr static UInt32 dstCount = static_cast<UInt32>(sizeof...(targets));
        constexpr static NGIClearValue clearValue{{0, 0, 0, 0}};
        REDColorTargetDesc renderTargetDescs[dstCount]{};
        NGIRenderPassTargetIndex colorTargetIndices[dstCount]{};

        REDTextureView* dstViews[dstCount]{targets...};

        for (UInt32 index = 0; index < dstCount; index++)
        {
            renderTargetDescs[index] = {
                dstViews[index],
                clearTarget ? NGILoadOp::Clear : NGILoadOp::Load,
                NGIStoreOp::Store,
                clearValue,
            };
            colorTargetIndices[index] = static_cast<NGIRenderPassTargetIndex>(index);
        }

        mRED->BeginRenderPass(passID.GetName(), dstCount, renderTargetDescs, nullptr);

        auto* postSubPass = mRED->AllocateSubRenderPass(passID.GetName(), 0, nullptr, dstCount, colorTargetIndices, REDPassFlagBit{0});
        onConfig(postSubPass);

        REDDrawScreenQuad drawInfo{material, passID};
        postSubPass->DrawScreenQuad(drawInfo);

        mRED->EndRenderPass();
    }

    template<typename TFunc, typename... TArgs>
    void PostProcessWithDepth(TFunc&& onConfig, MaterialR* material, NameID const& passID, bool clearTarget, REDTextureView* depthView, TArgs&&... targets)
    {
        constexpr static UInt32 dstCount = static_cast<UInt32>(sizeof...(targets));
        constexpr static NGIClearValue clearValue{{0, 0, 0, 0}};
        REDColorTargetDesc renderTargetDescs[dstCount]{};
        NGIRenderPassTargetIndex colorTargetIndices[dstCount]{};

        REDTextureView* dstViews[dstCount]{targets...};

        for (UInt32 index = 0; index < dstCount; index++)
        {
            renderTargetDescs[index] = {
                dstViews[index],
                clearTarget ? NGILoadOp::Clear : NGILoadOp::Load,
                NGIStoreOp::Store,
                clearValue,
            };
            colorTargetIndices[index] = static_cast<NGIRenderPassTargetIndex>(index);
        }
      
        REDDepthStencilTargetDesc depthTargetDesc{depthView, NGILoadOp::Load, NGIStoreOp::Store, NGILoadOp::Load, NGIStoreOp::Store, NGIClearValue{0.f}, nullptr, NGIResolveType::DontResolve, NGIResolveType::DontResolve};

        mRED->BeginRenderPass(passID.GetName(), dstCount, renderTargetDescs, &depthTargetDesc);

        auto* postSubPass = mRED->AllocateSubRenderPass(passID.GetName(), 0, nullptr, dstCount, colorTargetIndices, REDPassFlagBit::NeedDepth);
        onConfig(postSubPass);

        REDDrawScreenQuad drawInfo{material, passID};
        postSubPass->DrawScreenQuad(drawInfo);

        mRED->EndRenderPass();
    }

    template<PassSemanticName Semantic>
    inline auto& GetBuiltInTexture()
    {
        return boost::pfr::get<ToUnderlying(Semantic)>(mBuiltinVariable);
    }

    template<PassSemanticName Semantic>
    inline auto& GetBuiltInValue()
    {
        return boost::pfr::get<ToUnderlying(Semantic)>(mBuiltinVariable);
    }
   
    template<typename TFunc, PrimitiveMeshType MeshType = PrimitiveMeshType::FullScreenTriangle>
    void PostProcessDepthStencil(TFunc&& onConfig, MaterialR* material, NameID const& passID, bool clearDepth, bool clearStencil, REDTextureView* depthStencil)
    {
        constexpr static NGIClearValue clearValue{{0, 0}};

        REDDepthStencilTargetDesc depthStencilDesc = {depthStencil, clearDepth ? NGILoadOp::Clear : NGILoadOp::Load, NGIStoreOp::Store, clearStencil ? NGILoadOp::Clear : NGILoadOp::Load, NGIStoreOp::Store, clearValue};

        mRED->BeginRenderPass(passID.GetName(), 0, nullptr, &depthStencilDesc);

        auto* postSubPass = mRED->AllocateSubRenderPass(passID.GetName(), 0, nullptr, 0, nullptr, REDPassFlagBit::NeedDepth | REDPassFlagBit::NeedStencil);
        onConfig(postSubPass);

        PropertySet mObjCtx(mRED->GetREDFrameAllocator());
        auto* rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<cross::RendererSystemR>();
        auto* rdrPrim = rdrSys->GetRenderPrimitives();

        if constexpr (MeshType == PrimitiveMeshType::FullScreenTriangle)
        {
            REDDrawScreenQuad drawInfo{material, passID};
            postSubPass->DrawScreenQuad(drawInfo);
        }
        else if constexpr (MeshType == PrimitiveMeshType::FullScreenQuad)
        {
            REDDrawMesh drawMeshInfo{material,
                                     passID,
                                     std::nullopt,
                                     std::nullopt,
                                     std::nullopt,
                                     std::nullopt,
                                     mObjCtx,
                                     &rdrPrim->mPostProcessInputLayoutDesc,
                                     1,
                                     {
                                         rdrPrim->GetSixVertsQuad(),
                                     },
                                     nullptr,
                                     6,
                                     1};
            postSubPass->DrawMesh(drawMeshInfo);
        }

        mRED->EndRenderPass();
    }

    template<typename TFunc, PrimitiveMeshType MeshType = PrimitiveMeshType::FullScreenTriangle>
    void PostProcessDepthOnly(TFunc&& onConfig, MaterialR* material, NameID const& passID, bool clearDepth, bool clearStencil, REDTextureView* depthStencil)
    {
        constexpr static NGIClearValue clearValue{{0, 0}};

        REDDepthStencilTargetDesc depthStencilDesc = {depthStencil, clearDepth ? NGILoadOp::Clear : NGILoadOp::Load, NGIStoreOp::Store, clearStencil ? NGILoadOp::Clear : NGILoadOp::Load, NGIStoreOp::Store, clearValue};

        mRED->BeginRenderPass(passID.GetName(), 0, nullptr, &depthStencilDesc);

        auto* postSubPass = mRED->AllocateSubRenderPass(passID.GetName(), 0, nullptr, 0, nullptr, REDPassFlagBit::NeedDepth);
        onConfig(postSubPass);

        PropertySet mObjCtx(mRED->GetREDFrameAllocator());
        auto* rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<cross::RendererSystemR>();
        auto* rdrPrim = rdrSys->GetRenderPrimitives();

        if constexpr (MeshType == PrimitiveMeshType::FullScreenTriangle)
        {
            REDDrawScreenQuad drawInfo{material, passID};
            postSubPass->DrawScreenQuad(drawInfo);
        }
        else if constexpr (MeshType == PrimitiveMeshType::FullScreenQuad)
        {
            REDDrawMesh drawMeshInfo{material,
                                     passID,
                                     std::nullopt,
                                     std::nullopt,
                                     std::nullopt,
                                     std::nullopt,
                                     mObjCtx,
                                     &rdrPrim->mPostProcessInputLayoutDesc,
                                     1,
                                     {
                                         rdrPrim->GetSixVertsQuad(),
                                     },
                                     nullptr,
                                     6,
                                     1};
            auto s = rdrPrim->GetSixVertsQuad()->GetSize();
            s += 0;
            postSubPass->DrawMesh(drawMeshInfo);
        }

        mRED->EndRenderPass();
    }

    template<typename TFunc, typename... TArgs>
    void PostProcessDrawQuads(UInt32 QuadCount, TFunc&& onConfig, MaterialR* material, NameID const& passID, bool clearTarget, TArgs&&... targets)
    {
        constexpr static UInt32 dstCount = static_cast<UInt32>(sizeof...(targets));
        constexpr static NGIClearValue clearValue{{0, 0, 0, 0}};
        REDColorTargetDesc renderTargetDescs[dstCount]{};
        NGIRenderPassTargetIndex colorTargetIndexs[dstCount]{};

        REDTextureView* dstViews[dstCount]{targets...};

        for (UInt32 index = 0; index < dstCount; index++)
        {
            renderTargetDescs[index] = {
                dstViews[index],
                clearTarget ? NGILoadOp::Clear : NGILoadOp::Load,
                NGIStoreOp::Store,
                clearValue,
            };
            colorTargetIndexs[index] = static_cast<NGIRenderPassTargetIndex>(index);
        }

        mRED->BeginRenderPass(passID.GetName(), dstCount, renderTargetDescs, nullptr);

        auto* postSubPass = mRED->AllocateSubRenderPass(passID.GetName(), 0, nullptr, dstCount, colorTargetIndexs, REDPassFlagBit{0});
        onConfig(postSubPass);

        auto* rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<cross::RendererSystemR>();
        auto* rdrPrim = rdrSys->GetRenderPrimitives();

        PropertySet mObjCtx(mRED->GetREDFrameAllocator());
        REDDrawMesh drawMeshInfo{material,
                                 passID,
                                 std::nullopt,
                                 std::nullopt,
                                 std::nullopt,
                                 std::nullopt,
                                 mObjCtx,
                                 &rdrPrim->mPostProcessInputLayoutDesc,
                                 1,
                                 {
                                     rdrPrim->GetSixVertsQuad(),
                                 },
                                 nullptr,
                                 6,
                                 QuadCount};

        postSubPass->DrawMesh(drawMeshInfo);

        mRED->EndRenderPass();
    }

    template<typename TFunc, typename... TArgs>
    static void PostProcessUtilWithContext(TFunc&& onConfig, MaterialR* material, RenderingExecutionDescriptor* RED, NameID const& passID, bool clearTarget, TArgs&&... targets)
    {
        constexpr static UInt32 dstCount = static_cast<UInt32>(sizeof...(targets));
        constexpr static NGIClearValue clearValue{{0, 0, 0, 0}};
        REDColorTargetDesc renderTargetDescs[dstCount]{};
        NGIRenderPassTargetIndex colorTargetIndexs[dstCount]{};

        REDTextureView* dstViews[dstCount]{targets...};

        for (UInt32 index = 0; index < dstCount; index++)
        {
            renderTargetDescs[index] = {
                dstViews[index],
                clearTarget ? NGILoadOp::Clear : NGILoadOp::Load,
                NGIStoreOp::Store,
                clearValue,
            };
            colorTargetIndexs[index] = static_cast<NGIRenderPassTargetIndex>(index);
        }

        RED->BeginRenderPass(passID.GetName(), dstCount, renderTargetDescs, nullptr);

        auto* postSubPass = RED->AllocateSubRenderPass(passID.GetName(), 0, nullptr, dstCount, colorTargetIndexs, REDPassFlagBit{0});
        onConfig(postSubPass);
        REDDrawScreenQuad drawInfo{
            material,
            passID,
        };
        postSubPass->DrawScreenQuad(drawInfo);

        RED->EndRenderPass();   
    }
    
    
    template<typename TFunc, typename... TArgs>
    static void PostProcessUtil(TFunc&& onConfig, MaterialR* material, RenderingExecutionDescriptor* RED, NameID const& passID, bool clearTarget, TArgs&&... targets)
    {
        PostProcessUtilWithContext(onConfig, material, RED, passID, clearTarget, targets...);
    }

    static REDTextureView* GetDefaultTextureView2D();
    static REDTextureView* CreateTextureView2D(std::string_view name, UInt2 size, GraphicsFormat format, NGITextureUsage usage, UInt16 mipCount, UInt16 sampleCount);
    static REDTextureView* CreateTextureView2D(std::string_view name, UInt32 width, UInt32 height, GraphicsFormat format, NGITextureUsage usage, UInt16 mipLvl = 1, UInt16 sampleCount = 1, bool mutableFormat = false);
    static REDTextureView* CreateTextureView2D(std::string_view name, UInt32 width, UInt32 height, GraphicsFormat format, NGITextureUsage usage, NGITextureUsage viewUsage, UInt16 mipLvl = 1, UInt16 sampleCount = 1,
                                               bool mutableFormat = false);
    static REDTextureView* CreateTextureView3D(std::string_view name, UInt32 width, UInt32 height, UInt32 depth, GraphicsFormat format, NGITextureUsage usage, UInt16 mipLvl = 1, UInt16 sampleCount = 1);

    static REDTextureView* CreateTextureView2D(REDTexture* texture, GraphicsFormat format, NGITextureUsage usage);

    static REDTextureView* CreateTextureView2D(REDTextureView * tetxureView, NGITextureUsage usage);

    void UpdateCameraContext(const RenderCamera* camera) const;

    virtual void UpdateShadowContext(REDPass* pass, const ShadowProperties* shadowData) const {};

    virtual void  UpdateCloudShadowContext(REDPass* pass) const;

    void UpdateTargetView(REDTextureView* mTarget);

    virtual void UpdatePipelineSharedContext(){};

    virtual REDTextureView* GetDepthPyramid() const
    {
        return nullptr;
    };
    auto& GetDepthStencilFormat()
    {
        return mDepthStencilFormat;
    }
    // Temp API
    NGIBufferView* GetLightsInfoBufferView()
    {
        Assert(mLightsBufferView);
        return mLightsBufferView;
    }

    virtual REDBufferView* GetScreenBlurMask()
    {
        return nullptr;
    }

    void UpdateLightShadowDataIndices();

    void RegisterCustomPass(GeneralPass pass, bool bAfterFSR2 = true);
    auto& GetUIProcessMaterial()
    {
        return mUIProcessMtl;
    }
    auto& GetEditorEffectMat()
    {
        return mEditorEffectMtl;
    }
    auto& GetExtendPass()
    {
        return mExtendPasses;
    }
    template<typename T>
    T& GetSpecificPass(const char* name)
    {
        return folly::poly_cast<T>(mExtendPasses.find(name)->second);
    }
    UInt32 GetFrameCount() const;

    auto& GetReadBackSession()
    {
        return mReadBackSession;
    }

 private:
    // make it private, so only call it once in Assemble(),

    void UpdateGPUSKinContext();

    void UpdateLightsInfoBuffer();

    void WaitAndPrepareVisibleLightList();

protected:
    void UpdateLightContext();

    // Render Size
    // Geometry Size
    REDTextureView* mTargetView = nullptr;
    REDTexture* mDepthStencilTexForRender = nullptr;

    // Display Size
    REDTexture* mDepthStencilTexForDisplay = nullptr;

    ViewType mType;

    NGIBufferView* mLightsBufferView = nullptr;
    NGIBufferView* mLightOutSpaceColorBufferView = nullptr;
    ScratchBufferWrap mLightsBufferWrap;

    GameContext mGameContext;

    RendererSystemR::ReadBackSession mReadBackSession;

    RenderWorld* mWorld;
    RenderingExecutionDescriptor* mRED;
    WorldRenderPipeline* mWorldRenderPipeline;
    REDCullingResult* mCullingResult = nullptr;
    LightCullingResult* mVisibleLightCullingResult = nullptr;

    PostProcessVolumeSettingR* mPostProcessSetting = nullptr;

    SSAAResolve mSSAAResolve;

    bool mUseReverseZ = true;
    bool mTAA = false;
    JitterData mJitterData;
    SInt32 mTaaHaltonIndex = -1;
    bool mUpscale = false;
    bool mRenderDebugVisualize = false;
    float mUpscaleValue = 1;

    MaterialR* mEditorEffectMtl = nullptr;
    MaterialR* mEditorEffectMtlGPUSkin = nullptr;
    MaterialR* mEditorWireFrameMtl = nullptr;
    MaterialR* mUIProcessMtl = nullptr;

    InputLayoutDesc mPostProcessInputLayoutDesc{};
    InputLayoutDesc mUIMergeInputLayoutDesc{};

    GraphicsFormat mDepthStencilFormat{};

    const RenderPipelineSetting* mSetting = nullptr;

    const static inline NameID gForwardID = "forward";
    const static inline NameID gSkyID = "sky";

    PipelineBuiltInValue mBuiltinVariable;
    FrameStdHashMap<HashString, GeneralPass> mExtendPasses;
    FrameStdVector<GeneralPass*> mPassRefs;
    FrameStdVector<GeneralPass*> mPassRefsAfterFSR2;
    void AssembleDebugGUIPass(REDTextureView* colorView, REDTextureView* depthStencilView);

    void AssembleUIPass(RenderWorld* world, REDTextureView* colorView, REDTextureView* outputView, REDTextureView* depthStencilView);

    void AssembleGizmoPass(RenderWorld* world, REDTextureView* colorView, REDTextureView* depthStencilView, bool skipIsRenderToTargetCamera = true);

    void AssembleBeforeToneMappingGizmoPass(RenderWorld* world, REDTextureView* colorView, REDTextureView* depthStencilView, bool skipIsRenderToTargetCamera);

    void AssembleOutlinePass(RenderWorld* world, REDTextureView* colorView);

    void AllocateDepthStencilView(REDTextureView*& dsView, REDTextureView*& depthOnlyView, REDTexture*& dsTex, UInt2 size);

    void AssembleWireFramePass(RenderWorld* world, REDTextureView* colorView);

    void GenerateJitterData(const RenderCamera* renderCamera, const Float2& targetViewSize, const float upsacleValue = 1.0f);

public:
    UInt2 GetDisplaySize()
    {
        REDTextureView* displayview = nullptr;
        displayview = GetBuiltInTexture<PassSemanticName::DisplayColor>();
        return UInt2{displayview->GetWidth(), displayview->GetHeight()};
    }

private:
    void AssembleOutlinePassInternal(RenderWorld* world, REDTextureView* colorView);

    friend class ReflectionProbeRenderPipeline;
    friend class SkyLightSystemR;
    friend class FoliageGpuDriven;
    friend class ShadowPass;
    friend class VirtualShadowMapArray;   // temp
    friend class LocalLightShadowMapProxy;
};
}   // namespace cross