#include "AOPass.h"
#include "Resource/AssetStreaming.h"
#include "NGIManager.h"
#include "RenderEngine/RenderCamera.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"
#include "RenderEngine/RenderPipeline/RenderPipelineR.h"
#include "Common/FrameCounter.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipeline.h"

namespace cross
{

    void GTAOSettings::Initialize()
    {
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(GTAOComputeShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(RTAOComputeShader);
        LOAD_RENDER_PIPELINE_RAYTRACING_SHADER(RTAORayTracingShader);
    }

    AOPass::AOPass()
    {
        
    }

    AOPass::~AOPass()
    {
        DestroyNGIResource();
    }

    PassDesc AOPass::GetPassDesc()
    {
        return PassDesc("GTAO", "This is an implementation of GTAO(Ground Truth Ambient Occlusion), based on Intel's XeGTAO.");
    }

    void AOPass::CreateHilbertLUT()
    {
        // Hilbert look-up texture! It's a 64 x 64 uint16 texture generated using GTAO::HilbertIndex
        constexpr UInt32 HilbertLUTWidth = 64, HilbertLUTHeight = 64;
        size_t dataCount = HilbertLUTWidth * HilbertLUTHeight;
        std::vector<UInt16> data(dataCount);
        for (UInt32 x = 0; x < HilbertLUTWidth; ++x)
        {
            for (UInt32 y = 0; y < HilbertLUTHeight; ++y)
            {
                UInt32 r2index = HilbertIndex(x, y);
                assert(r2index < 65536);
                data[x + HilbertLUTWidth * y] = (UInt16)r2index;
            }
        }

        size_t dataSize = sizeof(UInt16) * dataCount;
        NGIBufferDesc bDesc{dataSize, NGIBufferUsage::TexelBuffer | NGIBufferUsage::CopyDst};
        mHilbertLUT.reset(GetNGIDevice().CreateBuffer(bDesc, "HilbertLUT"));
        NGIBufferViewDesc bvDesc{NGIBufferUsage::TexelBuffer, 0, dataSize, GraphicsFormat::R16_UInt, 0};
        mHilbertLUTView.reset(GetNGIDevice().CreateBufferView(mHilbertLUT.get(), bvDesc));
        EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->UpdateBufferWithRawData(data.data(), dataSize, mHilbertLUT.get());
    }

    void AOPass::PrepareNGIResource()
    {
        if (mNGIInit)
            return ;
        CreateHilbertLUT();
        mNGIInit = true;
    }

    void AOPass::DestroyHilbertLUT()
    {
        RendererSystemR* rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        rdrSys->DestroyNGIObject(std::move(mHilbertLUTView));
        rdrSys->DestroyNGIObject(std::move(mHilbertLUT));
    }

    void AOPass::DestroyNGIResource()
    {
        DestroyHilbertLUT();
        mNGIInit = false;
    }

    REDTextureView* AOPass::GetHistoryTextureView(RenderingExecutionDescriptor* RED, REDTexture* historyTex, REDTextureView* currentTexView, NGITextureAspect aspect)
    {
        REDTextureView* ret;
        if (RED->Validate(historyTex) && historyTex->mDesc.Width == currentTexView->GetWidth() && historyTex->mDesc.Height == currentTexView->GetHeight())
        {
            ret = RED->AllocateTextureView(historyTex, GetTexture2DViewDesc(historyTex->mDesc.Usage, historyTex->mDesc.Format, aspect));
        }
        else
        {
            ret = currentTexView;
        }
        Assert(ret);
        return ret;
    }

    NGITextureViewDesc AOPass::GetTexture2DViewDesc(NGITextureUsage usage, GraphicsFormat format, NGITextureAspect aspect)
    {
        return NGITextureViewDesc{usage, format, NGITextureType::Texture2D, {aspect, 0, 1, 0, 1}};
    }

    NGITextureDesc AOPass::GetTexture2DDesc(GraphicsFormat format, UInt32 width, UInt32 height, NGITextureUsage usage)
    {
        return NGITextureDesc{format, NGITextureType::Texture2D, 1, 1, width, height, 1, 1, usage};
    }

    // aoView : xyz = bent normal, w = [0,1] ao value
    bool AOPass::ExecuteImp(const GameContext& gameContext, bool enableTemporalNoise, const REDTextureView* depthStencilView, const std::array<REDTextureView*, 4>& gBufferViews, 
        REDTextureView*& aoView)
    {
        auto renderCamera = gameContext.mRenderCamera;
        auto red = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();
        UInt32 targetWidth = static_cast<UInt32>(depthStencilView->mTexture->mDesc.Width);
        UInt32 targetHeight = static_cast<UInt32>(depthStencilView->mTexture->mDesc.Height);
        Int2 ViewportSize = { static_cast<SInt32>(targetWidth), static_cast<SInt32>(targetHeight) };
        Float2 ViewportPixelSize = { 1.f / targetWidth, 1.f / targetHeight };
        constexpr GraphicsFormat aoTermViewFormat = GraphicsFormat::R16G16B16A16_UNorm;

#ifdef NGI_ENABLE_RAY_TRACING
        auto viewType = gameContext.mRenderPipeline->GetRenderPipelineType();
        if (mSetting.mEnableRTAO && (viewType == ViewType::GameView || viewType == ViewType::SceneView))
        {
            red->BeginRegion("RTAO");
            auto worldRenderPipeline = dynamic_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline)->GetWorldRenderPipeline();
            auto* rtaoTexView = IRenderPipeline::CreateTextureView2D("Denoised AO Term", targetWidth, targetHeight, aoTermViewFormat, NGITextureUsage::ShaderResource | NGITextureUsage::UnorderedAccess |NGITextureUsage::CopyDst, 1);
            
            auto* rtaoPass = red->AllocatePass("RTAO");
            
            rtaoPass->SetProperty(NAME_ID("_GBuffer0"), gBufferViews[0]);
            rtaoPass->SetProperty(NAME_ID("_GBuffer1"), gBufferViews[1]);
            rtaoPass->SetProperty(NAME_ID("_GBuffer2"), gBufferViews[2]);
            rtaoPass->SetProperty(NAME_ID("_GBuffer3"), gBufferViews[3]);
            REDTextureView* depthView = red->AllocateTextureView(depthStencilView->mTexture,
                NGITextureViewDesc{NGITextureUsage::ShaderResource,
                    depthStencilView->mDesc.Format,
                    NGITextureType::Texture2D,
                    NGITextureSubRange{
                        NGITextureAspect::Depth,
                        0,
                        depthStencilView->mDesc.SubRange.MipLevels,
                        0,
                        1}});
            rtaoPass->SetProperty(NAME_ID("_DepthTexture"), depthView);
            rtaoPass->SetProperty(NAME_ID("_outFinalAOTerm"), rtaoTexView);
            // worldRenderPipeline->GetRayTracingScene()->SetBindlessResources(rtaoPass);
            
            rtaoPass->SetProperty(NAME_ID("ce_InvViewProjMatrix"), renderCamera->GetInvertProjMatrix() * renderCamera->GetInvertViewMatrix());
            rtaoPass->SetProperty(NAME_ID("ce_ViewMatrix"), renderCamera->GetViewMatrix());
            rtaoPass->SetProperty(NAME_ID("_ScreenSizeAndInvSize"), Float4(
                static_cast<float>(ViewportSize.x),
                static_cast<float>(ViewportSize.y),
                ViewportPixelSize.x,
                ViewportPixelSize.y
            ));
            rtaoPass->SetProperty(NAME_ID("ce_CameraTilePosition"), renderCamera->GetTilePosition<false>());

            UInt3 groupThreadSize;
            mSetting.RTAOComputeShaderR->GetThreadGroupSize("RTAO", groupThreadSize.x, groupThreadSize.y, groupThreadSize.z);
            
            // rtaoPass->Dispatch(mSetting.RTAOComputeShaderR, "RTAO", (targetWidth + groupThreadSize.x - 1) / groupThreadSize.x,
               // (targetHeight + groupThreadSize.y - 1) / groupThreadSize.y, 1);
            
            // mHistroyAOView = IRenderPipeline::CreateTextureView2D("HistroyAO", targetWidth, targetHeight, aoTermViewFormat, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource, 1);
            //
            // if (mSetting.mEnableSVGF)
            // {
            //     auto* svgfPass = red->AllocatePass("SVGF");
            //     svgfPass->SetProperty(NAME_ID("_GBuffer0"), gBufferViews[0]);
            //     svgfPass->SetProperty(NAME_ID("_GBuffer1"), gBufferViews[1]);
            //     svgfPass->SetProperty(NAME_ID("_GBuffer2"), gBufferViews[2]);
            //     svgfPass->SetProperty(NAME_ID("_GBuffer3"), gBufferViews[3]);
            //     svgfPass->SetProperty(NAME_ID("_DepthTexture"), depthView);
            //     svgfPass->SetProperty(NAME_ID("_outFinalAOTerm"), rtaoTexView);
            //     svgfPass->SetProperty(NAME_ID("_historyAO"), mHistroyAOView);
            //
            //     mSetting.RTAOComputeShaderR->GetThreadGroupSize("SVGF", groupThreadSize.x, groupThreadSize.y, groupThreadSize.z);
            //     // svgfPass->Dispatch(mSetting.RTAOComputeShaderR, "SVGF", (targetWidth + groupThreadSize.x - 1) / groupThreadSize.x,
            //        // (targetHeight + groupThreadSize.y - 1) / groupThreadSize.y, 1);
            // }
            
            
            auto raytracingPass = red->AllocatePass("RayTracingPipeline", true);
            worldRenderPipeline->GetRayTracingScene()->SetBindlessResources(raytracingPass);
            raytracingPass->SetProperty("OutColor", rtaoTexView);
            raytracingPass->DispatchRays(mSetting.RTAORayTracingShaderR, targetWidth, targetHeight, 1);
            
            aoView = IRenderPipeline::CreateTextureView2D(rtaoTexView->GetTexture(), aoTermViewFormat, NGITextureUsage::ShaderResource);
            
            red->EndRegion();
        }
        else
#endif
        {
            PrepareNGIResource();
            const bool Use16bitDepth = true;

            const auto& projMat = renderCamera->GetProjMatrix();
            float depthLinearizeMul = -projMat.m32;
            float depthLinearizeAdd = projMat.m22;
            depthLinearizeAdd = depthLinearizeMul * depthLinearizeAdd < 0 ? -depthLinearizeAdd : depthLinearizeAdd;

            Float2 DepthUnpackConsts = { depthLinearizeMul, depthLinearizeAdd };

            float tanHalfFOVX = 1.0f / projMat.m00;
            float tanHalfFOVY = 1.0f / projMat.m11;

            Float2 NDCToViewMul = { tanHalfFOVX * 2.f, tanHalfFOVY * -2.f };
            Float2 NDCToViewAdd = { -tanHalfFOVX, tanHalfFOVY };

            Float2 NDCToViewMul_x_PixelSize = { NDCToViewMul.x * ViewportPixelSize.x, NDCToViewMul.y * ViewportPixelSize.y };

            SInt32 NoiseIndex = 0;

            NoiseIndex = (enableTemporalNoise && mSetting.DenoisePassNum > 0) ? static_cast<SInt32>(frame::GetRenderingFrameNumber() % 64) : 0;

            // Allocate a new REDTextureView with NGITextureUsage::ShaderResource
            REDTextureView* srcDepthView = red->AllocateTextureView(depthStencilView->mTexture,
                                                                    NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                                       depthStencilView->mDesc.Format,
                                                                                       NGITextureType::Texture2D,
                                                                                       NGITextureSubRange{
                                                                                           NGITextureAspect::Depth,
                                                                                           0,
                                                                                           depthStencilView->mDesc.SubRange.MipLevels,
                                                                                           0,
                                                                                           1,
                                                                                       }});

            auto* AOTermView = IRenderPipeline::CreateTextureView2D("Output AO Term", targetWidth, targetHeight, aoTermViewFormat, NGITextureUsage::ShaderResource | NGITextureUsage::UnorderedAccess | NGITextureUsage::CopyDst, 1);
            auto* edgesView = IRenderPipeline::CreateTextureView2D("Output Edges Term", targetWidth, targetHeight, GraphicsFormat::R8_UNorm, NGITextureUsage::ShaderResource | NGITextureUsage::UnorderedAccess | NGITextureUsage::CopyDst, 1);
            // This bentNormalView is temporally existed, will pack into aoTermView
            auto* bentNormalView = IRenderPipeline::CreateTextureView2D("Output Bent Normal", targetWidth, targetHeight, aoTermViewFormat, NGITextureUsage::ShaderResource | NGITextureUsage::UnorderedAccess | NGITextureUsage::CopyDst, 1);
            auto* denoisedAOTermView = IRenderPipeline::CreateTextureView2D("Denoised AO Term", targetWidth, targetHeight, aoTermViewFormat, NGITextureUsage::ShaderResource | NGITextureUsage::UnorderedAccess |NGITextureUsage::CopyDst, 1);

            red->BeginRegion("GTAO");
            // Should clear all textures before real calculation
            NGIClearValue clearValue{{0, 0, 0, 0}};
            red->AllocatePass("Clear AOView")->ClearTexture(AOTermView, clearValue);
            red->AllocatePass("Clear EdgeView")->ClearTexture(edgesView, clearValue);
            red->AllocatePass("Clear bentNormalView")->ClearTexture(bentNormalView, clearValue);
            red->AllocatePass("Clear denoisedAOTermView")->ClearTexture(denoisedAOTermView, clearValue);

            auto* mainPass = red->AllocatePass("GTAO_MainPass");
            // Constants
            mainPass->SetProperty(NAME_ID("GTAO_COMPUTE_BENT_NORMALS"), mSetting.EnableBentNormal);
            mainPass->SetProperty(NAME_ID("GTAO_COMPUTE_MULTI_BOUNCE"), mSetting.EnableMultiBounce);
            mainPass->SetProperty(NAME_ID("GTAO_USE_DEFAULT_CONSTANTS"), mSetting.UseDefaultConstants);
            mainPass->SetProperty(NAME_ID("ENABLE_SLICE_NOISE"), mSetting.EnableSliceNoise);
            mainPass->SetProperty(NAME_ID("ENABLE_STEP_NOISE"), mSetting.EnableStepNoise);
            mainPass->SetProperty(NAME_ID("ViewMatrix"), renderCamera->GetViewMatrix());
            mainPass->SetProperty(NAME_ID("InverseViewMatrix"), renderCamera->GetInvertViewMatrix());
            mainPass->SetProperty(NAME_ID("ViewportSize"), ViewportSize);
            mainPass->SetProperty(NAME_ID("ViewportPixelSize"), ViewportPixelSize);
            mainPass->SetProperty(NAME_ID("DepthUnpackConsts"), DepthUnpackConsts);
            mainPass->SetProperty(NAME_ID("NDCToViewMul"), NDCToViewMul);
            mainPass->SetProperty(NAME_ID("NDCToViewAdd"), NDCToViewAdd);
            mainPass->SetProperty(NAME_ID("NDCToViewMul_x_PixelSize"), NDCToViewMul_x_PixelSize);
            mainPass->SetProperty(NAME_ID("EffectRadius"), mSetting.EffectRadius);
            if (!mSetting.UseDefaultConstants)
            {
                float absoluteSplitDepthDifferenceRcp = mSetting.RelativeSplitDepthDifference / (Use16bitDepth ? 65504.f : 3.402823466e+38f);
                mainPass->SetProperty(NAME_ID("AbsoluteSplitDepthDifferenceRcp"), absoluteSplitDepthDifferenceRcp);
                mainPass->SetProperty(NAME_ID("EffectFalloffRange"), mSetting.EffectFalloffRange);
                mainPass->SetProperty(NAME_ID("VisibilityValuePower"), mSetting.VisibilityValuePower);
                mainPass->SetProperty(NAME_ID("Intensity"), mSetting.Intensity);
                mainPass->SetProperty(NAME_ID("SampleDistributionPower"), mSetting.SampleDistributionPower);
                mainPass->SetProperty(NAME_ID("ThinOccluderCompensation"), mSetting.ThinOccluderCompensation);
                mainPass->SetProperty(NAME_ID("DepthMIPSamplingOffset"), mSetting.DepthMIPSamplingOffset);
            }
            mainPass->SetProperty(NAME_ID("NoiseIndex"), NoiseIndex);
            // Textures
            mainPass->SetProperty(NAME_ID("_srcWorkingDepth"), srcDepthView);
            mainPass->SetProperty(NAME_ID("_GBuffer0"), gBufferViews[0]);
            mainPass->SetProperty(NAME_ID("_GBuffer1"), gBufferViews[1]);
            mainPass->SetProperty(NAME_ID("_GBuffer2"), gBufferViews[2]);
            mainPass->SetProperty(NAME_ID("_GBuffer3"), gBufferViews[3]);
            mainPass->SetProperty(NAME_ID("_HilbertLUT"), mHilbertLUTView.get());
            mainPass->SetProperty(NAME_ID("_outWorkingAOTerm"), AOTermView);
            mainPass->SetProperty(NAME_ID("_outWorkingEdges"), edgesView);
            mainPass->SetProperty(NAME_ID("_outWorkingBentNormal"), bentNormalView);

            // TODO(chopperlin) Should use as configuration
            mainPass->Dispatch(mSetting.GTAOComputeShaderR, "CSGTAOHigh", (targetWidth + GTAO_NUMTHREADS_X - 1) / GTAO_NUMTHREADS_X, (targetHeight + GTAO_NUMTHREADS_X - 1) / GTAO_NUMTHREADS_X, 1);

            for (SInt32 i = 0; i < mSetting.DenoisePassNum - 1; ++i)
            {
                auto* denoisePass = red->AllocatePass(Format("GTAO_DenoisePass {0}", i), true);
                // Constants
                denoisePass->SetProperty(NAME_ID("ViewportPixelSize"), ViewportPixelSize);
                denoisePass->SetProperty(NAME_ID("DenoiseBlurBeta"), mSetting.DenoiseBlurBeta);
                // Textures
                denoisePass->SetProperty(NAME_ID("_GBuffer2"), gBufferViews[2]);
                denoisePass->SetProperty(NAME_ID("_srcWorkingAOTerm"), AOTermView);
                denoisePass->SetProperty(NAME_ID("_srcWorkingEdges"), edgesView);
                denoisePass->SetProperty(NAME_ID("_outFinalAOTerm"), denoisedAOTermView);

                denoisePass->Dispatch(mSetting.GTAOComputeShaderR, "CSDenoisePass", (targetWidth + GTAO_NUMTHREADS_X - 1) / GTAO_NUMTHREADS_X, (targetHeight + GTAO_NUMTHREADS_X - 1) / GTAO_NUMTHREADS_X, 1);

                REDTextureView* anotherAOView = AOTermView;
                AOTermView = denoisedAOTermView;
                denoisedAOTermView = anotherAOView;
            }

            auto* denoiseLastPass = red->AllocatePass("GTAO_DenoiseLastPass");
            // Constants
            denoiseLastPass->SetProperty(NAME_ID("ViewportPixelSize"), ViewportPixelSize);
            denoiseLastPass->SetProperty(NAME_ID("DenoiseBlurBeta"), mSetting.DenoiseBlurBeta);
            // Textures
            denoiseLastPass->SetProperty(NAME_ID("_GBuffer2"), gBufferViews[2]);
            denoiseLastPass->SetProperty(NAME_ID("_srcWorkingAOTerm"), AOTermView);
            denoiseLastPass->SetProperty(NAME_ID("_srcWorkingEdges"), edgesView);
            denoiseLastPass->SetProperty(NAME_ID("_srcWorkingBentNormal"), bentNormalView);
            denoiseLastPass->SetProperty(NAME_ID("_outFinalAOTerm"), denoisedAOTermView);

            denoiseLastPass->Dispatch(mSetting.GTAOComputeShaderR, "CSDenoiseLastPass", (targetWidth + GTAO_NUMTHREADS_X - 1) / GTAO_NUMTHREADS_X, (targetHeight + GTAO_NUMTHREADS_X - 1) / GTAO_NUMTHREADS_X, 1);
            red->EndRegion();

            aoView = IRenderPipeline::CreateTextureView2D(denoisedAOTermView->GetTexture(), aoTermViewFormat, NGITextureUsage::ShaderResource);
        }
        return true;
    }

}