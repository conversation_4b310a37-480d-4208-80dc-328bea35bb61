#pragma once
#include "PassBase.h"
#include "Resource/Material.h"
#include "RenderEngine/RenderMaterial.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"

namespace cross {

    struct VisualizationParams
    {
        ViewModeVisualizeType mVisType;
    };

    struct VisualizationTexes
    {
        std::array<REDTextureView*, 4> mGbufferViews;
        REDTextureView* mDepthTex;
        REDTextureView* mAOTex;
        REDTextureView* mReflectionsTex;
        REDTextureView* mSeparateTranslucencyTex;
        REDTextureView* mSceneColorTex; // Opacity color, before transparent
        REDTextureView* mGITex;
        REDTextureView* mEmissiveTex;
        REDTextureView* mVisibilityBufferTex;
    };


    class RENDER_ENGINE_API ViewModeVisualization
    {
    public:
        constexpr std::string_view GetPassName()
        {
            return  std::string_view("ViewModeVisualization");
        }
        MaterialR* mViewModeVisMtl;
        VisualizationParams mVisParams;
        VisualizationTexes mVisTexes;
        REDTextureView* mDebugTexView{nullptr};
        void SetRenderDebugTex(REDTextureView* debugTex);
        void FillInputs(const GameContext& gameContex);
        void ExecuteImp(const GameContext& gameContext, REDTextureView* visOutputView, REDTextureView* sceneColorView);
        void ResetAllTexes();
    };



}