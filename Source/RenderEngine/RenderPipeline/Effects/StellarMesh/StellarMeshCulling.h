#pragma once
#include "RenderEngine/RenderPipeline/Effects/PassBase.h"
#include "NativeGraphicsInterface/NGI.h"

namespace cross {

struct InstancedCluster
{
    UInt32 mClusterIndex;
    UInt32 mInstanceIndex;
};

struct QueueState
{
    UInt32 mTotalClusters;
    // TODO
};

struct SlotCounter
{
    UInt32 mClusterSlot;
};

class CEMeta(Editor) RENDER_ENGINE_API StellarMeshCullingPipelineSetting : public PassSetting
{
public:
    StellarMeshCullingPipelineSetting()
    {
        //enable = false;
    }

    CE_Virtual_Serialize_Deserialize;
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, InitArg               , "PipelineResource/FFSRP/Shader/Features/StellarMesh/Culling/InitArg.compute.nda"                    , "Initialize Arguments"                 , "", "");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, InstanceCulling       , "PipelineResource/FFSRP/Shader/Features/StellarMesh/Culling/InstanceCulling.compute.nda"            , "Instance Culling"                     , "", "");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, InitClusterCullingArg , "PipelineResource/FFSRP/Shader/Features/StellarMesh/Culling/InitializeClusterCullingArg.compute.nda", "Initialize Cluster Culling Argument"  , "", "");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, ClusterCulling        , "PipelineResource/FFSRP/Shader/Features/StellarMesh/Culling/ClusterCulling.compute.nda"             , "Cluster Culling Shader"               , "", "");
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, CalculateRasterizerArg, "PipelineResource/FFSRP/Shader/Features/StellarMesh/Culling/CalculateRasterizerArg.compute.nda"     , "Calculate Arguments For Rasterization", "", "");

    void Initialize() override
    {
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(InitArg);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(InstanceCulling);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(InitClusterCullingArg);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(ClusterCulling);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(CalculateRasterizerArg);
    }
};

// Buffer in StellarMeshCullingContext is allocated in StellarMeshRenderPipeline,
// because culling pass will execute twice in a frame.
// It is used to store the culling intermediate data.
struct StellarMeshCullingContext
{
    REDBuffer* mSlotCounterBuffer;
    REDBufferView* mSlotCounterBufferUAV;
    REDBufferView* mSlotCounterBufferSRV;

    REDBuffer* mQueueStateBuffer;
    REDBufferView* mQueueStateBufferUAV;
    REDBufferView* mQueueStateBufferSRV;

    REDBuffer* mCandidateClusterQueueBuffer;
    REDBufferView* mCandidateClusterQueueBufferUAV;
    REDBufferView* mCandidateClusterQueueBufferSRV;

    REDBuffer* mClusterCullingDrawArgBuffer;
    REDBufferView* mClusterCullingDrawArgBufferUAV;
    REDBufferView* mClusterCullingDrawArgBufferSRV;
};

struct StellarMeshCullingPipelineInput
{
    
};

struct StellarMeshCullingPipelineOutput
{
    REDBuffer*     mClusterDrawArgBuffer{};
    REDBufferView* mClusterDrawArgBufferUAV;
    REDBufferView* mClusterDrawArgBufferSRV;

    REDBuffer*     mVisibleClusterBuffer;
    REDBufferView* mVisibleClusterBufferUAV;
    REDBufferView* mVisibleClusterBufferSRV;
};

class RENDER_ENGINE_API StellarMeshCullingPipeline
{
public:
    StellarMeshCullingPipeline();
    ~StellarMeshCullingPipeline();

    void InitArg(const GameContext& gameContext, StellarMeshCullingContext& cullingContext);
    void Execute(const GameContext& gameContext, StellarMeshCullingContext& cullingContext);

    StellarMeshCullingPipelineInput  m_Input{};
    StellarMeshCullingPipelineOutput m_Output{};

private:
    struct Impl;
    std::unique_ptr<Impl> pImpl{};
};

}   // namespace cross
