#pragma once
#include "RenderEngine/RenderPipeline/Effects/PassBase.h"
#include "StellarMeshCulling.h"
#include "StellarMeshRasterize.h"

namespace cross {

class CEMeta(Reflect, Editor, PartOf(FFSRenderPipelineSetting)) RENDER_ENGINE_API StellarMeshRenderPipelineSetting : public PassSetting
{
public:
    StellarMeshRenderPipelineSetting()
    {
        //enable = false;
    }
    CE_Virtual_Serialize_Deserialize;
    void Initialize() override
    {
        mStellarMeshCullingPipelineSetting.Initialize();
        mStellarMeshRasterizePipelineSetting.Initialize();
    }

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", DisplayName = "Culling Pipeline Setting", ToolTips = ""))
    StellarMeshCullingPipelineSetting mStellarMeshCullingPipelineSetting{};

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", DisplayName = "Rasterize Pipeline Setting", ToolTips = ""))
    StellarMeshRasterizePipelineSetting mStellarMeshRasterizePipelineSetting{};
};

struct StellarMeshRenderPipelineInput
{
    std::array<REDTextureView*, 4>* gBufferViews;
    REDTextureView* depthStencilView;
};

// Output is not necessary in StellarMeshRenderPipeline.
// It is only used to indicate which pipeline resources are written in StellarMeshRenderPipeline.
struct StellarMeshRenderPipelineOutput
{
    std::array<REDTextureView*, 4>* gBufferViews;
    REDTextureView* depthStencilView;
    REDTextureView* visbilityBufferView;
};

class RENDER_ENGINE_API StellarMeshRenderPipeline
{
public:
    StellarMeshRenderPipeline();
    ~StellarMeshRenderPipeline();

    void Execute(const GameContext& gameContext);

    StellarMeshRenderPipelineInput  m_Input{};
    StellarMeshRenderPipelineOutput m_Output{};

private:
    struct Impl;
    std::unique_ptr<Impl> pImpl;
};

}
