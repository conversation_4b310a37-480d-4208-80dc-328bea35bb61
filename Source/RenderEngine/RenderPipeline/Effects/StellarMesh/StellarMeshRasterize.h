#pragma once

namespace cross {


class CEMeta(Editor) RENDER_ENGINE_API StellarMeshRasterizePipelineSetting : public PassSetting
{
public:
    StellarMeshRasterizePipelineSetting()
    {
        //enable = false;
    }

    CE_Virtual_Serialize_Deserialize;
    RENDER_PIPELINE_RESOURCE_INVISIBLE(Material, BasicRasterizeMaterial, "PipelineResource/FFSRP/Shader/Features/StellarMesh/Rasterize/GenerateVisibilityBuffer.nda", "VisbilityBuffer Rasterize Material", "", "");

    void Initialize() override
    {
        // TODO: Why does dynamic_cast(TypeCast in debug mode) from Resource* to Material* failed here?
        //LOAD_RENDER_PIPELINE_MATERIAL(BasicRasterizeMaterial);
        BasicRasterizeMaterialRes = static_cast<resource::Material*>(gAssetStreamingManager->LoadSynchronously(BasicRasterizeMaterial).get());
        BasicRasterizeMaterialR = dynamic_cast<MaterialR*>(BasicRasterizeMaterialRes->GetRenderMaterial());
    }
};

struct StellarMeshRasterizePipelineInput
{
    REDBuffer* mClusterDrawArgBuffer{};
    REDBufferView* mClusterDrawArgBufferUAV;
    REDBufferView* mClusterDrawArgBufferSRV;

    REDBuffer* mVisibleClusterBuffer;
    REDBufferView* mVisibleClusterBufferUAV;
    REDBufferView* mVisibleClusterBufferSRV;
};

struct StellarMeshRasterizePipelineOutput
{
    REDTextureView* mVisbilityBufferView;
    REDTextureView* mDepthStencilView;
};

class RENDER_ENGINE_API StellarMeshRasterizePipeline
{
public:
    StellarMeshRasterizePipeline();
    ~StellarMeshRasterizePipeline();

    void Execute(const GameContext& gameContext);

    StellarMeshRasterizePipelineInput m_Input{};
    StellarMeshRasterizePipelineOutput m_Output{};

private:
    struct Impl;
    std::unique_ptr<Impl> pImpl;
};

}
