#include "StellarMeshRenderPipeline.h"
#include "StellarMeshCulling.h"
#include "StellarMeshRasterize.h"

namespace cross {

struct StellarMeshRenderPipeline::Impl
{
    Impl() = default;
    ~Impl() = default;

    StellarMeshCullingContext  mCullingContext{};
    StellarMeshCullingPipeline mCullingPipeline{};
    StellarMeshRasterizePipeline mRasterizePipeline{};
    
    RenderWorld* mRenderWorld;
    FFSWorldRenderPipeline* mWorldRenderPipeline;
    FFSRenderPipeline* mRenderPipeline;
    FFSRenderPipelineSetting const* mRenderPipelineSetting;
    RenderingExecutionDescriptor* mRED;
    StellarMeshCullingPipelineSetting const* mSetting;
    
    std::tuple<REDBuffer*, REDBufferView*, REDBufferView*> CreateBuffer(std::string_view name, UInt32 elementCount, UInt32 elementBytes, bool isDrawIndirect = false)
    {
        auto indirectUsage = isDrawIndirect ? NGIBufferUsage{0} : NGIBufferUsage::IndirectBuffer;
        NGIBufferUsage usage = NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::StructuredBuffer | indirectUsage;
        REDBuffer* buffer = mRED->AllocateBuffer(name, NGIBufferDesc{elementCount * elementBytes, usage});
        REDBufferView* bufferUAV = mRED->AllocateBufferView(buffer, NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer, 0u, elementCount * elementBytes, GraphicsFormat::Unknown, elementBytes});
        REDBufferView* bufferSRV = mRED->AllocateBufferView(buffer, NGIBufferViewDesc{NGIBufferUsage::StructuredBuffer | indirectUsage, 0u, elementCount * elementBytes, GraphicsFormat::Unknown, elementBytes});

        return {buffer, bufferUAV, bufferSRV};
    }

    void Initialize(const GameContext& gameContext)
    {
        mRenderWorld = gameContext.mRenderWorld;
        mRenderPipeline = static_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline);
        mWorldRenderPipeline = static_cast<FFSWorldRenderPipeline*>(mRenderPipeline->GetWorldRenderPipeline());
        mRED = mRenderPipeline->GetRenderingExecutionDescriptor();
        mRenderPipelineSetting = mRenderPipeline->GetSetting();
        mSetting = &(mRenderPipelineSetting->mStellarMeshRenderPipelineSetting.mStellarMeshCullingPipelineSetting);

        std::tie(mCullingContext.mSlotCounterBuffer, mCullingContext.mSlotCounterBufferUAV, mCullingContext.mSlotCounterBufferSRV) = CreateBuffer("SlotCounterBuffer", 1, sizeof(SlotCounter));
        std::tie(mCullingContext.mQueueStateBuffer, mCullingContext.mQueueStateBufferUAV, mCullingContext.mQueueStateBufferSRV) = CreateBuffer("QueueStateBuffer", 1, sizeof(QueueState));
        std::tie(mCullingContext.mCandidateClusterQueueBuffer, mCullingContext.mCandidateClusterQueueBufferUAV, mCullingContext.mCandidateClusterQueueBufferSRV) = CreateBuffer("CandidateClusterQueueBuffer", StellarMeshSceneSetting::MaxClusters, sizeof(InstancedCluster));
        std::tie(mCullingContext.mClusterCullingDrawArgBuffer, mCullingContext.mClusterCullingDrawArgBufferUAV, mCullingContext.mClusterCullingDrawArgBufferSRV) = CreateBuffer("ClusterCullingDrawArgBuffer", 1, sizeof(DispatchIndirectCommand), true);
    }

    REDTextureView* AllocateVisbilityBuffer(StellarMeshRenderPipeline& io)
    {

        auto usage = NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource | NGITextureUsage::CopySrc | NGITextureUsage::CopyDst;
        auto format = GraphicsFormat::R32_UInt;
        auto visibilityBuffer =
            mRED->AllocateTexture("VisibilityBuffer", NGITextureDesc{format, NGITextureType::Texture2D, 1, 1, (*io.m_Input.gBufferViews)[0]->GetWidth(), (*io.m_Input.gBufferViews)[0]->GetHeight(), 1, 1, usage, false, false});

        return mRED->AllocateTextureView(visibilityBuffer,
                                             NGITextureViewDesc{
                                                 usage,
                                                 format,
                                                 NGITextureType::Texture2D,
                                                 {NGITextureAspect::Color, 0, 1, 0, 1},
                                             });
    }

    void ExecuteImpl(const GameContext& gameContext, StellarMeshRenderPipeline& io)
    {
        std::tie(mCullingPipeline.m_Output.mVisibleClusterBuffer, mCullingPipeline.m_Output.mVisibleClusterBufferUAV, mCullingPipeline.m_Output.mVisibleClusterBufferSRV) = CreateBuffer("VisibleCluster", StellarMeshSceneSetting::MaxVisibleClusters, sizeof(InstancedCluster));
        std::tie(mCullingPipeline.m_Output.mClusterDrawArgBuffer, mCullingPipeline.m_Output.mClusterDrawArgBufferUAV, mCullingPipeline.m_Output.mClusterDrawArgBufferSRV) = CreateBuffer("ClusterDrawArgBuffer", 1, sizeof(UInt4), true);
        mCullingPipeline.InitArg(gameContext, mCullingContext);
        mCullingPipeline.Execute(gameContext, mCullingContext);

        mRasterizePipeline.m_Input.mClusterDrawArgBuffer = mCullingPipeline.m_Output.mClusterDrawArgBuffer;
        mRasterizePipeline.m_Input.mClusterDrawArgBufferUAV = mCullingPipeline.m_Output.mClusterDrawArgBufferUAV;
        mRasterizePipeline.m_Input.mClusterDrawArgBufferSRV = mCullingPipeline.m_Output.mClusterDrawArgBufferSRV;
        mRasterizePipeline.m_Input.mVisibleClusterBuffer = mCullingPipeline.m_Output.mVisibleClusterBuffer;
        mRasterizePipeline.m_Input.mVisibleClusterBufferUAV = mCullingPipeline.m_Output.mVisibleClusterBufferUAV;
        mRasterizePipeline.m_Input.mVisibleClusterBufferSRV = mCullingPipeline.m_Output.mVisibleClusterBufferSRV;
        mRasterizePipeline.m_Output.mVisbilityBufferView = AllocateVisbilityBuffer(io);
        mRasterizePipeline.m_Output.mDepthStencilView = io.m_Input.depthStencilView;
        mRasterizePipeline.Execute(gameContext);

        io.m_Output.visbilityBufferView = mRasterizePipeline.m_Output.mVisbilityBufferView;
    }
};

StellarMeshRenderPipeline::StellarMeshRenderPipeline()
    : pImpl(std::make_unique<Impl>())
{}

StellarMeshRenderPipeline::~StellarMeshRenderPipeline() = default;

void StellarMeshRenderPipeline::Execute(const GameContext& gameContext)
{
    pImpl->Initialize(gameContext);
    pImpl->ExecuteImpl(gameContext, *this);
}

}
