#include "StellarMeshRasterize.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDRenderStellarMeshPayload.h"

namespace cross {

class BasicRasterizer
{
public:
    REDTextureView* mVisbilityBufferView;
    StellarMeshRasterizePipelineSetting const* mSetting{};

    BasicRasterizer() = default;

    void Initialize(const GameContext& gameContext)
    {
        mRenderWorld = gameContext.mRenderWorld;
        mRenderPipeline = static_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline);
        mWorldRenderPipeline = static_cast<FFSWorldRenderPipeline*>(mRenderPipeline->GetWorldRenderPipeline());
        mRED = mRenderPipeline->GetRenderingExecutionDescriptor();
        mRenderPipelineSetting = mRenderPipeline->GetSetting();
        mSetting = &(mRenderPipelineSetting->mStellarMeshRenderPipelineSetting.mStellarMeshRasterizePipelineSetting);
        mPipeline.mMaterial = mSetting->BasicRasterizeMaterialR;
        //if (mRasterizeMaterial == nullptr)
        //{
        //    mRasterizeResource = gAssetStreamingManager->LoadSynchronously("PipelineResource/FFSRP/Shader/Features/StellarMesh/Rasterize/GenerateVisibilityBuffer.nda");
        //    if (mRasterizeResource == nullptr)
        //        Assert(false);
        //    auto res = TypeCast<resource::Material>(mRasterizeResource);
        //    mRasterizeMaterial = static_cast<MaterialR*>(res->GetRenderMaterial());
        //}
    }

    void Execute(const GameContext& gameContext, StellarMeshRasterizePipeline& io)
    {
        Initialize(gameContext);

        NGIClearValue clearValue{{0, 0, 0, 0}};
        clearValue.depthStencil = {0.f, 0};
        auto loadOp = NGILoadOp::Load;
        REDDepthStencilTargetDesc depthStencilTarget{io.m_Output.mDepthStencilView, loadOp, NGIStoreOp::Store, loadOp, NGIStoreOp::Store, clearValue, nullptr, NGIResolveType::DontResolve, NGIResolveType::DontResolve};
        REDColorTargetDesc vbColorTarget = {io.m_Output.mVisbilityBufferView, NGILoadOp::Clear, NGIStoreOp::Store, NGIClearValue{0, 0, 0, 0}, nullptr};
        mRED->BeginRenderPass("HW Rasterize", 1u, &vbColorTarget, &depthStencilTarget);

        NGIRenderPassTargetIndex colorTarget = NGIRenderPassTargetIndex::Target0;
        auto* pass = mRED->AllocateSubRenderPass("HW Rasterize", 0, nullptr, 1, &colorTarget, REDPassFlagBit::NeedDepth | REDPassFlagBit::NeedStencil);

        auto stellarMeshScene = mWorldRenderPipeline->GetStellarMeshScene();
        auto stellarMeshManager = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetStellarMeshManager();
        pass->SetProperty(NAME_ID("visibleClusters"), io.m_Input.mVisibleClusterBufferSRV);
        pass->SetProperty(NAME_ID("instances"), stellarMeshScene->GetStellarSceneBufferView());
        pass->SetProperty(NAME_ID("meshConstants"), stellarMeshManager->GetMeshConstantBufferView());
        pass->SetProperty(NAME_ID("clusters"), stellarMeshManager->GetClusterBufferView());
        pass->SetProperty(NAME_ID("indexes"), stellarMeshManager->GetIndexBufferView());
        pass->SetProperty(NAME_ID("positions"), stellarMeshManager->GetPositionBufferView());

        REDRenderStellarMeshDesc payloadDesc {
                "GenerateVisbilityBuffer",
                1,
                &mPipeline,
                io.m_Input.mClusterDrawArgBuffer
        };
        pass->RenderStellarMesh(payloadDesc);

        mRED->EndRenderPass();
    }

private:
    RenderWorld* mRenderWorld;
    FFSWorldRenderPipeline* mWorldRenderPipeline;
    FFSRenderPipeline* mRenderPipeline;
    FFSRenderPipelineSetting const* mRenderPipelineSetting;
    RenderingExecutionDescriptor* mRED;

    StellarMeshRasterPipeline mPipeline{};
    // MaterialR* mRasterizeMaterial{nullptr};
    //ResourcePtr mRasterizeResource{nullptr};
};

struct StellarMeshRasterizePipeline::Impl
{
	Impl() = default;
	~Impl() = default;
	RenderWorld* mRenderWorld;
	FFSWorldRenderPipeline* mWorldRenderPipeline;
	FFSRenderPipeline* mRenderPipeline;
	FFSRenderPipelineSetting const* mRenderPipelineSetting;
	RenderingExecutionDescriptor* mRED;
    StellarMeshRasterizePipelineSetting const* mSetting{};

    BasicRasterizer mBasicRasterizer{};

	void Initialize(const GameContext& gameContext)
    {
        mRenderWorld = gameContext.mRenderWorld;
        mRenderPipeline = static_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline);
        mWorldRenderPipeline = static_cast<FFSWorldRenderPipeline*>(mRenderPipeline->GetWorldRenderPipeline());
        mRED = mRenderPipeline->GetRenderingExecutionDescriptor();
        mRenderPipelineSetting = mRenderPipeline->GetSetting();
        mSetting = &(mRenderPipelineSetting->mStellarMeshRenderPipelineSetting.mStellarMeshRasterizePipelineSetting);
    }

    void ExecuteImpl(const GameContext& gameContext, StellarMeshRasterizePipeline& io)
    {
        mRED->BeginRegion("StellarMeshRasterize");

        mBasicRasterizer.Initialize(gameContext);
        mBasicRasterizer.Execute(gameContext, io);

        mRED->EndRegion();
    }
};

StellarMeshRasterizePipeline::StellarMeshRasterizePipeline()
    :pImpl(std::make_unique<Impl>())
{
}

StellarMeshRasterizePipeline::~StellarMeshRasterizePipeline() = default;

void StellarMeshRasterizePipeline::Execute(const GameContext& gameContext)
{
    pImpl->Initialize(gameContext);
    pImpl->ExecuteImpl(gameContext, *this);
}

}
