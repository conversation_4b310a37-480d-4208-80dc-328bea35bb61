#include "IndirectLightingComposite.h"
#include "RenderEngine/RenderPipeline/RenderPipelineR.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipeline.h"
#include "RenderEngine/RenderWorldConst.h"
#include "Resource/AssetStreaming.h"
#include "RenderEngine/SkyLightSystemR.h"

namespace cross {

void IndirectLightingCompositeSetting::Initialize()
{
    LOAD_RENDER_PIPELINE_MATERIAL(IndirectLightingCompositeMtl);
    LOAD_RENDER_PIPELINE_TEXTURE(EnvBRDFTexture);

    mSmartGISetting.Initialize();
    mReflectionIndirectSetting.Initialize();
    mAOSetting.Initialize();
}

void IndirectLightingComposite::FillInput(const GameContext& gameContext)
{
    auto ffsRdrPipe = dynamic_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline);
    auto ffsSetting = ffsRdrPipe->GetSetting();
    // Pack textures
    auto [smartDiffuse, smartSpecular] = ffsRdrPipe->GetSmartGIViews();
    mILCTexes = {ffsRdrPipe->GetReflectionIndirectView(), 
                 smartDiffuse,
                 smartSpecular,
                 ffsRdrPipe->GetAOView()};
    // Pack Params
    auto smartGISetting = ffsSetting->mIndirectLightingCompositeSettings.mSmartGISetting;
    bool bEnableSmartGI = smartGISetting.IsEnableGI(gameContext);
    bool bEnableReflectionIndirect = ffsSetting->mIndirectLightingCompositeSettings.mReflectionIndirectSetting.enable;
    mILCparams = {bEnableSmartGI,
                  bEnableReflectionIndirect};
}

// Indirect lighting composite write lighting into sceneColor
bool IndirectLightingComposite::ExecuteImp(const GameContext& gameContext, const std::array<REDTextureView*, 4>& gBufferViews, 
    REDTextureView* sceneDepthView, REDTextureView* sceneColorViewIn, REDTextureView* sceneColorViewOut, REDTextureView*& giViewOut)
{
    auto pipeline = dynamic_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline);
    mRED = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();

    giViewOut = gameContext.mRenderPipeline->CreateTextureView2D(
        "GIColorView", sceneColorViewIn->GetWidth(), sceneColorViewIn->GetHeight(), sceneColorViewIn->mTexture->mDesc.Format, NGITextureUsage::RenderTarget | NGITextureUsage::CopyDst | NGITextureUsage::ShaderResource);

    // Specular indirect : 1. from ReflectionIndirect pass, default enabled, if SSR
    // Diffuse indirect  : 1. from SmartGI, if not, use skylight's SH
    // AO                : 1. from GTAO, if not, use gbuffer3's AO value
    gameContext.mRenderPipeline->PostProcess(
        [&](REDPass* pass) {
            // Need these for DecodeFromGBuffer func
            // Keep aligned with "GlobalModelVariable.hlsl"
            pass->SetProperty(NAME_ID("_GBuffer0"), gBufferViews[0]);
            pass->SetProperty(NAME_ID("_GBuffer1"), gBufferViews[1]);
            pass->SetProperty(NAME_ID("_GBuffer2"), gBufferViews[2]);
            pass->SetProperty(NAME_ID("_GBuffer3"), gBufferViews[3]);
            pass->SetProperty(NAME_ID("_DepthMap"), sceneDepthView);
            pass->SetProperty(NAME_ID("SceneColorTex"), sceneColorViewIn);
            UInt2 viewSize = sceneColorViewIn->GetSize();
            Float4 viewSizeAndInvSize = {
                static_cast<float>(viewSize.x), static_cast<float>(viewSize.y),
                1.f / viewSize.x, 1.f / viewSize.y
            };
            pass->SetProperty(NAME_ID("_View_SizeAndInvSize"), viewSizeAndInvSize);
            pass->SetProperty(NAME_ID("IsolatePixelFlickingSuppression"), mSetting.mSmartGISetting.mIsolatedPixelFlickingSuppression);
            pass->SetProperty(NAME_ID("DEBUG_SHOW_ISOLATED_PIXEL"), mSetting.mSmartGISetting.mDebugShowIsolatedPixel);
            pass->SetProperty(NAME_ID("IsolatePixelThreshold"), std::clamp(mSetting.mSmartGISetting.mIsolatedPixelThreshold, 1u, 8u));

            auto smartGISetting = pipeline->GetSetting()->mIndirectLightingCompositeSettings.mSmartGISetting;
            pass->SetProperty(NAME_ID("IndirectLightIntensity"), smartGISetting.mIndirectLightingIntensity);
            pass->SetProperty(NAME_ID("IndirectSpecularLightingIntensity"), smartGISetting.mIndirectSpecularLightingIntensity);
            pass->SetProperty(NAME_ID("IndirectDiffuseLightingIntensity"), smartGISetting.mIndirectDiffuseLightingIntensity);
            pass->SetProperty(NAME_ID("DiffuseBoost"), smartGISetting.mDiffuseBoost);
            pass->SetProperty(NAME_ID("FoliageSkyLightLerpDistance"), smartGISetting.mFoliageSkyLightLerpDistance);
            pass->SetProperty(NAME_ID("LongDistanceUseSkyLightDistStart"), smartGISetting.mLongDistanceUseSkyLightDistStart);
            pass->SetProperty(NAME_ID("LongDistanceUseSkyLightDistLerp"), smartGISetting.mLongDistanceUseSkyLightDistLerp);
            pass->SetProperty(NAME_ID("MaxRoughnessToTrace"), smartGISetting.GetMaxRoughnessToTrace(gameContext));
            pass->SetProperty(NAME_ID("InvRoughnessFadeLength"), smartGISetting.GetInvRoughnessFadeLength(gameContext));
            pass->SetProperty(NAME_ID("FOLIAGE_TWOSIDED_LIGHTING_MODE"), static_cast<int>(smartGISetting.mFoliageTwosidedLightingMode));
            pass->SetProperty(NAME_ID("ENABLE_SKY_LIGHT_REALTIME_CAPTURE"), gameContext.mRenderWorld->GetRenderSystem<SkyLightSystemR>()->IsSkyLightRealtimeCapture());

            pass->SetProperty(NAME_ID("LONG_DISTANCE_USE_SKY_LIGHT"), false);   // [Not enable now]
            auto* skyLightSystem = gameContext.mRenderWorld->GetRenderSystem<SkyLightSystemR>();
            pass->SetProperty(BuiltInProperty::ce_SkyLightIntensity, skyLightSystem->GetSkyLightIntensity());

#if defined(CE_USE_DOUBLE_TRANSFORM)
            pass->SetProperty(NAME_ID("CE_USE_DOUBLE_TRANSFORM"), true);
#else
            pass->SetProperty(NAME_ID("CE_USE_DOUBLE_TRANSFORM"), false);
#endif

            pass->SetProperty(NAME_ID("VIEW_MODE"), static_cast<int>(pipeline->GetSetting()->viewModeVisualizeType));
            pass->SetProperty(NAME_ID("ENABLE_AO"), mILCTexes.mAOTex != nullptr);
            pass->SetProperty(NAME_ID("ENABLE_AO_RESCALE"), mSetting.mEnableAORescale);
            pass->SetProperty(NAME_ID("ENABLE_SMARTGI"), mILCparams.mEnableSmartGI);
            pass->SetProperty(NAME_ID("ENABLE_REFLECTION_INDIRECT"), mILCparams.mEnableReflectionIndirect);

            pass->SetProperty(NAME_ID("AOTex"), mILCTexes.mAOTex, NGIResourceState::PixelShaderShaderResource);
            pass->SetProperty(NAME_ID("ReflectionIndirectTex"), mILCTexes.mReflectionIndirecTex, NGIResourceState::PixelShaderShaderResource);
            pass->SetProperty(NAME_ID("SmartGIDiffuseTex"), mILCTexes.mSmartGIDiffuseTex, NGIResourceState::PixelShaderShaderResource);
            pass->SetProperty(NAME_ID("SmartGISpecularTex"), mILCTexes.mSmartGISpecularTex, NGIResourceState::PixelShaderShaderResource);
            pass->SetProperty(NAME_ID("EnvBRDFLutMap"), mSetting.EnvBRDFTextureR->GetNGITextureView());
        },
        mSetting.IndirectLightingCompositeMtlR,
        "IndirectLightingComposite",
        true,
        sceneColorViewOut,
        giViewOut);

    return true;
}
}   // namespace cross