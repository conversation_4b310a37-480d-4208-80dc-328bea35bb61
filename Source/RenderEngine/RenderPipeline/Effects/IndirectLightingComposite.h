#pragma once
#include "PassBase.h"
#include "Resource/Material.h"
#include "RenderEngine/RenderMaterial.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"
#include "ReflectionIndirect.h"
#include "SmartGIPass.h"
#include "AmbientOcclusion.h"

// This pass provide the indirect lighting composition after gpass
// Including specular GI, provided by Reflection_indirect, which combines SSR/ReflectionProbe/SkyLight
// And diffuse GI, provide by SmartGI, if SmartGI is closed, use SkyLight's SH
// AO is considered, if GTAO is enabled
namespace cross {

struct IndirectLightingCompositeTexs
{
    REDTextureView* mReflectionIndirecTex{nullptr};
    REDTextureView* mSmartGIDiffuseTex{nullptr};
    REDTextureView* mSmartGISpecularTex{nullptr};
    REDTextureView* mAOTex{nullptr};
};

struct IndirectLightingCompositeParams
{
    bool mEnableSmartGI{true};
    bool mEnableReflectionIndirect{true};
};

class CEMeta(Editor) RENDER_ENGINE_API IndirectLightingCompositeSetting : public PassSetting
{
public:
    CE_Virtual_Serialize_Deserialize;
    RENDER_PIPELINE_RESOURCE(Material, IndirectLightingCompositeMtl, "Material/IndirectLightingComposite.nda", "Indirect Lighting Composite Material", "", "");
    RENDER_PIPELINE_RESOURCE(Texture, EnvBRDFTexture, "Texture/envBRDFlut.nda", "EnvBRDF Texture", "", "");

    // AmbientOcclusion setting
    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", DisplayName = "Ambient Occlusion Settings", ToolTips = "AO"))
    AmbientOcclusionSetting mAOSetting;

    // ReflectionIndirect setting
    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", DisplayName = "ReflectionIndirect Settings", ToolTips = "ReflectionIndirect"))
    ReflectionIndirectSetting mReflectionIndirectSetting;
    
    // SmartGI setting
    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", DisplayName = "SmartGI Settings", ToolTips = "SmartGI"))
    SmartGIPassSettings mSmartGISetting;

    // ILC Setting
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Enable AO Rescale "))
    bool mEnableAORescale{false};

    virtual void Initialize() override;
};

class RENDER_ENGINE_API IndirectLightingComposite : public PassBase<IndirectLightingCompositeSetting, IndirectLightingComposite>
{
public:
    RenderWorld* mWorld;
    void FillInput(const GameContext& gameContext);

private:
    RenderingExecutionDescriptor* mRED;

protected:
    IndirectLightingCompositeTexs mILCTexes;
    IndirectLightingCompositeParams mILCparams;
    
    bool ExecuteImp(const GameContext& gameContext, const std::array<REDTextureView*, 4>& gBufferViews, REDTextureView* depthView, REDTextureView* sceneColorViewIn, REDTextureView* sceneColorViewOut, REDTextureView*& giViewOut);

    friend PassBase<IndirectLightingCompositeSetting, IndirectLightingComposite>;
};
}