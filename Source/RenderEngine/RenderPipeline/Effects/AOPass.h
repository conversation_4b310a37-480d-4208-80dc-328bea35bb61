#pragma once
#include "PassBase.h"
#include "Resource/Resource.h"
#include "Resource/Material.h"
#include "RenderEngine/RenderMaterial.h"
#include "RenderEngine/ComputeShaderR.h"
#include "RenderEngine/RayTracingShaderR.h"

namespace cross
{
    #define GTAO_DEPTH_MIP_LEVELS                    5                   // this one is hard-coded to 5 for now
    #define GTAO_NUMTHREADS_X                        8                   // these can be changed
    #define GTAO_NUMTHREADS_Y                        8                   // these can be changed

    class GTAOSettings :public PassSetting
    {
    public:
        CE_Virtual_Serialize_Deserialize

        RENDER_PIPELINE_RESOURCE(ComputeShader, GTAOComputeShader, "PipelineResource/FFSRP/Shader/Features/AO/GTAO.compute.nda", "Ground Truth Ambient Occlusion Compute Shader", "", "");
        RENDER_PIPELINE_RESOURCE(ComputeShader, RTAOComputeShader, "PipelineResource/FFSRP/Shader/Features/AO/RTAO.compute.nda", "Ray Tracing Ambient Occlusion Compute Shader", "", "");
        RENDER_PIPELINE_RESOURCE(RayTracingShader, RTAORayTracingShader, "PipelineResource/FFSRP/Shader/RayTracing/DefaultRT.raytracing.nda", "Ray Tracing Abiment Occlusion Ray Tracing Shader", "", "");
        
        CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Enable Ray Tracing Ambient Occlusion"))
        bool mEnableRTAO = false;

        CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Enable RTAO SVGF"))
        bool mEnableSVGF = false;
        
        CEMeta(Serialize, Editor) CECSAttribute(JsonProperty("EnableBentNormal"))
        bool EnableBentNormal = true;
        CEMeta(Serialize, Editor) CECSAttribute(JsonProperty("EnableMultiBounce"))
        bool EnableMultiBounce = true;

        CEMeta(Serialize, Editor) CECSAttribute(JsonProperty("EnableSliceNoise"))
        bool EnableSliceNoise = true;
        CEMeta(Serialize, Editor) CECSAttribute(JsonProperty("EnableStepNoise"))
        bool EnableStepNoise = true;

        CEMeta(Serialize, Editor) CECSAttribute(JsonProperty("RelativeSplitDepthDifference"))
            float RelativeSplitDepthDifference = 20.f;
        CEMeta(Serialize, Editor) CECSAttribute(JsonProperty("EffectRadius"))
            float EffectRadius = 100.f;
        CEMeta(Serialize, Editor) CECSAttribute(JsonProperty("DenoisePassNum"))
            float DenoisePassNum = 1;
        CEMeta(Serialize, Editor) CECSAttribute(JsonProperty("DenoiseBlurBeta"))
            float DenoiseBlurBeta = 1.2f;

        CEMeta(Serialize, Editor) CECSAttribute(JsonProperty("UseDefaultConstants"))
        bool UseDefaultConstants = false;
        // the following 6 constants reduce performance if provided as dynamic values; if these constants are not required to be dynamic and they match default values, 
        // set "UseDefaultConstants" and the code will compile into a more efficient shader
        CEMeta(Serialize, Editor) CECSAttribute(JsonProperty("EffectFalloffRange"))         // distant samples contribute less
        float EffectFalloffRange = 1.f;
        CEMeta(Serialize, Editor) CECSAttribute(JsonProperty("SampleDistributionPower"))    // small crevices more important than big surfaces
        float SampleDistributionPower = 4.0f;
        CEMeta(Serialize, Editor) CECSAttribute(JsonProperty("ThinOccluderCompensation"))   // the new 'thickness heuristic' approach
        float ThinOccluderCompensation = 0.0f;
        CEMeta(Serialize, Editor) CECSAttribute(JsonProperty("VisibilityValuePower"))       // modifies the visibility value using power function - this allows some of the above heuristics to do different things
        float VisibilityValuePower = 1.f;
        CEMeta(Serialize, Editor) CECSAttribute(JsonProperty("Intensity"))                  // regulates the final AO value linearly
        float Intensity = 1.0f;
        CEMeta(Serialize, Editor) CECSAttribute(JsonProperty("DepthMIPSamplingOffset"))     // main trade-off between performance (memory bandwidth) and quality (temporal stability is the first affected, thin objects next)
        float DepthMIPSamplingOffset = 1.f;

        virtual void Initialize() override;
    };

    class RENDER_ENGINE_API AOPass : public PassBase<GTAOSettings, AOPass>
    {
        friend PassBase<GTAOSettings, AOPass>;

    public:
        AOPass();
        ~AOPass();

        static PassDesc GetPassDesc();

    public:
        REDTextureView* output_aoView = nullptr;

    protected:
        bool ExecuteImp(const GameContext& gameContext, bool enableTemporalNoise, const REDTextureView* depthStencilView, const std::array<REDTextureView*, 4>& gBufferViews, 
            REDTextureView*& aoView);

    private:
        void CreateHilbertLUT();
        void PrepareNGIResource();

        void DestroyHilbertLUT();
        void DestroyNGIResource();

        // From https://www.shadertoy.com/view/3tB3z3 - except we're using R2 here
        #define HILBERT_LEVEL    6U
        #define HILBERT_WIDTH    ( (1U << HILBERT_LEVEL) )
        #define HILBERT_AREA     ( HILBERT_WIDTH * HILBERT_WIDTH )
        inline UInt32 HilbertIndex(UInt32 posX, UInt32 posY)
        {
            UInt32 index = 0U;
            for (UInt32 curLevel = HILBERT_WIDTH / 2U; curLevel > 0U; curLevel /= 2U)
            {
                UInt32 regionX = (posX & curLevel) > 0U;
                UInt32 regionY = (posY & curLevel) > 0U;
                index += curLevel * curLevel * ((3U * regionX) ^ regionY);
                if (regionY == 0U)
                {
                    if (regionX == 1U)
                    {
                        posX = UInt32((HILBERT_WIDTH - 1U)) - posX;
                        posY = UInt32((HILBERT_WIDTH - 1U)) - posY;
                    }

                    UInt32 temp = posX;
                    posX = posY;
                    posY = temp;
                }
            }
            return index;
        }

    private:
        bool mNGIInit = false;
        std::unique_ptr<NGIBuffer> mHilbertLUT;
        std::unique_ptr<NGIBufferView> mHilbertLUTView;

        static REDTextureView* GetHistoryTextureView(RenderingExecutionDescriptor* RED, REDTexture* historyTex, REDTextureView* currentTexView, NGITextureAspect aspect = NGITextureAspect::Color);

        static NGITextureViewDesc GetTexture2DViewDesc(NGITextureUsage usage, GraphicsFormat format, NGITextureAspect aspect = NGITextureAspect::Color);

        static NGITextureDesc GetTexture2DDesc(GraphicsFormat format, UInt32 width, UInt32 height, NGITextureUsage usage);
        
        REDTextureView* mHistroyAOView = nullptr;
    };
}