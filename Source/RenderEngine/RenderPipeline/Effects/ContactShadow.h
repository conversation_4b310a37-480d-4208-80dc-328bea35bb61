#pragma once
#include "PassBase.h"
#include "RayDebug.h"

namespace cross {
class CEMeta(Editor) RENDER_ENGINE_API ContactShadowSettings : public PassSetting
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual void Initialize() override;

public:
    CEMeta(Serial<PERSON>, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "")) bool HeightmapShadow_Enable = true;
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bAdvanced = true)) int HeightmapShadow_Steps = 16;
    
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "")) bool ContactShadow_Enable = true;
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "")) bool ContactShadow_FoliageOnly = false;

    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bAdvanced = true)) float ContactShadow_NearDistance = 70.0f;
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bAdvanced = true)) float ContactShadow_FarDistance = 200.0f;

    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bAdvanced = true)) float ContactShadow_StepUV_Near = 0.01f;
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bAdvanced = true)) int ContactShadow_MinSteps_Near = 2;
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bAdvanced = true)) int ContactShadow_MaxSteps_Near = 16;
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bAdvanced = true)) float ContactShadow_RayLength_Near = 1.0f;
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bAdvanced = true)) float ContactShadow_ThresholdScale_Near = 1.0f;
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bAdvanced = true)) float ContactShadow_DepthBias_Near = 0.01f;
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bAdvanced = true)) float ContactShadow_NormalBias_Near = 0.02f;

    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bAdvanced = true)) float ContactShadow_StepUV_Far = 0.005f;
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bAdvanced = true)) int ContactShadow_MinSteps_Far = 2;
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bAdvanced = true)) int ContactShadow_MaxSteps_Far = 16;
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bAdvanced = true)) float ContactShadow_RayLength_Far = 80.0f;
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bAdvanced = true)) float ContactShadow_ThresholdScale_Far = 1.0f;
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bAdvanced = true)) float ContactShadow_DepthBias_Far = 0.01f;
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bAdvanced = true)) float ContactShadow_NormalBias_Far = 0.02f;
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bAdvanced = true)) float ContactShadow_StepMultiplier = 1.06f;

    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "")) bool Transmission_Enable = true;
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bAdvanced = true)) float Transmission_StepUV = 0.066f;
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bAdvanced = true)) int Transmission_MinSteps = 8;
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bAdvanced = true)) int Transmission_MaxSteps = 32;
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bAdvanced = true)) float Transmission_DetectLength = 10.0f;
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "", bAdvanced = true)) float Transmission_ThresholdScale = 2.0f;
    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "")) float Transmission_Density = 1000.0f;

    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DebugRay", bAdvanced = true)) bool EnableDebugRay = false;
    CEMeta(Serialize, Reflect, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "DebugRayFreeze", bAdvanced = true)) bool EnableDebugRayFreeze = false;

    RENDER_PIPELINE_RESOURCE(ComputeShader, ContactShadowComputeShader, "Shader/Lighting/Shadow/ContactShadows.compute.nda", "Contact Shadow Compute Shader", "", "");
};

class RENDER_ENGINE_API ContactShadow : public PassBase<ContactShadowSettings, ContactShadow>
{
public:
    static void SetHeightmapListDirty();

    bool IsContactShadowTransmissionEnabled();

protected:
    bool ExecuteImp(const GameContext& gameContext, REDTextureView* depthOnlyView, REDTextureView* gBuffer1MapView, REDTextureView* gBuffer2MapView, 
        REDTextureView*& contactShadowView, UInt32 frameCount, RenderWorld* renderWorld);

    friend PassBase<ContactShadowSettings, ContactShadow>;

    std::unique_ptr<cross::NGITexture> mHeightMaps;
    std::unique_ptr<cross::NGITextureView> mHeightMapsView;

    std::vector<NGITextureView*> mHeightmapListLast;

    // Begin: the following code are moved from TerrainSystemR to here, since contact shadow related logic should not appear on terrain system @andersjiang
    void CollectHeightmapTextures(RenderWorld* renderWorld);

    std::vector<NGITextureView*> mHeightmapList;
    std::vector<UInt4> mTileNodeInfoList;
    std::vector<UInt4> mTileGridList;
    UInt32 mTileSize = 0;

    Float4 mTerrainTranslation;
    Float4 mTerrainScaling;
    // End

    static bool sHeightmapListDirty;

private:
    RayDebug mRayDebug;
};
}   // namespace cross