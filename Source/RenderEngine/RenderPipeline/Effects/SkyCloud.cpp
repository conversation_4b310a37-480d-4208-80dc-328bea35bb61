#include "SkyCloud.h"
#include "CECommon/Common/FrameCounter.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RenderCamera.h"
#include "RenderEngine/LightSystemR.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"
#include "RenderEngine/RenderPipeline/RenderPipelineR.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipeline.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/CloudSystemR.h"
#include "RenderEngine/RenderWorldConst.h"
#include "RenderEngine/SkyAtmosphereSystemR.h"
#include "Resource/AssetStreaming.h"
#include "NativeGraphicsInterface/NGIManager.h"

namespace cross {

    void SkyCloudSetting::Initialize()
    {
        LOAD_RENDER_PIPELINE_MATERIAL(SkyCloudMateial);
        LOAD_RENDER_PIPELINE_TEXTURE(BlueNoise);
    }


PassDesc SkyCloud::GetPassDesc()
{
    return PassDesc("SkyCloud", "Volumetric Sky Cloud");
}

void SkyCloud::SetScreenSize(uint32_t width, uint32_t height)
{
    mWidth = width;
    mHeight = height;
}

bool SkyCloud::ExecuteImp(const GameContext& gameContext, REDTextureView* sceneDepth, CloudResources& cloudRes, CLOUD_PIPE_METHOD method)
{
    DoTexturesSizeCalculation(gameContext, method);

    REDTextureView* cloudIntersectionTex{nullptr};
    REDTextureView* downsampleDepthTex{nullptr};
    REDTextureView* cloudColorTex{nullptr};
    REDTextureView* cloudDepthTex{nullptr};

    mRED->BeginRegion("VolumetricCloud");

    // Assemble cloud passes
    switch (method)
    {
    case SIMPLE:
        // raymarching and just apply
        AssembleCloudRayMarchingPass(gameContext, sceneDepth, cloudColorTex, cloudDepthTex);
        cloudRes.mCloudColorTex = std::move(cloudColorTex);
        cloudRes.mDownsampleDepthTex = sceneDepth;
        break;
        // down sample depth -> ray marching -> temporal reconstruction -> apply
    case SPATIAL_AND_TEMPORAL:
        // normally
        AssembleCloudIntersectionPass(gameContext, cloudIntersectionTex);
        AssembleDownSampleDepthPass(gameContext, sceneDepth, cloudIntersectionTex, downsampleDepthTex);
        AssembleCloudRayMarchingPass(gameContext, downsampleDepthTex, cloudColorTex, cloudDepthTex);
        AssembleTemporalReconsPass(gameContext, downsampleDepthTex, cloudColorTex, cloudDepthTex, cloudIntersectionTex);

        cloudRes.mCloudColorTex = mHistoryColorTexView.get();
        cloudRes.mDownsampleDepthTex = std::move(downsampleDepthTex);
        cloudRes.mCloudIntersectionTex = std::move(cloudIntersectionTex);
        break;
    default:
        break;
    }
    mRED->EndRegion();
    return true;
}

// The final outputs are: tempReconsTexWidth tempReconsTexHeight raymarchingTexWidth raymarchingTexHeight, spatialDownRatio, tempDownRatio
void SkyCloud::DoTexturesSizeCalculation(const GameContext& gameContext, CLOUD_PIPE_METHOD& method)
{
    // usually for sky light
    if (method == SIMPLE)
    {
        mFullTexSize.x = static_cast<float>(mWidth);
        mFullTexSize.y = static_cast<float>(mHeight);
        mFullTexSize.z = 1.f / std::max(mFullTexSize.x, 1.f);
        mFullTexSize.w = 1.f / std::max(mFullTexSize.y, 1.f);
        mTempReconsTexSize = mFullTexSize;
        mRaymarchingTexSize = mFullTexSize;
        // [important] should reset these params
        mSpatialDownRatio = 1;
        mTempDownRatio = 1;
        return;
    }
    // 2x(usually)
    UInt32 spatialDownLevel = static_cast<UInt32>(mSetting.SpatialDown);
    mSpatialDownRatio = 1u << spatialDownLevel;
    // 2x(usually)
    UInt32 tempDownLevel = static_cast<UInt32>(mSetting.TemporalDown);
    mTempDownRatio = 1u << tempDownLevel;

    uint32_t outputWidth = mWidth;
    uint32_t outputHeight = mHeight;

    uint32_t tempReconsWidth = math::DivideAndRoundUp(outputWidth, mSpatialDownRatio);
    uint32_t tempReconsHeight = math::DivideAndRoundUp(outputHeight, mSpatialDownRatio);

    uint32_t raymarchingWidth = math::DivideAndRoundUp(tempReconsWidth, mTempDownRatio);
    uint32_t raymarchingHeight = math::DivideAndRoundUp(tempReconsHeight, mTempDownRatio);

    // Full size resolution, eg. 4k
    mFullTexSize.x = static_cast<float>(outputWidth);
    mFullTexSize.y = static_cast<float>(outputHeight);
    mFullTexSize.z = 1.f / std::max(mFullTexSize.x, 1.f);
    mFullTexSize.w = 1.f / std::max(mFullTexSize.y, 1.f);

    // Temporal reconstruction resolution, eg, 2k, but if spatial downsize is not enabled, then tempReconsTexSize = FullResTexSize
    mTempReconsTexSize.x = static_cast<float>(tempReconsWidth);
    mTempReconsTexSize.y = static_cast<float>(tempReconsHeight);
    mTempReconsTexSize.z = 1.f / std::max(mTempReconsTexSize.x, 1.f);
    mTempReconsTexSize.w = 1.f / std::max(mTempReconsTexSize.y, 1.f);

    // Ray marching resolution, eg. 1k, but if temporal rendering is not enabled, then raymarchingTexSize = tempReconsTexSize
    mRaymarchingTexSize.x = static_cast<float>(raymarchingWidth);
    mRaymarchingTexSize.y = static_cast<float>(raymarchingHeight);
    mRaymarchingTexSize.z = 1.f / std::max(mRaymarchingTexSize.x, 1.f);
    mRaymarchingTexSize.w = 1.f / std::max(mRaymarchingTexSize.y, 1.f);
}

void SkyCloud::AssembleCloudIntersectionPass(const GameContext& gameContext, REDTextureView*& cloudIntersectionTex)
{
    auto* pipe = gameContext.mRenderPipeline;
    auto* cloudSys = mWorld->GetRenderSystem<CloudSystemR>();
    auto cloudIntersectionUAV = pipe->CreateTextureView2D("CloudIntersectionUAV", static_cast<UInt32>(mTempReconsTexSize.x), static_cast<UInt32>(mTempReconsTexSize.y), GraphicsFormat::R32_SFloat, mNGIRTUsage);
    auto* cloudIntersectionPass = mRED->AllocatePass("Cloud Layers Intersection");
    cloudIntersectionPass->SetProperty(NAME_ID("RWCloudIntersectionTex"), cloudIntersectionUAV, NGIResourceState::ComputeShaderUnorderedAccess);
    cloudIntersectionPass->SetProperty(NAME_ID("TempReconsTexSize"), mTempReconsTexSize);

    UpdateCloudShapeRelatedParamsContext(cloudIntersectionPass->GetContext());
    UpdateCloudLightsIndices(gameContext, cloudIntersectionPass->GetContext());
    cloudSys->UpdateCloudLayerProperties(cloudIntersectionPass);
    cloudSys->UpdateThunderstormProperties(cloudIntersectionPass);
    cloudSys->UpdateCloudDataProperties(cloudIntersectionPass);

    UInt3 groupSize;
    std::string downSamplePassName = "CloudIntersection";
    cloudSys->DownSampleDepthComputeShaderR->GetThreadGroupSize(downSamplePassName, groupSize.x, groupSize.y, groupSize.z);
    cloudIntersectionPass->Dispatch(
        cloudSys->DownSampleDepthComputeShaderR, downSamplePassName, math::DivideAndRoundUp(static_cast<UInt32>(mTempReconsTexSize.x), groupSize.x), math::DivideAndRoundUp(static_cast<UInt32>(mTempReconsTexSize.y), groupSize.y), 1);

    cloudIntersectionTex = IRenderPipeline::CreateTextureView2D(cloudIntersectionUAV, NGITextureUsage::ShaderResource);
}

// normally do 2x downsample depth
void SkyCloud::AssembleDownSampleDepthPass(const GameContext& gameContext, REDTextureView* depthViewIn, REDTextureView* cloudIntersectionTex, REDTextureView*& downsampleDepthTex)
{
    auto* pipe = gameContext.mRenderPipeline;
    auto* cloudSys = mWorld->GetRenderSystem<CloudSystemR>();
    auto downsampleDepthUAV = pipe->CreateTextureView2D("DownSampleDepth", static_cast<UInt32>(mTempReconsTexSize.x), static_cast<UInt32>(mTempReconsTexSize.y), GraphicsFormat::R32_SFloat, mNGIRTUsage);
    auto* downSampleDepthPass = mRED->AllocatePass("DownSample Scene Depth");
    downSampleDepthPass->SetProperty(NAME_ID("SceneDepthTex"), depthViewIn, NGIResourceState::ComputeShaderShaderResource);
    downSampleDepthPass->SetProperty(NAME_ID("CloudIntersectionTex"), cloudIntersectionTex, NGIResourceState::ComputeShaderShaderResource);
    downSampleDepthPass->SetProperty(NAME_ID("DownSampleDepthTex"), downsampleDepthUAV, NGIResourceState::ComputeShaderUnorderedAccess);
    downSampleDepthPass->SetProperty(NAME_ID("TempReconsTexSize"), mTempReconsTexSize);
    downSampleDepthPass->SetProperty(NAME_ID("SpatialDownRatio"), mSpatialDownRatio);
    UInt3 groupSize;
    std::string downSamplePassName = "DownSampleDepth";
    cloudSys->DownSampleDepthComputeShaderR->GetThreadGroupSize(downSamplePassName, groupSize.x, groupSize.y, groupSize.z);
    downSampleDepthPass->Dispatch(cloudSys->DownSampleDepthComputeShaderR, downSamplePassName, math::DivideAndRoundUp(static_cast<UInt32>(mTempReconsTexSize.x), groupSize.x), math::DivideAndRoundUp(static_cast<UInt32>(mTempReconsTexSize.y), groupSize.y), 1);

    downsampleDepthTex = IRenderPipeline::CreateTextureView2D(downsampleDepthUAV, NGITextureUsage::ShaderResource);
}

void SkyCloud::AssembleCloudRayMarchingPass(const GameContext& gameContext, REDTextureView* downsampleDepthTex, REDTextureView*& cloudColorTex, REDTextureView*& cloudDepthTex)
{
    auto* pipe = static_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline);
    auto* cloudSys = mWorld->GetRenderSystem<CloudSystemR>();
    // RGB: cloud Color, A: alpha (1 - transmittance)
    cloudColorTex = pipe->CreateTextureView2D("Cloud Color", static_cast<UInt32>(mRaymarchingTexSize.x), static_cast<UInt32>(mRaymarchingTexSize.y), GraphicsFormat::R16G16B16A16_SFloat, mNGIRTUsage);
    // cloud depth format R32G32, R: cloud_traced_depth meter unit; G: downsampled scene depth
    cloudDepthTex = pipe->CreateTextureView2D("Cloud Depth", static_cast<UInt32>(mRaymarchingTexSize.x), static_cast<UInt32>(mRaymarchingTexSize.y), GraphicsFormat::R32G32_SFloat, mNGIRTUsage);

    gameContext.mRenderPipeline->PostProcess(
        [&](REDPass* pass) {

            UpdateCloudShapeRelatedParamsContext(pass->GetContext());
            UpdateCloudLightsIndices(gameContext, pass->GetContext());
            cloudSys->UpdateCloudLayerProperties(pass);
            cloudSys->UpdateThunderstormProperties(pass);
            cloudSys->UpdateCloudDataProperties(pass);
            // Full resolution depth
            pass->SetProperty(NAME_ID("DownsampleDepth"), downsampleDepthTex, NGIResourceState::PixelShaderShaderResource);
            pass->SetProperty(NAME_ID("RayMarchingTexSize"), mRaymarchingTexSize);
            pass->SetProperty(NAME_ID("TempDownRatio"), mTempDownRatio);

            // integrate fog just when doing cloud raymarching
            if (auto bEnableSFog = pipe->ShouldRenderScreenSpaceFog(); bEnableSFog)
            {
                pass->SetProperty(NAME_ID("EnableSFog"), bEnableSFog);
                if (pipe->GetScreenSpaceFog() != nullptr)
                    pipe->GetScreenSpaceFog()->UpdateFogApplyContext(pass->GetContext());
            }

        },
        cloudSys->mCloudMtl,
        //mSetting.SkyCloudMateialR,
        "SkyCloud",
        true,
        cloudColorTex,
        cloudDepthTex);
}

void SkyCloud::AssembleTemporalReconsPass(const GameContext& gameContext, REDTextureView* downsampleDepthTex, REDTextureView* cloudColorTex, REDTextureView* cloudDepthTex, REDTextureView* cloudIntersectionTex)
{
    ValidateHistoryTexture(static_cast<UInt32>(mTempReconsTexSize.x), static_cast<UInt32>(mTempReconsTexSize.y));

    auto* pipe = gameContext.mRenderPipeline;
    auto* cloudSys = mWorld->GetRenderSystem<CloudSystemR>();
    auto* tempReconsColorOut = pipe->CreateTextureView2D("Temporal Recons Color", static_cast<UInt32>(mTempReconsTexSize.x), static_cast<UInt32>(mTempReconsTexSize.y), GraphicsFormat::R16G16B16A16_SFloat, mNGIRTUsage);

    const auto* camera = gameContext.mRenderPipeline->GetRenderCamera();
    auto reprojectionMat = camera->GetReprojectionClipToPrevClip();

    auto* cloudTempReconsPass = mRED->AllocatePass("Cloud Temporal Reconstruction");
    UpdateCloudShapeRelatedParamsContext(cloudTempReconsPass->GetContext());
    cloudSys->UpdateCloudLayerProperties(cloudTempReconsPass);
    cloudTempReconsPass->SetProperty(NAME_ID("DownsampleDepthTex"), downsampleDepthTex, NGIResourceState::ComputeShaderShaderResource);
    cloudTempReconsPass->SetProperty(NAME_ID("CloudIntersectionTex"), cloudIntersectionTex, NGIResourceState::ComputeShaderShaderResource);

    cloudTempReconsPass->SetProperty(NAME_ID("RayMarchingColorTex"), cloudColorTex, NGIResourceState::ComputeShaderShaderResource);
    cloudTempReconsPass->SetProperty(NAME_ID("RayMarchingDepthTex"), cloudDepthTex, NGIResourceState::ComputeShaderShaderResource);
    cloudTempReconsPass->SetProperty(NAME_ID("HistoryColorTex"), mHistoryColorTexView.get(), NGIResourceState::ComputeShaderShaderResource);
    cloudTempReconsPass->SetProperty(NAME_ID("TempReconsTexSize"), mTempReconsTexSize);
    cloudTempReconsPass->SetProperty(NAME_ID("RayMarchingTexSize"), mRaymarchingTexSize);
    cloudTempReconsPass->SetProperty(NAME_ID("ReprojectionMat"), reprojectionMat);
    cloudTempReconsPass->SetProperty(NAME_ID("TempDownRatio"), mTempDownRatio);
    cloudTempReconsPass->SetProperty(NAME_ID("Debug_Option"), mSetting.DebugOption);
    cloudTempReconsPass->SetProperty(NAME_ID("BlueNoise"), mSetting.BlueNoiseRes->GetNGITextureView());

    cloudTempReconsPass->SetProperty(NAME_ID("TempReconsColorTex"), tempReconsColorOut, NGIResourceState::ComputeShaderUnorderedAccess);
    UInt3 groupSize;
    cloudSys->TemporalReconstructComputeShaderR->GetThreadGroupSize("CloudTemporalReconstruction", groupSize.x, groupSize.y, groupSize.z);
    cloudTempReconsPass->Dispatch(cloudSys->TemporalReconstructComputeShaderR,
                                  "CloudTemporalReconstruction",
                                  math::DivideAndRoundUp(static_cast<UInt32>(mTempReconsTexSize.x), groupSize.x),
                                  math::DivideAndRoundUp(static_cast<UInt32>(mTempReconsTexSize.y), groupSize.y),
                                  1);

    auto* tempReconsColorSRV = pipe->CreateTextureView2D(tempReconsColorOut, NGITextureUsage::ShaderResource);

    // copy temporal reconstruction texture to history cloud texture
    pipe->PostProcessUtil([&](REDPass* pass) { pass->SetProperty("_ColorTex", tempReconsColorSRV); }, mPostMtl, mRED, "blit", true, mHistoryColorTexView.get());
}

// Reprojection history cloud color resident
void SkyCloud::ValidateHistoryTexture(UInt32 historyWidth, UInt32 historyHeight)
{
    NGITextureDesc cloudTexDesc{GraphicsFormat::R16G16B16A16_SFloat, NGITextureType::Texture2D, 1, 1, historyWidth, historyHeight, 1, 1, NGITextureUsage::ShaderResource | NGITextureUsage::RenderTarget | NGITextureUsage::CopyDst};
    NGITextureViewDesc cloudViewDesc{NGITextureUsage::ShaderResource | NGITextureUsage::RenderTarget, GraphicsFormat::R16G16B16A16_SFloat, NGITextureType::Texture2D, NGITextureSubRange{NGITextureAspect::Color, 0, 1, 0, 1}};

    // Handle enforce refresh situation from script(TOD change)
    auto* cloudSys = mWorld->GetRenderSystem<CloudSystemR>();
    bool bRefreshCloudHistory = cloudSys->GetPropertyRefreshCloudHistory();

    if (mHistoryColorTex == nullptr || mHistoryColorTex->GetDesc().Width != historyWidth || mHistoryColorTex->GetDesc().Height != historyHeight || bRefreshCloudHistory)
    {
        mHistoryColorTex = mRED->CreateTexture("HistoryCloudColor", cloudTexDesc);
        mHistoryColorTexView = mRED->CreateTextureView(mHistoryColorTex.get(), cloudViewDesc);

        NGIClearValue clearValue{{0, 0, 0, 0}};
        mRED->AllocatePass("ClearHistoryColor")->ClearTexture(mHistoryColorTexView.get(), clearValue);

        cloudSys->SetPropertyRefreshCloudHistory(false);
    }
}

void SkyCloud::UpdateCloudShapeRelatedParamsContext(RenderContext& context)
{
    auto cloudSys = mWorld->GetRenderSystem<CloudSystemR>();
    auto& cloudRange = cloudSys->GetGlobalSetting().CloudRange;

    bool useWGS84 = cloudRange.ProjectType == CloudProjectType::WGS84;
    bool useSphere = cloudRange.ProjectType == CloudProjectType::Sphere;
    float sphereRadius = cloudRange.SphereRadius;
    if (useWGS84)
        sphereRadius = 6371000.f;
    context.SetProperty("ceFrameNumber", static_cast<float>(cross::frame::GetRenderingFrameNumber()));

    context.SetProperty(NAME_ID("CloudSize"), cloudRange.Size);
    auto cloudUVScale = std::ceil(sphereRadius * 3.14f * 0.25f / cloudRange.Size) * 2.f;
    context.SetProperty(NAME_ID("CloudUVScale"), cloudUVScale);
    context.SetProperty(NAME_ID("CloudCutOffDistance"), cloudRange.CutOffDistance);
    // groundHeight should not be added to BottomHeight and TopHeight, they are ABSOLUTE values
    context.SetProperty(NAME_ID("BottomHeight"), cloudRange.BottomHeight);
    context.SetProperty(NAME_ID("TopHeight"), cloudRange.BottomHeight + cloudRange.Height);
    context.SetProperty(NAME_ID("CloudUseWGS84"), useWGS84);
    context.SetProperty(NAME_ID("CloudUseSphere"), useSphere);
    context.SetProperty(NAME_ID("SphereRadius"), sphereRadius);
    // New Added for Cloud Flow
    context.SetProperty(NAME_ID("WindDirection"), cloudRange.WindDirection);
    context.SetProperty(NAME_ID("CloudSpeed"), cloudRange.CloudSpeed);
    context.SetProperty(NAME_ID("CloudNoiseFlow"), cloudRange.CloudNoiseFlow);
}

void SkyCloud::UpdateCloudLightsIndices(const GameContext& gameContext, RenderContext& context)
{
    auto cloudSys = mWorld->GetRenderSystem<CloudSystemR>();
    auto cloudLightsEntityIDs = cloudSys->GetCloudLightsEntityIDs();
    auto filteredLights = mWorld->GetRenderSystem<LightSystemR>()->FilterLightsByRenderingLayerMask(static_cast<UInt32>(RenderingLayer::LayerCloud));
    auto entityIDs = gameContext.mRenderPipeline->GetLightList();

    // std::vector<UInt32> indices = gameContext.mRenderPipeline->GetLightIndexList();
    std::vector<UInt32> lightIndices;
    for (int i = 0; i < cloudLightsEntityIDs.size(); i++)
    { 
        auto it = std::ranges::find(entityIDs.begin(), entityIDs.end(), cloudLightsEntityIDs[i]);
        if (it != entityIDs.end())
        {
            lightIndices.emplace_back(static_cast<UInt32>(std::distance(entityIDs.begin(), it)));
        }
    }

    for (const auto& entity: filteredLights)
    {
        auto it = std::ranges::find(entityIDs.begin(), entityIDs.end(), entity);
        if (it != entityIDs.end())
        {
            lightIndices.emplace_back(static_cast<UInt32>(std::distance(entityIDs.begin(), it)));
        }
    }

    auto renderSysR = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    UInt32 lightIndexDataByteSize = static_cast<UInt32>(sizeof(UInt32) * lightIndices.size());
    UInt32 lightIndexDataBufferSize = std::max(lightIndexDataByteSize, 1u);
    auto* scratchBuffer = renderSysR->GetScratchBuffer();
    auto lightIndexDataBufferWrap = scratchBuffer->AllocateScratch(NGIBufferUsage::StructuredBuffer, lightIndexDataBufferSize);
    lightIndexDataBufferWrap.MemWrite(0, lightIndices.data(), lightIndexDataByteSize);

    auto bufferToShader = renderSysR->GetTransientResourceManager()->AllocateBufferView(
        NGIBufferViewDesc{
            NGIBufferUsage::StructuredBuffer,
            lightIndexDataBufferWrap.GetNGIOffset(),
            lightIndexDataBufferSize,
            GraphicsFormat::Unknown,
            sizeof(UInt32),
        },
        lightIndexDataBufferWrap.GetNGIBuffer());

    context.SetProperty("_ShineCloudLightIndex", bufferToShader);
    int shineCloudLightCount = static_cast<int>(lightIndices.size());
    context.SetProperty(NAME_ID("shineCloudLightCount"), shineCloudLightCount);
}

bool SkyCloud::AssembleFogAndCloudApplyPass(const GameContext& gameContext, REDTextureView* depthOnlyView, REDTextureView* volumetricFogView, REDTextureView*& sceneView, CloudResources& cloudRes, bool isSkyLight)
{
    auto pipe = static_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline);
    auto gameViewWidth = static_cast<UInt16>(sceneView->mTexture->mDesc.Width);
    auto gameViewHeight = static_cast<UInt16>(sceneView->mTexture->mDesc.Height);

    auto* sceneColor = pipe->CreateTextureView2D("Scene Color Texture", gameViewWidth, gameViewHeight, sceneView->mTexture->mDesc.Format, NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
    pipe->PostProcessUtil([&](REDPass* pass) { pass->SetProperty("_ColorTex", sceneView, NGIResourceState::PixelShaderShaderResource); }, mPostMtl, mRED, "blit", true, sceneColor);

    auto cloudSystem = mWorld->GetRenderSystem<CloudSystemR>();

    auto& cloudRange = cloudSystem->GetGlobalSetting().CloudRange;
    bool bEnableCloud = cloudSystem->EnableVolumetricCloud() && cloudRes.mCloudColorTex;

    gameContext.mRenderPipeline->PostProcess(
        [&](REDPass* pass) {
            pass->SetProperty(NAME_ID("sceneTex"), sceneColor, NGIResourceState::PixelShaderShaderResource);
            pass->SetProperty(NAME_ID("depthTex"), depthOnlyView, NGIResourceState::PixelShaderShaderResource);
            pass->SetProperty(NAME_ID("EnableCloud"), bEnableCloud);

            if (bEnableCloud)
            {
                UpdateCloudShapeRelatedParamsContext(pass->GetContext());
                UpdateCloudLightsIndices(gameContext, pass->GetContext());
                cloudSystem->UpdateCloudLayerProperties(pass);
                cloudSystem->UpdateThunderstormProperties(pass);
                cloudSystem->UpdateCloudDataProperties(pass);

                pass->SetProperty(NAME_ID("cloudTex"), cloudRes.mCloudColorTex, NGIResourceState::PixelShaderShaderResource);
                pass->SetProperty(NAME_ID("downsampleDepthTex"), cloudRes.mDownsampleDepthTex, NGIResourceState::PixelShaderShaderResource);
                pass->SetProperty(NAME_ID("cloudIntersectionTex"), cloudRes.mCloudIntersectionTex, NGIResourceState::PixelShaderShaderResource);
                const auto* camera = gameContext.mRenderPipeline->GetRenderCamera();
                auto farPlane = camera->GetFarPlane();
                pass->SetProperty(NAME_ID("FarPlane"), farPlane);
                pass->SetProperty(NAME_ID("CloudUseWGS84"), cloudRange.ProjectType == CloudProjectType::WGS84);
                pass->SetProperty(NAME_ID("IsSkyLight"), isSkyLight);
            }

            bool enableVFog = pipe->ShouldRenderVolumetricFog() && volumetricFogView;
            pass->SetProperty(NAME_ID("useVFog"), enableVFog);
            if (enableVFog)
            {
                pass->SetProperty(NAME_ID("fogTex"), volumetricFogView, NGIResourceState::PixelShaderShaderResource);
                pass->SetProperty(NAME_ID("VFogFar"), gameContext.mRenderPipeline->GetBuiltInValue<PassSemanticName::VFogVolumeCutOffDistance>());
            }
            bool enableSFog = pipe->ShouldRenderScreenSpaceFog();
            pass->SetProperty(NAME_ID("EnableSFog"), enableSFog);
            pass->SetProperty(NAME_ID("ENABLE_MULTI_LAYER_FOG"), pipe->ShouldUseMultiLayerFog());
            

        },
        mPostMtl,
        "sky_cloud_apply",
        false,
        sceneView);

    return true;
}

// Under cloud stat : vary from [0, 1], for raining control
void SkyCloud::AllocateUnderCloudStatBuffer()
{
    NGIBufferUsage usage = NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::StructuredBuffer | NGIBufferUsage::CopySrc;

    mUnderCloudBuffer = mRED->CreateBuffer("UnderCloudTestBuffer", NGIBufferDesc{1 * sizeof(float), usage});

    mUnderCloudBufferUAV = mRED->CreateBufferView(mUnderCloudBuffer.get(), NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer, 0u, 1 * sizeof(float), GraphicsFormat::Unknown, sizeof(float)});
    mUnderCloudBufferSRV = mRED->CreateBufferView(mUnderCloudBuffer.get(), NGIBufferViewDesc{NGIBufferUsage::StructuredBuffer, 0u, 1 * sizeof(float), GraphicsFormat::Unknown, sizeof(float)});
}

// If the camera is under cloud
void SkyCloud::AssembleUnderCloudStatPass(const GameContext& gameContext, REDBufferView*& outUnderCloud)
{
    const auto camera = gameContext.mRenderPipeline->GetRenderCamera();
    auto* cloudSys = gameContext.mRenderWorld->GetRenderSystem<CloudSystemR>();

    if (mUnderCloudBuffer == nullptr)
    {
        AllocateUnderCloudStatBuffer();
    }

    auto* UnderCloudStatPass = mRED->AllocatePass("UnderCloudStat");

    cloudSys->UpdateCloudLayerProperties(UnderCloudStatPass);
    cloudSys->UpdateThunderstormProperties(UnderCloudStatPass);
    cloudSys->UpdateCloudDataProperties(UnderCloudStatPass);
    cloudSys->UpdateCloudShadowParameters(UnderCloudStatPass);
    UnderCloudStatPass->SetProperty(NAME_ID("worldPos"), camera->GetCameraOrigin());
    UpdateCloudShapeRelatedParamsContext(UnderCloudStatPass->GetContext());
    UnderCloudStatPass->SetProperty(NAME_ID("_OutUnderCloudTestResult"), mUnderCloudBufferUAV.get(), NGIResourceState::ComputeShaderUnorderedAccess);

    UInt3 groupSize;
    UnderCloudStatPass->Dispatch(cloudSys->UnderCloudStatComputeShaderR, "UnderCloudTestCS", 1, 1, 1);

    outUnderCloud = mUnderCloudBufferSRV.get();
}

}   // namespace cross