#include "MotionBlur.h"
#include "CECommon/Common/EngineGlobal.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RendererSystemR.h"
#include "Resource/AssetStreaming.h"
#include "RenderEngine/RenderCamera.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"
#include "RenderEngine/RenderPipeline/RenderPipelineR.h"
#include "NativeGraphicsInterface/NGIManager.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipelineSetting.h"

namespace cross {
/**
 * Utility function to create the inverse depth projection transform to be used
 * by the shader system.
 * @param ProjMatrix - used to extract the scene depth ratios
 * @param InvertZ - projection calc is affected by inverted device Z
 * @return vector containing the ratios needed to convert from device Z to world Z
 */
Float4 CreateInvDeviceZToWorldZTransform(const Float4x4& ProjMatrix)
{
    // The perspective depth projection comes from the the following projection matrix:
    //
    // | 1  0  0  0 |
    // | 0  1  0  0 |
    // | 0  0  A  1 |
    // | 0  0  B  0 |
    //
    // Z' = (Z * A + B) / Z
    // Z' = A + B / Z
    //
    // So to get Z from Z' is just:
    // Z = B / (Z' - A)
    //
    // Note a reversed Z projection matrix will have A=0.
    //
    // Done in shader as:
    // Z = 1 / (Z' * C1 - C2)   --- Where C1 = 1/B, C2 = A/B
    //

    float DepthMul = ProjMatrix.m22;
    float DepthAdd = ProjMatrix.m32;

    if (DepthAdd == 0.f)
    {
        // Avoid dividing by 0 in this case
        DepthAdd = 0.00000001f;
    }

    // perspective
    // SceneDepth = 1.0f / (DeviceZ / ProjMatrix.M[3][2] - ProjMatrix.M[2][2] / ProjMatrix.M[3][2])

    // ortho
    // SceneDepth = DeviceZ / ProjMatrix.M[2][2] - ProjMatrix.M[3][2] / ProjMatrix.M[2][2];

    // combined equation in shader to handle either
    // SceneDepth = DeviceZ * View.InvDeviceZToWorldZTransform[0] + View.InvDeviceZToWorldZTransform[1] + 1.0f / (DeviceZ * View.InvDeviceZToWorldZTransform[2] - View.InvDeviceZToWorldZTransform[3]);

    // therefore perspective needs
    // View.InvDeviceZToWorldZTransform[0] = 0.0f
    // View.InvDeviceZToWorldZTransform[1] = 0.0f
    // View.InvDeviceZToWorldZTransform[2] = 1.0f / ProjMatrix.M[3][2]
    // View.InvDeviceZToWorldZTransform[3] = ProjMatrix.M[2][2] / ProjMatrix.M[3][2]

    // and ortho needs
    // View.InvDeviceZToWorldZTransform[0] = 1.0f / ProjMatrix.M[2][2]
    // View.InvDeviceZToWorldZTransform[1] = -ProjMatrix.M[3][2] / ProjMatrix.M[2][2] + 1.0f
    // View.InvDeviceZToWorldZTransform[2] = 0.0f
    // View.InvDeviceZToWorldZTransform[3] = 1.0f

    bool bIsPerspectiveProjection = ProjMatrix.m33 < 1.0f;

    if (bIsPerspectiveProjection)
    {
        float SubtractValue = DepthMul / DepthAdd;

        // Subtract a tiny number to avoid divide by 0 errors in the shader when a very far distance is decided from the depth buffer.
        // This fixes fog not being applied to the black background in the editor.
        SubtractValue -= 0.00000001f;

        return Float4(0.0f, 0.0f, 1.0f / DepthAdd, SubtractValue);
    }
    else
    {
        return Float4(1.0f / ProjMatrix.m22, -ProjMatrix.m32 / ProjMatrix.m22 + 1.0f, 0.0f, 1.0f);
    }
}
void MotionBlur::FillInput(const GameContext& gameContext) 
{
    mInput.GenerateInputData(gameContext);
    mSetting = gameContext.mRenderPipeline->GetPostProcessVolumeSetting()->BaseSettings.mMotionBlurSetting;
}

void MotionBlurInput::GenerateInputData(const GameContext& gameContext) 
{
    auto ffssetting = dynamic_cast<const FFSRenderPipelineSetting*>(gameContext.mRenderPipeline->GetSetting());
    color = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::LastPassColor>();
    depthOnly = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::DisplayDepthStencil>();
    auto gbuffer = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::GBufferViews>();
    motionVector = gbuffer[3];
    postProcessMtl = gameContext.mRenderPipeline->GetPostProcessMtl();
    upscaleValue = static_cast<float>(color->GetHeight()) / motionVector->GetHeight();
    velocityFlattenR = ffssetting->VelocityFlattenComputeShaderR;
    velocityNeighborMaxR = ffssetting->VelocityNeighborMaxComputeShaderR;
    motionBlurR = ffssetting->MotionBlurComputeShaderR;
}
void MotionBlurOutput::SetOutputData(const GameContext& gameContext) 
{
    gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::LastPassColor>() = outputcolor;
}
void MotionBlur::Execute(const GameContext& gameContext)
{
    QUICK_SCOPED_CPU_TIMING(__FUNCTION__);
    if (mSetting.enable && mSetting.IsNeedToExcute())
    {
        const float intensity = mSetting.Intensity;   // Clamp(mSetting.shutterAngle, 0.f, 360.f) / 360.f;

        auto color = mInput.color;
        auto depthOnly = mInput.depthOnly;
        auto motionVector = mInput.motionVector;

        auto desc = color->mTexture->mDesc;

        const UInt32 ScreenWidth = desc.Width;
        const UInt32 ScreenHeight = desc.Height;

        // percent in screen width
        const float UVVelocityMax = mSetting.MaxDistortion / 100.0f;
        const float VelocityUVToPixel = ScreenWidth * 0.5f;

        // velocity max in pixel space (0, screen size)
        const float VelocityMax = UVVelocityMax * VelocityUVToPixel;

        // Apply exposure time scale by fixed FPS
        auto timeScale = 1.0f;

        if (mSetting.TargetFPS > 0)
        {
            auto dt = MathUtils::Max(EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetDeltaTime(), 1.e-7f);
            timeScale = (1.0f / mSetting.TargetFPS) / dt;
        }

        auto RED = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();
        RED->BeginRegion("MotionBlur");

        auto Mtl = mInput.postProcessMtl;
        Float4x4 proj = gameContext.mRenderCamera->GetProjMatrix();

        if (!mOutput.outputcolor)
            mOutput.outputcolor = IRenderPipeline::CreateTextureView2D("mb_output", ScreenWidth, ScreenHeight, desc.Format, NGITextureUsage::ShaderResource | NGITextureUsage::RenderTarget | NGITextureUsage::UnorderedAccess);

        const float VelocityScale = intensity * timeScale * VelocityUVToPixel;

        UInt32 GroupSize = 16, y = 0, z = 0;
        mInput.velocityFlattenR->GetThreadGroupSize("MainCS", GroupSize, y, z);
        UInt32 VelocityTileWidth = (ScreenWidth + GroupSize - 1) / GroupSize;
        UInt32 VelocityTileHeight = (ScreenHeight + GroupSize - 1) / GroupSize;

        auto VelocityFlat = IRenderPipeline::CreateTextureView2D("VelocityFlat", ScreenWidth, ScreenHeight, GraphicsFormat::R11G11B10_UFloatPack32, NGITextureUsage::ShaderResource | NGITextureUsage::UnorderedAccess);

        auto VelocityTile = IRenderPipeline::CreateTextureView2D(
            "VelocityTile", VelocityTileWidth, VelocityTileHeight, GraphicsFormat::R16G16B16A16_SFloat, NGITextureUsage::ShaderResource | NGITextureUsage::RenderTarget | NGITextureUsage::UnorderedAccess);

        auto red = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();
        auto* velocityFlatten = red->AllocatePass("MotionBlur_velocity_flatten");
        velocityFlatten->SetProperty(NAME_ID("ViewportMin"), UInt2{0u, 0u});
        velocityFlatten->SetProperty(NAME_ID("ViewportMax"), UInt2{ScreenWidth, ScreenHeight});
        velocityFlatten->SetProperty(NAME_ID("VelocityScale"), VelocityScale);
        velocityFlatten->SetProperty(NAME_ID("fReprojection"), gameContext.mRenderCamera->GetReprojectionClipToPrevClip());
        velocityFlatten->SetProperty(NAME_ID("DownscaleValue"), 1.0f / mInput.upscaleValue);
        velocityFlatten->SetProperty(NAME_ID("VelocityTexture_TexelSize"), Float2{1.0f / motionVector->mTexture->mDesc.Width, 1.0f / motionVector->mTexture->mDesc.Height});
        velocityFlatten->SetProperty(NAME_ID("VelocityMax"), VelocityMax);
        // velocityFlatten->SetProperty(NAME_ID("DeviceToViewDepth"), Float4{proj.m22, proj.m23, proj.m32, FLT_EPSILON});
        velocityFlatten->SetProperty(NAME_ID("DeviceToViewDepth"), CreateInvDeviceZToWorldZTransform(proj));
        velocityFlatten->SetProperty(NAME_ID("DepthTexture"), depthOnly, NGIResourceState::ComputeShaderShaderResource);
        velocityFlatten->SetProperty(NAME_ID("VelocityTexture"), motionVector, NGIResourceState::ComputeShaderShaderResource);
        velocityFlatten->SetProperty(NAME_ID("OutVelocityFlatTexture"), VelocityFlat, NGIResourceState::ComputeShaderUnorderedAccess);
        velocityFlatten->SetProperty(NAME_ID("OutVelocityTileTexture"), VelocityTile, NGIResourceState::ComputeShaderUnorderedAccess);
        velocityFlatten->Dispatch(mInput.velocityFlattenR, "MainCS", VelocityTileWidth, VelocityTileHeight, 1);

        if (mSetting.ComputeShader)
        {
            auto VelocityTileInternal =
                IRenderPipeline::CreateTextureView2D("VelocityTileInternal", VelocityTileWidth, VelocityTileHeight, GraphicsFormat::R16G16B16A16_SFloat, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource);

            UInt32 GroupX = 8, GroupY = 8, GroupZ = 1;
            mInput.velocityNeighborMaxR->GetThreadGroupSize("MainCS", GroupX, GroupY, GroupZ);
            UInt32 DispatchX = (VelocityTileWidth + GroupX - 1) / GroupX;
            UInt32 DispatchY = (VelocityTileHeight + GroupY - 1) / GroupY;

            auto* neighborMax0 = red->AllocatePass("MotionBlur_velocity_neighbor_max");
            neighborMax0->SetProperty(NAME_ID("VelocityTileTexture"), VelocityTile, NGIResourceState::ComputeShaderShaderResource);
            neighborMax0->SetProperty(NAME_ID("OutVelocityTileTexture"), VelocityTileInternal, NGIResourceState::ComputeShaderUnorderedAccess);
            neighborMax0->Dispatch(mInput.velocityNeighborMaxR, "MainCS", DispatchX, DispatchY, GroupZ);

            auto* neighborMax1 = red->AllocatePass("MotionBlur_velocity_neighbor_max");
            neighborMax1->SetProperty(NAME_ID("VelocityTileTexture"), VelocityTileInternal, NGIResourceState::ComputeShaderShaderResource);
            neighborMax1->SetProperty(NAME_ID("OutVelocityTileTexture"), VelocityTile, NGIResourceState::ComputeShaderUnorderedAccess);
            neighborMax1->Dispatch(mInput.velocityNeighborMaxR, "MainCS", DispatchX, DispatchY, GroupZ);

            auto* motionBlur = red->AllocatePass("MotionBlur_reconstruct");
            motionBlur->SetProperty(NAME_ID("ViewportMin"), UInt2{0u, 0u});
            motionBlur->SetProperty(NAME_ID("ViewportMax"), UInt2{ScreenWidth, ScreenHeight});
            motionBlur->SetProperty(NAME_ID("InputColor"), color, NGIResourceState::ComputeShaderShaderResource);
            motionBlur->SetProperty(NAME_ID("InputColor_TexelSize"), Float2{1.0f / color->mTexture->mDesc.Width, 1.0f / color->mTexture->mDesc.Height});
            motionBlur->SetProperty(NAME_ID("VelocityFlat"), VelocityFlat, NGIResourceState::ComputeShaderShaderResource);
            motionBlur->SetProperty(NAME_ID("VelocityFlat_TexelSize"), Float2{1.0f / VelocityFlat->mTexture->mDesc.Width, 1.0f / VelocityFlat->mTexture->mDesc.Height});
            motionBlur->SetProperty(NAME_ID("VelocityTile"), VelocityTile, NGIResourceState::ComputeShaderShaderResource);
            motionBlur->SetProperty(NAME_ID("VelocityTile_TexelSize"), Float2{1.0f / VelocityTile->mTexture->mDesc.Width, 1.0f / VelocityTile->mTexture->mDesc.Height});
            motionBlur->SetProperty(NAME_ID("VelocityScale"), VelocityScale);
            motionBlur->SetProperty(NAME_ID("VelocityMax"), VelocityMax);
            motionBlur->SetProperty(NAME_ID("LoopCount"), MathUtils::Clamp(mSetting.SampleCount, 2u, 64u) / 2);
            motionBlur->SetProperty(NAME_ID("OutColorTexture"), mOutput.outputcolor, NGIResourceState::ComputeShaderUnorderedAccess);

            mInput.motionBlurR->GetThreadGroupSize("MainCS", GroupX, GroupY, GroupZ);
            DispatchX = (color->mTexture->mDesc.Width + GroupX - 1) / GroupX;
            DispatchY = (color->mTexture->mDesc.Height + GroupY - 1) / GroupY;
            motionBlur->Dispatch(mInput.motionBlurR, "MainCS", DispatchX, DispatchY, GroupZ);
        }
        else
        {
            auto VelocityTileInternal =
                IRenderPipeline::CreateTextureView2D("VelocityTileInternal", VelocityTileWidth, VelocityTileHeight, GraphicsFormat::R16G16B16A16_SFloat, NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource);

            IRenderPipeline::PostProcessUtil(
                [&](REDPass* pass) { pass->SetProperty(NAME_ID("VelocityTileTexture"), VelocityTile, NGIResourceState::PixelShaderShaderResource); }, Mtl, RED, "MotionBlur_neighbor_max", true, VelocityTileInternal);

            IRenderPipeline::PostProcessUtil(
                [&](REDPass* pass) { pass->SetProperty(NAME_ID("VelocityTileTexture"), VelocityTileInternal, NGIResourceState::PixelShaderShaderResource); }, Mtl, RED, "MotionBlur_neighbor_max", true, VelocityTile);

            IRenderPipeline::PostProcessUtil(
                [&](REDPass* pass) {
                    pass->SetProperty(NAME_ID("ViewportMin"), UInt2{0u, 0u});
                    pass->SetProperty(NAME_ID("ViewportMax"), UInt2{ScreenWidth, ScreenHeight});
                    pass->SetProperty(NAME_ID("InputColor"), color, NGIResourceState::PixelShaderShaderResource);
                    pass->SetProperty(NAME_ID("InputColor_TexelSize"), Float2{1.0f / color->mTexture->mDesc.Width, 1.0f / color->mTexture->mDesc.Height});
                    pass->SetProperty(NAME_ID("VelocityFlat"), VelocityFlat, NGIResourceState::PixelShaderShaderResource);
                    pass->SetProperty(NAME_ID("VelocityFlat_TexelSize"), Float2{1.0f / VelocityFlat->mTexture->mDesc.Width, 1.0f / VelocityFlat->mTexture->mDesc.Height});
                    pass->SetProperty(NAME_ID("VelocityTile"), VelocityTile, NGIResourceState::PixelShaderShaderResource);
                    pass->SetProperty(NAME_ID("VelocityTile_TexelSize"), Float2{1.0f / VelocityTile->mTexture->mDesc.Width, 1.0f / VelocityTile->mTexture->mDesc.Height});
                    pass->SetProperty(NAME_ID("VelocityScale"), VelocityScale);
                    pass->SetProperty(NAME_ID("VelocityMax"), VelocityMax);
                    pass->SetProperty(NAME_ID("LoopCount"), MathUtils::Clamp(mSetting.SampleCount, 2u, 64u) / 2);
                },
                Mtl,
                RED,
                "MotionBlur_reconstruct",
                true,
                mOutput.outputcolor);
        }

        RED->EndRegion();
    }
    else
    {
        mOutput.outputcolor = mInput.color;
    }
    mOutput.SetOutputData(gameContext);
}
}   // namespace cross


