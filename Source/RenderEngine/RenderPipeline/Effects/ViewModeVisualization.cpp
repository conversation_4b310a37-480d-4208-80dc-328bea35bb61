#include "ViewModeVisualization.h"
#include "RenderEngine/RenderPipeline/RenderPipelineR.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipeline.h"

namespace cross {

void ViewModeVisualization::FillInputs(const GameContext& gameContext)
{
    auto ffsRdrPipe = dynamic_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline);
    auto ffsSetting = ffsRdrPipe->GetSetting();
    mVisParams = {ffsSetting->viewModeVisualizeType};
    auto [prevEmissiveColorRT, curEmissiveColorRTView] = ffsRdrPipe->GetEmissiveColorForSmartGI();
    REDTextureView* emissiveColorTexView = TextureCreateHelper::GetHistoryTextureView(ffsRdrPipe->GetRenderingExecutionDescriptor(), prevEmissiveColorRT, curEmissiveColorRTView);

    mVisTexes = {ffsRdrPipe->GetBuiltInTexture<PassSemanticName::GBufferViews>(),
                 ffsRdrPipe->GetBuiltInTexture<PassSemanticName::DepthOnlyBeforeUpScale>(),
                 ffsRdrPipe->GetAOView(),
                 ffsRdrPipe->GetReflectionIndirectView(),
                 ffsRdrPipe->GetBuiltInTexture<PassSemanticName::SeperateTranslucencyView>(),
                 ffsRdrPipe->GetBuiltInTexture<PassSemanticName::SceneColorBeforeTransparent>(),
                 ffsRdrPipe->GetGIColorView(),
                 ffsRdrPipe->GetEmissiveColorView(),
                 ffsRdrPipe->GetBuiltInTexture<PassSemanticName::VisibilityBufferView>()
    };
    mViewModeVisMtl = ffsRdrPipe->GetViewModeVisualizationMtl();
}

void ViewModeVisualization::SetRenderDebugTex(REDTextureView* debugTex)
{
    if (debugTex)
        mDebugTexView = debugTex;
}

void ViewModeVisualization::ResetAllTexes()
{
    mVisTexes = {{}, nullptr, nullptr, nullptr, nullptr, nullptr, nullptr};
    mDebugTexView = nullptr;
}


void ViewModeVisualization::ExecuteImp(const GameContext& gameContext, REDTextureView* outputView, REDTextureView* sceneColorView)
{
    bool bEnableViewModeVisualization =  gameContext.mRenderPipeline->IsMainCamera() &&
                                        (gameContext.mRenderPipeline->GetRenderPipelineType() == ViewType::SceneView || gameContext.mRenderPipeline->GetRenderPipelineType() == ViewType::GameView) ;
    if (!bEnableViewModeVisualization)
    {
        return;
    }
    

    gameContext.mRenderPipeline->PostProcess(
        [&](auto pass) {
            pass->SetProperty(NAME_ID("VIEW_MODE"), static_cast<int>(mVisParams.mVisType));

            pass->SetProperty(NAME_ID("SceneColorTex"), sceneColorView);

            if (mVisTexes.mGbufferViews[0]) 
                pass->SetProperty(NAME_ID("_GBuffer0"), mVisTexes.mGbufferViews[0]);
            if (mVisTexes.mGbufferViews[1])
                pass->SetProperty(NAME_ID("_GBuffer1"), mVisTexes.mGbufferViews[1]);
            if (mVisTexes.mGbufferViews[2])
                pass->SetProperty(NAME_ID("_GBuffer2"), mVisTexes.mGbufferViews[2]);
            if (mVisTexes.mGbufferViews[3])
                pass->SetProperty(NAME_ID("_GBuffer3"), mVisTexes.mGbufferViews[3]);
            if (mVisTexes.mDepthTex)
                pass->SetProperty(NAME_ID("_DepthMap"), mVisTexes.mDepthTex);
            if (mVisTexes.mEmissiveTex)
                pass->SetProperty(NAME_ID("EmissiveColorTex"), mVisTexes.mEmissiveTex);
            if (mVisTexes.mSceneColorTex)
                pass->SetProperty(NAME_ID("OpacityColorTex"), mVisTexes.mSceneColorTex);

            if (mVisTexes.mAOTex) 
                pass->SetProperty(NAME_ID("AOTex"), mVisTexes.mAOTex);
            if (mVisTexes.mReflectionsTex)
                pass->SetProperty(NAME_ID("ReflectionTex"), mVisTexes.mReflectionsTex);
            if (mVisTexes.mSeparateTranslucencyTex)
                pass->SetProperty(NAME_ID("SeparateTranslucencyTex"), mVisTexes.mSeparateTranslucencyTex);
            if (mVisTexes.mGITex)
                pass->SetProperty(NAME_ID("GITex"), mVisTexes.mGITex);
            if (mVisTexes.mVisibilityBufferTex)
                pass->SetProperty(NAME_ID("VisibilityBufferTex"), mVisTexes.mVisibilityBufferTex);
            if (mDebugTexView)
                pass->SetProperty(NAME_ID("DebugTex"), mDebugTexView);

            pass->SetProperty(NAME_ID("ENABLE_AO"), mVisTexes.mAOTex != nullptr);
            pass->SetProperty(NAME_ID("ENABLE_REFLECTION"), mVisTexes.mReflectionsTex != nullptr);
            pass->SetProperty(NAME_ID("ENABLE_SEPARATE_TRANSLUCENCY"), mVisTexes.mSeparateTranslucencyTex != nullptr);
            pass->SetProperty(NAME_ID("ENABLE_GI"), mVisTexes.mGITex != nullptr);
            pass->SetProperty(NAME_ID("ENABLE_DEBUG_TEX"), mDebugTexView != nullptr);

        },
        mViewModeVisMtl,
        "ViewModeVisualization",
        true,
        outputView);
    
    ResetAllTexes();
}

}   // namespace cross