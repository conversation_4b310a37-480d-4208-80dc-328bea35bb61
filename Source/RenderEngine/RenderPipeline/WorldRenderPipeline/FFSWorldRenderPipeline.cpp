#include "FFSWorldRenderPipeline.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipelineSetting.h"
#include "RenderEngine/SkyLightSystemR.h"
#include "RenderEngine/SkyAtmosphereSystemR.h"

namespace cross {
void FFSWorldRenderPipeline::Initialize(RenderWorld* world, RenderingExecutionDescriptor* RED)
{
    WorldRenderPipeline::Initialize(world, RED);

    mGPUScene.Initialize(mRenderWorld, mRED);
#ifdef NGI_ENABLE_RAY_TRACING
    mRayTracingScene.Initialize(mRenderWorld, mRED, &mGPUScene);
#endif
    mStellarMeshScene.Initialize(mRenderWorld, mRED);
}

void FFSWorldRenderPipeline::Assemble(FrameParam* frameParam)
{
    QUICK_SCOPED_CPU_TIMING("FFSWorldRenderPipeline::Assemble");

    mCurFrameParam = frameParam;

    auto* ffsRenderPipelineSetting = static_cast<const FFSRenderPipelineSetting*>(mSetting);

    if (mRenderWorld->GetEnable())
    {
        mRED->BeginRegion(fmt::format("BeginWorld {}", mRenderWorld->GetName().GetCString()));

        UpdateWorldCameraContext();

        // GPUScene
        {
            mGPUScene.Update();
            mStellarMeshScene.Update();
        }

        mParticleSystemGpuDriven.Update(mRenderWorld, mSetting);

        mGPUScene.PostUpdate();
        mStellarMeshScene.PostUpdate();

#ifdef NGI_ENABLE_RAY_TRACING
        mRayTracingScene.Update();
        mRayTracingScene.PostUpdate();
#endif

        // VirtualShadowMapPhysicalPool
        {
            mVirtualShadowMapPhysicalPagePool.Update(mRenderWorld, this, &ffsRenderPipelineSetting->mVirtualShadowMapSettings);
        }

        auto* streamingManager = gResourceMgr.mStreamingMgr;
        streamingManager->UpdateRenderStatus(frameParam);

        // we should setup all the "scene related" but "view unrelated" result before per-view rendering, since this will save a lot of setting
        /*
         *  PreAssemble RenderPipelines
         *  Extracting some logic from Assemble to pre-assemble for flexibility.
         *  For example, some data preparing tasks can be moved to pre-assemble stage.
         */

        for (auto& [type, renderPipeline] : mRenderPipelines)
        {
            if (renderPipeline->IsEnable())
            {
                SCOPED_CPU_TIMING(GroupRendering, "RenderPipelinePreAssemble");
                renderPipeline->PreAssemble();
            }
        }

        // Setup frame index
        UpdateFrameIndexContext();

        /*
         * render textures and fill buffer
         *  Make sure that light information is up-to-date for rendering cloud
         */
        PreComputeSkyTextures();

        /*
         *  send texture and buffer to RED.RenderContext
         */
        UpdateSkyLightContext();

        // Assemble RenderPipelines
        // need to be false, since a lot of system/component dependency twisted.
        const bool PARRALLEL = false;

        threading::TaskEventArray tasks;
        for (auto& [type, renderPipeline] : mRenderPipelines)
        {
            if (renderPipeline->IsEnable())
            {
                SCOPED_CPU_TIMING(GroupRendering, "RenderPipelineAssemble");
                if (PARRALLEL)
                {
                    tasks.Add(threading::Dispatch([pipe = renderPipeline.get()](const auto) { pipe->Assemble(); }));
                }
                else
                {
                    renderPipeline->Assemble();
                }
            }
        }
        if (PARRALLEL)
        {
            tasks.WaitForCompletion();
        }

        mRED->EndRegion();
    }
    else if (EngineGlobal::GetSettingMgr()->GetFFXFrameInterpolation())
    {
        for (auto& [type, renderPipeline] : mRenderPipelines)
        {
            renderPipeline->FreeFGRes();
        }
    }
}

void FFSWorldRenderPipeline::UpdateWorldCameraContext() const
{
    UInt32 index = 0;
    for (auto& [type, renderPipeline] : mRenderPipelines)
    {
        if (type != ViewType::GameView)
            continue;
        const auto& view = renderPipeline->GetRenderCamera()->GetCameraView();
        const auto& preView = renderPipeline->GetRenderCamera()->GetPreCameraView();
        mRED->SetProperty(BUILTIN_CAMERA_LIST_VIEW(index), view.mViewMatrix);
        mRED->SetProperty(BUILTIN_CAMERA_LIST_INV_VIEW(index), view.mInvertViewMatrix);
        mRED->SetProperty(BUILTIN_CAMERA_LIST_PRE_VIEW(index), preView.mViewMatrix);
        mRED->SetProperty(BUILTIN_CAMERA_LIST_PRE_INV_VIEW(index), preView.mInvertViewMatrix);

#if defined(CE_USE_DOUBLE_TRANSFORM)
        mRED->SetProperty(BUILTIN_CAMERA_LIST_TILE_POSITION(index), view.mCameraTilePosition);
        mRED->SetProperty(BUILTIN_CAMERA_LIST_PRE_TILE_POSITION(index), preView.mCameraTilePosition);
#endif

        index++;
    }
   
}

void FFSWorldRenderPipeline::UpdateSetting(const RenderPipelineSetting* setting)
{
    WorldRenderPipeline::UpdateSetting(setting);

    mGPUScene.SetFFSRenderPipelineSetting(static_cast<const FFSRenderPipelineSetting*>(setting));
#ifdef NGI_ENABLE_RAY_TRACING
    mRayTracingScene.SetFFSRenderPipelineSetting(static_cast<const FFSRenderPipelineSetting*>(setting));
#endif
    mStellarMeshScene.SetFFSRenderPipelineSetting(static_cast<const FFSRenderPipelineSetting*>(setting));
}

void FFSWorldRenderPipeline::PreComputeSkyTextures()
{
    auto* skyAtmosphereSystem = mRenderWorld->GetRenderSystem<SkyAtmosphereSystemR>();
    auto* skyLightSystem = mRenderWorld->GetRenderSystem<SkyLightSystemR>();

    /*
     *  1. prepare sky atmosphere textures and precompute TransmittanceTexture and MultiScatTexture
     *  ** TransmittanceTexture and MultiScatTexture is view-unrelated
     */
    skyAtmosphereSystem->PrepareRenderSkyAtmosphere();

    /*
     * 2. prepare skylight cube map and buffers
     * ** if RealtimeCapture skylight is on, then 6 faces of the cube map will be rendered in 11 frames by default,
     * ** which contains the atmosphere, fog, cloud rendering, filtering and mipmap generating
     */
    skyLightSystem->RenderSkylightTextures();
}

void FFSWorldRenderPipeline::UpdateSkyLightContext()
{
    auto* skyLightSystem = mRenderWorld->GetRenderSystem<SkyLightSystemR>();

    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    if (auto* skyLightDiffuse = skyLightSystem->GetDiffuseProbe(); skyLightDiffuse)
    {
        mRED->SetProperty(BuiltInProperty::ce_UE4AmbientProbeSH, skyLightDiffuse);
    }

    if (skyLightSystem->GetSkyLightTexture())
    {
        mRED->SetProperty(BuiltInProperty::ce_SkyLightTexture, skyLightSystem->GetSkyLightTexture());
    }
    else
    {
        // use default cube, is it black?
        mRED->SetProperty(BuiltInProperty::ce_SkyLightTexture, rendererSystem->GetDefaultTextureCubeView());
    }
    mRED->SetProperty(BuiltInProperty::ce_SkyLightColor, skyLightSystem->GetColor().data(), sizeof(Float3));
    mRED->SetProperty(BuiltInProperty::ce_LightMapIntensity, skyLightSystem->GetLightMapIntensityDebug());
    mRED->SetProperty(BuiltInProperty::ce_SkyLightIntensity, skyLightSystem->GetSkyLightIntensity());
}
}   // namespace cross