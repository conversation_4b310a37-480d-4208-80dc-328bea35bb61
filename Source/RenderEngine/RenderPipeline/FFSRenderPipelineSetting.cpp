#include "EnginePrefix.h"
#include "FFSRenderPipelineSetting.h"
#include "Resource/AssetStreaming.h"
#include "Resource/Material.h"
#include "RenderMesh.h"
#include "RenderEngine/IVertexStreamLayoutPolicy.h"
#include "RenderEngine/RenderFactory.h"

cross::FFSRenderPipelineSetting::FFSRenderPipelineSetting()
{
    DefaultMaterial = "Shader/Material/Lit/FxTemplate.fx.nda";
    DefaultFX = "Shader/Material/Lit/LitUE.fx.nda";
    UseRenderPipeline = "UseFFSRP";
}

cross::RenderPipelineSetting* cross::FFSRenderPipelineSetting::Clone() const
{
    return new FFSRenderPipelineSetting{ *this };
}

bool cross::FFSRenderPipelineSetting::UseFSRorDLSS() const
{
#ifdef WIN32
    return (mDLSSSetting.enable&&SLWrapper::Get().GetDLSSAvailable()) || mFSR2Setting.enable||(mFSR3Setting.enable&&mFSR3Setting.SuperResolutionOn);
#else
    return mFSR2Setting.enable;
#endif
}

float cross::FFSRenderPipelineSetting::GetUpScaleValue(ViewType type)const
{
    if (mFSR3Setting.enable && mFSR3Setting.SuperResolutionOn)
    {
        return mFSR3Setting.GetUpscaleValue(type);
    }
    if (mFSR2Setting.enable)
    {
        return mFSR2Setting.GetUpscaleValue();
    }
#ifdef WIN32
    if (mDLSSSetting.enable && SLWrapper::Get().GetDLSSAvailable())
    {
        return mDLSSSetting.GetUpscaleValue(type);
    }
#endif
    return 1.0;
}


void cross::FFSRenderPipelineSetting::Initialize()
{
    if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() == AppStartUpTypeHeadless)
    {
        return;
    }

    RenderPipelineBaseSetting::Initialize();

    SettingsManager::RegisterRPSettingVar(this);
    LOAD_RENDER_PIPELINE_MATERIAL(PostProcessMtl);
    LOAD_RENDER_PIPELINE_MATERIAL(CombineLightingMtl);
    LOAD_RENDER_PIPELINE_MATERIAL(OutlineEditorMtl);
    LOAD_RENDER_PIPELINE_MATERIAL(WireframeMtl);
    LOAD_RENDER_PIPELINE_MATERIAL(ViewModeVisualizationMtl);

    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(CollectShadowMaskShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(SubsurfaceScatteringComputeShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(HistogramExposureComputeShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(ManualExposureComputeShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(VolumetricFogComputeShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(VFogLightInjectionComputeShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(VFogDispatchSizeComputeShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(VelocityFlattenComputeShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(VelocityNeighborMaxComputeShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(MotionBlurComputeShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(LocalExposureComputeShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(VTComputeShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(BloomDownsampleShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(ScatterCopyComputeShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(ScatterByteCopyComputeShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(InstanceCullingComputeShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(InstanceCulling2ComputeShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(ParticleIndirectArgsGenShader);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(ParticleGPUSimuateInstanceCountCopyComputeShader);
    LOAD_RENDER_PIPELINE_TEXTURE(MPCDI_AlphaTexture);
    LOAD_RENDER_PIPELINE_TEXTURE(MPCDI_LutTexture);
    //LOAD_RENDER_PIPELINE_TEXTURE(MPCDI_BetaTexture);
    LOAD_RENDER_PIPELINE_GEOMETRY(MPCDI_WarpMesh);
    //LOAD_RENDER_PIPELINE_TEXTURE(ScreenEffect_Texture);
    LOAD_RENDER_PIPELINE_GEOMETRY(MPCDI_ScreenEffect_Mesh);
    LOAD_RENDER_PIPELINE_MATERIAL(MPCDI_ScreenEffect_Mtl);
    LOAD_RENDER_PIPELINE_MATERIAL(DeferSSRMtl);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(StochasticReproject);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(StochasticPrefilter);
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(StochasticResolveTemporal);
    LOAD_RENDER_PIPELINE_TEXTURE(NoiseTexture);

    mVirtualShadowMapSettings.Initialize();
    PassSetting::RegisterSettingVars(&mVirtualShadowMapSettings);
    mLocalLightShadowMapSettings.Initialize();
    PassSetting::RegisterSettingVars(&mLocalLightShadowMapSettings);
    mDepthPyramidSettings.Initialize();
    PassSetting::RegisterSettingVars(&mDepthPyramidSettings);
    mDrawRenderTextureToAtlasPassSettings.Initialize();
    PassSetting::RegisterSettingVars(&mDrawRenderTextureToAtlasPassSettings);
    mApplyDBufferDataPassSettings.Initialize();
    PassSetting::RegisterSettingVars(&mApplyDBufferDataPassSettings);
    mFoliageGpuDrivenSettings.Initialize();
    PassSetting::RegisterSettingVars(&mFoliageGpuDrivenSettings);
    mContactShadowSettings.Initialize();
    PassSetting::RegisterSettingVars(&mContactShadowSettings);
    mBuildTileCullingSettings.Initialize();
    PassSetting::RegisterSettingVars(&mBuildTileCullingSettings);
    mIndirectLightingCompositeSettings.Initialize();
    PassSetting::RegisterSettingVars(&mIndirectLightingCompositeSettings);
    mFSR2Setting.Initialize();
    PassSetting::RegisterSettingVars(&mFSR2Setting);
    mMassiveLightsShadowSettings.Initialize();
    PassSetting::RegisterSettingVars(&mMassiveLightsShadowSettings);
    mSkyAtmosphereAdvancedVars.Initialize();
    PassSetting::RegisterSettingVars(&mSkyAtmosphereAdvancedVars);
    mSubSampleShadingSettings.Initialize();
    PassSetting::RegisterSettingVars(&mSubSampleShadingSettings);
    mTileBasedDefferedLightingSettings.Initialize();
    PassSetting::RegisterSettingVars(&mTileBasedDefferedLightingSettings);
    mDepthOfFieldSetting.Initialize();
    PassSetting::RegisterSettingVars(&mDepthOfFieldSetting);
    mCloudSetting.Initialize();
    PassSetting::RegisterSettingVars(&mCloudSetting);
    mDLSSSetting.Initialize();
    PassSetting::RegisterSettingVars(&mDLSSSetting);
    mFSR3Setting.Initialize();
    PassSetting::RegisterSettingVars(&mFSR3Setting);
    mSeparateTranslucencyBlendSetting.Initialize();
    PassSetting::RegisterSettingVars(&mSeparateTranslucencyBlendSetting);
    mStellarMeshRenderPipelineSetting.Initialize();
    PassSetting::RegisterSettingVars(&mStellarMeshRenderPipelineSetting);

}