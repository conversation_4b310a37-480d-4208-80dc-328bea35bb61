#pragma once
#include "Runtime/GameWorld/RenderWindowInitInfo.h"
#include "NativeGraphicsInterface/NGI.h"
#include "RenderEngine/RenderEngineForward.h"
#include "CECommon/Common/FrameParam.h"

namespace cross
{

struct REDTexture;

struct IWindowR
{
    virtual ~IWindowR() = default;

    virtual bool Acquire(NGIFence* toSingalFence) = 0;

    virtual REDTexture* PrepareBackbuffer(NGICommandList* cmdList) = 0;
    virtual bool Present() = 0;

    virtual void OnActivate(bool inActive) {}

#if CROSSENGINE_ANDROID
    virtual void ReCreateSurface(NativeWindow nativeWindow) {}
#endif
    virtual void Resize(UInt32 width, UInt32 height, cross::NGIScreenMode screenMode) = 0;

    virtual std::tuple<UInt32, UInt32> GetSize() = 0;
};

}
