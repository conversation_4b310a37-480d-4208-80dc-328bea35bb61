#include "EnginePrefix.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/FrameParam.h"
#include "CECommon/Common/FrameContainer.h"
#include "CECommon/Common/BuildInName.h"
#include "RenderEngine/SkinnedMeshBuilderR.h"
#include "RenderEngine/ModelSystemR.h"
#include "RenderEngine/MeshBuildSystemR.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/RenderFactory.h"
#include "RenderEngine/VertexStreamLayoutPolicy.h"
#include "RenderEngine/SkeletonComponentR.h"
#include "RenderEngine/SkeletonSystemR.h"
#include "RenderEngine/CameraSystemR.h"
#include "RenderEngine/RenderCamera.h"
#include "RenderEngine/RenderNode/ModelRenderNode.h"
#include "RenderEngine/TransformSystemR.h"

namespace cross {

SkinnedMeshBuilderR* SkinnedMeshBuilderR::CreateInstance()
{
    return new SkinnedMeshBuilderR();
}

void SkinnedMeshBuilderR::Release()
{
    delete this;
}

SkinnedMeshBuilderR::SkinnedMeshBuilderR()
    : MeshBuilderBaseR("crossSkinnedMeshBuilder")
{
}

void SkinnedMeshBuilderR::AddMesh(ecs::EntityID entity)
{
    auto itr = std::find_if(mMeshList->begin(), mMeshList->end(), [=](ecs::EntityID e) { return e == entity; });
    if (itr == mMeshList->end())
    {
        mMeshList->EmplaceBack(entity);
    }
}

void SkinnedMeshBuilderR::BeginFrame(FrameParam* fp)
{
    auto* frameAllocator = fp->GetFrameAllocator();
    mCurFrame = fp->GetFrameCount();
    mMeshList = frameAllocator->CreateFrameContainer<FrameVector<ecs::EntityID>>(FRAME_STAGE_RENDER, 100);
}

void SkinnedMeshBuilderR::EndFrame(FrameParam* fp) {}

bool SkinnedMeshBuilderR::HasUnbuiltMesh()
{
    return mUnbuiltMeshCount > 0;
}

void SkinnedMeshBuilderR::PostTick(FrameParam* fp, MeshBuildSystemR* meshBuildSystem)
{
    auto* modelSys = meshBuildSystem->GetModelSystem();

    //Remove skinned entities inserted in previous frames
    for (auto itr = mMeshesToBeSkinned.begin(); itr != mMeshesToBeSkinned.end();)
    {
        auto entity = *itr;
        RenderModelComponentHandle modelHandle = modelSys->GetModelHandle(entity);

        if (!modelHandle.Read()->mAsyncResourceAssemble || !meshBuildSystem->GetRenderWorld()->IsEntityAlive(entity))
        {
            itr = mMeshesToBeSkinned.erase(itr);
            continue;
        }

        if (modelSys->IsAllSkinningTasksFinished(modelHandle.Read()))
        {
            itr = mMeshesToBeSkinned.erase(itr);
        }
        else
            itr++;
    }

    //Add entities not skinned from the mesh list of the current frame
    for (UInt32 entityIdx = 0; entityIdx < mMeshList->GetSize(); ++entityIdx)
    {
        auto entity = mMeshList->At(entityIdx);
        RenderModelComponentHandle modelHandle = modelSys->GetModelHandle(entity);

        if (!modelHandle.Read()->mAsyncResourceAssemble)
            continue;

        if (!modelSys->IsAllSkinningTasksFinished(modelHandle.Read()))
            mMeshesToBeSkinned.emplace(entity);
    }
}

void SkinnedMeshBuilderR::Tick(FrameParam* fp, MeshBuildSystemR* meshBuildSystem)
{
    SCOPED_CPU_TIMING(GroupRendering, "SkinnedMeshBuilderTick");

    for (auto itr = mMeshesToBeSkinned.begin(); itr != mMeshesToBeSkinned.end(); itr++)
    {
        if (meshBuildSystem->GetRenderWorld()->IsEntityAlive(*itr))
            AddMesh(*itr);
    }

    UInt32 entityCount = mMeshList->GetSize();
    if (entityCount == 0)
        return;

    auto* modelSys = meshBuildSystem->GetModelSystem();
    auto* skSys = meshBuildSystem->GetSkeletonSystem();
    auto* frameAllocator = fp->GetFrameAllocator();
    {
        SCOPED_CPU_TIMING(GroupRendering, "Skinning");

        threading::TaskEventArray taskEventArray;

        // step 1: Prepare data for skinning if RenderGeometryList dirty.
        auto allSkinParams = CreateRenderMesh(modelSys, skSys, frameAllocator);

        // step 2: Dispatch skinning task for all meshes.
        for (UInt32 modelIdx = 0; modelIdx < allSkinParams->GetSize(); ++modelIdx)
        {
            auto& skinParam = allSkinParams->At(modelIdx);
            if (skinParam.SkinMode == GeomertySkinnedMode::Dummy)
                continue;

            if (skinParam.SkinMode != GeomertySkinnedMode::Type::CpuSkin)
                // Gpu skin execute with matrices buffer usage as STORAGE_BUFFER for GpuSkin
                skSys->UpdatePoseStagingBuffer(skinParam, taskEventArray);
            else
                CreateCpuSkinningTask(skinParam, taskEventArray);
        }

        taskEventArray.WaitForCompletion();
        taskEventArray.Reset();

        // step 3: Other CPU SKIN tasks.
        for (UInt32 modelIdx = 0; modelIdx < allSkinParams->GetSize(); ++modelIdx)
        {
            auto& skinParam = allSkinParams->At(modelIdx);

            if (skinParam.SkinMode == GeomertySkinnedMode::CpuSkin)
                CreatePostCpuSkinningTask(modelSys, allSkinParams->At(modelIdx));
        }
    }
}

SkinnedMeshBuilderR::SkinParamFrameVector* SkinnedMeshBuilderR::CreateRenderMesh(ModelSystemR* modelSystem, SkeletonSystemR* skSystem, FrameAllocator* frameAllocator)
{
    SCOPED_CPU_TIMING(GroupRendering, "Create SKIN MESH RENDER Geometry");
    mVertexCount = 0;
    const auto settingMgr = EngineGlobal::Inst().GetSettingMgr();
    const bool globalEnableGPUSkin = settingMgr->GetGPUSkinEnable();

    // Grab how much entity should be skinned 
    UInt32 entityCount = mMeshList->GetSize();
    
    // Grab how much Model should be skinned
    UInt32 skeltModelCount = 0;
    std::vector<UInt32> entityIdToModelIndexStart;
    entityIdToModelIndexStart.reserve(entityCount);
    for (UInt32 entityIdx = 0; entityIdx < entityCount; ++entityIdx)
    {
        RenderModelComponentHandle modelHandle = modelSystem->GetModelHandle(mMeshList->At(entityIdx));

        entityIdToModelIndexStart.push_back(skeltModelCount);
        skeltModelCount += modelSystem->GetModelCount(modelHandle.Read());
    }

    // Each Entity got a temporary SkinParamFrameVector
    // Each Model(MeshAssetData) in Entity got a VertexStreamLayoutSkinParameter
    SkinParamFrameVector* allSkinParams = frameAllocator->CreateFrameContainer<SkinParamFrameVector>(FRAME_STAGE_RENDER, skeltModelCount, MEM_ALIGN_16);
    allSkinParams->Resize(skeltModelCount);

    // Each Model get a valid skin mode by MeshAssetData, which represent specify mesh resource file
    threading::TaskEventArray taskEventArray;
    for (UInt32 entityIdx = 0; entityIdx < entityIdToModelIndexStart.size(); ++entityIdx)
    {
        taskEventArray.Add(threading::Dispatch([&, entityIdx = entityIdx](auto) 
        {
            auto curEntity = mMeshList->At(entityIdx);
          
            RenderModelComponentHandle modelHandle = modelSystem->GetModelHandle(curEntity);
            UInt32 curModelCount = modelSystem->GetModelCount(modelHandle.Read());
            UInt32 curEntityStart = entityIdToModelIndexStart[entityIdx];

            // Each Model (sub model sets) filling one VertexStreamLayoutSkinParameter for ANY SKIN task
            for (UInt32 modelIndex = 0; modelIndex < curModelCount; ++modelIndex)
            {
                // Static mesh is not allowed to enter this
                Assert(!modelSystem->IsModelStaticBuilt(modelHandle.Read(), modelIndex));

                // Skin all sub model in one specify Model
                const MeshAssetDataResourcePtr& meshAssetDataResource = modelSystem->GetModelAsset(modelHandle.Read(), modelIndex);
                const MeshAssetData* meshAsset = meshAssetDataResource->GetAssetData();

                if (!meshAsset)
                    continue;

                UInt8 curLod = modelSystem->GetModelLODIndex(modelHandle.Read(), modelIndex);
                // curSkinLevel is used to determine whether to skin all models(LOD_0 - LOD_N) or only the current LOD model.
                SkinLevel curSkinLevel = modelSystem->GetModelSkinLevel(modelHandle.Read(), modelIndex);

                if (!modelSystem->GetModelVisibility(curEntity, modelIndex))
                    continue;

                UInt32 subModelCount = modelSystem->GetSubModelCount(modelHandle.Read(), modelIndex);
                if (subModelCount < 1)
                    continue;

                UInt32 boneNum = (UInt32)(meshAsset->GetRefSkeleton()->GetRawBoneNum());

                // Emplace a new skin param to frame vector
                auto& curSkinParam                  = allSkinParams->At(curEntityStart + modelIndex);
                curSkinParam.SkinningMatrices       = frameAllocator->CreateFrameContainer<FrameVector<SIMDMatrix>>(FRAME_STAGE_RENDER, boneNum, MEM_ALIGN_16);
                curSkinParam.AllSubModelMaterials   = frameAllocator->CreateFrameContainer<FrameVector<MaterialR*>>(FRAME_STAGE_RENDER, subModelCount, MEM_ALIGN_16);
                curSkinParam.Entity                 = curEntity;
                curSkinParam.ModelIndex             = { modelIndex };

                // Get current LOD start vertex and end vertex
                curSkinParam.mCurLodVertexStart = curSkinLevel == SkinLevel::All ? 0 : meshAsset->GetMeshPartInfo(meshAsset->GetLodStartIndex()[curLod]).mVertexStart;
                if (curLod < meshAsset->GetLodCount() - 1 && curSkinLevel == SkinLevel::CurLod)
                {
                    curSkinParam.mCurLodVertexEnd = meshAsset->GetMeshPartInfo(meshAsset->GetLodStartIndex()[curLod + 1]).mVertexStart;
                }

                curSkinParam.mCurSkinLevel = curSkinLevel;

                // For blend shape
                curSkinParam.HasBlendShape = modelSystem->IsModelHasBlendShape(modelHandle.Read(), modelIndex);
                curSkinParam.ModelBlendShapeVertexDataPtr = modelSystem->GetModelBlendShapeVertexData(modelHandle.Read(), modelIndex);

                // For Skin mode check
                if (globalEnableGPUSkin)
                {
                    bool materialsAllTexelSkinMacro = true;

                    for (UInt32 iSubModel = 0; iSubModel < subModelCount; iSubModel++)
                    {
                        MaterialR* subModelMaterial = modelSystem->GetModelMaterial(modelHandle.Read(), iSubModel, modelIndex);
                        curSkinParam.AllSubModelMaterials->EmplaceBack(subModelMaterial);
                        if (subModelMaterial)
                        {
                            if (auto enable = subModelMaterial->GetBool("GPU_SKIN"); enable == std::nullopt || *enable == false)
                                materialsAllTexelSkinMacro = false;
                        }
                    }

                    curSkinParam.SkinMode = materialsAllTexelSkinMacro ? GeomertySkinnedMode::Type::GpuGraphicSkin : GeomertySkinnedMode::Type::GpuComputeSkin;
                }
                // Even if current model 
                else 
                    curSkinParam.SkinMode = GeomertySkinnedMode::Type::CpuSkin;

                // Calculate SkinningMatrices
                {
                    FrameVector<SIMDMatrix>* meshPosePtr = modelSystem->GetSkeltModelPose(curEntity, modelIndex);

                    const auto& BindPoseInvMatices = meshAsset->GetBindPoseInvMat();
                    curSkinParam.SkinningMatrices->Resize(boneNum);

                    // if no valid skeleton or no valid complete pose exists, use Ref Pose of Mesh's RefSkeleton to skin
                    if (meshPosePtr == nullptr)
                    {
                        auto const& RefPoseMatArray = meshAsset->GetRefSkeleton()->GetRawRefBonePoseWorldMatirx();
                        for (UInt32 iBone = 0; iBone < boneNum; iBone++)
                            curSkinParam.SkinningMatrices->At(iBone) = MathSIMD::MatrixMultiply(BindPoseInvMatices[iBone], SIMD::LoadFloat4x4A(&RefPoseMatArray[iBone]));
                    }
                    // use model pose(mapped from skeleton pose) to skin
                    else
                    {
                        for (UInt32 iBone = 0; iBone < boneNum; iBone++)
                            curSkinParam.SkinningMatrices->At(iBone) = MathSIMD::MatrixMultiply(BindPoseInvMatices[iBone], meshPosePtr->At(iBone));
                    }
                }
            }
        }));
    }

    // Wait per thread VertexStreamLayoutSkinParameter filled out
    taskEventArray.WaitForCompletion();
    taskEventArray.Reset();

    //// update buildtasks
    for (auto it = mBuildTasks.begin(); it != mBuildTasks.end();)
        it = (*it)->Complete() ? mBuildTasks.erase(it) : std::next(it);

    // Create RenderGeometry List
    for (auto& curSkinParam : *allSkinParams)
    {
        if (curSkinParam.SkinMode == GeomertySkinnedMode::Type::Dummy)
            continue;

        //SCOPED_CPU_TIMING(GroupRendering, "For each entity");
        RenderModelComponentHandle modelHandle = modelSystem->GetModelHandle(curSkinParam.Entity);

        // need clear skeletal model pose data after pose has been used for skinning,
        // because we use frame vector to store pose which will expire after several frames
        modelSystem->SetSkeltModelPose(curSkinParam.Entity, static_cast<UInt32>(curSkinParam.ModelIndex), nullptr);

        // Get mesh asset to skin
        const MeshAssetDataResourcePtr& meshAssetDataResource = modelSystem->GetModelAsset(modelHandle.Read(), static_cast<UInt32>(curSkinParam.ModelIndex));
        const MeshAssetData* meshAsset = meshAssetDataResource->GetAssetData();

        modelHandle.Write()->mAsyncResourceAssemble = false;
        if (curSkinParam.SkinMode == GeomertySkinnedMode::Type::GpuGraphicSkin)
            CreateRenderMeshByGPUTexelSkin(modelSystem, meshAsset, curSkinParam);
        else if (curSkinParam.SkinMode == GeomertySkinnedMode::Type::GpuComputeSkin)
        {
            modelHandle.Write()->mAsyncResourceAssemble = true; 
            CreateRenderMeshByGPUComputeSkin(modelSystem, meshAsset, curSkinParam);
        }
        else
            CreateRenderMeshByCPUSkin(modelSystem, meshAsset, curSkinParam);
    }
    decltype(mCreatedGeometries) createdGeometries;
    {
        std::unique_lock lock(mUpdateTaskMutex);
        mCreatedGeometries.swap(createdGeometries);
    }
    std::set<ecs::EntityID> entities;
    for (auto [geometryList, meshParam] : createdGeometries)
    {
        for (auto entity : mUncreatedGeoDict[geometryList])
        {
            if (modelSystem->GetRenderWorld()->IsEntityAlive(entity))
                modelSystem->SetModelDirty(entity, true);
        }
        mUncreatedGeoDict.erase(geometryList);
    }
    return allSkinParams;
}

void SkinnedMeshBuilderR::CreateCpuSkinningTask(VertexStreamLayoutSkinParameter& skinParam, threading::TaskEventArray& taskEventArray)
{
    Assert(skinParam.SkinMode == GeomertySkinnedMode::CpuSkin);    

    static const int sVertNumPerTask = 1000;
    if (skinParam.mCurLodVertexEnd == 0)
        skinParam.mCurLodVertexEnd = skinParam.SkinningWeightInfo.InfulenceVertCount;
    const int numInfluencedVert = skinParam.mCurLodVertexEnd;
    for (int iVert = skinParam.mCurLodVertexStart; iVert < numInfluencedVert; iVert += sVertNumPerTask)
    {
        cross::anim::SkeltMeshCPUSkinUtil::InputVertexSkinningInfo perTaskVertexSkinningInfo = skinParam.VertexSkinningInfo;
        cross::anim::SkeltMeshCPUSkinUtil::SkinWeightInfo perTaskSkinningWeightInfo = skinParam.SkinningWeightInfo;

        bool needProcessNormal{false}, needProcessTangent{false};
        perTaskVertexSkinningInfo.InPositions = skinParam.VertexSkinningInfo.InPositions + iVert;
        if (skinParam.VertexSkinningInfo.InNormals)
        {
            needProcessNormal = true;
            perTaskVertexSkinningInfo.InNormals = skinParam.VertexSkinningInfo.InNormals + iVert;
        }
        if (skinParam.VertexSkinningInfo.InTangents)
        {
            needProcessTangent = true;
            perTaskVertexSkinningInfo.InTangents = skinParam.VertexSkinningInfo.InTangents + iVert;
        }
        perTaskVertexSkinningInfo.StartIndex = iVert;

        perTaskSkinningWeightInfo.InfluenceBones = skinParam.SkinningWeightInfo.InfluenceBones + iVert;
        perTaskSkinningWeightInfo.InfluenceWeights = skinParam.SkinningWeightInfo.InfluenceWeights + iVert;
        perTaskSkinningWeightInfo.InfulenceVertCount = iVert + sVertNumPerTask <= numInfluencedVert ? sVertNumPerTask : numInfluencedVert - iVert;

        // Skin the vertices in the current LOD range. If the current LOD level is maximum, the mLodEnd set to 0. First time the model is skinned, all of its LOD models are skinned.
        taskEventArray.Add(threading::Dispatch([&, perTaskVertexSkinningInfo, perTaskSkinningWeightInfo, bNormal = needProcessNormal, bTangent = needProcessTangent](auto) {
            SCOPED_CPU_TIMING(GroupRendering, "SkinningTask");
            mSkinningUtil.SkinVertices(skinParam.VBStagingBufferWrap, perTaskVertexSkinningInfo, perTaskSkinningWeightInfo, skinParam.SkinningMatrices, skinParam.SkinningMatrices->GetSize(), bNormal, bTangent);
        }));

        taskEventArray.Add(threading::Dispatch([&, perTaskVertexSkinningInfo, perTaskSkinningWeightInfo](auto) {
            SCOPED_CPU_TIMING(GroupRendering, "CopyUnSkinnedChannel");

            for (int i = 0; i < perTaskSkinningWeightInfo.InfulenceVertCount; i++)
            {
                for (UInt32 bindingIdx = 0; bindingIdx < skinParam.BufferBindingCount; bindingIdx++)
                {
                    auto& curBinding = skinParam.UploadBufferBinding[bindingIdx];
                    skinParam.VBStagingBufferWrap.MemWrite(skinParam.VertexSkinningInfo.PerVertexStride * (i + perTaskVertexSkinningInfo.StartIndex) + curBinding.mChannelOffset,
                           curBinding.mSrc + (i + perTaskVertexSkinningInfo.StartIndex) * curBinding.mStride,
                           curBinding.mStride);
                }
            }
        }));
    }
}

void SkinnedMeshBuilderR::CreatePostCpuSkinningTask(ModelSystemR* modelSystem, VertexStreamLayoutSkinParameter& skinParam)
{
    if (skinParam.SkinMode != GeomertySkinnedMode::Type::CpuSkin)
        return;

    else if (skinParam.mCurSkinLevel == SkinLevel::All)
    {
        // Skin all lod models only once to initialize the model
        auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        NGICopyBuffer region{
            skinParam.VBStagingBufferWrap.GetNGIOffset(),
            0,
            skinParam.VBSize,
        };
        rendererSystem->UpdateBuffer(skinParam.VB.get(), skinParam.VBStagingBufferWrap.GetNGIBuffer(), region, NGIResourceState::Undefined, NGIResourceState::VertexBuffer);
        modelSystem->SetModelSkinLevelByEntityID(skinParam.Entity, static_cast<UInt32>(skinParam.ModelIndex), SkinLevel::CurLod);
    }
    else if (skinParam.mCurSkinLevel == SkinLevel::CurLod)
    {
        // Only skin the current lod model to reduce time overhead
        auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        UInt32 changeVBSize = skinParam.mCurLodVertexEnd == 0
                                  ? skinParam.VBSize - skinParam.VBSize / skinParam.SkinningWeightInfo.InfulenceVertCount * skinParam.mCurLodVertexStart
                                  : skinParam.VBSize / skinParam.SkinningWeightInfo.InfulenceVertCount * (skinParam.mCurLodVertexEnd - skinParam.mCurLodVertexStart);
        // Update VB corresponding to current lod model range
        NGICopyBuffer region{
            skinParam.VBStagingBufferWrap.GetNGIOffset() + skinParam.VBSize / skinParam.SkinningWeightInfo.InfulenceVertCount * skinParam.mCurLodVertexStart,
            skinParam.VBSize / skinParam.SkinningWeightInfo.InfulenceVertCount * skinParam.mCurLodVertexStart,
            changeVBSize,
        };
        rendererSystem->UpdateBuffer(skinParam.VB.get(), skinParam.VBStagingBufferWrap.GetNGIBuffer(), region, NGIResourceState::Undefined, NGIResourceState::VertexBuffer);
    }
}

void SkinnedMeshBuilderR::CreateRenderMeshByGPUTexelSkin(ModelSystemR* modelSystem, const MeshAssetData* meshAsset, VertexStreamLayoutSkinParameter& skinParam)
{
    RenderModelComponentHandle modelHandle = modelSystem->GetModelHandle(skinParam.Entity);

    auto CreateWithoutBlendShape = [&]() -> MeshR*
    {
        auto renderMesh = TYPE_CAST(MeshR*, meshAsset->GetRenderMesh());
        modelSystem->SetModelSharedRenderMesh(modelHandle.Write(), static_cast<UInt32>(skinParam.ModelIndex), renderMesh);
        modelSystem->ClearModelPrivateRenderMesh(modelHandle.Write(), static_cast<UInt32>(skinParam.ModelIndex));

        return renderMesh;
    };

    auto CreateWithBlendShape = [&]() -> MeshR*
    {
        // GPUSkin with blend shape use PrivateMesh from IndividualModel
        MeshR* privateRenderMesh = modelSystem->GetModelPrivateRenderMesh(modelHandle.Write(), static_cast<UInt32>(skinParam.ModelIndex)).get();
        modelSystem->SetModelSharedRenderMesh(modelHandle.Write(), static_cast<UInt32>(skinParam.ModelIndex), nullptr);

        return privateRenderMesh;
    };

    MeshR* renderMesh = skinParam.HasBlendShape ? CreateWithBlendShape() : CreateWithoutBlendShape();

    if (renderMesh->TryUpdate(mCurFrame))
    {
        IVertexStreamLayoutPolicy* streamLayoutPolicy = RenderFactory::Instance().GetVertexStreamLayoutPolicy(
            skinParam.HasBlendShape ? STREAM_LAYOUT_POLICY_STATIC_BLEND_SHAPE : STREAM_LAYOUT_POLICY_GPU_GRAPHIC_SKIN);
        AssertMsg(streamLayoutPolicy != nullptr, "SkinnedRenderMeshBuilder missing StreamLayoutPolicy....!");

        // Prevent repeating assemble RenderMesh when RenderMeshListDirty is false
        if (modelSystem->GetRenderMeshDirty(modelHandle.Read(), static_cast<UInt32>(skinParam.ModelIndex)))
        {
            streamLayoutPolicy->AssembleGpuResource(renderMesh, meshAsset, &skinParam);
            // Reset RenderMeshDirty flag
            modelSystem->SetRenderMeshDirty(modelHandle.Write(), static_cast<UInt32>(skinParam.ModelIndex), false);
            mVertexCount += meshAsset->GetVertexCount();
            modelSystem->SetModelDirty(skinParam.Entity, true);
            renderMesh->SetState(MeshR::State::Initialized);
        }
    }
}

void SkinnedMeshBuilderR::CreateRenderMeshByGPUComputeSkin(ModelSystemR* modelSystem, const MeshAssetData* meshAsset, VertexStreamLayoutSkinParameter& skinParam)
{
    RenderModelComponentHandle modelHandle = modelSystem->GetModelHandle(skinParam.Entity);

    auto GetSharedAndPrivateRenderMesh = [&]() -> std::pair<MeshR*, MeshR*> {
        // GPU Compute Skin always use private RenderMeshList
        auto privaterRenderMesh = modelSystem->GetModelPrivateRenderMesh(modelHandle.Write(), static_cast<UInt32>(skinParam.ModelIndex)).get();
        
        // GPU Compute Skin always use MeshAssetData's staticRenderGeometryList
        MeshR* sharedGeometryList = static_cast<MeshR*>(meshAsset->GetRenderMesh());
        modelSystem->SetModelSharedRenderMesh(modelHandle.Write(), static_cast<UInt32>(skinParam.ModelIndex), sharedGeometryList);

        return {sharedGeometryList, privaterRenderMesh};
    };

    auto [staticMesh, privateMesh] = GetSharedAndPrivateRenderMesh();

    if (privateMesh->TryUpdate(mCurFrame))
    {
        IVertexStreamLayoutPolicy* streamLayoutPolicy = RenderFactory::Instance().GetVertexStreamLayoutPolicy(STREAM_LAYOUT_POLICY_GPU_COMPUTE_SKIN);
        AssertMsg(streamLayoutPolicy != nullptr, "SkinnedRenderMeshBuilder missing StreamLayoutPolicy....!");

        // Prevent repeating assemble RenderGeometry when RenderGeometryListDirty is false
        if (modelSystem->GetRenderMeshDirty(modelHandle.Read(), static_cast<UInt32>(skinParam.ModelIndex)))
        {
            if (privateMesh->GetState() != MeshR::State::Initializing)
            {
                mUncreatedGeoDict[privateMesh].insert(skinParam.Entity);
                privateMesh->SetState(MeshR::State::Initializing);
                // Mark current Primary model use pre_position channel or not, while skinParam allocate every time in next frame

                auto layoutParam = std::make_shared<VertexStreamLayoutSkinningAndStaticParameter>();
                layoutParam->MeshAssetHolder = modelSystem->GetModelAsset(modelHandle.Read(), static_cast<UInt32>(skinParam.ModelIndex));
                VertexStreamLayoutUtil::CollectVertexChannelDataSeparatingSkinningAndStatic(meshAsset, layoutParam->VertexBufferLayouts, layoutParam->VertexBufferSpaces, &skinParam);

                if (skinParam.VertexSkinningInfo.InOutPrePositions != nullptr)
                    modelSystem->SetRenderGeometryAdditiveVertexChannel(skinParam.Entity, static_cast<UInt32>(skinParam.ModelIndex), VertexSemantic::SemanticPositionT);
                else
                    modelSystem->RemoveRenderGeomertyAdditiveVertexChannel(skinParam.Entity, static_cast<UInt32>(skinParam.ModelIndex), VertexSemantic::SemanticPositionT);

                mBuildTasks.push_back(threading::Async([=](auto) {
                    streamLayoutPolicy->AssembleGpuResource(privateMesh, meshAsset, layoutParam.get());
                    privateMesh->SetState(MeshR::State::Initialized);
                    std::lock_guard gurad(mUpdateTaskMutex);
                    mCreatedGeometries.emplace_back(privateMesh, layoutParam);
                }));
                modelSystem->SetRenderMeshDirty(modelHandle.Write(), static_cast<UInt32>(skinParam.ModelIndex), false);
            }
        }
    }

    if (staticMesh->GetGeometryCount() == 0 || (staticMesh->GetRenderGeometry(0).GetGeometryPacket() &&
         staticMesh->GetRenderGeometry(0).GetGeometryPacket()->GetStreamCount() < VertexStreamLayoutUtil::skinnedVertexBufferCount))
    {
        if (staticMesh->GetState() != MeshR::State::Initializing)
        {
            staticMesh->SetState(MeshR::State::Initializing);
            auto layoutParam = std::make_shared<VertexStreamLayoutSkinningAndStaticParameter>();
            layoutParam->MeshAssetHolder = modelSystem->GetModelAsset(modelHandle.Read(), static_cast<UInt32>(skinParam.ModelIndex));
            VertexStreamLayoutUtil::CollectVertexChannelDataSeparatingSkinningAndStatic(meshAsset, layoutParam->VertexBufferLayouts, layoutParam->VertexBufferSpaces, &skinParam);
            
            IVertexStreamLayoutPolicy* streamLayoutPolicy = RenderFactory::Instance().GetVertexStreamLayoutPolicy(STREAM_LAYOUT_POLICY_GPU_COMPUTE_SKIN);
            AssertMsg(streamLayoutPolicy != nullptr, "SkinnedRenderMeshBuilder missing StreamLayoutPolicy....!");

            mBuildTasks.push_back(threading::Async([=](auto) {
                staticMesh->ClearAndResize(VertexStreamLayoutUtil::skinnedVertexBufferCount);
                streamLayoutPolicy->AssembleGpuResource(staticMesh, meshAsset, layoutParam.get());
                staticMesh->SetState(MeshR::State::Initialized);
            }));
        }
    }
}

void SkinnedMeshBuilderR::CreateRenderMeshByCPUSkin(ModelSystemR* modelSystem, const MeshAssetData* meshAsset, VertexStreamLayoutSkinParameter& skinParam)
{
    RenderModelComponentHandle modelHandle = modelSystem->GetModelHandle(skinParam.Entity);

    // CPUSkin use PrivateGeometry from IndividualModel
    MeshR* renderMesh = modelSystem->GetModelPrivateRenderMesh(modelHandle.Write(), static_cast<UInt32>(skinParam.ModelIndex)).get();
    modelSystem->SetModelSharedRenderMesh(modelHandle.Write(), static_cast<UInt32>(skinParam.ModelIndex), nullptr);

    if (renderMesh->TryUpdate(mCurFrame))
    {
        IVertexStreamLayoutPolicy* streamLayoutPolicy = RenderFactory::Instance().GetVertexStreamLayoutPolicy(
            skinParam.HasBlendShape ? STREAM_LAYOUT_POLICY_CPUSKIN_BLEND_SHAPE : STREAM_LAYOUT_POLICY_CPU_SKIN);

        if (streamLayoutPolicy)
        {
            streamLayoutPolicy->AssembleGpuResource(renderMesh, meshAsset, &skinParam);
            modelSystem->SetRenderMeshDirty(modelHandle.Write(), static_cast<UInt32>(skinParam.ModelIndex), true);
            mVertexCount += meshAsset->GetVertexCount();
            modelSystem->SetModelDirty(skinParam.Entity, true);
        }
        else if (!streamLayoutPolicy)
        {
            LOG_ERROR("SkinnedRenderMeshBuilder missing StreamLayoutPolicy....!");
            return;
        }
        renderMesh->SetState(MeshR::State::Initialized);
    }
}
}   // namespace cross
