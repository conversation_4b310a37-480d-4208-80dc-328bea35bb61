#include "EnginePrefix.h"

#include "CECommon/Common/EngineGlobal.h"

#include "RenderEngine/RenderFactory.h"
#include "RenderEngine/RenderEngine.h"
#include "Resource/Material.h"

#include "RenderEngine/CanvasSystemR.h"
#include "RenderEngine/RenderNodeSystemR.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/RenderPropertySystemR.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderEngine/RenderWorld.h"

#include "NativeGraphicsInterface/NGIManager.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"
#include <functional>

namespace cross {
ecs::ComponentDesc* CanvasComponentR::GetDesc()
{
    static ecs::ComponentDesc* sDesc{nullptr};
    if (!sDesc)
    {
        sDesc = EngineGlobal::GetECSFramework().CreateOrGetRenderComponentDesc<CanvasComponentR>(false);
    }
    return sDesc;
}
CanvasSystemR::CanvasSystemR()
{
    mRenderMesh.reset(new MeshR(nullptr));
    mGeoPack = RenderFactory::Instance().CreateGeometryPacket();
}

CanvasSystemR* CanvasSystemR::CreateInstance() { return new CanvasSystemR(); }


std::tuple<UInt64, REDTextureView*, bool> CanvasSystemR::GetValidUITexView(RenderingExecutionDescriptor* red, NGITextureDesc texDesc, NGITextureViewDesc texViewDesc)
{
    // UI with real entity like PixUI
    for (auto iter = mUITexResourcesMap.begin(); iter != mUITexResourcesMap.end(); iter++)
    {
        auto& res = iter->second;
        if (res.mIsVisible)
        {
            mCurrentValidCanvas = iter->first;
            auto UITexture = red->AllocateTexture("UI Texture_" + std::to_string(iter->first), texDesc);
            auto UITextureView = red->AllocateTextureView(UITexture, texViewDesc);
            return std::make_tuple(mCurrentValidCanvas, UITextureView, res.mShouldUpdate);
        }
    }
    // UI with no entity like ScreenTerminalInfo
    auto defaultTexture = red->AllocateTexture("Default Texture", texDesc);
    auto defaultTextureView = red->AllocateTextureView(defaultTexture, texViewDesc);

    return std::make_tuple(0, defaultTextureView, false);
}

void CanvasSystemR::SetUIMaterial(MaterialR* material, GPUTexture* texture, ui::Font* font, Float2 texture_size)
{
    mUIMaterial = material;
    mTexture = texture;
    mTextureSize = texture_size;

    std::size_t pixel_count = static_cast<std::size_t>(texture_size.x * texture_size.y);
    mTextureData.resize(pixel_count * 4);
    mTextureBufferSize = pixel_count * 16;;

    std::vector<UInt32> index_buffer;
    index_buffer.resize(pixel_count);
    for (size_t i = 0; i < pixel_count; i++)
    {
        index_buffer[i] = static_cast<UInt32>(i);
    }

    auto index_buffer_size = pixel_count * 4;
    NGIBufferDesc desc;
    desc.Size = index_buffer_size;
    desc.Usage = NGIBufferUsage::IndexBuffer | NGIBufferUsage::CopyDst;
    mIndexBuffer = GetNGIDevice().CreateBuffer(desc, ("IndexBuffer(" + mUIMaterial->GetName() + ")").c_str());
    mIndexBuffer->mEnableRefCount = false;
    mIndexBufferSize = index_buffer_size;

    auto* rendererSystem = EngineGlobal::Inst().GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto instance_buffer_wrap = rendererSystem->GetScratchBuffer()->AllocateStaging(NGIBufferUsage::CopySrc, index_buffer_size);
    instance_buffer_wrap.MemWrite(0, index_buffer.data(), index_buffer_size);
    NGICopyBuffer region{
        instance_buffer_wrap.GetNGIOffset(),
        0,
        index_buffer_size,
    };
    rendererSystem->UpdateBuffer(mIndexBuffer, instance_buffer_wrap.GetNGIBuffer(), region, NGIResourceState::Undefined, NGIResourceState::IndexBuffer);
}

void CanvasSystemR::SetCanvasVisibleState(UInt64 key, bool state /*= true*/) 
{
    if (auto iter = mUITexResourcesMap.find(key); iter != mUITexResourcesMap.end())
    {
        auto& res = iter->second;
        res.mIsVisible = state;
    }
}

void CanvasSystemR::OnFirstUpdate(FrameParam* frameParam)
{
    auto* renderPropertySystem = TYPE_CAST(RenderPropertySystemR*, mRenderWorld->GetRenderSystem<RenderPropertySystemR>());
    renderPropertySystem->SetCullingProperty(mUIEntity, CullingProperty::CULLING_PROPERTY_ALWAYS_VISIBLE);
}

void CanvasSystemR::OnBuildUpdateTasks(FrameParam* frameParam) 
{
    CreateTaskFunction<threading::ThreadID::RenderingThreadLocal>(FrameTickStage::Update, {}, [=]
    {
        UInt32 eventCount = mRenderWorld->GetRemainedEventCount<EntityCreateEvent>(true);
        for (UInt32 i = 0; i < eventCount; ++i)
        {
            auto& lifeEvent = mRenderWorld->GetRemainedEvent<EntityCreateEvent>(i);
            if (ecs::HasComponentMask<CanvasComponentR>(lifeEvent.mData.mChangedComponentMask) && EnumHasAnyFlags(lifeEvent.mData.mEventFlag, EntityLifeCycleEventFlag::CreateComponent | EntityLifeCycleEventFlag::MoveComponent))
            {
                auto& entityID = lifeEvent.mData.mEntityID;
                auto [canvas, renderNodeComp] = mRenderWorld->GetComponent<CanvasComponentR, RenderNodeComponentR>(entityID);
                auto renderNode = canvas.mComponent->mRenderNode;
                renderNodeComp.Write()->SetRenderNode(canvas.Read()->mRenderNode);
            }
        }
    });
}

void CanvasSystemR::Render(oui::primitive_buffer* buffer)
{
    SCOPED_CPU_TIMING(GroupRendering, "CanvasRRender");
    if (mUIMaterial == nullptr) return;

    auto* rendererSystem = EngineGlobal::Inst().GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

    if (buffer->primitive_count > 0)
    // update vertex_id data
    {
        //if (mRenderMeshes.size() == 0)
        //    mRenderMeshes.resize(1);
        auto id_buffer_size = buffer->index_count * sizeof(UInt32);
        auto id_buffer_wrap = rendererSystem->GetScratchBuffer()->AllocateScratch(NGIBufferUsage::VertexBuffer, id_buffer_size);
        id_buffer_wrap.MemWrite(0, buffer->merged_index_buffer.data(), id_buffer_size);

        // update primitive buffer
        memcpy(mTextureData.data(), buffer->merged_primitive_buffer.data(), buffer->primitive_count * sizeof(float));
        mTexture->UploadImage(0, 0, 0, reinterpret_cast<UInt8*>(mTextureData.data()), static_cast<UInt32>(mTextureBufferSize));

        mGeoPack->Clear();
        mGeoPack->AddVertexStream(id_buffer_wrap.GetNGIBuffer(), static_cast<UInt32>(id_buffer_size), static_cast<UInt32>(id_buffer_wrap.GetNGIOffset()), mLayout);
        UInt32 IndexCount = static_cast<UInt32>(mIndexBufferSize) / 4;
        mGeoPack->SetIndexStream(mIndexBuffer, static_cast<UInt32>(mIndexBufferSize), IndexCount, 0);

        mRenderMesh->ClearAndResize(1);
        mRenderMesh->GetRenderGeometry(0).SetData(mGeoPack.get(), buffer->index_count, 0, buffer->index_count, 0, buffer->index_count / 3, PrimitiveTopology::TriangleList);
        //auto& renderMesh = mRenderMeshes[0];
        //renderMesh.mLODCount = 1;
        //renderMesh.mLODGeometries[0] = &(mGeometryLists->GetRenderGeometry(0));
        //renderMesh.mDefaultMaterial = mUIMaterial;

        //auto renderNode = mRenderWorld->GetComponent<RenderNodeComponentR>(mUIEntity);
        //auto* renderNodeSystem = mRenderWorld->GetRenderSystem<RenderNodeSystemR>();
        mViewports.resize(1);
        mViewports[0].first = nullptr;

        //renderNodeSystem->SetRenderMeshes(renderNode, mRenderMeshes);
        //renderNodeSystem->SetViewportAndScissors(renderNode, mViewports);
    }
}

void CanvasSystemR::NotifyEvent(const SystemEventBase& event, UInt32& flag) {
    RenderSystemBase::NotifyEvent(event, flag);

    if (event.mEventType == OnSystemAddToRenderWorldEvent::sEventType)
    {
        mUIEntity = mRenderWorld->CreateEntityID();
        mRenderWorld->CreateComponents<TransformComponentR, RenderNodeComponentR, RenderPropertyComponentR>(mUIEntity);

        mLayout.AddVertexChannelLayout(VertexChannel::Position0, VertexFormat::UInt, 0);
    }
}

void CanvasSystemR::OnBeginFrame(FrameParam* frameParam)
{
    mNVGDrawUnits.clear();
}

void CanvasSystemR::OnEndFrame(FrameParam* frameParam)
{
}

void CanvasSystemR::SetCanvasFlags(ecs::EntityID entity, bool flag)
{
    auto [id, canvas, renderNode] = mRenderWorld->GetComponent<ecs::EntityIDComponent, CanvasComponentR, RenderNodeComponentR>(entity);
    UInt64 key = id.GetEntityID().GetValue();

    if (!canvas.mComponent->mRenderNode->IsEnabled())
    {
        if (auto iter = mUITexResourcesMap.find(key); iter != mUITexResourcesMap.end())
        {
            auto& res = iter->second;
            res.mShouldUpdate = false;
        }
        return;
    }

    if (auto iter = mUITexResourcesMap.find(key); iter != mUITexResourcesMap.end())
    {
        auto& res = iter->second;
        res.mShouldUpdate = flag;
    }
}

void CanvasSystemR::SetNanoVGRenderData(ecs::EntityID entity, FrameVector<std::vector<NanoVGItem>>* items, std::map<int, NanoVGTexItem> textures, FrameVector<NanoVGVertex>* vertexData, Scissor scissor)
{
    SCOPED_CPU_TIMING(GroupRendering, "SetNanoVGRenderData");
    auto [id, canvas, renderNode] = mRenderWorld->GetComponent<ecs::EntityIDComponent, CanvasComponentR, RenderNodeComponentR>(entity);
    UInt64 key = id.GetEntityID().GetValue();

    if (!canvas.mComponent->mRenderNode->IsEnabled())
    {
        // hide canvas
        SetCanvasVisibleState(key, false);
        return;
    }

    SetCanvasVisibleState(key, true);

    // handle data here
    SCOPED_CPU_TIMING(GroupRendering, "CanvasSystemRUpdate -- NanoVG");
    if (!items || !vertexData || items->IsEmpty() || vertexData->IsEmpty())
        return;

    // currently not valid
    if (key != mCurrentValidCanvas)
        return;

    mScissor = scissor;

    // 1. get NGITexture and NGITextureView for g_texture
    for (auto iter = textures.begin(); iter != textures.end(); iter++)
    {
        auto ngiTex = iter->second.mTex->GetNGITexture();
        auto ngiTexView = iter->second.mTex->GetNGITextureView();
        mNVGTexViewMap[iter->first] = std::tuple(ngiTex, ngiTexView);
    }

    auto& renderFactory = RenderFactory::Instance();
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto stagingMgr = rendererSystem->GetScratchBuffer();

    auto allocator = EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameAllocator();

    mNVGDrawUnits = std::move(FrameStdVector<REDRawDrawUnit>(rendererSystem->GetRenderingExecutionDescriptor()->GetREDFrameAllocator()));

    // restrict nanovg rendering area using scissor
    auto* curScissor = allocator->Allocate<NGIScissor>(1, FrameStage::FRAME_STAGE_RENDER);
    *curScissor = {
        scissor.x,
        scissor.y,
        scissor.width,
        scissor.height,
    };

    // copy vertex data
    UInt32 vbSize = static_cast<UInt32>(vertexData->GetSize() * sizeof(NanoVGVertex));
    auto vertexBufferWrap = stagingMgr->AllocateScratch(NGIBufferUsage::VertexBuffer, vbSize);
    vertexBufferWrap.MemWriteContainer(vertexData, vbSize, 0);

    for (UInt32 ii = 0, len = items->GetSize(); ii < len; ++ii)
    {
        auto& itemVec = items->At(ii);
        if (itemVec.empty())
            continue;
        if (!mNVGGeoPack)
        {
            mNVGGeoPack = renderFactory.CreateGeometryPacket();
        }
        mNVGGeoPack->Clear();
        mNVGGeoPack->AddVertexStream(vertexBufferWrap.GetNGIBuffer(), vbSize, (UInt32)vertexBufferWrap.GetNGIOffset(), *itemVec.at(0).mLayout);

        for (UInt32 i = 0; i < itemVec.size(); i++)
        {
            auto& nvgItem = itemVec.at(i);
            auto& drawUnit = mNVGDrawUnits.emplace_back();

            // vertexStart does not consider stride, if you multi stride to offset, please divide it
            drawUnit.mGeometry = RenderGeometry{mNVGGeoPack.get(), nvgItem.mVertexCount, (UInt32)nvgItem.mVertexDataOffset, 0, 0, 0, nvgItem.mPrimitiveType, (UInt16)i};
            drawUnit.mMaterial = (TYPE_CAST(MaterialR*, nvgItem.mMaterial->GetRenderMaterial()));
            // don't need mInstanceCount = 1 here, RenderDrawUnits func will ensure this

            // g_texture (font atlas)
            auto* texHandle = nvgItem.mPropSet->GetNumericProperty<int>("texHandle");
            int texH = *texHandle;
            if (auto iterTex = mNVGTexViewMap.find(texH); iterTex != mNVGTexViewMap.end())
            {
                auto [_, texView] = iterTex->second;
                nvgItem.mPropSet->SetProperty(NAME_ID("g_texture"), texView);
            }
            drawUnit.mObjectProperties = (nvgItem.mPropSet.get());
            drawUnit.mScissor = curScissor;
        }
    }
}

void CanvasSystemR::SetCanvasRenderData(ecs::EntityID entity, FrameVector<CanvasItemBatch>* itemBatches, FrameArray<CanvasGeometryBatch>* geometryBatches)
{
    SCOPED_CPU_TIMING(GroupRendering, "CanvasComponentRUpdate");
    auto [id, canvas, renderNodeComp] = mRenderWorld->GetComponent<ecs::EntityIDComponent, CanvasComponentR, RenderNodeComponentR>(entity);
    UInt64 key = id.GetEntityID().GetValue();
    auto renderNode = canvas.mComponent->mRenderNode;

    if (!renderNode->IsEnabled())
    {
        SetCanvasVisibleState(key, false);
        return;
    }
    SetCanvasVisibleState(key, true);

    // create UI canvas
    if (auto iter = mUITexResourcesMap.find(key); iter == mUITexResourcesMap.end())
    {
        mUITexResourcesMap.insert({key, UIResidentTexResourses{}});
    }

    // currently not valid
    /*if (key != mCurrentValidCanvas)
        return;*/

    if (itemBatches->GetSize() == 0 || geometryBatches->GetSize() == 0)
    {
        return;
    }

    auto* renderSystem = EngineGlobal::Inst().GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

    for (UInt32 i = 0; i < CanvasItemTypeCount; ++i)
    {
        auto& pack = renderNode->mPackets[i];
        pack->Clear();
        auto& geometryBatch = geometryBatches->At(i);
        if (geometryBatch.mIndexCount == 0)
            continue;

        UInt32 vertexSize = geometryBatch.mVertexData->GetSize() * sizeof(CanvasVertex);
        auto vertexBufferWrap = renderSystem->GetScratchBuffer()->AllocateScratch(NGIBufferUsage::VertexBuffer, vertexSize);
        vertexBufferWrap.MemWriteContainer(geometryBatch.mVertexData, vertexSize, 0);

        UInt32 indexSize = geometryBatch.mIndexData->GetSize() * sizeof(UInt16);
        auto indexBufferWrap = renderSystem->GetScratchBuffer()->AllocateScratch(NGIBufferUsage::IndexBuffer, indexSize);
        indexBufferWrap.MemWriteContainer(geometryBatch.mIndexData, indexSize, 0);

        pack->AddVertexStream(vertexBufferWrap.GetNGIBuffer(), vertexSize, UInt32(vertexBufferWrap.GetNGIOffset()), geometryBatch.mLayout);
        pack->SetIndexStream(indexBufferWrap.GetNGIBuffer(), indexSize, geometryBatch.mIndexCount, UInt32(indexBufferWrap.GetNGIOffset()));
    }
    renderNode->mRenderMesh->ClearAndResize(itemBatches->GetSize());
    
    for (UInt32 i = 0; i < itemBatches->GetSize(); ++i)
    {
        auto& batch = itemBatches->At(i);
        auto pack = renderNode->mPackets[static_cast<UInt32>(batch.mType)];
        renderNode->mRenderMesh->GetRenderGeometry(i).SetData(
            pack.get(), batch.mGeometry.mVertexCount, batch.mGeometry.mVertexStart, batch.mGeometry.mIndexCount, batch.mGeometry.mIndexStart, batch.mGeometry.mPrimitiveCount, batch.mGeometry.mPrimitiveType);
    }

    renderNode->mItemBatches = { itemBatches->begin(), itemBatches->end() };
}

void CanvasSystemR::SetLayer(ecs::EntityID entity, int layer) {
    if (!mRenderWorld->HasComponent<CanvasComponentR>(entity))
        return;
    auto canvasComp = mRenderWorld->GetComponent<CanvasComponentR>(entity);
    canvasComp.Write()->mRenderNode->mLayer = layer;
}

int CanvasSystemR::GetLayer(ecs::EntityID entity) {
    if (!mRenderWorld->HasComponent<CanvasComponentR>(entity))
        return 0;
    auto canvasComp = mRenderWorld->GetComponent<CanvasComponentR>(entity);
    return canvasComp.Read()->mRenderNode->mLayer;
}

void CanvasSystemR::SetVisible(ecs::EntityID entity, bool visible) {
    auto [id, canvas, renderNode] = mRenderWorld->GetComponent<ecs::EntityIDComponent, CanvasComponentR, RenderNodeComponentR>(entity);
    canvas.mComponent->mRenderNode->SetEnabled(visible);
}

}   // namespace cross
