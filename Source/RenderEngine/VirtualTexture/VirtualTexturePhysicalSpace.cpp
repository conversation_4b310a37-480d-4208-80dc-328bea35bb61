#include "VirtualTexturePhysicalSpace.h"
#include "NativeGraphicsInterface/NGIManager.h"
#include "VirtualTexturePoolConfig.h"
#include "Resource/Texture/TextureFormatConvert.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/VirtualTexture/VirtualTextureSystemR.h"

namespace cross {
VTPhysicalSpace::VTPhysicalSpace(const VTPhysicalSpaceDescription& InDesc, UInt16 InID)
    : description(InDesc)
    , mNumRefs(0u)
    , mID(InID)
{
    // physicaltexture pagepool size depend on format, different format have different tile size bytes
    VTSpacePoolConfig config;
    config.sizeInMegabyte = EngineGlobal::GetSettingMgr()->GetRenderPipelineSetting()->VTPoolSizeMax;
    const UInt32 poolSizeInBytes = static_cast<UInt32>(config.sizeInMegabyte * 1024u * 1024u);
    // CalculateImageBytes
    SizeType tileSizeBytes = 0;
    for (UInt8 layer = 0; layer < InDesc.numLayers; ++layer)
    {
        cross::TexelBlockProperty texelBlockProp = GetFormatTexelBlockProperty(GetGraphicsFormat(InDesc.format[layer], InDesc.colorSapces[layer]));
        UInt32 blockByteSize = texelBlockProp.Size;
        blockByteSize = 4;          // TODO(huhvzhang): adaptive Pool size have some problem. 
        tileSizeBytes += InDesc.tileSize * InDesc.tileSize / blockByteSize;
    }
    const UInt32 maxTiles = std::max(static_cast<UInt32>((poolSizeInBytes / tileSizeBytes)), 1u);
    mTextureSizeInTiles = static_cast<UInt32>(std::floor(std::sqrt(static_cast<float>(maxTiles))));

    mPagePool.Initialize(GetNumTiles());
    printf("---VT----PhysicalSpace page pool initialize Tile size = %d\n", GetNumTiles());
    InitRenderResource();
}
void VTPhysicalSpace::ReleaseNGI() 
{
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    for (UInt8 layer = 0; layer < description.numLayers; ++layer)
    {
        rendererSystem->DestroyNGIObject(mTexture[layer]);
        rendererSystem->DestroyNGIObject(mTextureSRV[layer]);
    }
}
void VTPhysicalSpace::Release()
{
    mNumRefs--;
    if (mNumRefs == 0)
    {
        auto vtsystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<VirtualTextureSystemR>();
        mFrameDeleted = vtsystem->mFrame;
    }
}
void VTPhysicalSpace::InitRenderResource() 
{
    // PhysicalSpace 
    for (UInt8 layer = 0; layer < description.numLayers; ++layer) 
    {
        const UInt32 textureSize = GetTextureSize();
        auto graphicsFormat = GetGraphicsFormat(description.format[layer], description.colorSapces[layer]);
        NGITextureDesc desc{
            graphicsFormat,
            NGITextureType::Texture2D, 
            1, 
            1, 
            textureSize, 
            textureSize, 
            1, 
            1, 
            NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst
        };
        NGITexture* texture = GetNGIDevice().CreateTexture(desc, "VTPhysicalsTexture");
        mTexture[layer] = texture;

        NGITextureViewDesc viewDesc = 
        {
            NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst,
            graphicsFormat,
            NGITextureType::Texture2D,
            {
                NGITextureAspect::Color,
                0,
                1,
                0,
                1,
            }
        };
        mTextureSRV[layer] = GetNGIDevice().CreateTextureView(texture, viewDesc);

        auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        rendererSystem->UpdateTexture(mTexture[layer], nullptr, NGICopyBufferTexture{}, NGIResourceState::Undefined, NGIResourceState::ShaderStageBitMask | NGIResourceState::ShaderResourceBit);
    }
}
}   // namespace cross
