#pragma once

#include "VirtualTexturing.h"
#include "VirtualTextureAllocator.h"
#include "TexturePageMap.h"
#include "VirtualTextureShared.h"
#include "RenderEngine/RenderWorld.h"
#include "RenderEngine/ComputeShaderR.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDResource.h"
namespace cross {

class AllocatedVirtualTexture;
class VirtualTextureSystemR;
struct RenderingExecutionDescriptor;

struct VTSpaceDescription
{
    UInt32 tileSize = 0u;
    UInt32 tileBorderSize = 0u;
    UInt8 dimensions = 0u;
    EVTPageTableFormat pageTableFormat = EVTPageTableFormat::PageTableFormatUInt32;
    UInt8 numPageTableLayers = 0u;
    UInt8 bPrivateSpace = false;
    UInt32 maxSpaceSize = VIRTUALTEXTURE_MAX_PAGETABLE_SIZE;
    UInt32 indirectionTextureSize = 0u;
};

inline bool operator==(const VTSpaceDescription& lhs, const VTSpaceDescription& rhs)
{
    return lhs.dimensions == rhs.dimensions && lhs.tileSize == rhs.tileSize && lhs.tileBorderSize == rhs.tileBorderSize && lhs.numPageTableLayers == rhs.numPageTableLayers && lhs.pageTableFormat == rhs.pageTableFormat &&
           lhs.bPrivateSpace == rhs.bPrivateSpace && lhs.maxSpaceSize == rhs.maxSpaceSize && lhs.indirectionTextureSize == rhs.indirectionTextureSize;
}
inline bool operator!=(const VTSpaceDescription& lhs, const VTSpaceDescription& rhs)
{
    return !operator==(lhs, rhs);
}

// Virtual memory address space mapped by a page table texture
class VTSpace
{
public:
    static const UInt32 layersPerPageTableTexture = IAllocatedVirtualTexture::layersPerPageTableTexture;

    VTSpace();
    VTSpace(UInt8 inID, const VTSpaceDescription& inDesc);

    inline const VTSpaceDescription& GetDescription() const
    {
        return mDescription;
    }
    inline UInt32 GetPageTableWidth() const
    {
        return mCachedPageTableWidth;
    }
    inline UInt32 GetPageTableHeight() const
    {
        return mCachedPageTableHeight;
    }
    inline UInt8 GetDimensions() const
    {
        return mDescription.dimensions;
    }
    inline EVTPageTableFormat GetPageTableFormat() const
    {
        return mDescription.pageTableFormat;
    }
    inline UInt8 GetNumPageTableLayers() const
    {
        return mDescription.numPageTableLayers;
    }
    inline UInt32 GetNumPageTableTextures() const
    {
        return (mDescription.numPageTableLayers + layersPerPageTableTexture - 1u) / layersPerPageTableTexture;
    }
    inline UInt8 GetID() const
    {
        return mID;
    }

    inline UInt32 GetNumPageTableLevels() const
    {
        return mCachedNumPageTableLevels;
    }
    inline VTMemoryAllocator& GetAllocator()
    {
        return mAllocator;
    }
    inline const VTMemoryAllocator& GetAllocator() const
    {
        return mAllocator;
    }
    inline VTPageMap& GetPageMapForPageTableLayer(UInt8 pageTableLayerIndex)
    {
        Assert(pageTableLayerIndex < mDescription.numPageTableLayers);
        return mPhysicalPageMap[pageTableLayerIndex];
    }
    inline const VTPageMap& GetPageMapForPageTableLayer(UInt8 pageTableLayerIndex) const
    {
        Assert(pageTableLayerIndex < mDescription.numPageTableLayers);
        return mPhysicalPageMap[pageTableLayerIndex];
    }


    inline UInt32 AddRef()
    {
        return ++mNumRefs;
    }
    inline UInt32 Release()
    {
        return --mNumRefs;
    }
    inline UInt32 GetRefCount() const
    {
        return mNumRefs;
    }

    void ReleaseRenderResource();

    UInt32 GetSizeInBytes() const;

    UInt32 AllocateVirtualTexture(AllocatedVirtualTexture* virtualTexture);

    void FreeVirtualTexture(AllocatedVirtualTexture* virtualTexture);

    NGITexture* GetPageTableTexture(UInt32 pageTableIndex) const
    {
        Assert(pageTableIndex < GetNumPageTableTextures());
        return mPageTable[pageTableIndex].texture->GetNativeTexture();
    }

    NGITextureView* GetPageTableTextureSRV(UInt32 pageTableIndex) const
    {
        Assert(pageTableIndex < GetNumPageTableTextures());
        return mPageTable[pageTableIndex].textureView->GetNativeTextureView();
    }

    void QueueUpdate(UInt8 layer, UInt8 vLogSize, UInt32 vAddress, UInt8 vLevel, const PhysicalTileLocation& pTileLocation);
    void AllocateTextures();

    void ApplyUpdates(RenderingExecutionDescriptor* RED, RenderWorld* world, ComputeShaderR* VTCS, VirtualTextureSystemR* system);

    void InitRenderResource();

    void SetPagetableCleanedState(bool cleaned);

private:
    void ClearPageTableValue(RenderingExecutionDescriptor* RED);

    static const UInt32 TextureCapacity = (VIRTUALTEXTURE_SPACE_MAXLAYERS + layersPerPageTableTexture - 1u) / layersPerPageTableTexture;
    static const UInt32 MipCount = 8;
    struct FTextureEntry
    {
        REDUniquePtr<REDResidentTexture> texture;
        REDUniquePtr<REDResidentTextureView> textureView;
    };

    VTSpaceDescription mDescription;

    VTMemoryAllocator mAllocator;
    VTPageMap mPhysicalPageMap[VIRTUALTEXTURE_SPACE_MAXLAYERS];

    FTextureEntry mPageTable[TextureCapacity];
    TextureFormat mTexturePixelFormat[TextureCapacity];

    std::vector<PageTableUpdate> mPageTableUpdates[VIRTUALTEXTURE_SPACE_MAXLAYERS];
    UInt32 mNumRefs;

    UInt8 mID;
    UInt32 mCachedPageTableWidth;
    UInt32 mCachedPageTableHeight;
    UInt32 mCachedNumPageTableLevels;
    bool bNeedToAllocatePageTable;
    bool bForceEntireUpdate;
    bool bPageTableCleaned = false;
};
}
