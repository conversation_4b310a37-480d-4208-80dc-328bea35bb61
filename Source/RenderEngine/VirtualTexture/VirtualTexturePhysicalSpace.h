#pragma once
#include "NativeGraphicsInterface/NGI.h"
#include "TexturePagePool.h"
#include "VirtualTexturing.h"

namespace cross {
struct VTPhysicalSpaceDescription
{
    UInt32 tileSize;
    UInt8 dimensions;
    UInt8 numLayers;
    TextureFormat format[VIRTUALTEXTURE_SPACE_MAXLAYERS] = {TextureFormat::TextureFormat_None};
    ColorSpace colorSapces[VIRTUALTEXTURE_SPACE_MAXLAYERS] = {ColorSpace::Linear};
    bool bContinuousUpdate;
};


inline bool operator==(const VTPhysicalSpaceDescription& Lhs, const VTPhysicalSpaceDescription& Rhs)
{
    bool bSame = true;
    if (Lhs.tileSize != Rhs.tileSize || Lhs.numLayers != Rhs.numLayers || Lhs.dimensions != Rhs.dimensions || Lhs.bContinuousUpdate != Rhs.bContinuousUpdate)
    {
        bSame = false;
    }
    for (UInt8 i = 0; i < VIRTUALTEXTURE_SPACE_MAXLAYERS; i++)
    {
        if (Lhs.format[i] != Rhs.format[i])
            bSame = false;
        if (Lhs.colorSapces[i] != Rhs.colorSapces[i])
            bSame = false;
    }
    return bSame;
}
inline bool operator!=(const VTPhysicalSpaceDescription& Lhs, const VTPhysicalSpaceDescription& Rhs)
{
    return !operator==(Lhs, Rhs);
}

class VTPhysicalSpace
{
public:
    VTPhysicalSpace(const VTPhysicalSpaceDescription& inDesc, UInt16 inID);

    ~VTPhysicalSpace() {}

    void ReleaseNGI();
    void Release();

    inline const VTPagePool& GetPagePool() const
    {
        return mPagePool;
    }
    inline VTPagePool& GetPagePool()
    {
        return mPagePool;
    }
    inline UInt32 AddRef()
    {
        return ++mNumRefs;
    }
    inline UInt32 GetRefCount() const
    {
        return mNumRefs;
    }
    inline const VTPhysicalSpaceDescription& GetDescription() const
    {
        return description;
    }

    inline UInt16 GetID() const             { return mID;}
    inline UInt32 GetNumTiles() const       { return mTextureSizeInTiles * mTextureSizeInTiles;}
    inline UInt32 GetSizeInTiles() const
    {
        return mTextureSizeInTiles;
    }
    inline UInt32 GetTextureSize() const
    {
        return mTextureSizeInTiles * description.tileSize;
    }
    inline UInt2 GetPhysicalLocation(UInt16 pAddress) const
    {
        return UInt2(pAddress % mTextureSizeInTiles, pAddress / mTextureSizeInTiles);
    }

    NGITexture* GetPhysicalTexture(UInt32 Layer) const
    {
        return mTexture[Layer];
    }

    NGITextureView* GetPhysicalTextureSRV(SInt32 Layer) const
    {
        return mTextureSRV[Layer];
    }

    void InitRenderResource();
    void SetFrameDeleted(UInt32 frame) 
    {
        mFrameDeleted = static_cast<SInt32>(frame);
    }
    auto& GetFrameDeleted() const
    {
        return mFrameDeleted;
    }

private:
    VTPhysicalSpaceDescription description;
    VTPagePool mPagePool;

    NGITexture* mTexture[VIRTUALTEXTURE_SPACE_MAXLAYERS];
    NGITextureView* mTextureSRV[VIRTUALTEXTURE_SPACE_MAXLAYERS];
    UInt32 mTextureSizeInTiles;
    UInt32 mNumRefs;
    UInt16 mID;

    SInt32 mFrameDeleted = -1;
};
}
