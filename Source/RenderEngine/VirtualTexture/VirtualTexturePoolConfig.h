#pragma once

#include "VirtualTexturing.h"

namespace cross {

struct VTSpacePoolConfig
{
    VTSpacePoolConfig()
        : minTileSize(0)
        , maxTileSize(0)
        , sizeInMegabyte(0)
        , bAllowSizeScale(false)
        , scalabilityGroup(0)
    {}

    /** Minimum tile size to match (including tile border). */
    SInt32 minTileSize;

    /** Maximum tile size to match (including tile border). */
    SInt32 maxTileSize;

    /** Format set to match. One pool can contain multiple layers with synchronized page table mappings. */
    std::vector<TextureFormat> Formats;

    /** Upper limit of size in megabytes to allocate for the pool. The allocator will allocate as close as possible to this limit. */
    float sizeInMegabyte;

    /** Allow the size to allocate for the pool to be scaled by some factor. */
    bool bAllowSizeScale;

    /** Scalability group index that gives the size scale. */
    UInt32 scalabilityGroup;

    /** Is this the default config? Use this setting when we can't find any other match. */
    bool IsDefault() const
    {
        return false;
        //return Formats.size() == 0;
    }
};
}
