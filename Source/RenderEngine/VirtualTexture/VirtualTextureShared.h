#pragma once
#include "CrossBase/Log.h"
namespace cross {
union PhysicalTileLocation
{
    PhysicalTileLocation() {}
    PhysicalTileLocation(const UInt2& inVec)
        : tileX(static_cast<UInt8>(inVec.x))
        , tileY(static_cast<UInt8>(inVec.y))
    {
        Assert(inVec.x >= 0 && inVec.x <= 255);
        Assert(inVec.y >= 0 && inVec.y <= 255);
    }

    UInt16 packed;
    struct
    {
        UInt8 tileX;
        UInt8 tileY;
    };
};

struct PageTableUpdate
{
    // the address in the allocated VT, tile id, not miped
    UInt32 vAddress;
    // the location in the physical texture 
    PhysicalTileLocation pTileLocation;
    // the vLevel of the acutal teture data that store on physical texture
    UInt8 vLevel;
    // represent the mip level in Allocated VT.
    UInt8 vLogSize;
    PageTableUpdate() {}
    PageTableUpdate(const PageTableUpdate& other) = default;
    PageTableUpdate& operator=(const PageTableUpdate& other) = default;
    PageTableUpdate(const PageTableUpdate& update, UInt32 offset, UInt8 vDimensions)
        : vAddress(update.vAddress + (offset << (vDimensions * update.vLogSize)))
        , pTileLocation(update.pTileLocation)
        , vLevel(update.vLevel)
        , vLogSize(update.vLogSize)
    {}

    inline void Check(UInt8 vDimensions)
    {
        const UInt32 lowBitMask = (1u << (vDimensions * vLogSize)) - 1;
        Assert((vAddress & lowBitMask) == 0);
    }

    inline int OutputLog(const std::string & tag = "") const
    {
        UInt32 x = VTMath::ReverseMortonCode2(vAddress);
        UInt32 y = VTMath::ReverseMortonCode2(vAddress >> 1);
        UInt32 packedPageTableValue = vLevel;
        packedPageTableValue |= pTileLocation.tileX << 4;
        packedPageTableValue |= pTileLocation.tileY << (4 + 8);
        LOG_INFO("{} Expands write to Mip {} Pos {} {}, packed {}, physical value vLevel {}, {} {} packed {} ", 
            tag, vLogSize, x/(1 << vLogSize), y / (1 << vLogSize), vAddress, vLevel, pTileLocation.tileX, pTileLocation.tileY, packedPageTableValue);
        return 0;
    }
};

inline bool operator == (const PageTableUpdate& left, const PageTableUpdate& right)
{
    return left.vAddress == right.vAddress
        && left.pTileLocation.packed == right.pTileLocation.packed
        && left.pTileLocation.tileX == right.pTileLocation.tileX
        && left.pTileLocation.tileY == right.pTileLocation.tileY
        && left.vLevel == right.vLevel
        && left.vLogSize == right.vLogSize;
}
}   // namespace cross