#pragma once

#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"

namespace cross
{
struct VirtualShadowMapProjectionData
{
    Float4x4 worldToShadowMatrix;
    Float4x4 normalWorldToShadowMatrix;
    Float4x4 shadowViewToClipMatrix;
    Float3 clipmapWorldOrigin;
    SInt32 clipmapLevel;
    UInt32 clipmapLevelCount;
    UInt32 lightType;
    Int2 clipmapCornerOffset;
    Float3 lightDirection;   // temp
    float resolutionLodBias;
    Float3 clipmapTilePosition;
    SInt32 clipmapIndex;
};

struct VirtualShadowMapViewData
{
    Float4x4 worldToShadowMatrix;
    Float3 tilePosition;
    UInt32 virtualShadowMapId;
    UInt32 levelIndex;
    Float3 _pad;
    UInt4 viewRect;
};

struct VirtualShadowMapArrayFrameData
{
    UInt32 virtualShadowMapCount;

    REDBuffer* pageTableBuffer;
    REDBuffer* pageFlagsBuffer;
    REDBuffer* dynamicCasterPageFlagsBuffer;
    REDBuffer* pageRectBoundsBuffer;
    REDBuffer* physicalPageMetaDataBuffer;
    REDBuffer* hzbPhysicalBuffer;   // Unused

    FrameStdVector<VirtualShadowMapProjectionData> projectionData;
};

struct VirtualShadowMapCacheData
{
    SInt32 prevVirtualShadowMapId;
};
}