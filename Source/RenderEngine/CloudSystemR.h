#pragma once

#include "CECommon/Common/RenderSystemBase.h"
#include "ECS/Develop/Framework.h"
#include "ECS/Develop/Framework/Types.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/Texture/GPUTexture.h"
#include "Resource/AssetStreaming.h"

#include "RenderEngine/CameraSystemR.h"
#include "RenderEngine/RenderWorldConst.h"
#include "RenderEngine/CloudSetting.h"

#define RENDER_ClOUD_RESOURCE(Type, Name, DefaultValue)                                                                                                                                                                                        \
    CEMeta(Serialize, Editor)                                                                                                                                                                                                                  \
    CECSAttribute(JsonProperty(#Name))                                                                                                                                                                                                         \
    CECSAttribute(PropertyInfo(PropertyType = "StringAsResource", ToolTips = #Name, FileTypeDescriptor = "Assets#nda", ObjectClassID1 = ClassIDType.CLASS_##Type))                                                                             \
    std::string Name = DefaultValue;                                                                                                                                                                                                           \
    Type##Ptr Name##Res;                                                                                                                                                                                                                       \
    Type##R* Name##R;

#define LOAD_RENDER_CLOUD_COMPUTE_SHADER(Name)                                                                                                                                                                                                 \
    Name##Res = TypeCast<resource::ComputeShader>(gAssetStreamingManager->LoadSynchronously(Name));                                                                                                                                            \
    Name##R = dynamic_cast<ComputeShaderR*>(Name##Res->GetRenderObject());

namespace cross {
// to be deprecated
struct CloudComponentR : ecs::IComponent
{
    CEFunction(Reflect)
    RENDER_ENGINE_API static ecs::ComponentDesc* GetDesc();

public:
    CloudRangeSetting CloudRange;
    ThunderstormToRender ThunderStorm;
    CloudShadowSetting CloudShadow;

    MaterialR* CloudMtl{nullptr};

    friend class CloudSystemR;
};

class SkyCloud;

class RENDER_ENGINE_API CloudSystemR final : public RenderSystemBase
{
    CEMetaInternal(Reflect)
    using CloudHandle = ecs::ComponentHandle<CloudComponentR>;
    using CloudWriter = ecs::ScopedComponentWrite<CloudComponentR>;
    using CloudReader = ecs::ScopedComponentRead<CloudComponentR>;

private:
    void InitCloudSystemR();

    Float3 WGS84CoordinateTransform_To3D(float longitude, float latitude, float altitude);

public:
    // to be deprecated
    void UpdateCloudLayerProperties(REDPass* pass);
    void UpdateCloudDataProperties(REDPass* pass);
    // thunderstorm's uploading method is right
    void UpdateThunderstormProperties(REDPass* pass);
    void UpdateCloudShadowContext(REDPass* pass);
    void UpdateCloudShadowParameters(REDPass* pass);
    float GetCloudMaxCoverage();
    float GetCloudCoverage(UInt32 layer);

    void SetVolcanicAsh(bool isVolcanicAsh) { mIsVolcanicAsh = isVolcanicAsh; }
    bool GetVolcanicAsh() const { return mIsVolcanicAsh; }

    inline void SetCloudLightsEntity(const FrameStdVector<ecs::EntityID>& entityIDs)
    {
        mCloudLightsEntityIDs = entityIDs;
    }

    FrameStdVector<ecs::EntityID>& GetCloudLightsEntityIDs() { return mCloudLightsEntityIDs; }

    static CloudSystemR* CreateInstance()
    {
        auto sys = new CloudSystemR();
        DispatchRenderingCommandWithToken([sys] { sys->InitCloudSystemR(); });

        return sys;
    }

    virtual void Release() override
    {
        delete this;
    }

    CEFunction(Reflect)
    virtual void OnBuildUpdateTasks(FrameParam* frameParam) override;

    void SetPropertyCloudMtl(ecs::EntityID entity, MaterialR* mtl);

    void SetPropertyCloudSettings(ecs::EntityID entity, const CloudSetting& val);

    void SetPropertyThunderStorm(ecs::EntityID entity, const ThunderstormToRender& val);

    void SetPropertyCloudData(const std::vector<CloudData>& val);

    void SetPropertyRefreshCloudHistory(bool val) { mRefreshCloudHistory = val; }

    bool GetPropertyRefreshCloudHistory() const { return mRefreshCloudHistory; }

    void SetPostProcessMtl(MaterialR* postMtl)
    {
        mPostMtl = postMtl;
    }

    bool ShouldRenderThunderstorm() const;
    bool EnableVolumetricCloud() const;

    void SetStormRenderData(const std::vector<ThunderstormToRender>& stormRenderData) { mStormData = stormRenderData; }

    const auto & GetGlobalSetting() const { return mGlobalSetting; }

    void CollectCloudRenderData();

    std::pair<bool, Float4> DebugGetOneStormPosition();

    void SetFogTop(const float fogTop) { mFogTop = fogTop; }

    void SetLightning(bool enableLightning, const Float3 pos, const Float3 tilePos)
    { 
        mLightningData.enable = enableLightning;
        mLightningData.lightningPos = pos;
        mLightningData.lightningTilePos = tilePos;
    }


protected:
    RENDER_ClOUD_RESOURCE(ComputeShader, DownSampleDepthComputeShader, "PipelineResource/FFSRP/Shader/Features/SkyCloud/DownSampleDepth.compute.nda");
    RENDER_ClOUD_RESOURCE(ComputeShader, TemporalReconstructComputeShader, "PipelineResource/FFSRP/Shader/Features/SkyCloud/TemporalReconstruction.compute.nda");
    RENDER_ClOUD_RESOURCE(ComputeShader, UnderCloudStatComputeShader, "PipelineResource/FFSRP/Shader/Features/SkyCloud/UnderCloudTest.compute.nda");

protected:

    std::set<ecs::EntityID> mChangedEntity;

    REDUniquePtr<cross::REDResidentTexture> mCloudShadowTex = nullptr;
    REDUniquePtr<cross::REDResidentTextureView> mCloudShadowTexView = nullptr;
    uint64_t mFrameNum;

    FrameStdVector<ecs::EntityID> mCloudLightsEntityIDs;

    MaterialR* mPostMtl;
    MaterialR* mCloudMtl;

    // for now, it is the first active entity's setting 
    CloudComponentR mGlobalSetting;

    FrameStdVector<CloudLayerSetting> mCloudData;

    std::vector<ThunderstormToRender> mStormData;

    // New CloudData to store all the params
    std::vector<CloudData> mCloudDataNew;

    bool mRefreshCloudHistory{false};

    bool mIsVolcanicAsh;
    float mFogTop{0.f};

    LightningData mLightningData;
    
    friend SkyCloud;
};

}   // namespace cross