#include "SceneModelOctree.h"
#include "RenderEngine/PrimitiveRenderSystemR.h"
namespace cross {
ModelCullingInfoCompact::ModelCullingInfoCompact() 
{
    entity = ecs::EntityID::InvalidHandle();
    mBounds = BoundingBox();
    mOctreeId = new TOctreeElementId2();
}

ModelCullingInfoCompact::ModelCullingInfoCompact(ecs::EntityID inEntity, BoundingBox inBounds, Float3 inTilePos, ModelCullingOctreeData* inModelCullingOctreeData) 
{
    entity = inEntity;
    mBounds = inBounds;
    mPosition = inBounds.GetCenter();
    mRadius = inBounds.GetExtent().Length();
    mOctreeId = new TOctreeElementId2();
    mTilePosition = inTilePos;
    mModelCullingOctreeData = inModelCullingOctreeData;
}

void ModelCullingOctreeSemantics::SetOctreeNodeIndex(const ModelCullingInfoCompact& Element, TOctreeElementId2 Id) 
{
    Element.mModelCullingOctreeData->SetEntityToNodeMap(Element.entity, Id);
}

//For the time being, only the octree is constructed for the model within 3 tiles around the airport tile position.
ModelCullingOctreeData::ModelCullingOctreeData()
    : mBoxMin(FLT_MAX, FLT_MAX, FLT_MAX)
    , mBoxMax(FLT_MIN, FLT_MIN, FLT_MIN)
    , mTileExtent(16.0f,16.0f,16.0f)
{}

ModelCullingOctreeData::ModelCullingOctreeData(const cross::Float3A& min, const cross::Float3A& max, const cross::Float3& tileExtent)
{
    mBoxMin = min;
    mBoxMax = max;
    mTileExtent = tileExtent;
}

ModelCullingOctreeData::~ModelCullingOctreeData()
{
    delete mOctreeForRendering;
}

bool ModelCullingOctreeData::SetModelOctreeTile(Float3 inTilePos) 
{
    //Set the root tile for building the octree.
    if ((mOctreeRootTile - inTilePos).Length() > 10)
    {
        mOctreeRootTile = inTilePos;
        rootTileFlag = true;
        rebuildFlag = true;
        return true;
    }
    return false;
}

bool ModelCullingOctreeData::IsInOctree(Float3 elementTile)
{
    // Only models that are near the octet root tile in the scene are processed.
    if (std::abs(elementTile.x - mOctreeRootTile.x) <= mTileExtent.x && std::abs(elementTile.y - mOctreeRootTile.y) <= mTileExtent.y && std::abs(elementTile.z - mOctreeRootTile.z) <= mTileExtent.z)
    {
        return true;
    }
    return false;
}

void ModelCullingOctreeData::UpdateOctNodePrimitive(SInt32 inNodeIndex, const BoundingBox& bbox) 
{
    Float3 extent, center;
    bbox.GetExtent(&extent);
    bbox.GetCenter(&center);
    PrimitiveGenerator::GenerateCubeFrame(&mOctreeHierarchyPrimitives[inNodeIndex], extent, center);
}

void ModelCullingOctreeData::DrawShowingOctree(RenderWorld* inRenderWolrd, FrameAllocator* curAllocator) 
{
    // Draw scene octree nodes.
    auto* primitiveSystem = inRenderWolrd->GetRenderSystem<PrimitiveRenderSystemR>();
    auto* deletedEntities = curAllocator->CreateFrameContainer<FrameVector<ecs::EntityID>>(FRAME_STAGE_RENDER, 2);
    for (auto& [entity, primitive] : mOctreePrimitives)
    {
        if (inRenderWolrd->IsEntityAlive(entity))
        {
            primitiveSystem->BatchPrimitive(&primitive, primitiveSystem->GetPresetMaterial(true, false, false), true);
        }
        else
        {
            deletedEntities->EmplaceBack(entity);
        }
    }
    for (UInt32 i = 0; i < deletedEntities->GetSize(); ++i)
    {
        mOctreePrimitives.erase(deletedEntities->At(i));
    }
    for (auto& [nodeIndex, primitive] : mOctreeHierarchyPrimitives)
    {
        primitiveSystem->BatchPrimitive(&primitive, primitiveSystem->GetPresetMaterial(true, false, false), true);
    }
}

void ModelCullingOctreeData::DrawOctreeHierarchy() 
{
    // Collect scene octree node bounding box.
    mOctreeForRendering->FindNodesWithPredicate([](ModelCullingOctree::TNodeIndex ParentNodeIndex, ModelCullingOctree ::TNodeIndex mNodeIndex, const TBoxCenterAndExtent& mNodeBounds) { return true; },
                                                [this](ModelCullingOctree::TNodeIndex ParentNodeIndex, ModelCullingOctree ::TNodeIndex mNodeIndex, const TBoxCenterAndExtent& mNodeBounds) {
                                                    Float4 iCenter = mNodeBounds.mCenter;
                                                    BoundingBox mTempBounds = BoundingBox(Float3(iCenter.x, iCenter.y, iCenter.z), Float3(mNodeBounds.mExtent.x, mNodeBounds.mExtent.y, mNodeBounds.mExtent.z));
                                                    mTempBounds.Transform(mTempBounds, (mOctreeRootTile)*LENGTH_PER_TILE_F);
                                                    UpdateOctNodePrimitive(mNodeIndex, mTempBounds);
                                                });
}

void ModelCullingOctreeData::CollectInvisibleSubModel(const CameraView& cullingCamara) 
{
    //SCOPED_CPU_TIMING(GroupRendering, "CollectInvisibleSubModel");
    //mEntityInvisibleSubModelMap.clear();
    //for (auto& [entity, submodelNodeIndex] : mEntitySubModelMap)
    //{
    //    
    //    for (auto& [objectGUID, objNodeIndex] : submodelNodeIndex)
    //    {
    //        if (!GetNodePreCullingVisibility(objNodeIndex))
    //        {
    //            mEntityInvisibleSubModelMap[entity].emplace(objectGUID);
    //        }
    //        else
    //        {
    //            BoundingBox mSubModelBoudingBox = mEntitySubModelBoundsMap[entity][objectGUID];
    //            if (HasEntity(entity))
    //            {
    //                mSubModelBoudingBox.Transform(mSubModelBoudingBox, (mEntityCompactMap[entity].mTilePosition - cullingCamara.mCameraTilePosition) * LENGTH_PER_TILE_F);
    //            }
    //            else if (mLargeScaleEntityCompactMap.find(entity) != mLargeScaleEntityCompactMap.end())
    //            {
    //                mSubModelBoudingBox.Transform(mSubModelBoudingBox, (mLargeScaleEntityCompactMap[entity].mTilePosition - cullingCamara.mCameraTilePosition) * LENGTH_PER_TILE_F);
    //            }
    //            else {
    //                continue;
    //            }

    //            BoundingFrustum frustum;
    //            cullingCamara.mFrustum.Transform(frustum, cullingCamara.mInvertViewMatrix);
    //            if (frustum.Contains(mSubModelBoudingBox) == ContainmentType::ContainmentDisjoint)
    //            {
    //                mEntityInvisibleSubModelMap[entity].emplace(objectGUID);
    //            }
    //        }
    //    }
    //}
}

void ModelCullingOctreeData::SetEntityToNodeMap(ecs::EntityID entity, TOctreeElementId2 id) 
{
    // Store the index mapping of entity to octree node.
    mEntityOctreeMap[entity] = id;
    *(mEntityCompactMap[entity].mOctreeId)= id;
}

bool ModelCullingOctreeData::UpdateOrAddModelCullingOctreeData(ModelCullingInfoCompact&& node) 
{
    // The cache is used to update the node information of the octree.
    if (node.mRadius > 5 * LENGTH_PER_TILE_F)
    {
        // Large scale individual models have not yet been dealt with.
        mLargeScaleEntityCompactMap[node.entity] = node;
        mPreLoadEntityCompactMap.erase(node.entity);
        return true;
    }
    else if (node.mPosition.x > 65536.0f || node.mPosition.y > 65536.0f || node.mPosition.z > 65536.0f || node.mPosition.x < -65536.0f || node.mPosition.y < -65536.0f || node.mPosition.z < -65536.0f)
    {
        // Cache for non-standard double transform position
        Float3 tile, offset;
        GetTileAndOffsetForAbsolutePosition(Double3(node.mPosition.x, node.mPosition.y, node.mPosition.z), tile, offset);
        node.mPosition = offset;
        node.mTilePosition += tile;
        mEntityCompactTileTransformMap[node.entity] = node;
        mPreLoadEntityCompactMap.erase(node.entity);
        return true; 
    }
    else
    {
        // Cache model information when the airport is not yet loaded.
        mPreLoadEntityCompactMap[node.entity] = node;
    }
    if (!rootTileFlag)
    {
        // Octree is not constructed when the root tile position is not available.
        return false;
    }else if(!IsInOctree(node.mTilePosition))
    {
        // Only models that are near the octet root tile in the scene are processed.
        RemoveModelCullingOctreeData(node.entity);
        return true;
    }
    if (mEntityCompactMap.find(node.entity) == mEntityCompactMap.end())
    {
        // Cache octree update node list.
        mUpdateEntityCompactMap[node.entity] = node;
        return true;
    }
    else if (node.mRadius - mEntityCompactMap[node.entity].mRadius > 100.0f || (node.mPosition - mEntityCompactMap[node.entity].mPosition).Length() > 1000.0f )
    {
        // Models that have changed positions are also updated.
        mUpdateEntityCompactMap[node.entity] = node;
        return true;
    }
    return true;
}



bool ModelCullingOctreeData::RemoveModelCullingOctreeData(ecs::EntityID removeEntityID) 
{
    if (mEntityOctreeMap.find(removeEntityID) != mEntityOctreeMap.end() && mEntityCompactMap.find(removeEntityID) != mEntityCompactMap.end())
    {
        mOctreeForRendering->RemoveElement(mEntityOctreeMap[removeEntityID]);
        mEntityOctreeMap.erase(removeEntityID);
        mEntityCompactMap.erase(removeEntityID);
        mEntitySubModelMap.erase(removeEntityID);
        mEntitySubModelBoundsMap.erase(removeEntityID);
    }
    return true;
}

bool ModelCullingOctreeData::BuildEntitySubModelMap() 
{
  //  SCOPED_CPU_TIMING(GroupRendering, "BuildEntitySubModelMap");
    if (!HasTreeBuild())
    {
        return false;
    }

      //  SCOPED_CPU_TIMING(GroupRendering, "BuildEntitySubModelMapTask");
        if (mEntitySubModelBoundsMap.size() > mEntitySubModelMap.size() && entitySubModelCapacity != mEntitySubModelBoundsMap.size() || preEntityCapacity != mEntityCompactMap.size())
    {
        entitySubModelCapacity = mEntitySubModelBoundsMap.size();
        preEntityCapacity = mEntityCompactMap.size();
        for (auto& [EntityID, boundsmap] : mEntitySubModelBoundsMap)
        {
            if (HasEntity(EntityID))
            {
                mEntitySubModelMap.erase(EntityID);
                for (auto& [mObjectCullingGUIDStart, bounds] : boundsmap)
                {
                    auto* mEntitySubModelNodeIndex = &mEntitySubModelMap;
                    BoundingBox subModelBounds(bounds.GetCenter() + (mEntityCompactMap[EntityID].mTilePosition - mOctreeRootTile) * LENGTH_PER_TILE_F, bounds.GetExtent());
                    mOctreeForRendering->FindNodesWithPredicate(
                        [subModelBounds](ModelCullingOctree::TNodeIndex ParentNodeIndex, ModelCullingOctree ::TNodeIndex mNodeIndex, const TBoxCenterAndExtent& mNodeBounds) {
                            if (BoundingBox(mNodeBounds.mCenter.XYZ(), mNodeBounds.mExtent.XYZ()).Contains(subModelBounds) == ContainmentContain)
                            {
                                return true;
                            }
                            return false;
                        },
                        [EntityID, mObjectCullingGUIDStart, mEntitySubModelNodeIndex](ModelCullingOctree::TNodeIndex ParentNodeIndex, ModelCullingOctree ::TNodeIndex mNodeIndex, const TBoxCenterAndExtent& mNodeBounds) {
                            (*mEntitySubModelNodeIndex)[EntityID][mObjectCullingGUIDStart] = mNodeIndex;
                        });
                }
            }
            else if (mLargeScaleEntityCompactMap.find(EntityID) != mLargeScaleEntityCompactMap.end())
            {
                mEntitySubModelMap.erase(EntityID);
                for (auto& [mObjectCullingGUIDStart, bounds] : boundsmap)
                {
                    auto* mEntitySubModelNodeIndex = &mEntitySubModelMap;
                    BoundingBox subModelBounds(bounds.GetCenter() + (mLargeScaleEntityCompactMap[EntityID].mTilePosition - mOctreeRootTile) * LENGTH_PER_TILE_F, bounds.GetExtent());
                    mOctreeForRendering->FindNodesWithPredicate(
                        [subModelBounds](ModelCullingOctree::TNodeIndex ParentNodeIndex, ModelCullingOctree ::TNodeIndex mNodeIndex, const TBoxCenterAndExtent& mNodeBounds) {
                            if (BoundingBox(mNodeBounds.mCenter.XYZ(), mNodeBounds.mExtent.XYZ()).Contains(subModelBounds) == ContainmentContain)
                            {
                                return true;
                            }
                            return false;
                        },
                        [EntityID, mObjectCullingGUIDStart, mEntitySubModelNodeIndex](ModelCullingOctree::TNodeIndex ParentNodeIndex, ModelCullingOctree ::TNodeIndex mNodeIndex, const TBoxCenterAndExtent& mNodeBounds) {
                            (*mEntitySubModelNodeIndex)[EntityID][mObjectCullingGUIDStart] = mNodeIndex;
                        });
                }
            }
        }
    }

    return true;
}

void ModelCullingOctreeData::AddEntitySubModelMap(ecs::EntityID EntityID, SInt32 mObjectCullingGUIDStart, BoundingBox bounds) 
{
    mEntitySubModelBoundsMap[EntityID][mObjectCullingGUIDStart] = bounds;
}

void ModelCullingOctreeData::CleanModelCullingOctreeData(ecs::EntityID removeEntityID) 
{
    // Remove the invalid entity from the octree completely.
    RemoveModelCullingOctreeData(removeEntityID);
    mEntitySubModelMap.erase(removeEntityID);
    mEntitySubModelBoundsMap.erase(removeEntityID);
    if (mPreLoadEntityCompactMap.find(removeEntityID) != mPreLoadEntityCompactMap.end())
    {
        mPreLoadEntityCompactMap.erase(removeEntityID);
    }
    if (mLargeScaleEntityCompactMap.find(removeEntityID) != mLargeScaleEntityCompactMap.end())
    {
        mLargeScaleEntityCompactMap.erase(removeEntityID);
    }
}

bool ModelCullingOctreeData::UpdateOrBuildOctree() 
{
  //  SCOPED_CPU_TIMING(GroupRendering, "UpdateOrBuildOctree");
    CleanOctree();
    if (mOctreeForRendering == nullptr)
    {
        // Initialize the octree.
        float extentMax = std::max<float>(std::max<float>(mTileExtent.x, mTileExtent.y), mTileExtent.z);
        mOctreeForRendering = new ModelCullingOctree(Float4A(0, 0, 0, 0), (2 * extentMax + 1.0f) * LENGTH_PER_TILE_F);
    }
    {
        if (!mUpdateEntityCompactMap.empty())
        {
            // Add an element from the update list to the octree or update the octree node information.
            for (auto& [entity, element] : mUpdateEntityCompactMap)
            {
                element.mPosition += (element.mTilePosition - mOctreeRootTile) * LENGTH_PER_TILE_F;
                RemoveModelCullingOctreeData(entity);
                mOctreeForRendering->AddElement(element);
                mEntityCompactMap[entity] = element;
                mPreLoadEntityCompactMap.erase(entity);
            }
            mUpdateEntityCompactMap.clear();
        }
        if (!mPreLoadEntityCompactMap.empty() && rootTileFlag)
        {
            // Adds elements in the preloaded cache that are within the extent of the octree 
            for (auto& [entity, element] : mPreLoadEntityCompactMap)
            {
                if (IsInOctree(element.mTilePosition))
                {
                    RemoveModelCullingOctreeData(entity);
                    element.mPosition += (element.mTilePosition - mOctreeRootTile) * LENGTH_PER_TILE_F;
                    mOctreeForRendering->AddElement(element);
                    mEntityCompactMap[entity] = element;
                    mPreLoadEntityCompactMap.erase(entity);
                }
            }
        }
        if (!mEntityCompactTileTransformMap.empty() && rootTileFlag)
        {
            // Add elements within the octree after the normalized tile position.
            for (auto& [entity, element] : mEntityCompactTileTransformMap)
            {
                RemoveModelCullingOctreeData(entity);
                if (IsInOctree(element.mTilePosition))
                {
                    element.mPosition += (element.mTilePosition - mOctreeRootTile) * LENGTH_PER_TILE_F;
                    mOctreeForRendering->AddElement(element);
                    mEntityCompactMap[entity] = element;
                }
                else
                {
                    mPreLoadEntityCompactMap[entity] = element;
                }
                mEntityCompactTileTransformMap.erase(entity);
            }
        }
        if (!mLargeScaleEntityCompactMap.empty() && rootTileFlag)
        {
            // Remove large-scale models
            for (auto& [entity, element] : mLargeScaleEntityCompactMap)
            {
                if (mEntityOctreeMap.find(entity) != mEntityOctreeMap.end())
                {
                    RemoveModelCullingOctreeData(entity);
                }
            }
        }
    }
    return true;
}

bool ModelCullingOctreeData::CleanOctree() 
{
    // Clean up all elements of the octree when the root tile changes.
    if (rebuildFlag && mOctreeForRendering!=nullptr)
    {
        for (const auto& [entity, element] : mEntityOctreeMap)
        {
            RemoveModelCullingOctreeData(entity);
        }
        mEntityOctreeMap.clear();
        mEntityCompactMap.clear();
        mUpdateEntityCompactMap.clear();
        mOctreeForRendering = nullptr;
        rebuildFlag = false;
        return true;
    }
    return false;
}



void ModelCullingOctreeData::OctreeCameraCulling(const CameraView& cullingCamara) 
{
    OutVisibleNodes.resize(mOctreeForRendering->GetNumNodes() * 2,false);
    mOctreeForRendering->FindNodesWithPredicate(
        [=,&cullingCamara](ModelCullingOctree::TNodeIndex ParentNodeIndex, ModelCullingOctree ::TNodeIndex mNodeIndex, const TBoxCenterAndExtent& mNodeBounds) {
            // If the parent node is completely contained there is no need to test containment.
            if (ParentNodeIndex != INDEX_NONE && !OutVisibleNodes[(ParentNodeIndex * 2) + 1])
            {
                OutVisibleNodes[mNodeIndex * 2] = true;
                OutVisibleNodes[mNodeIndex * 2 + 1] = false;
                return true;
            }

            BoundingBox mNodeBoudingBox = BoundingBox(Float3(mNodeBounds.mCenter.x, mNodeBounds.mCenter.y, mNodeBounds.mCenter.z), Float3(mNodeBounds.mExtent.x, mNodeBounds.mExtent.y, mNodeBounds.mExtent.z));
            bool bIntersects = false;
            BoundingFrustum frustum;
            cullingCamara.mFrustum.Transform(frustum, cullingCamara.mInvertViewMatrix);

            mNodeBoudingBox.Transform(mNodeBoudingBox, (mOctreeRootTile - cullingCamara.mCameraTilePosition) * LENGTH_PER_TILE_F);

            {
                ContainmentType containState = frustum.Contains(mNodeBoudingBox);
                bIntersects = containState;
            }

            if (bIntersects)
            {
                OutVisibleNodes[mNodeIndex * 2] = true;
                OutVisibleNodes[mNodeIndex * 2 + 1] = frustum.Contains(mNodeBoudingBox) != ContainmentType::ContainmentContain || mNodeBoudingBox.Contains(frustum) == ContainmentType::ContainmentContain;
            }

            return bIntersects;
        },
        [](ModelCullingOctree::TNodeIndex ParentNodeIndex, ModelCullingOctree ::TNodeIndex mNodeIndex, const TBoxCenterAndExtent& mNodeBounds) {});
    //CollectInvisibleSubModel(cullingCamara);
}

bool ModelCullingOctreeData::GetEntityPreCullingInside(ecs::EntityID entityId) const
{
    return OutVisibleNodes[GetEntityNodeIndex(entityId) * 2];
}

bool ModelCullingOctreeData::GetEntityPreCullingOutside(ecs::EntityID entityId) const
{
    // 
    return OutVisibleNodes[GetEntityNodeIndex(entityId) * 2 + 1];
}

bool ModelCullingOctreeData::GetNodePreCullingVisibility(SInt32 nodeIndex) const 
{
    return OutVisibleNodes[nodeIndex * 2];
}

}