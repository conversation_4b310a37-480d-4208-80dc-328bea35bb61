#include "EnginePrefix.h"
#include "RenderEngine/PrimitiveGenerator.h"

#define PD_TILE_POSITION_CHANNEL VertexChannel::Position3
#define PD_TILE_POSITION_FORMAT  VertexFormat::Float3
namespace cross
{
PrimitiveData::PrimitiveData(UInt8* vertexData, UInt16* indexArray, UInt32 vertexCount, UInt32 indexCount, UInt32 primitiveCount, const VertexStreamLayout& layout, const PrimitiveTopology& topology, const ColorRGBAf& color)
    : mVertexData(vertexData), mIndexArray(indexArray), mVertexCount(vertexCount), mIndexCount(indexCount), mPrimitiveCount(primitiveCount), mLayout(layout), mPrimitiveTopology(topology), mIsIndex32(false), mNeedFree(false), mColor{color}
{}

PrimitiveData::PrimitiveData(UInt8* vertexData, UInt32* indexArray, UInt32 vertexCount, UInt32 indexCount, UInt32 primitiveCount, const VertexStreamLayout& layout, const PrimitiveTopology& topology, const ColorRGBAf& color)
    : mVertexData(vertexData), mIndexArray(reinterpret_cast<UInt16*>(indexArray)), mVertexCount(vertexCount), mIndexCount(indexCount), 
    mPrimitiveCount(primitiveCount), mLayout(layout), mPrimitiveTopology(topology), mIsIndex32(true), mNeedFree(false), mColor{ color }
{}

PrimitiveData::~PrimitiveData()
{
    if (mNeedFree)
    {
        cross::Memory::Free(mVertexData);
        cross::Memory::Free(mIndexArray);
    }
}

void PrimitiveData::SetIndexCount(UInt32 indexCount)
{
    mIndexCount = indexCount;
}

void PrimitiveData::Resize(UInt32 vertexCount, UInt32 indexCount, UInt32 primitiveCount, const VertexStreamLayout& layout, bool isIndex32)
{
    mPrimitiveCount = primitiveCount;
    if (vertexCount <= mVertexCount && indexCount <= mIndexArrayCapacity && layout.GetHash() == mLayout.GetHash())
    {
        return;
    }
    mVertexCount = vertexCount;
    mIndexCount = indexCount;
    mIsIndex32 = isIndex32;
    
    Assert(layout.GetFrequency() == VertexFrequency::PerVertex); 
    UInt32 vertexSizeInByte = layout.GetVertexStride() * vertexCount;
    if (vertexSizeInByte > mVertextDataCapacityInByte)
    {
        if(mNeedFree)cross::Memory::Free(mVertexData);
        mVertexData = static_cast<UInt8*>(cross::Memory::Malloc(vertexSizeInByte));
        memset(mVertexData, 0, vertexSizeInByte * sizeof(char));
        mVertextDataCapacityInByte = vertexSizeInByte;
    }
    mLayout = layout;

    if (indexCount > mIndexArrayCapacity)
    {
        if(mNeedFree)cross::Memory::Free(mIndexArray);
        UInt32 indexSizeInByte = indexCount * (isIndex32 ? sizeof(UInt32) : sizeof(UInt16));
        mIndexArray = static_cast<UInt16*>(cross::Memory::Malloc(indexSizeInByte));
        memset(mIndexArray, 0, indexSizeInByte * sizeof(char));
        mIndexArrayCapacity = indexCount;
    }

    mNeedFree = true;
}

void PrimitiveData::SetTilePosition(const Float3& inTilePosition) 
{
#ifdef CE_USE_DOUBLE_TRANSFORM
    // Check data ready
    Assert(HasData());

    // Check channel ready
    const auto& layout = GetVertexLayout();
    Assert(layout.HasChannel(GetVertexChannelMaskBit(PD_TILE_POSITION_CHANNEL)));

    auto TileChannelIndex = layout.GetChannelCount() - 1;
    UInt32 TileChannelOffsetInBytes = layout.GetChannelLayout(TileChannelIndex).mOffset;
    UInt32 VertexStride = layout.GetVertexStride();

    // enumerate all vertices and set the tile position channel as inTilePosition
    for (UInt32 iVertex = 0; iVertex < mVertexCount; iVertex++)
    {
        UInt8* VertexDataPtr = mVertexData + iVertex * VertexStride;
        Float3* TileDataPtr = reinterpret_cast<Float3*>(VertexDataPtr + TileChannelOffsetInBytes);
        *TileDataPtr = inTilePosition;
    }
 #endif
}

VertexStreamLayout PrimitiveGenerator::GetStandardTriangleLayout()
{
    static VertexStreamLayout layout;
    if (layout.GetVertexStride() == 0)
    {
        layout.AddVertexChannelLayout(VertexChannel::Position0, VertexFormat::Float3, 0);
        layout.AddVertexChannelLayout(VertexChannel::Normal0, VertexFormat::Float3, 12);
        layout.AddVertexChannelLayout(VertexChannel::TexCoord0, VertexFormat::Float2, 24);
        layout.AddVertexChannelLayout(VertexChannel::Color0, VertexFormat::UByte4_Norm, 32);

        /*
         *   Tile Position channel
         *   Keep the tile channel at last 
         */
#ifdef CE_USE_DOUBLE_TRANSFORM
        // last offset in byte is 32
        layout.AddVertexChannelLayout(PD_TILE_POSITION_CHANNEL, PD_TILE_POSITION_FORMAT, static_cast<UInt8>(layout.GetVertexStride()));
#endif
    }
    return layout;
}

VertexStreamLayout PrimitiveGenerator::GetStandardLineLayout()
{
    static VertexStreamLayout layout;
    if (layout.GetVertexStride() == 0)
    {
        layout.AddVertexChannelLayout(VertexChannel::Position0, VertexFormat::Float3, 0);
        layout.AddVertexChannelLayout(VertexChannel::Color0, VertexFormat::UByte4_Norm, 12);

        /*
         *   Tile Position channel
         *   Keep the tile channel at last
         */
#ifdef CE_USE_DOUBLE_TRANSFORM
        // last offset in byte is 12
        layout.AddVertexChannelLayout(PD_TILE_POSITION_CHANNEL, PD_TILE_POSITION_FORMAT, static_cast<UInt8>(layout.GetVertexStride()));
#endif
    }
    return layout;
}

VertexStreamLayout PrimitiveGenerator::GetHDRLineLayout()
{
    static VertexStreamLayout layout;
    if (layout.GetVertexStride() == 0)
    {
        layout.AddVertexChannelLayout(VertexChannel::Position0, VertexFormat::Float3, 0);
        layout.AddVertexChannelLayout(VertexChannel::Color0, VertexFormat::Float3, 12);

        /*
         *   Tile Position channel
         *   Keep the tile channel at last
         */
#ifdef CE_USE_DOUBLE_TRANSFORM
        // last offset in byte is 12
        layout.AddVertexChannelLayout(PD_TILE_POSITION_CHANNEL, PD_TILE_POSITION_FORMAT, static_cast<UInt8>(layout.GetVertexStride()));
#endif
    }
    return layout;
}

/*
reference: http://www.songho.ca/opengl/gl_sphere.html
*/
void PrimitiveGenerator::GenerateSphere(PrimitiveData* outData, float radius, UInt8 sliceNum, UInt8 stackNum, Float3 origin)
{
    // Set data
    UInt16 vertexCount = (stackNum + 1) * (sliceNum + 1);
    UInt16 indexCount = 3 * stackNum * (sliceNum * 2 - 2);
    VertexStreamLayout&& layout = GetStandardTriangleLayout();
    outData->Resize(vertexCount, indexCount, indexCount / 3, layout, false);
    outData->SetTopology(PrimitiveTopology::TriangleList);

    // Set vertex
    UInt32 offset = 0; UInt32 stride = layout.GetVertexStride();
    auto AddVertex = [vertexData = outData->GetVertexData(), &offset, stride, vertexCount](Float3* position, Float3* normal, Float2* uv)
    {
        Assert(stride * static_cast<UInt32>(vertexCount));
        memcpy(static_cast<void*>(vertexData + offset), position, 12);
        memcpy(static_cast<void*>(vertexData + offset + 12), normal, 12);
        memcpy(static_cast<void*>(vertexData + offset + 24), uv, 8);
        memcpy(static_cast<void*>(vertexData + offset + 32), &sColorWhite, 4);
        offset += stride;
    };
    Float3 position; float xy;                              
    Float3 normal; float lengthInv = 1.0f / radius;
    Float2 uv;
    float sliceStep = 2 * M_PI / sliceNum;
    float stackStep = M_PI / stackNum;
    float sliceAngle, stackAngle;
    for (int i = 0; i <= stackNum; ++i)
    {
        stackAngle = M_PI / 2 - i * stackStep;
        xy = radius * Cos(stackAngle);          
        position.z = radius * Sin(stackAngle) + origin.z;
        for (int j = 0; j <= sliceNum; ++j)
        {
            sliceAngle = j * sliceStep;
            position.x = xy * Cos(sliceAngle);
            position.y = xy * Sin(sliceAngle);

            normal.x = position.x * lengthInv;
            normal.y = position.y * lengthInv;
            normal.z = position.z * lengthInv;

            uv.x = static_cast<float>(j) / sliceNum;
            uv.y = static_cast<float>(i) / stackNum;

            position.x = position.x + origin.x;
            position.y = position.y + origin.y;
            AddVertex(&position, &normal, &uv);
        }
    }

    // Set index
    UInt16 arrayIndex = 0;
    auto AddTriangle = [indexArray = outData->GetIndexArray(), &arrayIndex, indexCount](UInt16 a, UInt16 b, UInt16 c)
    {
        Assert(arrayIndex < indexCount);
        indexArray[arrayIndex] = a;
        indexArray[arrayIndex + 1] = b;
        indexArray[arrayIndex + 2] = c;
        arrayIndex += 3;
    };
    int k1, k2;
    for (UInt8 i = 0; i < stackNum; ++i)
    {
        k1 = i * (stackNum + 1);
        k2 = k1 + sliceNum + 1; 
        for (UInt8 j = 0; j < sliceNum; ++j, ++k1, ++k2)
        {
            if (i != 0)
                AddTriangle(
                    static_cast<UInt16>(k1), 
                    static_cast<UInt16>(k2), 
                    static_cast<UInt16>(k1 + 1));

            if (i != (stackNum - 1))
                AddTriangle(
                    static_cast<UInt16>(k1 + 1), 
                    static_cast<UInt16>(k2), 
                    static_cast<UInt16>(k2 + 1));
        }
    }

    return;
}

void PrimitiveGenerator::GenerateCube(PrimitiveData* outData, float sideLength)
{
    VertexStreamLayout&& layout = GetStandardTriangleLayout();
    outData->Resize(36, 36, 12, layout, false);
    outData->SetTopology(PrimitiveTopology::TriangleList);

    float halfLength = sideLength * 0.5f;
    Float3 a0(-halfLength, -halfLength, -halfLength);
    Float3 a1(-halfLength, halfLength, -halfLength);
    Float3 a2(halfLength, halfLength, -halfLength);
    Float3 a3(halfLength, -halfLength, -halfLength);
    Float3 a4(-halfLength, -halfLength, halfLength);
    Float3 a5(-halfLength, halfLength, halfLength);
    Float3 a6(halfLength, halfLength, halfLength);
    Float3 a7(halfLength, -halfLength, halfLength);
    Float3 positions[36] =
    {
        a1, a2, a3, a3, a0, a1,
        a2, a6, a7, a7, a3, a2,
        a6, a5, a4, a4, a7, a6,
        a5, a1, a0, a0, a4, a5,
        a0, a3, a7, a7, a4, a0,
        a5, a6, a2, a2, a1, a5
    };
    Float3 normals[36];
    for (UInt8 i = 0; i < 36; i += 3)
    {
        normals[i] = (positions[i + 1] - positions[i])
            .Cross(positions[i + 2] - positions[i]);
        normals[i].Normalize();
        normals[i + 1] = normals[i];
        normals[i + 2] = normals[i];
    }
    Float2 uvs[36] = 
    {
        Float2(0, 1), Float2(0, 0), Float2(1, 0), Float2(1, 0), Float2(1, 1), Float2(0, 1),
        Float2(0, 1), Float2(0, 0), Float2(1, 0), Float2(1, 0), Float2(1, 1), Float2(0, 1),
        Float2(1, 0), Float2(1, 1), Float2(0, 1), Float2(0, 1), Float2(0, 0), Float2(1, 0),
        Float2(0, 1), Float2(0, 0), Float2(1, 0), Float2(1, 0), Float2(1, 1), Float2(0, 1),
        Float2(0, 0), Float2(1, 0), Float2(1, 1), Float2(1, 1), Float2(0, 1), Float2(0, 0),
        Float2(1, 1), Float2(0, 1), Float2(0, 0), Float2(0, 0), Float2(1, 0), Float2(1, 1),
    };
    auto vertexData = outData->GetVertexData();
    UInt32 stride = layout.GetVertexStride();
    UInt32 offset = 0;
    for (UInt8 i = 0; i < 36; i++)
    {
        memcpy(static_cast<void*>(vertexData + offset), &positions[i], sizeof(Float3));
        memcpy(static_cast<void*>(vertexData + offset + 12), &normals[i], sizeof(Float3));
        memcpy(static_cast<void*>(vertexData + offset + 24), &uvs[i], sizeof(Float2));
        memcpy(static_cast<void*>(vertexData + offset + 32), &sColorWhite, sizeof(UInt32));
        offset += stride;
    }
    
    auto indexArray = outData->GetIndexArray();
    for (UInt8 i = 0; i < 36; i += 3)
    {
        indexArray[i] = i;
        indexArray[i + 1] = i + 1;
        indexArray[i + 2] = i + 2;
    }

    return;
}

void PrimitiveGenerator::GeneratePoint(PrimitiveData* outData, const Float3& pos)
{
    VertexStreamLayout&& layout = GetStandardLineLayout();
    outData->Resize(1, 1, 1, layout, false);
    outData->SetTopology(PrimitiveTopology::Point);

    memcpy(static_cast<void*>(outData->GetVertexData()), pos.data(), sizeof(Float3));
    memcpy(static_cast<void*>(outData->GetVertexData() + 12), &sColorWhite, sizeof(UInt32));

    outData->GetIndexArray()[0] = 0;

    return;
}

/*
     5------6
    /|     /|
   1-4----2-7  // 4567 are on the back.
   |/     |/
   0------3
*/
void PrimitiveGenerator::GenerateCube(PrimitiveData* outData, const Float3& extent, const Float3& origin)
{
    VertexStreamLayout&& layout = GetStandardTriangleLayout();
    outData->Resize(36, 36, 12, layout, false);
    outData->SetTopology(PrimitiveTopology::TriangleList);

    Float3 halfLength = extent;
    Float3 a0(-halfLength.x, -halfLength.y, -halfLength.z);
    Float3 a1(-halfLength.x, halfLength.y, -halfLength.z);
    Float3 a2(halfLength.x, halfLength.y, -halfLength.z);
    Float3 a3(halfLength.x, -halfLength.y, -halfLength.z);
    Float3 a4(-halfLength.x, -halfLength.y, halfLength.z);
    Float3 a5(-halfLength.x, halfLength.y, halfLength.z);
    Float3 a6(halfLength.x, halfLength.y, halfLength.z);
    Float3 a7(halfLength.x, -halfLength.y, halfLength.z);
    a0 = a0 + origin;
    a1 = a1 + origin;
    a2 = a2 + origin;
    a3 = a3 + origin;
    a4 = a4 + origin;
    a5 = a5 + origin;
    a6 = a6 + origin;
    a7 = a7 + origin;
    Float3 positions[36] =
    {
        a1, a2, a3, a3, a0, a1,
        a2, a6, a7, a7, a3, a2,
        a6, a5, a4, a4, a7, a6,
        a5, a1, a0, a0, a4, a5,
        a0, a3, a7, a7, a4, a0,
        a5, a6, a2, a2, a1, a5
    };
    Float3 normals[36];
    for (UInt8 i = 0; i < 36; i += 3)
    {
        normals[i] = (positions[i + 1] - positions[i])
            .Cross(positions[i + 2] - positions[i]);
        normals[i].Normalize();
        normals[i + 1] = normals[i];
        normals[i + 2] = normals[i];
    }
    Float2 uvs[36] =
    {
        Float2(0, 1), Float2(0, 0), Float2(1, 0), Float2(1, 0), Float2(1, 1), Float2(0, 1),
        Float2(0, 1), Float2(0, 0), Float2(1, 0), Float2(1, 0), Float2(1, 1), Float2(0, 1),
        Float2(1, 0), Float2(1, 1), Float2(0, 1), Float2(0, 1), Float2(0, 0), Float2(1, 0),
        Float2(0, 1), Float2(0, 0), Float2(1, 0), Float2(1, 0), Float2(1, 1), Float2(0, 1),
        Float2(0, 0), Float2(1, 0), Float2(1, 1), Float2(1, 1), Float2(0, 1), Float2(0, 0),
        Float2(1, 1), Float2(0, 1), Float2(0, 0), Float2(0, 0), Float2(1, 0), Float2(1, 1),
    };
    auto vertexData = outData->GetVertexData();
    UInt32 stride = layout.GetVertexStride();
    UInt32 offset = 0;
    for (UInt8 i = 0; i < 36; i++)
    {
        memcpy(static_cast<void*>(vertexData + offset), &positions[i], sizeof(Float3));
        memcpy(static_cast<void*>(vertexData + offset + 12), &normals[i], sizeof(Float3));
        memcpy(static_cast<void*>(vertexData + offset + 24), &uvs[i], sizeof(Float2));
        memcpy(static_cast<void*>(vertexData + offset + 32), &sColorWhite, sizeof(UInt32));
        offset += stride;
    }

    auto indexArray = outData->GetIndexArray();
    for (UInt8 i = 0; i < 36; i += 3)
    {
        indexArray[i] = i;
        indexArray[i + 1] = i + 1;
        indexArray[i + 2] = i + 2;
    }

    return;
}

void PrimitiveGenerator::GenerateCube(PrimitiveData* outData, const Float3& extent, const Float3& origin, UInt32 color)
{
    VertexStreamLayout&& layout = GetStandardTriangleLayout();
    outData->Resize(36, 36, 12, layout, false);
    outData->SetTopology(PrimitiveTopology::TriangleList);

    Float3 halfLength = extent;
    Float3 a0(-halfLength.x, -halfLength.y, -halfLength.z);
    Float3 a1(-halfLength.x, halfLength.y, -halfLength.z);
    Float3 a2(halfLength.x, halfLength.y, -halfLength.z);
    Float3 a3(halfLength.x, -halfLength.y, -halfLength.z);
    Float3 a4(-halfLength.x, -halfLength.y, halfLength.z);
    Float3 a5(-halfLength.x, halfLength.y, halfLength.z);
    Float3 a6(halfLength.x, halfLength.y, halfLength.z);
    Float3 a7(halfLength.x, -halfLength.y, halfLength.z);
    a0 = a0 + origin;
    a1 = a1 + origin;
    a2 = a2 + origin;
    a3 = a3 + origin;
    a4 = a4 + origin;
    a5 = a5 + origin;
    a6 = a6 + origin;
    a7 = a7 + origin;
    Float3 positions[36] = {a1, a2, a3, a3, a0, a1, a2, a6, a7, a7, a3, a2, a6, a5, a4, a4, a7, a6, a5, a1, a0, a0, a4, a5, a0, a3, a7, a7, a4, a0, a5, a6, a2, a2, a1, a5};
    Float3 normals[36];
    for (UInt8 i = 0; i < 36; i += 3)
    {
        normals[i] = (positions[i + 1] - positions[i]).Cross(positions[i + 2] - positions[i]);
        normals[i].Normalize();
        normals[i + 1] = normals[i];
        normals[i + 2] = normals[i];
    }
    Float2 uvs[36] = {
        Float2(0, 1), Float2(0, 0), Float2(1, 0), Float2(1, 0), Float2(1, 1), Float2(0, 1), Float2(0, 1), Float2(0, 0), Float2(1, 0), Float2(1, 0), Float2(1, 1), Float2(0, 1),
        Float2(1, 0), Float2(1, 1), Float2(0, 1), Float2(0, 1), Float2(0, 0), Float2(1, 0), Float2(0, 1), Float2(0, 0), Float2(1, 0), Float2(1, 0), Float2(1, 1), Float2(0, 1),
        Float2(0, 0), Float2(1, 0), Float2(1, 1), Float2(1, 1), Float2(0, 1), Float2(0, 0), Float2(1, 1), Float2(0, 1), Float2(0, 0), Float2(0, 0), Float2(1, 0), Float2(1, 1),
    };
    auto vertexData = outData->GetVertexData();
    UInt32 stride = layout.GetVertexStride();
    UInt32 offset = 0;
    for (UInt8 i = 0; i < 36; i++)
    {
        memcpy(static_cast<void*>(vertexData + offset), &positions[i], sizeof(Float3));
        memcpy(static_cast<void*>(vertexData + offset + 12), &normals[i], sizeof(Float3));
        memcpy(static_cast<void*>(vertexData + offset + 24), &uvs[i], sizeof(Float2));
        memcpy(static_cast<void*>(vertexData + offset + 32), &color, sizeof(UInt32));
        offset += stride;
    }

    auto indexArray = outData->GetIndexArray();
    for (UInt8 i = 0; i < 36; i += 3)
    {
        indexArray[i] = i;
        indexArray[i + 1] = i + 1;
        indexArray[i + 2] = i + 2;
    }

    return;
}

void PrimitiveGenerator::GeneratePlane(PrimitiveData* outData, float sideLength)
{
    VertexStreamLayout&& layout = GetStandardTriangleLayout();
    outData->Resize(4, 6, 2, layout, false);
    outData->SetTopology(PrimitiveTopology::TriangleList);

    UInt32 offset = 0; UInt32 stride = layout.GetVertexStride(); 
    Float3 normal(0.f, 1.f, 0.f);
    auto AddVertex = [vertexData = outData->GetVertexData(), &offset, stride, normal ](Float3 position, Float2 uv)
    {
        memcpy(static_cast<void*>(vertexData + offset), &position, 12);
        memcpy(static_cast<void*>(vertexData + offset + 12), &normal, 12);
        memcpy(static_cast<void*>(vertexData + offset + 24), &uv, 8);
        memcpy(static_cast<void*>(vertexData + offset + 32), &sColorWhite, 4);
        offset += stride;
    };
    float halfLength = sideLength * 0.5f;
    AddVertex(Float3(-halfLength, 0, -halfLength), Float2(0.f, 0.f));
    AddVertex(Float3(-halfLength, 0, halfLength), Float2(0.f, 1.f));
    AddVertex(Float3(halfLength, 0, halfLength), Float2(1.f, 1.f));
    AddVertex(Float3(halfLength, 0, -halfLength), Float2(1.f, 0.f));

    auto indexArray = outData->GetIndexArray();
    indexArray[0] = 2; indexArray[1] = 0; indexArray[2] = 1;
    indexArray[3] = 2; indexArray[4] = 3; indexArray[5] = 0;

    return;
}

/*
* reference: https://github.com/vorg/primitive-capsule/blob/master/index.js
*/
void PrimitiveGenerator::GenerateCapsule(PrimitiveData* outData, float radius, float height, UInt8 sliceNum, UInt8 stackNum)
{
    std::vector<Float3> positions;
    std::vector<Float3> normals;
    std::vector<Float2> uvs;

    UInt8 ringsBody = stackNum + 1;
    UInt8 ringsTotal = stackNum + ringsBody;
    float bodyIncr = 1.0f / (ringsBody - 1);
    float ringIncr = 1.0f / (stackNum - 1);
    
    auto CalculateRing = [&positions, &normals, &uvs, radius, height](UInt8 segments, float r, float y, float dy)
    {
        float segIncr = 1.0f / (segments - 1);
        for (UInt8 s = 0; s < segments; ++s)
        {
            float x = -Cos((M_PI * 2) * s * segIncr) * r;
            float z = Sin((M_PI * 2) * s * segIncr) * r;

            positions.emplace_back(radius * x, radius * y + height * dy, radius * z);
            normals.emplace_back(x, y, z);

            float u = (s * segIncr);
            float v = 0.5f - ((radius * y + height * dy) / (2.0f * radius + height));
            uvs.emplace_back(u, 1.0f - v);
        }
    };

    for (UInt8 r = 0; r < stackNum / 2; r++) {
        CalculateRing(sliceNum, Sin(M_PI * r * ringIncr), Sin(M_PI * (r * ringIncr - 0.5f)), -0.5f);
    }

    for (UInt8 r = 0; r < ringsBody; r++) {
        CalculateRing(sliceNum, 1.0f, 0.0f, r * bodyIncr - 0.5f);
    }

    for (UInt8 r = stackNum / 2; r < stackNum; r++) {
        CalculateRing(sliceNum, Sin(M_PI * r * ringIncr), Sin(M_PI * (r * ringIncr - 0.5f)), +0.5f);
    }

    VertexStreamLayout&& layout = GetStandardTriangleLayout();
    outData->Resize(static_cast<UInt32>(positions.size()), ringsTotal * sliceNum * 6, ringsTotal * sliceNum * 2, layout, false);
    outData->SetTopology(PrimitiveTopology::TriangleList);

    UInt32 offset = 0; 
    UInt32 stride = layout.GetVertexStride();
    auto AddVertex = [vertexData = outData->GetVertexData(), &offset, stride](const Float3& position, const Float3& normal, const Float2& uv)
    {
        memcpy(static_cast<void*>(vertexData + offset), &position, 12);
        memcpy(static_cast<void*>(vertexData + offset + 12), &normal, 12);
        memcpy(static_cast<void*>(vertexData + offset + 24), &uv, 8);
        memcpy(static_cast<void*>(vertexData + offset + 32), &sColorWhite, 4);
        offset += stride;
    };

    for (size_t i = 0; i < positions.size(); ++i)
    {
        AddVertex(positions[i], normals[i], uvs[i]);
    }

    UInt16* indexArray = outData->GetIndexArray();
    UInt16 indexIndex = 0;
    for (UInt8 r = 0; r < ringsTotal - 1; r++) {
        for (UInt8 s = 0; s < sliceNum - 1; s++) {
            indexArray[indexIndex+ 2] = r * sliceNum + (s + 1);
            indexArray[indexIndex + 1] = r * sliceNum + (s + 0);
            indexArray[indexIndex] = (r + 1) * sliceNum + (s + 1);
            indexIndex += 3;

            indexArray[indexIndex + 2] = (r + 1) * sliceNum + (s + 0);
            indexArray[indexIndex + 1] = (r + 1) * sliceNum + (s + 1);
            indexArray[indexIndex] = r * sliceNum + s;
            indexIndex += 3;
        }
    }

    return;
}

void PrimitiveGenerator::GenerateCylinder(PrimitiveData* outData, float radius, float height, UInt8 sliceNum, UInt8 stackNum)
{
    UInt16 vertexCount = (sliceNum + 1) * (stackNum + 3) + 2;
    UInt16 indexCount = 3 * (sliceNum * stackNum * 2 + 2 * sliceNum);
    VertexStreamLayout&& layout = GetStandardTriangleLayout();
    outData->Resize(vertexCount, indexCount, indexCount / 3, layout, false);
    outData->SetTopology(PrimitiveTopology::TriangleList);

    UInt32 offset = 0; UInt32 stride = layout.GetVertexStride();
    auto AddVertex = [vertexData = outData->GetVertexData(), &offset, stride, vertexCount](Float3* position, Float3* normal, Float2* uv)
    {
        Assert(stride* static_cast<UInt32>(vertexCount));
        memcpy(static_cast<void*>(vertexData + offset), position, 12);
        memcpy(static_cast<void*>(vertexData + offset + 12), normal, 12);
        memcpy(static_cast<void*>(vertexData + offset + 24), uv, 8);
        memcpy(static_cast<void*>(vertexData + offset + 32), &sColorWhite, 4);
        offset += stride;
    };
    Float3 position;
    Float3 normal;
    Float2 uv;
    float sliceStep = 2 * M_PI / sliceNum;
    float stackStep = height / stackNum;
    float sliceAngle, stackHeight;
    for (int i = 0; i <= stackNum; ++i)
    {
        stackHeight = -height / 2 + i * stackStep;
        position.z = stackHeight;

        for (int j = 0; j <= sliceNum; ++j)
        {
            sliceAngle = j * sliceStep;
            position.x = radius * Cos(sliceAngle);
            position.y = radius * Sin(sliceAngle);

            normal.x = Cos(sliceAngle);
            normal.y = Sin(sliceAngle);
            normal.z = 0.0f;

            uv.x = static_cast<float>(j) / sliceNum;
            uv.y = static_cast<float>(i) / stackNum;

            AddVertex(&position, &normal, &uv);
        }
    }

    for (int i = 0; i < 2; ++i)
    {
        stackHeight = -height / 2 + i * height;
        position.z = stackHeight;
        
        position.x = 0;
        position.y = 0;

        normal.x = 0;
        normal.y = 0;
        normal.z = -1.0f + i * 2.0f;

        uv.x = 0.5f;
        uv.y = 0.5f;

        AddVertex(&position, &normal, &uv);
        for (int j = 0; j <= sliceNum; ++j)
        {
            sliceAngle = j * sliceStep;
            position.x = radius * Cos(sliceAngle);
            position.y = radius * Sin(sliceAngle);

            uv.x = 0.5f - 0.5f * Cos(sliceAngle);
            uv.y = 0.5f - 0.5f * Sin(sliceAngle);

            AddVertex(&position, &normal, &uv);
        }
    }

    UInt16 arrayIndex = 0;
    auto AddTriangle = [indexArray = outData->GetIndexArray(), &arrayIndex, indexCount](UInt16 a, UInt16 b, UInt16 c)
    {
        Assert(arrayIndex < indexCount);
        indexArray[arrayIndex] = a;
        indexArray[arrayIndex + 1] = b;
        indexArray[arrayIndex + 2] = c;
        arrayIndex += 3;
    };
    int k1, k2;
    for (UInt8 i = 0; i < stackNum; ++i)
    {
        k1 = i * (sliceNum + 1);
        k2 = k1 + sliceNum + 1;
        for (UInt8 j = 0; j < sliceNum; ++j, ++k1, ++k2)
        {
            AddTriangle(
                static_cast<UInt16>(k1),
                static_cast<UInt16>(k1 + 1),
                static_cast<UInt16>(k2));

            AddTriangle(
                static_cast<UInt16>(k1 + 1),
                static_cast<UInt16>(k2 + 1),
                static_cast<UInt16>(k2));
        }
    }

    k1 = (sliceNum + 1) * (stackNum + 1);
    for (UInt8 i = 0; i < sliceNum; ++i)
    {
        k2 = k1 + i + 1;
        AddTriangle(
            static_cast<UInt16>(k1),
            static_cast<UInt16>(k2 + 1),
            static_cast<UInt16>(k2));
    }
    k1 += sliceNum + 2;
    for (UInt8 i = 0; i < sliceNum; ++i)
    {
        k2 = k1 + i + 1;
        AddTriangle(
            static_cast<UInt16>(k1),
            static_cast<UInt16>(k2),
            static_cast<UInt16>(k2 + 1));
    }

    return;
}

void PrimitiveGenerator::GenerateCone(PrimitiveData* outData, float radius, float height, UInt8 sliceNum, UInt8 stackNum)
{
    UInt16 vertexCount = (sliceNum + 1) * (stackNum + 2) + 1;
    UInt16 indexCount = 3 * (sliceNum * stackNum * 2 + sliceNum);
    VertexStreamLayout&& layout = GetStandardTriangleLayout();
    outData->Resize(vertexCount, indexCount, indexCount / 3, layout, false);
    outData->SetTopology(PrimitiveTopology::TriangleList);

    UInt32 offset = 0; UInt32 stride = layout.GetVertexStride();
    auto AddVertex = [vertexData = outData->GetVertexData(), &offset, stride, vertexCount](Float3* position, Float3* normal, Float2* uv)
    {
        Assert(stride* static_cast<UInt32>(vertexCount));
        memcpy(static_cast<void*>(vertexData + offset), position, 12);
        memcpy(static_cast<void*>(vertexData + offset + 12), normal, 12);
        memcpy(static_cast<void*>(vertexData + offset + 24), uv, 8);
        memcpy(static_cast<void*>(vertexData + offset + 32), &sColorWhite, 4);
        offset += stride;
    };
    Float3 position;
    Float3 normal;
    Float2 uv;
    float sliceStep = 2 * M_PI / sliceNum;
    float stackStep = height / stackNum;
    float radiusStep = radius / stackNum;
    float sliceAngle, stackHeight, stackRadius;
    for (int i = 0; i <= stackNum; ++i)
    {
        stackHeight = -height / 2 + i * stackStep;
        stackRadius = radius - i * radiusStep;
        position.z = stackHeight;

        for (int j = 0; j <= sliceNum; ++j)
        {
            sliceAngle = j * sliceStep;
            position.x = stackRadius * Cos(sliceAngle);
            position.y = stackRadius * Sin(sliceAngle);

            normal.x = Cos(sliceAngle);
            normal.y = Sin(sliceAngle);
            normal.z = radius / height;
            normal.Normalize();

            uv.x = static_cast<float>(j) / sliceNum;
            uv.y = static_cast<float>(i) / stackNum;

            AddVertex(&position, &normal, &uv);
        }
    }

    for (int i = 0; i < 1; ++i)
    {
        position.x = 0.0f;
        position.y = 0.0f;
        position.z = -height / 2;

        normal.x = 0.0f;
        normal.y = 0.0f;
        normal.z = -1.0f;

        uv.x = 0.5f;
        uv.y = 0.5f;

        AddVertex(&position, &normal, &uv);
        for (int j = 0; j <= sliceNum; ++j)
        {
            sliceAngle = j * sliceStep;
            position.x = radius * Cos(sliceAngle);
            position.y = radius * Sin(sliceAngle);

            uv.x = 0.5f - 0.5f * Cos(sliceAngle);
            uv.y = 0.5f - 0.5f * Sin(sliceAngle);

            AddVertex(&position, &normal, &uv);
        }
    }

    UInt16 arrayIndex = 0;
    auto AddTriangle = [indexArray = outData->GetIndexArray(), &arrayIndex, indexCount](UInt16 a, UInt16 b, UInt16 c)
    {
        Assert(arrayIndex < indexCount);
        indexArray[arrayIndex] = a;
        indexArray[arrayIndex + 1] = b;
        indexArray[arrayIndex + 2] = c;
        arrayIndex += 3;
    };
    int k1, k2;
    for (UInt8 i = 0; i < stackNum; ++i)
    {
        k1 = i * (sliceNum + 1);
        k2 = k1 + sliceNum + 1;
        for (UInt8 j = 0; j < sliceNum; ++j, ++k1, ++k2)
        {
            AddTriangle(
                static_cast<UInt16>(k1),
                static_cast<UInt16>(k1 + 1),
                static_cast<UInt16>(k2));

            AddTriangle(
                static_cast<UInt16>(k1 + 1),
                static_cast<UInt16>(k2 + 1),
                static_cast<UInt16>(k2));
        }
    }

    k1 = (sliceNum + 1) * (stackNum + 1);
    for (UInt8 i = 0; i < sliceNum; ++i)
    {
        k2 = k1 + i + 1;
        AddTriangle(
            static_cast<UInt16>(k1),
            static_cast<UInt16>(k2 + 1),
            static_cast<UInt16>(k2));
    }

    return;
}

void PrimitiveGenerator::GenerateCircle(PrimitiveData* outData, float radius /*= 1.f*/, UInt8 sliceNum /*= 32*/, Float3 origin /*= { 0.f, 0.f ,0.f }*/)
{
    UInt16 vertexCount = sliceNum + 1;
    UInt16 indexCount = 3 * sliceNum;
    VertexStreamLayout&& layout = GetStandardTriangleLayout();
    outData->Resize(vertexCount, indexCount, indexCount / 3, layout, false);
    outData->SetTopology(PrimitiveTopology::TriangleList);

    // Set vertex
    UInt32 offset = 0; UInt32 stride = layout.GetVertexStride();
    auto AddVertex = [vertexData = outData->GetVertexData(), &offset, stride, vertexCount](Float3* position, Float3* normal, Float2* uv)
    {
        Assert(stride * static_cast<UInt32>(vertexCount));
        memcpy(static_cast<void*>(vertexData + offset), position, 12);
        memcpy(static_cast<void*>(vertexData + offset + 12), normal, 12);
        memcpy(static_cast<void*>(vertexData + offset + 24), uv, 8);
        memcpy(static_cast<void*>(vertexData + offset + 32), &sColorWhite, 4);
        offset += stride;
    };

    Float3 normal{ 0, 0, -1 };
    Float3 leftTop{ origin.x - radius, origin.y - radius, origin.z };

    Float3 position;
    Float2 uv;

    uv.x = (origin.x - leftTop.x) / (2 * radius);
    uv.y = (origin.y - leftTop.y) / (2 * radius);
    AddVertex(&origin, &normal, &uv); // Center

    float sliceStep = 2 * M_PI / sliceNum;
    float sliceAngle;
    for (int i = 0; i < sliceNum; ++i)
    {
        sliceAngle = i * sliceStep;
        position.x = radius * Cos(sliceAngle) + origin.x;
        position.y = radius * Sin(sliceAngle) + origin.y;
        position.z = origin.z;

        uv.x = (position.x - leftTop.x) / (2 * radius);
        uv.y = (position.y - leftTop.y) / (2 * radius);

        AddVertex(&position, &normal, &uv);
    }

    // Set index
    UInt16 arrayIndex = 0;
    auto AddTriangle = [indexArray = outData->GetIndexArray(), &arrayIndex, indexCount](UInt16 a, UInt16 b, UInt16 c)
    {
        Assert(arrayIndex < indexCount);
        indexArray[arrayIndex] = a;
        indexArray[arrayIndex + 1] = b;
        indexArray[arrayIndex + 2] = c;
        arrayIndex += 3;
    };

    UInt16 k1, k2;
    for (UInt8 i = 0; i < sliceNum; ++i)
    {
        k1 = i + 1; // 1 -> sliceNum
        k2 = i + 2; // 2 -> sliceNum + 1
        k2 = k2 > sliceNum ? k2 % sliceNum : k2;
        AddTriangle(0, k1, k2);
    }

    return;
}

void PrimitiveGenerator::GenerateLine(PrimitiveData* outData, const Float3& start, const Float3& end)
{
    VertexStreamLayout&& layout = GetStandardLineLayout();
    outData->Resize(2, 2, 1, layout, false);
    outData->SetTopology(PrimitiveTopology::LineList);

    memcpy(static_cast<void*>(outData->GetVertexData()), start.data(), sizeof(Float3));
    memcpy(static_cast<void*>(outData->GetVertexData() + 12), &sColorWhite, sizeof(UInt32));
    memcpy(static_cast<void*>(outData->GetVertexData() + layout.GetVertexStride()), end.data(), sizeof(Float3));
    memcpy(static_cast<void*>(outData->GetVertexData() + layout.GetVertexStride() + 12), &sColorWhite, sizeof(UInt32));
    
    outData->GetIndexArray()[0] = 0;
    outData->GetIndexArray()[1] = 1;

    return;
}

/* 
     5------6
    /|     /|
   1-4----2-7  // 4567 are on the back.
   |/     |/
   0------3 
*/
void PrimitiveGenerator::GenerateCubeFrame(PrimitiveData* outData, float sideLength)
{
    VertexStreamLayout&& layout = GetStandardLineLayout();
    outData->Resize(8, 24, 12, layout, false);
    outData->SetTopology(PrimitiveTopology::LineList);

    UInt32 offset = 0; UInt32 stride = layout.GetVertexStride();
    auto AddVertex = [vertexData = outData->GetVertexData(), &offset, stride](Float3 position)
    {
        memcpy(static_cast<void*>(vertexData + offset), &position, sizeof(Float3));
        memcpy(static_cast<void*>(vertexData + offset + 12), &sColorWhite, sizeof(UInt32));
        offset += stride;
    };
    float halfLength = sideLength * 0.5f;
    AddVertex(Float3(-halfLength, -halfLength, -halfLength));
    AddVertex(Float3(-halfLength, halfLength, -halfLength));
    AddVertex(Float3(halfLength, halfLength, -halfLength));
    AddVertex(Float3(halfLength, -halfLength, -halfLength));
    AddVertex(Float3(-halfLength, -halfLength, halfLength));
    AddVertex(Float3(-halfLength, halfLength, halfLength));
    AddVertex(Float3(halfLength, halfLength, halfLength));
    AddVertex(Float3(halfLength, -halfLength, halfLength));

    UInt16 arrayIndex = 0;
    auto AddLine = [indexArray = outData->GetIndexArray(), &arrayIndex](UInt16 a, UInt16 b)
    {
        indexArray[arrayIndex] = a;
        indexArray[arrayIndex + 1] = b;
        arrayIndex += 2;
    };
    AddLine(0, 1); AddLine(1, 2); AddLine(2, 3); AddLine(3, 0);
    AddLine(0, 4); AddLine(1, 5); AddLine(2, 6); AddLine(3, 7);
    AddLine(4, 5); AddLine(5, 6); AddLine(6, 7); AddLine(7, 4);

    return;
}

void PrimitiveGenerator::GenerateCubeFrame(PrimitiveData* outData, const Float3& extent, const Float3& origin, const Quaternion& rotation)
{
    VertexStreamLayout&& layout = GetStandardLineLayout();
    outData->Resize(8, 24, 12, layout, false);
    outData->SetTopology(PrimitiveTopology::LineList);

    UInt32 offset = 0; UInt32 stride = layout.GetVertexStride();
    auto AddVertex = [vertexData = outData->GetVertexData(), &offset, stride, &origin, &rotation](Float3 position)
    {
        position = Float3::Transform(position, Float4x4::CreateFromQuaternion(rotation));
        position += origin;
        memcpy(static_cast<void*>(vertexData + offset), &position, sizeof(Float3));
        memcpy(static_cast<void*>(vertexData + offset + 12), &sColorWhite, sizeof(UInt32));
        offset += stride;
    };
    AddVertex(Float3(-extent.x, -extent.y, -extent.z));
    AddVertex(Float3(-extent.x, extent.y, -extent.z));
    AddVertex(Float3(extent.x, extent.y, -extent.z));
    AddVertex(Float3(extent.x, -extent.y, -extent.z));
    AddVertex(Float3(-extent.x, -extent.y, extent.z));
    AddVertex(Float3(-extent.x, extent.y, extent.z));
    AddVertex(Float3(extent.x, extent.y, extent.z));
    AddVertex(Float3(extent.x, -extent.y, extent.z));

    UInt16 arrayIndex = 0;
    auto AddLine = [indexArray = outData->GetIndexArray(), &arrayIndex](UInt16 a, UInt16 b)
    {
        indexArray[arrayIndex] = a;
        indexArray[arrayIndex + 1] = b;
        arrayIndex += 2;
    };
    AddLine(0, 1); AddLine(1, 2); AddLine(2, 3); AddLine(3, 0);
    AddLine(0, 4); AddLine(1, 5); AddLine(2, 6); AddLine(3, 7);
    AddLine(4, 5); AddLine(5, 6); AddLine(6, 7); AddLine(7, 4);

    return;
}

void PrimitiveGenerator::GenerateOrientedBoxFrame(PrimitiveData* outData, const BoundingOrientedBox& box, const Float4x4* worldMatrix, UInt32 color)
{
    VertexStreamLayout&& layout = GetStandardLineLayout();
    outData->Resize(8, 24, 12, layout, false);
    outData->SetTopology(PrimitiveTopology::LineList);

    UInt32 offset = 0; UInt32 stride = layout.GetVertexStride();
    auto AddVertex = [vertexData = outData->GetVertexData(), &offset, stride, color, worldMatrix](Float3 position)
    {
        if (worldMatrix) position = Float3A::Transform(position, *worldMatrix);
        memcpy(static_cast<void*>(vertexData + offset), &position, sizeof(Float3));
        memcpy(static_cast<void*>(vertexData + offset + 12), &color, sizeof(UInt32));
        offset += stride;
    };
    Float3 corners[8];
    box.GetCorners(corners);
    for (UInt8 i = 0; i < 8; ++i)
    {
        AddVertex(corners[i]);
    }

    UInt16 arrayIndex = 0;
    auto AddLine = [indexArray = outData->GetIndexArray(), &arrayIndex](UInt16 a, UInt16 b)
    {
        indexArray[arrayIndex] = a;
        indexArray[arrayIndex + 1] = b;
        arrayIndex += 2;
    };
    AddLine(0, 1); AddLine(1, 2); AddLine(2, 3); AddLine(3, 0);
    AddLine(0, 4); AddLine(1, 5); AddLine(2, 6); AddLine(3, 7);
    AddLine(4, 5); AddLine(5, 6); AddLine(6, 7); AddLine(7, 4);

    return;
}

void PrimitiveGenerator::GenerateSphereLine(PrimitiveData* outData, float radius , UInt8 sliceNum , UInt8 stackNum , Float3 origin)
{
    // Set data
    UInt16 vertexCount = (stackNum + 1) * (sliceNum + 1);
    UInt16 indexCount = 6 * stackNum * (sliceNum * 2 - 2);
    VertexStreamLayout&& layout = GetStandardLineLayout();
    outData->Resize(vertexCount, indexCount, indexCount / 2, layout, false);
    outData->SetTopology(PrimitiveTopology::LineList);

    // Set vertex
    UInt32 offset = 0; UInt32 stride = layout.GetVertexStride();
    UInt32 color = 0xffffffff;
    auto AddVertex = [vertexData = outData->GetVertexData(), &offset, stride, color](Float3 position)
    {
        memcpy(static_cast<void*>(vertexData + offset), &position, sizeof(Float3));
        memcpy(static_cast<void*>(vertexData + offset + 12), &color, sizeof(UInt32));
        offset += stride;
    };
    Float3 position; float xy;
    Float3 normal; float lengthInv = 1.0f / radius;
    Float2 uv;
    float sliceStep = 2 * M_PI / sliceNum;
    float stackStep = M_PI / stackNum;
    float sliceAngle, stackAngle;
    for (int i = 0; i <= stackNum; ++i)
    {
        stackAngle = M_PI / 2 - i * stackStep;
        xy = radius * Cos(stackAngle);
        position.z = radius * Sin(stackAngle) + origin.z;
        for (int j = 0; j <= sliceNum; ++j)
        {
            sliceAngle = j * sliceStep;
            position.x = xy * Cos(sliceAngle);
            position.y = xy * Sin(sliceAngle);

            normal.x = position.x * lengthInv;
            normal.y = position.y * lengthInv;
            normal.z = position.z * lengthInv;

            uv.x = static_cast<float>(j) / sliceNum;
            uv.y = static_cast<float>(i) / stackNum;

            position.x = position.x + origin.x;
            position.y = position.y + origin.y;
            AddVertex(position);
        }
    }

    // Set index
    UInt16 arrayIndex = 0;
    auto AddLine = [indexArray = outData->GetIndexArray(), &arrayIndex](UInt16 a, UInt16 b)
    {
        indexArray[arrayIndex] = a;
        indexArray[arrayIndex + 1] = b;
        arrayIndex += 2;
    };
    int k1, k2;
    for (UInt8 i = 0; i < stackNum; ++i)
    {
        k1 = i * (stackNum + 1);
        k2 = k1 + sliceNum + 1;
        for (UInt8 j = 0; j < sliceNum; ++j, ++k1, ++k2)
        {
            if (i != 0) {
                AddLine(static_cast<UInt16>(k1), static_cast<UInt16>(k2));
                AddLine(static_cast<UInt16>(k2), static_cast<UInt16>(k1 + 1));
                AddLine(static_cast<UInt16>(k1 + 1), static_cast<UInt16>(k1));
            }
            if (i != (stackNum - 1)) {
                AddLine(static_cast<UInt16>(k1 + 1), static_cast<UInt16>(k2));
                AddLine(static_cast<UInt16>(k2), static_cast<UInt16>(k2 + 1));
                AddLine(static_cast<UInt16>(k2 + 1), static_cast<UInt16>(k1 + 1));
            }
        }
    }

    return;
}

void PrimitiveGenerator::GenerateSphereFrame(PrimitiveData* outData, float radius, UInt8 sliceNum, UInt8 stackNum, Float3 origin)
{
    UInt16 vertexCount = 2 + (stackNum - 1) * sliceNum;
    UInt16 indexCount = 2 * (2 * stackNum - 1) * sliceNum;
    VertexStreamLayout&& layout = GetStandardLineLayout();
    outData->Resize(vertexCount, indexCount, indexCount / 2, layout, false);
    outData->SetTopology(PrimitiveTopology::LineList);

    // Set vertex
    UInt32 offset = 0; UInt32 stride = layout.GetVertexStride();
    auto AddVertex = [vertexData = outData->GetVertexData(), &offset, stride, vertexCount](Float3* position)
    {
        Assert(offset < stride * vertexCount);
        memcpy(static_cast<void*>(vertexData + offset), position, 12);
        memcpy(static_cast<void*>(vertexData + offset + 12), &sColorWhite, 4);
        offset += stride;
    };
    Float3 point(0.0f, radius, 0.0f);
    point = point + origin;
    AddVertex(&point);
    for (UInt8 iStack = 0; iStack < stackNum - 1; ++iStack)
    {
        float phi = M_PI * static_cast<float>(iStack + 1) / static_cast<float>(stackNum);
        for (UInt8 jSlice = 0; jSlice < sliceNum; ++jSlice)
        {
            float theta = 2.0f * M_PI * static_cast<float>(jSlice) / static_cast<float>(sliceNum);
            float r = radius * Sin(phi);
            point.x = r * Cos(theta);
            point.y = radius * Cos(phi);
            point.z = r * Sin(theta);
            point = point + origin;
            AddVertex(&point);
        }
    }
    point = Float3{ 0.0f, -radius, 0.0f };
    point = point + origin;
    AddVertex(&point);

    UInt16 arrayIndex = 0;
    auto AddLine = [indexArray = outData->GetIndexArray(), &arrayIndex, indexCount](UInt16 a, UInt16 b)
    {
        Assert(arrayIndex < indexCount);
        indexArray[arrayIndex] = a;
        indexArray[arrayIndex + 1] = b;
        arrayIndex += 2;
    };
    for (UInt8 jSlice = 1; jSlice <= sliceNum; ++jSlice)
    {
        AddLine(0, jSlice);
    }
    UInt16 startIndex = 1;
    for (UInt8 iStack = 0; iStack < stackNum - 1; ++iStack)
    {
        startIndex = 1 + iStack * sliceNum;
        for (UInt8 jSlice = 0; jSlice < sliceNum - 1; ++jSlice)
        {
            AddLine(startIndex + jSlice, startIndex + jSlice + 1);
        }
        AddLine(startIndex + sliceNum - 1, startIndex);
    }
    startIndex = 1;
    for (UInt8 iStack = 0; iStack < stackNum - 2; ++iStack)
    {
        startIndex = 1 + iStack * sliceNum;
        for (UInt8 jSlice = 0; jSlice < sliceNum; ++jSlice)
        {
            AddLine(startIndex + jSlice, startIndex + jSlice + sliceNum);
        }
    }
    startIndex = sliceNum * (stackNum - 2) + 1;
    for (UInt8 jSlice = 0; jSlice < sliceNum; ++jSlice)
    {
        AddLine(startIndex + jSlice, vertexCount - 1);
    }

    return;
}

void PrimitiveGenerator::GenerateBoundingSphereFrame(PrimitiveData* outData, float radius, UInt8 sliceNum, Float3 origin)
{
    UInt16 vertexCount = sliceNum * 3;
    UInt16 indexCount = 2 * sliceNum * 3;
    VertexStreamLayout&& layout = GetStandardLineLayout();
    outData->Resize(vertexCount, indexCount, indexCount / 2, layout, false);
    outData->SetTopology(PrimitiveTopology::LineList);

    // Set vertex
    UInt32 offset = 0;
    UInt32 stride = layout.GetVertexStride();
    auto AddVertex = [vertexData = outData->GetVertexData(), &offset, stride, vertexCount](Float3& position) {
        Assert(offset < stride * vertexCount);
        memcpy(static_cast<void*>(vertexData + offset), &position, 12);
        memcpy(static_cast<void*>(vertexData + offset + 12), &sColorGreen, 4);
        offset += stride;
    };

    auto AddCircleVertex = [&](const Float3& xAxis, const Float3& yAxis) -> void {
        Float3 position = Float3::Zero();
        float sliceStep = 2 * M_PI / sliceNum;
        float sliceAngle;
        for (UInt16 i = 0; i < sliceNum; ++i)
        {
            sliceAngle = i * sliceStep;
            position = (xAxis * Cos(sliceAngle) + yAxis * Sin(sliceAngle)) * radius + origin;
            AddVertex(position);
        }
    };

    AddCircleVertex({1.0f, 0.0f, 0.0f}, {0.0f, 1.0f, 0.0f});
    AddCircleVertex({1.0f, 0.0f, 0.0f}, {0.0f, 0.0f, 1.0f});
    AddCircleVertex({0.0f, 1.0f, 0.0f}, {0.0f, 0.0f, 1.0f});

    // Set index
    UInt16 arrayIndex = 0;
    auto AddLine = [indexArray = outData->GetIndexArray(), &arrayIndex](UInt16 a, UInt16 b) {
        indexArray[arrayIndex] = a;
        indexArray[arrayIndex + 1] = b;
        arrayIndex += 2;
    };

    auto AddCircleLine = [&](UInt16 startIndex) {
        for (UInt16 i = startIndex; i < startIndex + sliceNum - 1; ++i)
        {
            AddLine(i, i + 1);
        }
        AddLine(startIndex + sliceNum - 1, startIndex);
    };

    AddCircleLine(0);
    AddCircleLine(sliceNum);
    AddCircleLine(sliceNum * 2);
}

void PrimitiveGenerator::GenerateFrustumFrame(PrimitiveData* outData, const BoundingFrustum& frustum, const Float4x4* worldMatrix, UInt32 color)
{
    VertexStreamLayout&& layout = GetStandardLineLayout();
    outData->Resize(8, 24, 12, layout, false);
    outData->SetTopology(PrimitiveTopology::LineList);

    UInt32 offset = 0; UInt32 stride = layout.GetVertexStride();
    auto AddVertex = [vertexData = outData->GetVertexData(), &offset, stride, color, worldMatrix](Float3 position)
    {
        if(worldMatrix) position = Float3A::Transform(position, *worldMatrix);
        memcpy(static_cast<void*>(vertexData + offset), &position, sizeof(Float3));
        memcpy(static_cast<void*>(vertexData + offset + 12), &color, sizeof(UInt32));
        offset += stride;
    };
    Float3 corners[8];
    frustum.GetCorners(corners);
    for (UInt8 i = 0; i < 8; ++i)
    {
        AddVertex(corners[i]);
    }

    UInt16 arrayIndex = 0;
    auto AddLine = [indexArray = outData->GetIndexArray(), &arrayIndex](UInt16 a, UInt16 b)
    {
        indexArray[arrayIndex] = a;
        indexArray[arrayIndex + 1] = b;
        arrayIndex += 2;
    };
    AddLine(0, 1); AddLine(1, 2); AddLine(2, 3); AddLine(3, 0);
    AddLine(0, 4); AddLine(1, 5); AddLine(2, 6); AddLine(3, 7);
    AddLine(4, 5); AddLine(5, 6); AddLine(6, 7); AddLine(7, 4);

    return;
}

void PrimitiveGenerator::GenerateRays(PrimitiveData* outData, const Float3* starts, const Float3* ends, const Float3* colors, UInt32 count) 
{
    VertexStreamLayout&& layout = GetHDRLineLayout();
    outData->Resize(count * 2, count * 2, count, layout, false);
    outData->SetIndexCount(count * 2);
    outData->SetTopology(PrimitiveTopology::LineList);

    // Set vertexes.
    UInt32 offset = 0; UInt32 stride = layout.GetVertexStride();
    auto AddLine = [vertexData = outData->GetVertexData(), &offset, stride](const Float3& start, const Float3& end, const Float3& color)
    {
        memcpy(static_cast<void*>(vertexData + offset), &start, 12);
        memcpy(static_cast<void*>(vertexData + offset + 12), &color, 12);
        offset += stride;
        memcpy(static_cast<void*>(vertexData + offset), &end, 12);
        memcpy(static_cast<void*>(vertexData + offset + 12), &color, 12);
        offset += stride;
    };
    for (UInt32 i = 0; i < count; i++)
    {
        AddLine(starts[i], ends[i], colors[i]);
    }

    // Set indexes.
    UInt16 arrayIndex = 0;
    auto AddLineIndex = [indexArray = outData->GetIndexArray(), &arrayIndex](UInt16 a, UInt16 b)
    {
        indexArray[arrayIndex] = a;
        indexArray[arrayIndex + 1] = b;
        arrayIndex += 2;
    };
    for (UInt16 i = 0; i < count; i++)
    {
        AddLineIndex(2 * i, 2 * i + 1);
    }

    return;
}

void PrimitiveGenerator::GenerateCircleLine(PrimitiveData* outData, const Float3& xAxis, const Float3& yAxis, float radius, UInt8 sliceNum, Float3 origin)
{
    UInt16 vertexCount = sliceNum;
    UInt16 indexCount = 2 * sliceNum;
    VertexStreamLayout&& layout = GetStandardLineLayout();
    outData->Resize(vertexCount, indexCount, indexCount / 2, layout, false);
    outData->SetTopology(PrimitiveTopology::LineList);

    // Set vertex
    UInt32 offset = 0;
    UInt32 stride = layout.GetVertexStride();
    auto AddVertex = [vertexData = outData->GetVertexData(), &offset, stride, vertexCount](Float3& position) {
        Assert(offset < stride * vertexCount);
        memcpy(static_cast<void*>(vertexData + offset), &position, 12);
        memcpy(static_cast<void*>(vertexData + offset + 12), &sColorWhite, 4);
        offset += stride;
    };

    Float3 position;
    float sliceStep = 2 * M_PI / sliceNum;
    float sliceAngle;
    for (UInt16 i = 0; i < vertexCount; ++i)
    {
        sliceAngle = i * sliceStep;
        position = (xAxis * Cos(sliceAngle) + yAxis * Sin(sliceAngle)) * radius + origin;

        AddVertex(position);
    }

    // Set index
    UInt16 arrayIndex = 0;
    auto AddLine = [indexArray = outData->GetIndexArray(), &arrayIndex](UInt16 a, UInt16 b) {
        indexArray[arrayIndex] = a;
        indexArray[arrayIndex + 1] = b;
        arrayIndex += 2;
    };
    for (UInt16 i = 0; i < vertexCount - 1; ++i)
    {
        AddLine(i, (i + 1));
    }
    AddLine(vertexCount - 1, 0);
}

void PrimitiveGenerator::GenerateCircleLine(PrimitiveData* outData, float radius /*= 1.f*/, UInt8 sliceNum /*= 32*/, Float3 origin /*= { 0.f, 0.f, 0.f }*/)
{
    PrimitiveGenerator::GenerateCircleLine(outData, {1.f, 0.f, 0.f}, {0.f, 1.f, 0.f}, radius, sliceNum, origin);
}

void PrimitiveGenerator::GeneratePolygon(PrimitiveData* outData, const std::vector<Float3>& points, UInt32 color)
{
    UInt16 vertexCount = static_cast<UInt16>(points.size());
    //std::cerr << vertexCount << std::endl;
    UInt16 indexCount = static_cast<UInt16>((points.size()-1)*2);
    VertexStreamLayout&& layout = GetStandardLineLayout();
    outData->Resize(vertexCount, indexCount, indexCount / 2, layout, false);
    outData->SetTopology(PrimitiveTopology::LineList);
    UInt32 offset = 0;
    UInt32 stride = layout.GetVertexStride();
    auto AddVertex = [vertexData = outData->GetVertexData(), &offset, stride, color](Float3 position) {
       /* if (worldMatrix)
            position = Float3A::Transform(position, *worldMatrix);*/
        memcpy(static_cast<void*>(vertexData + offset), &position, sizeof(Float3));
        memcpy(static_cast<void*>(vertexData + offset + 12), &color, sizeof(UInt32));
        offset += stride;
    };
    for (UInt16 i = 0; i < vertexCount; ++i)
    {
        AddVertex(points[i]);
    }

    UInt16 arrayIndex = 0;
    auto AddLine = [indexArray = outData->GetIndexArray(), &arrayIndex](UInt16 a, UInt16 b) {
        indexArray[arrayIndex] = a;
        indexArray[arrayIndex + 1] = b;
        arrayIndex += 2;
    };
    for (UInt16 i = 0; i < vertexCount-1; ++i) {
        AddLine(i , (i + 1) );
    }
    return;
}

void PrimitiveGenerator::GeneratePointLightBoundaryFrame(PrimitiveData* outData, const Float4x4* worldMatrix, float range, UInt8 stackNum /* = 8 */, UInt32 color)
{
    UInt16 vertexCount = 6 + (stackNum - 1) * 12;
    UInt16 indexCount = stackNum * 24;
    VertexStreamLayout&& layout = GetStandardLineLayout();
    outData->Resize(vertexCount, indexCount, indexCount / 2, layout, false);
    outData->SetTopology(PrimitiveTopology::LineList);

    // Set vertex
    UInt32 offset = 0; UInt32 stride = layout.GetVertexStride();
    auto AddVertex = [vertexData = outData->GetVertexData(), &offset, stride, vertexCount, color, worldMatrix](Float3 position)
    {
        if (worldMatrix)
            position = Float3A::Transform(position, *worldMatrix);
        memcpy(static_cast<void*>(vertexData + offset), &position, 12);
        memcpy(static_cast<void*>(vertexData + offset + 12), &color, 4);
        offset += stride;
    };

    Float3 point(0.0f, range, 0.0f);
    AddVertex(point);
    for (UInt8 iStack = 0; iStack < stackNum * 2 - 1; ++iStack)
    {
        float phi = M_PI * static_cast<float>(iStack + 1) / static_cast<float>(stackNum * 2);
        UInt8 sliceNum = iStack == stackNum - 1 ? stackNum * 4 : 4;
        for (UInt8 jSlice = 0; jSlice < sliceNum; ++jSlice)
        {
            float theta = 2.0f * M_PI * static_cast<float>(jSlice) / static_cast<float>(sliceNum);
            float r = range * Sin(phi);
            point.x = r * Cos(theta);
            point.y = range * Cos(phi);
            point.z = r * Sin(theta);
            AddVertex(point);
        }
    }
    point = Float3{ 0.0f, -range, 0.0f };
    AddVertex(point);

    // Set index
    UInt16 arrayIndex = 0;
    auto AddLine = [indexArray = outData->GetIndexArray(), &arrayIndex, indexCount](UInt16 a, UInt16 b)
    {
        Assert(arrayIndex < indexCount);
        indexArray[arrayIndex] = a;
        indexArray[arrayIndex + 1] = b;
        arrayIndex += 2;
    };
    for (UInt8 iSlice = 1; iSlice <= 4; ++iSlice)
    {
        AddLine(0, iSlice);
    }
    UInt16 startIndex = 1;
    for (UInt8 iStack = 0; iStack < stackNum - 2; ++iStack)
    {
        for (UInt8 jSlice = 0; jSlice < 4; ++jSlice)
        {
            AddLine(startIndex, startIndex + 4);
            startIndex++;
        }
    }
    for (UInt8 iSlice = 0; iSlice < 4; ++iSlice)
    {
        UInt16 endIndex = startIndex + (4 - iSlice) + iSlice * stackNum;
        AddLine(startIndex, endIndex);
        for (UInt8 jStack = 0; jStack < stackNum; ++jStack)
        {
            if (iSlice == 3 && jStack == stackNum - 1)
                AddLine(endIndex + jStack, endIndex - iSlice * stackNum);
            else
                AddLine(endIndex + jStack, endIndex + jStack + 1);
        }
        UInt16 nextEndIndex = endIndex + iSlice + (4 - iSlice) * stackNum;
        AddLine(endIndex, nextEndIndex);
        startIndex++;
    }
    startIndex += 4 * stackNum;
    for (UInt8 iStack = 0; iStack < stackNum - 2; ++iStack)
    {
        for (UInt8 jSlice = 0; jSlice < 4; ++jSlice)
        {
            AddLine(startIndex, startIndex + 4);
            startIndex++;
        }
    }
    for (UInt8 iSlice = 1; iSlice <= 4; ++iSlice)
    {
        AddLine(startIndex, vertexCount - 1);
        startIndex++;
    }

    return;
}

void PrimitiveGenerator::GenerateSpotLightBoundaryFrame(PrimitiveData* outData, const Float4x4* worldMatrix, float range,  float cosHalfAngle, UInt8 stackNum, UInt32 color)
{
    if (range < 1e-3 || cosHalfAngle < 1e-3 )
        return;

    UInt16 vertexCount = 6 + (stackNum - 1) * 8;
    UInt16 indexCount = 4 * 2 * (stackNum - 1) + 4 * 2 + 4 * stackNum * 2 * 2 + 4; // cone hor & ver arc + outside ellipse

    VertexStreamLayout&& layout = GetStandardLineLayout();
    outData->Resize(vertexCount, indexCount, indexCount / 2, layout, false);
    outData->SetTopology(PrimitiveTopology::LineList);

    // Set vertex
    UInt32 offset = 0; UInt32 stride = layout.GetVertexStride();
    auto AddVertex = [vertexData = outData->GetVertexData(), &offset, stride, vertexCount, color, worldMatrix](Float3 position)
    {
        if (worldMatrix)
            position = Float3A::Transform(position, *worldMatrix);
        memcpy(static_cast<void*>(vertexData + offset), &position, 12);
        memcpy(static_cast<void*>(vertexData + offset + 12), &color, 4);
        offset += stride;
    };

    Float3 point(0.0f, 0.0f, range);
    AddVertex(point);
    for (UInt8 iStack = 0; iStack < stackNum; ++iStack)
    {
        float axis_phi = acosf(cosHalfAngle) * static_cast<float>(iStack + 1) / static_cast<float>(stackNum);

        UInt8 sliceNum = iStack == stackNum - 1 ? stackNum * 4 : 4;
        for (UInt8 jSlice = 0; jSlice < sliceNum; ++jSlice)
        {
            float theta = 2.0f * M_PI * static_cast<float>(jSlice) / static_cast<float>(sliceNum);
            float axis_ell_len = range * Sin(axis_phi);

            point.z = range * Cos(axis_phi);
            point.x = axis_ell_len * Cos(theta);
            point.y = axis_ell_len * Sin(theta);
            AddVertex(point);
        }
    }
    point = Float3{ 0.0f, 0.0f, 0.0f };
    AddVertex(point);

    // Set index
    UInt16 arrayIndex = 0;
    auto AddLine = [indexArray = outData->GetIndexArray(), &arrayIndex, indexCount](UInt16 a, UInt16 b)
    {
        Assert(arrayIndex < indexCount);
        indexArray[arrayIndex] = a;
        indexArray[arrayIndex + 1] = b;
        arrayIndex += 2;
    };

    for (UInt8 iSlice = 1; iSlice <= 4; ++iSlice)
        AddLine(0, iSlice);

    UInt16 startIndex = 1;
    for (UInt8 iStack = 0; iStack < stackNum - 2; ++iStack)
    {
        for (UInt8 jSlice = 0; jSlice < 4; ++jSlice)
        {
            AddLine(startIndex, startIndex + 4);
            startIndex++;
        }
    }

    AddLine(0, vertexCount - 1);
    AddLine(startIndex + 4, vertexCount - 2);

    for (UInt8 iSlice = 0; iSlice < 4; ++iSlice)
    {
        UInt16 endIndex = startIndex + (4 - iSlice) + iSlice * stackNum;
        AddLine(startIndex, endIndex);
        for (UInt8 jStack = 0; jStack < stackNum; ++jStack)
        {
            AddLine(endIndex + jStack, endIndex + jStack + 1);
            AddLine(endIndex + jStack, vertexCount - 1);
        }
        startIndex++;
    }

    return;
}

void PrimitiveGenerator::GenerateRectLightBoundaryFrame(PrimitiveData* outData, const Float4x4* worldMatrix, float range, float width, float height, float angle, float length, UInt8 stackNum, UInt32 color)
{
    PrimitiveData* tempData = new PrimitiveData();
    GeneratePointLightBoundaryFrame(tempData, worldMatrix, range, stackNum, color);
    UInt32 vertexStartIndex = tempData->GetVertexCount();
    UInt32 indexStartIndex = tempData->GetIndexCount();

    UInt32 vertexCount = 8 + vertexStartIndex;
    UInt32 indexCount = 24 + indexStartIndex;
    VertexStreamLayout&& layout = GetStandardLineLayout();
    outData->Resize(vertexCount, indexCount, indexCount / 2, layout, false);
    outData->SetTopology(PrimitiveTopology::LineList);

    UInt32 stride = layout.GetVertexStride();
    memcpy(outData->GetVertexData(), tempData->GetVertexData(), vertexStartIndex * stride);
    memcpy(outData->GetIndexArray(), tempData->GetIndexArray(), indexStartIndex * sizeof(UInt16));
    delete(tempData);

    // Set vertex
    UInt32 offset = vertexStartIndex * stride;
    auto AddVertex = [vertexData = outData->GetVertexData(), &offset, stride, vertexCount, color, worldMatrix](Float3 position)
    {
        if (worldMatrix)
            position = Float3A::Transform(position, *worldMatrix);
        memcpy(static_cast<void*>(vertexData + offset), &position, 12);
        memcpy(static_cast<void*>(vertexData + offset + 12), &color, 4);
        offset += stride;
    };

    Float3 point;
    point = Float3{ -width / 2, -height / 2, 0.0f }; AddVertex(point);
    point = Float3{ width / 2, -height / 2, 0.0f }; AddVertex(point);
    point = Float3{ width / 2, height / 2, 0.0f }; AddVertex(point);
    point = Float3{ -width / 2, height / 2, 0.0f }; AddVertex(point);
    float expandWidth = length * Sin(MathUtils::ConvertToRadians(angle)), expandHeight = length * Cos(MathUtils::ConvertToRadians(angle));
    point = Float3{ -(width / 2 + expandWidth), -(height / 2 + expandWidth), expandHeight }; AddVertex(point);
    point = Float3{ (width / 2 + expandWidth), -(height / 2 + expandWidth), expandHeight }; AddVertex(point);
    point = Float3{ width / 2 + expandWidth, height / 2 + expandWidth, expandHeight }; AddVertex(point);
    point = Float3{ -(width / 2 + expandWidth),  height / 2 + expandWidth, expandHeight }; AddVertex(point);

    // Set index
    UInt32 arrayIndex = indexStartIndex;
    auto AddLine = [indexArray = outData->GetIndexArray(), &arrayIndex, indexCount, vertexStartIndex](UInt16 a, UInt16 b)
    {
        Assert(arrayIndex < indexCount);
        indexArray[arrayIndex] = a + static_cast<UInt16>(vertexStartIndex);
        indexArray[arrayIndex + 1] = b + static_cast<UInt16>(vertexStartIndex);
        arrayIndex += 2;
    };

    AddLine(0, 1); AddLine(1, 2); AddLine(2, 3); AddLine(3, 0);
    AddLine(4, 5); AddLine(5, 6); AddLine(6, 7); AddLine(7, 4);
    AddLine(0, 4); AddLine(1, 5); AddLine(2, 6); AddLine(3, 7);

    return;
}

void PrimitiveGenerator::GenerateLightDirection(PrimitiveData* outData, const Float4x4* worldMatrix, UInt32 color)
{
    float cylinderRadius = 2.0f;
    float cylinderHeight = 60.0f;
    float coneRadius = 5.0f;
    float coneHeight = 15.0f;

    VertexStreamLayout&& layout = GetStandardTriangleLayout();
    UInt32 stride = layout.GetVertexStride();

    PrimitiveData* cylinderData = new PrimitiveData();
    GenerateCylinder(cylinderData, cylinderRadius, cylinderHeight);
    UInt32 cylinderVertexCount = cylinderData->GetVertexCount();
    UInt32 cylinderIndexCount = cylinderData->GetIndexCount();

    UInt8* cylinderVertexArray = cylinderData->GetVertexData();
    for (UInt32 i = 0; i < cylinderVertexCount; ++i)
    {
        float* z = reinterpret_cast<float*>(cylinderVertexArray + i * stride + 8);
        *z += cylinderHeight / 2;
    }

    PrimitiveData* coneData = new PrimitiveData();
    GenerateCone(coneData, coneRadius, coneHeight);
    UInt32 coneVertexCount = coneData->GetVertexCount();
    UInt32 coneIndexCount = coneData->GetIndexCount();

    UInt8* coneVertexArray = coneData->GetVertexData();
    for (UInt32 i = 0; i < coneVertexCount; ++i)
    {
        float* z = reinterpret_cast<float*>(coneVertexArray + i * stride + 8);
        *z += cylinderHeight + coneHeight / 2;
    }

    UInt16* coneIndexArray = coneData->GetIndexArray();
    for (UInt32 i = 0; i < coneIndexCount; ++i)
    {
        coneIndexArray[i] += (UInt16)cylinderVertexCount;
    }

    UInt32 vertexCount = cylinderVertexCount + coneVertexCount;
    UInt32 indexCount = cylinderIndexCount + coneIndexCount;
    
    outData->Resize(vertexCount, indexCount, indexCount / 3, layout, false);
    outData->SetTopology(PrimitiveTopology::TriangleList);

    memcpy(outData->GetVertexData(), cylinderData->GetVertexData(), cylinderVertexCount * stride);
    memcpy(outData->GetIndexArray(), cylinderData->GetIndexArray(), cylinderIndexCount * sizeof(UInt16));
    memcpy(outData->GetVertexData() + cylinderVertexCount * stride, coneData->GetVertexData(), coneVertexCount * stride);
    memcpy(outData->GetIndexArray() + cylinderIndexCount, coneData->GetIndexArray(), coneIndexCount * sizeof(UInt16));

    delete(cylinderData);
    delete(coneData);

    UInt8* vertexArray = outData->GetVertexData();
    for (UInt32 i = 0; i < vertexCount; ++i)
    {
        float* x = reinterpret_cast<float*>(vertexArray + i * stride);
        float* y = reinterpret_cast<float*>(vertexArray + i * stride + 4);
        float* z = reinterpret_cast<float*>(vertexArray + i * stride + 8);
        UInt32* vertexColor = reinterpret_cast<UInt32*>(vertexArray + i * stride + 32);
        
        Float3 position(*x, *y, *z);
        position = Float3::Transform(position, *worldMatrix);
        *x = position.x;
        *y = position.y;
        *z = position.z;
        *vertexColor = color;
    }

    return;
}

void PrimitiveGenerator::GenerateIconPlane(PrimitiveData* outData, const Float4x4* worldMatrix, float sideLength)
{
    VertexStreamLayout&& layout = GetStandardTriangleLayout();
    outData->Resize(4, 6, 2, layout, false);
    outData->SetTopology(PrimitiveTopology::TriangleList);

    UInt32 offset = 0; UInt32 stride = layout.GetVertexStride();
    Float3 normal(0.f, 1.f, 0.f);
    UInt32 colorWhite = 0xffffffff;
    auto AddVertex = [vertexData = outData->GetVertexData(), &offset, stride, normal, colorWhite, worldMatrix](Float3 position, Float2 uv)
    {
        if (worldMatrix != nullptr)
        {
            position = Float3::Transform(position, *worldMatrix);
        }
        memcpy(static_cast<void*>(vertexData + offset), &position, 12);
        memcpy(static_cast<void*>(vertexData + offset + 12), &normal, 12);
        memcpy(static_cast<void*>(vertexData + offset + 24), &uv, 8);
        memcpy(static_cast<void*>(vertexData + offset + 32), &colorWhite, 4);
        offset += stride;
    };
    float halfLength = sideLength * 0.5f;
    AddVertex(Float3(-halfLength, 0, -halfLength), Float2(0.f, 1.f));
    AddVertex(Float3(-halfLength, 0, halfLength), Float2(0.f, 0.f));
    AddVertex(Float3(halfLength, 0, halfLength), Float2(1.f, 0.f));
    AddVertex(Float3(halfLength, 0, -halfLength), Float2(1.f, 1.f));

    auto indexArray = outData->GetIndexArray();
    indexArray[0] = 2; indexArray[1] = 0; indexArray[2] = 1;
    indexArray[3] = 2; indexArray[4] = 3; indexArray[5] = 0;
}

void PrimitiveGenerator::GeneratePrimitiveFromMeshResource(PrimitiveData* outData, MeshAssetDataResourcePtr meshResource, UInt32 meshLod)
{
    auto assetData = meshResource->GetAssetData();
    VertexStreamLayout layout;
    UInt8 offset = 0;
    for (UInt32 index = 0; index < assetData->GetVertexChannelCount(); ++index)
    {
        auto channelAssetData = assetData->GetVertexChannelDataByIndex(index);
        layout.AddVertexChannelLayout(channelAssetData->mVertexChannel, channelAssetData->mDataFormat, offset);
        offset = AlignUp<UInt8>(offset + static_cast<UInt8>(channelAssetData->mStride), 4);
    }

    /*
     *   Tile Position channel
     *   Keep the tile channel at last
     */
#ifdef CE_USE_DOUBLE_TRANSFORM
    // last offset in byte is 12
    layout.AddVertexChannelLayout(PD_TILE_POSITION_CHANNEL, PD_TILE_POSITION_FORMAT, static_cast<UInt8>(layout.GetVertexStride()));
#endif

    UInt32 stride = layout.GetVertexStride(); // Align here
    auto& indexStream = assetData->GetIndexStream();
    UInt32 primitiveCount = assetData->GetPrimitiveCount() == 0 ? assetData->GetMeshPartInfo(0).mPrimitiveCount : assetData->GetPrimitiveCount();
    outData->Resize(assetData->GetVertexCount(), indexStream.mCount, primitiveCount, layout, !indexStream.mIs16BitIndex);
    outData->SetTopology(assetData->GetMeshPartInfo(0).mPrimitiveType);
    for (UInt32 value = 0; value < assetData->GetAllLodMeshPartCount(); ++value) 
    {
        auto& meshPartInfo = assetData->GetMeshPartInfo(value);
        for (UInt32 index = 0; index < meshPartInfo.mVertexCount; ++index)
        {
            for (UInt32 channelIndex = 0; channelIndex < assetData->GetVertexChannelCount(); ++channelIndex)
            {
                auto channelLayout = layout.GetChannelLayout(channelIndex);
                auto channelAssetData = assetData->GetVertexChannelDataByIndex(channelIndex);

                memcpy(outData->GetVertexData() + stride * index + channelLayout.mOffset, channelAssetData->mData.data() + channelAssetData->mStride * index, channelAssetData->mStride);
            }
        }
    }
    memcpy(outData->GetIndexArray(), indexStream.mData.data(), indexStream.mCount * (indexStream.mIs16BitIndex ? sizeof(UInt16) : sizeof(UInt32)));
}

void PrimitiveGenerator::GenerateAABBVectorFrame(PrimitiveData* outData, const std::vector<BoundingBox> boxes, const Quaternion& rotation) 
{
    UInt32 vectorSize = static_cast<UInt32>(boxes.size()); 
    //UInt32 vectorSize = 1; 

    VertexStreamLayout&& layout = GetStandardLineLayout();
    outData->Resize(8 * vectorSize, 24 * vectorSize, 12 * vectorSize, layout, false);
    outData->SetTopology(PrimitiveTopology::LineList);

    UInt32 offset = 0;
    UInt32 stride = layout.GetVertexStride();
    UInt16 arrayIndex = 0;
    for (UInt16 index = 0; index < vectorSize; ++index) 
    {
        BoundingBox box = boxes[index];
        Float3 origin, extent;
        box.GetCenter(&origin);
        box.GetExtent(&extent);

        auto AddVertex = [vertexData = outData->GetVertexData(), &offset, stride, &origin, &rotation](Float3 position) {
            position = Float3::Transform(position, Float4x4::CreateFromQuaternion(rotation));
            position += origin;
            memcpy(static_cast<void*>(vertexData + offset), &position, sizeof(Float3));
            memcpy(static_cast<void*>(vertexData + offset + 12), &sColorWhite, sizeof(UInt32));
            offset += stride;
        };
        AddVertex(Float3(-extent.x, -extent.y, -extent.z));
        AddVertex(Float3(-extent.x, extent.y, -extent.z));
        AddVertex(Float3(extent.x, extent.y, -extent.z));
        AddVertex(Float3(extent.x, -extent.y, -extent.z));
        AddVertex(Float3(-extent.x, -extent.y, extent.z));
        AddVertex(Float3(-extent.x, extent.y, extent.z));
        AddVertex(Float3(extent.x, extent.y, extent.z));
        AddVertex(Float3(extent.x, -extent.y, extent.z));

        
        auto AddLine = [indexArray = outData->GetIndexArray(), &arrayIndex](UInt16 a, UInt16 b) {
            indexArray[arrayIndex] = a;
            indexArray[arrayIndex + 1] = b;
            arrayIndex += 2;
        };

        AddLine(0 + 8 * index, 1 + 8 * index); AddLine(1 + 8 * index, 2 + 8 * index); AddLine(2 + 8 * index, 3 + 8 * index); AddLine(3 + 8 * index, 0 + 8 * index);
        AddLine(0 + 8 * index, 4 + 8 * index); AddLine(1 + 8 * index, 5 + 8 * index); AddLine(2 + 8 * index, 6 + 8 * index); AddLine(3 + 8 * index, 7 + 8 * index);
        AddLine(4 + 8 * index, 5 + 8 * index); AddLine(5 + 8 * index, 6 + 8 * index); AddLine(6 + 8 * index, 7 + 8 * index); AddLine(7 + 8 * index, 4 + 8 * index);
        
    }
}

}

#undef PD_TILE_POSITION_CHANNEL