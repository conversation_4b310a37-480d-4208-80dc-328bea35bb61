#include "EnginePrefix.h"
#include "RenderEngine/ReflectionProbeSystemR.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/FrameParam.h"
#include "CECommon/Common/FrameContainer.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "CECommon/Common/SettingsManager.h"
#include "RenderEngine/RenderWorld.h"
#include "RenderEngine/PrimitiveRenderSystemR.h"
#include "RenderEngine/LightSystemR.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderEngine/CameraSystemR.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/RenderPipeline/ReflectionProbeRenderPipeline.h"
#include "RenderEngine/RenderEngine.h"

#include "NativeGraphicsInterface/NGIManager.h"
namespace cross
{
constexpr int BUDGET_PROBE_NUM = 1;

ecs::ComponentDesc* ReflectionProbeCameraComponentR::GetDesc()
{
    return EngineGlobal::GetECSFramework().CreateOrGetRenderComponentDesc<cross::ReflectionProbeCameraComponentR>(false);
}
void ReflectionProbeSystemR::OnBuildUpdateTasks(FrameParam* frameParam)
{
    mBudgetProbeNum.store(BUDGET_PROBE_NUM);

    CreateTaskFunction(FrameTickStage::Update, {}, [this, frameParam]
    {
        SCOPED_CPU_TIMING(GroupRendering, "ReflectionProbeRUpdate");
        DrawShowingReflectionProbes(frameParam->GetFrameAllocator());

        auto reflectionProbeEntities = mRenderWorld->Query<ReflectionProbeCameraComponentR, TransformComponentR>();
        auto* transformSystem = mRenderWorld->GetRenderSystem<TransformSystemR>();
        auto* renderSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        for (auto [ReflectionProbeCameraH, rpTransformH] : reflectionProbeEntities)
        {
            if (!ReflectionProbeCameraH.Read()->mEnable) continue;

            if (ReflectionProbeCameraH.Read()->mRefleProbeType == ReflectionProbeType::Baked || ReflectionProbeCameraH.Read()->mRefleProbeType == ReflectionProbeType::Realtime) {
                Float3 cameraPos = transformSystem->GetWorldTranslation(rpTransformH.Read());// +ReflectionProbeCameraH.Read()->mLocalPosition;
                //auto quaternionRP = transformSystem->GetWorldRotation(rpTransformH.Read());
                //auto rotationMat = Float4x4::QuaternionToRotationMatrix(quaternionRP);

                std::array<Float4x4, REFLECTION_PROBE_CAMERA_NUM> viewMat;
                Float4x4A projMat;
                //renderSystem->CalCubeCaptureMatrix(cameraPos, ReflectionProbeCameraH.Read()->mNearClip, ReflectionProbeCameraH.Read()->mFarClip, viewMat, projMat);
                float nearClip = ReflectionProbeCameraH.Read()->mNearPlane;
                float farClip = 1000.f;
                if (ReflectionProbeCameraH.Read()->mRefleProbeShapeType == ReflectionProbeShapeType::Sphere)
                {
                    farClip = ReflectionProbeCameraH.Read()->mSphereRadius;
                    renderSystem->CalCubeCaptureMatrix(cameraPos, nearClip, farClip, viewMat, projMat);
                }
                else
                {
                    auto& boxSize = ReflectionProbeCameraH.Read()->mBoxSize;
                    farClip = std::max(std::max(boxSize.x, boxSize.y), boxSize.z);
                    renderSystem->CalCubeCaptureMatrix(cameraPos, nearClip, farClip, viewMat, projMat);
                }

#ifdef CE_USE_DOUBLE_TRANSFORM
                auto entityID = rpTransformH.GetEntityID();
                auto tileH = mRenderWorld->GetComponent<TilePositionComponentR>(entityID);
                Float3A tile = transformSystem->GetTilePosition(tileH.Read());
#endif
                for (UInt16 faceIndex = 0; faceIndex < REFLECTION_PROBE_CAMERA_NUM; faceIndex++) {
                    Float4x4 invViewProjMat = (viewMat[faceIndex] * projMat).Inverted();
                    auto cameraID = ReflectionProbeCameraH.Write()->mCameras.mFaceCameras[faceIndex];
                    if (!mRenderWorld->IsEntityAlive(cameraID))
                        continue;
                    auto& camera = mRenderWorld->GetComponent<CameraComponentR>(cameraID).Write()->mCamera;
                    camera.mCameraView.mViewMatrix = viewMat[faceIndex];
                    camera.mCameraView.mInvertViewMatrix = viewMat[faceIndex].Inverted();
                    camera.mCameraView.mProjMatrix = projMat;
                    camera.mCameraView.mInvertProjMatrix = projMat.Inverted();
                    camera.mCameraView.mViewProjMatrix = viewMat[faceIndex] * projMat;
#ifdef CE_USE_DOUBLE_TRANSFORM
                    camera.mCameraView.mCameraTilePosition = tile;
#endif
                    camera.mCameraInfo.mWidth = static_cast<float>(mIndirectSetting->CaptureCubeArraySize);
                    camera.mCameraInfo.mHeight = static_cast<float>(mIndirectSetting->CaptureCubeArraySize);
                    camera.mCameraInfo.mNearPlane = nearClip;
                    camera.mCameraInfo.mFarPlane = farClip;
                    camera.mCameraInfo.mAspectRatio = 1.0;
                    camera.mCameraInfo.mFov = MathUtils::ConvertToRadians(90.f);
                    camera.mTargetWidth = mIndirectSetting->CaptureCubeArraySize;
                    camera.mTargetHeight = mIndirectSetting->CaptureCubeArraySize;
                    BoundingFrustum::CreateFromCameraInfo(camera.mCameraView.mFrustum, camera.mCameraInfo.mAspectRatio, camera.mCameraInfo.mFov, camera.mCameraInfo.mNearPlane, camera.mCameraInfo.mFarPlane, true);
                }
            }
        }
    });
}

void ReflectionProbeSystemR::SetEnable(ReflectionProbeCameraH comp, bool isEnable)
{
    comp.Write()->mEnable = isEnable;
}

const CubemapCamera* ReflectionProbeSystemR::GetReflectionProbeCamera(const ReflectionProbeCameraReader& comp) const
{
    return &comp->mCameras;
}

void ReflectionProbeSystemR::SetReflectionProbeShow(ecs::EntityID entity, bool isShow)
{
    if (!mRenderWorld->IsEntityAlive(entity))
    {
        return;
    }
    if (isShow)
    {
        if (mShowingReflecProbePrimitives.find(entity) == mShowingReflecProbePrimitives.end())
        {
            mShowingReflecProbePrimitives.emplace(std::pair<ecs::EntityID, std::array<PrimitiveData, 2>>(entity, { {}}));
        }
    }
    else
    {
        mShowingReflecProbePrimitives.erase(entity);
    }
}

void ReflectionProbeSystemR::DrawShowingReflectionProbes(FrameAllocator* curAllocator)
{
    if (mIsShowRPs) {
        auto* reflectionProbeSystem = mRenderWorld->GetRenderSystem<ReflectionProbeSystemR>();
        auto* transformSystem = mRenderWorld->GetRenderSystem<TransformSystemR>();
        auto primitiveSystem = mRenderWorld->GetRenderSystem<PrimitiveRenderSystemR>();
        auto* toDeleteEntities = curAllocator->CreateFrameContainer<FrameVector<ecs::EntityID>>(FRAME_STAGE_RENDER, 2);
        for (auto& [entity, primitive] : mShowingReflecProbePrimitives)
        {
            bool isValid = false;
            if (mRenderWorld->IsEntityAlive(entity))
            {
                auto [rpH, transformH] = mRenderWorld->GetComponent<ReflectionProbeCameraComponentR, TransformComponentR>(entity);
                if (rpH.IsValid())
                {
                    auto rpShapeType = reflectionProbeSystem->GetReflectionProbeCameraRefleProbeShapeType(rpH.Read());
                    if (rpShapeType == ReflectionProbeShapeType::Sphere)
                    {
                        float radius = reflectionProbeSystem->GetReflectionProbeCameraSphereRadius(rpH.Read());
                        Float3 center = transformSystem->GetWorldTranslation(transformH.Read());
#ifdef CE_USE_DOUBLE_TRANSFORM
                        auto tilePosH = mRenderWorld->GetComponent<TilePositionComponentR>(entity);
                        Float3 tilePosition = transformSystem->GetTilePosition(tilePosH.Read());
                        center += tilePosition * LENGTH_PER_TILE_F;
#endif
                        PrimitiveGenerator::GenerateSphere(&primitive[0], radius, 8, 8, center);
                        PrimitiveGenerator::GenerateSphereLine(&primitive[1], radius, 8, 8, center);
                    }
                    else
                    {
                        Float3 extent, center;
                        center = transformSystem->GetWorldTranslation(transformH.Read());
                        extent = reflectionProbeSystem->GetReflectionProbeCameraBoxSize(rpH.Read()) * 0.5f;

#ifdef CE_USE_DOUBLE_TRANSFORM
                        auto tilePosH = mRenderWorld->GetComponent<TilePositionComponentR>(entity);
                        Float3 tilePosition = transformSystem->GetTilePosition(tilePosH.Read());
                        center += tilePosition * LENGTH_PER_TILE_F;
#endif

                        PrimitiveGenerator::GenerateCube(&primitive[0], extent, center);
                        PrimitiveGenerator::GenerateCubeFrame(&primitive[1], extent, center);
                    }
                    primitiveSystem->BatchPrimitive(&primitive[0], GetTransparentColorMaterial(), false);
                    primitiveSystem->BatchPrimitive(&primitive[1], GetLineColorMaterial(), false);
                    isValid = true;
                }
            }
            if (!isValid) toDeleteEntities->EmplaceBack(entity);
        }
    }
}

void ReflectionProbeSystemR::SetReflectionProbeCamerasRenderTarget(ecs::EntityID entity, const UInt32& cameraIndex, RenderTextureR* renderTexture)
{
    if (!mRenderWorld->IsEntityAlive(entity))
    {
        return;
    }

    auto comp = mRenderWorld->GetComponent<cross::ReflectionProbeCameraComponentR>(entity);

    if (!mRenderWorld->IsEntityAlive(comp.Read()->mCameras.mFaceCameras[cameraIndex]))
    {
        return;
    }
    auto cameraHandle = mRenderWorld->GetComponent<cross::CameraComponentR>(comp.Read()->mCameras.mFaceCameras[cameraIndex]);
    cameraHandle.Write()->mCamera.renderTexture = renderTexture;
}

void ReflectionProbeSystemR::SetReflectionProbeCameraEntityId(ecs::EntityID entity, const UInt32& cameraIndex, const ecs::EntityID& id)
{
    if (!mRenderWorld->IsEntityAlive(entity))
    {
        return;
    }

    auto comp = mRenderWorld->GetComponent<cross::ReflectionProbeCameraComponentR>(entity);
    comp.Write()->mCameras.mFaceCameras[cameraIndex] = id;
}

void ReflectionProbeSystemR::InitializeProbeRenderTextureInfo(ecs::EntityID entity)
{
    if (!mRenderWorld->IsEntityAlive(entity))
    {
        return;
    }
    auto comp = mRenderWorld->GetComponent<cross::ReflectionProbeCameraComponentR>(entity);
    auto reader = comp.Read();
    auto writer = comp.Write();
    writer->mDirty = true;

    // UInt32 resolution = reader->mResolution;
    auto resolution = mIndirectSetting->CaptureCubeArraySize;

    if (auto& tex = writer->mRenderTextureInfo[0].mRenderCubeTexture; tex && tex->GetDesc().Width == resolution)
    {
        return;
    }

    UInt16 mipmapNum = static_cast<UInt16>(std::log2(resolution)) + 1;

    auto* RED = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetRenderingExecutionDescriptor();

    for (auto i = 0; i < 2; i++)
    {
        auto& info = writer->mRenderTextureInfo[i];

        NGITextureDesc RPTexDesc{
            gReflectionProbeColorFormat,
            NGITextureType::TextureCube,
            mipmapNum,
            1,
            resolution,
            resolution,
            1,
            REFLECTION_PROBE_CAMERA_NUM,
            NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource | NGITextureUsage::UnorderedAccess | NGITextureUsage::CopySrc | NGITextureUsage::CopyDst,
            false,
            true,
        };
        info.mRenderCubeTexture = RED->CreateTexture(fmt::format("ReflectionProbeTexture:{}", i), RPTexDesc);

        NGITextureViewDesc wholeViewDesc{NGITextureUsage::ShaderResource,
                                         gReflectionProbeColorFormat,
                                         NGITextureType::TextureCube,
                                         {
                                             NGITextureAspect::Color,
                                             0,
                                             mipmapNum,
                                             0,
                                             REFLECTION_PROBE_CAMERA_NUM,
                                         }};
        info.mRenderCubeTextureView = RED->CreateTextureView(info.mRenderCubeTexture.get(), wholeViewDesc);
        info.mPerMipCubeViews.resize(mipmapNum);
        info.mPerMip2DArrayViews.resize(mipmapNum);
        info.mPerMipPerFaceViews.resize(mipmapNum);

        for (UInt16 mipId = 0; mipId < mipmapNum; ++mipId)
        {
            NGITextureViewDesc RPTexPerMipCubeViewDesc{NGITextureUsage::ShaderResource,
                                                       gReflectionProbeColorFormat,
                                                       NGITextureType::TextureCube,
                                                       {
                                                           NGITextureAspect::Color,
                                                           mipId,
                                                           1,
                                                           0,
                                                           REFLECTION_PROBE_CAMERA_NUM,
                                                       }};
            info.mPerMipCubeViews[mipId] = RED->CreateTextureView(info.mRenderCubeTexture.get(), RPTexPerMipCubeViewDesc);

            NGITextureViewDesc RPTexPerMip2DArrayViewDesc{NGITextureUsage::ShaderResource | NGITextureUsage::UnorderedAccess,
                                                          gReflectionProbeColorFormat,
                                                          NGITextureType::Texture2DArray,
                                                          {
                                                              NGITextureAspect::Color,
                                                              mipId,
                                                              1,
                                                              0,
                                                              REFLECTION_PROBE_CAMERA_NUM,
                                                          }};
            info.mPerMip2DArrayViews[mipId] = RED->CreateTextureView(info.mRenderCubeTexture.get(), RPTexPerMip2DArrayViewDesc);

            for (UInt16 faceId = 0; faceId < REFLECTION_PROBE_CAMERA_NUM; faceId++)
            {
                NGITextureViewDesc RPTexPerMipPerFaceViewDesc{NGITextureUsage::RenderTarget,
                                                              gReflectionProbeColorFormat,
                                                              NGITextureType::Texture2D,
                                                              {
                                                                  NGITextureAspect::Color,
                                                                  mipId,
                                                                  1,
                                                                  faceId,
                                                                  1,
                                                              }};
                info.mPerMipPerFaceViews[mipId][faceId] = RED->CreateTextureView(info.mRenderCubeTexture.get(), RPTexPerMipPerFaceViewDesc);
            }

            RED->AllocatePass("Init Reflection Probe")->ClearTexture(info.mRenderCubeTextureView.get(), { .colorf = {0, 0, 0, 0} });
        }

        auto camera = mRenderWorld->GetComponent<CameraComponentR>(reader->mCameras.mFaceCameras[0]).Read()->mCamera;
        info.mRenderTextureName = "Realtime Reflection Probe";
    }
}

void ReflectionProbeSystemR::BindCameraToReflectionPipeline(std::vector<ecs::EntityID> entitys, IRenderPipeline* pipeline)
{
    auto& assistPipelines = dynamic_cast<ReflectionProbeRenderPipeline*>(pipeline)->GetAssistPipelines();
    auto* cameraSystem = TYPE_CAST(CameraSystemR*, mRenderWorld->GetRenderSystem<CameraSystemR>());
    for (UInt16 index = 0; index < REFLECTION_PROBE_CAMERA_NUM; index++)
    {
        if (!mRenderWorld->IsEntityAlive(entitys[index]))
        {
            continue;
        }
        auto cam = mRenderWorld->GetComponent<CameraComponentR>(entitys[index]);
        assistPipelines[index]->SetCamera(entitys[index]);
        assistPipelines[index]->SetRenderCamera(cameraSystem->GetRenderCamera(cam.Read()));
    }
}

#define REFLECTION_PROBE_CAMERA_COMPONENT_R_PROPERTY_IMPL(PROP, TYPE)\
    void ReflectionProbeSystemR::SetReflectionProbeCamera##PROP(ecs::EntityID entity, const TYPE& val)                                                                                                                                                                 \
    {                                                                                                                                                                                                                                          \
        ReflectionProbeCameraH comp = mRenderWorld->GetComponent<ReflectionProbeCameraComponentR>(entity);                                                                                                                                     \
        comp.Write()->mDirty = true;                                                                                                                                                                                                           \
        comp.Write()->m##PROP = val;                                                                                                                                                                                                           \
    }                                                                                                                                                                                                                                          \
    const TYPE ReflectionProbeSystemR::GetReflectionProbeCamera##PROP(const ReflectionProbeCameraReader& comp) const                                                                                                                                                   \
    {                                                                                                                                                                                                                                          \
        return comp->m##PROP;                                                                                                                                                                                                                  \
    }

    REFLECTION_PROBE_CAMERA_COMPONENT_R_PROPERTY_IMPL(RefleProbeType, ReflectionProbeType);
    REFLECTION_PROBE_CAMERA_COMPONENT_R_PROPERTY_IMPL(RefleProbeShapeType, ReflectionProbeShapeType);
    REFLECTION_PROBE_CAMERA_COMPONENT_R_PROPERTY_IMPL(SphereRadius, float);
    REFLECTION_PROBE_CAMERA_COMPONENT_R_PROPERTY_IMPL(BoxSize, Float3);
    REFLECTION_PROBE_CAMERA_COMPONENT_R_PROPERTY_IMPL(NearPlane, float);
    REFLECTION_PROBE_CAMERA_COMPONENT_R_PROPERTY_IMPL(BlendDistance, float);
    REFLECTION_PROBE_CAMERA_COMPONENT_R_PROPERTY_IMPL(Intensity, float);
    REFLECTION_PROBE_CAMERA_COMPONENT_R_PROPERTY_IMPL(RefreshMode, ReflectionProbeRefreshMode);
    REFLECTION_PROBE_CAMERA_COMPONENT_R_PROPERTY_IMPL(TimeSlicing, ReflectionProbeTimeSlicing);
    REFLECTION_PROBE_CAMERA_COMPONENT_R_PROPERTY_IMPL(BoxProjection, bool);
    REFLECTION_PROBE_CAMERA_COMPONENT_R_PROPERTY_IMPL(Dirty, bool);
    REFLECTION_PROBE_CAMERA_COMPONENT_R_PROPERTY_IMPL(Enable, bool);

    void ReflectionProbeSystemR::SetReflectionProbeCameraReflectionCubeMap(ecs::EntityID entity, GPUTexture* val)
    {
        ReflectionProbeCameraH comp = mRenderWorld->GetComponent<ReflectionProbeCameraComponentR>(entity);
        comp.Write()->mReflectionCubeMap = val;
    }
    GPUTexture* ReflectionProbeSystemR::GetReflectionProbeCameraReflectionCubeMap(const ReflectionProbeCameraReader& comp) const
    {
        return comp->mReflectionCubeMap;
    }

#undef REFLECTION_PROBE_CAMERA_COMPONENT_R_PROPERTY_IMPL
}
