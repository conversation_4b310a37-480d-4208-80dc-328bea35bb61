#include "EnginePrefix.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/FrameParam.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "RenderEngine/RenderCullingResultQueue.h"
#include "RenderEngine/ReflectionProbeCullingSystemR.h"
#include "RenderEngine/CameraSystemR.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderEngine/ReflectionProbeSystemR.h"
#include "RenderEngine/RenderWorld.h"
#include "RenderEngine/RenderPropertySystemR.h"
#include "RenderEngine/RenderPipelineSystemR.h"
#include "RenderEngine/RenderPipeline/RenderPipelineR.h"
#include "RenderEngine/RenderNodeSystemR.h"
// #include <chrono>

namespace cross
{
#ifdef CE_USE_DOUBLE_TRANSFORM
void CullingReflectionProbeData::AddSphereReflecProbe(ecs::EntityID rpEntity, const Float3& position, float range, float intensity, const Float3& tilePos)
{
    mSphereRefleProbe.emplace_back(rpEntity, position, range, intensity, tilePos);
}

void CullingReflectionProbeData::AddBoxReflecProbe(ecs::EntityID rpEntity, const Float3& position, const Float3& range, float intensity, const Float3& tilePos)
{
    mBoxRefleProbe.emplace_back(rpEntity, position, range, intensity, tilePos);
}
#else
void CullingReflectionProbeData::AddSphereReflecProbe(ecs::EntityID rpEntity, const Float3& position, float range, float intensity)
{
    mSphereRefleProbe.emplace_back(rpEntity, position, range, intensity);
}

void CullingReflectionProbeData::AddBoxReflecProbe(ecs::EntityID rpEntity, const Float3& position, const Float3& range, float intensity)
{
    mBoxRefleProbe.emplace_back(rpEntity, position, range, intensity);
}
#endif

void CullingReflectionProbeData::Clear()
{
    mSphereRefleProbe.clear();
    mBoxRefleProbe.clear();
}

void ReflectionProbeCulling::CullRefleProbesForCamera(const CullingReflectionProbeData& refleProbesData, const BoundingFrustumData& cameraFrustum, const Float3& cameraPos,
    std::function<void(FrameVector<std::pair<ecs::EntityID, float>>*)> outputCameraResultFunction, FrameAllocator* frameAllocator)
{
    auto* rpDistVector = frameAllocator->CreateFrameContainer<FrameVector<std::pair<ecs::EntityID, float>>>(FRAME_STAGE_RENDER, MAX_PIPELINE_REFLECTION_PROBE_NUM);
#ifdef CE_USE_DOUBLE_TRANSFORM
    for (auto& [sphereRPid, spherePos, range, intensity, rpTilePos] : refleProbesData.mSphereRefleProbe)
#else
    for (auto& [sphereRPid, spherePos, range, intensity] : refleProbesData.mSphereRefleProbe)
#endif
    {
        BoundingSphere rpSphere(spherePos, range);

#ifdef CE_USE_DOUBLE_TRANSFORM
        rpSphere.Transform(rpSphere, (rpTilePos - cameraFrustum.mTilePosition) * LENGTH_PER_TILE_F);
        spherePos.Add((rpTilePos - cameraFrustum.mTilePosition) * LENGTH_PER_TILE_F);
#endif

        if (cameraFrustum.mFrustum.Intersects(rpSphere)) {
            rpDistVector->EmplaceBack(sphereRPid, Float3::Distance(cameraPos, spherePos));
        }
    }

#ifdef CE_USE_DOUBLE_TRANSFORM
    for (auto& [boxRPid, boxPos, range, intesity, rpTilePos] : refleProbesData.mBoxRefleProbe)
#else
    for (auto& [boxRPid, boxPos, range, intesity] : refleProbesData.mBoxRefleProbe)
#endif
    {
        BoundingBox rpBox(boxPos, range/2.f);
#ifdef CE_USE_DOUBLE_TRANSFORM
        rpBox.Transform(rpBox, (rpTilePos - cameraFrustum.mTilePosition) * LENGTH_PER_TILE_F);
        boxPos.Add((rpTilePos - cameraFrustum.mTilePosition) * LENGTH_PER_TILE_F);
#endif
        if (cameraFrustum.mFrustum.Intersects(rpBox))
        {
            rpDistVector->EmplaceBack(boxRPid, Float3::Distance(cameraPos, boxPos));
        }
    }
    outputCameraResultFunction(rpDistVector);
}

ReflectionProbeCullingSystemR* ReflectionProbeCullingSystemR::CreateInstance()
{
    ReflectionProbeCullingSystemR* system = new ReflectionProbeCullingSystemR();
    return system;
}

ReflectionProbeCullingSystemR::ReflectionProbeCullingSystemR()
{
}

ReflectionProbeCullingSystemR::~ReflectionProbeCullingSystemR()
{
}

void ReflectionProbeCullingSystemR::Release()
{
    delete this;
}

void ReflectionProbeCullingSystemR::OnBuildUpdateTasks(FrameParam* frameParam)
{
    auto* reflectionProbeSystem = mRenderWorld->GetRenderSystem<ReflectionProbeSystemR>();
    auto* transformSystem = mRenderWorld->GetRenderSystem<TransformSystemR>();

    auto ReflectionProbeDistSortingFunction = [this, reflectionProbeSystem](const std::pair<ecs::EntityID, float>& aRefleProbeDist, const std::pair<ecs::EntityID, float>& bRefleProbeDist)
    {
        // volume
        auto& aRefleProbeH = tRefleProbeComponentMap.at(aRefleProbeDist.first);
        auto& bRefleProbeH = tRefleProbeComponentMap.at(bRefleProbeDist.first);
        // influenceRange (todo: the smaller replace the bigger)
        float aRange, bRange;
        if (reflectionProbeSystem->GetReflectionProbeCameraRefleProbeShapeType(aRefleProbeH.Read()) == ReflectionProbeShapeType::Sphere)
        {
            auto radius = reflectionProbeSystem->GetReflectionProbeCameraSphereRadius(aRefleProbeH.Read());
            aRange = radius;
        }
        else
        {
            auto boxSize = reflectionProbeSystem->GetReflectionProbeCameraBoxSize(aRefleProbeH.Read());
            aRange = std::max(std::max(boxSize.x, boxSize.y), boxSize.z);
        }

        if (reflectionProbeSystem->GetReflectionProbeCameraRefleProbeShapeType(bRefleProbeH.Read()) == ReflectionProbeShapeType::Sphere)
        {
            auto radius = reflectionProbeSystem->GetReflectionProbeCameraSphereRadius(bRefleProbeH.Read());
            bRange = radius;
        }
        else
        {
            auto boxSize = reflectionProbeSystem->GetReflectionProbeCameraBoxSize(bRefleProbeH.Read());
            bRange = std::max(std::max(boxSize.x, boxSize.y), boxSize.z);
        }
        if (aRange != bRange)
        {
            return aRange < bRange;
        }

        // distance
        return aRefleProbeDist.second < bRefleProbeDist.second;
    };

    auto ReflectionProbeSortingFunction = [this, reflectionProbeSystem](const std::pair<ecs::EntityID, float>& aRefleProbeWeight, const std::pair<ecs::EntityID, float>& bRefleProbeWeight)
    {           
        // weight
        if(aRefleProbeWeight.second != bRefleProbeWeight.second)
            return aRefleProbeWeight.second > bRefleProbeWeight.second;

        auto& aRefleProbeH = tRefleProbeComponentMap.at(aRefleProbeWeight.first);
        auto& bRefleProbeH = tRefleProbeComponentMap.at(bRefleProbeWeight.first);
        // influenceRange (todo: the smaller replace the bigger)
        float aVolume, bVolume;
        if (reflectionProbeSystem->GetReflectionProbeCameraRefleProbeShapeType(aRefleProbeH.Read()) == ReflectionProbeShapeType::Sphere)
        {
            auto radius = reflectionProbeSystem->GetReflectionProbeCameraSphereRadius(aRefleProbeH.Read());
            aVolume = 4.18879013f * radius * radius * radius;
        }
        else
        {
            auto boxSize = reflectionProbeSystem->GetReflectionProbeCameraBoxSize(aRefleProbeH.Read());
            aVolume = boxSize.x * boxSize.y * boxSize.z;
        }

        if (reflectionProbeSystem->GetReflectionProbeCameraRefleProbeShapeType(bRefleProbeH.Read()) == ReflectionProbeShapeType::Sphere)
        {
            auto radius = reflectionProbeSystem->GetReflectionProbeCameraSphereRadius(bRefleProbeH.Read());
            bVolume = 4.18879013f * radius * radius * radius;
        }
        else
        {
            auto boxSize = reflectionProbeSystem->GetReflectionProbeCameraBoxSize(bRefleProbeH.Read());
            bVolume = boxSize.x * boxSize.y * boxSize.z;
        }
        return aVolume < bVolume;
    };

    auto prepareTask = CreateTaskFunction(FrameTickStage::Update, {}, [this, reflectionProbeSystem, transformSystem]
    {
            SCOPED_CPU_TIMING(GroupRendering, "Prepare ReflectionProbe Culling");
            tRefleProbeData.Clear();
            tRefleProbeComponentMap.clear();
            auto reflectionProbes = mRenderWorld->Query<ReflectionProbeCameraComponentR, TransformComponentR>();
            mReflectionProbeCount = 0;
            // gather rpData
            for (auto [rp, transform] : reflectionProbes)
            {
                if (!reflectionProbeSystem->IsRefleProbeEnableForRuntime(rp.Read()))
                    continue;
                auto rpShapeType = reflectionProbeSystem->GetReflectionProbeCameraRefleProbeShapeType(rp.Read());
                if (rpShapeType == ReflectionProbeShapeType::Sphere)
#ifdef CE_USE_DOUBLE_TRANSFORM
                    tRefleProbeData.AddSphereReflecProbe(rp.GetEntityID(), transformSystem->GetWorldTranslation(transform.Read()),
                        reflectionProbeSystem->GetReflectionProbeCameraSphereRadius(rp.Read()), reflectionProbeSystem->GetReflectionProbeCameraIntensity(rp.Read()), 
                        transformSystem->GetTilePosition(mRenderWorld->GetComponent<TilePositionComponentR>(rp.GetEntityID()).Read()));
#else
                    tRefleProbeData.AddSphereReflecProbe(rp.GetEntityID(), transformSystem->GetWorldTranslation(transform.Read()), 
                    reflectionProbeSystem->GetReflectionProbeCameraSphereRadius(rp.Read()), reflectionProbeSystem->GetReflectionProbeCameraIntensity(rp.Read()));
#endif
                else if (rpShapeType == ReflectionProbeShapeType::Box)
#ifdef CE_USE_DOUBLE_TRANSFORM
                    tRefleProbeData.AddBoxReflecProbe(rp.GetEntityID(), transformSystem->GetWorldTranslation(transform.Read()),
                        reflectionProbeSystem->GetReflectionProbeCameraBoxSize(rp.Read()), reflectionProbeSystem->GetReflectionProbeCameraIntensity(rp.Read()),
                        transformSystem->GetTilePosition(mRenderWorld->GetComponent<TilePositionComponentR>(rp.GetEntityID()).Read())
                    );
#else
                    tRefleProbeData.AddBoxReflecProbe(rp.GetEntityID(), transformSystem->GetWorldTranslation(transform.Read()),
                    reflectionProbeSystem->GetReflectionProbeCameraBoxSize(rp.Read()), reflectionProbeSystem->GetReflectionProbeCameraIntensity(rp.Read()));
#endif
                tRefleProbeComponentMap[rp.GetEntityID()] = rp;
                mReflectionProbeCount++;
            }
    });

    if (mReflectionProbeCount)
    {
        CreateTaskFunction(FrameTickStage::Update, {prepareTask}, [this, frameParam, ReflectionProbeDistSortingFunction, transformSystem] {
            SCOPED_CPU_TIMING(GroupRendering, "ReflectionProbe Culling for cameras");

            auto* renderPipelineSystem = mRenderWorld->GetRenderSystem<RenderPipelineSystemR>();
            auto* cameraSystem = mRenderWorld->GetRenderSystem<CameraSystemR>();
            auto curFrameAllocator = frameParam->GetFrameAllocator();

            // Cull for camera's render pipeline
            {
                // Code will be removed after new RenderGraph is ready:
                for (auto& [type, renderPipeline] : renderPipelineSystem->GetAllRenderPipelines())
                {
                    if (type != ViewType::GameView)
                    {
                        continue;
                    }
                    renderPipeline->ClearRefleProbeList();
                    if (!renderPipeline->IsEnable())
                        continue;
                    auto cameraEntity = renderPipeline->GetCamera();
                    auto [cameraComp, cameraTransformComp] = mRenderWorld->GetComponent<CameraComponentR, TransformComponentR>(cameraEntity);
                    auto& boundingFrustum = cameraSystem->GetRenderCamera(cameraComp.Read())->GetFrustum();
                    Float4x4 cameraMatrix = *(transformSystem->GetWorldMatrix(cameraTransformComp.Read()));
                    BoundingFrustumData realCameraFrustum;
                    boundingFrustum.Transform(realCameraFrustum.mFrustum, cameraMatrix);
#if defined(CE_USE_DOUBLE_TRANSFORM)
                    realCameraFrustum.mTilePosition = transformSystem->GetTilePosition((mRenderWorld->GetComponent<TilePositionComponentR>(cameraEntity)).Read());
#endif
                    auto& cameraPos = transformSystem->GetWorldTranslation(cameraTransformComp.Read());
                    ReflectionProbeCulling::CullRefleProbesForCamera(
                        tRefleProbeData,
                        realCameraFrustum,
                        cameraPos,
                        [renderPipeline = renderPipeline.get(), &ReflectionProbeDistSortingFunction](FrameVector<std::pair<ecs::EntityID, float>>* rpDistances) {
                            std::sort(rpDistances->begin(), rpDistances->end(), ReflectionProbeDistSortingFunction);
                            for (auto rpDistance : *rpDistances)
                            {
                                renderPipeline->AddVisibleRefleProbe(rpDistance.first);
                            }
                        },
                        curFrameAllocator);
                }
            }
        });

        //const bool PerEntityReflectionProbeList = false;

        //if (false)
        //{
        //    auto* cullingGatherSystem = mRenderWorld->GetRenderSystem<CullingGatherSystemR>();
        //    auto* renderNodeSystem = mRenderWorld->GetRenderSystem<RenderNodeSystemR>();
        //    UInt32 preBlockNum = static_cast<UInt32>(cullingGatherSystem->GetCullingObjectsData().mBlocks.size());
        //    const UInt32 totalBlockNum = preBlockNum + static_cast<UInt32>(cullingGatherSystem->GetAlwaysVisibleObjectsData().mBlocks.size());
        //    auto curFrameAllocator = frameParam->GetFrameAllocator();
        //    for (UInt32 blockId = 0; blockId < totalBlockNum; ++blockId)
        //    {
        //        CreateTaskFunction(stage, { prepareTask }, [this, blockId, cullingGatherSystem, renderNodeSystem, preBlockNum, ReflectionProbeSortingFunction, curFrameAllocator]() {
        //            SCOPED_CPU_TIMING(GroupRendering, "DotheReflectionProbeCulling");
        //            const CullingAABBData* objectsData = nullptr;
        //            UInt32 realBlockId = blockId;
        //            if (blockId < preBlockNum)
        //            {
        //                objectsData = &cullingGatherSystem->GetCullingObjectsData();
        //            }
        //            else
        //            {
        //                objectsData = &cullingGatherSystem->GetAlwaysVisibleObjectsData();
        //                realBlockId -= preBlockNum;
        //            }
        //            ReflectionProbeCulling::CullRefleProbesForBBox(
        //                *objectsData,
        //                tRefleProbeData,
        //                [&ReflectionProbeSortingFunction, renderNodeSystem, this](ecs::EntityID objectEntity, FrameVector<std::pair<ecs::EntityID, float>>* rpWeights) {
        //                    std::sort(rpWeights->begin(), rpWeights->end(), ReflectionProbeSortingFunction);
        //                    if (!mRenderWorld->HasComponent<RenderNodeComponentR>(objectEntity))
        //                        return;
        //                    auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(objectEntity);
        //                    Assert(renderNodeComp.IsValid());
        //                    renderNodeComp.Write(ecs::ComponentAccessFlag::SubComponentAccess0)->GetRenderNode()->SetReflectionProbeList(rpWeights);
        //                },
        //                curFrameAllocator,
        //                realBlockId);
        //            });
        //    }
        //}
    }
}
}
