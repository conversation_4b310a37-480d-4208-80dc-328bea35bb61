#pragma once
#include "Math/CrossMath.h"
#include "CECommon/Common/FrameContainer.h"
#include "CECommon/Common/RenderSystemBase.h"

#include "Runtime/GameWorld/DecalSystemG.h"

#include "RenderEngine/RenderContext.h"
#include "RenderEngine/PrimitiveGenerator.h"
#include "RenderEngine/RenderPipeline/Effects/PassBase.h"

#define DECAL_FX_PASS_NAME "decal_gpass"
#define DECAL_SHADER_CONSTANT_0 "Decal_BaseColor_Blend"
#define DECAL_SHADER_CONSTANT_1 "Decal_Normal_Blend"
#define DECAL_SHADER_CONSTANT_2 "Decal_Metallic_Blend"
#define DECAL_SHADER_CONSTANT_3 "Decal_Specular_Blend"
#define DECAL_SHADER_CONSTANT_4 "Decal_Roughness_Blend"
#define DECAL_SHADER_CONSTANT_5 "Decal_Emissive_Blend"
#define DECAL_SHADER_CONSTANT_6 "Decal_Opacity"

namespace cross 
{
struct REDPass;
struct REDTextureView;
struct REDTexture;
struct RenderingExecutionDescriptor;

struct DecalComponentR : ecs::IComponent
{
    CEFunction(Reflect)
    RENDER_ENGINE_API static ecs::ComponentDesc* GetDesc();

private:
    bool enable = false;
    bool dirty = true;
    
    /*
    *   Decal material.
    */
    MaterialR* DecalMaterial;

    /*
    *   Decal to world space matrix with scale.
    *   if DOUBLE_TRANSFORM is enabled, this is decal to tile space local transform
    */
    Float4x4 DecalToWorldMatrix;

    /*
    *   bounding box for this decal
    *   ** need to use tile position for calculation **
    */
    BoundingOrientedBox DecalOBB;

    /*
     *   Decal to world space matrix without scale and translate.
     */
    Float4x4 DecalToWorldMatrixRotationOnly;

    /*
    *   Tile position of ModelToWorldTransform and OBB.
    */
    Float3 TilePosition = {0, 0, 0};

    /*
    *   Fading properties
    */
    float FadeScreenSize = 0.01f;
    float FadeStartDelay = 0.0f;
    float FadeDuration = 0.0f;
    float FadeInDuration = 0.0f;
    float FadeInStartDelay = 0.0f;

    /*
    *   Sort order
    */
    UInt32 SortOrder;

    /*
    *   Parent Entity
    */
    ecs::EntityID ParentEntityID;

    friend class DecalSystemR;

private:
    /*
    *   Hold decal_render_node ptr here
    */
    std::shared_ptr<class DecalRenderNode> mRenderNode;
};

using RenderDecalComponentHandle = ecs::ComponentHandle<DecalComponentR>;
using RenderDecalComponentReader = ecs::ScopedComponentRead<DecalComponentR>;
using RenderDecalComponentWriter = ecs::ScopedComponentWrite<DecalComponentR>;
using DecalList = FrameVector<RenderDecalComponentHandle>;

class DecalSystemR : public RenderSystemBase
{
    CEMetaInternal(Reflect)
public:
    RENDER_ENGINE_API static DecalSystemR* CreateInstance();

    virtual void Release() override;

    RENDER_ENGINE_API void SetDecalEnable(ecs::EntityID entity, bool enable);
    RENDER_ENGINE_API void SetDecalMaterial(ecs::EntityID entity, MaterialR* material);
    RENDER_ENGINE_API void SetDecalTransform(ecs::EntityID entity, const Float4x4& ModelToWorld, const Float3& TilePosition);
    RENDER_ENGINE_API void SetDecalFadingParameters(ecs::EntityID entity, float FadeScreenSize, float FadeStartDelay, float FadeDuration, float FadeInDuration, float FadeInStartDelay);
    RENDER_ENGINE_API void SetDecalSortOrder(ecs::EntityID entity, UInt32 order);
    RENDER_ENGINE_API void SetDecalWireFrameShow(ecs::EntityID entity, bool isShow);
    RENDER_ENGINE_API void SetDecalParentID(ecs::EntityID entity, ecs::EntityID ParentEntity);
    CEFunction(Reflect)
    virtual void OnBuildUpdateTasks(FrameParam* frameParam) override;

protected:
    virtual void OnFirstUpdate(FrameParam* frameParam) override;

    virtual void OnEndFrame(FrameParam* frameParam) override;

    RENDER_ENGINE_API virtual void NotifyEvent(const SystemEventBase& event, UInt32& flag) override;

    DecalSystemR();

    ~DecalSystemR();

    /*
    *   Draw selected decal wire frame gizmos
    */
    void DrawDecalMeshWireFrame(FrameAllocator* Allocator);

    /*
    *   Build visible decal list and sort by SortOrder
    *   Need valid mActivatedDecalList and mSortedVisibleDecalList
    */
    DecalList* CollectAndSortDecals(const RenderCamera* camera);

public:
    /*
    *   An interface for judge whether exists decals
    *   return true if activated decals exists
    */
    bool ShouldRenderDecal() const;

    /*
    *   Collect and sort visible decals and render decals to dbuffers
    */
    void RenderDecalBuffers(REDTextureView* depthStencilView, REDTextureView* depthViewAfterMsaa, const RenderCamera* camera, const std::array<REDTextureView*, 3>& DBufferViews, REDTextureView* sceneColorView);

private:
    /*
    *   Fill Object level params in property set, like world matrix
    */
    PropertySet SetupPropertySet(const RenderDecalComponentReader& InDecalCompH);

    /*
     *   fill or update decal render node
     */
    void SetupRenderNode(const RenderDecalComponentReader& InDecalCompH);

    UInt32 AssemblePriority(const RenderDecalComponentReader& InDecalCompH);

    void PrepareUnitCubeGeometry();

protected:
    friend class DecalSystemG;

    GeometryPacket* mUnitCubeGeoPack;
    RenderGeometry mUnitCubeGeometry;

    bool bShouldRenderDecal = true;

    std::unordered_map<ecs::EntityID, PrimitiveData> mShowingDecalMeshWireFramePrimitives;
    InputLayoutDesc mDecalMeshInputLayoutDesc;
};


}   // namespace cross
