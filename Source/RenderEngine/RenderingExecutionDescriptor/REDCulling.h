#pragma once
#include "CrossBase/PlatformDefs.h"
#include "CrossBase/String/NameID.h"
#include "CrossBase/Threading/ThreadSafeQueue.h"
#include "CECommon/Allocator/FrameAllocator.h"
#include "RenderEngine/RenderMaterial.h"
#include "RenderEngine/RenderWorld.h"
#include "RenderEngine/RenderCamera.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDDrawUnitList.h"
#include "RenderEngine/LightCulling.h"
#include <optional>
#include <deque>
#include <map>

namespace cross {
struct RenderNode;

enum class REDObjectType : UInt8
{
    Static,
    Dynamic,
    All,
};

enum class REDCullingSubjectType
{
    Camera,
    AABB
};

struct REDCulling;
struct CullingResultBase;
class ModelCullingOctreeData;

struct REDCullingDesc
{
    RenderWorld* World;
    // Camera for culling
    RenderCamera* Camera;
    // Temp, refactor with layer
    REDObjectType ObjectMask = REDObjectType::All;
};

struct REDCullingResultDesc : public REDCullingDesc
{
    // Subject bounding type (camera or AABB)
    REDCullingSubjectType CullingSubjectType = REDCullingSubjectType::Camera;
    BoundingBox* CullingBound = nullptr;

    bool OnlyShadowCasters = false;

    REDCullingResultDesc(const REDCullingDesc& desc, bool onlyShadowCaster = false)
        : REDCullingDesc(desc) 
        , CullingSubjectType(REDCullingSubjectType::Camera)
        , OnlyShadowCasters(onlyShadowCaster)
    {}
    REDCullingResultDesc(const REDCullingDesc& desc, BoundingBox* bounding, bool onlyShadowCaster = false)
        : REDCullingDesc(desc) 
        , CullingSubjectType(REDCullingSubjectType::AABB)
        , OnlyShadowCasters(onlyShadowCaster)
    {
        CullingBound = bounding;
    }
};

struct CEMeta(Editor, Cli, Puerts) RENDER_ENGINE_API EntityDistanceCulling
{
    CE_Virtual_Serialize_Deserialize

    // if this is set > 0.0, then entity is culled if dist(camera, bboxcenter(entity)) <minCullingDistance;
    CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "if entity distance less than this setting, it will not be drawn"))
    float minCullingDistance = 0.0f;
    // if this is set > 0.0, then entity is culled if dist(camera,  bboxcenter(entity)) > maxCullingDistance;
    CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "if entity distance larger than this setting, it will not be drawn"))
    float maxCullingDistance = 0.0f;

    bool IsCulled(float distance) const
    {
        if (minCullingDistance != 0.0 && distance < minCullingDistance)
        {
            return true;
        }

        if (maxCullingDistance != 0.0 && distance > maxCullingDistance)
        {
            return true;
        }

        return false;
    }
};

struct REDCullingResult
{
    friend struct REDCulling;
    friend struct REDDrawUnitList;

    REDCullingResult(const REDCullingResultDesc& desc, REDCulling* culling, UInt64 frameID);

    auto& GetDesc() const { return mDesc; }
    threading::TaskEventPtr GetCullingTask() { return mTask; }

    REDDrawUnitList* GenerateDrawUnitList(const REDDrawUnitsDesc& desc, REDDrawUnitStatistic* statistic = nullptr);

    struct EntityData
    {
        ecs::EntityID entity;
        Float3 boundingSphereCenter;   // in camera tile space
        float boundingSphereRadius;
        Float3 worldPosition;      // in camera tile space
        BoundingBox boundingBox;   // world space
        const RenderNode* renderNode;
#ifdef ENABLE_SCENE_OCTREE
        FrameStdSet<SInt32>* invisibleSubModelSet;
#endif        
    };

    void ReleaseFrameData();

    ~REDCullingResult();

        struct DrawUnitDescHasher
    {
        bool operator()(const REDDrawUnitsDesc& a, const REDDrawUnitsDesc& b) const;
        size_t operator()(const REDDrawUnitsDesc& desc) const;
    };

private:
    REDCullingResultDesc mDesc;
    REDCulling* mCulling;



    std::unordered_map<REDDrawUnitsDesc, std::pair<REDDrawUnitList*, UInt64>, DrawUnitDescHasher, DrawUnitDescHasher> mDrawUnitLists;
    bool mReleased = false;
    threading::TaskEventPtr mTask =nullptr;
    FrameVector<FrameVector<EntityData>*>* mBlockEntityData = nullptr;
    FrameVector<EntityData>* mEntityData = nullptr;
    FrameVector<EntityData>* mHeavyEntity = nullptr;

    UInt64 mFrameID = static_cast<UInt64>(-1);

    std::mutex mSharedDrawunitMutex;

    void Dispatch();
};

struct RenderNode;

struct REDDrawUnitCollector
{
#define DEFAULT_VALUE(X) X

#define ISOLATED_DRAWUNIT_PARAMS \
    UInt16 RenderGroup, \
    const RenderGeometry* Geometry, \
    MaterialR* Material, \
    REDDrawUnitFlag Flags DEFAULT_VALUE(= {}), \
    UInt32 LODIndex DEFAULT_VALUE(= 0), \
    UInt32 StateBucketID DEFAULT_VALUE(= 0), \
    const void* CustumData DEFAULT_VALUE(= nullptr), \
    const PropertySet* ObjectProperties DEFAULT_VALUE(= nullptr), \
    const TRSRenderMatrixType* WorldTransform DEFAULT_VALUE(= nullptr)

#define BATCHABLE_DRAWUNIT_PARAMS \
    UInt16 RenderGroup, \
    const RenderGeometry* Geometry, \
    MaterialR* Material, \
    UInt32 StateBucketID, \
    const GPUSceneBufferView* ObjectDataBufferView, \
    const GPUSceneBufferView* PrimitiveDataBufferView DEFAULT_VALUE(= nullptr), \
    REDDrawUnitFlag Flags DEFAULT_VALUE(= {}), \
    const void* CustumData DEFAULT_VALUE(= nullptr), \
    const PropertySet* ObjectProperties DEFAULT_VALUE(= nullptr)

    // use add drawunit function to reduce confusing
    void AddOpaqueIsolatedDrawUnit(ISOLATED_DRAWUNIT_PARAMS);

    REDDrawUnit& AddOpaqueBatchableDrawUnit(BATCHABLE_DRAWUNIT_PARAMS);

    void AddTransparentIsolatedDrawUnit(float Distance, ISOLATED_DRAWUNIT_PARAMS);

    REDDrawUnit& AddTransparentBatchableDrawUnit(float Distance, BATCHABLE_DRAWUNIT_PARAMS);

    void AddCustumPriorityIsolatedDrawUnit(UInt32 CustomPriority, ISOLATED_DRAWUNIT_PARAMS);

    REDDrawUnit& AddCustumPriorityBatchableDrawUnit(UInt32 CustomPriority, BATCHABLE_DRAWUNIT_PARAMS);

    auto GetFrameAllocator() const { return mFrameAlloc; }

#undef DEFAULT_VALUE

private:
    const REDCullingResult::EntityData* mEntityData;
    const RenderNode* mRenderNode;
    std::pmr::vector<REDDrawUnit>& mDrawUnits;
    FrameAllocator* mFrameAlloc;

    REDDrawUnitCollector(std::pmr::vector<REDDrawUnit>& drawUnits, FrameAllocator* frameAlloc)
        : mDrawUnits{drawUnits}
        , mFrameAlloc{frameAlloc}
    {}

    REDDrawUnitCollector(std::pmr::vector<REDDrawUnit>& drawUnits):mDrawUnits(drawUnits)
    {}

    void SetCurrentEntityData(const REDCullingResult::EntityData* entityData, const RenderNode* renderNode)
    {
        mEntityData = entityData;
        mRenderNode = renderNode;
    }


    friend struct REDCullingResult;
    friend struct REDDrawUnitList;
};




struct REDCulling
{

    const unsigned int MULTI_FRAME_CACHE = 3;



    struct Hasher
    {
        bool operator()(const REDCullingResultDesc& a, const REDCullingResultDesc& b) const;
        size_t operator()(const REDCullingResultDesc& desc) const;

        bool operator()(const LightCullingDesc& a, const LightCullingDesc& b) const;
        size_t operator()(const LightCullingDesc& desc) const;
    };
    std::mutex mCullingResultMutex;
    std::mutex mLightCullingResultMutex;
    std::unordered_map<REDCullingResultDesc, std::shared_ptr<REDCullingResult>, Hasher, Hasher> mCullingResults;
    std::unordered_map<LightCullingDesc, LightCullingResult, Hasher, Hasher> mLightCullingResults;

    ThreadSafeQueue<REDDrawUnitList*> mReadyDrawUnitLists;

    REDCullingResult* Dispatch(const REDCullingResultDesc& desc, uint64_t frameID);
    LightCullingResult* DispatchLightCulling(const LightCullingDesc& desc, uint64_t frameID);

    void WaitForCompletion();

    void Clear(UInt64 frameID);

    FrameAllocator* GetAllocator();

    static UInt32 CalculateDrawUnitStateBucketID(const RenderGeometry* geometry, const MaterialR* material, const PropertySet& properties, NameID passName, REDDrawUnitFlag flag, UInt32 lodIndex);
};

}   // namespace cross