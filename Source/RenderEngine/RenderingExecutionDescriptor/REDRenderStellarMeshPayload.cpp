#include "REDRenderStellarMeshPayload.h"
#include "RenderEngine.h"
#include "RendererSystemR.h"
#include "REDPass.h"

void cross::REDRenderStellarMeshPayload::OnCompile(REDPass* pass)
{
    QUICK_SCOPED_CPU_TIMING(__FUNCTION__);

    auto* rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto* fp = EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam();
    auto* fa = fp->GetFrameAllocator();

    mGraphicsDrawUnits = std::pmr::vector<REDGraphicsDrawUnit>(rdrSys->GetRenderingExecutionDescriptor()->GetREDFrameAllocator());
    mGraphicsDrawUnits.resize(mDesc.NumRasterPipelines);

    UInt32 drawUnitCount = mDesc.NumRasterPipelines;
    UInt32 batchSize = 32u;
    UInt32 batchCount = (drawUnitCount + batchSize - 1) / batchSize;

    threading::ParallelFor(batchCount, [=](UInt32 batchIndex) {
        QUICK_SCOPED_CPU_TIMING("REDRenderStellarMeshPayload::OnCompile");

        auto drawUnitStart = batchIndex * batchSize;
        auto drawUnitEnd = std::min((batchIndex + 1) * batchSize, drawUnitCount);

        for (auto drawUnitIndex = drawUnitStart; drawUnitIndex < drawUnitEnd; drawUnitIndex++)
        {
            auto& drawUnit = mDesc.RasterPipelines[drawUnitIndex];
            auto& graphicsDrawUnit = mGraphicsDrawUnits[drawUnitIndex];

            auto mtlState = drawUnit.mMaterial->GetMaterialRenderState(mDesc.PassName, nullptr, &pass->GetContext());

            const void* shaderConst = GenerateShaderConstData(fa, mtlState, &pass->GetContext(), nullptr);

            Assert(!mtlState.mProgram->InstanceDataLayout);

            InputLayoutDesc inputLayout;

            NGIGraphicsPipelineStateDesc pipelineStateDesc{
                pass->GetRenderPass(),
                pass->GetSubpass(),
                mtlState.mProgram->GUID,
                &mtlState.mProgram->GraphicsProgramDesc,
                mtlState.mProgram->PipelineLayout,
                inputLayout.GetHash().GetHash(),
                &inputLayout,
                PrimitiveTopology::TriangleList,
                *mtlState.mRaterizationState,
                *mtlState.mBlendState,
                *mtlState.mDepthStencilState,
                mtlState.mProgram->ShaderConstantLayout ? mtlState.mProgram->ShaderConstantLayout->ByteSize : 0,
                shaderConst,
            };

            graphicsDrawUnit.mProgram = mtlState.mProgram;
            graphicsDrawUnit.mPipelineState = rdrSys->GetGraphicsPipelineStatePool()->AllocateGraphicsPipelineState(pipelineStateDesc);

            graphicsDrawUnit.mResourceGroupPAS =
                pass->GetContext().GetResourceGroup(mtlState.mProgram->ResourceGroupLayouts[ShaderParamGroup_Pass], mtlState.mProgram->PipelineLayout->GetDesc().ResourceGroupLayouts[ShaderParamGroup_Pass]);

            graphicsDrawUnit.mResourceGroupMTL = mtlState.GetResourceBinding();
        }
    });
}

void cross::REDRenderStellarMeshPayload::OnExecute(REDPass* pass, NGICommandList* cmdList) const
{
    QUICK_SCOPED_CPU_TIMING(__FUNCTION__);

    UInt32 drawUnitsCount = static_cast<UInt32>(mGraphicsDrawUnits.size());
    constexpr UInt32 desiredBatchSize = 64;

    const UInt32 numWorkers = threading::TaskSystem::GetNumWorkerThreadsForTask();
    UInt32 numTasks = (drawUnitsCount + (desiredBatchSize - 1)) / desiredBatchSize;
    UInt32 batchSize = desiredBatchSize;
    UInt32 remainder = 0u;
    if (static_cast<UInt32>(numWorkers) < numTasks)
    {
        numTasks = static_cast<UInt32>(numWorkers);
        batchSize = drawUnitsCount / static_cast<UInt32>(numWorkers);
        remainder = drawUnitsCount % static_cast<UInt32>(numWorkers);
    }

    auto width = pass->mRenderPass->mFramebuffer->GetDesc().Width;
    auto height = pass->mRenderPass->mFramebuffer->GetDesc().Height;
    NGIViewport viewport{0, 0, static_cast<float>(width), static_cast<float>(height), 0, 1};
    NGIScissor scissor{0, 0, width, height};

    auto RecordDrawCommands = [&](NGIBundleCommandList* cmdList, UInt32 offset, UInt32 count) {
        SCOPED_CPU_TIMING(GroupRendering, "RecordDrawCommands");

        for (UInt32 j = offset; j < offset + count; j++)
        {
            const auto& drawUnit = mDesc.RasterPipelines[j];
            const auto& graphicsDrawUnit = mGraphicsDrawUnits[j];

            cmdList->SetGraphicsPipelineState(graphicsDrawUnit.mPipelineState);
            if (graphicsDrawUnit.mResourceGroupPAS)
            {
                cmdList->SetGraphicsResourceGroup(ShaderParamGroup_Pass, graphicsDrawUnit.mResourceGroupPAS);
            }
            if (graphicsDrawUnit.mResourceGroupMTL)
            {
                cmdList->SetGraphicsResourceGroup(ShaderParamGroup_Material, graphicsDrawUnit.mResourceGroupMTL);
            }
            if (graphicsDrawUnit.mResourceGroupOBJ)
            {
                cmdList->SetGraphicsResourceGroup(ShaderParamGroup_Model, graphicsDrawUnit.mResourceGroupOBJ);
            }

            if (graphicsDrawUnit.mPipelineState->GetDesc().DepthStencilState.EnableStencil)
            {
                cmdList->SetStencilRef(graphicsDrawUnit.mDynamicState.StencilReference);
            }

            if (graphicsDrawUnit.mPipelineState->GetDesc().PipelineLayout->GetDesc().ResourceGroupLayouts[ShaderParamGroup_Bindless]->GetDesc().ResourceCount > 0)
            {
                cmdList->SetGraphicsResourceGroup(ShaderParamGroup_Bindless, gBindlessResourceManager.GetBindlessResourceGroup().get());
            }

            cmdList->SetVertexBuffers(0, nullptr, nullptr);

            constexpr auto CmdStride = sizeof(UInt32) * 4;

            cmdList->DrawIndirect(mDesc.IndirectBuffer->GetNativeBuffer(), j * CmdStride, 1, CmdStride);
        }
    };

    std::vector<NGIBundleCommandList*> bundleCmdLists;
    bundleCmdLists.resize(numTasks);
    cmdList->GetCommandQueue()->AllocateBundleCommandLists(numTasks, bundleCmdLists.data());
    cmdList->ForkBundleCommandLists(numTasks, bundleCmdLists.data());

    threading::ParallelFor(numTasks, [&](UInt32 taskIndex) {
        QUICK_SCOPED_CPU_TIMING("REDRenderRawDrawUnitsPayload::OnExecute");

        NGIBundleCommandList* bundleCmdList = bundleCmdLists[taskIndex];

        UInt32 prevRemainderCount = std::min(remainder, taskIndex);
        UInt32 drawUnitStart = taskIndex * batchSize + prevRemainderCount;
        UInt32 drawUnitEnd = std::min(drawUnitStart + batchSize + (taskIndex < remainder ? 1u : 0u), drawUnitsCount);
        UInt32 drawUnitCount = drawUnitEnd - drawUnitStart;

        bundleCmdList->Begin();
        bundleCmdList->SetViewports(1, &viewport);
        bundleCmdList->SetScissors(1, &scissor);
        RecordDrawCommands(bundleCmdList, drawUnitStart, drawUnitCount);
        bundleCmdList->End();
    });
}
