#pragma once
#include "NativeGraphicsInterface/NGI.h"
#include "RenderEngine/RenderMaterial.h"
#include "RenderEngine/RenderCamera.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDCommon.h"
#include "CECommon/Allocator/FrameAllocator.h"
#include "CECommon/Common/TRS.h"
#include "RenderEngine/RenderGeometry.h"
#include "CrossBase/Template/Bitfield.hpp"
namespace cross
{
struct REDCullingResult;
struct REDCulling;
struct GPUSceneBufferView;

union REDDrawUnitSortKey
{
    UInt64 Value;
    BitField<16, 32> StateBucketID;
    BitField<16, 32> Distance;
    BitField<16, 32> CustomPriority;
    BitField<48, 16> RenderGroup;
};

struct ObjectPayloadData
{
    SInt32 cullingGUID;
    UInt32 indirectArgIndex;
    SInt32 objectIndex;
};

struct ObjectPayloadData2
{
    SInt32 cullingGUID;
    SInt32 objectIndex;
    UInt32 objectCount;
    UInt32 indirectArgIndex;
};

// Flags which would affect dynamic batching
enum class REDDrawUnitFlag : UInt32
{
    ReceiveDecal = 0x1,
    ReverseFaceOrder = 0x2,
    Isolated = 0x4,
};

ENUM_CLASS_FLAGS(REDDrawUnitFlag);

struct REDDrawUnit
{
    RenderNodeType mType;

    REDDrawUnitSortKey mKey;
    // can be cached
    UInt32 mStateBucketID;

    const void* mCustumData;
    REDDrawUnitFlag mFlags;

    const RenderGeometry* mGeometry;
    MaterialR* mMaterial;
    const PropertySet* mObjectProperties;

    // Isolated DrawUnit
    UInt32 mLODIndex;
    const TRSRenderMatrixType* mWorldTransform;

    // Batchable DrawUnit
    const GPUSceneBufferView* mTypedObjectDataBufferView;
    const GPUSceneBufferView* mTypedPrimitiveDataBufferView;

    struct Run
    {
        UInt32 mInstanceCount = 1;
        SInt32 mCullingGUIDStart = -1;
        SInt32 mObjectIndexStart = -1;
    };

    Run mSingleRun;

    std::optional<std::pmr::vector<Run>*> mRuns;


    auto GetInstanceCount() const
    {
        if (!mTypedObjectDataBufferView && mType != RenderNodeType::Foliage)
        {
            return 1u;
        }
        else if (mRuns.has_value())
        {
            return std::accumulate((*mRuns.value()).begin(), (*mRuns.value()).end(), 0U, [](UInt32 sum, const Run& run) { return sum + run.mInstanceCount; });
        }
        else
        {
            return mSingleRun.mInstanceCount;
        }
    }

    UInt32 mObjectIndexOffset = 0;

    // indirect count == 0 means draw indirect was disabled
    mutable UInt32 mIndirectCount = 0;
    mutable UInt32 mIndirectBufferOffset = 0;
    mutable UInt32 mIndirectStride = 0;

    // max draw count == 0 means draw indirect count was disabled
    mutable UInt32 mMaxIndirectDrawCount = 0;
    mutable UInt32 mCountBufferOffset = 0;

    mutable bool mFeedBackVisible = true;

    bool MatchesForDynamicInstancing(const NameID& passName, const REDDrawUnit& other);
};

// pass specified draw unit content
struct REDGraphicsDrawUnit
{
    const REDDrawUnit* mDrawUnit = nullptr;
    const resource::Shader::ProgramDesc* mProgram = nullptr;

    NGIGraphicsPipelineState* mPipelineState = nullptr;
    NGIResourceGroup* mResourceGroupPAS = nullptr;
    NGIResourceGroup* mResourceGroupOBJ = nullptr;
    NGIResourceGroup* mResourceGroupMTL = nullptr;
    NGIDynamicStateDesc mDynamicState;
};

struct REDDrawUnitsDesc
{
    NameID TagName;
    UInt16 RenderGroupLow;
    UInt16 RenderGroupHigh;
    RenderEffectTag RenderEffectMask;
    RenderNodeType RenderNodeTypeMask;
    MaterialR* OverrideMaterial;
    //for LOD Selection. If nullopt, use Camera's View for lod selection
    const RenderCamera* LODSelectionCamera;
};

struct REDDrawUnitStatistic
{
    SizeType DrawUnitCount;
    SizeType MergedDrawUnitCount;
    SizeType PrimitiveCount;
};

struct REDDrawUnitList
{
    friend struct REDPass;
    friend struct REDRenderDrawUnitsPayload;
    friend struct RenderingExecutionDescriptor;
    friend struct REDCulling;
    friend struct REDCullingResult;

    static constexpr UInt32 LARGE_INSTANCE_PAYLOAD_THRESHOLD = 1024;

    struct PrevFrame
    {
        std::pmr::vector<UInt32> mDrawUnitUniqueID;
        std::pmr::vector<UInt8> mReadbackData;
        // uniqueKey to drawUnit index;
        std::unordered_map<UInt32, std::vector<UInt32>> mIndexMap;
    };

    REDDrawUnitList(const REDCullingResult& cullingResult, const REDDrawUnitsDesc& desc, REDDrawUnitStatistic* statistic, FrameAllocator* FrameAlloc);

    REDDrawUnitList(const REDCullingResult& cullingResult, const REDDrawUnitsDesc& desc, REDDrawUnitStatistic* statistic, std::pmr::vector<PrevFrame*>&& prevReadBack, FrameAllocator* FrameAlloc);


    ~REDDrawUnitList();

    const REDCullingResult& GetCullingResult() const
    {
        return mCullingResult;
    }

    auto& GetDesc() const 
    {
        return mDesc;
    }

    const auto* GetDrawUnits() const
    {
        return &mDrawUnits;
    }

    // REDBuffer must be set by render pipeline assemble function
    // NGIBuffer can be set by render pipelin assemble function or by RED stage callback
    template<typename T>
    void SetIndirectBuffer(T indirectBuffer)
    {
        mIndirectBuffer = indirectBuffer;
    }

    // REDBuffer must be set by render pipeline assemble function
    // NGIBuffer can be set by render pipelin assemble function or by RED stage callback
    template<typename T>
    void SetCountBuffer(T countBuffer)
    {
        mCountBuffer = countBuffer;
    }

    template<typename T>
    void SetObjectIndexOffsetsBuffer(T instanceIDOffsetsBuffer)
    {
        mOverrideObjectIndexOffsetsBuffer = instanceIDOffsetsBuffer;
    }

    template<typename T>
    auto GetIndirectBuffer() const { return mIndirectBuffer.GetBuffer<T>(); }

    template<typename T>
    auto GetCountBuffer() const { return mCountBuffer.GetBuffer<T>(); }

    template<typename T>
    auto GetObjectIndexOffsetsBuffer() const { return mOverrideObjectIndexOffsetsBuffer.GetBuffer<T>(); }

    // default ObjectIndexOffsetsBufferView genraeted by auto instancing
    auto GetDefaultObjectIndexOffsetsBufferView() const { return mObjectIndexOffsetsBufferView; }

    auto GetDefaultObjectIndexBufferView() const
    {
        return mObjectIndexBufferView;
    }

    auto& GetObjectPayloadDatas() const { return mObjectPaylodDatas; }
    auto& GetObjectPayloadDatas2() const { return mObjectPayloadDatas2; }
    auto GetObjectPayloadDrawUnitIndex(UInt32 payloadIndex) const { return mObjectPayloadDrawUnitIndex[payloadIndex]; }
    auto GetRangePayloadOffset() const { return mRangePayloadOffset; }
    auto GetLargePayloadOffset() const { return mLargePayloadOffset; }


    // this is a function use to get unique id for drawUnit
    // 0.The stateBucket id is usually suitable but failed in some cases;
    // 1.The foliage case, where each LOD is a unique drawUnit, but they in effect belong to the same draw. they should return same uniqueKey and maintain in list
    // 2.The meshes that should be instanced (i.e. the same bucketid) but failed to instance due to some incorrect material setup or version
    typedef std::function<UInt32(const REDDrawUnit&)> UniqueDrawUnitKeyGenerator;

    // store drawUnit info into frame ring buffer
    // must culled after OnCulling
    bool RecordDrawUnitMetaInfo(UInt64 id, UniqueDrawUnitKeyGenerator keyGenerator);

    PrevFrame* GetReadBackBuffer(UInt64 id);

    struct DrawUnitListContainer : std::pmr::vector<REDDrawUnit>
    {
        using std::pmr::vector<REDDrawUnit>::vector;

        template<typename T>
        decltype(auto) At(T i) const { return at(i); }

        auto IsEmpty()const { return empty(); }

        UInt32 GetSize() const { return static_cast<UInt32>(size()); }
    };

    void ReleaseFrameData();

private:
    const REDCullingResult& mCullingResult;
    REDDrawUnitsDesc mDesc;

    FrameAllocator* mFrameAlloc;

    void Dispatch();
    std::pmr::vector<std::pmr::vector<REDDrawUnit>> mBatchDrawUnits;
    DrawUnitListContainer mDrawUnits;

    bool mVisited = false;
    threading::TaskEventPtr mTask = nullptr;

    // for AutoInstancing
    std::pmr::vector<ObjectPayloadData> mObjectPaylodDatas;
    std::pmr::vector<ObjectPayloadData2> mObjectPayloadDatas2;
    UInt32 mRangePayloadOffset = 0;
    UInt32 mLargePayloadOffset = 0;
    std::pmr::vector<UInt32> mObjectPayloadDrawUnitIndex;
    std::pmr::vector<SInt32> mObjectIndexList;

    struct BufferType : public std::variant<REDBuffer*, NGIBuffer*>
    {
        using std::variant<REDBuffer*, NGIBuffer*>::variant;

        template<typename T>
        T GetBuffer() const;
    };

    BufferType mIndirectBuffer;
    BufferType mCountBuffer;
    BufferType mOverrideObjectIndexOffsetsBuffer;

    NGIBuffer* mObjectIndexOffsetsBuffer = nullptr;
    SizeType mObjectIndexOffsetsBufferOffset = 0;
    NGIBufferView* mObjectIndexOffsetsBufferView = nullptr;

    NGIBuffer* mObjectIndexBuffer = nullptr;
    SizeType mObjectIndexBufferOffset = 0;
    NGIBufferView* mObjectIndexBufferView = nullptr;

    REDDrawUnitStatistic* mStatistic = nullptr;


    bool mRelease = false;
    std::pmr::vector<PrevFrame*> mReadBackFrames;

public:
    auto& GetModifiableDrawUnits() { return mDrawUnits; }
// dirty hack for clang strict rules
#if !CROSSENGINE_WIN
public:
#endif

    void AutoInstancing();
};

template<typename T>
T REDDrawUnitList::BufferType::GetBuffer() const
{
    if constexpr (std::is_same_v<T, REDBuffer*>)
    {
        if (std::holds_alternative<REDBuffer*>(*this))
        {
            return std::get<0>(*this);
        }
        else
        {
            return nullptr;
        }
    }
    else if constexpr (std::is_same_v<T, NGIBuffer*>)
    {
        if (std::holds_alternative<REDBuffer*>(*this))
        {
            return std::get<0>(*this) ? std::get<0>(*this)->GetNativeBuffer() : nullptr;
        }
        else
        {
            return std::get<1>(*this);
        }
    }
    else
    {
        Assert(false);
    }
}

std::tuple<SizeType, SizeType> Split2Batches(SizeType count, SizeType batchCount, SizeType batchIndex);

}