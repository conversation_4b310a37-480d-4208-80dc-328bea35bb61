#pragma once
#include "CrossBase/Platform/PlatformTypes.h"
#include "Resource/IResourceInterface.h"

#include <string>

namespace cross {

#define CONTINUE_IF(x) do { if (x) continue; } while(0)

/**
 * @brief Thread-Safe Continuous Index Allocator
 *
 * A thread-safe version of the index allocator that can be safely used in multi-threaded environments.
 * Provides sequential index allocation with index reuse for different types of objects.
 */
class ThreadSafeIndexAllocator
{
public:
    /**
     * @brief Constructor
     * @param name Name of the index allocator, used for debugging
     */
    explicit ThreadSafeIndexAllocator(const std::string& name = "")
        : mName(name)
    {
    }

    /**
     * @brief Allocate a block of one or more consecutive indices in a thread-safe manner.
     *
     * This function attempts to allocate `count` consecutive indices. It first searches the free
     * range set for a previously released block that can satisfy the request. If such a range
     * is found, a subrange is carved out and returned. If no suitable free range is found,
     * the indices are allocated from the top of the current allocation range (`mNextIndex`).
     *
     * This allows efficient reuse of previously released indices and minimizes fragmentation.
     *
     * Thread-safe: This function acquires a mutex lock to ensure safe concurrent access.
     *
     * @param count The number of consecutive indices to allocate. Must be >= 1.
     * @return The starting index of the allocated block.
     */
    UInt32 Allocate(UInt32 count = 1)
    {
        std::lock_guard<std::mutex> lock(mMutex);
        
        for (auto it = mFreeRanges.begin(); it != mFreeRanges.end(); ++it)
        {
            UInt32 rangeStart = it->first;
            UInt32 rangeEnd = it->second;

            if (rangeEnd - rangeStart >= count)
            {
                UInt32 allocStart = rangeStart;
                UInt32 newStart = rangeStart + count;
                
                mFreeRanges.erase(it);
                if (newStart < rangeEnd)
                    mFreeRanges.insert({ newStart, rangeEnd });

                return allocStart;
            }
        }
        
        UInt32 allocStart = mNextIndex;
        mNextIndex += count;
        return allocStart;
    }

    /**
     * @brief Release a block of consecutive indices for reuse.
     *
     * This function releases a range of indices starting from `start` and spanning `count` elements.
     * It automatically merges adjacent free ranges to reduce fragmentation.
     *
     * The range is specified as [start, start + count), and it is inserted into the internal
     * free list. If any existing free range is adjacent (touches either the beginning or the end
     * of this range), they are merged into a single, larger free range.
     *
     * Thread-safe: This function acquires a mutex lock to ensure safe concurrent access.
     *
     * @param start The starting index of the block to be released.
     * @param count The number of  indices to release.
     */
    void Release(UInt32 start, UInt32 count = 1)
    {
        std::lock_guard<std::mutex> lock(mMutex);

        UInt32 end = start + count;
        auto it = mFreeRanges.lower_bound({ start, 0 });
        if (it != mFreeRanges.begin())
        {
            auto prev = std::prev(it);
            if (prev->second >= start)
            {
                start = std::min(start, prev->first);
                end   = std::max(end,   prev->second);
                mFreeRanges.erase(prev);
            }
        }
        
        while (it != mFreeRanges.end() && it->first <= end)
        {
            end = std::max(end, it->second);
            it = mFreeRanges.erase(it);
        }

        mFreeRanges.insert({ start, end });
    }
    
    /**
     * @brief Reset the allocator, clearing all allocated indices
     */
    void Reset()
    {
        std::lock_guard<std::mutex> lock(mMutex);
        mNextIndex = 0;
        mFreeRanges.clear();
    }

    /**
     * @brief Get the maximum allocated index value
     * @return The maximum index value
     */
    UInt32 GetMaxAllocatedIndex() const
    {
        std::lock_guard<std::mutex> lock(mMutex);
        return mNextIndex;
    }

    /**
     * @brief Get the current number of allocated indices
     * @return The number of allocated indices
     */
    UInt32 GetAllocatedCount() const
    {
        std::lock_guard<std::mutex> lock(mMutex);
        UInt32 freeCount = 0;
        for (const auto& range : mFreeRanges) 
        {
            freeCount += (range.second - range.first);
        }
        return mNextIndex - freeCount;
    }

    /**
     * @brief Get the allocator name
     * @return The allocator name
     */
    const std::string& GetName() const
    {
        return mName;
    }

private:
    std::string mName;           // Allocator name, used for debugging
    UInt32 mNextIndex = 0;       // Next index to be allocated
    std::set<std::pair<UInt32, UInt32>> mFreeRanges;  // [start, end)
    mutable std::mutex mMutex;   // Mutex for thread synchronization
};

/**
 * Manage GPU bindless resources
 *
 * Common usage:
 *     uint subMeshIndex = InstanceID() + GeometryIndex();
 *     SubInstanceData subInstanceData = SubInstanceBuffer[subMeshIndex];
 *
 *     Access Geometry
 *     uint geoIndex = subInstanceData.GeometryIndex;
 *     
 *     Vertex vert0 = VertexBuffers[geoIndex][PrimitiveIndex() * 3 + 0];
 *     uint index0 = IndexBuffers[geoIndex][PrimitiveIndex() * 3 + 0];
 *
 *     Access Material
 *     uint matIndex = subInstanceData.MaterialIndex;
 *     MaterialData matData = MaterialBuffer[matIndex];
 */
class BindlessResourceManager
{
public:
    BindlessResourceManager();

    ~BindlessResourceManager() = default;

    static BindlessResourceManager& Instance();

    /**
     * Gather GPU buffer for each sub mesh
     */
    void OnMeshBuild(IMeshR* mesh);

    UInt32 AllocateMeshIndex(UInt32 count);

    void ReleaseMeshIndex(UInt32 index, UInt32 count = 1);

    UInt32 AllocateBindlessBufferView(NGIBuffer* buffer, GraphicsFormat format, SizeType size, UInt32 offset = 0);

    void ReleaseBindlessBufferView(UInt32 index, UInt32 count = 1);

    /**
     * Release geometry index for each sub mesh
     */
    void OnMeshRelease(IMeshR* mesh);

    /**
     * Allocate material index and caches material data on cpu side
     */
    void AddMaterial(IMaterialR* material);

    /**
     * Allocate texture index and caches texture resource pointer on cpu side
     */
    void AddTexture(IGPUTexture* tex);
    
    void CreateBindlessResourceGroup(UInt32 maxBindlessResourceCount);
    
    auto GetBindlessResourceGroup() const
    {
        return mBindlessResourceGroup;
    }
    
    std::shared_ptr<ThreadSafeIndexAllocator> mBindlessBufferIndexAllocator;
    std::shared_ptr<NGIResourceGroupLayout> mBindlessResourceGroupLayout;
    std::shared_ptr<NGIResourceGroup> mBindlessResourceGroup;
    std::unordered_map<UInt32, std::unique_ptr<NGIBufferView>> mTrackedBindlessBufferViews;
    std::mutex mUpdateBindlessResourceGroupMutex;
    std::mutex mReleaseTrackedBindlessBufferViewMutex;

    std::shared_ptr<ThreadSafeIndexAllocator> mMeshIndexAllocator;
    std::shared_ptr<ThreadSafeIndexAllocator> mGeometryIndexAllocator;
    std::shared_ptr<ThreadSafeIndexAllocator> mMaterialIndexAllocator;
    std::shared_ptr<ThreadSafeIndexAllocator> mTextureIndexAllocator;
    
    std::shared_ptr<NGIStagingBuffer> mMaterialBuffer;
    std::shared_ptr<NGIBufferView> mMaterialBufferView;

    std::vector<IGPUTexture*> mTextureResources;

private:
    void ProcessVertexChannel(const VertexStreamLayout& streamLayout, const BufferStream* bufferStream, GeometryData& geoData);
};

}

#define gBindlessResourceManager cross::BindlessResourceManager::Instance()
