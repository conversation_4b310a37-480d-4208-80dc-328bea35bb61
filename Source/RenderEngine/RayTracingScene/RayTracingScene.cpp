#include "RayTracingScene.h"
#include "RenderEngine/EntityLifeCycleRenderDataSystemR.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/GPUScene/GPUSceneData.h"
#include "RenderEngine/AABBSystemR.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderEngine/RenderPropertySystemR.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipelineSetting.h"
#include "RenderEngine/RenderMaterial.h"
#include "Resource/Shader.h"
#include "RenderEngine/RenderMesh.h"
#include "RenderEngine/ModelSystemR.h"
#include <ranges>
#include "CECommon/Common/FrameTickManager.h"
#include "Runtime/GameWorld/EntityMetaSystem.h"
#include "CameraSystemR.h"

#ifdef NGI_ENABLE_RAY_TRACING
namespace cross {

RayTracingScene::~RayTracingScene()
{
    EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(std::move(mAccelStruct));
    EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(std::move(mSingleTriangleBLAS));
    EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(std::move(mSubInstanceBuffer));
    EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(std::move(mSubInstanceBufferView));
}

void RayTracingScene::Initialize(RenderWorld* renderWorld, RenderingExecutionDescriptor* red, GPUScene* gpuScene)
{
    mRenderWorld = renderWorld;
    mRED = red;
    mGPUScene = gpuScene;
}

void RayTracingScene::SetFFSRenderPipelineSetting(const FFSRenderPipelineSetting* renderPipelineSetting)
{
    mFFSRenderPipelineSetting = renderPipelineSetting;
}

#pragma optimize("", off)
void RayTracingScene::Update()
{
    QUICK_SCOPED_CPU_TIMING("RayTracingScene::Update");
    
    // Step 1: Set up prerequisites
    {
        mRenderSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        mModelSystem = mRenderWorld->GetRenderSystem<ModelSystemR>();
        mTransformSystem = mRenderWorld->GetRenderSystem<TransformSystemR>();
        mEntityLifeCycleRenderDataSystem = mRenderWorld->GetRenderSystem<EntityLifeCycleRenderDataSystemR>();
        mMainCameraTilePosition = Float3(0.f, 0.f, 0.f);
        auto mainCamera = mRenderWorld->GetRenderSystem<CameraSystemR>()->GetMainCamera();
        if (mainCamera != ecs::EntityID::InvalidHandle())
        {
            mMainCameraTilePosition = mTransformSystem->GetTilePosition(mainCamera);
        }
        mCmd = mRenderSystem->GetAccelStructCmd();
    }
    
    // Step 2: Handle events
    {
        // Step 2.1: Handle entity create
        for (const auto& data :  mEntityLifeCycleRenderDataSystem->GetRenderEntityCreateList())
        {
            // TODO(scolu): cull sky sphere since it has huge impact on performance
            if (IsEntityValidForRayTracing(data.entity))
            {
                AddUpdateEntity(data.entity);
            }
        }

        // TODO(scolu): Handle skinned mesh and rebuilt it's blas
        
        // Step 2.2: Handle entity change
        for (const auto& data : mEntityLifeCycleRenderDataSystem->GetRenderEntityChangeList())
        {
            bool isCreateEvent = data.type == RenderEntityChangeData::ChangeType::All;
            if (IsEntityValidForRayTracing(data.entity))
            {
                AddUpdateEntity(data.entity);
            }
        }
    }

    // TODO(scolu): Rebuild Skinned Mesh BLAS
    BuildBLASes();

    if (mIsInstanceDataDirty)
    {
        RefreshIndex();
        UpdateSubInstanceData();
        mIsInstanceDataDirty = false;
    }
    
    // TODO(scolu): Compact BLASes
    BuildTLAS();
    mFrameUpdateEntitySet.clear();
}
#pragma optimize("", on)

void RayTracingScene::PostUpdate() {}

void RayTracingScene::AddUpdateEntity(ecs::EntityID entity)
{
    std::lock_guard locker(mMutex);
    mFrameUpdateEntitySet.insert(entity);
    mIsInstanceDataDirty = true;
}

void RayTracingScene::RemoveEntity(ecs::EntityID entity)
{
    std::lock_guard locker(mMutex);
    mRayTracingInstances.erase(std::remove_if(mRayTracingInstances.begin(), mRayTracingInstances.end(),
        [&entity](const RayTracingInstance& instance) {
            return instance.entity == entity;
        }),
        mRayTracingInstances.end());
    
    mRayTracingInstancesSet.erase(entity);
    mIsInstanceDataDirty = true;
}

#pragma optimize("", off)
void RayTracingScene::SetBindlessResources(REDPass* pass) const
{
    pass->SetProperty(BuiltInProperty::ce_AccelerationStructure, GetTopLevelAccelStruct());
    pass->SetProperty(BuiltInProperty::ce_SubInstanceData, mSubInstanceBufferView.get());
}
#pragma optimize("", on)

void RayTracingScene::RefreshIndex()
{
    UInt32 instanceID = 0, subInstanceID = 0;
    for (auto& instance : mRayTracingInstances)
    {
        auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(instance.entity);
        RenderNode* renderNode = renderNodeComp.Read()->mRenderNode.get();

        if (renderNode->GetSubMeshCount() > 0)
        {
            instance.InstanceID = instanceID++;
            instance.SubInstanceID = subInstanceID;
            subInstanceID += renderNode->GetSubMeshCount();
        }
    }
}

void RayTracingScene::SetRayTracingSceneDirty(ecs::EntityID entity)
{
    std::lock_guard locker(mMutex);
    AddUpdateEntity(entity);
}

MeshR* RayTracingScene::GetRenderMesh(ecs::EntityID entity) const
{
    if (mRenderWorld->IsEntityAlive(entity))
    {
        return nullptr;
    }
    auto modelComp = mRenderWorld->GetComponent<ModelComponentR>(entity);
    if (!modelComp.IsValid())
    {
        LOG_ERROR("ModelComponentR is not available\n");
    }
    auto modelCompReader = modelComp.Read();
    constexpr UInt32 modelIndex = 0;
    const ModelComponentR::IndividualModel& model = mModelSystem->GetModel(modelCompReader, modelIndex);

    return mModelSystem->GetRenderMeshInternal(model);
}

#pragma optimize("", off)
bool RayTracingScene::IsEntityValidForRayTracing(ecs::EntityID entity) const
{
    if (!mRenderWorld->IsEntityAlive(entity) ||
        !mRenderWorld->HasComponent<RenderNodeComponentR>(entity) || !mRenderWorld->GetComponent<RenderNodeComponentR>(entity).Read()->mRenderNode.get()->IsValidForRayTracing()
        || mRenderWorld->GetComponent<RenderNodeComponentR>(entity).Read()->mRenderNode.get()->GetSubMeshCount() == 0)
    {
        return false;
    }

    bool notFromGetMesh = mRenderWorld->GetComponent<RenderNodeComponentR>(entity).Read()->mRenderNode.get()->GetRenderGeometry(0)->GetGeometryIndex() == 0xffffffff;
    if (notFromGetMesh)
    {
        return false;
    }
    
    return true;
}
#pragma optimize("", on)

std::string_view RayTracingScene::GetEntityName(ecs::EntityID entity) const
{
    return "BLASDataBuffer";
}

UInt32 RayTracingScene::GetSubInstanceCount() const
{
    UInt32 subInstanceCount = 0;
    for (auto& instance : mRayTracingInstances)
    {
        auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(instance.entity);
        RenderNode* renderNode = renderNodeComp.Read()->mRenderNode.get();
        subInstanceCount += renderNode->GetSubMeshCount();
    }
    return subInstanceCount;
}

void RayTracingScene::UpdateSubInstanceData()
{
    if (mSubInstanceBuffer)
    {
        EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(std::move(mSubInstanceBuffer));
        EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(std::move(mSubInstanceBufferView));
    }

    UInt32 subInstanceCount = GetSubInstanceCount();
    mSubInstanceData.clear();
    mSubInstanceData.reserve(subInstanceCount);
    for (auto& instance : mRayTracingInstances)
    {
        auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(instance.entity);
        RenderNode* renderNode = renderNodeComp.Read()->mRenderNode.get();

        for (UInt32 index = 0; index < renderNode->GetSubMeshCount(); index++)
        {
            RenderGeometry* geometry = renderNode->GetRenderGeometry(index);

            SubInstanceData subInstanceData;
            const GeometryData& geoData = geometry->GetGeometryData();
            subInstanceData.PosBufferIndex = geoData.PosBufferIndex;
            subInstanceData.ColorBufferIndex = geoData.ColorBufferIndex;
            subInstanceData.NormalBufferIndex = geoData.NormalBufferIndex;
            subInstanceData.TangentBufferIndex = geoData.TangentBufferIndex;
            subInstanceData.BinormalBufferIndex = geoData.BinormalBufferIndex;
            subInstanceData.UVBufferIndex = geoData.UVBufferIndex;
            subInstanceData.UV1BufferIndex = geoData.UV1BufferIndex;
            subInstanceData.IndexBufferIndex = geoData.IndexBufferIndex;
            subInstanceData.MaterialIndex = 0;  // TODO(scolu)
            mSubInstanceData.push_back(subInstanceData);
        }
    }
    if (subInstanceCount == 0)
    {
        mSubInstanceData.push_back(SubInstanceData());
    }
    Assert(subInstanceCount == 0 || subInstanceCount == mSubInstanceData.size());
    
    // Upload SubInstanceBuffer
    {
        UInt32 subInstanceBufferSize = std::max(static_cast<UInt32>(mSubInstanceData.size()), 1u) * sizeof(SubInstanceData);
        NGIBufferDesc subInstanceBufferDesc{
            .Size = subInstanceBufferSize,
            .Usage = NGIBufferUsage::StructuredBuffer | NGIBufferUsage::CopyDst,
        };
        mSubInstanceBuffer.reset(GetNGIDevicePtr()->CreateBuffer(subInstanceBufferDesc, "SubInstanceData Buffer"));
        
        auto stagingBuffer = GetNGIDevicePtr()->CreateStagingBuffer(subInstanceBufferDesc);
        UInt8* dst = static_cast<UInt8*>(stagingBuffer->MapRange(NGIBufferUsage::CopyDst, 0, subInstanceBufferSize));
        memcpy(dst, mSubInstanceData.data(), subInstanceBufferSize);
        stagingBuffer->UnmapRange(0, subInstanceBufferSize);

        NGICopyBuffer region{
            .SrcOffset = 0,
            .DstOffset = 0,
            .NumBytes = subInstanceBufferSize};
        mRenderSystem->UpdateBuffer(mSubInstanceBuffer.get(), stagingBuffer, region, NGIResourceState::Undefined,
            NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);
        mRenderSystem->DestroyNGIObject(std::move(stagingBuffer));

        NGIBufferViewDesc subInstanceBufferViewDesc{
            .Usage = NGIBufferUsage::StructuredBuffer,
            .BufferLocation = 0,
            .SizeInBytes = subInstanceBufferSize,
            .StructureByteStride = sizeof(SubInstanceData)
        };
        mSubInstanceBufferView.reset(GetNGIDevicePtr()->CreateBufferView(mSubInstanceBuffer.get(), subInstanceBufferViewDesc));
    }
}

void RayTracingScene::BuildTLAS()
{
    QUICK_SCOPED_CPU_TIMING("RayTracingScene::BuildTLAS");

    mCmd->BeginDebugRegion("BuildTLAS");
    
    std::vector<NGIInstanceDesc> instanceDescs;
    if (!mRayTracingInstances.empty())
    {
        for (auto& instance: mRayTracingInstances)
        {
            auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(instance.entity);
            RenderNode* renderNode = renderNodeComp.Read()->mRenderNode.get();
            
            NGIInstanceDesc instanceDesc{};
            // Do preview translation for higher precision near camera
            auto transformComp = mRenderWorld->GetComponent<TransformComponentR>(instance.entity);
            Float4x4 transform = mTransformSystem->GetWorldAbsoMatrixMatrix(transformComp.Read())->Transpose();
            Float3 previewTranslation = Float3(0.f, 0.f, 0.f);
#ifdef CE_USE_DOUBLE_TRANSFORM
            previewTranslation = mMainCameraTilePosition * LENGTH_PER_TILE;
            transform[3] -= previewTranslation.x;
            transform[7] -= previewTranslation.y;
            transform[11] -= previewTranslation.z;
#endif
            memcpy(instanceDesc.Transform, &transform, sizeof(float) * 12);
            instanceDesc.InstanceID = instance.SubInstanceID;
            instanceDesc.InstanceMask = 0xff;
            instanceDesc.InstanceContribToHitGroupIndex = 0;  // TODO(scolu): set to determine hit group index
            instanceDesc.Flag = NGIInstanceFlag::ForceOpaque;
            Assert(renderNode->GetAccelStruct());
            instanceDesc.BottomLevelAS = renderNode->GetAccelStruct();
            instanceDescs.push_back(instanceDesc);
        }
    }
    else  // Build TLAS with a simple triangle when no valid instances are available
    {
        if (!mSingleTriangleBLAS)
        {
            NGIBufferDesc vertexBufferDesc{
                .Size = sizeof(Float3) * 3,
                .Usage = NGIBufferUsage::VertexBuffer
            };
            mSingleTriangleVertexBuffer.reset(GetNGIDevicePtr()->CreateBuffer(vertexBufferDesc, "SingleTriangleVertexBuffer"));

            NGIBufferDesc indexBufferDesc{
                .Size = sizeof(UInt32) * 3,
                .Usage = NGIBufferUsage::IndexBuffer
            };
            mSingleTriangleIndexBuffer.reset(GetNGIDevicePtr()->CreateBuffer(indexBufferDesc, "SingleTriangleIndexBuffer"));
            NGIBufferViewDesc indexBufferViewDesc{
                .Usage = NGIBufferUsage::VertexBuffer,
                .BufferLocation = 0,
                .SizeInBytes = 0
            };
            
            NGIAccelStructDesc blasDesc;
            blasDesc.IsTopLevel = false;
            blasDesc.BuildFlag = NGIAccelStructBuildFlag::PreferFastTrace;
            blasDesc.DebugName = "SingleTriangleBLAS";

            NGIGeometryDesc geoDesc{};
            geoDesc.GeometryType = NGIGeometryType::Triangle;
            NGIGeometryTriangle& triangles = geoDesc.GeometryData.Triangle;
            
            triangles.IndexBuffer = mSingleTriangleIndexBuffer.get();
            triangles.IndexFormat = IndexFormat_UInt32;
            triangles.IndexOffset = 0;
            triangles.IndexCount = 3;
            triangles.VertexBuffer = mSingleTriangleVertexBuffer.get();
            triangles.VertexFormat = GraphicsFormat::R32G32B32_SFloat;
            triangles.VertexOffset = 0;
            triangles.VertexStride = 12;
            triangles.VertexCount = 3;
            geoDesc.UseTransform = false;
            geoDesc.Flag = NGIGeometryFlag::Opaque;

            blasDesc.BottomLevelGeometries.push_back(geoDesc);

            mSingleTriangleBLAS.reset(mCmd->CreateAccelStruct(blasDesc));
            
            UInt64 scratchBufferSize = mSingleTriangleBLAS->mSizeInfo.BuildScratchSize;
            NGIBufferDesc scratchBufferDesc{
                .Size = scratchBufferSize,
                .Usage = NGIBufferUsage::RayTracingScratchBuffer
            };
            auto [scratchBuffer, scratchBufferState] =
                mRenderSystem->GetTransientResourceManager()->AllocateBuffer(scratchBufferDesc, "BLASBuildScratchBuffer", true, false);
            mCmd->BuildBottomLevelAccelStruct(mSingleTriangleBLAS.get(),
                                              blasDesc.BottomLevelGeometries.data(),
                                              blasDesc.BottomLevelGeometries.size(),
                                              blasDesc.BuildFlag,
                                              scratchBuffer);
        }
        
        NGIInstanceDesc instanceDesc{};
        instanceDesc.InstanceID = 0;
        instanceDesc.InstanceMask = 0xff;
        instanceDesc.InstanceContribToHitGroupIndex = 0;
        instanceDesc.Flag = NGIInstanceFlag::ForceOpaque;
        instanceDesc.BottomLevelAS = mSingleTriangleBLAS.get();
        
        instanceDescs.push_back(instanceDesc);
    }
    
    if (!instanceDescs.empty())
    {
        NGIAccelStructBuildFlag buildFlags = NGIAccelStructBuildFlag::PreferFastTrace | NGIAccelStructBuildFlag::AllowUpdate;

        UInt64 scratchBufferSize;
        if (!mAccelStruct || mAccelStruct->GetTopLevelMaxInstanceCount() != instanceDescs.size())
        {
            DestroyTLAS();
            CreateTLAS();
            scratchBufferSize = mAccelStruct->mSizeInfo.BuildScratchSize;
        }
        else
        {
            buildFlags |= NGIAccelStructBuildFlag::PerformUpdate;
            scratchBufferSize = mAccelStruct->mSizeInfo.UpdateScratchSize;
        }
        
        NGIBufferDesc scratchBufferDesc{
            .Size = scratchBufferSize,
            .Usage = NGIBufferUsage::RayTracingScratchBuffer
        };
        auto [scratchBuffer, scratchBufferState] =
            mRenderSystem->GetTransientResourceManager()->AllocateBuffer(scratchBufferDesc,
                                                                     "TLAS Build Scratch Buffer",
                                                                     true,
                                                                     false);

        UInt32 uploadBufferSize = mAccelStruct->GetTopLevelUploadBufferSize();
        ScratchBufferWrap uploadInstanceBuffer = mRenderSystem->GetScratchBuffer()->AllocateScratch(NGIBufferUsage::AccelStructBuildInputBuffer, uploadBufferSize);

        // Wait for blas build finish
        NGIMemoryBarrier barrier{.StateBefore = NGIResourceState::AccelStructWrite,
            .StateAfter = NGIResourceState::AccelStructBuildBLASBit | NGIResourceState::ShaderStageBitMask};
        mCmd->MemBarrier(&barrier);

        mCmd->BuildTopLevelAccelStructFromBuffer(mAccelStruct.get(),
                                                uploadInstanceBuffer,
                                                uploadInstanceBuffer.GetNGIOffset(),
                                                instanceDescs.data(),
                                                instanceDescs.size(),
                                                buildFlags,
                                                scratchBuffer);
        mCmd->TLASBarrier(mAccelStruct.get());

        // LOG_INFO("Instance Count {}", mInstanceCount);
        // std::cout << "SubmitTLASBuildCommand\n";
    }

    mCmd->EndDebugRegion();
}

void RayTracingScene::CreateTLAS()
{
    NGIAccelStructDesc tlasDesc;
    tlasDesc.IsTopLevel = true;
    tlasDesc.TopLevelMaxInstance = std::max(static_cast<UInt32>(mRayTracingInstances.size()), 1u);
    tlasDesc.BuildFlag = NGIAccelStructBuildFlag::AllowUpdate | NGIAccelStructBuildFlag::PreferFastTrace;
    tlasDesc.DebugName = "TLASDataBuffer";
    mAccelStruct.reset(mCmd->CreateAccelStruct(tlasDesc));
}

void RayTracingScene::DestroyTLAS()
{
    if (mAccelStruct)
    {
        EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(std::move(mAccelStruct));
        mAccelStruct = nullptr;
    }
}

void RayTracingScene::BuildBLASes()
{
    QUICK_SCOPED_CPU_TIMING("RayTracingScene::BuildBLASes");

    mCmd->BeginDebugRegion("BuildBLASes");
    
    for (auto& entity : mFrameUpdateEntitySet)
    {
        if (!mRayTracingInstancesSet.contains(entity))
        {
            mRayTracingInstances.push_back(entity);
            mRayTracingInstancesSet.insert(entity);
        }
        else
        {
            LOG_WARN("Processing an existing entity");
        }
        
        auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(entity);
        RenderNode* renderNode = renderNodeComp.Read()->mRenderNode.get();

        renderNode->BuildAccelStruct(*this, mRenderWorld, entity);
    }

    mCmd->EndDebugRegion();
}

}
#endif