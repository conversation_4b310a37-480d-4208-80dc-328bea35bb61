#pragma once
#include "EntityLifeCycleRenderDataSystemR.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"
#include "CECommon/Allocator/GrowOnlySpanAllocator.h"
#include "NativeGraphicsInterface/NGI.h"
#include "RayTracingInstance.h"
#include "RenderEngine/GPUScene/GPUScene.h"
#include "CECommon/Common/FrameTickManager.h"
#include "ModelSystemR.h"
#include "BindlessResourceManager.h"

#if CROSSENGINE_WIN
#include <concurrent_vector.h>
#else
#include <vector>
#endif

#ifdef NGI_ENABLE_RAY_TRACING

namespace cross {

class MeshR;

struct SubInstanceData
{
    UInt32 PosBufferIndex{CE_BINDLESS_INVALID_INDEX};
    UInt32 ColorBufferIndex{CE_BINDLESS_INVALID_INDEX};
    UInt32 NormalBufferIndex{CE_BINDLESS_INVALID_INDEX};
    UInt32 TangentBufferIndex{CE_BINDLESS_INVALID_INDEX};
    UInt32 BinormalBufferIndex{CE_BINDLESS_INVALID_INDEX};
    UInt32 UVBufferIndex{CE_BINDLESS_INVALID_INDEX};
    UInt32 UV1BufferIndex{CE_BINDLESS_INVALID_INDEX};
    UInt32 IndexBufferIndex{CE_BINDLESS_INVALID_INDEX};
    UInt32 MaterialIndex{CE_BINDLESS_INVALID_INDEX};
    UInt32 Padding[3];
};

class RayTracingScene
{
public:
    RayTracingScene() = default;

    ~RayTracingScene();

    void Initialize(RenderWorld* renderWorld,
        RenderingExecutionDescriptor* red,
        GPUScene* gpuScene);

    void SetFFSRenderPipelineSetting(const FFSRenderPipelineSetting* renderPipelineSetting);
    
    void Update();

    void PostUpdate();

    void RefreshIndex();

    void SetRayTracingSceneDirty(ecs::EntityID entity);

    NGIAccelStruct* GetTopLevelAccelStruct() const
    {
        return mAccelStruct.get();
    }

    auto GetSubInstanceBuffer() const
    {
        return mSubInstanceBuffer.get();
    }

    auto GetSubInstanceBufferView() const
    {
        return mSubInstanceBufferView.get();
    }

    void RemoveEntity(ecs::EntityID entity);

    void SetBindlessResources(REDPass* pass) const;
    
private:
    void CreateTLAS();

    void DestroyTLAS();

    void BuildBLASes();
    
    void BuildTLAS();

    void UpdateSubInstanceData();
    
    void AddUpdateEntity(ecs::EntityID entity);

    MeshR* GetRenderMesh(ecs::EntityID entity) const;

    bool IsEntityValidForRayTracing(ecs::EntityID entity) const;

    bool ShouldReleaseBLAS(ecs::EntityID entity) const;

    std::string_view GetEntityName(ecs::EntityID entity) const;

    UInt32 GetSubInstanceCount() const;

    void CreateBLASPlaceHolder();

    
private:
    RenderWorld* mRenderWorld = nullptr;
    RenderingExecutionDescriptor* mRED = nullptr;
    const FFSRenderPipelineSetting* mFFSRenderPipelineSetting = nullptr;
    GPUScene* mGPUScene = nullptr;

    struct RayTracingInstance
    {
        ecs::EntityID entity = ecs::EntityID::InvalidHandle();
        UInt32 InstanceID = 0xffffffff;
        UInt32 SubInstanceID = 0xffffffff;

        RayTracingInstance(ecs::EntityID entity) : entity(entity) {}
    };
    std::vector<RayTracingInstance> mRayTracingInstances;
    std::unordered_set<ecs::EntityID> mRayTracingInstancesSet;
    bool mIsInstanceDataDirty = true;  // true to make sure first frame will update sub instance data
    
    std::unique_ptr<NGIAccelStruct> mAccelStruct = nullptr;
    std::unordered_set<ecs::EntityID> mFrameUpdateEntitySet;
    std::mutex mMutex;
    
    std::vector<SubInstanceData> mSubInstanceData;
    std::unique_ptr<NGIBuffer> mSubInstanceBuffer = nullptr;
    std::unique_ptr<NGIBufferView> mSubInstanceBufferView = nullptr;

private:
    RendererSystemR* mRenderSystem = nullptr;
    ModelSystemR* mModelSystem = nullptr;
    TransformSystemR* mTransformSystem = nullptr;
    Float3 mMainCameraTilePosition{0.f, 0.f, 0.f};
    EntityLifeCycleRenderDataSystemR* mEntityLifeCycleRenderDataSystem = nullptr;
    NGICommandList* mCmd = nullptr;

    // A single triangle BLAS for creating TLAS empty world
    std::unique_ptr<NGIAccelStruct> mSingleTriangleBLAS = nullptr;
    
    std::unique_ptr<NGIBuffer> mSingleTriangleVertexBuffer;
    std::unique_ptr<NGIBuffer> mSingleTriangleIndexBuffer;
};

}
#endif
