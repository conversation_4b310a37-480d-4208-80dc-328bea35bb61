#include "Editor/LightmapBaker/Prt/PrtCommon.h"
#include "Relighting.h"

namespace cross 
{
template<int Order>
inline SHUtils::FSHVectorRGB3 ToSHVectorRGB3(const SHUtils::TSHVectorRGB<Order>& Other)
{
    if constexpr(Order >= 3)
    {
        return SHUtils::FSHVectorRGB3(Other);
    }
    else
    {
        SHUtils::FSHVectorRGB3 Output;
        std::memset(Output.R.V, 0, sizeof(Output.R.V));
        std::memset(Output.G.V, 0, sizeof(Output.G.V));
        std::memset(Output.B.V, 0, sizeof(Output.B.V));
        std::memcpy(Output.R.V, Other.R.V, sizeof(Other.R.V));
        std::memcpy(Output.G.V, Other.G.V, sizeof(Other.G.V));
        std::memcpy(Output.B.V, Other.B.V, sizeof(Other.B.V));
        return Output;
    }
}

RelightingTask::RelightingTask(SInt32 NumPrtProbesRelightPerFrame, const SHUtils::FSHVectorRGB3& EnvSH, const std::vector<LocalLightInfo>& LocalLights)
    : Status(ERelightingTaskStatus::Running)
    , NumPrtProbesRelightPerFrame(NumPrtProbesRelightPerFrame)
    , EnvSH(EnvSH)
    , LocalLights(LocalLights)
{
    Assert(this->NumPrtProbesRelightPerFrame > 0);
}

IndirectLightingCacheRelightingTask::IndirectLightingCacheRelightingTask(SInt32 NumPrtProbesRelightPerFrame, std::vector<LightProbeCache*>& LightmapSceneData, const SHUtils::FSHVectorRGB3& EnvSH,
                                                                         const std::vector<LocalLightInfo>& LocalLights, const std::vector<Float4>& InLocalPRTColorScales)
    : RelightingTask(NumPrtProbesRelightPerFrame, EnvSH, LocalLights)
{
    LocalPRTColorScales = InLocalPRTColorScales;

    // Deep copy FPrecomputedVolumetricLightmap.
    mCachedLightProbe.reserve(LightmapSceneData.size());
    for (SInt32 IdxLightMap = 0; IdxLightMap < LightmapSceneData.size(); ++IdxLightMap)
    {
        mCachedLightProbe.push_back(LightmapSceneData[IdxLightMap]);
    }
    if (LightmapSceneData.size() == 0)   // No lightmap at all... No need to do anything in this task.
    {
        Status = ERelightingTaskStatus::Finished;
    }
    else
    {
        SortCachedPrecomputedLightVolumes();
    }
}

void IndirectLightingCacheRelightingTask::SortCachedPrecomputedLightVolumes()
{
    //TODO(timllpan)
}

void IndirectLightingCacheRelightingTask::Run()
{
    QUICK_SCOPED_CPU_TIMING("RelightPrtProbes_Run");
    if (Status != ERelightingTaskStatus::Running)
    {
        return;
    }

    SInt32 NumProbesRelighted = 0;
    if (mCachedLightProbe.size() <= 0)
    {
        Status = ERelightingTaskStatus::Finished;
        return;
    }

    do
    {
        auto PrecomputedLightVolume = mCachedLightProbe[IdxCurLightMap];

        bool bBrickDataRelightFinished = false;
        SInt32 NumProbesRelightedInBrickData = 0;

        {
            NumProbesRelightedInBrickData = RelightProbes(PrecomputedLightVolume, IdxCurProbe, NumPrtProbesRelightPerFrame - NumProbesRelighted, bBrickDataRelightFinished);
            NumProbesRelighted += NumProbesRelightedInBrickData;
            IdxCurProbe += NumProbesRelightedInBrickData;

            if (bBrickDataRelightFinished)
            {
                //CachedScene->AfterRelightingPrecomputedLightVolume(PrecomputedLightVolume);
                IdxCurProbe = 0;
                ++IdxCurLightMap;
                break;
            }
        }
    } while (NumProbesRelighted < NumPrtProbesRelightPerFrame);

    if (IdxCurLightMap >= mCachedLightProbe.size())
    {
        Status = ERelightingTaskStatus::Finished;
    }
}

SInt32 IndirectLightingCacheRelightingTask::RelightProbes(LightProbeCache* BrickData, SInt32 IdxProbeBeg, SInt32 MaxProbeToRelight, bool& bBrickDataRelightFinished)
{
    if (!BrickData->bNeedsRelighting)
    {
        bBrickDataRelightFinished = true;
        return 0;
    }

    SInt32 NumProbesRelighted = 0;

    if (false) {
        SHUtils::FPrtTransferMatrix4x9 TransferMatrix;
        std::array<UInt16, 36> matFloat16 = {9472, 36097, 9476, 40294, 41947, 6750, 6745, 40794, 41070, 9571, 8287, 9697, 41780, 41654, 8489, 7927, 41907, 40650, 
            39361, 33703, 39298, 4614,  6496,  37201, 34610, 5245,  5381,  8546, 42069, 8574, 8380,  39538, 41887, 7197, 8331,  41159};
        for (auto i = 0; i < 36; i++)
        {
            TransferMatrix.M[i] = {GPUBaking::FFloat16(matFloat16[i]).GetFloat(), GPUBaking::FFloat16(matFloat16[i]).GetFloat(), GPUBaking::FFloat16(matFloat16[i]).GetFloat()};
        }
        std::array<float, 12> gVal = {0.886227548f, 0.00000000f, 1.08540297f, 1.08540308f, 0.00000000f, 0.00000000f, 0.495416462f, 1.71617043f, 0.858085334f, 0, 0, 0};
        std::array<float, 12> bVal = {0.886227548f, 0.00000000f, 1.08540297f, 1.08540308f, 0.00000000f, 0.00000000f, 0.495416462f, 1.71617043f, 0.858085334f, 0, 0, 0};
        SHUtils::FSHVectorRGB3 EnvSH0;
        for (auto i = 0; i < 12; i++)
        {
            EnvSH0.R.V[i] = 0;
            EnvSH0.G.V[i] = gVal[i];
            EnvSH0.B.V[i] = bVal[i];
        }
        SHUtils::FPrtTransferVector2 TransferSHVectorRGB;
        TransferMatrix.Multiply(EnvSH0, TransferSHVectorRGB);
    }

    BrickData->GetOctree()->FindAllElements([&](LightProbeNode& VolumeSample) {
        SHUtils::FPrtTransferVector2 TransferSHVectorRGB;
        VolumeSample.mTransferMatrix.Multiply(EnvSH, TransferSHVectorRGB);
        VolumeSample.mLighting = /*VolumeSample.mStaticLighting + */ToSHVectorRGB3(TransferSHVectorRGB);
        ++NumProbesRelighted;
    });

    BrickData->bNeedsRelighting = false;
    bBrickDataRelightFinished = true;

    return NumProbesRelighted;
}
}   // namespace cross