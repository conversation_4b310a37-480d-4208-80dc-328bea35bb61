#pragma once
#include "RenderEngine/RenderGeometry.h"
#include "RenderEngine/IVertexStreamLayoutPolicy.h"
#include "Resource/IResourceInterface.h"
#include "Resource/MeshAssetData.h"

#include <atomic>
#include <mutex>
#include <vector>

namespace cross
{
    // A Helper struct to collect data of mesh info at each LOD level
    struct RENDER_ENGINE_API LODMeshR
    {
        // Render geometries data
        std::vector<RenderGeometry*> GeometrySections;
        GeometryPacketPtr GeometryPacket;

        // Vertices data
        UInt32 VertexStartIndex = 0;
        UInt32 VertexCount;
        std::vector<VertexStreamLayout> VertexStreamLayouts;
        VertexUploadBufferBinding UploadBufferBinding[20];
        UInt32 BufferBindingCount = 0;
        std::vector<UInt32> StreamVertexBufferSize;

        // Indices data
        UInt32 IndexStartIndex = 0;
        UInt32 IndexCount;
        IndexBufferFormat IndexFormat;
        UInt32 IndexBufferSize;

        UInt32 GetBufferSize() const
        {
            return std::accumulate(StreamVertexBufferSize.begin(), StreamVertexBufferSize.end(), IndexBufferSize);
        }
    };

    struct StellarMeshResource
    {
        NGIBufferPtr mIndexBuffer{nullptr};
        std::unordered_map<StellarMeshVertexAttribute, NGIBufferPtr> mVertexAttributesBuffer{};
    };

    class RENDER_ENGINE_API MeshR : public IMeshR
    {
    public:
        enum struct State
        {
            Uninitialized,
            Initializing,
            Initialized,
            NeedReinitialized,
        };

        MeshR() = default;
        MeshR(MeshR const&) = delete;
        MeshR(MeshR&&) = delete;
        MeshR& operator=(MeshR const&) = delete;
        MeshR& operator=(MeshR&&) = delete;
        virtual ~MeshR();

        MeshR(MeshAssetData* meshAssetData);

        void BuildStaticMesh(IVertexStreamLayoutParameter* meshParameter = nullptr) override;

        const std::vector<RenderGeometry>& GetRenderGeometries() const;

        RenderGeometry& GetRenderGeometry(UInt32 index);

        UInt32 GetGeometryCount() const;

        bool IsGeometryEmpty() const;

        std::vector<LODMeshR>* GetLODMeshes();

        LODMeshR* GetLODMesh(UInt8 lodLevel);

        UInt32 GetLODMeshBufferSize(UInt8 lodLevel) const;

        UInt32 GetLODMeshCount() const;

        void ClearAndResizeLODMeshes(UInt32 lodCount);

        bool IsReadyForStreaming() const;

        void SetReadyForStreaming(bool ready);

        void SetNameHash(UInt32 index, StringHash32 hash);

        void ClearAndResize(UInt32 geometryCount);

        void SetState(State state);

        State GetState() const;

        bool TryUpdate(UInt32 curFrameId);

        std::mutex& GetBuildMutex();
        
        NGIAccelStruct* GetAccelStruct()
        {
            std::shared_lock locker(mBLASMutex);
            return mBLAS.get();
        }

        void SetAccelStruct(NGIAccelStruct* blas)
        {
            std::unique_lock locker(mBLASMutex);
            Assert(mBLAS == nullptr);
            mBLAS.reset(blas);
        }

        void SetMeshIndex(UInt32 index) override;

        UInt32 GetMeshIndex() override;

        StellarMeshResource const& GetStellarMeshResource() const;

        bool IsStellarMeshEnable() const;
    
    private:
        void UploadStellarMeshResource(MeshStreamData& meshStreamData);

        MeshAssetData* mMeshAssetData{nullptr};
        UInt32 mMeshIndex{0xFFFFFFFF};

        std::vector<RenderGeometry> mGeometries{};
        std::vector<StringHash32> mGeometryNameHashes{};

        std::vector<LODMeshR> mLODMeshes{};
        UInt8 mCurrentFirstLODIndex;
        UInt8 mLODBias = 0;
        bool mReadyForStreaming = false;

        std::atomic<UInt32> mVersion{ 0xffffffff };
        std::atomic<State> mState{State::Uninitialized};
        std::mutex mBuildMutex{};

        std::shared_mutex mBLASMutex;
        std::unique_ptr<NGIAccelStruct> mBLAS = nullptr;

        StellarMeshResource mStellarMeshResource{};
        bool mStellarMeshEnable{false};
    };
}
