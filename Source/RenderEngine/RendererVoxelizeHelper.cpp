#include "EnginePrefix.h"
#include "CrossBase/Threading/RenderingThread.h"
#include "CrossBase/Threading/PresentThread.h"
#include "CrossBase/Math/CrossMath.h"
#include "CrossBase/Hash/Hash.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/GlobalSystemBase.h"
#include "CECommon/Common/GlobalSystemDesc.h"
#include "CECommon/Common/FrameParam.h"
#include "CECommon/Common/FrameCounter.h"
#include "CECommon/Common/IRenderWindow.h"
#include "NativeGraphicsInterface/NGIManager.h"
#include "NativeGraphicsInterface/NGIXRRuntime.h"
#include "Resource/Material.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RenderFactory.h"
#include "RenderEngine/RenderPipelineSystemR.h"
#include "RenderEngine/RenderWorld.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/RenderContext.h"
#include "RenderEngine/VisibilitySystemR.h"
#include "RenderEngine/CameraSystemR.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderEngine/RenderNodeSystemR.h"
#include "RenderEngine/WindowSystemR.h"
#include "RenderEngine/WorldSystemR.h"
#include "RenderEngine/RenderWindowR.h"
#include "RenderEngine/MultiClientFrameSync.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipeline.h"
#include "RenderEngine/AABBSystemR.h"
#include "RendererVoxelizeHelper.h"

namespace cross {
void RendererVoxelizeHelper::InitVoxelPassRenderContext(RenderContext* passContext, bool preVoxelizePass, REDTextureView* rwAlbedoTex, REDTextureView* rwNormalTex, REDTextureView* rwEmissiveTex, REDTextureView* rwOpacityTex,
                                                        SInt32 directionIdx, SInt32 clipmapIndex, const Float3& cameraTilePos, const Float4& clipmapWorldToUVScale, const Float4& clipmapWorldToUVBias,
                                                        const Float4& clipmapWorldCenter, const Float4& clipmapWorldExtent, const Int3& clipmapResolution, float diffuseBoost,
                                                        const RenderCamera* camera)
{
    passContext->SetProperty("RWNormalTexture", rwNormalTex);
    passContext->SetProperty("RWAlbedoTexture", rwAlbedoTex);
    passContext->SetProperty("RWEmissiveTexture", rwEmissiveTex);
    passContext->SetProperty("RWOpacityTexture", rwOpacityTex);

    passContext->SetProperty("VOXELIZE_PASS", true);
    passContext->SetProperty("USE_CLIPMAP_WRAP", preVoxelizePass ? false : true);
#if defined(CE_USE_DOUBLE_TRANSFORM)
    passContext->SetProperty("CE_USE_DOUBLE_TRANSFORM", true);
    passContext->SetProperty("ce_CameraTilePosition", cameraTilePos);
#else
    passContext->SetProperty("CE_USE_DOUBLE_TRANSFORM", false);
#endif
    passContext->SetProperty("ClipmapWorldToUVScale", clipmapWorldToUVScale);
    passContext->SetProperty("ClipmapWorldToUVBias", clipmapWorldToUVBias);
    passContext->SetProperty("ClipmapGridResolution", clipmapResolution);

    passContext->SetProperty("ClipmapWorldCenter", clipmapWorldCenter);
    passContext->SetProperty("ClipmapWorldExtent", clipmapWorldExtent);

    passContext->SetProperty("Direction", directionIdx);
    passContext->SetProperty("ClipmapIndex", clipmapIndex);
    passContext->SetProperty("DiffuseBoost", diffuseBoost);
}

}   // namespace cross
