#include "StellarMeshManager.h"
#include "NativeGraphicsInterface/NGI.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/RenderEngine.h"
#include "CECommon/Common/EngineGlobal.h"

namespace cross::StellarMesh {

constexpr UInt32 MinConstantBufferSize = 1024u;
constexpr UInt32 MinClusterBufferSize = 4096u;

constexpr UInt32 MaxIndexCount  = 1024u * 1024u;
constexpr UInt32 MaxVertexCount = 1024u * 1024u;

struct StellarMeshManager::Impl
{
    Impl()
    {
        mMeshConstants.reserve(MinConstantBufferSize);
        mClusters.reserve(MinClusterBufferSize);

        ResizeBuffer<MeshConstant>(mMeshConstantBuffer, mMeshConstantBufferView, static_cast<UInt32>(mMeshConstants.size()), "StellarMeshManager.MeshConstantBuffer");
        ResizeBuffer<GPUCluster>(mClusterBuffer, mClusterBufferView, static_cast<UInt32>(mClusters.size()), "StellarMeshManager.ClusterBuffer");

        ResizeBuffer<UInt32>(mIndexBuffer, mIndexBufferView, MaxIndexCount, "StellarMeshManager.IndexBuffer");
        ResizeBuffer<Float4>(mPositionBuffer, mPositionBufferView, MaxVertexCount, "StellarMeshManager.PositionBuffer");
    }

    ~Impl() = default;

    std::unique_ptr<NGIBuffer> mMeshConstantBuffer;
    std::unique_ptr<NGIBuffer> mClusterBuffer;

    std::unique_ptr<NGIBufferView> mMeshConstantBufferView;
    std::unique_ptr<NGIBufferView> mClusterBufferView;

    // meshR->GetMeshIndex() is the index in meshConstants
    std::vector<MeshConstant> mMeshConstants{};
    std::vector<GPUCluster> mClusters{};

    // Mutex for thread safety
    mutable std::shared_mutex mSharedMutex;   // For read/write operations
    mutable std::mutex mMutex;                // For exclusive operations

    constexpr static NGIBufferUsage BufferUsage = NGIBufferUsage::StructuredBuffer | NGIBufferUsage::CopyDst;

    std::unique_ptr<NGIBuffer> mIndexBuffer;
    std::unique_ptr<NGIBuffer> mPositionBuffer;
    std::unique_ptr<NGIBufferView> mIndexBufferView;
    std::unique_ptr<NGIBufferView> mPositionBufferView;
    std::vector<UInt32> mIndexes;
    std::vector<Float4> mPositions;

    void AddRenderMesh(MeshR* meshR, GPUClusteredMeshData clusteredMeshData)
    {
        if (!meshR)
            return;
        
        auto padding_vector3_array = [&](std::span<Float3> array) -> std::vector<Float4>
        {
            auto result = std::vector<Float4>{};
            for (auto& element : array)
            {
                result.emplace_back(Float4{element, 1.0});
            }
            return result;
        };

        UInt32 meshIndex = meshR->GetMeshIndex();

        // Use exclusive lock for writing to shared data
        std::lock_guard<std::mutex> lock(mMutex);

        if (meshIndex >= mMeshConstants.size())
        {
            mMeshConstants.resize(meshIndex + 1);
        }

        UInt32 clusterOffset = static_cast<UInt32>(mClusters.size());
        UInt32 clusterCount = static_cast<UInt32>(clusteredMeshData.mClusters.size());

        MeshConstant& meshConstant = mMeshConstants[meshIndex];
        meshConstant.mClusterOffset = clusterOffset;
        meshConstant.mClusterCount = clusterCount;

        
        UInt32 indexCount = static_cast<UInt32>(clusteredMeshData.mMeshData.mIndexes.size());
        UInt32 indexOffset = static_cast<UInt32>(mIndexes.size());
        UInt32 vertexCount = static_cast<UInt32>(clusteredMeshData.mMeshData.mVertexAttribute.mPosition.size());
        UInt32 vertexOffset = static_cast<UInt32>(mPositions.size());
        meshConstant.mIndexOffset = indexOffset;
        meshConstant.mIndexCount = indexCount;
        meshConstant.mVertexOffset = vertexOffset;
        meshConstant.mVertexCount = vertexCount;

        //auto const& resource = meshR->GetStellarMeshResource();

        //meshConstant.mIndexBufferIndex = gBindlessResourceManager.AllocateBindlessBufferView(resource.mIndexBuffer.get(), GraphicsFormat::R32G32B32_UInt, resource.mIndexBuffer->GetSize());
        //for (auto& [attribute, buffer] : resource.mVertexAttributesBuffer)
        //{
        //    switch (attribute)
        //    {
        //    case StellarMeshVertexAttribute::mPosition:
        //        meshConstant.mPositionBufferIndex = gBindlessResourceManager.AllocateBindlessBufferView(buffer.get(), GraphicsFormat::R32G32B32_SFloat, buffer->GetSize());
        //        break;
        //    case StellarMeshVertexAttribute::mNormal:
        //        meshConstant.mNormalBufferIndex = gBindlessResourceManager.AllocateBindlessBufferView(buffer.get(), GraphicsFormat::R32G32B32_SFloat, buffer->GetSize());
        //        break;
        //    case StellarMeshVertexAttribute::mTangent:
        //        meshConstant.mTangetBufferIndex = gBindlessResourceManager.AllocateBindlessBufferView(buffer.get(), GraphicsFormat::R32G32B32A32_SFloat, buffer->GetSize());
        //        break;
        //    case StellarMeshVertexAttribute::mColor:
        //        meshConstant.mColorBufferIndex = gBindlessResourceManager.AllocateBindlessBufferView(buffer.get(), GraphicsFormat::R32G32B32A32_SFloat, buffer->GetSize());
        //        break;
        //    case StellarMeshVertexAttribute::mUV0:
        //        meshConstant.mUV0BufferIndex = gBindlessResourceManager.AllocateBindlessBufferView(buffer.get(), GraphicsFormat::R32G32_SFloat, buffer->GetSize());
        //        break;
        //    case StellarMeshVertexAttribute::mUV1:
        //        meshConstant.mUV1BufferIndex = gBindlessResourceManager.AllocateBindlessBufferView(buffer.get(), GraphicsFormat::R32G32_SFloat, buffer->GetSize());
        //        break;
        //    default:
        //        Assert(false);
        //    }
        //}

        mClusters.insert(mClusters.end(), clusteredMeshData.mClusters.begin(), clusteredMeshData.mClusters.end());
        mIndexes.insert(mIndexes.end(), clusteredMeshData.mMeshData.mIndexes.begin(), clusteredMeshData.mMeshData.mIndexes.end());
        auto pos = padding_vector3_array(clusteredMeshData.mMeshData.mVertexAttribute.mPosition);
        mPositions.insert(mPositions.end(), pos.begin(), pos.end());

        UpdateGPUBufferFor(meshIndex);
    }

    void RemoveRenderMesh(MeshR* meshR)
    {

        if (!meshR)
            return;

        UInt32 meshIndex = meshR->GetMeshIndex();

        // Use exclusive lock for writing to shared data
        std::lock_guard<std::mutex> lock(mMutex);

        // Check if index is valid
        if (meshIndex >= mMeshConstants.size())
            return;

        // Get cluster information
        MeshConstant& meshConstant = mMeshConstants[meshIndex];
        UInt32 clusterOffset = meshConstant.mClusterOffset;
        UInt32 clusterCount = meshConstant.mClusterCount;

        // If no clusters, return directly
        if (clusterCount == 0)
            return;

        // Clear mesh constant data
        //gBindlessResourceManager.ReleaseBindlessBufferView(meshConstant.mIndexBufferIndex);
        //gBindlessResourceManager.ReleaseBindlessBufferView(meshConstant.mPositionBufferIndex);
        //gBindlessResourceManager.ReleaseBindlessBufferView(meshConstant.mNormalBufferIndex);
        //gBindlessResourceManager.ReleaseBindlessBufferView(meshConstant.mTangetBufferIndex);
        //gBindlessResourceManager.ReleaseBindlessBufferView(meshConstant.mColorBufferIndex);
        //gBindlessResourceManager.ReleaseBindlessBufferView(meshConstant.mUV0BufferIndex);
        //gBindlessResourceManager.ReleaseBindlessBufferView(meshConstant.mUV1BufferIndex);

        meshConstant.mClusterOffset = 0;
        meshConstant.mClusterCount = 0;
        meshConstant.mIndexBufferIndex = CE_BINDLESS_INVALID_INDEX;
        meshConstant.mPositionBufferIndex = CE_BINDLESS_INVALID_INDEX;
        meshConstant.mNormalBufferIndex = CE_BINDLESS_INVALID_INDEX;
        meshConstant.mTangetBufferIndex = CE_BINDLESS_INVALID_INDEX;
        meshConstant.mColorBufferIndex = CE_BINDLESS_INVALID_INDEX;
        meshConstant.mUV0BufferIndex = CE_BINDLESS_INVALID_INDEX;
        meshConstant.mUV1BufferIndex = CE_BINDLESS_INVALID_INDEX;

        // Mark as dirty, needs update
        // mDirtyMeshIndices.push(meshIndex);
        // mMeshConstantBufferDirty = true;
        // UploadDataToGPUFor(meshIndex);
    }

    void UpdateGPUBufferFor(UInt32 meshIndex)
    {
        // Resize buffers if needed
        if (!mMeshConstants.empty())
        {
            ResizeBuffer<MeshConstant>(mMeshConstantBuffer, mMeshConstantBufferView, static_cast<UInt32>(mMeshConstants.size()), "StellarMeshManager.MeshConstantBuffer");
            UploadDataToGPUFor(std::span{mMeshConstants.begin() + meshIndex, 1}, mMeshConstantBuffer.get(), meshIndex * sizeof(MeshConstant));
        }

        if (!mClusters.empty())
        {
            ResizeBuffer<GPUCluster>(mClusterBuffer, mClusterBufferView, static_cast<UInt32>(mClusters.size()), "StellarMeshManager.ClusterBuffer");
            auto clusterOffset = mMeshConstants[meshIndex].mClusterOffset;
            auto clusterCount = mMeshConstants[meshIndex].mClusterCount;
            UploadDataToGPUFor(std::span{mClusters.begin() + clusterOffset, clusterCount}, mClusterBuffer.get(), clusterOffset * sizeof(GPUCluster));
        }

        if (!mIndexes.empty())
        {
            ResizeBuffer<UInt32>(mIndexBuffer, mIndexBufferView, static_cast<UInt32>(mIndexes.size()), "StellarMeshManager.IndexBuffer");
            auto indexOffset = mMeshConstants[meshIndex].mIndexOffset;
            auto indexCount = mMeshConstants[meshIndex].mIndexCount;
            UploadDataToGPUFor(std::span{mIndexes.begin() + indexOffset, indexCount}, mIndexBuffer.get(), indexOffset * sizeof(UInt32));
        }

        if (!mPositions.empty())
        {
            ResizeBuffer<Float4>(mPositionBuffer, mPositionBufferView, static_cast<UInt32>(mPositions.size()), "StellarMeshManager.PositionBuffer");
            auto vertexOffset = mMeshConstants[meshIndex].mVertexOffset;
            auto vertexCount = mMeshConstants[meshIndex].mVertexCount;
            UploadDataToGPUFor(std::span{mPositions.begin() + vertexOffset, vertexCount}, mPositionBuffer.get(), vertexOffset * sizeof(Float4));
        }
    }
    
    // Resize buffer for the specified type (MeshConstant or Cluster)
    template<typename T>
    void ResizeBuffer(std::unique_ptr<NGIBuffer>& buffer, std::unique_ptr<NGIBufferView>& bufferView, UInt32 dataSize, const char* bufferName)
    {
        // Calculate required buffer size
        UInt32 requiredSize = static_cast<UInt32>(dataSize * sizeof(T));

        // Calculate new buffer size (round up to power of 2)
        UInt32 newSize = MinConstantBufferSize;
        while (newSize < requiredSize)
        {
            newSize <<= 1;
        }

        // Ensure minimum capacity based on type
        constexpr UInt32 minSize = (sizeof(T) == sizeof(MeshConstant)) ? MinConstantBufferSize : MinClusterBufferSize;
        newSize = std::max(newSize, minSize);

        if (!buffer || buffer->GetDesc().Size < newSize)
        {
            NGIBufferDesc desc{newSize, BufferUsage};
            auto newBuffer = std::unique_ptr<NGIBuffer>(GetNGIDevice().CreateBuffer(desc, bufferName));

            // Copy data from old buffer if exists
            if (buffer)
            {
                auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

                // Copy from old buffer to new buffer
                SizeType copySize = std::min<SizeType>(buffer->GetDesc().Size, newSize);
                rendererSystem->UpdateBuffer(newBuffer.get(),
                                             buffer.get(),
                                             NGICopyBuffer{0, 0, copySize},
                                             NGIResourceState::Undefined,
                                             NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask,
                                             NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask,
                                             NGIResourceState::Undefined);

                rendererSystem->DestroyNGIObjectInAsyncThread(std::move(buffer));
                rendererSystem->DestroyNGIObjectInAsyncThread(std::move(bufferView));
            }

            //threading::DispatchRenderingCommand([newBuffer = std::move(newBuffer), newSize, &buffer, &bufferView]() mutable {
                buffer = std::move(newBuffer);

                // Create buffer view
                NGIBufferViewDesc viewDesc{NGIBufferUsage::StructuredBuffer, 0, newSize, GraphicsFormat::Unknown, sizeof(T)};
                bufferView.reset(GetNGIDevice().CreateBufferView(buffer.get(), viewDesc));
            //});
        }
    }

    template<typename T>
    void UploadDataToGPUFor(std::span<T> data, NGIBuffer* dstBuffer, UInt32 byteOffset)
    {
        auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

        UInt32 byteSize = static_cast<UInt32>(data.size_bytes());
        auto asycStagingbuffer = rendererSystem->GetScratchBuffer()->AllocateStaging_Async(NGIBufferUsage::CopySrc, byteSize);
        auto [stagingbuffer, stagingOffset, stagingData] = asycStagingbuffer.Raw();

        memcpy(stagingData, data.data(), byteSize);
        stagingbuffer->UnmapRange(stagingOffset, byteSize);
        rendererSystem->UpdateBuffer(
            dstBuffer,
            stagingbuffer,
            NGICopyBuffer{stagingOffset, byteOffset, byteSize},
            NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask,
            NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask
        );
        asycStagingbuffer.Free();
    }
};

StellarMeshManager::StellarMeshManager()
    : pImpl(std::make_unique<Impl>())
{
}

StellarMeshManager::~StellarMeshManager() = default;


void StellarMeshManager::AddRenderMesh(MeshR* meshR, GPUClusteredMeshData clusteredMeshData)
{
    pImpl->AddRenderMesh(meshR, std::move(clusteredMeshData));
}

void StellarMeshManager::RemoveRenderMesh(MeshR* meshR)
{
    pImpl->RemoveRenderMesh(meshR);
}

NGIBuffer* StellarMeshManager::GetMeshConstantBuffer() const
{
    // Use shared lock for read-only access
    std::shared_lock<std::shared_mutex> lock(pImpl->mSharedMutex);
    return pImpl->mMeshConstantBuffer.get();
}

NGIBuffer* StellarMeshManager::GetClusterBuffer() const
{
    // Use shared lock for read-only access
    std::shared_lock<std::shared_mutex> lock(pImpl->mSharedMutex);
    return pImpl->mClusterBuffer.get();
}

NGIBufferView* StellarMeshManager::GetMeshConstantBufferView() const
{
    // Use shared lock for read-only access
    std::shared_lock<std::shared_mutex> lock(pImpl->mSharedMutex);
    return pImpl->mMeshConstantBufferView.get();
}

NGIBufferView* StellarMeshManager::GetClusterBufferView() const
{
    // Use shared lock for read-only access
    std::shared_lock<std::shared_mutex> lock(pImpl->mSharedMutex);
    return pImpl->mClusterBufferView.get();
}

NGIBufferView* StellarMeshManager::GetIndexBufferView() const
{
    // Use shared lock for read-only access
    std::shared_lock<std::shared_mutex> lock(pImpl->mSharedMutex);
    return pImpl->mIndexBufferView.get();
}

NGIBufferView* StellarMeshManager::GetPositionBufferView() const
{
    // Use shared lock for read-only access
    std::shared_lock<std::shared_mutex> lock(pImpl->mSharedMutex);
    return pImpl->mPositionBufferView.get();
}

} // namespace cross::StellarMesh