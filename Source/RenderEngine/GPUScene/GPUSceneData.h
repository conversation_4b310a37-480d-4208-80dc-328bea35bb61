#pragma once
#include "CrossBase/Math/CrossMath.h"

namespace cross {

struct PrimitiveCullingData
{
    Float3 LocalBoundsCenter;
    UInt32 LastUpdateSceneFrameCount;
    Float3 LocalBoundsExtent;
    UInt32 Flag;
    Float3 TilePosition;
    UInt32 _pad;
};

struct ObjectCullingData
{
    Float4x4 WorldMatrix;
    UInt32 PrimitiveCullingGUID;
    Float3 _pad;
};

struct ObjectSceneData
{
    Float4x4 WorldMatrix;
    Float4x4 PrevWorldMatrix;
    Float4x4 InvTransposeWorldMatrix;
    Float3 TilePosition;
    float _pad;
};

}   // namespace cross