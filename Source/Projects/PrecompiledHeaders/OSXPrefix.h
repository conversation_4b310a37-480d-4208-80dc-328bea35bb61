#pragma once

#include "Runtime/Core/IOS/IOSPlatform.h"

// OSX uses the same names for primitive typedefs that we use but defines
// the 32bit integer variants as "long ints" on 32bit OSX (which is very
// stupid as the types end up being incomatible with "int").  Anyway, get
// rid of these by renaming their typedefs.
/*#define UInt8 MacUInt8
#define SInt8 MacSInt8
#define UInt16 MacUInt16
#define SInt16 MacSInt16
#define UInt32 MacUInt32
#define SInt32 MacSInt32
#define UInt64 MacUInt64
#define SInt64 MacSInt64*/

#if defined(__OBJC__) && !defined(__arm__) && !defined(__arm64__) && !(TARGET_IPHONE_SIMULATOR)
#import <Cocoa/Cocoa.h>
#endif

#if defined(__APPLE__) && !defined(__arm__) && !defined(__arm64__) && !(TARGET_IPHONE_SIMULATOR)
#include <Carbon/Carbon.h>
#endif

/*#undef UInt8
#undef SInt8
#undef UInt16
#undef SInt16
#undef UInt32
#undef SInt32
#undef UInt64
#undef SInt64*/
