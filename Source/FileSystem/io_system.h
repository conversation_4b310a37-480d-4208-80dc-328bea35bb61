#ifndef CROSSENGINE_IO_SYSTEM_H
#define CROSSENGINE_IO_SYSTEM_H

namespace cross
{
    namespace filesystem
    {
        namespace io
        {
            constexpr int InValidHandle = -1;

            class StreamSys final : public Stream
            {
            public:
                ~StreamSys();
                virtual size_t             Tell() const override;
                virtual SInt64             Seek(std::ptrdiff_t inOffset, int inFromWhere) override;
                virtual size_t             GetSize() const override;
                virtual bool               IsEof() const override;
                virtual void               Close() override;
                virtual bool               IsClosed() const override;
                virtual const std::string& GetPathName() const override;
                virtual Opener*            GetOpener() const override;
                virtual bool               GetFileDesc(int& outFd, size_t& outOffset) const override;

                size_t Read(char* inBuffer, size_t inLength);
                Input* ReOpen() { return mOpener->OpenInput(mPath); }
                size_t Write(const char* inSrc, size_t inLength);
                size_t WriteHuge(const char* inSrc, size_t inLength);
            private:
                std::string mPath = "";
                Opener*     mOpener = nullptr;
                int         mHandle = InValidHandle;
                size_t      mSize = 0;
                StreamSys() = default;

                friend class OpenerSys;
            };

            class OpenerSys final : public OpenerOS
            {
            public:
                OpenerSys() = default;
                
                const std::string& GetTag() const override { return mTag; }
                OpenerType         GetType() const override { return OPENER_TYPE_SYS; }
                Input*             OpenInput(const std::string& inPath) override;
                Output*            OpenOutput(const std::string& inPath) override;                
                
            private:
                std::string mTag = "sys";
                StreamSys* OpenStream(int inHandle, const std::string& inPath);
            };

            class InputSys final: public Input
            {
            public:
                ~InputSys() { 
					if(mStream) 
						delete mStream; 
					mStream = nullptr; 
				}
                virtual size_t Tell() const override { return mStream->Tell(); }
                virtual SInt64 Seek(std::ptrdiff_t inOffset, int inFromWhere) override { return mStream->Seek(inOffset, inFromWhere) ; }
                virtual size_t GetSize() const override { return mStream->GetSize() ; }
                virtual bool   IsEof() const override { return mStream->IsEof(); }
                virtual void   Close() override { mStream->Close(); }
                virtual bool   IsClosed() const override { return mStream->IsClosed() ; }
                virtual const std::string& GetPathName() const override { return mStream->GetPathName() ; }
                virtual Opener* GetOpener() const override { return mStream->GetOpener() ; }
                virtual bool    GetFileDesc(int& outFd, size_t& outOffset) const override { return mStream->GetFileDesc(outFd, outOffset); }                
                virtual size_t Read(char* outBuffer, size_t inLength) override { return mStream->Read(outBuffer, inLength) ; }
                Input* ReOpen() override { return mStream->ReOpen() ; }
            private:
                StreamSys* mStream = nullptr;
            	InputSys(StreamSys* inStream) : mStream(inStream) {}
                friend class OpenerSys;
                friend class StreamSys;
            };

			class OutputSys final : public Output
			{
			public:
				~OutputSys() {
					if (mStream)
						delete mStream;
					mStream = nullptr;
				}
				virtual size_t             Tell() const override { return mStream->Tell(); }
                virtual SInt64             Seek(std::ptrdiff_t inOffset, int inFromWhere) override { return mStream->Seek(inOffset, inFromWhere); }
				virtual size_t             GetSize() const override { return mStream->GetSize(); }
				virtual bool               IsEof() const override { return mStream->IsEof(); }
				virtual void               Close() override { mStream->Close(); }
				virtual bool               IsClosed() const override { return mStream->IsClosed(); }
				virtual const std::string& GetPathName() const override { return mStream->GetPathName();}
				virtual Opener*            GetOpener() const override { return mStream->GetOpener(); }
				virtual bool               GetFileDesc(int& outFd, size_t& outOffset) const override { return mStream->GetFileDesc(outFd, outOffset); }
				virtual size_t             Write(const char* inBuffer, size_t inLength) override;
			private:
				StreamSys* mStream = nullptr;
				OutputSys(StreamSys* inStream) : mStream(inStream)
				{}
				friend class OpenerSys;
			};


            

        }
    }
}

#endif//CROSSENGINE_IO_SYSTEM_H
