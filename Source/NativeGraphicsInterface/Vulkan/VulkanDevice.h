#pragma once
#include "VulkanInclude.h"
#include "NativeGraphicsInterface/NGI.h"
#include "NativeGraphicsInterface/Vulkan/VulkanSwapChain.h"
#include "NativeGraphicsInterface/Vulkan/VulkanObject.h"
#include "NativeGraphicsInterface/Vulkan/VulkanResource.h"
#include "CrossBase/Math/CrossMath.h"
#if NGI_ENABLE_GPU_DUMP
#include "NativeGraphicsInterface/Debug/NsightAftermathGpuCrashTracker.h"
#endif
#include <memory>
#include <set>

namespace cross {
class VulkanTimeStamp;
struct PhysicalDeviceInfo
{
    struct QueueInfo
    {
        uint32_t familyIndex = UINT_MAX;
        std::set<uint32_t> freeQueues;
    };

    PhysicalDeviceInfo() = default;

    PhysicalDeviceInfo(VkPhysicalDevice phyDev, VkInstance inInstance);

    QueueInfo GetQueueInfo(VkQueueFlags mandatoryFlags, VkQueueFlags optionalFlags, VkQueueFlags forbiddenBits);
    const VkPhysicalDeviceLimits& GetLimits() const
    {
        return mProps.properties.limits;
    }
    VkPhysicalDevice mPhyDev{};

    template<typename T, typename U, typename... Args>
    void LinkVulkanFeatures(T& currentFeature, U& nextFeature, Args&... remainingFeatures) {
        void* originalNext = currentFeature.pNext;
        currentFeature.pNext = &nextFeature;
        if constexpr (sizeof...(remainingFeatures) > 0) {
            LinkVulkanFeatures(nextFeature, remainingFeatures...);
        } else {
            nextFeature.pNext = originalNext;
        }
    }

    // property
    VkPhysicalDeviceProperties2 mProps{
        VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_PROPERTIES_2,
        &mVulkan11Props
    };

    VkPhysicalDeviceVulkan11Properties mVulkan11Props{
        VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_VULKAN_1_1_PROPERTIES,
        &mVulkan12Props,
    };

    VkPhysicalDeviceVulkan12Properties mVulkan12Props{
        VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_VULKAN_1_2_PROPERTIES,
        &mDescriptorIndexingProps,
    };

    VkPhysicalDeviceDescriptorIndexingPropertiesEXT mDescriptorIndexingProps{
        VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_DESCRIPTOR_INDEXING_PROPERTIES_EXT,
        &mConservativeRasterizationProps,
    };

    VkPhysicalDeviceConservativeRasterizationPropertiesEXT mConservativeRasterizationProps{
        VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_CONSERVATIVE_RASTERIZATION_PROPERTIES_EXT,
#ifdef NGI_ENABLE_RAY_TRACING
        &mAccelerationStructureProps
#endif
    };

#ifdef NGI_ENABLE_RAY_TRACING
    VkPhysicalDeviceAccelerationStructurePropertiesKHR mAccelerationStructureProps{
        VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_ACCELERATION_STRUCTURE_PROPERTIES_KHR,
        nullptr
    };
#endif

    // feature
    VkPhysicalDeviceFeatures2 mFeatures{
        VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_FEATURES_2,
        &mVulkan11Features,
    };

    VkPhysicalDeviceVulkan11Features mVulkan11Features{
        VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_VULKAN_1_1_FEATURES,
        &mVulkan12Features,
    };

    VkPhysicalDeviceVulkan12Features mVulkan12Features{
        VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_VULKAN_1_2_FEATURES,
        nullptr,
    };

    validationFeatures

    VkPhysicalDeviceCooperativeMatrixFeaturesNV mCooperativeMatrixFeature{VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_COOPERATIVE_MATRIX_FEATURES_NV, nullptr, VK_TRUE, VK_FALSE};

    VkPhysicalDeviceDescriptorIndexingFeaturesEXT mDescriptorIndexingFeature{
        VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_DESCRIPTOR_INDEXING_FEATURES_EXT,
        nullptr,
    };

    VkPhysicalDeviceBufferDeviceAddressFeaturesKHR mBufferDeviceAddressFeatures{
        VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_BUFFER_DEVICE_ADDRESS_FEATURES_KHR,
        nullptr,
    };

#ifdef NGI_ENABLE_RAY_TRACING
    // Ray Tracing Features
    VkPhysicalDeviceRayTracingPipelineFeaturesKHR mRayTracingPipelineFeatures{
        VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_RAY_TRACING_PIPELINE_FEATURES_KHR,
        nullptr,
    };
    VkPhysicalDeviceRayQueryFeaturesKHR mRayQueryFeatures{
        VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_RAY_QUERY_FEATURES_KHR,
        nullptr,
        VK_TRUE,
    };
    VkPhysicalDeviceAccelerationStructureFeaturesKHR mAccelerationStructureFeatures{
        VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_ACCELERATION_STRUCTURE_FEATURES_KHR,
        nullptr,
    };
#endif

    VkPhysicalDevicePresentIdFeaturesKHR mPresentIdFeature{
        VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_PRESENT_ID_FEATURES_KHR,
        &mPresentWaitFeature,
        VK_TRUE,
    };

    VkPhysicalDevicePresentWaitFeaturesKHR mPresentWaitFeature{
        VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_PRESENT_WAIT_FEATURES_KHR,
        nullptr,
        VK_TRUE,
    };

    VkPhysicalDeviceSynchronization2FeaturesKHR mSynchronization2Feature{
        VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SYNCHRONIZATION_2_FEATURES_KHR,
        nullptr,
    };

    // VkPhysicalDeviceBufferDeviceAddressFeaturesKHR mDeviceAddressFeature{ VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_BUFFER_DEVICE_ADDRESS_FEATURES_KHR };

    // VkPhysicalDeviceShaderFloat16Int8FeaturesKHR mFloat16Int8Feature{ VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SHADER_FLOAT16_INT8_FEATURES_KHR, &mScalarBlockLayoutFeature };

    // VkPhysicalDeviceScalarBlockLayoutFeaturesEXT mScalarBlockLayoutFeature { VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SCALAR_BLOCK_LAYOUT_FEATURES_EXT };

    // memory property
    VkPhysicalDeviceMemoryProperties mMemProp;

    std::vector<VkQueueFamilyProperties2> mQueFamilyProps;

    VkDeviceSize mDedicatedMemorySize = 0;

    QueueInfo mGraphicsQueueInfo;

    QueueInfo mComputeQueueInfo;

    QueueInfo mTransferQueueInfo;
};

class VulkanDevice : public NGIDevice
{
public:
    VulkanDevice();

    ~VulkanDevice();

    void Initialize();

    virtual VkInstance CreateInstance(VkInstanceCreateInfo& info);

    virtual std::unique_ptr<PhysicalDeviceInfo> GetPhysicalDevice();

    virtual VkDevice CreateDevice(VkDeviceCreateInfo& info);

    virtual void* GetNativeDevice() override
    {
        return mDevice;
    }

    NGIPlatform GetPlatform() override
    {
        return NGIPlatform::Vulkan;
    }

    bool NGISupportsMultithreading() override
    {
        return true;
    }

    bool IsSupportMSAA(NGIResolveType depthResolveType, NGIResolveType stencilResolveType) override;

    bool IsSupportDrawIndirectCount() override;

    void WaitIdle() override;

    NGIFormatCapability GetFormatCapability(GraphicsFormat format) override;

    NGISwapchain* CreateSwapchain(const NGISwapchainDesc& desc) override
    {
        return new VulkanSwapchain{desc, this};
    }

    NGICommandQueue* CreateCommandQueue(const NGICommandQueueDesc& desc) override
    {
        return new VulkanCommandQueue{desc, this};
    }

    NGIFence* CreateFence(UInt64 initialValue) override { return new VulkanFence(initialValue, this); }

    NGIFence* CreateTimeLineFence(UInt64 initialValue) override
    {
        if (mVulkanVersion >= VK_API_VERSION_1_2)
            return new VulkanTimelineSemaphore{initialValue, this, true};
        else
            return nullptr;
    }

    NGIRenderPass* CreateRenderPass(const NGIRenderPassDesc& desc) override
    {
        return new VulkanRenderPass{desc, this};
    }

    NGIFramebuffer* CreateFramebuffer(const NGIFramebufferDesc& desc) override
    {
        return new VulkanFramebuffer{desc, this};
    }

    NGIPipelineLayout* CreatePipelineLayout(const NGIPipelineLayoutDesc& desc) override
    {
        return new VulkanPipelineLayout{desc, this};
    }

    NGIPipelineStatePool* CreatePipelineStatePool(const NGIPipelineStatePoolDesc& desc) override
    {
        return new VulkanPipelineStatePool{desc, this};
    }

    NGIResourceGroupLayout* CreateResourceGroupLayout(const NGIResourceGroupLayoutDesc& desc) override
    {
        return new VulkanResourceGroupLayout{desc, this};
    }

    NGIResourceGroupPool* CreateResourceGroupPool(const NGIResourceGroupPoolDesc& desc) override
    {
        return new VulkanResourceGroupPool{desc, this};
    }

    NGI_API std::shared_ptr<GPUResourceStatistics> GetGpuResourceStatistics() override;

    NGI_API void DumpVideoGraphicsMemory(const std::string name) override;

    const UInt32 GetVulkanVersion() const
    {
        return mVulkanVersion;
    }

    NGIQueryHeap* CreateQueryHeap(const NGIQueryHeapDesc& desc) override;
#if NGI_ENABLE_GPU_DUMP
    bool GpuDump() const
    {
        return mGpuDump;
    }
#endif

    std::vector<std::tuple<GPUProfiling::GPUProfilingContextInfo, std::vector<GPUProfiling::GPUProfilingItem>>> PopGpuProfileItems() override;

protected:
    // resource
    NGIBuffer* CreateBufferImp(const NGIBufferDesc& desc, const char* pDebugName) override
    {
        return new VulkanBuffer{desc, this, pDebugName};
    }

    NGIBufferView* CreateBufferViewImp(NGIBuffer* pBuffer, const NGIBufferViewDesc& desc) override
    {
        return new VulkanBufferView{pBuffer, desc, this};
    }

    NGITexture* CreateTextureImp(const NGITextureDesc& desc, const char* pDebugName, bool shared) override
    {
        QUICK_SCOPED_CPU_TIMING("CreateTexture");

        return new VulkanTexture{desc, this, pDebugName, shared};
    }

    NGITextureView* CreateTextureViewImp(NGITexture* texture, const NGITextureViewDesc& desc) override
    {
        QUICK_SCOPED_CPU_TIMING("CreateTextureView");

        return new VulkanTextureView{texture, desc, this};
    }

    NGIStagingBuffer* CreateStagingBufferImp(const NGIBufferDesc& desc) override
    {
        return new VulkanStagingBuffer{desc, this};
    }

public:
    NGISampler* CreateSampler(const NGISamplerDesc& desc) override
    {
        return new VulkanSampler{desc, this};
    }

    SizeType GetTextureDataPlacementAlignment(GraphicsFormat format) override;

    SizeType GetTextureDataPitchAlignment(GraphicsFormat format) override;

    bool IsExtensionEnabled(const char* ext) const;

    bool IsMemoryBudgetEnabled() const override;

    virtual void SetFrameIndex(UInt32 frameIndex) override;

    auto Get()
    {
        return mDevice;
    }

    auto GetAllocator()
    {
        return mAllocator;
    }

    auto GetExportableVMAPool()
    {
        if (mExportablePool == nullptr)
        {
            CreateExportableVMAPool();
        }
        return mExportablePool;
    }

#ifdef NGI_ENABLE_RAY_TRACING
    auto GetRayTracingScratchBufferVMAPool()
    {
        if (mRayTracingScratchBufferPool == nullptr)
        {
            CreateRayTracingScratchBufferVMAPool();
        }
        return mRayTracingScratchBufferPool;
    }

    void CreateRayTracingScratchBufferVMAPool()
    {
        VmaPoolCreateInfo poolCreateInfo = {};

        VkBufferCreateInfo sampleBufCreateInfo = { VK_STRUCTURE_TYPE_BUFFER_CREATE_INFO };
        sampleBufCreateInfo.size = 0x01; // Doesn't matter.
        sampleBufCreateInfo.usage = VK_BUFFER_USAGE_STORAGE_BUFFER_BIT | VK_BUFFER_USAGE_SHADER_DEVICE_ADDRESS_BIT;

        VmaAllocationCreateInfo sampleAllocCreateInfo = {};
        sampleAllocCreateInfo.usage = VMA_MEMORY_USAGE_AUTO;

        uint32_t memTypeIndex;
        VkResult res = vmaFindMemoryTypeIndexForBufferInfo(mAllocator,
            &sampleBufCreateInfo, &sampleAllocCreateInfo, &memTypeIndex);

        Assert(res == VK_SUCCESS);

        poolCreateInfo.memoryTypeIndex = memTypeIndex;
        poolCreateInfo.minAllocationAlignment = mPhysicalDeviceInfo->mAccelerationStructureProps.minAccelerationStructureScratchOffsetAlignment;
            
        vmaCreatePool(mAllocator, &poolCreateInfo, &mRayTracingScratchBufferPool);
    }
#endif

    auto GetDescriptorPool()
    {
        return mDescriptorPool;
    }

    auto HasDedicatedMemory()
    {
        return mPhysicalDeviceInfo->mDedicatedMemorySize > 0;
    }

    VkInstance mInstance{};

    VkDebugUtilsMessengerEXT mDebugMessenger;

    VkPhysicalDevice mPhysicalDevice{};

    std::unique_ptr<PhysicalDeviceInfo> mPhysicalDeviceInfo;

    PFN_vkGetDeviceProcAddr vkGetDeviceProcAddrProxy = nullptr;

    std::vector<const char*> mInstExtension;
    std::vector<const char*> mDeviceExtension;
    bool mBudgetMemoryEnable = false;

    auto GetEmptyDescriptorSetLayout()
    {
        return mEmptyDescriptorSetLayout;
    }

#if NGI_ENABLE_GPU_DUMP
    auto& GetGPUCrashTracker()
    {
        return mGPUCrashTracker;
    }
#endif

    VkBuffer GetDefaultVertexBuffer()
    {
        return mDefaultVertexBuffer->mBuffer;
    }

    VulkanTimeStamp* GetVulkanTimeStamp() { return mVulkanTimeStamp.get(); }

    void SetDebugName(VkObjectType objectType, void* objectHandle, const char* pObjectName);

protected:
    void CreateExportableVMAPool()
    {
        VmaPoolCreateInfo poolCreateInfo = {};

        VkBufferCreateInfo sampleBufCreateInfo = { VK_STRUCTURE_TYPE_BUFFER_CREATE_INFO };
        sampleBufCreateInfo.size = 0x01; // Doesn't matter.
        sampleBufCreateInfo.usage = VK_BUFFER_USAGE_TRANSFER_SRC_BIT | VK_BUFFER_USAGE_TRANSFER_DST_BIT;

        VmaAllocationCreateInfo sampleAllocCreateInfo = {};
        sampleAllocCreateInfo.usage = VMA_MEMORY_USAGE_AUTO;

        uint32_t memTypeIndex;
        VkResult res = vmaFindMemoryTypeIndexForBufferInfo(mAllocator,
            &sampleBufCreateInfo, &sampleAllocCreateInfo, &memTypeIndex);

        Assert(res == VK_SUCCESS);

        poolCreateInfo.memoryTypeIndex = memTypeIndex;

        // be careful with the potential leak
        mexportMemory = new VkExportMemoryAllocateInfo{};
        mexportMemory->sType = VK_STRUCTURE_TYPE_EXPORT_MEMORY_ALLOCATE_INFO;
#if CROSSENGINE_WIN
        mexportMemory->handleTypes = VK_EXTERNAL_MEMORY_HANDLE_TYPE_OPAQUE_WIN32_BIT;
#else
        mexportMemory->handleTypes = VK_EXTERNAL_MEMORY_HANDLE_TYPE_OPAQUE_FD_BIT_KHR;
#endif
        poolCreateInfo.pMemoryAllocateNext = mexportMemory;
            
        vmaCreatePool(mAllocator, &poolCreateInfo, &mExportablePool);
    }

protected:
    VkDevice mDevice;

    UInt32 mDeviceSupportedVulkanVersion = 0;
    UInt32 mVulkanVersion = 0;

    VmaAllocator mAllocator;
    VmaPool      mExportablePool = nullptr;
#ifdef NGI_ENABLE_RAY_TRACING
    VmaPool      mRayTracingScratchBufferPool = nullptr;
#endif
    VkExportMemoryAllocateInfo* mexportMemory = nullptr;

    VkDescriptorPool mDescriptorPool;

    VkDescriptorSetLayout mEmptyDescriptorSetLayout;

    std::shared_mutex mFormatCapabilityLocker;
    std::unordered_map<GraphicsFormat, NGIFormatCapability> mFormatCapability;

    void CreateEmptyDescriptorSetLayout();

#if NGI_ENABLE_GPU_DUMP
    GpuCrashTracker::MarkerMap mMarkerMap;
    GpuCrashTracker mGPUCrashTracker{ mMarkerMap };
    bool mGpuDump{false};
#endif

    std::unique_ptr<VulkanStagingBuffer> mDefaultVertexBuffer;
    std::unique_ptr<VulkanTimeStamp> mVulkanTimeStamp;
    void CreateDefaultVertexBuffer();
};

struct VulkanCapability
{
    bool DebugUtil = false;
    bool GeometryShader = false;
    bool TessellationShader = false;
    bool Bindless = false;
    bool Synchronization2 = false;

    static VulkanCapability& Inst();
};

/// <summary>
/// ncnn lib's requirements for vulkan, remember to modify this struct if ncnn lib has updated
/// </summary>
struct NcnnVkRequirements
{
    int support_VK_KHR_external_memory_capabilities{0};
    int support_VK_KHR_get_physical_device_properties2{0};
    int support_VK_KHR_get_surface_capabilities2{0};
    int support_VK_KHR_portability_enumeration{0};
    int support_VK_KHR_surface{0};
    int support_VK_EXT_debug_utils{0};

    struct GpuInfo
    {
        bool initialized {false};
        // but sometimes bug is a feature
        bool bug_implicit_fp16_arithmetic{false};

        // fp16 and int8 feature
        bool support_fp16_packed{false};
        bool support_fp16_storage{false};
        bool support_fp16_arithmetic{false};
        bool support_int8_packed{false};
        bool support_int8_storage{false};
        bool support_int8_arithmetic{false};

        // ycbcr conversion feature
        bool support_ycbcr_conversion{false};

        // cooperative matrix
        bool support_cooperative_matrix{false};
        bool support_cooperative_matrix_16_8_8{false};

        // extension capability
        int support_VK_KHR_8bit_storage{0};
        int support_VK_KHR_16bit_storage{0};
        int support_VK_KHR_bind_memory2{0};
        int support_VK_KHR_create_renderpass2{0};
        int support_VK_KHR_dedicated_allocation{0};
        int support_VK_KHR_descriptor_update_template{0};
        int support_VK_KHR_external_memory{0};
        int support_VK_KHR_get_memory_requirements2{0};
        int support_VK_KHR_maintenance1{0};
        int support_VK_KHR_maintenance2{0};
        int support_VK_KHR_maintenance3{0};
        int support_VK_KHR_multiview{0};
        int support_VK_KHR_portability_subset{0};
        int support_VK_KHR_push_descriptor{0};
        int support_VK_KHR_sampler_ycbcr_conversion{0};
        int support_VK_KHR_shader_float16_int8{0};
        int support_VK_KHR_shader_float_controls{0};
        int support_VK_KHR_storage_buffer_storage_class{0};
        int support_VK_KHR_swapchain{0};
        int support_VK_EXT_descriptor_indexing{0};
        int support_VK_EXT_memory_budget{0};
        int support_VK_EXT_queue_family_foreign{0};
        int support_VK_NV_cooperative_matrix{0};
    };

    GpuInfo gpuInfo;

    /// <summary>
    /// Mirror version of ncnn::getNcnnInstanceExtensions, for remove dependency on ncnn lib in NGI
    /// </summary>
    /// <returns></returns>
    std::vector<const char*> GetNcnnInstanceExtensions(uint32_t instanceExtensionPropertyCount, std::vector<VkExtensionProperties>& instanceExtensionProperties);
    /// <summary>
    /// Mirror version of ncnn::setGpuInstance, core features are extracted, for remove dependency on ncnn lib in NGI
    /// </summary>
    void InitNcnnGpuInfo(VkPhysicalDevice& physicalDevices, uint32_t deviceExtensionPropertyCount, std::vector<VkExtensionProperties>& deviceExtensionProperties);
    /// <summary>
    /// Mirror version of ncnn::getNcnnDeviceExtensions, for remove dependency on ncnn lib in NGI
    /// </summary>
    /// <returns></returns>
    std::vector<const char*> GetNcnnDeviceExtensions();

    /// <summary>
    /// Mirror version of ncnn::setNcnnDeviceExtensionsFeaturesChain, for remove dependency on ncnn lib in NGI
    /// </summary>
    /// <param name="vulkan11Features"></param>
    /// <param name="vulkan12Features"></param>
    /// <param name="coopMatFeature"></param>
    void SetNcnnDeviceExtensionsFeaturesChain(VkPhysicalDeviceVulkan11Features& vulkan11Features, VkPhysicalDeviceVulkan12Features& vulkan12Features, VkPhysicalDeviceCooperativeMatrixFeaturesNV& coopMatFeature);
};

}   // namespace cross
