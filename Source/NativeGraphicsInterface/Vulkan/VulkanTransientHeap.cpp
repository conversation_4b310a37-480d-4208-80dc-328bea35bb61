#include "VulkanTransientHeap.h"
#include "Common/FrameCounter.h"
namespace cross 
{
VulkanTransientHeap::VulkanTransientHeap(NGIDevice* device, UInt64 size, UInt64 alignment, UInt32 memoryTypeBits, Initializer& initializer)
    : NGITransientHeap(device, initializer)
{
    VmaAllocationCreateInfo allocCreateInfo = {
        VMA_ALLOCATION_CREATE_DEDICATED_MEMORY_BIT,
        VMA_MEMORY_USAGE_GPU_ONLY,
    };
    // allocCreateInfo.usage = VMA_MEMORY_USAGE_AUTO;
    //allocCreateInfo.preferredFlags = VK_MEMORY_PROPERTY_DEVICE_LOCAL_BIT | VK_MEMORY_PROPERTY_LAZILY_ALLOCATED_BIT;
    VkMemoryRequirements memReq;
    Assert(initializer.mSize >= size);
    memReq.size = initializer.mSize;
    memReq.alignment = std::max(alignment, static_cast<UInt64>(initializer.mAlignment));
    memReq.memoryTypeBits = memoryTypeBits;
    VK_MEMCHECK(vmaAllocateMemory(GetDevice<VulkanDevice>()->GetAllocator(), &memReq, &allocCreateInfo, &mBuffer, nullptr), GetDevice<VulkanDevice>()->GetAllocator());
    Assert(mBuffer != nullptr);
    VmaAllocationInfo allocInfo;
    vmaGetAllocationInfo(GetDevice<VulkanDevice>()->GetAllocator(), mBuffer, &allocInfo);
    mMemoryType = allocInfo.memoryType;   // memoryTypeBits;

    vmaSetAllocationName(GetDevice<VulkanDevice>()->GetAllocator(), mBuffer, "transient heap");
    LOG_TRACE("create heap: {} with ID: {}", initializer.mSize, UInt64(mBuffer));
}
NGITransientTexture* VulkanTransientHeap::CreateTexture(const NGITextureDesc& desc, UInt64 textureSize, UInt32 textureAlignment, std::string_view name, CreateTextureFunction createFunc)
{
    //QUICK_SCOPED_CPU_TIMING("VulkanTransientHeap::CreateTexture");
    NGITransientHeapAllocation allocation = mAllocator->Allocate(textureSize, textureAlignment);
    allocation.mHeap = this;
    if (!allocation.IsValid())
    {
        return nullptr;
    }
    NGITransientTexture* tex = mTextureCache.Acquire(ComputeHash(desc, allocation.mOffset), [&](UInt64 hash) {
        auto* tex = createFunc(hash); 
        {
            QUICK_SCOPED_CPU_TIMING("vmaBindImageMemory2");
            VK_CHECK(vmaBindImageMemory2(GetDevice<VulkanDevice>()->GetAllocator(), mBuffer, allocation.mOffset, tex->GetImage(), nullptr));
            tex->mNativeTexture->mAllocation = mBuffer;
        }
        #if 0
            {
                auto frameNumber = frame::GetRenderingFrameNumber();
                VmaAllocationInfo allocInfo;
                vmaGetAllocationInfo(GetDevice<VulkanDevice>()->GetAllocator(), mBuffer, &allocInfo);
                std::string tmpName;
                if (allocInfo.pName)
                {
                    tmpName = std::string(allocInfo.pName) + "; ";
                }
                tmpName += std::string(name) + ": " + std::to_string(frameNumber) + " [" + std::to_string(allocation.mOffset) + ", " + std::to_string(allocation.mOffset + allocation.mSize) + ")";
                vmaSetAllocationName(GetDevice<VulkanDevice>()->GetAllocator(), mBuffer, tmpName.c_str());
            }
        #endif
        return tex;
    });
    Assert(tex && tex->IsAcquired());
    tex->CreateStates(desc);
    tex->GetAllocation() = allocation;
    mStats.AllocateTexture(allocation.mSize);
    return tex;
}
NGITransientBuffer* VulkanTransientHeap::CreateBuffer(const NGIBufferDesc& desc, UInt64 size, UInt32 alignment, std::string_view name, CreateBufferFunction createFunc)
{
    //QUICK_SCOPED_CPU_TIMING("VulkanTransientHeap::CreateBuffer");
    NGITransientHeapAllocation allocation = mAllocator->Allocate(size, alignment);
    allocation.mHeap = this;
    if (!allocation.IsValid())
    {
        return nullptr;
    }
    NGITransientBuffer* buf = mBufferCache.Acquire(ComputeHash(desc, allocation.mOffset), [&](UInt64 hash) {
        auto* buf = createFunc(hash);
        {
            QUICK_SCOPED_CPU_TIMING("vmaBindBufferMemory2");
            VK_CHECK(vmaBindBufferMemory2(GetDevice<VulkanDevice>()->GetAllocator(), mBuffer, allocation.mOffset, buf->GetBuffer(), nullptr));
            buf->mNativeBuffer->mAllocation = mBuffer;
        }
        return buf;    
    });
    Assert(buf && buf->IsAcquired());
    buf->CreateStates(desc);
    buf->GetAllocation() = allocation;
    mStats.AllocateBuffer(allocation.mSize);
    return buf;
}
}