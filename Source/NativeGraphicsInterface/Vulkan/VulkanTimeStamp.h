#pragma once
#include "VulkanInclude.h"
#include <unordered_map>
#include <shared_mutex>
namespace cross {
class VulkanDevice;
struct VulkanCommandQueue;

class VulkanTimeStamp
{
public:
    VulkanTimeStamp();
    ~VulkanTimeStamp();
    auto OnCreateVkCommandBuffer(VulkanCommandQueue* queue, VkCommandBuffer cmdbuf) -> void;
    auto OnBeginCommandBuffer(VulkanCommandQueue* queue, VkCommandBuffer cmdbuf) -> void;
    auto OnEndCommandBuffer(VulkanCommandQueue* queue, VkCommandBuffer cmdbuf) -> void;
    auto OnBeginDebugRegion(VulkanCommandQueue* queue, VkCommandBuffer cmdbuf, const char* label) -> void;
    auto OnEndDebugRegion(VulkanCommandQueue* queue, VkCommandBuffer cmdbuf) -> void;
    auto OnExecuteCommandLists(VulkanCommandQueue* queue, std::span<VkCommandBuffer> cmdbufList) -> void;
    auto GetGpuProfilingItems() -> std::vector<std::tuple<GPUProfiling::GPUProfilingContextInfo, std::vector<GPUProfiling::GPUProfilingItem>>>;

private:
    bool mEnable;
    class ProfileContext;

    std::shared_mutex mProfileContextMutex;
    std::unordered_map<VkQueue, std::unique_ptr<ProfileContext>> mProfileContextMap;

    // per VkCommandBuffer, Assert OnEndCommandBuffer stack is empty
    std::shared_mutex mCommandBufferMutex;
    std::unordered_map<VkCommandBuffer, std::deque<GPUProfiling::GPUProfilingItem>> mRecordProfileQueueMap;

    // to record total queue execute gpu time
    friend VulkanCommandQueue;
    auto SetQueueExecuteRegion(VulkanCommandQueue* queue, std::vector<VkCommandBuffer>& vkCommandBuffers) -> void;
};

}   // namespace cross