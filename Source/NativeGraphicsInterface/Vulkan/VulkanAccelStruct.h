#pragma once

#include "VulkanResource.h"
#include "VulkanDevice.h"


namespace cross {

struct VulkanAccelStruct : NGIAccelStruct
{
    VulkanAccelStruct(const NGIAccelStructDesc& desc, NGIDevice* device, const char* pDebugName = "");

    ~VulkanAccelStruct() override;
    
    UInt32 GetTopLevelUploadBufferSize() const override
    {
        UInt32 size = static_cast<UInt32>(sizeof(VkAccelerationStructureInstanceKHR) * mInstances.size());
        return size;
    }

    UInt32 GetTopLevelMaxInstanceCount() const override
    {
        return static_cast<UInt32>(mInstances.size());
    }
    
    VulkanBuffer* mDataBuffer;
    std::vector<VkAccelerationStructureInstanceKHR> mInstances;
    VkAccelerationStructureKHR mAccelStruct = VK_NULL_HANDLE;
    VkDeviceAddress mAccelStructDeviceAddress;
    bool mAllowUpdate = false;
    bool mAllowCompact = false;
};

}
