#include "VulkanTransientResourceAllocator.h"
#include "VulkanDevice.h"
namespace cross {
VkMemoryRequirements GetMemoryRequirements(const NGITextureDesc& desc, VulkanDevice* device, VkImageCreateInfo& imageCreateInfo)
{
    imageCreateInfo = VulkanTexture::CreateImageCreateInfo(desc);
    static std::unordered_map<NGITextureDesc, VkMemoryRequirements, NGIObjectDescHasher> memoryRequirementsMap;
    auto it = memoryRequirementsMap.find(desc);
    if (it != memoryRequirementsMap.end())
    {
        return it->second;
    }
    VkMemoryRequirements outMemReq;
    if (device->IsExtensionEnabled(VK_KHR_MAINTENANCE_4_EXTENSION_NAME))
    {
        QUICK_SCOPED_CPU_TIMING("VkMemoryRequirements");
        VkDeviceImageMemoryRequirementsKHR imageMemReq;
        memset(&imageMemReq, 0, sizeof(imageMemReq));
        imageMemReq.sType = VK_STRUCTURE_TYPE_DEVICE_IMAGE_MEMORY_REQUIREMENTS;
        imageMemReq.pCreateInfo = &imageCreateInfo;
        imageMemReq.planeAspect = (GraphicsFormat2ImageAspect(desc.Format) == VK_IMAGE_ASPECT_COLOR_BIT) ? VK_IMAGE_ASPECT_COLOR_BIT : VK_IMAGE_ASPECT_DEPTH_BIT;
        VkMemoryRequirements2KHR memReq2;
        memset(&memReq2, 0, sizeof(memReq2));
        memReq2.sType = VK_STRUCTURE_TYPE_MEMORY_REQUIREMENTS_2;
        vkGetDeviceImageMemoryRequirementsKHR(reinterpret_cast<VkDevice>(device->GetNativeDevice()), &imageMemReq, &memReq2);
        outMemReq = memReq2.memoryRequirements;
    }
    else
    {
        QUICK_SCOPED_CPU_TIMING("vkCreateImage vkDestroyImage");
        VkImage tmpImage;
        vkCreateImage(reinterpret_cast<VkDevice>(device->GetNativeDevice()), &imageCreateInfo, nullptr, &tmpImage);
        vkGetImageMemoryRequirements(reinterpret_cast<VkDevice>(device->GetNativeDevice()), tmpImage, &outMemReq);
        vkDestroyImage(reinterpret_cast<VkDevice>(device->GetNativeDevice()), tmpImage, nullptr);
    }
    memoryRequirementsMap[desc] = outMemReq;
    return outMemReq;
}
NGITransientTexture* VulkanTransientResourceAllocator::CreateTexture(const NGITextureDesc& desc, std::string_view name)
{
    //QUICK_SCOPED_CPU_TIMING("VulkanTransientResourceAllocator::CreateTexture");
    VkImageCreateInfo imageCreateInfo;
    VkMemoryRequirements outMemReq = GetMemoryRequirements(desc, GetDevice<VulkanDevice>(), imageCreateInfo);

    return CreateTextureInternal(desc, outMemReq.size, outMemReq.alignment, outMemReq.memoryTypeBits, name, [&](UInt64 hash) {
        VulkanTexture* nativeTexture = new VulkanTexture(desc, imageCreateInfo, GetDevice<VulkanDevice>(), name.data());
        return new NGITransientTexture(nativeTexture, hash, name);
    });
}
NGITransientBuffer* VulkanTransientResourceAllocator::CreateBuffer(const NGIBufferDesc& desc, std::string_view name)
{
    //QUICK_SCOPED_CPU_TIMING("VulkanTransientResourceAllocator::CreateBuffer");
    const bool bZeroSize = (desc.Size == 0);
    const UInt32 alignment = VulkanBuffer::CalculateBufferAlignment(GetDevice<VulkanDevice>(), desc.Usage, bZeroSize);
    UInt64 size = AlignUp(desc.Size, static_cast<SizeType>(alignment));

    // auto device = GetDevice<VulkanDevice>();
    // VkMemoryRequirements outMemReq;
    // VkBuffer tmpBuffer;
    VkBufferCreateInfo createInfo{
        VK_STRUCTURE_TYPE_BUFFER_CREATE_INFO,
        nullptr,
        0,
        desc.Size,
        MapBufferUsage(desc.Usage),
        VK_SHARING_MODE_EXCLUSIVE,
        0,
        nullptr,
    };
    // vkCreateBuffer(reinterpret_cast<VkDevice>(device->GetNativeDevice()), &createInfo, nullptr, &tmpBuffer);
    // vkGetBufferMemoryRequirements(reinterpret_cast<VkDevice>(device->GetNativeDevice()), tmpBuffer, &outMemReq);
    // vkDestroyBuffer(reinterpret_cast<VkDevice>(device->GetNativeDevice()), tmpBuffer, nullptr);
    // Assert(size == outMemReq.size && alignment == outMemReq.alignment);
    return CreateBufferInternal(desc, size, alignment, ~0u, name, [&](UInt64 hash) { 
        VulkanBuffer* nativeBuffer = new VulkanBuffer(desc, GetDevice<VulkanDevice>(), false, name.data());
        return new NGITransientBuffer(nativeBuffer, hash, name);
    });
}
}