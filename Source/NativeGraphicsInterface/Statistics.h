#pragma once
#include <string>
#include <map>
#include <vector>
#include "CrossBase/Platform/PlatformTypes.h"
#include "CrossBase/Template/TypeTraits.hpp"
#include "CrossBase/Serialization/SerializeNode.h"
#include "CrossBase/Threading/TaskSystem.h"

#include "NGI.h"

namespace cross
{
    struct NGIObject;
    struct NGITexture;
    struct NGIBuffer;

    struct NGI_API MeshLODStatistics
    {
        float VertexBufferSize = 0.0f;
        float IndexBufferSize = 0.0f;
        UInt32 LastSelectedFrameCount = 0;
    };

    struct NGI_API MeshStatistics
    {
        std::string DebugName = "";
        std::array<MeshLODStatistics, 8> LODStats;
        float TotalVertexBufferSize = 0.0f;
        float TotalIndexBufferSize = 0.0f;
    };

    class NGI_API EngineMeshStatistics
    {
    public:
        static EngineMeshStatistics& GetInstance();

        void AddMeshStat(const std::string& debugName, const MeshStatistics& stat);
        void EnsureMeshStat(const std::string& debugName, float debugSize = 0.0f);
        void ReleaseMeshStat(const std::string& debugName, float debugSize = 0.0f);

        void SetMeshLOD(UInt32 frameCount, const std::string& debugName, UInt8 lodIndex);
        void OutputMeshStat();

    private:
        EngineMeshStatistics() = default;
        ~EngineMeshStatistics() = default;

        std::unordered_map<std::string, MeshStatistics> mStats;
        float mTotalSize = 0.0f;
        UInt32 mFrameCount = 0;
        float mCurrentFrameLODSize = 0.0f;

        bool mFirstWrite = true;
        std::string mFirstWriteTime = "";
        mutable std::mutex mMutex;
    };

    // keep resource group, for possible texture group, geometry group settings in future
    struct ResourceStatisticGroup
    {
        std::string groupName = "Default";
        float total_size = 0.f; // in mega bytes;
        float total_released = 0.0f;
        std::map<NGIObject*, float> mResourceCaches;

        template<class T>
        void AddResouce(T* texture)
        {
            if (mResourceCaches.count(texture) == 0)
            {
                mResourceCaches[texture] = static_cast<float>(texture->GetSize()) / 1024.f / 1024.f;
                total_size += static_cast<float>(texture->GetSize()) / 1024.f / 1024.f;
            }
        }

        template<class T>
        bool RemoveResource(T* res)
        {
            if (mResourceCaches.count(res))
            {
                total_size -= mResourceCaches[res];
                total_released += mResourceCaches[res];
                mResourceCaches.erase(res);
                return true;
            }

            return false;
        }

        std::vector<std::pair<float, NGIObject*>> TopTen() const
        {
            std::vector<std::pair<float, NGIObject*>> ret;
            for (auto& itr : mResourceCaches)
            {
                ret.push_back(std::make_pair(itr.second, itr.first));
            }

            std::sort(ret.begin(), ret.end(), [](const std::pair<float, NGIObject*>& a, const std::pair<float, NGIObject*>& b) {
                return a.first > b.first;
                });
            ret.resize(10);

            return ret;
        }

        SerializeNode ToJson() const
        {
            SerializeNode statics_group = {
                "GroupName"_k = groupName,
            "TotalSize"_k = total_size,
            "TotalReleased"_k = total_released,
            "NumResource"_k = static_cast<UInt32>(mResourceCaches.size())
            };

            SerializeNode resources;
            for (auto& itr : mResourceCaches)
            {
                if (itr.first)
                {
                    SerializeNode record;
                    if (itr.first->GetDebugName() != nullptr)
                    {
                        record[itr.first->GetDebugName()] = itr.second;
                    }
                    else
                    {
                        record["no_name"] = itr.second;
                    }
                    
                    resources.PushBack(std::move(record));
                }
            }

            statics_group["resources"] = std::move(resources);

            return std::move(statics_group);
        }
    };


    enum class ResourceType : UInt32
    {
        Geometry,
        Texture,
        RT,
        StructuredBuffer,
        UniformBuffer,
        Staging,
        Count
    };

    inline std::string GetName(ResourceType type)
    {
        static std::vector<std::string> names = {
            "Geometry", "Texture", "RT", "StructuredBuffer", "UniformBuffer", "Staging", "Count"
        };
        return names[ToUnderlying(type)];
    }


    // description for GPU
    // copy from VMA budgets
    struct DeviceResources
    {
        float usage = 0; // used by this application
        float available = 0; // avaliable device memory for this GPU
        
        float allocated = 0;
        float used = 0;
    };


    struct GPUResourceStatistics
    {
        GPUResourceStatistics() = default;
        float mTotalSize = 0;
        DeviceResources mDeviceResources;

        mutable std::mutex mResourceMutex;

        std::vector<ResourceStatisticGroup> resources[ToUnderlying(ResourceType::Count)];
        template<class T>
        void AddResource(T* texture)
        {
            // Because multiple threads (not only the render thread)  can create resource,
            // without proper lock, it crashes on some android devices.
            // currently we only enable this in windows for future fix (maybe never)
#if CROSSENGINE_WIN
            auto resource_type = ToResourceType(texture);

            std::scoped_lock lock_(mResourceMutex);
            if (resources[ToUnderlying(resource_type)].empty())
            {
                resources[ToUnderlying(resource_type)].resize(1);
            }

            mTotalSize -= resources[ToUnderlying(resource_type)][0].total_size;
            resources[ToUnderlying(resource_type)][0].AddResouce(texture);
            //if (resource_type == ResourceType::Geometry)
            //{
            //    EngineMeshStatistics::GetInstance().EnsureMeshStat(texture->GetDebugName(), resources[ToUnderlying(resource_type)][0].total_size);
            //}
            mTotalSize += resources[ToUnderlying(resource_type)][0].total_size;
#endif
        }

        template<class T>
        void ReleaseResource(T* resource)
        {
#ifdef CROSSENGINE_WIN
            for (UInt32 i = 0; i < ToUnderlying(ResourceType::Count); i++)
            {
                std::scoped_lock lock_(mResourceMutex);
                for (auto& itr : resources[i])
                {
                    auto origin_size = itr.total_size;
                    if (itr.RemoveResource(resource))
                    {
                        //if (i == ToUnderlying(ResourceType::Geometry))
                        //{
                        //    EngineMeshStatistics::GetInstance().ReleaseMeshStat(resource->GetDebugName(), origin_size - itr.total_size);
                        //}

                        mTotalSize = mTotalSize - origin_size + itr.total_size;
                        return;
                    }
                }
            }
#endif
        }

        NGI_API ResourceType ToResourceType(NGITexture* texture) const;

        NGI_API ResourceType ToResourceType(NGIBuffer* buffer) const;

        std::string GetGroup(ResourceType type) const
        {
            UInt32 i = ToUnderlying(type);
            std::string ret;
            ret += "Resource: " + GetName(type) +" ";
            for (UInt32 j = 0; j < resources[i].size(); j++)
            {
                ret += "GroupName: " + resources[i][j].groupName + "\n";
                ret += " Size: " + std::to_string(resources[i][j].total_size) + "\n";
                ret += " Num: " + std::to_string(resources[i][j].mResourceCaches.size()) + "\n";
                ret += " Released: " + std::to_string(resources[i][j].total_released) + "\n";
            }
            return ret;
        }
        
        float GetGroupSize(ResourceType type) const
        {
            UInt32 i = ToUnderlying(type);
            float size = 0;
            for (UInt32 j = 0; j < resources[i].size(); j++)
            {
                size += resources[i][j].total_size;
            }
            return size;
        }

        std::string GetSummary() const
        {
            std::string ret = "TotalSize: " + std::to_string(mTotalSize) + "\n";
            ret += " DeviceUsage: " + std::to_string(mDeviceResources.usage) + "\n";
            ret += " DeviceAvaliable: " + std::to_string(mDeviceResources.available) + "\n";
            ret += " DeviceAllocated: " + std::to_string(mDeviceResources.allocated) + "\n";
            ret += " mDeviceUsd: " + std::to_string(mDeviceResources.used) + "\n";

            return ret;
        }

        std::vector<std::string> GetTopTen(ResourceType type)
        {
            UInt32 i = ToUnderlying(type);
            std::vector<std::string> ret;
            
            for (UInt32 j = 0; j < resources[i].size(); j++)
            {
                auto top10 = resources[i][j].TopTen();
                for (auto& itr : top10)
                {
                    if (itr.second && itr.first)
                    {
                        auto result = std::string(itr.second->GetDebugName()) + " " + std::to_string(itr.first);
                        ret.push_back(result);
                    }
                }
            }

            return ret;
        }

        SerializeNode ToJson() const
        {
            SerializeNode ret;
            for (UInt32 i = 0; i < (UInt32)ToUnderlying(ResourceType::Count); i++)
            {
                SerializeNode node = {
                    "ResourceType"_k = GetName(ResourceType(i))
                };
                SerializeNode array_node;
                for (UInt32 j = 0; j < resources[i].size(); j++)
                {
                    array_node.PushBack(std::move(resources[i][j].ToJson()));
                }

                node["groups"] = std::move(array_node);

                ret.PushBack(std::move(node));
            }
            return std::move(ret);
        }
    };
}

