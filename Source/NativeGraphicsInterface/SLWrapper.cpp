#pragma warning(push)
#pragma warning(disable : 4471)
#include "SLWrapper.h"
#ifdef WIN32
#include <sl_security.h>

namespace cross {
template<typename T, int N>
char (& dim_helper(T (&)[N]))[N];
#define dim(x) (sizeof(dim_helper(x)))

std::wstring GetDllLocation(std::wstring dllName)
{
    wchar_t path[260] = {0};
#ifdef _WIN32
    if (GetModuleFileNameW(nullptr, path, dim(path)) == 0)
        return std::wstring();
#else   // _WIN32
#    error Unsupported platform for GetSlInterposerDllLocation!
#endif   // _WIN32

    auto basePath = std::filesystem::path(path).parent_path();
    auto dllPath = basePath / dllName;
    return dllPath;
}

void SLWrapper::EraseViewport(void* ptr)
{
    if (!m_sl_initialised || m_viewports.find(ptr) == m_viewports.end())
    {
        return;
    }
    sl::ResourceTag inputs[] = {sl::ResourceTag{nullptr, sl::kBufferTypeDepth, sl::ResourceLifecycle::eValidUntilPresent},
                                sl::ResourceTag{nullptr, sl::kBufferTypeMotionVectors, sl::ResourceLifecycle::eValidUntilPresent},
                                sl::ResourceTag{nullptr, sl::kBufferTypeScalingInputColor, sl::ResourceLifecycle::eValidUntilPresent},
                                sl::ResourceTag{nullptr, sl::kBufferTypeScalingOutputColor, sl::ResourceLifecycle::eValidUntilPresent}
                                //sl::ResourceTag{nullptr, sl::kBufferTypeHUDLessColor, sl::ResourceLifecycle::eValidUntilPresent}
    };

    successCheck(pslSetTag(m_viewports[ptr], inputs, _countof(inputs), nullptr), "slSetTag_clear");
    CleanUpDLSS(m_viewports[ptr], true);
    m_viewports.erase(ptr);

}

SLWrapper& SLWrapper::Get()
{
    static SLWrapper instance;
    return instance;
}

VkFormat SLWrapper::MapGraphicsFormat(GraphicsFormat format)
{
    switch (format)
    {
    case GraphicsFormat::Unknown:
        return VK_FORMAT_UNDEFINED;
    case GraphicsFormat::R8_SRGB:
        return VK_FORMAT_R8_SRGB;
    case GraphicsFormat::R8G8_SRGB:
        return VK_FORMAT_R8G8_SRGB;
    case GraphicsFormat::R8G8B8_SRGB:
        return VK_FORMAT_R8G8B8_SRGB;
    case GraphicsFormat::R8G8B8A8_SRGB:
        return VK_FORMAT_R8G8B8A8_SRGB;
    case GraphicsFormat::R8_UNorm:
        return VK_FORMAT_R8_UNORM;
    case GraphicsFormat::R8G8_UNorm:
        return VK_FORMAT_R8G8_UNORM;
    case GraphicsFormat::R8G8B8_UNorm:
        return VK_FORMAT_R8G8B8_UNORM;
    case GraphicsFormat::R8G8B8A8_UNorm:
        return VK_FORMAT_R8G8B8A8_UNORM;
    case GraphicsFormat::R8_SNorm:
        return VK_FORMAT_R8_SNORM;
    case GraphicsFormat::R8G8_SNorm:
        return VK_FORMAT_R8G8_SNORM;
    case GraphicsFormat::R8G8B8_SNorm:
        return VK_FORMAT_R8G8B8_SNORM;
    case GraphicsFormat::R8G8B8A8_SNorm:
        return VK_FORMAT_R8G8B8A8_SNORM;
    case GraphicsFormat::R8_UInt:
        return VK_FORMAT_R8_UINT;
    case GraphicsFormat::R8G8_UInt:
        return VK_FORMAT_R8G8_UINT;
    case GraphicsFormat::R8G8B8_UInt:
        return VK_FORMAT_R8G8B8_UINT;
    case GraphicsFormat::R8G8B8A8_UInt:
        return VK_FORMAT_R8G8B8A8_UINT;
    case GraphicsFormat::R8_SInt:
        return VK_FORMAT_R8_SINT;
    case GraphicsFormat::R8G8_SInt:
        return VK_FORMAT_R8G8_SINT;
    case GraphicsFormat::R8G8B8_SInt:
        return VK_FORMAT_R8G8B8_SINT;
    case GraphicsFormat::R8G8B8A8_SInt:
        return VK_FORMAT_R8G8B8A8_SINT;
    case GraphicsFormat::R16_UNorm:
        return VK_FORMAT_R16_UNORM;
    case GraphicsFormat::R16G16_UNorm:
        return VK_FORMAT_R16G16_UNORM;
    case GraphicsFormat::R16G16B16_UNorm:
        return VK_FORMAT_R16G16B16_UNORM;
    case GraphicsFormat::R16G16B16A16_UNorm:
        return VK_FORMAT_R16G16B16A16_UNORM;
    case GraphicsFormat::R16_SNorm:
        return VK_FORMAT_R16_SNORM;
    case GraphicsFormat::R16G16_SNorm:
        return VK_FORMAT_R16G16_SNORM;
    case GraphicsFormat::R16G16B16_SNorm:
        return VK_FORMAT_R16G16B16_SNORM;
    case GraphicsFormat::R16G16B16A16_SNorm:
        return VK_FORMAT_R16G16B16A16_SNORM;
    case GraphicsFormat::R16_UInt:
        return VK_FORMAT_R16_UINT;
    case GraphicsFormat::R16G16_UInt:
        return VK_FORMAT_R16G16_UINT;
    case GraphicsFormat::R16G16B16_UInt:
        return VK_FORMAT_R16G16B16_UINT;
    case GraphicsFormat::R16G16B16A16_UInt:
        return VK_FORMAT_R16G16B16A16_UINT;
    case GraphicsFormat::R16_SInt:
        return VK_FORMAT_R16_SINT;
    case GraphicsFormat::R16G16_SInt:
        return VK_FORMAT_R16G16_SINT;
    case GraphicsFormat::R16G16B16_SInt:
        return VK_FORMAT_R16G16B16_SINT;
    case GraphicsFormat::R16G16B16A16_SInt:
        return VK_FORMAT_R16G16B16A16_SINT;
    case GraphicsFormat::R32_UInt:
        return VK_FORMAT_R32_UINT;
    case GraphicsFormat::R32G32_UInt:
        return VK_FORMAT_R32G32_UINT;
    case GraphicsFormat::R32G32B32_UInt:
        return VK_FORMAT_R32G32B32_UINT;
    case GraphicsFormat::R32G32B32A32_UInt:
        return VK_FORMAT_R32G32B32A32_UINT;
    case GraphicsFormat::R32_SInt:
        return VK_FORMAT_R32_SINT;
    case GraphicsFormat::R32G32_SInt:
        return VK_FORMAT_R32G32_SINT;
    case GraphicsFormat::R32G32B32_SInt:
        return VK_FORMAT_R32G32B32_SINT;
    case GraphicsFormat::R32G32B32A32_SInt:
        return VK_FORMAT_R32G32B32A32_SINT;
    case GraphicsFormat::R16_SFloat:
        return VK_FORMAT_R16_SFLOAT;
    case GraphicsFormat::R16G16_SFloat:
        return VK_FORMAT_R16G16_SFLOAT;
    case GraphicsFormat::R16G16B16_SFloat:
        return VK_FORMAT_R16G16B16_SFLOAT;
    case GraphicsFormat::R16G16B16A16_SFloat:
        return VK_FORMAT_R16G16B16A16_SFLOAT;
    case GraphicsFormat::R32_SFloat:
        return VK_FORMAT_R32_SFLOAT;
    case GraphicsFormat::R32G32_SFloat:
        return VK_FORMAT_R32G32_SFLOAT;
    case GraphicsFormat::R32G32B32_SFloat:
        return VK_FORMAT_R32G32B32_SFLOAT;
    case GraphicsFormat::R32G32B32A32_SFloat:
        return VK_FORMAT_R32G32B32A32_SFLOAT;
    case GraphicsFormat::B8G8R8_SRGB:
        return VK_FORMAT_B8G8R8_SRGB;
    case GraphicsFormat::B8G8R8A8_SRGB:
        return VK_FORMAT_B8G8R8A8_SRGB;
    case GraphicsFormat::B8G8R8_UNorm:
        return VK_FORMAT_B8G8R8_UNORM;
    case GraphicsFormat::B8G8R8A8_UNorm:
        return VK_FORMAT_B8G8R8A8_UNORM;
    case GraphicsFormat::B8G8R8_SNorm:
        return VK_FORMAT_B8G8R8_SNORM;
    case GraphicsFormat::B8G8R8A8_SNorm:
        return VK_FORMAT_B8G8R8A8_SNORM;
    case GraphicsFormat::B8G8R8_UInt:
        return VK_FORMAT_B8G8R8_UINT;
    case GraphicsFormat::B8G8R8A8_UInt:
        return VK_FORMAT_B8G8R8A8_UINT;
    case GraphicsFormat::B8G8R8_SInt:
        return VK_FORMAT_B8G8R8_SINT;
    case GraphicsFormat::B8G8R8A8_SInt:
        return VK_FORMAT_B8G8R8A8_SINT;

    case GraphicsFormat::R4G4B4A4_UNormPack16:
        return VK_FORMAT_R4G4B4A4_UNORM_PACK16;
    case GraphicsFormat::R5G6B5_UNormPack16:
        return VK_FORMAT_R5G6B5_UNORM_PACK16;
    case GraphicsFormat::R5G5B5A1_UNormPack16:
        return VK_FORMAT_R5G5B5A1_UNORM_PACK16;

    case GraphicsFormat::R9G9B9E5_UFloatPack32:
        return VK_FORMAT_E5B9G9R9_UFLOAT_PACK32;
    case GraphicsFormat::R11G11B10_UFloatPack32:
        return VK_FORMAT_B10G11R11_UFLOAT_PACK32;
    case GraphicsFormat::A2B10G10R10_UNormPack32:
        return VK_FORMAT_A2B10G10R10_UNORM_PACK32;
    case GraphicsFormat::A2B10G10R10_UIntPack32:
        return VK_FORMAT_A2B10G10R10_UINT_PACK32;
    case GraphicsFormat::A2B10G10R10_SIntPack32:
        return VK_FORMAT_A2B10G10R10_SINT_PACK32;
    case GraphicsFormat::A2R10G10B10_UNormPack32:
        return VK_FORMAT_A2R10G10B10_UNORM_PACK32;
    case GraphicsFormat::A2R10G10B10_UIntPack32:
        return VK_FORMAT_A2R10G10B10_UINT_PACK32;
    case GraphicsFormat::A2R10G10B10_SIntPack32:
        return VK_FORMAT_A2R10G10B10_SINT_PACK32;

    case GraphicsFormat::A2R10G10B10_XRSRGBPack32:
    case GraphicsFormat::A2R10G10B10_XRUNormPack32:
    case GraphicsFormat::R10G10B10_XRSRGBPack32:
    case GraphicsFormat::R10G10B10_XRUNormPack32:
    case GraphicsFormat::A10R10G10B10_XRSRGBPack32:
    case GraphicsFormat::A10R10G10B10_XRUNormPack32:
        Assert(false);
        return VK_FORMAT_UNDEFINED;

    case GraphicsFormat::D16_UNorm:
        return VK_FORMAT_D16_UNORM;
    case GraphicsFormat::D24_UNorm_X8:
        return VK_FORMAT_X8_D24_UNORM_PACK32;
    case GraphicsFormat::D24_UNorm_S8_UInt:
        return VK_FORMAT_D24_UNORM_S8_UINT;
    case GraphicsFormat::D32_SFloat:
        return VK_FORMAT_D32_SFLOAT;
    case GraphicsFormat::D32_SFloat_S8_UInt:
        return VK_FORMAT_D32_SFLOAT_S8_UINT;
    case GraphicsFormat::S8_UInt:
        return VK_FORMAT_S8_UINT;

    case GraphicsFormat::RGB_BC1_SRGB:
        return VK_FORMAT_BC1_RGB_SRGB_BLOCK;
    case GraphicsFormat::RGB_BC1_UNorm:
        return VK_FORMAT_BC1_RGB_UNORM_BLOCK;
    case GraphicsFormat::RGBA_BC1_SRGB:
        return VK_FORMAT_BC1_RGBA_SRGB_BLOCK;
    case GraphicsFormat::RGBA_BC1_UNorm:
        return VK_FORMAT_BC1_RGBA_UNORM_BLOCK;
    case GraphicsFormat::RGBA_BC2_SRGB:
        return VK_FORMAT_BC2_SRGB_BLOCK;
    case GraphicsFormat::RGBA_BC2_UNorm:
        return VK_FORMAT_BC2_UNORM_BLOCK;
    case GraphicsFormat::RGBA_BC3_SRGB:
        return VK_FORMAT_BC3_SRGB_BLOCK;
    case GraphicsFormat::RGBA_BC3_UNorm:
        return VK_FORMAT_BC3_UNORM_BLOCK;
    case GraphicsFormat::R_BC4_UNorm:
        return VK_FORMAT_BC4_UNORM_BLOCK;
    case GraphicsFormat::R_BC4_SNorm:
        return VK_FORMAT_BC4_SNORM_BLOCK;
    case GraphicsFormat::RG_BC5_UNorm:
        return VK_FORMAT_BC5_UNORM_BLOCK;
    case GraphicsFormat::RG_BC5_SNorm:
        return VK_FORMAT_BC5_SNORM_BLOCK;
    case GraphicsFormat::RGB_BC6H_UFloat:
        return VK_FORMAT_BC6H_UFLOAT_BLOCK;
    case GraphicsFormat::RGB_BC6H_SFloat:
        return VK_FORMAT_BC6H_SFLOAT_BLOCK;
    case GraphicsFormat::RGBA_BC7_SRGB:
        return VK_FORMAT_BC7_SRGB_BLOCK;
    case GraphicsFormat::RGBA_BC7_UNorm:
        return VK_FORMAT_BC7_UNORM_BLOCK;

    case GraphicsFormat::RGB_PVRTC_2Bpp_SRGB:
        return VK_FORMAT_PVRTC1_2BPP_SRGB_BLOCK_IMG;
    case GraphicsFormat::RGB_PVRTC_2Bpp_UNorm:
        return VK_FORMAT_PVRTC1_2BPP_UNORM_BLOCK_IMG;
    case GraphicsFormat::RGB_PVRTC_4Bpp_SRGB:
        return VK_FORMAT_PVRTC1_4BPP_SRGB_BLOCK_IMG;
    case GraphicsFormat::RGB_PVRTC_4Bpp_UNorm:
        return VK_FORMAT_PVRTC1_4BPP_UNORM_BLOCK_IMG;
    case GraphicsFormat::RGBA_PVRTC_2Bpp_SRGB:
        return VK_FORMAT_PVRTC2_2BPP_SRGB_BLOCK_IMG;
    case GraphicsFormat::RGBA_PVRTC_2Bpp_UNorm:
        return VK_FORMAT_PVRTC2_2BPP_UNORM_BLOCK_IMG;
    case GraphicsFormat::RGBA_PVRTC_4Bpp_SRGB:
        return VK_FORMAT_PVRTC2_4BPP_SRGB_BLOCK_IMG;
    case GraphicsFormat::RGBA_PVRTC_4Bpp_UNorm:
        return VK_FORMAT_PVRTC2_4BPP_UNORM_BLOCK_IMG;

    case GraphicsFormat::RGB_ETC2_SRGB:
        return VK_FORMAT_ETC2_R8G8B8_SRGB_BLOCK;
    case GraphicsFormat::RGB_ETC2_UNorm:
        return VK_FORMAT_ETC2_R8G8B8_UNORM_BLOCK;
    case GraphicsFormat::RGB_A1_ETC2_SRGB:
        return VK_FORMAT_ETC2_R8G8B8A1_SRGB_BLOCK;
    case GraphicsFormat::RGB_A1_ETC2_UNorm:
        return VK_FORMAT_ETC2_R8G8B8A1_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ETC2_SRGB:
        return VK_FORMAT_ETC2_R8G8B8A8_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ETC2_UNorm:
        return VK_FORMAT_ETC2_R8G8B8A8_UNORM_BLOCK;
    case GraphicsFormat::R_EAC_UNorm:
        return VK_FORMAT_EAC_R11_UNORM_BLOCK;
    case GraphicsFormat::R_EAC_SNorm:
        return VK_FORMAT_EAC_R11_SNORM_BLOCK;
    case GraphicsFormat::RG_EAC_UNorm:
        return VK_FORMAT_EAC_R11G11_UNORM_BLOCK;
    case GraphicsFormat::RG_EAC_SNorm:
        return VK_FORMAT_EAC_R11G11_SNORM_BLOCK;

    case GraphicsFormat::RGBA_ASTC4X4_SRGB:
        return VK_FORMAT_ASTC_4x4_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC4X4_UNorm:
        return VK_FORMAT_ASTC_4x4_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC4X4_UFloat:
        return VK_FORMAT_ASTC_4x4_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC5X4_SRGB:
        return VK_FORMAT_ASTC_5x4_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC5X4_UNorm:
        return VK_FORMAT_ASTC_5x4_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC5X4_UFloat:
        return VK_FORMAT_ASTC_5x4_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC5X5_SRGB:
        return VK_FORMAT_ASTC_5x5_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC5X5_UNorm:
        return VK_FORMAT_ASTC_5x5_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC5X5_UFloat:
        return VK_FORMAT_ASTC_5x5_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC6X5_SRGB:
        return VK_FORMAT_ASTC_6x5_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC6X5_UNorm:
        return VK_FORMAT_ASTC_6x5_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC6X5_UFloat:
        return VK_FORMAT_ASTC_6x5_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC6X6_SRGB:
        return VK_FORMAT_ASTC_6x6_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC6X6_UNorm:
        return VK_FORMAT_ASTC_6x6_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC6X6_UFloat:
        return VK_FORMAT_ASTC_6x6_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC8X5_SRGB:
        return VK_FORMAT_ASTC_8x5_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC8X5_UNorm:
        return VK_FORMAT_ASTC_8x5_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC8X5_UFloat:
        return VK_FORMAT_ASTC_8x5_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC8X6_SRGB:
        return VK_FORMAT_ASTC_8x6_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC8X6_UNorm:
        return VK_FORMAT_ASTC_8x6_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC8X6_UFloat:
        return VK_FORMAT_ASTC_8x6_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC8X8_SRGB:
        return VK_FORMAT_ASTC_8x8_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC8X8_UNorm:
        return VK_FORMAT_ASTC_8x8_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC8X8_UFloat:
        return VK_FORMAT_ASTC_8x8_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC10X5_SRGB:
        return VK_FORMAT_ASTC_10x5_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC10X5_UNorm:
        return VK_FORMAT_ASTC_10x5_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC10X5_UFloat:
        return VK_FORMAT_ASTC_10x5_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC10X6_SRGB:
        return VK_FORMAT_ASTC_10x6_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC10X6_UNorm:
        return VK_FORMAT_ASTC_10x6_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC10X6_UFloat:
        return VK_FORMAT_ASTC_10x6_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC10X8_SRGB:
        return VK_FORMAT_ASTC_10x8_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC10X8_UNorm:
        return VK_FORMAT_ASTC_10x8_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC10X8_UFloat:
        return VK_FORMAT_ASTC_10x8_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC10X10_SRGB:
        return VK_FORMAT_ASTC_10x10_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC10X10_UNorm:
        return VK_FORMAT_ASTC_10x10_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC10X10_UFloat:
        return VK_FORMAT_ASTC_10x10_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC12X10_SRGB:
        return VK_FORMAT_ASTC_12x10_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC12X10_UNorm:
        return VK_FORMAT_ASTC_12x10_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC12X10_UFloat:
        return VK_FORMAT_ASTC_12x10_SFLOAT_BLOCK_EXT;

    case GraphicsFormat::RGBA_ASTC12X12_SRGB:
        return VK_FORMAT_ASTC_12x12_SRGB_BLOCK;
    case GraphicsFormat::RGBA_ASTC12X12_UNorm:
        return VK_FORMAT_ASTC_12x12_UNORM_BLOCK;
    case GraphicsFormat::RGBA_ASTC12X12_UFloat:
        return VK_FORMAT_ASTC_12x12_SFLOAT_BLOCK_EXT;
    default:
        Assert(false);
        return VK_FORMAT_UNDEFINED;
    }
}

bool SLWrapper::Initialize_preDevice(NGIPlatform api, const bool& SLlog)
{
    if (m_sl_initialised)
    {
        LOG_INFO("SLWrapper is already initialized.");
        return true;
    }
    sl::Preferences pref{};
    pref.showConsole = true;
    pref.logMessageCallback = &logFunctionCallback;
    if (EngineGlobal::GetSettingMgr()->GetShowDLSSDebugInfo())
    {
        pref.logLevel = sl::LogLevel::eDefault;
    }
    else
    {
        pref.logLevel = sl::LogLevel::eOff;
    }
    

    std::vector<sl::Feature> featuresToLoad;

    if (EngineGlobal::GetSettingMgr()->GetEnableDLSS())
    {
        featuresToLoad.push_back(sl::kFeatureDLSS);
    }
    
    if (EngineGlobal::GetSettingMgr()->GetEnableDLSSG())
    {
        featuresToLoad.push_back(sl::kFeatureDLSS_G);
        featuresToLoad.push_back(sl::kFeatureReflex);
    }

    if (featuresToLoad.size() == 0)
    {
        LOG_ERROR("No features to load.");
        return false;
    }

    pref.featuresToLoad = featuresToLoad.data();
    pref.numFeaturesToLoad = uint32_t(featuresToLoad.size());
    sl::PreferenceFlags flags = sl::PreferenceFlags::eDisableCLStateTracking | sl::PreferenceFlags::eUseManualHooking;
    //pref.flags |= sl::PreferenceFlags::eUseManualHooking;
    pref.flags = flags;
    pref.applicationId = APP_ID; // Provided by NVDA, required if using NGX components (DLSS 2/3)
    switch (api)
    {
#if NGI_SUPPORTS_D3D12
    case NGIPlatform::D3D12:
        pslSetFeatureLoaded(sl::kFeatureDLSS_G, false);
        Assert(false);
        break;
#endif
#if NGI_SUPPORTS_VULKAN
    case NGIPlatform::Vulkan:
    {
        pref.renderAPI = sl::RenderAPI::eVulkan;
        break;
    }
#endif
#if NGI_SUPPORTS_OPENGLES30
        break;
#endif
#if NGI_SUPPORTS_METAL
        Assert(false);
        break;
#endif
#if NGI_SUPPORTS_WXGAME
        Assert(false);
        break;
#endif
    default:
        Assert(false);
        break;
    }
    m_sl_initialised = successCheck(pslInit(pref,sl::kSDKVersion), "slInit");
    if (!m_sl_initialised)
    {
        LOG_ERROR("Failed to initialize SL.");
        return false;
    }
    return true;
}

void SLWrapper::ManualHookCreateInstance()
{
    
    auto interposerDll = GetDllLocation(L"sl.interposer.dll");
    mInterposer = LoadLibraryW(interposerDll.c_str());
    if (!mInterposer)
    {
        LOG_ERROR("Unable to load Streamline Interposer");
        return;
    }
    
    {
        // Get the SL proxies for the current render mode
        pslInit = reinterpret_cast<PFun_slInit*>(GetProcAddress(mInterposer, "slInit"));
        pslShutdown = reinterpret_cast<PFun_slShutdown*>(GetProcAddress(mInterposer, "slShutdown"));
        pslIsFeatureSupported = reinterpret_cast<PFun_slIsFeatureSupported*>(GetProcAddress(mInterposer, "slIsFeatureSupported"));
        pslIsFeatureLoaded = reinterpret_cast<PFun_slIsFeatureLoaded*>(GetProcAddress(mInterposer, "slIsFeatureLoaded"));
        pslSetFeatureLoaded = reinterpret_cast<PFun_slSetFeatureLoaded*>(GetProcAddress(mInterposer, "slSetFeatureLoaded"));
        pslEvaluateFeature = reinterpret_cast<PFun_slEvaluateFeature*>(GetProcAddress(mInterposer, "slEvaluateFeature"));
        pslAllocateResources = reinterpret_cast<PFun_slAllocateResources*>(GetProcAddress(mInterposer, "slAllocateResources"));
        pslFreeResources = reinterpret_cast<PFun_slFreeResources*>(GetProcAddress(mInterposer, "slFreeResources"));
        pslSetTag = reinterpret_cast<PFun_slSetTag*>(GetProcAddress(mInterposer, "slSetTag"));
        pslGetFeatureRequirements = reinterpret_cast<PFun_slGetFeatureRequirements*>(GetProcAddress(mInterposer, "slGetFeatureRequirements"));
        pslGetFeatureVersion = reinterpret_cast<PFun_slGetFeatureVersion*>(GetProcAddress(mInterposer, "slGetFeatureVersion"));
        pslUpgradeInterface = reinterpret_cast<PFun_slUpgradeInterface*>(GetProcAddress(mInterposer, "slUpgradeInterface"));
        pslSetConstants = reinterpret_cast<PFun_slSetConstants*>(GetProcAddress(mInterposer, "slSetConstants"));
        pslGetNativeInterface = reinterpret_cast<PFun_slGetNativeInterface*>(GetProcAddress(mInterposer, "slGetNativeInterface"));
        pslGetFeatureFunction = reinterpret_cast<PFun_slGetFeatureFunction*>(GetProcAddress(mInterposer, "slGetFeatureFunction"));
        pslGetNewFrameToken = reinterpret_cast<PFun_slGetNewFrameToken*>(GetProcAddress(mInterposer, "slGetNewFrameToken"));
        Initialize_preDevice(EngineGlobal::GetSettingMgr()->GetRenderMode());
    }
    if (m_sl_initialised)
    {
        vkGetDeviceProcAddrProxy = reinterpret_cast<PFN_vkGetDeviceProcAddr>(GetProcAddress(mInterposer, "vkGetDeviceProcAddr"));
        vkGetInstanceProcAddrProxy = reinterpret_cast<PFN_vkGetInstanceProcAddr>(GetProcAddress(mInterposer, "vkGetInstanceProcAddr"));
        vkCreateInstance = reinterpret_cast<PFN_vkCreateInstance>(vkGetInstanceProcAddrProxy(NULL, "vkCreateInstance"));
    }
}

void SLWrapper::CleanUpDLSS(sl::ViewportHandle viewport, bool wfi)
{
    if (!m_sl_initialised)
    {
        return;
    }
    if (wfi)
    {
        m_device->WaitIdle();
    }
    sl::Result status = pslFreeResources(sl::kFeatureDLSS, viewport);
    assert(status == sl::Result::eOk || status == sl::Result::eErrorInvalidParameter);
}


void SLWrapper::ManualHookCreateDevice(VkInstance instance)
{
    vkEnumeratePhysicalDevices = reinterpret_cast<PFN_vkEnumeratePhysicalDevices>(vkGetInstanceProcAddrProxy(instance, "vkEnumeratePhysicalDevices"));
    vkCreateDevice = reinterpret_cast<PFN_vkCreateDevice>(vkGetInstanceProcAddrProxy(instance, "vkCreateDevice"));
    /*vkCreateWin32SurfaceKHR = reinterpret_cast<PFN_vkCreateWin32SurfaceKHR>(vkGetInstanceProcAddrProxy(instance, "vkCreateWin32SurfaceKHR"));
    vkDestroySurfaceKHR = reinterpret_cast<PFN_vkDestroySurfaceKHR>(vkGetInstanceProcAddrProxy(instance, "vkDestroySurfaceKHR"));*/
}

void SLWrapper::ManualHookAPIs(VkDevice device)
{
    if (!mInterposer)
    {
        LOG_ERROR("Unable to load Streamline Interposer");
        return;
    }
    // Get the SL proxies for the current render mode
    {
        auto renderNode = EngineGlobal::GetSettingMgr()->GetRenderMode();
        switch (renderNode)
        {
#if NGI_SUPPORTS_D3D12
        case NGIPlatform::D3D12:
            Assert(false);
            return;
#endif
#if NGI_SUPPORTS_VULKAN
        case NGIPlatform::Vulkan:
        {
            
            // Get SL proxies for ALL mandatory APIs listed in the sl_hooks.h
            //auto device = GetNGIDevicePtr();
            vkCreateSwapchainKHR = reinterpret_cast<PFN_vkCreateSwapchainKHR>(vkGetDeviceProcAddrProxy(device, "vkCreateSwapchainKHR"));
            vkDestroySwapchainKHR = reinterpret_cast<PFN_vkDestroySwapchainKHR>(vkGetDeviceProcAddrProxy(device, "vkDestroySwapchainKHR"));
            vkGetSwapchainImagesKHR = reinterpret_cast<PFN_vkGetSwapchainImagesKHR>(vkGetDeviceProcAddrProxy(device, "vkGetSwapchainImagesKHR"));

            vkAcquireNextImageKHR = reinterpret_cast<PFN_vkAcquireNextImageKHR>(vkGetDeviceProcAddrProxy(device, "vkAcquireNextImageKHR"));
            vkQueuePresentKHR = reinterpret_cast<PFN_vkQueuePresentKHR>(vkGetDeviceProcAddrProxy(device, "vkQueuePresentKHR"));
            vkDeviceWaitIdle = reinterpret_cast<PFN_vkDeviceWaitIdle>(vkGetDeviceProcAddrProxy(device, "vkDeviceWaitIdle"));
            return;
        }
#endif
#if NGI_SUPPORTS_OPENGLES30
        case NGIPlatform::OpenGLES3:
            Assert(false);
            return;
#endif
#if NGI_SUPPORTS_METAL
        case NGIPlatform::Metal:
            Assert(false);
            return;
#endif
#if NGI_SUPPORTS_WXGAME
        case NGIPlatform::WXGame:
            Assert(false);
            return;
#endif
        default:
            Assert(false);
            return;
        }

    }
    
}

bool SLWrapper::Initialize_postDevice()
{
    if (!m_sl_initialised)
    {
        LOG_ERROR("SL not initialized.");
        return false;
    }
    // We set reflex consts to a default config. This can be changed at runtime in the UI.
    auto reflexConst = sl::ReflexOptions{};
    reflexConst.mode = sl::ReflexMode::eOff;
    reflexConst.useMarkersToOptimize = true;
    reflexConst.virtualKey = VK_F13;
    reflexConst.frameLimitUs = 0;
    SetReflexConsts(reflexConst);

    return true;
}


void SLWrapper::SetReflexConsts(const sl::ReflexOptions options)
{
    if (!m_sl_initialised || !m_reflex_available)
    {
        LOG_WARN("SL not initialized or Reflex not available.");
        return;
    }

    m_reflex_consts = options;
    successCheck(pslReflexSetOptions(m_reflex_consts), "Reflex_Options");

    return;
}



void SLWrapper::SetDLSSGOptions(const sl::DLSSGOptions consts, GameContext gameContext)
{
    if (!m_sl_initialised || !m_dlssg_available)
    {
        LOG_WARN("SL not initialised or DLSSG not available.");
        return;
    }

    m_dlssg_consts = consts;

    successCheck(pslDLSSGSetOptions(m_viewports[gameContext.mRenderPipeline], m_dlssg_consts), "slDLSSGSetOptions");
}

void SLWrapper::QueryDLSSGState(GameContext gameContext, uint64_t& estimatedVRamUsage, int& fps_multiplier, sl::DLSSGStatus& status, int& minSize, int& maxFrameCount, void*& pFence, uint64_t& fenceValue)
{
    if (!m_sl_initialised || !m_dlssg_available)
    {
        LOG_WARN("SL not initialised or DLSSG not available.");
        return;
    }

    successCheck(pslDLSSGGetState(m_viewports[gameContext.mRenderPipeline], m_dlssg_settings, &m_dlssg_consts), "slDLSSGGetState");

    estimatedVRamUsage = m_dlssg_settings.estimatedVRAMUsageInBytes;
    fps_multiplier = m_dlssg_settings.numFramesActuallyPresented;
    status = m_dlssg_settings.status;
    minSize = m_dlssg_settings.minWidthOrHeight;
    /*maxFrameCount = m_dlssg_settings.numFramesToGenerateMax;
    pFence = m_dlssg_settings.inputsProcessingCompletionFence;
    fenceValue = m_dlssg_settings.lastPresentInputsProcessingCompletionFenceValue;*/
}

bool SLWrapper::Get_DLSSG_SwapChainRecreation(bool& turn_on) const
{
    turn_on = m_dlssg_shoudLoad;
    auto tmp = m_dlssg_triggerswapchainRecreation;
    return tmp;
}

void SLWrapper::CleanupDLSSG(sl::ViewportHandle viewport, bool wfi)
{
    if (!m_sl_initialised)
    {
        LOG_WARN("SL not initialised.");
        return;
    }
    if (!m_dlssg_available)
    {
        return;
    }

    if (wfi)
    {
        m_device->WaitIdle();
    }

    sl::Result status = pslFreeResources(sl::kFeatureDLSS_G, viewport);
    // if we've never ran the feature on this viewport, this call may return 'eErrorInvalidParameter'
    assert(status == sl::Result::eOk || status == sl::Result::eErrorInvalidParameter || status == sl::Result::eErrorFeatureMissing);
}

void SLWrapper::QueueGPUWaitOnSyncObjectSet(void* syncObj, uint64_t syncObjVal)
{
    if (m_device == nullptr)
    {
        LOG_FATAL("Invalid device!");
    }

    switch (m_device->GetPlatform())
    {

    case NGIPlatform::D3D12:
    {
        return;
    }
    break;
    case NGIPlatform::Vulkan:
    {
        VulkanDevice* device = dynamic_cast<VulkanDevice*>(m_device);
        //VkDevice device = reinterpret_cast<VkDevice>(m_device->GetNativeDevice());


        VkQueue graphicsQueue = VK_NULL_HANDLE;
        vkGetDeviceQueue(reinterpret_cast<VkDevice>(device->GetNativeDevice()), device->mPhysicalDeviceInfo->mGraphicsQueueInfo.familyIndex, 0, &graphicsQueue);


        VkSemaphoreWaitInfo waitInfo = {};
        waitInfo.sType = VK_STRUCTURE_TYPE_SEMAPHORE_WAIT_INFO;
        waitInfo.semaphoreCount = 1;
        waitInfo.pSemaphores = reinterpret_cast<VkSemaphore*>(&syncObj);
        waitInfo.pValues = &syncObjVal;


        VkResult result = vkWaitSemaphores(reinterpret_cast<VkDevice>(device->GetNativeDevice()), &waitInfo, UINT64_MAX);
        if (result != VK_SUCCESS)
        {
            LOG_ERROR("Failed to wait for semaphore!");
        }
    }
    break;
    default:
        break;
    }
}

void SLWrapper::ReflexCallback_Sleep(uint32_t frameID)
{
    successCheck(pslGetNewFrameToken(SLWrapper::Get().m_currentFrame, &frameID), "SL_GetFrameToken");
    if (SLWrapper::Get().GetDLSSGAvailable() && SLWrapper::Get().GetReflexAvailable())
    {
        // maybe need a pool
        // LOG_WARN("FrameIndex:{} ", frameID);
        successCheck(pslReflexSleep(*SLWrapper::Get().m_currentFrame), "Reflex_Sleep");
    }
}


//void SLWrapper::ReflexCallback_SimStart(uint32_t frameID)
//{
//    if (SLWrapper::Get().GetPCLAvailable())
//    {
//        sl::FrameToken* temp;
//        successCheck(pslGetNewFrameToken(temp, &frameID), "SL_GetFrameToken");
//        successCheck(pslPCLSetMarker(sl::PCLMarker::eSimulationStart, *temp), "PCL_SimStart");
//    }
//}
//
//void SLWrapper::ReflexCallback_SimEnd(uint32_t frameID)
//{
//    if (SLWrapper::Get().GetPCLAvailable())
//    {
//        sl::FrameToken* temp;
//        successCheck(pslGetNewFrameToken(temp, &frameID), "SL_GetFrameToken");
//        successCheck(pslPCLSetMarker(sl::PCLMarker::eSimulationEnd, *temp), "PCL_SimEnd");
//    }
//}
//
//void SLWrapper::ReflexCallback_RenderStart(uint32_t frameID)
//{
//    if (SLWrapper::Get().GetPCLAvailable())
//    {
//        sl::FrameToken* temp;
//        successCheck(pslGetNewFrameToken(temp, &frameID), "SL_GetFrameToken");
//        successCheck(pslPCLSetMarker(sl::PCLMarker::eRenderSubmitStart, *temp), "PCL_SubmitStart");
//    }
//}
//
//void SLWrapper::ReflexCallback_RenderEnd(uint32_t frameID)
//{
//    if (SLWrapper::Get().GetPCLAvailable())
//    {
//        sl::FrameToken* temp;
//        successCheck(pslGetNewFrameToken(temp, &frameID), "SL_GetFrameToken");
//        successCheck(pslPCLSetMarker(sl::PCLMarker::eRenderSubmitEnd, *temp), "PCL_SubmitEnd");
//    }
//}
//
// void SLWrapper::ReflexTriggerFlash()
//{
//     successCheck(pslPCLSetMarker(sl::PCLMarker::eTriggerFlash, *SLWrapper::Get().m_currentFrame), "Reflex_Flash");
// }
//
// void SLWrapper::ReflexTriggerPcPing()
//{
//     if (SLWrapper::Get().GetPCLAvailable())
//     {
//         successCheck(pslPCLSetMarker(sl::PCLMarker::ePCLatencyPing, *SLWrapper::Get().m_currentFrame), "PCL_PCPing");
//     }
// }


void SLWrapper::ReflexCallback_PresentStart(uint32_t frameID)
{
    if (SLWrapper::Get().GetDLSSGAvailable() && SLWrapper::Get().GetPCLAvailable())
    {
        sl::FrameToken* temp;
        successCheck(pslGetNewFrameToken(temp, &frameID), "SL_GetFrameToken");
        successCheck(pslPCLSetMarker(sl::PCLMarker::ePresentStart, *temp), "PCL_PresentStart");
    }
}

void SLWrapper::ReflexCallback_PresentEnd(uint32_t frameID)
{
    if (SLWrapper::Get().GetDLSSGAvailable() && SLWrapper::Get().GetPCLAvailable())
    {
        sl::FrameToken* temp;
        successCheck(pslGetNewFrameToken(temp, &frameID), "SL_GetFrameToken");
        successCheck(pslPCLSetMarker(sl::PCLMarker::ePresentEnd, *temp), "PCL_PresentEnd");
    }
}


void SLWrapper::QueryReflexStats(bool& reflex_lowLatencyAvailable, bool& reflex_flashAvailable, std::string& stats)
{
    if (GetReflexAvailable())
    {
        sl::ReflexState state;
        successCheck(pslReflexGetState(state), "Reflex_State");

        reflex_lowLatencyAvailable = state.lowLatencyAvailable;
        reflex_flashAvailable = state.flashIndicatorDriverControlled;

        //auto rep = state.frameReport[63];
        //if (state.latencyReportAvailable && rep.gpuRenderEndTime != 0)
        //{
        //    auto frameID = rep.frameID;
        //    auto totalGameToRenderLatencyUs = rep.gpuRenderEndTime - rep.inputSampleTime;
        //    auto simDeltaUs = rep.simEndTime - rep.simStartTime;
        //    auto renderDeltaUs = rep.renderSubmitEndTime - rep.renderSubmitStartTime;
        //    auto presentDeltaUs = rep.presentEndTime - rep.presentStartTime;
        //    /*if (presentDeltaUs != 0)
        //    {
        //        getchar();
        //    }*/
        //    auto driverDeltaUs = rep.driverEndTime - rep.driverStartTime;
        //    auto osRenderQueueDeltaUs = rep.osRenderQueueEndTime - rep.osRenderQueueStartTime;
        //    auto gpuRenderDeltaUs = rep.gpuRenderEndTime - rep.gpuRenderStartTime;

        //    stats = "frameID: " + std::to_string(frameID);
        //    stats += "\ntotalGameToRenderLatencyUs: " + std::to_string(totalGameToRenderLatencyUs);
        //    stats += "\nsimDeltaUs: " + std::to_string(simDeltaUs);
        //    stats += "\nrenderDeltaUs: " + std::to_string(renderDeltaUs);
        //    stats += "\npresentDeltaUs: " + std::to_string(presentDeltaUs);
        //    stats += "\ndriverDeltaUs: " + std::to_string(driverDeltaUs);
        //    stats += "\nosRenderQueueDeltaUs: " + std::to_string(osRenderQueueDeltaUs);
        //    stats += "\ngpuRenderDeltaUs: " + std::to_string(gpuRenderDeltaUs);
        //}
        //else
        //{
        //    stats = "Latency Report Unavailable";
        //}
    }
}

void SLWrapper::UpdateFeatureAvailable()
{

    sl::AdapterInfo adapterInfo;
    auto renderNode = EngineGlobal::GetSettingMgr()->GetRenderMode();
    switch (renderNode)
    {
#if NGI_SUPPORTS_D3D12
    case NGIPlatform::D3D12:
        Assert(false);
        break;
#endif
#if NGI_SUPPORTS_VULKAN
    case NGIPlatform::Vulkan:
    {
        adapterInfo.vkPhysicalDevice = m_device->GetNativeDevice();
        break;
    }
#endif
#if NGI_SUPPORTS_OPENGLES30
    case NGIPlatform::OpenGLES3:
        Assert(false);
        break;
#endif
#if NGI_SUPPORTS_METAL
    case NGIPlatform::Metal:
        Assert(false);
        break;
#endif
#if NGI_SUPPORTS_WXGAME
    case NGIPlatform::WXGame:
        Assert(false);
        break;
#endif
    default:
        Assert(false);
        break;
    }


    // Check if features are fully functional (2nd call of slIsFeatureSupported onwards)

    m_dlss_available = successCheck(pslIsFeatureSupported(sl::kFeatureDLSS, adapterInfo), "slIsFeatureSupported_DLSS");
    if (m_dlss_available)
    {
        LOG_INFO("DLSS is supported on this system.");
    }
    else
        LOG_WARN("DLSS is not fully functional on this system.");

    m_dlssg_available = successCheck(pslIsFeatureSupported(sl::kFeatureDLSS_G, adapterInfo), "slIsFeatureSupported_DLSSG");
    if (m_dlssg_available)
        LOG_INFO("DLSS-G is supported on this system.");
    else
        LOG_WARN("DLSS-G is not fully functional on this system.");



    m_reflex_available = successCheck(pslIsFeatureSupported(sl::kFeatureReflex, adapterInfo), "slIsFeatureSupported_REFLEX");
    if (m_reflex_available)
        LOG_INFO("Reflex is supported on this system.");
    else
        LOG_WARN("Reflex is not fully functional on this system.");

    m_pcl_available = successCheck(pslIsFeatureSupported(sl::kFeaturePCL, adapterInfo), "slIsFeatureSupported_PCL");
    if (m_pcl_available)
        LOG_INFO("PCL is supported on this system.");
    else
        LOG_WARN("PCL is not fully functional on this system.");
}

void SLWrapper::QueryDLSSOptimalSettings(DLSSSettings& settings)
{
    if (!m_sl_initialised || !m_dlss_available)
    {
        LOG_WARN("SL not initialised or DLSS not available.");
        settings = DLSSSettings{};
        return;
    }

    sl::DLSSOptimalSettings dlssOptimal = {};
    successCheck(pslDLSSGetOptimalSettings(m_dlss_consts, dlssOptimal), "slDLSSGetOptimalSettings");

    settings.optimalRenderSize.x = static_cast<int>(dlssOptimal.optimalRenderWidth);
    settings.optimalRenderSize.y = static_cast<int>(dlssOptimal.optimalRenderHeight);
    settings.sharpness = dlssOptimal.optimalSharpness;

    settings.minRenderSize.x = dlssOptimal.renderWidthMin;
    settings.minRenderSize.y = dlssOptimal.renderHeightMin;
    settings.maxRenderSize.x = dlssOptimal.renderWidthMax;
    settings.maxRenderSize.y = dlssOptimal.renderHeightMax;
}

void SLWrapper::SetSLConsts(const sl::Constants& consts, GameContext gameContext)
{
    if (!m_sl_initialised)
    {
        LOG_WARN("SL not initialised.");
        return;
    }
    //LOG_WARN("FrameIndex:{} viewport:{}", uint32_t(*m_currentFrame), uint32_t(m_viewports[gameContext.mRenderPipeline]));
    successCheck(pslSetConstants(consts, *m_currentFrame, m_viewports[gameContext.mRenderPipeline]), "slSetConstants");

}

void SLWrapper::ShutDown()
{
    // Shutdown Streamline
    if (m_sl_initialised)
    {
        // Un-set all tags
        for (auto it = m_viewports.begin(); it != m_viewports.end(); it++)
        {
            sl::ResourceTag inputs[] = {sl::ResourceTag{nullptr, sl::kBufferTypeDepth, sl::ResourceLifecycle::eValidUntilPresent},
                                        sl::ResourceTag{nullptr, sl::kBufferTypeMotionVectors, sl::ResourceLifecycle::eValidUntilPresent},
                                        sl::ResourceTag{nullptr, sl::kBufferTypeScalingInputColor, sl::ResourceLifecycle::eValidUntilPresent},
                                        sl::ResourceTag{nullptr, sl::kBufferTypeScalingOutputColor, sl::ResourceLifecycle::eValidUntilPresent},
                                        sl::ResourceTag{nullptr, sl::kBufferTypeHUDLessColor, sl::ResourceLifecycle::eValidUntilPresent}};

            successCheck(pslSetTag(it->second, inputs, _countof(inputs), nullptr), "slSetTag_clear");
            CleanUpDLSS(it->second, true);
        }

        successCheck(pslShutdown(), "slShutdown");
        m_sl_initialised = false;
    }
}

void SLWrapper::SetDevice(NGIDevice* device)
{
    m_device = device;
}

} // namespace cross
#endif
#pragma warning(pop)