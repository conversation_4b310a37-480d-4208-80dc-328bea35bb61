#include "EnginePrefix.h"
#include "NGIUtils.h"
#include "CrossBase/Log.h"

cross::TexelBlockProperty cross::GetFormatTexelBlockProperty(GraphicsFormat format)
{
    if (EnumHasAnyFlags(format, GraphicsFormat::CombinationBit))
    {
        UInt32 compCount = 0;
        switch (GetFormatCompLayout(format))
        {
        case GraphicsFormat::CompLayoutR:
            compCount = 1;
            break;
        case GraphicsFormat::CompLayoutRG:
            compCount = 2;
            break;
        case GraphicsFormat::CompLayoutRGB:
        case GraphicsFormat::CompLayoutBGR:
            compCount = 3;
            break;
        case GraphicsFormat::CompLayoutRGBA:
        case GraphicsFormat::CompLayoutBGRA:
            compCount = 4;
            break;
        default:
            Assert(false);
            break;
        }

        UInt32 compSize = 0;
        switch (GetFormatCompSize(format))
        {
        case GraphicsFormat::CompSize8:
            compSize = 1;
            break;
        case GraphicsFormat::CompSize16:
            compSize = 2;
            break;
        case GraphicsFormat::CompSize32:
            compSize = 4;
            break;
        default:
            Assert(false);
            break;
        }

        return {1, 1, compCount * compSize};
    }
    else if (GraphicsFormat::Pack16First <= format && format <= GraphicsFormat::Pack16Last)
    {
        return {
            1,
            1,
            2,
        };
    }
    else if (GraphicsFormat::Pack32First <= format && format <= GraphicsFormat::Pack32Last)
    {
        return {
            1,
            1,
            4,
        };
    }
    else if (GraphicsFormat::BCFirst <= format && format <= GraphicsFormat::BCLast)
    {
        UInt32 blockSize = 0;
        switch (format)
        {
        case GraphicsFormat::RGB_BC1_SRGB:
        case GraphicsFormat::RGB_BC1_UNorm:
        case GraphicsFormat::RGBA_BC1_SRGB:
        case GraphicsFormat::RGBA_BC1_UNorm:
        case GraphicsFormat::R_BC4_UNorm:
        case GraphicsFormat::R_BC4_SNorm:
            blockSize = 8;
            break;
        case GraphicsFormat::RGBA_BC2_SRGB:
        case GraphicsFormat::RGBA_BC2_UNorm:
        case GraphicsFormat::RGBA_BC3_SRGB:
        case GraphicsFormat::RGBA_BC3_UNorm:
        case GraphicsFormat::RG_BC5_UNorm:
        case GraphicsFormat::RG_BC5_SNorm:
        case GraphicsFormat::RGB_BC6H_UFloat:
        case GraphicsFormat::RGB_BC6H_SFloat:
        case GraphicsFormat::RGBA_BC7_SRGB:
        case GraphicsFormat::RGBA_BC7_UNorm:
            blockSize = 16;
            break;
        default:
            Assert(false);
            break;
        }
        return {
            4,
            4,
            blockSize,
        };
    }
    else if ((GraphicsFormat::ETCFirst <= format && format <= GraphicsFormat::ETCLast) || (GraphicsFormat::EACFirst <= format && format <= GraphicsFormat::EACLast))
    {
        UInt32 blockSize = 0;
        switch (format)
        {
        case GraphicsFormat::RGB_ETC2_UNorm:
        case GraphicsFormat::RGB_ETC2_SRGB:
        case GraphicsFormat::RGB_A1_ETC2_UNorm:
        case GraphicsFormat::RGB_A1_ETC2_SRGB:
            blockSize = 8;
            break;
        case GraphicsFormat::RGBA_ETC2_UNorm:
        case GraphicsFormat::RGBA_ETC2_SRGB:
        case GraphicsFormat::RG_EAC_SNorm:
        case GraphicsFormat::RG_EAC_UNorm:
            blockSize = 16;
            break;
        default:
            Assert(false);
            break;
        }
        return {4, 4, blockSize};
    }
    else if ((GraphicsFormat::ASTCFirst <= format && format <= GraphicsFormat::ASTCLast) || (GraphicsFormat::ASTCHDRFirst <= format && format <= GraphicsFormat::ASTCHDRLast))
    {
        UInt32 width = 0;
        UInt32 height = 0;
        switch (format)
        {
        case GraphicsFormat::RGBA_ASTC4X4_SRGB:
        case GraphicsFormat::RGBA_ASTC4X4_UNorm:
        case GraphicsFormat::RGBA_ASTC4X4_UFloat:
            width = height = 4;
            break;
        case GraphicsFormat::RGBA_ASTC5X4_SRGB:
        case GraphicsFormat::RGBA_ASTC5X4_UNorm:
        case GraphicsFormat::RGBA_ASTC5X4_UFloat:
            width = 5;
            height = 4;
            break;
        case GraphicsFormat::RGBA_ASTC5X5_SRGB:
        case GraphicsFormat::RGBA_ASTC5X5_UNorm:
        case GraphicsFormat::RGBA_ASTC5X5_UFloat:
            width = height = 5;
            break;
        case GraphicsFormat::RGBA_ASTC6X5_SRGB:
        case GraphicsFormat::RGBA_ASTC6X5_UNorm:
        case GraphicsFormat::RGBA_ASTC6X5_UFloat:
            width = 6;
            height = 5;
            break;
        case GraphicsFormat::RGBA_ASTC6X6_SRGB:
        case GraphicsFormat::RGBA_ASTC6X6_UNorm:
        case GraphicsFormat::RGBA_ASTC6X6_UFloat:
            width = height = 6;
            break;
        case GraphicsFormat::RGBA_ASTC8X5_SRGB:
        case GraphicsFormat::RGBA_ASTC8X5_UNorm:
        case GraphicsFormat::RGBA_ASTC8X5_UFloat:
            width = 8;
            height = 5;
            break;
        case GraphicsFormat::RGBA_ASTC8X6_SRGB:
        case GraphicsFormat::RGBA_ASTC8X6_UNorm:
        case GraphicsFormat::RGBA_ASTC8X6_UFloat:
            width = 8;
            height = 6;
            break;
        case GraphicsFormat::RGBA_ASTC8X8_SRGB:
        case GraphicsFormat::RGBA_ASTC8X8_UNorm:
        case GraphicsFormat::RGBA_ASTC8X8_UFloat:
            width = height = 8;
            break;
        case GraphicsFormat::RGBA_ASTC10X5_SRGB:
        case GraphicsFormat::RGBA_ASTC10X5_UNorm:
        case GraphicsFormat::RGBA_ASTC10X5_UFloat:
            width = 10;
            height = 5;
            break;
        case GraphicsFormat::RGBA_ASTC10X6_SRGB:
        case GraphicsFormat::RGBA_ASTC10X6_UNorm:
        case GraphicsFormat::RGBA_ASTC10X6_UFloat:
            width = 10;
            height = 6;
            break;
        case GraphicsFormat::RGBA_ASTC10X8_SRGB:
        case GraphicsFormat::RGBA_ASTC10X8_UNorm:
        case GraphicsFormat::RGBA_ASTC10X8_UFloat:
            width = 10;
            height = 8;
            break;
        case GraphicsFormat::RGBA_ASTC10X10_SRGB:
        case GraphicsFormat::RGBA_ASTC10X10_UNorm:
        case GraphicsFormat::RGBA_ASTC10X10_UFloat:
            width = height = 10;
            break;
        case GraphicsFormat::RGBA_ASTC12X10_SRGB:
        case GraphicsFormat::RGBA_ASTC12X10_UNorm:
        case GraphicsFormat::RGBA_ASTC12X10_UFloat:
            width = 12;
            height = 10;
            break;
        case GraphicsFormat::RGBA_ASTC12X12_SRGB:
        case GraphicsFormat::RGBA_ASTC12X12_UNorm:
        case GraphicsFormat::RGBA_ASTC12X12_UFloat:
            width = height = 12;
            break;
        default:
            Assert(false);
            break;
        }
        return {width, height, 16};
    }
    else
    {
        AssertMsg(false, "Unimplented");
        return {
            0,
            0,
            0,
        };
    }
}

cross::NGIResourceState cross::MapStateStageBit(UInt32 stageMask)
{
    using namespace CrossSchema;
    NGIResourceState ret{0};
    static const std::tuple<ShaderStageBit, NGIResourceState> gStageMaskMappings[]{
        {
            ShaderStageBit::Vertex,
            NGIResourceState::VertexShaderBit,
        },
        {
            ShaderStageBit::Hull,
            NGIResourceState::HullShaderBit,
        },
        {
            ShaderStageBit::Domain,
            NGIResourceState::DomainShaderBit,
        },
        {
            ShaderStageBit::Geometry,
            NGIResourceState::GeometryShaderBit,
        },
        {
            ShaderStageBit::Pixel,
            NGIResourceState::PixelShaderBit,
        },
        {
            ShaderStageBit::Compute,
            NGIResourceState::ComputeShaderBit,
        },
        {
            ShaderStageBit::RayGen,
            NGIResourceState::RayTracingShaderBit
        },
        {
            ShaderStageBit::ClosestHit,
            NGIResourceState::RayTracingShaderBit
        },
        {
            ShaderStageBit::AnyHit,
            NGIResourceState::RayTracingShaderBit
        },
        {
            ShaderStageBit::Miss,
            NGIResourceState::RayTracingShaderBit
        },
        {
            ShaderStageBit::Callable,
            NGIResourceState::RayTracingShaderBit
        },
        {
            ShaderStageBit::InterSection,
            NGIResourceState::RayTracingShaderBit
        },
    };
    for (auto [stageBit, stateBit] : gStageMaskMappings)
    {
        if (stageMask & ToUnderlying(stageBit))
        {
            ret |= stateBit;
        }
    }
    return ret;
}

cross::NGIResourceState cross::MapStateBit(CrossSchema::ShaderResourceType type)
{
    using namespace CrossSchema;
    switch (type)
    {
    case ShaderResourceType::ConstantBuffer:
    case ShaderResourceType::TextureBuffer:
        return NGIResourceState::ConstantBufferBit;
    case ShaderResourceType::TexelBuffer:
    case ShaderResourceType::StructuredBuffer:
    case ShaderResourceType::ByteAddressBuffer:
    case ShaderResourceType::Texture1D:
    case ShaderResourceType::Texture1DArray:
    case ShaderResourceType::Texture2D:
    case ShaderResourceType::Texture2DArray:
    case ShaderResourceType::Texture2DMS:
    case ShaderResourceType::Texture2DMSArray:
    case ShaderResourceType::Texture3D:
    case ShaderResourceType::TextureCube:
    case ShaderResourceType::TextureCubeArray:
        return NGIResourceState::ShaderResourceBit;
    case ShaderResourceType::RWTexelBuffer:
    case ShaderResourceType::RWStructuredBuffer:
    case ShaderResourceType::RWByteAddressBuffer:
    case ShaderResourceType::RWTexture1D:
    case ShaderResourceType::RWTexture1DArray:
    case ShaderResourceType::RWTexture2D:
    case ShaderResourceType::RWTexture2DArray:
    case ShaderResourceType::RWTexture3D:
        return NGIResourceState::UnorderedAccessBit;
    case ShaderResourceType::SubpassInput:
    case ShaderResourceType::SubpassInputMS:
        return NGIResourceState::SubpassRead;
    case ShaderResourceType::Sampler:
        return NGIResourceState::Undefined;
    default:
        Assert(false);
        return NGIResourceState::Undefined;
    }
}

cross::NGIResourceBindingType cross::GetResourceBindingType(CrossSchema::ShaderResourceType type)
{
    using namespace CrossSchema;
    switch (type)
    {
    case ShaderResourceType::ConstantBuffer:
    case ShaderResourceType::TextureBuffer:
        return NGIResourceBindingType::ConstBuffer;
    case ShaderResourceType::TexelBuffer:
    case ShaderResourceType::StructuredBuffer:
    case ShaderResourceType::ByteAddressBuffer:
    case ShaderResourceType::RWTexelBuffer:
    case ShaderResourceType::RWStructuredBuffer:
    case ShaderResourceType::RWByteAddressBuffer:
        return NGIResourceBindingType::Buffer;
    case ShaderResourceType::Texture1D:
    case ShaderResourceType::Texture1DArray:
    case ShaderResourceType::Texture2D:
    case ShaderResourceType::Texture2DArray:
    case ShaderResourceType::Texture2DMS:
    case ShaderResourceType::Texture2DMSArray:
    case ShaderResourceType::Texture3D:
    case ShaderResourceType::TextureCube:
    case ShaderResourceType::TextureCubeArray:
    case ShaderResourceType::RWTexture1D:
    case ShaderResourceType::RWTexture1DArray:
    case ShaderResourceType::RWTexture2D:
    case ShaderResourceType::RWTexture2DArray:
    case ShaderResourceType::RWTexture3D:
    case ShaderResourceType::SubpassInput:
    case ShaderResourceType::SubpassInputMS:
        return NGIResourceBindingType::Texture;
    case ShaderResourceType::Sampler:
        return NGIResourceBindingType::Sampler;
    case ShaderResourceType::AccelStruct:
        return NGIResourceBindingType::AccelStruct;
    default:
        Assert(false);
        return NGIResourceBindingType::Unknown;
    }
}