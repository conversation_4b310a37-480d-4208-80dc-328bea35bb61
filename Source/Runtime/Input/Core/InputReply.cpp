#include "EnginePrefix.h"
#include "Runtime/Input/Core/InputReply.h"

namespace cross::input 
{
    /* An event should return a FReply::Handled().CaptureMouse( SomeWidget ) as a means of asking the system to forward all mouse events to SomeWidget */
    CEReply& CEReply::CaptureMouse(std::shared_ptr<SequenceableWidget> inMouseCaptor)
    {
        this->mPointerCaptor = inMouseCaptor;
        return Me();
    }

    CEReply& CEReply::CaptureTouch(std::shared_ptr<SequenceableWidget> inTouchCaptor) 
    {
        this->mPointerCaptor = inTouchCaptor;
        return Me();
    }

    CEReply& CEReply::ReleasePointCapture()
    {
        this->mPointerCaptor.reset();
        this->mReleasePointerCapture = true;
        return Me();
    }

    CEReply& CEReply::ReleaseMouseLock()
    {
        this->mReleaseMouseLock = true;
        this->mReceiveMouseLock = false;
        return Me();
    }

    CEReply& CEReply::SetHandler(std::shared_ptr<SequenceableWidget> inHandler)
    {
        this->mHandler = inHandler;
        return Me();
    }
  
    CEReply& CEReply::SetMousePos(const Float2& inNewMousePos) 
    {
        this->mRequestedMousePos = inNewMousePos;
        return Me();
    }

    CEReply& CEReply::SetUserFocus(std::shared_ptr<SequenceableWidget> giveMeFocus, FocusCause::Type reasonFocusIsChanging /*= FocusCause::Type::SetDirectly*/) 
    {
        this->mKeyboardCaptor = giveMeFocus;
        this->mFocusChangeReason = reasonFocusIsChanging;
        this->mReceiveUserFocus = true;
        this->mReleaseUserFocus = false;
        return Me();
    }

    CEReply& CEReply::ClearUserFocus(FocusCause::Type reasonFocusIsChanging /*= FocusCause::Type::SetDirectly*/)
    {
        this->mKeyboardCaptor = nullptr;
        this->mFocusChangeReason = reasonFocusIsChanging;
        this->mReceiveUserFocus = false;
        this->mReleaseUserFocus = true;
        return Me();
    }

    CEReply& CEReply::LockMouseToWidget(std::shared_ptr<SequenceableWidget> giveMeLock)
    {
        this->mMouseLockWidget = giveMeLock;
        this->mReceiveMouseLock = true;
        this->mReleaseMouseLock = false;
        return Me();
    }
}