#pragma once
#include "CrossBase/Math/CrossMath.h"
#include "Runtime/Input/Core/InputReply.h"
#include "Runtime/Input/Core/InputKeyState.h"

namespace cross
{
class SequenceableWidgetPath;

enum class CEWindowActivation
{
    Activate = 0,
    ActivateByMouse,
    Deactivate
};
}

namespace cross::input 
{
    struct ENGINE_API Event
    {
        Event() = default;

        Event(const UserHandle inUser, const bool inIsRepeat) 
            : User(inUser)
            , bIsRepeat(inIsRepeat)
        {}

        // True if this key was auto-repeated.
        bool bIsRepeat;

        // The index of the user that caused the event.
        UserHandle User = UserHandle::InvalidHandle();
    };

    /*
     * WindowActivateEvent describes a window being activated or deactivated.
     * (i.e. brought to the foreground or moved to the background)
     * This event is only passed to top level windows; most widgets are incapable
     * of receiving this event.
     */
    struct ENGINE_API WindowActivateEvent : public Event
    {
        WindowActivateEvent(const UserHandle inUser, CEWindowActivation inActivation) 
            : Event(inUser, false)
            , Activation(inActivation)
        {}

        CEWindowActivation const Activation;
    };

    /*
     * FocusEvent is used when notifying widgets about keyboard focus changes
     * It is passed to event handlers dealing with keyboard focus
     */
    struct ENGINE_API FocusEvent : public Event
    {
        /*
         * Not meant for normal usage.
         */
        FocusEvent()
            : Event()
            , Cause(FocusCause::Type::SetDirectly)
        {}

        /*
         * Constructor.  Events are immutable once constructed.
         *
         * @param  InCause  The cause of the focus event
         */
        FocusEvent(const FocusCause::Type InCause, const UserHandle inUser)
            : Event(inUser, false)
            , Cause(InCause)
        {}

        /* The cause of the focus change */
        const FocusCause::Type Cause;
    };

    struct ENGINE_API PointerLostEvent : public Event
    {
        PointerLostEvent(const UserHandle inUser, PointerHandle inPointer)
            : Event(inUser, false)
            , Pointer(inPointer)
        {}

        /* The handle of the pointer lost ctrl */
        const PointerHandle Pointer;
    };

    struct ENGINE_API InputEvent : public Event
    {
        InputEvent(const UserHandle inUser, const bool inIsRepeat)
            : Event(inUser, inIsRepeat)
        {}

        InputEvent(const UserHandle inUser, const bool inIsRepeat, const CEModifierKeyStates& inModifierKeys)
            : Event(inUser, inIsRepeat)
            , mModifierKeys(inModifierKeys)
        {}

        /*
         * Returns whether or not this character is an auto-repeated keystroke
         *
         * @return  True if this character is a repeat
         */
        inline bool IsRepeat() const
        {
            return bIsRepeat;
        }

        /*
         * Returns true if either shift key was down when this event occurred
         *
         * @return  True if shift is pressed
         */
        inline bool IsShiftDown() const
        {
            return mModifierKeys.IsShiftDown();
        }

        /*
         * Returns true if left shift key was down when this event occurred
         */
        inline bool IsLeftShiftDown() const
        {
            return mModifierKeys.IsLeftShiftDown();
        }

        /*
         * Returns true if right shift key was down when this event occurred
         */
        inline bool IsRightShiftDown() const
        {
            return mModifierKeys.IsRightShiftDown();
        }

        /*
         * Returns true if either control key was down when this event occurred
         */
        inline bool IsControlDown() const
        {
            return mModifierKeys.IsControlDown();
        }

        /*
         * Returns true if left control key was down when this event occurred
         */
        inline bool IsLeftControlDown() const
        {
            return mModifierKeys.IsLeftControlDown();
        }

        /*
         * Returns true if right control key was down when this event occurred
         */
        inline bool IsRightControlDown() const
        {
            return mModifierKeys.IsRightControlDown();
        }

        /*
         * Returns true if either alt key was down when this event occurred
         */
        inline bool IsAltDown() const
        {
            return mModifierKeys.IsAltDown();
        }

        /*
         * Returns true if left alt key was down when this event occurred
         */
        inline bool IsLeftAltDown() const
        {
            return mModifierKeys.IsLeftAltDown();
        }

        /*
         * Returns true if right alt key was down when this event occurred
         */
        inline bool IsRightAltDown() const
        {
            return mModifierKeys.IsRightAltDown();
        }

        /*
         * Returns true if either command key was down when this event occurred
         */
        inline bool IsCommandDown() const
        {
            return mModifierKeys.IsCommandDown();
        }

        /*
         * Returns true if left command key was down when this event occurred
         */
        inline bool IsLeftCommandDown() const
        {
            return mModifierKeys.IsLeftCommandDown();
        }

        /*
         * Returns true if right command key was down when this event occurred
         */
        inline bool IsRightCommandDown() const
        {
            return mModifierKeys.IsRightCommandDown();
        }

        /*
         * Returns true if caps lock was on when this event occurred
         */
        inline bool AreCapsLocked() const
        {
            return mModifierKeys.AreCapsLocked();
        }

    protected:
        // State of modifier keys when this event happened.
        CEModifierKeyStates const mModifierKeys;

        // Events are sent along paths. See (GetEventPath).
        const SequenceableWidgetPath* mEventPath = nullptr;
    };

    /*
     * CharEvent describes a keyboard action where the utf-8 code is given. Used for OnKeyChar messages
     */
    struct CharEvent : public InputEvent
    {
    public:
        CharEvent(const UserHandle inUser, const char inChar, const CEModifierKeyStates& inModifierStates, UInt32 Modifier,
            const bool inIsRepeat)
            : InputEvent(inUser, inIsRepeat, inModifierStates)
            , Character(inChar)
            , Modifier(Modifier)
        {}
        
        // The character that was pressed.
        char Character;
        UInt32 Modifier;
    };

    /*
     * KeyEvent describes a key action (keyboard/controller key/button pressed or released.)
     * It is passed to event handlers dealing with key input.
     */
    struct ENGINE_API KeyEvent : public InputEvent
    {
        KeyEvent(
            const UserHandle inUser, 
            const CEKey& inKey, 
            const CEModifierKeyStates& inModifierStates,
            const bool inIsRepeat) 
            : InputEvent(inUser, inIsRepeat, inModifierStates)
            , Key(inKey)
            , CharCode(CEInputKeyManager::Get().GetCharCodeFromKey(Key))
            , PlatformCode(CEInputKeyManager::Get().GetPlatformCodeFromKey(Key))
        {}

        // Name of the key that was possessing.
        CEKey const& Key;

        // The character code of the key that was pressed.  Only applicable to typed character keys, 0 otherwise.
        UInt32 const CharCode;

        // Original key code received from hardware before any conversion/mapping
        UInt32 const PlatformCode;
    };

    struct ENGINE_API AnalogInputEvent : public KeyEvent
    {
    public:
        AnalogInputEvent()
            : KeyEvent({0}, CEKey(), CEModifierKeyStates(), false)
            , AxisValue(0.0f)
        {}

        AnalogInputEvent(
            const UserHandle inUser, 
            const CEKey& inKey, 
            const CEModifierKeyStates& inModifierStates, 
            const bool inIsRepeat, 
            const float InAxisValue)
            : KeyEvent(inUser, inKey, inModifierStates, inIsRepeat)
            , AxisValue(InAxisValue)
        {}

        float GetAnalogValue() const { return AxisValue; }
    private:
        //  Analog value between 0 and 1 (0 being not pressed at all, 1 being fully pressed).
        float AxisValue;
    };

    /*
     * PointerEvent describes a mouse or touch action (e.g. Press, Release, Move, Wheel, etc).
     * It is passed to event handlers dealing with pointer-based input.
     */
    struct ENGINE_API PointerEvent : public InputEvent
    {
        /* Not meant for normal usage. */
        PointerEvent();

        /* A constructor for raw mouse events */
        PointerEvent(const UserHandle inUser, const CEKey& inKey, const std::set<CEKey>& inPressedButtons, const CEModifierKeyStates& inModifierStates, const Float2& inScreenSpacePosition, const Float2& inLastScreenSpacePosition); 

        /* A constructor for touch events */
        PointerEvent(const UserHandle inUser, const CEKey& inKey, const std::set<CEKey>& inPressedButtons, const Float2& inScreenSpacePosition, const Float2& inLastScreenSpacePosition, float inForce, bool inIsForceChanged = false,
                     bool inIsFirstMove = false);

        /* A constructor to alter cursor positions */
        PointerEvent(const PointerEvent& inOther, const Float2& inScreenSpacePosition, const Float2& inLastScreenSpacePosition);

        // Name of the abstract key that was possessing.
        CEKey const& Key;
        // 
        const std::set<CEKey>* PressedButtons;

        Float2 const ScreenSpacePosition;

        Float2 const LastScreenSpacePosition;

        /* Unique identifier of the pointer (e.g., finger index) */
        PointerHandle const PointerIndex;

        float Force{1.0f};

        // Float2 WheelOrGestureDelta{Float2::Zero()};
        float WheelDelta{0.f};

        input::CEGestureEvent::Type GestureType;

        bool const bIsTouchForceChanged;
        
        bool const bIsTouchFirstMove;

        /* Is this event a result from a touch (as opposed to a mouse) */
        bool IsTouchEvent() const;

        /* Mouse buttons that are currently pressed */
        bool IsMouseEvent() const;
    };

    /*
     * PointerEvent describes a mouse or touch move action 
     */
    class ENGINE_API PointerMoveEvent : public PointerEvent
    {
    public:
        /* A constructor for raw mouse events */
        PointerMoveEvent(const UserHandle inUser, const CEModifierKeyStates& inModifierStates, const std::set<CEKey>& inPressedButtons, const Float2& inScreenSpacePosition, const Float2& inLastScreenSpacePosition,
                         const Float2& inScreenDelta); 
        
        /* A constructor for touch events */
        PointerMoveEvent(const UserHandle inUser, const PointerHandle inHandle, const std::set<CEKey>& inPressedButtons, const Float2& inScreenSpacePosition, const Float2& inLastScreenSpacePosition, float inForce,
                         bool inIsForceChanged = false, bool inIsFirstMove = false);

        /* A constructor to alter cursor positions */
        PointerMoveEvent(const PointerMoveEvent& inOther, const Float2& inScreenSpacePosition, const Float2& inLastScreenSpacePosition);

        // Reltv delta for move in screen space
        Float2 const CursorDelta;

        /* A constructor for gesture events TODO */
    };

    // Helper class to auto-populate a set with the set of "keys" a touch represents
    class CETouchKeySet : public std::set<CEKey>
    {
    public:
        // Creates and initializes a new instance with the specified key.
        CETouchKeySet(const CEKey& key)
        {
            this->insert(key);
        }

    public:
        // The standard set consists of just the left mouse button key.
        ENGINE_API static const CETouchKeySet StandardSet;

        // The empty set contains no valid keys.
        ENGINE_API static const CETouchKeySet EmptySet;
    };
}
