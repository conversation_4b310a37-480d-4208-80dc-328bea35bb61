#include "EnginePrefix.h"
#include "Runtime/Input/Core/PlatformUser.h"
#include "Runtime/Input/Core/GameViewPortWidget.h"
#include "Runtime/Input/Core/WidgetPathHelper.h"
#include "Runtime/Input/Core/Public/SlateApplication.h"
#include "Runtime/UI/DebugImguiWidget.h"
#include "Runtime/Interface/CrossEngineImp.h"
#include "Runtime/GameWorld/WorldSystemG.h"
#include "Runtime/GameWorld/GameWindowSystem.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/RenderWindowR.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/SettingsManager.h"
#include "Runtime/GameWorld/ScreenTerminalSystemG.h"

namespace cross 
{
    GameViewPortWidget::GameViewPortWidget(PlatformUser* inUser, SInt32& inWidgetLeft, SInt32& inWidgetTop, SInt32& inWidth, SInt32& inHeight)
        : mVisibility(Visibility::Visible())
        , mControllerPtr(inUser) 
        , mRefWindowsLeft(inWidgetLeft)
        , mRefWindowsTop(inWidgetTop)
        , mRefWindowsWidth(inWidth)
        , mRefWindowsHeight(inHeight)
        , mCurrentReply(input::CEReply::Handled())
    {       
        for (UInt64 pointerIndex = 0, size = static_cast<SInt32>(input::CETouchIndex::MAX_TOUCHES); pointerIndex < size; pointerIndex++)
        {
            PointerHandle handle = {pointerIndex};

            numPointersSamples[handle] = 0;
            pointersDelta[handle] = Float2(0.f, 0.f);
        } 
    }
      
    void GameViewPortWidget::OnFocusChanging(const SequenceableWidgetPath* previousFocusPath, const SequenceableWidgetPath* newWidgetPath, const input::FocusEvent& inFocusEvent) 
    {
    }

    void GameViewPortWidget::OnFocusLost(const SequenceableWidgetPath* newWidgetPath, const input::FocusEvent& inFocusEvent)
    {
    }

    input::CEReply GameViewPortWidget::OnFocusReceived(const SequenceableWidgetPath* newWidgetPath, const input::FocusEvent& inFocusEvent)
    {
        mCurrentReply = input::CEReply::Handled();
        return mCurrentReply;
    }

    void GameViewPortWidget::OnOSWindowDeactivated(const input::WindowActivateEvent& inActivateEvent) 
    {
        if (InputSetting::GetInputSetting().ShowDebugInfo)
            LOG_DEBUG("GameViewPortWidget::OnOSWindowDeactivated");

        mKeyStateMap.clear();

        auto* windowSystem = static_cast<WindowSystemG*>(EngineGlobal::GetEngine()->GetGlobalSystemByID(WindowSystemG::GetDesc().mID));
        auto* window = windowSystem->GetAppGlobalWindow();
        if (window)
        {
            auto* windowR = window->GetRenderObject();

            cross::threading::DispatchRenderingCommand([windowR] {
                if (windowR)
                    windowR->OnActivate(false);
            });
        }

        if (mControllerPtr != nullptr)
            mControllerPtr->Deactivated(inActivateEvent);
    }

    input::CEReply GameViewPortWidget::OnOSWindowActivated(const input::WindowActivateEvent& inActivateEvent)
    {
        if (InputSetting::GetInputSetting().ShowDebugInfo)
            LOG_DEBUG("GameViewPortWidget::OnOSWindowActivated");

        // Default pass through widget opportunity to handle event
        auto* windowSystem = static_cast<WindowSystemG*>(EngineGlobal::GetEngine()->GetGlobalSystemByID(WindowSystemG::GetDesc().mID));
 
        cross::threading::DispatchRenderingCommand([windowSystem] { 
            Window* window = windowSystem->GetAppGlobalWindow();
            if (window == nullptr)
                return;
            
            auto* windowR = window->GetRenderObject();
            if (windowR == nullptr)
                return;

            windowR->OnActivate(true);
        });
    
        if (inActivateEvent.Activation == CEWindowActivation::Activate)
        {
            SequenceableWidgetPath widgetToFocusPath;
            if (WidgetPathHelper::FindPathToWidget(shared_from_this(), widgetToFocusPath))
            {
                SlateApplication::Instance()->SetUserFocus(inActivateEvent.User, widgetToFocusPath, FocusCause::WindowActivate);
            }
            return AcquireFocusAndCapture(GetWidgetSize() / 2, FocusCause::WindowActivate);
        }

        return input::CEReply::Handled();  
    }

    void GameViewPortWidget::OnPointerCaptureLost(const input::PointerLostEvent& pointerLostEvent)
    {
    }

    void GameViewPortWidget::OnPointerEnter(const input::PointerEvent& inPointerEvent)
    {
    }

    void GameViewPortWidget::OnPointerLeave(const input::PointerEvent& inPointerEvent)
    {
    }

    input::CEReply GameViewPortWidget::OnPointerMove(const input::PointerMoveEvent& inPointerEvent) 
    {
        if (inPointerEvent.IsMouseEvent())
        {
            mCurrentReply = OnMouseMoved(inPointerEvent);
            return mCurrentReply;
        }

        if (inPointerEvent.IsTouchEvent())
        {
            mCurrentReply = OnTouchMoved(inPointerEvent);
            return mCurrentReply;
        }

        return input::CEReply::Unhandled();
    }

    input::CEReply GameViewPortWidget::OnMouseMoved(const input::PointerMoveEvent& inMouseEvent) 
    {        
        // Start a new reply state
        mCurrentReply = input::CEReply::Handled();
        Assert(inMouseEvent.PointerIndex == PlatformUser::CursorPointerIndex());

        // Cache cursor position if needed
        ApplyCachedCursorPos(inMouseEvent);

        const bool bViewportHasCaptured = HasMouseCapture();
        if (bViewportHasCaptured)
        {
            // do nothing for now
        }

        const bool bViewportHasOverlay = HasMouseOverlay();
        if (bViewportHasOverlay)
        {
            //LOG_INFO("--Mouse  Move -- {} {}, widgets {}", inMouseEvent.ScreenSpacePosition.x, inMouseEvent.ScreenSpacePosition.y, mControllerPtr->GetKeyValue(input::CEKeys::CursorX));

            mControllerPtr->HandleMouseMoveEvent(inMouseEvent, 1.0f);

            auto& curSampleCount = numPointersSamples[inMouseEvent.PointerIndex];
            auto& curPointerDelta = pointersDelta[inMouseEvent.PointerIndex];

            // Accumulate delta changes to mouse movement.  Depending on the sample frequency of a mouse we may get many per frame.
            //@todo Slate: In directinput, number of samples in x/y could be different...
            const Float2& cursorDelta = inMouseEvent.CursorDelta;
            curPointerDelta.x += cursorDelta.x;
            curPointerDelta.y += cursorDelta.y;
            ++curSampleCount;
        }

        // Attention: Mouse move make no sense with capture behavior 
        return mCurrentReply;
    }

    input::CEReply GameViewPortWidget::OnTouchMoved(const input::PointerMoveEvent& inTouchEvent)
    {
        // LOG_INFO("GameViewPort touch move, index={}", inTouchEvent.PointerIndex.mVal);
        mCurrentReply = input::CEReply::Handled();
        Assert(inTouchEvent.PointerIndex != PlatformUser::CursorPointerIndex());

        if (mControllerPtr->HandleTouchEvent(inTouchEvent, input::CETouchType::Moved))
        {
            auto& curSampleCount = numPointersSamples[inTouchEvent.PointerIndex];
            auto& curPointerDelta = pointersDelta[inTouchEvent.PointerIndex];

            const Float2& cursorDelta = inTouchEvent.CursorDelta;
            curPointerDelta.x += cursorDelta.x;
            curPointerDelta.y += cursorDelta.y;
            ++curSampleCount;
        }

        return mCurrentReply.CaptureTouch(shared_from_this());
    }

    input::CEReply GameViewPortWidget::OnPointerWheel(const input::PointerEvent& inPointerEvent)
    {
        return input::CEReply::Unhandled();
    }

    input::CEReply GameViewPortWidget::OnPreviewPointerButtonDown(const input::PointerEvent& inPointerEvent)
    {
        return input::CEReply::Unhandled();
    }

    input::CEReply GameViewPortWidget::OnPointerButtonDown(const input::PointerEvent& inPointerEvent) 
    {
        if (inPointerEvent.IsMouseEvent())
        {
            mCurrentReply = OnMouseDown(inPointerEvent);
            return mCurrentReply;
        }

        if (inPointerEvent.IsTouchEvent())
        {
            mCurrentReply = OnTouchStarted(inPointerEvent);
            return mCurrentReply;
        }

        return input::CEReply::Unhandled();
    }

    input::CEReply GameViewPortWidget::OnMouseDown(const input::PointerEvent& inMouseEvent)
    {
        if (InputSetting::GetInputSetting().ShowDebugInfo)
            LOG_DEBUG("GameViewPort Mouse Down, x={}, y={}, widgets={}", inMouseEvent.ScreenSpacePosition.x, inMouseEvent.ScreenSpacePosition.y, mControllerPtr->GetAllPointersCaptorWidgets().size());

        mCurrentReply = input::CEReply::Handled();

        mKeyStateMap[inMouseEvent.Key] = true;

        // Cache cursor position if needed
        ApplyCachedCursorPos(inMouseEvent);

        if (!mControllerPtr->HandleMouseEvent(inMouseEvent, inMouseEvent.IsRepeat() ? input::CEInputEvent::Repeat : input::CEInputEvent::Pressed, 1.0f))
            mCurrentReply = input::CEReply::Unhandled();

        // if current pointer equals cursor, attempt to focus it
        if (!mControllerPtr->IsIgnoreInput() && inMouseEvent.PointerIndex == PlatformUser::CursorPointerIndex())
            mCurrentReply = AcquireFocusAndCapture(inMouseEvent.ScreenSpacePosition, FocusCause::Type::Point);

        return mCurrentReply.CaptureMouse(shared_from_this());
    }

    input::CEReply GameViewPortWidget::OnTouchStarted(const input::PointerEvent& inTouchEvent)
    {
        mCurrentReply = input::CEReply::Handled();

        mNumTouches++;

        // Cache cursor position if needed
        ApplyCachedCursorPos(inTouchEvent);

        const Float2 touchPosition = mCursorPos;

        if (InputSetting::GetInputSetting().ShowDebugInfo)
            LOG_DEBUG("GameViewPort touch start, index={}, x={}, y={}", inTouchEvent.PointerIndex.mVal, touchPosition.x, touchPosition.y);

        if (!mControllerPtr->HandleTouchEvent(inTouchEvent, input::CETouchType::Began))
            mCurrentReply = input::CEReply::Unhandled();

        // if current pointer equals cursor, attempt to focus it
        if (!mControllerPtr->IsIgnoreInput() && inTouchEvent.PointerIndex == PlatformUser::CursorPrimaryTouchIndex())
            mCurrentReply = AcquireFocusAndCapture(inTouchEvent.ScreenSpacePosition, FocusCause::Type::Point);

        return input::CEReply::Handled();
    }

    input::CEReply GameViewPortWidget::OnPointerButtonUp(const input::PointerEvent& inPointerEvent)
    {
        if (inPointerEvent.IsMouseEvent())
        {
            mCurrentReply = OnMouseUp(inPointerEvent);
            return mCurrentReply;
        }

        if (inPointerEvent.IsTouchEvent())
        {
            mCurrentReply = OnTouchEnded(inPointerEvent);
            return mCurrentReply;
        }

        return input::CEReply::Unhandled();
    }

    input::CEReply GameViewPortWidget::OnMouseUp(const input::PointerEvent& inMouseEvent)
    {
        if (InputSetting::GetInputSetting().ShowDebugInfo)
            LOG_DEBUG("GameViewPort Mouse Up, x={}, y={}, widgets={}", inMouseEvent.ScreenSpacePosition.x, inMouseEvent.ScreenSpacePosition.y, mControllerPtr->GetAllPointersCaptorWidgets().size());

        mCurrentReply = input::CEReply::Handled();

        mKeyStateMap[inMouseEvent.Key] = false;

        // Cache cursor position if needed
        ApplyCachedCursorPos(inMouseEvent);

        if (!mControllerPtr->HandleMouseEvent(inMouseEvent, input::CEInputEvent::Released))
            mCurrentReply = input::CEReply::Unhandled();

        // if current pointer equals cursor, attempt to deactive it
        if (inMouseEvent.PointerIndex == PlatformUser::CursorPointerIndex())
            mCurrentReply = DisregardAndLoose(inMouseEvent.ScreenSpacePosition, FocusCause::Type::Point);

        return mCurrentReply;
    }

    input::CEReply GameViewPortWidget::OnTouchEnded(const input::PointerEvent& inTouchEvent)
    {
        if (InputSetting::GetInputSetting().ShowDebugInfo)
            LOG_DEBUG("GameViewPort touch end, index={}", inTouchEvent.PointerIndex.mVal);

        // Start a new reply state
        mCurrentReply = input::CEReply::Handled();

        Float2 curCursorPos;
        if (--mNumTouches > 0)
        {
            ApplyCachedCursorPos(inTouchEvent);
            curCursorPos = mCursorPos;
        }
        else
        {
            ApplyCachedCursorPos(inTouchEvent);
            curCursorPos = mCursorPos;
            mCursorPos = Float2(-1, -1);
        }

        if (!mControllerPtr->HandleTouchEvent(inTouchEvent, input::CETouchType::Ended))
            mCurrentReply = input::CEReply::Unhandled();

        mCurrentReply.ReleasePointCapture();
        return input::CEReply::Handled();
    }

    input::CEReply GameViewPortWidget::OnKeyChar(const input::CharEvent& inKeyEvent)
    {
        return input::CEReply::Handled();
    }

    input::CEReply GameViewPortWidget::OnPreviewKeyDown(const input::KeyEvent& inKeyEvent) 
    {
        return input::CEReply::Unhandled();
    }

    input::CEReply GameViewPortWidget::OnKeyDown(const input::KeyEvent& inKeyEvent) 
    {
        mCurrentReply = input::CEReply::Handled();
        mKeyStateMap[inKeyEvent.Key] = true;

        // disable it, since it is not a universal needs and crash now
        //if (inKeyEvent.Key == input::CEKeys::Tilde) {
        //    DispatchRenderingCommandWithToken([=] {
        //        auto rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        //        if (rdrSys->GetREDGUIState() != REDGUIState::REDVisualizer)
        //        {
        //            rdrSys->SetREDGUIState(REDGUIState::REDVisualizer);
        //        }
        //        else
        //        {
        //            rdrSys->SetREDGUIState(REDGUIState::None);
        //        }
        //    });
        //    for (auto widget : mChildren) {
        //        if (dynamic_cast<DebugImguiWidget*>(widget.get())) {
        //            widget->OnKeyDown(inKeyEvent);
        //        }
        //    }
        //}
        
        if (!mControllerPtr->HandleKeyboardEvent(
            inKeyEvent, 
            inKeyEvent.IsRepeat() ? input::CEInputEvent::Repeat : input::CEInputEvent::Pressed, 
            1.0f))
            mCurrentReply = input::CEReply::Unhandled();

        return mCurrentReply;
    }

    input::CEReply GameViewPortWidget::OnKeyUp(const input::KeyEvent& inKeyEvent)
    {
        mCurrentReply = input::CEReply::Handled();

        mKeyStateMap[inKeyEvent.Key] = false;

        if (!mControllerPtr->HandleKeyboardEvent(
            inKeyEvent,
            input::CEInputEvent::Released, 
            1.0f))
            mCurrentReply = input::CEReply::Unhandled();

        return mCurrentReply;
    }

    input::CEReply GameViewPortWidget::OnAnalogValueChanged(const input::AnalogInputEvent& inAnalogEvent)
    {
        mCurrentReply = input::CEReply::Handled();

        mKeyStateMap[inAnalogEvent.Key] = true;

        float deltaTime = EngineGlobal::Inst().GetFrameParamMgr()->GetCurrentGameFrameParam()->GetDeltaTime();

        if (!mControllerPtr->SimulatePointerAxis1DBy(inAnalogEvent.Key, inAnalogEvent.GetAnalogValue(), deltaTime, 1))
            mCurrentReply = input::CEReply::Unhandled();

        return mCurrentReply;
    }

    input::CEReply GameViewPortWidget::OnTouchGesture(const input::PointerEvent& inGestureEvent)
    {
        mCurrentReply = input::CEReply::Handled();

        ApplyCachedCursorPos(inGestureEvent);

        if (inGestureEvent.GestureType != input::CEGestureEvent::LongPress)
        {
            SlateApplication::Instance()->SetUserFocusToGameViewport({0});
        }

        mCurrentReply = input::CEReply::Unhandled();

        return mCurrentReply;
    }

    void GameViewPortWidget::OnFinishedPointerInput(cross::FrameParam* fp, float elapsedTime)
    {
        ProcessAccumulatedPointerInput(elapsedTime);
    }

    void GameViewPortWidget::OnFinishedKeyInput(cross::FrameParam* fp, float elapsedTime) 
    {
    }

    bool GameViewPortWidget::IsAnyPointerCaptured() const
    {
        auto& global = EngineGlobal::Inst();
        auto users = global.GetInputManager()->GetUsers();

        for (const PlatformUserPtr& user : users)
        {
            if (user && user->DoesWidgetHaveAnyCapture(shared_from_this()))
            {
                return true;
            }
        }
        return false;
    }

    bool GameViewPortWidget::HasMouseCapture() const
    {
        auto& global = EngineGlobal::Inst();
        auto users = global.GetInputManager()->GetUsers();

        for (const PlatformUserPtr& user : users)
        {
            if (user && user->DoesWidgetHaveMouseCapture(shared_from_this()))
            {
                return true;
            }
        }
        return false;
    }

    bool GameViewPortWidget::HasTouchCapture() const
    {
        auto& global = EngineGlobal::Inst();
        auto users = global.GetInputManager()->GetUsers();

        for (const PlatformUserPtr& user : users)
        {
            if (user && user->DoesWidgetHaveTouchCapture(shared_from_this()))
            {
                return true;
            }
        }
        return false;
    }

    bool GameViewPortWidget::HasPointerCaptureByUser(UserHandle inUserIndex, PointerHandle inPointerIndex /*= { input::CETouchIndex::CursorPointerIndex }*/) const
    {
        auto& global = EngineGlobal::Inst();
        auto user = global.GetInputManager()->GetUser(inUserIndex);

        return user->DoesWidgetHaveCapture(shared_from_this(), inPointerIndex);
    }

    bool GameViewPortWidget::HasMouseOverlay() const 
    {
        auto& global = EngineGlobal::Inst();
        auto users = global.GetInputManager()->GetUsers();

        for (const PlatformUserPtr& user : users)
        {
            if (user && user->DoesWidgetHaveMouseOverlay(shared_from_this()))
            {
                return true;
            }
        }
        return false;
    }

    bool GameViewPortWidget::HasTouchOverlay() const 
    {
        auto& global = EngineGlobal::Inst();
        const auto& users = global.GetInputManager()->GetUsers();

        for (const PlatformUserPtr& user : users)
        {
            if (user && user->DoesWidgetHaveTouchOverlay(shared_from_this()))
            {
                return true;
            }
        }
        return false;
    }

    bool GameViewPortWidget::HasPointerOverlayByUser(UserHandle inUserIndex, PointerHandle inPointerIndex /*= {input::CETouchIndex::CursorPointerIndex}*/) const 
    {
        auto& global = EngineGlobal::Inst();
        auto user = global.GetInputManager()->GetUser(inUserIndex);

        return user->DoesWidgetHaveOverlay(shared_from_this(), inPointerIndex);
    }

    void GameViewPortWidget::ProcessAccumulatedPointerInput(float elapsedTime) 
    {
        if (mControllerPtr == nullptr)
            return;

        // Process cursor axis value into CEKey::MouseX & CEKey::MouseY
        auto& numCursorSampleCount = numPointersSamples[PlatformUser::CursorPointerIndex()];
        auto& accCursorDelta = pointersDelta[PlatformUser::CursorPointerIndex()];

        if (numCursorSampleCount) 
        {
            // 
            mControllerPtr->SimulatePointerAxis1DBy(input::CEKeys::CursorX, accCursorDelta.x, elapsedTime, numCursorSampleCount);
            mControllerPtr->SimulatePointerAxis1DBy(input::CEKeys::CursorY, accCursorDelta.y, elapsedTime, numCursorSampleCount);
        
            // If current cursor captured by mouse not touch,
            // calculate mouse sample rate then
            if (HasMouseOverlay()) 
            {
                // calculate sampling time
                // make sure not first non-zero sample
                if (mControllerPtr->mMouseAxisSmoothedSpeed[0] > 0.f || mControllerPtr->mMouseAxisSmoothedSpeed[1] > 0.f)
                {
                    // not first non-zero
                    mControllerPtr->mMouseSamplingTotal += elapsedTime;
                    mControllerPtr->mMouseSampleCount += numCursorSampleCount;
                }
            }
        }

        // Reset acculatme values
        numCursorSampleCount = 0;
        accCursorDelta = Float2(0.f, 0.f);
    }

    void GameViewPortWidget::ApplyModifierKeys(const input::CEModifierKeyStates& inKeysState)
    {
        if (mControllerPtr && mRefWindowsWidth > 0 && mRefWindowsHeight > 0)
        {
            {
                input::KeyEvent leftAltEvent(mControllerPtr->GetUserHandle(), input::CEKeys::LeftAlt, inKeysState, false);
                mControllerPtr->HandleKeyboardEvent(
                    leftAltEvent, 
                    inKeysState.IsLeftAltDown() ? input::CEInputEvent::Pressed : input::CEInputEvent::Released);

                mKeyStateMap[input::CEKeys::LeftAlt] = inKeysState.IsLeftAltDown() ? true : false;
            }

            {
                input::KeyEvent rightAltEvent(mControllerPtr->GetUserHandle(), input::CEKeys::RightAlt, inKeysState, false);
                mControllerPtr->HandleKeyboardEvent(rightAltEvent, 
                    inKeysState.IsRightAltDown() ? input::CEInputEvent::Pressed : input::CEInputEvent::Released);

                mKeyStateMap[input::CEKeys::RightAlt] = inKeysState.IsRightAltDown() ? true : false;
            }

            //if (inKeysState.IsRightAltDown())
            //{
            //    ViewportClient->InputKey(FInputKeyEventArgs(this, 0, EKeys::RightAlt, IE_Pressed));
            //}
            //if (inKeysState.IsLeftControlDown())
            //{
            //    ViewportClient->InputKey(FInputKeyEventArgs(this, 0, EKeys::LeftControl, IE_Pressed));
            //}
            //if (inKeysState.IsRightControlDown())
            //{
            //    ViewportClient->InputKey(FInputKeyEventArgs(this, 0, EKeys::RightControl, IE_Pressed));
            //}
            //if (inKeysState.IsLeftShiftDown())
            //{
            //    ViewportClient->InputKey(FInputKeyEventArgs(this, 0, EKeys::LeftShift, IE_Pressed));
            //}
            //if (inKeysState.IsRightShiftDown())
            //{
            //    ViewportClient->InputKey(FInputKeyEventArgs(this, 0, EKeys::RightShift, IE_Pressed));
            //}
        }
    }

    void GameViewPortWidget::ApplyCachedCursorPos(const input::PointerEvent& inPointerEvent) 
    {
        if (inPointerEvent.PointerIndex == PlatformUser::CursorPointerIndex() ||
            inPointerEvent.PointerIndex == PlatformUser::CursorPrimaryTouchIndex())
        {
            mCursorPos = ConvertScreenSpaceIntoWidgetSpace(inPointerEvent.ScreenSpacePosition);
        }
    }

    input::CEReply GameViewPortWidget::AcquireFocusAndCapture(Float2 inPointerPos, FocusCause::Type focusCause /*= FocusCause::SetDirectly*/) 
    {
        input::CEReply replyState = input::CEReply::Handled();
                                
        // Mouse down should focus viewport for user input
        replyState.SetUserFocus(shared_from_this(), focusCause);

        // Capture mouse into current widget
        //replyState.CaptureMouse(shared_from_this());

        // Lock cursor range into current widget
        replyState.LockMouseToWidget(shared_from_this());

        return replyState;
    }
     
    input::CEReply GameViewPortWidget::DisregardAndLoose(Float2 inScreenPos, FocusCause::Type focusCause/* = FocusCause::SetDirectly*/) 
    {
        input::CEReply replyState = input::CEReply::Handled();

        // replyState.ClearUserFocus(focusCause);
        // replyState.ReleaseMouseLock();

        return replyState;
    }

}

