#pragma once

/** Generic implementation for the process handle. */
template<typename T, T InvalidHandleValue>
struct TProcessHandle
{
	typedef T HandleType;
public:

	/** Default constructor. */
	FORCEINLINE TProcessHandle()
		: Handle(InvalidHandleValue)
	{ }

	/** Initialization constructor. */
	FORCEINLINE explicit TProcessHandle(T Other)
		: Handle(Other)
	{ }

	/** Assignment operator. */
	FORCEINLINE TProcessHandle& operator=(const TProcessHandle& Other)
	{
		if (this != &Other)
		{
			Handle = Other.Handle;
		}
		return *this;
	}

	/** Accessors. */
	FORCEINLINE T Get() const
	{
		return Handle;
	}

	FORCEINLINE void Reset()
	{
		Handle = InvalidHandleValue;
	}

	FORCEINLINE bool IsValid() const
	{
		return Handle != InvalidHandleValue;
	}

	/**
	* (Deprecated. Handles created with PlatformProcess::CreateProc() should be closed with PlatformProcess::CloseProc())
	* Closes handle and frees this resource to the operating system.
	* @return true, if this handle was valid before closing it
	*/
	FORCEINLINE bool Close()
	{
		return IsValid();
	}

protected:
	/** Platform specific handle. */
	T Handle;
};

struct ProcessHandle;

/**
* Generic implementation for most platforms, these tend to be unused and unimplemented
**/
struct GenericPlatformProcess
{
	static const char* ShaderDir();
};
