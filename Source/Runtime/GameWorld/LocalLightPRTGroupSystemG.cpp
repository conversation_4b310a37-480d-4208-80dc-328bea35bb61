#include "EnginePrefix.h"
#include "CrossBase/Template/TypeCast.hpp"
#include "Threading/RenderingThread.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "Resource/MeshAssetDataResource.h"
#include "Resource/Resource.h"
#include "Resource/ResourceManager.h"
#include "Resource/AssetStreaming.h"
#include "Runtime/GameWorld/GameWorld.h"

#include "Runtime/GameWorld/TransformSystemG.h"
#include "Runtime/GameWorld/LocalLightPRTGroupSystemG.h"
#include "Runtime/GameWorld/PrimitiveRenderSystemG.h"
#include "Runtime/GameWorld/AABBSystemG.h"
#include "Runtime/GameWorld/LightSystemG.h"
#include "Runtime/GameWorld/LightMapSystemG.h"
#include "RenderEngine/PrimitiveGenerator.h"

namespace cross {
ecs::ComponentDesc* cross::LocalLightPRTGroupComponentG::GetDesc()
{
    return EngineGlobal::GetECSFramework().CreateOrGetGameComponentDesc<cross::LocalLightPRTGroupComponentG>({ false, true, false },
                                                                                                             &LocalLightPRTGroupSystemG::SerializeLocalLightPRTGroupComponent,
                                                                                                             &LocalLightPRTGroupSystemG::DeserializeLocalLightPRTGroupComponent,
                                                                                                             &LocalLightPRTGroupSystemG::PostDeserializeLocalLightPRTGroupComponent,
                                                                                                             ecs::ComponentDesc::ComponnetUpdateDeserializeDeFunc(),
                                                                                                             &LocalLightPRTGroupSystemG::GetResourceLocalLightPRTGroupComponent);
}

cross::ecs::EntityID LocalLightPRTGroupSystemG::GetLocalLightPRTGroup(ecs::EntityID lightID)
{
    auto transformSys = mGameWorld->GetGameSystem<cross::TransformSystemG>();
    ecs::EntityID retID = ecs::EntityID::InvalidHandle();
    transformSys->TraverseHierarchyParent(lightID, [this, &retID](ecs::EntityID eID) { 
        if (mGameWorld->HasComponent<LocalLightPRTGroupComponentG>(eID))
        {
            retID = eID;
            return true;
        }
        return false;
    });
    return retID;
}

int LocalLightPRTGroupSystemG::GetLocalLightNum(ecs::EntityID groupID)
{
    auto transformSys = mGameWorld->GetGameSystem<cross::TransformSystemG>();
    auto lightmapSys = mGameWorld->GetGameSystem<cross::LightMapSystemG>();
    int retNum = 0;
    transformSys->TraverseHierarchyDepth(groupID, [lightmapSys = lightmapSys, &retNum](ecs::EntityID eID){
        if (lightmapSys->IsBakedLight(eID)) {
            retNum++;
        }
    });
    return retNum;
}

bool LocalLightPRTGroupSystemG::IntersectWithBoundingBox(ecs::EntityID eID, std::vector<ecs::EntityID>& intersectIDs)
{
    auto transformSys = mGameWorld->GetGameSystem<cross::TransformSystemG>();
    auto aabbSys = mGameWorld->GetGameSystem<cross::AABBSystemG>();

    bool useEntityAABB = true;
    BoundingBox entityAABB;
    Float3 entityPos;
    if (mGameWorld->HasComponent<AABBComponentG>(eID))
    {
        entityAABB = aabbSys->GetWorldAABB(mGameWorld->GetComponent<AABBComponentG>(eID).Read());
        useEntityAABB = true;
    }
    else
    {
        entityPos = transformSys->GetWorldTranslation(mGameWorld->GetComponent<WorldTransformComponentG>(eID).Read());
        useEntityAABB = false;
    }
    
    auto queryResult = mGameWorld->Query<LocalLightPRTGroupComponentG>();
    auto entityNum = queryResult.GetEntityNum();

    intersectIDs.clear();
    for (UInt32 eIdx = 0; eIdx < entityNum; eIdx++)
    {
        auto&& localLightPRTGroupComp = queryResult[eIdx];
        auto groupID = localLightPRTGroupComp.GetEntityID();
        auto comp = localLightPRTGroupComp.Write();
        if (GetLocalLightNum(groupID) <= 0)
        {
            continue;
        }

        auto transComp = mGameWorld->GetComponent<WorldTransformComponentG>(groupID);
        auto worldMat = transformSys->GetWorldMatrix(transComp.Read());
        Float4A scale, translation;
        QuaternionA rotation;
        worldMat.Decompose(scale, rotation, translation);
        BoundingBox localLightGroupAABB(Float3(translation.x, translation.y, translation.z), comp->mLocalPRTBoundsExtent * cross::Float3{scale.x, scale.y, scale.z});

        if (useEntityAABB)
        {
            if (localLightGroupAABB.Intersects(entityAABB))
            {
                intersectIDs.push_back(groupID);
            }
        }
        else
        {
            if (localLightGroupAABB.Contains(entityPos) == ContainmentType::ContainmentContain)
            {
                intersectIDs.push_back(groupID);
            }
        }
    }
    return intersectIDs.size() > 0;
}

SerializeNode LocalLightPRTGroupSystemG::SerializeLocalLightPRTGroupComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr)
{
    SerializeNode json;
    SerializeContext context;
    auto comp = static_cast<LocalLightPRTGroupComponentG*>(componentPtr);
    json = comp->Serialize(context);
    return json;
}

void LocalLightPRTGroupSystemG::GetResourceLocalLightPRTGroupComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr, ResourcePtr resource)
{
}

void LocalLightPRTGroupSystemG::DeserializeLocalLightPRTGroupComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr)
{
    if (json.IsNull())
    {
        return;
    }

    auto comp = static_cast<LocalLightPRTGroupComponentG*>(componentPtr);
    if (json.IsObject())
    {
        SerializeContext context;
        comp->Deserialize(json, context);
    }
    return;
}

void LocalLightPRTGroupSystemG::PostDeserializeLocalLightPRTGroupComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId)
{
    auto gameSys = gameWorld->GetGameSystem<LocalLightPRTGroupSystemG>();
    gameSys->AfterInitComponent(entityId);
    return;
}
LocalLightPRTGroupSystemG* LocalLightPRTGroupSystemG::CreateInstance()
{
    return new LocalLightPRTGroupSystemG();
}

LocalLightPRTGroupSystemG::LocalLightPRTGroupSystemG()
{
}

LocalLightPRTGroupSystemG::~LocalLightPRTGroupSystemG() {}

void LocalLightPRTGroupSystemG::AfterInitComponent(ecs::EntityID entityID)
{
    //auto localLightComp = mGameWorld->GetComponent<LocalLightPRTGroupComponentG>(entityID).Write();
    return;
}

void LocalLightPRTGroupSystemG::Release()
{
    delete this;
}

RenderSystemBase* LocalLightPRTGroupSystemG::GetRenderSystem()
{
    return nullptr;
}

void LocalLightPRTGroupSystemG::OnBuildUpdateTasks(FrameParam* frameParam)
{
    static PrimitiveRenderSystemG::PrimitiveLook look({1, 0, 0}, PrimitiveDepth::SceneDepth);

#if CROSSENGINE_EDITOR && CROSSENGINE_WIN
    CreateTaskFunction(FrameTickStage::Update, {}, [this] {
        QUICK_SCOPED_CPU_TIMING("LocalLightPRTGroupSystemUpdate");
        auto primitiveSystem = mGameWorld->GetGameSystem<cross::PrimitiveRenderSystemG>();
        auto transformSystem = mGameWorld->GetGameSystem<cross::TransformSystemG>();

        auto queryResult = mGameWorld->Query<LocalLightPRTGroupComponentG>();
        auto entityNum = queryResult.GetEntityNum();

        for (UInt32 eIdx = 0; eIdx < entityNum; eIdx++)
        {
            auto&& localLightPRTGroupComp = queryResult[eIdx];
            auto transComp = mGameWorld->GetComponent<WorldTransformComponentG>(localLightPRTGroupComp.GetEntityID());
            auto worldMat = transformSystem->GetWorldMatrix(transComp.Read());
            Float4A scale, translation;
            QuaternionA rotation;
            worldMat.Decompose(scale, rotation, translation);

            auto comp = localLightPRTGroupComp.Write();
            if (comp->mDebugShow)
            {
                PrimitiveData cubePrim;
                PrimitiveGenerator::GenerateCubeFrame(&cubePrim, comp->mLocalPRTBoundsExtent * cross::Float3{scale.x, scale.y, scale.z}, {translation.x, translation.y, translation.z});
                primitiveSystem->DrawPrimitive(&cubePrim, Float4x4A::CreateTranslation(0.f, 0.f, 0.f), look);
            }
        }
    });
#endif
}

void LocalLightPRTGroupSystemG::GetLocalLightPRTGroupComponent(const LocalLightPRTGroupComponentReader& component, cross::LocalLightPRTGroupComponentG& outValue)
{
    outValue.mLocalPRTColorScale = component->mLocalPRTColorScale;
    outValue.mLocalPRTBoundsExtent = component->mLocalPRTBoundsExtent;
    outValue.mDebugShow = component->mDebugShow;
}

void LocalLightPRTGroupSystemG::SetLocalLightPRTGroupComponent(const LocalLightPRTGroupComponentWriter& component, const cross::LocalLightPRTGroupComponentG& inValue)
{
    component->mLocalPRTBoundsExtent = inValue.mLocalPRTBoundsExtent;
    component->mDebugShow = inValue.mDebugShow;
    if (component->mLocalPRTColorScale != inValue.mLocalPRTColorScale)
    {
        component->mLocalPRTColorScale = inValue.mLocalPRTColorScale;
        LocalLightPRTGroupChangeEvent event(component.GetEntityID());
        DispatchImmediateEvent(event);
    }
}
}   // namespace cross
