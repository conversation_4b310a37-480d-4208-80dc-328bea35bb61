#include "EnginePrefix.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "Runtime/GameWorld/PostProcessVolumeSystemG.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "Resource/AssetStreaming.h"

#include "Runtime/Input/InputManager.h"
#include "Runtime/Input/InputCore.h"
#include "Runtime/GameWorld/TransformSystemG.h"

namespace cross {
PostProcessVolumeSystemG::PostProcessVolumeSystemG()
{
    mPostProcessVolumeSystemR = PostProcessVolumeSystemR::CreateInstance();
}

void PostProcessVolumeSystemG::SetPostProcessVolumeTRS(const PostProcessVolumeCompWriter& comp, const TRS_A& TRS, GameWorld* gameWorld)
{
    auto* gameSystem = gameWorld->GetGameSystem<cross::PostProcessVolumeSystemG>();
    auto renderSystem = gameSystem->mPostProcessVolumeSystemR;
    Float4x4 boundingToWorld;
    Float3 tilePos;
    ComputeBoundingToWorld(TRS, boundingToWorld, tilePos);

    Float3 extend = Float3(static_cast<float>(TRS.mScale.x / 2), static_cast<float>(TRS.mScale.y / 2), static_cast<float>(TRS.mScale.z / 2));

    DispatchRenderingCommandWithToken([renderSystem = renderSystem, eID = comp.GetEntityID(), boundingToWorld, extend, tilePos]() { renderSystem->SetPostProcessVolumeBounding(eID, boundingToWorld, extend, tilePos); });
}

void PostProcessVolumeSystemG::ComputeBoundingToWorld(const TRS_A& inTRS, Float4x4& outTRS, Float3& outTile)
{
    TRS_A BoundingTRS = inTRS;

    // Avoid negative and very small scale
    BoundingTRS.mScale = BoundingTRS.mScale.Abs();
    BoundingTRS.mScale.x = MathUtils::Max(BoundingTRS.mScale.x, static_cast<TRSScalarType>(MathUtils::MathSmallNumber));
    BoundingTRS.mScale.y = MathUtils::Max(BoundingTRS.mScale.y, static_cast<TRSScalarType>(MathUtils::MathSmallNumber));
    BoundingTRS.mScale.z = MathUtils::Max(BoundingTRS.mScale.z, static_cast<TRSScalarType>(MathUtils::MathSmallNumber));

    Float3 Translation = static_cast<Float3>(BoundingTRS.mTranslation);
    Float3 Scale = static_cast<Float3>(BoundingTRS.mScale);
    Quaternion Rotation = static_cast<Quaternion>(BoundingTRS.mRotation);

    outTile = {0, 0, 0};
#ifdef CE_USE_DOUBLE_TRANSFORM
    GetTileAndOffsetForAbsolutePosition(BoundingTRS.mTranslation, outTile, Translation);
#endif

    outTRS = Float4x4::Compose(Scale, Rotation, Translation);
}

void PostProcessVolumeSystemG::OnEntityPropChange(ecs::EntityID entity, ecs::EntityDescFlags::EntityProps prop, bool bValue) 
{
    auto ppvCompH = mGameWorld->GetComponent<cross::PostProcessVolumeComponentG>(entity);
    if (ppvCompH.IsValid())
        SetEnable(ppvCompH.Write(), bValue);
}

ecs::ComponentDesc* PostProcessVolumeComponentG::GetDesc()
{
    return EngineGlobal::GetECSFramework().CreateOrGetGameComponentDesc<cross::PostProcessVolumeComponentG>(
        { false, true, true}, 
        &PostProcessVolumeSystemG::SerializePostProcessVolumeComponent, 
        &PostProcessVolumeSystemG::DeserializePostProcessVolumeComponent, 
        &PostProcessVolumeSystemG::PostDeserializePostProcessVolumeComponent,
        &PostProcessVolumeSystemG::UpdateDeserializeComponent,
        &PostProcessVolumeSystemG::GetResourceComponent);
}

PostProcessVolumeSystemG* PostProcessVolumeSystemG::CreateInstance()
{
    return new PostProcessVolumeSystemG();
}

void PostProcessVolumeSystemG::NotifyEvent(const SystemEventBase& event, UInt32& flag)
{
    GameSystemBase::NotifyEvent(event, flag);
    if (event.mEventType == TRSChangedEvent::sEventType)
    {
        const TRSChangedEvent& e = TYPE_CAST(const TRSChangedEvent&, event);
        auto& eventData = e.mData;

        ecs::EntityID entityId = eventData.mEntity;

        if (eventData.mEventFlag & TRSEventFlagTRSChanged)
        {
            if (!mGameWorld->HasComponent<PostProcessVolumeComponentG>(entityId))
            {
                return;
            }
            auto ppvHandle = mGameWorld->GetComponent<PostProcessVolumeComponentG>(entityId);
            if (ppvHandle == PostProcessVolumeCompHandle::InvalidHandle())
            {
                return;
            }

            auto transformH = mGameWorld->GetComponent<WorldTransformComponentG>(ppvHandle.GetEntityID());
            Assert(transformH.IsValid());
            auto transformReader = transformH.Read();
            const TRS_A& worldTransform = mGameWorld->GetGameSystem<TransformSystemG>()->GetWorldTransformT(transformReader);
            {
                auto ppvWriter = ppvHandle.Write();
                SetPostProcessVolumeTRS(ppvWriter, worldTransform, mGameWorld);
            }
        }
    }
    else if (event.mEventType == OnSystemAddToGameWorldEvent::sEventType || event.mEventType == GameWorldSystemChangedEvent::sEventType)
    {
        // Subscribe events when add to world
        mGameWorld->SubscribeRemainedEvent<EntityDestroyEvent>(this, true);
        mGameWorld->SubscribeRemainedEvent<EntityCreateEvent>(this, true);

        auto transformSys = mGameWorld->GetGameSystem<TransformSystemG>();
        if (transformSys)
        {
            transformSys->SubscribeEvent<TRSChangedEvent>(this, 10);
        }
    }
}

SerializeNode PostProcessVolumeSystemG::SerializePostProcessVolumeComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr)
{
    auto postProcessCompPtr = static_cast<PostProcessVolumeComponentG*>(componentPtr);

    SerializeContext context;
    SerializeNode outNode = postProcessCompPtr->Serialize(context);
    return outNode;
}

void PostProcessVolumeSystemG::DeserializePostProcessVolumeComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr)
{
    if (json.IsNull())
        return;

    auto postProcessCompPtr = static_cast<PostProcessVolumeComponentG*>(componentPtr);
    if (json.IsObject())
    {
        SerializeContext context;
        postProcessCompPtr->Deserialize(json, context);
    }
}

void PostProcessVolumeSystemG::PostDeserializePostProcessVolumeComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId)
{
    auto* gameSystem = gameWorld->GetGameSystem<cross::PostProcessVolumeSystemG>();
    auto handle = gameWorld->GetComponent<cross::PostProcessVolumeComponentG>(entityId);

    // auto comp = (PostProcessVolumeComponentG*)componentPtr;
    auto compW = handle.Write();
    auto& lutPath = compW->PostProcessVolumeSettings.mTonemapSettings.ManuelLUTSetting.LUT;
    auto renderSystem = gameSystem->mPostProcessVolumeSystemR;

    LoadToUpdateLUTTexture(compW, lutPath);

    auto texPtr = compW->LUTTexturePtr;
    auto& val = compW->PostProcessVolumeSettings;
    DispatchRenderingCommandWithToken([renderSystem = renderSystem, eID = entityId, val, texPtr]() {
        renderSystem->SetPropertyPostProcessVolumeSettings(eID, val);
        renderSystem->SetBoundingWireFrameShow(eID, true);
        if (texPtr != nullptr)
            renderSystem->SetPostProcessVolumeLUTTexture(eID, TYPE_CAST(GPUTexture*, texPtr->GetTextureR()));
    });
    UpdatePostProcessVolumeTRS(compW, gameWorld);
}

void PostProcessVolumeSystemG::GetResourceComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr, ResourcePtr resource)
{
    auto postProcessCompPtr = static_cast<PostProcessVolumeComponentG*>(componentPtr);
    if (postProcessCompPtr->PostProcessVolumeSettings.mTonemapSettings.ManuelLUTSetting.EnableLUT)
    {
        resource->AddReferenceResource(postProcessCompPtr->LUTTexturePtr->GetGuid_Str());
    }
}

void PostProcessVolumeSystemG::GetPostProcessVolumeComponent(const PostProcessVolumeCompReader& inHandle, cross::PostProcessVolumeComponentG& outValue) const
{
    outValue.PostProcessVolumeSettings = inHandle->PostProcessVolumeSettings;
}
void PostProcessVolumeSystemG::SetPostProcessVolumeComponent(const PostProcessVolumeCompWriter& inHandle, cross::PostProcessVolumeComponentG& inValue)
{
    inHandle->PostProcessVolumeSettings = inValue.PostProcessVolumeSettings;
    if (inValue.PostProcessVolumeSettings.mTonemapSettings.ManuelLUTSetting.LUT != "")
    {
        LoadToUpdateLUTTexture(inHandle, inValue.PostProcessVolumeSettings.mTonemapSettings.ManuelLUTSetting.LUT);
    }
    SetPropertyPostProcessVolumeSettings(inHandle, inValue.PostProcessVolumeSettings);
}

void PostProcessVolumeSystemG::UpdatePostProcessVolumeTRS(const PostProcessVolumeCompWriter& inHandle, GameWorld* gameWorld)
{
    auto transformH = gameWorld->GetComponent<WorldTransformComponentG>(inHandle.GetEntityID());
    Assert(transformH.IsValid());
    auto transformReader = transformH.Read();
    const TRS_A& worldTransform = gameWorld->GetGameSystem<TransformSystemG>()->GetWorldTransformT(transformReader);
    {
        SetPostProcessVolumeTRS(inHandle, worldTransform, gameWorld);
    }
}

void PostProcessVolumeSystemG::SetEnable(const PostProcessVolumeCompWriter& comp, bool val)
{
    DispatchRenderingCommandWithToken([renderSystem = mPostProcessVolumeSystemR, eID = comp.GetEntityID(), val]() { renderSystem->SetEnable(eID, val); });
}

void PostProcessVolumeSystemG::SetGameOverrideSetting(const PostProcessGameOverride& ppSetting, PostProcessOverrideState overrideState)
{
    DispatchRenderingCommandWithToken([renderSystem = mPostProcessVolumeSystemR, ppSetting, overrideState]() { renderSystem->SetGameOverrideSetting(ppSetting, overrideState); });
}

void PostProcessVolumeSystemG::ClearGameOverride(PostProcessOverrideState overrideState)
{
    DispatchRenderingCommandWithToken([renderSystem = mPostProcessVolumeSystemR, overrideState]() { renderSystem->ClearGameOverrideSetting(overrideState); });
}

void PostProcessVolumeSystemG::SetEnableGameOverrideSetting(bool bEnable) 
{
    DispatchRenderingCommandWithToken([renderSystem = mPostProcessVolumeSystemR, bEnable]() { renderSystem->SetEnableGameOverride(bEnable); });
}

void PostProcessVolumeSystemG::SetSelected(const PostProcessVolumeCompWriter& comp, bool val)
{
    DispatchRenderingCommandWithToken([renderSystem = mPostProcessVolumeSystemR, eID = comp.GetEntityID(), val]() { renderSystem->SetSelected(eID, val); });
}

void PostProcessVolumeSystemG::LoadToUpdateLUTTexture(const PostProcessVolumeCompWriter& component, const std::string& texPath)
{
    component->LUTTexturePtr = TypeCast<resource::Texture>(gAssetStreamingManager->LoadSynchronously(texPath));
    if (component->LUTTexturePtr == nullptr)
    {
        Assert(false);
        LOG_ERROR("Tone Mapping LUT Texture resource loading failed");
    }
}
}   // namespace cross
