#pragma once
#include "CECommon/Common/GlobalSystemBase.h"
#include "CECommon/Common/IRenderWindow.h"
#include "RenderEngine/IWindowR.h"
#include "Resource/RenderTextureResource.h"

#ifdef CreateWindow
#undef CreateWindow
#endif

struct IRenderWindow;
class ICrossEngine;

namespace cross {

class FrameParam;
class WindowSystemR;
struct RenderWindowInitInfo;
struct ResolutionChangedEventData
{
    UInt32 width;
	UInt32 height;
};
using ResolutionChangedEvent = SystemEvent<ResolutionChangedEventData>;
class Window : public Named, public IRenderWindow
{
public:
    static int GetClassIDStatic() { return ClassID(Window); }
    static Object* Produce() { return nullptr; }

    Window(const RenderWindowInitInfo& info);
    virtual ~Window();
    virtual UInt32 GetWidth() const override { return mInfo.width; }
    virtual UInt32 GetHeight() const override { return mInfo.height; }
#if CROSSENGINE_ANDROID
    virtual void ReCreateSurface(NativeWindow nativeWindow) override;
#endif
    virtual void Resize(UInt32 width, UInt32 height, UInt32 screenMode = 0) override;

    virtual bool IsForegroundWindow() const override;

    auto* GetRenderObject() const { return mRenderObject == nullptr ? nullptr : mRenderObject.get(); }

    auto GetRenderTexture() const { return mRenderTexture; }

    auto GetNativeHandle() const { return mInfo.handle; }

private:
    RenderWindowInitInfo mInfo;
    bool                 mIsDirty = false;

    std::unique_ptr<IWindowR> mRenderObject = {};
    RenderTextureResourcePtr mRenderTexture;
};


class ENGINE_API WindowSystemG : public GlobalGameSystemBase, public SystemEventManager<ResolutionChangedEvent>
{
public:
    CEFunction(Reflect)
    static const GlobalSystemDesc& GetDesc();
    CEFunction(Reflect)
    static WindowSystemG* CreateInstance();

    virtual void Release() override;

    virtual const GlobalSystemDesc& GetSystemDesc() const override
    {
        return GetDesc();
    }

    virtual void NotifyShutdownEngine() override;

    virtual void NotifyAddRenderSystemToRenderEngine() override
    {
        mIsRenderObjectOwner = false;
    }

    Window* CreateWindow(RenderWindowInitInfo const& info);

    void DestroyWindow(Window* renderWnd);

    Window* GetAppGlobalWindow()
    {
        return mAppWindow;
    }

protected:
    WindowSystemG();

    ~WindowSystemG();

    virtual GlobalRenderSystemBase* GetRenderSystem() override;

    Window* mAppWindow{nullptr};
    std::vector<std::unique_ptr<Window>> mWindows;
    WindowSystemR* mRenderObject{nullptr};
    bool mIsRenderObjectOwner{true};
};

}   // namespace cross