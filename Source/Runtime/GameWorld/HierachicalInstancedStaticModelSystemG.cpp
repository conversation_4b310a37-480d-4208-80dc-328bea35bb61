#include "EnginePrefix.h"
#include "CrossBase/Template/TypeCast.hpp"
#include "CrossBase/Math/CrossMath.h"
#include "Threading/RenderingThread.h"
#include "CECommon/Common/EngineGlobal.h"
#include "Resource/Resource.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "Runtime/GameWorld/HierachicalInstancedStaticModelSystemG.h"

namespace cross {
ecs::ComponentDesc* cross::HierachicalInstancedStaticModelComponentG::GetDesc()
{
    return EngineGlobal::GetECSFramework().CreateOrGetGameComponentDesc<cross::HierachicalInstancedStaticModelComponentG>({false, true, true, false},
                                                                                                                          &HierachicalInstancedStaticModelSystemG::SerializeHierachicalInstancedStaticModelComponent,
                                                                                                                          &HierachicalInstancedStaticModelSystemG::DeserializeHierachicalInstancedStaticModelComponent,
                                                                                                                          &HierachicalInstancedStaticModelSystemG::PostDeserializeHierachicalInstancedStaticModelComponent,
                                                                                                                          &HierachicalInstancedStaticModelSystemG::UpdateDeserializeHierachicalInstancedStaticModelComponent);
}

SerializeNode HierachicalInstancedStaticModelSystemG::SerializeHierachicalInstancedStaticModelComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr)
{
    SCOPED_CPU_TIMING(GroupSerialize, "Serialize HierachicalInstancedStaticModelComponent");

    auto comp = static_cast<HierachicalInstancedStaticModelComponentG*>(componentPtr);

    SerializeNode rootNode;
    SerializeContext context;

    rootNode = comp->Serialize(context);
    return rootNode;
}

void HierachicalInstancedStaticModelSystemG::DeserializeHierachicalInstancedStaticModelComponent(ISerializeWorld* serializeWorld, const DeserializeNode& rootNode, ecs::IComponent* componentPtr)
{
    SCOPED_CPU_TIMING(GroupSerialize, "Deserialize HierachicalInstancedStaticModelComponent");

    using namespace resource;

    if (rootNode.IsNull())
    {
        return;
    }

    SerializeContext context;
    auto comp = static_cast<HierachicalInstancedStaticModelComponentG*>(componentPtr);

    comp->Deserialize(rootNode, context);
}

void HierachicalInstancedStaticModelSystemG::PostDeserializeHierachicalInstancedStaticModelComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId)
{
    auto sys = gameWorld->GetGameSystem<HierachicalInstancedStaticModelSystemG>();
    auto hismComp = static_cast<HierachicalInstancedStaticModelComponentG*>(componentPtr);
    DispatchRenderingCommandWithToken([renderSystem = TYPE_CAST(HierachicalInstancedStaticModelSystemR*, sys->GetRenderSystem()),
                                      entityId,
                                      DensityLODCount = hismComp->mDensityLODCount,
                                      densityLodDistanceScalar = hismComp->mDensityLodDistanceScalar,
                                      maxInstancesPerLeaf = hismComp->mMaxInstancesPerLeaf,
                                      internalNodeBranchingFactor = hismComp->mInternalNodeBranchingFactor
                                      ]() {
                                      renderSystem->SetMaxInstancesPerLeaf(entityId, maxInstancesPerLeaf, false);
                                      renderSystem->SetInternalNodeBranchingFactor(entityId, internalNodeBranchingFactor, false);
                                      renderSystem->SetDensityLODCount(entityId, DensityLODCount, false);
                                      renderSystem->SetDensityLodDistanceScalar(entityId, densityLodDistanceScalar);
                                      });
}

void HierachicalInstancedStaticModelSystemG::UpdateDeserializeHierachicalInstancedStaticModelComponent(const DeserializeNode& rootNode, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId)
{
    Assert(false);
    LOG_ERROR("Not Implemented");
    if (rootNode.IsNull())
    {
        return;
    }
}

void HierachicalInstancedStaticModelSystemG::GetResourceHierachicalInstancedStaticModelComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr, ResourcePtr resource) {}

HierachicalInstancedStaticModelSystemG* HierachicalInstancedStaticModelSystemG::CreateInstance()
{
    return new HierachicalInstancedStaticModelSystemG();
}

HierachicalInstancedStaticModelSystemG::HierachicalInstancedStaticModelSystemG()
{
    mRenderSystem = HierachicalInstancedStaticModelSystemR::CreateInstance();
}

HierachicalInstancedStaticModelSystemG::~HierachicalInstancedStaticModelSystemG()
{
    if (mIsRenderObjectOwner)
    {
        mRenderSystem->Release();
    }
    mRenderSystem = nullptr;
}

void HierachicalInstancedStaticModelSystemG::Release()
{
    delete this;
}

RenderSystemBase* HierachicalInstancedStaticModelSystemG::GetRenderSystem()
{
    return mRenderSystem;
}

void HierachicalInstancedStaticModelSystemG::SetMaxInstancesPerLeaf(const HierachicalInstancedStaticModelComponentWriter& writer, int value, bool needRebuild)
{
    writer->mMaxInstancesPerLeaf = value;
    DispatchRenderingCommandWithToken([entity = writer.GetEntityID(), value, needRebuild, this]() { mRenderSystem->SetMaxInstancesPerLeaf(entity, value, needRebuild); });
}

void HierachicalInstancedStaticModelSystemG::SetInternalNodeBranchingFactor(const HierachicalInstancedStaticModelComponentWriter& writer, int value, bool needRebuild)
{
    writer->mInternalNodeBranchingFactor = value;
    DispatchRenderingCommandWithToken([entity = writer.GetEntityID(), value, needRebuild, this]() { mRenderSystem->SetInternalNodeBranchingFactor(entity, value, needRebuild); });
}

void HierachicalInstancedStaticModelSystemG::SetDensityLODCount(const HierachicalInstancedStaticModelComponentWriter& writer, int count, bool needRebuild)
{
    writer->mDensityLODCount = count;

    DispatchRenderingCommandWithToken([entity = writer.GetEntityID(), count, needRebuild, this]() {
        mRenderSystem->SetDensityLODCount(entity, count, needRebuild);
    });
}

void HierachicalInstancedStaticModelSystemG::SetDensityLodDistanceScalar(const HierachicalInstancedStaticModelComponentWriter& writer, float value)
{
    writer->mDensityLodDistanceScalar = value;

    DispatchRenderingCommandWithToken([entity = writer.GetEntityID(), value, this]() { mRenderSystem->SetDensityLodDistanceScalar(entity, value); });
}
}   // namespace cross
