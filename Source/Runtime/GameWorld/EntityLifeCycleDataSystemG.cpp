//#include "EnginePrefix.h"
//#include "CECommon/Common/EngineGlobal.h"
//#include "EntityLifeCycleDataSystemG.h"
//#include "GameWorld.h"
//#include "RenderEngine/EntityLifeCycleDataSystemR.h"
//
//namespace cross
//{
//RenderSystemBase* EntityLifeCycleDataSystemG::GetRenderSystem()
//{
//    return mRenderSystem;
//}
//
//void EntityLifeCycleDataSystemG::NotifyEvent(const SystemEventBase& event, UInt32& flag)
//{
//    GameSystemBase::NotifyEvent(event, flag);
//    if(event.mEventType == OnSystemAddToGameWorldEvent::sEventType)
//    {
//        mGameWorld->RegisterSingletonComponent<EntityLifeCycleDataComponentG>();
//    }
//}
//
//EntityLifeCycleDataSystemG::EntityLifeCycleDataSystemG()
//{
//    mRenderSystem = EntityLifeCycleDataSystemR::CreateInstance();
//}
//
//EntityLifeCycleDataSystemG::~EntityLifeCycleDataSystemG()
//{
//    if(mIsRenderObjectOwner && mRenderSystem)
//    {
//        mRenderSystem->Release();
//    }
//    mRenderSystem = nullptr;
//}
//
//ecs::ComponentDesc* EntityLifeCycleDataComponentG::GetDesc()
//{
//    return EngineGlobal::GetECSFramework().CreateOrGetGameComponentDesc<cross::EntityLifeCycleDataComponentG>(false, false, false);
//}
//
//SystemDesc& EntityLifeCycleDataSystemG::GetDesc()
//{
//    return EngineGlobal::GetCompSystemDescSystem()->CreateOrGetGameSystemDesc<EntityLifeCycleDataSystemG>();
//}
//
//EntityLifeCycleDataSystemG* EntityLifeCycleDataSystemG::CreateInstance()
//{
//    return new EntityLifeCycleDataSystemG();
//}
//
//void EntityLifeCycleDataSystemG::Release()
//{
//    delete this;
//}
//
//void EntityLifeCycleDataSystemG::OnBeginFrame(FrameParam* frameParam)
//{
//    GameSystemBase::OnBeginFrame(frameParam, elapsedTime);
//
//    auto writer = mGameWorld->GetSingletonComponent<EntityLifeCycleDataComponentG>().Write();
//    mImp.BeginFrame(writer->mData, frameParam);
//}
//
//void EntityLifeCycleDataSystemG::OnEndFrame(FrameParam* frameParam)
//{
//    GameSystemBase::OnEndFrame(frameParam, elapsedTime);
//
//    auto writer = mGameWorld->GetSingletonComponent<EntityLifeCycleDataComponentG>().Write();
//    mImp.EndFrame(writer->mData);
//}
//}
