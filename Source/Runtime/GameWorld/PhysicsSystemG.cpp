#include "EnginePrefix.h"
#include "Runtime/GameWorld/PhysicsSystemG.h"

#include "PhysicsEngine/PhysicsCooker.h"
#include "CECommon/Common/WorldConst.h"

#include "Runtime/GameWorld/GameWorld.h"
#include "CECommon/Common/EngineGlobal.h"
#include "Runtime/GameWorld/TransformSystemG.h"
#include "Runtime/GameWorld/ModelSystemG.h"
#include "Runtime/GameWorld/InstancedStaticModelSystemG.h"
#include "CECommon/Common/SettingsManager.h"
#include "CECommon/Common/FrameContainer.h"
#include "CECommon/Common/FrameParam.h"
#include "Runtime/GameWorld/PrimitiveRenderSystemG.h"
#include "Runtime/GameWorld/ScriptSystemG.h"
#include "Runtime/GameWorld/SkeletonSystemG.h"
#include "Runtime/GameWorld/TerrainSystemG.h"
#include "RenderEngine/PrimitiveGenerator.h"
#include "Runtime/GameWorld/PhysicsCustomData.h"
#include "Runtime/GameWorld/EntityMetaSystem.h"
#include "Runtime/Interface/CrossEngineImp.h"
#include "Runtime/GameWorld/CameraSystemG.h"
#include "CrossBase/TileBasedCoordinates.h"
#include "Runtime/GameWorld/ProceduralModelSystemG.h"

namespace cross {
ecs::ComponentDesc* PhysicsComponentG::GetDesc()
{
    return EngineGlobal::GetECSFramework().CreateOrGetGameComponentDesc<cross::PhysicsComponentG>(
        {false, true, true, false}, &PhysicsSystemG::SerializePhysicsComponent, &PhysicsSystemG::DeserializePhysicsComponent, &PhysicsSystemG::PostDeserializeComponent);
}

void PhysicsComponentG::AdditionalDeserialize(const DeserializeNode& inNode, SerializeContext& context)
{
    if (auto t_mCollisionType = inNode.HasMember("mCollisionChannel"); t_mCollisionType)
        mCollisionMask.SetValue(t_mCollisionType->AsUInt16());
}

PhysicsSystemG* PhysicsSystemG::CreateInstance()
{
    return new PhysicsSystemG();
}
PhysicsHitResultDebug PhysicsSystemG::RayCastForDebug(const TRSVector3Type& start, const TRSVector3Type& direction)
{
    PhysicsHitResultDebug result = {-1, TRSVector3Type::Zero(), "null", ""};

    PhysicsHitResult hitResult;
    UInt32 hitNum = SceneQuery()->RayCast(start, Float3(direction.Normalized()), 1e10, CollisionMask::All(), hitResult);
    if (hitNum > 0)
    {
        auto hitActorEntityID = hitResult.hitActor->GetCustomData<CustomPhyData>()->entityId;
        auto metaSys = mGameWorld->GetGameSystem<EntityMetaSystem>();
        auto metaComp = mGameWorld->GetComponent<ecs::EntityMetaComponentG>(hitActorEntityID);
        auto phyComp = mGameWorld->GetComponent<PhysicsComponentG>(hitActorEntityID);

        auto matType = GetMaterialType(phyComp.Read());
        auto wgs84 = TransformSystemG::CartesianCoordinateTransform_ToWGS84(start.x / 100.0, start.y / 100.0, start.z / 100.0);
        Double4x4 NEUMatrix = TransformSystemG::WGS84CoordinateTransform_ToNEU_Transform(wgs84.y, wgs84.x, 0);
        Double4x4 invNEUMatrix = NEUMatrix.Inverted();
        Double3 NEU_Up = {NEUMatrix.m10, NEUMatrix.m11, NEUMatrix.m12};
        Double3 worldTranslate = GetAbsolutePosition(hitResult.tile, hitResult.position);
        Double3 NEUPos = Double3::Transform(worldTranslate, invNEUMatrix) / 100.0;   // Unit meter
        double hog = NEUPos.y;

        result.distance = hitResult.distance;
        result.position = worldTranslate;
        result.entityName = metaSys->GetName(metaComp.Read());
        result.debugMsg = fmt::format("material:{}({}), hog: {:.2f}", MaterialTypeToString(matType), matType, hog);
    }
    return result;
}
PhysicsSystemG::PhysicsSystemG() {}

PhysicsSystemG::~PhysicsSystemG() {}

void PhysicsSystemG::Release()
{
    if (mScene)
    {
        PhysicsEngine* physicsEngine = EngineGlobal::GetPhysicsEngine();
        physicsEngine->ReleasePhysicsScene(mScene);
        mScene = nullptr;
    }
    delete this;
}

void PhysicsSystemG::NotifyEvent(const SystemEventBase& event, UInt32& flag)
{
    GameSystemBase::NotifyEvent(event, flag);

    if (event.mEventType == OnSystemAddToGameWorldEvent::sEventType || event.mEventType == GameWorldSystemChangedEvent::sEventType)
    {
        mGameWorld->SubscribeRemainedEvent<EntityDestroyEvent>(this, true);

        auto modelSys = mGameWorld->GetGameSystem<ModelSystemG>();
        if (modelSys)
        {
            modelSys->SubscribeEvent<ModelChangeEvent>(this, 0);
        }
        auto transformSys = mGameWorld->GetGameSystem<TransformSystemG>();
        if (transformSys)
        {
            transformSys->SubscribeEvent<TRSChangedEvent>(this, 0);
        }
        auto ismSys = mGameWorld->GetGameSystem<InstancedStaticModelSystemG>();
        if (ismSys)
		{
            ismSys->SubscribeEvent<InstanceDataChangeEvent>(this, 0);
		}
    }
    else if (event.mEventType == RemainedEventUpdatedEvent::sEventType)
    {
        const RemainedEventUpdatedEvent& e = static_cast<const RemainedEventUpdatedEvent&>(event);
        if (e.mData.mRemainedEventType == EntityDestroyEvent::sEventType)
        {
            for (UInt32 eventIndex = e.mData.mFirstIndex; eventIndex <= e.mData.mLastIndex; ++eventIndex)
            {
                const EntityDestroyEvent& entityLifeCycleEvent = mGameWorld->GetRemainedEvent<EntityDestroyEvent>(eventIndex);
                if (ecs::HasComponentMask<PhysicsComponentG>(entityLifeCycleEvent.mData.mChangedComponentMask))
                {
                    auto phyComp = mGameWorld->GetComponent<PhysicsComponentG>(entityLifeCycleEvent.mData.mEntityID);
                    if (phyComp.Read()->mRigidActor)
                    {
                        ResetActor(phyComp.Write());
                        // TODO(dango): Make sure that this is called on exclusive thread
                    }
                }
            }
        }
    }
    else if (event.mEventType == ModelChangeEvent::sEventType)
    {
        const ModelChangeEvent* modelEvent = TYPE_CAST(const ModelChangeEvent*, &event);
        auto phyComp = mGameWorld->GetComponent<PhysicsComponentG>(modelEvent->mData.mEntity);
        if (phyComp && modelEvent->mData.mModelIndex==0)
        {
            InitPhysics(phyComp.Write());
        }
    }
    else if (event.mEventType == TRSChangedEvent::sEventType)
    {
        const TRSChangedEvent* trsChangeEvent = TYPE_CAST(const TRSChangedEvent*, &event);
        auto phyComp = mGameWorld->GetComponent<PhysicsComponentG>(trsChangeEvent->mData.mEntity);
        if (phyComp)
        {
            OnTransformChanged(phyComp.Write(), *trsChangeEvent->mData.mUpdatedTRS);
        }
    }
    else if (event.mEventType == InstanceDataChangeEvent::sEventType)
    {
        const InstanceDataChangeEvent* ismEvent = TYPE_CAST(const InstanceDataChangeEvent*, &event);
        auto phyComp = mGameWorld->GetComponent<PhysicsComponentG>(ismEvent->mData.mEntity);
        if (phyComp)
        {
            InitPhysics(phyComp.Write());
        }
    }
    else if (event.mEventType == OnSystemEnableChangedEvent::sEventType)
    {
        if (mEnable)
        {
            // subscribe event...
            Assert(false);
        }
        else
        {
            auto modelSys = mGameWorld->GetGameSystem<ModelSystemG>();
            if (modelSys)
            {
                modelSys->Unsubscribe<ModelChangeEvent>(this);
            }

            auto ismSys = mGameWorld->GetGameSystem<InstancedStaticModelSystemG>();
            if (ismSys)
            {
                ismSys->Unsubscribe<InstanceDataChangeEvent>(this);
			}
        }
    }
}

void PhysicsSystemG::OnFirstUpdate(FrameParam* frameParam)
{
    PhysicsEngine* physicsEngine = EngineGlobal::GetPhysicsEngine();
    bool requireRWLock = false;
    bool validValue = EngineGlobal::GetSettingMgr()->GetValue("Physics.RequireRWLock", requireRWLock);

    mScene = physicsEngine->CreatePhysicsScene(validValue && requireRWLock);
    mScene->SetDebugViewOption(mDebugViewOpt);
    Assert(mScene);
    mSceneQuery = new PhysicsQuery(mScene);
    mControllerManager = new PhysicsControllerManager(mScene);
}

void PhysicsSystemG::OnBuildPreUpdateTasks(FrameParam* frameParam)
{
    auto preUpdate = CreateTaskFunction(FrameTickStage::PreUpdate, {}, [this, frameParam] {
        // dango phy TODO: Don't call FetchResult for the first frame
        SCOPED_CPU_TIMING(GroupPhysics, "PhysicsPreUpdate");

        if (mSimulated)
        {
            mSimulated = false;
            mScene->FetchResult();
        }

        if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() == cross::AppStartUpType::AppStartUpTypeStandAlone || mGameWorld->GetWorldType() == WorldTypeTag::PIEWorld)
        {
            auto transformSystem = mGameWorld->GetGameSystem<TransformSystemG>();
            FrameVector<SkeletonSystemG::SkeletonCompHandle>* skeltNeedToFinish = nullptr;
            mScene->TraverseActiveActors([this, frameParam, &skeltNeedToFinish, transformSystem](PhysicsActor* actor) {
                CustomPhyData* customData = actor->GetCustomData<CustomPhyData>();
                Assert(customData && customData->entityId);
                const Float3 tile = mScene->GetTile();
                mSyncingActorFromPhyWorldToCEWorld = actor;
                if (customData->boneHandle)   // this is skeleton physics
                {
                    auto skeletonSys = mGameWorld->GetGameSystem<SkeletonSystemG>();
                    auto skeletonComp = mGameWorld->GetComponent<SkeletonComponentG>(customData->entityId);
                    Assert(skeletonComp);

                    if (!skeltNeedToFinish)
                    {
                        skeltNeedToFinish = frameParam->GetFrameAllocator()->CreateFrameContainer<FrameVector<SkeletonSystemG::SkeletonCompHandle>>(FRAME_STAGE_GAME, 1);
                    }
                    skeltNeedToFinish->EmplaceBack(skeletonComp);

                    auto [pos, qua] = actor->GetTransform();
#if defined(CE_USE_DOUBLE_TRANSFORM)
                    //TODO(hydrochen):
                    //handle double transform for skeleton physics
                    auto absolutePos = Float3(GetAbsolutePosition(tile, pos));
#else
                    const auto& absolutePos = pos;
#endif
                    skeletonSys->BeforeSkeletonPhysicsActorFetch(skeletonComp.Write());
                    skeletonSys->OnSkeletonPhysicsActorFetch(skeletonComp.Write(), customData->boneHandle, absolutePos, qua);
                }
                else
                {
                    auto [pos, qua] = actor->GetTransform();
                    auto comp = mGameWorld->GetComponent<WorldTransformComponentG>(customData->entityId);
#if defined(CE_USE_DOUBLE_TRANSFORM)
                    auto absolutePos = GetAbsolutePosition(tile, pos);
                    transformSystem->SetWorldTranslationT(comp.Write(), absolutePos);
#else
                    transformSystem->SetWorldTranslation(comp.Write(), pos);
#endif
                    transformSystem->SetWorldRotation(comp.Write(), qua);
                }
                mSyncingActorFromPhyWorldToCEWorld = nullptr;
            });
            if (skeltNeedToFinish)
            {
                auto skeletonSys = mGameWorld->GetGameSystem<SkeletonSystemG>();
                for (SkeletonSystemG::SkeletonCompHandle& comp : *skeltNeedToFinish)
                {
                    skeletonSys->FinishSkeletonPhysicsActorFetch(comp.Write());
                }
            }
        }
    });

    CreateTaskFunction<threading::ThreadID::GameThreadLocal>(FrameTickStage::PreUpdate, {preUpdate}, [this] {
        SCOPED_CPU_TIMING(GroupPhysics, "Physics_Script_OnCollision");
        mScene->BeforeUpdate();
    });
}

void PhysicsSystemG::OnBuildPostUpdateTasks(FrameParam* frameParam)
{
    auto taskDrawDebug = CreateTaskFunction(FrameTickStage::PostUpdate, {}, [this, frameParam]() mutable {

#ifdef CROSSENGINE_RELEASE
        // no need in release
        if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() != AppStartUpType::AppStartUpTypeStandAlone)
#endif
        {
            SCOPED_CPU_TIMING(GroupPhysics, "DrawDebugVisualization");
            DrawDebugVisualization(frameParam->GetFrameAllocator(), mScene->GetTile());
        }
    });

    auto taskUpdatePose = CreateTaskFunction<threading::ThreadID::GameThreadLocal>(FrameTickStage::PostUpdate, {}, [this, frameParam] {
#if defined(CE_USE_DOUBLE_TRANSFORM)
        SCOPED_CPU_TIMING(GroupPhysics, "UpdateActorGlobalPostion");

        auto cameraSys = mGameWorld->GetGameSystem<CameraSystemG>();
        auto transformSystem = mGameWorld->GetGameSystem<TransformSystemG>();
        auto mainCamera = cameraSys->GetMainCamera();

        const Float3 preTile = mScene->GetTile();
        Float3 curTile = preTile;
        if (mainCamera)
        {
            curTile = transformSystem->GetWorldTranslationTile(mGameWorld->GetComponent<WorldTransformComponentG>(mainCamera).Read());
        }

        auto delta = (curTile - preTile).Abs();
        if (std::max(delta.x, std::max(delta.y, delta.z)) > 0.5)
        {
            mScene->SetTile(curTile);
            //PhysicsSceneWriteLockGurad writeLockGurad(mScene);
            mScene->TraverseAllActors([this, transformSystem, tile = curTile](PhysicsActor* actor) {
                CustomPhyData* customData = actor->GetCustomData<CustomPhyData>();
                if (!mGameWorld->IsEntityAlive(customData->entityId))
                    return;
                auto comp = mGameWorld->GetComponent<WorldTransformComponentG>(customData->entityId);
                auto pos = transformSystem->GetWorldTranslationT(comp.Read());

                auto [t_pos, rot] = actor->GetTransform();
                Float3 relativePos = {
                    static_cast<float>(GetRelativePosition(pos.x, tile.x)),
                    static_cast<float>(GetRelativePosition(pos.y, tile.y)),
                    static_cast<float>(GetRelativePosition(pos.z, tile.z)),
                };
                actor->SetTransformAssumeSceneLocked(relativePos, rot);
            });
        }
        // auto primitiveRenderSys = mGameWorld->GetGameSystem<cross::PrimitiveRenderSystemG>();
        // primitiveRenderSys->LogScreen(fmt::format("Physx tile: {:.3f}, {:.3f}, {:.3f}", mTile.x, mTile.y, mTile.z));
#endif
    });

    CreateTaskFunction(FrameTickStage::PostUpdate, {taskUpdatePose, taskDrawDebug}, [this, frameParam]() mutable {
        SCOPED_CPU_TIMING(GroupPhysics, "PhysicsPostUpdate");
        StepPhysicsScene(frameParam->GetDeltaTime());
    });
}

void PhysicsSystemG::OnEndFrame(FrameParam* frameParam)
{
    Assert(true);
}

void PhysicsSystemG::NotifyAddRenderSystemToRenderWorld() {}

PhysicsShape* PhysicsSystemG::AddTerrainCollision(const PhysicsComponentWriter& phyComp, const PhysicsGeometryTerrain& collision) {
    if (phyComp->mRigidActor)
    {
        PhysicsEngine* physicsEngine = EngineGlobal::GetPhysicsEngine();
        return phyComp->mRigidActor->AddTerrainShape(collision, physicsEngine->GetDefaultMaterial());
    }
    return nullptr;
}
void PhysicsSystemG::RemoveShape(const PhysicsComponentWriter& phyComp, PhysicsShape* shape) {
    if (phyComp->mRigidActor)
    {
        phyComp->mRigidActor->ClearShape(shape);
    }
}
void PhysicsSystemG::OnShapeChanged(const PhysicsComponentWriter& phyComp)
{
    if (phyComp->mRigidActor)
    {
        auto [metaComp, transComp, modelComp, ismComp] = mGameWorld->GetComponent<ecs::EntityMetaComponentG, WorldTransformComponentG, ModelComponentG, InstancedStaticModelComponentG>(phyComp.GetEntityID());

        Assert(metaComp && transComp);
        auto transformSys = mGameWorld->GetGameSystem<TransformSystemG>();

        auto phySys = mGameWorld->GetGameSystem<PhysicsSystemG>();
        auto metaSys = mGameWorld->GetGameSystem<EntityMetaSystem>();
        PhysicsEngine* physicsEngine = EngineGlobal::GetPhysicsEngine();

        if (!phyComp->mCollision)
            phyComp->mCollision = std::make_unique<PhysicsCollision>();

        phyComp->mRigidActor->ClearAllShapes();
        phyComp->mCollision->Clear();

        Float3A worldScale = transformSys->GetWorldScale(transComp.Read());
        phyComp->mRigidActor->SetScale(worldScale);

        static PhysicsMaterial* material = physicsEngine->CreateMaterial(0.7f, 0.7f, 0.3f);
        // dango phy TODO: use editor to config physics material

        bool needAddCollisonShape = phyComp->mCollisionType != CollisionType::NoCollision;
       
        if (needAddCollisonShape && modelComp)
        {
            auto modelSys = mGameWorld->GetGameSystem<ModelSystemG>();

            for (unsigned modelIndex = 0; modelIndex < modelSys->GetModelCount(modelComp.Read()); modelIndex++)
            {
                auto meshAssetData = modelSys->GetModelAsset(modelComp.Read(), modelIndex);

                if (phyComp->mUseMeshCollision)
                {
                    if (phyComp->mIsDynamic)
                    {
                        Assert(false);
                        LOG_ERROR("Dynamic actor [{}] can't use mesh collision!", metaSys->GetName(metaComp.Read()).c_str());
                    }
                    else
                    {
                        PhysicsCollision* phyCollision = meshAssetData->GetAssetData()->GetPhysicsCollision();
                        if (phyCollision->mMeshGeometry.size() > 0)
                        {
                            for (auto& mesh : phyCollision->mMeshGeometry)
                            {
                                phyComp->mRigidActor->AddTriangleMeshShape(mesh, material);
                                phyComp->mCollision->mMeshGeometry.push_back(mesh);
                            }
                                
                        }
                    }
                }
                else
                {
                    PhysicsCollision* phyCollision = meshAssetData->GetAssetData()->GetPhysicsCollision();
                    for (auto& box : phyCollision->mBoxGeometry)
                    {
                        phyComp->mRigidActor->AddBoxShape(box, material);
                        phyComp->mCollision->mBoxGeometry.push_back(box);
                    }
                    for (auto& sphere : phyCollision->mSphereGeometry)
                    {
                        phyComp->mRigidActor->AddSphereShape(sphere, material);
                        phyComp->mCollision->mSphereGeometry.push_back(sphere);
                    }
                    for (auto& capsule : phyCollision->mCapsuleGeometry)
                    {
                        phyComp->mRigidActor->AddCapsuleShape(capsule, material);
                        phyComp->mCollision->mCapsuleGeometry.push_back(capsule);

                    }
                    for (auto& convex : phyCollision->mConvexGeometry)
                    {
                        phyComp->mRigidActor->AddConvexMeshShape(convex, material);
                        phyComp->mCollision->mConvexGeometry.push_back(convex);
                    }
                }
            }
        }
        else if (needAddCollisonShape && ismComp)
        {
            if (phyComp->mIsDynamic||phyComp->mUseMeshCollision)
			{
				Assert(false);
				LOG_ERROR("instanced static model can't be dynamic or use mesh collision!");
            }
            else
            {
                auto ismReader = ismComp.Read();
                MeshAssetDataResourcePtr meshAsset = ismReader->mAsset;
                InstanceDataResourcePtr dataAsset = ismReader->mInstanceDataResource;
                if (meshAsset && dataAsset)
                {
                    float globalScale = ismReader->mGlobalScale;
                    auto* collisions = meshAsset->GetAssetData()->GetPhysicsCollision();
                    UInt32 instanceCount = dataAsset->mInstanceCount;


                    const UInt8* tranlationDataPtr = dataAsset->GetInstanceMemberData("Translation").mData.data();
                    const UInt32 tranlationDataStride = sizeof(Float3);

                    const UInt8* rotationDataPtr = dataAsset->GetInstanceMemberData("Rotation").mData.data();
                    const UInt32 rotationDataStride = sizeof(Float3);

                    const UInt8* scaleDataPtr = dataAsset->GetInstanceMemberData("Scale").mData.data();
                    const UInt32 scaleDataStride = sizeof(Float3);

                    for (UInt32 instanceIndex = 0; instanceIndex < instanceCount; instanceIndex++)
                    {
                        Float3 translation(reinterpret_cast<const float*>(tranlationDataPtr));
                        tranlationDataPtr += tranlationDataStride;

                        Float3 rotationEular(reinterpret_cast<const float*>(rotationDataPtr));
                        rotationDataPtr += rotationDataStride;
                        Quaternion rotate = Quaternion::EulerToQuaternion(rotationEular);

                        Float3 scale(reinterpret_cast<const float*>(scaleDataPtr));
                        scaleDataPtr += scaleDataStride;
                        scale *= globalScale;
                        for (PhysicsGeometryBox box : collisions->mBoxGeometry)
                        {
                            box.rotate *= rotate;
                            box.position = translation + box.rotate.Float3Rotate(box.position*scale);
                            box.halfExtents *= scale;
                            phyComp->mRigidActor->AddBoxShape(box, material);
                            // it seems no need to save collision in physics component for ism
                            // phyComp->mCollision->mBoxGeometry.push_back(box);
                        }
                        for (PhysicsGeometrySphere sphere : collisions->mSphereGeometry)
                        {
                            sphere.position = translation + rotate.Float3Rotate(sphere.position * scale);
                            sphere.radius *= MathUtils::Max(scale.x, MathUtils::Max(scale.y, scale.z));

                            phyComp->mRigidActor->AddSphereShape(sphere, material);
                        }
                        //TODO(hydrochen): how to handle capsule and convex?
                    }
                }

            }
        }

        if (needAddCollisonShape && phyComp->mExtraCollision)
        {
            for (auto& box : phyComp->mExtraCollision->mBoxGeometry)
            {
                phyComp->mRigidActor->AddBoxShape(box, material);
                phyComp->mCollision->mBoxGeometry.push_back(box);

            }
            for (auto& sphere : phyComp->mExtraCollision->mSphereGeometry)
            {
                phyComp->mRigidActor->AddSphereShape(sphere, material);
                phyComp->mCollision->mSphereGeometry.push_back(sphere);

            }
            for (auto& capsule : phyComp->mExtraCollision->mCapsuleGeometry)
            {
                phyComp->mRigidActor->AddCapsuleShape(capsule, material);
                phyComp->mCollision->mCapsuleGeometry.push_back(capsule);
            }
            for (auto& convex : phyComp->mExtraCollision->mConvexGeometry)
            {
                phyComp->mRigidActor->AddConvexMeshShape(convex, material);
                phyComp->mCollision->mConvexGeometry.push_back(convex);
            }
        }
        //Must apply mass after add shapes to actor;
        ApplyMass(phyComp);
    }
}

void PhysicsSystemG::OnTransformChanged(const PhysicsComponentWriter& phyComp, const TRS_A& transform)
{
    static auto QuatNearEquals = [](const Quaternion& q1, const Quaternion& q2, float tolerance = 1.e-4f) {
        return (std::fabs(q1.x - q2.x) <= tolerance && std::fabs(q1.y - q2.y) <= tolerance && std::fabs(q1.z - q2.z) <= tolerance && std::fabs(q1.w - q2.w) <= tolerance) ||
               (std::fabs(q1.x + q2.x) <= tolerance && std::fabs(q1.y + q2.y) <= tolerance && std::fabs(q1.z + q2.z) <= tolerance && std::fabs(q1.w + q2.w) <= tolerance);
    };
    static auto Float3NearEquals = [](const Float3& v1, const Float3& v2, float tolerance = 1.e-4f) { return std::fabs(v1.x - v2.x) <= tolerance && std::fabs(v1.y - v2.y) <= tolerance && std::fabs(v1.z - v2.z) <= tolerance; };

    if (PhysicsActor* actor = phyComp->mRigidActor; actor)
    {
        auto translation = Float3(GetRelativePosition(transform.mTranslation, mScene->GetTile()));
        auto rotation = Quaternion(transform.mRotation);
        auto scale = Float3(transform.mScale);

        if (mSyncingActorFromPhyWorldToCEWorld == actor)
        {
            auto [pos, rot] = actor->GetTransform();
            if (Float3NearEquals(pos, translation) && QuatNearEquals(rot, rotation))
            {
                return;   // when I'm syncing from physics world to CE world, I don't want to awake actor if transform is not changed.
            }
        }
        actor->SetScale(scale);
        bool isEditor = EngineGlobal::GetSettingMgr()->GetAppStartUpType() == cross::AppStartUpType::AppStartUpTypeCrossEditor && mGameWorld->GetWorldType() != WorldTypeTag::PIEWorld;
        if ((actor->GetIsKinematic() /*|| actor->GetIsTrigger()*/) && !isEditor)
        {
            actor->SetKinematicTarget(translation, rotation);
        }
        else
        {
            actor->SetTransform(translation, rotation);   // dango phy TODO: apply scale
        }
    }
}

void PhysicsSystemG::AddPhysicsActorToPhysicsScene(PhysicsActor* actor)
{
    mScene->AddActor(actor);
}

void PhysicsSystemG::RemovePhysicsActorFromPhysicsScene(PhysicsActor* actor)
{
    mScene->RemoveActor(actor);
}

void PhysicsSystemG::SetDebugViewOption(const PhysicsSceneDebugViewOption& option)
{
    mDebugViewOpt = option;
    if (mScene)
    {
        mScene->SetDebugViewOption(option);
    }
}

PhysicsComponentG PhysicsSystemG::CreatePhysicsComp(CollisionType type, MaterialType materialType)
{
    PhysicsComponentG comp;
    comp.mCollisionType = type;
    comp.mMaterialType = materialType;
    return comp;
}

PhysicsSimpleCollision* PhysicsSystemG::GetPhysicsSimpleCollision(const PhysicsComponentReader& component)
{
    return component->mExtraCollision.get();
}
void PhysicsSystemG::ResetActor(const PhysicsComponentWriter& phyComp)
{
    if (phyComp->mRigidActor != nullptr)
    {
        PhysicsEngine* physicsEngine = EngineGlobal::GetPhysicsEngine();
        mScene->RemoveActor(phyComp->mRigidActor);
        physicsEngine->ReleasePhysicsActor(phyComp->mRigidActor);
        phyComp->mCollision->Clear();
        phyComp->mRigidActor = nullptr;
    }
}
void PhysicsSystemG::InitPhysics(const PhysicsComponentWriter& phyComp)
{
    if (phyComp->mEnable && phyComp->mRigidActor == nullptr)
    {
        PhysicsEngine* physicsEngine = EngineGlobal::GetPhysicsEngine();

        auto [metaComp, transComp] = mGameWorld->GetComponent<ecs::EntityMetaComponentG, WorldTransformComponentG>(phyComp.GetEntityID());

        Assert(transComp);
        auto transformSys = mGameWorld->GetGameSystem<TransformSystemG>();
        auto metaSys = mGameWorld->GetGameSystem<EntityMetaSystem>();
        TRSVector3Type absolutePos = transformSys->GetWorldTranslationT(transComp.Read());
        Float3 relativePos = Float3(GetRelativePosition(absolutePos, mScene->GetTile()));
        QuaternionA rota = transformSys->GetWorldRotation(transComp.Read());

        phyComp->mRigidActor = phyComp->mIsDynamic ? physicsEngine->CreateRigidDynamic(relativePos, rota) : physicsEngine->CreateRigidStatic(relativePos, rota);
        phyComp->mRigidActor->SetCustomData(std::make_unique<CustomPhyData>(phyComp.GetEntityID()));
        phyComp->mRigidActor->SetDebugName(metaSys->GetName(metaComp.Read()).c_str());
        phyComp->mRigidActor->SetCollisionType(phyComp->mCollisionType);
        phyComp->mRigidActor->SetCollisionMask(phyComp->mCollisionMask);

        if (phyComp->mIsDynamic)
        {
            bool isEditor = EngineGlobal::GetSettingMgr()->GetAppStartUpType() == cross::AppStartUpType::AppStartUpTypeCrossEditor && mGameWorld->GetWorldType() != WorldTypeTag::PIEWorld;

            phyComp->mRigidActor->SetEnableGravity(phyComp->mEnableGravity);
            phyComp->mRigidActor->SetLinearDamping(phyComp->mLinearDamping);
            phyComp->mRigidActor->SetMaxDepenetrationVelocity(phyComp->mMaxDepenetrationVelocity > 0 ? phyComp->mMaxDepenetrationVelocity : FLT_MAX);
            phyComp->mRigidActor->SetIsKinematic(phyComp->mIsKinematic || isEditor);
        }

        phyComp->mRigidActor->SetIsTrigger(phyComp->mIsTrigger);

        if (mScene)
            mScene->AddActor(phyComp->mRigidActor);

        if (phyComp->mIsDynamic)
        {
            if (phyComp->mStartAsleep)
            {
                //It is invalid to use this method if the actor has not been added to a scene
                phyComp->mRigidActor->PutToSleep();
            }
        }

        RigidCreatedEvent e;
        e.mData.mEntity = phyComp.GetEntityID();
        e.mData.mNewRigidActor = phyComp->mRigidActor;
        DispatchImmediateEvent(e);
    }
    OnShapeChanged(phyComp);
}

UInt32 PhysicsSystemG::RayCast(const TRSVector3Type& origin, const Float3& unitDir, float maxDistance, CollisionMask mask, HitFlag flag, UInt32 maxHit, const PhysicsActor* self, PhysicsHitResult* outResults /*= nullptr*/)
{
    return mScene->RayCast(origin, unitDir, maxDistance, mask, flag, maxHit, self,outResults);
}

UInt32 PhysicsSystemG::Sweep(PhysicsGeometryBase* geometry, const TRSVector3Type& position, const TRSQuaternionType& rotation, const TRSVector3Type& scale, const Float3& unitDir, float maxDistance, CollisionMask mask, HitFlag flag,
                             UInt32 maxHit,
                             const PhysicsActor* self,
                             PhysicsHitResult* outResults)
{
    return mScene->Sweep(geometry, position, rotation, scale, unitDir, maxDistance, mask, flag, maxHit, self,outResults);
}

UInt32 PhysicsSystemG::Overlap(PhysicsGeometryBase* geometry, const TRSVector3Type& position, const TRSQuaternionType& rotation, const TRSVector3Type& scale, CollisionMask mask, UInt32 maxHit, const PhysicsActor* self,
                               PhysicsHitResult* outResults)
{
    return mScene->Overlap(geometry, position, rotation, scale, mask, HitFlag::Default, maxHit, self, outResults);
}

void PhysicsSystemG::SetCollisionEventEnable(const PhysicsComponentWriter& component, bool enable, bool reportContactPoints)
{
    component->mEnableCollisionEvent = enable;
    RegisterCollisionCallback(component);
}

bool PhysicsSystemG::GetCollisionEventEnable(const PhysicsComponentReader& component)
{
    return component->mEnableCollisionEvent;
}

bool PhysicsSystemG::HasCollisionCallback(const PhysicsComponentReader& component, CollisionEventType eventType) const
{
    switch (eventType)
    {
    case CollisionEventType::OnCollisionEnter:
        return component->mCollisionEnterCBs.size() > 0;
    case CollisionEventType::OnCollisionExit:
        return component->mCollisionExitCBs.size() > 0;
    case CollisionEventType::OnTriggerEnter:
        return component->mTriggerEnterCBs.size() > 0;
    case CollisionEventType::OnTriggerExit:
        return component->mTriggerExitCBs.size() > 0;
    default:
        return false;
    }
}
void PhysicsSystemG::RegisterCollisionCallback(const PhysicsComponentWriter& component) {
    ecs::EntityID entity = component.GetEntityID();
    //if not collision event enabled, just set null;
    if (!component->mEnableCollisionEvent)
    {
        component->mRigidActor->SetCollisionEnter(nullptr);
        component->mRigidActor->SetCollisionStay(nullptr);
        component->mRigidActor->SetCollisionExit(nullptr);
        return;
    }
    //set trigger event or collision event
    if (component->mIsTrigger)
    {
        component->mRigidActor->SetCollisionEnter([entity, this](std::shared_ptr<CollisionInfo>&& info) {
            auto reader = mGameWorld->GetComponent<PhysicsComponentG>(entity).Read();
            this->TriggerEnterEvent(reader, info->other->GetCustomData<CustomPhyData>()->entityId, *info.get());
        });
        component->mRigidActor->SetCollisionStay(nullptr);
        component->mRigidActor->SetCollisionExit([entity, this](std::shared_ptr<CollisionInfo>&& info) {
            auto reader = mGameWorld->GetComponent<PhysicsComponentG>(entity).Read();
            this->TriggerExitEvent(reader, info->other->GetCustomData<CustomPhyData>()->entityId, *info.get());
        });
    }
    else
    {
        component->mRigidActor->SetCollisionEnter([entity, this](std::shared_ptr<CollisionInfo>&& info) {
            auto reader = mGameWorld->GetComponent<PhysicsComponentG>(entity).Read();
            this->CollisionEnterEvent(reader, info->other->GetCustomData<CustomPhyData>()->entityId, *info.get());
        });
        component->mRigidActor->SetCollisionStay([entity, this](std::shared_ptr<CollisionInfo>&& info) {
            auto reader = mGameWorld->GetComponent<PhysicsComponentG>(entity).Read();
            this->CollisionStayEvent(reader, info->other->GetCustomData<CustomPhyData>()->entityId, *info.get());
        });
        component->mRigidActor->SetCollisionExit([entity, this](std::shared_ptr<CollisionInfo>&& info) {
            auto reader = mGameWorld->GetComponent<PhysicsComponentG>(entity).Read();
            this->CollisionExitEvent(reader, info->other->GetCustomData<CustomPhyData>()->entityId, *info.get());
        });
    }
}
void PhysicsSystemG::RegisterCollisionEnterCallback(const PhysicsComponentWriter& component, CollisionCallback collisionEnterCallback)
{
    component->mCollisionEnterCBs.push_back(std::move(collisionEnterCallback));
}

void PhysicsSystemG::CollisionEnterEvent(const PhysicsComponentReader& component, ecs::EntityID collidedEntityID, const CollisionInfo& collisionInfo)
{
    if (component->mEnableCollisionEvent)
        for (auto& cb : component->mCollisionEnterCBs)
        {
            cb(collidedEntityID);
        }
}
void PhysicsSystemG::RegisterCollisionStayCallback(const PhysicsComponentWriter& component, CollisionCallback collisionStayCallback)
{
    component->mCollisionStayCBs.push_back(std::move(collisionStayCallback));
}

void PhysicsSystemG::CollisionStayEvent(const PhysicsComponentReader& component, ecs::EntityID collidedEntityID, const CollisionInfo& collisionInfo)
{
    if (component->mEnableCollisionEvent)
        for (auto& cb : component->mCollisionStayCBs)
        {
            cb(collidedEntityID);
        }
}
void PhysicsSystemG::RegisterCollisionExitCallback(const PhysicsComponentWriter& component, CollisionCallback collisionExitCallback)
{
    component->mCollisionExitCBs.push_back(std::move(collisionExitCallback));
}

void PhysicsSystemG::CollisionExitEvent(const PhysicsComponentReader& component, ecs::EntityID collidedEntityID, const CollisionInfo& collisionInfo)
{
    if (component->mEnableCollisionEvent)
        for (auto& cb : component->mCollisionExitCBs)
        {
            cb(collidedEntityID);
        }
}

void PhysicsSystemG::RegisterTriggerEnterCallback(const PhysicsComponentWriter& component, CollisionCallback triggerEnterCallback)
{
    component->mTriggerEnterCBs.push_back(std::move(triggerEnterCallback));
}

void PhysicsSystemG::TriggerEnterEvent(const PhysicsComponentReader& component, ecs::EntityID collidedEntityID, const CollisionInfo& collisionInfo)
{
    if (component->mEnableCollisionEvent)
        for (auto& cb : component->mTriggerEnterCBs)
        {
            cb(collidedEntityID);
        }
}

void PhysicsSystemG::RegisterTriggerExitCallback(const PhysicsComponentWriter& component, CollisionCallback triggerExitCallback)
{
    component->mTriggerExitCBs.push_back(std::move(triggerExitCallback));
}

void PhysicsSystemG::TriggerExitEvent(const PhysicsComponentReader& component, ecs::EntityID collidedEntityID, const CollisionInfo& collisionInfo)
{
    if (component->mEnableCollisionEvent)
        for (auto& cb : component->mTriggerExitCBs)
        {
            cb(collidedEntityID);
        }
}

void PhysicsSystemG::AddForce(const PhysicsComponentWriter& component, const Float3A& force)
{
    component->mRigidActor->AddForce(force);
}

void PhysicsSystemG::ClearForce(const PhysicsComponentWriter& component)
{
    component->mRigidActor->ClearForce();
}

void PhysicsSystemG::AddImpulse(const PhysicsComponentWriter& component, const Float3A& impulse)
{
    component->mRigidActor->AddImpulse(impulse);
}

void PhysicsSystemG::ClearImpulse(const PhysicsComponentWriter& component)
{
    component->mRigidActor->ClearImpulse();
}

void PhysicsSystemG::AddLinearVelocity(const PhysicsComponentWriter& component, const Float3A& velocity)
{
    component->mRigidActor->AddLinearVelocity(velocity);
}

void PhysicsSystemG::ClearLinearVelocity(const PhysicsComponentWriter& component)
{
    component->mRigidActor->ClearLinearVelocity();
}

void PhysicsSystemG::AddLinearAcceleration(const PhysicsComponentWriter& component, const Float3A& acceleration)
{
    component->mRigidActor->AddLinearAcceleration(acceleration);
}

void PhysicsSystemG::ClearLinearAcceleration(const PhysicsComponentWriter& component)
{
    component->mRigidActor->ClearLinearAcceleration();
}

void PhysicsSystemG::AddTorque(const PhysicsComponentWriter& component, const Float3& torque)
{
    component->mRigidActor->AddTorque(torque);
}
void PhysicsSystemG::AddImpulseTorque(const PhysicsComponentWriter& component, const Float3& impulse)
{
    component->mRigidActor->AddImpulseTorque(impulse);
}
void PhysicsSystemG::AddAngularVelocity(const PhysicsComponentWriter& component, const Float3& velocity)
{
    component->mRigidActor->AddAngularVelocity(velocity);
}
void PhysicsSystemG::AddAngularAcceleration(const PhysicsComponentWriter& component, const Float3& acceleration)
{
    component->mRigidActor->AddAngularAcceleration(acceleration);
}
void PhysicsSystemG::ClearTorque(const PhysicsComponentWriter& component)
{
    component->mRigidActor->ClearTorque();
}
void PhysicsSystemG::ClearImpulseTorque(const PhysicsComponentWriter& component)
{
    component->mRigidActor->ClearImpulseTorque();
}
void PhysicsSystemG::ClearAngularVelocity(const PhysicsComponentWriter& component)
{
    component->mRigidActor->ClearAngularVelocity();
}
void PhysicsSystemG::ClearAngularAcceleration(const PhysicsComponentWriter& component)
{
    component->mRigidActor->ClearAngularAcceleration();
}

Float3 PhysicsSystemG::GetAngularVelocity(const PhysicsComponentReader& component) const
{
    return component->mRigidActor->GetAngularVelocity();
}

Float3 PhysicsSystemG::GetLinearVelocity(const PhysicsComponentReader& component) const
{
    return component->mRigidActor->GetLinearVelocity();
}

const PhysicsCollision* PhysicsSystemG::GetPhysicsGeometry(const PhysicsComponentReader& comp) const
{
    return comp->mCollision.get();
}

const PhysicsSimpleCollision* PhysicsSystemG::GetExtraShape(const PhysicsComponentReader& comp) const { return comp->mExtraCollision ? comp->mExtraCollision.get() : nullptr; }

PhysicsSimpleCollision* PhysicsSystemG::GetExtraShape(const PhysicsComponentReader& comp) { return comp->mExtraCollision ? comp->mExtraCollision.get() : nullptr; }

void PhysicsSystemG::AddExtraBoxShape(const PhysicsComponentWriter& comp, const PhysicsGeometryBox& boxGeo)
{
    if (!comp->mExtraCollision)
        comp->mExtraCollision = std::make_unique<PhysicsSimpleCollision>();
    comp->mExtraCollision->mBoxGeometry.emplace_back(boxGeo);
    auto phySys = mGameWorld->GetGameSystem<PhysicsSystemG>();
    phySys->OnShapeChanged(comp);
}

void PhysicsSystemG::AddExtraSphereShape(const PhysicsComponentWriter& comp, const PhysicsGeometrySphere& sphereGeo)
{
    if (!comp->mExtraCollision)
        comp->mExtraCollision = std::make_unique<PhysicsSimpleCollision>();
    comp->mExtraCollision->mSphereGeometry.emplace_back(sphereGeo);
    auto phySys = mGameWorld->GetGameSystem<PhysicsSystemG>();
    phySys->OnShapeChanged(comp);
}

void PhysicsSystemG::AddExtraCapsuleShape(const PhysicsComponentWriter& comp, const PhysicsGeometryCapsule& capsuleGeo)
{
    if (!comp->mExtraCollision)
        comp->mExtraCollision = std::make_unique<PhysicsSimpleCollision>();
    comp->mExtraCollision->mCapsuleGeometry.emplace_back(capsuleGeo);
    auto phySys = mGameWorld->GetGameSystem<PhysicsSystemG>();
    phySys->OnShapeChanged(comp);
}


void PhysicsSystemG::AddExtraConvexShape(const PhysicsComponentWriter& comp, const PhysicsGeometryConvex& convexGeo)
{
    if (!comp->mExtraCollision)
        comp->mExtraCollision = std::make_unique<PhysicsSimpleCollision>();
    comp->mExtraCollision->mConvexGeometry.emplace_back(convexGeo);
    auto phySys = mGameWorld->GetGameSystem<PhysicsSystemG>();
    phySys->OnShapeChanged(comp);
}

float PhysicsSystemG::UpdateMassAndInertia(const PhysicsComponentWriter& comp, float density)
{
    Assert(comp->mRigidActor);
    comp->mRigidActor->UpdateMassAndInertia(density);
    comp->mMass = comp->mRigidActor->GetMass();
    ApplyMass(comp);
    return comp->mMass;
}

float PhysicsSystemG::SetMassAndUpdateInertia(const PhysicsComponentWriter& comp, float mass)
{
    Assert(comp->mRigidActor);
    comp->mMass = comp->mRigidActor->GetMass();
    ApplyMass(comp);
    return comp->mMass;
}

SerializeNode PhysicsSystemG::SerializePhysicsComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr)
{
    auto comp = static_cast<PhysicsComponentG*>(componentPtr);
    SerializeContext context;
    SerializeNode json;
    json = comp->Serialize(context);
    return json;
}

void PhysicsSystemG::DeserializePhysicsComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr)
{
    if (json.IsNull())
    {
        return;
    }
    auto cmpntPtr = static_cast<PhysicsComponentG*>(componentPtr);
    SerializeContext context;
    cmpntPtr->Deserialize(json, context);
}

void PhysicsSystemG::PostDeserializeComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId)
{
    auto phyComp = gameWorld->GetComponent<PhysicsComponentG>(entityId);
    auto phySys = gameWorld->GetGameSystem<PhysicsSystemG>();
    if (phyComp.Read()->mRigidActor==nullptr)
        phySys->InitPhysics(phyComp.Write());
}

void PhysicsSystemG::SetMass(const PhysicsComponentWriter& comp, float mass)
{
    Assert(!comp->mRigidActor || !comp->mRigidActor->IsDynamicActor() || (comp->mMass == 0 || comp->mMass == comp->mRigidActor->GetMass()));
    comp->mMass = mass;
    if (comp->mRigidActor && comp->mRigidActor->IsDynamicActor())
        comp->mRigidActor->SetMass(mass);
}

void PhysicsSystemG::SetEnable(const PhysicsComponentWriter& comp, bool enable)
{
    if (comp->mEnable != enable)
    {
        comp->mEnable = enable;
        ResetActor(comp);
        InitPhysics(comp);
    }
}

void PhysicsSystemG::SetIsDynamic(const PhysicsComponentWriter& comp, bool enable)
{
    if (mGameWorld->GetWorldType() == WorldTypeTag::SDKWorld)
    {
        comp->mIsDynamic = enable;
    }
    else if (comp->mRigidActor && (comp->mRigidActor->IsDynamicActor() ^ enable))
    {
        comp->mIsDynamic = enable;
        ResetActor(comp);
        InitPhysics(comp);
    }
}

void PhysicsSystemG::SetEnableGravity(const PhysicsComponentWriter& comp, bool enable)
{
    comp->mEnableGravity = enable;
    if (comp->mRigidActor)
    {
        comp->mRigidActor->SetEnableGravity(enable);
    }
}

bool PhysicsSystemG::GetEnableGravity(const PhysicsComponentReader& comp)
{
    return comp->mEnableGravity;
}

bool PhysicsSystemG::GetIsTrigger(const PhysicsComponentReader& comp) const
{
    return comp->mIsTrigger;
}

void PhysicsSystemG::SetIsTrigger(const PhysicsComponentWriter& comp, bool isTrigger)
{
    comp->mIsTrigger = isTrigger;
    if (comp->mRigidActor)
    {
        comp->mRigidActor->SetIsTrigger(isTrigger);
        // switch between trigger and collision event
        RegisterCollisionCallback(comp);
    }
}

bool PhysicsSystemG::GetIsKinematic(const PhysicsComponentReader& comp) const
{
    return comp->mIsKinematic;
}

void PhysicsSystemG::SetIsKinematic(const PhysicsComponentWriter& comp, bool isKinematic)
{
    if (mGameWorld->GetWorldType() == WorldTypeTag::SDKWorld)
    {
        comp->mIsKinematic = isKinematic;
    }
    else
    {
        bool isEditor = EngineGlobal::GetSettingMgr()->GetAppStartUpType() == cross::AppStartUpType::AppStartUpTypeCrossEditor && mGameWorld->GetWorldType() != WorldTypeTag::PIEWorld;
        AssertMsg(comp->mIsDynamic, "Kinematic actor must be a dynamic one at first!");
        if (comp->mRigidActor && comp->mIsDynamic)
        {
            comp->mIsKinematic = isKinematic;
            comp->mRigidActor->SetIsKinematic(isKinematic || isEditor);
        }
    }
}

bool PhysicsSystemG::GetUseMeshCollision(const PhysicsComponentReader& comp) const {
    return comp->mUseMeshCollision;
};

void PhysicsSystemG::SetUseMeshCollision(const PhysicsComponentWriter& comp, bool useMeshCollision) {
    if (mGameWorld->GetWorldType() == WorldTypeTag::SDKWorld)
    {
        comp->mUseMeshCollision = useMeshCollision;
    }
    else
    {
        AssertMsg(!comp->mIsDynamic, "Only static actor can use triangle mesh as collision!");
        if (!comp->mIsDynamic)
        {
            comp->mUseMeshCollision = useMeshCollision;
            OnShapeChanged(comp);
        }
    }
}
float PhysicsSystemG::GetMaxDepenetrationVelocity(const PhysicsComponentReader& comp)
{
    return comp->mMaxDepenetrationVelocity;
}

void PhysicsSystemG::SetMaxDepenetrationVelocity(const PhysicsComponentWriter& comp, float velocity)
{
    if (velocity < 0)
    {
        velocity = 0;
    }
    comp->mMaxDepenetrationVelocity = velocity;
    if (comp->mRigidActor && comp->mRigidActor->IsDynamicActor())
    {
        comp->mRigidActor->SetMaxDepenetrationVelocity(velocity > 0 ? velocity : FLT_MAX);
    }
}

void PhysicsSystemG::SetMassSpaceInertiaTensorMultiplier(const PhysicsComponentWriter& comp, const Float3& multiplier)
{
    comp->mMassSpaceInertiaTensorMultiplier = multiplier;
    ApplyMass(comp);
}

CollisionType PhysicsSystemG::GetCollisionType(const PhysicsComponentReader& comp) const
{
    Assert(!comp->mRigidActor || comp->mRigidActor->GetCollisionType() == comp->mCollisionType);
    return comp->mCollisionType;
}

void PhysicsSystemG::SetCollisionType(const PhysicsComponentWriter& comp, CollisionType type)
{
    Assert(!comp->mRigidActor || comp->mRigidActor->GetCollisionType() == comp->mCollisionType);
    bool needUpdateShapes = comp->mCollisionType == CollisionType::NoCollision || type == CollisionType::NoCollision;
    comp->mCollisionType = type;
    if (comp->mRigidActor)
    {
        comp->mRigidActor->SetCollisionType(type);
        if (needUpdateShapes)
            OnShapeChanged(comp);
    }
}

CollisionMask PhysicsSystemG::GetCollisionMask(const PhysicsComponentReader& comp) const
{
    return comp->mCollisionMask;
    // return CollisionMask(comp->mCollisionChannel);
}

void PhysicsSystemG::SetCollisionMask(const PhysicsComponentWriter& comp, CollisionMask mask)
{
    comp->mCollisionMask = mask;
    if (comp->mRigidActor)
    {
        comp->mRigidActor->SetCollisionMask(mask);
    }
}

float PhysicsSystemG::GetLinearDamping(const PhysicsComponentReader& comp) const
{
    if (comp->mIsDynamic)
    {
        Assert(!comp->mRigidActor || FloatEqual(comp->mRigidActor->GetLinearDamping(), comp->mLinearDamping, 0.0001f));
        return comp->mLinearDamping;
    }
    return comp->mLinearDamping;
}

void PhysicsSystemG::SetLinearDamping(const PhysicsComponentWriter& comp, float linerDamping) const
{
    if (comp->mIsDynamic)
    {
        Assert(!comp->mRigidActor || FloatEqual(comp->mRigidActor->GetLinearDamping(), comp->mLinearDamping, 0.0001f));
        comp->mLinearDamping = linerDamping;
        if (comp->mRigidActor)
            comp->mRigidActor->SetLinearDamping(linerDamping);
    }
    else
    {
        comp->mLinearDamping = linerDamping;
    }
}

void PhysicsSystemG::DrawDebugVisualization(FrameAllocator* curAllocator, Float3 tile)
{
    std::unique_ptr<PhysicsDebugVisualization> visual = mScene->RefreshDebugVisualization();
    if (!visual)
    {
        return;
    }

    auto primitiveSystem = mGameWorld->GetGameSystem<PrimitiveRenderSystemG>();

    // Lines
    if (visual->GetNbLines())
    {
        UInt32 vertexCount = visual->GetNbLines() * 2;   // indexCount is also this value
        if (mVisualLinesIB.size() < static_cast<size_t>(vertexCount))
        {
            mVisualLinesIB.reserve(vertexCount);
            while (mVisualLinesIB.size() < static_cast<size_t>(vertexCount))
            {
                mVisualLinesIB.emplace_back((UInt32)mVisualLinesIB.size());
            }
        }
#if defined(CE_USE_DOUBLE_TRANSFORM)
        mVisualLinesVB.resize(visual->GetNbLines());
        // memcpy(mVisualLinesVB.data(), visual->GetLines(), sizeof(mVisualLinesVB[0]) * visual->GetNbLines());
        auto* visualLines = visual->GetLines();
        for (UInt32 i = 0; i < visual->GetNbLines(); i++)
        {
            memcpy(&mVisualLinesVB[i].p0, &visualLines[i].p0, sizeof(visual->GetLines()[0].p0));
            memcpy(&mVisualLinesVB[i].p0.tile, &tile, sizeof(tile));
            memcpy(&mVisualLinesVB[i].p1, &visualLines[i].p1, sizeof(visual->GetLines()[0].p1));
            memcpy(&mVisualLinesVB[i].p1.tile, &tile, sizeof(tile));
        }
        auto pVisualLinesVB = mVisualLinesVB.data();
#else
        auto pVisualLinesVB = const_cast<PhysicsDebugVisualization::Line*>(visual->GetLines());
#endif
        VertexStreamLayout layout = PrimitiveGenerator::GetStandardLineLayout();
        PrimitiveData primitiveData(reinterpret_cast<UInt8*>(pVisualLinesVB), mVisualLinesIB.data(), vertexCount, vertexCount, visual->GetNbLines(), layout, PrimitiveTopology::LineList);
        primitiveSystem->DrawPrimitive(&primitiveData, Float4x4A::Identity(), PrimitiveRenderSystemG::PrimitiveLook(), tile);
    }

    // Points
    {
        // dango phy TODO: What's our plane to display a debug point? Generate a sphere or what?
    }

    // Triangle
    if (visual->GetNbTriangles())
    {
        UInt32 trianglesCount = visual->GetNbTriangles();

        UInt32 indexCount = trianglesCount * 3;
        if (mVisualTrianIB.size() < static_cast<size_t>(indexCount))
        {
            mVisualTrianIB.reserve(indexCount);
            while (mVisualTrianIB.size() < static_cast<size_t>(indexCount))
            {
                mVisualTrianIB.emplace_back(static_cast<UInt32>(mVisualTrianIB.size()));
            }
        }

        UInt32 vertexCount = trianglesCount * 3;
        mVisualTrianVB.resize(vertexCount);

        for (size_t trianIndex = 0; trianIndex < trianglesCount; ++trianIndex)
        {
            const PhysicsDebugVisualization::Triangle& trian = visual->GetTriangles()[trianIndex];
            Float3 normal = -(trian.p2.pos - trian.p0.pos).Cross(trian.p1.pos - trian.p0.pos).Normalized();
#if defined(CE_USE_DOUBLE_TRANSFORM)
            auto pos1 = Float3(GetAbsolutePosition(tile, trian.p0.pos));
            auto pos2 = Float3(GetAbsolutePosition(tile, trian.p1.pos));
            auto pos3 = Float3(GetAbsolutePosition(tile, trian.p2.pos));
#else
            const auto& pos1 = trian.p0.pos;
            const auto& pos2 = trian.p1.pos;
            const auto& pos3 = trian.p2.pos;
#endif
            mVisualTrianVB[trianIndex * 3 + 0] = {pos1, trian.p0.color, normal};
            mVisualTrianVB[trianIndex * 3 + 1] = {pos2, trian.p1.color, normal};
            mVisualTrianVB[trianIndex * 3 + 2] = {pos3, trian.p2.color, normal};
        }

        VertexStreamLayout layout;
        layout.AddVertexChannelLayout(VertexChannel::Position0, VertexFormat::Float3, 0);
        layout.AddVertexChannelLayout(VertexChannel::Color0, VertexFormat::UByte4_Norm, 12);
        layout.AddVertexChannelLayout(VertexChannel::Normal0, VertexFormat::Float3, 16);

        PrimitiveData primitiveData(reinterpret_cast<UInt8*>(mVisualTrianVB.data()), mVisualTrianIB.data(), vertexCount, indexCount, trianglesCount, layout, PrimitiveTopology::TriangleList);
        primitiveSystem->DrawPrimitive(&primitiveData);
    }
}

void PhysicsSystemG::StepPhysicsScene(float elapsedTime)
{
    float averagedeltatime = cross::EngineGlobal::Inst().GetEngine()->GetRunAverageDeltaTime();
    // If this frame is too long, drop this frame and use 2 * avgframe
    if (elapsedTime > averagedeltatime * 2.0f)
    {
        elapsedTime = averagedeltatime * 2.0f;
    }
    mDeltaTimeRemain += elapsedTime;
    constexpr float MIN_DELTA_TIME = 1.0f / 80.0f;
    if (mDeltaTimeRemain >= MIN_DELTA_TIME)   // if remaintime < mintime, we wait until min time
    {
        float dtSmooth = 0;
        if (averagedeltatime >= MIN_DELTA_TIME)
            dtSmooth = (mDeltaTimeRemain + averagedeltatime) / 2.0f;   // if avg >= min, we use 50% remain and 50% avg to smooth the simulation
        else
            dtSmooth = MIN_DELTA_TIME;   // if avg < min, it means we are running at a very high frame rate, we use min directly

        if (dtSmooth > 0)
        {
            mDeltaTimeRemain -= dtSmooth;
            mScene->Simulate(dtSmooth);
            mSimulated = true;
        }
    }
}

void PhysicsSystemG::ApplyMass(const PhysicsComponentWriter& comp)
{
    if (comp->mRigidActor && comp->mIsDynamic)
    {
        if (comp->mMass > 0)
        {
            comp->mRigidActor->SetMassAndUpdateInertia(comp->mMass);
        }
        else
        {
            comp->mRigidActor->UpdateMassAndInertia(0.0015f);
            // kg/cm3 //dango phy TODO: Use physics material to specify inertia
        }
        if (comp->mMassSpaceInertiaTensorMultiplier != Float3(1.0f, 1.0f, 1.0f))
        {
            comp->mRigidActor->SetMassSpaceInertiaTensor(comp->mRigidActor->GetMassSpaceInertiaTensor() * comp->mMassSpaceInertiaTensorMultiplier);
        }
    }
}

std::vector<PhysicsGeometryBox> PhysicsSystemG::GetExtraBoxes(const PhysicsComponentReader& component) const
{
    if (component->mExtraCollision)
        return component->mExtraCollision->mBoxGeometry;
    return {};
}

void PhysicsSystemG::SetExtraBoxes(const PhysicsComponentWriter& component, const std::vector<PhysicsGeometryBox>& boxes)
{
    if (!component->mExtraCollision)
        component->mExtraCollision = std::make_unique<PhysicsSimpleCollision>();
    component->mExtraCollision->mBoxGeometry = boxes;
    for (auto& box : component->mExtraCollision->mBoxGeometry)
    {
        if (box.halfExtents.x == 0)
            box.halfExtents.x = 1;
        if (box.halfExtents.y == 0)
            box.halfExtents.y = 1;
        if (box.halfExtents.z == 0)
            box.halfExtents.z = 1;
    }
    OnShapeChanged(component);
}

std::vector<PhysicsGeometrySphere> PhysicsSystemG::GetExtraSpheres(const PhysicsComponentReader& component) const
{
    if (component->mExtraCollision)
        return component->mExtraCollision->mSphereGeometry;
    return {};
}

void PhysicsSystemG::SetExtraSpheres(const PhysicsComponentWriter& component, const std::vector<PhysicsGeometrySphere>& spheres)
{
    if (!component->mExtraCollision)
        component->mExtraCollision = std::make_unique<PhysicsSimpleCollision>();
    component->mExtraCollision->mSphereGeometry = spheres;
    for (auto& sphere : component->mExtraCollision->mSphereGeometry)
    {
        if (sphere.radius == 0)
            sphere.radius = 1;
    }
    OnShapeChanged(component);
}

std::vector<PhysicsGeometryCapsule> PhysicsSystemG::GetExtraCapsules(const PhysicsComponentReader& component) const
{
    if (component->mExtraCollision)
        return component->mExtraCollision->mCapsuleGeometry;
    return {};
}

void PhysicsSystemG::SetExtraCapsules(const PhysicsComponentWriter& component, const std::vector<PhysicsGeometryCapsule>& capsules)
{
    if (!component->mExtraCollision)
        component->mExtraCollision = std::make_unique<PhysicsSimpleCollision>();
    component->mExtraCollision->mCapsuleGeometry = capsules;
    for (auto& capsule : component->mExtraCollision->mCapsuleGeometry)
    {
        if (capsule.halfHeight == 0)
            capsule.halfHeight = 1;
        if (capsule.radius == 0)
            capsule.radius = 1;
    }
    OnShapeChanged(component);
}

PhysicsSimpleCollision PhysicsSystemG::GetExtraCollision(const PhysicsComponentReader& component) const
{
    if (component->mExtraCollision)
        return *(component->mExtraCollision);
    return {};
}

void PhysicsSystemG::SetExtraCollision(const PhysicsComponentWriter& component, const cross::PhysicsSimpleCollision& inValue)
{
    if (!component->mExtraCollision)
        component->mExtraCollision = std::make_unique<PhysicsSimpleCollision>();
    *(component->mExtraCollision) = inValue;
    OnShapeChanged(component);
}
}   // namespace cross
