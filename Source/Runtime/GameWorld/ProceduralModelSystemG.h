#pragma once
#include "ECS/Develop/Framework.h"
#include "ECS/Develop/Framework/Types.h"
#include "CrossBase/Math/CrossMath.h"
#include "CECommon/Common/GameSystemBase.h"
#include "CECommon/Common/FrameContainer.h"
#include "RenderEngine/RenderWorldConst.h"
#include "CEAnimation/Transform/AnimPose.h"
#include "Resource/MeshAssetData.h"
#include "Resource/Material.h"
#include "Runtime/GameWorld/ModelSystemG.h"
namespace cross {
class   ProceduralModelSystemG;
class   ProceduralModelSystemR;
struct ProceduralModelComponentG : ecs::IComponent
{
    struct ProceduralModel
    {
        MeshAssetDataPtr        mMeshData{nullptr};
        MaterialInterfacePtr    mMaterial{nullptr};
        BoundingBox             mBox{BoundingBox::Flags::MergeIdentity};
        bool                    mVisible{true};

        void Reset() {
            mMeshData = nullptr;
            mMaterial.reset();
        }
    };

    CEFunction(Reflect)
    static ENGINE_API ecs::ComponentDesc* GetDesc();

private:
    std::vector<ProceduralModel> mModels;
    
    friend class ProceduralModelSystemG;
};

struct ProceduralModelChangeData
{
    ecs::EntityID mEntity;
};

using  ProceduralModelChangeEvent = SystemEvent<ProceduralModelChangeData>;

class ENGINE_API ProceduralModelSystemG : public GameSystemBase, public SystemEventManager<ProceduralModelChangeEvent>
{
protected:
    ProceduralModelSystemG();
    virtual ~ProceduralModelSystemG();

public:
    using ModelChangeList = FrameChangeList<ecs::EntityID>;
    using ProceduralModelComponentHandle = ecs::ComponentHandle<ProceduralModelComponentG>;
    DEFINE_COMPONENT_READER_WRITER(ProceduralModelComponentG, ProceduralModelComponentReader, ProceduralModelComponentWriter)

    CEFunction(Reflect) 
    static ProceduralModelSystemG* CreateInstance();
    virtual void Release() override;
    virtual void OnBeginFrame(FrameParam* frameParam) override;
    CEFunction(Reflect) 
    virtual void OnBuildUpdateTasks(FrameParam* frameParam) override {}
    virtual void OnEndFrame(FrameParam* frameParam) override;
    virtual RenderSystemBase* GetRenderSystem() override;
    virtual void NotifyAddRenderSystemToRenderWorld() override;

private:
    CESystemInternal(ComponentType = ProceduralModelComponentG) 

    ProceduralModelSystemR*     mRenderMeshSystem{nullptr};
    bool                        mIsRenderObjectOwner{true};
    ModelChangeList             mModelChangeList;

public:
    ProceduralModelComponentG CreateProceduralModelComponent();

    const ProceduralModelComponentG::ProceduralModel& GetModel(const ProceduralModelComponentReader& modelH, UInt32 modelIndex) const;

    const MeshAssetDataPtr& GetModelMesh(const ProceduralModelComponentReader& modelH, UInt32 modelIndex) const
    {
        return GetModel(modelH, modelIndex).mMeshData;
    }

    UInt64 GetModelCount(const ProceduralModelComponentReader& modelH){ return modelH->mModels.size(); };

    const auto& GetChangeList() { return mModelChangeList; };

    BoundingBox GetCurrentBoundingBox(const ProceduralModelComponentReader& modelH) const;

    void SetModelVisibility(const ProceduralModelComponentWriter& modelH, UInt32 modelIndex, bool visible);

    void SetModelMaterial(const ProceduralModelComponentWriter& modelH, UInt32 modelIndex, MaterialInterfacePtr matrial);

    void CreateModel(const ProceduralModelComponentWriter& modelH, UInt32 modelIndex, MeshAssetDataPtr mesh, const BoundingBox& box, MaterialPtr matrial);

    void UpdateModel(const ProceduralModelComponentWriter& modelH, UInt32 modelIndex, MeshAssetDataPtr mesh, const BoundingBox& box, MaterialPtr matrial);

    void CreateModel(const ProceduralModelComponentWriter& modelH, UInt32 modelIndex, MeshAssetDataPtr mesh, const BoundingBox& box, MaterialInterfacePtr matrial);

    void UpdateModel(const ProceduralModelComponentWriter& modelH, UInt32 modelIndex, MeshAssetDataPtr mesh, const BoundingBox& box, MaterialInterfacePtr matrial);
    
    void DeleteModel(const ProceduralModelComponentWriter& modelH, UInt32 modelIndex);
};
}