#include "EnginePrefix.h"
#include "ECS/Develop/Framework.h"
#include "CrossBase/Template/TypeCast.hpp"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "CECommon/Common/SettingsManager.h"
#include "Runtime/GameWorld/MeshBuildSystemG.h"
#include "Runtime/GameWorld/SkinnedMeshBuilderG.h"
#include "Threading/RenderingThread.h"

namespace cross
{
    MeshBuildSystemG* MeshBuildSystemG::CreateInstance() { return new MeshBuildSystemG(); }

    MeshBuilderBaseG * MeshBuildSystemG::GetBuilderByName(const char * name)
    {
        if (name)
        {
			StringHash32 nameHash = HashFunction::HashString32(name);
            for (UInt32 i = 0; i < mMeshBuilders.size(); i++)
            {
                if (mMeshBuilders[i]->GetName().GetHash32() == nameHash)
                {
                    return mMeshBuilders[i];
                }
            }
        }
        return nullptr;
    }

    void MeshBuildSystemG::AddMeshBuilder(MeshBuilderBaseG * meshBuilder)
    {
        if (meshBuilder == nullptr)
            return;

        bool replaced = false;
        auto flag = meshBuilder->GetBatchFlag();
        for (UInt32 i = 0; i < (UInt32)mMeshBuilders.size(); i++)
        {
            auto builderFlag = mMeshBuilders[i]->GetBatchFlag();
            if ((builderFlag & flag) == flag)
            {
                if (mMeshBuilders[i] != meshBuilder)
                {
                    if (mMeshBuilders[i])
                    {
                        mMeshBuilders[i]->Release();
                    }

                    mMeshBuilders[i] = meshBuilder;
                    replaced = true;
                    break;
                }
                else
                {
                    return;
                }
            }
        }

        if (!replaced)
        {
            mMeshBuilders.emplace_back(meshBuilder);
        }

		auto* renderBuilder = meshBuilder->GetRenderObject();
		Assert(renderBuilder);

        DispatchRenderingCommandWithToken([this, renderBuilder]
		{
			mRenderMeshBuildSystem->AddMeshBuilder(renderBuilder);
		});

		meshBuilder->NotifyAddRenderObjectToRenderWorld();
    }

    void MeshBuildSystemG::DestroyMeshBuilder(const char * name)
    {
        if (!name)
        {
            return;
        }
        StringHash32 nameHash = HashFunction::HashString32(name);
        for (UInt32 i = 0; i < mMeshBuilders.size(); i++)
        {
            if (mMeshBuilders[i]->GetName().GetHash32() == nameHash)
            {
                mMeshBuilders[i]->Release();
                mMeshBuilders.erase(mMeshBuilders.begin() + i);

                DispatchRenderingCommandWithToken([this, name]
				{
					mRenderMeshBuildSystem->DestroyMeshBuilder(name);
				});
                return;
            }
        }
    }

    MeshBuildSystemG::MeshBuildSystemG()
    {
        //create render mesh build system
        mRenderMeshBuildSystem = MeshBuildSystemR::CreateInstance();

		//create mesh builder for build system
		//this->AddMeshBuilder(new SimpleMeshBuilderG());
		this->AddMeshBuilder(new SkinnedMeshBuilderG());
    }

    MeshBuildSystemG::~MeshBuildSystemG()
    {
        for (auto builder : mMeshBuilders)
        {
            builder->Release();
        }
        mMeshBuilders.clear();

        if (mIsRenderObjectOwner)
        {
            mRenderMeshBuildSystem->Release();
        }
        mRenderMeshBuildSystem = nullptr;
    }

    void MeshBuildSystemG::Release()
    {
        delete this;
    }

    cross::RenderSystemBase* MeshBuildSystemG::GetRenderSystem()
    {
        return mRenderMeshBuildSystem;
    }

    void MeshBuildSystemG::NotifyAddRenderSystemToRenderWorld()
    {
        mIsRenderObjectOwner = false;
    }

}
