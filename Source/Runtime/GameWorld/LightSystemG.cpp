
#include "EnginePrefix.h"
#include "CrossBase/Template/TypeCast.hpp"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "Runtime/GameWorld/LightSystemG.h"
#include "Runtime/GameWorld/TransformSystemG.h"

namespace cross
{

    ecs::ComponentDesc* LightComponentG::GetDesc()
    {
        return EngineGlobal::GetECSFramework().CreateOrGetGameComponentDesc<cross::LightComponentG>(
            {false, true, true}, & LightSystemG::SerializeLightComponent, & LightSystemG::DeserializeLightComponent, & LightSystemG::PostDeserializeComponent, & LightSystemG::UpdateDeserializeComponent);
}

LightSystemG * LightSystemG::CreateInstance()
{
    return new LightSystemG;
}

void LightSystemG::Release()
{
    delete this;
}

LightSystemG::LightSystemG()
{   
    mRenderLightSystem = LightSystemR::CreateInstance();
}

void LightSystemG::OnEntityPropChange(ecs::EntityID entity, ecs::EntityDescFlags::EntityProps prop, bool bValue) 
{
    if (prop == ecs::EntityDescFlags::Visibility)
    {
        auto lightH = mGameWorld->GetComponent<cross::LightComponentG>(entity);
        if (lightH.IsValid())
            SetLightEnable(lightH.Write(), bValue);
    }
}

LightSystemG::~LightSystemG()
{
    if (mIsRenderObjectOwner)
    {
        mRenderLightSystem->Release();
    }
    mRenderLightSystem = nullptr;
}

RenderSystemBase * LightSystemG::GetRenderSystem()
{
    return mRenderLightSystem;
}

void LightSystemG::NotifyAddRenderSystemToRenderWorld()
{
    mIsRenderObjectOwner = false;
}

void LightSystemG::GetLightComponent(const LightComponentReader & component, LightComponentG & outValue)
{
    outValue = *component;
}

void cross::LightSystemG::SetLightComponent(const LightComponentWriter & component, const LightComponentG & inValue)
{
    *component = inValue;

    // clamp degree from 1 to 80 and keep at least 1.f degree larger than inner angle
    float innerAngle = std::clamp(inValue.mInnerConeAngle, 1.f, 80.f);
    float outerAngle = std::clamp(inValue.mOuterConeAngle, innerAngle + 1.f, 80.f);
    component->mInnerConeAngle = innerAngle;
    component->mOuterConeAngle = outerAngle;

    auto* entityMetaSystem = mGameWorld->GetGameSystem<cross::EntityMetaSystem>();
    bool isVisible = entityMetaSystem->GetEntityVisibility(component.GetEntityID());
    DispatchRenderingCommandWithToken([renderLightSystem = mRenderLightSystem, entityId = component.GetEntityID(), inValue, isVisible,innerAngle,outerAngle] {
        renderLightSystem->SetLightType(entityId, inValue.mType);
        renderLightSystem->SetLightIntensity(entityId, inValue.mIntensity);
        renderLightSystem->SetLightPrtIntensity(entityId, inValue.mPrtIntensity);
        renderLightSystem->SetLightSpecularIntensity(entityId, inValue.mSpecularIntensity);
        renderLightSystem->SetLightVolumetricFactor(entityId, inValue.mVolumetricFactor);
        renderLightSystem->SetLightSourceAngle(entityId, inValue.mSourceAngle);
        renderLightSystem->SetLightAtmosphereLightConfig(entityId, inValue.mAtmosphereLightConfig);
        renderLightSystem->SetLightColor(entityId, inValue.mColor);

        renderLightSystem->SetLightInnerCosAngle(entityId, cos(Deg2Rad(innerAngle)));
        renderLightSystem->SetLightOuterCosAngle(entityId, cos(Deg2Rad(outerAngle)));
        renderLightSystem->SetLightConeFadeIntensity(entityId, inValue.mConeFadeIntensity);
        renderLightSystem->SetLightConeOverFlowLength(entityId, inValue.mConeOverFlowLength);
        renderLightSystem->SetLightSpotDistanceExp(entityId, inValue.mSpotDistanceExp);

        renderLightSystem->SetLightRange(entityId, inValue.mRange);
        renderLightSystem->SetLightCastShadow(entityId, inValue.mCastShadow);
        renderLightSystem->SetLightCastScreenSpaceShadow(entityId, inValue.mCastScreenSpaceShadow);
        renderLightSystem->SetLightShadowStrength(entityId, inValue.mShadowStrength);
        renderLightSystem->SetLightEnable(entityId, inValue.mEnable&&isVisible);
        renderLightSystem->SetLightMode(entityId, inValue.mMode);
        renderLightSystem->SetLightPriority(entityId, inValue.mPriority);
        renderLightSystem->SetLightSourceWidth(entityId, inValue.mSourceWidth);
        renderLightSystem->SetLightSourceHeight(entityId, inValue.mSourceHeight);
        renderLightSystem->SetLightBarnDoorAngle(entityId, std::clamp(inValue.mBarnDoorAngle,0.0f,88.0f));
        renderLightSystem->SetLightBarnDoorLength(entityId,std::max(0.0f, inValue.mBarnDoorLength));
        renderLightSystem->SetLightShadowType(entityId, inValue.mShadowType);
        renderLightSystem->SetLightShadowAmount(entityId, inValue.mShadowAmount);
        renderLightSystem->SetLightShadowBias(entityId, inValue.mShadowBias);
        renderLightSystem->SetLightShadowSlopeBias(entityId, inValue.mShadowSlopeBias);
        renderLightSystem->SetLightVarianceBiasVSM(entityId, inValue.mVarianceBiasVSM);
        renderLightSystem->SetLightLightLeakBiasVSM(entityId, inValue.mLightLeakBiasVSM);
        renderLightSystem->SetLightFilterSizePCF(entityId, inValue.mFilterSizePCF);
        renderLightSystem->SetLightSoftnessPCSS(entityId, inValue.mSoftnessPCSS);
        renderLightSystem->SetLightSampleCountPCSS(entityId, inValue.mSampleCountPCSS);
        renderLightSystem->SetLightRenderingLayerMask(entityId, static_cast<UInt32>(inValue.mRenderingLayerMask));
        renderLightSystem->SetLightEnableTransmittance(entityId, inValue.mEnableTransmittance);

    });
}

SerializeNode LightSystemG::SerializeLightComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr)
{
    SerializeNode json;
    auto lightComponentPtr = static_cast<LightComponentG*>(componentPtr);
    SerializeContext context;
    return lightComponentPtr->Serialize(context);
        
    // json["mLightData"] = {
    //     "mType"_k = (UInt32)componetPtr->mType,
    //     "mColor"_k = componetPtr->mColor.Serialize(),
    //     "mIntensity"_k = componetPtr->mIntensity,
    //     "mPrtIntensity"_k = componetPtr->mPrtIntensity,
    //     "mVolumetricFactor"_k = componetPtr->mVolumetricFactor,
    //     "mSourceAngle"_k = componetPtr->mSourceAngle,
    //     "mRange"_k = componetPtr->mRange,
    //     "mInnerConeAngle"_k = std::clamp(componetPtr->mInnerConeAngle, 1.f, 80.f),
    //     "mOuterConeAngle"_k = std::clamp(componetPtr->mOuterConeAngle, 2.f, 80.f),
    //     "mVersion"_k = 1,
    //     "mConeFadeIntensity"_k = componetPtr->mConeFadeIntensity,
    //     "mConeOverFlowLength"_k = componetPtr->mConeOverFlowLength,
    //     "mSpotDistanceExp"_k = componetPtr->mSpotDistanceExp,
    //     "mCastShadow"_k = componetPtr->mCastShadow,
    //     "mCastScreenSpaceShadow"_k = componetPtr->mCastScreenSpaceShadow,
    //     "mShadowStrength"_k = componetPtr->mShadowStrength,
    //     "mSourceWidth"_k = componetPtr->mSourceWidth,
    //     "mSourceHeight"_k = componetPtr->mSourceHeight,
    //     "mBarnDoorAngle"_k = std::clamp(componetPtr->mBarnDoorAngle, 0.0f, 88.0f),
    //     "mBarnDoorLength"_k = std::max(componetPtr->mBarnDoorLength,0.0f)
    // };
    //
    // json["mEnable"] = componetPtr->mEnable;
    // json["mMode"] = (UInt8)componetPtr->mMode;
    // json["mPriority"] = (UInt8)componetPtr->mPriority;
    // json["mShadowType"] = static_cast<UInt8>(componetPtr->mShadowType);
    // json["mShadowAmount"] = componetPtr->mShadowAmount;
    // json["mShadowBias"] = componetPtr->mShadowBias;
    // json["mShadowSlopeBias"] = componetPtr->mShadowSlopeBias;
    // json["mVarianceBiasVSM"] = componetPtr->mVarianceBiasVSM;
    // json["mLightLeakBiasVSM"] = componetPtr->mLightLeakBiasVSM;
    // json["mFilterSizePCF"] = componetPtr->mFilterSizePCF;
    // json["mSoftnessPCSS"] = componetPtr->mSoftnessPCSS;
    // json["mSampleCountPCSS"] = componetPtr->mSampleCountPCSS;
    //     
    // SerializeContext context;
    // json["mAtmosphereLightConfig"] = componetPtr->mAtmosphereLightConfig.Serialize(context);
    //
    // json["mRenderingLayerMask"] = componetPtr->mRenderingLayerMask;
    // json["mEnableTransmittance"] = componetPtr->mEnableTransmittance;
    // return json;
}

void LightSystemG::DeserializeLightComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr)
{
    if (json.IsNull())
    {
        return;
    }
    auto lightComponentPtr = static_cast<LightComponentG*>(componentPtr);

    SerializeContext context;
    lightComponentPtr->Deserialize(json, context);
    if (json.HasMember("mLightData"))  // For compatibility
    {
        lightComponentPtr->Deserialize(json["mLightData"], context);
    }
        
    // if (json.IsObject())
    // {
    //     auto lightInfoJson = json["mLightData"];
    //     if (lightInfoJson.IsObject())
    //     {
    //         lightComponentPtr->mType = (LightType)lightInfoJson.Value("mType", (UInt8)LightType::Directional);
    //         lightComponentPtr->mColor.Deserialize(lightInfoJson["mColor"]);
    //         lightComponentPtr->mIntensity = lightInfoJson.Value("mIntensity", 1.0f);
    //         lightComponentPtr->mPrtIntensity = lightInfoJson.Value("mPrtIntensity", 1.0f);
    //         lightComponentPtr->mVolumetricFactor = lightInfoJson.Value("mVolumetricFactor", 1.0f);
    //         lightComponentPtr->mSourceAngle = lightInfoJson.Value("mSourceAngle", 0.5347f);
    //         lightComponentPtr->mRange = lightInfoJson.Value("mRange", 1.0f);
    //         lightComponentPtr->mInnerConeAngle = lightInfoJson.Value("mInnerConeAngle", 60.f);
    //         lightComponentPtr->mOuterConeAngle = lightInfoJson.Value("mOuterConeAngle", 60.f);
    //         if (!lightInfoJson.HasMember("mVersion"))
    //         {
    //             lightComponentPtr->mInnerConeAngle *= _Rad2Deg;
    //             lightComponentPtr->mOuterConeAngle *= _Rad2Deg;
    //         }
    //         lightComponentPtr->mConeFadeIntensity = lightInfoJson.Value("mConeFadeIntensity", 1.0f);
    //         lightComponentPtr->mConeOverFlowLength = lightInfoJson.Value("mConeOverFlowLength", 0.0f);
    //         lightComponentPtr->mSpotDistanceExp = lightInfoJson.Value("mSpotDistanceExp", 1.0f);
    //         lightComponentPtr->mCastShadow = lightInfoJson.Value("mCastShadow", true);
    //         lightComponentPtr->mCastScreenSpaceShadow = lightInfoJson.Value("mCastScreenSpaceShadow", true);
    //         lightComponentPtr->mShadowStrength = lightInfoJson.Value("mShadowStrength", 1.0f);
    //         lightComponentPtr->mSourceWidth = lightInfoJson.Value("mSourceWidth", 1.0f);
    //         lightComponentPtr->mSourceHeight = lightInfoJson.Value("mSourceHeight", 1.0f);
    //         lightComponentPtr->mBarnDoorAngle = lightInfoJson.Value("mBarnDoorAngle", 1.0f);
    //         lightComponentPtr->mBarnDoorLength = lightInfoJson.Value("mBarnDoorLength", 1.0f);
    //     };
    //     lightComponentPtr->mEnable = json.Value("mEnable", true);
    //     lightComponentPtr->mMode = (LightMode)json.Value("mMode", (UInt8)LightMode::Realtime);
    //     lightComponentPtr->mPriority = (LightPriority)json.Value("mPriority", (UInt8)LightPriority::Major);
    //     lightComponentPtr->mShadowType = static_cast<LightShadowType>(json.Value("mShadowType", static_cast<UInt8>(LightShadowType::HardShadow)));
    //     lightComponentPtr->mShadowAmount = json.Value("mShadowAmount", 1.f);
    //     lightComponentPtr->mShadowBias = json.Value("mShadowBias", 0.6f);
    //     lightComponentPtr->mShadowSlopeBias = json.Value("mShadowSlopeBias", 0.5f);
    //     lightComponentPtr->mVarianceBiasVSM = json.Value("mVarianceBiasVSM", 0.01f);
    //     lightComponentPtr->mLightLeakBiasVSM = json.Value("mLightLeakBiasVSM", 0.01f);
    //     lightComponentPtr->mFilterSizePCF = json.Value("mFilterSizePCF", 2.f);
    //     lightComponentPtr->mSoftnessPCSS = json.Value("mSoftnessPCSS", 0.005f);
    //     lightComponentPtr->mSampleCountPCSS = json.Value("mSampleCountPCSS", 32.f);
    //
    //     lightComponentPtr->mRenderingLayerMask = json.Value("mRenderingLayerMask", RenderingLayer::Default);
    //     lightComponentPtr->mEnableTransmittance = json.Value("mEnableTransmittance", true);
    //
    //     if (auto atmoLight = json.HasMember("mAtmosphereLightConfig"); atmoLight)
    //     {
    //         SerializeContext context_;
    //         lightComponentPtr->mAtmosphereLightConfig.Deserialize(*atmoLight, context_);
    //     }
    // }
}

void LightSystemG::PostDeserializeComponent(const DeserializeNode& json, ecs::IComponent * componentPtr, GameWorld* gameWorld, ecs::EntityID entityId)
{
    auto sys = gameWorld->GetGameSystem<LightSystemG>();  
    auto componetPtr = static_cast<LightComponentG*>(componentPtr);
    //Sync all data to render
    DispatchRenderingCommandWithToken([renderLightSystem = TYPE_CAST(LightSystemR*, sys->GetRenderSystem()), entityId, comp = *componetPtr]
    {
        renderLightSystem->SetLightType(entityId, comp.mType);
        renderLightSystem->SetLightIntensity(entityId, comp.mIntensity);
        renderLightSystem->SetLightPrtIntensity(entityId, comp.mPrtIntensity);
        renderLightSystem->SetLightVolumetricFactor(entityId, comp.mVolumetricFactor);
        renderLightSystem->SetLightSourceAngle(entityId, comp.mSourceAngle);
        renderLightSystem->SetLightAtmosphereLightConfig(entityId, comp.mAtmosphereLightConfig);
        renderLightSystem->SetLightColor(entityId, comp.mColor);
        
        auto cosAngle = static_cast<float>(cos(Deg2Rad(comp.mInnerConeAngle)));
        renderLightSystem->SetLightInnerCosAngle(entityId, cosAngle);

        cosAngle = static_cast<float>(cos(Deg2Rad(comp.mOuterConeAngle)));
        renderLightSystem->SetLightOuterCosAngle(entityId, cosAngle);
        renderLightSystem->SetLightConeFadeIntensity(entityId, comp.mConeFadeIntensity);
        renderLightSystem->SetLightConeOverFlowLength(entityId, comp.mConeOverFlowLength);
        renderLightSystem->SetLightSpotDistanceExp(entityId, comp.mSpotDistanceExp);

        renderLightSystem->SetLightRange(entityId, comp.mRange);
        renderLightSystem->SetLightCastShadow(entityId, comp.mCastShadow);
        renderLightSystem->SetLightCastScreenSpaceShadow(entityId, comp.mCastScreenSpaceShadow);
        renderLightSystem->SetLightShadowStrength(entityId, comp.mShadowStrength);
        renderLightSystem->SetLightEnable(entityId, comp.mEnable);
        renderLightSystem->SetLightMode(entityId, comp.mMode);
        renderLightSystem->SetLightPriority(entityId, comp.mPriority);
        renderLightSystem->SetLightSourceWidth(entityId, comp.mSourceWidth);
        renderLightSystem->SetLightSourceHeight(entityId, comp.mSourceHeight);
        renderLightSystem->SetLightBarnDoorAngle(entityId, comp.mBarnDoorAngle);
        renderLightSystem->SetLightBarnDoorLength(entityId, comp.mBarnDoorLength);
        renderLightSystem->SetLightShadowType(entityId, comp.mShadowType);
        renderLightSystem->SetLightShadowAmount(entityId, comp.mShadowAmount);
        renderLightSystem->SetLightShadowBias(entityId, comp.mShadowBias);
        renderLightSystem->SetLightShadowSlopeBias(entityId, comp.mShadowSlopeBias);
        renderLightSystem->SetLightVarianceBiasVSM(entityId, comp.mVarianceBiasVSM);
        renderLightSystem->SetLightLightLeakBiasVSM(entityId, comp.mLightLeakBiasVSM);
        renderLightSystem->SetLightFilterSizePCF(entityId, comp.mFilterSizePCF);
        renderLightSystem->SetLightSoftnessPCSS(entityId, comp.mSoftnessPCSS);
        renderLightSystem->SetLightSampleCountPCSS(entityId, comp.mSampleCountPCSS);
        renderLightSystem->SetLightRenderingLayerMask(entityId, static_cast<UInt32>(comp.mRenderingLayerMask));
        renderLightSystem->SetLightEnableTransmittance(entityId, comp.mEnableTransmittance);
    });
}

void LightSystemG::UpdateDeserializeComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId)
{
    DeserializeLightComponent(gameWorld, json, componentPtr);
    PostDeserializeComponent(json, componentPtr, gameWorld, entityId);
}

void LightSystemG::SetLightInnerConeAngle(const LightComponentWriter& lightHandle, const float& angle)
{
    // clamp degree from 1 to 80
    lightHandle->mInnerConeAngle = std::clamp(angle, 1.f, 80.f);
    lightHandle->mOuterConeAngle = std::clamp(lightHandle->mOuterConeAngle, lightHandle->mInnerConeAngle + 1.f, 80.f);

    DispatchRenderingCommandWithToken([this, entity = lightHandle.GetEntityID(), innerAngle = lightHandle->mInnerConeAngle, outerAngle = lightHandle->mOuterConeAngle] {
        mRenderLightSystem->SetLightInnerCosAngle(entity, cos(Deg2Rad(innerAngle)));
        mRenderLightSystem->SetLightOuterCosAngle(entity, cos(Deg2Rad(outerAngle)));
    });
}

void LightSystemG::SetLightOuterConeAngle(const LightComponentWriter& lightHandle, const float& angle)
{
    // clamp degree from 1 to 80 and keep at 1.f degree larger than inner angle
    lightHandle->mOuterConeAngle = std::clamp(angle, 2.f, 80.f);
    lightHandle->mInnerConeAngle = std::clamp(lightHandle->mInnerConeAngle, 1.f, lightHandle->mOuterConeAngle - 1.f);

    DispatchRenderingCommandWithToken([this, entity = lightHandle.GetEntityID(), outerAngle = lightHandle->mOuterConeAngle, innerAngle = lightHandle->mInnerConeAngle] {
        mRenderLightSystem->SetLightOuterCosAngle(entity, cos(Deg2Rad(outerAngle)));
        mRenderLightSystem->SetLightInnerCosAngle(entity, cos(Deg2Rad(innerAngle)));
    });
}

void LightSystemG::SetLightConeFadeIntensity(const LightComponentWriter& lightHandle, const float& intensity) 
{
    lightHandle->mConeFadeIntensity = intensity;

    DispatchRenderingCommandWithToken([this, entity = lightHandle.GetEntityID(), intensity] { 
        mRenderLightSystem->SetLightConeFadeIntensity(entity, intensity); 
    });
}

void LightSystemG::SetLightConeOverFlowLength(const LightComponentWriter& lightHandle, const float& intensity) {
    lightHandle->mConeOverFlowLength = intensity;

    DispatchRenderingCommandWithToken([this, entity = lightHandle.GetEntityID(), intensity] { 
        mRenderLightSystem->SetLightConeOverFlowLength(entity, intensity); 
    });
}

void LightSystemG::SetLightSpotDistanceExp(const LightComponentWriter& lightHandle, const float& var)
{
    lightHandle->mSpotDistanceExp = var;

    DispatchRenderingCommandWithToken([this, entity = lightHandle.GetEntityID(), var] { 
        mRenderLightSystem->SetLightSpotDistanceExp(entity, std::max(var, 1.0f)); 
    });
}

void LightSystemG::SetBoundaryShow(const LightComponentWriter& writer, bool isShow)
{
    writer->mIsBoundaryShow = isShow;
    DispatchRenderingCommandWithToken([renderSystem = mRenderLightSystem, entity = writer.GetEntityID(), isShow]()
    {
        renderSystem->SetBoundaryShow(entity, isShow);
    });
}

const char* LightSystemG::GetLightIconTexturePath(LightType lightType)
{
        switch (lightType)
        {
        case cross::LightType::Directional:
            return "EngineResource/Editor/Icons/AssetIcon/DirectionalLight.nda";
        case cross::LightType::Point:
            return "EngineResource/Editor/Icons/AssetIcon/PointLight.nda";
        case cross::LightType::Spot:
            return "EngineResource/Editor/Icons/AssetIcon/SpotLight.nda";
        case cross::LightType::Rect:
            return "EngineResource/Editor/Icons/AssetIcon/RectLight.nda";
        default:
            return "EngineResource/Editor/Icons/AssetIcon/Entity.nda";
        }
}
 float LightSystemG::GetLightRange(const LightComponentReader& comp) const
{      
        return comp->mRange;
}
}   // namespace cross
