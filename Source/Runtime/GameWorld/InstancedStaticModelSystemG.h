#pragma once
#include "ECS/Develop/Framework.h"
#include "ECS/Develop/Framework/Types.h"
#include "CrossBase/Math/CrossMath.h"
#include "CECommon/Common/GameSystemBase.h"
#include "CECommon/Common/FrameStdContainer.h"
#include "Resource/MeshAssetDataResource.h"
#include "Resource/InstanceDataResource.h"
#include "RenderEngine/RenderWorldConst.h"
#include "RenderEngine/InstancedStaticModelSystemR.h"
#include "CECommon/Common/FrameStdContainer.h"

namespace cross {

struct StaticModelSubModelProperty
{
    CEProperty()
    std::string mMaterialPath{};
    MaterialInterfacePtr mMaterial{};

    CEProperty()
    bool mVisible{true};

    CE_Serialize_Deserialize;
};

struct StaticModelLODProperty
{
    CEProperty()
    std::vector<StaticModelSubModelProperty> mSubModelProperties{};

    CE_Serialize_Deserialize;
};

struct InstancedStaticModelComponentG : ecs::IComponent
{
public:
    CEFunction(Reflect)
    static ENGINE_API ecs::ComponentDesc* GetDesc();

public:
    bool mEnable = false;

    CEProperty()
    bool mVisible = true;

    CEProperty()
    bool mReceiveDecals = true;

    CEProperty()
    bool mUseLod0Bbox = false;   // Use LOD0 bounding box for culling, otherwise use the bounding box of the current LOD model.

    CEProperty(Editor, Script)
    bool mEnabledIntersection = true;

    CEProperty()
    std::string mAssetPath{};
    MeshAssetDataResourcePtr mAsset;

    CEProperty()
    std::vector<StaticModelLODProperty> mLODModelProperties;

    CEProperty()
    EntityDistanceCulling mDistanceCulling;

    CEProperty()
    std::string mInstanceDataResourcePath;

    CEProperty()
    float mGlobalScale = 1.0f;
    InstanceDataResourcePtr mInstanceDataResource;


        CEProperty()
    float RenderPercentage = 1.0f;

    CEProperty()
    std::string mRenderInstanceDataResourcePath;

    InstanceDataResourcePtr mRenderInstanceDataProxy;



    CE_Serialize_Deserialize;

    CEFunction(AdditionalDeserialize)
    void AdditionalDeserialize(const DeserializeNode& inNode, SerializeContext& context);

    bool RebuildRenderProxyInstance();
};

struct InstanceDataChangeEventData
{
    cross::ecs::EntityID mEntity;
};


using InstanceDataChangeEvent = SystemEvent<InstanceDataChangeEventData>;

class ModelSystemR;
class ENGINE_API CEMeta(Cli) InstancedStaticModelSystemG : public GameSystemBase, public SystemEventManager<InstanceDataChangeEvent>
{
    CESystemInternal(ComponentType = InstancedStaticModelComponentG)
public:
    using InstancedStaticModelComponentHandle = ecs::ComponentHandle<InstancedStaticModelComponentG>;
    DEFINE_COMPONENT_READER_WRITER(InstancedStaticModelComponentG, InstancedStaticModelComponentReader, InstancedStaticModelComponentWriter)

    InstancedStaticModelSystemG();

    virtual void Release() override;

    virtual void OnBeginFrame(FrameParam * frameParam) override;

    virtual void OnFirstUpdate(FrameParam * frameParam) override;

    virtual void OnEndFrame(FrameParam * frameParam) override;

    virtual RenderSystemBase* GetRenderSystem() override;

    virtual void NotifyAddRenderSystemToRenderWorld() override
    {
        mIsRenderObjectOwner = false;
    };

public:
    CEFunction(Reflect)
    static InstancedStaticModelSystemG* CreateInstance();

    static SerializeNode SerializeInstancedStaticModelComponent(ISerializeWorld * serializeWorld, ecs::IComponent * componentPtr);

    static void DeserializeInstancedStaticModelComponent(ISerializeWorld * serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr);

    static void PostDeserializeInstancedStaticModelComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId);

    static void UpdateDeserializeInstancedStaticModelComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId);

    static void GetResourceInstancedStaticModelComponent(ISerializeWorld * serializeWorld, ecs::IComponent * componentPtr, ResourcePtr resource);

public:
    bool SetModelAsset(const InstancedStaticModelComponentWriter& modelH, const MeshAssetDataResourcePtr& meshAssetDataResourcePtr);

    CEFunction(Editor, Script, Cli)
    bool SetModelAssetPath(const InstancedStaticModelComponentWriter& modelH, const std::string& assetpath);

    const MeshAssetDataResourcePtr& GetModelAsset(const InstancedStaticModelComponentReader& modelH) const
    {
        return modelH->mAsset;
    }

    CEFunction(Editor, Script, Cli)
    std::string GetModelAssetPath(const InstancedStaticModelComponentReader& modelH) const
    {
        return modelH->mAsset ? modelH->mAsset->GetGuid_Str() : "";
    }

    CEFunction(Editor, Script, Cli)
    UInt32 GetModelAssetLODCount(const InstancedStaticModelComponentReader& modelH) const;

    void SetModelMaterial(const InstancedStaticModelComponentWriter& modelH, MaterialInterfacePtr material, SInt32 subModelIndex = -1, SInt32 lodIndex = -1);

    CEFunction(Editor, Script)
    bool SetModelMaterialPath(const InstancedStaticModelComponentWriter& modelH, const std::string& assetpath, SInt32 subModelIndex = -1, SInt32 lodIndex = -1);

    MaterialInterfacePtr GetModelMaterial(const InstancedStaticModelComponentReader& modelH, UInt32 subModelIndex, UInt32 lodIndex) const;

    CEFunction(Editor, Script, Cli)
    std::string GetModelMaterialPath(const InstancedStaticModelComponentReader& modelH, UInt32 subModelIndex, UInt32 lodIndex) const
    {
        auto material = GetModelMaterial(modelH, subModelIndex, lodIndex);
        return material ? material->GetGuid_Str() : "";
    }

    CEFunction(Editor)
    void SetModelEnityDistanceCulling(const InstancedStaticModelComponentWriter& modelH, const EntityDistanceCulling& entityCulling);

    CEFunction(Editor)
    EntityDistanceCulling GetModelEnityDistanceCulling(const InstancedStaticModelComponentReader& model);

    CEFunction(Editor, Script)
    bool IsModelVisible(const InstancedStaticModelComponentReader& modelH) const;

    CEFunction(Editor, Script)
    void SetModelVisible(const InstancedStaticModelComponentWriter& modelH, bool isVisible);

    CEFunction(Editor, Script)
    bool IsSubModelVisible(const InstancedStaticModelComponentReader& modelH, UInt32 lodIndex, UInt32 subModelIndex) const;

    CEFunction(Editor, Script)
    void SetSubModelVisible(const InstancedStaticModelComponentWriter& modelH, bool isVisible, UInt32 lodIndex, SInt32 subModelIndex = -1);   // -1 modify all subModels

    CEFunction(Editor, Script)
    void SetModelReceiveDecals(const InstancedStaticModelComponentWriter& modelH, bool value);

    CEFunction(Editor, Script)
    bool GetModelReceiveDecals(const InstancedStaticModelComponentReader& modelH) const;

    CEFunction(Editor, Script)
    void SetModelUseLod0Bbox(const InstancedStaticModelComponentWriter& modelH, bool value);

    CEFunction(Editor, Script)
    bool GetModelUseLod0Bbox(const InstancedStaticModelComponentReader& modelH) const;

    CEFunction(Editor, Script)
    void SetModelDirty(const InstancedStaticModelComponentWriter& modelH);

    CEFunction(Editor, Script)
    UInt32 GetSubModelCount(const InstancedStaticModelComponentReader& modelH, UInt32 lodIndex) const
    {
        return static_cast<UInt32>(modelH->mLODModelProperties[lodIndex].mSubModelProperties.size());
    }

    /*CEFunction(Editor, Script)
    void SetInstanceCount(const InstancedStaticModelComponentWriter& modelH, UInt32 instanceCount);

    CEFunction(Editor, Script)
    UInt32 GetInstanceCount(const InstancedStaticModelComponentReader& modelH) const;

    CEFunction(Editor, Script)
    void ClearAllInstanceDatas(const InstancedStaticModelComponentWriter& modelH);

    CEFunction(Editor, Script)
    void ClearInstanceMemberData(const InstancedStaticModelComponentWriter& modelH, const std::string& name);

    CEFunction(Editor)
    void SetInstanceMemberData(const InstancedStaticModelComponentWriter& modelH, const std::string& name, UInt32 type, const void* data, UInt32 size, UInt32 stride);

    CEFunction(Editor)
    std::vector<std::string> GetInstanceMemberDataNames(const InstancedStaticModelComponentWriter& modelH) const;

    CEFunction(Editor)
    void GetInstanceMemberData(const InstancedStaticModelComponentWriter& modelH, const std::string& name, UInt32& outType, const void*& outData, UInt32& outSize, UInt32& outStride);*/

    bool SetInstanceDataResource(const InstancedStaticModelComponentWriter& modelH, const InstanceDataResourcePtr& instanceDataResourcePtr);

    CEFunction(Editor, Script, Cli)
    bool SetInstanceDataResourcePath(const InstancedStaticModelComponentWriter& modelH, const std::string& resourcePath);

    const InstanceDataResourcePtr& GetInstanceDataResource(const InstancedStaticModelComponentReader& modelH) const
    {
        return modelH->mInstanceDataResource;
    }

    CEFunction(Editor, Script, Cli)
    const std::string& GetInstanceDataResourcePath(const InstancedStaticModelComponentReader& modelH) const
    {
        static std::string emptyStr("");
        return modelH->mInstanceDataResource ? modelH->mInstanceDataResource->GetGuid_Str() : emptyStr;
    }
    CEFunction(Editor, Script)
    void SetGlobalScale(const InstancedStaticModelComponentWriter& modelH, float value,bool rebuildClusterTree = true);

    CEFunction(Editor, Script)
    float GetGlobalScale(const InstancedStaticModelComponentReader& modelH) const;


    CEFunction(Editor, Script)
    void SetRenderPercentage(const InstancedStaticModelComponentWriter& modelH, float value);

    CEFunction(Editor, Script)
    float GetRenderPercentage(const InstancedStaticModelComponentReader& modelH) const
    {
        return modelH->RenderPercentage;
    }


    const auto& GetChangeList() const
    {
        return mModelChangeList;
    }

    auto& GetChangeList()
    {
        return mModelChangeList;
    }

    // Composite bounding boxes of current mesh resources. Go to AABBSystem for cheaper bounding box with transform.
    BoundingBox GetCurrentBoundingBox(ecs::EntityID entity) const;

    void OnMeshChanged(MeshAssetDataResourcePtr meshAsset);

    void AddChangedEntity(const ecs::EntityID& entity)
    {
        mModelChangeList.EmplaceChangeData(entity);
    }

    // Intersection
    CEFunction(Editor, Script)
    void SetIntersection(const InstancedStaticModelComponentWriter& modelH, bool enable);

    CEFunction(Editor, Script)
    bool GetIntersection(const InstancedStaticModelComponentReader& modelH) const;

    void NotifyInstanceDataResourceChange(const resource::InstanceDataResource* instanceDataResource);

protected:
    virtual ~InstancedStaticModelSystemG();

    virtual void NotifyEvent(const SystemEventBase& event, UInt32& flag) override;

private:
    void UpdateAABB(ecs::EntityID entity);

private:
    InstancedStaticModelSystemR* mRenderSystem = nullptr;
    bool mIsRenderObjectOwner = true;
    InstancedStaticModelChangeList<ecs::EntityID> mModelChangeList;
    MaterialInterfacePtr mDefaultMaterial;
};

}   // namespace cross
