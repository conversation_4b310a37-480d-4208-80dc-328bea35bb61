#include "EnginePrefix.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "CECommon/Common/GlobalSystemDesc.h"
#include "CECommon/Utilities/ImguiwsConsole.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "Runtime/GameWorld/PrimitiveRenderSystemG.h"
#include "Runtime/GameWorld/RendererSystemG.h"
#include "Runtime/GameWorld/GameWindowSystem.h"
#include "Runtime/Interface/CrossEngineImp.h"

#include "RenderEngine/RenderMaterial.h"
#include "Resource/AssetStreaming.h"
#include "Resource/AsyncResource.h"
#include "RenderEngine/PrimitiveGenerator.h"
#include "Threading/RenderingThread.h"

#include "Resource/Material.h"
#include "Resource/Fx.h"
#include "Resource/ResourceManager.h"
#include "Resource/RenderTextureResource.h"
#include "Runtime/Input/Core/Public/SlateApplication.h"
#include "RenderPipelineSystemG.h"

namespace cross
{
namespace
{
    bool checkPrimitiveIsLine(PrimitiveData const* primitiveData)
    {
        return primitiveData->GetPrimitiveTopology() == PrimitiveTopology::LineList
            || primitiveData->GetPrimitiveTopology() == PrimitiveTopology::LineStrip
            || primitiveData->GetPrimitiveTopology() == PrimitiveTopology::LineListAdj
            || primitiveData->GetPrimitiveTopology() == PrimitiveTopology::LineStripAdj;
    }

    bool checkPrimitiveIsPoint(PrimitiveData const* primitiveData)
    {
        return primitiveData->GetPrimitiveTopology() == PrimitiveTopology::Point;
    }
}
PrimitiveRenderSystemG* PrimitiveRenderSystemG::CreateInstance()
{
    PrimitiveRenderSystemG* system = new PrimitiveRenderSystemG();
    return system;
}

PrimitiveRenderSystemG::PrimitiveRenderSystemG()
{
    mRenderSystem = PrimitiveRenderSystemR::CreateInstance();
}

PrimitiveRenderSystemG::~PrimitiveRenderSystemG()
{
    if (mGameWorld->GetWorldType() == WorldTypeTag::DefaultWorld)
    {
        WindowSystemG* windowSystem = EngineGlobal::GetEngine()->GetGlobalSystem<WindowSystemG>();
        windowSystem->Unsubscribe<ResolutionChangedEvent>(this);
    }

    if (mIsRenderObjectOwner && mRenderSystem)
    {
        mRenderSystem->Release();
    }
    mRenderSystem = nullptr;
}

void PrimitiveRenderSystemG::Release()
{
    delete this;
}

void PrimitiveRenderSystemG::OnBeginFrame(FrameParam* frameParam)
{
    mCurrentTextPos = gTopLeftTextPos;
}

void PrimitiveRenderSystemG::OnEndFrame(FrameParam* frameParam)
{
}

void PrimitiveRenderSystemG::OnFirstUpdate(FrameParam* frameParam) {  
    std::shared_ptr<ui::SDFFont> msdfFont = std::make_shared<ui::SDFFont>("EngineResource/Font/consola.json");
    std::shared_ptr<ui::Font> font = std::make_shared<ui::Font>(msdfFont);

    font->SetSize(16);
    auto fx = TypeCast<resource::Fx>(gAssetStreamingManager->LoadSynchronously("EngineResource/Shader/DebugTextFx.nda"));
    MaterialPtr textMaterial = TypeCast<resource::Material>(resource::Material::CreateMaterialInstance(fx));
    textMaterial->SetTexture("color_texture", msdfFont->mTexture);

    Float2 map_size{static_cast<float>(msdfFont->mTextureWidth), static_cast<float>(msdfFont->mTextureHeight)};
    textMaterial->SetFloat2("TextureSize", map_size.data());

    RegisterFontSet("Default", FontInfo{font,textMaterial});

    ResizeWindowSize();


    InGameTerminalManager::DeclareTerminalCommand("ToggleLogMode", this, [this]() { bOnlyLogToImgui = !bOnlyLogToImgui; });


    if (mGameWorld->GetWorldType() == WorldTypeTag::DefaultWorld)
    {
        WindowSystemG* windowSystem = EngineGlobal::GetEngine()->GetGlobalSystem<WindowSystemG>();
        windowSystem->SubscribeEvent<ResolutionChangedEvent>(this, 0);
    }
}

void PrimitiveRenderSystemG::LoadPresetMaterials()
{
    static const std::string colorPath = "EngineResource/Material/PrimitiveColorMaterial";
    static const std::string linePath = "EngineResource/Material/PureVertexColorMaterial";

    auto LoadMaterial = [this](bool isLineOrPoint, bool isTransparent, bool isSelfDepth)
    {
        std::string path = (isLineOrPoint ? linePath : colorPath) + (isTransparent ? "_Transparent" : "_Opaque") + (isSelfDepth ? "_SelfDepth.nda" : "_SceneDepth.nda");
        MaterialPtr material = TypeCast<resource::Material>(gAssetStreamingManager->LoadSynchronously(path));
        mPresetMaterials[PrimitiveRenderSystemR::GetPresetMaterialKey(isLineOrPoint, isTransparent, isSelfDepth)] = material;
    };
    
    LoadMaterial(false, false, false);
    LoadMaterial(true, false, false);
    LoadMaterial(false, true, false);
    LoadMaterial(false, false, true);
    LoadMaterial(true, true, false);
    LoadMaterial(false, true, true);
    LoadMaterial(true, false, true);
    LoadMaterial(true, true, true);

    mBeforeToneMappingPureVertexMaterial = TypeCast<resource::Material>(gAssetStreamingManager->LoadSynchronously("EngineResource/Material/PureVertexColorMaterial_Opaque_SceneDepth_BeforeToneMapping.nda"));
}

const MaterialPtr PrimitiveRenderSystemG::GetMaterial(bool isLineOrPoint, bool isTransparent, bool isSelfDepth)
{
    UInt8 materialKey = PrimitiveRenderSystemR::GetPresetMaterialKey(isLineOrPoint, isTransparent, isSelfDepth);
    
    if (auto it = mPresetMaterials.find(materialKey); it != mPresetMaterials.end())
    {
        return it->second;
    }
    AssertMsg(false, "Preset materials are not loaded.");
    return MaterialPtr();
}

void PrimitiveRenderSystemG::NotifyEvent(const SystemEventBase& event, UInt32& flag)
{
    GameSystemBase::NotifyEvent(event, flag);

    if (event.mEventType == ResolutionChangedEvent::sEventType)
    {
        const ResolutionChangedEvent& e = static_cast<const ResolutionChangedEvent&>(event);
        ResizeWindowSize();
    }

}

RenderSystemBase* PrimitiveRenderSystemG::GetRenderSystem()
{
    return mRenderSystem;
}
void PrimitiveRenderSystemG::NotifyAddRenderSystemToRenderWorld()
{
    mIsRenderObjectOwner = false;
    LoadPresetMaterials();
    DispatchRenderingCommandWithToken([renderSystem = mRenderSystem, safeMapPtr = &mPresetMaterials, BeforeToneMappingPureVertexMaterial = mBeforeToneMappingPureVertexMaterial]()
    {
        auto& presetMaterialMap = *safeMapPtr;
        for (auto [materialKey, material] : presetMaterialMap)
        {
            renderSystem->SetPresetMaterial(materialKey, TYPE_CAST(MaterialR*, material->GetRenderMaterial()));
        }
        renderSystem->SetBeforeToneMappingPureVertexMaterial(TYPE_CAST(MaterialR*, BeforeToneMappingPureVertexMaterial->GetRenderMaterial()));
    });
}
void PrimitiveRenderSystemG::EditorLogScreen(cross::IGameWorld* world, const char* message) 
{
    cross::GameWorld* gameWorld = (cross::GameWorld*)world;
    auto* primitiveRenderSys = gameWorld->GetGameSystem<cross::PrimitiveRenderSystemG>();
    primitiveRenderSys->LogScreen(message);
}
void PrimitiveRenderSystemG::DrawPrimitive(const PrimitiveData* primitiveData, const Double4x4A& worldTransformMatrix, MaterialPtr material)
    {
    Float3 tilePosition, offset;
    GetTileAndOffsetForAbsolutePosition(Double3{worldTransformMatrix.m30, worldTransformMatrix.m31, worldTransformMatrix.m32}, tilePosition, offset);
    Float4x4A relativeMatrix = AbsoluteMatrixToRelativeMatrix(tilePosition, worldTransformMatrix);
    DrawPrimitive(primitiveData, relativeMatrix, material, tilePosition);
}

void PrimitiveRenderSystemG::DrawPrimitive(const PrimitiveData* primitiveData, const Float4x4A& worldTransformMatrix, MaterialPtr material, const Float3& tilePosition)
{
    Assert(primitiveData->HasData());
    AddPrimitiveData(primitiveData, worldTransformMatrix, material, 0xffffffff, tilePosition);
}

void PrimitiveRenderSystemG::DrawPrimitive(const PrimitiveData* primitiveData, const Float4x4A& worldTransformMatrix, const PrimitiveLook& renderLook, const Float3& tilePosition)
{
    if (primitiveData->GetPrimitiveCount() == 0) return;
    const MaterialPtr material = GetMaterial(checkPrimitiveIsLine(primitiveData) || checkPrimitiveIsPoint(primitiveData), renderLook.vertexColor.a < 1.f, renderLook.depthSetting == PrimitiveDepth::SelfDepth);
    
    if (checkPrimitiveIsLine(primitiveData))
    {
        auto fx = material->GetFx();
        for (auto& pass : fx->GetAllPass())
        {
            auto rasterize = material->GetState(pass.first)->RasterizationStateDesc;
            rasterize.LineWidth = renderLook.lineWidth;
            material->SetRasterizerState(pass.first, rasterize);
        }
    }

    AddPrimitiveData(primitiveData, worldTransformMatrix, material, renderLook.vertexColor.GetHex(), tilePosition);
}

void PrimitiveRenderSystemG::DrawPoint(Float3 const& position, float scale, ColorRGBAf const& color, MaterialPtr material, Float3 const& tilePosition, bool isScreenSize)
{
    DispatchRenderingCommandWithToken([=, renderMaterial = TYPE_CAST(MaterialR*, material->GetRenderMaterial())]() 
    {
        mRenderSystem->BatchPoint(position, scale, color, renderMaterial, tilePosition, isScreenSize);
    });
}

void PrimitiveRenderSystemG::ResizeWindowSize() 
{
    float windowWidth = mWindowSize.x;
    float windowHeight = mWindowSize.y;  
    if (cross::EngineGlobal::GetSettingMgr()->GetAppStartUpType() == cross::AppStartUpType::AppStartUpTypeCrossEditor)
    {
        auto renderTexture = mGameWorld->GetGameSystem<RenderPipelineSystemG>()->GetEditorSceneViewTexture();
        if (renderTexture)
        {
            windowWidth = static_cast<float>(renderTexture->GetWidth());
            windowHeight = static_cast<float>(renderTexture->GetHeight());
        }
    }
    else
    {
        auto* windowSystem = static_cast<WindowSystemG*>(EngineGlobal::GetEngine()->GetGlobalSystemByID(WindowSystemG::GetDesc().mID));
        auto* window = windowSystem->GetAppGlobalWindow();
        windowWidth = static_cast<float>(window->GetWidth());
        windowHeight = static_cast<float>(window->GetHeight());
    }

    mWindowSize = {windowWidth, windowHeight};

    for (auto it = fontSet.begin(); it != fontSet.end(); it++)
    {
        it->second.textMaterial->SetFloat2("WindowSize", mWindowSize.data());
    }
}

static MaterialPtr _text_material;
static PrimitiveData _text_primitive;
static std::vector<UnicodeChar> _unicode;
static std::map<UInt32, MaterialPtr> _material_map;

void PrimitiveRenderSystemG::DrawScreenText(const Float2& char_origin, std::string content, const ColorRGBAf& color, UInt32 fontSize, const std::string fontType)
{
    //ResizeWindowSize();

    auto imguiConsole = EngineGlobal::GetEngine()->GetGlobalSystem<ImguiwsConsole>();
    imguiConsole->AddLog(content);
    if (!bOnlyLogToImgui)
    {
        DispatchRenderingCommandWithToken([renderSystem = mRenderSystem, char_origin, content = content, color, fontType, fontSize]() { renderSystem->DrawScreenText(char_origin, content, color, fontSize); });
    }
}

void PrimitiveRenderSystemG::DrawSceneText(Float3& origin, const SceneTextData& textData, float lineSpacePer)
{
    DispatchRenderingCommandWithToken([renderSystem = mRenderSystem, textData]() { renderSystem->DrawSceneText(textData); });
    // calculate LineSpace
    FontInfo fontInfo = fontSet.at(textData.fontType);
    origin += (fontInfo.font->mSDFFont->mLineHeight * textData.scale.y * lineSpacePer) * textData.tangent;
}
void PrimitiveRenderSystemG::RegisterFontSet(std::string fontType, const FontInfo fontInfo)
{
    if (fontSet.find(fontType) == fontSet.end())
    {
        fontSet.emplace(fontType, fontInfo);
    }
    
    DispatchRenderingCommandWithToken([renderSystem = mRenderSystem, fontType, fontInfo] { renderSystem->PrepareTextMap(fontType, fontInfo); });

    return;
}

void PrimitiveRenderSystemG::AddPrimitiveData(const PrimitiveData* primitiveData, const Float4x4A& worldTransformMatrix, MaterialPtr material, UInt32 vertexColor, const Float3& tilePosition)
{
    //PrimitiveDataBatch* properBatch = nullptr;
    // Copy index
    UInt32 indexDataSizeInByte = primitiveData->GetIndexCount() * (primitiveData->IsIndex32()? sizeof(UInt32) : sizeof(UInt16));
    FrameAllocator* curFrameAllocator = EngineGlobal::GetFrameParamMgr()->GetCurrentGameFrameParam()->GetFrameAllocator();
    auto indexDataCopy = curFrameAllocator->CreateFrameContainer<FixedSizeFrameDataLot>(FRAME_STAGE_GAME_RENDER, indexDataSizeInByte);
    indexDataCopy->PushBack(reinterpret_cast<UInt8*>(primitiveData->GetIndexArray()), indexDataSizeInByte);

    // Copy and modify vertex
    auto& layout = primitiveData->GetVertexLayout();
    UInt32 vertexDataSizeInByte = primitiveData->GetVertexCount() * layout.GetVertexStride();;
    auto vertexDataCopy = curFrameAllocator->CreateFrameContainer<FixedSizeFrameDataLot>(FRAME_STAGE_GAME_RENDER, vertexDataSizeInByte);
    vertexDataCopy->PushBack(primitiveData->GetVertexData(), vertexDataSizeInByte);

    UInt32 stride = layout.GetVertexStride();
    UInt8* toWriteVertexData = vertexDataCopy->GetData();
    for (UInt16 l = 0; l < layout.GetChannelCount(); l++)
    {
        if (vertexColor != 0xffffffff && layout.GetChannelLayout(l).mChannelName == VertexChannel::Color0)
        {
            UInt32 colorOffset = layout.GetChannelLayout(l).mOffset;
            for (UInt32 v = 0; v < primitiveData->GetVertexCount(); v++)
            {
                UInt32* colorV = reinterpret_cast<UInt32*>(toWriteVertexData + (v * stride + colorOffset));
                *colorV = vertexColor;
            }
        }
    }

    DispatchRenderingCommandWithToken([renderSystem = mRenderSystem, vertexDataCopy, indexDataCopy, 
        layout = primitiveData->GetVertexLayout(), topology = primitiveData->GetPrimitiveTopology(), primitiveCount = primitiveData->GetPrimitiveCount(),
        isIndex32 = primitiveData->IsIndex32(), renderMaterial = TYPE_CAST(MaterialR*, material->GetRenderMaterial()), tilePosition, worldTransformMatrix]()
    {
        renderSystem->BatchPrimitive(vertexDataCopy, indexDataCopy, layout, topology, primitiveCount, isIndex32, renderMaterial, tilePosition, worldTransformMatrix);
    });
}
}
