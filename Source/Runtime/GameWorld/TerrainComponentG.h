#pragma once

#include "ECS/Develop/Framework.h"
#include "Resource/TerrainResource.h"
#include "CECommon/Geometry/AABBTree.h"

#define TERRAIN_USE_ARRAY_GRID 0

namespace cross
{
    class PhysicsShape;
    struct TerrainComponentG : ecs::IComponent
    {
        CEComponentInternal(SystemType = TerrainSystemG)

        friend class TerrainSystemG;

        template<typename Type>
        using DataVector = std::vector<Type, ce_stl_allocator<Type>>;

        CEFunction(Reflect)
        static ENGINE_API ecs::ComponentDesc* GetDesc();

    private:
        struct TileNode
        {
            TerrainRenderProperties mRenderProperties;
            PhysicsGeometryTerrain mTerrainCollision;
            UInt32 mLodInfo;
            UInt32 mUsageHistory;
            UInt32 mSlotIndex;
            UInt32 mTouched : 1;
            UInt32 mInvalid : 1;
            UInt32 mAlwaysResident : 1;
            UInt32 mResident : 1;
            UInt32 mDefaultResource : 1;
            UInt32 mStreaming : 1;
            UInt32 mRequested : 1;
            UInt32 mDrawable : 1;
            UInt32 mVisible : 1;

#ifdef CROSSENGINE_EDITOR
            UInt32 mResidentLock : 1;
            UInt32 mEditorDataIsInitialized : 1;
            UInt32 mHeightmapDirty : 1;
            UInt32 mWeightTextureDirty : 1;
            BoundingBox mBoundingBox;
            std::vector<float> mHeightmap;
            std::vector<UInt32> mEncodeHeightmap;
            std::array<std::vector<UInt8>, NumMaxTerrainBlendLayers> mBlendLayerWeights;
#endif
        };

        struct TileLocator
        {
            UInt32 mBlockX : 16;
            UInt32 mBlockY : 16;
            UInt32 mBlockIndex : 16;
            UInt32 mLevel : 16;
            UInt32 mTileX : 16;
            UInt32 mTileY : 16;
            UInt32 mTileIndex;

            bool operator==(const TileLocator& other) const
            {
                return mBlockIndex == other.mBlockIndex && mLevel == other.mLevel && mTileIndex == other.mTileIndex;
                // assuming tightly packed memory layout
                //return std::memcmp(this, &other, sizeof(TileLocator)) == 0;
            }

            bool operator!=(const TileLocator& other) const
            {
                return !(*this == other);
            }
        };

        struct TileLocatorHasher
        {
            std::size_t operator()(const TileLocator& key) const
            {
                std::size_t hash = 17;
                hash = hash * 31 + std::hash<UInt32>()(key.mBlockIndex);
                hash = hash * 31 + std::hash<UInt32>()(key.mLevel);
                hash = hash * 31 + std::hash<UInt32>()(key.mTileIndex);
                return hash;
            }
        };

        struct ActiveTile
        {
            bool operator==(const ActiveTile& other) const
            {
                return mTile == other.mTile;
            }

            TerrainComponentG::TileLocator mTile;
            resource::TerrainTileAssetData mTileAssetData;
            UInt32 mFrameNumber;
        };

        struct ActiveTileHasher
        {
            std::size_t operator()(const ActiveTile& key) const
            {
                return TerrainComponentG::TileLocatorHasher()(key.mTile);
            }
        };

        struct TileRequestData
        {
            TerrainComponentG::TileLocator mTile;
            BoundingSphere mBound;
            float mDistance;
        };

        struct SlotIndexTable
        {
            void AddSlots(UInt32 num)
            {
                Assert(mNumSlots < (1U << 16U));
                while (num--)
                {
                    mAvailableSlots.push_back(mNumSlots++);
                }
            }

            bool HasNextSlot() const
            {
                return !mAvailableSlots.empty();
            }

            void ReleaseSlot(UInt32 slot)
            {
                mAvailableSlots.push_back(slot);
            }

            UInt32 AcquireNextSlot()
            {
                Assert(HasNextSlot());
                auto slot = mAvailableSlots.front();
                mAvailableSlots.pop_front();
                return slot;
            }

            void Clear()
            {
                mAvailableSlots.clear();
                mNumSlots = 0U;
            }

            std::list<UInt32> mAvailableSlots;
            UInt32 mNumSlots{};
        };

        struct TileRange
        {
            TerrainComponentG::TileLocator mTile;
            UInt2 mRangeX;
            UInt2 mRangeY;
            UInt2 mDimX;
            UInt2 mDimY;
        };

        struct CustomTerrainOverride
        {
            UInt64 mHandle;
            MaterialPtr mMaterial;
            TerrainRenderProperties mRenderProperties;
            UInt2 mRangeX;
            UInt2 mRangeY;
            UInt32 mHeightmapWidth;
            UInt32 mHeightmapHeight;
            DataVector<UInt32> mEncodedHeightmap;
            DataVector<TileRange> mTileRangeList;
            std::unordered_set<TileLocator, TileLocatorHasher> mModifiedTileList;
        };

#if TERRAIN_USE_ARRAY_GRID
        using Block = std::vector<std::vector<TileNode>>;
        using Grid = std::vector<Block>;
#else
        using Grid = std::unordered_map<TileLocator, TileNode, TileLocatorHasher>;
#endif
        bool mEnabled{ true };
        bool mTransformUpdated{ true };

        TerrainSurfaceType mSurfaceType;

        UInt32 mGridSizeX;
        UInt32 mGridSizeY;
        UInt32 mBlockSize;
        UInt32 mTileSize;
        UInt32 mTextureSize;
        float mTexelDensity;

        Grid mGrid;

        std::vector<std::string> mBaseColorTextureNameList;
        std::vector<std::string> mNormalTextureNameList;
        std::vector<std::string> mHMRATextureNameList;

        std::string mRootDataPath;
        std::string mHeightmapName;
        std::string mAlbedoTextureName;
        std::string mWeightTextureName;

        std::string mTerrainPath;
        TerrainResourcePtr mTerrainResourcePtr;

        MaterialInterfacePtr mMaterial;

        // persistent
        DataVector<ActiveTile> mAlwaysResidentTileList;
        DataVector<ActiveTile> mActiveTileList;
        DataVector<resource::TerrainTileAssetData> mPendingReleaseList;
        std::unordered_map<TerrainComponentG::TileLocator, std::pair<cross::PhysicsShape*, bool>, TerrainComponentG::TileLocatorHasher> mCollisionShapes;
        SlotIndexTable mLocalSlotIndexTable;
        UInt32 mDefaultResourceSlotIndex{ 0U };
        UInt32 mDefaultResourceSlotCount{ 0U };

        // per-frame
        DataVector<TileRequestData> mTileStreamingRequests;
        DataVector<TerrainComponentG::TileLocator> mDrawableTileList;
        DataVector<TerrainComponentG::TileLocator> mAddedTileList;
        DataVector<TerrainComponentG::TileLocator> mRemovedTileList;
        std::unordered_map<UInt64, CustomTerrainOverride> mCustomTerrains;

        std::unique_ptr<AABBTree<cross::TileIndex>> mTerrainAABBTree;
        std::unordered_map<TileIndex, AABBTree<std::string>> mTerrainTileAABBTreeMap;
        UInt32 mBlock = 16;
    };
}
