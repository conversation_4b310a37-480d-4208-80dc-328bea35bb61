#pragma once
#include "ECS/Develop/Framework/Types.h"
#include "CECommon/Common/GameSystemBase.h"
#include "CECommon/Common/WorldConst.h"
#include <Runtime/GameWorld/Prefab/PrefabManager.h>
#include "Runtime/GameWorld/Prefab/PrefabStreamingSystemG.h"

namespace cross
{
class PrefabManager;
class PrefabSystemG;

struct CEMeta(Editor) PrefabComponentG : ecs::IComponent
{
    CEFunction(Reflect)
    static ENGINE_API ecs::ComponentDesc* GetDesc();

    CE_Serialize_Deserialize

    CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Replace Tag"))
    std::string mReplaceTag = "";
};

enum PrefabStatus
{
    Loading = 0,
    Loaded,
    Removed
};

struct PrefabEventData
{
    std::string mPrefabPath;
    PrefabStatus mLoadingStatus;
    ecs::EntityID entity;
};

struct PrefabReplaceData
{
    ecs::EntityID   mEntity;
    std::string     mPrefabPath;
    int             mStage = 0;
    PrefabStreamingHandlePtr mStreamingHandle = nullptr;
};

struct PrefabChangeData
{
    ecs::EntityID           mEntity;
    std::string             mPrefabId;
    PrefabEntityChangedMap  mChangeMap;
    bool                    mUseNewGuid;
};

using PreafabChangedEvent = SystemEvent<PrefabEventData>;

// Prefab loading and saving
class PrefabSystemG final : public GameSystemBase, public SystemEventManager<PreafabChangedEvent>
{
    using ChangedList = std::unordered_set<CrossUUID>;
    using PrefabReplceDataList = std::vector<PrefabReplaceData>;
    using PrefabUpdateEntityList = std::unordered_set<ecs::EntityID>;
    using PrefabChangeDataList = std::vector<PrefabChangeData>;

    CESystemInternal(ComponentType = PrefabComponentG)
public:
    using PrefabComponentHandle = ecs::ComponentHandle<PrefabComponentG>;
    DEFINE_COMPONENT_READER_WRITER(PrefabComponentG, PrefabComponentGReader, PrefabComponentGWriter)
    // @brief CreateInstance
    CEFunction(Reflect)
    static PrefabSystemG* CreateInstance();
    // @brief Release
    virtual void Release() override;
    // @brief NotifyAddRenderSystemToRenderWorld
    virtual void NotifyAddRenderSystemToRenderWorld() override;
    // @brief NotifyEvent
    virtual void NotifyEvent(const SystemEventBase& event, UInt32& flag) override;

    CEFunction(Reflect)
    virtual void OnBuildUpdateTasks(FrameParam* frameParam) override;

    CEFunction(Reflect)
    virtual void OnBuildPreUpdateTasks(FrameParam* frameParam) override;

protected:
    // @brief Constructor
    PrefabSystemG();
    // @brief Destructor
    virtual ~PrefabSystemG();
    // @brief GetRenderSystem
    virtual RenderSystemBase* GetRenderSystem() override;
    // @brief OnBeginFrame
    virtual void OnBeginFrame(FrameParam* frameParam) override;

public:
    static PrefabComponentG CreatePrefabComponent();
    static SerializeNode SerializePrefabComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr);
    static void DeserializePrefabComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr);
    static void PostDeserializePrefabComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId);

public:
    // @brief CreatePrefabInstance
    ENGINE_API ecs::EntityID CreatePrefabInstance(const std::string& prefabPath, const ecs::EntityID& mount);
    // @brief AddPrefabInstance
    PrefabInsEntityDataPtr GetPrefabInstanceData(CrossUUID euid);
    // @brief AddPrefabInstance
    void LinkPrefabInstance(const ecs::EntityID& entity, bool recursive = false, bool remainTransfrom = false);
    // @brief AddPrefabInstance
    void UnlinkPrefabInstance(const ecs::EntityID& entity, bool recursive = false);
    // @brief UpdatePrefabInstance
    void UpdatePrefabInstanceData(const ecs::EntityID& entity, bool recursive = false, bool force = false);
    // @brief UpdatePrefabInstance
    void UpdatePrefabInstanceData(CrossUUID euid, PrefabInsEntityDataPtr insData);
    // @brief DeletePrefabInstanceData
    bool DeletePrefabInstanceData(CrossUUID euid);
    // @brief UpdatePrefabInstanceChildren
    bool UpdatePrefabEntityChildren(const ecs::EntityID& entity, std::string changedPrefabId, bool useNewEuid);
    // @brief UpdatePrefabEntity
    void UpdatePrefabEntity(const ecs::EntityID& entity, SerializeNode& newEntityJson);
    //@brief UpdateWorldPrefabInsEntities
    void UpdatePrefabInsEntities(const ecs::EntityID& entity, const std::string& prefabId, const PrefabEntityChangedMap& changedMap, bool useNewEuid);
    //@brief GetEntityInPrefabIns
    ecs::EntityID GetEntityInPrefabIns(const ecs::EntityID& entity, CrossUUID prefabEUID);
    //@brief SerializeEntityOnlyTranslationAndRotation
    SerializeNode SerializeEntityOnlyTranslationAndRotation(const ecs::EntityID& entity);
    // @brief UnlinkRemovedPrefabs
    bool UnlinkRemovedPrefabs(const ecs::EntityID& root);
    // @brief DispatchPrefabLoadingEvent
    void DispatchPrefabLoadingEvent(const ecs::EntityID& entity, const std::string path, PrefabStatus status);
    // @brief AddPrefabReplaceData
    CEFunction(Editor) void AddPrefabReplaceData(const std::string& tag, const std::string& prefabId);
    // @brief AddPrefabReplaceData
    void AddPrefabReplaceData(const ecs::EntityID& entity, const std::string& prefabId);
    // @brief PreUpdateReplacePrefab
    void PreUpdateReplacePrefab();
    // @brief HandleReplacePrefab
    void UpdateReplacePrefab();
    // @brief AddPrefabReplaceData
    void AddPrefabChangeData(const ecs::EntityID& entity, const std::string& prefabId, const PrefabEntityChangedMap& changeData, bool useNewGuid);
    // @brief PreUpdateReplacePrefab
    void PreUpdateChangePrefab();
#ifdef CROSSENGINE_EDITOR
    // @brief CreatePrefabProxy
    CEFunction(Editor) void CreatePrefabProxy(GameWorld* world);
    // @brief HandlePrefabProxy
    void UpdatePrefabProxy();
#endif

private:
    bool mIsRenderObjectOwner{ true };
    class PrefabSystemR* mPrefabSystemR{};

    // it also serve as world editor, if a diff is store in block file, it would store here
#ifdef USE_PREFAB_EDITOR
    std::map<CrossUUID, PrefabInsEntityDataPtr> mPrefabInsEntityMap;   // use for dynamic modify
#endif

#ifdef CROSSENGINE_EDITOR
    struct PrefabProxyCaptureData
    {
        ecs::EntityID mPrefabProxyEntity;
        class RenderPipelineG* mPrefabProxyRenderPipeline{};
        std::vector<UInt32> mReadbackData;
    };

    BoundingBox mBBox;

    UInt32 mMaxTileResolution{ 2048U };
    float mNumTexelsPerWorldUnitInCM{ 1.f };

    UInt32 mTotalWidthCount{};
    UInt32 mTotalHeightCount{};
        
    Float3 mTileOrigin{};
    float mTileWorldSize{};

    std::vector<PrefabProxyCaptureData> mPrefabProxyCapture;
    UInt32 mPendingCapture{};

    bool mProcessNext{ false };
#endif
    PrefabReplceDataList mReplaceDataList;
    PrefabChangeDataList mChangeDataList;
    PrefabUpdateEntityList mUpdateEntityList;
    friend class PrefabManager;
};
}
