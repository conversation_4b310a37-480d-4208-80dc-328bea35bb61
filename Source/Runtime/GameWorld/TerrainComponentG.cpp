#include "EnginePrefix.h"
#include "TerrainComponentG.h"
#include "TerrainSystemG.h"

namespace cross
{
    ecs::ComponentDesc* TerrainComponentG::GetDesc()
    {
        return EngineGlobal::GetECSFramework().CreateOrGetGameComponentDesc<TerrainComponentG>({ false, true, true, false},
            TerrainSystemG::SerializeTerrainComponent, TerrainSystemG::DeserializeTerrainComponent, TerrainSystemG::PostDeserializeTerrainComponent);
    }
};
