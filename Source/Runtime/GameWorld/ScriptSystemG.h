#pragma once
#include "ECS/Develop/Framework/Types.h"
#include "CrossBase/Math/CrossMath.h"
#include "Scripts/ScriptModule.h"
#include "CECommon/Common/GameSystemBase.h"
#include "Resource/ScriptResource.h"
#include "Resource/Resource.h"
#if SUPPROT_LUA
#include "ScriptEngine/ScriptEngine.h"
#endif
#include "Runtime/GameWorld/GameWorld.h"
#include "Runtime/GameWorld/ScriptComponentG.h"
#include "Runtime/Animation/Animator/Animator.h"
#include "Runtime/Scripts/EngineLayer/ScriptWorld.h"
#include "CEAnimation/IExecutableAnim.h"

namespace cross {
    enum class ScriptSystemState
    {
        Initial,
        Loading,
        Play,
        Finished,
    };
    struct ScriptEventData
    {
        ecs::EntityID mScriptEntity;
        ScriptEventType mEventType;

        ScriptEventData() = default;
        ScriptEventData(ecs::EntityID entity, ScriptEventType eventType)
            :mScriptEntity(entity), mEventType(eventType) {}
    };

    using ScriptEvent = SystemEvent<ScriptEventData>;
  
    class ENGINE_API ScriptSystemG final : public GameSystemBase
    {

        CESystemInternal(ComponentType = ScriptComponentG)
    public:
        using ScriptComponentHandle = ecs::ComponentHandle<ScriptComponentG>;
        DEFINE_COMPONENT_READER_WRITER(ScriptComponentG, ScriptComponentReader, ScriptComponentWriter);
    
        CEFunction(Reflect)
        static ScriptSystemG* CreateInstance();
        virtual void Release() override;
        /// Serialize
        static SerializeNode SerializeScriptComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr);
        static void DeserializeScriptComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr);
        static void PostDeserializeComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId);
        static void UpdateDeserializeComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId);
        static void GetResourceComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr, ResourcePtr resource);
        virtual void NotifyAddRenderSystemToRenderWorld() override {}

    protected:
        ScriptSystemG();
        virtual ~ScriptSystemG();
        virtual RenderSystemBase* GetRenderSystem() override { return nullptr; }

    public:
        CEFunction(Editor)
        bool ReflectScriptMember(const ScriptComponentWriter& script);
        CEFunction(Editor, Script)
        const std::string& GetScriptPath(const ScriptComponentReader& script);
        CEFunction(Editor, Script)
        bool SetScriptPath(const ScriptComponentWriter& handle, const std::string& scriptPath);

        CEFunction(Editor, Script)
        void SetScriptEnable(const ScriptComponentWriter& handle, bool bEnable);
        CEFunction(Editor, Script)
        bool GetScriptEnable(const ScriptComponentReader& handle) { return handle->mEnable; }



        CEFunction(Editor)
        const std::string& GetMemberName(const ScriptComponentReader& script, UInt32 index);
        const bool GetMemberIndex(const ScriptComponentReader& script, std::string name, UInt32& index);
        CEFunction(Editor)
        const UInt32 GetMemberCount(const ScriptComponentReader& script);
        CEFunction(Editor)
        const int GetMemberType(const ScriptComponentReader& script, std::string name);

        CEFunction(Editor)
        const bool GetBoolMember(const ScriptComponentReader& script, std::string name);
        CEFunction(Editor)
        const double GetNumberMember(const ScriptComponentReader& script, std::string name);
        CEFunction(Editor)
        const Float3 GetVector3Member(const ScriptComponentReader& script, std::string name);
        CEFunction(Editor)
        const Float2 GetVector2Member(const ScriptComponentReader& script, std::string name);
        CEFunction(Editor)
        const std::string GetStringMember(const ScriptComponentReader& script, std::string name);
        CEFunction(Editor)
        const ecs::EntityID GetEntityMember(const ScriptComponentReader& script, std::string name);

        CEFunction(Editor)
        void SetBoolMember(const ScriptComponentWriter& script, std::string name, bool value);
        CEFunction(Editor)
        void SetNumberMember(const ScriptComponentWriter& script, std::string name, double value);
        CEFunction(Editor)
        void SetVector3Member(const ScriptComponentWriter& script, std::string name, Float3 value);
        CEFunction(Editor)
        void SetVector2Member(const ScriptComponentWriter& script, std::string name, Float2 value);
        CEFunction(Editor)
        void SetStringMember(const ScriptComponentWriter& script, std::string name, std::string value);
        CEFunction(Editor)
        void SetEntityMember(const ScriptComponentWriter& script, std::string name, ecs::EntityID value);
#if SUPPORT_LUA
        CEFunction(Script)
        script::Local<script::Object> GetScriptInstance(const ScriptComponentReader& script);
        virtual void OnEndFrame(FrameParam* frameParam) override;
        CEFunction(Reflect)
        void OnBuildUpdateTasks(FrameParam* frameParam) override;
        CEFunction(Reflect)
        void OnBuildPreUpdateTasks(FrameParam* frameParam) override;
        CEFunction(Reflect)
        void OnBuildPostUpdateTasks(FrameParam* frameParam) override;

        virtual void NotifyEvent(const SystemEventBase& event, UInt32& flag) override;
    
        ////Script interface
        auto LoadScriptResource(std::string const& scriptPath) -> std::pair<ScriptPtr, script::Local<script::Object>>;

        bool LoadWorldScript(const std::string& path);
        script::Global<script::Object> GetWorldScriptInstance() { return mWorldScriptInstance; }
    
     
    
        /// unload not destroy the data for editor usage
    
        bool UnloadScript(const ScriptComponentWriter& handle);
        void CreateGameworldScriptInstance();
    
        ////Event Implementation
        template<typename... Args> 
        void OnEvent(ScriptEventType scriptEvent, ScriptComponentHandle script, Args&&... args)
        {
            if (script.Read()->mIsGameObject)
                return;

            auto object = script.Read()->mScriptInstance->get();
            script::Local<script::Value> func;
            switch (scriptEvent)
            {
            case ScriptEventType::Create:
                func = object.get("OnCreate");
                if (func.isFunction())
                {
                    func.asFunction().call(object, std::forward<Args>(args)...);
                }
                break;
            case ScriptEventType::PreUpdate:
                func = object.get("OnPreUpdate");
                if (func.isFunction() && mSystemState == ScriptSystemState::Play)
                {
                    func.asFunction().call(object, std::forward<Args>(args)...);
                }
                break;
            case ScriptEventType::Update:
                func = object.get("OnUpdate");
                if (func.isFunction() && mSystemState == ScriptSystemState::Play)
                {
                    func.asFunction().call(object, std::forward<Args>(args)...);
                }
                break;
            case ScriptEventType::PostUpdate:
                func = object.get("OnPostUpdate");
                if (func.isFunction() && mSystemState == ScriptSystemState::Play)
                {
                    func.asFunction().call(object, std::forward<Args>(args)...);
                }
                break;
            case ScriptEventType::Destroy:
                func = object.get("OnDestroy");
                if (func.isFunction())
                {
                    func.asFunction().call(object, std::forward<Args>(args)...);
                }
                break;
            case ScriptEventType::Collision:
                func = object.get("OnCollision");
                if (func.isFunction())
                {
                    func.asFunction().call(object, mGameWorld->GetElapsedTime(), std::forward<Args>(args)...);
                }
                break;
            case ScriptEventType::Message:
                func = object.get("OnMessage");
                if (func.isFunction())
                {
                    func.asFunction().call(object, std::forward<Args>(args)...);
                }
                break;
            case ScriptEventType::NetworkMessage:
                func = object.get("OnNetworkMessage");
                if (func.isFunction())
                {
                    func.asFunction().call(object, std::forward<Args>(args)...);
                }
                break;
            case ScriptEventType::BeginOverlap:
                func = object.get("OnBeginOverlap");
                if (func.isFunction())
                {
                    func.asFunction().call(object, std::forward<Args>(args)...);
                }
                break;
            case ScriptEventType::EndOverlap:
                func = object.get("OnEndOverlap");
                if (func.isFunction())
                {
                    func.asFunction().call(object, std::forward<Args>(args)...);
                }
                break;
            default:
                LOG_WARN("Script System do not support this event({})", scriptEvent);
                break;
            }
        }

        template<typename... Args>
        void OnEntityCollisionEvent(ScriptComponentHandle script, ecs::EntityID CollidedEntityID, const CollisionInfo& collisionInfo, Args&&... args)
        {
            if (script.Read()->mIsGameObject)
                return;

            auto scope = ScriptModule::Instance().Enter();
            try
            {
                auto reader = script.Read();
                if (reader->mScriptInstance)
                {
                    auto object = reader->mScriptInstance->get();
                    script::Local<script::Value> func = object.get("OnCollision");
                    if (func.isFunction())
                    {
                        auto* engine = script::EngineScope::currentEngine();
                        scripts::World* world = engine->getNativeInstance<scripts::World>(mGameworldInstance.getValue());
                        auto entityObject = engine->newNativeClass<scripts::Entity>(world->getScriptObject());
                        scripts::Entity* entity = engine->getNativeInstance<scripts::Entity>(entityObject);
                        entity->EntityID = CollidedEntityID;

                        auto status = script::Number::newNumber(static_cast<int>(collisionInfo.status));
                        if (collisionInfo.contactPoints)
                        {
                            auto hitResult = script::Array::newArray(collisionInfo.contactPoints->size());
                            for (const ContactPoint& contact : *collisionInfo.contactPoints)
                            {
                                auto oneHit = script::Object::newObject();
                                oneHit.set("contactPoint", engine->newNativeClass<scripts::Vector3>(contact.contactPoint.x, contact.contactPoint.y, contact.contactPoint.z));
                                oneHit.set("normal", engine->newNativeClass<scripts::Vector3>(contact.normal.x, contact.normal.y, contact.normal.z));
                                oneHit.set("impulse", engine->newNativeClass<scripts::Vector3>(contact.impulse.x, contact.impulse.y, contact.impulse.z));
                                hitResult.add(std::move(oneHit));
                            }
                            func.asFunction().call(object, mGameWorld->GetElapsedTime(), entityObject, hitResult, status, std::forward<Args>(args)...);
                        }
                        else
                        {
                            func.asFunction().call(object, mGameWorld->GetElapsedTime(), entityObject, status, std::forward<Args>(args)...);
                        }
                    }
                    else
                    {
                        LOG_WARN("Script [{}]: Script Event Function \"OnCollision\" not implemented or mismatches, it's maybe a spelling error.", reader->mScriptPath);
                    }
                }
                else
                {
                    LOG_ERROR("Script component of entity {} doesn't be set a script.", script.GetEntityID());
                }
            }
            catch (script::Exception const& e)
            {
                LOG_ERROR("{}\n{}", e.message(), e.stacktrace());
            }
            catch (std::exception const& e)
            {
                LOG_ERROR("{}", e.what());
            }
            catch (...)
            {
                LOG_ERROR("lua other exception:{}", GetScriptPath(script.Read()));
            }
        }
        ////// Animation Notify Functions Begin
        void OnCallBackEvent(anim::Animator const* inAnimator, const std::string& callback)
        {
            auto entity = inAnimator->GetOwner();
            const auto& scriptHandle = mGameWorld->GetComponent<ScriptComponentG>(entity);
    
            if (!scriptHandle.Read()->mOnCreateFinished)
                return;
    
            auto scope = ScriptModule::Instance().Enter();
            try
            {
                auto object = scriptHandle.Read()->mScriptInstance->get();
    
                script::Local<script::Value> func;
                func = object.get(callback);
                if (func.isFunction())
                    func.asFunction().call(object);
            }
            catch (script::Exception const& e)
            {
                LOG_ERROR("ScriptEngine Exception:{}\n{}", e.message(), e.stacktrace());
            }
            catch (std::exception const& e)
            {
                LOG_ERROR("std exception:{}", e.what());
            }
            catch (...)
            {
                LOG_ERROR("lua other exception:{}", GetScriptPath(scriptHandle.Read()));
            }
        }

        bool OnJumpSectionJudge(anim::Animator const* inAnimator, anim::IExecutableAnim const* inJudger, const std::string& judgeFuncStr)
        { 
            auto entity = inAnimator->GetOwner();
            const auto& scriptHandle = mGameWorld->GetComponent<ScriptComponentG>(entity);

            if (!scriptHandle.Read()->mOnCreateFinished)
                return false;

            auto scope = ScriptModule::Instance().Enter();
            try
            {
                auto object = scriptHandle.Read()->mScriptInstance->get();

                script::Local<script::Value> func;
                func = object.get(judgeFuncStr);
                if (func.isFunction())
                {
                    auto re = func.asFunction().call(object);
                    Assert(re.isBoolean() || re.isNumber());

                    return re.asBoolean().value();
                }
            }    
            catch (script::Exception const& e)
            {
                LOG_ERROR("ScriptEngine Exception:{}\n{}", e.message(), e.stacktrace());
            }
            catch (std::exception const& e)
            {
                LOG_ERROR("std exception:{}", e.what());
            }
            catch (...)
            {
                LOG_ERROR("lua other exception:{}", GetScriptPath(scriptHandle.Read()));
            }

            return false;                
        }
        ////// Animation Notify Functions End
    
        template<typename... Args> 
        void OnEntityEvent(ScriptComponentHandle script, ScriptEventType eventtype, Args&&... args)
        {
            auto scope = ScriptModule::Instance().Enter();
            
            try
            {
                if (script.Read()->mScriptInstance)
                {
                    OnEvent(eventtype, script, std::forward<Args>(args)...);
                }
                else
                {
                    LOG_ERROR("Script component of entity {} doesn't be set a script.", script.GetEntityID());
                }
            }
            catch (script::Exception const& e)
            {
                //LOG_ERROR("ScriptEngine Exception:{}\n{}", e.message(), e.stacktrace());
            }
            catch (std::exception const& e)
            {
                LOG_ERROR("std exception:{}", e.what());
            }
            catch (...)
            {
                LOG_ERROR("lua other exception:{}", GetScriptPath(script.Read()));
            }
        }
    
        template<typename... Args>
        void OnEntityOverlapEvent(ScriptComponentHandle script, ScriptEventType eventtype, ecs::EntityID OverlapEntityID, Args&&... args)
        {
            if (script.Read()->mIsGameObject)
                return;

            auto scope = ScriptModule::Instance().Enter();

            try
            {
                if (script.Read()->mScriptInstance)
                {
                    auto object = script.Read()->mScriptInstance->get();
                    script::Local<script::Value> func;
                    auto* engine = script::EngineScope::currentEngine();
                    scripts::World* world = engine->getNativeInstance<scripts::World>(mGameworldInstance.getValue());
                    auto entityObject = engine->newNativeClass<scripts::Entity>(world->getScriptObject());
                    scripts::Entity* entity = engine->getNativeInstance<scripts::Entity>(entityObject);
                    entity->EntityID = OverlapEntityID;
                    if (eventtype == ScriptEventType::BeginOverlap) 
                    {
                        func = object.get("OnBeginOverlap");
                    }
                    else if (eventtype == ScriptEventType::EndOverlap) 
                    {
                        func = object.get("OnEndOverlap");
                    }
                    if (func.isFunction())
                    {
                        func.asFunction().call(object, entityObject, std::forward<Args>(args)...);
                    }    
                }
                else
                {
                    LOG_ERROR("Script component of entity {} doesn't be set a script.", script.GetEntityID());
                }
            }
            catch (script::Exception const& e)
            {
                LOG_ERROR("ScriptEngine Exception:{}\n{}", e.message(), e.stacktrace());
            }
            catch (std::exception const& e)
            {
                LOG_ERROR("std exception:{}", e.what());
            }
            catch (...)
            {
                LOG_ERROR("lua other exception:{}", GetScriptPath(script.Read()));
            }
        }


        template<typename Type> 
        inline bool GetPropertyByType(const ScriptComponentReader& script, UInt32 index, Type& result)
        {
            auto& props = script->mProperties;
            if (props.size() <= index)
                return false;
    
            auto& propdata = props[index].mPropData;
            if constexpr (std::is_same_v<Type, std::string>)
            {
                constexpr size_t typeindex = 0;
                if (std::get_if<typeindex>(&propdata))
                {
                    result = *std::get_if<typeindex>(&propdata);
                    return true;
                }
            }
            else if constexpr (std::is_same_v<Type, bool>)
            {
                constexpr size_t typeindex = 1;
                if (std::get_if<typeindex>(&propdata))
                {
                    result = *std::get_if<typeindex>(&propdata);
                    return true;
                }
            }
            else if constexpr (std::is_same_v<Type, double>)
            {
                constexpr size_t typeindex = 2;
                if (std::get_if<typeindex>(&propdata))
                {
                    result = *std::get_if<typeindex>(&propdata);
                    return true;
                }
            }
            else if constexpr (std::is_same_v<Type, cross::Float2>)
            {
                constexpr size_t typeindex = 3;
                if (std::get_if<typeindex>(&propdata))
                {
                    result = *std::get_if<typeindex>(&propdata);
                    return true;
                }
            }
            else if constexpr (std::is_same_v<Type, cross::Float3>)
            {
                constexpr size_t typeindex = 4;
                if (std::get_if<typeindex>(&propdata))
                {
                    result = *std::get_if<typeindex>(&propdata);
                    return true;
                }
            }
            else if constexpr (std::is_same_v<Type, cross::ecs::EntityID>)
            {
                constexpr size_t typeindex = 5;
                if (std::get_if<typeindex>(&propdata))
                {
                    result = *std::get_if<typeindex>(&propdata);
                    return true;
                }
            }
    
            return false;
        }
        // TODO(unknown):Level Script Event

        template<typename... Args>
        void OnCurveControllerEvent(ecs::EntityID CurveOwnerEntityID, CurveControllerEventType type, Args&&... args)
        {
            const auto& scriptHandle = mGameWorld->GetComponent<ScriptComponentG>(CurveOwnerEntityID);

            if (!scriptHandle.IsValid() || !scriptHandle.Read()->mOnCreateFinished)
                return;

            auto scope = ScriptModule::Instance().Enter();
            try
            {
                auto object = scriptHandle.Read()->mScriptInstance->get();

                script::Local<script::Value> func;
                switch (type)
                {
                case CurveControllerEventType::Start:
                    func = object.get("OnCurveStart");
                    break;
                case CurveControllerEventType::End:
                    func = object.get("OnCurveEnd");
                    break;
                case CurveControllerEventType::Loop:
                    func = object.get("OnCurveLoop");
                    break;
                case CurveControllerEventType::ScriptValueUpdate:
                    func = object.get("OnCurveScriptValueUpdate");
                    break;
                case CurveControllerEventType::SpawnSpotEnd:
                    func = object.get("OnSpawnSpotEnd");
                    break;
                case CurveControllerEventType::None:
                default:
                    break;
                }

                if (func.isFunction())
                    func.asFunction().call(object, std::forward<Args>(args)...);
            }
            catch (script::Exception const& e)
            {
                LOG_ERROR("ScriptEngine Exception:{}\n{}", e.message(), e.stacktrace());
            }
            catch (std::exception const& e)
            {
                LOG_ERROR("std exception:{}", e.what());
            }
            catch (...)
            {
                LOG_ERROR("lua other exception:{}", GetScriptPath(scriptHandle.Read()));
            }
        }

        void DispatchScriptEvent();
        void NotifyScriptEvent(const ecs::EntityID entity, ScriptEventType type);

        std::string GetMessage(const ScriptComponentReader& script);

        void CallCunstomFunction(const ScriptComponentReader& script, const std::string& functionName);

    
    private:
        void ClearTick();
        static void SetupProp(script::Global<script::Object>& gameworldInstance, const ScriptComponentReader& scriptComp);
    
        // world script
        std::string mWorldScriptPath;
        ScriptPtr mWorldScriptRes;
        script::Global<script::Object> mWorldScriptInstance;   // The world script object
        // world instance
        script::Global<script::Object> mGameworldInstance;   // The game world export in script
        script::Global<script::Object> mInputInstance;
        ScriptSystemState mSystemState = ScriptSystemState::Initial;
        EventQueue<ScriptEvent> mScriptEventQueue;
     #endif
    };
    /// TODO gloabl instance object
}   // namespace cross
