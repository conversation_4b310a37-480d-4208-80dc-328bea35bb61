#include "EnginePrefix.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/SettingsManager.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "CECommon/Common/WorldConst.h"
#include "ECS/Develop/Framework.h"
#include "Runtime/GameWorld/RenderPropertySystemG.h"
#include "Runtime/GameWorld/ModelSystemG.h"
#include "Resource/Material.h"
#include "RenderEngine/RenderPropertySystemR.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "Threading/RenderingThread.h"

namespace cross {

ecs::ComponentDesc* RenderPropertyComponentG::GetDesc()
{
    return EngineGlobal::GetECSFramework().CreateOrGetGameComponentDesc<cross::RenderPropertyComponentG>(
        { false, true, true },
        &RenderPropertySystemG::SerializeComponent, 
        RenderPropertySystemG::DeserializeComponent, 
        RenderPropertySystemG::PostDeserializeComponent, 
        RenderPropertySystemG::UpdateComponent);
}

SerializeNode RenderPropertySystemG::SerializeComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr)
{
    SerializeNode json;
    auto componetPtr = static_cast<RenderPropertyComponentG*>(componentPtr);
    json["mCullingProperty"] = (UInt32)componetPtr->mCullingData.mCullingProperty;
    // json["mHide"] = componetPtr->mCullingData.mHide;
    json["mLayerIndex"] = componetPtr->mCullingData.mLayerIndex;

    SerializeNode RenderEffect;
    RenderEffect["RuntimeEffectMask"] = componetPtr->mRenderEffect.mRuntimeEffectMask & FixedRenderEffectMask; // Only serialize fixed effect
    json["RenderEffect"] = std::move(RenderEffect);
    json["mNeedVoxelized"] = componetPtr->mNeedVoxelized;
    return json;
}

void RenderPropertySystemG::DeserializeComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr)
{
    if (json.IsNull())
    {
        LOG_WARN("RenderProperty's deserialize json is null");
        return;
    }

    auto renderPropertyComp = static_cast<RenderPropertyComponentG*>(componentPtr);
    if (json.IsObject())
    {
        renderPropertyComp->mCullingData.mCullingProperty = static_cast<CullingProperty>(json["mCullingProperty"].AsInt32());
        // renderPropertyComp->mCullingData.mHide = json["mHide"].AsBoolean();
        renderPropertyComp->mCullingData.mLayerIndex = json.Value("mLayerIndex", 0u);

        if (json.HasMember("RenderEffect"))
        {
            renderPropertyComp->mRenderEffect.mRuntimeEffectMask = static_cast<RenderEffectTag>(json["RenderEffect"].Value("RuntimeEffectMask", ToUnderlying(DefaultRenderEffectMask)));
        }
        if (json.HasMember("mNeedVoxelized"))
        {
            renderPropertyComp->mNeedVoxelized = json["mNeedVoxelized"].AsBoolean();
        }
    }
}

void RenderPropertySystemG::PostDeserializeComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId)
{
    // set data again to sent info to render
    auto renderPropertySys = gameWorld->GetGameSystem<RenderPropertySystemG>();
    auto renderPropertyHandle = gameWorld->GetComponent<RenderPropertyComponentG>(entityId);
    auto cullingProperty = renderPropertyHandle.Read()->mCullingData.mCullingProperty;
    auto needVoxelized = renderPropertyHandle.Read()->mNeedVoxelized;
    renderPropertySys->SetCullingProperty(renderPropertyHandle.Write(), cullingProperty);
    renderPropertySys->SetNeedVoxelized(renderPropertyHandle.Write(), needVoxelized);
    DispatchRenderingCommandWithToken([renderPropertySystem = renderPropertySys->GetRenderSystem(), entity = entityId, renderEffect = renderPropertyHandle.Read()->mRenderEffect,
                                       layerIndex = renderPropertyHandle.Read()->mCullingData.mLayerIndex] ()
    {
        static_cast<RenderPropertySystemR*>(renderPropertySystem)->SetRenderEffect(entity, renderEffect);
        static_cast<RenderPropertySystemR*>(renderPropertySystem)->SetLayerIndex(entity, layerIndex);
    });
}
void RenderPropertySystemG::RenderProperty_SetLodSelected(cross::IGameWorld* world, UInt64 entity, SInt32 lodIndex)
{
    cross::ecs::EntityID entityID = cross::ecs::EntityID(entity);
    cross::GameWorld* gameWorld = static_cast<cross::GameWorld*>(world);
    auto handle = gameWorld->GetComponent<cross::RenderPropertyComponentG>(entityID);
    auto renderPropertyH = gameWorld->GetComponent<cross::RenderPropertyComponentG>(entityID);
    auto renderPropertySystem = gameWorld->GetGameSystem<cross::RenderPropertySystemG>();

    renderPropertySystem->SetLodSelected(renderPropertyH.Write(), lodIndex);
}
void RenderPropertySystemG::UpdateComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId) 
{
    if (json.IsObject())
    {
        auto cullingProp = static_cast<CullingProperty>(json["mCullingProperty"].AsInt32());
        // renderPropertyComp->mCullingData.mHide = json["mHide"].AsBoolean();
        UInt32 effectMask = 0;
        if (json.HasMember("RenderEffect"))
        {
            effectMask = json["RenderEffect"].Value("RuntimeEffectMask", 0);
        }
        bool needVoxelized = true;
        if (json.HasMember("mNeedVoxelized"))
        {
            needVoxelized = json["mNeedVoxelized"].AsBoolean();
        }
        auto sys = gameWorld->GetGameSystem<RenderPropertySystemG>();
        auto renderProperty = gameWorld->GetComponent<RenderPropertyComponentG>(entityId);
        auto writer = renderProperty.Write();
        sys->SetCullingProperty(writer, cullingProp);
        sys->SetRuntimeRenderEffect(writer, effectMask);
        sys->SetNeedVoxelized(writer, needVoxelized);
    }
}
RenderPropertySystemG* RenderPropertySystemG::CreateInstance()
{
    return new RenderPropertySystemG();
}

void RenderPropertySystemG::Release()
{
    delete this;
}

void RenderPropertySystemG::OnBeginFrame(FrameParam* frameParam) 
{
    mCullingDataChangeList.BeginFrame(frameParam, FRAME_STAGE_GAME);
}

void RenderPropertySystemG::OnEndFrame(FrameParam* frameParam) 
{
}

void RenderPropertySystemG::OnEntityPropChange(ecs::EntityID entity, ecs::EntityDescFlags::EntityProps prop, bool bValue) 
{
    if (prop == ecs::EntityDescFlags::Visibility)
    {
        auto renderPropertyH = mGameWorld->GetComponent<cross::RenderPropertyComponentG>(entity);
        if (renderPropertyH.IsValid())
            SetHide(renderPropertyH.Write(), !bValue);
    }
}

void RenderPropertySystemG::NotifyAddRenderSystemToRenderWorld()
{
    mIsRenderObjectOwner = false;
}

RenderPropertySystemG::RenderPropertySystemG()
{
    mRenderPropertySystem = RenderPropertySystemR::CreateInstance();
}

RenderPropertySystemG::~RenderPropertySystemG()
{
    if (mIsRenderObjectOwner)
    {
        mRenderPropertySystem->Release();
    }
    mRenderPropertySystem = nullptr;
}

RenderSystemBase* RenderPropertySystemG::GetRenderSystem()
{
    return mRenderPropertySystem;
}

void RenderPropertySystemG::SetLayerIndex(const GameRenderPropertyWriter& handle, UInt32 layerIndex)
{
    if (LayerSetting::IsLayerIndexValid(layerIndex))
    {
        handle->mCullingData.mLayerIndex = layerIndex;

        if (mEnableCullingDataList)
        {
            mCullingDataChangeList.EmplaceChangeData(handle.GetEntityID(), handle->mCullingData);
        }

        DispatchRenderingCommandWithToken([renderPropertySystem = mRenderPropertySystem, entity = handle.GetEntityID(), layerIndex] 
        { 
            renderPropertySystem->SetLayerIndex(entity, layerIndex); 
        });
    }
}

void RenderPropertySystemG::SetLayerName(const GameRenderPropertyWriter& handle, const std::string& layerName) 
{
    const auto& layerSetting = EngineGlobal::GetSettingMgr()->GetLayerSetting();
    
    SInt32 layerIndex = layerSetting.GetLayerIndex(layerName);
    if (layerIndex >= 0)
    {
        SetLayerIndex(handle, layerIndex);
    }
}

const std::string& RenderPropertySystemG::GetLayerName(const GameRenderPropertyReader& handle) const
{
    const auto& layerSetting = EngineGlobal::GetSettingMgr()->GetLayerSetting();

    return layerSetting.GetLayerInfo(GetLayerIndex(handle)).LayerName;
}

UInt32 RenderPropertySystemG::GetLayerIndex(const GameRenderPropertyReader& handle) const
{
    const auto& layerSetting = EngineGlobal::GetSettingMgr()->GetLayerSetting();

    if (!layerSetting.IsLayerAllocated(handle->mCullingData.mLayerIndex))
    {
        handle->mCullingData.mLayerIndex = 0;
    }

    return handle->mCullingData.mLayerIndex;
}

void RenderPropertySystemG::SetCullingProperty(const GameRenderPropertyWriter& handle, CullingProperty cullingProperty)
{
    handle->mCullingData.mCullingProperty = cullingProperty;
    if (mEnableCullingDataList)
        mCullingDataChangeList.EmplaceChangeData(handle.GetEntityID(), handle->mCullingData);

    DispatchRenderingCommandWithToken([renderPropertySystem = mRenderPropertySystem, entity = handle.GetEntityID(), cullingProperty] 
    { 
        renderPropertySystem->SetCullingProperty(entity, cullingProperty); 
    });
}

void RenderPropertySystemG::SetHide(const GameRenderPropertyWriter& handle, bool hide)
{
    handle->mCullingData.mHide = hide;
    if (mEnableCullingDataList)
        mCullingDataChangeList.EmplaceChangeData(handle.GetEntityID(), handle->mCullingData);

    DispatchRenderingCommandWithToken([renderPropertySystem = mRenderPropertySystem, entity = handle.GetEntityID(), hide] 
    { 
        renderPropertySystem->SetHide(entity, hide); 
    });
}

void RenderPropertySystemG::SetHideInGame(const GameRenderPropertyWriter& handle, bool hideInGame)
{
    handle->mCullingData.mHideInGame = hideInGame;
    if (mEnableCullingDataList)
        mCullingDataChangeList.EmplaceChangeData(handle.GetEntityID(), handle->mCullingData);

    DispatchRenderingCommandWithToken([renderPropertySystem = mRenderPropertySystem, entity = handle.GetEntityID(), hideInGame] 
    { 
        renderPropertySystem->SetHideInGame(entity, hideInGame); 
    });
}

void RenderPropertySystemG::AddRuntimeRenderEffect(const GameRenderPropertyWriter& handle, RenderEffectTag renderEffectTag)
{
    handle->mRenderEffect.mRuntimeEffectMask |= renderEffectTag;

    DispatchRenderingCommandWithToken([renderPropertySystem = mRenderPropertySystem, entity = handle.GetEntityID(), renderEffect = handle->mRenderEffect] 
    { 
        renderPropertySystem->SetRenderEffect(entity, renderEffect); 
    });
}

void RenderPropertySystemG::RemoveRuntimeRenderEffect(const GameRenderPropertyWriter& handle, RenderEffectTag renderEffectTag)
{
    handle->mRenderEffect.mRuntimeEffectMask &= ~renderEffectTag;

    DispatchRenderingCommandWithToken([renderPropertySystem = mRenderPropertySystem, entity = handle.GetEntityID(), renderEffect = handle->mRenderEffect] 
    { 
        renderPropertySystem->SetRenderEffect(entity, renderEffect); 
    });
}

void RenderPropertySystemG::SetRuntimeRenderEffect(const GameRenderPropertyWriter& handle, UInt32 flags)
{
    handle->mRenderEffect.mRuntimeEffectMask = static_cast<RenderEffectTag>(flags);

    DispatchRenderingCommandWithToken([renderPropertySystem = mRenderPropertySystem, entity = handle.GetEntityID(), renderEffect = handle->mRenderEffect] 
    { 
        renderPropertySystem->SetRenderEffect(entity, renderEffect); 
    });
}

void RenderPropertySystemG::SetLodSelected(const GameRenderPropertyWriter& handle, SInt32 lodIndex)
{
    handle->mLodSelected = lodIndex;

    DispatchRenderingCommandWithToken([renderPropertySystem = mRenderPropertySystem, entity = handle.GetEntityID(), lodIndex = lodIndex] 
    { 
        renderPropertySystem->SetLodSelected(entity, {lodIndex}); 
    });
}

void RenderPropertySystemG::SetNeedVoxelized(const GameRenderPropertyWriter& handle, bool needVoxelized)
{
    // auto HasVoxelizePass = [&]()
    // {
    //     auto modelHandle = mGameWorld->GetComponent<ModelComponentG>(handle.GetEntityID());
    //     if (modelHandle.IsValid())
    //     {
    //         auto modelSys = mGameWorld->GetGameSystem<ModelSystemG>();
    //         auto material = modelSys->GetModelMaterial(modelHandle.Read(), 0, 0);   // get default model material
    //         if (material)
    //         {
    //             auto fx = material->GetFx();
    //             if (fx)
    //             {
    //                 return fx->HasPass("VoxelizePass");
    //             }
    //         }
    //     }
    //     return false;
    // };

    // Note(scolu): HasVoxelizePass is Deprecated for when copying an entity some component attributes(struct members in component) may not copied correctly, e.g. ModelSystemG::mMainModel
    //  here we just copy mNeedVoxelized field directly, and judge whether if an entity has VoxelizePass when voxelize itself
    bool realNeedVoxelized = needVoxelized;

    handle->mNeedVoxelized = realNeedVoxelized;
    DispatchRenderingCommandWithToken([renderPropertySystem = mRenderPropertySystem, entity = handle.GetEntityID(), realNeedVoxelized = realNeedVoxelized] 
    { 
        renderPropertySystem->SetNeedVoxelized(entity, realNeedVoxelized); 
    });
}

RenderPropertyComponentG RenderPropertySystemG::GreateComponentByEffectTag(RenderEffectTag renderEffectTag) 
{
    RenderPropertyComponentG comp;
    comp.mRenderEffect.mRuntimeEffectMask = renderEffectTag;
    return std::move(comp);
}

}   // namespace cross
