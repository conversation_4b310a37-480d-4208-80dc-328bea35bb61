#pragma once

#include "AudioEngine.h"

namespace cross {
class ENGINE_API AudioEngineManager : public AudioEngine
{
public:
    // constructor and destructor
    AudioEngineManager() = default;
    virtual ~AudioEngineManager() = default;

    // setup sound engine
    inline void SetAudioEngine(AudioEngine * const sound_engine) { mAudioEngine = sound_engine; }
    inline AudioEngine* GetAudioEngine() { return mAudioEngine; }
    
    // ID and string convertion
    virtual inline ID StringToID(Char const* const str) override
    {
        if (mAudioEngine)
            return mAudioEngine->StringToID(str);
        return 0;
    }

    // configuration
    virtual Result LanguageSet(Char const * const language_name) override
    {
        if (mAudioEngine)
            return mAudioEngine->LanguageSet(language_name);
        return 0;
    }

    virtual Result RootpathSet(Char const * const rootpath) override
    {
        if (mAudioEngine)
            return mAudioEngine->RootpathSet(rootpath);
        return 0;
    }

    // processing
    virtual Result Begin() override 
    {
        if (mAudioEngine)
            return mAudioEngine->Begin();
        return 0;
    }

    virtual Result End() override
    {
        if (mAudioEngine)
            return mAudioEngine->End();
        return 0;
    }

    // bank API
    virtual inline Result BankLoad(Char const * const bank_name) override
    {
        if (mAudioEngine)
            return mAudioEngine->BankLoad(bank_name);
        return 0;
    }

    virtual inline Result BankUnload(Char const * const bank_name) override
    {
        if (mAudioEngine)
            return mAudioEngine->BankUnload(bank_name);
        return 0;
    }

    // object API
    virtual inline Result ObjectRegister(ObjectID const object_id) override
    {
        if (mAudioEngine)
            return mAudioEngine->ObjectRegister(object_id);
        return 0;
    }

    virtual inline Result ObjectUnregister(ObjectID const object_id) override
    {
        if (mAudioEngine)
            return mAudioEngine->ObjectUnregister(object_id);
        return 0;
    }

    virtual inline Result ObjectSetPose(ObjectID const object_id, Float3 const & position, Float3 const & forward, Float3 const & up) override
    {
        if (mAudioEngine)
            return mAudioEngine->ObjectSetPose(object_id, position, forward, up);
        return 0;
    }

    virtual inline Result ObjectSetPose(ObjectID const object_id, Double3 const & position, Double3 const & forward, Double3 const & up) override
    {
        if (mAudioEngine)
            return mAudioEngine->ObjectSetPose(object_id, position, forward, up);
        return 0;
    }

    virtual inline Result ObjectSetListeners(ObjectID const object_id, ObjectID const * const listeners, Size const num_listeners) override
    {
        if (mAudioEngine)
            return mAudioEngine->ObjectSetListeners(object_id, listeners, num_listeners);
        return 0;
    }

    virtual inline Result ObjectAddListener(ObjectID const object_id, ObjectID const listener) override
    {
        if (mAudioEngine)
            return mAudioEngine->ObjectAddListener(object_id, listener);
        return 0;
    }

    virtual inline Result ObjectRemoveListener(ObjectID const object_id, ObjectID const listener) override
    {
        if (mAudioEngine)
            return mAudioEngine->ObjectRemoveListener(object_id, listener);
        return 0;
    }

    virtual Result ObjectSetVolume(ObjectID const object_id, ObjectID const listener, Float const volume) const override
    {
        if (mAudioEngine)
            return mAudioEngine->ObjectSetVolume(object_id, listener, volume);
        return 0;
    }

    // event API
    virtual inline Result EventPrepare(EventID const * const event_ids, Size const size) override
    {
        if (mAudioEngine)
            return mAudioEngine->EventPrepare(event_ids, size);
        return 0;
    }

    virtual inline Result EventUnprepare(EventID const* const event_ids, Size const size) override
    {
        if (mAudioEngine)
            return mAudioEngine->EventUnprepare(event_ids, size);
        return 0;
    }

    virtual inline Result EventPost(ObjectID const object_id, EventID const event_id, EventPtr* const out_event_ptr
        , EndCallback const & end_callback = nullptr
        , MarkerCallback const & marker_callback = nullptr
        , void* const cookie = nullptr) override
    {
        if (mAudioEngine)
            return mAudioEngine->EventPost(object_id, event_id, out_event_ptr, end_callback, marker_callback, cookie);
        return 0;
    }

    virtual inline Result EventStop(EventPtr const event_ptr, TimeMs const duration, CurveInterpolation const curve) override
    {
        if (mAudioEngine)
            return mAudioEngine->EventStop(event_ptr, duration, curve);
        return 0;
    }

    virtual inline Result EventStopAll(ObjectID const object_id) override
    {
        if (mAudioEngine)
            return mAudioEngine->EventStopAll(object_id);
        return 0;
    }

    // parameter API
    virtual inline Result ParamSet(ParamID const param_id, Float const value, TimeMs const duration = 0, CurveInterpolation const curve = CurveInterpolation::Linear) override
    {
        if (mAudioEngine)
            return mAudioEngine->ParamSet(param_id, value, duration, curve);
        return 0;
    }

    virtual inline Result ParamSet(ObjectID const object_id, ParamID const param_id, Float const value, TimeMs const duration = 0, CurveInterpolation const curve = CurveInterpolation::Linear) override
    {
        if (mAudioEngine)
            return mAudioEngine->ParamSet(object_id, param_id, value, duration, curve);
        return 0;
    }

    virtual inline Result ParamSet(EventPtr const event_ptr, ParamID const param_id, Float const value, TimeMs const duration = 0, CurveInterpolation const curve = CurveInterpolation::Linear) override
    {
        if (mAudioEngine)
            return mAudioEngine->ParamSet(event_ptr, param_id, value, duration, curve);
        return 0;
    }

    virtual inline Result ParamReset(ObjectID const object_id, ParamID const param_id, TimeMs const duration = 0, CurveInterpolation const curve = CurveInterpolation::Linear) override
    {
        if (mAudioEngine)
            return mAudioEngine->ParamReset(object_id, param_id, duration, curve);
        return 0;
    }

    // switch API
    virtual inline Result SwitchSet(ObjectID const object_id, SwitchID const switch_id, SwitchValueID const switch_value_id) override
    {
        if (mAudioEngine)
            return mAudioEngine->SwitchSet(object_id, switch_id, switch_value_id);
        return 0;
    }

    // state API
    virtual inline Result StateSet(StateID const state_id, StateValueID const state_value_id) override
    {
        if (mAudioEngine)
            return mAudioEngine->StateSet(state_id, state_value_id);
        return 0;
    }

    // output device API
    virtual Index OutputGetNum() override
    {
        if (mAudioEngine)
            return mAudioEngine->OutputGetNum();
        return 0;
    }

    virtual Index OutputGetDefault() override
    {
        if (mAudioEngine)
            return mAudioEngine->OutputGetDefault();
        return 0;
    }

    virtual Result OutputGetName(Index const output_index, std::string & out_name) override
    {
        if (mAudioEngine)
            return mAudioEngine->OutputGetName(output_index, out_name);
        return 0;
    }

    virtual Result OutputAdd(Index const output_index, ObjectID const object_id) override
    {
        if (mAudioEngine)
            return mAudioEngine->OutputAdd(output_index, object_id);
        return 0;
    }

    virtual Result OutputRemove(Index const output_index) override
    {
        if (mAudioEngine)
            return mAudioEngine->OutputRemove(output_index);
        return 0;
    }

    virtual Result OutputReplace(Index const output_index_src, Index const output_index_dest) override
    {
        if (mAudioEngine)
            return mAudioEngine->OutputReplace(output_index_src, output_index_dest);
        return 0;
    }

	virtual Result OutputSetVolume(Index const output_index, Float const volume) override
	{
        if (mAudioEngine)
            return mAudioEngine->OutputSetVolume(output_index, volume);
        return 0;
	}

    virtual Result SpatialGetGeometry(GeometryID const geometry_id) override
    {
        if (mAudioEngine)
            return mAudioEngine->SpatialGetGeometry(geometry_id);
        return -1;
    }

    virtual Result SpatialSetOutside(std::string const & reverbAuxBus
        , Float const reverbLevel, Float const auxSendLevelToSelf)
    {
        if (mAudioEngine)
            return mAudioEngine->SpatialSetOutside(reverbAuxBus, reverbLevel, auxSendLevelToSelf);
        return 0;
    }

    virtual Result SpatialCreateGeometry(GeometryID const geometry_id
        , std::vector<Float3> const & vertexes, std::vector<Short4> const & triangles, std::vector<Surface> const & surfaces
        , bool const enableDiffraction, bool const enableDiffractionOnBoundaryEdges) override
    {
        if (mAudioEngine)
            return mAudioEngine->SpatialCreateGeometry(geometry_id, vertexes, triangles, surfaces, enableDiffraction, enableDiffractionOnBoundaryEdges);
        return 0;
    }
    virtual Result SpatialDestroyGeometry(GeometryID const geometry_id) override
    {
        if (mAudioEngine)
            return mAudioEngine->SpatialDestroyGeometry(geometry_id);
        return 0;
    }

    virtual Result SpatialCreateObstacle(ObstacleID const obstacle_id, GeometryID const geometry_id
        , Double4x4 const & transform
        , bool const useForReflectionAndDiffraction, bool const bypassPortalSubtraction, bool const isSolid) override
    {
        if (mAudioEngine)
            return mAudioEngine->SpatialCreateObstacle(obstacle_id, geometry_id, transform, useForReflectionAndDiffraction, bypassPortalSubtraction, isSolid);
        return 0;
    }
    virtual Result SpatialDestroyObstacle(ObstacleID const obstacle_id) override
    {
        if (mAudioEngine)
            return mAudioEngine->SpatialDestroyObstacle(obstacle_id);
        return 0;
    }

    virtual Result SpatialCreateRoom(RoomID const room_id, GeometryID const geometry_id
        , Double4x4 const & transform
        , bool const useForReflectionAndDiffraction, bool const bypassPortalSubtraction, bool const isSolid
        , std::string const & reverbAuxBus, Float const reverbLevel, Float const transmissionLoss
        , Float const auxSendLevelToSelf, Float const priority) override
    {
        if (mAudioEngine)
            return mAudioEngine->SpatialCreateRoom(room_id, geometry_id, transform
                , useForReflectionAndDiffraction, bypassPortalSubtraction, isSolid
                , reverbAuxBus, reverbLevel, transmissionLoss
                , auxSendLevelToSelf, priority);
        return 0;
    }
    virtual Result SpatialDestroyRoom(RoomID const room_id) override
    {
        if (mAudioEngine)
            return mAudioEngine->SpatialDestroyRoom(room_id);
        return 0;
    }

    virtual Result SpatialCreatePortal(PortalID const portal_id
        , Float3 const & extent, Double4x4 const & transform) override
    {
        if (mAudioEngine)
            return mAudioEngine->SpatialCreatePortal(portal_id, extent, transform);
        return 0;
    }
    virtual Result SpatialDestroyPortal(PortalID const portal_id) override
    {
        if (mAudioEngine)
            return mAudioEngine->SpatialDestroyPortal(portal_id);
        return 0;
    }
    
    virtual Result SpatialSetListener(ObjectID const object_id) const override
    {
        if (mAudioEngine)
            return mAudioEngine->SpatialSetListener(object_id);
        return 0;
    }
    
    virtual Result SpatialUnsetListener(ObjectID const object_id) const override
    {
        if (mAudioEngine)
            return mAudioEngine->SpatialUnsetListener(object_id);
        return 0;
    }

private:
    AudioEngine * mAudioEngine = nullptr;
};
}