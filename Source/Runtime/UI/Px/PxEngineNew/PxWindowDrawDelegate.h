#pragma once
#include "IWindowDrawDelegate.h"
#include "PxWindowContext.h"

namespace cross::px {
using namespace pixui;

struct MaskInfo
{
    matrix mat;
    position pos;
    ImageMask imageMask;
};

class PxWindowDrawDelegate : public IWindowDrawDelegate
{
public:
    IMPL_IUNKNOWN_EMPTY(0);

    PxWindowDrawDelegate(std::shared_ptr<PxWindowContext> windowContext)
        : mContext(windowContext)
    {}

    virtual void paintStart() override;
    virtual void paintEnd(const int nPaintCode) override;

    virtual void pushGroup(uint32 id, const pixui::matrix& mat, const pixui::size& size) override;
    virtual void popGroup() override;

    virtual void pushClip(pixui::uint32 id, const ClipRegion& polygon) override;
    virtual void popClip() override;

    virtual void pushImageMask(uint32 id, const pixui::matrix& mat, const pixui::position& posRect, const pixui::ImageMask& imageMask) override;
    virtual void popImageMask(uint32 id) override;

    virtual void pushSlot(uint32 id, PxSlotHandle attachment, const matrix& mat, const position& rect) override;
    virtual void popSlot() override;

    virtual void paintPushRenderLayer(uint32 id, const pixui::matrix& mat, const pixui::size& size) override;
    virtual void paintPopRenderLayer() override;

    virtual void drawFillRect(uint32 id, const matrix& mat, const position& rcDrawPos, const color& stColor, const BorderRadiuses& radius) override;
    
    virtual void drawImage(
        uint32 id, PxImageHandle hImage, const matrix& mat,
        const position& rcDraw, const position& uvImageSrc,
        const BackgroundRepeat emRepeat, const BackgroundAttachment emAttachment,
        const ImageSlice& sliceImage, const color& tintColor, const BorderRadiuses& radius, const position& rcRadius) override;

    virtual void drawRect(
        uint32 id, const matrix& mat, const position& stDrawPos,
        const uint32 nLeftWidth, const BorderStyle emLeftLineType, const color& stLeftColor,
        const uint32 nRightWidth, const BorderStyle emRightLineType, const color& stRightColor,
        const uint32 nTopWidth, const BorderStyle emTopLineType, const color& stTopColor,
        const uint32 nBottomWidth, const BorderStyle emBottomLineType, const color& stBottomColor,
        const BorderRadiuses& radius) override;

    virtual void drawText(
        uint32 id, PxFontHandle hFont, const char* pszTextDraw, const matrix& mat,
        const color& drawColor, const float fBlur, const position& rcDraw, const float letterSpace = 0.0f, const float fTextStrokeWidth = 0,
        const color& strokeColor = color()) override;

    virtual void drawTextUtf32(
        uint32 id, PxFontHandle hFont, const uint32* pszTextDraw, uint32 textLen, const matrix& mat,
        const color& tintColor, const float fBlur, const position& rcDraw,
        const float letterSpace, const float fTextStrokeWidth, const color& strokeColor) override;

    virtual void drawLine(
        uint32 id, const matrix& mat, const point& ptStart, const point& ptEnd, const color& tintColor, const int32 width = 1,
        const BorderStyle line_type = BorderStyle::border_style_solid) override;

    virtual void drawSlot(PxSlotHandle hSlot, uint32 id, PxSlotHandle attachment, const matrix& mat, const position& rect) override;

    virtual void drawCanvas(ICanvas* hCanvas, uint32 id, const matrix& mat, const position& rect) override;

#ifdef HARFBUZZ
    virtual void drawShapeText(uint32 id, PxFontHandle hFont, const matrix& mat, const uint* glyphs, uint glpyhCount, const color& drawColor,
                               const position& rcDraw, const float fBlur, const float fTextStrokeWidth, const color& strokeColor) override{};
#endif
    virtual void drawMesh(uint32 id, PxImageHandle texture, const matrix& mat, Vertex* vertices, size_t count) override;

public:
    void Text(const PxFont& font, const matrix& mat, const position& pos, const std::string& u16str, const color& c, const float blur, const float letterSpacing);
    void Border(const matrix& mat, const position& pos, const Float4& borderWidth, const Float4x4& borderColor, const BorderRadiuses& radius);
    void FillRect(const matrix& mat, const position& pos, const color& c, const BorderRadiuses& radius);
    void Image(TexturePtr texture, const matrix& mat, const position& pos, const position& imagePos, const color& c, const BorderRadiuses& radius);
    void Video(TexturePtr texture, TexturePtr alphaTexture, const matrix& mat, const position& pos, const position& videoPos, const color& c, const BorderRadiuses& radius);
    void Line(const matrix& mat, const unsigned int width, const BorderStyle& border, const color& c, const point start, const point end);

    void UpdateCanvasCommonState(CanvasItem& item);

    Canvas* GetCanvas();

private:
    resource::Material* GetMaterial(const CanvasItemType type);

    CanvasItem& CreateRectItem(const matrix& mat, const position& pos, const BorderRadiuses& radius);


private:
    std::shared_ptr<PxWindowContext> mContext;

    UInt8 mStencilRef{0};

    std::vector<ClipRegion> mClips;
    // new
    std::map<UInt32, ClipRegion> mClipMap;

    std::vector<MaskInfo> mMaskInfos;

    Float3 mTempRotate{0, 0, 0};
    float mTempPerspective{0};
    Float2 mRotateOrigin{0, 0};
    int mTempBlur{0};
    float mPushClipFilterArea{0};
};
}   // namespace cross::px