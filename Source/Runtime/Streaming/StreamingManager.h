#pragma once
#include <shared_mutex>
#include "Streaming/StreamingForward.h"
#include "Streaming/StreamingResource.h"
#include "Streaming/StreamingSettings.h"
#include "Streaming/StreamingStats.h"
#include "Streaming/StreamingCalculationTask.h"
#include "Streaming/StreamingUpdateTask.h"
#include "RenderEngine/RenderWorld.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "Runtime/GameWorld/ModelSystemG.h"

namespace cross
{

/**
 * Lightweight struct used to list the LOD levels of rendered assets.
 */
struct FRenderedResourceStats
{
    int MaxLODLevelShown;
    std::string TextureGroup;
};

class STREAMING_API StreamingManager : public IStreamingMgr, public SystemEventReceiver
{
public:
    using RemovedRenderAssetArray = std::array<const StreamableResource*, 12>;

    static StreamingManager& GetInstance();

    /** Constructor, initializing all members */
    StreamingManager();

    virtual ~StreamingManager();

    virtual void Shutdown();

    /**
     * Updates streaming, taking into account all current view infos. Can be called multiple times per frame.
     *
     * @param DeltaTime				Time since last call in seconds
     * @param bProcessEverything	[opt] If true, process all resources with no throttling limits
     */
    virtual void UpdateResourceStreaming(float DeltaTime, bool bProcessEverything = false)/* override*/;

    /**
     * Updates streaming for an individual texture/mesh, taking into account all view infos.
     *
     * @param RenderAsset	Texture or mesh to update
     */
    virtual void UpdateIndividualRenderAsset(StreamableResource* RenderAsset) /* override*/;

    /**
     * Register an asset whose non-resident LODs need to be loaded ASAP when visible.
     * bIgnoreStreamingLODBias must be set on the asset.
     * Either bForceLODlevelsToBeResident or ForceLODLevelsToBeResidentTimestamp need to be set on the asset.
     *
     * @param RenderAsset The asset to register
     * @return bool True if the streaming request is successful
     */
    virtual bool FastForceFullyResident(StreamableResource* RenderAsset) /* override*/;

    /**
     * Blocks till all pending requests are fulfilled.
     *
     * @param TimeLimit		Optional time limit for processing, in seconds. Specifying 0 means infinite time limit.
     * @param bLogResults	Whether to dump the results to the log.
     * @return				Number of streaming requests still in flight, if the time limit was reached before they were finished.
     */
    virtual int BlockTillAllRequestsFinished(float TimeLimit = 0.0f, bool bLogResults = false) /* override*/;

    /**
     * Cancels the timed Forced resources (i.e used the Kismet action "Stream In Textures").
     */
    virtual void CancelForcedResources() /* override*/;

    /** Don't stream world resources for the next NumFrames. */
    virtual void SetDisregardWorldResourcesForFrames(int NumFrames) /* override*/;

    /**
     *	Try to stream out texture/mesh LOD-levels to free up more memory.
     *	@param RequiredMemorySize	- Required minimum available texture memory
     *	@return						- Whether it succeeded or not
     **/
    virtual bool StreamOutRenderAssetData(SInt64 RequiredMemorySize) /*override*/;

    virtual SInt64 GetMemoryOverBudget() const /*override*/ { return MemoryOverBudget; }

    /** Pool size for streaming. */
    virtual SInt64 GetPoolSize() const /*override*/;

    virtual SInt64 GetRequiredPoolSize() const /*override*/ { return DisplayedStats.RequiredPool; }

    virtual SInt64 GetMaxEverRequired() const /*override*/ { return MaxEverRequired; }

    virtual SInt64 GetCachedLODs() const /*override*/ { return DisplayedStats.CachedLODs; }

    virtual void ResetMaxEverRequired() /*override*/ { MaxEverRequired = 0; }

    /**
     * Exec command handlers
     */
// #if !CROSSENGINE_RELEASE
//     bool HandleDumpTextureStreamingStatsCommand(const TCHAR* Cmd, FOutputDevice& Ar);
//     bool HandleListStreamingRenderAssetsCommand(const TCHAR* Cmd, FOutputDevice& Ar);
//     bool HandleResetMaxEverRequiredRenderAssetMemoryCommand(const TCHAR* Cmd, FOutputDevice& Ar);
//     bool HandleCancelRenderAssetStreamingCommand(const TCHAR* Cmd, FOutputDevice& Ar);
//     bool HandleNumStreamedLODsCommand(const TCHAR* Cmd, FOutputDevice& Ar);
//     bool HandleTrackRenderAssetCommand(const TCHAR* Cmd, FOutputDevice& Ar);
//     bool HandleListTrackedRenderAssetsCommand(const TCHAR* Cmd, FOutputDevice& Ar);
//     bool HandleUntrackRenderAssetCommand(const TCHAR* Cmd, FOutputDevice& Ar);
//     bool HandleStreamOutCommand(const TCHAR* Cmd, FOutputDevice& Ar);
//     bool HandlePauseRenderAssetStreamingCommand(const TCHAR* Cmd, FOutputDevice& Ar);
//     bool HandleStreamingManagerMemoryCommand(const TCHAR* Cmd, FOutputDevice& Ar, UWorld* InWorld);
//     bool HandleLODGroupsCommand(const TCHAR* Cmd, FOutputDevice& Ar);
//     bool HandleInvestigateRenderAssetCommand(const TCHAR* Cmd, FOutputDevice& Ar, UWorld* InWorld);
// #endif   // !CROSSENGINE_RELEASE

    /** Adds a new texture/mesh to the streaming manager. */
    virtual void AddStreamingRenderAsset(StreamableResource* RenderAsset) /*override*/;

    /** Removes a texture/mesh from the streaming manager. */
    virtual void RemoveStreamingRenderAsset(StreamableResource* RenderAsset) /*override*/;

    /** Only call on the game thread. */
    virtual bool IsFullyStreamedIn(StreamableResource* RenderAsset) /*override*/;

    /** Returns the corresponding StreamingResource for a texture or mesh. */
    StreamingResource* GetStreamingRenderAsset(const StreamableResource* RenderAsset);

    /** Set current pause state for texture/mesh streaming */
    virtual void PauseRenderAssetStreaming(bool bInShouldPause) /*override*/ { bPauseRenderAssetStreaming = bInShouldPause; }

    /** Return all bounds related to the ref object */
    virtual void GetObjectReferenceBounds(const void* RefObject, std::vector<BoundingBox>& AssetBoxes) /*override*/;

    /**
     * Mark the textures/meshes with a timestamp. They're about to lose their location-based heuristic and we don't want them to
     * start using LastRenderTime heuristic for a few seconds until they are garbage collected!
     *
     * @param RemovedRenderAssets	List of removed textures or meshes.
     */
    void SetRenderAssetsRemovedTimestamp(const RemovedRenderAssetArray& RemovedRenderAssets);

private:
    // BEGIN: Thread-safe functions and data
    friend class StreamingCalculationTask;
    friend class StreamingUpdateTask;

    /** Remove any references in level managers to this component */
    //void RemoveStaticReferences(const UPrimitiveComponent* Primitive);

    /**
     * Not thread-safe: Updates a portion (as indicated by 'StageIndex') of all streaming textures/meshes,
     * allowing their streaming state to progress.
     *
     * @param StageIndex		Current stage index
     * @param NumUpdateStages	Number of texture/mesh update stages
     * @Param bAsync			Whether this is called on an async task
     */
    void UpdateStreamingRenderAssets(int StageIndex, int NumStages, bool bAsync = false);

    /** Check visibility of fast response assets and initiate stream-in requests if necessary. */
    void TickFastResponseAssets();

    void ProcessRemovedRenderAssets();
    void ProcessAddedRenderAssets();

    /** Adds new textures/meshes and level data on the gamethread (while the worker thread isn't active). */
    void PrepareAsyncTask(bool bProcessEverything);

    /** Checks for updates in the user settings (CVars, etc). */
    void CheckUserSettings();

    /**
     * Stream textures/meshes in/out, based on the priorities calculated by the async work.
     *
     * @param bProcessEverything	Whether we're processing all textures in one go
     */
    void StreamRenderAssets(bool bProcessEverything);

    int GetNumStreamedLODsArray(StreamableResourceType AssetType, const int*& OutArray)
    {
        switch (AssetType)
        {
        case StreamableResourceType::StaticMesh:
            OutArray = NumStreamedLODs_StaticMesh.data();
            return static_cast<int>(NumStreamedLODs_StaticMesh.size());
            break;
        //case StreamableResourceType::SkeletalMesh:
        //    OutArray = NumStreamedLODs_SkeletalMesh.GetData();
        //    return NumStreamedLODs_SkeletalMesh.Num();
        default:
            Assert(false);
            OutArray = nullptr;
            return -1;
        }
    }

    /** All streaming texture or mesh objects.
     * Use directly only if it's safe to overlap with UpdateStreamingRenderAssets.
     * For an async safe version, use GetStreamingRenderAssetsAsyncSafe
     */
    std::vector<StreamingResource> AsyncUnsafeStreamingRenderAssets;

    /** All the textures/meshes referenced in StreamingRenderAssets. Used to handled deleted textures/meshes.  */
    std::set<const StreamableResource*> ReferencedRenderAssets;

    /** Index of the StreamingTexture that will be updated next by UpdateStreamingRenderAssets(). */
    int CurrentUpdateStreamingRenderAssetIndex;
    // END: Thread-safe functions and data

    void SetLastUpdateTime();
    void UpdateStats();
    void UpdateCSVOnlyStats();
    void LogViewLocationChange();

    void IncrementalUpdate(float Percentage, bool bUpdateDynamicComponents);

    /**
     * Update all pending states.
     *
     * @param bUpdateDynamicComponents		Whether dynamic component state should also be updated.
     */
    void UpdatePendingStates(bool bUpdateDynamicComponents);

    /**
     * Complete all pending async work and complete all pending state updates.
     *
     * @param bCompleteFullUpdateCycle		Whether to complete the full update cycle usually spread accross several frames.
     */
    void SyncStates(bool bCompleteFullUpdateCycle);

    /**
     * Called on game thread when no new elements are added to the StreamingRenderAssets array and
     * the elements in the array are not removed or reordered. This runs in parrallel with the
     * async update task so the streaming meta data in each StreamingResource can change
     */
    void ProcessPendingLODCopyRequests();

    /** Cached from the system settings. */
    std::vector<int> NumStreamedLODs_StaticMesh;
    //std::vector<int> NumStreamedLODs_SkeletalMesh;

    StreamingSettings Settings;

    /** Async work for calculating priorities and target number of LODs for all textures/meshes. */
    StreamingCalculationTask AsyncWorkTask;
    threading::TaskEventPtr AsyncWork;

    /** Async work for render asset instance managers. */
    //std::shared_ptr<StreamingUpdateTask::FDoWorkAsyncTask> RenderAssetInstanceAsyncTask;
    // TODO:
    StreamingUpdateTask RenderAssetInstanceAsyncWorkTask;
    threading::TaskEventPtr RenderAssetInstanceAsyncWork;

    /** New textures/meshes, before they've been added to the thread-safe container. */
    std::vector<StreamableResource*> PendingStreamingRenderAssets;

    /** The list of indices with null render asset in StreamingRenderAssets. */
    std::vector<int> RemovedRenderAssetIndices;

    /** [Game Thread] Forced fully resident assets that need to be loaded ASAP when visible. */
    std::set<StreamableResource*> FastResponseRenderAssets;

    /** [Game Thread] Fast response assets detected visible before the end of full streaming update. */
    std::set<int> VisibleFastResponseRenderAssetIndices;

    // Represent a pending request to stream in/out LODs to/from GPU for a texture or mesh
    struct FPendingLODCopyRequest
    {
        const StreamableResource* RenderAsset;
        // Used to find the corresponding StreamingResource in the StreamingRenderAssets array
        int CachedIdx;

        FPendingLODCopyRequest() = default;

        FPendingLODCopyRequest(const StreamableResource* InAsset, int InCachedIdx)
            : RenderAsset(InAsset)
            , CachedIdx(InCachedIdx)
        {}
    };
    std::vector<FPendingLODCopyRequest> PendingLODCopyRequests;

    // The index of the next FPendingLODCopyRequest to process so the requests can be amortized accross multiple frames
    int CurrentPendingLODCopyRequestIdx;

    /** Stages [0,N-2] is non-threaded data collection, Stage N-1 is wait-for-AsyncWork-and-finalize. */
    int ProcessingStage;

    /** Total number of processing stages (N). */
    int NumRenderAssetProcessingStages;

    /** Whether to support texture/mesh instance streaming for dynamic (movable/spawned) objects. */
    bool bUseDynamicStreaming;

    float BoostPlayerTextures;

    /** Amount of memory to leave free in the render asset pool. */
    SInt64 MemoryMargin;

    /** The actual memory pool size available to stream textures/meshes, excludes non-streaming texture/mesh, temp memory (for streaming LODs), memory margin (allocator overhead). */
    SInt64 EffectiveStreamingPoolSize;

    // Stats we need to keep across frames as we only iterate over a subset of textures.

    SInt64 MemoryOverBudget;
    SInt64 MaxEverRequired;

    /** Whether render asset streaming is paused or not. When paused, it won't stream any textures/meshes in or out. */
    bool bPauseRenderAssetStreaming;

    /** Last time all data were fully updated. Instances are considered visible if they were rendered between that last time and the current time. */
    float LastWorldUpdateTime;

    /** LastWorldUpdateTime seen by the async task. */
    float LastWorldUpdateTime_LODCalcTask;

    StreamingStats DisplayedStats;
    StreamingStats GatheredStats;

    std::vector<int> InflightRenderAssets;

    // A critical section use around code that could be called in parallel with NotifyPrimitiveUpdated() or NotifyPrimitiveUpdated_Concurrent().
    mutable std::shared_mutex CriticalSection;

    // An event used to prevent FUpdateStreamingRenderAssetsTask from overlapping with related work
    //FGraphEventRef StreamingRenderAssetsSyncEvent;
    threading::TaskEventPtr StreamingRenderAssetsSyncEvent;
    std::vector<StreamingResource>& GetStreamingRenderAssetsAsyncSafe();

    //friend bool TrackRenderAssetEvent(StreamingResource* StreamingRenderAsset, StreamableResource* RenderAsset, bool bForceLODLevelsToBeResident, const StreamingManager* Manager);

// ------ Interface from IStreamingManager
public:
    bool IsRenderAssetStreamingEnabled(StreamableResourceType assetType) const {
        if (assetType == StreamableResourceType::StaticMesh)
        {
            return true;
        }
        else
        {
            return false;
        }
    }

protected:
    /**
     * Sets up the CurrentViewInfos array based on PendingViewInfos, LastingViewInfos and SecondaryLocations.
     * Removes out-dated LastingViewInfos.
     *
     * @param DeltaTime		Time since last call in seconds
     */
    void SetupViewInfos(float DeltaTime);

    /** Set when Tick() has been called. The first time a new view is added, it will clear out all old views. */
    static bool bPendingRemoveViews;

    /** Count of how many nested DisableResourceStreaming's were called - will enable when this is 0 */
    int DisableResourceStreamingCount;

    std::vector<StreamingViewInfo> CurrentViewInfos;

// ------ Cross region
public:
    // The default size will reserve ~3MB, the element size is 168 bytes.
    static constexpr int StreamingResourcesDefaultReservedSize = 20000;

    virtual void Tick(FrameParam* fp);

    bool IsWorldSuitableForStreaming(GameWorld* gameWorld) const;

    void OnWorldCreated(GameWorld* gameWorld);

    void OnWorldDestroyed(GameWorld* gameWorld);

    void AddReferenceEntity(const ecs::EntityID& entity, StreamableResource* resource)
    {
        std::scoped_lock lock(CriticalSection);

        if (mEntityToResourceLookUpMap.try_emplace(entity, resource).second)
        {
            mRegisteredEntitiesMap[resource].emplace(entity);
        }
    }
    
    void RemoveReferenceEntity(const ecs::EntityID& entity)
    {
        std::scoped_lock lock(CriticalSection);

        auto it = mEntityToResourceLookUpMap.find(entity);
        if (it != mEntityToResourceLookUpMap.end())
        {
            auto* resource = it->second;
            auto& entitySet = mRegisteredEntitiesMap[resource];
            entitySet.erase(entity);
            if (entitySet.empty())
            {
                mRegisteredEntitiesMap.erase(resource);
            }
        }
    }

    virtual void UpdateRenderStatus(FrameParam* fp) override
    {
        std::scoped_lock lock(CriticalSection);

        if (CurrentRenderFrameCount < fp->GetFrameCount())
        {
            // Now a new render frame is coming, stage collected visible resources of the last frame
            mResourceRequestLODMap.swap(mResourceRequestLODMapStaged);
            mResourceRequestLODMap.clear();
            CurrentRenderFrameCount = fp->GetFrameCount();
        }
    }

    virtual void UpdateDesiredLODCount(const ecs::EntityID& entity, int count) override
    {
        std::scoped_lock lock(CriticalSection);
        auto it = mEntityToResourceLookUpMap.find(entity);
        if (it != mEntityToResourceLookUpMap.end())
        {
            auto* resource = it->second;
            mResourceRequestLODMap[resource] = std::max(mResourceRequestLODMap[resource], count);
        }
    }

    int GetDesiredLODCount(StreamableResource* resource)
    {
        std::shared_lock lock(CriticalSection);

        int count = 0;
        auto it = mResourceRequestLODMapInFlight.find(resource);
        if (it != mResourceRequestLODMapInFlight.end())
        {
            count = it->second;
        }
        return count;
    }

    virtual int GetResidentLODCount(const ecs::EntityID& entity) const override
    {
        SCOPED_CPU_TIMING(GroupRendering, "GetResidentLODCount");
        std::shared_lock lock(CriticalSection);

        auto it = mEntityToResourceLookUpMap.find(entity);
        if (it != mEntityToResourceLookUpMap.end())
        {
            int assetIndex = it->second->GetStreamingIndex();
            const auto& renderAsset = AsyncUnsafeStreamingRenderAssets[assetIndex];

            // If a stream out task is completed as the buffers have been released in an async thread,
            // it is possible that the resident LOD count of this mesh is not yet updated (until streaming manager tick later in the main thread).
            // Therefore, when choosing the desired LOD level to generate draw units, the requested LOD count should be considered as it is decided before a streaming task is dispatched 
            return std::min(renderAsset.ResidentLODs, renderAsset.RequestedLODs);
        }
        return -1;
    }

    virtual void NotifyEvent(const SystemEventBase& event, UInt32& flag) override;

    virtual bool IsStreamingEnabled() const override
    {
        //return DisableResourceStreamingCount == 0;
        return Settings.EnableStreaming;
    }

    void PrintStreamingStats(GameWorld* gameWorld) const;

protected:
    bool mIsFirstTick = true;
    bool mDisplayStats = false;

    // TODO: Remove these game world pointers, which are current used for printing stats on screens
    std::unordered_set<GameWorld*> mGameWorlds;

    // Visible resources in current render frame / incomplete / use in rendering thread
    CEHashMap<StreamableResource*, int> mResourceRequestLODMap;
    // Staged visible resources of last render frame / complete
    CEHashMap<StreamableResource*, int> mResourceRequestLODMapStaged;
    // Locked visible resources during streaming / complete / copy from staged ones in game thread and use in async thread
    CEHashMap<StreamableResource*, int> mResourceRequestLODMapInFlight;

    UInt32 CurrentRenderFrameCount = 0;

    CEHashMap<ecs::EntityID, StreamableResource*> mEntityToResourceLookUpMap;
    CEHashMap<StreamableResource*, std::unordered_set<ecs::EntityID>> mRegisteredEntitiesMap;
};

} // namespace cross

#define gStreamingManager cross::StreamingManager::GetInstance()
