#include "Streaming/StaticMeshStreamingTask.h"
#include "RenderEngine/VertexStreamLayoutPolicy.h"
#include "RenderEngine/RenderMesh.h"

#define SRA_UPDATE_CALLBACK(FunctionName) [this](const StaticMeshStreamingContext& C) { FunctionName(C); }

namespace cross
{

// Instantiate TRenderAssetUpdate for FStaticMeshUpdateContext
template class StreamingTask<StaticMeshStreamingContext>;

StaticMeshStreamingContext::StaticMeshStreamingContext(const StreamableResource* InMesh, threading::ThreadID InCurrentThread)
    : Mesh(InMesh)
    , CurrentThread(InCurrentThread)
{
    Assert(InMesh);
    Assert(InCurrentThread != threading::ThreadID::RenderingThread || threading::TaskSystem::IsInRenderingThread());

    // TODO:
    //RenderData = const_cast<StreamableResource*>(InMesh)->GetRenderData();
    //if (RenderData)
    //{
    //    LODResourcesView = std::vector<FStaticMeshLODResources*>(RenderData->LODResources.GetData() + InMesh->GetStreamableResourceState().LODBias, InMesh->GetStreamableResourceState().LODCount);
    //}
    RenderData = dynamic_cast<MeshR*>(InMesh->GetMeshAssetDataResource()->GetAssetData()->GetRenderMesh());
}

StaticMeshStreamingTask::StaticMeshStreamingTask(const StreamableResource* InMesh)
    : StreamingTask<StaticMeshStreamingContext>(InMesh)
{}

StaticMeshStreamInTask::StaticMeshStreamInTask(const StreamableResource* InMesh, threading::ThreadID CreateResourcesThread)
    : StaticMeshStreamingTask(InMesh)
    , CreateResourcesThread(CreateResourcesThread)
{}

void StaticMeshStreamInTask::CreateBuffers(const StaticMeshStreamingContext& Context)
{
    Assert(Context.Mesh && Context.RenderData);

    VertexStreamLayoutStaticLODStreamingParameter meshParam;
    UInt8 lodCount = Context.Mesh->GetMeshAssetDataResource()->GetAssetData()->GetLodCount();
    for (int LODIdx = mPendingFirstLODIndex; LODIdx < mCurrentFirstLODIndex; ++LODIdx)
    {
        // The mesh buffers of the highest LOD level should always stay in the GPU and not be refreshed until it is destroyed
        if (LODIdx == lodCount - 1)
        {
            continue;
        }

        meshParam.EnableStreaming = true;
        meshParam.SelectedLOD = static_cast<SInt8>(LODIdx);
        Context.RenderData->BuildStaticMesh(&meshParam);
    }
}

void StaticMeshStreamInTask::DiscardNewLODs(const StaticMeshStreamingContext& Context)
{
    //if (Context.RenderData)
    //{
    //    for (int LODIdx = mPendingFirstLODIndex; LODIdx < mCurrentFirstLODIndex; ++LODIdx)
    //    {
    //        FStaticMeshLODResources& LODResource = *Context.LODResourcesView[LODIdx];
    //        LODResource.DiscardCPUData();
    //    }
    //}
}

void StaticMeshStreamInTask::DoFinishUpdate(const StaticMeshStreamingContext& Context)
{
    Assert(Context.CurrentThread == threading::ThreadID::RenderingThread);
    Assert(threading::TaskSystem::IsInRenderingThread());

    // TODO:
    //Context.RenderData->mCurrentFirstLODIndex = ResourceState.LODCountToAssetFirstLODIdx(ResourceState.NumRequestedLODs);
    //Context.Mesh->RequestUpdateCachedRenderState(); // Maybe no need, used in nanite
    MarkAsSuccessfullyFinished();
}

void StaticMeshStreamInTask::DoCancel(const StaticMeshStreamingContext& Context)
{
    // TODO: support streaming CPU data for editor builds
    //if (!FPlatformProperties::HasEditorOnlyData())
    //{
    //    DiscardNewLODs(Context);
    //}

    //Assert(!StreamingRHICmdList);
}

int StaticMeshStreamOutTask::StreamingMaxReferenceChecks = 2;

StaticMeshStreamOutTask::StaticMeshStreamOutTask(const StreamableResource* InMesh, bool InDiscardCPUData)
    : StaticMeshStreamingTask(InMesh)
    , bDiscardCPUData(InDiscardCPUData)
{
    Assert(InMesh);

    // Immediately change mCurrentFirstLODIndex to prevent new references from being made to the streamed out lods.
    // #TODO RenderData under a const UStaticMesh should stay const
    // TODO:
    //FStaticMeshRenderData* RenderData = const_cast<StreamableResource*>(InMesh)->GetRenderData();
    //if (RenderData)
    //{
    //    RenderData->mCurrentFirstLODIndex = mResourceState.GetFirstAssetLODIndex(mResourceState.RequestedLODCount);
    //}

    if (InDiscardCPUData)
    {
        PushTask(StaticMeshStreamingContext(InMesh, threading::ThreadID::AnyThread), threading::ThreadID::AsyncThread, SRA_UPDATE_CALLBACK(CheckReferencesAndDiscardCPUData), threading::ThreadID::AsyncThread, SRA_UPDATE_CALLBACK(Cancel));
    }
    else
    {
        PushTask(StaticMeshStreamingContext(InMesh, threading::ThreadID::AnyThread), threading::ThreadID::RenderingThread, SRA_UPDATE_CALLBACK(ReleaseBuffers), threading::ThreadID::AsyncThread, SRA_UPDATE_CALLBACK(Cancel));
    }
}

void StaticMeshStreamOutTask::CheckReferencesAndDiscardCPUData(const StaticMeshStreamingContext& Context)
{
    SCOPED_CPU_TIMING(GroupStreaming, "StaticMeshStreamOutTask::CheckReferencesAndDiscardCPUData");

    Assert(Context.CurrentThread == threading::ThreadID::AsyncThread);

    const StreamableResource* Mesh = Context.Mesh;
    MeshR* RenderData = Context.RenderData;
    UInt32 NumExternalReferences = 0;

    if (!NumExternalReferences || NumReferenceChecks >= StreamingMaxReferenceChecks)
    {
        if (RenderData)
        {
            for (int LODIdx = mCurrentFirstLODIndex; LODIdx < mPendingFirstLODIndex; ++LODIdx)
            {
                //Context.LODResourcesView[LODIdx]->DiscardCPUData();
            }
        }

        // Because we discarded the CPU data, the stream out can not be cancelled anymore.
        PushTask(Context, threading::ThreadID::RenderingThread, SRA_UPDATE_CALLBACK(ReleaseBuffers), threading::ThreadID::RenderingThread, SRA_UPDATE_CALLBACK(ReleaseBuffers));
    }
    else
    {
        ++NumReferenceChecks;
        if (NumReferenceChecks >= StreamingMaxReferenceChecks)
        {
            LOG_WARN("[{}] Streamed out LODResources references are not getting released.", Mesh->GetFullName());
        }

        bDeferExecution = true;
        PushTask(Context, threading::ThreadID::AsyncThread, SRA_UPDATE_CALLBACK(CheckReferencesAndDiscardCPUData), threading::ThreadID::AsyncThread, SRA_UPDATE_CALLBACK(Cancel));
    }
}

void StaticMeshStreamOutTask::ReleaseBuffers(const StaticMeshStreamingContext& Context)
{
    SCOPED_CPU_TIMING(GroupStreaming, "StaticMeshStreamOutTask::ReleaseBuffers");

    Assert(Context.CurrentThread == threading::ThreadID::RenderingThread);

    if (Context.RenderData)
    {
        //static constexpr UInt32 GStaticMeshMaxNumResourceUpdatesPerLOD = 14;
        //static constexpr UInt32 GStaticMeshMaxNumResourceUpdatesPerBatch = (cross::resource::MAX_MESH_LOD_NUM - 1) * GStaticMeshMaxNumResourceUpdatesPerLOD;
        //FRHIResourceReplaceBatcher Batcher(FRHICommandListImmediate::Get(), GStaticMeshMaxNumResourceUpdatesPerBatch);
        for (int LODIdx = mCurrentFirstLODIndex; LODIdx < mPendingFirstLODIndex; ++LODIdx)
        {
            MeshR* renderMesh = dynamic_cast<MeshR*>(Context.Mesh->GetMeshAssetDataResource()->GetAssetData()->GetRenderMesh());
            if (!renderMesh)
                continue;

            auto* lodRenderMesh = renderMesh->GetLODMesh(static_cast<UInt8>(LODIdx));
            if (lodRenderMesh && lodRenderMesh->GeometryPacket)
            {
                lodRenderMesh->GeometryPacket->Clear();
            }

            //FStaticMeshLODResources& LODResource = *Context.LODResourcesView[LODIdx];
            //LODResource.DecrementMemoryStats();
            //LODResource.ReleaseRHIForStreaming(Batcher);
        }

        //Context.Mesh->RequestUpdateCachedRenderState();
    }
    MarkAsSuccessfullyFinished();
}

void StaticMeshStreamOutTask::Cancel(const StaticMeshStreamingContext& Context)
{
    SCOPED_CPU_TIMING(GroupStreaming, "StaticMeshStreamOutTask::Cancel");

    Assert(Context.CurrentThread == threading::ThreadID::AsyncThread);

    if (Context.RenderData)
    {
        // TODO:
        //Context.RenderData->mCurrentFirstLODIndex = ResourceState.LODCountToAssetFirstLODIdx(ResourceState.NumResidentLODs);
    }
}

//void StaticMeshStreamInTask::FCancelIORequestsTask::DoWork()
//{
//    Assert(PendingUpdate);
//    // Acquire the lock of this object in order to cancel any pending IO.
//    // If the object is currently being ticked, wait.
//    const TaskState PreviousTaskState = PendingUpdate->DoLock();
//    PendingUpdate->CancelIORequest();
//    PendingUpdate->DoUnlock(PreviousTaskState);
//}

void StaticMeshStreamInTask::Abort()
{
    if (!IsCancelled() && !IsCompleted())
    {
        StaticMeshStreamingTask::Abort();

        //if (BulkDataRequest.IsPending())
        //{
        //    // Prevent the update from being considered done before this is finished.
        //    // By checking that it was not already cancelled, we make sure this doesn't get called twice.
        //    (new FAsyncCancelIORequestsTask(this))->StartBackgroundTask();
        //}
    }
}

void StaticMeshStreamInTask::SetIORequest(const StaticMeshStreamingContext& Context)
{
    if (IsCancelled())
    {
        return;
    }

//    Assert(BulkDataRequest.IsNone() && mPendingFirstLODIndex < mCurrentFirstLODIndex);
//
//    if (Context.Mesh && Context.RenderData)
//    {
//        const int BatchCount = mCurrentFirstLODIndex - mPendingFirstLODIndex;
//        FBulkDataBatchRequest::FScatterGatherBuilder Batch = FBulkDataBatchRequest::ScatterGather(BatchCount);
//        for (int Index = mPendingFirstLODIndex; Index < mCurrentFirstLODIndex; ++Index)
//        {
//            Batch.Read(Context.LODResourcesView[Index]->StreamingBulkData);
//        }
//
//        // Increment as we push the request. If a request complete immediately, then it will call the callback
//        // but that won't do anything because the tick would not try to acquire the lock since it is already locked.
//        TaskSynchronization.Increment();
//
//        EAsyncIOPriorityAndFlags Priority = AIOP_Low;
//        if (bHighPrioIORequest)
//        {
//            static IConsoleVariable* CVarAsyncLoadingPrecachePriority = IConsoleManager::Get().FindConsoleVariable(TEXT("s.AsyncLoadingPrecachePriority"));
//            const bool bLoadBeforeAsyncPrecache = CVarStreamingLowResHandlingMode.GetValueOnAnyThread() == (int)FRenderAssetStreamingSettings::LRHM_LoadBeforeAsyncPrecache;
//
//            if (CVarAsyncLoadingPrecachePriority && bLoadBeforeAsyncPrecache)
//            {
//                const int32 AsyncIOPriority = CVarAsyncLoadingPrecachePriority->GetInt();
//                // Higher priority than regular requests but don't go over max
//                Priority = (EAsyncIOPriorityAndFlags)FMath::Clamp<int32>(AsyncIOPriority + 1, AIOP_BelowNormal, AIOP_MAX);
//            }
//            else
//            {
//                Priority = AIOP_BelowNormal;
//            }
//        }
//
//        Batch.Issue(
//            BulkData,
//            Priority,
//            [this](FBulkDataRequest::EStatus Status) {
//                TaskSynchronization.Decrement();
//
//                if (FBulkDataRequest::EStatus::Ok != Status)
//                {
//                    // If IO requests was cancelled but the streaming request wasn't, this is an IO error.
//                    if (!bIsCancelled)
//                    {
//                        bFailedOnIOError = true;
//                    }
//                    MarkAsCancelled();
//                }
//
//#if !CROSSENGINE_RELEASE
//                // On some platforms the IO is too fast to test cancelation requests timing issues.
//                if (FRenderAssetStreamingSettings::ExtraIOLatency > 0 && TaskSynchronization.GetValue() == 0)
//                {
//                    FPlatformProcess::Sleep(FRenderAssetStreamingSettings::ExtraIOLatency * .001f);   // Slow down the streaming.
//                }
//#endif
//                // The tick here is intended to schedule the success or cancel callback.
//                // Using TT_None ensure gets which could create a dead lock.
//                Tick(FStaticMeshUpdate::TT_None);
//            },
//            BulkDataRequest);
//    }
//    else
//    {
//        MarkAsCancelled();
//    }
}

void StaticMeshStreamInTask::ClearIORequest(const StaticMeshStreamingContext& Context)
{
    //if (BulkDataRequest.IsPending())
    //{
    //    BulkDataRequest.Cancel();
    //    BulkDataRequest.Wait();
    //}

    //BulkDataRequest = FBulkDataBatchRequest();
    //BulkData = FIoBuffer();
}

void StaticMeshStreamInTask::ReportIOError(const StaticMeshStreamingContext& Context)
{
    //// Invalidate the cache state of all initial LODs (note that when using FIoChunkId each LOD has a different value).
    //if (bFailedOnIOError && Context.Mesh)
    //{
    //    IRenderAssetStreamingManager& StreamingManager = IStreamingManager::Get().GetTextureStreamingManager();
    //    for (int LODIndex = 0; LODIndex < mCurrentFirstLODIndex; ++LODIndex)
    //    {
    //        StreamingManager.MarkMountedStateDirty(Context.Mesh->GetLODIoFilenameHash(LODIndex));
    //    }

    //    UE_LOG(LogContentStreaming, Warning, TEXT("[%s] StaticMesh stream in request failed due to IO error (LOD %d-%d)."), *Context.Mesh->GetName(), mPendingFirstLODIndex, mCurrentFirstLODIndex - 1);
    //}
}

void StaticMeshStreamInTask::SerializeLODData(const StaticMeshStreamingContext& Context)
{
//    Assert(!TaskSynchronization.GetValue());
//    const UStaticMesh* Mesh = Context.Mesh;
//    FStaticMeshRenderData* RenderData = Context.RenderData;
//    if (!IsCancelled() && Mesh && RenderData)
//    {
//        Assert(BulkData.GetSize() >= 0 && BulkData.GetSize() <= TNumericLimits<UInt32>::Max());
//
//        FMemoryReaderView Ar(BulkData.GetView(), true);
//        for (int LODIdx = mPendingFirstLODIndex; LODIdx < mCurrentFirstLODIndex; ++LODIdx)
//        {
//            FStaticMeshLODResources& LODResource = *Context.LODResourcesView[LODIdx];
//            constexpr uint8 DummyStripFlags = 0;
//            typename FStaticMeshLODResources::FStaticMeshBuffersSize DummyBuffersSize;
//            LODResource.SerializeBuffers(Ar, const_cast<UStaticMesh*>(Mesh), DummyStripFlags, DummyBuffersSize);
//
//            // Attempt to recover from possibly corrupted data if allowed
//            if (Ar.IsError())
//            {
//                UE_LOG(LogContentStreaming,
//                       Error,
//                       TEXT("[%s] StaticMesh stream in failed due to possibly corrupted data. LOD %d %d-%d. BulkData %#x offset %lld size %lld flags %#x."),
//                       *Mesh->GetPathName(),
//                       LODIdx,
//                       mPendingFirstLODIndex,
//                       mCurrentFirstLODIndex - 1,
//                       LODResource.StreamingBulkData.GetIoFilenameHash(),
//                       LODResource.StreamingBulkData.GetBulkDataOffsetInFile(),
//                       LODResource.StreamingBulkData.GetBulkDataSize(),
//                       LODResource.StreamingBulkData.GetBulkDataFlags());
//
//#if STREAMING_RETRY_ON_DESERIALIZATION_ERROR
//                bFailedOnIOError = true;
//                MarkAsCancelled();
//                break;
//#else
//                GLog->FlushThreadedLogs();
//                GLog->Flush();
//                UE_LOG(LogContentStreaming, Fatal, TEXT("Possibly corrupted static mesh LOD data detected."));
//#endif
//            }
//        }
//
//        BulkData = FIoBuffer();
//    }
}

void StaticMeshStreamInTask::Cancel(const StaticMeshStreamingContext& Context)
{
    DoCancel(Context);
    ReportIOError(Context);
}

void StaticMeshStreamInTask::CancelIORequest()
{
    //if (BulkDataRequest.IsPending())
    //{
    //    BulkDataRequest.Cancel();
    //}
}

StaticMeshStreamInTask::StaticMeshStreamInTask(const StreamableResource* InMesh, bool bHighPrio, threading::ThreadID CreateResourcesThread)
    : StaticMeshStreamInTask(InMesh, CreateResourcesThread)
{
    bHighPrioIORequest = bHighPrio;
    PushTask(StaticMeshStreamingContext(InMesh, threading::ThreadID::AnyThread), threading::ThreadID::AsyncThread, SRA_UPDATE_CALLBACK(DoInitiateIO), threading::ThreadID::AnyThread, nullptr);
}

void StaticMeshStreamInTask::DoInitiateIO(const StaticMeshStreamingContext& Context)
{
    SCOPED_CPU_TIMING(GroupStreaming, "StaticMeshStreamInTask::DoInitiateIO");

    Assert(Context.CurrentThread == threading::ThreadID::AsyncThread);

    SetIORequest(Context);

    PushTask(Context, threading::ThreadID::AsyncThread, SRA_UPDATE_CALLBACK(DoSerializeLODData), threading::ThreadID::AsyncThread, SRA_UPDATE_CALLBACK(DoCancelIO));
}

void StaticMeshStreamInTask::DoSerializeLODData(const StaticMeshStreamingContext& Context)
{
    SCOPED_CPU_TIMING(GroupStreaming, "StaticMeshStreamInTask::DoSerializeLODData");

    Assert(Context.CurrentThread == threading::ThreadID::AsyncThread);

    SerializeLODData(Context);
    ClearIORequest(Context);

    PushTask(Context, CreateResourcesThread, SRA_UPDATE_CALLBACK(DoCreateBuffers), Context.CurrentThread, SRA_UPDATE_CALLBACK(Cancel));
}

void StaticMeshStreamInTask::DoCreateBuffers(const StaticMeshStreamingContext& Context)
{
    SCOPED_CPU_TIMING(GroupStreaming, "StaticMeshStreamInTask::DoCreateBuffers");

    if (threading::TaskSystem::IsInAsyncThread())
    {
        CreateBuffers(Context);
    }
    else
    {
        // There are cases (very unlikely though) that this task is running while the streamable resource itself is being deconstructed
        // In this situation the game thread will try to take over this async task and complete it asap
        // Therefore, cancel this create buffers task if it is not being executed in async threads to avoid stalling the thread
        LOG_WARN("Streaming: Stream in task for {} is cancelled at thread {}", Context.Mesh->GetFullName(), threading::TaskSystem::GetCurrentThreadID());
        MarkAsCancelled();
        return;
    }

    Assert(!mTaskSynchronization.load());

    // We cannot cancel once DoCreateBuffers has started executing, as there's an RHICmdList that must be submitted.
    // Pass the same callback for both task and cancel.
    PushTask(Context, threading::ThreadID::RenderingThread, SRA_UPDATE_CALLBACK(DoFinishUpdate), threading::ThreadID::RenderingThread, SRA_UPDATE_CALLBACK(DoFinishUpdate));
}

void StaticMeshStreamInTask::DoCancelIO(const StaticMeshStreamingContext& Context)
{
    SCOPED_CPU_TIMING(GroupStreaming, "StaticMeshStreamInTask::DoCancelIO");

    ClearIORequest(Context);

    PushTask(Context, threading::ThreadID::AnyThread, nullptr, Context.CurrentThread, SRA_UPDATE_CALLBACK(Cancel));
}

} // namespace cross
