#pragma once
#include "Streaming/StreamingForward.h"
#include "Streaming/StreamingResource.h"
#include "Streaming/StreamingData.h"
#include "CrossBase/Threading/Threading.h"

namespace cross
{

/** Async work for calculating priorities and target number of LODs for all textures/meshes. */
// this could implement a better abandon, but give how it is used, it does that anyway via the abort mechanism
class StreamingCalculationTask
{
public:
    StreamingCalculationTask() = default;

    StreamingCalculationTask(StreamingManager* InStreamingManager)
        : StreamingManager(InStreamingManager)
        , bAbort(false)
    {
        Reset(0, 0, 0, 0, 0);
        MemoryBudget = 0;
        MeshMemoryBudget = 0;
        PerfectWantedLODsBudgetResetThresold = 0;
    }

    /** Resets the state to start a new async job. */
    void Reset(SInt64 InTotalGraphicsMemory, SInt64 InAllocatedMemory, SInt64 InPoolSize, SInt64 InTempMemoryBudget, SInt64 InMemoryMargin)
    {
        TotalGraphicsMemory = InTotalGraphicsMemory;
        AllocatedMemory = InAllocatedMemory;
        PoolSize = InPoolSize;
        TempMemoryBudget = InTempMemoryBudget;
        MemoryMargin = InMemoryMargin;

        bAbort = false;
    }

    /** Notifies the async work that it should abort the thread ASAP. */
    void Abort() { bAbort = true; }

    /** Whether the async work is being aborted. Can be used in conjunction with IsDone() to see if it has finished. */
    bool IsAborted() const { return bAbort; }

    /** Returns the resulting priorities, matching the FRenderAssetStreamingManager::StreamingRenderAssets array. */
    const std::vector<int>& GetLoadRequests() const { return LoadRequests; }
    const std::vector<int>& GetCancelationRequests() const { return CancelationRequests; }
    const std::vector<int>& GetPendingUpdateDirties() const { return PendingUpdateDirties; }

    StreamingViewData StreamingData;

    /** Performs the async work. */
    void DoWork();

    bool HasAnyView() const { return StreamingData.HasAnyView(); }

    void ReleaseAsyncViews() { StreamingData.ReleaseViews(); }

protected:
    /** Ensures that no temporary streaming boost are active which could interfere with render asset streaming bias in undesirable ways. */
    bool AllowPerRenderAssetLODBiasChanges() const;

private:
    friend class threading::TTask<StreamingCalculationTask>;

    void TryDropMaxResolutions(std::vector<int>& PrioritizedRenderAssets, SInt64& MemoryBudgeted, const SInt64 InMemoryBudget);

    void TryDropLODs(std::vector<int>& PrioritizedRenderAssets, SInt64& MemoryBudgeted, const SInt64 InMemoryBudget);

    void TryKeepLODs(std::vector<int>& PrioritizedRenderAssets, SInt64& MemoryBudgeted, const SInt64 InMemoryBudget);

    void UpdateBudgetedLODs();

    void UpdateLoadAndCancelationRequests();

    void UpdatePendingStreamingStatus();

    void UpdateStats();

    void UpdateCSVOnlyStats();

    //FORCEINLINE TStatId GetStatId() const { RETURN_QUICK_DECLARE_CYCLE_STAT(StreamingCalculationTask, STATGROUP_ThreadPoolAsyncTasks); }

    /** Reference to the owning streaming manager, for accessing the thread-safe data. */
    StreamingManager* StreamingManager;

    /** Indices for load/unload requests, sorted by load order. */
    std::vector<int> LoadRequests;
    std::vector<int> CancelationRequests;

    /** Indices of texture with dirty values for bHasUpdatePending */
    std::vector<int> PendingUpdateDirties;

    /** Whether the async work should abort its processing. */
    volatile bool bAbort;

    /** How much VRAM the hardware has. */
    SInt64 TotalGraphicsMemory;

    /** How much gpu resources are currently allocated in the texture/mesh pool (all category). */
    SInt64 AllocatedMemory;

    /** Size of the pool once non streaming data is removed and value is stabilized */
    SInt64 PoolSize;

    /** How much temp memory is allowed (temp memory is taken when changing LOD count). */
    SInt64 TempMemoryBudget;

    /** How much temp memory is allowed (temp memory is taken when changing LOD count). */
    SInt64 MemoryMargin;

    /** How much memory is available for textures/meshes. */
    SInt64 MemoryBudget;

    /** How much memory is available for meshes if a separate pool is used. */
    SInt64 MeshMemoryBudget;

    /**
     * The value of all required LODs (without memory constraint) used to trigger a budget reset.
     * Whenever the perfect wanted LODs drops significantly, we reset the budget to avoid keeping
     * resolution constraint used to fit that previous situation.
     */
    SInt64 PerfectWantedLODsBudgetResetThresold;

// ----- cross region
public:
    void Execute(const threading::TaskEventPtr& selfTaskEvent)
    {
        DoWork();
    }

    threading::ThreadID GetThreadToExecuteOn() const { return threading::ThreadID::AsyncThread; }

    threading::Priority GetTaskPriority() const { return threading::Priority::Default; }
};

class StreamingCalculationTaskFunction
{
public:
    StreamingCalculationTaskFunction(StreamingCalculationTask* task)
        : mTask(task)
    {}

    void Execute(const threading::TaskEventPtr& selfTaskEvent)
    {
        if (mTask)
        {
            mTask->DoWork();
        }
    }

    threading::ThreadID GetThreadToExecuteOn() const { return threading::ThreadID::AsyncThread; }

    threading::Priority GetTaskPriority() const { return threading::Priority::Default; }

private:
    StreamingCalculationTask* mTask;
};

} // namespace cross
