#include "Streaming/StreamingTask.h"
#include "Streaming/StreamingUtils.h"

namespace cross
{

// Time before we timeout when flushing streaming (default=3)
float GStreamingFlushTimeOut = 3.00f;

volatile std::atomic<int> GRenderAssetStreamingSuspension = 0;

bool IsAssetStreamingSuspended()
{
    return GRenderAssetStreamingSuspension > 0;
}

void SuspendRenderAssetStreaming()
{
    Assert(threading::TaskSystem::IsInGameThread());
    GRenderAssetStreamingSuspension.fetch_add(1);
    if (GRenderAssetStreamingSuspension.load() == 1)
    {
        bool bHasPendingStreamingRequest = false;

        // Wait for all assets to have their update lock unlocked.
        std::vector<StreamableResource*> LockedAssets;
        for (auto It = LockedAssets.begin(); It != LockedAssets.end(); ++It)
        {
            StreamableResource* CurrentAsset = *It;
            if (CurrentAsset && CurrentAsset->IsStreamable() && CurrentAsset->HasPendingInitOrStreaming())
            {
                bHasPendingStreamingRequest = true;
                if (CurrentAsset->IsPendingStreamingRequestLocked())
                {
                    LockedAssets.emplace_back(CurrentAsset);
                }
            }
        }

        // If an asset stays locked for  GStreamingFlushTimeOut,
        // we conclude there is a deadlock or that the object is never going to recover.

        float TimeLimit = GStreamingFlushTimeOut;

        while (LockedAssets.size() && (TimeLimit > 0 || GStreamingFlushTimeOut <= 0))
        {
            std::this_thread::sleep_for(std::chrono::milliseconds(static_cast<SInt64>(StreamableResource::StreamingSleepDeltaTimeInSeconds * 1000)));
            threading::FlushRenderingCommands();

            TimeLimit -= StreamableResource::StreamingSleepDeltaTimeInSeconds;

            for (int LockedIndex = 0; LockedIndex < LockedAssets.size(); ++LockedIndex)
            {
                auto* CurrentAsset = LockedAssets[LockedIndex];
                if (!CurrentAsset || !CurrentAsset->IsPendingStreamingRequestLocked())
                {
                    LockedAssets.erase(LockedAssets.begin() + LockedIndex);
                    --LockedIndex;
                }
            }
        }

        if (TimeLimit <= 0 && GStreamingFlushTimeOut > 0)
        {
            LOG_ERROR("SuspendRenderAssetStreaming timed out while waiting for asset:");
            // TODO:
            //for (int LockedIndex = 0; LockedIndex < LockedAssets.size(); ++LockedIndex)
            //{
            //    StreamableResource* CurrentAsset = LockedAssets[LockedIndex];
            //    if (CurrentAsset)
            //    {
            //        if (!IsValid(CurrentAsset) || CurrentAsset->HasAnyFlags(RF_BeginDestroyed | RF_FinishDestroyed))
            //        {
            //            UE_LOG(LogContentStreaming, Error, TEXT("	%s"), *CurrentAsset->GetFullName());
            //        }
            //        else
            //        {
            //            UE_LOG(LogContentStreaming, Error, TEXT("	%s (PendingKill)"), *CurrentAsset->GetFullName());
            //        }
            //    }
            //}
        }

        // At this point, no more rendercommands or IO requests can be generated before a call to ResumeRenderAssetStreamingRenderTasksInternal().

        if (bHasPendingStreamingRequest)
        {
            // Ensure any pending render command executes.
            threading::FlushRenderingCommands();
        }
    }
}

void ResumeRenderAssetStreaming()
{
    GRenderAssetStreamingSuspension.fetch_sub(1);
    Assert(GRenderAssetStreamingSuspension.load() >= 0);
}

StreamingTaskBase::StreamingTaskBase(const StreamableResource* resource)
    : mResourceState(resource->GetStreamableResourceState())
    , mCurrentFirstLODIndex(resource->GetStreamableResourceState().GetFirstResidentLODIndex())
    , mPendingFirstLODIndex(resource->GetStreamableResourceState().GetFirstRequestedLODIndex())
    , ScheduledGTTasks(0)
    , ScheduledRenderTasks(0)
    , ScheduledAsyncTasks(0)
    , mStreamingResource(resource)
    , bIsCancelled(false)
    , bDeferExecution(false)
    , bSuccess(false)
    , mTaskState(StreamingTaskState::Init)
{
    Assert(resource);
    if (!mResourceState.IsValidForStreamingRequest())
    {
        bIsCancelled = true;
    }
}

StreamingTaskBase::~StreamingTaskBase()
{
    // Work must be done here because derived destructors have been called now and so derived members are invalid.
    Assert(mTaskSynchronization.load() == 0 && mTaskState == StreamingTaskState::Done);
}

void StreamingTaskBase::OnZeroReference() const
{
    if (mTaskState == StreamingTaskState::Done && !mTaskSynchronization.load())
    {
        delete this;
    }
    else
    {
        // Can't delete this object if some other system has some token to decrement.
        LOG_ERROR("RenderAssetUpdate is leaking (State={})", ToUnderlying(mTaskState));
    }
}

void StreamingTaskBase::Tick(threading::ThreadID threadId)
{
    if (mTaskState != StreamingTaskState::Done)
    {
        bool bIsLocked = true;

        // Should we do aynthing about FApp::ShouldUseThreadingForPerformance()? For example to prevent async thread from stalling game/renderthreads.
        // When the renderthread is the gamethread, don't lock if this is the renderthread to prevent stalling on low priority async tasks.
        if (threadId == threading::ThreadID::AnyThread || (threadId == threading::ThreadID::RenderingThread /*&& !GIsThreadedRendering*/))
        {
            bIsLocked = mMutex.try_lock();
        }
        else if (threadId == threading::ThreadID::GameThread)
        {
            // When the GameThread tries to execute the async task, in GC, allow several attempts.
            bIsLocked = mMutex.try_lock();
            threadId = threading::ThreadID::AsyncThread;
        }
        else
        {
            mMutex.lock();
        }

        if (bIsLocked)
        {
            // This will happens in PushTask() or when ScheduleRenderTask() ends up executing the command.
            const bool bWasAlreadyLocked = (mTaskState == StreamingTaskState::Locked);
            mTaskState = StreamingTaskState::Locked;

            StreamingTaskState TickResult;
            do   // Iterate as longs as there is progress
            {
                // Only test for suspension the first time and in normal progress.
                // When cancelled, we want the update to complete without interruptions, allowing reference to be freed.
                TickResult = TickInternal(threadId, !bWasAlreadyLocked && !bIsCancelled);
            } while (TickResult == StreamingTaskState::Locked);

            // We do this to prevent updating the TaskState while in the Lock.
            if (!bWasAlreadyLocked)
            {
                mTaskState = TickResult;
            }
            mMutex.unlock();
        }
    }
}

//class FRenderAssetUpdateTickGTTask
//{
//public:
//    FORCEINLINE FRenderAssetUpdateTickGTTask(FRenderAssetUpdate* InUpdate)
//        : PendingUpdate(InUpdate)
//    {}
//
//    static FORCEINLINE TStatId GetStatId() { RETURN_QUICK_DECLARE_CYCLE_STAT(FRenderAssetUpdateTickGTTask, STATGROUP_TaskGraphTasks); }
//
//    static FORCEINLINE ENamedThreads::Type GetDesiredThread() { return ENamedThreads::GameThread; }
//
//    static FORCEINLINE ESubsequentsMode::Type GetSubsequentsMode() { return ESubsequentsMode::FireAndForget; }
//
//    void DoTask(ENamedThreads::Type CurThread, const FGraphEventRef& MyCompletionGraphEvent)
//    {
//        check(PendingUpdate);
//        PendingUpdate->Tick(FRenderAssetUpdate::TT_GameThread);
//        --PendingUpdate->ScheduledGTTasks;
//    }
//
//private:
//    TRefCountPtr<FRenderAssetUpdate> PendingUpdate;
//};

void StreamingTaskBase::ScheduleGTTask()
{
    Assert(mTaskState == StreamingTaskState::Locked);

    if (threading::TaskSystem::IsInGameThread())
    {
        Tick(threading::ThreadID::GameThread);
    }
    else
    {
        // Notify that a tick is scheduled on the game thread
        ++ScheduledGTTasks;

        threading::Dispatch<threading::ThreadID::GameThreadLocal>([this](auto) {
            Assert(threading::TaskSystem::IsInGameThread());
            Tick(threading::ThreadID::GameThread);
        });
    }
}

void StreamingTaskBase::ScheduleRenderTask()
{
    Assert(mTaskState == StreamingTaskState::Locked);

    // Notify that a tick is scheduled on the render thread.
    ++ScheduledRenderTasks;
    // Increment refcount because we don't use a TRefCountPtr with ENQUEUE_RENDER_COMMAND.
    IncreaseRefCount();

    DispatchRenderingCommandWithToken([this]() {
        // Recompute the context has things might have changed!
        Tick(threading::ThreadID::RenderingThread);

        --ScheduledRenderTasks;

        // Decrement refcount because we don't use a TRefCountPtr with ENQUEUE_RENDER_COMMAND.
        DecreaseRefCount();
    });
}

void StreamingTaskBase::ScheduleAsyncTask()
{
    Assert(mTaskState == StreamingTaskState::Locked);

    // Notify that an async tick is scheduled.
    ++ScheduledAsyncTasks;
    threading::Async([this](auto) {
        // TODO: latency
        Tick(threading::ThreadID::AsyncThread);
        ScheduledAsyncTasks--;
    });
}

// TODO:
//void StreamingTaskBase::FLODUpdateTask::DoWork()
//{
//    FTaskTagScope Scope(ETaskTag::EParallelGameThread);
//    check(PendingUpdate.IsValid());
//
//#if !CROSSENGINE_RELEASE
//    const int32 ExtraSyncLatency = CVarStreamingStressTestExtraAsyncLatency.GetValueOnAnyThread();
//    if (ExtraSyncLatency > 0)
//    {
//        // Slow down the async. Used to test GC issues.
//        FPlatformProcess::Sleep(ExtraSyncLatency * .001f);
//    }
//#endif
//
//    // Recompute the context has things might have changed!
//    PendingUpdate->Tick(threading::ThreadID::AsyncThread);
//
//    --PendingUpdate->ScheduledAsyncTasks;
//}

StreamingTaskState StreamingTaskBase::DoLock()
{
    mMutex.lock();
    StreamingTaskState PreviousTaskState = mTaskState;
    mTaskState = StreamingTaskState::Locked;
    return PreviousTaskState;
}

void StreamingTaskBase::DoUnlock(StreamingTaskState PreviousTaskState)
{
    mTaskState = PreviousTaskState;
    mMutex.unlock();
}

} // namespace cross
