#include "Streaming/StreamableResource.h"
#include "Streaming/StreamingManager.h"
#include "Streaming/StreamingTask.h"
#include "Streaming/StreamingUtils.h"
#include "Streaming/StaticMeshStreamingTask.h"
#include "RenderEngine/VertexStreamLayoutPolicy.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/CameraSystemR.h"

namespace cross
{

StreamableResource::StreamableResource()
{
    SetNoRefStreamingLODBias(-1);
}

StreamableResource::~StreamableResource()
{
    BeginDestroy();

    while (!IsReadyForFinishDestroy())
    {
        ;
    }
}

//#if WITH_EDITOR
//struct FResourceSizeNeedsUpdating
//{
//    static FResourceSizeNeedsUpdating& Get()
//    {
//        static FResourceSizeNeedsUpdating Singleton;
//        return Singleton;
//    }
//
//    void Add(UObject* InObject)
//    {
//        TWeakObjectPtr<UObject> NewValue(InObject);
//        const uint NewHash = GetTypeHash(NewValue);
//
//        FScopeLock ScopeLock(&Lock);
//        const int OriginalNum = Pending.size();
//        Pending.AddByHash(NewHash, MoveTemp(NewValue));
//
//        // Schedule update to occur when not in the middle of a TickStreaming call
//        // TickStreaming can be called by multiple threads
//        // TickStreaming may also possibly be called where OnObjectPropertyChanged could cause problems
//        if (OriginalNum == 0)
//        {
//            FTSTicker::GetCoreTicker().AddTicker(FTickerDelegate::CreateRaw(this, &FResourceSizeNeedsUpdating::BroadcastOnObjectPropertyChanged));
//        }
//    }
//
//private:
//    bool BroadcastOnObjectPropertyChanged(float DeltaTime)
//    {
//        Assert(IsInGameThread());
//        if (IsInGameThread())
//        {
//            FScopeLock ScopeLock(&Lock);
//            if (Pending.size() > 0)
//            {
//                TRACE_CPUPROFILER_EVENT_SCOPE(FResourceSizeNeedsUpdating::BroadcastOnObjectPropertyChanged);
//                FPropertyChangedEvent EmptyPropertyChangedEvent(nullptr);
//                for (const TWeakObjectPtr<UObject>& WeakObjectPtr : Pending)
//                {
//                    if (UObject* Obj = WeakObjectPtr.Get())
//                    {
//                        // Note: This is too expensive 0.05 to 3 seconds per call, needs to be re-written in a more performance friendly manner
//                        FCoreUObjectDelegates::OnObjectPropertyChanged.Broadcast(Obj, EmptyPropertyChangedEvent);
//                    }
//                }
//                Pending.Empty();
//            }
//        }
//
//        // Return false because we only wanted one tick
//        return false;
//    }
//
//    FCriticalSection Lock;
//    TSet<TWeakObjectPtr<UObject>> Pending;
//};
//#endif   // WITH_EDITOR

int StreamableResource::CalcCumulativeLODSize(int NumLODs) const
{
    int sum = 0;
    const int LODCount = GetLODCount();
    const int LastLODIdx = LODCount - NumLODs;

    if (!GetMeshAssetDataResource() || NumLODs > LODCount)
    {
        return 0;
    }

    for (int Idx = LODCount - 1; Idx >= LastLODIdx; --Idx)
    {
        const auto* meshAssetData = GetMeshAssetDataResource()->GetAssetData();
        const auto* renderMesh = TYPE_CAST(MeshR*, meshAssetData->GetRenderMesh());

        sum += renderMesh->GetLODMeshBufferSize(static_cast<UInt8>(Idx));
    }
    return sum;
}

void StreamableResource::TickStreaming(bool bSendCompletionEvents)
{
    // if resident and requested LOD counts match then no pending request is in flight
    if (PendingUpdate)
    {
        // When there is no renderthread, allow the gamethread to tick as the renderthread.
        PendingUpdate->Tick(threading::TaskSystem::Get()->UseSeparateRenderingThread() ? threading::ThreadID::AnyThread : threading::ThreadID::RenderingThread);

        if (PendingUpdate->IsCompleted())
        {
            if (PendingUpdate->IsSuccessfullyFinished())
            {
                mCachedState.ResidentLODCount = mCachedState.RequestedLODCount;
                Assert(mCachedState.ResidentLODCount > 0);
                //LOG_DEBUG("Streaming: Streamable task for {} is completed, resident LOD count is {}", GetFullName(), mCachedState.ResidentLODCount);
            }
            else
            {
                if (!PendingUpdate->IsCancelled())
                {
                    LOG_ERROR("Invalid completion of streaming request for asset {} of type {}.", GetFullName(), ToUnderlying(GetRenderAssetType()));
                    Assert(false);
                }
                mCachedState.RequestedLODCount = mCachedState.ResidentLODCount;
            }

            PendingUpdate.reset();

            // TODO:
            //if (StreamableRenderAsset::bAllowUpdateResourceSize && StreamingUtils::IsEditor() && bSendCompletionEvents)
            //{
            //     // When all the requested LODs are streamed in, generate an empty property changed event, to force the
            //     // ResourceSize asset registry tag to be recalculated.
            //     FResourceSizeNeedsUpdating::Get().Add(this);
            //}
        }
    }
}

StreamableResourceType StreamableResource::GetRenderAssetType() const
{
    return mResourceType;
}

void StreamableResource::SetForceLODLevelsToBeResident(float Seconds)
{
    ForceLODLevelsToBeResidentTimestamp = time::CurrentMs() / 1000.0 + Seconds;
}

void StreamableResource::CancelPendingStreamingRequest()
{
    if (PendingUpdate && !PendingUpdate->IsCancelled())
    {
        std::string taskName = "";
        if (std::dynamic_pointer_cast<StaticMeshStreamInTask>(PendingUpdate))
        {
            taskName = "IN";
        }
        else if (std::dynamic_pointer_cast<StaticMeshStreamOutTask>(PendingUpdate))
        {
            taskName = "OUT";
        }
        LOG_DEBUG("Streaming: {} task {} is cancelled at Frame {}", GetFullName(), taskName, EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameCount());
        PendingUpdate->Abort();
    }
}

bool StreamableResource::HasPendingInitOrStreaming(bool bWaitForLODTransition) const
{
    if (!!PendingUpdate)
    {
        return true;
    }

    if (mCachedState.IsValidForStreamingRequest())
    {
        // Avoid a cache miss unless the hint suggests Init could be pending.
        if (mCachedState.bHasPendingInitHint)
        {
            if (HasPendingRenderResourceInitialization())
            {
                return true;
            }
            else if (threading::TaskSystem::IsInGameThread())
            {
                mCachedState.bHasPendingInitHint = false;
            }
        }
        if (bWaitForLODTransition && mCachedState.bHasPendingLODTransitionHint)
        {
            if (HasPendingLODTransition())
            {
                return true;
            }
            else if (threading::TaskSystem::IsInGameThread())
            {
                mCachedState.bHasPendingLODTransitionHint = false;
            }
        }
        return false;
    }

    return HasPendingRenderResourceInitialization() || (bWaitForLODTransition && HasPendingLODTransition());
}

/** Whether there is a pending update and it is locked within an update step. Used to prevent dealocks in SuspendRenderAssetStreaming(). */
bool StreamableResource::IsPendingStreamingRequestLocked() const
{
    return PendingUpdate && PendingUpdate->IsLocked();
}

bool StreamableResource::StreamOut(int NewLODCount)
{
    Assert(threading::TaskSystem::IsInGameThread());
    if (!HasPendingInitOrStreaming() && mCachedState.ValidateStreamOut(NewLODCount))
    {
        // We need to keep the CPU data in non cook in order to be able for tools to work correctly.
        PendingUpdate = std::make_shared<StaticMeshStreamOutTask>(this, false /*discard cpu data*/);
        return !PendingUpdate->IsCancelled();
    }
    else
    {
        Assert(false);
    }
    return false;
}

bool StreamableResource::StreamIn(int NewLODCount, bool bHighPrio)
{
    Assert(threading::TaskSystem::IsInGameThread());
    if (!HasPendingInitOrStreaming() && mCachedState.ValidateStreamIn(NewLODCount))
    {
        //FRenderAssetUpdate::EThreadType CreateResourcesThread = GRHISupportsAsyncTextureCreation ? FRenderAssetUpdate::TT_Async : FRenderAssetUpdate::TT_Render;
        {
            PendingUpdate = std::make_shared<StaticMeshStreamInTask>(this, bHighPrio, threading::ThreadID::AsyncThread);
        }
        return !PendingUpdate->IsCancelled();
    }
    else
    {
        Assert(false);
    }
    return false;
}

void StreamableResource::RegisterToManager()
{
    // Note that this must be called after InitResource() otherwise IsStreamable will always be false.
    StreamableResourceType RenderAssetType = GetRenderAssetType();
    if (/*!IsTemplate() && */ RenderResourceSupportsStreaming() && StreamingManager::GetInstance().IsRenderAssetStreamingEnabled(RenderAssetType))
    {
        if (StreamingIndex == INDEX_NONE)
        {
            //if (RenderAssetType == StreamableResourceType::NaniteCoarseMesh)
            //{
            //    IStreamingManager::Get().GetNaniteCoarseMeshStreamingManager()->RegisterRenderAsset(this);
            //}
            //else
            {
                //IStreamingManager::Get().GetRenderAssetStreamingManager().AddStreamingRenderAsset(this);
                StreamingManager::GetInstance().AddStreamingRenderAsset(this);
                Assert(StreamingIndex != INDEX_NONE);
            }
        }
    }
    else
    {
        UnregisterFromManager();
    }
}

void StreamableResource::UnregisterFromManager()
{
    if (StreamingIndex != INDEX_NONE)
    {
        //StreamableResourceType RenderAssetType = GetRenderAssetType();
        //if (RenderAssetType == StreamableResourceType::NaniteCoarseMesh)
        //{
        //    IStreamingManager::Get().GetNaniteCoarseMeshStreamingManager()->UnregisterRenderAsset(this);
        //}
        //else
        {
            StreamingManager::GetInstance().RemoveStreamingRenderAsset(this);
        }

        // Reset the timer effect from SetForceLODLevelsToBeResident()
        ForceLODLevelsToBeResidentTimestamp = 0;
        // No more streaming events can happen now
        //RemoveAllLODLevelChangeCallbacks();
    }
}

bool StreamableResource::IsFullyStreamedIn()
{
    // consider a texture fully streamed when it hits this number of LODs :
    //	MaxNumLODs has already been reduced by the "drop LOD" LOD Bias
    //	Note that just subtracting off NumCinematicLODLevels is not the right way to get the cinematic lod bias
    //	it should be CalculateLODBias(false) , but we don't have that information here
    int FullyStreamedNumLODs = mCachedState.LODCount;

    // Note that if mCachedState is not valid, then this asset is not streamable and is then at max resolution.
    if (!mCachedState.IsValidForStreamingRequest() || !mCachedState.EnableStreaming || mCachedState.ResidentLODCount >= FullyStreamedNumLODs)
    {
        return true;
    }

//#if WITH_EDITOR
//    UPackage* Package = GetOutermost();
//    if (Package && Package->bIsCookedForEditor && mCachedState.NumNonOptionalLODs < mCachedState.MaxNumLODs && IStreamingManager::Get().IsRenderAssetStreamingEnabled(EStreamableRenderAssetType::None))
//    {
//        return IStreamingManager::Get().GetRenderAssetStreamingManager().IsFullyStreamedIn(this);
//    }
//#endif

    // IsFullyStreamedIn() might be used incorrectly if any logic waits on it to be true.
    // there could be optional LODs which are not available to be loaded, so waiting on IsFullyStreamedIn would never finish
    //ensureMsgf(mCachedState.ResidentLODCount != mCachedState.NumNonOptionalLODs, TEXT("IsFullyStreamedIn() is being called on (%s) which might not have optional LODs mounted."), *GetFName().ToString());

    return false;
}

void StreamableResource::WaitForPendingInitOrStreaming(bool bWaitForLODTransition, bool bSendCompletionEvents)
{
    SCOPED_CPU_TIMING(GroupStreaming, "StreamableResource::WaitForPendingInitOrStreaming");

    // this can be called with IsAssetStreamingSuspended == true
    //	because Interchange Tasks due to PostEditChange do Texture UpdateResource
    //	those tasks can be retracted in the D3D RHI Wait which runs in the Viewport resize
    //	which turns off streaming
    // @todo : Viewport resize should not turn off streaming

    while (HasPendingInitOrStreaming(bWaitForLODTransition))
    {
        // Advance the streaming state.
        TickStreaming(bSendCompletionEvents);
        // Make sure any render commands are executed, in particular things like InitRHI, or asset updates on the render thread.
        threading::FlushRenderingCommands();

        // Most of the time, sleeping is not required, so avoid loosing a whole quantum (10ms on W10Pro) unless stricly necessary.
        if (HasPendingInitOrStreaming(bWaitForLODTransition))
        {
            // try to make sure streaming is enabled before doing horrible busy wait
            if (!IsAssetStreamingSuspended())
            {
                return;
            }

            // Give some time increment so that LOD transition can complete, and also for the gamethread to give room for streaming async tasks.
            std::this_thread::sleep_for(std::chrono::milliseconds(static_cast<SInt64>(StreamingSleepDeltaTimeInSeconds * 1000)));
        }
    }
}

void StreamableResource::WaitForStreaming(bool bWaitForLODTransition, bool bSendCompletionEvents)
{
    // Complete pending streaming so that the asset can executing new requests if needed.
    WaitForPendingInitOrStreaming(bWaitForLODTransition, bSendCompletionEvents);

    if (IsStreamable())
    {
        // Update the streamer state for this asset and execute new requests if needed. For example force loading to all LODs.
        StreamingManager::GetInstance().UpdateIndividualRenderAsset(this);
        // Wait for any action to complete.
        WaitForPendingInitOrStreaming(bWaitForLODTransition, bSendCompletionEvents);
    }
}

void StreamableResource::BeginDestroy()
{
    // Abort any pending streaming operation.
    CancelPendingStreamingRequest();

    // Safely unlink the asset from list of streamable.
    UnregisterFromManager();

    // Remove from the list of tracked assets if necessary
    //TrackRenderAssetEvent(nullptr, this, false, nullptr);
}

bool StreamableResource::IsReadyForFinishDestroy()
{
    if (PendingUpdate)
    {
        LOG_DEBUG("Streaming: {} is waiting for PendingUpdate destroyed", GetFullName());
        // To avoid async tasks from timing-out the GC, we tick as Async to force completion if this is relevant.
        // This could lead the asset from releasing the PendingUpdate, which will be deleted once the async task completes.
        if (PendingUpdate->GetRelevantThread() == threading::ThreadID::AsyncThread)
        {
            //PendingUpdate->Tick(threading::ThreadID::TT_GameRunningAsync);
            PendingUpdate->Tick(threading::ThreadID::GameThread);
        }
        else
        {
            PendingUpdate->Tick(threading::TaskSystem::Get()->UseSeparateRenderingThread() ? threading::ThreadID::AnyThread : threading::ThreadID::RenderingThread);
        }

        if (PendingUpdate->IsCompleted())
        {
            PendingUpdate.reset();
        }
    }

    return (PendingUpdate == nullptr);
}

int StreamableResource::GetCurrentNoRefStreamingLODBias() const
{
    return GetNoRefStreamingLODBias();
}

void StreamableResource::InitResource()
{
    //bRenderingResourcesInitialized = true;

    //UpdateUVChannelData(false);

    //mCachedState.Clear();
    auto meshAssetDataResource = GetMeshAssetDataResource();
    if (meshAssetDataResource)
    {
        auto* meshAssetData = meshAssetDataResource->GetAssetData();
        auto* renderMesh = TYPE_CAST(MeshR*, meshAssetDataResource->GetAssetData()->GetRenderMesh());

        auto pipelineSetting = EngineGlobal::GetSettingMgr()->GetRenderPipelineSetting();

        // In LOD selection process, LOD bias will be taken into account, thus there is no need to apply bias during initialization
        const SInt8 NumLODs = static_cast<SInt8>(meshAssetData->GetLodCount());
        const SInt8 MinFirstLOD = 0/*static_cast<SInt8>(pipelineSetting->GlobalLODBias)*/;

        mCachedState.NonStreamingLODCount = 0 /*GetRenderData()->NumInlinedLODs*/;
        // mCachedState.NumNonOptionalLODs = GetRenderData()->GetNumNonOptionalLODs();
        //  Limit the number of LODs based on MinLOD value.
        mCachedState.LODCount = std::clamp<SInt8>(NumLODs - MinFirstLOD, mCachedState.NonStreamingLODCount, NumLODs);
        // mCachedState.AssetLODBias = MinFirstLOD;
        // mCachedState.LODBiasModifier = GetRenderData()->LODBiasModifier; // TODO: This may be needed for foliage
        mCachedState.LODBias = MinFirstLOD;
        // The optional LOD might be culled now.
        // mCachedState.NumNonOptionalLODs = std::min(mCachedState.NumNonOptionalLODs, mCachedState.LODCount);
        // Set LOD count to fit the current state.
        mCachedState.ResidentLODCount = 0 /*NumLODs - 0*/ /*GetRenderData()->CurrentFirstLODIdx*/;
        mCachedState.RequestedLODCount = mCachedState.ResidentLODCount;
        // Set whether the LODs can be streamed.
        // TODO:
        NeverStream = false;
        mCachedState.EnableStreaming = !NeverStream && mCachedState.NonStreamingLODCount != mCachedState.LODCount;

        // TODO : Update RenderData->CurrentFirstLODIdx based on whether IStreamingManager::Get().IsRenderAssetStreamingEnabled(EStreamableRenderAssetType::StaticMesh).
        // TODO : This will require to refactor code in FStaticMeshLODResources::Serialize() and FStaticMeshRenderData::Cache() around bBuffersInlined (in cooked).

        //UWorld* World = GetWorld();
        //GetRenderData()->InitResources(World ? World->GetFeatureLevel() : ERHIFeatureLevel::Num, this);
        //CachedSRRState.bHasPendingInitHint = true;

        mResourceType = StreamableResourceType::StaticMesh;
    }

    RegisterToManager();
}

} // namespace cross
