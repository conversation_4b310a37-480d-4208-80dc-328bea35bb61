#pragma once
#include "CECommon/GameWorld/IGameWorld.h"
#include "Resource/ParticleSystem/ParticleEmitterInfo.h"

namespace cross::fx {

enum class CEMeta(Editor) ParticleDebugEventType
{
    None = 0,
    DrawShape,
    DrawKillVolume,
    DebugRestart,
    DebugStop
};

struct ParticleSystemDebugEvent
{
    CEMeta(Editor)
    ParticleDebugEventType eventType = ParticleDebugEventType::None;

    CEMeta(Editor)
    SInt32 emitterIndex = -1;

    CEMeta(Editor)
    bool activate = true;
};

CEMeta(Editor)
ENGINE_API void DrawEmitterShape(cross::IGameWorld* world, UInt64 entity, const LocationShapeInfo& shape, bool selected);

CEMeta(Editor)
ENGINE_API void DrawEmitterKillVolume(cross::IGameWorld* world, UInt64 entity, const Float3& center, const Float3& tile, const Float4x4& transform, const Float3& extent, const Float3& rotation, bool selected);

}