#pragma once
#include <vector>
#include <unordered_map>
#include "CrossBase/String/UniqueString.h"
#include "CrossBase/Serialization/SerializeNode.h"
#include "ECS/Develop/Framework/Types.h"
#include "CECommon/Common/SettingsManager.h"

namespace cross {
    enum class ModuleType
    {
        <PERSON><PERSON>ult,
        GameOnly,
        EditorOnly,
    };
    struct EngineModuleInfo
    {
        ModuleType mModuleType = ModuleType::Default;
        cross::SerializeNode mGameSystemList;
        cross::SerializeNode mGlobalSystemList;
        cross::SerializeNode mDependencies;
        cross::SerializeNode mComponentPairs;
        gbf::IModule* mLoadedModule = nullptr;
        std::string mModuleFullPath = "";
        bool mEnable;
    };

    class EngineModuleConfig
    {
    public:
        EngineModuleConfig();
        ~EngineModuleConfig();
        void EnumerateModulesFromConfig(const cross::SerializeNode& engineConfig, const cross::SerializeNode& projectConfig, cross::AppStartUpType type);
        void LoadAllConfigModules();
        auto& GetLoadedModulesInfo()
        {
            return mModuleListFromConfig;
        }

        void InitModules();
    private:
        std::unordered_map<UniqueString, EngineModuleInfo, UniqueString::hash32> mModuleListFromConfig;
    };
}