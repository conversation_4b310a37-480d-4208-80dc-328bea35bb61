#pragma once

#include <memory>
#include "CECommon/GameWorld/IGameWorld.h"
#include "CrossBase/Math/CrossMath.h"
#include "CrossPhysics/PhysicsEngine/PhysicsGeometry.h"
#include "CrossPhysics/PhysicsEngine/PhysicsShape.h"
#include "Runtime/Animation/Skeleton/SkeletonPhysicsResource.h"
#include "CECommon/Common/RenderPipelineSetting.h"
#include "Runtime/Input/LagacyInput.h"
#include "CECommon/Common/SettingsManager.h"
#include "CrossPhysics/PhysicsEngine/PhysicsScene.h"
#include "Resource/TextureResourceInfo.h"
#include "Resource/TextureResource.h"
#include "CECommon/Common/EntityHashDefines.h"
#include "Runtime/GameWorld/ReflectionProbeSystemG.h"
#include "Runtime/GameWorld/WorldPartition/WorldLoadingSystemG.h"


namespace cross {
CEMeta(Editor) void GetSkeletonPhysicsResourceData(const char* path, cross::skeleton::SkeletonPhysicsResourceData& outVal);
CEMeta(Editor) void SetSkeletonPhysicsResourceDataInResorce(const char* path, const cross::skeleton::SkeletonPhysicsResourceData& inValue);
CEMeta(Editor) void SetSkeletonPhysicsResourceDataInEntity(GameWorld* world, UInt64 entity, const cross::skeleton::SkeletonPhysicsResourceData& inValue);

CEMeta(Editor) void SetWorldPhysicsDebugView(cross::IGameWorld* world, const cross::PhysicsSceneDebugViewOption& option);

struct RayPickSkeletonPhysicsForMSASceneResult
{
    CEMeta(Editor) bool isHitBone = true; //If true, hit a bone, otherwise hit a joint
    CEMeta(Editor) Float3 hitPos;

    //for bone
    CEMeta(Editor) UInt32 boneIndex = (std::numeric_limits<UInt32>::max)();
    CEMeta(Editor) PhysicsShapeType geoType;
    CEMeta(Editor) UInt32 geoIndex;

    //for joint
    CEMeta(Editor) UInt32 jointIndex = (std::numeric_limits<UInt32>::max)();
};
CEMeta(Editor) RayPickSkeletonPhysicsForMSASceneResult RayPickSkeletonPhysicsForMSAScene(cross::IGameWorld* world, UInt64 previewSkeletonEntityId, const TRSVector3Type& start, const Float3& direction);

CEMeta(Editor) void GetReflectionProbe(cross::IGameWorld* world, UInt64 entity, cross::ReflectionProbeComponentG& outValue);
CEMeta(Editor) void SetReflectionProbe(cross::IGameWorld* world, UInt64 entity, const cross::ReflectionProbeComponentG& inValue);
CEMeta(Editor) void SetReflectionProbeBakeState(cross::IGameWorld* world, bool state);
CEMeta(Editor) void BakeOneReflectionProbe(cross::IGameWorld* world, UInt64 entity);
CEMeta(Editor) void SetReflectionProbeShowState(cross::IGameWorld* world, bool state);
CEMeta(Editor) bool IsReflectionProbeShow(cross::IGameWorld* world);
CEMeta(Editor) void SetBakeRPPathCallback(cross::IGameWorld* world, UInt64 entity, EditorBakedReflectionProbePathCallBack callback);

CEMeta(Editor) void GetTextureInfoFromResource(cross::Resource* resource, TextureResourceInfo& texureInfo);
CEMeta(Editor) bool SetTexture2DArrayTexures(cross::Resource* resource, const std::vector<std::string>& paths);
CEMeta(Editor) bool GetTexture2DArrayTexures(cross::Resource* resource, std::vector<std::string>& paths);
CEMeta(Editor) void SetTexture2DArrayEnableVT(cross::Resource* resource, bool enable);
CEMeta(Editor) bool GetTexture2DArrayEnableVT(cross::Resource* resource);
CEMeta(Editor) void SetTexture2DArrayEnableMergeVT(cross::Resource* resource, bool enable);
CEMeta(Editor) bool GetTexture2DArrayEnableMergeVT(cross::Resource* resource);

CEMeta(Editor) cross::SettingsManager& GetSettingManager();

CEMeta(Editor) bool GetBlockPaths(const std::string& worldPath, std::vector<std::string>& outBlocks);
CEMeta(Editor) bool GetResourceDependencies(const std::string& resourcePath, std::vector<std::string>& outDependencyPaths);

CEMeta(Editor) bool IsWorldLoaded(GameWorld* world);
CEMeta(Editor) bool WorldEntityWGS84CurvationCorrection(GameWorld* world, UInt64 entity, bool recurisive);
CEMeta(Editor) bool SaveWorldBack(GameWorld* world, const std::string& path);
CEMeta(Editor) bool IsDrawingWorldPartitionGrid(GameWorld* world);
CEMeta(Editor) void SetDrawWorldPartitionGrid(GameWorld* world, bool isDraw);
CEMeta(Editor) EntityHashWay GetEntityHashWay(GameWorld* world, UInt64 entity);
CEMeta(Editor) void SetEntityHashWay(GameWorld* world, UInt64 entity, EntityHashWay hashWay);
CEMeta(Editor) std::string GetEntityBlockInfo(GameWorld* world, UInt64 entity); // x y z l
CEMeta(Editor) Float3 GetWorldPartitionOrigin(GameWorld* world);
CEMeta(Editor) void SetWorldPartitionPosition(GameWorld* world, Float3 worldPosition);
CEMeta(Editor) WorldLoadingPolicy GetWorldPartitionLoadingPolicy(GameWorld* world);   // 1: Distance, 2: All // TODO(yuanwan): Export policy enum
CEMeta(Editor) void SetWorldPartitionLoadingPolicy(GameWorld* world, WorldLoadingPolicy policy);
CEMeta(Editor) UInt32 GetWorldBlockCount(GameWorld* world);
CEMeta(Editor) UInt32 GetWorldLoadingBlockCount(GameWorld* world);
CEMeta(Editor) const WorldPartitionConfiguration& GetWorldPartitionConfig(GameWorld* world);
CEMeta(Editor) void SetWorldPartitionConfig(GameWorld* world, const WorldPartitionConfiguration& newConfig);
CEMeta(Editor) void AddWorldCustomBlock(GameWorld* world, const std::string& blockId);
CEMeta(Editor) std::vector<std::string> GetAllWorldBlockIds(GameWorld* world);
CEMeta(Editor) bool ChangeEntityBlock(GameWorld* world, UInt64 entity, const std::string& blockId);
CEMeta(Editor) bool IsEntityAlive(GameWorld* world, UInt64 entity);
CEMeta(Editor) std::string GetWorldScriptPath(GameWorld* world);
CEMeta(Editor) void SetWorldScriptPath(GameWorld* world, const std::string& path);

CEMeta(Editor) Float3 GetModelCenter(GameWorld* world, UInt64 entity);
CEMeta(Editor) Float3 GetModelExtents(GameWorld* world, UInt64 entity);
CEMeta(Editor) UInt32 GetModelAssetLODCount(GameWorld* world, UInt64 entity, UInt32 modelIndex);
CEMeta(Editor) std::string GetModelLODMaterialPath(GameWorld* world, UInt64 entity, UInt32 modelIndex, UInt32 subModelIndex, UInt32 lodIndex);
CEMeta(Editor) void SetModelLODMaterialPath(GameWorld* world, UInt64 entity, UInt32 modelIndex, UInt32 subModelIndex, UInt32 lodIndex, const std::string& materialPath);
CEMeta(Editor) void ToggleNoDrawMode();
CEMeta(Editor) void StartPIEPlay();
CEMeta(Editor) void StopPIEPlay();
CEMeta(Editor) std::string GenerateTypeScriptDeclaration(bool buildIn);
CEMeta(Editor) std::string GenerateTypeScriptEnumImpl(bool buildIn);
}
