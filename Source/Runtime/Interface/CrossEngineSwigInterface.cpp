#include "EnginePrefix.h"

#include "Runtime/Interface/CrossEngineSwigInterface.h"
#include "ECS/Develop/Framework/Types.h"
// #include "Runtime/Resource/MeshAssetDataResource.h"
// #include "Runtime/Animation/Skeleton/SkeletonResource.h"
// #include "Runtime/Animation/Skeleton/SkeletonPhysicsResource.h"

#include "Runtime/GameWorld/GameWorld.h"

#include "CECommon/Common/GameSystemBase.h"
#include "Runtime/GameWorld/PhysicsSystemG.h"
#include "PhysicsEngine/PhysicsGeometry.h"
#include "Resource/AssetStreaming.h"
#include "Resource/ResourceManager.h"
#include "Runtime/Animation/Skeleton/SkeletonPhysicsResource.h"
#include "Runtime/GameWorld/SkeletonSystemG.h"
#include "Runtime/Animation/Skeleton/SkeletonComponent.h"
#include "Resource/Texture/Texture.h"
#include "Runtime/GameWorld/ModelSystemG.h"
#include "Runtime/GameWorld/AABBSystemG.h"
#include "Runtime/GameWorld/WorldPartition/WorldLoadingSystemG.h"
#include "Runtime/GameWorld/RenderPipelineSystemG.h"
#include "Runtime/GameWorld/EntityMetaSystem.h"
#include "Runtime/GameWorld/WorldPartition/EntityHashSystemG.h"
#include "Runtime/GameWorld/WorldPartition/WorldPartitionBlock.h"
#include "Runtime/GameWorld/RendererSystemG.h"
#include "Runtime/GameWorld/TransformSystemG.h"
#include "Runtime/Interface/CrossEngine.h"
#include "Runtime/Interface/CrossEngineImp.h"
#include "Resource/Texture/Texture2DArray.h"
#include "Runtime/GameWorld/FFSWGS84SystemG.h"
#include "Runtime/GameWorld/FoliageSystemG.h"
#include "Runtime/Audio/AudioEngine.h"
#include "TypeScriptEngine/TypeScriptModule.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CrossBase/Log.h"

namespace cross
{
void GetSkeletonPhysicsResourceData(const char* path, cross::skeleton::SkeletonPhysicsResourceData& outVal)
{
    if (!path)
        return;

    auto resPtr = gAssetStreamingManager->LoadSynchronously(path);
    cross::skeleton::SkeltPhyResPtr skeltPhyRes = cross::TypeCast<cross::skeleton::SkeletonPhysicsResource>(resPtr);
    outVal = skeltPhyRes->GetData();
}

void SetSkeletonPhysicsResourceDataInResorce(const char* path, const cross::skeleton::SkeletonPhysicsResourceData& inValue)
{
    if (!path)
        return;

    auto resPtr = gAssetStreamingManager->LoadSynchronously(path);
    cross::skeleton::SkeltPhyResPtr skeltPhyRes = cross::TypeCast<cross::skeleton::SkeletonPhysicsResource>(resPtr);
    skeltPhyRes->SetData(inValue);
}

void SetSkeletonPhysicsResourceDataInEntity(GameWorld* world, UInt64 entity, const cross::skeleton::SkeletonPhysicsResourceData& inValue)
{
    cross::ecs::EntityID entityId = cross::ecs::EntityID(entity);
    auto handle = world->GetComponent<cross::SkeletonComponentG>(entityId);
    auto sys = world->GetGameSystem<cross::SkeletonSystemG>();
    sys->UpdateSkeletonPhysicsData(handle.Write(), inValue);
}

void SetWorldPhysicsDebugView(cross::IGameWorld* world, const PhysicsSceneDebugViewOption& option)
{
    cross::GameWorld* gworld = (cross::GameWorld*)world;
    auto sys = gworld->GetGameSystem<cross::PhysicsSystemG>();
    sys->SetDebugViewOption(option);
}

RayPickSkeletonPhysicsForMSASceneResult RayPickSkeletonPhysicsForMSAScene(cross::IGameWorld* world, UInt64 previewSkeletonEntityId, const TRSVector3Type& start, const Float3& direction)
{
    RayPickSkeletonPhysicsForMSASceneResult ret;

    cross::GameWorld* gworld = (cross::GameWorld*)world;
    auto phySys = gworld->GetGameSystem<PhysicsSystemG>();
    std::array<PhysicsHitResult,32u> hitResults;
    UInt32 hitCount = phySys->SceneQuery()->RayCastMulti(start, direction.Normalized(), 1e6, CollisionMask::All(), &hitResults[0], 32U);

    if (hitCount>0)
    {
        // find joint if possible
        static auto isJoint = [](const PhysicsHitResult& item) { return !item.hitActor->GetCustomData<CustomPhyData>()->entityId; };
        auto itEnd = hitResults.begin()+hitCount;
        auto it = std::find_if(hitResults.begin(), itEnd, [](const PhysicsHitResult& item) { return isJoint(item); });

        if (it == itEnd)   // can't find joint
        {
            const PhysicsHitResult& result = hitResults[0];   // use the first(nearest) hit result
            ret.isHitBone = true;
            ret.hitPos = {
                result.position.x,
                result.position.y,
                result.position.z,
            };
            if (result.hitActor->GetCustomData<CustomPhyData>()->entityId.GetValue() == previewSkeletonEntityId)
            {
                ret.boneIndex = result.hitActor->GetCustomData<CustomPhyData>()->boneHandle;
                ret.geoType = result.hitShape->GetShapeType();
                ret.geoIndex = result.hitShape->GetCustomData<CustomPhyShapeData>()->geoIndex;
            }
        }
        else   // hit a joint
        {
            ret.isHitBone = false;
            ret.hitPos = {it->position.x, it->position.y, it->position.z};
            ret.jointIndex = it->hitShape->GetCustomData<CustomPhyShapeData>()->geoIndex;
        }
    }
    return ret;
}

void GetReflectionProbe(cross::IGameWorld* world, UInt64 entity, cross::ReflectionProbeComponentG& outValue)
{
    cross::ecs::EntityID entityId = cross::ecs::EntityID(entity);
    cross::GameWorld* gworld = (cross::GameWorld*)world;
    auto handle = gworld->GetComponent<cross::ReflectionProbeComponentG>(entityId);
    auto rpSys = ((cross::GameWorld*)world)->GetGameSystem<cross::ReflectionProbeSystemG>();
    rpSys->GetReflectionProbeComponent(handle.Read(), &outValue);
}

void SetReflectionProbe(cross::IGameWorld* world, UInt64 entity, const cross::ReflectionProbeComponentG& inValue)
{
    cross::ecs::EntityID entityId = cross::ecs::EntityID(entity);
    cross::GameWorld* gworld = (cross::GameWorld*)world;
    auto handle = gworld->GetComponent<cross::ReflectionProbeComponentG>(entityId);
    auto rpSys = ((cross::GameWorld*)world)->GetGameSystem<cross::ReflectionProbeSystemG>();
    rpSys->SetReflectionProbeComponent(handle.Write(), &inValue);
}

void SetReflectionProbeBakeState(cross::IGameWorld* world, bool state)
{
    auto rpSys = ((cross::GameWorld*)world)->GetGameSystem<cross::ReflectionProbeSystemG>();
    rpSys->SetBakeReflectionProbeState(state);
}

void BakeOneReflectionProbe(cross::IGameWorld* world, UInt64 entity)
{
    auto rpSys = ((cross::GameWorld*)world)->GetGameSystem<cross::ReflectionProbeSystemG>();
    rpSys->BakeOneReflectionProbe(entity);
}

void SetReflectionProbeShowState(cross::IGameWorld* world, bool state)
{
    auto rpSys = ((cross::GameWorld*)world)->GetGameSystem<cross::ReflectionProbeSystemG>();
    rpSys->SetShowReflectionProbeState(state);
}

bool IsReflectionProbeShow(cross::IGameWorld* world)
{
    auto rpSys = ((cross::GameWorld*)world)->GetGameSystem<cross::ReflectionProbeSystemG>();
    return rpSys->IsShowReflectionProbe();
}

void SetBakeRPPathCallback(cross::IGameWorld* world, UInt64 entity, EditorBakedReflectionProbePathCallBack callback)
{
    cross::ecs::EntityID entityId = cross::ecs::EntityID(entity);
    cross::GameWorld* gworld = (cross::GameWorld*)world;
    auto handle = gworld->GetComponent<cross::ReflectionProbeComponentG>(entityId);
    auto rpSys = ((cross::GameWorld*)world)->GetGameSystem<cross::ReflectionProbeSystemG>();
    rpSys->SetFetchBakedRPPathCallBack(handle.Write(), callback);
}

void GetTextureInfoFromResource(cross::Resource* resource, TextureResourceInfo& texureInfo)
{
    auto texRes = TYPE_CAST(resource::Texture*, resource);
    if (texRes && texRes->GetTextureData())
    {
        texureInfo = texRes->GetTextureData()->mResourceInfo;
    }
}

bool SetTexture2DArrayTexures(cross::Resource* resource, const std::vector<std::string>& paths)
{
    auto texRes = TYPE_CAST(resource::Texture2DArray*, resource);
    if (texRes)
    {
        return texRes->SetTextures(paths);
    }
    return false;
}

bool GetTexture2DArrayTexures(cross::Resource* resource, std::vector<std::string>& paths)
{
    auto texRes = TYPE_CAST(resource::Texture2DArray*, resource);
    if (texRes)
    {
        paths = texRes->GetTextures();
        return true;
    }
    return false;
}

void SetTexture2DArrayEnableVT(cross::Resource* resource, bool enable)
{
    auto texRes = TYPE_CAST(resource::Texture2DArray*, resource);
    if (texRes)
    {
        return texRes->SetEnableVTStreaming(enable);
    }
}

bool GetTexture2DArrayEnableVT(cross::Resource* resource)
{
    auto texRes = TYPE_CAST(resource::Texture2DArray*, resource);
    if (texRes)
    {
        return texRes->GetEnableVTStreaming();
    }
    return false;
}

void SetTexture2DArrayEnableMergeVT(cross::Resource* resource, bool enable)
{
    auto texRes = TYPE_CAST(resource::Texture2DArray*, resource);
    if (texRes)
    {
        return texRes->SetEnableMergeVT(enable);
    }
}

bool GetTexture2DArrayEnableMergeVT(cross::Resource* resource)
{
    auto texRes = TYPE_CAST(resource::Texture2DArray*, resource);
    if (texRes)
    {
        return texRes->GetEnableMergeVT();
    }
    return false;
}

cross::SettingsManager& GetSettingManager()
{
    return *EngineGlobal::GetSettingMgr();
}

bool GetBlockPaths(const std::string& worldPath, std::vector<std::string>& outBlocks)
{
#if CROSSENGINE_EDITOR
    return WorldLoadingSystemG::GetBlockPaths(worldPath, outBlocks);
#else // CROSSENGINE_EDITOR
    return false;
#endif // CROSSENGINE_EDITOR
}

bool GetResourceDependencies(const std::string& resourcePath, std::vector<std::string>& outDependencyPaths)
{
#if CROSSENGINE_EDITOR
    return gResourceMgr.GetResourceDependencies(resourcePath.c_str(), outDependencyPaths);
#else // CROSSENGINE_EDITOR 
    return false;
#endif // CROSSENGINE_EDITOR 
}

bool IsWorldLoaded(GameWorld* world)
{
    return world->IsLoaded();
}

bool WorldEntityWGS84CurvationCorrection(GameWorld* world, UInt64 entity, bool recurisive)
{
    auto tWGSComps = world->Query<FFSWGS84ComponentG>();
    if (tWGSComps.GetEntityNum() <= 0)
        return false;
    auto modelSys = world->GetGameSystem<ModelSystemG>();
    auto foliageSys = world->GetGameSystem<FoliageSystemG>();
    // wgs 
    auto wgsComp = tWGSComps[0].Read();
    const double longitude = wgsComp->mDoubleWGS84.mLongitude;
    const double latitude = wgsComp->mDoubleWGS84.mLatitude;
    const double elevation = wgsComp->mDoubleWGS84.mElevation;
    std::unordered_set<std::string> traverseMeshs;

    auto HandleEntity = [modelSys, foliageSys, longitude, latitude, elevation, &traverseMeshs](GameWorld* world, const ecs::EntityID& ent) 
    {
        if (auto modelCom = world->GetComponent<ModelComponentG>(ent))
        {
            auto modelHandle = modelCom.Read();
            for (UInt32 idx = 0; idx < modelSys->GetModelCount(modelHandle); idx++)
            {
                auto mesh = modelSys->GetModelAsset(modelHandle, idx);
                const std::string& meshPath = mesh->GetName();
                if (traverseMeshs.find(meshPath) != traverseMeshs.end())
                    continue;
                traverseMeshs.insert(meshPath);
                WGS84SystemG::Mesh_WGS84CurvationCorrection(mesh.get(), longitude, latitude, elevation, editor::kSystemUnit::UNIT_CM);
                mesh->Serialize(meshPath);
#if CROSSENGINE_EDITOR
                gResourceMgr.TryReloadResource(meshPath);
#endif   // CROSSENGINE_EDITOR 
            }
            return;
        }
        if (auto foliageCom = world->GetComponent<FoliageComponentG>(ent))
        {
            auto foliageHandle = foliageCom.Write();
            foliageSys->TraverseInstanceData(foliageHandle, [longitude, latitude, elevation](InstanceDataVecContainer::InstanceData& insData) {
                insData.mTransform.translation = static_cast<Float3>(WGS84SystemG::WGS84CurvationCorrection((Double3)insData.mTransform.translation, longitude, latitude, elevation, editor::kSystemUnit::UNIT_CM));
            });
            foliageSys->TraverseInstanceDataLight(foliageHandle, [longitude, latitude, elevation](InstanceDataVecContainer::InstanceDataLight& insData) {
                insData.mTransform.translation = static_cast<Float3>(WGS84SystemG::WGS84CurvationCorrection((Double3)insData.mTransform.translation, longitude, latitude, elevation, editor::kSystemUnit::UNIT_CM));
                insData.mLightDirPos.x = insData.mTransform.translation.x;
                insData.mLightDirPos.y = insData.mTransform.translation.y;
                insData.mLightDirPos.z = insData.mTransform.translation.z;
            });
            foliageSys->SetGlobalScale(foliageHandle, foliageSys->GetGlobalScale(foliageCom.Read()));
            return;
        }
    };
    if (recurisive) {
        auto tranSys = world->GetGameSystem<TransformSystemG>();
        tranSys->TraverseHierarchyDepth(ecs::EntityID(entity), [&HandleEntity, world](ecs::EntityID ent) { HandleEntity(world, ent); });
    }
    else
    {
        HandleEntity(world, ecs::EntityID(entity));
    }
    return true;
}

bool SaveWorldBack(GameWorld* world, const std::string& path)
{
    auto backPath = EngineGlobal::GetFileSystem()->GetAbsoluteBackPath(path);
    threading::Dispatch([world, backPath](const auto&) { world->SaveWorld(backPath); });
    return true;
}

bool IsDrawingWorldPartitionGrid(GameWorld* world)
{
    return world->GetGameSystem<WorldLoadingSystemG>()->IsDrawingGrid();
}

void SetDrawWorldPartitionGrid(GameWorld* world, bool isDraw)
{
    world->GetGameSystem<WorldLoadingSystemG>()->SetDrawGrid(isDraw);
}

EntityHashWay GetEntityHashWay(GameWorld* world, UInt64 entity)
{
    auto reader = world->GetComponent<cross::WorldTransformComponentG>(cross::ecs::EntityID(entity)).Read();
    return world->GetGameSystem<TransformSystemG>()->GetEntityHashWay(reader);
}

void SetEntityHashWay(GameWorld* world, UInt64 entity, EntityHashWay hashWay)
{
    auto writer = world->GetComponent<cross::WorldTransformComponentG>(cross::ecs::EntityID(entity)).Write();
    world->GetGameSystem<TransformSystemG>()->SetEntityHashWay(writer, hashWay);
}

std::string GetEntityBlockInfo(GameWorld* world, UInt64 entity)
{
    auto entityId = cross::ecs::EntityID(entity);
    if (!world || !world->IsEntityAlive(entityId))
        return "Error";   // error
    if (entityId == world->GetGameSystem<TransformSystemG>()->GetRootEntity())
        return WorldPartitionBlockGrid::PreBlockID.GetString();   // preload
    auto metaReader = world->GetComponent<cross::ecs::EntityMetaComponentG>(entityId).Read();
    auto* blockPtr = world->GetGameSystem<EntityMetaSystem>()->GetBelongedBlockInstance(metaReader);
    return blockPtr ? blockPtr->GetBlockId().GetString() : "Error";
}

Float3 GetWorldPartitionOrigin(GameWorld* world)
{
    return world->GetGameSystem<WorldLoadingSystemG>()->GetOrigin();
}

void SetWorldPartitionPosition(GameWorld* world, Float3 worldPosition)
{
    world->GetGameSystem<WorldLoadingSystemG>()->SetPosition(worldPosition);
}

WorldLoadingPolicy GetWorldPartitionLoadingPolicy(GameWorld* world)
{
    return world->GetGameSystem<WorldLoadingSystemG>()->GetWorldPartitionPolicy();
}

void SetWorldPartitionLoadingPolicy(GameWorld* world, WorldLoadingPolicy policy)
{
    world->GetGameSystem<WorldLoadingSystemG>()->SetWorldPartitionPolicy(policy);
}

UInt32 GetWorldBlockCount(GameWorld* world)
{
    auto loadSys = world->GetGameSystem<WorldLoadingSystemG>();
    return loadSys->GetBlockCount();
}

UInt32 GetWorldLoadingBlockCount(GameWorld* world)
{
    auto loadSys = world->GetGameSystem<WorldLoadingSystemG>();
    return loadSys->GetBlockLoadingCount();
}

const WorldPartitionConfiguration& GetWorldPartitionConfig(GameWorld* world)
{
    auto loadSys = world->GetGameSystem<WorldLoadingSystemG>();
    return *loadSys->GetWorldPartitionConfiguration();
}

void SetWorldPartitionConfig(GameWorld* world, const WorldPartitionConfiguration& newConfig)
{
    auto loadSys = world->GetGameSystem<WorldLoadingSystemG>();
    loadSys->SetWorldPartitionConfiguration(newConfig);
}

void AddWorldCustomBlock(GameWorld* world, const std::string& blockId)
{
    auto loadSys = world->GetGameSystem<WorldLoadingSystemG>();
    loadSys->AddCustomBlock(blockId);
}

std::vector<std::string> GetAllWorldBlockIds(GameWorld* world)
{
    auto loadSys = world->GetGameSystem<WorldLoadingSystemG>();
    return loadSys->GetAllBlockIds();
}

bool ChangeEntityBlock(GameWorld* world, UInt64 entity, const std::string& blockId)
{
    auto entityId = cross::ecs::EntityID(entity);
    auto loadSys = world->GetGameSystem<WorldLoadingSystemG>();
    return loadSys->ChangeEntityBlock(entity, blockId);
}

bool IsEntityAlive(GameWorld* world, UInt64 entity)
{
    auto entityId = cross::ecs::EntityID(entity);
    return world->IsEntityAlive(entityId);
}

std::string GetWorldScriptPath(GameWorld* world)
{
    return world->GetGameSystem<WorldLoadingSystemG>()->GetWorldScript();
}

void SetWorldScriptPath(GameWorld* world, const std::string& path)
{
    world->GetGameSystem<WorldLoadingSystemG>()->SetWorldScript(path);
}

Float3 GetModelCenter(GameWorld* world, UInt64 entity)
{
    auto aabbH = world->GetComponent<AABBComponentG>(entity);
    Float3 center = Float3::Zero();
    if (aabbH.IsValid())
    {
        auto* aabbSys = world->GetGameSystem<AABBSystemG>();
        const auto& worldAABB = aabbSys->GetWorldAABB(aabbH.Read());
        worldAABB.GetCenter(&center);
    }
    return center;
}

Float3 GetModelExtents(GameWorld* world, UInt64 entity)
{
    auto aabbH = world->GetComponent<AABBComponentG>(entity);
    Float3 extents = Float3::Zero();
    if (aabbH.IsValid())
    {
        auto* aabbSys = world->GetGameSystem<AABBSystemG>();
        const auto& worldAABB = aabbSys->GetWorldAABB(aabbH.Read());
        worldAABB.GetExtent(&extents);
    }
    return extents;
}

UInt32 GetModelAssetLODCount(GameWorld* world, UInt64 entity, UInt32 modelIndex)
{
    auto* modelSys = world->GetGameSystem<ModelSystemG>();
    auto modelCompH = world->GetComponent<ModelComponentG>(cross::ecs::EntityID(entity));
    auto asset = modelSys->GetModelAsset(modelCompH.Read(), modelIndex);
    if (asset && asset->GetAssetData()) return asset->GetAssetData()->GetLodCount();
    return 0;
}

std::string GetModelLODMaterialPath(GameWorld* world, UInt64 entity, UInt32 modelIndex, UInt32 subModelIndex, UInt32 lodIndex)
{
    auto* modelSys = world->GetGameSystem<ModelSystemG>();
    auto modelCompH = world->GetComponent<ModelComponentG>(cross::ecs::EntityID(entity));
    auto material = modelSys->GetModelLodMaterial(modelCompH.Read(), subModelIndex, lodIndex, modelIndex);
    if (material) return material->GetName();
    return "";
}

void SetModelLODMaterialPath(GameWorld* world, UInt64 entity, UInt32 modelIndex, UInt32 subModelIndex, UInt32 lodIndex, const std::string& materialPath)
{
    auto* modelSys = world->GetGameSystem<ModelSystemG>();
    auto modelCompH = world->GetComponent<ModelComponentG>(cross::ecs::EntityID(entity));
    modelSys->SetModelLodMaterialPath(modelCompH.Write(), materialPath, subModelIndex, lodIndex, modelIndex);
}
void ToggleNoDrawMode()
{
    cross::EngineGlobal::GetEngine()->GetGlobalSystem<RendererSystemG>()->ToggleNoDrawWorldMode();
}

void StartPIEPlay()
{
    TypeScriptModule::Instance()->Stop();
    TypeScriptModule::Instance()->Start(true);
    auto& global = EngineGlobal::Inst();
	global.GetAudioEngine()->Begin();
}

void StopPIEPlay()
{
    auto& global = EngineGlobal::Inst();
	global.GetAudioEngine()->End();
}

std::string GenerateTypeScriptDeclaration(bool buildIn)
{
    return TypeScriptModule::Instance()->GenerateTypeScriptDeclaration(buildIn);
}

std::string GenerateTypeScriptEnumImpl(bool buildIn)
{
    return TypeScriptModule::Instance()->GenerateTypeScriptEnumImpl(buildIn);
}

}
