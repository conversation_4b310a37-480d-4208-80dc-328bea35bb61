#include "EnginePrefix.h"

static HMODULE gModule;
HMODULE GetDllModuleHandle()
{
	return gModule;
}

#if defined(CROSSENGINE_WIN32)
bool APIENTRY DllMain(HMODULE hModule,
	DWORD  ul_reason_for_call,
	LPVOID /*lpReserved*/
	)
{
	switch (ul_reason_for_call)
	{
	case DLL_PROCESS_ATTACH:
		// save module handle used for loading embedded resources.
		gModule = hModule;
		break;
	case DLL_THREAD_ATTACH:
		//printf("thread attached\n");
		break;
	case DLL_THREAD_DETACH:
		//printf("thread detached\n");
		break;
	case DLL_PROCESS_DETACH:
		//printf("process detached\n");
		break;

	}
	return TRUE;
}
#endif
