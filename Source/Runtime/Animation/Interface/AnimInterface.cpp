#include "EnginePrefix.h"
#include <typeinfo>
#include "Runtime/Animation/Interface/AnimInterface.h"

#include "CECommon/Common/GameSystemBase.h"
#include "CECommon/Animation/Curve/FloatCurve.h"

#include "Resource/AssetStreaming.h"
#include "Resource/MeshAssetDataResource.h"

#include "Runtime/GameWorld/GameWorld.h"
#include "Runtime/GameWorld/SkeletonSystemG.h"
#include "Runtime/GameWorld/AnimatorSystemG.h"
#include "Runtime/GameWorld/SkeltSocketSystemG.h"
#include "Runtime/GameWorld/PrimitiveRenderSystemG.h"
#include "Runtime/GameWorld/ModelSystemG.h"
#include "Runtime/GameWorld/MeshBlendShapeSystemG.h"
#include "RenderEngine/PrimitiveGenerator.h"



namespace cross {

/*
 * Get RefSkeleton from Skeleton, Skeletal Mesh, AnimSequence.
 */
static auto GetRefSkeletonFromAsset(const char* assetPath) -> std::pair<const cross::skeleton::ReferenceSkeleton*, cross::editor::MsaHierItemType>
{
    const cross::skeleton::ReferenceSkeleton* refSkeltPtr = nullptr;

    auto resPtr = gAssetStreamingManager->LoadSynchronously(assetPath);
    if (resPtr == nullptr)
        return std::make_pair(nullptr, cross::editor::MsaHierItemType::UnKnown);

    if (resPtr->GetClassID() == ClassID(MeshAssetDataResource))
    {
        cross::MeshAssetDataResourcePtr meshResPtr = cross::TypeCast<cross::resource::MeshAssetDataResource>(resPtr);
        if (meshResPtr.get() == nullptr)
            return std::make_pair(nullptr, cross::editor::MsaHierItemType::UnKnown);

        refSkeltPtr = meshResPtr->GetRefSkeleton();
        return std::make_pair(refSkeltPtr, cross::editor::MsaHierItemType::SkeletalMeshAsset);
    }
    else if (resPtr->GetClassID() == ClassID(SkeletonResource))
    {
        cross::skeleton::SkeltResPtr skeltResPtr = cross::TypeCast<cross::skeleton::SkeletonResource>(resPtr);
        if (skeltResPtr.get() == nullptr)
            return std::make_pair(nullptr, cross::editor::MsaHierItemType::UnKnown);

        refSkeltPtr = &skeltResPtr->GetStreamingSkeleton().GetReferenceSkeleton();
        return std::make_pair(refSkeltPtr, cross::editor::MsaHierItemType::SkeletonAsset);
    }
    else if (resPtr->GetClassID() == ClassID(AnimSequenceRes))
    {
        cross::anim::AnimSeqResPtr animSeqResPtr = cross::TypeCast<cross::anim::AnimSequenceRes>(resPtr);
        if (animSeqResPtr.get() == nullptr)
            return std::make_pair(nullptr, cross::editor::MsaHierItemType::UnKnown);

        refSkeltPtr = &animSeqResPtr->GetStreamingAnimation()->RefSkeleton;
        return std::make_pair(refSkeltPtr, cross::editor::MsaHierItemType::AnimSequenceAsset);
    }

    return std::make_pair(nullptr, cross::editor::MsaHierItemType::UnKnown);
}

void DrawLine(cross::IGameWorld* world, const cross::Float3& startPos, const cross::Float3& endPos, bool isSelected)
{
    cross::GameWorld* gworld = (cross::GameWorld*)world;
    auto primitiveSystem = gworld->GetGameSystem<cross::PrimitiveRenderSystemG>();

    cross::PrimitiveData linePrim;
    cross::PrimitiveGenerator::GenerateLine(&linePrim, startPos, endPos);

    if (isSelected)
        primitiveSystem->DrawPrimitive(&linePrim, cross::Float4x4::Identity(), cross::PrimitiveRenderSystemG::PrimitiveLook(ColorRGBAf(1.0f, 0.0f, 0.0f)));
    else
        primitiveSystem->DrawPrimitive(&linePrim, cross::Float4x4::Identity(), cross::PrimitiveRenderSystemG::PrimitiveLook(ColorRGBAf(1.0f, 1.0f, 1.0f)));
}

void DrawSphereFrame(cross::IGameWorld* world, float radius, const cross::NodeTransform& worldTransform, bool isSelected)
{
    cross::GameWorld* gworld = (cross::GameWorld*)world;
    auto primitiveSystem = gworld->GetGameSystem<cross::PrimitiveRenderSystemG>();

    auto trans = worldTransform;
    trans.SetScale(Float3(1.0f, 1.0f, 1.0f));
    cross::Float4x4 sphereTrans = trans.GetTransformMatrix();

    cross::PrimitiveData spherePrim;
    cross::PrimitiveGenerator::GenerateSphereFrame(&spherePrim, radius, 4, 3);

    if (isSelected)
        primitiveSystem->DrawPrimitive(&spherePrim, sphereTrans, cross::PrimitiveRenderSystemG::PrimitiveLook(ColorRGBAf(1.0f, 0.0f, 0.0f)));
    else
        primitiveSystem->DrawPrimitive(&spherePrim, sphereTrans, cross::PrimitiveRenderSystemG::PrimitiveLook(ColorRGBAf(1.0f, 1.0f, 1.0f)));
}

int GetRefSkBoneNumFromAsset(const char* assetPath)
{
    auto result = GetRefSkeletonFromAsset(assetPath);
    return result.first == nullptr ? -1 : static_cast<int>(result.first->GetRawBoneNum());
}

bool GetRefSkBoneDataFromAsset(const char* assetPath, int boneIndex, cross::editor::BoneData& outRefSkBoneData)
{
    auto result = GetRefSkeletonFromAsset(assetPath);
    auto refSkeltPtr = result.first;

    if (refSkeltPtr != nullptr)
    {
        const auto boneNum = refSkeltPtr->GetRawBoneNum();
        Assert(boneNum > boneIndex && "Preview array size should equals asset ref-skelt bone count");

        outRefSkBoneData.Name = refSkeltPtr->GetRawBoneName({static_cast<UInt32>(boneIndex)});
        outRefSkBoneData.BoneIndex = boneIndex;
        outRefSkBoneData.ParentIndex = (boneIndex == 0) ? -1 : refSkeltPtr->GetRawBoneParentIndex({static_cast<UInt32>(boneIndex)});

        // grab children indices
        SkBoneHandle skeltSize = {static_cast<UInt32>(boneNum)};
        for (SkBoneHandle j = {0}; j < skeltSize; ++j)
        {
            if (refSkeltPtr->GetRawBoneParentIndex(j) == static_cast<UInt32>(boneIndex))
                outRefSkBoneData.ChildrenIndices.emplace_back(j);
        }

        // grab bone type
        outRefSkBoneData.BoneType = (result.second == cross::editor::MsaHierItemType::SkeletonAsset) ? 
            cross::editor::MsaHierItemType::RunSkeltBone : cross::editor::MsaHierItemType::RefSkeltBone;

        // grab bone bind pose local space transform
        const auto& translate = refSkeltPtr->GetRawRefBonePoseTranslate()[boneIndex];
        const auto& rotation = refSkeltPtr->GetRawRefBonePoseRotate()[boneIndex];
        const auto& scale = refSkeltPtr->GetRawRefBonePoseScale()[boneIndex];
        outRefSkBoneData.LocalSpaceTransform = NodeTransform(scale, rotation, translate);

        // grab bone bind pose root space transform
        const auto& curBoneWorldTrans = refSkeltPtr->GetRawRefBonePoseWorldMatirx()[boneIndex];
        outRefSkBoneData.RootSpaceTransform = NodeTransform(curBoneWorldTrans);

        return true;
    }

    return false;
}

bool GetBoneTransformFromSkeletonComp(cross::IGameWorld* world, UInt64 entity, int boneIndex, cross::editor::BoneData& outBoneData)
{
    cross::ecs::EntityID entityId{entity};
    cross::GameWorld* gworld = (cross::GameWorld*)world;
    auto skeletonHandle = gworld->GetComponent<cross::SkeletonComponentG>(entityId);
    cross::anim::SkeltPosePtr skPosePtr = skeletonHandle.Read()->PosePtr;

    if (skPosePtr != nullptr)
    {
        // grab bone local space transform from local space pose
        cross::NodeTransform boneTransLocalSpace = skPosePtr->GetLocalSpaceTransform({static_cast<UInt32>(boneIndex)});
        outBoneData.LocalSpaceTransform = NodeTransform(boneTransLocalSpace.GetScale(), boneTransLocalSpace.GetRotation(), boneTransLocalSpace.GetTranslation());

        // grab bone root space transform from root space pose
        cross::NodeTransform boneTransRootSpace = skPosePtr->GetRootSpaceTransform({static_cast<UInt32>(boneIndex)});
        outBoneData.RootSpaceTransform = NodeTransform(boneTransRootSpace.GetScale(), boneTransRootSpace.GetRotation(), boneTransRootSpace.GetTranslation());

        return true;
    }

    return false;
}

cross::editor::MsaCompatibleType IsSkeletonCompatible(const char* runSkeltPath, const char* assetPath, 
    cross::editor::BoneData& outRunSkMissedBone, cross::editor::BoneData& outResRefSkMissedBone)
{
    auto resPtr = gAssetStreamingManager->LoadSynchronously(runSkeltPath);
    cross::skeleton::SkeltResPtr skeltResPtr = cross::TypeCast<cross::skeleton::SkeletonResource>(resPtr);
    if (skeltResPtr.get() == nullptr)
    {
        return cross::editor::MsaCompatibleType::RunSkeltNoSkelt;
    }

    auto assetResult = GetRefSkeletonFromAsset(assetPath);
    auto assetRefSkelt = assetResult.first;
    if (assetRefSkelt == nullptr)
    {
        return cross::editor::MsaCompatibleType::AssetNoSkelt;
    }

    cross::skeleton::SkBoneHandle runSkMissedBone = cross::skeleton::SkBoneHandle::InvalidHandle();
    cross::skeleton::SkBoneHandle resRefSkMissedBone = cross::skeleton::SkBoneHandle::InvalidHandle();
    if (ReferenceSkeleton::IsCompatible(skeltResPtr->GetStreamingSkeleton().GetReferenceSkeleton(), *assetRefSkelt, &runSkMissedBone, &resRefSkMissedBone))
    {
        return cross::editor::MsaCompatibleType::Success;
    }

    GetRefSkBoneDataFromAsset(runSkeltPath, runSkMissedBone, outRunSkMissedBone);
    GetRefSkBoneDataFromAsset(assetPath, resRefSkMissedBone, outResRefSkMissedBone);
    return cross::editor::MsaCompatibleType::CompatibleFailed;
}

void ResetSkPoseToRefPose(cross::IGameWorld* world, UInt64 entity)
{
    cross::ecs::EntityID entityId{entity};
    cross::GameWorld* gworld = (cross::GameWorld*)world;
    auto skeletonHandle = gworld->GetComponent<cross::SkeletonComponentG>(entityId);
    auto skeletonSys = gworld->GetGameSystem<cross::SkeletonSystemG>();

    skeletonSys->ResetSkPoseToRefPose(skeletonHandle.Write());
}

void DrawBoneWithParent(cross::IGameWorld* world, const cross::editor::BoneData& inCurBone, const cross::editor::BoneData& inParentBone, bool isSelected)
{
    const auto& parentTranslate = inParentBone.RootSpaceTransform.GetTranslation();
    const auto& selfTranslate = inCurBone.RootSpaceTransform.GetTranslation();
    Float3 normal = selfTranslate - parentTranslate;
    normal.Normalize();

    Float3 vec1(-normal.y, normal.x, 0.0f);
    Float3 vec2(0, -normal.z, normal.y);
    vec1.Normalize();
    vec2.Normalize();

    Float3 pos1 = parentTranslate + vec1 * 1.5f;
    Float3 pos2 = parentTranslate - vec1 * 1.5f;
    Float3 pos3 = parentTranslate + vec2 * 1.5f;
    Float3 pos4 = parentTranslate - vec2 * 1.5f;

    DrawLine(world, pos1, selfTranslate, isSelected);
    DrawLine(world, pos2, selfTranslate, isSelected);
    DrawLine(world, pos3, selfTranslate, isSelected);
    DrawLine(world, pos4, selfTranslate, isSelected);

    DrawLine(world, pos1, pos3, isSelected);
    DrawLine(world, pos3, pos2, isSelected);
    DrawLine(world, pos2, pos4, isSelected);
    DrawLine(world, pos4, pos1, isSelected);

    DrawBoneWithoutParent(world, inCurBone, isSelected);
}

void DrawBoneWithoutParent(cross::IGameWorld* world, const cross::editor::BoneData& inCurBone, bool isSelected) 
{
    DrawSphereFrame(world, 1.5f, inCurBone.RootSpaceTransform, isSelected);
}

}   // namespace cross