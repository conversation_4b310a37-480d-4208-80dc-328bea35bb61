#pragma once
#include "Resource/BaseClasses/ClassIDs.h"
#include "Resource/Resource.h"
#include "PhysicsEngine/PhysicsGeometry.h"
#include "PhysicsEngine/PhysicsJoint.h"
#include "CEAnimation/AnimBase.h"
#include "PhysicsEngine/PhysicsShape.h"

namespace cross::skeleton {
struct SkeletonJoint
{
    CEMeta(<PERSON><PERSON><PERSON>, Editor)
    cross::anim::CEName mFromBone;

    CEMeta(Serial<PERSON>, Editor)
    cross::anim::CEName mToBone;

    CEMeta(<PERSON><PERSON><PERSON>, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Struct"))
    cross::PhysicsJointConfig mConfig;

    CE_Serialize_Deserialize;
};

struct SkeletonPhysicsResourceData
{
    CEMeta(<PERSON><PERSON><PERSON>, Editor)
    CollisionType mNodesCollisionType = CollisionType::Actor;

    CEMeta(Serialize, Editor)
    CollisionMask mNodesCollisionMask = {CollisionType::WorldStatic, CollisionType::WorldDynamic};

    CEMeta(<PERSON><PERSON><PERSON>, Editor)
    std::map<cross::anim::CEName, PhysicsSimpleCollision> mNodes;
    
    CEMeta(<PERSON><PERSON><PERSON>, Editor)
    std::vector<SkeletonJoint> mJoints;

    CE_Serialize_Deserialize;
};

class CEMeta(Script) SkeletonPhysicsResource : public Resource
{
public:
    FRIEND_WITH_REFLECTION_MODULE;

    SkeletonPhysicsResource() = default;
    ~SkeletonPhysicsResource() override = default;

    static int GetClassIDStatic()
    {
        return ClassID(SkeletonPhysicsResource);
    }

    bool Deserialize(const DeserializeNode& s) override;
    bool Serialize(SerializeNode&& s, const std::string& path) override;

    const PhysicsSimpleCollision* GetNode(const cross::anim::CEName& boneName) const;
    const std::vector<SkeletonJoint>& GetJoints() const { return mData.mJoints; }

    // For cs export
    const SkeletonPhysicsResourceData& GetData() { return mData; }
    void SetData(const SkeletonPhysicsResourceData& data) { mData = data; }
    // End for cs export

protected:
    SkeletonPhysicsResourceData mData;
};
}
