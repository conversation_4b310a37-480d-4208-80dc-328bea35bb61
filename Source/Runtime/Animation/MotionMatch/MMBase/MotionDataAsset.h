#pragma once
#include "MotionMatchConfig.h"
#define USE_FAST_SEARCH
#ifdef USE_FAST_SEARCH
#if CROSSENGINE_OSX || CROSSENGINE_IOS || CROSSENGINE_ANDROID
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wshorten-64-to-32"
#endif
#include "hnswlib.h"
#include "CrossBase/CEMetaMacros.h"
#if CROSSENGINE_OSX || CROSSENGINE_IOS || CROSSENGINE_ANDROID
#pragma clang diagnostic pop
#endif

#endif

namespace cross::anim
{

//#ifdef CROSS_EDITOR
    struct CEMeta(Cli) MotionMatchResourceSetting
    {
        CEMeta(Cli)
        int PoseMatching = 0;
        /// <summary>
        /// 
        /// </summary>
        /// 
        CEMeta(Cli)
        int valid = 0;
        // flatten dataset at y axis?
        CEMeta(Cli)
        int flattenY = 0;
        // interval to extract pose;
        CEMeta(Cli)
        float poseInterval = 0.1f;

        // extracted feature from [startClamp, Length() - endClamp] of an animation
        CEMeta(Cli)
        float startAnimClamp = 1.0f;
        CEMeta(Cli)
        float endAnimClamp = -1.0f;
        CEMeta(Cli)
        int TrajSize = 0;
        CEMeta(Cli)
        std::vector<float> TrajTimes;

        /// <summary>
        ///  weights for calculate feature dists
        /// </summary>
        CEMeta(Cli)
        float QualityVsResponse = 0.5f;
        CEMeta(Cli)
        float BodyVelocityWeight = 1.0f;
        CEMeta(Cli)
        float BodyRotVelocityWeight = 1.0f;
        CEMeta(Cli)
        float TrajTranslationWeight = 1.0f;
        CEMeta(Cli)
        float TrajFacingWeight = 1.0f;
        CEMeta(Cli)
        float PoseTranslationWeight = 1.0f;
        CEMeta(Cli)
        float PoseVelocityWeight = 1.0f;
        /// <summary>
        ///  visualize settings
        /// </summary>
        CEMeta(Cli)
        int PreviewAnimID = -1;
        CEMeta(Cli)
        bool PredictedTrajectory = false;
        CEMeta(Cli)
        bool SelectedTrajectory = false;
        CEMeta(Cli)
        bool BonePoses = false;
    };


    struct CEMeta(Cli) MotionMatchResourceStatistics
    {
        CEMeta(Cli)
        float MMFeatureSizeInMemory =-1.f;
        CEMeta(Cli)
        float MMMinSpeed = -1.f;
        CEMeta(Cli)
        float MMAverageSpeed = -1.f;
        CEMeta(Cli)
        float MMMaxSpeed = -1.f;
        CEMeta(Cli)
        int   MMPoseNumber = -1;
        CEMeta(Cli)
        int   FeatureNum = -1;
    };
//#endif

    using BodyCostFunc = std::function<float(const Float3& current_body_velo, const float current_body_rot_velo, const Float3& feature_body_velo, const float feature_body_rot_velo, float weight_velo, float weight_rot_velo)>;
    using PoseCostFunc = std::function<float(const Float3& current_bone_pos, const Float3& current_bone_velo, const Float3& feature_bone_pos, const Float3& feature_bone_velo, float weight_pos, float weight_velo, int i)>;
    using TrajCostFunc = std::function<float(const Float3& current_traj_pos, const float current_traj_facing, const Float3& feature_traj_pos, const float feature_traj_facing, float weight_pos, float weight_facing, int i)>;

    class ENGINE_API CEMeta(Cli, Script) MotionDataAsset : public AnimationAnalyzer
    {
    public:
        enum Feature {
            Body,
            Pose,
            Traj
        };

    public:
        MotionDataAsset():AnimationAnalyzer() {}
        MotionDataAsset(const CEName & name, const std::vector<CEName>& referenceBoneNames, const TrajGenConfig& inConfig, bool flattenY = false) :AnimationAnalyzer(name, referenceBoneNames, flattenY), MMConfig(inConfig) {}
        MotionDataAsset(const CEName& name, const std::vector<CEName>& animations, const std::vector<CEName>& referenceBoneNames, const TrajGenConfig& inConfig, bool flattenY = false) :
            AnimationAnalyzer(name, animations, referenceBoneNames, flattenY), MMConfig(inConfig) {}

        virtual void ConstructAnimationReference(int animationIndex, const AnimSequencePtr inAnimSeq, std::vector<MotionMatchPoseDesc>& mAnimDescs, std::vector<MotionMatchPoseData>& mAnimDatas,
            float StartClamp = 1.0f, float EndClamp = -1.0f) override;
        void GenerateStandardDeviationWeights(const WeightControl& weights, float QualityVsReponsive = 0.5f);

        const auto& GetNormalizedWeight() const
        {
            return mNormalizedWeights;
        }

        const auto& GetFinalWeights() const
        {
            return mFinalWeights;
        }

        const auto& GetConfig() const { return MMConfig; }
        CEMeta(Cli)
        static cross::Resource* CreateMotionMatchData(const char* animation_sequence);

        CEMeta(Cli)
        void ClearMotionMatchSequences()
        {
            mAnimationSequences.clear();
        }
        CEMeta(Cli)
        int AddMotionMatchSequencePath(const char* path)
        {
            mAnimationSequences.push_back(path);
            return static_cast<int>(mAnimationSequences.size());
        }
        CEMeta(Cli)
        int AddMotionMatchJointName(const char* joint_name)
        {
            mReferencedJoints.push_back(joint_name);
            return static_cast<int>(mReferencedJoints.size());
        }
        CEMeta(Cli)
        void ClearMotionMatchJoints()
        {
            mReferencedJoints.clear();
        }
        CEMeta(Cli)
        int GetMotionMatchJointNum()
        {
            return static_cast<int>(mReferencedJoints.size());
        }
        CEMeta(Cli)
        void EditorSetMotionMatchResourceSetting(MotionMatchResourceSetting setting);
        CEMeta(Cli)
        MotionMatchResourceSetting EditorGetMotionMatchResourceSetting();
        CEMeta(Cli)
        MotionMatchResourceStatistics GetMotionMatchResourceStatistics();

        CEMeta(Cli)
        int GetMotionMatchSequenceNum()
        {
            return static_cast<int>(mAnimationSequences.size());
        }

         CEMeta(Cli)
         std::string  GetMotionMatchSequencePath(int id)
         {
             if (id < mAnimationSequences.size())
             {
                 std::string name = mAnimationSequences[id].GetCString();
                 return name;
             }
             return "";
         }
         CEMeta(Cli)
         bool CookMotionMatchData()
         {
             InitializeDatabase();
             // TODO(yazhenyuan): -> weights not needed
             GenerateStandardDeviationWeights(WeightControl::GetDefaultWeights(), 0.5f);
             return true;
         }

        CEMeta(Cli)
        std::string GetMotionMatchJointName(int id);

        virtual int SearchForBestPose(const MotionMatchPoseData& currentPose, float& poseCost, float& trajCost, float& bodyCost, float& currentSmallest) const;

        void ExtractDataFromAnimation(MotionMatchingPose& Pose, const AnimSequencePtr InSequence, const int AtSrcAnimIndex, const float AtSrcStartTime)
        {
            // our animation source
            Pose.mPoseInfo.SrcAnimIndex = AtSrcAnimIndex;

            //the start time.
            Pose.mPoseInfo.StartTime = AtSrcStartTime;

            ExtractJointDataFromAnimation(Pose, InSequence, AtSrcAnimIndex, AtSrcStartTime);

            // Extract Trajectory;
            ExtractTrajectoryDataFromAnimation(Pose, InSequence, AtSrcAnimIndex, AtSrcStartTime);
        }

        void ExtractJointDataFromAnimation(MotionMatchingPose& Pose, const AnimSequencePtr InSequence, const int AtSrcAnimIndex, const float AtSrcStartTime)
        {
            /// Translation Velocity and Rotation Velocity
            UMotionMatchingHelpers::GetAnimVelocityBoth(InSequence, AtSrcStartTime, MMConfig.PoseInterval, Pose.mPoseData.PresentVel, Pose.mPoseData.RotVelocity);

            // Extract Joint
            Pose.mPoseData.MotionJointData.resize(mReferencedJoints.size());
            for (int i = 0; i < mReferencedJoints.size(); i++)
            {
                //Our joint data
                UMotionMatchingHelpers::GetAnimJointData(InSequence, Pose.mPoseInfo.StartTime, mReferencedJoints[i], Pose.mPoseData.MotionJointData[i], MMConfig.PoseInterval, false);
            }
        }

        void ExtractTrajectoryDataFromAnimation(MotionMatchingPose& Pose, const AnimSequencePtr InSequence, const int AtSrcAnimIndex, const float AtSrcStartTime)
        {
#ifndef _MANAGED
            Pose.mPoseData.TrajectoryData.resize(MMConfig.mTrajTimes.size());

            for (int i = 0; i < MMConfig.mTrajTimes.size(); i++)
            {
                auto rootTrans = InSequence->ExtractRootMotionFromDeltaTime({ Pose.mPoseInfo.StartTime }, MMConfig.mTrajTimes[i], true);

                Pose.mPoseData.TrajectoryData[i].CurrentRefTransform = mFlattenY ? UMotionMatchingHelpers::FlattenRootMotionY(rootTrans) : rootTrans;

                Pose.mPoseData.TrajectoryData[i].FacingAngle = Quaternion::QuaternionToEuler(Pose.mPoseData.TrajectoryData[i].CurrentRefTransform.GetRotation()).y;
            }
#endif
        }

        void SetCustomizedFunc(BodyCostFunc func)
        {
            mBodyCostFunc = func;
        }

        void SetCustomizedFunc(PoseCostFunc func)
        {
            mPoseCostFunc = func;
        }

        void SetCustomizedFunc(TrajCostFunc func)
        {
            mTrajCostFunc = func;
        }

        float CalculateCost(const MotionMatchPoseData& lhs, const MotionMatchPoseData& rhs, float& bodyCost, float& poseCost, float& trajCost);

        void HandlePoseMatching();

        ~MotionDataAsset()
        {
        }

#ifdef USE_FAST_SEARCH
        virtual  MotionMatchPoseData GetPoseData(int id) const override;

        virtual void GetPoseData(int id, MotionMatchPoseData& poseData) const override;

#endif 
    /*
    * // Resource related interfaces
    */
    public:
        static int GetClassIDStatic() { return ClassID(MotionDataAsset); }
        bool Serialize(SerializeNode&& s, const std::string& path) override;

        bool Deserialize(const DeserializeNode& s) override;

    public:
        // Editor related
        void SetMotionMatchResourceSetting(const MotionMatchResourceSetting& setting)
        {
            mValid = setting.valid;
            mPoseMatching = setting.PoseMatching;
            mFlattenY = setting.flattenY;

            mStartAnimClamp = setting.startAnimClamp;
            mEndAnimClamp = setting.endAnimClamp;

            MMConfig.PoseInterval = setting.poseInterval;
            MMConfig.mTrajTimes.resize(setting.TrajSize);
            for (int i = 0; i < setting.TrajSize; i++)
            {
                MMConfig.mTrajTimes[i] = setting.TrajTimes[i];
            }

            // weights 
            mQualityVsResponse = setting.QualityVsResponse;
            mUserWeights = WeightControl::GetDefaultWeights();

            mUserWeights.mVelocity_weight = setting.BodyVelocityWeight;
            mUserWeights.mRotVelocity_weight = setting.BodyRotVelocityWeight;

            mUserWeights.mTrajPos_weights[0] = setting.TrajTranslationWeight;
            mUserWeights.mTrajAngle_weights[0] = setting.TrajFacingWeight;

            mUserWeights.mPosePos_weights[0] = setting.PoseTranslationWeight;
            mUserWeights.mPoseVelo_weights[0] = setting.PoseVelocityWeight;

            // visualizer;
            // TODO(yazhenyuan): asset/or node?
        }

        void GetMotionMatchResourceSetting(MotionMatchResourceSetting& setting)
        {
            setting.valid = mValid;
            setting.PoseMatching = mPoseMatching;
            setting.flattenY = mFlattenY;
            
            setting.poseInterval = MMConfig.PoseInterval;

            setting.startAnimClamp = mStartAnimClamp;
            setting.endAnimClamp = mEndAnimClamp;

            setting.TrajSize = static_cast<int>(MMConfig.mTrajTimes.size());
            for (int i = 0; i < MMConfig.mTrajTimes.size(); i++)
            {
                setting.TrajTimes[i] = MMConfig.mTrajTimes[i];
            }


            setting.QualityVsResponse = mQualityVsResponse;

            // weights 
            setting.BodyVelocityWeight = mUserWeights.mVelocity_weight;
            setting.BodyRotVelocityWeight = mUserWeights.mRotVelocity_weight;

            auto SafeGet = [](float& lhs, std::vector<float>& rhs)
            {
                lhs = rhs.empty() ? 1.0f : rhs[0];
            };
            SafeGet(setting.TrajTranslationWeight, mUserWeights.mTrajPos_weights);
            SafeGet(setting.TrajFacingWeight, mUserWeights.mTrajAngle_weights);

            SafeGet(setting.PoseTranslationWeight, mUserWeights.mPosePos_weights);
            SafeGet(setting.PoseVelocityWeight, mUserWeights.mPoseVelo_weights);
        }

        const MotionMatchResourceStatistics& GetStatistics()
        {
            return mStatistics;
        }

    protected:
        static float DefaultBodyCost(const Float3& current_body_velo, const float current_body_rot_velo, const Float3& feature_body_velo, const float feature_body_rot_velo, float weight_velo, float weight_rot_velo);
        static float DefaultPoseCost(const Float3& current_bone_pos, const Float3& current_bone_velo, const Float3& feature_bone_pos, const Float3& feature_bone_velo, float weight_pos, float weight_velo, int i);
        static float DefaultTrajCost(const Float3& current_traj_pos, const float current_traj_facing, const Float3& feature_traj_pos, const float feature_traj_facing, float weight_pos, float weight_facing, int i);


        int LinearSearchForBestPose(const MotionMatchPoseData& currentPose, float& poseCost, float& trajCost, float& bodyCost, float& currentSmallest) const;


        static void PoseToFeature(const MotionMatchPoseData& poseData, std::vector<float>& data)
        {
#ifndef _MANAGED
            int count = 0;
            data[count++] = poseData.PresentVel.x;
            data[count++] = poseData.PresentVel.y;
            data[count++] = poseData.PresentVel.z;

            data[count++] = poseData.RotVelocity;

            for (auto& itr : poseData.MotionJointData)
            {
                data[count++] = itr.BoneTransformPosition.x;
                data[count++] = itr.BoneTransformPosition.y;
                data[count++] = itr.BoneTransformPosition.z;

                data[count++] = itr.BoneTransformVelocity.x;
                data[count++] = itr.BoneTransformVelocity.y;
                data[count++] = itr.BoneTransformVelocity.z;
            }

            for (auto& itr : poseData.TrajectoryData)
            {
                const auto& trans = itr.CurrentRefTransform.GetTranslation();
                data[count++] = trans.x;
                data[count++] = trans.y;
                data[count++] = trans.z;

                data[count++] = itr.FacingAngle;
            }
#endif
        };

        static void FeatureToPose(MotionMatchPoseData& poseData, const std::vector<float>& data)
        {
            int count = 0;
            poseData.PresentVel.x = data[count++];
            poseData.PresentVel.y = data[count++];
            poseData.PresentVel.z = data[count++];

            poseData.RotVelocity = data[count++];

            for (auto& itr : poseData.MotionJointData)
            {
                itr.BoneTransformPosition.x = data[count++];
                itr.BoneTransformPosition.y = data[count++];
                itr.BoneTransformPosition.z = data[count++];

                itr.BoneTransformVelocity.x = data[count++];
                itr.BoneTransformVelocity.y = data[count++];
                itr.BoneTransformVelocity.z = data[count++];
            }

            for (auto& itr : poseData.TrajectoryData)
            {
                Float3 trans;
                trans.x = data[count++];
                trans.y = data[count++];
                trans.z = data[count++];

                itr = ExtractTrajPoint(trans, data[count++]);
            }
        }
#ifdef USE_FAST_SEARCH
        int FastSearchForBestPose(const MotionMatchPoseData& currentPose, float& poseCost, float& trajCost, float& bodyCost, float& currentSmallest) const;

        int TotalFeatureDimension() const;

        void InitializeNSG();

#endif

    protected:
        bool mPoseMatching = false;
        bool mValid = false;

        TrajGenConfig MMConfig;

        WeightControl mNormalizedWeights;

        WeightControl mFinalWeights;

        WeightControl mUserWeights;
        float mQualityVsResponse = 0.5f;

        MotionMatchResourceStatistics mStatistics;

        BodyCostFunc mBodyCostFunc = DefaultBodyCost;
        TrajCostFunc mTrajCostFunc = DefaultTrajCost;
        PoseCostFunc mPoseCostFunc = DefaultPoseCost;
        

#ifdef USE_FAST_SEARCH

protected:
        class CustomizedPoseDistSpace : public hnswlib::SpaceInterface<float>
        {
            size_t body_;
            size_t pose_;
            size_t traj_;

            size_t data_size_;
            size_t dim_;
            const MotionDataAsset* mParentAsset;
            std::function<float(const void*, const void*, const void*)> customized_func;

            friend class MotionDataAsset;
        public:
            CustomizedPoseDistSpace(int body_dim, int pose_dim, int traj_dim, const MotionDataAsset* asset) : body_(body_dim), pose_(pose_dim), traj_(traj_dim), mParentAsset(asset)
            {
                customized_func = std::bind(&CustomizedPoseDistSpace::DistFunc, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);

                dim_ = body_dim + pose_dim * 6 + traj_dim * 4;
                data_size_ = dim_ * sizeof(float);
            }

            size_t get_data_size() {
                return data_size_;
            }

            hnswlib::DISTFUNC<float> get_dist_func() {
                return customized_func;
            }

            void* get_dist_func_param() {
                return &dim_;
            }

        protected:
            float DistFunc(const void* pVect1v, const void* pVect2v, const void* qty_ptr)
            {
                float body_cost = 0.f;
                float pose_cost = 0.f;
                float traj_cost = 0.f;

                return DistFucnWithDetails(pVect1v, pVect2v, qty_ptr, body_cost, traj_cost, pose_cost);
            }

            float DistFucnWithDetails(const void* pVect1v, const void* pVect2v, const void* qty_ptr, float& body_cost, float& traj_cost, float& pose_cost)
            {
                const float* pVect1 = reinterpret_cast<const float*>(pVect1v);
                const float* pVect2 = reinterpret_cast<const float*>(pVect2v);
                //const size_t qty = *(reinterpret_cast<const size_t*>(qty_ptr));

                float res = 0;

                const  Float3* velocity1 = reinterpret_cast<const Float3*>(pVect1);
                const Float3* velocity2 = reinterpret_cast<const Float3*>(pVect2);

                auto& weights = mParentAsset->GetFinalWeights();

                // body dist
                //res += Float3::DistanceSquared(*velocity1, *velocity2) * weights.mVelocity_weight;
                //res += std::abs(pVect1[3]-pVect2[3]) * weights.mRotVelocity_weight;
                body_cost = mParentAsset->mBodyCostFunc(*velocity1, pVect1[3], *velocity2, pVect2[3], weights.mVelocity_weight, weights.mRotVelocity_weight);

                res += body_cost;
                // pose dist

                pose_cost = 0.f;
                int offset = 4;
                for (int i = 0; i < pose_; i++)
                {
                    const  Float3* pose1 = reinterpret_cast<const Float3*>(pVect1 + offset);
                    const  Float3* pose2 = reinterpret_cast<const Float3*>(pVect2 + offset);
                    //res += Float3::DistanceSquared(pose1[0], pose2[0]) * weights.mPosePos_weights[i];
                    //res += Float3::DistanceSquared(pose1[1], pose2[1]) * weights.mPoseVelo_weights[i];
                    pose_cost += mParentAsset->mPoseCostFunc(pose1[0], pose1[1], pose2[0], pose2[1], weights.mPosePos_weights[i], weights.mPoseVelo_weights[i], i);
                    offset += 6;
                }

                res += pose_cost;

                traj_cost = 0.f;
                for (int i = 0; i < traj_; i++)
                {
                    const  Float3* trans1 = reinterpret_cast<const Float3*>(pVect1 + offset);
                    const Float3* trans2 = reinterpret_cast<const Float3*>(pVect2 + offset);

                    //res += Float3::DistanceSquared(trans1[0], trans2[0]) * weights.mTrajPos_weights[i];

                    //offset += 3;
                    //res += std::abs(MathUtils::FindDeltaAngle<MathUtils::AngleType::Radian>(*(pVect1 + offset), *(pVect2 + offset))) * weights.mTrajAngle_weights[i];
                    //offset += 1;

                    traj_cost += mParentAsset->mTrajCostFunc(trans1[0], *(pVect1 + offset + 3), trans2[0], *(pVect2 + offset + 3), weights.mTrajPos_weights[i], weights.mTrajAngle_weights[i], i);

                    offset += 4;
                }

                res += traj_cost;
                return res;
            }
        };

        std::shared_ptr<hnswlib::HierarchicalNSW<float>> mSearchAlg;
        std::shared_ptr<CustomizedPoseDistSpace> mSearchSpace;
#endif
    };



 
}
