#pragma once
#include "CrossBase/Math/CrossMath.h"
#include "CrossBase/NodeTransform/NodeTransformSIMD.h"
#include "CEAnimation/AnimBase.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/SettingsManager.h"
#ifdef CROSSENGINE_DEBUG
#    define LOG_SMALL(...)                                                                                                                                                                                                                     \
        if (cross::EngineGlobal::GetSettingMgr()->GetAppStartUpType() != cross::AppStartUpType::AppStartUpTypeCrossEditor)                                                                                                                     \
        {                                                                                                                                                                                                                                      \
            cross::LogModule::Instance().Log(LogModule::LogMode::eGame, Logger::LogLevel::LOG_LEVEL_INFO, SourceLoc{}, __VA_ARGS__);                                                                                                           \
        }
#    define LOG_SMALL_WARN(...)                                                                                                                                                                                                                \
        if (cross::EngineGlobal::GetSettingMgr()->GetAppStartUpType() != cross::AppStartUpType::AppStartUpTypeCrossEditor)                                                                                                                     \
        {                                                                                                                                                                                                                                      \
            cross::LogModule::Instance().Log(LogModule::LogMode::eGame, Logger::LogLevel::LOG_LEVEL_WARN, SourceLoc{}, __VA_ARGS__);                                                                                                           \
        }
#else
#    define LOG_SMALL(...)
#    define LOG_SMALL_WARN(...)
#endif



namespace cross::anim
{
    class IAnimSequence;
    using AnimSequencePtr = std::shared_ptr<IAnimSequence>;
    using FTransform = NodeTransform;
    using FVector = SIMDVector4;
    struct  ExtractTrajPoint
    {
#ifndef _MANAGED
        FTransform CurrentRefTransform = FTransform::Identity();
#else
        FTransform CurrentRefTransform;
#endif
        float AllowedTimeOffset = 0.f;
        float FacingAngle = 0.0; // in Radians;

        ExtractTrajPoint()
        {
#ifndef _MANAGED
            CurrentRefTransform = FTransform::Identity();
            AllowedTimeOffset = 0.f;
#endif
        }

        ExtractTrajPoint(const Float3& trans, float angle)
        {
#ifndef _MANAGED
            Quaternion Rot = Quaternion::CreateFromAxisAngle(Float3(0.0, 1.0, 0.0), angle);

            CurrentRefTransform =  NodeTransform(Float3(1.0, 1.f, 1.f), Rot, trans);
            FacingAngle = angle;
#endif
        }

        ExtractTrajPoint(const FTransform& InputTransform, const float InputAllowedTimeOffset)
        {
#ifndef _MANAGED
            CurrentRefTransform = InputTransform;
            AllowedTimeOffset = InputAllowedTimeOffset;
            FacingAngle = Quaternion::QuaternionToEuler(InputTransform.GetRotation()).y;
#endif
        }
    };


    typedef std::vector <ExtractTrajPoint> Trajectory;

    struct JointFeature
    {
        Float3A BoneTransformPosition = Float3A::Zero();
        Float3A BoneTransformVelocity = Float3A::Zero();

        JointFeature()
        {
        }

        float CompareTo(const JointFeature SkeletonStructRef) const;
    };


   

    struct MotionMatchPoseDesc
    {
        int PoseId = -1;

        bool mMirror = false;

        bool mDoNotUse = false;
        //Curent animation number
        int SrcAnimIndex;
        //The start time of the animation
        float StartTime;
    };

    struct MotionMatchPoseData
    {
        //The actual bone data
        std::vector <JointFeature> MotionJointData;
        //Current velocity
        Float3A PresentVel = Float3A::Zero();
        float  RotVelocity = 0.f; // in Radians
        //Future input prediction
        Trajectory TrajectoryData;


        //Let's calculate the actual cost of running the anim data
        float AnimationMatchMathData(const float ControllerInputReactionSpeed, const float VelocityStrength,
            const float AnimationMatchPower,
            //const FInputPlayerDirectionData & InputDirection, 
            //const std::vector <FSkeletonStructure> PresentJointData, const Float3A  & CurrentTrackingVelocity,
            const MotionMatchPoseData& PlayerPose,
            float& poseCost, float& DistCost, float& TrajCost);
    };

    class  MotionMatchingPose
    {
    public:
        MotionMatchingPose();

        MotionMatchPoseDesc mPoseInfo;

        MotionMatchPoseData mPoseData;



        //Also extract some of the animation data
        //Our extracted data from the animation - grabs the source animation at the correct time and references that again the skeleton name.
        void ExtractDataFromAnimation(const AnimSequencePtr InSequence, const int AtSrcAnimIndex, const float AtSrcStartTime, std::vector <CEName> SkeletonRuntimeName);

        bool operator==(const MotionMatchingPose RPData) const
        {
            return (mPoseInfo.SrcAnimIndex == RPData.mPoseInfo.SrcAnimIndex) && (mPoseInfo.StartTime == RPData.mPoseInfo.StartTime);
        }
    };

    using MotionMatchingMathPosePtr = std::shared_ptr<MotionMatchingPose>;
}

