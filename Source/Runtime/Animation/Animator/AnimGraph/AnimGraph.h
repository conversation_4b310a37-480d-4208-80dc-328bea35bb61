#pragma once
#include <typeinfo>
#include "Resource/BaseClasses/ClassIDs.h"
#include "Resource/Resource.h"
#include "CrossBase/Template/TypeCast.hpp"
#include "Template/TypeTraits.hpp"
#include "CEAnimation/Animator/AnimatorParameter.h"
#include "Runtime/Animation/Animator/AnimGraph/IAnimGraph.h"
#include "Runtime/Animation/Animator/AnimGraph/Nodes/AnimGraph_BaseNode.h"
#include "Runtime/Animation/Animator/AnimGraph/Nodes/AnimGraph_RootNode.h"
#include "Runtime/Animation/Animator/AnimGraph/Nodes/AnimGraph_SavePoseNode.h"
#include "Runtime/Animation/Animator/AnimGraph/Nodes/AnimGraph_GetPoseNode.h"
#include "Runtime/Animation/Animator/AnimGraph/Links/AnimGraph_BaseLink.h"
#include "Runtime/Animation/Animator/AnimGraph/Links/AnimGraph_ParameterLink.h"

namespace cross::anim {

template<typename T> 
using IsNodeOrLink = std::disjunction<std::is_base_of<AnimGraph_BaseNode, T>, std::is_base_of<AnimGraph_BaseLink, T>>;

template<typename T> 
static CEName NodeOrLinkTypeName()
{
    std::string_view fullname = NameDetailPretty<T>();
    size_t start = fullname.find_first_of('_') + 1;
    fullname.remove_prefix(start);
    CEName Name{fullname.data(), (UInt32)fullname.size()};
    return Name;
}

enum class GraphType
{
    StoryBoard,
    StateGraph,
    LayerGraph,
    None
};

class AnimGraph : public IAnimGraph
{
public:
    AnimGraph(const CEName& inGraphName)
        : mName(inGraphName)
    {}

    virtual ~AnimGraph();

    //// ~IAnimGraph Interface Begin~
    ////

    virtual void Initialize(const AnimInitContext& inContext) override;

    virtual void Update(const AnimUpdateContext& inContext) override;

    virtual void Evaluate(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& inContext) override;

    virtual void PostUpate() override;

    virtual AnimGraph_BaseNode* GetNode(GraphNodeH inHandle) const override
    {
        if (inHandle >= 0 && inHandle < mNodes.size())
            return mNodes[inHandle];

        return nullptr;
    }

    virtual AnimGraph_BaseLink* GetLink(GraphLinkH inHandle) const override
    {
        if (inHandle >= 0 && inHandle < mLinks.size())
            return mLinks[inHandle];

        return nullptr;
    }

    virtual const Parameter* GetParameter(GraphParamH inHandle) const override
    {
        if (inHandle >= 0 && inHandle < mParameters->size())
            return (*mParameters)[inHandle].get();

        return nullptr;
    }

    virtual void GatherDebugData(GraphNodeDebugData& debugData) override {}

    ////
    //// ~IAnimGraph Interface End~

#if CROSSENGINE_EDITOR

    void AddVisitRecord(const StbVisitRecord& data) const;

    const StbDebugData& GetStbDebugData() const
    {
        return mStbDebugData;
    }
#endif   // CROSSENGINE_EDITOR

    virtual GraphType GetGraphType() const
    {
        return GraphType::None;
    }

    CEName GetName() const
    {
        return mName;
    }

    size_t GetNumOfNodes() const
    {
        return mNodes.size();
    }

    size_t GetNumOfLinks() const
    {
        return mLinks.size();
    }

protected:
    AnimGraph_RootNode* GetRootNode()
    {
        return TYPE_CAST(AnimGraph_RootNode*, mNodes[0]);
    }

protected:
    // Graph Name
    CEName mName;

    // Non-serialized
    // all GraphNode instances that are running in the graph currently
    std::vector<AnimGraph_BaseNode*> mNodes;

    // Non-serialized
    // all GraphLink instances that are running in the graph currently
    std::vector<AnimGraph_BaseLink*> mLinks;

    // Non-serialized
    // Runtime cached parameters ptr from animator
    const std::vector<ParameterPtr>* mParameters{nullptr};

#if CROSSENGINE_EDITOR
    mutable StbDebugData mStbDebugData;
#endif   // CROSSENGINE_EDITOR

    friend class AnimAssembler;
    friend class AnimStoryBoard;
};

using AnimGraphPtr = std::shared_ptr<AnimGraph>;

using NodeGeneratorFunction = AnimGraph_BaseNode* (*)(const AnimGraph* inOwner, GraphNodeH inNodeH, const DeserializeNode& inNodeJson, 
    const CENameMap<CEName, size_t>& inLinkNameToIndexMap, const CENameMap<CEName, size_t>& inParamNameToIndexMap);

using LinkGeneratorFunction = AnimGraph_BaseLink* (*)(const AnimGraph* inOwner, GraphLinkH inLinkH, const DeserializeNode& inLinkJson, const CENameMap<CEName, size_t>& inNodeNameToIndexMap);

class AnimGraphAssembler
{
public:
    static AnimGraphAssembler& GetInstance()
    {
        static AnimGraphAssembler instance;
        return instance;
    }

    ~AnimGraphAssembler() = default;

    bool IsNodeTypeValid(const std::string& typeName) const;

    AnimGraph_BaseNode* AssembleGraphNode(
        const AnimGraph* inOwner, 
        GraphNodeH inNodeH,
        const DeserializeNode& inNodeJson, 
        const CENameMap<CEName, size_t>& inLinkNameToIndexMap, 
        const CENameMap<CEName, size_t>& inParamNameToIndexMap);

    AnimGraph_BaseLink* AssembleGraphLink(
        const AnimGraph* inOwner, 
        GraphLinkH inLinkH, 
        const DeserializeNode& inLinkJson, 
        const CENameMap<CEName, size_t>& inNodeNameToIndexMap);

    template<typename NodeType> bool RegisterNodeGenerator()
    {
        static_assert(IsNodeOrLink<NodeType>::value);
        CEName name = NodeOrLinkTypeName<NodeType>();
        mNodeGeneratorMap[name] = NodeType::Produce;
        return true;
    }

    template<typename NodeType> bool RegisterNodeGenerator(const CEName& name)
    {
        if constexpr (IsNodeOrLink<NodeType>::value)
        {
            mNodeGeneratorMap[name] = NodeType::Produce;
            // return true;
        }
        return true;
        // return false; weired result when in mac
    }

    template<typename LinkType> bool RegisterLinkGenerator()
    {
        static_assert(IsNodeOrLink<LinkType>::value);
        CEName name = NodeOrLinkTypeName<LinkType>();
        mLinkGeneratorMap[name] = LinkType::Produce;
        return true;
    }

    template<typename LinkType> bool RegisterLinkGenerator(const CEName& name)
    {
        if constexpr (IsNodeOrLink<LinkType>::value)
        {
            mLinkGeneratorMap[name] = LinkType::Produce;
        }
        return true;
        // return false; weired result when
    }

#define CONCAT(a, b)         CONCAT_INNER(a, b)
#define CONCAT_INNER(a, b)   a##b
#define UNIQUE_VARNAME(base) CONCAT(base, __COUNTER__)

#define REGISTER_NODE_TYPE(NodeT) static bool UNIQUE_VARNAME(register) = AnimGraphAssembler::GetInstance().RegisterNodeGenerator<NodeT>();

#define REGISTER_NODE_TYPE_NAME(name, NodeT) static bool UNIQUE_VARNAME(register) = AnimGraphAssembler::GetInstance().RegisterNodeGenerator<NodeT>(name);

#define REGISTER_LINK_TYPE(LinkT) static bool UNIQUE_VARNAME(register) = AnimGraphAssembler::GetInstance().RegisterLinkGenerator<LinkT>();

#define REGISTER_LINK_TYPE_NAME(name, LinkT) static bool UNIQUE_VARNAME(register) = AnimGraphAssembler::GetInstance().RegisterLinkGenerator<LinkT>(name);

private:
    AnimGraphAssembler();

    CENameMap<CEName, NodeGeneratorFunction> mNodeGeneratorMap;
    CENameMap<CEName, LinkGeneratorFunction> mLinkGeneratorMap;
};

}   // namespace cross::anim
