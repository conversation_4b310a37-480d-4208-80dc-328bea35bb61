#include "EnginePrefix.h"
#include "Runtime/Animation/Animator/AnimGraph/Nodes/AnimGraph_SpaceRootToLocalNode.h"
#include "Runtime/Animation/Animator/AnimGraph/AnimGraphDefine.h"
#include "Runtime/Animation/Animator/AnimGraph/AnimGraph.h"

namespace cross::anim {

REGISTER_NODE_TYPE(AnimGraph_SpaceRootToLocalNode)

void AnimGraph_SpaceRootToLocalNode::Initialize(const AnimInitContext& inContext)
{
    if (PoseLink() != nullptr)
        PoseLink()->Initialize(inContext);
}

void AnimGraph_SpaceRootToLocalNode::Update(const AnimUpdateContext& inContext)
{
    AnimGraph_BaseNode::Update(inContext);

    if (PoseLink() != nullptr)
        PoseLink()->Update(inContext);
}

void AnimGraph_SpaceRootToLocalNode::EvaluateLocalSpace(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& inContext)
{
    if (PoseLink() != nullptr)
        PoseLink()->Evaluate(outPose, inContext);

    outPose.ConvertAllBoneToLocalSpace();
}

AnimGraph_BaseNode* AnimGraph_SpaceRootToLocalNode::Produce(const AnimGraph* inOwner, GraphNodeH inNodeH, const DeserializeNode& inNodeJson, 
    const CENameMap<CEName, size_t>& inLinkNameToIndexMap, const CENameMap<CEName, size_t>& inParamNameToIndexMap)
{
    return new AnimGraph_SpaceRootToLocalNode(inOwner, inNodeH, inNodeJson, inLinkNameToIndexMap, inParamNameToIndexMap);
}

}   // namespace cross::anim