#include "EnginePrefix.h"
#include "Runtime/Animation/Animator/Animator.h"
#include "Runtime/Animation/Animator/AnimGraph/AnimStoryBoard.h"
#include "Runtime/Animation/Animator/AnimGraph/Nodes/AnimGraph_FSMNode.h"

namespace cross::anim {

void AnimStoryBoard::Initialize(const AnimInitContext& inContext)
{
    // Initialize Graph
    AnimGraph::Initialize(inContext);
}

void AnimStoryBoard::Update(const AnimUpdateContext& inContext)
{
    // Update Graph
    AnimGraph::Update(AnimUpdateContext(inContext, &mAnimSync));

    // Update SavePoseNode in order
    for (auto& savePoseNode : mSortedSavePoseNodes)
    {
        savePoseNode->UpdateSavePoseNode();
    }

    // Sync Animation
    mAnimSync.UpdateAnimations(inContext.DeltaTime);
}

void AnimStoryBoard::Evaluate(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& inContext)
{
    auto context = inContext.Move(nullptr);
    context.RootMotionExtractMode = mRootMotionExtractMode;
    context.RootMotionApplyMode = mRootMotionApplyMode;
    // We can extract root motion from storyboard
    if (mRootMotionExtractMode != RootMotion::DoNotExtract)
    {
        // make sure root motion param is clean before evaluating
        mRootMotionParams.Reset();
        context.RootMotionParamsPtr = &mRootMotionParams;
    }

    // Evaluate happened here
    AnimGraph::Evaluate(outPose, context);

    // Apply root motion of storyboard to inContext.RootMotionParamsPtr
    if (CanApplyRootMotion())
    {
        // Make up to full weight
        mRootMotionParams.MakeUpToFullWeight();

        inContext.RootMotionParamsPtr->Accumulate(mRootMotionParams);
    }
}

void AnimStoryBoard::PostUpate()
{
    AnimGraph::PostUpate();

    // Reset all SavePoseNode's flag in this story board
    for (auto& savePoseNode : mSortedSavePoseNodes)
    {
        savePoseNode->ResetNodeFlag();
    }

    // Reset the synchronizer
    mAnimSync.Reset();
}

void AnimStoryBoard::RecordSavePoseNode(const CEName& inPoseName, AnimGraph_SavePoseNode* inSavePoseNode)
{
    Assert(mSavePoseNodeMap.find(inPoseName) == mSavePoseNodeMap.end());
    mSavePoseNodeMap.insert({inPoseName, inSavePoseNode});

    mSortedSavePoseNodes.emplace_back(inSavePoseNode);
}

void AnimStoryBoard::RecordGetPoseNode(AnimGraph_GetPoseNode* inGetPoseNode)
{
    auto iter = std::find_if(mGetPoseNodeList.begin(), mGetPoseNodeList.end(), [&](auto& elem) { return elem == inGetPoseNode; });
    if (iter != mGetPoseNodeList.end())
        return;

    mGetPoseNodeList.push_back(inGetPoseNode);
}

}   // namespace cross::anim