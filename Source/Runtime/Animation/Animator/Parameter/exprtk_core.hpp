#pragma once

#include "exprtk_base.hpp"

///// Expression Nodes And Operations Define /////
//
namespace exprtk
{
   namespace details
   {
      enum operator_type
      {
         e_default , e_null    , e_add     , e_sub     ,
         e_mul     , e_div     , e_mod     , e_pow     ,
         e_atan2   , e_min     , e_max     , e_avg     ,
         e_sum     , e_prod    , e_lt      , e_lte     ,
         e_eq      , e_equal   , e_ne      , e_nequal  ,
         e_gte     , e_gt      , e_and     , e_nand    ,
         e_or      , e_nor     , e_xor     , e_xnor    ,
         e_mand    , e_mor     , e_scand   , e_scor    ,
         e_shr     , e_shl     , e_abs     , e_acos    ,
         e_acosh   , e_asin    , e_asinh   , e_atan    ,
         e_atanh   , e_ceil    , e_cos     , e_cosh    ,
         e_exp     , e_expm1   , e_floor   , e_log     ,
         e_log10   , e_log2    , e_log1p   , e_logn    ,
         e_neg     , e_pos     , e_round   , e_roundn  ,
         e_root    , e_sqrt    , e_sin     , e_sinc    ,
         e_sinh    , e_sec     , e_csc     , e_tan     ,
         e_tanh    , e_cot     , e_clamp   , e_iclamp  ,
         e_inrange , e_sgn     , e_r2d     , e_d2r     ,
         e_d2g     , e_g2d     , e_hypot   , e_notl    ,
         e_erf     , e_erfc    , e_ncdf    , e_frac    ,
         e_trunc   , e_assign  , e_addass  , e_subass  ,
         e_mulass  , e_divass  , e_modass  , e_in      ,
         e_like    , e_ilike   , e_multi   , e_smulti  ,
         e_swap    ,

         // Do not add new functions/operators after this point.
         e_sf00 = 1000, e_sf01 = 1001, e_sf02 = 1002, e_sf03 = 1003,
         e_sf04 = 1004, e_sf05 = 1005, e_sf06 = 1006, e_sf07 = 1007,
         e_sf08 = 1008, e_sf09 = 1009, e_sf10 = 1010, e_sf11 = 1011,
         e_sf12 = 1012, e_sf13 = 1013, e_sf14 = 1014, e_sf15 = 1015,
         e_sf16 = 1016, e_sf17 = 1017, e_sf18 = 1018, e_sf19 = 1019,
         e_sf20 = 1020, e_sf21 = 1021, e_sf22 = 1022, e_sf23 = 1023,
         e_sf24 = 1024, e_sf25 = 1025, e_sf26 = 1026, e_sf27 = 1027,
         e_sf28 = 1028, e_sf29 = 1029, e_sf30 = 1030, e_sf31 = 1031,
         e_sf32 = 1032, e_sf33 = 1033, e_sf34 = 1034, e_sf35 = 1035,
         e_sf36 = 1036, e_sf37 = 1037, e_sf38 = 1038, e_sf39 = 1039,
         e_sf40 = 1040, e_sf41 = 1041, e_sf42 = 1042, e_sf43 = 1043,
         e_sf44 = 1044, e_sf45 = 1045, e_sf46 = 1046, e_sf47 = 1047,
         e_sf48 = 1048, e_sf49 = 1049, e_sf50 = 1050, e_sf51 = 1051,
         e_sf52 = 1052, e_sf53 = 1053, e_sf54 = 1054, e_sf55 = 1055,
         e_sf56 = 1056, e_sf57 = 1057, e_sf58 = 1058, e_sf59 = 1059,
         e_sf60 = 1060, e_sf61 = 1061, e_sf62 = 1062, e_sf63 = 1063,
         e_sf64 = 1064, e_sf65 = 1065, e_sf66 = 1066, e_sf67 = 1067,
         e_sf68 = 1068, e_sf69 = 1069, e_sf70 = 1070, e_sf71 = 1071,
         e_sf72 = 1072, e_sf73 = 1073, e_sf74 = 1074, e_sf75 = 1075,
         e_sf76 = 1076, e_sf77 = 1077, e_sf78 = 1078, e_sf79 = 1079,
         e_sf80 = 1080, e_sf81 = 1081, e_sf82 = 1082, e_sf83 = 1083,
         e_sf84 = 1084, e_sf85 = 1085, e_sf86 = 1086, e_sf87 = 1087,
         e_sf88 = 1088, e_sf89 = 1089, e_sf90 = 1090, e_sf91 = 1091,
         e_sf92 = 1092, e_sf93 = 1093, e_sf94 = 1094, e_sf95 = 1095,
         e_sf96 = 1096, e_sf97 = 1097, e_sf98 = 1098, e_sf99 = 1099,
         e_sffinal  = 1100,
         e_sf4ext00 = 2000, e_sf4ext01 = 2001, e_sf4ext02 = 2002, e_sf4ext03 = 2003,
         e_sf4ext04 = 2004, e_sf4ext05 = 2005, e_sf4ext06 = 2006, e_sf4ext07 = 2007,
         e_sf4ext08 = 2008, e_sf4ext09 = 2009, e_sf4ext10 = 2010, e_sf4ext11 = 2011,
         e_sf4ext12 = 2012, e_sf4ext13 = 2013, e_sf4ext14 = 2014, e_sf4ext15 = 2015,
         e_sf4ext16 = 2016, e_sf4ext17 = 2017, e_sf4ext18 = 2018, e_sf4ext19 = 2019,
         e_sf4ext20 = 2020, e_sf4ext21 = 2021, e_sf4ext22 = 2022, e_sf4ext23 = 2023,
         e_sf4ext24 = 2024, e_sf4ext25 = 2025, e_sf4ext26 = 2026, e_sf4ext27 = 2027,
         e_sf4ext28 = 2028, e_sf4ext29 = 2029, e_sf4ext30 = 2030, e_sf4ext31 = 2031,
         e_sf4ext32 = 2032, e_sf4ext33 = 2033, e_sf4ext34 = 2034, e_sf4ext35 = 2035,
         e_sf4ext36 = 2036, e_sf4ext37 = 2037, e_sf4ext38 = 2038, e_sf4ext39 = 2039,
         e_sf4ext40 = 2040, e_sf4ext41 = 2041, e_sf4ext42 = 2042, e_sf4ext43 = 2043,
         e_sf4ext44 = 2044, e_sf4ext45 = 2045, e_sf4ext46 = 2046, e_sf4ext47 = 2047,
         e_sf4ext48 = 2048, e_sf4ext49 = 2049, e_sf4ext50 = 2050, e_sf4ext51 = 2051,
         e_sf4ext52 = 2052, e_sf4ext53 = 2053, e_sf4ext54 = 2054, e_sf4ext55 = 2055,
         e_sf4ext56 = 2056, e_sf4ext57 = 2057, e_sf4ext58 = 2058, e_sf4ext59 = 2059,
         e_sf4ext60 = 2060, e_sf4ext61 = 2061
      };

      inline std::string to_str(const operator_type opr)
      {
         switch (opr)
         {
            case e_add    : return  "+"  ;
            case e_sub    : return  "-"  ;
            case e_mul    : return  "*"  ;
            case e_div    : return  "/"  ;
            case e_mod    : return  "%"  ;
            case e_pow    : return  "^"  ;
            case e_assign : return ":="  ;
            case e_addass : return "+="  ;
            case e_subass : return "-="  ;
            case e_mulass : return "*="  ;
            case e_divass : return "/="  ;
            case e_modass : return "%="  ;
            case e_lt     : return  "<"  ;
            case e_lte    : return "<="  ;
            case e_eq     : return "=="  ;
            case e_equal  : return  "="  ;
            case e_ne     : return "!="  ;
            case e_nequal : return "<>"  ;
            case e_gte    : return ">="  ;
            case e_gt     : return  ">"  ;
            case e_and    : return "and" ;
            case e_or     : return "or"  ;
            case e_xor    : return "xor" ;
            case e_nand   : return "nand";
            case e_nor    : return "nor" ;
            case e_xnor   : return "xnor";
            default       : return "N/A" ;
         }
      }

      struct base_operation_t
      {
         base_operation_t(const operator_type t, const unsigned int& np)
         : type(t)
         , num_params(np)
         {}

         operator_type type;
         unsigned int num_params;
      };

      namespace loop_unroll
      {
         #ifndef exprtk_disable_superscalar_unroll
         const unsigned int global_loop_batch_size = 16;
         #else
         const unsigned int global_loop_batch_size = 4;
         #endif

         struct details
         {
            explicit details(const std::size_t& vsize,
                             const unsigned int loop_batch_size = global_loop_batch_size)
            : batch_size(loop_batch_size   )
            , remainder (vsize % batch_size)
            , upper_bound(static_cast<int>(vsize - (remainder ? loop_batch_size : 0)))
            {}

            unsigned int batch_size;
            int remainder;
            int upper_bound;
         };
      }

      #ifdef exprtk_enable_debugging
      inline void dump_ptr(const std::string& s, const void* ptr, const std::size_t size = 0)
      {
         if (size)
            exprtk_debug(("%s - addr: %p\n",s.c_str(),ptr));
         else
            exprtk_debug(("%s - addr: %p size: %d\n",
                          s.c_str(),
                          ptr,
                          static_cast<unsigned int>(size)));
      }
      #else
      inline void dump_ptr(const std::string&, const void*) {}
      inline void dump_ptr(const std::string&, const void*, const std::size_t) {}
      #endif

      template <typename T>
      class vec_data_store
      {
      public:

         typedef vec_data_store<T> type;
         typedef T* data_t;

      private:

         struct control_block
         {
            control_block()
            : ref_count(1)
            , size     (0)
            , data     (0)
            , destruct (true)
            {}

            explicit control_block(const std::size_t& dsize)
            : ref_count(1    )
            , size     (dsize)
            , data     (0    )
            , destruct (true )
            { create_data(); }

            control_block(const std::size_t& dsize, data_t dptr, bool dstrct = false)
            : ref_count(1     )
            , size     (dsize )
            , data     (dptr  )
            , destruct (dstrct)
            {}

           ~control_block()
            {
               if (data && destruct && (0 == ref_count))
               {
                  dump_ptr("~control_block() data",data);
                  delete[] data;
                  data = reinterpret_cast<data_t>(0);
               }
            }

            static inline control_block* create(const std::size_t& dsize, data_t data_ptr = data_t(0), bool dstrct = false)
            {
               if (dsize)
               {
                  if (0 == data_ptr)
                     return (new control_block(dsize));
                  else
                     return (new control_block(dsize, data_ptr, dstrct));
               }
               else
                  return (new control_block);
            }

            static inline void destroy(control_block*& cntrl_blck)
            {
               if (cntrl_blck)
               {
                  if (
                       (0 !=   cntrl_blck->ref_count) &&
                       (0 == --cntrl_blck->ref_count)
                     )
                  {
                     delete cntrl_blck;
                  }

                  cntrl_blck = 0;
               }
            }

            std::size_t ref_count;
            std::size_t size;
            data_t      data;
            bool        destruct;

         private:

            control_block(const control_block&);
            control_block& operator=(const control_block&);

            inline void create_data()
            {
               destruct = true;
               data     = new T[size];
               std::fill_n(data, size, T(0));
               dump_ptr("control_block::create_data() - data",data,size);
            }
         };

      public:

         vec_data_store()
         : control_block_(control_block::create(0))
         {}

         explicit vec_data_store(const std::size_t& size)
         : control_block_(control_block::create(size,reinterpret_cast<data_t>(0),true))
         {}

         vec_data_store(const std::size_t& size, data_t data, bool dstrct = false)
         : control_block_(control_block::create(size, data, dstrct))
         {}

         vec_data_store(const type& vds)
         {
            control_block_ = vds.control_block_;
            control_block_->ref_count++;
         }

        ~vec_data_store()
         {
            control_block::destroy(control_block_);
         }

         type& operator=(const type& vds)
         {
            if (this != &vds)
            {
               std::size_t final_size = min_size(control_block_, vds.control_block_);

               vds.control_block_->size = final_size;
                   control_block_->size = final_size;

               if (control_block_->destruct || (0 == control_block_->data))
               {
                  control_block::destroy(control_block_);

                  control_block_ = vds.control_block_;
                  control_block_->ref_count++;
               }
            }

            return (*this);
         }

         inline data_t data()
         {
            return control_block_->data;
         }

         inline data_t data() const
         {
            return control_block_->data;
         }

         inline std::size_t size()
         {
            return control_block_->size;
         }

         inline std::size_t size() const
         {
            return control_block_->size;
         }

         inline data_t& ref()
         {
            return control_block_->data;
         }

         inline void dump() const
         {
            #ifdef exprtk_enable_debugging
            exprtk_debug(("size: %d\taddress:%p\tdestruct:%c\n",
                          size(),
                          data(),
                          (control_block_->destruct ? 'T' : 'F')));

            for (std::size_t i = 0; i < size(); ++i)
            {
               if (5 == i)
                  exprtk_debug(("\n"));

               exprtk_debug(("%15.10f ",data()[i]));
            }
            exprtk_debug(("\n"));
            #endif
         }

         static inline void match_sizes(type& vds0, type& vds1)
         {
            const std::size_t size = min_size(vds0.control_block_,vds1.control_block_);
            vds0.control_block_->size = size;
            vds1.control_block_->size = size;
         }

      private:

         static inline std::size_t min_size(control_block* cb0, control_block* cb1)
         {
            const std::size_t size0 = cb0->size;
            const std::size_t size1 = cb1->size;

            if (size0 && size1)
               return (std::min)(size0,size1);
            else
               return (size0) ? size0 : size1;
         }

         control_block* control_block_;
      };

      namespace numeric
      {
         namespace details
         {
            template <typename T>
            inline T process_impl(const operator_type operation, const T arg)
            {
               switch (operation)
               {
                  case e_abs   : return numeric::abs  (arg);
                  case e_acos  : return numeric::acos (arg);
                  case e_acosh : return numeric::acosh(arg);
                  case e_asin  : return numeric::asin (arg);
                  case e_asinh : return numeric::asinh(arg);
                  case e_atan  : return numeric::atan (arg);
                  case e_atanh : return numeric::atanh(arg);
                  case e_ceil  : return numeric::ceil (arg);
                  case e_cos   : return numeric::cos  (arg);
                  case e_cosh  : return numeric::cosh (arg);
                  case e_exp   : return numeric::exp  (arg);
                  case e_expm1 : return numeric::expm1(arg);
                  case e_floor : return numeric::floor(arg);
                  case e_log   : return numeric::log  (arg);
                  case e_log10 : return numeric::log10(arg);
                  case e_log2  : return numeric::log2 (arg);
                  case e_log1p : return numeric::log1p(arg);
                  case e_neg   : return numeric::neg  (arg);
                  case e_pos   : return numeric::pos  (arg);
                  case e_round : return numeric::round(arg);
                  case e_sin   : return numeric::sin  (arg);
                  case e_sinc  : return numeric::sinc (arg);
                  case e_sinh  : return numeric::sinh (arg);
                  case e_sqrt  : return numeric::sqrt (arg);
                  case e_tan   : return numeric::tan  (arg);
                  case e_tanh  : return numeric::tanh (arg);
                  case e_cot   : return numeric::cot  (arg);
                  case e_sec   : return numeric::sec  (arg);
                  case e_csc   : return numeric::csc  (arg);
                  case e_r2d   : return numeric::r2d  (arg);
                  case e_d2r   : return numeric::d2r  (arg);
                  case e_d2g   : return numeric::d2g  (arg);
                  case e_g2d   : return numeric::g2d  (arg);
                  case e_notl  : return numeric::notl (arg);
                  case e_sgn   : return numeric::sgn  (arg);
                  case e_erf   : return numeric::erf  (arg);
                  case e_erfc  : return numeric::erfc (arg);
                  case e_ncdf  : return numeric::ncdf (arg);
                  case e_frac  : return numeric::frac (arg);
                  case e_trunc : return numeric::trunc(arg);

                  default      : exprtk_debug(("numeric::details::process_impl<T> - Invalid unary operation.\n"));
                                 return std::numeric_limits<T>::quiet_NaN();
               }
            }

            template <typename T>
            inline T process_impl(const operator_type operation, const T arg0, const T arg1)
            {
               switch (operation)
               {
                  case e_add    : return (arg0 + arg1);
                  case e_sub    : return (arg0 - arg1);
                  case e_mul    : return (arg0 * arg1);
                  case e_div    : return (arg0 / arg1);
                  case e_mod    : return modulus<T>(arg0,arg1);
                  case e_pow    : return pow<T>(arg0,arg1);
                  case e_atan2  : return atan2<T>(arg0,arg1);
                  case e_min    : return std::min<T>(arg0,arg1);
                  case e_max    : return std::max<T>(arg0,arg1);
                  case e_logn   : return logn<T>(arg0,arg1);
                  case e_lt     : return (arg0 <  arg1) ? T(1) : T(0);
                  case e_lte    : return (arg0 <= arg1) ? T(1) : T(0);
                  case e_eq     : return std::equal_to<T>()(arg0,arg1) ? T(1) : T(0);
                  case e_ne     : return std::not_equal_to<T>()(arg0,arg1) ? T(1) : T(0);
                  case e_gte    : return (arg0 >= arg1) ? T(1) : T(0);
                  case e_gt     : return (arg0 >  arg1) ? T(1) : T(0);
                  case e_and    : return and_opr <T>(arg0,arg1);
                  case e_nand   : return nand_opr<T>(arg0,arg1);
                  case e_or     : return or_opr  <T>(arg0,arg1);
                  case e_nor    : return nor_opr <T>(arg0,arg1);
                  case e_xor    : return xor_opr <T>(arg0,arg1);
                  case e_xnor   : return xnor_opr<T>(arg0,arg1);
                  case e_root   : return root    <T>(arg0,arg1);
                  case e_roundn : return roundn  <T>(arg0,arg1);
                  case e_equal  : return equal      (arg0,arg1);
                  case e_nequal : return nequal     (arg0,arg1);
                  case e_hypot  : return hypot   <T>(arg0,arg1);
                  case e_shr    : return shr     <T>(arg0,arg1);
                  case e_shl    : return shl     <T>(arg0,arg1);

                  default       : exprtk_debug(("numeric::details::process_impl<T> - Invalid binary operation.\n"));
                                  return std::numeric_limits<T>::quiet_NaN();
               }
            }

            template <typename T>
            inline T process_impl(const operator_type operation, const T arg0, const T arg1, int_type_tag)
            {
               switch (operation)
               {
                  case e_add    : return (arg0 + arg1);
                  case e_sub    : return (arg0 - arg1);
                  case e_mul    : return (arg0 * arg1);
                  case e_div    : return (arg0 / arg1);
                  case e_mod    : return arg0 % arg1;
                  case e_pow    : return pow<T>(arg0,arg1);
                  case e_min    : return std::min<T>(arg0,arg1);
                  case e_max    : return std::max<T>(arg0,arg1);
                  case e_logn   : return logn<T>(arg0,arg1);
                  case e_lt     : return (arg0 <  arg1) ? T(1) : T(0);
                  case e_lte    : return (arg0 <= arg1) ? T(1) : T(0);
                  case e_eq     : return (arg0 == arg1) ? T(1) : T(0);
                  case e_ne     : return (arg0 != arg1) ? T(1) : T(0);
                  case e_gte    : return (arg0 >= arg1) ? T(1) : T(0);
                  case e_gt     : return (arg0 >  arg1) ? T(1) : T(0);
                  case e_and    : return ((arg0 != T(0)) && (arg1 != T(0))) ? T(1) : T(0);
                  case e_nand   : return ((arg0 != T(0)) && (arg1 != T(0))) ? T(0) : T(1);
                  case e_or     : return ((arg0 != T(0)) || (arg1 != T(0))) ? T(1) : T(0);
                  case e_nor    : return ((arg0 != T(0)) || (arg1 != T(0))) ? T(0) : T(1);
                  case e_xor    : return arg0 ^ arg1;
                  case e_xnor   : return !(arg0 ^ arg1);
                  case e_root   : return root<T>(arg0,arg1);
                  case e_equal  : return arg0 == arg1;
                  case e_nequal : return arg0 != arg1;
                  case e_hypot  : return hypot<T>(arg0,arg1);
                  case e_shr    : return arg0 >> arg1;
                  case e_shl    : return arg0 << arg1;

                  default       : exprtk_debug(("numeric::details::process_impl<IntType> - Invalid binary operation.\n"));
                                  return std::numeric_limits<T>::quiet_NaN();
               }
            }
         }

         template <typename T>
         inline T process(const operator_type operation, const T arg)
         {
            return exprtk::details::numeric::details::process_impl(operation,arg);
         }

         template <typename T>
         inline T process(const operator_type operation, const T arg0, const T arg1)
         {
            return exprtk::details::numeric::details::process_impl(operation, arg0, arg1);
         }
      }

      template <typename Node>
      struct node_collector_interface
      {
         typedef Node* node_ptr_t;
         typedef Node** node_pp_t;
         typedef std::vector<node_pp_t> noderef_list_t;

         virtual ~node_collector_interface() {}

         virtual void collect_nodes(noderef_list_t&) {}
      };

      template <typename Node>
      struct node_depth_base;

      template <typename T>
      class expression_node : public node_collector_interface<expression_node<T> >,
                              public node_depth_base<expression_node<T> >
      {
      public:

         enum node_type
         {
            e_none          , e_null          , e_constant    , e_unary        ,
            e_binary        , e_binary_ext    , e_trinary     , e_quaternary   ,
            e_vararg        , e_conditional   , e_while       , e_repeat       ,
            e_for           , e_switch        , e_mswitch     , e_return       ,
            e_retenv        , e_variable      , e_stringvar   , e_stringconst  ,
            e_stringvarrng  , e_cstringvarrng , e_strgenrange , e_strconcat    ,
            e_stringvarsize , e_strswap       , e_stringsize  , e_stringvararg ,
            e_function      , e_vafunction    , e_genfunction , e_strfunction  ,
            e_strcondition  , e_strccondition , e_add         , e_sub          ,
            e_mul           , e_div           , e_mod         , e_pow          ,
            e_lt            , e_lte           , e_gt          , e_gte          ,
            e_eq            , e_ne            , e_and         , e_nand         ,
            e_or            , e_nor           , e_xor         , e_xnor         ,
            e_in            , e_like          , e_ilike       , e_inranges     ,
            e_ipow          , e_ipowinv       , e_abs         , e_acos         ,
            e_acosh         , e_asin          , e_asinh       , e_atan         ,
            e_atanh         , e_ceil          , e_cos         , e_cosh         ,
            e_exp           , e_expm1         , e_floor       , e_log          ,
            e_log10         , e_log2          , e_log1p       , e_neg          ,
            e_pos           , e_round         , e_sin         , e_sinc         ,
            e_sinh          , e_sqrt          , e_tan         , e_tanh         ,
            e_cot           , e_sec           , e_csc         , e_r2d          ,
            e_d2r           , e_d2g           , e_g2d         , e_notl         ,
            e_sgn           , e_erf           , e_erfc        , e_ncdf         ,
            e_frac          , e_trunc         , e_uvouv       , e_vov          ,
            e_cov           , e_voc           , e_vob         , e_bov          ,
            e_cob           , e_boc           , e_vovov       , e_vovoc        ,
            e_vocov         , e_covov         , e_covoc       , e_vovovov      ,
            e_vovovoc       , e_vovocov       , e_vocovov     , e_covovov      ,
            e_covocov       , e_vocovoc       , e_covovoc     , e_vococov      ,
            e_sf3ext        , e_sf4ext        , e_nulleq      , e_strass       ,
            e_vector        , e_vecelem       , e_rbvecelem   , e_rbveccelem   ,
            e_vecdefass     , e_vecvalass     , e_vecvecass   , e_vecopvalass  ,
            e_vecopvecass   , e_vecfunc       , e_vecvecswap  , e_vecvecineq   ,
            e_vecvalineq    , e_valvecineq    , e_vecvecarith , e_vecvalarith  ,
            e_valvecarith   , e_vecunaryop    , e_break       , e_continue     ,
            e_swap
         };

         typedef T value_type;
         typedef expression_node<T>* expression_ptr;
         typedef node_collector_interface<expression_node<T> > nci_t;
         typedef typename nci_t::noderef_list_t noderef_list_t;
         typedef node_depth_base<expression_node<T> > ndb_t;

         virtual ~expression_node()
         {}

         inline virtual T value() const
         {
            return std::numeric_limits<T>::quiet_NaN();
         }

         inline virtual expression_node<T>* branch(const std::size_t& index = 0) const
         {
            return reinterpret_cast<expression_ptr>(index * 0);
         }

         inline virtual node_type type() const
         {
            return e_none;
         }
      };

      template <typename T>
      inline bool is_generally_string_node(const expression_node<T>* node);

      inline bool is_true(const double v)
      {
         return std::not_equal_to<double>()(0.0,v);
      }

      inline bool is_true(const long double v)
      {
         return std::not_equal_to<long double>()(0.0L,v);
      }

      inline bool is_true(const float v)
      {
         return std::not_equal_to<float>()(0.0f,v);
      }

      template <typename T>
      inline bool is_true(const std::complex<T>& v)
      {
         return std::not_equal_to<std::complex<T> >()(std::complex<T>(0),v);
      }

      template <typename T>
      inline bool is_true(const expression_node<T>* node)
      {
         return std::not_equal_to<T>()(T(0),node->value());
      }

      template <typename T>
      inline bool is_true(const std::pair<expression_node<T>*,bool>& node)
      {
         return std::not_equal_to<T>()(T(0),node.first->value());
      }

      template <typename T>
      inline bool is_false(const expression_node<T>* node)
      {
         return std::equal_to<T>()(T(0),node->value());
      }

      template <typename T>
      inline bool is_false(const std::pair<expression_node<T>*,bool>& node)
      {
         return std::equal_to<T>()(T(0),node.first->value());
      }

      template <typename T>
      inline bool is_unary_node(const expression_node<T>* node)
      {
         return node && (details::expression_node<T>::e_unary == node->type());
      }

      template <typename T>
      inline bool is_neg_unary_node(const expression_node<T>* node)
      {
         return node && (details::expression_node<T>::e_neg == node->type());
      }

      template <typename T>
      inline bool is_binary_node(const expression_node<T>* node)
      {
         return node && (details::expression_node<T>::e_binary == node->type());
      }

      template <typename T>
      inline bool is_variable_node(const expression_node<T>* node)
      {
         return node && (details::expression_node<T>::e_variable == node->type());
      }

      template <typename T>
      inline bool is_ivariable_node(const expression_node<T>* node)
      {
         return node &&
                (
                  details::expression_node<T>::e_variable   == node->type() ||
                  details::expression_node<T>::e_vecelem    == node->type() ||
                  details::expression_node<T>::e_rbvecelem  == node->type() ||
                  details::expression_node<T>::e_rbveccelem == node->type()
                );
      }

      template <typename T>
      inline bool is_vector_elem_node(const expression_node<T>* node)
      {
         return node && (details::expression_node<T>::e_vecelem == node->type());
      }

      template <typename T>
      inline bool is_rebasevector_elem_node(const expression_node<T>* node)
      {
         return node && (details::expression_node<T>::e_rbvecelem == node->type());
      }

      template <typename T>
      inline bool is_rebasevector_celem_node(const expression_node<T>* node)
      {
         return node && (details::expression_node<T>::e_rbveccelem == node->type());
      }

      template <typename T>
      inline bool is_vector_node(const expression_node<T>* node)
      {
         return node && (details::expression_node<T>::e_vector == node->type());
      }

      template <typename T>
      inline bool is_ivector_node(const expression_node<T>* node)
      {
         if (node)
         {
            switch (node->type())
            {
               case details::expression_node<T>::e_vector      :
               case details::expression_node<T>::e_vecvalass   :
               case details::expression_node<T>::e_vecvecass   :
               case details::expression_node<T>::e_vecopvalass :
               case details::expression_node<T>::e_vecopvecass :
               case details::expression_node<T>::e_vecvecswap  :
               case details::expression_node<T>::e_vecvecarith :
               case details::expression_node<T>::e_vecvalarith :
               case details::expression_node<T>::e_valvecarith :
               case details::expression_node<T>::e_vecunaryop  : return true;
               default                                         : return false;
            }
         }
         else
            return false;
      }

      template <typename T>
      inline bool is_constant_node(const expression_node<T>* node)
      {
         return node && (details::expression_node<T>::e_constant == node->type());
      }

      template <typename T>
      inline bool is_null_node(const expression_node<T>* node)
      {
         return node && (details::expression_node<T>::e_null == node->type());
      }

      template <typename T>
      inline bool is_break_node(const expression_node<T>* node)
      {
         return node && (details::expression_node<T>::e_break == node->type());
      }

      template <typename T>
      inline bool is_continue_node(const expression_node<T>* node)
      {
         return node && (details::expression_node<T>::e_continue == node->type());
      }

      template <typename T>
      inline bool is_swap_node(const expression_node<T>* node)
      {
         return node && (details::expression_node<T>::e_swap == node->type());
      }

      template <typename T>
      inline bool is_function(const expression_node<T>* node)
      {
         return node && (details::expression_node<T>::e_function == node->type());
      }

      template <typename T>
      inline bool is_return_node(const expression_node<T>* node)
      {
         return node && (details::expression_node<T>::e_return == node->type());
      }

      template <typename T> class unary_node;

      template <typename T>
      inline bool is_negate_node(const expression_node<T>* node)
      {
         if (node && is_unary_node(node))
         {
            return (details::e_neg == static_cast<const unary_node<T>*>(node)->operation());
         }
         else
            return false;
      }

      template <typename T>
      inline bool branch_deletable(expression_node<T>* node)
      {
         return (0 != node)             &&
                !is_variable_node(node) &&
                !is_string_node  (node) ;
      }

      template <std::size_t N, typename T>
      inline bool all_nodes_valid(expression_node<T>* (&b)[N])
      {
         for (std::size_t i = 0; i < N; ++i)
         {
            if (0 == b[i]) return false;
         }

         return true;
      }

      template <typename T,
                typename Allocator,
                template <typename, typename> class Sequence>
      inline bool all_nodes_valid(const Sequence<expression_node<T>*,Allocator>& b)
      {
         for (std::size_t i = 0; i < b.size(); ++i)
         {
            if (0 == b[i]) return false;
         }

         return true;
      }

      template <std::size_t N, typename T>
      inline bool all_nodes_variables(expression_node<T>* (&b)[N])
      {
         for (std::size_t i = 0; i < N; ++i)
         {
            if (0 == b[i])
               return false;
            else if (!is_variable_node(b[i]))
               return false;
         }

         return true;
      }

      template <typename T,
                typename Allocator,
                template <typename, typename> class Sequence>
      inline bool all_nodes_variables(Sequence<expression_node<T>*,Allocator>& b)
      {
         for (std::size_t i = 0; i < b.size(); ++i)
         {
            if (0 == b[i])
               return false;
            else if (!is_variable_node(b[i]))
               return false;
         }

         return true;
      }

      template <typename Node>
      class node_collection_destructor
      {
      public:

         typedef node_collector_interface<Node> nci_t;

         typedef typename nci_t::node_ptr_t     node_ptr_t;
         typedef typename nci_t::node_pp_t      node_pp_t;
         typedef typename nci_t::noderef_list_t noderef_list_t;

         static void delete_nodes(node_ptr_t& root)
         {
            std::vector<node_pp_t> node_delete_list;
            node_delete_list.reserve(1000);

            collect_nodes(root, node_delete_list);

            for (std::size_t i = 0; i < node_delete_list.size(); ++i)
            {
               node_ptr_t& node = *node_delete_list[i];
               exprtk_debug(("ncd::delete_nodes() - deleting: %p\n", static_cast<void*>(node)));
               delete node;
               node = reinterpret_cast<node_ptr_t>(0);
            }
         }

      private:

         static void collect_nodes(node_ptr_t& root, noderef_list_t& node_delete_list)
         {
            std::deque<node_ptr_t> node_list;
            node_list.push_back(root);
            node_delete_list.push_back(&root);

            noderef_list_t child_node_delete_list;
            child_node_delete_list.reserve(1000);

            while (!node_list.empty())
            {
               node_list.front()->collect_nodes(child_node_delete_list);

               if (!child_node_delete_list.empty())
               {
                  for (std::size_t i = 0; i < child_node_delete_list.size(); ++i)
                  {
                     node_pp_t& node = child_node_delete_list[i];

                     if (0 == (*node))
                     {
                        exprtk_debug(("ncd::collect_nodes() - null node encountered.\n"));
                     }

                     node_list.push_back(*node);
                  }

                  node_delete_list.insert(
                     node_delete_list.end(),
                     child_node_delete_list.begin(), child_node_delete_list.end());

                  child_node_delete_list.clear();
               }

               node_list.pop_front();
            }

            std::reverse(node_delete_list.begin(), node_delete_list.end());
         }
      };

      template <typename NodeAllocator, typename T, std::size_t N>
      inline void free_all_nodes(NodeAllocator& node_allocator, expression_node<T>* (&b)[N])
      {
         for (std::size_t i = 0; i < N; ++i)
         {
            free_node(node_allocator,b[i]);
         }
      }

      template <typename NodeAllocator,
                typename T,
                typename Allocator,
                template <typename, typename> class Sequence>
      inline void free_all_nodes(NodeAllocator& node_allocator, Sequence<expression_node<T>*,Allocator>& b)
      {
         for (std::size_t i = 0; i < b.size(); ++i)
         {
            free_node(node_allocator,b[i]);
         }

         b.clear();
      }

      template <typename NodeAllocator, typename T>
      inline void free_node(NodeAllocator&, expression_node<T>*& node)
      {
         if ((0 == node) || is_variable_node(node) || is_string_node(node))
         {
            return;
         }

         node_collection_destructor<expression_node<T> >
            ::delete_nodes(node);
      }

      template <typename T>
      inline void destroy_node(expression_node<T>*& node)
      {
         if (0 != node)
         {
            node_collection_destructor<expression_node<T> >
               ::delete_nodes(node);
         }
      }

      template <typename Node>
      struct node_depth_base
      {
         typedef Node* node_ptr_t;
         typedef std::pair<node_ptr_t,bool> nb_pair_t;

         node_depth_base()
         : depth_set(false)
         , depth(0)
         {}

         virtual ~node_depth_base() {}

         virtual std::size_t node_depth() const { return 1; }

         std::size_t compute_node_depth(const Node* const& node) const
         {
            if (!depth_set)
            {
               depth = 1 + (node ? node->node_depth() : 0);
               depth_set = true;
            }

            return depth;
         }

         std::size_t compute_node_depth(const nb_pair_t& branch) const
         {
            if (!depth_set)
            {
               depth = 1 + (branch.first ? branch.first->node_depth() : 0);
               depth_set = true;
            }

            return depth;
         }

         template <std::size_t N>
         std::size_t compute_node_depth(const nb_pair_t (&branch)[N]) const
         {
            if (!depth_set)
            {
               depth = 0;
               for (std::size_t i = 0; i < N; ++i)
               {
                  if (branch[i].first)
                  {
                     depth = (std::max)(depth,branch[i].first->node_depth());
                  }
               }
               depth += 1;
               depth_set = true;
            }

            return depth;
         }

         template <typename BranchType>
         std::size_t compute_node_depth(const BranchType& n0, const BranchType& n1) const
         {
            if (!depth_set)
            {
               depth = 1 + (std::max)(compute_node_depth(n0), compute_node_depth(n1));
               depth_set = true;
            }

            return depth;
         }

         template <typename BranchType>
         std::size_t compute_node_depth(const BranchType& n0, const BranchType& n1,
                                        const BranchType& n2) const
         {
            if (!depth_set)
            {
               depth = 1 + (std::max)((std::max)(compute_node_depth(n0), compute_node_depth(n1)),
                              compute_node_depth(n2));
               depth_set = true;
            }

            return depth;
         }

         template <typename BranchType>
         std::size_t compute_node_depth(const BranchType& n0, const BranchType& n1,
                                        const BranchType& n2, const BranchType& n3) const
         {
            if (!depth_set)
            {
               depth = 1 + (std::max)(
				   (std::max)(compute_node_depth(n0), compute_node_depth(n1)),
				   (std::max)(compute_node_depth(n2), compute_node_depth(n3)));
               depth_set = true;
            }

            return depth;
         }

         template <typename Allocator,
                   template <typename, typename> class Sequence>
         std::size_t compute_node_depth(const Sequence<node_ptr_t, Allocator>& branch_list) const
         {
            if (!depth_set)
            {
               for (std::size_t i = 0; i < branch_list.size(); ++i)
               {
                  if (branch_list[i])
                  {
                     depth = (std::max)(depth, compute_node_depth(branch_list[i]));
                  }
               }
               depth_set = true;
            }

            return depth;
         }

         template <typename Allocator,
                   template <typename, typename> class Sequence>
         std::size_t compute_node_depth(const Sequence<nb_pair_t,Allocator>& branch_list) const
         {
            if (!depth_set)
            {
               for (std::size_t i = 0; i < branch_list.size(); ++i)
               {
                  if (branch_list[i].first)
                  {
                     depth = (std::max)(depth, compute_node_depth(branch_list[i].first));
                  }
               }
               depth_set = true;
            }

            return depth;
         }

         mutable bool depth_set;
         mutable std::size_t depth;

         template <typename NodeSequence>
         void collect(node_ptr_t const& node,
                      const bool deletable,
                      NodeSequence& delete_node_list) const
         {
            if ((0 != node) && deletable)
            {
               delete_node_list.push_back(const_cast<node_ptr_t*>(&node));
            }
         }

         template <typename NodeSequence>
         void collect(const nb_pair_t& branch,
                      NodeSequence& delete_node_list) const
         {
            collect(branch.first, branch.second, delete_node_list);
         }

         template <typename NodeSequence>
         void collect(Node*& node,
                      NodeSequence& delete_node_list) const
         {
            collect(node, branch_deletable(node), delete_node_list);
         }

         template <std::size_t N, typename NodeSequence>
         void collect(const nb_pair_t(&branch)[N],
                      NodeSequence& delete_node_list) const
         {
            for (std::size_t i = 0; i < N; ++i)
            {
               collect(branch[i].first, branch[i].second, delete_node_list);
            }
         }

         template <typename Allocator,
                   template <typename, typename> class Sequence,
                   typename NodeSequence>
         void collect(const Sequence<nb_pair_t, Allocator>& branch,
                      NodeSequence& delete_node_list) const
         {
            for (std::size_t i = 0; i < branch.size(); ++i)
            {
               collect(branch[i].first, branch[i].second, delete_node_list);
            }
         }

         template <typename Allocator,
                   template <typename, typename> class Sequence,
                   typename NodeSequence>
         void collect(const Sequence<node_ptr_t, Allocator>& branch_list,
                      NodeSequence& delete_node_list) const
         {
            for (std::size_t i = 0; i < branch_list.size(); ++i)
            {
               collect(branch_list[i], branch_deletable(branch_list[i]), delete_node_list);
            }
         }

         template <typename Boolean,
                   typename AllocatorT,
                   typename AllocatorB,
                   template <typename, typename> class Sequence,
                   typename NodeSequence>
         void collect(const Sequence<node_ptr_t, AllocatorT>& branch_list,
                      const Sequence<Boolean, AllocatorB>& branch_deletable_list,
                      NodeSequence& delete_node_list) const
         {
            for (std::size_t i = 0; i < branch_list.size(); ++i)
            {
               collect(branch_list[i], branch_deletable_list[i], delete_node_list);
            }
         }
      };

      template <typename Type>
      class vector_holder
      {
      private:

         typedef Type value_type;
         typedef value_type* value_ptr;
         typedef const value_ptr const_value_ptr;

         class vector_holder_base
         {
         public:

            virtual ~vector_holder_base() {}

            inline value_ptr operator[](const std::size_t& index) const
            {
               return value_at(index);
            }

            inline std::size_t size() const
            {
               return vector_size();
            }

            inline value_ptr data() const
            {
               return value_at(0);
            }

            virtual inline bool rebaseable() const
            {
               return false;
            }

            virtual void set_ref(value_ptr*) {}

         protected:

            virtual value_ptr value_at(const std::size_t&) const = 0;
            virtual std::size_t vector_size()              const = 0;
         };

         class array_vector_impl : public vector_holder_base
         {
         public:

            array_vector_impl(const Type* vec, const std::size_t& vec_size)
            : vec_(vec)
            , size_(vec_size)
            {}

         protected:

            value_ptr value_at(const std::size_t& index) const
            {
               if (index < size_)
                  return const_cast<const_value_ptr>(vec_ + index);
               else
                  return const_value_ptr(0);
            }

            std::size_t vector_size() const
            {
               return size_;
            }

         private:

            array_vector_impl operator=(const array_vector_impl&);

            const Type* vec_;
            const std::size_t size_;
         };

         template <typename Allocator,
                   template <typename, typename> class Sequence>
         class sequence_vector_impl : public vector_holder_base
         {
         public:

            typedef Sequence<Type,Allocator> sequence_t;

            sequence_vector_impl(sequence_t& seq)
            : sequence_(seq)
            {}

         protected:

            value_ptr value_at(const std::size_t& index) const
            {
               return (index < sequence_.size()) ? (&sequence_[index]) : const_value_ptr(0);
            }

            std::size_t vector_size() const
            {
               return sequence_.size();
            }

         private:

            sequence_vector_impl operator=(const sequence_vector_impl&);

            sequence_t& sequence_;
         };

         class vector_view_impl : public vector_holder_base
         {
         public:

            typedef exprtk::vector_view<Type> vector_view_t;

            vector_view_impl(vector_view_t& vec_view)
            : vec_view_(vec_view)
            {}

            void set_ref(value_ptr* ref)
            {
               vec_view_.set_ref(ref);
            }

            virtual inline bool rebaseable() const
            {
               return true;
            }

         protected:

            value_ptr value_at(const std::size_t& index) const
            {
               return (index < vec_view_.size()) ? (&vec_view_[index]) : const_value_ptr(0);
            }

            std::size_t vector_size() const
            {
               return vec_view_.size();
            }

         private:

            vector_view_impl operator=(const vector_view_impl&);

            vector_view_t& vec_view_;
         };

      public:

         typedef typename details::vec_data_store<Type> vds_t;

         vector_holder(Type* vec, const std::size_t& vec_size)
         : vector_holder_base_(new(buffer)array_vector_impl(vec,vec_size))
         {}

         vector_holder(const vds_t& vds)
         : vector_holder_base_(new(buffer)array_vector_impl(vds.data(),vds.size()))
         {}

         template <typename Allocator>
         vector_holder(std::vector<Type,Allocator>& vec)
         : vector_holder_base_(new(buffer)sequence_vector_impl<Allocator,std::vector>(vec))
         {}

         vector_holder(exprtk::vector_view<Type>& vec)
         : vector_holder_base_(new(buffer)vector_view_impl(vec))
         {}

         inline value_ptr operator[](const std::size_t& index) const
         {
            return (*vector_holder_base_)[index];
         }

         inline std::size_t size() const
         {
            return vector_holder_base_->size();
         }

         inline value_ptr data() const
         {
            return vector_holder_base_->data();
         }

         void set_ref(value_ptr* ref)
         {
            vector_holder_base_->set_ref(ref);
         }

         bool rebaseable() const
         {
            return vector_holder_base_->rebaseable();
         }

      private:

         mutable vector_holder_base* vector_holder_base_;
         uchar_t buffer[64];
      };

      template <typename T>
      class null_node exprtk_final : public expression_node<T>
      {
      public:

         inline T value() const
         {
            return std::numeric_limits<T>::quiet_NaN();
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_null;
         }
      };

      template <typename T, std::size_t N>
      inline void construct_branch_pair(std::pair<expression_node<T>*,bool> (&branch)[N],
                                        expression_node<T>* b,
                                        const std::size_t& index)
      {
         if (b && (index < N))
         {
            branch[index] = std::make_pair(b,branch_deletable(b));
         }
      }

      template <typename T>
      inline void construct_branch_pair(std::pair<expression_node<T>*,bool>& branch, expression_node<T>* b)
      {
         if (b)
         {
            branch = std::make_pair(b,branch_deletable(b));
         }
      }

      template <std::size_t N, typename T>
      inline void init_branches(std::pair<expression_node<T>*,bool> (&branch)[N],
                                expression_node<T>* b0,
                                expression_node<T>* b1 = reinterpret_cast<expression_node<T>*>(0),
                                expression_node<T>* b2 = reinterpret_cast<expression_node<T>*>(0),
                                expression_node<T>* b3 = reinterpret_cast<expression_node<T>*>(0),
                                expression_node<T>* b4 = reinterpret_cast<expression_node<T>*>(0),
                                expression_node<T>* b5 = reinterpret_cast<expression_node<T>*>(0),
                                expression_node<T>* b6 = reinterpret_cast<expression_node<T>*>(0),
                                expression_node<T>* b7 = reinterpret_cast<expression_node<T>*>(0),
                                expression_node<T>* b8 = reinterpret_cast<expression_node<T>*>(0),
                                expression_node<T>* b9 = reinterpret_cast<expression_node<T>*>(0))
      {
         construct_branch_pair(branch, b0, 0);
         construct_branch_pair(branch, b1, 1);
         construct_branch_pair(branch, b2, 2);
         construct_branch_pair(branch, b3, 3);
         construct_branch_pair(branch, b4, 4);
         construct_branch_pair(branch, b5, 5);
         construct_branch_pair(branch, b6, 6);
         construct_branch_pair(branch, b7, 7);
         construct_branch_pair(branch, b8, 8);
         construct_branch_pair(branch, b9, 9);
      }

      template <typename T>
      class null_eq_node exprtk_final : public expression_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef std::pair<expression_ptr,bool> branch_t;

         explicit null_eq_node(expression_ptr branch, const bool equality = true)
         : equality_(equality)
         {
            construct_branch_pair(branch_, branch);
         }

         inline T value() const
         {
            assert(branch_.first);

            const T v = branch_.first->value();
            const bool result = details::numeric::is_nan(v);

            if (result)
               return (equality_) ? T(1) : T(0);
            else
               return (equality_) ? T(0) : T(1);
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_nulleq;
         }

         inline operator_type operation() const
         {
            return details::e_eq;
         }

         inline expression_node<T>* branch(const std::size_t&) const
         {
            return branch_.first;
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::collect(branch_, node_delete_list);
         }

         std::size_t node_depth() const
         {
            return expression_node<T>::ndb_t::compute_node_depth(branch_);
         }

      private:

         bool equality_;
         branch_t branch_;
      };

      template <typename T>
      class literal_node exprtk_final : public expression_node<T>
      {
      public:

         explicit literal_node(const T& v)
         : value_(v)
         {}

         inline T value() const
         {
            return value_;
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_constant;
         }

         inline expression_node<T>* branch(const std::size_t&) const
         {
            return reinterpret_cast<expression_node<T>*>(0);
         }

      private:

         literal_node(literal_node<T>&) {}
         literal_node<T>& operator=(literal_node<T>&) { return (*this); }

         const T value_;
      };

      template <typename T>
      struct range_pack;

      template <typename T>
      struct range_data_type;

      template <typename T>
      class range_interface
      {
      public:

         typedef range_pack<T> range_t;

         virtual ~range_interface()
         {}

         virtual range_t& range_ref() = 0;

         virtual const range_t& range_ref() const = 0;
      };

      #ifndef exprtk_disable_string_capabilities
      template <typename T>
      class string_base_node
      {
      public:

         typedef range_data_type<T> range_data_type_t;

         virtual ~string_base_node()
         {}

         virtual std::string str () const = 0;

         virtual char_cptr   base() const = 0;

         virtual std::size_t size() const = 0;
      };

      template <typename T>
      class string_literal_node exprtk_final
                                : public expression_node <T>,
                                  public string_base_node<T>,
                                  public range_interface <T>
      {
      public:

         typedef range_pack<T> range_t;

         explicit string_literal_node(const std::string& v)
         : value_(v)
         {
            rp_.n0_c = std::make_pair<bool,std::size_t>(true,0);
            rp_.n1_c = std::make_pair<bool,std::size_t>(true,v.size() - 1);
            rp_.cache.first  = rp_.n0_c.second;
            rp_.cache.second = rp_.n1_c.second;
         }

         inline T value() const
         {
            return std::numeric_limits<T>::quiet_NaN();
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_stringconst;
         }

         inline expression_node<T>* branch(const std::size_t&) const
         {
            return reinterpret_cast<expression_node<T>*>(0);
         }

         std::string str() const
         {
            return value_;
         }

         char_cptr base() const
         {
            return value_.data();
         }

         std::size_t size() const
         {
            return value_.size();
         }

         range_t& range_ref()
         {
            return rp_;
         }

         const range_t& range_ref() const
         {
            return rp_;
         }

      private:

         string_literal_node(const string_literal_node<T>&);
         string_literal_node<T>& operator=(const string_literal_node<T>&);

         const std::string value_;
         range_t rp_;
      };
      #endif

      template <typename T>
      class unary_node : public expression_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef std::pair<expression_ptr,bool> branch_t;

         unary_node(const operator_type& opr, expression_ptr branch)
         : operation_(opr)
         {
            construct_branch_pair(branch_,branch);
         }

         inline T value() const
         {
            assert(branch_.first);

            const T arg = branch_.first->value();

            return numeric::process<T>(operation_,arg);
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_unary;
         }

         inline operator_type operation() const
         {
            return operation_;
         }

         inline expression_node<T>* branch(const std::size_t&) const
         {
            return branch_.first;
         }

         inline void release()
         {
            branch_.second = false;
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::collect(branch_, node_delete_list);
         }

         std::size_t node_depth() const exprtk_final
         {
            return expression_node<T>::ndb_t::compute_node_depth(branch_);
         }

      protected:

         operator_type operation_;
         branch_t branch_;
      };

      template <typename T>
      class binary_node : public expression_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef std::pair<expression_ptr,bool> branch_t;

         binary_node(const operator_type& opr,
                     expression_ptr branch0,
                     expression_ptr branch1)
         : operation_(opr)
         {
            init_branches<2>(branch_, branch0, branch1);
         }

         inline T value() const
         {
            assert(branch_[0].first);
            assert(branch_[1].first);

            const T arg0 = branch_[0].first->value();
            const T arg1 = branch_[1].first->value();

            return numeric::process<T>(operation_,arg0,arg1);
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_binary;
         }

         inline operator_type operation()
         {
            return operation_;
         }

         inline expression_node<T>* branch(const std::size_t& index = 0) const
         {
            if (0 == index)
               return branch_[0].first;
            else if (1 == index)
               return branch_[1].first;
            else
               return reinterpret_cast<expression_ptr>(0);
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::template collect(branch_, node_delete_list);
         }

         std::size_t node_depth() const exprtk_final
         {
            return expression_node<T>::ndb_t::template compute_node_depth<2>(branch_);
         }

      protected:

         operator_type operation_;
         branch_t branch_[2];
      };

      template <typename T, typename Operation>
      class binary_ext_node exprtk_final : public expression_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef std::pair<expression_ptr,bool> branch_t;

         binary_ext_node(expression_ptr branch0, expression_ptr branch1)
         {
            init_branches<2>(branch_, branch0, branch1);
         }

         inline T value() const
         {
            assert(branch_[0].first);
            assert(branch_[1].first);

            const T arg0 = branch_[0].first->value();
            const T arg1 = branch_[1].first->value();

            return Operation::process(arg0,arg1);
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_binary_ext;
         }

         inline operator_type operation()
         {
            return Operation::operation();
         }

         inline expression_node<T>* branch(const std::size_t& index = 0) const
         {
            if (0 == index)
               return branch_[0].first;
            else if (1 == index)
               return branch_[1].first;
            else
               return reinterpret_cast<expression_ptr>(0);
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::template collect(branch_, node_delete_list);
         }

         std::size_t node_depth() const
         {
            return expression_node<T>::ndb_t::template compute_node_depth<2>(branch_);
         }

      protected:

         branch_t branch_[2];
      };

      template <typename T>
      class trinary_node : public expression_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef std::pair<expression_ptr,bool> branch_t;

         trinary_node(const operator_type& opr,
                      expression_ptr branch0,
                      expression_ptr branch1,
                      expression_ptr branch2)
         : operation_(opr)
         {
            init_branches<3>(branch_, branch0, branch1, branch2);
         }

         inline T value() const
         {
            assert(branch_[0].first);
            assert(branch_[1].first);
            assert(branch_[2].first);

            const T arg0 = branch_[0].first->value();
            const T arg1 = branch_[1].first->value();
            const T arg2 = branch_[2].first->value();

            switch (operation_)
            {
               case e_inrange : return (arg1 < arg0) ? T(0) : ((arg1 > arg2) ? T(0) : T(1));

               case e_clamp   : return (arg1 < arg0) ? arg0 : (arg1 > arg2 ? arg2 : arg1);

               case e_iclamp  : if ((arg1 <= arg0) || (arg1 >= arg2))
                                   return arg1;
                                else
                                   return ((T(2) * arg1  <= (arg2 + arg0)) ? arg0 : arg2);

               default        : exprtk_debug(("trinary_node::value() - Error: Invalid operation\n"));
                                return std::numeric_limits<T>::quiet_NaN();
            }
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_trinary;
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::template collect(branch_, node_delete_list);
         }

         std::size_t node_depth() const exprtk_final
         {
            return expression_node<T>::ndb_t::template compute_node_depth<3>(branch_);
         }

      protected:

         operator_type operation_;
         branch_t branch_[3];
      };

      template <typename T>
      class quaternary_node : public expression_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef std::pair<expression_ptr,bool> branch_t;

         quaternary_node(const operator_type& opr,
                         expression_ptr branch0,
                         expression_ptr branch1,
                         expression_ptr branch2,
                         expression_ptr branch3)
         : operation_(opr)
         {
            init_branches<4>(branch_, branch0, branch1, branch2, branch3);
         }

         inline T value() const
         {
            return std::numeric_limits<T>::quiet_NaN();
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_quaternary;
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::template collect(branch_, node_delete_list);
         }

         std::size_t node_depth() const exprtk_final
         {
            return expression_node<T>::ndb_t::template compute_node_depth<4>(branch_);
         }

      protected:

         operator_type operation_;
         branch_t branch_[4];
      };

      template <typename T>
      class conditional_node exprtk_final : public expression_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef std::pair<expression_ptr,bool> branch_t;

         conditional_node(expression_ptr condition,
                          expression_ptr consequent,
                          expression_ptr alternative)
         {
            construct_branch_pair(condition_  , condition  );
            construct_branch_pair(consequent_ , consequent );
            construct_branch_pair(alternative_, alternative);
         }

         inline T value() const
         {
            assert(condition_  .first);
            assert(consequent_ .first);
            assert(alternative_.first);

            if (is_true(condition_))
               return consequent_.first->value();
            else
               return alternative_.first->value();
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_conditional;
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::collect(condition_   , node_delete_list);
            expression_node<T>::ndb_t::collect(consequent_  , node_delete_list);
            expression_node<T>::ndb_t::collect(alternative_ , node_delete_list);
         }

         std::size_t node_depth() const
         {
            return expression_node<T>::ndb_t::compute_node_depth
               (condition_, consequent_, alternative_);
         }

      private:

         branch_t condition_;
         branch_t consequent_;
         branch_t alternative_;
      };

      template <typename T>
      class cons_conditional_node exprtk_final : public expression_node<T>
      {
      public:

         // Consequent only conditional statement node
         typedef expression_node<T>* expression_ptr;
         typedef std::pair<expression_ptr,bool> branch_t;

         cons_conditional_node(expression_ptr condition,
                               expression_ptr consequent)
         {
            construct_branch_pair(condition_ , condition );
            construct_branch_pair(consequent_, consequent);
         }

         inline T value() const
         {
            assert(condition_ .first);
            assert(consequent_.first);

            if (is_true(condition_))
               return consequent_.first->value();
            else
               return std::numeric_limits<T>::quiet_NaN();
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_conditional;
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::collect(condition_  , node_delete_list);
            expression_node<T>::ndb_t::collect(consequent_ , node_delete_list);
         }

         std::size_t node_depth() const
         {
            return expression_node<T>::ndb_t::
               compute_node_depth(condition_, consequent_);
         }

      private:

         branch_t condition_;
         branch_t consequent_;
      };

      #ifndef exprtk_disable_break_continue
      template <typename T>
      class break_exception
      {
      public:

         explicit break_exception(const T& v)
         : value(v)
         {}

         T value;
      };

      class continue_exception
      {};

      template <typename T>
      class break_node exprtk_final : public expression_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef std::pair<expression_ptr,bool> branch_t;

         break_node(expression_ptr ret = expression_ptr(0))
         {
            construct_branch_pair(return_, ret);
         }

         inline T value() const
         {
            throw break_exception<T>(return_.first ? return_.first->value() : std::numeric_limits<T>::quiet_NaN());
            #ifndef _MSC_VER
            return std::numeric_limits<T>::quiet_NaN();
            #endif
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_break;
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::collect(return_, node_delete_list);
         }

         std::size_t node_depth() const
         {
            return expression_node<T>::ndb_t::compute_node_depth(return_);
         }

      private:

         branch_t return_;
      };

      template <typename T>
      class continue_node exprtk_final : public expression_node<T>
      {
      public:

         inline T value() const
         {
            throw continue_exception();
            #ifndef _MSC_VER
            return std::numeric_limits<T>::quiet_NaN();
            #endif
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_break;
         }
      };
      #endif

      #ifdef exprtk_enable_runtime_checks
      struct loop_runtime_checker
      {
         loop_runtime_checker(loop_runtime_check_ptr loop_rt_chk = loop_runtime_check_ptr(0),
                              loop_runtime_check::loop_types lp_typ = loop_runtime_check::e_invalid)
         : iteration_count_(0)
         , loop_runtime_check_(loop_rt_chk)
         , loop_type(lp_typ)
         {}

         inline void reset(const _uint64_t initial_value = 0) const
         {
            iteration_count_ = initial_value;
         }

         inline bool check() const
         {
            if (
                 (0 == loop_runtime_check_) ||
                 (++iteration_count_ <= loop_runtime_check_->max_loop_iterations)
               )
            {
               return true;
            }

            loop_runtime_check::violation_context ctxt;
            ctxt.loop      = loop_type;
            ctxt.violation = loop_runtime_check::e_iteration_count;

            loop_runtime_check_->handle_runtime_violation(ctxt);

            return false;
         }

         mutable _uint64_t iteration_count_;
         mutable loop_runtime_check_ptr loop_runtime_check_;
         loop_runtime_check::loop_types loop_type;
      };
      #else
      struct loop_runtime_checker
      {
         loop_runtime_checker(loop_runtime_check_ptr, loop_runtime_check::loop_types)
         {}

         inline void reset(const _uint64_t = 0) const
         {}

         inline bool check() const
         {
            return true;
         }
      };
      #endif

      template <typename T>
      class while_loop_node exprtk_final
                            : public expression_node<T>,
                              public loop_runtime_checker
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef std::pair<expression_ptr,bool> branch_t;

         while_loop_node(expression_ptr condition,
                         expression_ptr loop_body,
                         loop_runtime_check_ptr loop_rt_chk = loop_runtime_check_ptr(0))
         : loop_runtime_checker(loop_rt_chk,loop_runtime_check::e_while_loop)
         {
            construct_branch_pair(condition_, condition);
            construct_branch_pair(loop_body_, loop_body);
         }

         inline T value() const
         {
            assert(condition_.first);
            assert(loop_body_.first);

            T result = T(0);

            loop_runtime_checker::reset();

            while (is_true(condition_) && loop_runtime_checker::check())
            {
               result = loop_body_.first->value();
            }

            return result;
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_while;
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::collect(condition_ , node_delete_list);
            expression_node<T>::ndb_t::collect(loop_body_ , node_delete_list);
         }

         std::size_t node_depth() const
         {
            return expression_node<T>::ndb_t::compute_node_depth(condition_, loop_body_);
         }

      private:

         branch_t condition_;
         branch_t loop_body_;
      };

      template <typename T>
      class repeat_until_loop_node exprtk_final
                                   : public expression_node<T>,
                                     public loop_runtime_checker
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef std::pair<expression_ptr,bool> branch_t;

         repeat_until_loop_node(expression_ptr condition,
                                expression_ptr loop_body,
                                loop_runtime_check_ptr loop_rt_chk = loop_runtime_check_ptr(0))
         : loop_runtime_checker(loop_rt_chk, loop_runtime_check::e_repeat_until_loop)
         {
            construct_branch_pair(condition_, condition);
            construct_branch_pair(loop_body_, loop_body);
         }

         inline T value() const
         {
            assert(condition_.first);
            assert(loop_body_.first);

            T result = T(0);

            loop_runtime_checker::reset(1);

            do
            {
               result = loop_body_.first->value();
            }
            while (is_false(condition_.first) && loop_runtime_checker::check());

            return result;
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_repeat;
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::collect(condition_ , node_delete_list);
            expression_node<T>::ndb_t::collect(loop_body_ , node_delete_list);
         }

         std::size_t node_depth() const
         {
            return expression_node<T>::ndb_t::compute_node_depth(condition_, loop_body_);
         }

      private:

         branch_t condition_;
         branch_t loop_body_;
      };

      template <typename T>
      class for_loop_node exprtk_final
                          : public expression_node<T>,
                            public loop_runtime_checker
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef std::pair<expression_ptr,bool> branch_t;

         for_loop_node(expression_ptr initialiser,
                       expression_ptr condition,
                       expression_ptr incrementor,
                       expression_ptr loop_body,
                       loop_runtime_check_ptr loop_rt_chk = loop_runtime_check_ptr(0))
         : loop_runtime_checker(loop_rt_chk, loop_runtime_check::e_for_loop)
         {
            construct_branch_pair(initialiser_, initialiser);
            construct_branch_pair(condition_  , condition  );
            construct_branch_pair(incrementor_, incrementor);
            construct_branch_pair(loop_body_  , loop_body  );
         }

         inline T value() const
         {
            assert(condition_.first);
            assert(loop_body_.first);

            T result = T(0);

            loop_runtime_checker::reset();

            if (initialiser_.first)
               initialiser_.first->value();

            if (incrementor_.first)
            {
               while (is_true(condition_) && loop_runtime_checker::check())
               {
                  result = loop_body_.first->value();
                  incrementor_.first->value();
               }
            }
            else
            {
               while (is_true(condition_) && loop_runtime_checker::check())
               {
                  result = loop_body_.first->value();
               }
            }

            return result;
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_for;
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::collect(initialiser_ , node_delete_list);
            expression_node<T>::ndb_t::collect(condition_   , node_delete_list);
            expression_node<T>::ndb_t::collect(incrementor_ , node_delete_list);
            expression_node<T>::ndb_t::collect(loop_body_   , node_delete_list);
         }

         std::size_t node_depth() const
         {
            return expression_node<T>::ndb_t::compute_node_depth
               (initialiser_, condition_, incrementor_, loop_body_);
         }

      private:

         branch_t initialiser_;
         branch_t condition_  ;
         branch_t incrementor_;
         branch_t loop_body_  ;
      };

      #ifndef exprtk_disable_break_continue
      template <typename T>
      class while_loop_bc_node exprtk_final
                               : public expression_node<T>,
                                 public loop_runtime_checker
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef std::pair<expression_ptr,bool> branch_t;

         while_loop_bc_node(expression_ptr condition,
                            expression_ptr loop_body,
                            loop_runtime_check_ptr loop_rt_chk = loop_runtime_check_ptr(0))
         : loop_runtime_checker(loop_rt_chk, loop_runtime_check::e_while_loop)
         {
            construct_branch_pair(condition_, condition);
            construct_branch_pair(loop_body_, loop_body);
         }

         inline T value() const
         {
            assert(condition_.first);
            assert(loop_body_.first);

            T result = T(0);

            loop_runtime_checker::reset();

            while (is_true(condition_) && loop_runtime_checker::check())
            {
               try
               {
                  result = loop_body_.first->value();
               }
               catch(const break_exception<T>& e)
               {
                  return e.value;
               }
               catch(const continue_exception&)
               {}
            }

            return result;
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_while;
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::collect(condition_ , node_delete_list);
            expression_node<T>::ndb_t::collect(loop_body_ , node_delete_list);
         }

         std::size_t node_depth() const
         {
            return expression_node<T>::ndb_t::compute_node_depth(condition_, loop_body_);
         }

      private:

         branch_t condition_;
         branch_t loop_body_;
      };

      template <typename T>
      class repeat_until_loop_bc_node exprtk_final
                                      : public expression_node<T>,
                                        public loop_runtime_checker
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef std::pair<expression_ptr,bool> branch_t;

         repeat_until_loop_bc_node(expression_ptr condition,
                                   expression_ptr loop_body,
                                   loop_runtime_check_ptr loop_rt_chk = loop_runtime_check_ptr(0))
         : loop_runtime_checker(loop_rt_chk, loop_runtime_check::e_repeat_until_loop)
         {
            construct_branch_pair(condition_, condition);
            construct_branch_pair(loop_body_, loop_body);
         }

         inline T value() const
         {
            assert(condition_.first);
            assert(loop_body_.first);

            T result = T(0);

            loop_runtime_checker::reset();

            do
            {
               try
               {
                  result = loop_body_.first->value();
               }
               catch(const break_exception<T>& e)
               {
                  return e.value;
               }
               catch(const continue_exception&)
               {}
            }
            while (is_false(condition_.first) && loop_runtime_checker::check());

            return result;
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_repeat;
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::collect(condition_ , node_delete_list);
            expression_node<T>::ndb_t::collect(loop_body_ , node_delete_list);
         }

         std::size_t node_depth() const
         {
            return expression_node<T>::ndb_t::compute_node_depth(condition_, loop_body_);
         }

      private:

         branch_t condition_;
         branch_t loop_body_;
      };

      template <typename T>
      class for_loop_bc_node exprtk_final
                             : public expression_node<T>,
                               public loop_runtime_checker
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef std::pair<expression_ptr,bool> branch_t;

         for_loop_bc_node(expression_ptr initialiser,
                          expression_ptr condition,
                          expression_ptr incrementor,
                          expression_ptr loop_body,
                          loop_runtime_check_ptr loop_rt_chk = loop_runtime_check_ptr(0))
         : loop_runtime_checker(loop_rt_chk, loop_runtime_check::e_for_loop)
         {
            construct_branch_pair(initialiser_, initialiser);
            construct_branch_pair(condition_  , condition  );
            construct_branch_pair(incrementor_, incrementor);
            construct_branch_pair(loop_body_  , loop_body  );
         }

         inline T value() const
         {
            assert(condition_.first);
            assert(loop_body_.first);

            T result = T(0);

            loop_runtime_checker::reset();

            if (initialiser_.first)
               initialiser_.first->value();

            if (incrementor_.first)
            {
               while (is_true(condition_) && loop_runtime_checker::check())
               {
                  try
                  {
                     result = loop_body_.first->value();
                  }
                  catch(const break_exception<T>& e)
                  {
                     return e.value;
                  }
                  catch(const continue_exception&)
                  {}

                  incrementor_.first->value();
               }
            }
            else
            {
               while (is_true(condition_) && loop_runtime_checker::check())
               {
                  try
                  {
                     result = loop_body_.first->value();
                  }
                  catch(const break_exception<T>& e)
                  {
                     return e.value;
                  }
                  catch(const continue_exception&)
                  {}
               }
            }

            return result;
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_for;
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::collect(initialiser_ , node_delete_list);
            expression_node<T>::ndb_t::collect(condition_   , node_delete_list);
            expression_node<T>::ndb_t::collect(incrementor_ , node_delete_list);
            expression_node<T>::ndb_t::collect(loop_body_   , node_delete_list);
         }

         std::size_t node_depth() const
         {
            return expression_node<T>::ndb_t::compute_node_depth
               (initialiser_, condition_, incrementor_, loop_body_);
         }

      private:

         branch_t initialiser_;
         branch_t condition_  ;
         branch_t incrementor_;
         branch_t loop_body_  ;
      };
      #endif

      template <typename T>
      class switch_node : public expression_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef std::pair<expression_ptr,bool> branch_t;

         template <typename Allocator,
                   template <typename, typename> class Sequence>
         explicit switch_node(const Sequence<expression_ptr,Allocator>& arg_list)
         {
            if (1 != (arg_list.size() & 1))
               return;

            arg_list_.resize(arg_list.size());

            for (std::size_t i = 0; i < arg_list.size(); ++i)
            {
               if (arg_list[i])
               {
                  construct_branch_pair(arg_list_[i], arg_list[i]);
               }
               else
               {
                  arg_list_.clear();
                  return;
               }
            }
         }

         inline T value() const
         {
            if (!arg_list_.empty())
            {
               const std::size_t upper_bound = (arg_list_.size() - 1);

               for (std::size_t i = 0; i < upper_bound; i += 2)
               {
                  expression_ptr condition  = arg_list_[i    ].first;
                  expression_ptr consequent = arg_list_[i + 1].first;

                  if (is_true(condition))
                  {
                     return consequent->value();
                  }
               }

               return arg_list_[upper_bound].first->value();
            }
            else
               return std::numeric_limits<T>::quiet_NaN();
         }

         inline typename expression_node<T>::node_type type() const exprtk_final
         {
            return expression_node<T>::e_switch;
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::collect(arg_list_, node_delete_list);
         }

         std::size_t node_depth() const exprtk_final
         {
            return expression_node<T>::ndb_t::compute_node_depth(arg_list_);
         }

      protected:

         std::vector<branch_t> arg_list_;
      };

      template <typename T, typename Switch_N>
      class switch_n_node exprtk_final : public switch_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;

         template <typename Allocator,
                   template <typename, typename> class Sequence>
         explicit switch_n_node(const Sequence<expression_ptr,Allocator>& arg_list)
         : switch_node<T>(arg_list)
         {}

         inline T value() const
         {
            return Switch_N::process(switch_node<T>::arg_list_);
         }
      };

      template <typename T>
      class multi_switch_node exprtk_final : public expression_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef std::pair<expression_ptr,bool> branch_t;

         template <typename Allocator,
                   template <typename, typename> class Sequence>
         explicit multi_switch_node(const Sequence<expression_ptr,Allocator>& arg_list)
         {
            if (0 != (arg_list.size() & 1))
               return;

            arg_list_.resize(arg_list.size());

            for (std::size_t i = 0; i < arg_list.size(); ++i)
            {
               if (arg_list[i])
               {
                  construct_branch_pair(arg_list_[i], arg_list[i]);
               }
               else
               {
                  arg_list_.clear();
                  return;
               }
            }
         }

         inline T value() const
         {
            T result = T(0);

            if (arg_list_.empty())
            {
               return std::numeric_limits<T>::quiet_NaN();
            }

            const std::size_t upper_bound = (arg_list_.size() - 1);

            for (std::size_t i = 0; i < upper_bound; i += 2)
            {
               expression_ptr condition  = arg_list_[i    ].first;
               expression_ptr consequent = arg_list_[i + 1].first;

               if (is_true(condition))
               {
                  result = consequent->value();
               }
            }

            return result;
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_mswitch;
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::collect(arg_list_, node_delete_list);
         }

         std::size_t node_depth() const exprtk_final
         {
            return expression_node<T>::ndb_t::compute_node_depth(arg_list_);
         }

      private:

         std::vector<branch_t> arg_list_;
      };

      template <typename T>
      class ivariable
      {
      public:

         virtual ~ivariable()
         {}

         virtual T& ref() = 0;
         virtual const T& ref() const = 0;
      };

      template <typename T>
      class variable_node exprtk_final
                          : public expression_node<T>,
                            public ivariable      <T>
      {
      public:

         static T null_value;

         explicit variable_node()
         : value_(&null_value)
         {}

         explicit variable_node(T& v)
         : value_(&v)
         {}

         inline bool operator <(const variable_node<T>& v) const
         {
            return this < (&v);
         }

         inline T value() const
         {
            return (*value_);
         }

         inline T& ref()
         {
            return (*value_);
         }

         inline const T& ref() const
         {
            return (*value_);
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_variable;
         }

      private:

         T* value_;
      };

      template <typename T>
      T variable_node<T>::null_value = T(std::numeric_limits<T>::quiet_NaN());

      template <typename T>
      struct range_pack
      {
         typedef expression_node<T>*           expression_node_ptr;
         typedef std::pair<std::size_t,std::size_t> cached_range_t;

         range_pack()
         : n0_e (std::make_pair(false,expression_node_ptr(0)))
         , n1_e (std::make_pair(false,expression_node_ptr(0)))
         , n0_c (std::make_pair(false,0))
         , n1_c (std::make_pair(false,0))
         , cache(std::make_pair(0,0))
         {}

         void clear()
         {
            n0_e  = std::make_pair(false,expression_node_ptr(0));
            n1_e  = std::make_pair(false,expression_node_ptr(0));
            n0_c  = std::make_pair(false,0);
            n1_c  = std::make_pair(false,0);
            cache = std::make_pair(0,0);
         }

         void free()
         {
            if (n0_e.first && n0_e.second)
            {
               n0_e.first = false;

               if (
                    !is_variable_node(n0_e.second) &&
                    !is_string_node  (n0_e.second)
                  )
               {
                  destroy_node(n0_e.second);
               }
            }

            if (n1_e.first && n1_e.second)
            {
               n1_e.first = false;

               if (
                    !is_variable_node(n1_e.second) &&
                    !is_string_node  (n1_e.second)
                  )
               {
                  destroy_node(n1_e.second);
               }
            }
         }

         bool const_range() const
         {
           return ( n0_c.first &&  n1_c.first) &&
                  (!n0_e.first && !n1_e.first);
         }

         bool var_range() const
         {
           return ( n0_e.first &&  n1_e.first) &&
                  (!n0_c.first && !n1_c.first);
         }

         bool operator() (std::size_t& r0, std::size_t& r1,
                          const std::size_t& size = (std::numeric_limits<std::size_t>::max)()) const
         {
            if (n0_c.first)
               r0 = n0_c.second;
            else if (n0_e.first)
            {
               r0 = static_cast<std::size_t>(details::numeric::to_int64(n0_e.second->value()));
            }
            else
               return false;

            if (n1_c.first)
               r1 = n1_c.second;
            else if (n1_e.first)
            {
               r1 = static_cast<std::size_t>(details::numeric::to_int64(n1_e.second->value()));
            }
            else
               return false;

            if (
                 ((std::numeric_limits<std::size_t>::max)() != size) &&
                 ((std::numeric_limits<std::size_t>::max)() == r1  )
               )
            {
               r1 = size - 1;
            }

            cache.first  = r0;
            cache.second = r1;

            #ifndef exprtk_enable_runtime_checks
            return (r0 <= r1);
            #else
            return range_runtime_check(r0, r1, size);
            #endif
         }

         inline std::size_t const_size() const
         {
            return (n1_c.second - n0_c.second + 1);
         }

         inline std::size_t cache_size() const
         {
            return (cache.second - cache.first + 1);
         }

         std::pair<bool,expression_node_ptr> n0_e;
         std::pair<bool,expression_node_ptr> n1_e;
         std::pair<bool,std::size_t        > n0_c;
         std::pair<bool,std::size_t        > n1_c;
         mutable cached_range_t             cache;

         #ifdef exprtk_enable_runtime_checks
         bool range_runtime_check(const std::size_t r0,
                                  const std::size_t r1,
                                  const std::size_t size) const
         {
            if (r0 >= size)
            {
               throw std::runtime_error("range error: (r0 < 0) || (r0 >= size)");
               return false;
            }

            if (r1 >= size)
            {
               throw std::runtime_error("range error: (r1 < 0) || (r1 >= size)");
               return false;
            }

            return (r0 <= r1);
         }
         #endif
      };

      template <typename T>
      class string_base_node;

      template <typename T>
      struct range_data_type
      {
         typedef range_pack<T> range_t;
         typedef string_base_node<T>* strbase_ptr_t;

         range_data_type()
         : range(0)
         , data (0)
         , size (0)
         , type_size(0)
         , str_node (0)
         {}

         range_t*      range;
         void*         data;
         std::size_t   size;
         std::size_t   type_size;
         strbase_ptr_t str_node;
      };

      template <typename T> class vector_node;

      template <typename T>
      class vector_interface
      {
      public:

         typedef vector_node<T>*   vector_node_ptr;
         typedef vec_data_store<T>           vds_t;

         virtual ~vector_interface()
         {}

         virtual std::size_t size   () const = 0;

         virtual vector_node_ptr vec() const = 0;

         virtual vector_node_ptr vec()       = 0;

         virtual       vds_t& vds   ()       = 0;

         virtual const vds_t& vds   () const = 0;

         virtual bool side_effect   () const { return false; }
      };

      template <typename T>
      class vector_node exprtk_final
                        : public expression_node <T>,
                          public vector_interface<T>
      {
      public:

         typedef expression_node<T>*  expression_ptr;
         typedef vector_holder<T>    vector_holder_t;
         typedef vector_node<T>*     vector_node_ptr;
         typedef vec_data_store<T>             vds_t;

         explicit vector_node(vector_holder_t* vh)
         : vector_holder_(vh)
         , vds_((*vector_holder_).size(),(*vector_holder_)[0])
         {
            vector_holder_->set_ref(&vds_.ref());
         }

         vector_node(const vds_t& vds, vector_holder_t* vh)
         : vector_holder_(vh)
         , vds_(vds)
         {}

         inline T value() const
         {
            return vds().data()[0];
         }

         vector_node_ptr vec() const
         {
            return const_cast<vector_node_ptr>(this);
         }

         vector_node_ptr vec()
         {
            return this;
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_vector;
         }

         std::size_t size() const
         {
            return vds().size();
         }

         vds_t& vds()
         {
            return vds_;
         }

         const vds_t& vds() const
         {
            return vds_;
         }

         inline vector_holder_t& vec_holder()
         {
            return (*vector_holder_);
         }

      private:

         vector_holder_t* vector_holder_;
         vds_t                      vds_;
      };

      template <typename T>
      class vector_elem_node exprtk_final
                             : public expression_node<T>,
                               public ivariable      <T>
      {
      public:

         typedef expression_node<T>*            expression_ptr;
         typedef vector_holder<T>               vector_holder_t;
         typedef vector_holder_t*               vector_holder_ptr;
         typedef std::pair<expression_ptr,bool> branch_t;

         vector_elem_node(expression_ptr index, vector_holder_ptr vec_holder)
         : vec_holder_(vec_holder)
         , vector_base_((*vec_holder)[0])
         {
            construct_branch_pair(index_, index);
         }

         inline T value() const
         {
            return *(vector_base_ + static_cast<std::size_t>(details::numeric::to_int64(index_.first->value())));
         }

         inline T& ref()
         {
            return *(vector_base_ + static_cast<std::size_t>(details::numeric::to_int64(index_.first->value())));
         }

         inline const T& ref() const
         {
            return *(vector_base_ + static_cast<std::size_t>(details::numeric::to_int64(index_.first->value())));
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_vecelem;
         }

         inline vector_holder_t& vec_holder()
         {
            return (*vec_holder_);
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::collect(index_, node_delete_list);
         }

         std::size_t node_depth() const
         {
            return expression_node<T>::ndb_t::compute_node_depth(index_);
         }

      private:

         vector_holder_ptr vec_holder_;
         T* vector_base_;
         branch_t index_;
      };

      template <typename T>
      class rebasevector_elem_node exprtk_final
                                   : public expression_node<T>,
                                     public ivariable      <T>
      {
      public:

         typedef expression_node<T>*            expression_ptr;
         typedef vector_holder<T>               vector_holder_t;
         typedef vector_holder_t*               vector_holder_ptr;
         typedef vec_data_store<T>              vds_t;
         typedef std::pair<expression_ptr,bool> branch_t;

         rebasevector_elem_node(expression_ptr index, vector_holder_ptr vec_holder)
         : vector_holder_(vec_holder)
         , vds_((*vector_holder_).size(),(*vector_holder_)[0])
         {
            vector_holder_->set_ref(&vds_.ref());
            construct_branch_pair(index_, index);
         }

         inline T value() const
         {
            return *(vds_.data() + static_cast<std::size_t>(details::numeric::to_int64(index_.first->value())));
         }

         inline T& ref()
         {
            return *(vds_.data() + static_cast<std::size_t>(details::numeric::to_int64(index_.first->value())));
         }

         inline const T& ref() const
         {
            return *(vds_.data() + static_cast<std::size_t>(details::numeric::to_int64(index_.first->value())));
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_rbvecelem;
         }

         inline vector_holder_t& vec_holder()
         {
            return (*vector_holder_);
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::template collect(index_, node_delete_list);
         }

         std::size_t node_depth() const
         {
            return expression_node<T>::ndb_t::compute_node_depth(index_);
         }

      private:

         vector_holder_ptr vector_holder_;
         vds_t             vds_;
         branch_t          index_;
      };

      template <typename T>
      class rebasevector_celem_node exprtk_final
                                    : public expression_node<T>,
                                      public ivariable      <T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef vector_holder<T>    vector_holder_t;
         typedef vector_holder_t*    vector_holder_ptr;
         typedef vec_data_store<T>   vds_t;

         rebasevector_celem_node(const std::size_t index, vector_holder_ptr vec_holder)
         : index_(index)
         , vector_holder_(vec_holder)
         , vds_((*vector_holder_).size(),(*vector_holder_)[0])
         {
            vector_holder_->set_ref(&vds_.ref());
         }

         inline T value() const
         {
            return *(vds_.data() + index_);
         }

         inline T& ref()
         {
            return *(vds_.data() + index_);
         }

         inline const T& ref() const
         {
            return *(vds_.data() + index_);
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_rbveccelem;
         }

         inline vector_holder_t& vec_holder()
         {
            return (*vector_holder_);
         }

      private:

         const std::size_t index_;
         vector_holder_ptr vector_holder_;
         vds_t vds_;
      };

      template <typename T>
      class vector_assignment_node exprtk_final : public expression_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;

         vector_assignment_node(T* vector_base,
                                const std::size_t& size,
                                const std::vector<expression_ptr>& initialiser_list,
                                const bool single_value_initialse)
         : vector_base_(vector_base)
         , initialiser_list_(initialiser_list)
         , size_(size)
         , single_value_initialse_(single_value_initialse)
         {}

         inline T value() const
         {
            if (single_value_initialse_)
            {
               for (std::size_t i = 0; i < size_; ++i)
               {
                  *(vector_base_ + i) = initialiser_list_[0]->value();
               }
            }
            else
            {
               std::size_t il_size = initialiser_list_.size();

               for (std::size_t i = 0; i < il_size; ++i)
               {
                  *(vector_base_ + i) = initialiser_list_[i]->value();
               }

               if (il_size < size_)
               {
                  for (std::size_t i = il_size; i < size_; ++i)
                  {
                     *(vector_base_ + i) = T(0);
                  }
               }
            }

            return *(vector_base_);
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_vecdefass;
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::collect(initialiser_list_, node_delete_list);
         }

         std::size_t node_depth() const
         {
            return expression_node<T>::ndb_t::compute_node_depth(initialiser_list_);
         }

      private:

         vector_assignment_node<T>& operator=(const vector_assignment_node<T>&);

         mutable T* vector_base_;
         std::vector<expression_ptr> initialiser_list_;
         const std::size_t size_;
         const bool single_value_initialse_;
      };

      template <typename T>
      class swap_node exprtk_final : public expression_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef variable_node<T>*   variable_node_ptr;

         swap_node(variable_node_ptr var0, variable_node_ptr var1)
         : var0_(var0)
         , var1_(var1)
         {}

         inline T value() const
         {
            std::swap(var0_->ref(),var1_->ref());
            return var1_->ref();
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_swap;
         }

      private:

         variable_node_ptr var0_;
         variable_node_ptr var1_;
      };

      template <typename T>
      class swap_generic_node exprtk_final : public binary_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef ivariable<T>* ivariable_ptr;

         swap_generic_node(expression_ptr var0, expression_ptr var1)
         : binary_node<T>(details::e_swap, var0, var1)
         , var0_(dynamic_cast<ivariable_ptr>(var0))
         , var1_(dynamic_cast<ivariable_ptr>(var1))
         {}

         inline T value() const
         {
            std::swap(var0_->ref(),var1_->ref());
            return var1_->ref();
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_swap;
         }

      private:

         ivariable_ptr var0_;
         ivariable_ptr var1_;
      };

      template <typename T>
      class swap_vecvec_node exprtk_final
                             : public binary_node     <T>,
                               public vector_interface<T>
      {
      public:

         typedef expression_node<T>*  expression_ptr;
         typedef vector_node<T>*     vector_node_ptr;
         typedef vec_data_store<T>             vds_t;

         swap_vecvec_node(expression_ptr branch0,
                          expression_ptr branch1)
         : binary_node<T>(details::e_swap, branch0, branch1)
         , vec0_node_ptr_(0)
         , vec1_node_ptr_(0)
         , vec_size_     (0)
         , initialised_  (false)
         {
            if (is_ivector_node(binary_node<T>::branch_[0].first))
            {
               vector_interface<T>* vi = reinterpret_cast<vector_interface<T>*>(0);

               if (0 != (vi = dynamic_cast<vector_interface<T>*>(binary_node<T>::branch_[0].first)))
               {
                  vec0_node_ptr_ = vi->vec();
                  vds()          = vi->vds();
               }
            }

            if (is_ivector_node(binary_node<T>::branch_[1].first))
            {
               vector_interface<T>* vi = reinterpret_cast<vector_interface<T>*>(0);

               if (0 != (vi = dynamic_cast<vector_interface<T>*>(binary_node<T>::branch_[1].first)))
               {
                  vec1_node_ptr_ = vi->vec();
               }
            }

            if (vec0_node_ptr_ && vec1_node_ptr_)
            {
               vec_size_ = (std::min)(vec0_node_ptr_->vds().size(),
                                    vec1_node_ptr_->vds().size());

               initialised_ = true;
            }
         }

         inline T value() const
         {
            assert(binary_node<T>::branch_[0].first);
            assert(binary_node<T>::branch_[1].first);

            if (initialised_)
            {
               binary_node<T>::branch_[0].first->value();
               binary_node<T>::branch_[1].first->value();

               T* vec0 = vec0_node_ptr_->vds().data();
               T* vec1 = vec1_node_ptr_->vds().data();

               for (std::size_t i = 0; i < vec_size_; ++i)
               {
                  std::swap(vec0[i],vec1[i]);
               }

               return vec1_node_ptr_->value();
            }
            else
               return std::numeric_limits<T>::quiet_NaN();
         }

         vector_node_ptr vec() const
         {
            return vec0_node_ptr_;
         }

         vector_node_ptr vec()
         {
            return vec0_node_ptr_;
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_vecvecswap;
         }

         std::size_t size() const
         {
            return vec_size_;
         }

         vds_t& vds()
         {
            return vds_;
         }

         const vds_t& vds() const
         {
            return vds_;
         }

      private:

         vector_node<T>* vec0_node_ptr_;
         vector_node<T>* vec1_node_ptr_;
         std::size_t     vec_size_;
         bool            initialised_;
         vds_t           vds_;
      };

      #ifndef exprtk_disable_string_capabilities
      template <typename T>
      class stringvar_node exprtk_final
                           : public expression_node <T>,
                             public string_base_node<T>,
                             public range_interface <T>
      {
      public:

         typedef range_pack<T> range_t;

         static std::string null_value;

         explicit stringvar_node()
         : value_(&null_value)
         {}

         explicit stringvar_node(std::string& v)
         : value_(&v)
         {
            rp_.n0_c = std::make_pair<bool,std::size_t>(true,0);
            rp_.n1_c = std::make_pair<bool,std::size_t>(true,v.size() - 1);
            rp_.cache.first  = rp_.n0_c.second;
            rp_.cache.second = rp_.n1_c.second;
         }

         inline bool operator <(const stringvar_node<T>& v) const
         {
            return this < (&v);
         }

         inline T value() const
         {
            rp_.n1_c.second  = (*value_).size() - 1;
            rp_.cache.second = rp_.n1_c.second;

            return std::numeric_limits<T>::quiet_NaN();
         }

         std::string str() const
         {
            return ref();
         }

         char_cptr base() const
         {
            return &(*value_)[0];
         }

         std::size_t size() const
         {
            return ref().size();
         }

         std::string& ref()
         {
            return (*value_);
         }

         const std::string& ref() const
         {
            return (*value_);
         }

         range_t& range_ref()
         {
            return rp_;
         }

         const range_t& range_ref() const
         {
            return rp_;
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_stringvar;
         }

      private:

         std::string* value_;
         mutable range_t rp_;
      };

      template <typename T>
      std::string stringvar_node<T>::null_value = std::string("");

      template <typename T>
      class string_range_node exprtk_final
                              : public expression_node <T>,
                                public string_base_node<T>,
                                public range_interface <T>
      {
      public:

         typedef range_pack<T> range_t;

         static std::string null_value;

         explicit string_range_node(std::string& v, const range_t& rp)
         : value_(&v)
         , rp_(rp)
         {}

         virtual ~string_range_node()
         {
            rp_.free();
         }

         inline bool operator <(const string_range_node<T>& v) const
         {
            return this < (&v);
         }

         inline T value() const
         {
            return std::numeric_limits<T>::quiet_NaN();
         }

         inline std::string str() const
         {
            return (*value_);
         }

         char_cptr base() const
         {
            return &(*value_)[0];
         }

         std::size_t size() const
         {
            return ref().size();
         }

         inline range_t range() const
         {
            return rp_;
         }

         inline virtual std::string& ref()
         {
            return (*value_);
         }

         inline virtual const std::string& ref() const
         {
            return (*value_);
         }

         inline range_t& range_ref()
         {
            return rp_;
         }

         inline const range_t& range_ref() const
         {
            return rp_;
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_stringvarrng;
         }

      private:

         std::string* value_;
         range_t      rp_;
      };

      template <typename T>
      std::string string_range_node<T>::null_value = std::string("");

      template <typename T>
      class const_string_range_node exprtk_final
                                    : public expression_node <T>,
                                      public string_base_node<T>,
                                      public range_interface <T>
      {
      public:

         typedef range_pack<T> range_t;

         explicit const_string_range_node(const std::string& v, const range_t& rp)
         : value_(v)
         , rp_(rp)
         {}

        ~const_string_range_node()
         {
            rp_.free();
         }

         inline T value() const
         {
            return std::numeric_limits<T>::quiet_NaN();
         }

         std::string str() const
         {
            return value_;
         }

         char_cptr base() const
         {
            return value_.data();
         }

         std::size_t size() const
         {
            return value_.size();
         }

         range_t range() const
         {
            return rp_;
         }

         range_t& range_ref()
         {
            return rp_;
         }

         const range_t& range_ref() const
         {
            return rp_;
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_cstringvarrng;
         }

      private:

         const_string_range_node<T>& operator=(const const_string_range_node<T>&);

         const std::string value_;
         range_t rp_;
      };

      template <typename T>
      class generic_string_range_node exprtk_final
                                      : public expression_node <T>,
                                        public string_base_node<T>,
                                        public range_interface <T>
      {
      public:

         typedef expression_node <T>*      expression_ptr;
         typedef stringvar_node  <T>*     strvar_node_ptr;
         typedef string_base_node<T>*        str_base_ptr;
         typedef range_pack      <T>              range_t;
         typedef range_t*                       range_ptr;
         typedef range_interface<T>              irange_t;
         typedef irange_t*                     irange_ptr;
         typedef std::pair<expression_ptr,bool>  branch_t;


         generic_string_range_node(expression_ptr str_branch, const range_t& brange)
         : initialised_(false)
         , str_base_ptr_ (0)
         , str_range_ptr_(0)
         , base_range_(brange)
         {
            range_.n0_c = std::make_pair<bool,std::size_t>(true,0);
            range_.n1_c = std::make_pair<bool,std::size_t>(true,0);
            range_.cache.first  = range_.n0_c.second;
            range_.cache.second = range_.n1_c.second;

            construct_branch_pair(branch_, str_branch);

            if (is_generally_string_node(branch_.first))
            {
               str_base_ptr_ = dynamic_cast<str_base_ptr>(branch_.first);

               if (0 == str_base_ptr_)
                  return;

               str_range_ptr_ = dynamic_cast<irange_ptr>(branch_.first);

               if (0 == str_range_ptr_)
                  return;
            }

            initialised_ = (str_base_ptr_ && str_range_ptr_);
         }

        ~generic_string_range_node()
         {
            base_range_.free();
         }

         inline T value() const
         {
            if (initialised_)
            {
               assert(branch_.first);

               branch_.first->value();

               std::size_t str_r0 = 0;
               std::size_t str_r1 = 0;

               std::size_t r0 = 0;
               std::size_t r1 = 0;

               const range_t& range = str_range_ptr_->range_ref();

               const std::size_t base_str_size = str_base_ptr_->size();

               if (
                    range      (str_r0, str_r1, base_str_size) &&
                    base_range_(    r0,     r1, base_str_size - str_r0)
                  )
               {
                  const std::size_t size = (r1 - r0) + 1;

                  range_.n1_c.second  = size - 1;
                  range_.cache.second = range_.n1_c.second;

                  value_.assign(str_base_ptr_->base() + str_r0 + r0, size);
               }
            }

            return std::numeric_limits<T>::quiet_NaN();
         }

         std::string str() const
         {
            return value_;
         }

         char_cptr base() const
         {
            return &value_[0];
         }

         std::size_t size() const
         {
            return value_.size();
         }

         range_t& range_ref()
         {
            return range_;
         }

         const range_t& range_ref() const
         {
            return range_;
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_strgenrange;
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::collect(branch_, node_delete_list);
         }

         std::size_t node_depth() const
         {
            return expression_node<T>::ndb_t::compute_node_depth(branch_);
         }

      private:

         bool                initialised_;
         branch_t                 branch_;
         str_base_ptr       str_base_ptr_;
         irange_ptr        str_range_ptr_;
         mutable range_t      base_range_;
         mutable range_t           range_;
         mutable std::string       value_;
      };

      template <typename T>
      class string_concat_node exprtk_final
                               : public binary_node     <T>,
                                 public string_base_node<T>,
                                 public range_interface <T>
      {
      public:

         typedef expression_node <T>*  expression_ptr;
         typedef string_base_node<T>*    str_base_ptr;
         typedef range_pack      <T>          range_t;
         typedef range_t*                   range_ptr;
         typedef range_interface<T>          irange_t;
         typedef irange_t*                 irange_ptr;

         string_concat_node(const operator_type& opr,
                            expression_ptr branch0,
                            expression_ptr branch1)
         : binary_node<T>(opr, branch0, branch1)
         ,  initialised_(false)
         ,  str0_base_ptr_ (0)
         ,  str1_base_ptr_ (0)
         ,  str0_range_ptr_(0)
         ,  str1_range_ptr_(0)
         {
            range_.n0_c = std::make_pair<bool,std::size_t>(true,0);
            range_.n1_c = std::make_pair<bool,std::size_t>(true,0);

            range_.cache.first  = range_.n0_c.second;
            range_.cache.second = range_.n1_c.second;

            if (is_generally_string_node(binary_node<T>::branch_[0].first))
            {
               str0_base_ptr_ = dynamic_cast<str_base_ptr>(binary_node<T>::branch_[0].first);

               if (0 == str0_base_ptr_)
                  return;

               str0_range_ptr_ = dynamic_cast<irange_ptr>(binary_node<T>::branch_[0].first);

               if (0 == str0_range_ptr_)
                  return;
            }

            if (is_generally_string_node(binary_node<T>::branch_[1].first))
            {
               str1_base_ptr_ = dynamic_cast<str_base_ptr>(binary_node<T>::branch_[1].first);

               if (0 == str1_base_ptr_)
                  return;

               str1_range_ptr_ = dynamic_cast<irange_ptr>(binary_node<T>::branch_[1].first);

               if (0 == str1_range_ptr_)
                  return;
            }

            initialised_ = str0_base_ptr_  &&
                           str1_base_ptr_  &&
                           str0_range_ptr_ &&
                           str1_range_ptr_ ;
         }

         inline T value() const
         {
            if (initialised_)
            {
               assert(binary_node<T>::branch_[0].first);
               assert(binary_node<T>::branch_[1].first);

               binary_node<T>::branch_[0].first->value();
               binary_node<T>::branch_[1].first->value();

               std::size_t str0_r0 = 0;
               std::size_t str0_r1 = 0;

               std::size_t str1_r0 = 0;
               std::size_t str1_r1 = 0;

               const range_t& range0 = str0_range_ptr_->range_ref();
               const range_t& range1 = str1_range_ptr_->range_ref();

               if (
                    range0(str0_r0, str0_r1, str0_base_ptr_->size()) &&
                    range1(str1_r0, str1_r1, str1_base_ptr_->size())
                  )
               {
                  const std::size_t size0 = (str0_r1 - str0_r0) + 1;
                  const std::size_t size1 = (str1_r1 - str1_r0) + 1;

                  value_.assign(str0_base_ptr_->base() + str0_r0, size0);
                  value_.append(str1_base_ptr_->base() + str1_r0, size1);

                  range_.n1_c.second  = value_.size() - 1;
                  range_.cache.second = range_.n1_c.second;
               }
            }

            return std::numeric_limits<T>::quiet_NaN();
         }

         std::string str() const
         {
            return value_;
         }

         char_cptr base() const
         {
            return &value_[0];
         }

         std::size_t size() const
         {
            return value_.size();
         }

         range_t& range_ref()
         {
            return range_;
         }

         const range_t& range_ref() const
         {
            return range_;
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_strconcat;
         }

      private:

         bool initialised_;
         str_base_ptr str0_base_ptr_;
         str_base_ptr str1_base_ptr_;
         irange_ptr   str0_range_ptr_;
         irange_ptr   str1_range_ptr_;
         mutable range_t     range_;
         mutable std::string value_;
      };

      template <typename T>
      class swap_string_node exprtk_final
                             : public binary_node     <T>,
                               public string_base_node<T>,
                               public range_interface <T>
      {
      public:

         typedef expression_node <T>*  expression_ptr;
         typedef stringvar_node  <T>* strvar_node_ptr;
         typedef string_base_node<T>*    str_base_ptr;
         typedef range_pack      <T>          range_t;
         typedef range_t*                   range_ptr;
         typedef range_interface<T>          irange_t;
         typedef irange_t*                 irange_ptr;

         swap_string_node(expression_ptr branch0, expression_ptr branch1)
         : binary_node<T>(details::e_swap, branch0, branch1),
           initialised_(false),
           str0_node_ptr_(0),
           str1_node_ptr_(0)
         {
            if (is_string_node(binary_node<T>::branch_[0].first))
            {
               str0_node_ptr_ = static_cast<strvar_node_ptr>(binary_node<T>::branch_[0].first);
            }

            if (is_string_node(binary_node<T>::branch_[1].first))
            {
               str1_node_ptr_ = static_cast<strvar_node_ptr>(binary_node<T>::branch_[1].first);
            }

            initialised_ = (str0_node_ptr_ && str1_node_ptr_);
         }

         inline T value() const
         {
            if (initialised_)
            {
               assert(binary_node<T>::branch_[0].first);
               assert(binary_node<T>::branch_[1].first);

               binary_node<T>::branch_[0].first->value();
               binary_node<T>::branch_[1].first->value();

               std::swap(str0_node_ptr_->ref(), str1_node_ptr_->ref());
            }

            return std::numeric_limits<T>::quiet_NaN();
         }

         std::string str() const
         {
            return str0_node_ptr_->str();
         }

         char_cptr base() const
         {
           return str0_node_ptr_->base();
         }

         std::size_t size() const
         {
            return str0_node_ptr_->size();
         }

         range_t& range_ref()
         {
            return str0_node_ptr_->range_ref();
         }

         const range_t& range_ref() const
         {
            return str0_node_ptr_->range_ref();
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_strswap;
         }

      private:

         bool initialised_;
         strvar_node_ptr str0_node_ptr_;
         strvar_node_ptr str1_node_ptr_;
      };

      template <typename T>
      class swap_genstrings_node exprtk_final : public binary_node<T>
      {
      public:

         typedef expression_node <T>* expression_ptr;
         typedef string_base_node<T>*   str_base_ptr;
         typedef range_pack      <T>         range_t;
         typedef range_t*                  range_ptr;
         typedef range_interface<T>         irange_t;
         typedef irange_t*                irange_ptr;

         swap_genstrings_node(expression_ptr branch0,
                              expression_ptr branch1)
         : binary_node<T>(details::e_default, branch0, branch1)
         , str0_base_ptr_ (0)
         , str1_base_ptr_ (0)
         , str0_range_ptr_(0)
         , str1_range_ptr_(0)
         , initialised_(false)
         {
            if (is_generally_string_node(binary_node<T>::branch_[0].first))
            {
               str0_base_ptr_ = dynamic_cast<str_base_ptr>(binary_node<T>::branch_[0].first);

               if (0 == str0_base_ptr_)
                  return;

               irange_ptr range = dynamic_cast<irange_ptr>(binary_node<T>::branch_[0].first);

               if (0 == range)
                  return;

               str0_range_ptr_ = &(range->range_ref());
            }

            if (is_generally_string_node(binary_node<T>::branch_[1].first))
            {
               str1_base_ptr_ = dynamic_cast<str_base_ptr>(binary_node<T>::branch_[1].first);

               if (0 == str1_base_ptr_)
                  return;

               irange_ptr range = dynamic_cast<irange_ptr>(binary_node<T>::branch_[1].first);

               if (0 == range)
                  return;

               str1_range_ptr_ = &(range->range_ref());
            }

            initialised_ = str0_base_ptr_  &&
                           str1_base_ptr_  &&
                           str0_range_ptr_ &&
                           str1_range_ptr_ ;
         }

         inline T value() const
         {
            if (initialised_)
            {
               assert(binary_node<T>::branch_[0].first);
               assert(binary_node<T>::branch_[1].first);

               binary_node<T>::branch_[0].first->value();
               binary_node<T>::branch_[1].first->value();

               std::size_t str0_r0 = 0;
               std::size_t str0_r1 = 0;

               std::size_t str1_r0 = 0;
               std::size_t str1_r1 = 0;

               const range_t& range0 = (*str0_range_ptr_);
               const range_t& range1 = (*str1_range_ptr_);

               if (
                    range0(str0_r0, str0_r1, str0_base_ptr_->size()) &&
                    range1(str1_r0, str1_r1, str1_base_ptr_->size())
                  )
               {
                  const std::size_t size0    = range0.cache_size();
                  const std::size_t size1    = range1.cache_size();
                  const std::size_t max_size = (std::min)(size0,size1);

                  char_ptr s0 = const_cast<char_ptr>(str0_base_ptr_->base() + str0_r0);
                  char_ptr s1 = const_cast<char_ptr>(str1_base_ptr_->base() + str1_r0);

                  loop_unroll::details lud(max_size);
                  char_cptr upper_bound = s0 + lud.upper_bound;

                  while (s0 < upper_bound)
                  {
                     #define exprtk_loop(N)   \
                     std::swap(s0[N], s1[N]); \

                     exprtk_loop( 0) exprtk_loop( 1)
                     exprtk_loop( 2) exprtk_loop( 3)
                     #ifndef exprtk_disable_superscalar_unroll
                     exprtk_loop( 4) exprtk_loop( 5)
                     exprtk_loop( 6) exprtk_loop( 7)
                     exprtk_loop( 8) exprtk_loop( 9)
                     exprtk_loop(10) exprtk_loop(11)
                     exprtk_loop(12) exprtk_loop(13)
                     exprtk_loop(14) exprtk_loop(15)
                     #endif

                     s0 += lud.batch_size;
                     s1 += lud.batch_size;
                  }

                  int i = 0;

                  exprtk_disable_fallthrough_begin
                  switch (lud.remainder)
                  {
                     #define case_stmt(N)                       \
                     case N : { std::swap(s0[i], s1[i]); ++i; } \

                     #ifndef exprtk_disable_superscalar_unroll
                     case_stmt(15) case_stmt(14)
                     case_stmt(13) case_stmt(12)
                     case_stmt(11) case_stmt(10)
                     case_stmt( 9) case_stmt( 8)
                     case_stmt( 7) case_stmt( 6)
                     case_stmt( 5) case_stmt( 4)
                     #endif
                     case_stmt( 3) case_stmt( 2)
                     case_stmt( 1)
                  }
                  exprtk_disable_fallthrough_end

                  #undef exprtk_loop
                  #undef case_stmt
               }
            }

            return std::numeric_limits<T>::quiet_NaN();
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_strswap;
         }

      private:

         swap_genstrings_node(swap_genstrings_node<T>&);
         swap_genstrings_node<T>& operator=(swap_genstrings_node<T>&);

         str_base_ptr str0_base_ptr_;
         str_base_ptr str1_base_ptr_;
         range_ptr    str0_range_ptr_;
         range_ptr    str1_range_ptr_;
         bool         initialised_;
      };

      template <typename T>
      class stringvar_size_node exprtk_final : public expression_node<T>
      {
      public:

         static std::string null_value;

         explicit stringvar_size_node()
         : value_(&null_value)
         {}

         explicit stringvar_size_node(std::string& v)
         : value_(&v)
         {}

         inline T value() const
         {
            return T((*value_).size());
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_stringvarsize;
         }

      private:

         std::string* value_;
      };

      template <typename T>
      std::string stringvar_size_node<T>::null_value = std::string("");

      template <typename T>
      class string_size_node exprtk_final : public expression_node<T>
      {
      public:

         typedef expression_node <T>*      expression_ptr;
         typedef string_base_node<T>*        str_base_ptr;
         typedef std::pair<expression_ptr,bool>  branch_t;


         explicit string_size_node(expression_ptr branch)
         : str_base_ptr_(0)
         {
            construct_branch_pair(branch_, branch);

            if (is_generally_string_node(branch_.first))
            {
               str_base_ptr_ = dynamic_cast<str_base_ptr>(branch_.first);

               if (0 == str_base_ptr_)
                  return;
            }
         }

         inline T value() const
         {
            T result = std::numeric_limits<T>::quiet_NaN();

            if (str_base_ptr_)
            {
               branch_.first->value();
               result = T(str_base_ptr_->size());
            }

            return result;
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_stringsize;
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::collect(branch_, node_delete_list);
         }

         std::size_t node_depth() const
         {
            return expression_node<T>::ndb_t::compute_node_depth(branch_);
         }

      private:

         branch_t           branch_;
         str_base_ptr str_base_ptr_;
      };

      struct asn_assignment
      {
         static inline void execute(std::string& s, char_cptr data, const std::size_t size)
         { s.assign(data,size); }
      };

      struct asn_addassignment
      {
         static inline void execute(std::string& s, char_cptr data, const std::size_t size)
         { s.append(data,size); }
      };

      template <typename T, typename AssignmentProcess = asn_assignment>
      class assignment_string_node exprtk_final
                                   : public binary_node     <T>,
                                     public string_base_node<T>,
                                     public range_interface <T>
      {
      public:

         typedef expression_node <T>*  expression_ptr;
         typedef stringvar_node  <T>* strvar_node_ptr;
         typedef string_base_node<T>*    str_base_ptr;
         typedef range_pack      <T>          range_t;
         typedef range_t*                   range_ptr;
         typedef range_interface<T>          irange_t;
         typedef irange_t*                 irange_ptr;

         assignment_string_node(const operator_type& opr,
                                expression_ptr branch0,
                                expression_ptr branch1)
         : binary_node<T>(opr, branch0, branch1)
         , initialised_(false)
         , str0_base_ptr_ (0)
         , str1_base_ptr_ (0)
         , str0_node_ptr_ (0)
         , str1_range_ptr_(0)
         {
            if (is_string_node(binary_node<T>::branch_[0].first))
            {
               str0_node_ptr_ = static_cast<strvar_node_ptr>(binary_node<T>::branch_[0].first);

               str0_base_ptr_ = dynamic_cast<str_base_ptr>(binary_node<T>::branch_[0].first);
            }

            if (is_generally_string_node(binary_node<T>::branch_[1].first))
            {
               str1_base_ptr_ = dynamic_cast<str_base_ptr>(binary_node<T>::branch_[1].first);

               if (0 == str1_base_ptr_)
                  return;

               irange_ptr range = dynamic_cast<irange_ptr>(binary_node<T>::branch_[1].first);

               if (0 == range)
                  return;

               str1_range_ptr_ = &(range->range_ref());
            }

            initialised_ = str0_base_ptr_  &&
                           str1_base_ptr_  &&
                           str0_node_ptr_  &&
                           str1_range_ptr_ ;
         }

         inline T value() const
         {
            if (initialised_)
            {
               assert(binary_node<T>::branch_[0].first);
               assert(binary_node<T>::branch_[1].first);

               binary_node<T>::branch_[1].first->value();

               std::size_t r0 = 0;
               std::size_t r1 = 0;

               const range_t& range = (*str1_range_ptr_);

               if (range(r0, r1, str1_base_ptr_->size()))
               {
                  AssignmentProcess::execute(str0_node_ptr_->ref(),
                                             str1_base_ptr_->base() + r0,
                                             (r1 - r0) + 1);

                  binary_node<T>::branch_[0].first->value();
               }
            }

            return std::numeric_limits<T>::quiet_NaN();
         }

         std::string str() const
         {
            return str0_node_ptr_->str();
         }

         char_cptr base() const
         {
           return str0_node_ptr_->base();
         }

         std::size_t size() const
         {
            return str0_node_ptr_->size();
         }

         range_t& range_ref()
         {
            return str0_node_ptr_->range_ref();
         }

         const range_t& range_ref() const
         {
            return str0_node_ptr_->range_ref();
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_strass;
         }

      private:

         bool            initialised_;
         str_base_ptr    str0_base_ptr_;
         str_base_ptr    str1_base_ptr_;
         strvar_node_ptr str0_node_ptr_;
         range_ptr       str1_range_ptr_;
      };

      template <typename T, typename AssignmentProcess = asn_assignment>
      class assignment_string_range_node exprtk_final
                                         : public binary_node     <T>,
                                           public string_base_node<T>,
                                           public range_interface <T>
      {
      public:

         typedef expression_node  <T>*   expression_ptr;
         typedef stringvar_node   <T>*  strvar_node_ptr;
         typedef string_range_node<T>* str_rng_node_ptr;
         typedef string_base_node <T>*     str_base_ptr;
         typedef range_pack       <T>           range_t;
         typedef range_t*                     range_ptr;
         typedef range_interface<T>            irange_t;
         typedef irange_t*                   irange_ptr;

         assignment_string_range_node(const operator_type& opr,
                                      expression_ptr branch0,
                                      expression_ptr branch1)
         : binary_node<T>(opr, branch0, branch1)
         , initialised_(false)
         , str0_base_ptr_    (0)
         , str1_base_ptr_    (0)
         , str0_rng_node_ptr_(0)
         , str0_range_ptr_   (0)
         , str1_range_ptr_   (0)
         {
            if (is_string_range_node(binary_node<T>::branch_[0].first))
            {
               str0_rng_node_ptr_ = static_cast<str_rng_node_ptr>(binary_node<T>::branch_[0].first);

               str0_base_ptr_ = dynamic_cast<str_base_ptr>(binary_node<T>::branch_[0].first);

               irange_ptr range = dynamic_cast<irange_ptr>(binary_node<T>::branch_[0].first);

               if (0 == range)
                  return;

               str0_range_ptr_ = &(range->range_ref());
            }

            if (is_generally_string_node(binary_node<T>::branch_[1].first))
            {
               str1_base_ptr_ = dynamic_cast<str_base_ptr>(binary_node<T>::branch_[1].first);

               if (0 == str1_base_ptr_)
                  return;

               irange_ptr range = dynamic_cast<irange_ptr>(binary_node<T>::branch_[1].first);

               if (0 == range)
                  return;

               str1_range_ptr_ = &(range->range_ref());
            }

            initialised_ = str0_base_ptr_     &&
                           str1_base_ptr_     &&
                           str0_rng_node_ptr_ &&
                           str0_range_ptr_    &&
                           str1_range_ptr_    ;
         }

         inline T value() const
         {
            if (initialised_)
            {
               assert(binary_node<T>::branch_[0].first);
               assert(binary_node<T>::branch_[1].first);

               binary_node<T>::branch_[0].first->value();
               binary_node<T>::branch_[1].first->value();

               std::size_t s0_r0 = 0;
               std::size_t s0_r1 = 0;

               std::size_t s1_r0 = 0;
               std::size_t s1_r1 = 0;

               const range_t& range0 = (*str0_range_ptr_);
               const range_t& range1 = (*str1_range_ptr_);

               if (
                    range0(s0_r0, s0_r1, str0_base_ptr_->size()) &&
                    range1(s1_r0, s1_r1, str1_base_ptr_->size())
                  )
               {
                  const std::size_t size = (std::min)((s0_r1 - s0_r0), (s1_r1 - s1_r0)) + 1;

                  std::copy(str1_base_ptr_->base() + s1_r0,
                            str1_base_ptr_->base() + s1_r0 + size,
                            const_cast<char_ptr>(base() + s0_r0));
               }
            }

            return std::numeric_limits<T>::quiet_NaN();
         }

         std::string str() const
         {
            return str0_base_ptr_->str();
         }

         char_cptr base() const
         {
            return str0_base_ptr_->base();
         }

         std::size_t size() const
         {
            return str0_base_ptr_->size();
         }

         range_t& range_ref()
         {
            return str0_rng_node_ptr_->range_ref();
         }

         const range_t& range_ref() const
         {
            return str0_rng_node_ptr_->range_ref();
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_strass;
         }

      private:

         bool             initialised_;
         str_base_ptr     str0_base_ptr_;
         str_base_ptr     str1_base_ptr_;
         str_rng_node_ptr str0_rng_node_ptr_;
         range_ptr        str0_range_ptr_;
         range_ptr        str1_range_ptr_;
      };

      template <typename T>
      class conditional_string_node exprtk_final
                                    : public trinary_node    <T>,
                                      public string_base_node<T>,
                                      public range_interface <T>
      {
      public:

         typedef expression_node <T>* expression_ptr;
         typedef string_base_node<T>*   str_base_ptr;
         typedef range_pack      <T>         range_t;
         typedef range_t*                  range_ptr;
         typedef range_interface<T>         irange_t;
         typedef irange_t*                irange_ptr;

         conditional_string_node(expression_ptr condition,
                                 expression_ptr consequent,
                                 expression_ptr alternative)
         : trinary_node<T>(details::e_default,consequent,alternative,condition)
         , initialised_(false)
         , str0_base_ptr_ (0)
         , str1_base_ptr_ (0)
         , str0_range_ptr_(0)
         , str1_range_ptr_(0)
         , condition_  (condition  )
         , consequent_ (consequent )
         , alternative_(alternative)
         {
            range_.n0_c = std::make_pair<bool,std::size_t>(true,0);
            range_.n1_c = std::make_pair<bool,std::size_t>(true,0);

            range_.cache.first  = range_.n0_c.second;
            range_.cache.second = range_.n1_c.second;

            if (is_generally_string_node(trinary_node<T>::branch_[0].first))
            {
               str0_base_ptr_ = dynamic_cast<str_base_ptr>(trinary_node<T>::branch_[0].first);

               if (0 == str0_base_ptr_)
                  return;

               str0_range_ptr_ = dynamic_cast<irange_ptr>(trinary_node<T>::branch_[0].first);

               if (0 == str0_range_ptr_)
                  return;
            }

            if (is_generally_string_node(trinary_node<T>::branch_[1].first))
            {
               str1_base_ptr_ = dynamic_cast<str_base_ptr>(trinary_node<T>::branch_[1].first);

               if (0 == str1_base_ptr_)
                  return;

               str1_range_ptr_ = dynamic_cast<irange_ptr>(trinary_node<T>::branch_[1].first);

               if (0 == str1_range_ptr_)
                  return;
            }

            initialised_ = str0_base_ptr_  &&
                           str1_base_ptr_  &&
                           str0_range_ptr_ &&
                           str1_range_ptr_ ;

         }

         inline T value() const
         {
            if (initialised_)
            {
               assert(condition_  );
               assert(consequent_ );
               assert(alternative_);

               std::size_t r0 = 0;
               std::size_t r1 = 0;

               if (is_true(condition_))
               {
                  consequent_->value();

                  const range_t& range = str0_range_ptr_->range_ref();

                  if (range(r0, r1, str0_base_ptr_->size()))
                  {
                     const std::size_t size = (r1 - r0) + 1;

                     value_.assign(str0_base_ptr_->base() + r0, size);

                     range_.n1_c.second  = value_.size() - 1;
                     range_.cache.second = range_.n1_c.second;

                     return T(1);
                  }
               }
               else
               {
                  alternative_->value();

                  const range_t& range = str1_range_ptr_->range_ref();

                  if (range(r0, r1, str1_base_ptr_->size()))
                  {
                     const std::size_t size = (r1 - r0) + 1;

                     value_.assign(str1_base_ptr_->base() + r0, size);

                     range_.n1_c.second  = value_.size() - 1;
                     range_.cache.second = range_.n1_c.second;

                     return T(0);
                  }
               }
            }

            return std::numeric_limits<T>::quiet_NaN();
         }

         std::string str() const
         {
            return value_;
         }

         char_cptr base() const
         {
            return &value_[0];
         }

         std::size_t size() const
         {
            return value_.size();
         }

         range_t& range_ref()
         {
            return range_;
         }

         const range_t& range_ref() const
         {
            return range_;
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_strcondition;
         }

      private:

         bool initialised_;
         str_base_ptr str0_base_ptr_;
         str_base_ptr str1_base_ptr_;
         irange_ptr   str0_range_ptr_;
         irange_ptr   str1_range_ptr_;
         mutable range_t     range_;
         mutable std::string value_;

         expression_ptr condition_;
         expression_ptr consequent_;
         expression_ptr alternative_;
      };

      template <typename T>
      class cons_conditional_str_node exprtk_final
                                      : public binary_node     <T>,
                                        public string_base_node<T>,
                                        public range_interface <T>
      {
      public:

         typedef expression_node <T>* expression_ptr;
         typedef string_base_node<T>*   str_base_ptr;
         typedef range_pack      <T>         range_t;
         typedef range_t*                  range_ptr;
         typedef range_interface<T>         irange_t;
         typedef irange_t*                irange_ptr;

         cons_conditional_str_node(expression_ptr condition,
                                   expression_ptr consequent)
         : binary_node<T>(details::e_default, consequent, condition)
         , initialised_(false)
         , str0_base_ptr_ (0)
         , str0_range_ptr_(0)
         , condition_ (condition )
         , consequent_(consequent)
         {
            range_.n0_c = std::make_pair<bool,std::size_t>(true,0);
            range_.n1_c = std::make_pair<bool,std::size_t>(true,0);

            range_.cache.first  = range_.n0_c.second;
            range_.cache.second = range_.n1_c.second;

            if (is_generally_string_node(binary_node<T>::branch_[0].first))
            {
               str0_base_ptr_ = dynamic_cast<str_base_ptr>(binary_node<T>::branch_[0].first);

               if (0 == str0_base_ptr_)
                  return;

               str0_range_ptr_ = dynamic_cast<irange_ptr>(binary_node<T>::branch_[0].first);

               if (0 == str0_range_ptr_)
                  return;
            }

            initialised_ = str0_base_ptr_ && str0_range_ptr_ ;
         }

         inline T value() const
         {
            if (initialised_)
            {
               assert(condition_ );
               assert(consequent_);

               if (is_true(condition_))
               {
                  consequent_->value();

                  const range_t& range = str0_range_ptr_->range_ref();

                  std::size_t r0 = 0;
                  std::size_t r1 = 0;

                  if (range(r0, r1, str0_base_ptr_->size()))
                  {
                     const std::size_t size = (r1 - r0) + 1;

                     value_.assign(str0_base_ptr_->base() + r0, size);

                     range_.n1_c.second  = value_.size() - 1;
                     range_.cache.second = range_.n1_c.second;

                     return T(1);
                  }
               }
            }

            return std::numeric_limits<T>::quiet_NaN();
         }

         std::string str() const
         {
            return value_;
         }

         char_cptr base() const
         {
            return &value_[0];
         }

         std::size_t size() const
         {
            return value_.size();
         }

         range_t& range_ref()
         {
            return range_;
         }

         const range_t& range_ref() const
         {
            return range_;
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_strccondition;
         }

      private:

         bool initialised_;
         str_base_ptr str0_base_ptr_;
         irange_ptr   str0_range_ptr_;
         mutable range_t     range_;
         mutable std::string value_;

         expression_ptr condition_;
         expression_ptr consequent_;
      };

      template <typename T, typename VarArgFunction>
      class str_vararg_node exprtk_final
                            : public expression_node <T>,
                              public string_base_node<T>,
                              public range_interface <T>
      {
      public:

         typedef expression_node <T>*     expression_ptr;
         typedef string_base_node<T>*       str_base_ptr;
         typedef range_pack      <T>             range_t;
         typedef range_t*                      range_ptr;
         typedef range_interface<T>             irange_t;
         typedef irange_t*                    irange_ptr;
         typedef std::pair<expression_ptr,bool> branch_t;

         template <typename Allocator,
                   template <typename, typename> class Sequence>
         explicit str_vararg_node(const Sequence<expression_ptr,Allocator>& arg_list)
         : initialised_(false)
         , str_base_ptr_ (0)
         , str_range_ptr_(0)
         {
            construct_branch_pair(final_node_, const_cast<expression_ptr>(arg_list.back()));

            if (0 == final_node_.first)
               return;
            else if (!is_generally_string_node(final_node_.first))
               return;

            str_base_ptr_ = dynamic_cast<str_base_ptr>(final_node_.first);

            if (0 == str_base_ptr_)
               return;

            str_range_ptr_ = dynamic_cast<irange_ptr>(final_node_.first);

            if (0 == str_range_ptr_)
               return;

            initialised_ = str_base_ptr_  && str_range_ptr_;

            if (arg_list.size() > 1)
            {
               const std::size_t arg_list_size = arg_list.size() - 1;

               arg_list_.resize(arg_list_size);

               for (std::size_t i = 0; i < arg_list_size; ++i)
               {
                  if (arg_list[i])
                  {
                     construct_branch_pair(arg_list_[i], arg_list[i]);
                  }
                  else
                  {
                     arg_list_.clear();
                     return;
                  }
               }
            }
         }

         inline T value() const
         {
            if (!arg_list_.empty())
            {
               VarArgFunction::process(arg_list_);
            }

            final_node_.first->value();

            return std::numeric_limits<T>::quiet_NaN();
         }

         std::string str() const
         {
            return str_base_ptr_->str();
         }

         char_cptr base() const
         {
            return str_base_ptr_->base();
         }

         std::size_t size() const
         {
            return str_base_ptr_->size();
         }

         range_t& range_ref()
         {
            return str_range_ptr_->range_ref();
         }

         const range_t& range_ref() const
         {
            return str_range_ptr_->range_ref();
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_stringvararg;
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::collect(final_node_ , node_delete_list);
            expression_node<T>::ndb_t::collect(arg_list_   , node_delete_list);
         }

         std::size_t node_depth() const
         {
            return (std::max)(
               expression_node<T>::ndb_t::compute_node_depth(final_node_),
               expression_node<T>::ndb_t::compute_node_depth(arg_list_  ));
         }

      private:

         bool                  initialised_;
         branch_t              final_node_;
         str_base_ptr          str_base_ptr_;
         irange_ptr            str_range_ptr_;
         std::vector<branch_t> arg_list_;
      };
      #endif

      template <typename T, std::size_t N>
      inline T axn(const T a, const T x)
      {
         // a*x^n
         return a * exprtk::details::numeric::fast_exp<T,N>::result(x);
      }

      template <typename T, std::size_t N>
      inline T axnb(const T a, const T x, const T b)
      {
         // a*x^n+b
         return a * exprtk::details::numeric::fast_exp<T,N>::result(x) + b;
      }

      template <typename T>
      struct sf_base
      {
         typedef typename details::functor_t<T>::Type Type;
         typedef typename details::functor_t<T> functor_t;
         typedef typename functor_t::qfunc_t quaternary_functor_t;
         typedef typename functor_t::tfunc_t    trinary_functor_t;
         typedef typename functor_t::bfunc_t     binary_functor_t;
         typedef typename functor_t::ufunc_t      unary_functor_t;
      };

      #define define_sfop3(NN,OP0,OP1)                   \
      template <typename T>                              \
      struct sf##NN##_op : public sf_base<T>             \
      {                                                  \
         typedef typename sf_base<T>::Type const Type;   \
         static inline T process(Type x, Type y, Type z) \
         {                                               \
            return (OP0);                                \
         }                                               \
         static inline std::string id()                  \
         {                                               \
            return (OP1);                                \
         }                                               \
      };                                                 \

      define_sfop3(00,(x + y) / z       ,"(t+t)/t")
      define_sfop3(01,(x + y) * z       ,"(t+t)*t")
      define_sfop3(02,(x + y) - z       ,"(t+t)-t")
      define_sfop3(03,(x + y) + z       ,"(t+t)+t")
      define_sfop3(04,(x - y) + z       ,"(t-t)+t")
      define_sfop3(05,(x - y) / z       ,"(t-t)/t")
      define_sfop3(06,(x - y) * z       ,"(t-t)*t")
      define_sfop3(07,(x * y) + z       ,"(t*t)+t")
      define_sfop3(08,(x * y) - z       ,"(t*t)-t")
      define_sfop3(09,(x * y) / z       ,"(t*t)/t")
      define_sfop3(10,(x * y) * z       ,"(t*t)*t")
      define_sfop3(11,(x / y) + z       ,"(t/t)+t")
      define_sfop3(12,(x / y) - z       ,"(t/t)-t")
      define_sfop3(13,(x / y) / z       ,"(t/t)/t")
      define_sfop3(14,(x / y) * z       ,"(t/t)*t")
      define_sfop3(15,x / (y + z)       ,"t/(t+t)")
      define_sfop3(16,x / (y - z)       ,"t/(t-t)")
      define_sfop3(17,x / (y * z)       ,"t/(t*t)")
      define_sfop3(18,x / (y / z)       ,"t/(t/t)")
      define_sfop3(19,x * (y + z)       ,"t*(t+t)")
      define_sfop3(20,x * (y - z)       ,"t*(t-t)")
      define_sfop3(21,x * (y * z)       ,"t*(t*t)")
      define_sfop3(22,x * (y / z)       ,"t*(t/t)")
      define_sfop3(23,x - (y + z)       ,"t-(t+t)")
      define_sfop3(24,x - (y - z)       ,"t-(t-t)")
      define_sfop3(25,x - (y / z)       ,"t-(t/t)")
      define_sfop3(26,x - (y * z)       ,"t-(t*t)")
      define_sfop3(27,x + (y * z)       ,"t+(t*t)")
      define_sfop3(28,x + (y / z)       ,"t+(t/t)")
      define_sfop3(29,x + (y + z)       ,"t+(t+t)")
      define_sfop3(30,x + (y - z)       ,"t+(t-t)")
      define_sfop3(31,(axnb<T,2>(x,y,z)),"       ")
      define_sfop3(32,(axnb<T,3>(x,y,z)),"       ")
      define_sfop3(33,(axnb<T,4>(x,y,z)),"       ")
      define_sfop3(34,(axnb<T,5>(x,y,z)),"       ")
      define_sfop3(35,(axnb<T,6>(x,y,z)),"       ")
      define_sfop3(36,(axnb<T,7>(x,y,z)),"       ")
      define_sfop3(37,(axnb<T,8>(x,y,z)),"       ")
      define_sfop3(38,(axnb<T,9>(x,y,z)),"       ")
      define_sfop3(39,x * numeric::log(y)   + z,"")
      define_sfop3(40,x * numeric::log(y)   - z,"")
      define_sfop3(41,x * numeric::log10(y) + z,"")
      define_sfop3(42,x * numeric::log10(y) - z,"")
      define_sfop3(43,x * numeric::sin(y) + z  ,"")
      define_sfop3(44,x * numeric::sin(y) - z  ,"")
      define_sfop3(45,x * numeric::cos(y) + z  ,"")
      define_sfop3(46,x * numeric::cos(y) - z  ,"")
      define_sfop3(47,details::is_true(x) ? y : z,"")

      #define define_sfop4(NN,OP0,OP1)                           \
      template <typename T>                                      \
      struct sf##NN##_op : public sf_base<T>                     \
      {                                                          \
         typedef typename sf_base<T>::Type const Type;           \
         static inline T process(Type x, Type y, Type z, Type w) \
         {                                                       \
            return (OP0);                                        \
         }                                                       \
         static inline std::string id()                          \
         {                                                       \
            return (OP1);                                        \
         }                                                       \
      };                                                         \

      define_sfop4(48,(x + ((y + z) / w)),"t+((t+t)/t)")
      define_sfop4(49,(x + ((y + z) * w)),"t+((t+t)*t)")
      define_sfop4(50,(x + ((y - z) / w)),"t+((t-t)/t)")
      define_sfop4(51,(x + ((y - z) * w)),"t+((t-t)*t)")
      define_sfop4(52,(x + ((y * z) / w)),"t+((t*t)/t)")
      define_sfop4(53,(x + ((y * z) * w)),"t+((t*t)*t)")
      define_sfop4(54,(x + ((y / z) + w)),"t+((t/t)+t)")
      define_sfop4(55,(x + ((y / z) / w)),"t+((t/t)/t)")
      define_sfop4(56,(x + ((y / z) * w)),"t+((t/t)*t)")
      define_sfop4(57,(x - ((y + z) / w)),"t-((t+t)/t)")
      define_sfop4(58,(x - ((y + z) * w)),"t-((t+t)*t)")
      define_sfop4(59,(x - ((y - z) / w)),"t-((t-t)/t)")
      define_sfop4(60,(x - ((y - z) * w)),"t-((t-t)*t)")
      define_sfop4(61,(x - ((y * z) / w)),"t-((t*t)/t)")
      define_sfop4(62,(x - ((y * z) * w)),"t-((t*t)*t)")
      define_sfop4(63,(x - ((y / z) / w)),"t-((t/t)/t)")
      define_sfop4(64,(x - ((y / z) * w)),"t-((t/t)*t)")
      define_sfop4(65,(((x + y) * z) - w),"((t+t)*t)-t")
      define_sfop4(66,(((x - y) * z) - w),"((t-t)*t)-t")
      define_sfop4(67,(((x * y) * z) - w),"((t*t)*t)-t")
      define_sfop4(68,(((x / y) * z) - w),"((t/t)*t)-t")
      define_sfop4(69,(((x + y) / z) - w),"((t+t)/t)-t")
      define_sfop4(70,(((x - y) / z) - w),"((t-t)/t)-t")
      define_sfop4(71,(((x * y) / z) - w),"((t*t)/t)-t")
      define_sfop4(72,(((x / y) / z) - w),"((t/t)/t)-t")
      define_sfop4(73,((x * y) + (z * w)),"(t*t)+(t*t)")
      define_sfop4(74,((x * y) - (z * w)),"(t*t)-(t*t)")
      define_sfop4(75,((x * y) + (z / w)),"(t*t)+(t/t)")
      define_sfop4(76,((x * y) - (z / w)),"(t*t)-(t/t)")
      define_sfop4(77,((x / y) + (z / w)),"(t/t)+(t/t)")
      define_sfop4(78,((x / y) - (z / w)),"(t/t)-(t/t)")
      define_sfop4(79,((x / y) - (z * w)),"(t/t)-(t*t)")
      define_sfop4(80,(x / (y + (z * w))),"t/(t+(t*t))")
      define_sfop4(81,(x / (y - (z * w))),"t/(t-(t*t))")
      define_sfop4(82,(x * (y + (z * w))),"t*(t+(t*t))")
      define_sfop4(83,(x * (y - (z * w))),"t*(t-(t*t))")

      define_sfop4(84,(axn<T,2>(x,y) + axn<T,2>(z,w)),"")
      define_sfop4(85,(axn<T,3>(x,y) + axn<T,3>(z,w)),"")
      define_sfop4(86,(axn<T,4>(x,y) + axn<T,4>(z,w)),"")
      define_sfop4(87,(axn<T,5>(x,y) + axn<T,5>(z,w)),"")
      define_sfop4(88,(axn<T,6>(x,y) + axn<T,6>(z,w)),"")
      define_sfop4(89,(axn<T,7>(x,y) + axn<T,7>(z,w)),"")
      define_sfop4(90,(axn<T,8>(x,y) + axn<T,8>(z,w)),"")
      define_sfop4(91,(axn<T,9>(x,y) + axn<T,9>(z,w)),"")
      define_sfop4(92,((details::is_true(x) && details::is_true(y)) ? z : w),"")
      define_sfop4(93,((details::is_true(x) || details::is_true(y)) ? z : w),"")
      define_sfop4(94,((x <  y) ? z : w),"")
      define_sfop4(95,((x <= y) ? z : w),"")
      define_sfop4(96,((x >  y) ? z : w),"")
      define_sfop4(97,((x >= y) ? z : w),"")
      define_sfop4(98,(details::is_true(numeric::equal(x,y)) ? z : w),"")
      define_sfop4(99,(x * numeric::sin(y) + z * numeric::cos(w)),"")

      define_sfop4(ext00,((x + y) - (z * w)),"(t+t)-(t*t)")
      define_sfop4(ext01,((x + y) - (z / w)),"(t+t)-(t/t)")
      define_sfop4(ext02,((x + y) + (z * w)),"(t+t)+(t*t)")
      define_sfop4(ext03,((x + y) + (z / w)),"(t+t)+(t/t)")
      define_sfop4(ext04,((x - y) + (z * w)),"(t-t)+(t*t)")
      define_sfop4(ext05,((x - y) + (z / w)),"(t-t)+(t/t)")
      define_sfop4(ext06,((x - y) - (z * w)),"(t-t)-(t*t)")
      define_sfop4(ext07,((x - y) - (z / w)),"(t-t)-(t/t)")
      define_sfop4(ext08,((x + y) - (z - w)),"(t+t)-(t-t)")
      define_sfop4(ext09,((x + y) + (z - w)),"(t+t)+(t-t)")
      define_sfop4(ext10,((x + y) + (z + w)),"(t+t)+(t+t)")
      define_sfop4(ext11,((x + y) * (z - w)),"(t+t)*(t-t)")
      define_sfop4(ext12,((x + y) / (z - w)),"(t+t)/(t-t)")
      define_sfop4(ext13,((x - y) - (z + w)),"(t-t)-(t+t)")
      define_sfop4(ext14,((x - y) + (z + w)),"(t-t)+(t+t)")
      define_sfop4(ext15,((x - y) * (z + w)),"(t-t)*(t+t)")
      define_sfop4(ext16,((x - y) / (z + w)),"(t-t)/(t+t)")
      define_sfop4(ext17,((x * y) - (z + w)),"(t*t)-(t+t)")
      define_sfop4(ext18,((x / y) - (z + w)),"(t/t)-(t+t)")
      define_sfop4(ext19,((x * y) + (z + w)),"(t*t)+(t+t)")
      define_sfop4(ext20,((x / y) + (z + w)),"(t/t)+(t+t)")
      define_sfop4(ext21,((x * y) + (z - w)),"(t*t)+(t-t)")
      define_sfop4(ext22,((x / y) + (z - w)),"(t/t)+(t-t)")
      define_sfop4(ext23,((x * y) - (z - w)),"(t*t)-(t-t)")
      define_sfop4(ext24,((x / y) - (z - w)),"(t/t)-(t-t)")
      define_sfop4(ext25,((x + y) * (z * w)),"(t+t)*(t*t)")
      define_sfop4(ext26,((x + y) * (z / w)),"(t+t)*(t/t)")
      define_sfop4(ext27,((x + y) / (z * w)),"(t+t)/(t*t)")
      define_sfop4(ext28,((x + y) / (z / w)),"(t+t)/(t/t)")
      define_sfop4(ext29,((x - y) / (z * w)),"(t-t)/(t*t)")
      define_sfop4(ext30,((x - y) / (z / w)),"(t-t)/(t/t)")
      define_sfop4(ext31,((x - y) * (z * w)),"(t-t)*(t*t)")
      define_sfop4(ext32,((x - y) * (z / w)),"(t-t)*(t/t)")
      define_sfop4(ext33,((x * y) * (z + w)),"(t*t)*(t+t)")
      define_sfop4(ext34,((x / y) * (z + w)),"(t/t)*(t+t)")
      define_sfop4(ext35,((x * y) / (z + w)),"(t*t)/(t+t)")
      define_sfop4(ext36,((x / y) / (z + w)),"(t/t)/(t+t)")
      define_sfop4(ext37,((x * y) / (z - w)),"(t*t)/(t-t)")
      define_sfop4(ext38,((x / y) / (z - w)),"(t/t)/(t-t)")
      define_sfop4(ext39,((x * y) * (z - w)),"(t*t)*(t-t)")
      define_sfop4(ext40,((x * y) / (z * w)),"(t*t)/(t*t)")
      define_sfop4(ext41,((x / y) * (z / w)),"(t/t)*(t/t)")
      define_sfop4(ext42,((x / y) * (z - w)),"(t/t)*(t-t)")
      define_sfop4(ext43,((x * y) * (z * w)),"(t*t)*(t*t)")
      define_sfop4(ext44,(x + (y * (z / w))),"t+(t*(t/t))")
      define_sfop4(ext45,(x - (y * (z / w))),"t-(t*(t/t))")
      define_sfop4(ext46,(x + (y / (z * w))),"t+(t/(t*t))")
      define_sfop4(ext47,(x - (y / (z * w))),"t-(t/(t*t))")
      define_sfop4(ext48,(((x - y) - z) * w),"((t-t)-t)*t")
      define_sfop4(ext49,(((x - y) - z) / w),"((t-t)-t)/t")
      define_sfop4(ext50,(((x - y) + z) * w),"((t-t)+t)*t")
      define_sfop4(ext51,(((x - y) + z) / w),"((t-t)+t)/t")
      define_sfop4(ext52,((x + (y - z)) * w),"(t+(t-t))*t")
      define_sfop4(ext53,((x + (y - z)) / w),"(t+(t-t))/t")
      define_sfop4(ext54,((x + y) / (z + w)),"(t+t)/(t+t)")
      define_sfop4(ext55,((x - y) / (z - w)),"(t-t)/(t-t)")
      define_sfop4(ext56,((x + y) * (z + w)),"(t+t)*(t+t)")
      define_sfop4(ext57,((x - y) * (z - w)),"(t-t)*(t-t)")
      define_sfop4(ext58,((x - y) + (z - w)),"(t-t)+(t-t)")
      define_sfop4(ext59,((x - y) - (z - w)),"(t-t)-(t-t)")
      define_sfop4(ext60,((x / y) + (z * w)),"(t/t)+(t*t)")
      define_sfop4(ext61,(((x * y) * z) / w),"((t*t)*t)/t")

      #undef define_sfop3
      #undef define_sfop4

      template <typename T, typename SpecialFunction>
      class sf3_node exprtk_final : public trinary_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;

         sf3_node(const operator_type& opr,
                  expression_ptr branch0,
                  expression_ptr branch1,
                  expression_ptr branch2)
         : trinary_node<T>(opr, branch0, branch1, branch2)
         {}

         inline T value() const
         {
            assert(trinary_node<T>::branch_[0].first);
            assert(trinary_node<T>::branch_[1].first);
            assert(trinary_node<T>::branch_[2].first);

            const T x = trinary_node<T>::branch_[0].first->value();
            const T y = trinary_node<T>::branch_[1].first->value();
            const T z = trinary_node<T>::branch_[2].first->value();

            return SpecialFunction::process(x, y, z);
         }
      };

      template <typename T, typename SpecialFunction>
      class sf4_node exprtk_final : public quaternary_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;

         sf4_node(const operator_type& opr,
                  expression_ptr branch0,
                  expression_ptr branch1,
                  expression_ptr branch2,
                  expression_ptr branch3)
         : quaternary_node<T>(opr, branch0, branch1, branch2, branch3)
         {}

         inline T value() const
         {
            assert(quaternary_node<T>::branch_[0].first);
            assert(quaternary_node<T>::branch_[1].first);
            assert(quaternary_node<T>::branch_[2].first);
            assert(quaternary_node<T>::branch_[3].first);

            const T x = quaternary_node<T>::branch_[0].first->value();
            const T y = quaternary_node<T>::branch_[1].first->value();
            const T z = quaternary_node<T>::branch_[2].first->value();
            const T w = quaternary_node<T>::branch_[3].first->value();

            return SpecialFunction::process(x, y, z, w);
         }
      };

      template <typename T, typename SpecialFunction>
      class sf3_var_node exprtk_final : public expression_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;

         sf3_var_node(const T& v0, const T& v1, const T& v2)
         : v0_(v0)
         , v1_(v1)
         , v2_(v2)
         {}

         inline T value() const
         {
            return SpecialFunction::process(v0_, v1_, v2_);
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_trinary;
         }

      private:

         sf3_var_node(sf3_var_node<T,SpecialFunction>&);
         sf3_var_node<T,SpecialFunction>& operator=(sf3_var_node<T,SpecialFunction>&);

         const T& v0_;
         const T& v1_;
         const T& v2_;
      };

      template <typename T, typename SpecialFunction>
      class sf4_var_node exprtk_final : public expression_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;

         sf4_var_node(const T& v0, const T& v1, const T& v2, const T& v3)
         : v0_(v0)
         , v1_(v1)
         , v2_(v2)
         , v3_(v3)
         {}

         inline T value() const
         {
            return SpecialFunction::process(v0_, v1_, v2_, v3_);
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_trinary;
         }

      private:

         sf4_var_node(sf4_var_node<T,SpecialFunction>&);
         sf4_var_node<T,SpecialFunction>& operator=(sf4_var_node<T,SpecialFunction>&);

         const T& v0_;
         const T& v1_;
         const T& v2_;
         const T& v3_;
      };

      template <typename T, typename VarArgFunction>
      class vararg_node exprtk_final : public expression_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef std::pair<expression_ptr,bool> branch_t;

         template <typename Allocator,
                   template <typename, typename> class Sequence>
         explicit vararg_node(const Sequence<expression_ptr,Allocator>& arg_list)
         {
            arg_list_.resize(arg_list.size());

            for (std::size_t i = 0; i < arg_list.size(); ++i)
            {
               if (arg_list[i])
               {
                  construct_branch_pair(arg_list_[i],arg_list[i]);
               }
               else
               {
                  arg_list_.clear();
                  return;
               }
            }
         }

         inline T value() const
         {
            return VarArgFunction::process(arg_list_);
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_vararg;
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::collect(arg_list_, node_delete_list);
         }

         std::size_t node_depth() const
         {
            return expression_node<T>::ndb_t::compute_node_depth(arg_list_);
         }

      private:

         std::vector<branch_t> arg_list_;
      };

      template <typename T, typename VarArgFunction>
      class vararg_varnode exprtk_final : public expression_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;

         template <typename Allocator,
                   template <typename, typename> class Sequence>
         explicit vararg_varnode(const Sequence<expression_ptr,Allocator>& arg_list)
         {
            arg_list_.resize(arg_list.size());

            for (std::size_t i = 0; i < arg_list.size(); ++i)
            {
               if (arg_list[i] && is_variable_node(arg_list[i]))
               {
                  variable_node<T>* var_node_ptr = static_cast<variable_node<T>*>(arg_list[i]);
                  arg_list_[i] = (&var_node_ptr->ref());
               }
               else
               {
                  arg_list_.clear();
                  return;
               }
            }
         }

         inline T value() const
         {
            if (!arg_list_.empty())
               return VarArgFunction::process(arg_list_);
            else
               return std::numeric_limits<T>::quiet_NaN();
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_vararg;
         }

      private:

         std::vector<const T*> arg_list_;
      };

      template <typename T, typename VecFunction>
      class vectorize_node exprtk_final : public expression_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef std::pair<expression_ptr,bool> branch_t;

         explicit vectorize_node(const expression_ptr v)
         : ivec_ptr_(0)
         {
            construct_branch_pair(v_, v);

            if (is_ivector_node(v_.first))
            {
               ivec_ptr_ = dynamic_cast<vector_interface<T>*>(v_.first);
            }
            else
               ivec_ptr_ = 0;
         }

         inline T value() const
         {
            if (ivec_ptr_)
            {
               assert(v_.first);

               v_.first->value();

               return VecFunction::process(ivec_ptr_);
            }
            else
               return std::numeric_limits<T>::quiet_NaN();
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_vecfunc;
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::collect(v_, node_delete_list);
         }

         std::size_t node_depth() const
         {
            return expression_node<T>::ndb_t::compute_node_depth(v_);
         }

      private:

         vector_interface<T>* ivec_ptr_;
         branch_t                    v_;
      };

      template <typename T>
      class assignment_node exprtk_final : public binary_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;

         assignment_node(const operator_type& opr,
                         expression_ptr branch0,
                         expression_ptr branch1)
         : binary_node<T>(opr, branch0, branch1)
         , var_node_ptr_(0)
         {
            if (is_variable_node(binary_node<T>::branch_[0].first))
            {
               var_node_ptr_ = static_cast<variable_node<T>*>(binary_node<T>::branch_[0].first);
            }
         }

         inline T value() const
         {
            if (var_node_ptr_)
            {
               assert(binary_node<T>::branch_[1].first);

               T& result = var_node_ptr_->ref();

               result = binary_node<T>::branch_[1].first->value();

               return result;
            }
            else
               return std::numeric_limits<T>::quiet_NaN();
         }

      private:

         variable_node<T>* var_node_ptr_;
      };

      template <typename T>
      class assignment_vec_elem_node exprtk_final : public binary_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;

         assignment_vec_elem_node(const operator_type& opr,
                                  expression_ptr branch0,
                                  expression_ptr branch1)
         : binary_node<T>(opr, branch0, branch1)
         , vec_node_ptr_(0)
         {
            if (is_vector_elem_node(binary_node<T>::branch_[0].first))
            {
               vec_node_ptr_ = static_cast<vector_elem_node<T>*>(binary_node<T>::branch_[0].first);
            }
         }

         inline T value() const
         {
            if (vec_node_ptr_)
            {
               assert(binary_node<T>::branch_[1].first);

               T& result = vec_node_ptr_->ref();

               result = binary_node<T>::branch_[1].first->value();

               return result;
            }
            else
               return std::numeric_limits<T>::quiet_NaN();
         }

      private:

         vector_elem_node<T>* vec_node_ptr_;
      };

      template <typename T>
      class assignment_rebasevec_elem_node exprtk_final : public binary_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;

         assignment_rebasevec_elem_node(const operator_type& opr,
                                        expression_ptr branch0,
                                        expression_ptr branch1)
         : binary_node<T>(opr, branch0, branch1)
         , rbvec_node_ptr_(0)
         {
            if (is_rebasevector_elem_node(binary_node<T>::branch_[0].first))
            {
               rbvec_node_ptr_ = static_cast<rebasevector_elem_node<T>*>(binary_node<T>::branch_[0].first);
            }
         }

         inline T value() const
         {
            if (rbvec_node_ptr_)
            {
               assert(binary_node<T>::branch_[1].first);

               T& result = rbvec_node_ptr_->ref();

               result = binary_node<T>::branch_[1].first->value();

               return result;
            }
            else
               return std::numeric_limits<T>::quiet_NaN();
         }

      private:

         rebasevector_elem_node<T>* rbvec_node_ptr_;
      };

      template <typename T>
      class assignment_rebasevec_celem_node exprtk_final : public binary_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;

         assignment_rebasevec_celem_node(const operator_type& opr,
                                         expression_ptr branch0,
                                         expression_ptr branch1)
         : binary_node<T>(opr, branch0, branch1)
         , rbvec_node_ptr_(0)
         {
            if (is_rebasevector_celem_node(binary_node<T>::branch_[0].first))
            {
               rbvec_node_ptr_ = static_cast<rebasevector_celem_node<T>*>(binary_node<T>::branch_[0].first);
            }
         }

         inline T value() const
         {
            if (rbvec_node_ptr_)
            {
               assert(binary_node<T>::branch_[1].first);

               T& result = rbvec_node_ptr_->ref();

               result = binary_node<T>::branch_[1].first->value();

               return result;
            }
            else
               return std::numeric_limits<T>::quiet_NaN();
         }

      private:

         rebasevector_celem_node<T>* rbvec_node_ptr_;
      };

      template <typename T>
      class assignment_vec_node exprtk_final
                                : public binary_node     <T>,
                                  public vector_interface<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef vector_node<T>*    vector_node_ptr;
         typedef vec_data_store<T>            vds_t;

         assignment_vec_node(const operator_type& opr,
                             expression_ptr branch0,
                             expression_ptr branch1)
         : binary_node<T>(opr, branch0, branch1)
         , vec_node_ptr_(0)
         {
            if (is_vector_node(binary_node<T>::branch_[0].first))
            {
               vec_node_ptr_ = static_cast<vector_node<T>*>(binary_node<T>::branch_[0].first);
               vds()         = vec_node_ptr_->vds();
            }
         }

         inline T value() const
         {
            if (vec_node_ptr_)
            {
               assert(binary_node<T>::branch_[1].first);

               const T v = binary_node<T>::branch_[1].first->value();

               T* vec = vds().data();

               loop_unroll::details lud(size());
               const T* upper_bound = vec + lud.upper_bound;

               while (vec < upper_bound)
               {
                  #define exprtk_loop(N) \
                  vec[N] = v;            \

                  exprtk_loop( 0) exprtk_loop( 1)
                  exprtk_loop( 2) exprtk_loop( 3)
                  #ifndef exprtk_disable_superscalar_unroll
                  exprtk_loop( 4) exprtk_loop( 5)
                  exprtk_loop( 6) exprtk_loop( 7)
                  exprtk_loop( 8) exprtk_loop( 9)
                  exprtk_loop(10) exprtk_loop(11)
                  exprtk_loop(12) exprtk_loop(13)
                  exprtk_loop(14) exprtk_loop(15)
                  #endif

                  vec += lud.batch_size;
               }

               exprtk_disable_fallthrough_begin
               switch (lud.remainder)
               {
                  #define case_stmt(N) \
                  case N : *vec++ = v; \

                  #ifndef exprtk_disable_superscalar_unroll
                  case_stmt(15) case_stmt(14)
                  case_stmt(13) case_stmt(12)
                  case_stmt(11) case_stmt(10)
                  case_stmt( 9) case_stmt( 8)
                  case_stmt( 7) case_stmt( 6)
                  case_stmt( 5) case_stmt( 4)
                  #endif
                  case_stmt( 3) case_stmt( 2)
                  case_stmt( 1)
               }
               exprtk_disable_fallthrough_end

               #undef exprtk_loop
               #undef case_stmt

               return vec_node_ptr_->value();
            }
            else
               return std::numeric_limits<T>::quiet_NaN();
         }

         vector_node_ptr vec() const
         {
            return vec_node_ptr_;
         }

         vector_node_ptr vec()
         {
            return vec_node_ptr_;
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_vecvalass;
         }

         std::size_t size() const
         {
            return vds().size();
         }

         vds_t& vds()
         {
            return vds_;
         }

         const vds_t& vds() const
         {
            return vds_;
         }

      private:

         vector_node<T>* vec_node_ptr_;
         vds_t           vds_;
      };

      template <typename T>
      class assignment_vecvec_node exprtk_final
                                   : public binary_node     <T>,
                                     public vector_interface<T>
      {
      public:

         typedef expression_node<T>*  expression_ptr;
         typedef vector_node<T>*     vector_node_ptr;
         typedef vec_data_store<T>             vds_t;

         assignment_vecvec_node(const operator_type& opr,
                                expression_ptr branch0,
                                expression_ptr branch1)
         : binary_node<T>(opr, branch0, branch1)
         , vec0_node_ptr_(0)
         , vec1_node_ptr_(0)
         , initialised_(false)
         , src_is_ivec_(false)
         {
            if (is_vector_node(binary_node<T>::branch_[0].first))
            {
               vec0_node_ptr_ = static_cast<vector_node<T>*>(binary_node<T>::branch_[0].first);
               vds()          = vec0_node_ptr_->vds();
            }

            if (is_vector_node(binary_node<T>::branch_[1].first))
            {
               vec1_node_ptr_ = static_cast<vector_node<T>*>(binary_node<T>::branch_[1].first);
               vds_t::match_sizes(vds(),vec1_node_ptr_->vds());
            }
            else if (is_ivector_node(binary_node<T>::branch_[1].first))
            {
               vector_interface<T>* vi = reinterpret_cast<vector_interface<T>*>(0);

               if (0 != (vi = dynamic_cast<vector_interface<T>*>(binary_node<T>::branch_[1].first)))
               {
                  vec1_node_ptr_ = vi->vec();

                  if (!vi->side_effect())
                  {
                     vi->vds()    = vds();
                     src_is_ivec_ = true;
                  }
                  else
                     vds_t::match_sizes(vds(),vi->vds());
               }
            }

            initialised_ = (vec0_node_ptr_ && vec1_node_ptr_);
         }

         inline T value() const
         {
            if (initialised_)
            {
               assert(binary_node<T>::branch_[1].first);

               binary_node<T>::branch_[1].first->value();

               if (src_is_ivec_)
                  return vec0_node_ptr_->value();

               T* vec0 = vec0_node_ptr_->vds().data();
               T* vec1 = vec1_node_ptr_->vds().data();

               loop_unroll::details lud(size());
               const T* upper_bound = vec0 + lud.upper_bound;

               while (vec0 < upper_bound)
               {
                  #define exprtk_loop(N) \
                  vec0[N] = vec1[N];     \

                  exprtk_loop( 0) exprtk_loop( 1)
                  exprtk_loop( 2) exprtk_loop( 3)
                  #ifndef exprtk_disable_superscalar_unroll
                  exprtk_loop( 4) exprtk_loop( 5)
                  exprtk_loop( 6) exprtk_loop( 7)
                  exprtk_loop( 8) exprtk_loop( 9)
                  exprtk_loop(10) exprtk_loop(11)
                  exprtk_loop(12) exprtk_loop(13)
                  exprtk_loop(14) exprtk_loop(15)
                  #endif

                  vec0 += lud.batch_size;
                  vec1 += lud.batch_size;
               }

               exprtk_disable_fallthrough_begin
               switch (lud.remainder)
               {
                  #define case_stmt(N)        \
                  case N : *vec0++ = *vec1++; \

                  #ifndef exprtk_disable_superscalar_unroll
                  case_stmt(15) case_stmt(14)
                  case_stmt(13) case_stmt(12)
                  case_stmt(11) case_stmt(10)
                  case_stmt( 9) case_stmt( 8)
                  case_stmt( 7) case_stmt( 6)
                  case_stmt( 5) case_stmt( 4)
                  #endif
                  case_stmt( 3) case_stmt( 2)
                  case_stmt( 1)
               }
               exprtk_disable_fallthrough_end

               #undef exprtk_loop
               #undef case_stmt

               return vec0_node_ptr_->value();
            }
            else
               return std::numeric_limits<T>::quiet_NaN();
         }

         vector_node_ptr vec() const
         {
            return vec0_node_ptr_;
         }

         vector_node_ptr vec()
         {
            return vec0_node_ptr_;
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_vecvecass;
         }

         std::size_t size() const
         {
            return vds().size();
         }

         vds_t& vds()
         {
            return vds_;
         }

         const vds_t& vds() const
         {
            return vds_;
         }

      private:

         vector_node<T>* vec0_node_ptr_;
         vector_node<T>* vec1_node_ptr_;
         bool            initialised_;
         bool            src_is_ivec_;
         vds_t           vds_;
      };

      template <typename T, typename Operation>
      class assignment_op_node exprtk_final : public binary_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;

         assignment_op_node(const operator_type& opr,
                            expression_ptr branch0,
                            expression_ptr branch1)
         : binary_node<T>(opr, branch0, branch1)
         , var_node_ptr_(0)
         {
            if (is_variable_node(binary_node<T>::branch_[0].first))
            {
               var_node_ptr_ = static_cast<variable_node<T>*>(binary_node<T>::branch_[0].first);
            }
         }

         inline T value() const
         {
            if (var_node_ptr_)
            {
               assert(binary_node<T>::branch_[1].first);

               T& v = var_node_ptr_->ref();
               v = Operation::process(v,binary_node<T>::branch_[1].first->value());

               return v;
            }
            else
               return std::numeric_limits<T>::quiet_NaN();
         }

      private:

         variable_node<T>* var_node_ptr_;
      };

      template <typename T, typename Operation>
      class assignment_vec_elem_op_node exprtk_final : public binary_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;

         assignment_vec_elem_op_node(const operator_type& opr,
                                     expression_ptr branch0,
                                     expression_ptr branch1)
         : binary_node<T>(opr, branch0, branch1)
         , vec_node_ptr_(0)
         {
            if (is_vector_elem_node(binary_node<T>::branch_[0].first))
            {
               vec_node_ptr_ = static_cast<vector_elem_node<T>*>(binary_node<T>::branch_[0].first);
            }
         }

         inline T value() const
         {
            if (vec_node_ptr_)
            {
               assert(binary_node<T>::branch_[1].first);

               T& v = vec_node_ptr_->ref();
                  v = Operation::process(v,binary_node<T>::branch_[1].first->value());

               return v;
            }
            else
               return std::numeric_limits<T>::quiet_NaN();
         }

      private:

         vector_elem_node<T>* vec_node_ptr_;
      };

      template <typename T, typename Operation>
      class assignment_rebasevec_elem_op_node exprtk_final : public binary_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;

         assignment_rebasevec_elem_op_node(const operator_type& opr,
                                           expression_ptr branch0,
                                           expression_ptr branch1)
         : binary_node<T>(opr, branch0, branch1)
         , rbvec_node_ptr_(0)
         {
            if (is_rebasevector_elem_node(binary_node<T>::branch_[0].first))
            {
               rbvec_node_ptr_ = static_cast<rebasevector_elem_node<T>*>(binary_node<T>::branch_[0].first);
            }
         }

         inline T value() const
         {
            if (rbvec_node_ptr_)
            {
               assert(binary_node<T>::branch_[1].first);

               T& v = rbvec_node_ptr_->ref();
                  v = Operation::process(v,binary_node<T>::branch_[1].first->value());

               return v;
            }
            else
               return std::numeric_limits<T>::quiet_NaN();
         }

      private:

         rebasevector_elem_node<T>* rbvec_node_ptr_;
      };

      template <typename T, typename Operation>
      class assignment_rebasevec_celem_op_node exprtk_final : public binary_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;

         assignment_rebasevec_celem_op_node(const operator_type& opr,
                                            expression_ptr branch0,
                                            expression_ptr branch1)
         : binary_node<T>(opr, branch0, branch1)
         , rbvec_node_ptr_(0)
         {
            if (is_rebasevector_celem_node(binary_node<T>::branch_[0].first))
            {
               rbvec_node_ptr_ = static_cast<rebasevector_celem_node<T>*>(binary_node<T>::branch_[0].first);
            }
         }

         inline T value() const
         {
            if (rbvec_node_ptr_)
            {
               assert(binary_node<T>::branch_[1].first);

               T& v = rbvec_node_ptr_->ref();
                  v = Operation::process(v,binary_node<T>::branch_[1].first->value());

               return v;
            }
            else
               return std::numeric_limits<T>::quiet_NaN();
         }

      private:

         rebasevector_celem_node<T>* rbvec_node_ptr_;
      };

      template <typename T, typename Operation>
      class assignment_vec_op_node exprtk_final
                                   : public binary_node     <T>,
                                     public vector_interface<T>
      {
      public:

         typedef expression_node<T>*  expression_ptr;
         typedef vector_node<T>*     vector_node_ptr;
         typedef vec_data_store<T>             vds_t;

         assignment_vec_op_node(const operator_type& opr,
                                expression_ptr branch0,
                                expression_ptr branch1)
         : binary_node<T>(opr, branch0, branch1)
         , vec_node_ptr_(0)
         {
            if (is_vector_node(binary_node<T>::branch_[0].first))
            {
               vec_node_ptr_ = static_cast<vector_node<T>*>(binary_node<T>::branch_[0].first);
               vds()         = vec_node_ptr_->vds();
            }
         }

         inline T value() const
         {
            if (vec_node_ptr_)
            {
               assert(binary_node<T>::branch_[1].first);

               const T v = binary_node<T>::branch_[1].first->value();

               T* vec = vds().data();

               loop_unroll::details lud(size());
               const T* upper_bound = vec + lud.upper_bound;

               while (vec < upper_bound)
               {
                  #define exprtk_loop(N)       \
                  Operation::assign(vec[N],v); \

                  exprtk_loop( 0) exprtk_loop( 1)
                  exprtk_loop( 2) exprtk_loop( 3)
                  #ifndef exprtk_disable_superscalar_unroll
                  exprtk_loop( 4) exprtk_loop( 5)
                  exprtk_loop( 6) exprtk_loop( 7)
                  exprtk_loop( 8) exprtk_loop( 9)
                  exprtk_loop(10) exprtk_loop(11)
                  exprtk_loop(12) exprtk_loop(13)
                  exprtk_loop(14) exprtk_loop(15)
                  #endif

                  vec += lud.batch_size;
               }

               exprtk_disable_fallthrough_begin
               switch (lud.remainder)
               {
                  #define case_stmt(N)                  \
                  case N : Operation::assign(*vec++,v); \

                  #ifndef exprtk_disable_superscalar_unroll
                  case_stmt(15) case_stmt(14)
                  case_stmt(13) case_stmt(12)
                  case_stmt(11) case_stmt(10)
                  case_stmt( 9) case_stmt( 8)
                  case_stmt( 7) case_stmt( 6)
                  case_stmt( 5) case_stmt( 4)
                  #endif
                  case_stmt( 3) case_stmt( 2)
                  case_stmt( 1)
               }
               exprtk_disable_fallthrough_end


               #undef exprtk_loop
               #undef case_stmt

               return vec_node_ptr_->value();
            }
            else
               return std::numeric_limits<T>::quiet_NaN();
         }

         vector_node_ptr vec() const
         {
            return vec_node_ptr_;
         }

         vector_node_ptr vec()
         {
            return vec_node_ptr_;
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_vecopvalass;
         }

         std::size_t size() const
         {
            return vds().size();
         }

         vds_t& vds()
         {
            return vds_;
         }

         const vds_t& vds() const
         {
            return vds_;
         }

         bool side_effect() const
         {
            return true;
         }

      private:

         vector_node<T>* vec_node_ptr_;
         vds_t           vds_;
      };

      template <typename T, typename Operation>
      class assignment_vecvec_op_node exprtk_final
                                      : public binary_node     <T>,
                                        public vector_interface<T>
      {
      public:

         typedef expression_node<T>*  expression_ptr;
         typedef vector_node<T>*     vector_node_ptr;
         typedef vec_data_store<T>             vds_t;

         assignment_vecvec_op_node(const operator_type& opr,
                                   expression_ptr branch0,
                                   expression_ptr branch1)
         : binary_node<T>(opr, branch0, branch1)
         , vec0_node_ptr_(0)
         , vec1_node_ptr_(0)
         , initialised_(false)
         {
            if (is_vector_node(binary_node<T>::branch_[0].first))
            {
               vec0_node_ptr_ = static_cast<vector_node<T>*>(binary_node<T>::branch_[0].first);
               vds()          = vec0_node_ptr_->vds();
            }

            if (is_vector_node(binary_node<T>::branch_[1].first))
            {
               vec1_node_ptr_ = static_cast<vector_node<T>*>(binary_node<T>::branch_[1].first);
               vec1_node_ptr_->vds() = vds();
            }
            else if (is_ivector_node(binary_node<T>::branch_[1].first))
            {
               vector_interface<T>* vi = reinterpret_cast<vector_interface<T>*>(0);

               if (0 != (vi = dynamic_cast<vector_interface<T>*>(binary_node<T>::branch_[1].first)))
               {
                  vec1_node_ptr_ = vi->vec();
                  vec1_node_ptr_->vds() = vds();
               }
               else
                  vds_t::match_sizes(vds(),vec1_node_ptr_->vds());
            }

            initialised_ = (vec0_node_ptr_ && vec1_node_ptr_);
         }

         inline T value() const
         {
            if (initialised_)
            {
               assert(binary_node<T>::branch_[0].first);
               assert(binary_node<T>::branch_[1].first);

               binary_node<T>::branch_[0].first->value();
               binary_node<T>::branch_[1].first->value();

                     T* vec0 = vec0_node_ptr_->vds().data();
               const T* vec1 = vec1_node_ptr_->vds().data();

               loop_unroll::details lud(size());
               const T* upper_bound = vec0 + lud.upper_bound;

               while (vec0 < upper_bound)
               {
                  #define exprtk_loop(N)                          \
                  vec0[N] = Operation::process(vec0[N], vec1[N]); \

                  exprtk_loop( 0) exprtk_loop( 1)
                  exprtk_loop( 2) exprtk_loop( 3)
                  #ifndef exprtk_disable_superscalar_unroll
                  exprtk_loop( 4) exprtk_loop( 5)
                  exprtk_loop( 6) exprtk_loop( 7)
                  exprtk_loop( 8) exprtk_loop( 9)
                  exprtk_loop(10) exprtk_loop(11)
                  exprtk_loop(12) exprtk_loop(13)
                  exprtk_loop(14) exprtk_loop(15)
                  #endif

                  vec0 += lud.batch_size;
                  vec1 += lud.batch_size;
               }

               int i = 0;

               exprtk_disable_fallthrough_begin
               switch (lud.remainder)
               {
                  #define case_stmt(N)                                              \
                  case N : { vec0[i] = Operation::process(vec0[i], vec1[i]); ++i; } \

                  #ifndef exprtk_disable_superscalar_unroll
                  case_stmt(15) case_stmt(14)
                  case_stmt(13) case_stmt(12)
                  case_stmt(11) case_stmt(10)
                  case_stmt( 9) case_stmt( 8)
                  case_stmt( 7) case_stmt( 6)
                  case_stmt( 5) case_stmt( 4)
                  #endif
                  case_stmt( 3) case_stmt( 2)
                  case_stmt( 1)
               }
               exprtk_disable_fallthrough_end

               #undef exprtk_loop
               #undef case_stmt

               return vec0_node_ptr_->value();
            }
            else
               return std::numeric_limits<T>::quiet_NaN();
         }

         vector_node_ptr vec() const
         {
            return vec0_node_ptr_;
         }

         vector_node_ptr vec()
         {
            return vec0_node_ptr_;
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_vecopvecass;
         }

         std::size_t size() const
         {
            return vds().size();
         }

         vds_t& vds()
         {
            return vds_;
         }

         const vds_t& vds() const
         {
            return vds_;
         }

         bool side_effect() const
         {
            return true;
         }

      private:

         vector_node<T>* vec0_node_ptr_;
         vector_node<T>* vec1_node_ptr_;
         bool            initialised_;
         vds_t           vds_;
      };

      template <typename T, typename Operation>
      class vec_binop_vecvec_node exprtk_final
                                  : public binary_node     <T>,
                                    public vector_interface<T>
      {
      public:

         typedef expression_node<T>*    expression_ptr;
         typedef vector_node<T>*       vector_node_ptr;
         typedef vector_holder<T>*   vector_holder_ptr;
         typedef vec_data_store<T>               vds_t;

         vec_binop_vecvec_node(const operator_type& opr,
                               expression_ptr branch0,
                               expression_ptr branch1)
         : binary_node<T>(opr, branch0, branch1)
         , vec0_node_ptr_(0)
         , vec1_node_ptr_(0)
         , temp_         (0)
         , temp_vec_node_(0)
         , initialised_(false)
         {
            bool v0_is_ivec = false;
            bool v1_is_ivec = false;

            if (is_vector_node(binary_node<T>::branch_[0].first))
            {
               vec0_node_ptr_ = static_cast<vector_node_ptr>(binary_node<T>::branch_[0].first);
            }
            else if (is_ivector_node(binary_node<T>::branch_[0].first))
            {
               vector_interface<T>* vi = reinterpret_cast<vector_interface<T>*>(0);

               if (0 != (vi = dynamic_cast<vector_interface<T>*>(binary_node<T>::branch_[0].first)))
               {
                  vec0_node_ptr_ = vi->vec();
                  v0_is_ivec     = true;
               }
            }

            if (is_vector_node(binary_node<T>::branch_[1].first))
            {
               vec1_node_ptr_ = static_cast<vector_node_ptr>(binary_node<T>::branch_[1].first);
            }
            else if (is_ivector_node(binary_node<T>::branch_[1].first))
            {
               vector_interface<T>* vi = reinterpret_cast<vector_interface<T>*>(0);

               if (0 != (vi = dynamic_cast<vector_interface<T>*>(binary_node<T>::branch_[1].first)))
               {
                  vec1_node_ptr_ = vi->vec();
                  v1_is_ivec     = true;
               }
            }

            if (vec0_node_ptr_ && vec1_node_ptr_)
            {
               vector_holder<T>& vec0 = vec0_node_ptr_->vec_holder();
               vector_holder<T>& vec1 = vec1_node_ptr_->vec_holder();

               if (v0_is_ivec && (vec0.size() <= vec1.size()))
                  vds_ = vds_t(vec0_node_ptr_->vds());
               else if (v1_is_ivec && (vec1.size() <= vec0.size()))
                  vds_ = vds_t(vec1_node_ptr_->vds());
               else
                  vds_ = vds_t((std::min)(vec0.size(),vec1.size()));

               temp_          = new vector_holder<T>(vds().data(),vds().size());
               temp_vec_node_ = new vector_node<T>  (vds(),temp_);

               initialised_ = true;
            }
         }

        ~vec_binop_vecvec_node()
         {
            delete temp_;
            delete temp_vec_node_;
         }

         inline T value() const
         {
            if (initialised_)
            {
               assert(binary_node<T>::branch_[0].first);
               assert(binary_node<T>::branch_[1].first);

               binary_node<T>::branch_[0].first->value();
               binary_node<T>::branch_[1].first->value();

               const T* vec0 = vec0_node_ptr_->vds().data();
               const T* vec1 = vec1_node_ptr_->vds().data();
                     T* vec2 = vds().data();

               loop_unroll::details lud(size());
               const T* upper_bound = vec2 + lud.upper_bound;

               while (vec2 < upper_bound)
               {
                  #define exprtk_loop(N)                          \
                  vec2[N] = Operation::process(vec0[N], vec1[N]); \

                  exprtk_loop( 0) exprtk_loop( 1)
                  exprtk_loop( 2) exprtk_loop( 3)
                  #ifndef exprtk_disable_superscalar_unroll
                  exprtk_loop( 4) exprtk_loop( 5)
                  exprtk_loop( 6) exprtk_loop( 7)
                  exprtk_loop( 8) exprtk_loop( 9)
                  exprtk_loop(10) exprtk_loop(11)
                  exprtk_loop(12) exprtk_loop(13)
                  exprtk_loop(14) exprtk_loop(15)
                  #endif

                  vec0 += lud.batch_size;
                  vec1 += lud.batch_size;
                  vec2 += lud.batch_size;
               }

               int i = 0;

               exprtk_disable_fallthrough_begin
               switch (lud.remainder)
               {
                  #define case_stmt(N)                                              \
                  case N : { vec2[i] = Operation::process(vec0[i], vec1[i]); ++i; } \

                  #ifndef exprtk_disable_superscalar_unroll
                  case_stmt(15) case_stmt(14)
                  case_stmt(13) case_stmt(12)
                  case_stmt(11) case_stmt(10)
                  case_stmt( 9) case_stmt( 8)
                  case_stmt( 7) case_stmt( 6)
                  case_stmt( 5) case_stmt( 4)
                  #endif
                  case_stmt( 3) case_stmt( 2)
                  case_stmt( 1)
               }
               exprtk_disable_fallthrough_end

               #undef exprtk_loop
               #undef case_stmt

               return (vds().data())[0];
            }
            else
               return std::numeric_limits<T>::quiet_NaN();
         }

         vector_node_ptr vec() const
         {
            return temp_vec_node_;
         }

         vector_node_ptr vec()
         {
            return temp_vec_node_;
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_vecvecarith;
         }

         std::size_t size() const
         {
            return vds_.size();
         }

         vds_t& vds()
         {
            return vds_;
         }

         const vds_t& vds() const
         {
            return vds_;
         }

      private:

         vector_node_ptr   vec0_node_ptr_;
         vector_node_ptr   vec1_node_ptr_;
         vector_holder_ptr temp_;
         vector_node_ptr   temp_vec_node_;
         bool              initialised_;
         vds_t             vds_;
      };

      template <typename T, typename Operation>
      class vec_binop_vecval_node exprtk_final
                                  : public binary_node     <T>,
                                    public vector_interface<T>
      {
      public:

         typedef expression_node<T>*    expression_ptr;
         typedef vector_node<T>*       vector_node_ptr;
         typedef vector_holder<T>*   vector_holder_ptr;
         typedef vec_data_store<T>               vds_t;

         vec_binop_vecval_node(const operator_type& opr,
                               expression_ptr branch0,
                               expression_ptr branch1)
         : binary_node<T>(opr, branch0, branch1)
         , vec0_node_ptr_(0)
         , temp_         (0)
         , temp_vec_node_(0)
         {
            bool v0_is_ivec = false;

            if (is_vector_node(binary_node<T>::branch_[0].first))
            {
               vec0_node_ptr_ = static_cast<vector_node_ptr>(binary_node<T>::branch_[0].first);
            }
            else if (is_ivector_node(binary_node<T>::branch_[0].first))
            {
               vector_interface<T>* vi = reinterpret_cast<vector_interface<T>*>(0);

               if (0 != (vi = dynamic_cast<vector_interface<T>*>(binary_node<T>::branch_[0].first)))
               {
                  vec0_node_ptr_ = vi->vec();
                  v0_is_ivec     = true;
               }
            }

            if (vec0_node_ptr_)
            {
               if (v0_is_ivec)
                  vds() = vec0_node_ptr_->vds();
               else
                  vds() = vds_t(vec0_node_ptr_->size());

               temp_          = new vector_holder<T>(vds());
               temp_vec_node_ = new vector_node<T>  (vds(),temp_);
            }
         }

        ~vec_binop_vecval_node()
         {
            delete temp_;
            delete temp_vec_node_;
         }

         inline T value() const
         {
            if (vec0_node_ptr_)
            {
               assert(binary_node<T>::branch_[0].first);
               assert(binary_node<T>::branch_[1].first);

                           binary_node<T>::branch_[0].first->value();
               const T v = binary_node<T>::branch_[1].first->value();

               const T* vec0 = vec0_node_ptr_->vds().data();
                     T* vec1 = vds().data();

               loop_unroll::details lud(size());
               const T* upper_bound = vec0 + lud.upper_bound;

               while (vec0 < upper_bound)
               {
                  #define exprtk_loop(N)                    \
                  vec1[N] = Operation::process(vec0[N], v); \

                  exprtk_loop( 0) exprtk_loop( 1)
                  exprtk_loop( 2) exprtk_loop( 3)
                  #ifndef exprtk_disable_superscalar_unroll
                  exprtk_loop( 4) exprtk_loop( 5)
                  exprtk_loop( 6) exprtk_loop( 7)
                  exprtk_loop( 8) exprtk_loop( 9)
                  exprtk_loop(10) exprtk_loop(11)
                  exprtk_loop(12) exprtk_loop(13)
                  exprtk_loop(14) exprtk_loop(15)
                  #endif

                  vec0 += lud.batch_size;
                  vec1 += lud.batch_size;
               }

               int i = 0;

               exprtk_disable_fallthrough_begin
               switch (lud.remainder)
               {
                  #define case_stmt(N)                                        \
                  case N : { vec1[i] = Operation::process(vec0[i], v); ++i; } \

                  #ifndef exprtk_disable_superscalar_unroll
                  case_stmt(15) case_stmt(14)
                  case_stmt(13) case_stmt(12)
                  case_stmt(11) case_stmt(10)
                  case_stmt( 9) case_stmt( 8)
                  case_stmt( 7) case_stmt( 6)
                  case_stmt( 5) case_stmt( 4)
                  #endif
                  case_stmt( 3) case_stmt( 2)
                  case_stmt( 1)
               }
               exprtk_disable_fallthrough_end

               #undef exprtk_loop
               #undef case_stmt

               return (vds().data())[0];
            }
            else
               return std::numeric_limits<T>::quiet_NaN();
         }

         vector_node_ptr vec() const
         {
            return temp_vec_node_;
         }

         vector_node_ptr vec()
         {
            return temp_vec_node_;
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_vecvalarith;
         }

         std::size_t size() const
         {
            return vds().size();
         }

         vds_t& vds()
         {
            return vds_;
         }

         const vds_t& vds() const
         {
            return vds_;
         }

      private:

         vector_node_ptr   vec0_node_ptr_;
         vector_holder_ptr temp_;
         vector_node_ptr   temp_vec_node_;
         vds_t             vds_;
      };

      template <typename T, typename Operation>
      class vec_binop_valvec_node exprtk_final
                                  : public binary_node     <T>,
                                    public vector_interface<T>
      {
      public:

         typedef expression_node<T>*    expression_ptr;
         typedef vector_node<T>*       vector_node_ptr;
         typedef vector_holder<T>*   vector_holder_ptr;
         typedef vec_data_store<T>               vds_t;

         vec_binop_valvec_node(const operator_type& opr,
                               expression_ptr branch0,
                               expression_ptr branch1)
         : binary_node<T>(opr, branch0, branch1)
         , vec1_node_ptr_(0)
         , temp_         (0)
         , temp_vec_node_(0)
         {
            bool v1_is_ivec = false;

            if (is_vector_node(binary_node<T>::branch_[1].first))
            {
               vec1_node_ptr_ = static_cast<vector_node_ptr>(binary_node<T>::branch_[1].first);
            }
            else if (is_ivector_node(binary_node<T>::branch_[1].first))
            {
               vector_interface<T>* vi = reinterpret_cast<vector_interface<T>*>(0);

               if (0 != (vi = dynamic_cast<vector_interface<T>*>(binary_node<T>::branch_[1].first)))
               {
                  vec1_node_ptr_ = vi->vec();
                  v1_is_ivec     = true;
               }
            }

            if (vec1_node_ptr_)
            {
               if (v1_is_ivec)
                  vds() = vec1_node_ptr_->vds();
               else
                  vds() = vds_t(vec1_node_ptr_->size());

               temp_          = new vector_holder<T>(vds());
               temp_vec_node_ = new vector_node<T>  (vds(),temp_);
            }
         }

        ~vec_binop_valvec_node()
         {
            delete temp_;
            delete temp_vec_node_;
         }

         inline T value() const
         {
            if (vec1_node_ptr_)
            {
               assert(binary_node<T>::branch_[0].first);
               assert(binary_node<T>::branch_[1].first);

               const T v = binary_node<T>::branch_[0].first->value();
                           binary_node<T>::branch_[1].first->value();

                     T* vec0 = vds().data();
               const T* vec1 = vec1_node_ptr_->vds().data();

               loop_unroll::details lud(size());
               const T* upper_bound = vec0 + lud.upper_bound;

               while (vec0 < upper_bound)
               {
                  #define exprtk_loop(N)                    \
                  vec0[N] = Operation::process(v, vec1[N]); \

                  exprtk_loop( 0) exprtk_loop( 1)
                  exprtk_loop( 2) exprtk_loop( 3)
                  #ifndef exprtk_disable_superscalar_unroll
                  exprtk_loop( 4) exprtk_loop( 5)
                  exprtk_loop( 6) exprtk_loop( 7)
                  exprtk_loop( 8) exprtk_loop( 9)
                  exprtk_loop(10) exprtk_loop(11)
                  exprtk_loop(12) exprtk_loop(13)
                  exprtk_loop(14) exprtk_loop(15)
                  #endif

                  vec0 += lud.batch_size;
                  vec1 += lud.batch_size;
               }

               int i = 0;

               exprtk_disable_fallthrough_begin
               switch (lud.remainder)
               {
                  #define case_stmt(N)                                        \
                  case N : { vec0[i] = Operation::process(v, vec1[i]); ++i; } \

                  #ifndef exprtk_disable_superscalar_unroll
                  case_stmt(15) case_stmt(14)
                  case_stmt(13) case_stmt(12)
                  case_stmt(11) case_stmt(10)
                  case_stmt( 9) case_stmt( 8)
                  case_stmt( 7) case_stmt( 6)
                  case_stmt( 5) case_stmt( 4)
                  #endif
                  case_stmt( 3) case_stmt( 2)
                  case_stmt( 1)
               }
               exprtk_disable_fallthrough_end

               #undef exprtk_loop
               #undef case_stmt

               return (vds().data())[0];
            }
            else
               return std::numeric_limits<T>::quiet_NaN();
         }

         vector_node_ptr vec() const
         {
            return temp_vec_node_;
         }

         vector_node_ptr vec()
         {
            return temp_vec_node_;
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_vecvalarith;
         }

         std::size_t size() const
         {
            return vds().size();
         }

         vds_t& vds()
         {
            return vds_;
         }

         const vds_t& vds() const
         {
            return vds_;
         }

      private:

         vector_node_ptr   vec1_node_ptr_;
         vector_holder_ptr temp_;
         vector_node_ptr   temp_vec_node_;
         vds_t             vds_;
      };

      template <typename T, typename Operation>
      class unary_vector_node exprtk_final
                              : public unary_node      <T>,
                                public vector_interface<T>
      {
      public:

         typedef expression_node<T>*    expression_ptr;
         typedef vector_node<T>*       vector_node_ptr;
         typedef vector_holder<T>*   vector_holder_ptr;
         typedef vec_data_store<T>               vds_t;

         unary_vector_node(const operator_type& opr, expression_ptr branch0)
         : unary_node<T>(opr, branch0)
         , vec0_node_ptr_(0)
         , temp_         (0)
         , temp_vec_node_(0)
         {
            bool vec0_is_ivec = false;

            if (is_vector_node(unary_node<T>::branch_.first))
            {
               vec0_node_ptr_ = static_cast<vector_node_ptr>(unary_node<T>::branch_.first);
            }
            else if (is_ivector_node(unary_node<T>::branch_.first))
            {
               vector_interface<T>* vi = reinterpret_cast<vector_interface<T>*>(0);

               if (0 != (vi = dynamic_cast<vector_interface<T>*>(unary_node<T>::branch_.first)))
               {
                  vec0_node_ptr_ = vi->vec();
                  vec0_is_ivec   = true;
               }
            }

            if (vec0_node_ptr_)
            {
               if (vec0_is_ivec)
                  vds_ = vec0_node_ptr_->vds();
               else
                  vds_ = vds_t(vec0_node_ptr_->size());

               temp_          = new vector_holder<T>(vds());
               temp_vec_node_ = new vector_node<T>  (vds(),temp_);
            }
         }

        ~unary_vector_node()
         {
            delete temp_;
            delete temp_vec_node_;
         }

         inline T value() const
         {
            assert(unary_node<T>::branch_.first);

            unary_node<T>::branch_.first->value();

            if (vec0_node_ptr_)
            {
               const T* vec0 = vec0_node_ptr_->vds().data();
                     T* vec1 = vds().data();

               loop_unroll::details lud(size());
               const T* upper_bound = vec0 + lud.upper_bound;

               while (vec0 < upper_bound)
               {
                  #define exprtk_loop(N)                 \
                  vec1[N] = Operation::process(vec0[N]); \

                  exprtk_loop( 0) exprtk_loop( 1)
                  exprtk_loop( 2) exprtk_loop( 3)
                  #ifndef exprtk_disable_superscalar_unroll
                  exprtk_loop( 4) exprtk_loop( 5)
                  exprtk_loop( 6) exprtk_loop( 7)
                  exprtk_loop( 8) exprtk_loop( 9)
                  exprtk_loop(10) exprtk_loop(11)
                  exprtk_loop(12) exprtk_loop(13)
                  exprtk_loop(14) exprtk_loop(15)
                  #endif

                  vec0 += lud.batch_size;
                  vec1 += lud.batch_size;
               }

               int i = 0;

               exprtk_disable_fallthrough_begin
               switch (lud.remainder)
               {
                  #define case_stmt(N)                                     \
                  case N : { vec1[i] = Operation::process(vec0[i]); ++i; } \

                  #ifndef exprtk_disable_superscalar_unroll
                  case_stmt(15) case_stmt(14)
                  case_stmt(13) case_stmt(12)
                  case_stmt(11) case_stmt(10)
                  case_stmt( 9) case_stmt( 8)
                  case_stmt( 7) case_stmt( 6)
                  case_stmt( 5) case_stmt( 4)
                  #endif
                  case_stmt( 3) case_stmt( 2)
                  case_stmt( 1)
               }
               exprtk_disable_fallthrough_end

               #undef exprtk_loop
               #undef case_stmt

               return (vds().data())[0];
            }
            else
               return std::numeric_limits<T>::quiet_NaN();
         }

         vector_node_ptr vec() const
         {
            return temp_vec_node_;
         }

         vector_node_ptr vec()
         {
            return temp_vec_node_;
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_vecunaryop;
         }

         std::size_t size() const
         {
            return vds().size();
         }

         vds_t& vds()
         {
            return vds_;
         }

         const vds_t& vds() const
         {
            return vds_;
         }

      private:

         vector_node_ptr   vec0_node_ptr_;
         vector_holder_ptr temp_;
         vector_node_ptr   temp_vec_node_;
         vds_t             vds_;
      };

      template <typename T>
      class scand_node exprtk_final : public binary_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;

         scand_node(const operator_type& opr,
                    expression_ptr branch0,
                    expression_ptr branch1)
         : binary_node<T>(opr, branch0, branch1)
         {}

         inline T value() const
         {
            assert(binary_node<T>::branch_[0].first);
            assert(binary_node<T>::branch_[1].first);

            return (
                     std::not_equal_to<T>()
                        (T(0),binary_node<T>::branch_[0].first->value()) &&
                     std::not_equal_to<T>()
                        (T(0),binary_node<T>::branch_[1].first->value())
                   ) ? T(1) : T(0);
         }
      };

      template <typename T>
      class scor_node exprtk_final : public binary_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;

         scor_node(const operator_type& opr,
                   expression_ptr branch0,
                   expression_ptr branch1)
         : binary_node<T>(opr, branch0, branch1)
         {}

         inline T value() const
         {
            assert(binary_node<T>::branch_[0].first);
            assert(binary_node<T>::branch_[1].first);

            return (
                     std::not_equal_to<T>()
                        (T(0),binary_node<T>::branch_[0].first->value()) ||
                     std::not_equal_to<T>()
                        (T(0),binary_node<T>::branch_[1].first->value())
                   ) ? T(1) : T(0);
         }
      };

      template <typename T, typename IFunction, std::size_t N>
      class function_N_node exprtk_final : public expression_node<T>
      {
      public:

         // Function of N paramters.
         typedef expression_node<T>* expression_ptr;
         typedef std::pair<expression_ptr,bool> branch_t;
         typedef IFunction ifunction;

         explicit function_N_node(ifunction* func)
         : function_((N == func->param_count) ? func : reinterpret_cast<ifunction*>(0))
         , parameter_count_(func->param_count)
         {}

         template <std::size_t NumBranches>
         bool init_branches(expression_ptr (&b)[NumBranches])
         {
            // Needed for incompetent and broken msvc compiler versions
            #ifdef _MSC_VER
             #pragma warning(push)
             #pragma warning(disable: 4127)
            #endif
            if (N != NumBranches)
               return false;
            else
            {
               for (std::size_t i = 0; i < NumBranches; ++i)
               {
                  if (b[i])
                     branch_[i] = std::make_pair(b[i],branch_deletable(b[i]));
                  else
                     return false;
               }
               return true;
            }
            #ifdef _MSC_VER
             #pragma warning(pop)
            #endif
         }

         inline bool operator <(const function_N_node<T,IFunction,N>& fn) const
         {
            return this < (&fn);
         }

         inline T value() const
         {
            // Needed for incompetent and broken msvc compiler versions
            #ifdef _MSC_VER
             #pragma warning(push)
             #pragma warning(disable: 4127)
            #endif
            if ((0 == function_) || (0 == N))
               return std::numeric_limits<T>::quiet_NaN();
            else
            {
               T v[N];
               evaluate_branches<T,N>::execute(v,branch_);
               return invoke<T,N>::execute(*function_,v);
            }
            #ifdef _MSC_VER
             #pragma warning(pop)
            #endif
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_function;
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::template collect(branch_, node_delete_list);
         }

         std::size_t node_depth() const
         {
            return expression_node<T>::ndb_t::template compute_node_depth<N>(branch_);
         }

         template <typename T_, std::size_t BranchCount>
         struct evaluate_branches
         {
            static inline void execute(T_ (&v)[BranchCount], const branch_t (&b)[BranchCount])
            {
               for (std::size_t i = 0; i < BranchCount; ++i)
               {
                  v[i] = b[i].first->value();
               }
            }
         };

         template <typename T_>
         struct evaluate_branches <T_,5>
         {
            static inline void execute(T_ (&v)[5], const branch_t (&b)[5])
            {
               v[0] = b[0].first->value();
               v[1] = b[1].first->value();
               v[2] = b[2].first->value();
               v[3] = b[3].first->value();
               v[4] = b[4].first->value();
            }
         };

         template <typename T_>
         struct evaluate_branches <T_,4>
         {
            static inline void execute(T_ (&v)[4], const branch_t (&b)[4])
            {
               v[0] = b[0].first->value();
               v[1] = b[1].first->value();
               v[2] = b[2].first->value();
               v[3] = b[3].first->value();
            }
         };

         template <typename T_>
         struct evaluate_branches <T_,3>
         {
            static inline void execute(T_ (&v)[3], const branch_t (&b)[3])
            {
               v[0] = b[0].first->value();
               v[1] = b[1].first->value();
               v[2] = b[2].first->value();
            }
         };

         template <typename T_>
         struct evaluate_branches <T_,2>
         {
            static inline void execute(T_ (&v)[2], const branch_t (&b)[2])
            {
               v[0] = b[0].first->value();
               v[1] = b[1].first->value();
            }
         };

         template <typename T_>
         struct evaluate_branches <T_,1>
         {
            static inline void execute(T_ (&v)[1], const branch_t (&b)[1])
            {
               v[0] = b[0].first->value();
            }
         };

         template <typename T_, std::size_t ParamCount>
         struct invoke { static inline T execute(ifunction&, branch_t (&)[ParamCount]) { return std::numeric_limits<T_>::quiet_NaN(); } };

         template <typename T_>
         struct invoke<T_,20>
         {
            static inline T_ execute(ifunction& f, T_ (&v)[20])
            { return f(v[0],v[1],v[2],v[3],v[4],v[5],v[6],v[7],v[8],v[9],v[10],v[11],v[12],v[13],v[14],v[15],v[16],v[17],v[18],v[19]); }
         };

         template <typename T_>
         struct invoke<T_,19>
         {
            static inline T_ execute(ifunction& f, T_ (&v)[19])
            { return f(v[0],v[1],v[2],v[3],v[4],v[5],v[6],v[7],v[8],v[9],v[10],v[11],v[12],v[13],v[14],v[15],v[16],v[17],v[18]); }
         };

         template <typename T_>
         struct invoke<T_,18>
         {
            static inline T_ execute(ifunction& f, T_ (&v)[18])
            { return f(v[0], v[1], v[2], v[3], v[4], v[5], v[6], v[7], v[8], v[9], v[10], v[11], v[12], v[13], v[14], v[15], v[16], v[17]); }
         };

         template <typename T_>
         struct invoke<T_,17>
         {
            static inline T_ execute(ifunction& f, T_ (&v)[17])
            { return f(v[0], v[1], v[2], v[3], v[4], v[5], v[6], v[7], v[8], v[9], v[10], v[11], v[12], v[13], v[14], v[15], v[16]); }
         };

         template <typename T_>
         struct invoke<T_,16>
         {
            static inline T_ execute(ifunction& f, T_ (&v)[16])
            { return f(v[0], v[1], v[2], v[3], v[4], v[5], v[6], v[7], v[8], v[9], v[10], v[11], v[12], v[13], v[14], v[15]); }
         };

         template <typename T_>
         struct invoke<T_,15>
         {
            static inline T_ execute(ifunction& f, T_ (&v)[15])
            { return f(v[0], v[1], v[2], v[3], v[4], v[5], v[6], v[7], v[8], v[9], v[10], v[11], v[12], v[13], v[14]); }
         };

         template <typename T_>
         struct invoke<T_,14>
         {
            static inline T_ execute(ifunction& f, T_ (&v)[14])
            { return f(v[0], v[1], v[2], v[3], v[4], v[5], v[6], v[7], v[8], v[9], v[10], v[11], v[12], v[13]); }
         };

         template <typename T_>
         struct invoke<T_,13>
         {
            static inline T_ execute(ifunction& f, T_ (&v)[13])
            { return f(v[0], v[1], v[2], v[3], v[4], v[5], v[6], v[7], v[8], v[9], v[10], v[11], v[12]); }
         };

         template <typename T_>
         struct invoke<T_,12>
         {
            static inline T_ execute(ifunction& f, T_ (&v)[12])
            { return f(v[0], v[1], v[2], v[3], v[4], v[5], v[6], v[7], v[8], v[9], v[10], v[11]); }
         };

         template <typename T_>
         struct invoke<T_,11>
         {
            static inline T_ execute(ifunction& f, T_ (&v)[11])
            { return f(v[0], v[1], v[2], v[3], v[4], v[5], v[6], v[7], v[8], v[9], v[10]); }
         };

         template <typename T_>
         struct invoke<T_,10>
         {
            static inline T_ execute(ifunction& f, T_ (&v)[10])
            { return f(v[0], v[1], v[2], v[3], v[4], v[5], v[6], v[7], v[8], v[9]); }
         };

         template <typename T_>
         struct invoke<T_,9>
         {
            static inline T_ execute(ifunction& f, T_ (&v)[9])
            { return f(v[0], v[1], v[2], v[3], v[4], v[5], v[6], v[7], v[8]); }
         };

         template <typename T_>
         struct invoke<T_,8>
         {
            static inline T_ execute(ifunction& f, T_ (&v)[8])
            { return f(v[0], v[1], v[2], v[3], v[4], v[5], v[6], v[7]); }
         };

         template <typename T_>
         struct invoke<T_,7>
         {
            static inline T_ execute(ifunction& f, T_ (&v)[7])
            { return f(v[0], v[1], v[2], v[3], v[4], v[5], v[6]); }
         };

         template <typename T_>
         struct invoke<T_,6>
         {
            static inline T_ execute(ifunction& f, T_ (&v)[6])
            { return f(v[0], v[1], v[2], v[3], v[4], v[5]); }
         };

         template <typename T_>
         struct invoke<T_,5>
         {
            static inline T_ execute(ifunction& f, T_ (&v)[5])
            { return f(v[0], v[1], v[2], v[3], v[4]); }
         };

         template <typename T_>
         struct invoke<T_,4>
         {
            static inline T_ execute(ifunction& f, T_ (&v)[4])
            { return f(v[0], v[1], v[2], v[3]); }
         };

         template <typename T_>
         struct invoke<T_,3>
         {
            static inline T_ execute(ifunction& f, T_ (&v)[3])
            { return f(v[0], v[1], v[2]); }
         };

         template <typename T_>
         struct invoke<T_,2>
         {
            static inline T_ execute(ifunction& f, T_ (&v)[2])
            { return f(v[0], v[1]); }
         };

         template <typename T_>
         struct invoke<T_,1>
         {
            static inline T_ execute(ifunction& f, T_ (&v)[1])
            { return f(v[0]); }
         };

      private:

         ifunction*  function_;
         std::size_t parameter_count_;
         branch_t    branch_[N];
      };

      template <typename T, typename IFunction>
      class function_N_node<T,IFunction,0> exprtk_final : public expression_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef IFunction ifunction;

         explicit function_N_node(ifunction* func)
         : function_((0 == func->param_count) ? func : reinterpret_cast<ifunction*>(0))
         {}

         inline bool operator <(const function_N_node<T,IFunction,0>& fn) const
         {
            return this < (&fn);
         }

         inline T value() const
         {
            if (function_)
               return (*function_)();
            else
               return std::numeric_limits<T>::quiet_NaN();
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_function;
         }

      private:

         ifunction* function_;
      };

      template <typename T, typename VarArgFunction>
      class vararg_function_node exprtk_final : public expression_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;

         vararg_function_node(VarArgFunction*  func,
                              const std::vector<expression_ptr>& arg_list)
         : function_(func)
         , arg_list_(arg_list)
         {
            value_list_.resize(arg_list.size(),std::numeric_limits<T>::quiet_NaN());
         }

         inline bool operator <(const vararg_function_node<T,VarArgFunction>& fn) const
         {
            return this < (&fn);
         }

         inline T value() const
         {
            if (function_)
            {
               populate_value_list();
               return (*function_)(value_list_);
            }
            else
               return std::numeric_limits<T>::quiet_NaN();
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_vafunction;
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            for (std::size_t i = 0; i < arg_list_.size(); ++i)
            {
               if (arg_list_[i] && !details::is_variable_node(arg_list_[i]))
               {
                  node_delete_list.push_back(&arg_list_[i]);
               }
            }
         }

         std::size_t node_depth() const
         {
            return expression_node<T>::ndb_t::compute_node_depth(arg_list_);
         }

      private:

         inline void populate_value_list() const
         {
            for (std::size_t i = 0; i < arg_list_.size(); ++i)
            {
               value_list_[i] = arg_list_[i]->value();
            }
         }

         VarArgFunction* function_;
         std::vector<expression_ptr> arg_list_;
         mutable std::vector<T> value_list_;
      };

      template <typename T, typename GenericFunction>
      class generic_function_node : public expression_node<T>
      {
      public:

         typedef type_store<T>                         type_store_t;
         typedef expression_node<T>*                 expression_ptr;
         typedef variable_node<T>                   variable_node_t;
         typedef vector_node<T>                       vector_node_t;
         typedef variable_node_t*               variable_node_ptr_t;
         typedef vector_node_t*                   vector_node_ptr_t;
         typedef range_interface<T>               range_interface_t;
         typedef range_data_type<T>               range_data_type_t;
         typedef range_pack<T>                              range_t;
         typedef std::pair<expression_ptr,bool>            branch_t;
         typedef std::pair<void*,std::size_t>                void_t;
         typedef std::vector<T>                            tmp_vs_t;
         typedef std::vector<type_store_t>         typestore_list_t;
         typedef std::vector<range_data_type_t>        range_list_t;

         explicit generic_function_node(const std::vector<expression_ptr>& arg_list,
                                        GenericFunction* func = reinterpret_cast<GenericFunction*>(0))
         : function_(func)
         , arg_list_(arg_list)
         {}

         virtual ~generic_function_node()
         {}

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::collect(branch_, node_delete_list);
         }

         std::size_t node_depth() const exprtk_final
         {
            return expression_node<T>::ndb_t::compute_node_depth(branch_);
         }

         virtual bool init_branches()
         {
            expr_as_vec1_store_.resize(arg_list_.size(),T(0)               );
            typestore_list_    .resize(arg_list_.size(),type_store_t()     );
            range_list_        .resize(arg_list_.size(),range_data_type_t());
            branch_            .resize(arg_list_.size(),branch_t(reinterpret_cast<expression_ptr>(0),false));

            for (std::size_t i = 0; i < arg_list_.size(); ++i)
            {
               type_store_t& ts = typestore_list_[i];

               if (0 == arg_list_[i])
                  return false;
               else if (is_ivector_node(arg_list_[i]))
               {
                  vector_interface<T>* vi = reinterpret_cast<vector_interface<T>*>(0);

                  if (0 == (vi = dynamic_cast<vector_interface<T>*>(arg_list_[i])))
                     return false;

                  ts.size = vi->size();
                  ts.data = vi->vds().data();
                  ts.type = type_store_t::e_vector;
                  vi->vec()->vec_holder().set_ref(&ts.vec_data);
               }
               #ifndef exprtk_disable_string_capabilities
               else if (is_generally_string_node(arg_list_[i]))
               {
                  string_base_node<T>* sbn = reinterpret_cast<string_base_node<T>*>(0);

                  if (0 == (sbn = dynamic_cast<string_base_node<T>*>(arg_list_[i])))
                     return false;

                  ts.size = sbn->size();
                  ts.data = reinterpret_cast<void*>(const_cast<char_ptr>(sbn->base()));
                  ts.type = type_store_t::e_string;

                  range_list_[i].data      = ts.data;
                  range_list_[i].size      = ts.size;
                  range_list_[i].type_size = sizeof(char);
                  range_list_[i].str_node  = sbn;

                  range_interface_t* ri = reinterpret_cast<range_interface_t*>(0);

                  if (0 == (ri = dynamic_cast<range_interface_t*>(arg_list_[i])))
                     return false;

                  const range_t& rp = ri->range_ref();

                  if (
                       rp.const_range() &&
                       is_const_string_range_node(arg_list_[i])
                     )
                  {
                     ts.size = rp.const_size();
                     ts.data = static_cast<char_ptr>(ts.data) + rp.n0_c.second;
                     range_list_[i].range = reinterpret_cast<range_t*>(0);
                  }
                  else
                     range_list_[i].range = &(ri->range_ref());
               }
               #endif
               else if (is_variable_node(arg_list_[i]))
               {
                  variable_node_ptr_t var = variable_node_ptr_t(0);

                  if (0 == (var = dynamic_cast<variable_node_ptr_t>(arg_list_[i])))
                     return false;

                  ts.size = 1;
                  ts.data = &var->ref();
                  ts.type = type_store_t::e_scalar;
               }
               else
               {
                  ts.size = 1;
                  ts.data = reinterpret_cast<void*>(&expr_as_vec1_store_[i]);
                  ts.type = type_store_t::e_scalar;
               }

               branch_[i] = std::make_pair(arg_list_[i],branch_deletable(arg_list_[i]));
            }

            return true;
         }

         inline bool operator <(const generic_function_node<T,GenericFunction>& fn) const
         {
            return this < (&fn);
         }

         inline T value() const
         {
            if (function_)
            {
               if (populate_value_list())
               {
                  typedef typename GenericFunction::parameter_list_t parameter_list_t;

                  return (*function_)(parameter_list_t(typestore_list_));
               }
            }

            return std::numeric_limits<T>::quiet_NaN();
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_genfunction;
         }

      protected:

         inline virtual bool populate_value_list() const
         {
            for (std::size_t i = 0; i < branch_.size(); ++i)
            {
               expr_as_vec1_store_[i] = branch_[i].first->value();
            }

            for (std::size_t i = 0; i < branch_.size(); ++i)
            {
               range_data_type_t& rdt = range_list_[i];

               if (rdt.range)
               {
                  const range_t& rp = (*rdt.range);
                  std::size_t r0    = 0;
                  std::size_t r1    = 0;

                  if (rp(r0, r1, rdt.size))
                  {
                     type_store_t& ts = typestore_list_[i];

                     ts.size = rp.cache_size();
                     #ifndef exprtk_disable_string_capabilities
                     if (ts.type == type_store_t::e_string)
                        ts.data = const_cast<char_ptr>(rdt.str_node->base()) + rp.cache.first;
                     else
                     #endif
                        ts.data = static_cast<char_ptr>(rdt.data) + (rp.cache.first * rdt.type_size);
                  }
                  else
                     return false;
               }
            }

            return true;
         }

         GenericFunction* function_;
         mutable typestore_list_t typestore_list_;

      private:

         std::vector<expression_ptr> arg_list_;
         std::vector<branch_t>         branch_;
         mutable tmp_vs_t  expr_as_vec1_store_;
         mutable range_list_t      range_list_;
      };

      #ifndef exprtk_disable_string_capabilities
      template <typename T, typename StringFunction>
      class string_function_node : public generic_function_node<T,StringFunction>,
                                   public string_base_node<T>,
                                   public range_interface <T>
      {
      public:

         typedef generic_function_node<T,StringFunction> gen_function_t;
         typedef range_pack<T> range_t;

         string_function_node(StringFunction* func,
                              const std::vector<typename gen_function_t::expression_ptr>& arg_list)
         : gen_function_t(arg_list,func)
         {
            range_.n0_c = std::make_pair<bool,std::size_t>(true,0);
            range_.n1_c = std::make_pair<bool,std::size_t>(true,0);
            range_.cache.first  = range_.n0_c.second;
            range_.cache.second = range_.n1_c.second;
         }

         inline bool operator <(const string_function_node<T,StringFunction>& fn) const
         {
            return this < (&fn);
         }

         inline T value() const
         {
            if (gen_function_t::function_)
            {
               if (gen_function_t::populate_value_list())
               {
                  typedef typename StringFunction::parameter_list_t parameter_list_t;

                  const T result = (*gen_function_t::function_)
                                      (
                                        ret_string_,
                                        parameter_list_t(gen_function_t::typestore_list_)
                                      );

                  range_.n1_c.second  = ret_string_.size() - 1;
                  range_.cache.second = range_.n1_c.second;

                  return result;
               }
            }

            return std::numeric_limits<T>::quiet_NaN();
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_strfunction;
         }

         std::string str() const
         {
            return ret_string_;
         }

         char_cptr base() const
         {
           return &ret_string_[0];
         }

         std::size_t size() const
         {
            return ret_string_.size();
         }

         range_t& range_ref()
         {
            return range_;
         }

         const range_t& range_ref() const
         {
            return range_;
         }

      protected:

         mutable range_t     range_;
         mutable std::string ret_string_;
      };
      #endif

      template <typename T, typename GenericFunction>
      class multimode_genfunction_node : public generic_function_node<T,GenericFunction>
      {
      public:

         typedef generic_function_node<T,GenericFunction> gen_function_t;
         typedef range_pack<T> range_t;

         multimode_genfunction_node(GenericFunction* func,
                                    const std::size_t& param_seq_index,
                                    const std::vector<typename gen_function_t::expression_ptr>& arg_list)
         : gen_function_t(arg_list,func)
         , param_seq_index_(param_seq_index)
         {}

         inline T value() const
         {
            if (gen_function_t::function_)
            {
               if (gen_function_t::populate_value_list())
               {
                  typedef typename GenericFunction::parameter_list_t parameter_list_t;

                  return (*gen_function_t::function_)
                            (
                              param_seq_index_,
                              parameter_list_t(gen_function_t::typestore_list_)
                            );
               }
            }

            return std::numeric_limits<T>::quiet_NaN();
         }

         inline typename expression_node<T>::node_type type() const exprtk_final
         {
            return expression_node<T>::e_genfunction;
         }

      private:

         std::size_t param_seq_index_;
      };

      #ifndef exprtk_disable_string_capabilities
      template <typename T, typename StringFunction>
      class multimode_strfunction_node exprtk_final : public string_function_node<T,StringFunction>
      {
      public:

         typedef string_function_node<T,StringFunction> str_function_t;
         typedef range_pack<T> range_t;

         multimode_strfunction_node(StringFunction* func,
                                    const std::size_t& param_seq_index,
                                    const std::vector<typename str_function_t::expression_ptr>& arg_list)
         : str_function_t(func,arg_list)
         , param_seq_index_(param_seq_index)
         {}

         inline T value() const
         {
            if (str_function_t::function_)
            {
               if (str_function_t::populate_value_list())
               {
                  typedef typename StringFunction::parameter_list_t parameter_list_t;

                  const T result = (*str_function_t::function_)
                                      (
                                        param_seq_index_,
                                        str_function_t::ret_string_,
                                        parameter_list_t(str_function_t::typestore_list_)
                                      );

                  str_function_t::range_.n1_c.second  = str_function_t::ret_string_.size() - 1;
                  str_function_t::range_.cache.second = str_function_t::range_.n1_c.second;

                  return result;
               }
            }

            return std::numeric_limits<T>::quiet_NaN();
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_strfunction;
         }

      private:

         const std::size_t param_seq_index_;
      };
      #endif

      class return_exception
      {};

      template <typename T>
      class null_igenfunc
      {
      public:

         virtual ~null_igenfunc()
         {}

         typedef type_store<T> generic_type;
         typedef typename generic_type::parameter_list parameter_list_t;

         inline virtual T operator() (parameter_list_t)
         {
            return std::numeric_limits<T>::quiet_NaN();
         }
      };

      #ifndef exprtk_disable_return_statement
      template <typename T>
      class return_node exprtk_final : public generic_function_node<T,null_igenfunc<T> >
      {
      public:

         typedef null_igenfunc<T> igeneric_function_t;
         typedef igeneric_function_t* igeneric_function_ptr;
         typedef generic_function_node<T,igeneric_function_t> gen_function_t;
         typedef results_context<T> results_context_t;

         return_node(const std::vector<typename gen_function_t::expression_ptr>& arg_list,
                     results_context_t& rc)
         : gen_function_t  (arg_list)
         , results_context_(&rc)
         {}

         inline T value() const
         {
            if (
                 (0 != results_context_) &&
                 gen_function_t::populate_value_list()
               )
            {
               typedef typename type_store<T>::parameter_list parameter_list_t;

               results_context_->
                  assign(parameter_list_t(gen_function_t::typestore_list_));

               throw return_exception();
            }

            return std::numeric_limits<T>::quiet_NaN();
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_return;
         }

      private:

         results_context_t* results_context_;
      };

      template <typename T>
      class return_envelope_node exprtk_final : public expression_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef results_context<T>  results_context_t;
         typedef std::pair<expression_ptr,bool> branch_t;

         return_envelope_node(expression_ptr body, results_context_t& rc)
         : results_context_(&rc  )
         , return_invoked_ (false)
         {
            construct_branch_pair(body_, body);
         }

         inline T value() const
         {
            assert(body_.first);

            try
            {
               return_invoked_ = false;
               results_context_->clear();

               return body_.first->value();
            }
            catch(const return_exception&)
            {
               return_invoked_ = true;
               return std::numeric_limits<T>::quiet_NaN();
            }
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_retenv;
         }

         inline bool* retinvk_ptr()
         {
            return &return_invoked_;
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::collect(body_, node_delete_list);
         }

         std::size_t node_depth() const
         {
            return expression_node<T>::ndb_t::compute_node_depth(body_);
         }

      private:

         results_context_t* results_context_;
         mutable bool        return_invoked_;
         branch_t                      body_;
      };
      #endif

      #define exprtk_define_unary_op(OpName)                    \
      template <typename T>                                     \
      struct OpName##_op                                        \
      {                                                         \
         typedef typename functor_t<T>::Type Type;              \
         typedef typename expression_node<T>::node_type node_t; \
                                                                \
         static inline T process(Type v)                        \
         {                                                      \
            return numeric:: OpName (v);                        \
         }                                                      \
                                                                \
         static inline node_t type()                            \
         {                                                      \
            return expression_node<T>::e_##OpName;              \
         }                                                      \
                                                                \
         static inline details::operator_type operation()       \
         {                                                      \
            return details::e_##OpName;                         \
         }                                                      \
      };                                                        \

      exprtk_define_unary_op(abs  )
      exprtk_define_unary_op(acos )
      exprtk_define_unary_op(acosh)
      exprtk_define_unary_op(asin )
      exprtk_define_unary_op(asinh)
      exprtk_define_unary_op(atan )
      exprtk_define_unary_op(atanh)
      exprtk_define_unary_op(ceil )
      exprtk_define_unary_op(cos  )
      exprtk_define_unary_op(cosh )
      exprtk_define_unary_op(cot  )
      exprtk_define_unary_op(csc  )
      exprtk_define_unary_op(d2g  )
      exprtk_define_unary_op(d2r  )
      exprtk_define_unary_op(erf  )
      exprtk_define_unary_op(erfc )
      exprtk_define_unary_op(exp  )
      exprtk_define_unary_op(expm1)
      exprtk_define_unary_op(floor)
      exprtk_define_unary_op(frac )
      exprtk_define_unary_op(g2d  )
      exprtk_define_unary_op(log  )
      exprtk_define_unary_op(log10)
      exprtk_define_unary_op(log2 )
      exprtk_define_unary_op(log1p)
      exprtk_define_unary_op(ncdf )
      exprtk_define_unary_op(neg  )
      exprtk_define_unary_op(notl )
      exprtk_define_unary_op(pos  )
      exprtk_define_unary_op(r2d  )
      exprtk_define_unary_op(round)
      exprtk_define_unary_op(sec  )
      exprtk_define_unary_op(sgn  )
      exprtk_define_unary_op(sin  )
      exprtk_define_unary_op(sinc )
      exprtk_define_unary_op(sinh )
      exprtk_define_unary_op(sqrt )
      exprtk_define_unary_op(tan  )
      exprtk_define_unary_op(tanh )
      exprtk_define_unary_op(trunc)
      #undef exprtk_define_unary_op

      template <typename T>
      struct opr_base
      {
         typedef typename details::functor_t<T>::Type    Type;
         typedef typename details::functor_t<T>::RefType RefType;
         typedef typename details::functor_t<T>          functor_t;
         typedef typename functor_t::qfunc_t  quaternary_functor_t;
         typedef typename functor_t::tfunc_t     trinary_functor_t;
         typedef typename functor_t::bfunc_t      binary_functor_t;
         typedef typename functor_t::ufunc_t       unary_functor_t;
      };

      template <typename T>
      struct add_op : public opr_base<T>
      {
         typedef typename opr_base<T>::Type    Type;
         typedef typename opr_base<T>::RefType RefType;

         static inline T process(Type t1, Type t2) { return t1 + t2; }
         static inline T process(Type t1, Type t2, Type t3) { return t1 + t2 + t3; }
         static inline void assign(RefType t1, Type t2) { t1 += t2; }
         static inline typename expression_node<T>::node_type type() { return expression_node<T>::e_add; }
         static inline details::operator_type operation() { return details::e_add; }
      };

      template <typename T>
      struct mul_op : public opr_base<T>
      {
         typedef typename opr_base<T>::Type    Type;
         typedef typename opr_base<T>::RefType RefType;

         static inline T process(Type t1, Type t2) { return t1 * t2; }
         static inline T process(Type t1, Type t2, Type t3) { return t1 * t2 * t3; }
         static inline void assign(RefType t1, Type t2) { t1 *= t2; }
         static inline typename expression_node<T>::node_type type() { return expression_node<T>::e_mul; }
         static inline details::operator_type operation() { return details::e_mul; }
      };

      template <typename T>
      struct sub_op : public opr_base<T>
      {
         typedef typename opr_base<T>::Type    Type;
         typedef typename opr_base<T>::RefType RefType;

         static inline T process(Type t1, Type t2) { return t1 - t2; }
         static inline T process(Type t1, Type t2, Type t3) { return t1 - t2 - t3; }
         static inline void assign(RefType t1, Type t2) { t1 -= t2; }
         static inline typename expression_node<T>::node_type type() { return expression_node<T>::e_sub; }
         static inline details::operator_type operation() { return details::e_sub; }
      };

      template <typename T>
      struct div_op : public opr_base<T>
      {
         typedef typename opr_base<T>::Type    Type;
         typedef typename opr_base<T>::RefType RefType;

         static inline T process(Type t1, Type t2) { return t1 / t2; }
         static inline T process(Type t1, Type t2, Type t3) { return t1 / t2 / t3; }
         static inline void assign(RefType t1, Type t2) { t1 /= t2; }
         static inline typename expression_node<T>::node_type type() { return expression_node<T>::e_div; }
         static inline details::operator_type operation() { return details::e_div; }
      };

      template <typename T>
      struct mod_op : public opr_base<T>
      {
         typedef typename opr_base<T>::Type    Type;
         typedef typename opr_base<T>::RefType RefType;

         static inline T process(Type t1, Type t2) { return numeric::modulus<T>(t1,t2); }
         static inline void assign(RefType t1, Type t2) { t1 = numeric::modulus<T>(t1,t2); }
         static inline typename expression_node<T>::node_type type() { return expression_node<T>::e_mod; }
         static inline details::operator_type operation() { return details::e_mod; }
      };

      template <typename T>
      struct pow_op : public opr_base<T>
      {
         typedef typename opr_base<T>::Type    Type;
         typedef typename opr_base<T>::RefType RefType;

         static inline T process(Type t1, Type t2) { return numeric::pow<T>(t1,t2); }
         static inline void assign(RefType t1, Type t2) { t1 = numeric::pow<T>(t1,t2); }
         static inline typename expression_node<T>::node_type type() { return expression_node<T>::e_pow; }
         static inline details::operator_type operation() { return details::e_pow; }
      };

      template <typename T>
      struct lt_op : public opr_base<T>
      {
         typedef typename opr_base<T>::Type Type;

         static inline T process(Type t1, Type t2) { return ((t1 < t2) ? T(1) : T(0)); }
         static inline T process(const std::string& t1, const std::string& t2) { return ((t1 < t2) ? T(1) : T(0)); }
         static inline typename expression_node<T>::node_type type() { return expression_node<T>::e_lt; }
         static inline details::operator_type operation() { return details::e_lt; }
      };

      template <typename T>
      struct lte_op : public opr_base<T>
      {
         typedef typename opr_base<T>::Type Type;

         static inline T process(Type t1, Type t2) { return ((t1 <= t2) ? T(1) : T(0)); }
         static inline T process(const std::string& t1, const std::string& t2) { return ((t1 <= t2) ? T(1) : T(0)); }
         static inline typename expression_node<T>::node_type type() { return expression_node<T>::e_lte; }
         static inline details::operator_type operation() { return details::e_lte; }
      };

      template <typename T>
      struct gt_op : public opr_base<T>
      {
         typedef typename opr_base<T>::Type Type;

         static inline T process(Type t1, Type t2) { return ((t1 > t2) ? T(1) : T(0)); }
         static inline T process(const std::string& t1, const std::string& t2) { return ((t1 > t2) ? T(1) : T(0)); }
         static inline typename expression_node<T>::node_type type() { return expression_node<T>::e_gt; }
         static inline details::operator_type operation() { return details::e_gt; }
      };

      template <typename T>
      struct gte_op : public opr_base<T>
      {
         typedef typename opr_base<T>::Type Type;

         static inline T process(Type t1, Type t2) { return ((t1 >= t2) ? T(1) : T(0)); }
         static inline T process(const std::string& t1, const std::string& t2) { return ((t1 >= t2) ? T(1) : T(0)); }
         static inline typename expression_node<T>::node_type type() { return expression_node<T>::e_gte; }
         static inline details::operator_type operation() { return details::e_gte; }
      };

      template <typename T>
      struct eq_op : public opr_base<T>
      {
         typedef typename opr_base<T>::Type Type;
         static inline T process(Type t1, Type t2) { return (std::equal_to<T>()(t1,t2) ? T(1) : T(0)); }
         static inline T process(const std::string& t1, const std::string& t2) { return ((t1 == t2) ? T(1) : T(0)); }
         static inline typename expression_node<T>::node_type type() { return expression_node<T>::e_eq; }
         static inline details::operator_type operation() { return details::e_eq; }
      };

      template <typename T>
      struct equal_op : public opr_base<T>
      {
         typedef typename opr_base<T>::Type Type;

         static inline T process(Type t1, Type t2) { return numeric::equal(t1,t2); }
         static inline T process(const std::string& t1, const std::string& t2) { return ((t1 == t2) ? T(1) : T(0)); }
         static inline typename expression_node<T>::node_type type() { return expression_node<T>::e_eq; }
         static inline details::operator_type operation() { return details::e_equal; }
      };

      template <typename T>
      struct ne_op : public opr_base<T>
      {
         typedef typename opr_base<T>::Type Type;

         static inline T process(Type t1, Type t2) { return (std::not_equal_to<T>()(t1,t2) ? T(1) : T(0)); }
         static inline T process(const std::string& t1, const std::string& t2) { return ((t1 != t2) ? T(1) : T(0)); }
         static inline typename expression_node<T>::node_type type() { return expression_node<T>::e_ne; }
         static inline details::operator_type operation() { return details::e_ne; }
      };

      template <typename T>
      struct and_op : public opr_base<T>
      {
         typedef typename opr_base<T>::Type Type;

         static inline T process(Type t1, Type t2) { return (details::is_true(t1) && details::is_true(t2)) ? T(1) : T(0); }
         static inline typename expression_node<T>::node_type type() { return expression_node<T>::e_and; }
         static inline details::operator_type operation() { return details::e_and; }
      };

      template <typename T>
      struct nand_op : public opr_base<T>
      {
         typedef typename opr_base<T>::Type Type;

         static inline T process(Type t1, Type t2) { return (details::is_true(t1) && details::is_true(t2)) ? T(0) : T(1); }
         static inline typename expression_node<T>::node_type type() { return expression_node<T>::e_nand; }
         static inline details::operator_type operation() { return details::e_nand; }
      };

      template <typename T>
      struct or_op : public opr_base<T>
      {
         typedef typename opr_base<T>::Type Type;

         static inline T process(Type t1, Type t2) { return (details::is_true(t1) || details::is_true(t2)) ? T(1) : T(0); }
         static inline typename expression_node<T>::node_type type() { return expression_node<T>::e_or; }
         static inline details::operator_type operation() { return details::e_or; }
      };

      template <typename T>
      struct nor_op : public opr_base<T>
      {
         typedef typename opr_base<T>::Type Type;

         static inline T process(Type t1, Type t2) { return (details::is_true(t1) || details::is_true(t2)) ? T(0) : T(1); }
         static inline typename expression_node<T>::node_type type() { return expression_node<T>::e_nor; }
         static inline details::operator_type operation() { return details::e_nor; }
      };

      template <typename T>
      struct xor_op : public opr_base<T>
      {
         typedef typename opr_base<T>::Type Type;

         static inline T process(Type t1, Type t2) { return numeric::xor_opr<T>(t1,t2); }
         static inline typename expression_node<T>::node_type type() { return expression_node<T>::e_nor; }
         static inline details::operator_type operation() { return details::e_xor; }
      };

      template <typename T>
      struct xnor_op : public opr_base<T>
      {
         typedef typename opr_base<T>::Type Type;

         static inline T process(Type t1, Type t2) { return numeric::xnor_opr<T>(t1,t2); }
         static inline typename expression_node<T>::node_type type() { return expression_node<T>::e_nor; }
         static inline details::operator_type operation() { return details::e_xnor; }
      };

      template <typename T>
      struct in_op : public opr_base<T>
      {
         typedef typename opr_base<T>::Type Type;

         static inline T process(const T&, const T&) { return std::numeric_limits<T>::quiet_NaN(); }
         static inline T process(const std::string& t1, const std::string& t2) { return ((std::string::npos != t2.find(t1)) ? T(1) : T(0)); }
         static inline typename expression_node<T>::node_type type() { return expression_node<T>::e_in; }
         static inline details::operator_type operation() { return details::e_in; }
      };

      template <typename T>
      struct like_op : public opr_base<T>
      {
         typedef typename opr_base<T>::Type Type;

         static inline T process(const T&, const T&) { return std::numeric_limits<T>::quiet_NaN(); }
         static inline T process(const std::string& t1, const std::string& t2) { return (details::wc_match(t2,t1) ? T(1) : T(0)); }
         static inline typename expression_node<T>::node_type type() { return expression_node<T>::e_like; }
         static inline details::operator_type operation() { return details::e_like; }
      };

      template <typename T>
      struct ilike_op : public opr_base<T>
      {
         typedef typename opr_base<T>::Type Type;

         static inline T process(const T&, const T&) { return std::numeric_limits<T>::quiet_NaN(); }
         static inline T process(const std::string& t1, const std::string& t2) { return (details::wc_imatch(t2,t1) ? T(1) : T(0)); }
         static inline typename expression_node<T>::node_type type() { return expression_node<T>::e_ilike; }
         static inline details::operator_type operation() { return details::e_ilike; }
      };

      template <typename T>
      struct inrange_op : public opr_base<T>
      {
         typedef typename opr_base<T>::Type Type;

         static inline T process(const T& t0, const T& t1, const T& t2) { return ((t0 <= t1) && (t1 <= t2)) ? T(1) : T(0); }
         static inline T process(const std::string& t0, const std::string& t1, const std::string& t2)
         {
            return ((t0 <= t1) && (t1 <= t2)) ? T(1) : T(0);
         }
         static inline typename expression_node<T>::node_type type() { return expression_node<T>::e_inranges; }
         static inline details::operator_type operation() { return details::e_inrange; }
      };

      template <typename T>
      inline T value(details::expression_node<T>* n)
      {
         return n->value();
      }

      template <typename T>
      inline T value(std::pair<details::expression_node<T>*,bool> n)
      {
         return n.first->value();
      }

      template <typename T>
      inline T value(const T* t)
      {
         return (*t);
      }

      template <typename T>
      inline T value(const T& t)
      {
         return t;
      }

      template <typename T>
      struct vararg_add_op : public opr_base<T>
      {
         typedef typename opr_base<T>::Type Type;

         template <typename Type,
                   typename Allocator,
                   template <typename, typename> class Sequence>
         static inline T process(const Sequence<Type,Allocator>& arg_list)
         {
            switch (arg_list.size())
            {
               case 0  : return T(0);
               case 1  : return process_1(arg_list);
               case 2  : return process_2(arg_list);
               case 3  : return process_3(arg_list);
               case 4  : return process_4(arg_list);
               case 5  : return process_5(arg_list);
               default :
                         {
                            T result = T(0);

                            for (std::size_t i = 0; i < arg_list.size(); ++i)
                            {
                              result += value(arg_list[i]);
                            }

                            return result;
                         }
            }
         }

         template <typename Sequence>
         static inline T process_1(const Sequence& arg_list)
         {
            return value(arg_list[0]);
         }

         template <typename Sequence>
         static inline T process_2(const Sequence& arg_list)
         {
            return value(arg_list[0]) + value(arg_list[1]);
         }

         template <typename Sequence>
         static inline T process_3(const Sequence& arg_list)
         {
            return value(arg_list[0]) + value(arg_list[1]) +
                   value(arg_list[2]) ;
         }

         template <typename Sequence>
         static inline T process_4(const Sequence& arg_list)
         {
            return value(arg_list[0]) + value(arg_list[1]) +
                   value(arg_list[2]) + value(arg_list[3]) ;
         }

         template <typename Sequence>
         static inline T process_5(const Sequence& arg_list)
         {
            return value(arg_list[0]) + value(arg_list[1]) +
                   value(arg_list[2]) + value(arg_list[3]) +
                   value(arg_list[4]) ;
         }
      };

      template <typename T>
      struct vararg_mul_op : public opr_base<T>
      {
         typedef typename opr_base<T>::Type Type;

         template <typename Type,
                   typename Allocator,
                   template <typename, typename> class Sequence>
         static inline T process(const Sequence<Type,Allocator>& arg_list)
         {
            switch (arg_list.size())
            {
               case 0  : return T(0);
               case 1  : return process_1(arg_list);
               case 2  : return process_2(arg_list);
               case 3  : return process_3(arg_list);
               case 4  : return process_4(arg_list);
               case 5  : return process_5(arg_list);
               default :
                         {
                            T result = T(value(arg_list[0]));

                            for (std::size_t i = 1; i < arg_list.size(); ++i)
                            {
                               result *= value(arg_list[i]);
                            }

                            return result;
                         }
            }
         }

         template <typename Sequence>
         static inline T process_1(const Sequence& arg_list)
         {
            return value(arg_list[0]);
         }

         template <typename Sequence>
         static inline T process_2(const Sequence& arg_list)
         {
            return value(arg_list[0]) * value(arg_list[1]);
         }

         template <typename Sequence>
         static inline T process_3(const Sequence& arg_list)
         {
            return value(arg_list[0]) * value(arg_list[1]) *
                   value(arg_list[2]) ;
         }

         template <typename Sequence>
         static inline T process_4(const Sequence& arg_list)
         {
            return value(arg_list[0]) * value(arg_list[1]) *
                   value(arg_list[2]) * value(arg_list[3]) ;
         }

         template <typename Sequence>
         static inline T process_5(const Sequence& arg_list)
         {
            return value(arg_list[0]) * value(arg_list[1]) *
                   value(arg_list[2]) * value(arg_list[3]) *
                   value(arg_list[4]) ;
         }
      };

      template <typename T>
      struct vararg_avg_op : public opr_base<T>
      {
         typedef typename opr_base<T>::Type Type;

         template <typename Type,
                   typename Allocator,
                   template <typename, typename> class Sequence>
         static inline T process(const Sequence<Type,Allocator>& arg_list)
         {
            switch (arg_list.size())
            {
               case 0  : return T(0);
               case 1  : return process_1(arg_list);
               case 2  : return process_2(arg_list);
               case 3  : return process_3(arg_list);
               case 4  : return process_4(arg_list);
               case 5  : return process_5(arg_list);
               default : return vararg_add_op<T>::process(arg_list) / arg_list.size();
            }
         }

         template <typename Sequence>
         static inline T process_1(const Sequence& arg_list)
         {
            return value(arg_list[0]);
         }

         template <typename Sequence>
         static inline T process_2(const Sequence& arg_list)
         {
            return (value(arg_list[0]) + value(arg_list[1])) / T(2);
         }

         template <typename Sequence>
         static inline T process_3(const Sequence& arg_list)
         {
            return (value(arg_list[0]) + value(arg_list[1]) + value(arg_list[2])) / T(3);
         }

         template <typename Sequence>
         static inline T process_4(const Sequence& arg_list)
         {
            return (value(arg_list[0]) + value(arg_list[1]) +
                    value(arg_list[2]) + value(arg_list[3])) / T(4);
         }

         template <typename Sequence>
         static inline T process_5(const Sequence& arg_list)
         {
            return (value(arg_list[0]) + value(arg_list[1]) +
                    value(arg_list[2]) + value(arg_list[3]) +
                    value(arg_list[4])) / T(5);
         }
      };

      template <typename T>
      struct vararg_min_op : public opr_base<T>
      {
         typedef typename opr_base<T>::Type Type;

         template <typename Type,
                   typename Allocator,
                   template <typename, typename> class Sequence>
         static inline T process(const Sequence<Type,Allocator>& arg_list)
         {
            switch (arg_list.size())
            {
               case 0  : return T(0);
               case 1  : return process_1(arg_list);
               case 2  : return process_2(arg_list);
               case 3  : return process_3(arg_list);
               case 4  : return process_4(arg_list);
               case 5  : return process_5(arg_list);
               default :
                         {
                            T result = T(value(arg_list[0]));

                            for (std::size_t i = 1; i < arg_list.size(); ++i)
                            {
                               const T v = value(arg_list[i]);

                               if (v < result)
                                  result = v;
                            }

                            return result;
                         }
            }
         }

         template <typename Sequence>
         static inline T process_1(const Sequence& arg_list)
         {
            return value(arg_list[0]);
         }

         template <typename Sequence>
         static inline T process_2(const Sequence& arg_list)
         {
            return std::min<T>(value(arg_list[0]),value(arg_list[1]));
         }

         template <typename Sequence>
         static inline T process_3(const Sequence& arg_list)
         {
            return std::min<T>(std::min<T>(value(arg_list[0]),value(arg_list[1])),value(arg_list[2]));
         }

         template <typename Sequence>
         static inline T process_4(const Sequence& arg_list)
         {
            return std::min<T>(
                        std::min<T>(value(arg_list[0]), value(arg_list[1])),
                        std::min<T>(value(arg_list[2]), value(arg_list[3])));
         }

         template <typename Sequence>
         static inline T process_5(const Sequence& arg_list)
         {
            return std::min<T>(
                   std::min<T>(std::min<T>(value(arg_list[0]), value(arg_list[1])),
                               std::min<T>(value(arg_list[2]), value(arg_list[3]))),
                               value(arg_list[4]));
         }
      };

      template <typename T>
      struct vararg_max_op : public opr_base<T>
      {
         typedef typename opr_base<T>::Type Type;

         template <typename Type,
                   typename Allocator,
                   template <typename, typename> class Sequence>
         static inline T process(const Sequence<Type,Allocator>& arg_list)
         {
            switch (arg_list.size())
            {
               case 0  : return T(0);
               case 1  : return process_1(arg_list);
               case 2  : return process_2(arg_list);
               case 3  : return process_3(arg_list);
               case 4  : return process_4(arg_list);
               case 5  : return process_5(arg_list);
               default :
                         {
                            T result = T(value(arg_list[0]));

                            for (std::size_t i = 1; i < arg_list.size(); ++i)
                            {
                               const T v = value(arg_list[i]);

                               if (v > result)
                                  result = v;
                            }

                            return result;
                         }
            }
         }

         template <typename Sequence>
         static inline T process_1(const Sequence& arg_list)
         {
            return value(arg_list[0]);
         }

         template <typename Sequence>
         static inline T process_2(const Sequence& arg_list)
         {
            return std::max<T>(value(arg_list[0]),value(arg_list[1]));
         }

         template <typename Sequence>
         static inline T process_3(const Sequence& arg_list)
         {
            return std::max<T>(std::max<T>(value(arg_list[0]),value(arg_list[1])),value(arg_list[2]));
         }

         template <typename Sequence>
         static inline T process_4(const Sequence& arg_list)
         {
            return std::max<T>(
                        std::max<T>(value(arg_list[0]), value(arg_list[1])),
                        std::max<T>(value(arg_list[2]), value(arg_list[3])));
         }

         template <typename Sequence>
         static inline T process_5(const Sequence& arg_list)
         {
            return std::max<T>(
                   std::max<T>(std::max<T>(value(arg_list[0]), value(arg_list[1])),
                               std::max<T>(value(arg_list[2]), value(arg_list[3]))),
                               value(arg_list[4]));
         }
      };

      template <typename T>
      struct vararg_mand_op : public opr_base<T>
      {
         typedef typename opr_base<T>::Type Type;

         template <typename Type,
                   typename Allocator,
                   template <typename, typename> class Sequence>
         static inline T process(const Sequence<Type,Allocator>& arg_list)
         {
            switch (arg_list.size())
            {
               case 1  : return process_1(arg_list);
               case 2  : return process_2(arg_list);
               case 3  : return process_3(arg_list);
               case 4  : return process_4(arg_list);
               case 5  : return process_5(arg_list);
               default :
                         {
                            for (std::size_t i = 0; i < arg_list.size(); ++i)
                            {
                               if (std::equal_to<T>()(T(0), value(arg_list[i])))
                                  return T(0);
                            }

                            return T(1);
                         }
            }
         }

         template <typename Sequence>
         static inline T process_1(const Sequence& arg_list)
         {
            return std::not_equal_to<T>()
                      (T(0), value(arg_list[0])) ? T(1) : T(0);
         }

         template <typename Sequence>
         static inline T process_2(const Sequence& arg_list)
         {
            return (
                     std::not_equal_to<T>()(T(0), value(arg_list[0])) &&
                     std::not_equal_to<T>()(T(0), value(arg_list[1]))
                   ) ? T(1) : T(0);
         }

         template <typename Sequence>
         static inline T process_3(const Sequence& arg_list)
         {
            return (
                     std::not_equal_to<T>()(T(0), value(arg_list[0])) &&
                     std::not_equal_to<T>()(T(0), value(arg_list[1])) &&
                     std::not_equal_to<T>()(T(0), value(arg_list[2]))
                   ) ? T(1) : T(0);
         }

         template <typename Sequence>
         static inline T process_4(const Sequence& arg_list)
         {
            return (
                     std::not_equal_to<T>()(T(0), value(arg_list[0])) &&
                     std::not_equal_to<T>()(T(0), value(arg_list[1])) &&
                     std::not_equal_to<T>()(T(0), value(arg_list[2])) &&
                     std::not_equal_to<T>()(T(0), value(arg_list[3]))
                   ) ? T(1) : T(0);
         }

         template <typename Sequence>
         static inline T process_5(const Sequence& arg_list)
         {
            return (
                     std::not_equal_to<T>()(T(0), value(arg_list[0])) &&
                     std::not_equal_to<T>()(T(0), value(arg_list[1])) &&
                     std::not_equal_to<T>()(T(0), value(arg_list[2])) &&
                     std::not_equal_to<T>()(T(0), value(arg_list[3])) &&
                     std::not_equal_to<T>()(T(0), value(arg_list[4]))
                   ) ? T(1) : T(0);
         }
      };

      template <typename T>
      struct vararg_mor_op : public opr_base<T>
      {
         typedef typename opr_base<T>::Type Type;

         template <typename Type,
                   typename Allocator,
                   template <typename, typename> class Sequence>
         static inline T process(const Sequence<Type,Allocator>& arg_list)
         {
            switch (arg_list.size())
            {
               case 1  : return process_1(arg_list);
               case 2  : return process_2(arg_list);
               case 3  : return process_3(arg_list);
               case 4  : return process_4(arg_list);
               case 5  : return process_5(arg_list);
               default :
                         {
                            for (std::size_t i = 0; i < arg_list.size(); ++i)
                            {
                               if (std::not_equal_to<T>()(T(0), value(arg_list[i])))
                                  return T(1);
                            }

                            return T(0);
                         }
            }
         }

         template <typename Sequence>
         static inline T process_1(const Sequence& arg_list)
         {
            return std::not_equal_to<T>()
                      (T(0), value(arg_list[0])) ? T(1) : T(0);
         }

         template <typename Sequence>
         static inline T process_2(const Sequence& arg_list)
         {
            return (
                     std::not_equal_to<T>()(T(0), value(arg_list[0])) ||
                     std::not_equal_to<T>()(T(0), value(arg_list[1]))
                   ) ? T(1) : T(0);
         }

         template <typename Sequence>
         static inline T process_3(const Sequence& arg_list)
         {
            return (
                     std::not_equal_to<T>()(T(0), value(arg_list[0])) ||
                     std::not_equal_to<T>()(T(0), value(arg_list[1])) ||
                     std::not_equal_to<T>()(T(0), value(arg_list[2]))
                   ) ? T(1) : T(0);
         }

         template <typename Sequence>
         static inline T process_4(const Sequence& arg_list)
         {
            return (
                     std::not_equal_to<T>()(T(0), value(arg_list[0])) ||
                     std::not_equal_to<T>()(T(0), value(arg_list[1])) ||
                     std::not_equal_to<T>()(T(0), value(arg_list[2])) ||
                     std::not_equal_to<T>()(T(0), value(arg_list[3]))
                   ) ? T(1) : T(0);
         }

         template <typename Sequence>
         static inline T process_5(const Sequence& arg_list)
         {
            return (
                     std::not_equal_to<T>()(T(0), value(arg_list[0])) ||
                     std::not_equal_to<T>()(T(0), value(arg_list[1])) ||
                     std::not_equal_to<T>()(T(0), value(arg_list[2])) ||
                     std::not_equal_to<T>()(T(0), value(arg_list[3])) ||
                     std::not_equal_to<T>()(T(0), value(arg_list[4]))
                   ) ? T(1) : T(0);
         }
      };

      template <typename T>
      struct vararg_multi_op : public opr_base<T>
      {
         typedef typename opr_base<T>::Type Type;

         template <typename Type,
                   typename Allocator,
                   template <typename, typename> class Sequence>
         static inline T process(const Sequence<Type,Allocator>& arg_list)
         {
            switch (arg_list.size())
            {
               case 0  : return std::numeric_limits<T>::quiet_NaN();
               case 1  : return process_1(arg_list);
               case 2  : return process_2(arg_list);
               case 3  : return process_3(arg_list);
               case 4  : return process_4(arg_list);
               case 5  : return process_5(arg_list);
               case 6  : return process_6(arg_list);
               case 7  : return process_7(arg_list);
               case 8  : return process_8(arg_list);
               default :
                         {
                            for (std::size_t i = 0; i < (arg_list.size() - 1); ++i)
                            {
                               value(arg_list[i]);
                            }

                            return value(arg_list.back());
                         }
            }
         }

         template <typename Sequence>
         static inline T process_1(const Sequence& arg_list)
         {
            return value(arg_list[0]);
         }

         template <typename Sequence>
         static inline T process_2(const Sequence& arg_list)
         {
                   value(arg_list[0]);
            return value(arg_list[1]);
         }

         template <typename Sequence>
         static inline T process_3(const Sequence& arg_list)
         {
                   value(arg_list[0]);
                   value(arg_list[1]);
            return value(arg_list[2]);
         }

         template <typename Sequence>
         static inline T process_4(const Sequence& arg_list)
         {
                   value(arg_list[0]);
                   value(arg_list[1]);
                   value(arg_list[2]);
            return value(arg_list[3]);
         }

         template <typename Sequence>
         static inline T process_5(const Sequence& arg_list)
         {
                   value(arg_list[0]);
                   value(arg_list[1]);
                   value(arg_list[2]);
                   value(arg_list[3]);
            return value(arg_list[4]);
         }

         template <typename Sequence>
         static inline T process_6(const Sequence& arg_list)
         {
                   value(arg_list[0]);
                   value(arg_list[1]);
                   value(arg_list[2]);
                   value(arg_list[3]);
                   value(arg_list[4]);
            return value(arg_list[5]);
         }

         template <typename Sequence>
         static inline T process_7(const Sequence& arg_list)
         {
                   value(arg_list[0]);
                   value(arg_list[1]);
                   value(arg_list[2]);
                   value(arg_list[3]);
                   value(arg_list[4]);
                   value(arg_list[5]);
            return value(arg_list[6]);
         }

         template <typename Sequence>
         static inline T process_8(const Sequence& arg_list)
         {
                   value(arg_list[0]);
                   value(arg_list[1]);
                   value(arg_list[2]);
                   value(arg_list[3]);
                   value(arg_list[4]);
                   value(arg_list[5]);
                   value(arg_list[6]);
            return value(arg_list[7]);
         }
      };

      template <typename T>
      struct vec_add_op
      {
         typedef vector_interface<T>* ivector_ptr;

         static inline T process(const ivector_ptr v)
         {
            const T* vec = v->vec()->vds().data();
            const std::size_t vec_size = v->vec()->vds().size();

            loop_unroll::details lud(vec_size);

            if (vec_size <= static_cast<std::size_t>(lud.batch_size))
            {
               T result = T(0);
               int i    = 0;

               exprtk_disable_fallthrough_begin
               switch (vec_size)
               {
                  #define case_stmt(N)         \
                  case N : result += vec[i++]; \

                  #ifndef exprtk_disable_superscalar_unroll
                  case_stmt(16) case_stmt(15)
                  case_stmt(14) case_stmt(13)
                  case_stmt(12) case_stmt(11)
                  case_stmt(10) case_stmt( 9)
                  case_stmt( 8) case_stmt( 7)
                  case_stmt( 6) case_stmt( 5)
                  #endif
                  case_stmt( 4) case_stmt( 3)
                  case_stmt( 2) case_stmt( 1)
               }
               exprtk_disable_fallthrough_end

               #undef case_stmt

               return result;
            }

            T r[] = {
                      T(0), T(0), T(0), T(0), T(0), T(0), T(0), T(0),
                      T(0), T(0), T(0), T(0), T(0), T(0), T(0), T(0)
                    };

            const T* upper_bound = vec + lud.upper_bound;

            while (vec < upper_bound)
            {
               #define exprtk_loop(N) \
               r[N] += vec[N];        \

               exprtk_loop( 0) exprtk_loop( 1)
               exprtk_loop( 2) exprtk_loop( 3)
               #ifndef exprtk_disable_superscalar_unroll
               exprtk_loop( 4) exprtk_loop( 5)
               exprtk_loop( 6) exprtk_loop( 7)
               exprtk_loop( 8) exprtk_loop( 9)
               exprtk_loop(10) exprtk_loop(11)
               exprtk_loop(12) exprtk_loop(13)
               exprtk_loop(14) exprtk_loop(15)
               #endif

               vec += lud.batch_size;
            }

            int i = 0;

            exprtk_disable_fallthrough_begin
            switch (lud.remainder)
            {
               #define case_stmt(N)       \
               case N : r[0] += vec[i++]; \

               #ifndef exprtk_disable_superscalar_unroll
               case_stmt(15) case_stmt(14)
               case_stmt(13) case_stmt(12)
               case_stmt(11) case_stmt(10)
               case_stmt( 9) case_stmt( 8)
               case_stmt( 7) case_stmt( 6)
               case_stmt( 5) case_stmt( 4)
               #endif
               case_stmt( 3) case_stmt( 2)
               case_stmt( 1)
            }
            exprtk_disable_fallthrough_end

            #undef exprtk_loop
            #undef case_stmt

            return (r[ 0] + r[ 1] + r[ 2] + r[ 3])
                   #ifndef exprtk_disable_superscalar_unroll
                 + (r[ 4] + r[ 5] + r[ 6] + r[ 7])
                 + (r[ 8] + r[ 9] + r[10] + r[11])
                 + (r[12] + r[13] + r[14] + r[15])
                   #endif
                   ;
         }
      };

      template <typename T>
      struct vec_mul_op
      {
         typedef vector_interface<T>* ivector_ptr;

         static inline T process(const ivector_ptr v)
         {
            const T* vec = v->vec()->vds().data();
            const std::size_t vec_size = v->vec()->vds().size();

            loop_unroll::details lud(vec_size);

            if (vec_size <= static_cast<std::size_t>(lud.batch_size))
            {
               T result = T(1);
               int i    = 0;

               exprtk_disable_fallthrough_begin
               switch (vec_size)
               {
                  #define case_stmt(N)         \
                  case N : result *= vec[i++]; \

                  #ifndef exprtk_disable_superscalar_unroll
                  case_stmt(16) case_stmt(15)
                  case_stmt(14) case_stmt(13)
                  case_stmt(12) case_stmt(11)
                  case_stmt(10) case_stmt( 9)
                  case_stmt( 8) case_stmt( 7)
                  case_stmt( 6) case_stmt( 5)
                  #endif
                  case_stmt( 4) case_stmt( 3)
                  case_stmt( 2) case_stmt( 1)
               }
               exprtk_disable_fallthrough_end

               #undef case_stmt

               return result;
            }

            T r[] = {
                      T(1), T(1), T(1), T(1), T(1), T(1), T(1), T(1),
                      T(1), T(1), T(1), T(1), T(1), T(1), T(1), T(1)
                    };

            const T* upper_bound = vec + lud.upper_bound;

            while (vec < upper_bound)
            {
               #define exprtk_loop(N) \
               r[N] *= vec[N];        \

               exprtk_loop( 0) exprtk_loop( 1)
               exprtk_loop( 2) exprtk_loop( 3)
               #ifndef exprtk_disable_superscalar_unroll
               exprtk_loop( 4) exprtk_loop( 5)
               exprtk_loop( 6) exprtk_loop( 7)
               exprtk_loop( 8) exprtk_loop( 9)
               exprtk_loop(10) exprtk_loop(11)
               exprtk_loop(12) exprtk_loop(13)
               exprtk_loop(14) exprtk_loop(15)
               #endif

               vec += lud.batch_size;
            }

            int i = 0;

            exprtk_disable_fallthrough_begin
            switch (lud.remainder)
            {
               #define case_stmt(N)       \
               case N : r[0] *= vec[i++]; \

               #ifndef exprtk_disable_superscalar_unroll
               case_stmt(15) case_stmt(14)
               case_stmt(13) case_stmt(12)
               case_stmt(11) case_stmt(10)
               case_stmt( 9) case_stmt( 8)
               case_stmt( 7) case_stmt( 6)
               case_stmt( 5) case_stmt( 4)
               #endif
               case_stmt( 3) case_stmt( 2)
               case_stmt( 1)
            }
            exprtk_disable_fallthrough_end

            #undef exprtk_loop
            #undef case_stmt

            return (r[ 0] * r[ 1] * r[ 2] * r[ 3])
                   #ifndef exprtk_disable_superscalar_unroll
                 + (r[ 4] * r[ 5] * r[ 6] * r[ 7])
                 + (r[ 8] * r[ 9] * r[10] * r[11])
                 + (r[12] * r[13] * r[14] * r[15])
                   #endif
                   ;
         }
      };

      template <typename T>
      struct vec_avg_op
      {
         typedef vector_interface<T>* ivector_ptr;

         static inline T process(const ivector_ptr v)
         {
            const std::size_t vec_size = v->vec()->vds().size();

            return vec_add_op<T>::process(v) / vec_size;
         }
      };

      template <typename T>
      struct vec_min_op
      {
         typedef vector_interface<T>* ivector_ptr;

         static inline T process(const ivector_ptr v)
         {
            const T* vec = v->vec()->vds().data();
            const std::size_t vec_size = v->vec()->vds().size();

            T result = vec[0];

            for (std::size_t i = 1; i < vec_size; ++i)
            {
               const T v_i = vec[i];

               if (v_i < result)
                  result = v_i;
            }

            return result;
         }
      };

      template <typename T>
      struct vec_max_op
      {
         typedef vector_interface<T>* ivector_ptr;

         static inline T process(const ivector_ptr v)
         {
            const T* vec = v->vec()->vds().data();
            const std::size_t vec_size = v->vec()->vds().size();

            T result = vec[0];

            for (std::size_t i = 1; i < vec_size; ++i)
            {
               const T v_i = vec[i];

               if (v_i > result)
                  result = v_i;
            }

            return result;
         }
      };

      template <typename T>
      class vov_base_node : public expression_node<T>
      {
      public:

         virtual ~vov_base_node()
         {}

         inline virtual operator_type operation() const
         {
            return details::e_default;
         }

         virtual const T& v0() const = 0;

         virtual const T& v1() const = 0;
      };

      template <typename T>
      class cov_base_node : public expression_node<T>
      {
      public:

       virtual ~cov_base_node()
          {}

         inline virtual operator_type operation() const
         {
            return details::e_default;
         }

         virtual const T c() const = 0;

         virtual const T& v() const = 0;
      };

      template <typename T>
      class voc_base_node : public expression_node<T>
      {
      public:

         virtual ~voc_base_node()
         {}

         inline virtual operator_type operation() const
         {
            return details::e_default;
         }

         virtual const T c() const = 0;

         virtual const T& v() const = 0;
      };

      template <typename T>
      class vob_base_node : public expression_node<T>
      {
      public:

         virtual ~vob_base_node()
         {}

         virtual const T& v() const = 0;
      };

      template <typename T>
      class bov_base_node : public expression_node<T>
      {
      public:

         virtual ~bov_base_node()
         {}

         virtual const T& v() const = 0;
      };

      template <typename T>
      class cob_base_node : public expression_node<T>
      {
      public:

         virtual ~cob_base_node()
         {}

         inline virtual operator_type operation() const
         {
            return details::e_default;
         }

         virtual const T c() const = 0;

         virtual void set_c(const T) = 0;

         virtual expression_node<T>* move_branch(const std::size_t& index) = 0;
      };

      template <typename T>
      class boc_base_node : public expression_node<T>
      {
      public:

         virtual ~boc_base_node()
         {}

         inline virtual operator_type operation() const
         {
            return details::e_default;
         }

         virtual const T c() const = 0;

         virtual void set_c(const T) = 0;

         virtual expression_node<T>* move_branch(const std::size_t& index) = 0;
      };

      template <typename T>
      class uv_base_node : public expression_node<T>
      {
      public:

         virtual ~uv_base_node()
         {}

         inline virtual operator_type operation() const
         {
            return details::e_default;
         }

         virtual const T& v() const = 0;
      };

      template <typename T>
      class sos_base_node : public expression_node<T>
      {
      public:

         virtual ~sos_base_node()
         {}

         inline virtual operator_type operation() const
         {
            return details::e_default;
         }
      };

      template <typename T>
      class sosos_base_node : public expression_node<T>
      {
      public:

         virtual ~sosos_base_node()
         {}

         inline virtual operator_type operation() const
         {
            return details::e_default;
         }
      };

      template <typename T>
      class T0oT1oT2_base_node : public expression_node<T>
      {
      public:

         virtual ~T0oT1oT2_base_node()
         {}

         virtual std::string type_id() const = 0;
      };

      template <typename T>
      class T0oT1oT2oT3_base_node : public expression_node<T>
      {
      public:

         virtual ~T0oT1oT2oT3_base_node()
         {}

         virtual std::string type_id() const = 0;
      };

      template <typename T, typename Operation>
      class unary_variable_node exprtk_final : public uv_base_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef Operation operation_t;

         explicit unary_variable_node(const T& var)
         : v_(var)
         {}

         inline T value() const
         {
            return Operation::process(v_);
         }

         inline typename expression_node<T>::node_type type() const
         {
            return Operation::type();
         }

         inline operator_type operation() const
         {
            return Operation::operation();
         }

         inline const T& v() const
         {
            return v_;
         }

      private:

         unary_variable_node(unary_variable_node<T,Operation>&);
         unary_variable_node<T,Operation>& operator=(unary_variable_node<T,Operation>&);

         const T& v_;
      };

      template <typename T>
      class uvouv_node exprtk_final : public expression_node<T>
      {
      public:

         // UOpr1(v0) Op UOpr2(v1)

         typedef expression_node<T>* expression_ptr;
         typedef typename details::functor_t<T> functor_t;
         typedef typename functor_t::bfunc_t      bfunc_t;
         typedef typename functor_t::ufunc_t      ufunc_t;

         explicit uvouv_node(const T& var0,const T& var1,
                             ufunc_t uf0, ufunc_t uf1, bfunc_t bf)
         : v0_(var0)
         , v1_(var1)
         , u0_(uf0 )
         , u1_(uf1 )
         , f_ (bf  )
         {}

         inline T value() const
         {
            return f_(u0_(v0_),u1_(v1_));
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_uvouv;
         }

         inline operator_type operation() const
         {
            return details::e_default;
         }

         inline const T& v0()
         {
            return v0_;
         }

         inline const T& v1()
         {
            return v1_;
         }

         inline ufunc_t u0()
         {
            return u0_;
         }

         inline ufunc_t u1()
         {
            return u1_;
         }

         inline ufunc_t f()
         {
            return f_;
         }

      private:

         uvouv_node(uvouv_node<T>&);
         uvouv_node<T>& operator=(uvouv_node<T>&);

         const T& v0_;
         const T& v1_;
         const ufunc_t u0_;
         const ufunc_t u1_;
         const bfunc_t f_;
      };

      template <typename T, typename Operation>
      class unary_branch_node exprtk_final : public expression_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef std::pair<expression_ptr,bool> branch_t;
         typedef Operation operation_t;

         explicit unary_branch_node(expression_ptr branch)
         {
            construct_branch_pair(branch_, branch);
         }

         inline T value() const
         {
            return Operation::process(branch_.first->value());
         }

         inline typename expression_node<T>::node_type type() const
         {
            return Operation::type();
         }

         inline operator_type operation() const
         {
            return Operation::operation();
         }

         inline expression_node<T>* branch(const std::size_t&) const
         {
            return branch_.first;
         }

         inline void release()
         {
            branch_.second = false;
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::collect(branch_, node_delete_list);
         }

         std::size_t node_depth() const
         {
            return expression_node<T>::ndb_t::compute_node_depth(branch_);
         }

      private:

         unary_branch_node(unary_branch_node<T,Operation>&);
         unary_branch_node<T,Operation>& operator=(unary_branch_node<T,Operation>&);

         branch_t branch_;
      };

      template <typename T> struct is_const                { enum {result = 0}; };
      template <typename T> struct is_const <const T>      { enum {result = 1}; };
      template <typename T> struct is_const_ref            { enum {result = 0}; };
      template <typename T> struct is_const_ref <const T&> { enum {result = 1}; };
      template <typename T> struct is_ref                  { enum {result = 0}; };
      template <typename T> struct is_ref<T&>              { enum {result = 1}; };
      template <typename T> struct is_ref<const T&>        { enum {result = 0}; };

      template <std::size_t State>
      struct param_to_str { static std::string result() { static const std::string r("v"); return r; } };

      template <>
      struct param_to_str<0> { static std::string result() { static const std::string r("c"); return r; } };

      #define exprtk_crtype(Type)                          \
      param_to_str<is_const_ref< Type >::result>::result() \

      template <typename T>
      struct T0oT1oT2process
      {
         typedef typename details::functor_t<T> functor_t;
         typedef typename functor_t::bfunc_t      bfunc_t;

         struct mode0
         {
            static inline T process(const T& t0, const T& t1, const T& t2, const bfunc_t bf0, const bfunc_t bf1)
            {
               // (T0 o0 T1) o1 T2
               return bf1(bf0(t0,t1),t2);
            }

            template <typename T0, typename T1, typename T2>
            static inline std::string id()
            {
               static const std::string result = "(" + exprtk_crtype(T0) + "o"   +
                                                       exprtk_crtype(T1) + ")o(" +
                                                       exprtk_crtype(T2) + ")"   ;
               return result;
            }
         };

         struct mode1
         {
            static inline T process(const T& t0, const T& t1, const T& t2, const bfunc_t bf0, const bfunc_t bf1)
            {
               // T0 o0 (T1 o1 T2)
               return bf0(t0,bf1(t1,t2));
            }

            template <typename T0, typename T1, typename T2>
            static inline std::string id()
            {
               static const std::string result = "(" + exprtk_crtype(T0) + ")o(" +
                                                       exprtk_crtype(T1) + "o"   +
                                                       exprtk_crtype(T2) + ")"   ;
               return result;
            }
         };
      };

      template <typename T>
      struct T0oT1oT20T3process
      {
         typedef typename details::functor_t<T> functor_t;
         typedef typename functor_t::bfunc_t      bfunc_t;

         struct mode0
         {
            static inline T process(const T& t0, const T& t1,
                                    const T& t2, const T& t3,
                                    const bfunc_t bf0, const bfunc_t bf1, const bfunc_t bf2)
            {
               // (T0 o0 T1) o1 (T2 o2 T3)
               return bf1(bf0(t0,t1),bf2(t2,t3));
            }

            template <typename T0, typename T1, typename T2, typename T3>
            static inline std::string id()
            {
               static const std::string result = "(" + exprtk_crtype(T0) + "o"  +
                                                       exprtk_crtype(T1) + ")o" +
                                                 "(" + exprtk_crtype(T2) + "o"  +
                                                       exprtk_crtype(T3) + ")"  ;
               return result;
            }
         };

         struct mode1
         {
            static inline T process(const T& t0, const T& t1,
                                    const T& t2, const T& t3,
                                    const bfunc_t bf0, const bfunc_t bf1, const bfunc_t bf2)
            {
               // (T0 o0 (T1 o1 (T2 o2 T3))
               return bf0(t0,bf1(t1,bf2(t2,t3)));
            }
            template <typename T0, typename T1, typename T2, typename T3>
            static inline std::string id()
            {
               static const std::string result = "(" + exprtk_crtype(T0) +  ")o((" +
                                                       exprtk_crtype(T1) +  ")o("  +
                                                       exprtk_crtype(T2) +  "o"    +
                                                       exprtk_crtype(T3) +  "))"   ;
               return result;
            }
         };

         struct mode2
         {
            static inline T process(const T& t0, const T& t1,
                                    const T& t2, const T& t3,
                                    const bfunc_t bf0, const bfunc_t bf1, const bfunc_t bf2)
            {
               // (T0 o0 ((T1 o1 T2) o2 T3)
               return bf0(t0,bf2(bf1(t1,t2),t3));
            }

            template <typename T0, typename T1, typename T2, typename T3>
            static inline std::string id()
            {
               static const std::string result = "(" + exprtk_crtype(T0) + ")o((" +
                                                       exprtk_crtype(T1) + "o"    +
                                                       exprtk_crtype(T2) + ")o("  +
                                                       exprtk_crtype(T3) + "))"   ;
               return result;
            }
         };

         struct mode3
         {
            static inline T process(const T& t0, const T& t1,
                                    const T& t2, const T& t3,
                                    const bfunc_t bf0, const bfunc_t bf1, const bfunc_t bf2)
            {
               // (((T0 o0 T1) o1 T2) o2 T3)
               return bf2(bf1(bf0(t0,t1),t2),t3);
            }

            template <typename T0, typename T1, typename T2, typename T3>
            static inline std::string id()
            {
               static const std::string result = "((" + exprtk_crtype(T0) + "o"    +
                                                        exprtk_crtype(T1) + ")o("  +
                                                        exprtk_crtype(T2) + "))o(" +
                                                        exprtk_crtype(T3) + ")";
               return result;
            }
         };

         struct mode4
         {
            static inline T process(const T& t0, const T& t1,
                                    const T& t2, const T& t3,
                                    const bfunc_t bf0, const bfunc_t bf1, const bfunc_t bf2)
            {
               // ((T0 o0 (T1 o1 T2)) o2 T3
               return bf2(bf0(t0,bf1(t1,t2)),t3);
            }

            template <typename T0, typename T1, typename T2, typename T3>
            static inline std::string id()
            {
               static const std::string result = "((" + exprtk_crtype(T0) + ")o("  +
                                                        exprtk_crtype(T1) + "o"    +
                                                        exprtk_crtype(T2) + "))o(" +
                                                        exprtk_crtype(T3) + ")"    ;
               return result;
            }
         };
      };

      #undef exprtk_crtype

      template <typename T, typename T0, typename T1>
      struct nodetype_T0oT1 { static const typename expression_node<T>::node_type result; };
      template <typename T, typename T0, typename T1>
      const typename expression_node<T>::node_type nodetype_T0oT1<T,T0,T1>::result = expression_node<T>::e_none;

      #define synthesis_node_type_define(T0_,T1_,v_)                                                            \
      template <typename T, typename T0, typename T1>                                                           \
      struct nodetype_T0oT1<T,T0_,T1_> { static const typename expression_node<T>::node_type result; };         \
      template <typename T, typename T0, typename T1>                                                           \
      const typename expression_node<T>::node_type nodetype_T0oT1<T,T0_,T1_>::result = expression_node<T>:: v_; \

      synthesis_node_type_define(const T0&, const T1&,  e_vov)
      synthesis_node_type_define(const T0&, const T1 ,  e_voc)
      synthesis_node_type_define(const T0 , const T1&,  e_cov)
      synthesis_node_type_define(      T0&,       T1&, e_none)
      synthesis_node_type_define(const T0 , const T1 , e_none)
      synthesis_node_type_define(      T0&, const T1 , e_none)
      synthesis_node_type_define(const T0 ,       T1&, e_none)
      synthesis_node_type_define(const T0&,       T1&, e_none)
      synthesis_node_type_define(      T0&, const T1&, e_none)
      #undef synthesis_node_type_define

      template <typename T, typename T0, typename T1, typename T2>
      struct nodetype_T0oT1oT2 { static const typename expression_node<T>::node_type result; };
      template <typename T, typename T0, typename T1, typename T2>
      const typename expression_node<T>::node_type nodetype_T0oT1oT2<T,T0,T1,T2>::result = expression_node<T>::e_none;

      #define synthesis_node_type_define(T0_,T1_,T2_,v_)                                                               \
      template <typename T, typename T0, typename T1, typename T2>                                                     \
      struct nodetype_T0oT1oT2<T,T0_,T1_,T2_> { static const typename expression_node<T>::node_type result; };         \
      template <typename T, typename T0, typename T1, typename T2>                                                     \
      const typename expression_node<T>::node_type nodetype_T0oT1oT2<T,T0_,T1_,T2_>::result = expression_node<T>:: v_; \

      synthesis_node_type_define(const T0&, const T1&, const T2&, e_vovov)
      synthesis_node_type_define(const T0&, const T1&, const T2 , e_vovoc)
      synthesis_node_type_define(const T0&, const T1 , const T2&, e_vocov)
      synthesis_node_type_define(const T0 , const T1&, const T2&, e_covov)
      synthesis_node_type_define(const T0 , const T1&, const T2 , e_covoc)
      synthesis_node_type_define(const T0 , const T1 , const T2 , e_none )
      synthesis_node_type_define(const T0 , const T1 , const T2&, e_none )
      synthesis_node_type_define(const T0&, const T1 , const T2 , e_none )
      synthesis_node_type_define(      T0&,       T1&,       T2&, e_none )
      #undef synthesis_node_type_define

      template <typename T, typename T0, typename T1, typename T2, typename T3>
      struct nodetype_T0oT1oT2oT3 { static const typename expression_node<T>::node_type result; };
      template <typename T, typename T0, typename T1, typename T2, typename T3>
      const typename expression_node<T>::node_type nodetype_T0oT1oT2oT3<T,T0,T1,T2,T3>::result = expression_node<T>::e_none;

      #define synthesis_node_type_define(T0_,T1_,T2_,T3_,v_)                                                                  \
      template <typename T, typename T0, typename T1, typename T2, typename T3>                                               \
      struct nodetype_T0oT1oT2oT3<T,T0_,T1_,T2_,T3_> { static const typename expression_node<T>::node_type result; };         \
      template <typename T, typename T0, typename T1, typename T2, typename T3>                                               \
      const typename expression_node<T>::node_type nodetype_T0oT1oT2oT3<T,T0_,T1_,T2_,T3_>::result = expression_node<T>:: v_; \

      synthesis_node_type_define(const T0&, const T1&, const T2&, const T3&, e_vovovov)
      synthesis_node_type_define(const T0&, const T1&, const T2&, const T3 , e_vovovoc)
      synthesis_node_type_define(const T0&, const T1&, const T2 , const T3&, e_vovocov)
      synthesis_node_type_define(const T0&, const T1 , const T2&, const T3&, e_vocovov)
      synthesis_node_type_define(const T0 , const T1&, const T2&, const T3&, e_covovov)
      synthesis_node_type_define(const T0 , const T1&, const T2 , const T3&, e_covocov)
      synthesis_node_type_define(const T0&, const T1 , const T2&, const T3 , e_vocovoc)
      synthesis_node_type_define(const T0 , const T1&, const T2&, const T3 , e_covovoc)
      synthesis_node_type_define(const T0&, const T1 , const T2 , const T3&, e_vococov)
      synthesis_node_type_define(const T0 , const T1 , const T2 , const T3 , e_none   )
      synthesis_node_type_define(const T0 , const T1 , const T2 , const T3&, e_none   )
      synthesis_node_type_define(const T0 , const T1 , const T2&, const T3 , e_none   )
      synthesis_node_type_define(const T0 , const T1&, const T2 , const T3 , e_none   )
      synthesis_node_type_define(const T0&, const T1 , const T2 , const T3 , e_none   )
      synthesis_node_type_define(const T0 , const T1 , const T2&, const T3&, e_none   )
      synthesis_node_type_define(const T0&, const T1&, const T2 , const T3 , e_none   )
      #undef synthesis_node_type_define

      template <typename T, typename T0, typename T1>
      class T0oT1 exprtk_final : public expression_node<T>
      {
      public:

         typedef typename details::functor_t<T> functor_t;
         typedef typename functor_t::bfunc_t      bfunc_t;
         typedef T value_type;
         typedef T0oT1<T,T0,T1> node_type;

         T0oT1(T0 p0, T1 p1, const bfunc_t p2)
         : t0_(p0)
         , t1_(p1)
         , f_ (p2)
         {}

         inline typename expression_node<T>::node_type type() const
         {
            static const typename expression_node<T>::node_type result = nodetype_T0oT1<T,T0,T1>::result;
            return result;
         }

         inline operator_type operation() const
         {
            return e_default;
         }

         inline T value() const
         {
            return f_(t0_,t1_);
         }

         inline T0 t0() const
         {
            return t0_;
         }

         inline T1 t1() const
         {
            return t1_;
         }

         inline bfunc_t f() const
         {
            return f_;
         }

         template <typename Allocator>
         static inline expression_node<T>* allocate(Allocator& allocator,
                                                    T0 p0, T1 p1,
                                                    bfunc_t p2)
         {
            return allocator
                     .template allocate_type<node_type, T0, T1, bfunc_t&>
                        (p0, p1, p2);
         }

      private:

         T0oT1(T0oT1<T,T0,T1>&) {}
         T0oT1<T,T0,T1>& operator=(T0oT1<T,T0,T1>&) { return (*this); }

         T0 t0_;
         T1 t1_;
         const bfunc_t f_;
      };

      template <typename T, typename T0, typename T1, typename T2, typename ProcessMode>
      class T0oT1oT2 exprtk_final : public T0oT1oT2_base_node<T>
      {
      public:

         typedef typename details::functor_t<T> functor_t;
         typedef typename functor_t::bfunc_t      bfunc_t;
         typedef T value_type;
         typedef T0oT1oT2<T,T0,T1,T2,ProcessMode> node_type;
         typedef ProcessMode process_mode_t;

         T0oT1oT2(T0 p0, T1 p1, T2 p2, const bfunc_t p3, const bfunc_t p4)
         : t0_(p0)
         , t1_(p1)
         , t2_(p2)
         , f0_(p3)
         , f1_(p4)
         {}

         inline typename expression_node<T>::node_type type() const
         {
            static const typename expression_node<T>::node_type result = nodetype_T0oT1oT2<T,T0,T1,T2>::result;
            return result;
         }

         inline operator_type operation() const
         {
            return e_default;
         }

         inline T value() const
         {
            return ProcessMode::process(t0_, t1_, t2_, f0_, f1_);
         }

         inline T0 t0() const
         {
            return t0_;
         }

         inline T1 t1() const
         {
            return t1_;
         }

         inline T2 t2() const
         {
            return t2_;
         }

         bfunc_t f0() const
         {
            return f0_;
         }

         bfunc_t f1() const
         {
            return f1_;
         }

         std::string type_id() const
         {
            return id();
         }

         static inline std::string id()
         {
            return process_mode_t::template id<T0,T1,T2>();
         }

         template <typename Allocator>
         static inline expression_node<T>* allocate(Allocator& allocator, T0 p0, T1 p1, T2 p2, bfunc_t p3, bfunc_t p4)
         {
            return allocator
                      .template allocate_type<node_type, T0, T1, T2, bfunc_t, bfunc_t>
                         (p0, p1, p2, p3, p4);
         }

      private:

         T0oT1oT2(node_type&) {}
         node_type& operator=(node_type&) { return (*this); }

         T0 t0_;
         T1 t1_;
         T2 t2_;
         const bfunc_t f0_;
         const bfunc_t f1_;
      };

      template <typename T, typename T0_, typename T1_, typename T2_, typename T3_, typename ProcessMode>
      class T0oT1oT2oT3 exprtk_final : public T0oT1oT2oT3_base_node<T>
      {
      public:

         typedef typename details::functor_t<T> functor_t;
         typedef typename functor_t::bfunc_t      bfunc_t;
         typedef T value_type;
         typedef T0_ T0;
         typedef T1_ T1;
         typedef T2_ T2;
         typedef T3_ T3;
         typedef T0oT1oT2oT3<T,T0,T1,T2,T3,ProcessMode> node_type;
         typedef ProcessMode process_mode_t;

         T0oT1oT2oT3(T0 p0, T1 p1, T2 p2, T3 p3, bfunc_t p4, bfunc_t p5, bfunc_t p6)
         : t0_(p0)
         , t1_(p1)
         , t2_(p2)
         , t3_(p3)
         , f0_(p4)
         , f1_(p5)
         , f2_(p6)
         {}

         inline T value() const
         {
            return ProcessMode::process(t0_, t1_, t2_, t3_, f0_, f1_, f2_);
         }

         inline T0 t0() const
         {
            return t0_;
         }

         inline T1 t1() const
         {
            return t1_;
         }

         inline T2 t2() const
         {
            return t2_;
         }

         inline T3 t3() const
         {
            return t3_;
         }

         inline bfunc_t f0() const
         {
            return f0_;
         }

         inline bfunc_t f1() const
         {
            return f1_;
         }

         inline bfunc_t f2() const
         {
            return f2_;
         }

         inline std::string type_id() const
         {
            return id();
         }

         static inline std::string id()
         {
            return process_mode_t::template id<T0, T1, T2, T3>();
         }

         template <typename Allocator>
         static inline expression_node<T>* allocate(Allocator& allocator,
                                                    T0 p0, T1 p1, T2 p2, T3 p3,
                                                    bfunc_t p4, bfunc_t p5, bfunc_t p6)
         {
            return allocator
                      .template allocate_type<node_type, T0, T1, T2, T3, bfunc_t, bfunc_t>
                         (p0, p1, p2, p3, p4, p5, p6);
         }

      private:

         T0oT1oT2oT3(node_type&) {}
         node_type& operator=(node_type&) { return (*this); }

         T0 t0_;
         T1 t1_;
         T2 t2_;
         T3 t3_;
         const bfunc_t f0_;
         const bfunc_t f1_;
         const bfunc_t f2_;
      };

      template <typename T, typename T0, typename T1, typename T2>
      class T0oT1oT2_sf3 exprtk_final : public T0oT1oT2_base_node<T>
      {
      public:

         typedef typename details::functor_t<T> functor_t;
         typedef typename functor_t::tfunc_t      tfunc_t;
         typedef T value_type;
         typedef T0oT1oT2_sf3<T,T0,T1,T2> node_type;

         T0oT1oT2_sf3(T0 p0, T1 p1, T2 p2, const tfunc_t p3)
         : t0_(p0)
         , t1_(p1)
         , t2_(p2)
         , f_ (p3)
         {}

         inline typename expression_node<T>::node_type type() const
         {
            static const typename expression_node<T>::node_type result = nodetype_T0oT1oT2<T,T0,T1,T2>::result;
            return result;
         }

         inline operator_type operation() const
         {
            return e_default;
         }

         inline T value() const
         {
            return f_(t0_, t1_, t2_);
         }

         inline T0 t0() const
         {
            return t0_;
         }

         inline T1 t1() const
         {
            return t1_;
         }

         inline T2 t2() const
         {
            return t2_;
         }

         tfunc_t f() const
         {
            return f_;
         }

         std::string type_id() const
         {
            return id();
         }

         static inline std::string id()
         {
            return "sf3";
         }

         template <typename Allocator>
         static inline expression_node<T>* allocate(Allocator& allocator, T0 p0, T1 p1, T2 p2, tfunc_t p3)
         {
            return allocator
                     .template allocate_type<node_type, T0, T1, T2, tfunc_t>
                        (p0, p1, p2, p3);
         }

      private:

         T0oT1oT2_sf3(node_type&) {}
         node_type& operator=(node_type&) { return (*this); }

         T0 t0_;
         T1 t1_;
         T2 t2_;
         const tfunc_t f_;
      };

      template <typename T, typename T0, typename T1, typename T2>
      class sf3ext_type_node : public T0oT1oT2_base_node<T>
      {
      public:

         virtual ~sf3ext_type_node()
         {}

         virtual T0 t0() const = 0;

         virtual T1 t1() const = 0;

         virtual T2 t2() const = 0;
      };

      template <typename T, typename T0, typename T1, typename T2, typename SF3Operation>
      class T0oT1oT2_sf3ext exprtk_final : public sf3ext_type_node<T,T0,T1,T2>
      {
      public:

         typedef typename details::functor_t<T> functor_t;
         typedef typename functor_t::tfunc_t      tfunc_t;
         typedef T value_type;
         typedef T0oT1oT2_sf3ext<T,T0,T1,T2,SF3Operation> node_type;

         T0oT1oT2_sf3ext(T0 p0, T1 p1, T2 p2)
         : t0_(p0)
         , t1_(p1)
         , t2_(p2)
         {}

         inline typename expression_node<T>::node_type type() const
         {
            static const typename expression_node<T>::node_type result = nodetype_T0oT1oT2<T,T0,T1,T2>::result;
            return result;
         }

         inline operator_type operation() const
         {
            return e_default;
         }

         inline T value() const
         {
            return SF3Operation::process(t0_, t1_, t2_);
         }

         T0 t0() const
         {
            return t0_;
         }

         T1 t1() const
         {
            return t1_;
         }

         T2 t2() const
         {
            return t2_;
         }

         std::string type_id() const
         {
            return id();
         }

         static inline std::string id()
         {
            return SF3Operation::id();
         }

         template <typename Allocator>
         static inline expression_node<T>* allocate(Allocator& allocator, T0 p0, T1 p1, T2 p2)
         {
            return allocator
                     .template allocate_type<node_type, T0, T1, T2>
                        (p0, p1, p2);
         }

      private:

         T0oT1oT2_sf3ext(node_type&) {}
         node_type& operator=(node_type&) { return (*this); }

         T0 t0_;
         T1 t1_;
         T2 t2_;
      };

      template <typename T>
      inline bool is_sf3ext_node(const expression_node<T>* n)
      {
         switch (n->type())
         {
            case expression_node<T>::e_vovov : return true;
            case expression_node<T>::e_vovoc : return true;
            case expression_node<T>::e_vocov : return true;
            case expression_node<T>::e_covov : return true;
            case expression_node<T>::e_covoc : return true;
            default                          : return false;
         }
      }

      template <typename T, typename T0, typename T1, typename T2, typename T3>
      class T0oT1oT2oT3_sf4 exprtk_final : public T0oT1oT2_base_node<T>
      {
      public:

         typedef typename details::functor_t<T> functor_t;
         typedef typename functor_t::qfunc_t      qfunc_t;
         typedef T value_type;
         typedef T0oT1oT2oT3_sf4<T,T0,T1,T2,T3> node_type;

         T0oT1oT2oT3_sf4(T0 p0, T1 p1, T2 p2, T3 p3, const qfunc_t p4)
         : t0_(p0)
         , t1_(p1)
         , t2_(p2)
         , t3_(p3)
         , f_ (p4)
         {}

         inline typename expression_node<T>::node_type type() const
         {
            static const typename expression_node<T>::node_type result = nodetype_T0oT1oT2oT3<T,T0,T1,T2,T3>::result;
            return result;
         }

         inline operator_type operation() const
         {
            return e_default;
         }

         inline T value() const
         {
            return f_(t0_, t1_, t2_, t3_);
         }

         inline T0 t0() const
         {
            return t0_;
         }

         inline T1 t1() const
         {
            return t1_;
         }

         inline T2 t2() const
         {
            return t2_;
         }

         inline T3 t3() const
         {
            return t3_;
         }

         qfunc_t f() const
         {
            return f_;
         }

         std::string type_id() const
         {
            return id();
         }

         static inline std::string id()
         {
            return "sf4";
         }

         template <typename Allocator>
         static inline expression_node<T>* allocate(Allocator& allocator, T0 p0, T1 p1, T2 p2, T3 p3, qfunc_t p4)
         {
            return allocator
                     .template allocate_type<node_type, T0, T1, T2, T3, qfunc_t>
                        (p0, p1, p2, p3, p4);
         }

      private:

         T0oT1oT2oT3_sf4(node_type&) {}
         node_type& operator=(node_type&) { return (*this); }

         T0 t0_;
         T1 t1_;
         T2 t2_;
         T3 t3_;
         const qfunc_t f_;
      };

      template <typename T, typename T0, typename T1, typename T2, typename T3, typename SF4Operation>
      class T0oT1oT2oT3_sf4ext exprtk_final : public T0oT1oT2oT3_base_node<T>
      {
      public:

         typedef typename details::functor_t<T> functor_t;
         typedef typename functor_t::tfunc_t      tfunc_t;
         typedef T value_type;
         typedef T0oT1oT2oT3_sf4ext<T,T0,T1,T2,T3,SF4Operation> node_type;

         T0oT1oT2oT3_sf4ext(T0 p0, T1 p1, T2 p2, T3 p3)
         : t0_(p0)
         , t1_(p1)
         , t2_(p2)
         , t3_(p3)
         {}

         inline typename expression_node<T>::node_type type() const
         {
            static const typename expression_node<T>::node_type result = nodetype_T0oT1oT2oT3<T,T0,T1,T2,T3>::result;
            return result;
         }

         inline operator_type operation() const
         {
            return e_default;
         }

         inline T value() const
         {
            return SF4Operation::process(t0_, t1_, t2_, t3_);
         }

         inline T0 t0() const
         {
            return t0_;
         }

         inline T1 t1() const
         {
            return t1_;
         }

         inline T2 t2() const
         {
            return t2_;
         }

         inline T3 t3() const
         {
            return t3_;
         }

         std::string type_id() const
         {
            return id();
         }

         static inline std::string id()
         {
            return SF4Operation::id();
         }

         template <typename Allocator>
         static inline expression_node<T>* allocate(Allocator& allocator, T0 p0, T1 p1, T2 p2, T3 p3)
         {
            return allocator
                     .template allocate_type<node_type, T0, T1, T2, T3>
                        (p0, p1, p2, p3);
         }

      private:

         T0oT1oT2oT3_sf4ext(node_type&) {}
         node_type& operator=(node_type&) { return (*this); }

         T0 t0_;
         T1 t1_;
         T2 t2_;
         T3 t3_;
      };

      template <typename T>
      inline bool is_sf4ext_node(const expression_node<T>* n)
      {
         switch (n->type())
         {
            case expression_node<T>::e_vovovov : return true;
            case expression_node<T>::e_vovovoc : return true;
            case expression_node<T>::e_vovocov : return true;
            case expression_node<T>::e_vocovov : return true;
            case expression_node<T>::e_covovov : return true;
            case expression_node<T>::e_covocov : return true;
            case expression_node<T>::e_vocovoc : return true;
            case expression_node<T>::e_covovoc : return true;
            case expression_node<T>::e_vococov : return true;
            default                            : return false;
         }
      }

      template <typename T, typename T0, typename T1>
      struct T0oT1_define
      {
         typedef details::T0oT1<T, T0, T1> type0;
      };

      template <typename T, typename T0, typename T1, typename T2>
      struct T0oT1oT2_define
      {
         typedef details::T0oT1oT2<T, T0, T1, T2, typename T0oT1oT2process<T>::mode0> type0;
         typedef details::T0oT1oT2<T, T0, T1, T2, typename T0oT1oT2process<T>::mode1> type1;
         typedef details::T0oT1oT2_sf3<T, T0, T1, T2> sf3_type;
         typedef details::sf3ext_type_node<T, T0, T1, T2> sf3_type_node;
      };

      template <typename T, typename T0, typename T1, typename T2, typename T3>
      struct T0oT1oT2oT3_define
      {
         typedef details::T0oT1oT2oT3<T, T0, T1, T2, T3, typename T0oT1oT20T3process<T>::mode0> type0;
         typedef details::T0oT1oT2oT3<T, T0, T1, T2, T3, typename T0oT1oT20T3process<T>::mode1> type1;
         typedef details::T0oT1oT2oT3<T, T0, T1, T2, T3, typename T0oT1oT20T3process<T>::mode2> type2;
         typedef details::T0oT1oT2oT3<T, T0, T1, T2, T3, typename T0oT1oT20T3process<T>::mode3> type3;
         typedef details::T0oT1oT2oT3<T, T0, T1, T2, T3, typename T0oT1oT20T3process<T>::mode4> type4;
         typedef details::T0oT1oT2oT3_sf4<T, T0, T1, T2, T3> sf4_type;
      };

      template <typename T, typename Operation>
      class vov_node exprtk_final : public vov_base_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef Operation operation_t;

         // variable op variable node
         explicit vov_node(const T& var0, const T& var1)
         : v0_(var0)
         , v1_(var1)
         {}

         inline T value() const
         {
            return Operation::process(v0_,v1_);
         }

         inline typename expression_node<T>::node_type type() const
         {
            return Operation::type();
         }

         inline operator_type operation() const
         {
            return Operation::operation();
         }

         inline const T& v0() const
         {
            return v0_;
         }

         inline const T& v1() const
         {
            return v1_;
         }

      protected:

         const T& v0_;
         const T& v1_;

      private:

         vov_node(vov_node<T,Operation>&);
         vov_node<T,Operation>& operator=(vov_node<T,Operation>&);
      };

      template <typename T, typename Operation>
      class cov_node exprtk_final : public cov_base_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef Operation operation_t;

         // constant op variable node
         explicit cov_node(const T& const_var, const T& var)
         : c_(const_var)
         , v_(var)
         {}

         inline T value() const
         {
            return Operation::process(c_,v_);
         }

         inline typename expression_node<T>::node_type type() const
         {
            return Operation::type();
         }

         inline operator_type operation() const
         {
            return Operation::operation();
         }

         inline const T c() const
         {
            return c_;
         }

         inline const T& v() const
         {
            return v_;
         }

      protected:

         const T  c_;
         const T& v_;

      private:

         cov_node(const cov_node<T,Operation>&);
         cov_node<T,Operation>& operator=(const cov_node<T,Operation>&);
      };

      template <typename T, typename Operation>
      class voc_node exprtk_final : public voc_base_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef Operation operation_t;

         // variable op constant node
         explicit voc_node(const T& var, const T& const_var)
         : v_(var)
         , c_(const_var)
         {}

         inline T value() const
         {
            return Operation::process(v_,c_);
         }

         inline operator_type operation() const
         {
            return Operation::operation();
         }

         inline const T c() const
         {
            return c_;
         }

         inline const T& v() const
         {
            return v_;
         }

      protected:

         const T& v_;
         const T  c_;

      private:

         voc_node(const voc_node<T,Operation>&);
         voc_node<T,Operation>& operator=(const voc_node<T,Operation>&);
      };

      template <typename T, typename Operation>
      class vob_node exprtk_final : public vob_base_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef std::pair<expression_ptr,bool> branch_t;
         typedef Operation operation_t;

         // variable op constant node
         explicit vob_node(const T& var, const expression_ptr branch)
         : v_(var)
         {
            construct_branch_pair(branch_, branch);
         }

         inline T value() const
         {
            assert(branch_.first);
            return Operation::process(v_,branch_.first->value());
         }

         inline operator_type operation() const
         {
            return Operation::operation();
         }

         inline const T& v() const
         {
            return v_;
         }

         inline expression_node<T>* branch(const std::size_t&) const
         {
            return branch_.first;
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::template collect(branch_, node_delete_list);
         }

         std::size_t node_depth() const
         {
            return expression_node<T>::ndb_t::compute_node_depth(branch_);
         }

      private:

         vob_node(const vob_node<T,Operation>&);
         vob_node<T,Operation>& operator=(const vob_node<T,Operation>&);

         const T& v_;
         branch_t branch_;
      };

      template <typename T, typename Operation>
      class bov_node exprtk_final : public bov_base_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef std::pair<expression_ptr,bool> branch_t;
         typedef Operation operation_t;

         // variable op constant node
         explicit bov_node(const expression_ptr branch, const T& var)
         : v_(var)
         {
            construct_branch_pair(branch_, branch);
         }

         inline T value() const
         {
            assert(branch_.first);
            return Operation::process(branch_.first->value(),v_);
         }

         inline operator_type operation() const
         {
            return Operation::operation();
         }

         inline const T& v() const
         {
            return v_;
         }

         inline expression_node<T>* branch(const std::size_t&) const
         {
            return branch_.first;
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::template collect(branch_, node_delete_list);
         }

         std::size_t node_depth() const
         {
            return expression_node<T>::ndb_t::compute_node_depth(branch_);
         }

      private:

         bov_node(const bov_node<T,Operation>&);
         bov_node<T,Operation>& operator=(const bov_node<T,Operation>&);

         const T& v_;
         branch_t branch_;
      };

      template <typename T, typename Operation>
      class cob_node exprtk_final : public cob_base_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef std::pair<expression_ptr,bool> branch_t;
         typedef Operation operation_t;

         // variable op constant node
         explicit cob_node(const T const_var, const expression_ptr branch)
         : c_(const_var)
         {
            construct_branch_pair(branch_, branch);
         }

         inline T value() const
         {
            assert(branch_.first);
            return Operation::process(c_,branch_.first->value());
         }

         inline operator_type operation() const
         {
            return Operation::operation();
         }

         inline const T c() const
         {
            return c_;
         }

         inline void set_c(const T new_c)
         {
            (*const_cast<T*>(&c_)) = new_c;
         }

         inline expression_node<T>* branch(const std::size_t&) const
         {
            return branch_.first;
         }

         inline expression_node<T>* move_branch(const std::size_t&)
         {
            branch_.second = false;
            return branch_.first;
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::template collect(branch_, node_delete_list);
         }

         std::size_t node_depth() const
         {
            return expression_node<T>::ndb_t::compute_node_depth(branch_);
         }

      private:

         cob_node(const cob_node<T,Operation>&);
         cob_node<T,Operation>& operator=(const cob_node<T,Operation>&);

         const T  c_;
         branch_t branch_;
      };

      template <typename T, typename Operation>
      class boc_node exprtk_final : public boc_base_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef std::pair<expression_ptr,bool> branch_t;
         typedef Operation operation_t;

         // variable op constant node
         explicit boc_node(const expression_ptr branch, const T const_var)
         : c_(const_var)
         {
            construct_branch_pair(branch_, branch);
         }

         inline T value() const
         {
            assert(branch_.first);
            return Operation::process(branch_.first->value(),c_);
         }

         inline operator_type operation() const
         {
            return Operation::operation();
         }

         inline const T c() const
         {
            return c_;
         }

         inline void set_c(const T new_c)
         {
            (*const_cast<T*>(&c_)) = new_c;
         }

         inline expression_node<T>* branch(const std::size_t&) const
         {
            return branch_.first;
         }

         inline expression_node<T>* move_branch(const std::size_t&)
         {
            branch_.second = false;
            return branch_.first;
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::template collect(branch_, node_delete_list);
         }

         std::size_t node_depth() const
         {
            return expression_node<T>::ndb_t::compute_node_depth(branch_);
         }

      private:

         boc_node(const boc_node<T,Operation>&);
         boc_node<T,Operation>& operator=(const boc_node<T,Operation>&);

         const T  c_;
         branch_t branch_;
      };

      #ifndef exprtk_disable_string_capabilities
      template <typename T, typename SType0, typename SType1, typename Operation>
      class sos_node exprtk_final : public sos_base_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef Operation operation_t;

         // string op string node
         explicit sos_node(SType0 p0, SType1 p1)
         : s0_(p0)
         , s1_(p1)
         {}

         inline T value() const
         {
            return Operation::process(s0_,s1_);
         }

         inline typename expression_node<T>::node_type type() const
         {
            return Operation::type();
         }

         inline operator_type operation() const
         {
            return Operation::operation();
         }

         inline std::string& s0()
         {
            return s0_;
         }

         inline std::string& s1()
         {
            return s1_;
         }

      protected:

         SType0 s0_;
         SType1 s1_;

      private:

         sos_node(sos_node<T,SType0,SType1,Operation>&);
         sos_node<T,SType0,SType1,Operation>& operator=(sos_node<T,SType0,SType1,Operation>&);
      };

      template <typename T, typename SType0, typename SType1, typename RangePack, typename Operation>
      class str_xrox_node exprtk_final : public sos_base_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef Operation operation_t;

         // string-range op string node
         explicit str_xrox_node(SType0 p0, SType1 p1, RangePack rp0)
         : s0_ (p0 )
         , s1_ (p1 )
         , rp0_(rp0)
         {}

        ~str_xrox_node()
         {
            rp0_.free();
         }

         inline T value() const
         {
            std::size_t r0 = 0;
            std::size_t r1 = 0;

            if (rp0_(r0, r1, s0_.size()))
               return Operation::process(s0_.substr(r0, (r1 - r0) + 1), s1_);
            else
               return T(0);
         }

         inline typename expression_node<T>::node_type type() const
         {
            return Operation::type();
         }

         inline operator_type operation() const
         {
            return Operation::operation();
         }

         inline std::string& s0()
         {
            return s0_;
         }

         inline std::string& s1()
         {
            return s1_;
         }

      protected:

         SType0    s0_;
         SType1    s1_;
         RangePack rp0_;

      private:

         str_xrox_node(str_xrox_node<T,SType0,SType1,RangePack,Operation>&);
         str_xrox_node<T,SType0,SType1,RangePack,Operation>& operator=(str_xrox_node<T,SType0,SType1,RangePack,Operation>&);
      };

      template <typename T, typename SType0, typename SType1, typename RangePack, typename Operation>
      class str_xoxr_node exprtk_final : public sos_base_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef Operation operation_t;

         // string op string range node
         explicit str_xoxr_node(SType0 p0, SType1 p1, RangePack rp1)
         : s0_ (p0 )
         , s1_ (p1 )
         , rp1_(rp1)
         {}

        ~str_xoxr_node()
         {
            rp1_.free();
         }

         inline T value() const
         {
            std::size_t r0 = 0;
            std::size_t r1 = 0;

            if (rp1_(r0, r1, s1_.size()))
               return Operation::process(s0_, s1_.substr(r0, (r1 - r0) + 1));
            else
               return T(0);
         }

         inline typename expression_node<T>::node_type type() const
         {
            return Operation::type();
         }

         inline operator_type operation() const
         {
            return Operation::operation();
         }

         inline std::string& s0()
         {
            return s0_;
         }

         inline std::string& s1()
         {
            return s1_;
         }

      protected:

         SType0    s0_;
         SType1    s1_;
         RangePack rp1_;

      private:

         str_xoxr_node(str_xoxr_node<T,SType0,SType1,RangePack,Operation>&);
         str_xoxr_node<T,SType0,SType1,RangePack,Operation>& operator=(str_xoxr_node<T,SType0,SType1,RangePack,Operation>&);
      };

      template <typename T, typename SType0, typename SType1, typename RangePack, typename Operation>
      class str_xroxr_node exprtk_final : public sos_base_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef Operation operation_t;

         // string-range op string-range node
         explicit str_xroxr_node(SType0 p0, SType1 p1, RangePack rp0, RangePack rp1)
         : s0_ (p0 )
         , s1_ (p1 )
         , rp0_(rp0)
         , rp1_(rp1)
         {}

        ~str_xroxr_node()
         {
            rp0_.free();
            rp1_.free();
         }

         inline T value() const
         {
            std::size_t r0_0 = 0;
            std::size_t r0_1 = 0;
            std::size_t r1_0 = 0;
            std::size_t r1_1 = 0;

            if (
                 rp0_(r0_0, r1_0, s0_.size()) &&
                 rp1_(r0_1, r1_1, s1_.size())
               )
            {
               return Operation::process(
                                          s0_.substr(r0_0, (r1_0 - r0_0) + 1),
                                          s1_.substr(r0_1, (r1_1 - r0_1) + 1)
                                        );
            }
            else
               return T(0);
         }

         inline typename expression_node<T>::node_type type() const
         {
            return Operation::type();
         }

         inline operator_type operation() const
         {
            return Operation::operation();
         }

         inline std::string& s0()
         {
            return s0_;
         }

         inline std::string& s1()
         {
            return s1_;
         }

      protected:

         SType0    s0_;
         SType1    s1_;
         RangePack rp0_;
         RangePack rp1_;

      private:

         str_xroxr_node(str_xroxr_node<T,SType0,SType1,RangePack,Operation>&);
         str_xroxr_node<T,SType0,SType1,RangePack,Operation>& operator=(str_xroxr_node<T,SType0,SType1,RangePack,Operation>&);
      };

      template <typename T, typename Operation>
      class str_sogens_node exprtk_final : public binary_node<T>
      {
      public:

         typedef expression_node <T>* expression_ptr;
         typedef string_base_node<T>*   str_base_ptr;
         typedef range_pack      <T>         range_t;
         typedef range_t*                  range_ptr;
         typedef range_interface<T>         irange_t;
         typedef irange_t*                irange_ptr;

         str_sogens_node(const operator_type& opr,
                         expression_ptr branch0,
                         expression_ptr branch1)
         : binary_node<T>(opr, branch0, branch1)
         , str0_base_ptr_ (0)
         , str1_base_ptr_ (0)
         , str0_range_ptr_(0)
         , str1_range_ptr_(0)
         {
            if (is_generally_string_node(binary_node<T>::branch_[0].first))
            {
               str0_base_ptr_ = dynamic_cast<str_base_ptr>(binary_node<T>::branch_[0].first);

               if (0 == str0_base_ptr_)
                  return;

               irange_ptr range = dynamic_cast<irange_ptr>(binary_node<T>::branch_[0].first);

               if (0 == range)
                  return;

               str0_range_ptr_ = &(range->range_ref());
            }

            if (is_generally_string_node(binary_node<T>::branch_[1].first))
            {
               str1_base_ptr_ = dynamic_cast<str_base_ptr>(binary_node<T>::branch_[1].first);

               if (0 == str1_base_ptr_)
                  return;

               irange_ptr range = dynamic_cast<irange_ptr>(binary_node<T>::branch_[1].first);

               if (0 == range)
                  return;

               str1_range_ptr_ = &(range->range_ref());
            }
         }

         inline T value() const
         {
            if (
                 str0_base_ptr_  &&
                 str1_base_ptr_  &&
                 str0_range_ptr_ &&
                 str1_range_ptr_
               )
            {
               binary_node<T>::branch_[0].first->value();
               binary_node<T>::branch_[1].first->value();

               std::size_t str0_r0 = 0;
               std::size_t str0_r1 = 0;

               std::size_t str1_r0 = 0;
               std::size_t str1_r1 = 0;

               const range_t& range0 = (*str0_range_ptr_);
               const range_t& range1 = (*str1_range_ptr_);

               if (
                    range0(str0_r0, str0_r1, str0_base_ptr_->size()) &&
                    range1(str1_r0, str1_r1, str1_base_ptr_->size())
                  )
               {
                  return Operation::process(
                                             str0_base_ptr_->str().substr(str0_r0,(str0_r1 - str0_r0) + 1),
                                             str1_base_ptr_->str().substr(str1_r0,(str1_r1 - str1_r0) + 1)
                                           );
               }
            }

            return std::numeric_limits<T>::quiet_NaN();
         }

         inline typename expression_node<T>::node_type type() const
         {
            return Operation::type();
         }

         inline operator_type operation() const
         {
            return Operation::operation();
         }

      private:

         str_sogens_node(str_sogens_node<T,Operation>&);
         str_sogens_node<T,Operation>& operator=(str_sogens_node<T,Operation>&);

         str_base_ptr str0_base_ptr_;
         str_base_ptr str1_base_ptr_;
         range_ptr    str0_range_ptr_;
         range_ptr    str1_range_ptr_;
      };

      template <typename T, typename SType0, typename SType1, typename SType2, typename Operation>
      class sosos_node exprtk_final : public sosos_base_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef Operation operation_t;

         // variable op variable node
         explicit sosos_node(SType0 p0, SType1 p1, SType2 p2)
         : s0_(p0)
         , s1_(p1)
         , s2_(p2)
         {}

         inline T value() const
         {
            return Operation::process(s0_, s1_, s2_);
         }

         inline typename expression_node<T>::node_type type() const
         {
            return Operation::type();
         }

         inline operator_type operation() const
         {
            return Operation::operation();
         }

         inline std::string& s0()
         {
            return s0_;
         }

         inline std::string& s1()
         {
            return s1_;
         }

         inline std::string& s2()
         {
            return s2_;
         }

      protected:

         SType0 s0_;
         SType1 s1_;
         SType2 s2_;

      private:

         sosos_node(sosos_node<T,SType0,SType1,SType2,Operation>&);
         sosos_node<T,SType0,SType1,SType2,Operation>& operator=(sosos_node<T,SType0,SType1,SType2,Operation>&);
      };
      #endif

      template <typename T, typename PowOp>
      class ipow_node exprtk_final: public expression_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef PowOp operation_t;

         explicit ipow_node(const T& v)
         : v_(v)
         {}

         inline T value() const
         {
            return PowOp::result(v_);
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_ipow;
         }

      private:

         ipow_node(const ipow_node<T,PowOp>&);
         ipow_node<T,PowOp>& operator=(const ipow_node<T,PowOp>&);

         const T& v_;
      };

      template <typename T, typename PowOp>
      class bipow_node exprtk_final : public expression_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef std::pair<expression_ptr, bool> branch_t;
         typedef PowOp operation_t;

         explicit bipow_node(expression_ptr branch)
         {
            construct_branch_pair(branch_, branch);
         }

         inline T value() const
         {
            assert(branch_.first);
            return PowOp::result(branch_.first->value());
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_ipow;
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::collect(branch_, node_delete_list);
         }

         std::size_t node_depth() const
         {
            return expression_node<T>::ndb_t::compute_node_depth(branch_);
         }

      private:

         bipow_node(const bipow_node<T,PowOp>&);
         bipow_node<T,PowOp>& operator=(const bipow_node<T,PowOp>&);

         branch_t branch_;
      };

      template <typename T, typename PowOp>
      class ipowinv_node exprtk_final : public expression_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef PowOp operation_t;

         explicit ipowinv_node(const T& v)
         : v_(v)
         {}

         inline T value() const
         {
            return (T(1) / PowOp::result(v_));
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_ipowinv;
         }

      private:

         ipowinv_node(const ipowinv_node<T,PowOp>&);
         ipowinv_node<T,PowOp>& operator=(const ipowinv_node<T,PowOp>&);

         const T& v_;
      };

      template <typename T, typename PowOp>
      class bipowninv_node exprtk_final : public expression_node<T>
      {
      public:

         typedef expression_node<T>* expression_ptr;
         typedef std::pair<expression_ptr, bool> branch_t;
         typedef PowOp operation_t;

         explicit bipowninv_node(expression_ptr branch)
         {
            construct_branch_pair(branch_, branch);
         }

         inline T value() const
         {
            assert(branch_.first);
            return (T(1) / PowOp::result(branch_.first->value()));
         }

         inline typename expression_node<T>::node_type type() const
         {
            return expression_node<T>::e_ipowinv;
         }

         void collect_nodes(typename expression_node<T>::noderef_list_t& node_delete_list)
         {
            expression_node<T>::ndb_t::template collect(branch_, node_delete_list);
         }

         std::size_t node_depth() const
         {
            return expression_node<T>::ndb_t::compute_node_depth(branch_);
         }

      private:

         bipowninv_node(const bipowninv_node<T,PowOp>&);
         bipowninv_node<T,PowOp>& operator=(const bipowninv_node<T,PowOp>&);

         branch_t branch_;
      };

      template <typename T>
      inline bool is_vov_node(const expression_node<T>* node)
      {
         return (0 != dynamic_cast<const vov_base_node<T>*>(node));
      }

      template <typename T>
      inline bool is_cov_node(const expression_node<T>* node)
      {
         return (0 != dynamic_cast<const cov_base_node<T>*>(node));
      }

      template <typename T>
      inline bool is_voc_node(const expression_node<T>* node)
      {
         return (0 != dynamic_cast<const voc_base_node<T>*>(node));
      }

      template <typename T>
      inline bool is_cob_node(const expression_node<T>* node)
      {
         return (0 != dynamic_cast<const cob_base_node<T>*>(node));
      }

      template <typename T>
      inline bool is_boc_node(const expression_node<T>* node)
      {
         return (0 != dynamic_cast<const boc_base_node<T>*>(node));
      }

      template <typename T>
      inline bool is_t0ot1ot2_node(const expression_node<T>* node)
      {
         return (0 != dynamic_cast<const T0oT1oT2_base_node<T>*>(node));
      }

      template <typename T>
      inline bool is_t0ot1ot2ot3_node(const expression_node<T>* node)
      {
         return (0 != dynamic_cast<const T0oT1oT2oT3_base_node<T>*>(node));
      }

      template <typename T>
      inline bool is_uv_node(const expression_node<T>* node)
      {
         return (0 != dynamic_cast<const uv_base_node<T>*>(node));
      }

      template <typename T>
      inline bool is_string_node(const expression_node<T>* node)
      {
         return node && (expression_node<T>::e_stringvar == node->type());
      }

      template <typename T>
      inline bool is_string_range_node(const expression_node<T>* node)
      {
         return node && (expression_node<T>::e_stringvarrng == node->type());
      }

      template <typename T>
      inline bool is_const_string_node(const expression_node<T>* node)
      {
         return node && (expression_node<T>::e_stringconst == node->type());
      }

      template <typename T>
      inline bool is_const_string_range_node(const expression_node<T>* node)
      {
         return node && (expression_node<T>::e_cstringvarrng == node->type());
      }

      template <typename T>
      inline bool is_string_assignment_node(const expression_node<T>* node)
      {
         return node && (expression_node<T>::e_strass == node->type());
      }

      template <typename T>
      inline bool is_string_concat_node(const expression_node<T>* node)
      {
         return node && (expression_node<T>::e_strconcat == node->type());
      }

      template <typename T>
      inline bool is_string_function_node(const expression_node<T>* node)
      {
         return node && (expression_node<T>::e_strfunction == node->type());
      }

      template <typename T>
      inline bool is_string_condition_node(const expression_node<T>* node)
      {
         return node && (expression_node<T>::e_strcondition == node->type());
      }

      template <typename T>
      inline bool is_string_ccondition_node(const expression_node<T>* node)
      {
         return node && (expression_node<T>::e_strccondition == node->type());
      }

      template <typename T>
      inline bool is_string_vararg_node(const expression_node<T>* node)
      {
         return node && (expression_node<T>::e_stringvararg == node->type());
      }

      template <typename T>
      inline bool is_genricstring_range_node(const expression_node<T>* node)
      {
         return node && (expression_node<T>::e_strgenrange == node->type());
      }

      template <typename T>
      inline bool is_generally_string_node(const expression_node<T>* node)
      {
         if (node)
         {
            switch (node->type())
            {
               case expression_node<T>::e_stringvar     :
               case expression_node<T>::e_stringconst   :
               case expression_node<T>::e_stringvarrng  :
               case expression_node<T>::e_cstringvarrng :
               case expression_node<T>::e_strgenrange   :
               case expression_node<T>::e_strass        :
               case expression_node<T>::e_strconcat     :
               case expression_node<T>::e_strfunction   :
               case expression_node<T>::e_strcondition  :
               case expression_node<T>::e_strccondition :
               case expression_node<T>::e_stringvararg  : return true;
               default                                  : return false;
            }
         }

         return false;
      }

      class node_allocator
      {
      public:

         template <typename ResultNode, typename OpType, typename ExprNode>
         inline expression_node<typename ResultNode::value_type>* allocate(OpType& operation, ExprNode (&branch)[1])
         {
            expression_node<typename ResultNode::value_type>* result =
               allocate<ResultNode>(operation, branch[0]);
            result->node_depth();
            return result;
         }

         template <typename ResultNode, typename OpType, typename ExprNode>
         inline expression_node<typename ResultNode::value_type>* allocate(OpType& operation, ExprNode (&branch)[2])
         {
            expression_node<typename ResultNode::value_type>* result =
               allocate<ResultNode>(operation, branch[0], branch[1]);
            result->node_depth();
            return result;
         }

         template <typename ResultNode, typename OpType, typename ExprNode>
         inline expression_node<typename ResultNode::value_type>* allocate(OpType& operation, ExprNode (&branch)[3])
         {
            expression_node<typename ResultNode::value_type>* result =
               allocate<ResultNode>(operation, branch[0], branch[1], branch[2]);
            result->node_depth();
            return result;
         }

         template <typename ResultNode, typename OpType, typename ExprNode>
         inline expression_node<typename ResultNode::value_type>* allocate(OpType& operation, ExprNode (&branch)[4])
         {
            expression_node<typename ResultNode::value_type>* result =
               allocate<ResultNode>(operation, branch[0], branch[1], branch[2], branch[3]);
            result->node_depth();
            return result;
         }

         template <typename ResultNode, typename OpType, typename ExprNode>
         inline expression_node<typename ResultNode::value_type>* allocate(OpType& operation, ExprNode (&branch)[5])
         {
            expression_node<typename ResultNode::value_type>* result =
               allocate<ResultNode>(operation, branch[0],branch[1], branch[2], branch[3], branch[4]);
            result->node_depth();
            return result;
         }

         template <typename ResultNode, typename OpType, typename ExprNode>
         inline expression_node<typename ResultNode::value_type>* allocate(OpType& operation, ExprNode (&branch)[6])
         {
            expression_node<typename ResultNode::value_type>* result =
               allocate<ResultNode>(operation, branch[0], branch[1], branch[2], branch[3], branch[4], branch[5]);
            result->node_depth();
            return result;
         }

         template <typename node_type>
         inline expression_node<typename node_type::value_type>* allocate() const
         {
            return (new node_type());
         }

         template <typename node_type,
                   typename Type,
                   typename Allocator,
                   template <typename, typename> class Sequence>
         inline expression_node<typename node_type::value_type>* allocate(const Sequence<Type,Allocator>& seq) const
         {
            expression_node<typename node_type::value_type>*
            result = (new node_type(seq));
            result->node_depth();
            return result;
         }

         template <typename node_type, typename T1>
         inline expression_node<typename node_type::value_type>* allocate(T1& t1) const
         {
            expression_node<typename node_type::value_type>*
            result = (new node_type(t1));
            result->node_depth();
            return result;
         }

         template <typename node_type, typename T1>
         inline expression_node<typename node_type::value_type>* allocate_c(const T1& t1) const
         {
            expression_node<typename node_type::value_type>*
            result = (new node_type(t1));
            result->node_depth();
            return result;
         }

         template <typename node_type,
                   typename T1, typename T2>
         inline expression_node<typename node_type::value_type>* allocate(const T1& t1, const T2& t2) const
         {
            expression_node<typename node_type::value_type>*
            result = (new node_type(t1, t2));
            result->node_depth();
            return result;
         }

         template <typename node_type,
                   typename T1, typename T2>
         inline expression_node<typename node_type::value_type>* allocate_cr(const T1& t1, T2& t2) const
         {
            expression_node<typename node_type::value_type>*
            result = (new node_type(t1, t2));
            result->node_depth();
            return result;
         }

         template <typename node_type,
                   typename T1, typename T2>
         inline expression_node<typename node_type::value_type>* allocate_rc(T1& t1, const T2& t2) const
         {
            expression_node<typename node_type::value_type>*
            result = (new node_type(t1, t2));
            result->node_depth();
            return result;
         }

         template <typename node_type,
                   typename T1, typename T2>
         inline expression_node<typename node_type::value_type>* allocate_rr(T1& t1, T2& t2) const
         {
            expression_node<typename node_type::value_type>*
            result = (new node_type(t1, t2));
            result->node_depth();
            return result;
         }

         template <typename node_type,
                   typename T1, typename T2>
         inline expression_node<typename node_type::value_type>* allocate_tt(T1 t1, T2 t2) const
         {
            expression_node<typename node_type::value_type>*
            result = (new node_type(t1, t2));
            result->node_depth();
            return result;
         }

         template <typename node_type,
                   typename T1, typename T2, typename T3>
         inline expression_node<typename node_type::value_type>* allocate_ttt(T1 t1, T2 t2, T3 t3) const
         {
            expression_node<typename node_type::value_type>*
            result = (new node_type(t1, t2, t3));
            result->node_depth();
            return result;
         }

         template <typename node_type,
                   typename T1, typename T2, typename T3, typename T4>
         inline expression_node<typename node_type::value_type>* allocate_tttt(T1 t1, T2 t2, T3 t3, T4 t4) const
         {
            expression_node<typename node_type::value_type>*
            result = (new node_type(t1, t2, t3, t4));
            result->node_depth();
            return result;
         }

         template <typename node_type,
                   typename T1, typename T2, typename T3>
         inline expression_node<typename node_type::value_type>* allocate_rrr(T1& t1, T2& t2, T3& t3) const
         {
            expression_node<typename node_type::value_type>*
            result = (new node_type(t1, t2, t3));
            result->node_depth();
            return result;
         }

         template <typename node_type,
                   typename T1, typename T2, typename T3, typename T4>
         inline expression_node<typename node_type::value_type>* allocate_rrrr(T1& t1, T2& t2, T3& t3, T4& t4) const
         {
            expression_node<typename node_type::value_type>*
            result = (new node_type(t1, t2, t3, t4));
            result->node_depth();
            return result;
         }

         template <typename node_type,
                   typename T1, typename T2, typename T3, typename T4, typename T5>
         inline expression_node<typename node_type::value_type>* allocate_rrrrr(T1& t1, T2& t2, T3& t3, T4& t4, T5& t5) const
         {
            expression_node<typename node_type::value_type>*
            result = (new node_type(t1, t2, t3, t4, t5));
            result->node_depth();
            return result;
         }

         template <typename node_type,
                   typename T1, typename T2, typename T3>
         inline expression_node<typename node_type::value_type>* allocate(const T1& t1, const T2& t2,
                                                                          const T3& t3) const
         {
            expression_node<typename node_type::value_type>*
            result = (new node_type(t1, t2, t3));
            result->node_depth();
            return result;
         }

         template <typename node_type,
                   typename T1, typename T2,
                   typename T3, typename T4>
         inline expression_node<typename node_type::value_type>* allocate(const T1& t1, const T2& t2,
                                                                          const T3& t3, const T4& t4) const
         {
            expression_node<typename node_type::value_type>*
            result = (new node_type(t1, t2, t3, t4));
            result->node_depth();
            return result;
         }

         template <typename node_type,
                   typename T1, typename T2,
                   typename T3, typename T4, typename T5>
         inline expression_node<typename node_type::value_type>* allocate(const T1& t1, const T2& t2,
                                                                          const T3& t3, const T4& t4,
                                                                          const T5& t5) const
         {
            expression_node<typename node_type::value_type>*
            result = (new node_type(t1, t2, t3, t4, t5));
            result->node_depth();
            return result;
         }

         template <typename node_type,
                   typename T1, typename T2,
                   typename T3, typename T4, typename T5, typename T6>
         inline expression_node<typename node_type::value_type>* allocate(const T1& t1, const T2& t2,
                                                                          const T3& t3, const T4& t4,
                                                                          const T5& t5, const T6& t6) const
         {
            expression_node<typename node_type::value_type>*
            result = (new node_type(t1, t2, t3, t4, t5, t6));
            result->node_depth();
            return result;
         }

         template <typename node_type,
                   typename T1, typename T2,
                   typename T3, typename T4,
                   typename T5, typename T6, typename T7>
         inline expression_node<typename node_type::value_type>* allocate(const T1& t1, const T2& t2,
                                                                          const T3& t3, const T4& t4,
                                                                          const T5& t5, const T6& t6,
                                                                          const T7& t7) const
         {
            expression_node<typename node_type::value_type>*
            result = (new node_type(t1, t2, t3, t4, t5, t6, t7));
            result->node_depth();
            return result;
         }

         template <typename node_type,
                   typename T1, typename T2,
                   typename T3, typename T4,
                   typename T5, typename T6,
                   typename T7, typename T8>
         inline expression_node<typename node_type::value_type>* allocate(const T1& t1, const T2& t2,
                                                                          const T3& t3, const T4& t4,
                                                                          const T5& t5, const T6& t6,
                                                                          const T7& t7, const T8& t8) const
         {
            expression_node<typename node_type::value_type>*
            result = (new node_type(t1, t2, t3, t4, t5, t6, t7, t8));
            result->node_depth();
            return result;
         }

         template <typename node_type,
                   typename T1, typename T2,
                   typename T3, typename T4,
                   typename T5, typename T6,
                   typename T7, typename T8, typename T9>
         inline expression_node<typename node_type::value_type>* allocate(const T1& t1, const T2& t2,
                                                                          const T3& t3, const T4& t4,
                                                                          const T5& t5, const T6& t6,
                                                                          const T7& t7, const T8& t8,
                                                                          const T9& t9) const
         {
            expression_node<typename node_type::value_type>*
            result = (new node_type(t1, t2, t3, t4, t5, t6, t7, t8, t9));
            result->node_depth();
            return result;
         }

         template <typename node_type,
                   typename T1, typename T2,
                   typename T3, typename T4,
                   typename T5, typename T6,
                   typename T7, typename T8,
                   typename T9, typename T10>
         inline expression_node<typename node_type::value_type>* allocate(const T1& t1, const  T2&  t2,
                                                                          const T3& t3, const  T4&  t4,
                                                                          const T5& t5, const  T6&  t6,
                                                                          const T7& t7, const  T8&  t8,
                                                                          const T9& t9, const T10& t10) const
         {
            expression_node<typename node_type::value_type>*
            result = (new node_type(t1, t2, t3, t4, t5, t6, t7, t8, t9, t10));
            result->node_depth();
            return result;
         }

         template <typename node_type,
                   typename T1, typename T2, typename T3>
         inline expression_node<typename node_type::value_type>* allocate_type(T1 t1, T2 t2, T3 t3) const
         {
            expression_node<typename node_type::value_type>*
            result = (new node_type(t1, t2, t3));
            result->node_depth();
            return result;
         }

         template <typename node_type,
                   typename T1, typename T2,
                   typename T3, typename T4>
         inline expression_node<typename node_type::value_type>* allocate_type(T1 t1, T2 t2,
                                                                               T3 t3, T4 t4) const
         {
            expression_node<typename node_type::value_type>*
            result = (new node_type(t1, t2, t3, t4));
            result->node_depth();
            return result;
         }

         template <typename node_type,
                   typename T1, typename T2,
                   typename T3, typename T4,
                   typename T5>
         inline expression_node<typename node_type::value_type>* allocate_type(T1 t1, T2 t2,
                                                                               T3 t3, T4 t4,
                                                                               T5 t5) const
         {
            expression_node<typename node_type::value_type>*
            result = (new node_type(t1, t2, t3, t4, t5));
            result->node_depth();
            return result;
         }

         template <typename node_type,
                   typename T1, typename T2,
                   typename T3, typename T4,
                   typename T5, typename T6>
         inline expression_node<typename node_type::value_type>* allocate_type(T1 t1, T2 t2,
                                                                               T3 t3, T4 t4,
                                                                               T5 t5, T6 t6) const
         {
            expression_node<typename node_type::value_type>*
            result = (new node_type(t1, t2, t3, t4, t5, t6));
            result->node_depth();
            return result;
         }

         template <typename node_type,
                   typename T1, typename T2,
                   typename T3, typename T4,
                   typename T5, typename T6, typename T7>
         inline expression_node<typename node_type::value_type>* allocate_type(T1 t1, T2 t2,
                                                                               T3 t3, T4 t4,
                                                                               T5 t5, T6 t6,
                                                                               T7 t7) const
         {
            expression_node<typename node_type::value_type>*
            result = (new node_type(t1, t2, t3, t4, t5, t6, t7));
            result->node_depth();
            return result;
         }

         template <typename T>
         void inline free(expression_node<T>*& e) const
         {
            exprtk_debug(("node_allocator::free() - deleting expression_node "
                          "type: %03d addr: %p\n",
                          static_cast<int>(e->type()),
                          reinterpret_cast<void*>(e)));
            delete e;
            e = 0;
         }
      };

      inline void load_operations_map(std::multimap<std::string,details::base_operation_t,details::ilesscompare>& m)
      {
         #define register_op(Symbol,Type,Args)                                               \
         m.insert(std::make_pair(std::string(Symbol),details::base_operation_t(Type,Args))); \

         register_op(      "abs", e_abs     , 1)
         register_op(     "acos", e_acos    , 1)
         register_op(    "acosh", e_acosh   , 1)
         register_op(     "asin", e_asin    , 1)
         register_op(    "asinh", e_asinh   , 1)
         register_op(     "atan", e_atan    , 1)
         register_op(    "atanh", e_atanh   , 1)
         register_op(     "ceil", e_ceil    , 1)
         register_op(      "cos", e_cos     , 1)
         register_op(     "cosh", e_cosh    , 1)
         register_op(      "exp", e_exp     , 1)
         register_op(    "expm1", e_expm1   , 1)
         register_op(    "floor", e_floor   , 1)
         register_op(      "log", e_log     , 1)
         register_op(    "log10", e_log10   , 1)
         register_op(     "log2", e_log2    , 1)
         register_op(    "log1p", e_log1p   , 1)
         register_op(    "round", e_round   , 1)
         register_op(      "sin", e_sin     , 1)
         register_op(     "sinc", e_sinc    , 1)
         register_op(     "sinh", e_sinh    , 1)
         register_op(      "sec", e_sec     , 1)
         register_op(      "csc", e_csc     , 1)
         register_op(     "sqrt", e_sqrt    , 1)
         register_op(      "tan", e_tan     , 1)
         register_op(     "tanh", e_tanh    , 1)
         register_op(      "cot", e_cot     , 1)
         register_op(  "rad2deg", e_r2d     , 1)
         register_op(  "deg2rad", e_d2r     , 1)
         register_op( "deg2grad", e_d2g     , 1)
         register_op( "grad2deg", e_g2d     , 1)
         register_op(      "sgn", e_sgn     , 1)
         register_op(      "not", e_notl    , 1)
         register_op(      "erf", e_erf     , 1)
         register_op(     "erfc", e_erfc    , 1)
         register_op(     "ncdf", e_ncdf    , 1)
         register_op(     "frac", e_frac    , 1)
         register_op(    "trunc", e_trunc   , 1)
         register_op(    "atan2", e_atan2   , 2)
         register_op(      "mod", e_mod     , 2)
         register_op(     "logn", e_logn    , 2)
         register_op(      "pow", e_pow     , 2)
         register_op(     "root", e_root    , 2)
         register_op(   "roundn", e_roundn  , 2)
         register_op(    "equal", e_equal   , 2)
         register_op("not_equal", e_nequal  , 2)
         register_op(    "hypot", e_hypot   , 2)
         register_op(      "shr", e_shr     , 2)
         register_op(      "shl", e_shl     , 2)
         register_op(    "clamp", e_clamp   , 3)
         register_op(   "iclamp", e_iclamp  , 3)
         register_op(  "inrange", e_inrange , 3)
         #undef register_op
      }

   } // namespace details
}
