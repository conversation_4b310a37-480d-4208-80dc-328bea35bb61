#pragma once

#include "CEAnimation/AnimBase.h"
#include "CEAnimation/Transform/AnimPose.h"

namespace cross::anim {
/** In AnimationRunTime Library, we extract animation data based on Skeleton hierarchy, not ref pose hierarchy.
 *	Ref pose will need to be re-mapped later
 **/
class Animation_API AnimRuntime
{
public:
    enum TransformBlendType
    {
        Overwrite,
        Accumulate
    };

    /**
     * Advance CurrentTime to CurrentTime + MoveDelta.
     * It will handle wrapping if bAllowLooping is true
     *
     * return AdvanceAnim type
     */
    template<class T, class = std::enable_if_t<TIsInstanceOf<T, time::AnimTimeHandle>::value>> 
    static anim::AdvanceAnim::Type AdvanceTime(const bool& bAllowLooping, const float& moveDelta, T& inOutTime, const T& endTime)
    {
        inOutTime.mVal += moveDelta;

        if (inOutTime < 0.f || inOutTime > endTime)
        {
            if (bAllowLooping)
            {
                if (endTime != 0.f)
                {
                    inOutTime.mVal = std::fmod(inOutTime, endTime);

                    // Fmod doesn't give result that falls into (0, EndTime), but one that falls into (-EndTime, EndTime). Negative values need to be handled in custom way
                    if (inOutTime < 0.f)
                        inOutTime.mVal += endTime.mVal;
                }
                else
                {
                    // end time is 0.f
                    inOutTime.mVal = 0.f;
                }

                // it has been looped
                return AdvanceAnim::Type::AA_Looped;
            }
            else
            {
                // If not, snap time to end of sequence and stop playing.
                inOutTime.mVal = std::clamp(inOutTime.mVal, 0.f, endTime.mVal);
                return AdvanceAnim::Type::AA_Finished;
            }
        }

        return AdvanceAnim::Type::AA_Default;
    }

    template<class T, class = std::enable_if_t<TIsInstanceOf<T, time::AnimTimeHandle>::value>> 
    static anim::AdvanceAnim::Type AdvanceTime(const bool& bAllowLooping, const float& moveDelta, T& inOutTime, const T& startTime, const T& endTime)
    {
        Assert(inOutTime >= startTime && inOutTime <= endTime && startTime <= endTime);
        inOutTime.mVal += moveDelta;

        if (inOutTime < startTime || inOutTime > endTime)
        {
            if (bAllowLooping)
            {
                auto length = endTime - startTime;

                if (length < 0.0001f)
                {
                    inOutTime.mVal = std::fmod(inOutTime - startTime, length) + startTime;

                    // Fmod doesn't give result that falls into (0, EndTime), but one that falls into (-EndTime, EndTime). Negative values need to be handled in custom way
                    if (inOutTime < 0.f)
                        inOutTime.mVal += length;
                }
                // end time is close to start time
                else
                    inOutTime.mVal = startTime;

                // it has been looped
                return AdvanceAnim::Type::AA_Looped;
            }
            else
            {
                // If not, snap time to end of sequence and stop playing.
                inOutTime.mVal = std::clamp(inOutTime.mVal, startTime.mVal, endTime.mVal);
                return AdvanceAnim::Type::AA_Finished;
            }
        }

        return AdvanceAnim::Type::AA_Default;
    }

    static anim::AdvanceAnim::Type AdvanceTime(const bool& bAllowLooping, const float& moveDelta, float& inOutTime, const float& startTime, const float& endTime)
    {
        Assert(inOutTime >= startTime && inOutTime <= endTime && startTime <= endTime);
        inOutTime += moveDelta;

        if (inOutTime < startTime || inOutTime > endTime)
        {
            if (bAllowLooping)
            {
                auto length = endTime - startTime;

                if (length < 0.0001f)
                {
                    inOutTime = std::fmod(inOutTime - startTime, length) + startTime;

                    // Fmod doesn't give result that falls into (0, EndTime), but one that falls into (-EndTime, EndTime). Negative values need to be handled in custom way
                    if (inOutTime < 0.f)
                        inOutTime += length;
                }
                // end time is close to start time
                else
                    inOutTime = startTime;

                // it has been looped
                return AdvanceAnim::Type::AA_Looped;
            }
            else
            {
                // If not, snap time to end of sequence and stop playing.
                inOutTime = std::clamp(inOutTime, startTime, endTime);
                return AdvanceAnim::Type::AA_Finished;
            }
        }

        return AdvanceAnim::Type::AA_Default;
    }

    /** convert transform to additive */
    static void ConvertTransformToAdditive(NodeTransform& outTargetTrasnform, NodeTransform const& inBaseTransform);

    /** Convert TargetPose into an AdditivePose, by doing TargetPose = TargetPose - BasePose */
    static void ConvertPoseToAdditive(RootSpacePose& outTargetPose, RootSpacePose const& inBasePose);

    /** Convert LocalPose into RootSpaceRotations. Rotations are NOT normalized. */
    static void ConvertPoseToRootSpaceForRotationOnly(RootSpacePose& outPose);

    /** Convert a RootSpaceRotations pose to Local Space. Rotations are NOT normalized. */
    static void ConvertRootSpaceForRotationOnlyToLocalSpace(RootSpacePose& outPose);

    /** Accumulate Additive Pose based on AdditiveType*/
    static void AccumulateAdditivePose(RootSpacePose& outPose, const RootSpacePose& additivePose, AdditiveAnimSpace::Type additiveType, float weight = 1.0f);

private:
    /** Accumulates weighted AdditivePose to BasePose. Rotations are NOT normalized. */
    static void AccumulateLocalSpaceAdditivePoseInternal(RootSpacePose& outPose, const RootSpacePose& additivePose, float Weight);

    /** Accumulate a RootSpaceRotation Additive pose to a local pose. Rotations are NOT normalized */
    static void AccumulateRootSpaceRotationAdditiveToLocalPoseInternal(RootSpacePose& outPose, const RootSpacePose& additivePose, float Weight);
};

template<int> FORCEINLINE void BlendTransform(const NodeTransform& source, NodeTransform& dest, const float blendWeight);

template<> FORCEINLINE void BlendTransform<AnimRuntime::TransformBlendType::Overwrite>(const NodeTransform& source, NodeTransform& dest, const float blendWeight)
{
    // const ScalarRegister VBlendWeight(BlendWeight);
    dest = source * blendWeight;
}

template<> FORCEINLINE void BlendTransform<AnimRuntime::TransformBlendType::Accumulate>(const NodeTransform& source, NodeTransform& dest, const float blendWeight)
{
    // const ScalarRegister VBlendWeight(BlendWeight);
#ifndef _MANAGED
    dest = NodeTransform::AccumulateByShortestRotation(dest, source, blendWeight);
#endif
}

template<int POSE_BLEND_MODE> FORCEINLINE void BlendPose(RootSpacePose& sourcePose, RootSpacePose& dstPoses, const float blendWeight)
{
    for (int i = 0; i < sourcePose.GetNumBones(); i++)
    {
        auto Handle = PoseBoneHandle::From(i);
        BlendTransform<POSE_BLEND_MODE>(sourcePose[Handle], dstPoses[Handle], blendWeight);
    }
}

Animation_API void BlendPosesTogether(std::vector<RootSpacePose>& sourcePoses, const std::vector<float>& sourceWeights, RootSpacePose& outPose);

Animation_API void MirrorNodeTransform(SkBoneHandle inMirrorBoneIndex, const Skeleton* inRunSkelt, Axis::Type inAxis, NodeTransform& outTrans);

struct InertializationPolynomial
{
    // x = A  * t ^ 5 +  B  * t ^ 4 +  C  * t ^ 3 +  D  * t ^ 2 +  E  * t +  F
    float A, B, C, D, E, F;

    // Eval(t) >= 0 with t in [0, range]
    float range;

    void Init(float x0, float v0, float t1);

    float Eval(float t) const
    {
        t = MathUtils::Clamp(t, 0.f, range);
        return ((((A * t + B) * t + C) * t + D) * t + E) * t + F;
    }
};

struct InertializationBone
{
    Float3 TranslateDir;
    Float3 RotateDir;
    Float3 ScaleDir;

    InertializationPolynomial TranslatePolynomial;
    InertializationPolynomial RotatePolynomial;
    InertializationPolynomial ScalePolynomial;

    static void InitVector(const Float3& inPrevVecN, const Float3& inPrevVecN_1, const Float3& inCurVec, float deltaTime, float duration, Float3& outDir, InertializationPolynomial& outPolynomial);
    static void InitQuaternion(const Quaternion& inPrevRotN, const Quaternion& inPrevRotN_1, const Quaternion& inCurRot, float deltaTime, float duration, Float3& outDir, InertializationPolynomial& outPolynomial);

    static void ApplyVector(Float3& inOutCurVec, float t, const Float3& inVecDir, const InertializationPolynomial& inPolynomial);
    static void ApplyQuaternion(Quaternion& inOutCurQuat, float t, const Float3& inQuatDir, const InertializationPolynomial& inPolynomial);
};

class Animation_API InertializationPose final
{
public:
    enum State
    {
        InActive,
        Starting,
        Active
    };

    InertializationPose() = default;
    ~InertializationPose() = default;

    void SetState(State inState) { mState = inState; }
    State GetState() const { return mState; }

    void PushPose(const RootSpacePose& CurPose);

    void PushDeltaTime(float t)
    {
        mPrevDeltaTime = mCurDeltaTime;
        mCurDeltaTime = t;
    }

    // cal cal v0, x0, t1, a0 for each bone's rts
    /* CurPose : Pose to be blend to
     *  t1 : total amount of blendtime
     */
    void Start(RootSpacePose& CurPose, float t1);

    /* CurPose:  resulted pose;
     *  CurTime:  current blend time, not normalized time (ie. not 0-1)
     */
    void Apply(RootSpacePose& CurPose, float CurTime);

private:
    SInt32 mPrevN_1_Index{-1};
    SInt32 mPrevN_Index{-1};
    float mPrevDeltaTime{1.0f};
    float mCurDeltaTime{1.0f};
    RootSpacePose mPrevPose[2];
    State mState;

    std::vector<InertializationBone> mInertiaBones;
};

}   // namespace cross::anim
