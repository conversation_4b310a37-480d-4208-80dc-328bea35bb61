#ifndef CROSSENGIN_ANIMATIONFORWARD_H
#define CROSSENGIN_ANIMATIONFORWARD_H


#if CROSSENGINE_WIN
#pragma warning(disable : 4251)
#pragma warning(disable : 4275)
#ifdef CEAnimation_EXPORTS
#define Animation_API __declspec(dllexport)
#else
#define Animation_API __declspec(dllimport)
#endif
#else
#ifdef __GNUC__
#define Animation_API __attribute__((visibility("default")))
#else
#define Animation_API
#endif
#endif


#endif//CROSSENGIN_ANIMATIONFORWARD_H
