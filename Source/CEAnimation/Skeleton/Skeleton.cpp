
#include "EnginePrefix.h"
#include "CEAnimation/Skeleton/Skeleton.h"
#include "CEAnimation/Skeleton/SkeletonModifier.h"
#include "Resource/Animation/Sequence/AnimSequence.h"

#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/FrameParam.h"
#include "CECommon/Common/FrameContainer.h"

namespace cross::skeleton {
/////////////////////////////////////////////
// Skeleton
//
/////////////////////////////////////////////
bool Skeleton::Initialize(const StreamingSkeleton* inStreamingSkelt)
{
    if (inStreamingSkelt == nullptr)
        return false;

    // Generate reference skeleton
    mRefSkeleton = inStreamingSkelt->GetReferenceSkeleton();

    // Generate runtime skeleton bone tree
    InitBoneTree(inStreamingSkelt->GetRetargetModes());

    auto streamingTwinBones = inStreamingSkelt->GetTwinBones();
    std::for_each(streamingTwinBones.begin(), streamingTwinBones.end(), [this](auto& elem) 
    {
        this->mBoneTree[elem.First].SymBoneH = elem.Second;
        this->mBoneTree[elem.Second].SymBoneH = elem.First;
    });

    // Generate slot groups which single group contain multi slots name
    mSlotGroups = inStreamingSkelt->GetSlotGroups();

    return true;
}

void Skeleton::InitBoneTree(std::vector<BoneTranslateRetargetingMode> const& bonesRetargetMode)
{
    Assert(bonesRetargetMode.size() == mRefSkeleton.GetRawBoneNum());

    UInt32 size = static_cast<UInt32>(mRefSkeleton.GetRawBoneNum());
    mBoneTree.clear();
    mBoneTree.reserve(size);   // resize more default construct

    for (SkBoneHandle skeltBoneIndex = SkBoneHandle::From(0U); skeltBoneIndex < size; ++skeltBoneIndex)
    {
        mBoneTree.emplace_back(mRefSkeleton.GetRawBoneName(skeltBoneIndex), 
            mRefSkeleton.GetRawBoneParentIndex(skeltBoneIndex), bonesRetargetMode[skeltBoneIndex]);
    }
}

void Skeleton::InitBoneTree()
{
    if (mRefSkeleton.GetRawBoneNum() == 0)
        return;

    std::vector<BoneTranslateRetargetingMode> bonesRetargetMode;
    bonesRetargetMode.resize(mRefSkeleton.GetRawBoneNum(), BoneTranslateRetargetingMode::Skeleton);

    InitBoneTree(bonesRetargetMode);
}

bool Skeleton::MergeAllBonesToBoneTree(const ReferenceSkeleton& inRefSkeleton)
{
    std::vector<SkBoneHandle> requiredBoneIndices;

    auto boneNum = inRefSkeleton.GetRawBoneNum();
    // for now add all in this case.
    requiredBoneIndices.resize(boneNum);

    // gather bone list
    for (SkBoneHandle i = SkBoneHandle::From(0U); i < boneNum; ++i)
        requiredBoneIndices[i] = i;

    // merge bones to the selected skeleton
    if (requiredBoneIndices.size() > 0)
        return MergeBonesToBoneTree(inRefSkeleton, requiredBoneIndices);

    return false;
}

bool Skeleton::MergeBonesToBoneTree(const ReferenceSkeleton& inRefSkeleton, const std::vector<SkBoneHandle>& inRequiredRefBones)
{
    // see if it needs all animation data to remap - only happens when bone structure CHANGED - added
    bool bSuccess = false;
    bool bShouldHandleHierarchyChange = false;

    // clear cache data since it won't work anymore once this is done
    ClearCacheData();

    // can we play? - hierarchy matches
    if (IsCompatible(inRefSkeleton))
    {
        // Exclude bones who do not have a parent.
        std::vector<SkBoneHandle> filteredRequiredBones;
        for (size_t requiredIndex = 0, size = inRequiredRefBones.size(); requiredIndex < size; ++requiredIndex)
        {
            SkBoneHandle requiredId = inRequiredRefBones[requiredIndex];

            // Always add root bone.
            if (requiredId == SK_BONE_INDEX_ROOT)
                filteredRequiredBones.push_back(requiredId);
            else
            {
                SkBoneHandle requiredParId = inRefSkeleton.GetRawBoneParentIndex(requiredId);

                if (requiredParId != SK_BONE_INDEX_NONE)
                    filteredRequiredBones.push_back(requiredId);
            }
        }

        ReferenceSkeletonModifier refSkelModifier(mRefSkeleton, this);

        for (SInt32 requiredIndex = 0; requiredIndex < filteredRequiredBones.size(); ++requiredIndex)
        {
            const SkBoneHandle& meshBoneIndex = filteredRequiredBones[requiredIndex];
            const SkBoneHandle& skeletonBoneIndex = mRefSkeleton.FindRawBoneIndex(inRefSkeleton.GetRawBoneName(meshBoneIndex));

            // Bone doesn't already exist. Add it.
            if (skeletonBoneIndex == SK_BONE_INDEX_NONE)
            {
                RefBoneNode const& mergedMeshBone = inRefSkeleton.GetRefBoneNodes()[meshBoneIndex];
                CEName const& mergeBoneParentName = inRefSkeleton.GetRawBoneName(inRefSkeleton.GetRawBoneParentIndex(meshBoneIndex));

                // Fix up ParentIndex for our new Skeleton.
                SkBoneHandle newMeshBone_ParentIndex = SK_BONE_INDEX_ROOT;

                if (mRefSkeleton.GetRawBoneNum() == 0 || inRefSkeleton.GetRawBoneParentIndex(meshBoneIndex) == SK_BONE_INDEX_NONE)
                    newMeshBone_ParentIndex = SK_BONE_INDEX_NONE;   // root
                else
                    newMeshBone_ParentIndex = mRefSkeleton.FindRawBoneIndex(mergeBoneParentName);

                // Extract new bone node with same local transform
                RefBoneNode newMeshBone(mergedMeshBone, newMeshBone_ParentIndex);

                refSkelModifier.Add(newMeshBone, inRefSkeleton.GetRawRefBonePoseTranslate()[meshBoneIndex], inRefSkeleton.GetRawRefBonePoseRotate()[meshBoneIndex], inRefSkeleton.GetRawRefBonePoseScale()[meshBoneIndex], mergeBoneParentName);

                bShouldHandleHierarchyChange = true;
            }
        }

        bSuccess = true;
    }

    // if succeed
    if (bShouldHandleHierarchyChange)
    {
        InitBoneTree();

        // call back for editor in the future
    }

    return bSuccess;
}

void Skeleton::OverrideBoneTreeRetargetMode(bool isMerged, BoneTranslateRetargetingMode mode)
{
    auto const& nodes = mRefSkeleton.GetRefBoneNodes();
    Assert(mBoneTree.size() == nodes.size());

    for (int i = 0; i < nodes.size(); i++)
    {
        if (nodes[i].IsMergedSkin == isMerged)
        {
            mBoneTree[i].TranslationRetargetingMode = mode;
        }
    }
}

bool Skeleton::IsCompatible(const ReferenceSkeleton& inRefSkeleton, SkBoneHandle* outRunSkMissedJoint, SkBoneHandle* outResRefSkMissedJoint) const
{
    // originally we made sure at least matches more than 50%
    // but then slave components can't play since they're only partial
    // if the hierarchy matches, and if it's more then 1 bone, we allow
    bool result = ReferenceSkeleton::IsCompatible(mRefSkeleton, inRefSkeleton, outRunSkMissedJoint, outResRefSkMissedJoint) > 0;

    // We Should build link up only when Compatible.
    // And we only build once for each new inRefSkeleton, because we will Record Compatible checking result for RefSkeleton exists
    if (result)
    {
        const_cast<Skeleton*>(this)->BuildLinkup(inRefSkeleton);
    }

    return result;
}

bool Skeleton::IsCompatibleAnimSeq(const cross::anim::AnimSequence* inAnimSeq) const
{
    const auto& inRefSkeleton = inAnimSeq->GetReferenceSkeleton();
    auto hierarchyHash = inRefSkeleton.GetHierarchyHash();
    // inRefSkeleton already checked with cur runtime skeleton, and link up is already built up when Compatible
    if (auto itr = mCompatibilityMap.find(hierarchyHash); itr != mCompatibilityMap.end())
        return itr->second;

    // inRefSkeleton is new coming, so check Compatible
    bool result = IsCompatible(inRefSkeleton);
    mCompatibilityMap[hierarchyHash] = result;
    return result;
}

bool Skeleton::IsCompatibleSkeleton(const Skeleton* inSkeleton) const
{
    const auto& inRefSkeleton = inSkeleton->GetReferenceSkeleton();
    auto hierarchyHash = inRefSkeleton.GetHierarchyHash();
    // inRefSkeleton already checked with cur runtime skeleton, and link up is already built up when Compatible
    if (auto itr = mCompatibilityMap.find(hierarchyHash); itr != mCompatibilityMap.end())
        return itr->second;

    // inRefSkeleton is new coming, so check Compatible
    bool result = IsCompatible(inRefSkeleton);
    mCompatibilityMap[hierarchyHash] = result;
    return result;
}

//bool Skeleton::IsCompatibleMesh(const resource::MeshAssetDataResource* InSkeletalMesh) const
bool Skeleton::IsCompatibleMesh(const skeleton::ReferenceSkeleton* InRefSkeletal) const
{
    //const auto& inRefSkeleton = *(InSkeletalMesh->GetRefSkeleton());
    auto hierarchyHash = InRefSkeletal->GetHierarchyHash();
    // inRefSkeleton already checked with cur runtime skeleton, and link up is already built up when Compatible
    if (auto itr = mCompatibilityMap.find(hierarchyHash); itr != mCompatibilityMap.end())
        return itr->second;

    // inRefSkeleton is new coming, so check Compatible
    bool result = IsCompatible(*InRefSkeletal);
    mCompatibilityMap[hierarchyHash] = result;
    return result;
}

void Skeleton::ClearCacheData()
{
    mLinkupCache.clear();
    mLinkupCacheIndexMap.clear();
    mCompatibilityMap.clear();
}

SInt32 Skeleton::BuildLinkup(const ReferenceSkeleton& inRefSkeleton)
{
    const ReferenceSkeleton& skeletonRefSkel = mRefSkeleton;
    const ReferenceSkeleton& resourceRefSkel = inRefSkeleton;

    SkeletonToResourceLinkup newResourceLinkup;

    // First, map resource ref-skeleton(may come from mesh or animation) to runtime skeleton
    const UInt32 resBoneNum = static_cast<UInt32>(resourceRefSkel.GetRawBoneNum());
    newResourceLinkup.ResourceToSkeletonTable.resize(resBoneNum);

    for (SkBoneHandle resBoneIndex = SkBoneHandle::From(0U); resBoneIndex < resBoneNum; ++resBoneIndex)
    {
        const CEName resBoneName = resourceRefSkel.GetRawBoneName(resBoneIndex);
        SkBoneHandle skeletonBoneIndex = skeletonRefSkel.FindRawBoneIndex(resBoneName);

        // Resource Ref skeleton has bone that not exist in runtime skeleton
        if (skeletonBoneIndex == SK_BONE_INDEX_NONE)
            newResourceLinkup.Mode = RertargetingSkeletonMode::LeafBoneDiff;

        newResourceLinkup.ResourceToSkeletonTable[resBoneIndex] = skeletonBoneIndex;
    }

    // Second, map runtime skeleton to resource ref-skeleton(may come from mesh or animation)
    const UInt32 skeletonBoneNum = static_cast<UInt32>(skeletonRefSkel.GetRawBoneNum());
    newResourceLinkup.SkeletonToResourceTable.resize(skeletonBoneNum);

    for (SkBoneHandle skeletonBoneIndex = SkBoneHandle::From(0U); skeletonBoneIndex < skeletonBoneNum; ++skeletonBoneIndex)
    {
        const CEName skeletonBoneName = skeletonRefSkel.GetRawBoneName(skeletonBoneIndex);
        const SkBoneHandle resourceBoneIndex = resourceRefSkel.FindRawBoneIndex(skeletonBoneName);

        newResourceLinkup.SkeletonToResourceTable[skeletonBoneIndex] = resourceBoneIndex;
    }

    auto itrMesh = mLinkupCacheIndexMap.find(inRefSkeleton.GetHierarchyHash());
    if (itrMesh != mLinkupCacheIndexMap.end())
        mLinkupCache[itrMesh->second] = std::move(newResourceLinkup);
    else
    {
        mLinkupCache.push_back(std::move(newResourceLinkup));
        mLinkupCacheIndexMap.insert({inRefSkeleton.GetHierarchyHash(), static_cast<SInt32>(mLinkupCache.size() - 1)});
    }

    return mLinkupCacheIndexMap.find(inRefSkeleton.GetHierarchyHash())->second;
}

void Skeleton::RemoveLinkup(const ReferenceSkeleton& inRefSkeleton)
{
    auto itr = mLinkupCacheIndexMap.find(inRefSkeleton.GetHierarchyHash());
    if (itr != mLinkupCacheIndexMap.end())
    {
        auto linkupEraseIndex = itr->second;
        auto linkupLastIndex = mLinkupCache.size() - 1;

        mLinkupCacheIndexMap.erase(itr);

        itr = std::find_if(mLinkupCacheIndexMap.begin(), mLinkupCacheIndexMap.end(), [=](robin_hood::pair<SkHierarchyHashId, SInt32> elem) {
            if (elem.second == linkupLastIndex)
                return true;

            return false;
        });

        if (itr != mLinkupCacheIndexMap.end())
        {
            itr->second = linkupEraseIndex;
        }

        // Back swap erase
        mLinkupCache[linkupEraseIndex] = mLinkupCache[linkupLastIndex];
        mLinkupCache.resize(mLinkupCache.size() - 1);
    }
}

SInt32 Skeleton::GetResourceLinkupIndex(const ReferenceSkeleton& inRefSkeleton) const
{
    auto itr = mLinkupCacheIndexMap.find(inRefSkeleton.GetHierarchyHash());

    // we will build link up cache when IsCompatible() is true
    if (itr == mLinkupCacheIndexMap.end())
    {
        LOG_ERROR("You need to check compatible before getting link up index");
        Assert(false);
    }

    return itr->second;
}

SkBoneHandle Skeleton::GetSkeletonBoneIndexFromResBoneIndex(const ReferenceSkeleton& inRefSkeleton, const SkBoneHandle resBoneIndex) const
{
    Assert(resBoneIndex != SK_BONE_INDEX_NONE);
    const SInt32 linkupCacheIdx = GetResourceLinkupIndex(inRefSkeleton);
    const SkeletonToResourceLinkup& linkupTable = mLinkupCache[linkupCacheIdx];

    return linkupTable.ResourceToSkeletonTable[resBoneIndex];
}

SkBoneHandle Skeleton::GetResBoneIndexFromSkeletonBoneIndex(const ReferenceSkeleton& inRefSkeleton, const SkBoneHandle skeletonBoneIndex) const
{
    Assert(skeletonBoneIndex != SK_BONE_INDEX_NONE);
    const SInt32 linkupCacheIdx = GetResourceLinkupIndex(inRefSkeleton);
    const SkeletonToResourceLinkup& linkupTable = mLinkupCache[linkupCacheIdx];

    return linkupTable.SkeletonToResourceTable[skeletonBoneIndex];
}

BoneTranslateRetargetingMode Skeleton::GetBoneRetargetingMode(SkBoneHandle const inSkeltBoneIndex) const
{
    Assert(IsValidBoneIndex(inSkeltBoneIndex));
    return mBoneTree[inSkeltBoneIndex].TranslationRetargetingMode;
}

SkBoneHandle Skeleton::GetSymmetricBoneIndex(SkBoneHandle const inSkeltBoneIndex) const
{
    Assert(IsValidBoneIndex(inSkeltBoneIndex));
    return mBoneTree[inSkeltBoneIndex].SymBoneH;
}

SkBoneHandle Skeleton::GetParentBoneIndex(SkBoneHandle const inSkeltBoneIndex) const
{
    Assert(IsValidBoneIndex(inSkeltBoneIndex));
    return mBoneTree[inSkeltBoneIndex].ParentBoneH;
}

const CEName& Skeleton::GetBoneNameInBoneTree(SkBoneHandle const inBoneTreeIndex) const 
{ 
    return mBoneTree[inBoneTreeIndex].Name; 
}

SkBoneHandle Skeleton::GetBoneNumOfBoneTree() const 
{ 
    return SkBoneHandle{static_cast<UInt32>(mBoneTree.size())}; 
}

bool Skeleton::IsValidBoneIndex(SkBoneHandle const inSkeltBoneIndex) const
{
    return inSkeltBoneIndex >= 0U && inSkeltBoneIndex < mBoneTree.size();
}

void Skeleton::SetBoneRetargetingMode(SkBoneHandle boneIndex, BoneTranslateRetargetingMode retargetingMode) 
{
    Assert(IsValidBoneIndex(boneIndex));
    mBoneTree[boneIndex].TranslationRetargetingMode = retargetingMode;
}

void Skeleton::SetBoneMirrorBoneInfo(SkBoneHandle boneIndex, SkBoneHandle mirrorBoneIndex)
{
    if (!IsValidBoneIndex(boneIndex) || !IsValidBoneIndex(mirrorBoneIndex))
        return;

    if (boneIndex == mirrorBoneIndex)
        return;

    mBoneTree[boneIndex].SymBoneH = mirrorBoneIndex;
}

}   // namespace cross::skeleton
