#include "EnginePrefix.h"
#include "CEAnimation/AnimRuntime.h"
#include "CEAnimation/Composite/AnimCompositeBase.h"
#include "CEAnimation/Composite/AnimSectionedTrack.h"

namespace cross::anim
{
    /////////////////////////////////////////////
    // AnimCompositeBase
    //
    /////////////////////////////////////////////
    static std::vector<AnimNotifyEvent*> MergeAnimNotifyTrack(const std::vector<AnimNotifyTrack>& tracks) {
        std::vector<AnimNotifyEvent*> mergedTrack;
        for (auto& track : tracks)
            mergedTrack.insert(mergedTrack.begin(), track.Notifies.begin(), track.Notifies.end());
        return mergedTrack;
    }

    AnimCompositeBase::AnimCompositeBase(const AnimNotifyTrack& notifyTrack, const FloatCurveList& animCurves)
        : mNotifies(notifyTrack.Notifies)
        , mAnimCurves(animCurves)
    {}

    AnimCompositeBase::AnimCompositeBase(const std::vector<AnimNotifyTrack>& notifyTracks, const FloatCurveList& animCurves)
        : mNotifies(MergeAnimNotifyTrack(notifyTracks))
        , mAnimCurves(animCurves)
    {
    }

    void AnimCompositeBase::GetAllAnimSequence(FrameVector<const IAnimSequence*>* inContainer) const
    {
        Assert(inContainer);
        inContainer->Clear();

        for (auto const& seqPtr : mRunAnims)
            inContainer->EmplaceBack(seqPtr.get());
    }

    void AnimCompositeBase::ExtractRootMotionFromTrack(
        const CEName& slotName,
        TrackUnWrapperH startTrackPosition,
        TrackUnWrapperH endTrackPosition,
        RootMotionParams& rootMotion) const
    {
        auto slotAnimTrackPtr = GetTrack(slotName);

        if (slotAnimTrackPtr == nullptr)
        {
            LOG_WARN("ExtractRootMotionFromTrack find track {} failed. ", slotName.GetCString());
            return;
        }

        std::vector<RootMotionExtractionStep> rootMotionExtractionSteps;
        slotAnimTrackPtr->GetRootMotionExtractionStepsFromTrackRange(startTrackPosition, endTrackPosition, rootMotionExtractionSteps);

#if CROSSENGINE_DEBUG
        //LOG_INFO("AnimComposite::ExtractRootMotionFromTrack, NumSteps: {}, StartTrackPosition: {}, EndTrackPosition: {}",
        //rootMotionExtractionSteps.size(), startTrackPosition.mVal, endTrackPosition.mVal);
#endif

        // Go through steps sequentially, extract root motion, and accumulate it.
        // This has to be done in order so root motion translation & rotation is applied properly (as translation is relative to rotation)
        for (auto stepIndex = 0; stepIndex < rootMotionExtractionSteps.size(); ++stepIndex)
        {
            const RootMotionExtractionStep& currentStep = rootMotionExtractionSteps[stepIndex];
            if (currentStep.IAnimSeqPtr->HasRootMotion())
            {
                NodeTransform deltaTransform = currentStep.IAnimSeqPtr->ExtractRootMotionFromRange(currentStep.StartPos, currentStep.EndPos);
                rootMotion.Accumulate(deltaTransform);

#if CROSSENGINE_DEBUG
                //LOG_INFO("CurrentStep: {}, StartPos: {}, EndPos: {}, Anim: [{}] DeltaTransform Translation: {} {} {}",
                //stepIndex, currentStep.StartPos.mVal, currentStep.EndPos.mVal, currentStep.IAnimSeqPtr->GetName(),
                //deltaTransform.GetTranslation().x, deltaTransform.GetTranslation().y, deltaTransform.GetTranslation().z);
#endif
            }
        }
    }

    void AnimCompositeBase::ExtractNotifiesFromSelfRange(const TrackUnWrapperH& startPos, const TrackUnWrapperH& endPos, std::vector<AnimNotifyEventReference>& outActiveNotifies) const
    {
        // Early out if we have no notifies
        if (!IsNotifyAvailable() || std::abs(startPos - endPos) < 0.001f)
            return;

        bool const bPlayingBwd = (endPos < startPos);

        // If playing backwards, flip Min and Max.
        if (bPlayingBwd)
        {
            for (SInt32 notifyIdx = 0; notifyIdx < mNotifies.size(); ++notifyIdx)
            {
                const AnimNotifyEvent& animNotifyEvent = *mNotifies[notifyIdx];
                const float notifyStartTime = animNotifyEvent.GetTriggerTime();

                if (endPos <= notifyStartTime && notifyStartTime < startPos)
                {
                    outActiveNotifies.emplace_back(&animNotifyEvent);

#if DEBUG_ANIMATION_SYSTEM
                    LOG_INFO("CurrentNotify: {}, Composite_StartPos: {}, Composite_EndPos: {}, TriggerTime: {}", animNotifyEvent.NotifyName.GetCString(), startPos.mVal, endPos.mVal, animNotifyEvent.GetTriggerTime());
#endif
                }
            }
        }
        else
        {
            for (SInt32 notifyIdx = 0; notifyIdx < mNotifies.size(); ++notifyIdx)
            {
                const AnimNotifyEvent& animNotifyEvent = *mNotifies[notifyIdx];
                const float notifyStartTime = animNotifyEvent.GetTriggerTime();

                if (startPos < notifyStartTime && notifyStartTime <= endPos)
                {
                    outActiveNotifies.emplace_back(&animNotifyEvent);

#if DEBUG_ANIMATION_SYSTEM
                    LOG_INFO("CurrentNotify: {}, Composite_StartPos: {}, Composite_EndPos: {}, TriggerTime: {}", animNotifyEvent.NotifyName.GetCString(), startPos.mVal, endPos.mVal, animNotifyEvent.GetTriggerTime());
#endif
                }
            }
        }
    }

    void AnimCompositeBase::ExtractNotifiesFromTrack(
        const CEName& slotName,
        TrackUnWrapperH startTrackPosition,
        TrackUnWrapperH endTrackPosition,
        std::vector<AnimNotifyEventReference>& outActiveNotifies) const
    {
        auto slotAnimTrackPtr = GetTrack(slotName);

        if (slotAnimTrackPtr == nullptr)
        {
            LOG_WARN("ExtractNotifiesFromTrack find track {} failed. ", slotName.GetCString());
            return;
        }

        slotAnimTrackPtr->GetAnimNotifiesFromTrackRange(startTrackPosition, endTrackPosition, outActiveNotifies);
    }

    void AnimCompositeBase::ExtractAnimCurvesFromSelf(const TrackUnWrapperH& curPos, AnimCurveData& outCurveData) const
    {
        outCurveData.ExtractAnimCurves(curPos, mAnimCurves);
    }

    void AnimCompositeBase::ExtractAnimCurvesFromTrack(const CEName& slotName, const TrackUnWrapperH& curPos, AnimCurveData& outCurveData) const
    {
        auto animTrackPtr = GetTrack(slotName);

        if (animTrackPtr == nullptr)
        {
            LOG_WARN("ExtractCurvesFromTrack: find track {} failed. ", slotName.GetCString());
            return;
        }

        animTrackPtr->ExtractAnimCurves(curPos, outCurveData);
    }

    /////////////////////////////////////////////
    // AnimCmpInstanceBase
    //
    /////////////////////////////////////////////
    AnimCmpInstanceBase::AnimCmpInstanceBase(bool isTrigger)
        : mIsTrigger(isTrigger)
    {
        static SInt32 InstanceID = 0;
        mInstanceID = InstanceID++;
    }

    void AnimCmpInstanceBase::SetPlayWithoutBlend(bool playing)
    {
        mPlaying = playing;
    }

    void AnimCmpInstanceBase::Play()
    {
        Assert(IsValid());

        mPlaying = true;
        const float cachedCursorValue = mBlend.GetBlendedValue();
        mBlend.Reset();
        mBlend.SetBlendTime(GetBlendInReferenceTime());
        mBlend.SetValueRange(cachedCursorValue, 1.0f);
    }

    void AnimCmpInstanceBase::Replay()
    {
        Assert(IsValid());

        mDeltaTime = {0.f};
        mBlend.Reset();
        Play();
    }

    void AnimCmpInstanceBase::Stop(float blendOutTime, bool bInterrupt)
    {
        // set interrupted flag
        if (!mInterrupted && bInterrupt)
        {
            mInterrupted = true;
        }

        if (!IsStopped())
        {
            mBlend.SetBlendTime(blendOutTime);
            // do not set begin value to 1, set begin value to blendedValue instead.
            mBlend.SetDesiredValue(0.f);
            mBlend.Update(0.f);
        }
        else
        {
            if (blendOutTime < mBlend.GetBlendTime())
            {
                mBlend.SetBlendTime(blendOutTime);
                mBlend.SetDesiredValue(0.f);
            }
        }

        if (mBlend.GetBlendTime() <= 0.f)
            mPlaying = false;
    }

    float AnimCmpInstanceBase::GetBlendInReferenceTime() const
    {
        Assert(IsValid());
        return GetInstanceShell()->mBlendInTime;
    }

    float AnimCmpInstanceBase::GetBlendOutReferenceTime() const
    {
        Assert(IsValid());
        return GetInstanceShell()->mBlendOutTime;
    }

    bool AnimCmpInstanceBase::IsSkeletonAttached() const
    {
        if (IsValid())
            return GetInstanceShell()->IsSkeletonAttached() && (mRunSkelt != nullptr);

        return false;
    }

    void AnimCmpInstanceBase::GetAllAnimSequence(FrameVector<const IAnimSequence*>* inContainer) const
    {
        inContainer->Clear();

        if (IsValid())
            GetInstanceShell()->GetAllAnimSequence(inContainer);
    }

    const CEName& AnimCmpInstanceBase::GetGroupName() const
    {
        Assert(IsValid());

        return GetInstanceShell()->GetGroupName();
    }

    bool AnimCmpInstanceBase::IsValidSlot(const CEName& slotName) const
    {
        if (IsValid())
        {
            return GetInstanceShell()->IsValidSlot(slotName);
        }

        return false;
    }

    void AnimCmpInstanceBase::ExtractDataArbitraryDeltaTime_DefaultTrack(
        AnimReferenceDefTrack const& inTrack, 
        bool bExtractRootMotion, 
        RootMotionParams* RootMotionParamsPtr, 
        bool bExtractNotifies, 
        AnimNotifyQueue* AnimNotifyQueuePtr) 
    {
        const AnimTrack& curTrack = inTrack.GetReference();

        float desiredDelta = mDeltaTime;

        bool const bPlayingBwd = (desiredDelta < 0.f);
        TrackUnWrapperH compLength = {curTrack.GetRunLength() / MathUtils::Sign<float>(mPlayRate)};

        TrackUnWrapperH prevPos = inTrack.GetPreCursor();
        TrackUnWrapperH curPos = prevPos;

        static UInt32 maxLoopCount = 3;
        UInt32 curLoopCount = 0;

        do
        {
            // disable looping here. Advance to desired position, or beginning / end of animation
            const AdvanceAnim::Type advanceType = AnimRuntime::AdvanceTime(false, desiredDelta, curPos, compLength);

            if (bExtractNotifies)
            {
                auto& outNotifyArray = AnimNotifyQueuePtr->AnimNotifies;
                size_t extractedBeforeSize = outNotifyArray.size();

                ExtractAnimNotifies(curTrack.SlotName, prevPos, curPos, outNotifyArray);

                size_t extractedAfterSize = outNotifyArray.size();
                for (size_t index = extractedBeforeSize; index < extractedAfterSize; ++index)
                {
                    auto& curNotify = outNotifyArray.at(index);
                    curNotify.SetExecInstance(this, curTrack.SlotName);
                }
            }

            if (bExtractRootMotion && (std::abs)(prevPos - curPos) >= 0.001f)
            {
                auto& outRootMotionParams = *RootMotionParamsPtr;
                ExtractRootMotion(curTrack.SlotName, prevPos, curPos, outRootMotionParams);
            }

            // If we've hit the end of the animation, and we're allowed to loop, keep going.
            if ((advanceType == AdvanceAnim::Type::AA_Finished) && this->IsLoopingAllowed())
            {
                const float actualDelta = (curPos - prevPos);
                desiredDelta -= actualDelta;

                prevPos = bPlayingBwd ? compLength : TrackUnWrapperH::From(0.f);
                curPos = prevPos;

                if (curLoopCount++ > maxLoopCount)
                    break;
            }
            else
            {
                break;
            }
        } while (true);
    }

    void AnimCmpInstanceBase::ExtractDataArbitraryDeltaTime_SectionedTrack(
        IAnimator const* inAnimator,
        AnimReferenceSecTrack const& inTrack,
        bool bExtractRootMotion,
        RootMotionParams* RootMotionParamsPtr,
        bool bExtractNotifies,
        AnimNotifyQueue* AnimNotifyQueuePtr)
    {
        auto const* activatedSection = &inTrack.GetActiveSection();
        float desiredDelta = mDeltaTime;
        TrackUnWrapperH curLength = { activatedSection->SectionPtr->RunLength(mPlayRate) };
    
        TrackUnWrapperH prevPos = inTrack.GetPreCursor();
        TrackUnWrapperH curPos = inTrack.GetCursor();

        static UInt32 maxLoopCount = 3;
        UInt32 curLoopCount = 0;

        do
        {
            if (curLoopCount++ > maxLoopCount)
                break;

            if (desiredDelta < 0.001f)
                break;

            TrackUnWrapperH trackStartPos = activatedSection->SectionPtr->TrackStartPos(mPlayRate);
            TrackUnWrapperH trackEndPos = activatedSection->SectionPtr->TrackEndPos(mPlayRate);

            // disable looping here. Advance to desired position, or beginning / end of animation 
            const AdvanceAnim::Type advanceType = AnimRuntime::AdvanceTime(
                false,
                desiredDelta,
                curPos,
                trackStartPos,
                trackEndPos);

            if (bExtractNotifies)
            {
                auto& outNotifyArray = AnimNotifyQueuePtr->AnimNotifies;
                size_t extractedBeforeSize = outNotifyArray.size();

                ExtractAnimNotifies(inTrack.GetReference().SlotName, prevPos, curPos, outNotifyArray);

                size_t extractedAfterSize = outNotifyArray.size();
                for (size_t index = extractedBeforeSize; index < extractedAfterSize; ++index)
                {
                    auto& curNotify = outNotifyArray.at(index);
                    curNotify.SetExecInstance(this, inTrack.GetReference().SlotName);

                    // Meet a notify which type is branching point, process it instantly
                    if (curNotify.GetNotify()->IsBranchingPointNotify())
                    {
                        curNotify.SetOwner(inAnimator);

                        if (curNotify.Broadcast() != NotifyGetResult::JumpSection)
                            continue;

                        // a valid jump operate processed, then
                        // 1. remove all notifies include current one
                        outNotifyArray.erase(outNotifyArray.begin() + index, outNotifyArray.end());

                        // 2. extract root motion for sub range from prePose to notify-branching point
                        auto* animSlotPtr = GetActivatedSlot(inTrack.GetReference().SlotName);
                        Assert(animSlotPtr != nullptr);

                        auto curNotifyTriggerTime = curNotify.GetNotify()->GetTriggerTime();

                        if (bExtractRootMotion && (std::abs)(prevPos - curNotifyTriggerTime) >= 0.001f)
                        {
                            auto& outRootMotionParams = *RootMotionParamsPtr;
                            ExtractRootMotion(inTrack.GetReference().SlotName, prevPos, {curNotifyTriggerTime}, outRootMotionParams);
                        }

                        // 3. correct delta time & cur pos & pre pos after jump (ignore the followed root motion extract by step2.)
                        desiredDelta += (std::abs)(curPos - curNotifyTriggerTime);
                        curPos = animSlotPtr->GetCursor();
                        prevPos = curPos;   // forbid repeat root motion extract back-to-back

                        break;
                    }
                }
            }

            if (bExtractRootMotion)
            {
                auto& outRootMotionParams = *RootMotionParamsPtr;
                ExtractRootMotion(inTrack.GetReference().SlotName, prevPos, curPos, outRootMotionParams);
            }

            // calculate the left delta time after pre advance
            const float actualDelta = (curPos - prevPos);
            desiredDelta -= actualDelta;

            // If we've hit the end of the animation, and we're allowed to loop, keep going.
            if ((advanceType == AdvanceAnim::Type::AA_Finished))
            {
                auto nextSection = activatedSection->Next();

                if (nextSection)
                    activatedSection = &(*nextSection);
                else
                    break;
            }
        } while (true);
    }

    void AnimCmpInstanceBase::ExtractRootMotion(
        const CEName& slotName,
        const TrackUnWrapperH& prevPos,
        const TrackUnWrapperH& curPos,
        RootMotionParams& outRootMotion)
    {
        const auto instanceShell = GetInstanceShell();
        Assert(instanceShell);

        if (mExtractedRootMotionSlots.find(slotName) != mExtractedRootMotionSlots.end())
            return;

        if (instanceShell->IsValidSlot(slotName))
        {
            // We extract info from track directly while treated Animatrix as Executable animation
            instanceShell->ExtractRootMotionFromTrack(slotName, prevPos, curPos, outRootMotion);

            // forbid repeat calculation
            mExtractedRootMotionSlots.insert(slotName);
        }
    }

    void AnimCmpInstanceBase::ExtractAnimNotifies(
        const CEName& slotName,
        const TrackUnWrapperH& prevPos,
        const TrackUnWrapperH& curPos,
        std::vector<AnimNotifyEventReference>& outActiveNotifies)
    {
        const auto instanceShell = GetInstanceShell();
        Assert(instanceShell);

        // Retrieves notify from its own notify track if available 
        if (mExtractedNotifySlots.find(CEName{ instanceShell->GetAssetPath().c_str() }) == mExtractedNotifySlots.end())
        {
            instanceShell->ExtractNotifiesFromSelfRange(prevPos, curPos, outActiveNotifies);

            // forbid repeat calculation
            mExtractedNotifySlots.insert(CEName{ instanceShell->GetAssetPath().c_str() });
        }

        // Retrieves particular slot track if available
        if (mExtractedNotifySlots.find(slotName) != mExtractedNotifySlots.end())
            return;

        if (instanceShell->IsValidSlot(slotName) && !instanceShell->IsDynamic())
        {
            // We extract info from track directly while treated Composite as Executable animation
            instanceShell->ExtractNotifiesFromTrack(slotName, prevPos, curPos, outActiveNotifies);

            // forbid repeat calculation
            mExtractedNotifySlots.insert(slotName);
        }
    }

    void AnimCmpInstanceBase::ExtractAnimCurves(const CEName& slotName, const TrackUnWrapperH& curPos, AnimCurveData& outAnimCurves)
    {
        const auto instanceShell = GetInstanceShell();
        Assert(instanceShell);

        // Extract curves from its own curve track
        instanceShell->ExtractAnimCurvesFromSelf(curPos, outAnimCurves);

        // Extract curves from specific track
        if (instanceShell->IsValidSlot(slotName) && !instanceShell->IsDynamic())
        {
            instanceShell->ExtractAnimCurvesFromTrack(slotName, curPos, outAnimCurves);
        }
    }

    void AnimCmpInstanceBase::UpdateByMarkerAsLeader(
        SyncMarkerUpdateRecord& markerUpdateRecord,
        SyncMarkerUpdateContext& markerUpdateContext,
        TrackUnWrapperH& inOutPrevPos,
        TrackUnWrapperH& inOutCurPos,
        const float moveDelta)
    {
        const AnimSyncMarkerData& syncMarkerData = *markerUpdateContext.GetSyncTrackMarkerData();
        const bool allowLoop = IsLoopingAllowed();

        if (!markerUpdateRecord.IsValid(allowLoop))
        {
            if (markerUpdateContext.IsMarkerSyncStartValid())
            {
                syncMarkerData.GetMarkerIndicesForSyncMarkerBasedPos(markerUpdateRecord, inOutCurPos.mVal, allowLoop, markerUpdateContext.GetMarkerSyncStartPosition());
            }
            else
            {
                syncMarkerData.GetMarkerIndicesForTime(markerUpdateRecord, inOutCurPos.mVal, allowLoop, markerUpdateContext.GetValidMarkers());
            }
        }

        // Set MarkerSyncStartPosition before update
        markerUpdateContext.SetMarkerSyncStartPosition(syncMarkerData.GetSyncMarkerBasedPosFromMarkerIndicies(markerUpdateRecord, inOutCurPos.mVal));

        inOutPrevPos = inOutCurPos;

        // Advance time as leader
        syncMarkerData.AdvanceMarkerPhaseAsLeader(inOutCurPos.mVal, markerUpdateRecord, markerUpdateContext, allowLoop, moveDelta);

        // Set MarkerSyncEndPosition after update
        markerUpdateContext.SetMarkerSyncEndPosition(syncMarkerData.GetSyncMarkerBasedPosFromMarkerIndicies(markerUpdateRecord, inOutCurPos.mVal));
    }

    void AnimCmpInstanceBase::UpdateByMarkerAsFollower(
        SyncMarkerUpdateRecord& markerUpdateRecord,
        SyncMarkerUpdateContext& markerUpdateContext,
        TrackUnWrapperH& inOutPrevPos,
        TrackUnWrapperH& inOutCurPos,
        const float moveDelta)
    {
        const AnimSyncMarkerData& syncMarkerData = *markerUpdateContext.GetSyncTrackMarkerData();
        const bool allowLoop = IsLoopingAllowed();

        if (!markerUpdateRecord.IsValid(allowLoop))
        {
            syncMarkerData.GetMarkerIndicesForSyncMarkerBasedPos(markerUpdateRecord, inOutCurPos.mVal, allowLoop, markerUpdateContext.GetMarkerSyncStartPosition());
        }

        inOutPrevPos = inOutCurPos;

        syncMarkerData.AdvanceMarkerPhaseAsFollower(inOutCurPos.mVal, markerUpdateRecord, markerUpdateContext, allowLoop, moveDelta);
    }
}
