#include "EnginePrefix.h"
#include "CEAnimation/AnimRuntime.h"
#include "CEAnimation/Composite/AnimSegment.h"
#include "CEAnimation/Composite/AnimCompositeBase.h"

namespace cross::anim {

 ////////////////////////////////////////////
// SlotTrackResSection
//
/////////////////////////////////////////////
bool SlotTrackResSection::Deserialize(const DeserializeNode& node)
{
    ASGK_STRING_TO_VARIABLE(node, Name);
    ASGK_STRING_TO_VARIABLE(node, SegmentStartIndex);
    ASGK_STRING_TO_VARIABLE(node, SegmentEndIndex);
    ASGK_STRING_TO_VARIABLE(node, Loop);
    return true;
}


void SlotTrackResSection::Serialize(SerializeNode& node) const
{
    ASGK_VARIABLE_TO_STRING(node, Name);
    ASGK_VARIABLE_TO_STRING(node, SegmentStartIndex);
    ASGK_VARIABLE_TO_STRING(node, SegmentEndIndex);
    ASGK_VARIABLE_TO_STRING(node, Loop);
}

    
/////////////////////////////////////////////
// SlotTrackData
//
/////////////////////////////////////////////
bool SlotTrackRes::Deserialize(const DeserializeNode& node)
{
    // Grab track name in the very beginning
    ASGK_STRING_TO_VARIABLE(node, SlotName);

    // Grab segments from current json track
    auto jsonSegments = node["Segments"];
    SegmentsDesc.resize(jsonSegments.Size());

    for (auto segmentIndex = 0; segmentIndex < SegmentsDesc.size(); ++segmentIndex)
    {
        auto segNode = jsonSegments[segmentIndex];
        auto& seg = SegmentsDesc[segmentIndex];
        seg.Deserialize(segNode);
    }

    // Grab sections from current json track
    auto jsonSections = node["Sections"];
    SectionsDesc.resize(jsonSections.Size());

    for (auto sectionIndex = 0; sectionIndex < SectionsDesc.size(); ++sectionIndex)
    {
        auto sectionNode = jsonSections[sectionIndex];
        auto& section = SectionsDesc[sectionIndex];
        section.Deserialize(sectionNode);
    }

    //// Grab next sections property Non-Serialized by raw json data
    //if (SectionsDesc.size() > 0)
    //{
    //    for (auto sectionIndex = 0; sectionIndex < SectionsDesc.size() - 1; ++sectionIndex)
    //    {
    //        auto& section = SectionsDesc[sectionIndex];
    //        auto& next_section = SectionsDesc[sectionIndex + 1];

    //        section.NextIndex = {(UInt32)sectionIndex + 1};
    //        section.NextName = next_section.Name;
    //    }
    //}

    return true;
}


void SlotTrackRes::Serialize(SerializeNode& node) const
{
    // Grab track name in the very beginning
    ASGK_VARIABLE_TO_STRING(node, SlotName);

    // Grab segments js node from runtime struct
    SerializeNode jsSegments = SerializeNode::EmptyArray();
    for (const auto& seg : SegmentsDesc)
    {
        SerializeNode jsSegNode{};
        seg.Serialize(jsSegNode);
        jsSegments.PushBack(std::move(jsSegNode));
    }
    node["Segments"] = std::move(jsSegments);

    // Grab sections js node from runtime struct
    SerializeNode jsSections = SerializeNode::EmptyArray();
    for (const auto& section : SectionsDesc)
    {
        SerializeNode jsSection{};
        section.Serialize(jsSection);
        jsSections.PushBack(std::move(jsSection));
    }
    node["Sections"] = std::move(jsSections);

    // Force assign type variable which got no reltv class member
    ASGK_TYPE_TO_STRING(node, SlotTrackRes);
}

SerializeNode SlotTrackRes::Serialize() const
{
    SerializeNode node;
    Serialize(node);
    return node;
}

/////////////////////////////////////////////
// SlotTrackResSegment
//
/////////////////////////////////////////////
bool SlotTrackResSegment::Deserialize(const DeserializeNode& node)
{
    ASGK_STRING_TO_VARIABLE(node, Name);
    ASGK_STRING_TO_VARIABLE(node, ReltvPath);
    ASGK_STRING_TO_VARIABLE(node, StartPos);
    ASGK_STRING_TO_VARIABLE(node, EndPos);
    ASGK_STRING_TO_VARIABLE(node, PlayRate);
    ASGK_STRING_TO_VARIABLE(node, LoopingCount);
    ASGK_STRING_TO_VARIABLE(node, PriorityDelta);
    return true;
}


void SlotTrackResSegment::Serialize(SerializeNode& node) const
{
    ASGK_VARIABLE_TO_STRING(node, Name);
    ASGK_VARIABLE_TO_STRING(node, ReltvPath);
    ASGK_VARIABLE_TO_STRING(node, StartPos);
    ASGK_VARIABLE_TO_STRING(node, EndPos);
    ASGK_VARIABLE_TO_STRING(node, PlayRate);
    ASGK_VARIABLE_TO_STRING(node, LoopingCount);
    ASGK_VARIABLE_TO_STRING(node, PriorityDelta);

    // Force assign type variable which got no reltv class member
    ASGK_TYPE_TO_STRING(node, SlotTrackResSegment);
}


/////////////////////////////////////////////
// AnimSegment
//
/////////////////////////////////////////////
auto AnimSegment::GetAnimationCursor(const TrackUnWrapperH& inTrackPos) const -> std::pair<IAnimSequence*, RawH>
{
    if (IsInRange(inTrackPos))
    {
        if (SequencePtr)
        {
            auto outSequence = SequencePtr;
            return std::make_pair<IAnimSequence*, RawH>(std::move(outSequence), ConvertTrackPosToAnimPos(inTrackPos));
        }
    }

    return std::make_pair<IAnimSequence*, RawH>(nullptr, {0.f});
}

RawH AnimSegment::ConvertTrackPosToAnimPos(const TrackUnWrapperH& inTrackPos) const
{
    Assert(IsInRange(inTrackPos) && "inTrackPos is not within this segment!");

    const float validPlayRate = GetValidPlayRate();

    // this result position should be pure position within animation
    float delta = inTrackPos - StartPos;

    // LoopingCount should not be zero, and it should not get here, but just in case
    if (Reference.LoopingCount > 1)
    {
        // we need to consider looping count
        float animPlayLength = GetRunSingleLength();
        delta = std::fmod(delta, animPlayLength);
    }

    const float animPoint = (validPlayRate >= 0.f) ? Reference.StartPos : Reference.EndPos;
    // pos within AnimSequence
    float posInAnim = animPoint + delta * validPlayRate;

    return {posInAnim};
}

template<typename TypeData>
void AnimSegment::GetTypeDataFromTrackRange(const TrackUnWrapperH& startTrackPos, const TrackUnWrapperH& endTrackPos, std::vector<TypeData>& outData) const
{
    if (startTrackPos == endTrackPos)
    {
        return;
    }

    const bool bTrackPlayingBwd = (startTrackPos > endTrackPos);
    const TrackUnWrapperH segEndPos = GetTrackUnWrapperEndPos();

    // if track range overlaps segment
    if (IsTrackRangeOverlap(startTrackPos, endTrackPos))
    {
        // Only allow AnimSequences for now. Other types will need additional support.
        if (SequencePtr)
        {
            const float validPlayRate = GetValidPlayRate();
            const float absValidPlayRate = std::abs(validPlayRate);

            // get actual range within segment when track range overlaps this segment
            const TrackUnWrapperH startTrackPosForSeg = bTrackPlayingBwd ? (std::min)(startTrackPos, segEndPos) : (std::max)(startTrackPos, StartPos);
            const TrackUnWrapperH endTrackPosForSeg = bTrackPlayingBwd ? (std::max)(endTrackPos, StartPos) : (std::min)(endTrackPos, segEndPos);

            // Get current starting position in anim-sequence, closest overlap.
            RawH animCheckingPos = ConvertTrackPosToAnimPos(startTrackPosForSeg);
            float trackTimeToGo = (std::abs)(endTrackPosForSeg - startTrackPosForSeg);

            // The track can be playing backwards and the animation can be playing backwards, so we
            // need to combine to work out what direction we are traveling through the animation
            bool bAnimPlayingBwd = bTrackPlayingBwd ^ (validPlayRate < 0.f);

            // The param below is the Seg-range's start pos in Current Anim Context
            const RawH animRunStartPos = bAnimPlayingBwd ? Reference.EndPos : Reference.StartPos;

            // The param below is the Seg-range's end pos in Current Anim Context
            const RawH animRunEndPos = bAnimPlayingBwd ? Reference.StartPos : Reference.EndPos;

            for (SInt32 iterationsLeft = (std::max)(Reference.LoopingCount, (UInt32)1); ((iterationsLeft > 0) && (trackTimeToGo > 0.f)); --iterationsLeft)
            {
                // Track time left to reach end point of animation.
                const float trackTimeToAnimEndLeft = (animRunEndPos - animCheckingPos) / absValidPlayRate;

                // If our time left is shorter than time to end point, no problem. End there.
                if (std::abs(trackTimeToGo) < std::abs(trackTimeToAnimEndLeft))
                {
                    const float playRate = validPlayRate * (bTrackPlayingBwd ? -1.f : 1.f);
                    const RawH animCurEndPos = animCheckingPos + (trackTimeToGo * playRate);

                    if constexpr (std::is_same_v<TypeData, AnimNotifyEventReference>)
                        SequencePtr->ExtractNotifiesFromRange(animCheckingPos, animCurEndPos, outData);

                    if constexpr (std::is_same_v<TypeData, RootMotionExtractionStep>)
                        outData.emplace_back(RootMotionExtractionStep(SequencePtr, animCheckingPos, animCurEndPos));

                    break;
                }
                // Otherwise we hit the end point of the animation first...
                else
                {
                    if constexpr (std::is_same_v<TypeData, AnimNotifyEventReference>)
                        SequencePtr->ExtractNotifiesFromRange(animCheckingPos, animRunEndPos, outData);

                    if constexpr (std::is_same_v<TypeData, RootMotionExtractionStep>)
                        outData.emplace_back(RootMotionExtractionStep(SequencePtr, animCheckingPos, animRunEndPos));

                    // decrease our TrackTimeToGo if we have to do another iteration.
                    // and put ourselves back at the beginning of the animation.
                    trackTimeToGo -= trackTimeToAnimEndLeft;
                    animCheckingPos = animRunStartPos;
                }
            }
        }
    }
}

void AnimSegment::GetPose(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& extractContext) const
{
    Assert(SequencePtr != nullptr);
    RawH curSeqPos = ConvertTrackPosToAnimPos(extractContext.CurrentTime());

    auto seqExtractContext = extractContext.Move<RawH>(curSeqPos, Reference.PlayRate);
    SequencePtr->GetPose(outPose, seqExtractContext);
}

void AnimSegment::ExtractAnimCurves(const TrackUnWrapperH& curPose, AnimCurveData& outAnimCurves) const
{
    Assert(SequencePtr != nullptr);
    RawH curSeqPos = ConvertTrackPosToAnimPos(curPose);

    SequencePtr->ExtractAnimCurves(curSeqPos, outAnimCurves);
}

/////////////////////////////////////////////
// AnimTrack
//
/////////////////////////////////////////////
void AnimTrack::GetPose(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& extractContext) const
{
    if (const AnimSegment* const animSegment = GetSegmentAtTime(extractContext.CurrentTime()))
    {
        if (animSegment->SequencePtr != nullptr)
        {
            animSegment->GetPose(outPose, extractContext);
            return;
        }
    }
}

void AnimTrack::ExtractAnimCurves(const TrackUnWrapperH& curPose, AnimCurveData& outAnimCurves) const
{
    if (const AnimSegment* const animSegment = GetSegmentAtTime(curPose))
    {
        if (animSegment->SequencePtr != nullptr)
        {
            animSegment->ExtractAnimCurves(curPose, outAnimCurves);
            return;
        }
    }
}

void AnimTrack::GetBoneTransform(NodeTransform& outBoneTrans, SkBoneHandle boneIndex, const float currentTime) const
{
    if (const AnimSegment* const animSegment = GetSegmentAtTime({currentTime}))
    {
        if (animSegment->SequencePtr != nullptr)
        {
            animSegment->SequencePtr->GetBoneTransform(outBoneTrans, boneIndex, currentTime);
            return;
        }
    }
}

AnimTrack& AnimTrack::operator=(const AnimTrack& other) noexcept
{
    // Grab slot name
    SlotName = other.SlotName;

    // Grab all anim segments here
    AnimSegments.clear();
    for (auto segIndex = 0; segIndex < other.AnimSegments.size(); ++segIndex)
    {
        auto& otherSeg = other.AnimSegments[segIndex];

        // Grab each anim segment here
        AnimSegments.emplace_back(otherSeg.SequencePtr, otherSeg.Reference, otherSeg.StartPos);
    }

    // Grab all anim i-sequences
    AnimSequences = other.AnimSequences;
    return *this;
}

bool AnimTrack::Deserialize(const SlotTrackRes& slotTrack, const std::vector<AnimSeqPtr>& inAnimSeqs, const std::vector<AnimNotifyEvent*>& Notifies)
{
    SlotName = slotTrack.SlotName;

    const auto& segDescs = slotTrack.SegmentsDesc;
    Assert(segDescs.size() == inAnimSeqs.size());

    TrackUnWrapperH curTrackCursor = {0.f};
    for (auto segIndex = 0; segIndex < segDescs.size(); ++segIndex)
    {
        auto const& segmentDesc = segDescs[segIndex];
        IAnimSequence* segmentSeqPtr = inAnimSeqs[segIndex].get();

        // Grab each anim segment here
        AnimSegments.emplace_back(segmentSeqPtr, segmentDesc, curTrackCursor);

        // Grab un_duplicated anim sequence here
        if (AnimSequences.find(segmentSeqPtr) == AnimSequences.end() && segmentSeqPtr != nullptr)
            AnimSequences.insert(segmentSeqPtr);

        curTrackCursor = {curTrackCursor + AnimSegments.back().GetRunLoopingLength()};
    }

    return AnimSegments.size() > 0;
}


void AnimTrack::Serialize(SerializeNode& node) const {}


void AnimTrack::ValidateSegmentTimes() {}

SInt32 AnimTrack::GetSegmentIndexAtTime(TrackUnWrapperH inTime) const
{
    // Segments overlap on a single frame occurs between segments.
    // So last frame of Segment1 overlaps first frame of Segment2.
    // But in that case we want Segment2 to win.
    // So we iterate through these segments in reverse
    // and return the first match with an inclusive range check.
    for (SInt32 idx = (SInt32)AnimSegments.size() - 1; idx >= 0; --idx)
    {
        const AnimSegment& segment = AnimSegments[idx];
        if (segment.IsInRange(inTime))
        {
            return static_cast<SInt32>(idx);
        }
    }

    return static_cast<SInt32>(INDEX_NONE);
}

AnimSegment* AnimTrack::GetSegmentAtTime(TrackUnWrapperH inTime)
{
    const SInt32 segmentIndex = GetSegmentIndexAtTime(inTime);
    return (segmentIndex != INDEX_NONE) ? &(AnimSegments[segmentIndex]) : nullptr;
}

const AnimSegment* AnimTrack::GetSegmentAtTime(TrackUnWrapperH inTime) const
{
    const SInt32 segmentIndex = GetSegmentIndexAtTime(inTime);
    return (segmentIndex != INDEX_NONE) ? &(AnimSegments[segmentIndex]) : nullptr;
}

void AnimTrack::InvalidateRecursiveAsset(class AnimCompositeBase* inCheckAnim)
{
    for (AnimSegment& animSeg : AnimSegments)
    {
        IAnimSequence* animSeq = animSeg.SequencePtr;
        if (animSeq)
        {
            // add owner
            std::vector<const IAnimSequence*> seqsRecurisve;
            seqsRecurisve.push_back(animSeq);

            if (inCheckAnim->ContainRecursive(seqsRecurisve))
                animSeg.SequencePtr = nullptr;
        }
        else
        {
            animSeg.SequencePtr = nullptr;
        }
    }
}

// this is recursive function that look thorough internal assets
// and return true if it finds nested same assets
bool AnimTrack::ContainRecursive(std::vector<const IAnimSequence*>& inAccumulatedList) const
{
    for (const AnimSegment& segment : AnimSegments)
    {
        // we don't want to send this list broad widely (but in depth search)
        // to do that, we copy the current accumulated list, and send that only, not the siblings
        std::vector<const IAnimSequence*> localCurrentAccumulatedList = inAccumulatedList;

        if (segment.SequencePtr && segment.SequencePtr->ContainRecursive(localCurrentAccumulatedList))
            return true;
    }

    return false;
}

bool AnimTrack::IsNotifyAvailable() const
{
    for (const auto& animSegment : AnimSegments)
    {
        if (animSegment.IsNotifyAvailable())
            return true;
    }

    return false;
}

void AnimTrack::GetAnimNotifiesFromTrackRange(const TrackUnWrapperH& startTrackPos, const TrackUnWrapperH& endTrackPos, std::vector<AnimNotifyEventReference>& outActiveNotifies) const
{
    for (const auto& animSegment : AnimSegments)
    {
        if (animSegment.IsValid() && animSegment.IsTrackRangeOverlap(startTrackPos, endTrackPos))
            animSegment.GetTypeDataFromTrackRange<AnimNotifyEventReference>(startTrackPos, endTrackPos, outActiveNotifies);
    }
}

void AnimTrack::GetRootMotionExtractionStepsFromTrackRange(const TrackUnWrapperH& startTrackPos, const TrackUnWrapperH& endTrackPos, std::vector<RootMotionExtractionStep>& outRootMotionExtractionSteps) const
{
    // must extract root motion in right order sequentially
    const bool bTrackPlayingBwd = (startTrackPos > endTrackPos);
    if (bTrackPlayingBwd)
    {
        for (SInt32 animSegIdx = static_cast<SInt32>(AnimSegments.size() - 1); animSegIdx >= 0; --animSegIdx)
        {
            const AnimSegment& animSegment = AnimSegments[animSegIdx];

            if (animSegment.IsValid() && animSegment.IsTrackRangeOverlap(startTrackPos, endTrackPos))
                animSegment.GetTypeDataFromTrackRange<RootMotionExtractionStep>(startTrackPos, endTrackPos, outRootMotionExtractionSteps);
        }
    }
    else
    {
        for (auto animSegIdx = 0; animSegIdx < AnimSegments.size(); ++animSegIdx)
        {
            const AnimSegment& animSegment = AnimSegments[animSegIdx];

            if (animSegment.IsValid() && animSegment.IsTrackRangeOverlap(startTrackPos, endTrackPos))
                animSegment.GetTypeDataFromTrackRange<RootMotionExtractionStep>(startTrackPos, endTrackPos, outRootMotionExtractionSteps);
        }
    }
}

/////////////////////////////////////////////
// AnimReferenceDefTrack
//
/////////////////////////////////////////////
cross::anim::AdvanceAnim::Type AnimReferenceDefTrack::Advance(float deltaTime, bool isLoopAllowed)
{
    // advance prepos here
    mPrePos = mCurPos;

    // advance curpos here
    TrackUnWrapperH compositeLength = {mReference.GetRunLength()};
    auto re = AnimRuntime::AdvanceTime(isLoopAllowed, deltaTime, mCurPos, compositeLength);

    // calculate delta
    if (mCurPos != mPrePos)
    {
        // figure out delta time
        mDeltaTime = mCurPos - mPrePos;

        // if we went against play rate, this means looping happened.
        if ((deltaTime * mDeltaTime) < 0.f)
            mDeltaTime += GetRunLength();
    }

    return re;
}

bool AnimReferenceDefTrack::IsCompleted(float inBlendTime /* = 0.001f*/, bool isLoopAllowed /*= false*/) const
{
    if (mIsComplete == true)
        return true;

    if (isLoopAllowed)
        return false;

    mIsComplete = std::abs(mCurPos - mReference.GetRunLength()) < inBlendTime;
    return mIsComplete;
}

void AnimReferenceTrackBase::RatioScaleCursor(float preRatio, float curRatio)
{
    mPrePos = {preRatio * GetRunLength()};
    mCurPos = {curRatio * GetRunLength()};
}

void AnimReferenceTrackBase::SetCursor(TrackUnWrapperH prePosition, TrackUnWrapperH curPosition)
{
    mPrePos = prePosition;
    mCurPos = curPosition;
}

void AnimReferenceDefTrack::GetPose(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& extractContext) const
{
    mReference.GetPose(outPose, extractContext);
}

void AnimReferenceDefTrack::ExtractAnimCurves(AnimCurveData& outAnimCurves) const
{
    mReference.ExtractAnimCurves(mCurPos, outAnimCurves);
}

}   // namespace cross::anim
