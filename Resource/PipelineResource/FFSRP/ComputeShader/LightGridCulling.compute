#pragma compute Clear<PERSON>ufferCS
#pragma compute GridCull<PERSON>
#pragma compute GridCompactCS
#pragma compute GridCullWithLightBoundCS

#define GROUP_NUM_PER_BLOCK 32
#define TASK_NUM_PER_GROUP 32

#include"../Shader/ShaderLibrary/Common.hlsl"
SHADER_CONST(bool, CE_USE_DOUBLE_TRANSFORM, false);

const static uint GC_MAX_RP_NUM = 100;
cbuffer _cbCommon
{
    uint4 _CulledGridSize;
	float _LightGridZParamsB;//B,O,S
    float _LightGridZParamsO;
    float _LightGridZParamsS;
	float4 _ViewSizeAndInvSize;//width  height  1/width   1/height
	uint _LightGridPixelSizeShift;//log2(LightGridPixelSize)
	matrix _InvProjMat;
	matrix _InvViewMat;
	matrix _ProjMat;
	matrix _ViewMat;
	uint _NumReflectionCaptures;
    uint _NumLights;
    uint _NumLocalLights;
	float4 _ReflectionCapture_PositionAndRadius[GC_MAX_RP_NUM];
    float4 ce_ReflectionProbeTilePos[GC_MAX_RP_NUM];
	uint _MaxCulledLightsPerCell;

    float3 ce_CameraTilePosition;
}
StructuredBuffer<float4> _LightPositionAndRadius;
StructuredBuffer<float4> _LightDirAndTanOuterAngle;

RWBuffer<uint> RWNextCulledLightLink;//1 ,For Cull
RWBuffer<uint> RWNextCulledLightData;//1 ,For Reverse
RWBuffer<uint> RWStartOffsetGrid;//MaxGridNum * 2   ,First rp then light
RWBuffer<uint> RWCulledLightLinks;//MaxGridNum * MaxCullLightsPerTile * 2
RWBuffer<uint> RWNumCulledLightsGrid;//MaxGridNum * 2 * 2
RWBuffer<uint> RWCulledLightDataGrid;//MaxGridNum * MaxCullLightsPerTile * 2

//RWNumCulledLightsGrid  : 2*_MaxCulledLightsPerCell lightCulling     2*_MaxCulledLightsPerCell rpCulling
//NumGridCells:_CulledGridSize.x*_CulledGridSize.y*_CulledGridSize.z
float ComputeCellNearViewDepthFromZSlice(uint ZSlice)
{
    //slice = log2(z*B + O) * S    z:view space  
	float SliceDepth = (exp2(ZSlice / _LightGridZParamsS) - _LightGridZParamsO) / _LightGridZParamsB;

	if (ZSlice == (uint)_CulledGridSize.z)
	{
		// Extend the last slice depth max out to world max
		// This allows clamping the depth range to reasonable values, 
		// But has the downside that any lights falling into the last depth slice will have very poor culling,
		// Since the view space AABB will be bloated in x and y
		SliceDepth = 20000000.0f;
	}

	if (ZSlice == 0)
	{
		// The exponential distribution of z slices contains an offset, but some screen pixels
		// may be nearer to the camera than this offset. To avoid false light rejection, we set the
		// first depth slice to zero to ensure that the AABB includes the [0, offset] depth range.
		SliceDepth = 0.0f;
	}

	return SliceDepth;
}

//float ConvertFromDeviceZ(float DeviceZ, matrix projMat)
//{
//	return (projMat[2][3] / (DeviceZ - projMat[2][2]));
//}
//
//float ConvertToDeviceZ(float SceneDepth, matrix projMat)
//{
//	return projMat[2][3] / SceneDepth + projMat[2][2];
//}

void ComputeCellViewAABB(uint3 GridCoordinate, matrix projMat, out float3 ViewTileMin, out float3 ViewTileMax)
{
	const float2 InvCulledGridSizeF = (1u << _LightGridPixelSizeShift) * _ViewSizeAndInvSize.zw;
	const float2 TileSize = float2(2.0f, -2.0f) * InvCulledGridSizeF.xy;
	const float2 UnitPlaneMin = float2(-1.0f, 1.0f);

	float2 UnitPlaneTileMin = GridCoordinate.xy * TileSize + UnitPlaneMin;
	float2 UnitPlaneTileMax = (GridCoordinate.xy + 1) * TileSize + UnitPlaneMin;

	float MinTileZ = ComputeCellNearViewDepthFromZSlice(GridCoordinate.z);
	float MaxTileZ = ComputeCellNearViewDepthFromZSlice(GridCoordinate.z + 1);

	float MinTileDeviceZ = ConvertToDeviceZ(MinTileZ, projMat);
	float4 MinDepthCorner0 = mul(_InvProjMat, float4(UnitPlaneTileMin.x, UnitPlaneTileMin.y, MinTileDeviceZ, 1));
	float4 MinDepthCorner1 = mul(_InvProjMat, float4(UnitPlaneTileMax.x, UnitPlaneTileMax.y, MinTileDeviceZ, 1));
	float4 MinDepthCorner2 = mul(_InvProjMat, float4(UnitPlaneTileMin.x, UnitPlaneTileMax.y, MinTileDeviceZ, 1));
	float4 MinDepthCorner3 = mul(_InvProjMat, float4(UnitPlaneTileMax.x, UnitPlaneTileMin.y, MinTileDeviceZ, 1));

	float MaxTileDeviceZ = ConvertToDeviceZ(MaxTileZ, projMat);
	float4 MaxDepthCorner0 = mul(_InvProjMat, float4(UnitPlaneTileMin.x, UnitPlaneTileMin.y, MaxTileDeviceZ, 1));
	float4 MaxDepthCorner1 = mul(_InvProjMat, float4(UnitPlaneTileMax.x, UnitPlaneTileMax.y, MaxTileDeviceZ, 1));
	float4 MaxDepthCorner2 = mul(_InvProjMat, float4(UnitPlaneTileMin.x, UnitPlaneTileMax.y, MaxTileDeviceZ, 1));
	float4 MaxDepthCorner3 = mul(_InvProjMat, float4(UnitPlaneTileMax.x, UnitPlaneTileMin.y, MaxTileDeviceZ, 1));

	float2 ViewMinDepthCorner0 = MinDepthCorner0.xy / MinDepthCorner0.w;
	float2 ViewMinDepthCorner1 = MinDepthCorner1.xy / MinDepthCorner1.w;
	float2 ViewMinDepthCorner2 = MinDepthCorner2.xy / MinDepthCorner2.w;
	float2 ViewMinDepthCorner3 = MinDepthCorner3.xy / MinDepthCorner3.w;
	float2 ViewMaxDepthCorner0 = MaxDepthCorner0.xy / MaxDepthCorner0.w;
	float2 ViewMaxDepthCorner1 = MaxDepthCorner1.xy / MaxDepthCorner1.w;
	float2 ViewMaxDepthCorner2 = MaxDepthCorner2.xy / MaxDepthCorner2.w;
	float2 ViewMaxDepthCorner3 = MaxDepthCorner3.xy / MaxDepthCorner3.w;

    // not necessary to use mins and maxs since dictionary order will return the same result in view space
	ViewTileMin.xy = min(ViewMinDepthCorner0, ViewMinDepthCorner1);
	ViewTileMin.xy = min(ViewTileMin.xy, ViewMinDepthCorner2);
	ViewTileMin.xy = min(ViewTileMin.xy, ViewMinDepthCorner3);
	ViewTileMin.xy = min(ViewTileMin.xy, ViewMaxDepthCorner0);
	ViewTileMin.xy = min(ViewTileMin.xy, ViewMaxDepthCorner1);
	ViewTileMin.xy = min(ViewTileMin.xy, ViewMaxDepthCorner2);
	ViewTileMin.xy = min(ViewTileMin.xy, ViewMaxDepthCorner3);

	ViewTileMax.xy = max(ViewMinDepthCorner0, ViewMinDepthCorner1);
	ViewTileMax.xy = max(ViewTileMax.xy, ViewMinDepthCorner2);
	ViewTileMax.xy = max(ViewTileMax.xy, ViewMinDepthCorner3);
	ViewTileMax.xy = max(ViewTileMax.xy, ViewMaxDepthCorner0);
	ViewTileMax.xy = max(ViewTileMax.xy, ViewMaxDepthCorner1);
	ViewTileMax.xy = max(ViewTileMax.xy, ViewMaxDepthCorner2);
	ViewTileMax.xy = max(ViewTileMax.xy, ViewMaxDepthCorner3);

	ViewTileMin.z = MinTileZ;
	ViewTileMax.z = MaxTileZ;
}

float  ComputeSquaredDistanceFromBoxToPoint(float3  BoxCenter, float3  BoxExtent, float3  InPoint)
{
	float3  AxisDistances = max(abs(InPoint - BoxCenter) - BoxExtent, 0);
	return dot(AxisDistances, AxisDistances);
}

bool AabbOutsidePlane(float3 center, float3 extents, float4 plane)
{
    float dist = dot(float4(center, 1.0), plane);
    float radius = dot(extents, abs(plane.xyz));

    return dist > radius;
}

bool IsAabbOutsideInfiniteAcuteConeApprox(float3 ConeVertex, float3 ConeAxis, float TanConeAngle, float3 AabbCentre, float3 AabbExt)
{
    float3 D = AabbCentre - ConeVertex;

    float3 M = -normalize(cross(cross(D, ConeAxis), ConeAxis));
    float3 N = -TanConeAngle * ConeAxis + M;
    float4 Plane = float4(N, 0.0);

    return AabbOutsidePlane(D, AabbExt, Plane);
}

[numthreads(4, 4, 4)]
void ClearBufferCS(
    uint GI:SV_GroupIndex,
    uint3 Gid : SV_GroupID,
    uint3 GTid : SV_GroupThreadID,
    uint3 DTid : SV_DispatchThreadID
)
{
    uint3 GridCoordinate = DTid;
    if (all(GridCoordinate == uint3(0, 0, 0)))
    {
        RWNextCulledLightLink[0] = 0;
        RWNextCulledLightData[0] = 0;
    }
    if (all(GridCoordinate < uint3(_CulledGridSize.x, _CulledGridSize.y, _CulledGridSize.z)))
    {
        uint GridIndex = (GridCoordinate.z * _CulledGridSize.y + GridCoordinate.y) * _CulledGridSize.x + GridCoordinate.x;
        RWNumCulledLightsGrid[(GridIndex) * 2 + 0] = 0;
        RWNumCulledLightsGrid[(GridIndex) * 2 + 1] = 0;
        RWStartOffsetGrid[(GridIndex) * 2 + 0] = 0xFFFFFFFF;
        RWStartOffsetGrid[(GridIndex) * 2 + 1] = 0xFFFFFFFF;
        // for (uint id = 0; id < _MaxCulledLightsPerCell; id++)
        // {
        //     uint LightOffset = _CulledGridSize.w * _MaxCulledLightsPerCell;
        //     RWCulledLightDataGrid[(GridIndex)*_MaxCulledLightsPerCell + id] = 0xFFFFFFFF;
        //     RWCulledLightDataGrid[LightOffset + (GridIndex)*_MaxCulledLightsPerCell + id] = 0xFFFFFFFF;
        //     RWCulledLightLinks[(GridIndex)*_MaxCulledLightsPerCell + id] = 0xFFFFFFFF;
        //     RWCulledLightLinks[LightOffset + (GridIndex)*_MaxCulledLightsPerCell + id] = 0xFFFFFFFF;
        // }
    }
}


[numthreads(4, 4, 4)]
void GridCullCS(
	uint GI:SV_GroupIndex,
	uint3 Gid : SV_GroupID,
	uint3 GTid : SV_GroupThreadID,
	uint3 DTid : SV_DispatchThreadID
)
{
	uint3 GridCoordinate = DTid;
	if (all(GridCoordinate < uint3(_CulledGridSize.x, _CulledGridSize.y, _CulledGridSize.z)))
	{
		uint GridIndex = (GridCoordinate.z * _CulledGridSize.y + GridCoordinate.y) * _CulledGridSize.x + GridCoordinate.x;

		//View Space 
		float3 ViewTileMin;
		float3 ViewTileMax;
		ComputeCellViewAABB(GridCoordinate, _ProjMat, ViewTileMin, ViewTileMax);

		float3 ViewTileCenter = 0.5f * (ViewTileMin + ViewTileMax);
		float3 ViewTileExtent = ViewTileMax - ViewTileCenter;
        ////Camera Tile
		//float3 WorldTileCenter = mul(_InvViewMat, float4(ViewTileCenter, 1)).xyz;
		//float4 WorldTileBoundingSphere = float4(WorldTileCenter, length(ViewTileExtent));//CenterPos,Radius

        uint NumAvailableLinks = _CulledGridSize.w * _MaxCulledLightsPerCell * 2;

        /** Reflection Probe **/
		for (uint ReflectionCaptureIndex = 0; ReflectionCaptureIndex < _NumReflectionCaptures; ReflectionCaptureIndex++)
		{
			//FLWCVector3 CaptureWorldPosition = MakeLWCVector3(ReflectionCapture_TilePosition[ReflectionCaptureIndex].xyz, ReflectionCapture_PositionAndRadius[ReflectionCaptureIndex].xyz);
			float3 CaptureTranslatedWorldPosition = _ReflectionCapture_PositionAndRadius[ReflectionCaptureIndex].xyz;
            if (CE_USE_DOUBLE_TRANSFORM)
            {
                //Camera Tile
                CaptureTranslatedWorldPosition = GetLargeCoordinateReltvPosition(CaptureTranslatedWorldPosition, ce_ReflectionProbeTilePos[ReflectionCaptureIndex].xyz, ce_CameraTilePosition);
            }
			float3 ViewSpaceCapturePosition = mul(_ViewMat, float4(CaptureTranslatedWorldPosition, 1)).xyz;
			float CaptureRadius = _ReflectionCapture_PositionAndRadius[ReflectionCaptureIndex].w;

			float BoxDistanceSq = ComputeSquaredDistanceFromBoxToPoint(ViewTileCenter, ViewTileExtent, ViewSpaceCapturePosition);

			if (BoxDistanceSq < CaptureRadius * CaptureRadius)
			{
                uint NextLink;
                InterlockedAdd(RWNextCulledLightLink[0], 1U, NextLink);\
                if (NextLink < NumAvailableLinks)
                {
                    uint PreviousLink;
                    InterlockedExchange(RWStartOffsetGrid[GridIndex], NextLink, PreviousLink);
                    RWCulledLightLinks[NextLink * 2 + 0] = ReflectionCaptureIndex;
                    RWCulledLightLinks[NextLink * 2 + 1] = PreviousLink;
                }
			}
		}

        /** Light **/
        for (uint LocalLightIndex = 0; LocalLightIndex < _NumLights; LocalLightIndex++)
        {
            float4 LightPositionAndRadius = _LightPositionAndRadius[LocalLightIndex];
            if (LightPositionAndRadius.w < 0)
                continue;

            float3 lightTranslatedWorldPos = LightPositionAndRadius.xyz;
            float3 ViewSpaceLightPosition = mul(_ViewMat, float4(lightTranslatedWorldPos, 1)).xyz;
            float LightRadius = LightPositionAndRadius.w;

            float BoxDistanceSq = ComputeSquaredDistanceFromBoxToPoint(ViewTileCenter, ViewTileExtent, ViewSpaceLightPosition);

            if (BoxDistanceSq < LightRadius * LightRadius)
            {

                bool bPassSpotlightTest = true;
                {
                    float4 DirAndPreprocAngle = _LightDirAndTanOuterAngle[LocalLightIndex];
                    float TanConeAngle = DirAndPreprocAngle.w;

                    if (TanConeAngle > 0.0f)
                    {
                        float3 ViewSpaceLightDirection = mul(_ViewMat, float4(DirAndPreprocAngle.xyz, 0)).xyz;
                        bPassSpotlightTest = !IsAabbOutsideInfiniteAcuteConeApprox(ViewSpaceLightPosition, ViewSpaceLightDirection, TanConeAngle, ViewTileCenter, ViewTileExtent);
                    }
                }
                if (bPassSpotlightTest)
                {
                    uint NextLink;
                    InterlockedAdd(RWNextCulledLightLink[0], 1U, NextLink);

                    if (NextLink < NumAvailableLinks)
                    {
                        uint PreviousLink;
                        InterlockedExchange(RWStartOffsetGrid[_CulledGridSize.w + GridIndex], NextLink, PreviousLink);
                        RWCulledLightLinks[NextLink * 2 + 0] = LocalLightIndex;
                        RWCulledLightLinks[NextLink * 2 + 1] = PreviousLink;
                    }
                }
            }
        }
	}
}

StructuredBuffer<uint> _GroupNumPerLightList;
StructuredBuffer<uint> _SumUpBlock;
StructuredBuffer<uint4> _LightBoundingGrid;

cbuffer _cbPerLightCluster{
    uint _BlockCnt;
    uint _LightCnt;
}

[numthreads(TASK_NUM_PER_GROUP, 1, 1)]
void GridCullWithLightBoundCS(
	uint GI:SV_GroupIndex,
	uint3 Gid : SV_GroupID,
	uint3 GTid : SV_GroupThreadID,
	uint3 DTid : SV_DispatchThreadID
)
{
    uint gid = Gid.x;
    uint lightIdx = 0;
    uint groupSum = 0;
    uint lightBlockIdx;
    for (lightBlockIdx = 0; lightBlockIdx < _BlockCnt; ++lightBlockIdx){
        uint nowBlock = groupSum + _SumUpBlock[lightBlockIdx];
        if (nowBlock >= gid){
            break;
        }
        else{
            groupSum = nowBlock;
        }
    }

    uint lightOffset = lightBlockIdx * GROUP_NUM_PER_BLOCK;
    for (lightIdx = lightOffset; lightIdx < lightOffset + GROUP_NUM_PER_BLOCK; ++lightIdx){
        if (lightIdx >= _LightCnt) return;
        uint nowBlock = groupSum + _GroupNumPerLightList[lightIdx];
        if (nowBlock > gid){
            break;
        }
        else{
            groupSum = nowBlock;
        }
    }
    
    uint id = DTid.x;
    id -= groupSum * TASK_NUM_PER_GROUP;

    uint3 boundingMin = _LightBoundingGrid[lightIdx * 2].xyz;
    uint3 boundingMax = _LightBoundingGrid[lightIdx * 2 + 1].xyz;
    uint3 boundingSize = boundingMax - boundingMin;
    uint totalCount = boundingSize.x * boundingSize.y * boundingSize.z;
    if (id >= totalCount) return;

	uint3 GridCoordinate;
    GridCoordinate.z = id / (boundingSize.x * boundingSize.y);
    GridCoordinate.y = id % (boundingSize.x * boundingSize.y) / boundingSize.x;
    GridCoordinate.x = id % boundingSize.x;
    GridCoordinate += boundingMin;

	if (all(GridCoordinate < uint3(_CulledGridSize.x, _CulledGridSize.y, _CulledGridSize.z)))
	{
		uint GridIndex = (GridCoordinate.z * _CulledGridSize.y + GridCoordinate.y) * _CulledGridSize.x + GridCoordinate.x;

		//View Space 
		float3 ViewTileMin;
		float3 ViewTileMax;
		ComputeCellViewAABB(GridCoordinate, _ProjMat, ViewTileMin, ViewTileMax);

		float3 ViewTileCenter = 0.5f * (ViewTileMin + ViewTileMax);
		float3 ViewTileExtent = ViewTileMax - ViewTileCenter;
        uint NumAvailableLinks = _CulledGridSize.w * _MaxCulledLightsPerCell * 2;

        /** Light **/
        //for (uint LocalLightIndex = 0; LocalLightIndex < _NumLights; LocalLightIndex++)
        {
            float4 LightPositionAndRadius = _LightPositionAndRadius[lightIdx];
            if (LightPositionAndRadius.w < 0)
                return;

            float3 lightTranslatedWorldPos = LightPositionAndRadius.xyz;
            float3 ViewSpaceLightPosition = mul(_ViewMat, float4(lightTranslatedWorldPos, 1)).xyz;
            float LightRadius = LightPositionAndRadius.w;

            float BoxDistanceSq = ComputeSquaredDistanceFromBoxToPoint(ViewTileCenter, ViewTileExtent, ViewSpaceLightPosition);

            if (BoxDistanceSq < LightRadius * LightRadius)
            {

                bool bPassSpotlightTest = true;
                {
                    float4 DirAndPreprocAngle = _LightDirAndTanOuterAngle[lightIdx];
                    float TanConeAngle = DirAndPreprocAngle.w;

                    if (TanConeAngle > 0.0f)
                    {
                        float3 ViewSpaceLightDirection = mul(_ViewMat, float4(DirAndPreprocAngle.xyz, 0)).xyz;
                        bPassSpotlightTest = !IsAabbOutsideInfiniteAcuteConeApprox(ViewSpaceLightPosition, ViewSpaceLightDirection, TanConeAngle, ViewTileCenter, ViewTileExtent);
                    }
                }
                if (bPassSpotlightTest)
                {
                    uint NextLink;
                    InterlockedAdd(RWNextCulledLightLink[0], 1U, NextLink);

                    if (NextLink < NumAvailableLinks)
                    {
                        uint PreviousLink;
                        InterlockedExchange(RWStartOffsetGrid[_CulledGridSize.w + GridIndex], NextLink, PreviousLink);
                        RWCulledLightLinks[NextLink * 2 + 0] = lightIdx;
                        RWCulledLightLinks[NextLink * 2 + 1] = PreviousLink;
                    }
                }
            }
        }
	}
}

void CompactReverseLinkedList(uint GridIndex, uint NumMax)
{
    uint NumCulledLights = 0;
    uint StartLinkOffset = 0;
    uint LinkOffset = 0;
    uint CulledLightDataStart = 0;

    StartLinkOffset = RWStartOffsetGrid[GridIndex];
    LinkOffset = StartLinkOffset;

    while (LinkOffset != 0xFFFFFFFF && NumCulledLights < NumMax)
    {
        NumCulledLights++;
        LinkOffset = RWCulledLightLinks[LinkOffset * 2 + 1];
    }

    InterlockedAdd(RWNextCulledLightData[0], NumCulledLights, CulledLightDataStart);

    RWNumCulledLightsGrid[GridIndex * 2 + 0] = NumCulledLights;
    RWNumCulledLightsGrid[GridIndex * 2 + 1] = CulledLightDataStart;

    LinkOffset = StartLinkOffset;
    uint CulledLightIndex = 0;

    while (LinkOffset != 0xFFFFFFFF && CulledLightIndex < NumCulledLights)
    {
        RWCulledLightDataGrid[CulledLightDataStart + NumCulledLights - CulledLightIndex - 1] = RWCulledLightLinks[LinkOffset * 2 + 0];
        CulledLightIndex++;
        LinkOffset = RWCulledLightLinks[LinkOffset * 2 + 1];
    }

}

[numthreads(4, 4, 4)]
void GridCompactCS(
    uint GI:SV_GroupIndex,
    uint3 Gid : SV_GroupID,
    uint3 GTid : SV_GroupThreadID,
    uint3 DTid : SV_DispatchThreadID
)
{
    uint3 GridCoordinate = DTid;
    if (all(GridCoordinate < uint3(_CulledGridSize.x, _CulledGridSize.y, _CulledGridSize.z)))
    {
        uint GridIndex = (GridCoordinate.z * _CulledGridSize.y + GridCoordinate.y) * _CulledGridSize.x + GridCoordinate.x;

        //ReflectionProbe
        CompactReverseLinkedList(GridIndex, _NumReflectionCaptures);

        //Light
        CompactReverseLinkedList(_CulledGridSize.w + GridIndex, _NumLocalLights);
    }
}