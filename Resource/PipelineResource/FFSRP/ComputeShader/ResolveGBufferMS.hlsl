#ifndef RESOLVE_GBUFFERMS_HLSL
#define RESOLVE_GBUFFERMS_HLSL
#ifndef SAMPLE_COUNT 
#define SAMPLE_COUNT 4
#endif

#define FILTER_METHOD_CUBIC 0
#define FILTER_METHOD_CATMULLROM 1
#define FILTER_METHOD_MITCHEL 2
#define MAX_SAMPLE_COUNT 8
#define NUM_THREAD 8
#if SAMPLE_COUNT > MAX_SAMPLE_COUNT
#error SAMPLE_COUNT can not bigger than MAX_SAMPLE_COUNT
#endif

// #include "../Shader/ShaderLibrary/Common.hlsl"
// #include "../Sahder/ShaderLibrary/Packing.hlsl"
#include "../Shader/Material/NormalBuffer.hlsl"

Texture2DMS<float, SAMPLE_COUNT> in_depthBufferMS;
Texture2DMS<float4, SAMPLE_COUNT> in_baseColorBufferMS;
Texture2DMS<float4, SAMPLE_COUNT> in_NormalBufferMS;
Texture2DMS<float4, SAMPLE_COUNT> in_fresnelShadingModeMS;
Texture2DMS<float4, SAMPLE_COUNT> in_SceneColorMS; // Coverage is stored in the w component
Texture2DMS<float4, SAMPLE_COUNT> in_MotionVectorOpacityAOMS;

RWTexture2D<float4> out_baseColorBuffer;
RWTexture2D<float4> out_BaseColorBuffer2;
RWTexture2D<float4> out_NormalBuffer;
RWTexture2D<float4> out_NormalBuffer2;
RWTexture2D<float4> out_fresnelShadingModeBuffer;
RWTexture2D<float4> out_fresnelShadingModeBuffer2;
RWTexture2D<float4> out_SceneColor;
RWTexture2D<float4> out_SceneColor2;
RWTexture2D<float> out_depth;
RWTexture2D<float> out_depth2;
RWTexture2D<float4> out_MotionVectorOpacityAO;
RWTexture2D<float> out_AgaaCoverage;

cbuffer RGBMSConstantbuffer
{
    uint Width;
    uint Height;
    float ComplexPixelThreshold;
    float DepthWeight;
    float NormalWeight;
    float RoughnessWeight;
    int ResolveFilterDiameter;
    int EmssiveResolve;
    int FilterMethod;
}

// https://registry.khronos.org/vulkan/specs/1.3-extensions/html/vkspec.html#primsrast-multisampling

static const float2 SubSample1Offsets[1] =
{
    float2(0.5, 0.5) - float2(0.5, 0.5),
};

static const float2 SubSample2Offsets[2] =
{
    float2(0.75, 0.75) - float2(0.5, 0.5),
    float2(0.25, 0.25) - float2(0.5, 0.5),
};

static const float2 SubSample4Offsets[4] =
{
    float2(0.375, 0.125) - float2(0.5, 0.5),
    float2(0.875, 0.375) - float2(0.5, 0.5),
    float2(0.125, 0.625) - float2(0.5, 0.5),
    float2(0.625, 0.875) - float2(0.5, 0.5),
};

static const float2 SubSample8Offsets[8] =
{
    float2(0.5625, 0.3125) - float2(0.5, 0.5),
    float2(0.4375, 0.6875) - float2(0.5, 0.5),
    float2(0.8125, 0.5625) - float2(0.5, 0.5),
    float2(0.3125, 0.1875) - float2(0.5, 0.5),
    float2(0.1875, 0.8125) - float2(0.5, 0.5),
    float2(0.0625, 0.4375) - float2(0.5, 0.5),
    float2(0.6875, 0.9375) - float2(0.5, 0.5),
    float2(0.9375, 0.0625) - float2(0.5, 0.5),
};

static const float2 SubSample16Offsets[16] =
{
    float2(0.5625, 0.5625) - float2(0.5, 0.5),
    float2(0.4375, 0.3125) - float2(0.5, 0.5),
    float2(0.3125, 0.6250) - float2(0.5, 0.5),
    float2(0.7500, 0.4375) - float2(0.5, 0.5),
    float2(0.1875, 0.3750) - float2(0.5, 0.5),
    float2(0.6250, 0.8125) - float2(0.5, 0.5),
    float2(0.8125, 0.6875) - float2(0.5, 0.5),
    float2(0.6875, 0.1875) - float2(0.5, 0.5),
    float2(0.3750, 0.8750) - float2(0.5, 0.5),
    float2(0.5000, 0.0625) - float2(0.5, 0.5),
    float2(0.2500, 0.1250) - float2(0.5, 0.5),
    float2(0.1250, 0.7500) - float2(0.5, 0.5),
    float2(0.0000, 0.5000) - float2(0.5, 0.5),
    float2(0.9375, 0.2500) - float2(0.5, 0.5),
    float2(0.8750, 0.9375) - float2(0.5, 0.5),
    float2(0.0625, 0.0000) - float2(0.5, 0.5),
};

#if SAMPLE_COUNT == 1
#define SubSampleOffsets SubSample1Offsets
#elif SAMPLE_COUNT == 2
#define SubSampleOffsets SubSample2Offsets
#elif SAMPLE_COUNT == 4
#define SubSampleOffsets SubSample4Offsets
#elif SAMPLE_COUNT == 8
#define SubSampleOffsets SubSample8Offsets
#elif SAMPLE_COUNT == 16
#define SubSampleOffsets SubSample16Offsets
#else
    #error do not support SAMPLE_COUNT
#endif

// All filtering functions assume that 'x' is normalized to [0, 1], where 1 == FilteRadius
float FilterBox(in float x)
{
    return x <= 1.0f;
}

static float FilterTriangle(in float x)
{
    return saturate(1.0f - x);
}

// static float FilterGaussian(in float x)
// {
//     const float sigma = GaussianSigma;
//     const float g = 1.0f / sqrt(2.0f * 3.14159f * sigma * sigma);
//     return (g * exp(-(x * x) / (2 * sigma * sigma)));
// }

float FilterCubic(float x, float B, float C)
{
    float y = 0.0f;
    float x2 = x * x;
    float x3 = x * x * x;
    if (x < 1)
        y = (12 - 9 * B - 6 * C) * x3 + (-18 + 12 * B + 6 * C) * x2 + (6 - 2 * B);
    else if (x <= 2)
        y = (-B - 6 * C) * x3 + (6 * B + 30 * C) * x2 + (-12 * B - 48 * C) * x + (8 * B + 24 * C);

    return y / 6.0f;
}



// float FilterSinc(in float x, in float filterRadius)
// {
//     float s;

//     x *= filterRadius * 2.0f;

//     if(x < 0.001f)
//         s = 1.0f;
//     else
//         s = sin(x * Pi) / (x * Pi);

//     return s;
// }

// float FilterBlackmanHarris(in float x)
// {
//     x = 1.0f - x;

//     const float a0 = 0.35875f;
//     const float a1 = 0.48829f;
//     const float a2 = 0.14128f;
//     const float a3 = 0.01168f;
//     return saturate(a0 - a1 * cos(Pi * x) + a2 * cos(2 * Pi * x) - a3 * cos(3 * Pi * x));
// }

// float FilterSmoothstep(in float x)
// {
//     return 1.0f - smoothstep(0.0f, 1.0f, x);
// }

float FilterCubic(float x)
{
    return FilterCubic(x * 2.0, 1.0, 0.0);
}

float FilterCatmullRom(float x)
{
    return FilterCubic(x * 2.0, 0.0, 0.5);
}

float FilterMitchell(float x)
{
    return FilterCubic(x * 2.0, 1.0 / 3.0, 1.0 / 3.0);
}

#define NUM_SCENE_COLOR_CACHE NUM_THREAD * NUM_THREAD * SAMPLE_COUNT
groupshared float4 SceneColorCache[NUM_SCENE_COLOR_CACHE];
uint GetSceneColorCacheIndex(uint groupIndex, uint sample_index)
{
    return groupIndex * SAMPLE_COUNT + sample_index;
}

uint GetSceneColorCacheIndex(uint2 groupThreadID, uint sample_index)
{
    uint groupIndex = groupThreadID.y * NUM_THREAD + groupThreadID.x;
    return GetSceneColorCacheIndex(groupIndex, sample_index);
}

void PreLoadSceneColorCache(const uint2 pixCoord, const uint2 groupThreadID, const uint groupIndex)
{
    if (pixCoord.x < Width || pixCoord.y < Height)
    {
        for (uint i = 0; i < SAMPLE_COUNT; i++)
        {
            SceneColorCache[GetSceneColorCacheIndex(groupIndex, i)] = in_SceneColorMS.Load(pixCoord, i);
        }
    }
}

groupshared float FilterWeightCache[NUM_SCENE_COLOR_CACHE];
void PreLoadFilterWeightCache(const uint2 pixCoord, const uint2 groupThreadID, const uint groupIndex)
{
    for (uint subSampleIdx = 0; subSampleIdx < SAMPLE_COUNT; ++subSampleIdx)
    {
        float2 sampleOffset = float2(int2(groupThreadID) - NUM_THREAD / 2);
        float2 subSampleOffset = SubSampleOffsets[subSampleIdx].xy;
        float2 sampleDist = abs(sampleOffset + subSampleOffset) / (ResolveFilterDiameter / 2.0f);

        float weight = 0.0;

        if (FilterMethod == FILTER_METHOD_CUBIC)
        {
            weight = FilterCubic(sampleDist.x) * FilterCubic(sampleDist.y);
        }
        else if (FilterMethod == FILTER_METHOD_CATMULLROM)
        {
            weight = FilterCatmullRom(sampleDist.x) * FilterCatmullRom(sampleDist.y);
        }
        else // if (FilterMethod == FILTER_METHOD_MITCHEL)
        {
            weight = FilterMitchell(sampleDist.x) * FilterMitchell(sampleDist.y);
        }

        FilterWeightCache[GetSceneColorCacheIndex(groupIndex, subSampleIdx)] = weight;
    }
}

float3 ResolveEmssive(const uint2 pixelPos, const uint2 groupThreadID, const uint groupIndex)
{
    float3 sum = 0.0f;
    float totalWeight = 0.0f;

    // float3 clrMin = 99999999.0f;
    // float3 clrMax = -99999999.0f;

    // float3 m1 = 0.0f;
    // float3 m2 = 0.0f;
    float mWeight = 0.0f;

    const int ResolveWidth = floor(ResolveFilterDiameter / 2.0f + 0.49999f);
    for (int y = -ResolveWidth; y <= ResolveWidth; y++)
    {
        for (int x = -ResolveWidth; x <= ResolveWidth; x++)
        {
            float2 sampleOffset = float2(x, y);
            float2 samplePos = pixelPos + sampleOffset;
            samplePos = clamp(samplePos, 0, float2(Width, Height) - 1.0f);

            int2 threadGroupPos = (int2)groupThreadID + int2(x, y);

            [unroll]
            for (uint subSampleIdx = 0; subSampleIdx < SAMPLE_COUNT; ++subSampleIdx)
            {
                float2 subSampleOffset = SubSampleOffsets[subSampleIdx].xy;
                float2 sampleDist = abs(sampleOffset + subSampleOffset) / (ResolveFilterDiameter / 2.0f);

                bool useSample = all(sampleDist <= 1.0f);
                if (useSample)
                {
                    float3 sample = 0.0;
                    if (0 <= threadGroupPos.x && threadGroupPos.x < NUM_THREAD && 0 <= threadGroupPos.y && threadGroupPos.y < NUM_THREAD)
                    {
                        sample = SceneColorCache[GetSceneColorCacheIndex(threadGroupPos, subSampleIdx)].xyz;
                    }
                    else
                    {
                        sample = in_SceneColorMS.Load(samplePos, subSampleIdx).xyz; //MSAALoad_(InputTexture, samplePos, subSampleIdx).xyz;
                    }
                    sample = max(sample, 0.0f);

                    float weight = FilterWeightCache[GetSceneColorCacheIndex(uint2(int2(x, y) + NUM_THREAD / 2), subSampleIdx)];
                    //float weight = 0.0;

                    //if (FilterMethod == FILTER_METHOD_CUBIC)
                    //{
                    //    weight = FilterCubic(sampleDist.x) * FilterCubic(sampleDist.y);
                    //}
                    //else if (FilterMethod == FILTER_METHOD_CATMULLROM)
                    //{
                    //    weight = FilterCatmullRom(sampleDist.x) * FilterCatmullRom(sampleDist.y);
                    //}
                    //else // if (FilterMethod == FILTER_METHOD_MITCHEL)
                    //{
                    //    weight = FilterMitchell(sampleDist.x) * FilterMitchell(sampleDist.y);
                    //}

                    //float sampleLum = Luminance(sample);

                    //if(UseExposureFiltering)
                    //    sampleLum *= exp2(ManualExposure - ExposureScale + ExposureFilterOffset);

                    //if(InverseLuminanceFiltering)
                    //    weight *= 1.0f / (1.0f + sampleLum);

                    sum += sample * weight;
                    totalWeight += weight;

                    // m1 += sample;
                    // m2 += sample * sample;
                    mWeight += 1.0f;
                }
            }
        }
    }

    float3 output = sum / max(totalWeight, 0.00001f);
    return output;
}

float ToksvigPrefilteringRoughness(float3 avgNormal, float originalRoughness)
{
    float BlinnPhoneExponent = 2.0 / (originalRoughness * originalRoughness) - 2.0;
    float avgNormalLen = length(avgNormal);
    // ft represents the “Toksvig factor”
    float ft = avgNormalLen / (avgNormalLen + BlinnPhoneExponent * (1.0 - avgNormalLen));

    float prefilteredBlinnPhoneExponent = ft * BlinnPhoneExponent;

    float prefilteredRoughness = sqrt(2.0 / (prefilteredBlinnPhoneExponent + 2.0));

    return prefilteredRoughness;
}

void ParseDepthBufferMS(
    in const uint2 pixCoord, in float rcpValidCount, in float validFlagList[SAMPLE_COUNT],
    out float avgDepth, out float depthList[SAMPLE_COUNT])
{
    avgDepth = 0.0;
    [unroll]
    for (uint i = 0; i < SAMPLE_COUNT; ++i)
    {
        float sampleDepth = in_depthBufferMS.Load(pixCoord, i);
        avgDepth += sampleDepth * validFlagList[i];
        depthList[i] = sampleDepth;
    }
    avgDepth *= rcpValidCount;
}

void ParseBaseColorBufferMS(
    in const uint2 pixCoord, in float rcpValidCount, in float validFlagList[SAMPLE_COUNT],
    out float4 avgBaseColor, out float4 baseColorList[SAMPLE_COUNT])
{
    avgBaseColor = 0;
    [unroll]
    for (uint i = 0; i < SAMPLE_COUNT; ++i)
    {
        float4 sampleBaseColor = in_baseColorBufferMS.Load(pixCoord, i);
        avgBaseColor += sampleBaseColor * validFlagList[i];
        baseColorList[i] = sampleBaseColor * validFlagList[i];
    }
    avgBaseColor *= rcpValidCount;
}

void ParseFresnelShadingModeMS(
    in const uint2 pixCoord, in float rcpValidCount, in float validFlagList[SAMPLE_COUNT],
    out float3 avgFresnel, out float3 fresnelList[SAMPLE_COUNT], out float materialTypeList[SAMPLE_COUNT])
{
    avgFresnel = 0.0;
    [unroll]
    for (uint i = 0; i < SAMPLE_COUNT; ++i)
    {
        float4 sampleFresnel = in_fresnelShadingModeMS.Load(pixCoord, i);
        avgFresnel += sampleFresnel.xyz * validFlagList[i];
        fresnelList[i] = sampleFresnel.xyz * validFlagList[i];
        materialTypeList[i] = sampleFresnel.w;
    }
    avgFresnel *= rcpValidCount;
}

void ParseMotionVectorOpacityAOMS(
    in const uint2 pixCoord, in float rcpValidCount, in float validFlagList[SAMPLE_COUNT],
    out float2 maxMotionVector, out float2 avgOpacityAO)
{
    avgOpacityAO = 0.0;
    [unroll]
    for (uint i = 0; i < SAMPLE_COUNT; ++i)
    {
        float4 sampleMotionVectorOpacityAO = in_MotionVectorOpacityAOMS.Load(pixCoord, i);
        maxMotionVector = length(sampleMotionVectorOpacityAO.xy) > 1e-3 ? sampleMotionVectorOpacityAO.xy : maxMotionVector;
        avgOpacityAO += sampleMotionVectorOpacityAO.zw * validFlagList[i];
    }
    avgOpacityAO *= rcpValidCount;
}

void ParseNormalBufferMS(
    in const uint2 pixCoord, in float rcpValidCount, in float validFlagList[SAMPLE_COUNT], in float4 normalBufferList[SAMPLE_COUNT],
    out float3 avgNormal, out float3 NormalWS, out float avgRoughness, out float avgSpecular, out float3 normalList[SAMPLE_COUNT], out float roughnessList[SAMPLE_COUNT], out float specularList[SAMPLE_COUNT])
{
    avgNormal = 0.0;
    avgRoughness = 0.0;
    avgSpecular = 0.0;
    [unroll]
    for (uint i = 0; i < SAMPLE_COUNT; ++i)
    {
        float4 normalBuffer = normalBufferList[i];
        float3 normalData;
        DecodeFromNormalBuffer(normalBuffer, normalData);
        avgNormal += normalData * validFlagList[i];
        avgRoughness += normalBuffer.b * validFlagList[i];;
        avgSpecular += normalBuffer.a * validFlagList[i];
        normalList[i] = normalData * validFlagList[i];
        roughnessList[i] = normalBuffer.b * validFlagList[i];
        specularList[i] = normalBuffer.a * validFlagList[i];
    }
    avgNormal *= rcpValidCount;
    avgRoughness *= rcpValidCount;
    avgSpecular *= rcpValidCount;
    NormalWS = normalize(avgNormal);
}

void ResolveMultiSampledGBuffer(const uint2 pixCoord, const uint2 groupThreadID, const uint groupIndex, bool ENABLE_AGREGATE, bool ENABLE_AGGREGATED_BASECOLOR)
{
    PreLoadSceneColorCache(pixCoord, groupThreadID, groupIndex);
    PreLoadFilterWeightCache(pixCoord, groupThreadID, groupIndex);
    GroupMemoryBarrierWithGroupSync();

    if (pixCoord.x >= Width || pixCoord.y >= Height)
    {
        return;
    }

    float validCount = 0.0;
    float4 normalBufferList[SAMPLE_COUNT];
    float validFlagList[SAMPLE_COUNT];
    uint i;
    [unroll]
    for (i = 0; i < SAMPLE_COUNT; i++)
    {
        normalBufferList[i] = in_NormalBufferMS.Load(pixCoord, i);
        float validFlag = float(int(any(normalBufferList[i])));
        validFlagList[i] = validFlag;
        validCount += validFlag;
    }

    if (validCount == 0)
    {
        return;
    }

    if (EmssiveResolve == 1)
    {

    }

    {
        float rcpValidCount = rcp(validCount); // validCount can't be zero

        // in_depthBufferMS
        float avgDepth;
        float depthList[SAMPLE_COUNT];
        ParseDepthBufferMS(pixCoord, rcpValidCount, validFlagList, avgDepth, depthList);

        // in_baseColorBufferMS
        float4 avgBaseColor;
        float4 baseColorList[SAMPLE_COUNT];
        ParseBaseColorBufferMS(pixCoord, rcpValidCount, validFlagList, avgBaseColor, baseColorList);
        

        // in_fresnelShadingModeMS
        float3 avgFresnel;
        float3 fresnelList[SAMPLE_COUNT];
        float materialTypeList[SAMPLE_COUNT];
        ParseFresnelShadingModeMS(pixCoord, rcpValidCount, validFlagList, avgFresnel, fresnelList, materialTypeList);

        // in_MotionVectorOpacityAOMS
        float2 maxMotionVector;
        float2 avgOpacityAO;
        ParseMotionVectorOpacityAOMS(pixCoord, rcpValidCount, validFlagList, maxMotionVector, avgOpacityAO);

        // in_NormalBufferMS
        float3 avgNormal;
        float3 NormalWS;
        float avgRoughness;
        float avgSpecular;
        float3 normalList[SAMPLE_COUNT];
        float roughnessList[SAMPLE_COUNT];
        float specularList[SAMPLE_COUNT];
        ParseNormalBufferMS(pixCoord, rcpValidCount, validFlagList, normalBufferList, avgNormal, NormalWS, avgRoughness, avgSpecular, normalList, roughnessList, specularList);

        
        float4 normalBuffer1 = 0;
        EncodeIntoNormalBuffer(NormalWS, avgRoughness, avgSpecular, normalBuffer1);

        float depth1 = avgDepth;
        float4 baseColor1 = avgBaseColor;

      
        

            float sumD = 0;
            float allD[SAMPLE_COUNT];
            float maxD0 = 0.0;
            uint D0i = 0;

            [unroll]
            for (i = 0; i < SAMPLE_COUNT; ++i)
            {
                float sampleDepth = depthList[i];
                float3 normal = normalList[i];
                float roughness = roughnessList[i];
                float A = abs(sampleDepth - avgDepth);
                float B = 1.0 - dot(normal, avgNormal);
                float DR = abs(roughness - avgRoughness);
                float D = A * DepthWeight + B * NormalWeight + DR * RoughnessWeight;
                D *= validFlagList[i];

                bool flag = D > maxD0;
                maxD0 = flag ? D : maxD0;
                D0i = flag ? i : D0i;
                allD[i] = D;
                sumD += D;
            }
            float avgD = sumD * rcpValidCount;

            float S = 0;
            float materialType = -1; // default unlit
            [unroll]
            for (i = 0; i < SAMPLE_COUNT; ++i)
            {
                float sigma = allD[i] - avgD;
                S += abs(sigma) * validFlagList[i];
                // find the first valid type
                // maybe a dither with frameIndex would be better? or it's not neccessary since the fsr id dithered
                materialType = validFlagList[i] > 0 && materialType ==-1? materialTypeList[i] : materialType;
            }

           float4 fresnelBuffer1 = float4(avgFresnel, materialType);


            bool complexPixel = maxD0 > ComplexPixelThreshold || S > ComplexPixelThreshold;

            if (complexPixel)
            {
                if (ENABLE_AGREGATE)
                {
                    float maxD1 = 0.0;
                    uint D1i = 0;
                    [unroll]
                    for (i = 0; i < SAMPLE_COUNT; ++i)
                    {
                        float A1 = abs(depthList[i] - depthList[D0i]);
                        float B1 = 1.0 - dot(normalList[i], normalList[D0i]);
                        float DR1 = abs(roughnessList[i] - roughnessList[D0i]);
                        float D1 = A1 * DepthWeight + B1 * NormalWeight + DR1 * RoughnessWeight;
                        D1 *= validFlagList[i];
                        bool flag = D1 > maxD1;
                        maxD1 = flag ? D1 : maxD1;
                        D1i = flag ? i : D1i;
                    }

                    if (D0i != D1i) // for prevent the rcpAggregateSampleCount being nan
                    {
                        uint aggregateAssignment[SAMPLE_COUNT] = (uint[SAMPLE_COUNT]) 0; // record each sample's aggregate index
                        [unroll]
                        for (i = 0; i < SAMPLE_COUNT; ++i)
                        {
                            aggregateAssignment[i] = 0;
                        }

                        uint aggregateSampleCount[2] = (uint[2]) 0;

                        [unroll]
                        for (i = 0; i < SAMPLE_COUNT; ++i)
                        {
                            float Ax0 = abs(depthList[i] - depthList[D0i]);
                            float Ax1 = abs(depthList[i] - depthList[D1i]);
                            float Bx0 = 1.0 - dot(normalList[i], normalList[D0i]);
                            float Bx1 = 1.0 - dot(normalList[i], normalList[D1i]);
                            float DRx0 = abs(roughnessList[i] - roughnessList[D0i]);
                            float DRx1 = abs(roughnessList[i] - roughnessList[D1i]);
                            float Dx0 = Ax0 * DepthWeight + Bx0 * NormalWeight + DRx0 * RoughnessWeight;
                            float Dx1 = Ax1 * DepthWeight + Bx1 * NormalWeight + DRx1 * RoughnessWeight;
                            uint aggregateIndex = Dx0 < Dx1 ? 0 : 1;
                            aggregateAssignment[i] = aggregateIndex;
                            aggregateSampleCount[aggregateIndex] += 1 * validFlagList[i];
                        }

                        float aggregateMaterialTypeList[2] = { materialTypeList[D0i], materialTypeList[D1i] };

                        float rcpAggregateSampleCount[2] = { rcp(float(aggregateSampleCount[0])), rcp(float(aggregateSampleCount[1])) };

                        float aggregateDepthList[2] = (float[2]) 0;
                        float4 aggregateBasecolorList[2] = (float4[2]) 0;
                        float4 aggregateNormalList[2] = (float4[2]) 0;
                        float3 aggregateFresnelList[2] = (float3[2]) 0;

                        [unroll]
                        for (i = 0; i < SAMPLE_COUNT; ++i)
                        {
                            uint agIdx = aggregateAssignment[i];
                            aggregateDepthList[agIdx] += depthList[i] * validFlagList[i];
                            aggregateBasecolorList[agIdx] += baseColorList[i] * validFlagList[i];
                            aggregateNormalList[agIdx] += float4(normalList[i], roughnessList[i]) * validFlagList[i];
                            aggregateFresnelList[agIdx] += fresnelList[i] * validFlagList[i];
                        }

                        [unroll]
                        for (i = 0; i < 2; ++i)
                        {
                            aggregateDepthList[i] *= rcpAggregateSampleCount[i];
                            aggregateBasecolorList[i] *= rcpAggregateSampleCount[i];
                            aggregateNormalList[i] *= rcpAggregateSampleCount[i];
                            aggregateFresnelList[i] *= rcpAggregateSampleCount[i];
                        }

                        float4 aggregateNormalBufferList[2] = (float4[2]) 0;
                        [unroll]
                        for (i = 0; i < 2; ++i)
                        {
                            EncodeIntoNormalBuffer(aggregateNormalList[i].xyz, aggregateNormalList[i].w, specularList[i], aggregateNormalBufferList[i]);
                        }

                        uint majorAggregateIndex = aggregateSampleCount[0] > aggregateSampleCount[1] ? 0 : 1;
                        majorAggregateIndex = aggregateSampleCount[0] == aggregateSampleCount[1] ? (aggregateDepthList[0] > aggregateDepthList[1] ? 0 : 1) : majorAggregateIndex;
                        uint minorAggregateIndex = majorAggregateIndex ^ 1;

                        // uint majorAggregateIndex = 0;
                        // uint minorAggregateIndex = 1;

                        depth1 = aggregateDepthList[majorAggregateIndex];
                        if (ENABLE_AGGREGATED_BASECOLOR)
                        {
                            baseColor1 = aggregateBasecolorList[majorAggregateIndex];
                        }
                        normalBuffer1 = aggregateNormalBufferList[majorAggregateIndex];
                        fresnelBuffer1 = float4(aggregateFresnelList[majorAggregateIndex], aggregateMaterialTypeList[majorAggregateIndex]);

                        out_depth2[pixCoord] = aggregateDepthList[minorAggregateIndex];
                        if (ENABLE_AGGREGATED_BASECOLOR)
                        {
                            out_BaseColorBuffer2[pixCoord] = aggregateBasecolorList[minorAggregateIndex];
                        }
                        out_NormalBuffer2[pixCoord] = aggregateNormalBufferList[minorAggregateIndex];
                        out_fresnelShadingModeBuffer2[pixCoord] = float4(aggregateFresnelList[minorAggregateIndex], aggregateMaterialTypeList[minorAggregateIndex]);

                        out_AgaaCoverage[pixCoord] = aggregateSampleCount[minorAggregateIndex] * rcp(SAMPLE_COUNT); // sample count in the second aggregate / sample count in that pixel
                        // todo sceneColor for resolv;
                    }
                }
                else // DISABLE AGREGATE
                {
                    uint aggregateSampleCount[2] = (uint[2]) 0; // record the sample count in each aggregate

                    float maxD1 = 0.0;
                    uint D1i = 0;
                    [unroll]
                    for (i = 0; i < SAMPLE_COUNT; ++i)
                    {
                        float A1 = abs(depthList[i] - depthList[D0i]);
                        float B1 = 1.0 - dot(normalList[i], normalList[D0i]);
                        float DR1 = abs(roughnessList[i] - roughnessList[D0i]);
                        float D1 = A1 * DepthWeight + B1 * NormalWeight + DR1 * RoughnessWeight;
                        D1 *= validFlagList[i];
                        bool flag = D1 > maxD1;
                        maxD1 = flag ? D1 : maxD1;
                        D1i = flag ? i : D1i;
                    }
                    
                    [unroll]
                    for (i = 0; i < SAMPLE_COUNT; ++i)
                    {
                        float Ax0 = abs(depthList[i] - depthList[D0i]);
                        float Ax1 = abs(depthList[i] - depthList[D1i]);
                        float Bx0 = 1.0 - dot(normalList[i], normalList[D0i]);
                        float Bx1 = 1.0 - dot(normalList[i], normalList[D1i]);
                        float DRx0 = abs(roughnessList[i] - roughnessList[D0i]);
                        float DRx1 = abs(roughnessList[i] - roughnessList[D1i]);
                        float Dx0 = Ax0 * DepthWeight + Bx0 * NormalWeight + DRx0 * RoughnessWeight;
                        float Dx1 = Ax1 * DepthWeight + Bx1 * NormalWeight + DRx1 * RoughnessWeight;
                        uint aggregateIndex = Dx0 < Dx1 ? 0 : 1;
                        aggregateSampleCount[aggregateIndex] += 1 * validFlagList[i];
                    }

                    uint majorSampleIndex = aggregateSampleCount[0] > aggregateSampleCount[1] ? D0i : D1i;
                    majorSampleIndex = aggregateSampleCount[0] == aggregateSampleCount[1] ? (depthList[D0i] > depthList[D1i] ? D0i : D1i) : majorSampleIndex;
                    materialType = materialTypeList[majorSampleIndex];

                    fresnelBuffer1 = float4(avgFresnel, materialType);
                }
            }

        out_depth[pixCoord] = depth1;
        out_baseColorBuffer[pixCoord] = baseColor1;
        out_NormalBuffer[pixCoord] = normalBuffer1;
        out_fresnelShadingModeBuffer[pixCoord] = fresnelBuffer1;
        out_MotionVectorOpacityAO[pixCoord] = float4(maxMotionVector.xy, avgOpacityAO.xy);
    }

    // find the maximum fragment coverage
    /*uint maxCoveringCount = 0;
    uint selectedSampleIndex = 0;
    for (uint i = 0; i < SampleCount; ++i)
    {
        uint coverage = uint(in_SceneColorMS.Load(pixCoord, i).w);
        uint curCoveringCount = countbits(coverage);
        
        if (curCoveringCount > 1)
        {
            selectedSampleIndex = i;
            break;
        }

        if (curCoveringCount > maxCoveringCount)
        {
            maxCoveringCount = curCoveringCount;
            selectedSampleIndex = i;
        }
    }

    out_NormalBuffer[pixCoord] = in_NormalBufferMS.Load(pixCoord, selectedSampleIndex);
    manual reoslve use sample_zero mode
    out_fresnelShadingModeBuffer[pixCoord] = in_fresnelShadingModeMS.Load(pixCoord, selectedSampleIndex);
    resolve the depth according to maximum fragment coverage
    out_depth[pixCoord] = in_depthBufferMS.Load(pixCoord, selectedSampleIndex);*/

    /*float maxSampleDepth = 0.0;
    uint theIndex = 0;
    for (uint i = 0; i < SampleCount; ++i)
    {
        float sampleDepth = in_depthBufferMS.Load(pixCoord, i);
        if (sampleDepth > maxSampleDepth)
        {
            maxSampleDepth = sampleDepth;
            theIndex = i;
        }
    }

    out_NormalBuffer[pixCoord] = in_NormalBufferMS.Load(pixCoord, theIndex);
    out_fresnelShadingModeBuffer[pixCoord] = in_fresnelShadingModeMS.Load(pixCoord, theIndex);
    out_depth[pixCoord] = maxSampleDepth;*/

    //ma  
    if (EmssiveResolve == 1 && validCount >= 1.0)
    {
        out_SceneColor[pixCoord].xyz = ResolveEmssive(pixCoord, groupThreadID, groupIndex);
    }

}

[numthreads(NUM_THREAD, NUM_THREAD, 1)]
void ResolveMultiSampledGBufferToAggregateBufferPass_aggregatedBasecolor(const uint2 pixCoord : SV_DispatchThreadID, const uint2 groupThreadID : SV_GroupThreadID, const uint groupIndex : SV_GroupIndex)
{
    ResolveMultiSampledGBuffer(pixCoord, groupThreadID, groupIndex, true, true);
}

[numthreads(NUM_THREAD, NUM_THREAD, 1)]
void ResolveMultiSampledGBufferToAggregateBufferPass(const uint2 pixCoord : SV_DispatchThreadID, const uint2 groupThreadID : SV_GroupThreadID, const uint groupIndex : SV_GroupIndex)
{
    ResolveMultiSampledGBuffer(pixCoord, groupThreadID, groupIndex, true, false);
}

[numthreads(NUM_THREAD, NUM_THREAD, 1)]
void ResolveMultiSampledGBufferPass(const uint2 pixCoord : SV_DispatchThreadID, const uint2 groupThreadID : SV_GroupThreadID, const uint groupIndex : SV_GroupIndex)
{
    ResolveMultiSampledGBuffer(pixCoord, groupThreadID, groupIndex, false, false);
}
#endif // RESOLVE_GBUFFERMS_HLSL