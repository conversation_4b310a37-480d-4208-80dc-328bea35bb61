#pragma compute K<PERSON>yeHistogramClear
#pragma compute MainAtomicCS
#pragma compute HistogramConvertCS
#pragma compute EyeAdaptationCS
#pragma compute AverageSceneLuminanceCS

RWTexture2D<uint> _HistogramScatter32Output : register(space0);
Texture2D<uint> _HistogramScatter32Texture: register(space0);
RWTexture2D<float4> _HistogramOutput : register(space0);
Texture2D<float4> _HistogramOutputTexture:register(space0);
Texture2D<float4> _Source : register(space0);
Texture2D<float4> _EyeAdaptationTextureHistory : register(space0);
Texture2D<float4> InSceneColor : register(space0);
RWTexture2D<float4> OutSceneColor : register(space0);
RWTexture2D<float4> _EyeAdaptationTenxture : register(space0);
SamplerState ce_Sampler_Clamp : register(space0);

cbuffer _cbCommon
{
    float4 _ScaleOffsetRes; // x: scale, y: offset, z: width, w: height
}

cbuffer _cb1
{
    float EyeAdaptation_ExposureLowPercent;
    float EyeAdaptation_ExposureHighPercent;
    float EyeAdaptation_MinAverageLuminance;
    float EyeAdaptation_MaxAverageLuminance;
    float EyeAdaptation_ExposureCompensationSettings;
    float EyeAdaptation_ExposureCompensationCurve;
    float EyeAdaptation_DeltaWorldTime;
    float EyeAdaptation_ExposureSpeedUp;
    float EyeAdaptation_ExposureSpeedDown;
    float EyeAdaptation_HistogramScale;
    float EyeAdaptation_HistogramBias;
    float EyeAdaptation_LuminanceMin;
    float EyeAdaptation_ExponentialUpM;
    float EyeAdaptation_ExponentialDownM;
}

#define BlackHistogramBucketInfluence 0.0f;
#define HISTOGRAMS_PER_THREAD 1
#define THREADGROUP_SIZEX 64
#define HISTOGRAM_SIZE 64
groupshared uint HistogramLocal[HISTOGRAM_SIZE];
//------------------------ Pass 1 -------------------------------
//-------------------- Clear Histogram --------------------------

[numthreads(16, 1, 1)]
void KEyeHistogramClear(uint3 id : SV_DISPATCHTHREADID)
{
    if (id.x < HISTOGRAM_SIZE * 2)
        _HistogramScatter32Output[uint2(id.x, 0u)] = 0u;
}

//------------------------ Pass 2 ------------------------------
//----------------- Accumulate Histogram -----------------------

void ClearLDS(uint GroupThreadId)
{
	for (uint t = 0 ; t < HISTOGRAMS_PER_THREAD ; ++t)
	{
		uint index = GroupThreadId * HISTOGRAMS_PER_THREAD + t;
		if (index < HISTOGRAM_SIZE)
		{
			HistogramLocal[index] = 0;
		}
	}
}
float CalculateEyeAdaptationLuminance(float3 Color)
{
	return max(dot(Color, float3(1.0f, 1.0f, 1.0f) / 3.0f), EyeAdaptation_LuminanceMin);
}
float ComputeHistogramPositionFromLuminance(float Luminance)
{
    float LogLuminance = log2(Luminance);
	return LogLuminance * EyeAdaptation_HistogramScale + EyeAdaptation_HistogramBias;
}
void CalculateBucketsAndWeights(in uint2 TexelPos, out float Weight0, out float Weight1, out uint Bucket0, out uint Bucket1)
{

	float4 SceneColor = _Source.Load(int3(TexelPos, 0));

	float LuminanceVal = CalculateEyeAdaptationLuminance(SceneColor.xyz);
	float LogLuminance = ComputeHistogramPositionFromLuminance(LuminanceVal);
	// Map the normalized histogram position into texels.
	float fBucket = saturate(LogLuminance) * (HISTOGRAM_SIZE-1);

	// Find two discrete buckets that straddle the continuous histogram position.
	Bucket0 = (uint)(fBucket);
	Bucket1 = Bucket0 + 1;

	Bucket0 = min(Bucket0, uint(HISTOGRAM_SIZE - 1));
	Bucket1 = min(Bucket1, uint(HISTOGRAM_SIZE - 1));

	// Weighted blend between the two buckets.
	Weight1 = frac(fBucket);
	Weight0 = 1.0f - Weight1;

	// When EyeAdaptat=.0, we will ignore the last bucket. The main use
	// case is so the black background pixels in the editor have no effect. But if we have cases where
	// pixel values can actually be black, we want to set EyeAdaptation_LastHistogramBucketInfluence=1.0.
	// This value is controlled by the cvar "r.EyeAdaptation.BlackHistogramBucketInfluence"
	if (Bucket0 == 0)
	{
		Weight0 *= BlackHistogramBucketInfluence;
	}
			
}
uint PackFloatToUINT32(float v)
{
	return (uint)(v*float( 1<<19 ));
}
void ScatterWeightsToLDS(uint Bucket0, uint Bucket1, float Weight0, float Weight1, float ScreenWeight, uint GroupThreadId)
{
    InterlockedAdd(HistogramLocal[Bucket0], PackFloatToUINT32(Weight0*ScreenWeight));
	InterlockedAdd(HistogramLocal[Bucket1], PackFloatToUINT32(Weight1*ScreenWeight));
}
void InterlockedAddToTexture(uint index, uint V)
{
	uint OldV;
	InterlockedAdd(_HistogramScatter32Output[uint2(index*2 + 0 , 0)], V, OldV);
	if (0xffffffff - V < OldV) // if 0xffffffff < OldV + V then it overflowed
	{
		InterlockedAdd(_HistogramScatter32Output[uint2(index*2 + 1, 0)], 1);
	}
}
void GatherWeightsFromLDSToTempTexture_Helper(uint GroupThreadId )
{
	uint Weight0=0;
	uint Bucket0 = GroupThreadId;
	Weight0 = HistogramLocal[Bucket0];
	InterlockedAddToTexture(Bucket0, Weight0);
	
}
void GatherWeightsFromLDSToTempTexture(uint GroupThreadId)
{
	for (uint t = 0 ; t < HISTOGRAMS_PER_THREAD ; ++t)
	{
		uint index = GroupThreadId * HISTOGRAMS_PER_THREAD + t;
		if (index < HISTOGRAM_SIZE)
		{
			GatherWeightsFromLDSToTempTexture_Helper(index);
		}
	}
}

[numthreads(THREADGROUP_SIZEX, 1, 1)]
void MainAtomicCS(
	uint3 GroupId : SV_GroupID,
	uint3 GroupThreadId : SV_GroupThreadID)
{
	//////////////////////////////////////////////////////
	// * CLEAR LDS *
	ClearLDS(GroupThreadId.x);
	
	GroupMemoryBarrierWithGroupSync();

	//////////////////////////////////////////////////////
	// * ACCUMULATE HISTOGRAM
	uint y = GroupId.x;
	for (uint x = 0 ; x < _ScaleOffsetRes.z ; x += THREADGROUP_SIZEX)
	{
		uint2 TexelPos = uint2(x + GroupThreadId.x, y);

		if (TexelPos.x < _ScaleOffsetRes.z)
		{
			float Weight0,Weight1;
			uint Bucket0,Bucket1;
			
			CalculateBucketsAndWeights(TexelPos, Weight0, Weight1, Bucket0, Bucket1);
           
			float2 InvViewSize = float2(1.0f/_ScaleOffsetRes.z, 1.0f/_ScaleOffsetRes.w);
			float2 ScreenUV = (TexelPos.xy) * InvViewSize;
			float ScreenWeight = 1.0f;
			ScatterWeightsToLDS(Bucket0, Bucket1, Weight0, Weight1, ScreenWeight, GroupThreadId.x);
		}
	}
    GroupMemoryBarrierWithGroupSync();
	//////////////////////////////////////////////////////
	// * UNPACK LDS TO BUFFER WITH ATOMICS (once per thread group)  (GATHER)
	GatherWeightsFromLDSToTempTexture(GroupThreadId.x);
}
float Unpack2UINT32ToFloat(uint hi, uint low)
{
	float flow = float(low) * (1.0f/float(1<<19));
	//so high 32 bits is effectively (hi<<32) * (1.0f/float(1<<19))
	float fhigh = float(hi) * float(1<<(32-19));
	return fhigh + flow;
}

[numthreads(THREADGROUP_SIZEX, 1, 1)]
void HistogramConvertCS(uint3 GroupThreadId : SV_GroupThreadID)
{
	float2 InvViewSize = float2(1.0f/_ScaleOffsetRes.z, 1.0f/_ScaleOffsetRes.w);
	float NormalizeFactor = InvViewSize.x * InvViewSize.y;
    float4 OldExposure = _EyeAdaptationTextureHistory.Load(int3(0, 0, 0));

	for (uint t = 0; t < HISTOGRAMS_PER_THREAD; t++)
	{
		uint index = GroupThreadId*HISTOGRAMS_PER_THREAD + t;
		if (index < HISTOGRAM_SIZE /4)
		{
				uint4 ValLow = uint4(
					_HistogramScatter32Texture[uint2((index * 4 + 0) * 2 + 0, 0)], 	
					_HistogramScatter32Texture[uint2((index * 4 + 1) * 2 + 0, 0)],
					_HistogramScatter32Texture[uint2((index * 4 + 2) * 2 + 0, 0)],
					_HistogramScatter32Texture[uint2((index * 4 + 3) * 2 + 0, 0)]);
				uint4 ValHigh = uint4(
					_HistogramScatter32Texture[uint2((index * 4 + 0) * 2 + 1, 0)], 	
					_HistogramScatter32Texture[uint2((index * 4 + 1) * 2 + 1, 0)],
					_HistogramScatter32Texture[uint2((index * 4 + 2) * 2 + 1, 0)],
					_HistogramScatter32Texture[uint2((index * 4 + 3) * 2 + 1, 0)]);
				//can't shift up by 32 in a 32 bit value
				float4 Val;
				Val.x = Unpack2UINT32ToFloat(ValHigh.x, ValLow.x);
				Val.y = Unpack2UINT32ToFloat(ValHigh.y, ValLow.y);
				Val.z = Unpack2UINT32ToFloat(ValHigh.z, ValLow.z);
				Val.w = Unpack2UINT32ToFloat(ValHigh.w, ValLow.w);

			Val *= 0.5f; 
			_HistogramOutput[uint2(index, 0)] = Val * NormalizeFactor;
			_HistogramOutput[uint2(index, 1)] = OldExposure;
		}
	}
}
float GetHistogramBucket(Texture2D HistogramTexture, uint BucketIndex)
{
	uint Texel = BucketIndex / 4;
	
	float4 HistogramColor = HistogramTexture.Load(int3(Texel, 0, 0));

	uint channel = BucketIndex % 4;
	float UnweightedValue = HistogramColor.r;
	UnweightedValue = (channel == 1) ? HistogramColor.g : UnweightedValue;
	UnweightedValue = (channel == 2) ? HistogramColor.b : UnweightedValue;
	UnweightedValue = (channel == 3) ? HistogramColor.a : UnweightedValue;

	return UnweightedValue;
}

float ComputeHistogramSum(Texture2D HistogramTexture)
{
	float Sum = 0;

	for(uint i = 0; i < HISTOGRAM_SIZE; ++i)
	{
		Sum += GetHistogramBucket(HistogramTexture, i);
	}

	return Sum;
}
float ComputeLogLuminanceFromHistogramPosition(float HistogramPosition)
{
	return ((HistogramPosition - EyeAdaptation_HistogramBias) / EyeAdaptation_HistogramScale);
}
float ComputeAverageLuminanceWithoutOutlier(Texture2D HistogramTexture, float MinFractionSum, float MaxFractionSum)
{
	float2 SumWithoutOutliers = 0;
	
	for(uint i = 0; i < HISTOGRAM_SIZE; ++i)
	{
		float LocalValue = GetHistogramBucket(HistogramTexture, i);

		// remove outlier at lower end
		float Sub = min(LocalValue, MinFractionSum);
		LocalValue = LocalValue - Sub;
		MinFractionSum -= Sub;
		MaxFractionSum -= Sub;
		
		// remove outlier at upper end
		LocalValue = min(LocalValue, MaxFractionSum);
		MaxFractionSum -= LocalValue;

		float LogLuminanceAtBucket = ComputeLogLuminanceFromHistogramPosition(float(i) / (float)(HISTOGRAM_SIZE-1));

		SumWithoutOutliers += float2(LogLuminanceAtBucket, 1) * LocalValue;
	}

	float AvgLogLuminance = SumWithoutOutliers.x / max(0.0001f, SumWithoutOutliers.y);

	return exp2(AvgLogLuminance);
}

float ComputeEyeAdaptationExposure(Texture2D HistogramTexture)
{
	const float HistogramSum = ComputeHistogramSum(HistogramTexture);
	const float AverageSceneLuminance = ComputeAverageLuminanceWithoutOutlier(HistogramTexture, HistogramSum * EyeAdaptation_ExposureLowPercent, HistogramSum * EyeAdaptation_ExposureHighPercent);
	const float LumAve = AverageSceneLuminance;

	const float ClampedLumAve = LumAve;
		
	// No longer clamping here. We are letting the target exposure be outside the min/max range, and then letting the exposure
	// gradually transion.
	return ClampedLumAve;
}
float ExponentialAdaption(float Current, float Target, float FrameTime, float AdaptionSpeed, float M)
{
	const float Factor = 1.0f - exp2(-FrameTime * AdaptionSpeed);
	const float Value = Current + (Target - Current) * Factor * M;
	return Value;
}

float LinearAdaption(float Current, float Target, float FrameTime, float AdaptionSpeed)
{
	const float Offset = FrameTime * AdaptionSpeed;

	const float Value = (Current < Target) ? min(Target, Current + Offset) : max(Target, Current - Offset);

	return Value;
}

float ComputeEyeAdaptation(float OldExposure, float TargetExposure, float FrameTime)
{
	const float LogTargetExposure = log2(TargetExposure);
	const float LogOldExposure = log2(OldExposure);

	const float LogDiff = LogTargetExposure - LogOldExposure;

	const float AdaptionSpeed = (LogDiff > 0) ? EyeAdaptation_ExposureSpeedUp : EyeAdaptation_ExposureSpeedDown;
	const float M = (LogDiff > 0) ? EyeAdaptation_ExponentialUpM : EyeAdaptation_ExponentialDownM;

	const float AbsLogDiff = abs(LogDiff);

	// blended exposure
	const float LogAdaptedExposure_Exponential = ExponentialAdaption(LogOldExposure, LogTargetExposure, FrameTime, AdaptionSpeed, M);
	const float LogAdaptedExposure_Linear = LinearAdaption(LogOldExposure, LogTargetExposure, FrameTime, AdaptionSpeed);

	const float LogAdaptedExposure = AbsLogDiff > 1.5f ? LogAdaptedExposure_Linear : LogAdaptedExposure_Exponential;

	// Note: no clamping here. The target exposure should always be clamped so if we are below the min or above the max,
	// instead of clamping, we will gradually transition to the target exposure. If were to clamp, the then we would have a harsh transition
	// when going from postFX volumes with different min/max luminance values.
	const float AdaptedExposure = exp2(LogAdaptedExposure);

	// for manual mode or camera cuts, just lerp to the target
	const float AdjustedExposure = lerp(AdaptedExposure,TargetExposure,0.0f);
	
	return AdjustedExposure;
}

float4 EyeAdaptationCommon(float AverageSceneLuminance, float OldExposureScale)
{
    const float TargetAverageLuminance = clamp(AverageSceneLuminance, EyeAdaptation_MinAverageLuminance, EyeAdaptation_MaxAverageLuminance);

	// White point luminance is target luminance divided by 0.18 (18% grey).
    const float TargetExposure = TargetAverageLuminance / 0.18;

    const float MiddleGreyExposureCompensation = EyeAdaptation_ExposureCompensationSettings * EyeAdaptation_ExposureCompensationCurve; // we want the average luminance remapped to 0.18, not 1.0
    const float OldExposure = MiddleGreyExposureCompensation / (OldExposureScale != 0 ? OldExposureScale : 1.0f);

	// eye adaptation changes over time
    const float EstimatedExposure = ComputeEyeAdaptation(OldExposure, TargetExposure, EyeAdaptation_DeltaWorldTime);

	// maybe make this an option to avoid hard clamping when transitioning between different exposure volumes?
    const float SmoothedExposure = clamp(EstimatedExposure, EyeAdaptation_MinAverageLuminance / .18f, EyeAdaptation_MaxAverageLuminance / .18f);

    const float SmoothedExposureScale = 1.0f / max(0.0001f, SmoothedExposure);
    const float TargetExposureScale = 1.0f / max(0.0001f, TargetExposure);

    float4 OutData;
	// Output the number that will rescale the image intensity
    OutData.x = MiddleGreyExposureCompensation * SmoothedExposureScale;
	// Output the target value
    OutData.y = MiddleGreyExposureCompensation * TargetExposureScale;
    OutData.z = AverageSceneLuminance;
    OutData.w = MiddleGreyExposureCompensation;

    return OutData;
}

[numthreads(1, 1, 1)]
void EyeAdaptationCS(uint2 DispatchThreadId : SV_DispatchThreadID,
    uint2 GroupThreadId : SV_GroupThreadID)
{
    const float AverageSceneLuminance = ComputeEyeAdaptationExposure(_HistogramOutputTexture);
    const float OldExposureScale = _HistogramOutputTexture.Load(int3(0, 1, 0)).x;

    const float4 OutColor = EyeAdaptationCommon(AverageSceneLuminance, OldExposureScale);
    _EyeAdaptationTenxture[DispatchThreadId] = OutColor;
}

[numthreads(1, 1, 1)]
void AverageSceneLuminanceCS(uint2 DispatchThreadId : SV_DispatchThreadID,
    uint2 GroupThreadId : SV_GroupThreadID)
{
    float4 OutColor = 0;
    const float AverageSceneLuminance = ComputeEyeAdaptationExposure(_HistogramOutputTexture);
	
    OutColor.x = 1;
    OutColor.y = 1;
    OutColor.z = AverageSceneLuminance;
    OutColor.w = 1;
    _EyeAdaptationTenxture[DispatchThreadId] = OutColor;
}