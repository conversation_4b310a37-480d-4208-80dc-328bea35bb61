#ifndef VIEW_MODE_VISUALIZE_HLSL
#define VIEW_MODE_VISUALIZE_HLSL

//#ifdef ENABLE_VIEW_MODE_VISUALIZE
#define LIGHTING_ONLY_DIFFUSE_COLOR 0.5f
SHADER_CONST(int, VIEW_MODE, 0);

#define ViewMode_Lit                     0
#define ViewMode_Unlit                   1
#define ViewMode_Wireframe               2
#define ViewMode_DetailLighting          3
#define ViewMode_LightingOnly            4

#define ViewMode_BaseColor               5
#define ViewMode_Depth                   6
#define ViewMode_WorldNormal             7
#define ViewMode_DiffuseColor            8
#define ViewMode_MaterialAO              9
#define ViewMode_Metallic                10
#define ViewMode_Opacity                 11
#define ViewMode_Roughness               12
#define ViewMode_Specular                13
#define ViewMode_SpecularColor           14
#define ViewMode_SubsurfaceColor         15
#define ViewMode_ShadingModel            16
#define ViewMode_MotionVector            17

#define ViewMode_AmbientOcclusion        18
#define ViewMode_BentNormal              19
#define ViewMode_Reflections             20
#define ViewMode_SeparateTranslucencyRGB 21
#define ViewMode_GlobalIllumination      22
#define ViewMode_SceneColor              23

#define ViewMode_GILighting              24
#define ViewMode_DebugColor              25
#define ViewMode_EmissiveColor           26

#define ViewMode_Cluster                 27
#define ViewMode_Triangle                28

#endif

