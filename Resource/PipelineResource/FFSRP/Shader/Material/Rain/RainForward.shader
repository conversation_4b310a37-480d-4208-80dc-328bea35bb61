#pragma vertex VSMain
#pragma pixel PSMain
#pragma only_renderers vulkan
#pragma keyword LIGHT_MAP_ENABLE
#pragma keyword LIGHT_MAP_UV_CHANNEL_0
#pragma keyword USE_VERTEX_COLOR
#pragma keyword REFLECTION_BOX_PROJECTION
#pragma keyword CE_USE_DOUBLE_TRANSFORM
#define NUM_MATERIAL_TEXCOORDS 2

#define ADDITIONAL_MTL_PARAM \
float FlowSpeed_General; \
float BlurStrength; \
float Distortion; \
float UVScale; \
float Aspect; \
float4 _CustomBaseColor; \
float _CustomRoughness; \
float3 Velocity; \
float3 Wind; \
float RainIntensity; \
float SpeedFactor; \
float Layer0_Aspect; \
float Layer0_UVScale; \
float Layer0_FlowSpeed; \
float Layer0_DropSize; \
float Layer0_Wiggle; \
float Layer1_Aspect; \
float Layer1_UVScale; \
float Layer1_FlowSpeed; \
float Layer1_DropSize; \
float Layer1_Wiggle; \
float Static_Aspect; \
float Static_UVScale; \
float Static_DropSize; \
float Static_ChangeSpeed;\
bool MtlUseWGS84; \
bool MipMapDebug;\
float DropletLength; 

#include "../Lit/SurfaceShaderIncludes.hlsl"

Texture2D<float4> _CustomBaseColorTex : register(space1);
Texture2D<float> _CustomRoughnessTex : register(space1);
Texture2D<float4> _NoiseTex : register(space1);
Texture2D<float4> ce_Scene_Color : register(space0);

struct DropLayerOut{
	float m;
	float trail;
	//float2 offset;
	//float blur;
	float2 subUV;
};

struct DropLayerIn{
	float2 uv;
	float uvScale;
	float aspect;
	float flowSpeed;
	float dropSize;
	float wiggle;
	float time;
	float noise;
};

struct StaticLayerIn{
	float2 uv;
	float aspect;
	float uvScale;
	float changeSpeed;
	float dropSize;
	float time;
};

float N21(float2 p){
	p = frac(p * float2(123.34, 345.45));
	p += dot(p, p + 34.345);
	return frac(p.x + p.y);
}

float3 N13(float p) {
   float3 p3 = frac(p * float3(0.1031f, 0.11369f, 0.13787f));
   p3 += dot(p3, p3.yzx + 19.19f);
   return frac(float3((p3.x + p3.y) * p3.z, (p3.x + p3.z) * p3.y, (p3.y + p3.z) * p3.x));
}

float Nrand(float x){
	return frac(sin(x * 1234.564) * 7658.76);
}		

float Saw(float b, float t){
	return smoothstep(0.f, b, t) * smoothstep(1.f, b, t);
}

float GenerateStaticLayer(StaticLayerIn param){
	float2 uv = param.uv * param.uvScale * float2(param.aspect, 1.f) * 40.f;
	float t = param.time * param.changeSpeed;

	float2 id = floor(uv);
	uv = frac(uv) - 0.5f;
	float3 rand3 = N13(id.x*107.45+id.y*3543.654);
	float2 p = (rand3.xy - 0.5f.xx) * 0.7f;
	float dist = length((uv - p) * float2(1.f, param.aspect));

	float fade = Saw(0.025f, frac(t + rand3.z));
	float c = smoothstep(0.3f * param.dropSize, 0.f, dist) * frac(rand3.z * 10.f) * fade;
	return c;
}

DropLayerOut GenerateDropLayer(DropLayerIn param){
	float t = param.time * param.flowSpeed * FlowSpeed_General;

	float2 uv = param.uv;
	uv.y += t;
	//float2 uv = ( * param.flowSpeed) * param.uvScale * aspect;
	float2 aspect = float2(param.aspect, 1);
	float2 id = floor(uv * aspect * param.uvScale);
	float colShift = Nrand(id.x);
	uv.y += colShift;

	id = floor(uv * aspect * param.uvScale);
	float3 rand3 = N13(id.x * 35.2f + id.y * 2376.1f);
	float2 subUV = frac(uv * aspect * param.uvScale) - float2(0.5f, 0.5f);

	float2 dropSize = float2(0.03f, 0.05f) * param.dropSize;
	float2 trailSize = float2(0.01f, 0.03f) * param.dropSize;

	// float2 randSeed = floor(subUV);
	// float rand = N21(randSeed);
	// t += rand * 6.2831f;
	// subUV = frac(subUV) - 0.5f.xx; // [-0.5, 0.5] for each grid

	float posX = rand3.x - 0.5f;
	float posY = param.uv.y * param.uvScale * 10.f;
	float wiggle = sin((posY + sin(posY)));
	posX += param.wiggle * wiggle * (0.5f - abs(posX)) * (rand3.z - 0.5f);
	posX *= 0.7f;
	posX += 0.3 * param.noise;
	//posX = clamp(posX, -0.5 + dropSize.y, 0.5 - dropSize.y);
	float ti = frac(t + rand3.z);
	posY = (Saw(0.85f, ti) - 0.5f) * 0.9f;



	float2 pos = float2(posX, posY);
	float dist = length((subUV - pos) * aspect.yx);
	float mainDrop = smoothstep(0.4f * param.dropSize, 0.f, dist);
	float r = sqrt(smoothstep(1.f, posY, subUV.y));
	float cd = abs(subUV.x - posX);
	float trail = smoothstep(0.23f * r * param.dropSize, 0.15f * r * r * param.dropSize, cd);
	float trailFront = smoothstep(-0.02f, 0.02f, subUV.y - posY);
	trailFront *= smoothstep(0.5, posY, subUV.y); // make trail softer
	trail *= trailFront * r * r;

	float y = param.uv.y;
	float trail2 = smoothstep(0.2f * r * param.dropSize, 0.f, cd);
	float droplets = max(0.f, (sin(y * (1.f - y) * 120.f) - subUV.y)) * trail2 * trailFront * rand3.z;
	y = frac(y) + (subUV.y + rand3.y - 0.5f);
	float dropletDist = length((subUV - float2(posX, y)));
	droplets = smoothstep(0.3f * param.dropSize, 0.f, dropletDist);
	float m = mainDrop + droplets * r * trailFront;

	DropLayerOut ret;
	ret.m = m;
	ret.trail = trail;
	ret.subUV = subUV;



	// float W = uv.y * 10.f;
	// float posX = (rand3.x - 0.5f) * 0.8f;
	// posX += (0.4 - abs(posX)) * sin(1.4f * W) * pow(abs(sin(W)), 6) * param.wiggle;
	// posX = clamp(posX, -0.5 + dropSize.y, 0.5 - dropSize.y);
	// float posY = -sin(t + sin(t + sin(t) * 0.5)).x * 0.45f;
	// //posY -= ((subUV.x - posX) * (subUV.x - posX)) / aspect;

	// float2 dropPos = (subUV - float2(posX, posY)) / aspect;
	// float2 trailPos = (subUV - float2(posX, t)) / aspect;
	// trailPos.y = (frac(trailPos.y * 8) - 0.5f) / 8;
	
	// //float lengthDrop = length(dropPos / aspect);
	// float drop = smoothstep(dropSize.y, dropSize.x, length(dropPos));
	// float trail  = smoothstep(trailSize.y, trailSize.x, length(trailPos));
	// trail *= smoothstep(-0.05, 0.05, dropPos.y);
	// trail *= smoothstep(0.5, posY, subUV.y);
	// float fogTrail = smoothstep(-0.05f, 0.05f, dropPos.y);  // Fog Trail Size
	// fogTrail *= smoothstep(0.5, posY, subUV.y);
	// fogTrail *= smoothstep(0.05f, 0.04f, abs(dropPos.x));
	// trail *= fogTrail;

	// DropLayerOut ret;
	// ret.offset = drop * dropPos + trail * trailPos;
	// ret.blur = BlurStrength * 7.f * (1.f - fogTrail);
	// ret.subUV = subUV;

	return ret;
}

SurfaceData GetSurfaceData(SurfaceInput input)
{
	float3 positionWS = input.positionWS.xyz;
#ifdef CE_USE_DOUBLE_TRANSFORM
	positionWS.xyz = GetLargeCoordinateAbsolutePosition(positionWS.xyz, ce_TilePosition);
	//baseColor.xyz = float3(1, 0, 0);
#endif

	float3 gravityDir = float3(0, -1, 0);
	if (MtlUseWGS84)
		gravityDir = -normalize(positionWS.xyz);
	float3 force = -gravityDir + (Wind - Velocity) * SpeedFactor;

	float sinA = -dot(input.tangentWS, force);
	//sinA = -sinA; // for certain window model
	float cosA = dot(input.binormalWS, force);
	float lenD = sqrt(sinA * sinA + cosA * cosA);
	sinA /= lenD;
	cosA /= lenD;

	float2 uv = float2(input.uv.x * cosA - input.uv.y * sinA, input.uv.x * sinA + input.uv.y * cosA);

	float noise = _NoiseTex.Sample(ce_Sampler_Repeat, input.uv * 1.4 );
	noise = (noise-0.5)*2;
	
	float t = fmod(ce_Time.x, 7200);
	DropLayerIn Layer0_in;
	Layer0_in.uv = uv * UVScale;
	Layer0_in.uvScale = Layer0_UVScale;
	Layer0_in.aspect = Layer0_Aspect;
	Layer0_in.flowSpeed = Layer0_FlowSpeed * RainIntensity;
	Layer0_in.dropSize = Layer0_DropSize;
	Layer0_in.wiggle = Layer0_Wiggle;
	Layer0_in.time = t;
	Layer0_in.noise = noise;
	DropLayerOut Layer0_out = GenerateDropLayer(Layer0_in);

	DropLayerIn Layer1_in;
	Layer1_in.uv = uv * UVScale;
	Layer1_in.uvScale = Layer1_UVScale;
	Layer1_in.aspect = Layer1_Aspect;
	Layer1_in.flowSpeed = Layer1_FlowSpeed * RainIntensity;
	Layer1_in.dropSize = Layer1_DropSize;
	Layer1_in.wiggle = Layer1_Wiggle;
	Layer1_in.time = t;
	Layer1_in.noise = noise;
	DropLayerOut Layer1_out = GenerateDropLayer(Layer1_in);

	StaticLayerIn Static_in;
	Static_in.uv = uv * UVScale;
	Static_in.uvScale = Static_UVScale;
	Static_in.aspect = Static_Aspect;
	Static_in.changeSpeed = Static_ChangeSpeed * RainIntensity;
	Static_in.dropSize = Static_DropSize;
	Static_in.time = t;
	float Static_out = GenerateStaticLayer(Static_in);
	

	// float2 offset = max(max(Layer0_out.offset, Layer1_out.offset), Layer2_out.offset);
	// float blur = min(min(Layer0_out.blur, Layer1_out.blur), Layer2_out.blur);
	float m = smoothstep(0.3, 1, Layer0_out.m + Layer1_out.m + Static_out);
	//m  = smoothstep(0.3f, 1.f, Static_out);
	float trail = max(Layer0_out.trail, Layer1_out.trail);
	float2 subUV = Layer0_out.subUV;
	float2 cheapNormal = float2(ddx(m), ddy(m));

	float blur = BlurStrength * 7.f * (1.f - trail);
	//float blur = lerp(BlurStrength - trail, 0.f, smoothstep(0.1f, 0.2f, m));

	//float c = trail + m;
	//float4 baseColor = float4(c,c,c,1.f);
	
	float4 baseColor;
	if (MipMapDebug)
		baseColor = _CustomBaseColorTex.SampleLevel(ce_Sampler_Repeat, input.screenUV + cheapNormal, blur);
	else 
		baseColor = ce_Scene_Color.Sample(ce_Sampler_Mirror, input.screenUV + cheapNormal.xy * _NormalScale);
		

	//float4 baseColor = ce_Scene_Color.Sample(ce_Sampler_Repeat, input.screenUV + offset * Distortion);
	//float4 baseColor = ce_Scene_Color.Sample(ce_Sampler_Repeat, input.screenUV + cheapNormal);
	//if (subUV.x > 0.48 || subUV.y > 0.48) baseColor = float4(1,0,0,1);
	//float4 baseColor = float4(input.screenUV, 0.f, 1.f);
	//float4 baseColor = float4(0.5,0,0,0.8f);
	//baseColor = float4(du, 0, dv, 1.f);

	SurfaceData surfaceData = (SurfaceData)0;

	surfaceData.baseColor = 0;
	//surfaceData.opacityMask = baseColor.a;
	//surfaceData.opacityMask = m;
	surfaceData.opacity = m;
	//surfaceData.opacityMaskClip = 1.0f;
	surfaceData.normalTS = float3(0, 0, 1);
	surfaceData.ambientOcclusion = 1;
	surfaceData.roughness = _CustomRoughness;
	surfaceData.emissiveColor = baseColor.xyz;
	surfaceData.temporalReactive = float2(surfaceData.opacity * 0.5, surfaceData.opacity * 0.5);
	surfaceData.materialType = MATERIAL_TYPE;
	return surfaceData;
}

#include "../../Material/Lit/Lit.hlsl"
#include "../../Lighting/LightLoop/LightLoop.hlsl"


VSOutput VSMain(VSInput input)
{
	return VSInputToVSOutput(input);
}

float4 PSMain(VSOutput vsoutput, bool isFrontFace : SV_IsFrontFace) : SV_Target0
{
	PSInput input = VSOutputToPSInput(vsoutput);
	PositionInputs posInput = GetPositionInput(input.positionWS, input.screenUV);
	float3 V = GetWorldSpaceNormalizeViewDir(posInput.positionWS);

	SurfaceData surfaceData;
	BuiltinData builtinData;
// #if defined(INSTANCING) && INSTANCING == 1
	//GetSurfaceAndBuiltinData(V, input, isFrontFace, input.instanceID, surfaceData, builtinData);
// #else
	GetSurfaceAndBuiltinData(V, input, isFrontFace, 0, surfaceData, builtinData);
//#endif

	return float4(surfaceData.emissiveColor.xyz, 1.f);
}


/*

#include "Lighting/TransparentShadingUtils.hlsl"

VSOutput VSMain(VSInput input)
{
	return VSInputToVSOutput(input);
}


TransparentPSOutput PSMain(VSOutput vsoutput, bool isFrontFace : SV_IsFrontFace) 
{
	TransparentPSOutput psOut = (TransparentPSOutput)0;
	PSInput input = VSOutputToPSInput(vsoutput);
	PositionInputs posInput = GetPositionInput(input.positionWS, input.screenUV);
    posInput.positionSS = input.positionNDC.xy;
    posInput.deviceDepth = input.positionNDC.z;

	float3 V = GetWorldSpaceNormalizeViewDir(posInput.positionWS);

	SurfaceData surfaceData;
	BuiltinData builtinData;

	GetSurfaceAndBuiltinData(V, input, isFrontFace, 0, surfaceData, builtinData);


	BSDFData bsdfData = ConvertSurfaceDataToBSDFData(surfaceData);

	LightLoopOutput lightLoopOutput = LightLoop(V, posInput, bsdfData, builtinData, input);
	
	float3 directLighting = lightLoopOutput.diffuseLighting + lightLoopOutput.specularLighting + surfaceData.emissiveColor;
	float3 indirectLighting = builtinData.indirectLighting;
	float4 outColor = float4(directLighting + indirectLighting, surfaceData.opacity);

	// Blend with fog : ----------------
	float3 pinAbsWS = posInput.positionWS.xyz;
    float3 cameraAbsWS = ce_CameraPos.xyz;
#ifdef CE_USE_DOUBLE_TRANSFORM
    pinAbsWS = GetLargeCoordinateAbsolutePosition(pinAbsWS, ce_CameraTilePosition);
    cameraAbsWS = GetLargeCoordinateAbsolutePosition(cameraAbsWS, ce_CameraTilePosition);
#endif

	float linearDepth = LinearEyeDepth(posInput.deviceDepth, ce_Projection);


	psOut.sceneColor = outColor;



    psOut.reactiveMask.xy = surfaceData.temporalReactive;

	
	return psOut;
}

*/