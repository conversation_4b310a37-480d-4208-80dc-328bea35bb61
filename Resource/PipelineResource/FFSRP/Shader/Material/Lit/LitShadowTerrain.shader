#pragma vertex VSMain
#pragma pixel PSMain
#pragma keyword INSTANCING
#pragma keyword PUNCTUAL_LIGHT
#pragma keyword CE_USE_DOUBLE_TRANSFORM
#define NUM_MATERIAL_TEXCOORDS 2

#define SHADOW_PASS

#include "../../Material/Lit/SurfaceTerrainShaderIncludes.hlsl"

#include "../../Material/Material.hlsl"
#include "../../Lighting/Lighting.hlsl"

#include "../../Material/Lit/LitDataTerrain.hlsl"
#include "../../Material/Lit/Lit.hlsl"
#include "../../RenderPass/ShaderPassDepthForShadow.hlsl"