#pragma vertex VSMain
#pragma pixel PSMain

#pragma keyword LIGHT_MAP_ENABLE
#pragma keyword LIGHT_MAP_UV_CHANNEL_0
#pragma keyword LOD_VISUALIZER

#include "../../ShaderLibrary/GlobalModelVariables.hlsl"
#include "../../Material/Lit/LitVariables.hlsl"

#include "../../ShaderLibrary/VertexQTangents.hlsl"

#include "../../Material/Material.hlsl"
#include "../../Lighting/Lighting.hlsl"

#include "../../Material/Lit/LitData.hlsl"
#include "../../Lighting/LightLoop/LightLoop.hlsl"
#include "../../RenderPass/ShaderPassForward.hlsl"