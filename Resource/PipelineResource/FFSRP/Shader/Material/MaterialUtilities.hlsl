#ifndef __MATERIAL_UTILITIES__
#define __MATERIAL_UTILITIES__


//float SampleGrassOcclusion(float2 terrainUV)
//{
//    return lerp(1.0, _GrassOcclusionMap.Sample(ce_Sampler_Clamp, terrainUV).a, _GrassOcclusionAmountTerrain);
//}


// float GetTreeOcclusion(float3 positionWS, float4 treeOcclusionInput, PSInput psInput) {
// #if defined(_ANIM_SINGLE_PIVOT_COLOR) || defined(_ANIM_HIERARCHY_PIVOT)
//     if (_UseTreeOcclusion) {

//         float matrix13;
// #if defined(INSTANCING) && INSTANCING == 1
//         matrix13 = psInput.ce_World[1][3] - ce_CameraPos.y;//camera relative
// #else
//         matrix13 = ce_World[1][3] - ce_CameraPos.y;//camera relative
// #endif

//         float treeWidth = _Tree12Width == 0 ? 1.f : saturate((positionWS.y - matrix13) / _Tree12Width);
//         float treeDO = lerp(_TreeDO, _TreeDO2, treeWidth);
//         float treeAO = lerp(_TreeAO, _TreeAO2, treeWidth);
//         float4 lightDir = float4(-normalize(ce_LightDirPos[0].xyz) * treeDO, treeAO);
//         float treeDOBias = lerp(_TreeDOBias, _TreeDOBias2, treeWidth);
//         float treeAOBias = lerp(_TreeAOBias, _TreeAOBias2, treeWidth);
//         return saturate(dot(saturate(treeOcclusionInput + float4(treeDOBias.rrr, treeAOBias)), lightDir));
//     }
//     else
// #endif
//     {
//         return 1.f;
//     }
// }

half3 SHEvalLinearL0L1(half3 N, half4 shAr, half4 shAg, half4 shAb)
{
    half4 vA = half4(N, 1.0);

    half3 x1;
    // Linear (L1) + constant (L0) polynomial terms
    x1.r = dot(shAr, vA);
    x1.g = dot(shAg, vA);
    x1.b = dot(shAb, vA);

    return x1;
}

half3 SHEvalLinearL2(half3 N, half4 shBr, half4 shBg, half4 shBb, half4 shC)
{
    half3 x2;
    // 4 of the quadratic (L2) polynomials
    half4 vB = N.xyzz * N.yzzx;
    x2.r = dot(shBr, vB);
    x2.g = dot(shBg, vB);
    x2.b = dot(shBb, vB);

    // Final (5th) quadratic (L2) polynomial
    half vC = N.x * N.x - N.y * N.y;
    half3 x3 = shC.rgb * vC;

    return x2 + x3;
}

half3 SampleSH9(half4 SHCoefficients[7], half3 N)
{
    half4 shAr = SHCoefficients[0];
    half4 shAg = SHCoefficients[1];
    half4 shAb = SHCoefficients[2];
    half4 shBr = SHCoefficients[3];
    half4 shBg = SHCoefficients[4];
    half4 shBb = SHCoefficients[5];
    half4 shCr = SHCoefficients[6];

    // Linear + constant polynomial terms
    half3 res = SHEvalLinearL0L1(N, shAr, shAg, shAb);

    // Quadratic polynomials
    res += SHEvalLinearL2(N, shBr, shBg, shBb, shCr);

    return res;
}

//half SampleOcclusionProbes(float3 positionWS)
//{
//	float4 pos = mul(_OcclusionProbesWorldToLocal, float4(positionWS, 1));
//	return _OcclusionProbes.Sample(texture_sampler, pos.xyz).r;
//}



float hash2D(float2 input)
{
    return frac(1.0e4 * sin(17.0*input.x + 0.1*input.y) * (0.1 + abs(sin(13*input.y + input.x))));
}

float hash3D(float3 input)
{
    return hash2D(float2(hash2D(input.xy), input.z));
}

// GDC 2017. Hashed Alpha Testing
float hashedAlpha(float3 objCoord)
{
    //Find the discretized derivatives of our coordinates
    float maxDeriv = max( length(ddx(objCoord.xyz)), length(ddy(objCoord.xyz)) );
    float g_HashScale = 0.1;
    float pixScale = 1.0/(g_HashScale*maxDeriv);
    //Find two nearest log-discretized noise scales
    float2 pixScales = float2( exp2(floor(log2(pixScale))),exp2(ceil(log2(pixScale))) );
    //Compute alpha thresholds at our two noise scales
    float2 alpha=float2(hash3D(floor(pixScales.x*objCoord.xyz)), hash3D(floor(pixScales.y*objCoord.xyz)));
    //Factor to interpolate lerp with
    float lerpFactor = frac(log2(pixScale));
    //Interpolate alpha threshold from noise at two scales
    float x = (1-lerpFactor)*alpha.x + lerpFactor*alpha.y;
    //Pass into CDF to compute uniformly distrib threshold
    float a = min( lerpFactor, 1-lerpFactor);
    float3 cases = float3( x*x/(2*a*(1-a)), (x-0.5*a)/(1-a), 1.0-((1-x)*(1-x)/(2*a*(1-a))));
    //Find our final, uniformly distributed alpha threshold
    float threshold= (x < (1-a)) ? ((x < a) ? cases.x : cases.y) : cases.z;
    //Avoids ατ== 0. Could also do ατ=1-ατ
    threshold = clamp(threshold, 1.0e-6, 1.0);
    return threshold;
}


#endif