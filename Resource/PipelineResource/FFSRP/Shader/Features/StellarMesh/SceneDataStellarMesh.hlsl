#ifndef SCENE_DATA_SETLLAR_MESH_HLSL
#define SCENE_DATA_SETLLAR_MESH_HLSL
#include "IndirectDrawCommandArguments.hlsl"

struct StellarMeshScene
{
    float4x4 ce_World;
    float3 ce_LocalBoundsCenter;
    uint mPadding0;
    float3 ce_LocalBoundsExtent;
    uint  ce_MeshID;
};

struct MeshConstant
{
    uint mClusterOffset;
    uint mClusterCount;

    uint mIndexOffset;
    uint mIndexCount;
    uint mVertexOffset;
    uint mVertexCount;

    uint mIndexBufferIndex;
    uint mPositionBufferIndex;
    uint mNormalBufferIndex;
    uint mTangetBufferIndex;
    uint mColorBufferIndex;
    uint mUV0BufferIndex;
    uint mUV1BufferIndex;

    uint3 padding;
};

struct Cluster
{
    uint mVertexOffset;
    uint mVertexCount;
    uint mTriangleOffset;
    uint mTriangleCount;
};

struct InstancedCluster
{
    uint mClusterIndex;
    uint mInstanceIndex;
};

struct QueueState
{
    uint mTotalClusters;
    // TODO
};

struct SlotCounter
{
    uint mClusterSlot; // Current visible cluster index
};

#endif
