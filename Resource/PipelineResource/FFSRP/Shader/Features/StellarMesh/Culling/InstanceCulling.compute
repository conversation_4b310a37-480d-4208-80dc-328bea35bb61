#pragma compute InstanceCulling

#include "../Util.hlsl"
#include "../SceneDataStellarMesh.hlsl"

cbuffer _cbCommon
{
    uint totalInstances;
};

StructuredBuffer<StellarMeshScene> instances;
StructuredBuffer<MeshConstant>     meshConstants;

globallycoherent RWStructuredBuffer<QueueState>       queueState;
globallycoherent RWStructuredBuffer<InstancedCluster> candidateClusterQueue;

[numthreads(64, 1, 1)]
void InstanceCulling(
	uint3 groupID       : SV_GroupID,
	uint3 groupThreadID : SV_GroupThreadID
)
{
    uint taskIndex = GetDispatchThreadIndex(groupID, groupThreadID, 64);

    if (taskIndex < totalInstances)
    {
        bool isVisible = true;
        if (isVisible)
        {
            StellarMeshScene instanceInfo = instances[taskIndex];
            if (instanceInfo.ce_MeshID == 0xFFFFFFFF) return;
            MeshConstant meshConstant = meshConstants[instanceInfo.ce_MeshID];

            uint writeIndex = 0;
            InterlockedAdd(queueState[0].mTotalClusters, meshConstant.mClusterCount, writeIndex);

            for (uint i = 0; i < meshConstant.mClusterCount; i++)
            {
                uint clusterIndex = meshConstant.mClusterOffset + i;
                candidateClusterQueue[writeIndex].mInstanceIndex = taskIndex;
                candidateClusterQueue[writeIndex].mClusterIndex  = clusterIndex;
                writeIndex++;
            }
        }
    }
}
