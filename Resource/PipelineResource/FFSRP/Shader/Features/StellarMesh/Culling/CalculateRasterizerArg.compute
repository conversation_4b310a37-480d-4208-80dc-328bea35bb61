#pragma compute CalculateRasterizerArg

#include "../SceneDataStellarMesh.hlsl"
#include "../StellarMeshDefinitions.hlsl"

StructuredBuffer<SlotCounter> slotCounter;

RWStructuredBuffer<DrawArraysIndirectCommand> clusterDrawArgs;

[numthreads(1, 1, 1)]
void CalculateRasterizerArg()
{
    DrawArraysIndirectCommand command;

    uint visibleClusterCount = slotCounter[0].mClusterSlot;
    uint clusterOffset = 0;

    command.mVertexCount   = STELLAR_MESH_MAXIMUM_TRIANGLES_IN_CLUSTER * 3;
    command.mInstanceCount = min(visibleClusterCount, STELLAR_MESH_MAX_VISIBLE_CLUSTERS);
    command.mStartVertex   = 0;
    command.mStartInstance = clusterOffset;

    clusterDrawArgs[0] = command;
}
