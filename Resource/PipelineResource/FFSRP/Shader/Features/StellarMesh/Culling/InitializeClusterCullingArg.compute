#pragma compute InitializeClusterCullingArg
#include "../SceneDataStellarMesh.hlsl"

StructuredBuffer<QueueState> queueState;
RWStructuredBuffer<DispatchIndirectCommand> clusterCullingDrawArg;

[numthreads(1, 1, 1)]
void InitializeClusterCullingArg()
{
    uint candidateClusterCount = queueState[0].mTotalClusters;
    uint candidateClusterBatchCount = (candidateClusterCount + 63) / 64;
    clusterCullingDrawArg[0].mGroupThreadX = candidateClusterBatchCount;
    clusterCullingDrawArg[0].mGroupThreadY = 1;
    clusterCullingDrawArg[0].mGroupThreadZ = 1;
}
