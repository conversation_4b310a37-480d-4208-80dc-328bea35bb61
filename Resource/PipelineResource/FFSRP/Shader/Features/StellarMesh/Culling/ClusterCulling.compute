#pragma compute ClusterCulling

#include "../Util.hlsl"
#include "../SceneDataStellarMesh.hlsl"

StructuredBuffer<Cluster>          clusters;
StructuredBuffer<QueueState>       queueState;
StructuredBuffer<InstancedCluster> candidateClusterQueue;

RWStructuredBuffer<InstancedCluster> visibleCluster;
globallycoherent RWStructuredBuffer<SlotCounter> slotCounter;

[numthreads(64, 1, 1)]
void ClusterCulling(
	uint3 groupID          : SV_GroupID,
	uint3 groupThreadID    : SV_GroupThreadID
)
{
    uint taskIndex = GetDispatchThreadIndex(groupID, groupThreadID, 64);
    uint totalClusters = queueState[0].mTotalClusters;

    if (taskIndex < totalClusters)
    {
        InstancedCluster candidateCluster = candidateClusterQueue[taskIndex];

        bool isVisible = true;
        if (isVisible)
        {
            uint clusterSlot;
            InterlockedAdd(slotCounter[0].mClusterSlot, 1, clusterSlot);
            visibleCluster[clusterSlot].mInstanceIndex = candidateCluster.mInstanceIndex;
            visibleCluster[clusterSlot].mClusterIndex  = candidateCluster.mClusterIndex;
        }
    }
}
