#pragma vertex VSMain
#pragma pixel HWRasterizePS
#pragma only_renderers vulkan
// PipelineResource/FFSRP/Shader/Features/StellarMesh/Rasterize/
#include "../../ShaderLibrary/BindlessCommon.hlsl"
#include "../SceneDataStellarMesh.hlsl"
#include "VisibilityBuffer.hlsl"

StructuredBuffer<InstancedCluster>   visibleClusters;
StructuredBuffer<StellarMeshScene>   instances;
StructuredBuffer<MeshConstant>       meshConstants;
StructuredBuffer<Cluster>            clusters;

StructuredBuffer<uint>   indexes;
StructuredBuffer<float4> positions;

cbuffer _cb : register(space0)
{
	float4x4 ce_View;
	float4x4 ce_Projection;
}

struct VSOutput
{
	float4 Pos : SV_POSITION;
	nointerpolation uint clusterIndex_triangleIndex : TEXCOORD0;
};

VSOutput VSMain(
    uint vertexID	: SV_VertexID,
	uint instanceID : SV_InstanceID
)
{
	uint visibleClusterIndex = instanceID;
	uint triangleIndex = vertexID / 3;
	uint localVertexID = vertexID - triangleIndex * 3;

	VSOutput vsOut;
	vsOut.Pos = float4(0, 0, 0, 1);

	InstancedCluster visibleCluster = visibleClusters[visibleClusterIndex];

	StellarMeshScene instanceInfo = instances[visibleCluster.mInstanceIndex];
	MeshConstant meshConstant     = meshConstants[instanceInfo.ce_MeshID];
	Cluster cluster               = clusters[visibleCluster.mClusterIndex];

	if (triangleIndex < cluster.mTriangleCount)
	{
        // uint vertexIndex = GetBindlessUInt(meshConstant.mIndexBufferIndex)[triangleIndex][localVertexID];
        // float3 localPosition = GetBindlessFloat(meshConstant.mPositionBufferIndex)[vertexIndex].xyz;
        uint vertexIndex = indexes[meshConstant.mIndexOffset + cluster.mTriangleOffset * 3 + vertexID];
        float3 localPosition = positions[meshConstant.mVertexOffset + cluster.mVertexOffset + vertexIndex].xyz;

        float4 worldPosition = mul(instanceInfo.ce_World, float4(localPosition, 1.0));

        vsOut.Pos = mul(ce_Projection, mul(ce_View, float4(worldPosition.xyz, 1.0)));
        vsOut.clusterIndex_triangleIndex = EncodeClusterTriangleIndex(visibleCluster.mClusterIndex, triangleIndex);

        // float offset = visibleCluster.mInstanceIndex == 1 ? 0.0 : 1.0;
        // float subOffset = triangleIndex * 0.02;

        // float depth = visibleCluster.mInstanceIndex == 1 ? 0.2 : 0.1; 

        // if (localVertexID == 0)
        // {
        // 	vsOut.Pos = float4(float3(float2(-1.0, -1.0) + offset + subOffset, depth), 1.0);
        // }
        // else if (localVertexID == 1)
        // {
        // 	vsOut.Pos = float4(float3(float2(-0.9, -0.9) + offset + subOffset, depth), 1.0);
        // }
        // else if(localVertexID == 2)
        // {
        // 	vsOut.Pos = float4(float3(float2(-0.9, -1.0) + offset + subOffset, depth), 1.0);
        // }
        // vsOut.clusterIndex_triangleIndex = EncodeClusterTriangleIndex(visibleCluster.mClusterIndex, meshConstant.mIndexBufferIndex);
	}

    return vsOut;
}

#if COMPILER_SUPPORTS_UINT64
RWTexture2D<uint64_t>	visibilityBuffer;

#else

struct PSOutput
{
	uint visbilityValue : SV_Target0;
    float outDepth       : SV_Depth;
};

#endif

#if COMPILER_SUPPORTS_UINT64
void HWRasterizePS(VSOutput In)
#else
PSOutput HWRasterizePS(VSOutput In)
#endif
{
	float4 position = float4(In.Pos.xyz / In.Pos.w, In.Pos.w);
	float depth = position.z;

#if COMPILER_SUPPORTS_UINT64
	uint2 pixelPos = (uint2)position.xy;
	uint depthInt = asuint(depth);
	InterlockedMax(visibilityBuffer[PixelPos], PackUInt64Type(In.clusterIndex_triangleIndex, depthInt));
#else
	PSOutput output;
	output.visbilityValue = In.clusterIndex_triangleIndex;
	output.outDepth = depth;
	return output;
#endif
}
