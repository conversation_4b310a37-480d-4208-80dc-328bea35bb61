#ifndef VISIBILITY_BUFFER_HLSL
#define VISIBILITY_BUFFER_HLSL

#if COMPILER_SUPPORTS_UINT64

uint2 UnpackUInt64Type(uint64_t value)
{
    return uint2(uint(value), value >> 32);
}

#endif

uint EncodeClusterTriangleIndex(uint clusterIndex, uint triangleIndex)
{
    return (clusterIndex << 7) | (triangleIndex & 0x7F);
}

uint2 DecodeClusterTriangleIndex(uint clusterIndex_triangleIndex)
{
    return uint2(clusterIndex_triangleIndex >> 7, clusterIndex_triangleIndex & 0x7F);
}

#endif
