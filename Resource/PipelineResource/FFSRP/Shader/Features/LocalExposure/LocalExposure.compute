#pragma compute MainCS
#pragma compute SetupLogLuminanceCS
#pragma compute GaussianBlurFilterCS
//#pragma enable debug_symbol

#define FLATTE<PERSON> [flatten]
#define UNROLL  [unroll]
#define BRANCH  [branch]
#define LOOP  	[loop]

#define BILATERAL_GRID 1
#define HISTOGRAM_SIZE 32
#define THREADGROUP_SIZEX 8
#define THREADGROUP_SIZEY 8

// #define BILATERAL_GRID 0
// #define HISTOGRAM_SIZE 64
// #define THREADGROUP_SIZEX 8
// #define THREADGROUP_SIZEY 4

#define LOOP_SIZEX 8
#define LOOP_SIZEY 8

/////////////////////////////////////////
cbuffer constant
{
	uint2 Input_ViewportMin;
	uint2 Input_ViewportMax;
	float2 Input_ViewportSizeInverse;
	float2 Output_ExtentInverse;
	float EyeAdaptation_HistogramScale;
	float EyeAdaptation_HistogramBias;
	float EyeAdaptation_LuminanceMin;
}

// Texture2D EyeAdaptation_MeterMaskTexture;
// SamplerState EyeAdaptation_MeterMaskSampler;

float CalculateEyeAdaptationLuminance(float3 Color)
{
	return max(dot(Color, float3(1.0f, 1.0f, 1.0f) / 3.0f), EyeAdaptation_LuminanceMin);
}

// inverse of ComputeLogLuminanceFromHistogramPosition
// @param LogLuminance
// @return HistogramPosition 0..1
float ComputeHistogramPositionFromLogLuminance(float LogLuminance)
{
	return LogLuminance * EyeAdaptation_HistogramScale + EyeAdaptation_HistogramBias;
}

#ifndef HISTOGRAM_SIZE
	#define HISTOGRAM_SIZE 64
#endif

#define HISTOGRAM_TEXEL_SIZE (HISTOGRAM_SIZE / 4)

Texture2D InputTexture;
Texture2D LastExposureTexture;

#define OneOverPreExposure 1.0

#if BILATERAL_GRID
// Output bilateral grid texture (UAV)
RWTexture3D<float2> BilateralGridRWTexture;
#else
// Output histogram texture (UAV)
RWTexture2D<float4> HistogramRWTexture;
#endif

RWTexture2D<float4> DebugRWTexture;

// Number of thread groups in the dispatch
// uint2 ThreadGroupCount;

// THREADGROUP_SIZEX*THREADGROUP_SIZEY histograms of the size HISTOGRAM_SIZE
groupshared float SharedHistogram[HISTOGRAM_SIZE][THREADGROUP_SIZEX][THREADGROUP_SIZEY];

#if BILATERAL_GRID
groupshared float SharedLuminanceSum[HISTOGRAM_SIZE][THREADGROUP_SIZEX][THREADGROUP_SIZEY];
#endif

[numthreads(THREADGROUP_SIZEX, THREADGROUP_SIZEY, 1)]
void MainCS(
	uint3 GroupId : SV_GroupID,
	uint3 DispatchThreadId : SV_DispatchThreadID,
    uint3 GroupThreadId : SV_GroupThreadID,
	uint GroupIndex: SV_GroupIndex)
{
	// todo: can be cleared more efficiently
	// clear all THREADGROUP_SIZEX*THREADGROUP_SIZEY histograms
	UNROLL for (uint i = 0; i < HISTOGRAM_SIZE; ++i)
	{
		SharedHistogram[i][GroupThreadId.x][GroupThreadId.y] = 0.0f;
#if BILATERAL_GRID
		SharedLuminanceSum[i][GroupThreadId.x][GroupThreadId.y] = 0.0f;
#endif
	}
	
	GroupMemoryBarrierWithGroupSync();

	// Each thread group processes LoopX * LoopY texels of the input.
	uint2 TileSize = uint2(LOOP_SIZEX, LOOP_SIZEY);

	// Top left input texel for this group.
	uint2 LeftTop = Input_ViewportMin + DispatchThreadId.xy * TileSize;

	float2 InvViewSize = Input_ViewportSizeInverse.xy;

	// Accumulate all pixels into THREADGROUP_SIZEX*THREADGROUP_SIZEY histograms
	LOOP for (uint y = 0; y < LOOP_SIZEY; ++y)
	{
		LOOP for (uint x = 0; x < LOOP_SIZEX; ++x)
		{
			uint2 Tile = uint2(x, y);
			uint2 TexelPos = LeftTop + Tile;

			if(TexelPos.x < Input_ViewportMax.x && TexelPos.y < Input_ViewportMax.y)
			{
				float4 SceneColor = InputTexture.Load(int3(TexelPos, 0));

				SceneColor.xyz *= OneOverPreExposure;
				// SceneColor.xyz *= View.OneOverPreExposure; //TD

				float LuminanceVal = CalculateEyeAdaptationLuminance(SceneColor.xyz);

				float LogLuminance = log2(LuminanceVal);
				float LogLuminanceHist = ComputeHistogramPositionFromLogLuminance(LogLuminance);

				float2 ScreenUV = (TexelPos.xy - Input_ViewportMin) * InvViewSize;

				// Map the normalized histogram position into texels.
				float fBucket = saturate(LogLuminanceHist) * (HISTOGRAM_SIZE-1);// * 0.9999f;

				// Find two discrete buckets that straddle the continuous histogram position.
				uint Bucket0 = (uint)(fBucket);
				uint Bucket1 = Bucket0 + 1;

				Bucket0 = min(Bucket0, uint(HISTOGRAM_SIZE - 1));
				Bucket1 = min(Bucket1, uint(HISTOGRAM_SIZE - 1));

				// Weighted blend between the two buckets.
				float Weight1 = frac(fBucket);
				float Weight0 = 1.0f - Weight1;

				// Accumulate the weight to the nearby history buckets.
				SharedHistogram[Bucket0][GroupThreadId.x][GroupThreadId.y] += Weight0;
				SharedHistogram[Bucket1][GroupThreadId.x][GroupThreadId.y] += Weight1;

#if BILATERAL_GRID
				SharedLuminanceSum[Bucket0][GroupThreadId.x][GroupThreadId.y] += LogLuminance * Weight0;
				SharedLuminanceSum[Bucket1][GroupThreadId.x][GroupThreadId.y] += LogLuminance * Weight1;
#endif
			}
		}
	}

	GroupMemoryBarrierWithGroupSync();

	// Accumulate all histograms into one.
	if (GroupIndex < HISTOGRAM_SIZE / 4) 
	{
		float4 Sum = 0;
#if BILATERAL_GRID
		float4 SumLuminance = 0;
#endif

		LOOP for (uint y = 0; y < THREADGROUP_SIZEY; ++y)
		{
			LOOP for (uint x = 0; x < THREADGROUP_SIZEX; ++x)
			{
				Sum += float4(
					SharedHistogram[GroupIndex * 4 + 0][x][y],
					SharedHistogram[GroupIndex * 4 + 1][x][y],
					SharedHistogram[GroupIndex * 4 + 2][x][y],
					SharedHistogram[GroupIndex * 4 + 3][x][y]);

#if BILATERAL_GRID
				SumLuminance += float4(
					SharedLuminanceSum[GroupIndex * 4 + 0][x][y],
					SharedLuminanceSum[GroupIndex * 4 + 1][x][y],
					SharedLuminanceSum[GroupIndex * 4 + 2][x][y],
					SharedLuminanceSum[GroupIndex * 4 + 3][x][y]);
#endif
			}
		}

#if BILATERAL_GRID
		BilateralGridRWTexture[uint3(GroupId.xy, GroupIndex * 4 + 0)] = float2(SumLuminance.x, Sum.x);
		BilateralGridRWTexture[uint3(GroupId.xy, GroupIndex * 4 + 1)] = float2(SumLuminance.y, Sum.y);
		BilateralGridRWTexture[uint3(GroupId.xy, GroupIndex * 4 + 2)] = float2(SumLuminance.z, Sum.z);
		BilateralGridRWTexture[uint3(GroupId.xy, GroupIndex * 4 + 3)] = float2(SumLuminance.w, Sum.w);
#else
		float2 MaxExtent = Input_ViewportSize;
		float Area = MaxExtent.x * MaxExtent.y;
	
		// Fixed to include borders.
		float NormalizeFactor = 1.0f / Area;

		// Output texture with one histogram per line, x and y unwrapped into all the lines
		HistogramRWTexture[uint2(GroupIndex, GroupId.x + GroupId.y * ThreadGroupCount.x)] = Sum * NormalizeFactor;
#endif
	}	
}

RWTexture2D<float> Output2DFRW;

[numthreads(THREADGROUP_SIZEX, THREADGROUP_SIZEY, 1)]
void SetupLogLuminanceCS(uint3 DispatchThreadId : SV_DispatchThreadID)
{
	float3 SceneColor = InputTexture.Load(uint3(Input_ViewportMin + DispatchThreadId.xy, 0)).rgb;
	// float3 SceneColor = InputTexture.Load(uint3(Input_ViewportMin + DispatchThreadId.xy, 0)).rgb * View.OneOverPreExposure;
	float LuminanceVal = CalculateEyeAdaptationLuminance(SceneColor);
	Output2DFRW[DispatchThreadId.xy] = log2(LuminanceVal);
}

///////////////////////////

bool IsComputeUVOutOfBounds(in float2 UV)
{
	float2 CenterDist = abs(UV - 0.5f);
	return (max(CenterDist.x, CenterDist.y) >= 0.5f);
}

#ifdef CROSS_NGI_D3D12 
    #define SHADER_CONST(Type, Name, Value) Type Name
#else
    #define SHADER_CONST(Type, Name, Value) [[vk::constant_id(__COUNTER__)]] const Type Name = Value
#endif

SHADER_CONST(bool, BLUR_VERTICAL, false);

SamplerState Filter_Sampler;
RWTexture2D<float4> RWOutputTexture;

#define STATIC_SAMPLE_COUNT 24
#define PACKED_STATIC_SAMPLE_COUNT ((STATIC_SAMPLE_COUNT + 1) / 2)

[numthreads(THREADGROUP_SIZEX, THREADGROUP_SIZEY, 1)]
void GaussianBlurFilterCS(uint2 DispatchThreadId : SV_DispatchThreadID)
{
	uint2 iPixelPos = DispatchThreadId;
	float2 PixelPos = float2(iPixelPos);
	float2 BaseUV = (PixelPos + 0.5) * Output_ExtentInverse;

	if (IsComputeUVOutOfBounds(BaseUV))
	{
		return;
	}

	float4 Color = 0;
	
	float4 SampleOffsets[PACKED_STATIC_SAMPLE_COUNT];
	
	if (BLUR_VERTICAL)
	{
		SampleOffsets[0] = float4(0.00, -0.50431, 0.00, -0.45933);
		SampleOffsets[1] = float4(0.00, -0.41438, 0.00, -0.36946);
		SampleOffsets[2] = float4(0.00, -0.32459, 0.00, -0.27974);
		SampleOffsets[3] = float4(0.00, -0.23493, 0.00, -0.19014);
		SampleOffsets[4] = float4(0.00, -0.14538, 0.00, -0.10063);
		SampleOffsets[5] = float4(0.00, -0.0559, 0.00, -0.01118);
		SampleOffsets[6] = float4(0.00, 0.03354, 0.00, 0.07827);
		SampleOffsets[7] = float4(0.00, 0.123, 0.00, 0.16776  );
		SampleOffsets[8] = float4(0.00, 0.21253, 0.00, 0.25733);
		SampleOffsets[9] = float4(0.00, 0.30216, 0.00, 0.34702);
		SampleOffsets[10] = float4(0.00, 0.39192, 0.00, 0.43685);
		SampleOffsets[11] = float4(0.00, 0.48181, 0.00, 0.52273);
	}
	else
	{
		SampleOffsets[0] = float4(-0.24384, 0.00, -0.22209, 0.00);
		SampleOffsets[1] = float4(-0.20036, 0.00, -0.17864, 0.00);
		SampleOffsets[2] = float4(-0.15694, 0.00, -0.13526, 0.00);
		SampleOffsets[3] = float4(-0.11359, 0.00, -0.09194, 0.00);
		SampleOffsets[4] = float4(-0.07029, 0.00, -0.04866, 0.00);
		SampleOffsets[5] = float4(-0.02703, 0.00, -0.00541, 0.00);
		SampleOffsets[6] = float4(0.01622, 0.00, 0.03784, 0.00);
		SampleOffsets[7] = float4(0.05947, 0.00, 0.08111, 0.00);
		SampleOffsets[8] = float4(0.10276, 0.00, 0.12442, 0.00);
		SampleOffsets[9] = float4(0.1461, 0.00, 0.16779, 0.00 );
		SampleOffsets[10] = float4(0.1895, 0.00, 0.21122, 0.00 );
		SampleOffsets[11] = float4(0.23296, 0.00, 0.25275, 0.00);
	}
	
	float4 SampleWeights[STATIC_SAMPLE_COUNT];

	SampleWeights[0] = float4(2.06421E-08, 2.06421E-08, 2.06421E-08, 2.06421E-08);
	SampleWeights[1] = float4(3.18469E-07, 3.18469E-07, 3.18469E-07, 3.18469E-07);
	SampleWeights[2] = float4(3.80608E-06, 3.80608E-06, 3.80608E-06, 3.80608E-06);
	SampleWeights[3] = float4(0.00004, 0.00004, 0.00004, 0.00004);
	SampleWeights[4] = float4(0.00025, 0.00025, 0.00025, 0.00025);
	SampleWeights[5] = float4(0.00141, 0.00141, 0.00141, 0.00141);
	SampleWeights[6] = float4(0.00607, 0.00607, 0.00607, 0.00607);
	SampleWeights[7] = float4(0.02028, 0.02028, 0.02028, 0.02028);
	SampleWeights[8] = float4(0.05257, 0.05257, 0.05257, 0.05257);
	SampleWeights[9] = float4(0.10571, 0.10571, 0.10571, 0.10571);
	SampleWeights[10] = float4(0.16488, 0.16488, 0.16488, 0.16488);
	SampleWeights[11] = float4(0.19947, 0.19947, 0.19947, 0.19947);
	SampleWeights[12] = float4(0.1872, 0.1872, 0.1872, 0.1872);
	SampleWeights[13] = float4(0.13628, 0.13628, 0.13628, 0.13628);
	SampleWeights[14] = float4(0.07695, 0.07695, 0.07695, 0.07695);
	SampleWeights[15] = float4(0.0337, 0.0337, 0.0337, 0.0337);
	SampleWeights[16] = float4(0.01145, 0.01145, 0.01145, 0.01145);
	SampleWeights[17] = float4(0.00302, 0.00302, 0.00302, 0.00302);
	SampleWeights[18] = float4(0.00062, 0.00062, 0.00062, 0.00062);
	SampleWeights[19] = float4(0.0001, 0.0001, 0.0001, 0.0001);
	SampleWeights[20] = float4(0.00001, 0.00001, 0.00001, 0.00001);
	SampleWeights[21] = float4(1.13666E-06, 1.13666E-06, 1.13666E-06, 1.13666E-06);
	SampleWeights[22] = float4(8.37104E-08, 8.37104E-08, 8.37104E-08, 8.37104E-08);
	SampleWeights[23] = float4(3.91568E-09, 3.91568E-09, 3.91568E-09, 3.91568E-09);

    int SampleIndex = 0;

	[unroll]  
    for (; SampleIndex < STATIC_SAMPLE_COUNT - 1; SampleIndex += 2)
    {
        float4 UVUV = BaseUV.xyxy + SampleOffsets[SampleIndex / 2];
        Color += InputTexture.SampleLevel(Filter_Sampler, UVUV.xy, 0) * SampleWeights[SampleIndex + 0];
        Color += InputTexture.SampleLevel(Filter_Sampler, UVUV.zw, 0) * SampleWeights[SampleIndex + 1];
    }
	
	[flatten] 
	if (SampleIndex < STATIC_SAMPLE_COUNT)
	{
		float2 UV = BaseUV + SampleOffsets[SampleIndex / 2].xy;
		Color += InputTexture.SampleLevel(Filter_Sampler, UV, 0) * SampleWeights[SampleIndex];
	}
	
	RWOutputTexture[PixelPos] = Color;
}
