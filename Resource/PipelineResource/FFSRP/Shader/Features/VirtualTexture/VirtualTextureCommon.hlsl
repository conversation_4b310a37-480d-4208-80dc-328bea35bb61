#ifndef VIRTUAL_TEXTURE_COMMON_HLSL
#define VIRTUAL_TEXTURE_COMMON_HLSL

#ifndef NUM_VIRTUALTEXTURE_SAMPLES
#define NUM_VIRTUALTEXTURE_SAMPLES 0
#endif

#ifndef LIGHTMAP_VT_ENABLED
#define LIGHTMAP_VT_ENABLED 0
#endif

#define VIRTUAL_TEXTURE_FEEDBACK_FACTOR 16			// r.vt.FeedbackFactor

#define VTADDRESSMODE_CLAMP 0u
#define VTADDRESSMODE_WRAP 1u
#define VTADDRESSMODE_MIRROR 2u

#define VIRTUAL_TEXTURE_ANISOTROPIC_FILTERING 1
#if VIRTUAL_TEXTURE_ANISOTROPIC_FILTERING
	#define ENABLE_STOCHASTIC_FILTERING 1
#endif

float2 BoxMullerTransform(float2 u)
{
	float2 r;
	float mag = sqrt(-2.0 * log(u.x));
	return mag * float2(cos(2.0 * M_PI * u.y), sin(2.0 * M_PI * u.y));
}

/** Struct used to store feedback information in. */
struct FVirtualTextureFeedbackParams
{
	uint Request;	// The tile to request for this pixel
	uint RequestId;	// The id of the VT texture sample this pixel will generate feedback for
};

/** Initializes the FVirtualTextureFeedbackParams for the pixel shader. */
void InitializeVirtualTextureFeedback(in out FVirtualTextureFeedbackParams Params, uint2 SvPosition)
{
#if (NUM_VIRTUALTEXTURE_SAMPLES + LIGHTMAP_VT_ENABLED) > 1
	const uint NumVTSamplesInShader = NUM_VIRTUALTEXTURE_SAMPLES + LIGHTMAP_VT_ENABLED;
	const uint2 PixelPos = SvPosition >> VTFeedbackShift;
	const uint FeedbackPos = PixelPos.y * (uint)VTFeedbackStride + PixelPos.x;
	Params.RequestId = ((uint)VTFeedbackSampleOffset + FeedbackPos) % NumVTSamplesInShader;
	Params.Request = 0xFFFFFFFF;
#else
	Params.Request = 0xFFFFFFFF;	
#endif	
}

void InitializeRandParams(in out float4 randParams, float2 svPositionXY)
{
	randParams.x = InterleavedGradientNoise(svPositionXY, ce_FrameIDMod1024);
	randParams.y = InterleavedGradientNoise(svPositionXY, ce_FrameIDMod1024 * 117.f);
	randParams.z = InterleavedGradientNoise(svPositionXY, ce_FrameIDMod1024 * 7901.f);
	randParams.w = InterleavedGradientNoise(svPositionXY + 17.f, ce_FrameIDMod1024);
}

/** Store feedback info for a VT request in the passed in FVirtualTextureFeedbackParams. */
void StoreVirtualTextureFeedback(in out FVirtualTextureFeedbackParams Params, uint RequestId, uint Request)
{
#if (NUM_VIRTUALTEXTURE_SAMPLES + LIGHTMAP_VT_ENABLED) > 1
	Params.Request = (RequestId == Params.RequestId) ? Request : Params.Request;
#else
	Params.Request = Request;
#endif
}

/** This should be called at the end of the pixel shader to write out the gathered VT feedback info to the OutputBuffer. */
void FinalizeVirtualTextureFeedback(FVirtualTextureFeedbackParams Params, float3 SvPosition, float Opacity, uint FrameNumber,out RWBuffer<uint> OutputBuffer)
{
	uint2 PixelTilePos = (uint2)SvPosition.xy % VIRTUAL_TEXTURE_FEEDBACK_FACTOR;
	uint PixelTileIndex = PixelTilePos.y * VIRTUAL_TEXTURE_FEEDBACK_FACTOR + PixelTilePos.x;

	[branch] if (PixelTileIndex == (uint)VTFeedbackJitterOffset)		
	{
		uint2 PixelPos = (uint2)SvPosition.xy / VIRTUAL_TEXTURE_FEEDBACK_FACTOR;
		
		uint FeedbackPos = PixelPos.y * (uint)VTFeedbackStride + PixelPos.x;
		
		OutputBuffer[FeedbackPos] = Params.Request;
	}
}

/** Unpacked contents of per page table uniforms. */
struct VTPageTableUniform
{
	uint XOffsetInPages; // 12
	uint YOffsetInPages; // 12
	uint MaxLevel; // 4
	uint vPageTableMipBias; // 8
	uint ShiftedPageTableID; // 4
	uint AdaptiveLevelBias; // 4

	float2 SizeInPages;
	float2 UVScale;
	float MaxAnisoLog2;
};

/** Unpack the per page table uniforms. */
VTPageTableUniform VTPageTableUniform_Unpack(uint4 PackedPageTableUniform0, uint4 PackedPageTableUniform1)
{
	VTPageTableUniform result;
	result.UVScale = asfloat(PackedPageTableUniform0.xy);
	result.SizeInPages = asfloat(PackedPageTableUniform0.zw);
	result.MaxAnisoLog2 = asfloat(PackedPageTableUniform1.x);
	result.XOffsetInPages = PackedPageTableUniform1.y & 0xfff;
	result.YOffsetInPages = (PackedPageTableUniform1.y >> 12) & 0xfff;
	result.vPageTableMipBias = (PackedPageTableUniform1.y >> 24) & 0xff;
	result.MaxLevel = PackedPageTableUniform1.z & 0xf;
	result.AdaptiveLevelBias = (PackedPageTableUniform1.z >> 4) & 0xf;
	result.ShiftedPageTableID = PackedPageTableUniform1.w;
	return result;
}

struct VTPageTableResult
{
	float2 UV;
	float2 dUVdx;
	float2 dUVdy;
	uint4 PageTableValue[2];
	uint PackedRequest;
	uint UsedMip;
	uint DesiredMip;
	uint MaxMip;
	float4 DebugField0;
	float4 DebugField1;
};

/** Unpacked contents of per physical sample uniform. */
struct VTUniform
{
	// Page sizes are scaled by RcpPhysicalTextureSize
	float pPageSize;
	float vPageSize;
	float vPageBorderSize;
	bool bPageTableExtraBits;
	float4 FallbackValue;
};


/** Applies proper scaling to dUVdx/dUVdy in PageTableResult. */
float2 VTComputePhysicalUVs(in out VTPageTableResult PageTableResult, uint LayerIndex, VTUniform Uniform)
{
	const uint PackedPageTableValue = PageTableResult.PageTableValue[LayerIndex / 4u][LayerIndex & 3u];

	// See packing in PageTableUpdate.usf
	const uint vLevel = PackedPageTableValue & 0xf;
	const float UVScale = float(4096u >> vLevel) * (1.0f / 4096.0f);

	// This will compile to runtime branch, but should in theory be conditional moves selecting 1 of 2 different bitfield extracting instructions
	const uint pPageX = Uniform.bPageTableExtraBits ? (PackedPageTableValue >> 4) & 0xff : (PackedPageTableValue >> 4) & 0x3f;
	const uint pPageY = Uniform.bPageTableExtraBits ? (PackedPageTableValue >> 12) & 0xff : (PackedPageTableValue >> 10) & 0x3f;

	const float2 vPageFrac = frac(PageTableResult.UV * UVScale);
	const float2 pUV = float2(pPageX, pPageY) * Uniform.pPageSize + (vPageFrac * Uniform.vPageSize + Uniform.vPageBorderSize);

	const float ddxyScale = UVScale * Uniform.vPageSize;
	PageTableResult.dUVdx *= ddxyScale;
	PageTableResult.dUVdy *= ddxyScale;
	PageTableResult.UsedMip = vLevel;
	return pUV;
}

/** We use 0 to mark an unmapped page table entry. */
bool IsValid(VTPageTableResult PageTableResult, uint LayerIndex)
{
	const uint PackedPageTableValue = PageTableResult.PageTableValue[LayerIndex / 4u][LayerIndex & 3u];
	return (PackedPageTableValue >> 4) != 0;
}

float4 TextureVirtualSample(
	Texture2D Physical, SamplerState PhysicalSampler, in out
	VTPageTableResult PageTableResult, uint LayerIndex,
	VTUniform Uniform, const float MaxAnisoLog2
#if ENABLE_STOCHASTIC_FILTERING
	, float4 randParams = float4(0, 0, 0, 0)
#endif
	)
{
	if (IsValid(PageTableResult, LayerIndex))
	{
		float2 pUV = VTComputePhysicalUVs(PageTableResult, LayerIndex, Uniform);
		// No need to apply dUVdx/dUVdy, don't support anisotropic when sampling a specific level
		float4 color = 0.0f.xxxx;
#if VIRTUAL_TEXTURE_ANISOTROPIC_FILTERING 
        float2 dUV = select(abs(PageTableResult.dUVdx) > abs(PageTableResult.dUVdy), PageTableResult.dUVdx, PageTableResult.dUVdy);
		int sampleNum = 1 << (PageTableResult.MaxMip - PageTableResult.UsedMip);
		sampleNum = clamp(sampleNum, 1, 1 << (int)MaxAnisoLog2);
#if !ENABLE_STOCHASTIC_FILTERING
		for (int i = 0; i < sampleNum; i++)
		{
			color += Physical.Sample(PhysicalSampler, pUV + dUV * ((float)(i + 1) / (sampleNum + 1) - 0.5f));
		}
		
		color /= sampleNum;

		// float2 distribution = BoxMullerTransform(seed);
		// color = Physical.Sample(PhysicalSampler, pUV + dUV * distribution.x * 0.5);
#else
		// According to "Stochastic Texture Filtering"
		// https://arxiv.org/abs/2305.05810
		// Uniform jittering in screen-space within pixel bounds produces trapezoid, non-uniform coverage in the UV texture space 
		// Filter importance sampling then additionally jitters the resulting UVs in texture space for a Gaussian distribution
		float2 seed = randParams.yx; //float2(InterleavedGradientNoise(screenUV, ce_FrameIDMod1024 * 117.f), InterleavedGradientNoise(screenUV, ce_FrameIDMod1024));
		// float2 distribution = BoxMullerTransform(seed);
		float2 jitter = randParams.wz - 0.5.xx;

		// float2 radius = clamp(distribution, -6, 6);
		// radius *= length(dUV) * 0.01;
		// color = Physical.Sample(PhysicalSampler, pUV + PageTableResult.dUVdx * jitter.x + PageTableResult.dUVdy * jitter.y + radius);
		
		color = Physical.Sample(PhysicalSampler, pUV + PageTableResult.dUVdx * jitter.x + PageTableResult.dUVdy * jitter.y);
#endif // !ENABLE_STOCHASTIC_FILTERING

#else
	color = Physical.Sample(PhysicalSampler, pUV);
#endif // VIRTUAL_TEXTURE_ANISOTROPIC_FILTERING

		return color;
	}
	return Uniform.FallbackValue;
}

float4 TextureVirtualSample_VS(
	Texture2D Physical, SamplerState PhysicalSampler, int LOD, in out
	VTPageTableResult PageTableResult, uint LayerIndex,
	VTUniform Uniform)
{
	if (IsValid(PageTableResult, LayerIndex))
	{
		float2 pUV = VTComputePhysicalUVs(PageTableResult, LayerIndex, Uniform);
		// No need to apply dUVdx/dUVdy, don't support anisotropic when sampling a specific level
		float4 color = Physical.SampleLevel(PhysicalSampler, pUV, LOD);
		return color;
	}
	return Uniform.FallbackValue;
}

float ApplyAddressModeMirror(float v)
{
	float t = frac(v * 0.5f) * 2.0f;
	return 1.0f - abs(t - 1.0f);
}


float ApplyAddressMode(float v, uint AddressMode)
{
	// For CLAMP address mode, can't clamp to 1.0f, otherwise 'uint(UV * SizeInPages)' will overflow page table bounds by 1
	// Instead, clamp to slightly below 1, this ensures that when rounded down to uint, above value will be at most 'SizeInPages - 1'
	// The actual texel we clamp to doesn't matter too much for sampling physical texture, since we have borders around the physical pages
	// Just need to make sure we don't clamp too far and chop off valid texels at the edge of texture
	const float MaxTextureSize = 65536.0f;

	if(AddressMode == VTADDRESSMODE_WRAP) return frac(v);
	else if(AddressMode == VTADDRESSMODE_MIRROR) return ApplyAddressModeMirror(v);
	else return clamp(v, 0.0f, 1.0f - (1.0f / MaxTextureSize));
}

float2 ApplyAddressMode(float2 UV, uint AddressU, uint AddressV)
{
	return float2(ApplyAddressMode(UV.x, AddressU), ApplyAddressMode(UV.y, AddressV));
}

// Only used in forced bilinear filtering
/** Non aniso mip level calculation. */
float MipLevel2D( float2 dUVdx, float2 dUVdy )
{
	const float px = dot( dUVdx, dUVdx );
	const float py = dot( dUVdy, dUVdy );
	return 0.5f * log2( max( px, py ) );
}

/** Aniso mip level calculation. */
float MipLevelAniso2D( float2 dUVdx, float2 dUVdy, out float OutMaxLevel, const float MaxAnisoLog2 )
{
	const float px = dot( dUVdx, dUVdx );
	const float py = dot( dUVdy, dUVdy );

	const float MinLevel = 0.5f * log2( min( px, py ) );
	const float MaxLevel = 0.5f * log2( max( px, py ) );
	OutMaxLevel = MaxLevel;
	const float AnisoBias = min( MaxLevel - MinLevel, MaxAnisoLog2 );
	const float Level = MaxLevel - AnisoBias;
	
	return Level;
}

/** Calculate mip level including stochastic noise. Also stores derivatives to OutResult for use when sampling physical texture. */
int TextureComputeVirtualMipLevel(
	in out VTPageTableResult OutResult,
	float2 dUVdx, float2 dUVdy, float MipBias,
	float2 SvPositionXY,
	VTPageTableUniform PageTableUniform)
{
	OutResult.dUVdx = dUVdx * PageTableUniform.SizeInPages;
	OutResult.dUVdy = dUVdy * PageTableUniform.SizeInPages;
	
	float MaxLevel = 0;
	// Always compute mip level using MipLevelAniso2D, even if VIRTUAL_TEXTURE_ANISOTROPIC_FILTERING is disabled
	// This way the VT mip level selection will come much closer to HW mip selection, even if we're not sampling the texture using anisotropic filtering
	const float ComputedLevel = MipLevelAniso2D(OutResult.dUVdx, OutResult.dUVdy, MaxLevel, PageTableUniform.MaxAnisoLog2);

	const float GlobalMipBias = 0.0f;
#if VIRTUAL_TEXTURE_ANISOTROPIC_FILTERING 
	float Noise = InterleavedGradientNoise(SvPositionXY, ce_FrameIDMod8);
#else
	float Noise = 0.5f;
#endif
	const float MipLevel = ComputedLevel + MipBias + GlobalMipBias + Noise;
	const float MipLevelFloor = floor(MipLevel);

	OutResult.MaxMip = max(0, floor(MaxLevel + MipBias + GlobalMipBias + Noise) + int(PageTableUniform.vPageTableMipBias));
	return (int)MipLevelFloor + int(PageTableUniform.vPageTableMipBias);
}

/** Load from page table and store results. Single page table texture version. */
void TextureLoadVirtualPageTableInternal(
	in out VTPageTableResult OutResult,
	Texture2D<uint4> PageTable0,
	VTPageTableUniform PageTableUniform,
	float2 UV, int vLevel, int vtLayer)
{
	OutResult.UV = UV * PageTableUniform.SizeInPages;

	const uint vLevelClamped = clamp(vLevel, 0, int(PageTableUniform.MaxLevel));
	uint vPageX = (uint(OutResult.UV.x) + PageTableUniform.XOffsetInPages) >> vLevelClamped;
	uint vPageY = (uint(OutResult.UV.y) + PageTableUniform.YOffsetInPages) >> vLevelClamped;

	uint4 PageTable0Value = PageTable0.Load(int3(vPageX, vPageY, vLevelClamped));	
	uint vPageXMip = vPageX;
	uint vPageYMip = vPageY;
	uint vLevelClampedMip = vLevelClamped;
	while(PageTable0Value[vtLayer & 3u] == 0)	// float to int bit value
	//while((PageTable0Value[0] >> 4) == 0)	// float to int bit value
	{
		if(vLevelClampedMip > int(PageTableUniform.MaxLevel))
			break;
		vPageXMip = vPageXMip / 2;
		vPageYMip = vPageYMip / 2;
		vLevelClampedMip = vLevelClampedMip + 1;

		PageTable0Value = PageTable0.Load(int3(vPageXMip, vPageYMip, vLevelClampedMip));

	}
	OutResult.PageTableValue[0] = PageTable0Value;
	OutResult.PageTableValue[1] = uint4(0u, 0u, 0u, 0u);

	// PageTableID packed in upper 4 bits of 'PackedPageTableUniform', which is the bit position we want it in for PackedRequest as well, just need to mask off extra bits
	OutResult.PackedRequest = PageTableUniform.ShiftedPageTableID;
	OutResult.PackedRequest |= vPageX;
	OutResult.PackedRequest |= vPageY << 12;

	// Feedback always encodes vLevel+1, and subtracts 1 on the CPU side.
	// This allows the CPU code to know when we requested a negative vLevel which indicates that we don't have sufficient virtual texture resolution.
	const uint vLevelPlusOneClamped = clamp(vLevel + 1, 0, int(PageTableUniform.MaxLevel + 1));
	OutResult.PackedRequest |= vLevelPlusOneClamped << 24;
	OutResult.UsedMip = vLevelClampedMip;
	OutResult.DesiredMip = vLevel;
}

// LoadPageTable: 1 Page table, No feedback
VTPageTableResult TextureLoadVirtualPageTable(
	Texture2D<uint4> PageTable0,
	VTPageTableUniform PageTableUniform,
	float2 UV, uint AddressU, uint AddressV, 
	float MipBias, float2 SvPositionXY, int vtLayer, uint SampleIndex,
	in out FVirtualTextureFeedbackParams Feedback)
{
	VTPageTableResult Result = (VTPageTableResult)0;
	UV = UV * PageTableUniform.UVScale;
	int vLevel = 0;
	vLevel = TextureComputeVirtualMipLevel(Result, ddx(UV), ddy(UV), MipBias, SvPositionXY, PageTableUniform);
	UV = ApplyAddressMode(UV, AddressU, AddressV);
	TextureLoadVirtualPageTableInternal(Result, PageTable0, PageTableUniform, UV, vLevel, vtLayer);
	StoreVirtualTextureFeedback(Feedback, SampleIndex, Result.PackedRequest);
	Result.DesiredMip = vLevel;
	return Result;
}

VTPageTableResult TextureLoadVirtualPageTable_VS(
	Texture2D<uint4> PageTable0,
	VTPageTableUniform PageTableUniform,
	float2 UV, uint AddressU, uint AddressV, 
	int LOD, int vtLayer, uint SampleIndex,
	in out FVirtualTextureFeedbackParams Feedback)
{
	VTPageTableResult Result = (VTPageTableResult)0;
	UV = UV * PageTableUniform.UVScale;
	int vLevel = LOD;
	UV = ApplyAddressMode(UV, AddressU, AddressV);
	TextureLoadVirtualPageTableInternal(Result, PageTable0, PageTableUniform, UV, vLevel, vtLayer);
	StoreVirtualTextureFeedback(Feedback, SampleIndex, Result.PackedRequest);
	return Result;
}

/** Unpack the physical sample uniform. */
VTUniform VTUniform_Unpack(uint4 PackedUniform)
{
	VTUniform result;
	result.pPageSize = abs(asfloat(PackedUniform.w));
	result.vPageSize = asfloat(PackedUniform.y);
	result.vPageBorderSize = asfloat(PackedUniform.z);
	result.bPageTableExtraBits = asfloat(PackedUniform.w) > 0;
	result.FallbackValue.b = float((PackedUniform.x >> 0) & 0xFF) * (1.0f / 255.0f);
	result.FallbackValue.g = float((PackedUniform.x >> 8) & 0xFF) * (1.0f / 255.0f);
	result.FallbackValue.r = float((PackedUniform.x >> 16) & 0xFF) * (1.0f / 255.0f);
	result.FallbackValue.a = float((PackedUniform.x >> 24) & 0xFF) * (1.0f / 255.0f);
	//result.FallbackValue = float4(1,0,0,1);		// cpu get last mip block data resize to 1x1 as fallback value
	return result;
}



float4 CustomizedSampleVT(float2 uv, float2 screen_pos, Texture2D PhysicalTexture, in uint layerIndex, SamplerState PhysicalSampler, uint SampleIndex, in out FVirtualTextureFeedbackParams feedback, uint addressU = VTADDRESSMODE_WRAP, uint addressV = VTADDRESSMODE_WRAP
#if ENABLE_STOCHASTIC_FILTERING
	, float4 randParams = float4(0, 0, 0, 0)
#endif
	)
{
	uint RemappedLayerIndex = _VTLayerRemap[layerIndex&0xf];
	VTPageTableUniform PageTableUniform = VTPageTableUniform_Unpack(_VTPageTableUniform0, _VTPageTableUniform1);
	VTPageTableResult PageTableResult = TextureLoadVirtualPageTable(_VTPageTable, 
		PageTableUniform, 
		uv, addressU, addressV, 0.0f, screen_pos, RemappedLayerIndex, SampleIndex, feedback);
    float4 color = TextureVirtualSample(PhysicalTexture, PhysicalSampler, PageTableResult, RemappedLayerIndex, VTUniform_Unpack(_VTPackedUniform), PageTableUniform.MaxAnisoLog2
#if ENABLE_STOCHASTIC_FILTERING
					, randParams
#endif
					);
	return color;
}

float4 CustomizedSampleVT_VS(float2 uv, float2 screen_pos, Texture2D PhysicalTexture, in uint layerIndex, SamplerState PhysicalSampler, int LOD, uint SampleIndex, in out FVirtualTextureFeedbackParams feedback, uint addressU = VTADDRESSMODE_WRAP, uint addressV = VTADDRESSMODE_WRAP
#if ENABLE_STOCHASTIC_FILTERING
	, float4 randParams = float4(0, 0, 0, 0)
#endif
	)
{
	uint RemappedLayerIndex = _VTLayerRemap[layerIndex&0xf];
	VTPageTableResult PageTableResult = TextureLoadVirtualPageTable_VS(_VTPageTable, 
			VTPageTableUniform_Unpack(_VTPageTableUniform0, _VTPageTableUniform1), 
		uv, addressU, addressV, LOD, RemappedLayerIndex, SampleIndex, feedback);
    float4 color =  TextureVirtualSample_VS(PhysicalTexture, PhysicalSampler, LOD, PageTableResult, RemappedLayerIndex, VTUniform_Unpack(_VTPackedUniform));
	return color;
}

float4 DEBUGCustomizedSampleVT(float2 uv, float2 screen_pos, Texture2D PhysicalTexture, in uint layerIndex, SamplerState PhysicalSampler, uint SampleIndex, in out FVirtualTextureFeedbackParams feedback, in out VTPageTableResult DebugPageTableResult, uint addressU = VTADDRESSMODE_WRAP, uint addressV = VTADDRESSMODE_WRAP
#if ENABLE_STOCHASTIC_FILTERING
	, float4 randParams = float4(0, 0, 0, 0)
#endif
	)
{
	uint RemappedLayerIndex = _VTLayerRemap[layerIndex&0xf];
	VTPageTableUniform PageTableUniform = VTPageTableUniform_Unpack(_VTPageTableUniform0, _VTPageTableUniform1);
	DebugPageTableResult = TextureLoadVirtualPageTable(_VTPageTable, 
		PageTableUniform, 
		uv, addressU, addressV, 0.0f, screen_pos, RemappedLayerIndex, SampleIndex, feedback);
    float4 color =  TextureVirtualSample(PhysicalTexture, PhysicalSampler, DebugPageTableResult, RemappedLayerIndex, VTUniform_Unpack(_VTPackedUniform), PageTableUniform.MaxAnisoLog2
#if ENABLE_STOCHASTIC_FILTERING
					, randParams
#endif
					);
	return color;
}


#define VT(tetxureName, index) tetxureName##_VT_##index 

#define DECLARVT(Type, tetxureName, index)  Type VT(tetxureName, index) :register(space1);



#define VT_PARA(tetxureName, index)  VT(tetxureName, index) , index



#define SampleVT(input, Texture, Sampler, FeedBack) \
CustomizedSampleVT(input.uv, input.positionNDC.xy, Texture, Sampler, FeedBack)

#define SampleVT_VS(input, Texture, Sampler, LOD, FeedBack) \
CustomizedSampleVT_VS(input.uv, input.positionNDC.xy, Texture, Sampler, LOD, FeedBack)

#endif


#define SampleVTOld(input, Texture, layerIndex, Sampler, FeedBack) \
CustomizedSampleVT(input.uv, input.positionNDC.xy, Texture, layerIndex, Sampler, FeedBack)

