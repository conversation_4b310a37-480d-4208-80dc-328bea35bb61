#ifndef PARTICLE_SYSTEM_UTILS
#define PARTICLE_SYSTEM_UTILS

#include "/ShaderLibrary/Common.hlsl"

#define THREADGROUP_SIZE THREADS_PER_GROUP
#define USE_WAVE_INTRINSICS 0
#define USE_GROUP_SHARED ((THREADGROUP_SIZE == 64 || THREADGROUP_SIZE == 32) && !USE_WAVE_INTRINSICS)
#define THREADS_PER_GROUP 64
#define MAX_THREAD_GROUPS_PER_DIMENSION 65535
#define CURVE_LUT_WIDTH 128
#define CURVE_LUT_WIDTH_MINUS_ONE 127

#define PARTICLE_UPDATE_SIZE_SCALE           0
#define PARTICLE_UPDATE_COLOR_SCALE          1
#define PARTICLE_UPDATE_SUBUV                2
#define PARTICLE_UPDATE_SPRITE_ROTATION_RATE 3
#define PARTICLE_UPDATE_VELOCITY             4
#define PARTICLE_UPDATE_VECTOR_NOISE         5
#define PARTICLE_UPDATE_GRAVITY              6
#define PARTICLE_UPDATE_FORCE                7
#define PARTICLE_UPDATE_VORTEX_FORCE         8
#define PARTICLE_UPDATE_POINT_ATTRACTION     9
#define PARTICLE_UPDATE_SOLVE_FORCE_VELOCITY 10

static const uint sForceSpaceLocal = 0;
static const uint sForceSpaceWorld = 1;

float GetScaleByInstance(float3 p1, float3 tile1, float3 p2, float3 tile2, float4 remapBound)
{
    float3 newPos = p2 + (tile2 - tile1) * LENGTH_PER_TILE;
    float disatnce = distance(p1, newPos);
    float inputOffset = remapBound.y - remapBound.x;
    float ratio = 1.0;
    [branch]
    if (inputOffset > 0.0f)
    {
        ratio = (disatnce - remapBound.x) / inputOffset;
    }
    float scale = ratio * (remapBound.w - remapBound.z) + remapBound.z;
    return clamp(scale, remapBound.z, remapBound.w);
}

float3 ApplyForceTransform(bool useLocalSpace, uint forceSpace, float3 force, matrix inverseMatrix)
{
    [branch] if (useLocalSpace && forceSpace == sForceSpaceWorld)
    {
        force = mul(inverseMatrix, float4(force, 0.0)).xyz;
    }
    return force;
}

static const uint sHash[512] =
{
    151, 160, 137, 91,  90,  15,  131, 13,  201, 95,  96,  53,  194, 233, 7,   225, 140, 36,  103, 30,  69,  142, 8,   99,  37,  240, 21,  10,  23,  190, 6,   148, 247, 120, 234, 75,  0,   26,  197, 62,  94,  252, 219,
    203, 117, 35,  11,  32,  57,  177, 33,  88,  237, 149, 56,  87,  174, 20,  125, 136, 171, 168, 68,  175, 74,  165, 71,  134, 139, 48,  27,  166, 77,  146, 158, 231, 83,  111, 229, 122, 60,  211, 133, 230, 220, 105,
    92,  41,  55,  46,  245, 40,  244, 102, 143, 54,  65,  25,  63,  161, 1,   216, 80,  73,  209, 76,  132, 187, 208, 89,  18,  169, 200, 196, 135, 130, 116, 188, 159, 86,  164, 100, 109, 198, 173, 186, 3,   64,  52,
    217, 226, 250, 124, 123, 5,   202, 38,  147, 118, 126, 255, 82,  85,  212, 207, 206, 59,  227, 47,  16,  58,  17,  182, 189, 28,  42,  223, 183, 170, 213, 119, 248, 152, 2,   44,  154, 163, 70,  221, 153, 101, 155,
    167, 43,  172, 9,   129, 22,  39,  253, 19,  98,  108, 110, 79,  113, 224, 232, 178, 185, 112, 104, 218, 246, 97,  228, 251, 34,  242, 193, 238, 210, 144, 12,  191, 179, 162, 241, 81,  51,  145, 235, 249, 14,  239,
    107, 49,  192, 214, 31,  181, 199, 106, 157, 184, 84,  204, 176, 115, 121, 50,  45,  127, 4,   150, 254, 138, 236, 205, 93,  222, 114, 67,  29,  24,  72,  243, 141, 128, 195, 78,  66,  215, 61,  156, 180,

    151, 160, 137, 91,  90,  15,  131, 13,  201, 95,  96,  53,  194, 233, 7,   225, 140, 36,  103, 30,  69,  142, 8,   99,  37,  240, 21,  10,  23,  190, 6,   148, 247, 120, 234, 75,  0,   26,  197, 62,  94,  252, 219,
    203, 117, 35,  11,  32,  57,  177, 33,  88,  237, 149, 56,  87,  174, 20,  125, 136, 171, 168, 68,  175, 74,  165, 71,  134, 139, 48,  27,  166, 77,  146, 158, 231, 83,  111, 229, 122, 60,  211, 133, 230, 220, 105,
    92,  41,  55,  46,  245, 40,  244, 102, 143, 54,  65,  25,  63,  161, 1,   216, 80,  73,  209, 76,  132, 187, 208, 89,  18,  169, 200, 196, 135, 130, 116, 188, 159, 86,  164, 100, 109, 198, 173, 186, 3,   64,  52,
    217, 226, 250, 124, 123, 5,   202, 38,  147, 118, 126, 255, 82,  85,  212, 207, 206, 59,  227, 47,  16,  58,  17,  182, 189, 28,  42,  223, 183, 170, 213, 119, 248, 152, 2,   44,  154, 163, 70,  221, 153, 101, 155,
    167, 43,  172, 9,   129, 22,  39,  253, 19,  98,  108, 110, 79,  113, 224, 232, 178, 185, 112, 104, 218, 246, 97,  228, 251, 34,  242, 193, 238, 210, 144, 12,  191, 179, 162, 241, 81,  51,  145, 235, 249, 14,  239,
    107, 49,  192, 214, 31,  181, 199, 106, 157, 184, 84,  204, 176, 115, 121, 50,  45,  127, 4,   150, 254, 138, 236, 205, 93,  222, 114, 67,  29,  24,  72,  243, 141, 128, 195, 78,  66,  215, 61,  156, 180
};

static const uint sHashMask = 255;
static const uint sGradientsMask3D = 15;

static const float3 sGradients3D[16] =
{
    float3(1.0f, 1.0f, 0.0f),
    float3(-1.0f, 1.0f, 0.0f),
    float3(1.0f, -1.0f, 0.0f),
    float3(-1.0f, -1.0f, 0.0f),
    float3(1.0f, 0.0f, 1.0f),
    float3(-1.0f, 0.0f, 1.0f),
    float3(1.0f, 0.0f, -1.0f),
    float3(-1.0f, 0.0f, -1.0f),
    float3(0.0f, 1.0f, 1.0f),
    float3(0.0f, -1.0f, 1.0f),
    float3(0.0f, 1.0f, -1.0f),
    float3(0.0f, -1.0f, -1.0f),

    float3(1.0f, 1.0f, 0.0f),
    float3(-1.0f, 1.0f, 0.0f),
    float3(0.0f, -1.0f, 1.0f),
    float3(0.0f, -1.0f, -1.0f)
};

float SmoothDerivative(float t)
{
    return 30.0f * t * t * (t * (t - 2.0f) + 1.0f);
}

float Smooth(float t)
{
    return t * t * t * (t * (t * 6.0f - 15.0f) + 10.0f);
}

float3 LoadGradients3D(uint index)
{
    return sGradients3D[sHash[index] & sGradientsMask3D];
}

float Dot(const float3 g, float x, float y, float z)
{
    return g.x * x + g.y * y + g.z * z;
}

float2 Perlin3D(const float3 pt, const float frequency)
{
    float3 point3 = pt * frequency;
    float3 f0 = {floor(point3.x), floor(point3.y), floor(point3.z)};
    float3 t0 = point3 - f0;
    float3 t1 = t0 - float3(1.0f, 1.0f, 1.0f);
    uint ix0 = uint(f0.x) & sHashMask;
    uint iy0 = uint(f0.y) & sHashMask;
    uint iz0 = uint(f0.z) & sHashMask;
    uint ix1 = ix0 + 1;
    uint iy1 = iy0 + 1;
    uint iz1 = iz0 + 1;

    uint h0 = sHash[ix0];
    uint h1 = sHash[ix1];
    uint h00 = sHash[h0 + iy0];
    uint h10 = sHash[h1 + iy0];
    uint h01 = sHash[h0 + iy1];
    uint h11 = sHash[h1 + iy1];
    float3 g000 = LoadGradients3D(h00 + iz0);
    float3 g100 = LoadGradients3D(h10 + iz0);
    float3 g010 = LoadGradients3D(h01 + iz0);
    float3 g110 = LoadGradients3D(h11 + iz0);
    float3 g001 = LoadGradients3D(h00 + iz1);
    float3 g101 = LoadGradients3D(h10 + iz1);
    float3 g011 = LoadGradients3D(h01 + iz1);
    float3 g111 = LoadGradients3D(h11 + iz1);

    float v000 = Dot(g000, t0.x, t0.y, t0.z);
    float v100 = Dot(g100, t1.x, t0.y, t0.z);
    float v010 = Dot(g010, t0.x, t1.y, t0.z);
    float v110 = Dot(g110, t1.x, t1.y, t0.z);
    float v001 = Dot(g001, t0.x, t0.y, t1.z);
    float v101 = Dot(g101, t1.x, t0.y, t1.z);
    float v011 = Dot(g011, t0.x, t1.y, t1.z);
    float v111 = Dot(g111, t1.x, t1.y, t1.z);

    float dtx = SmoothDerivative(t0.x);
    float dty = SmoothDerivative(t0.y);
    float tx = Smooth(t0.x);
    float ty = Smooth(t0.y);
    float tz = Smooth(t0.z);

    float b = v100 - v000;
    float c = v010 - v000;
    float e = v110 - v010 - v100 + v000;
    float f = v101 - v001 - v100 + v000;
    float g = v011 - v001 - v010 + v000;
    float h = v111 - v011 - v101 + v001 - v110 + v010 + v100 - v000;

    float2 g100xy = {g100.x, g100.y};
    float2 g010xy = {g010.x, g010.y};
    float2 g001xy = {g001.x, g001.y};
    float2 g110xy = {g110.x, g110.y};
    float2 g101xy = {g101.x, g101.y};
    float2 g011xy = {g011.x, g011.y};
    float2 g111xy = {g111.x, g111.y};

    float2 da = float2(g000.x, g000.y);
    float2 db = g100xy - da;
    float2 dc = g010xy - da;
    float2 dd = g001xy - da;
    float2 de = g110xy - g010xy - g100xy + da;
    float2 df = g101xy - g001xy - g100xy + da;
    float2 dg = g011xy - g001xy - g010xy + da;
    float2 dh = g111xy - g011xy - g101xy + g001xy - g110xy + g010xy + g100xy - da;

    float2 sample;
    sample = da + db * tx + (dc + de * tx) * ty + (dd + df * tx + (dg + dh * tx) * ty) * tz;
    sample.x += (b + e * ty + (f + h * ty) * tz) * dtx;
    sample.y += (c + e * tx + (g + h * tx) * tz) * dty;
    sample = sample * frequency;

    return sample;
}

float3 SafeNormalize(float3 vec, float3 fallbackVec, float threshold, out float len)
{
    len = length(vec);
    [flatten] if (len < threshold)
    {
        len = length(fallbackVec);
        return fallbackVec;
    }
    return vec / len;
}

#endif