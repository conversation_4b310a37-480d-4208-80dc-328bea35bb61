#include "../../ShaderLibrary/GlobalModelVariables.hlsl"

Texture2D<float4> sceneTex: register(space0);
Texture2D<float4> fogTex: register(space0);

struct VS2PS
{
	float2 UV : 		TEXCOORD0;
	float4 Pos : 		SV_POSITION;
};

void DrawRectangle(
	float4 InPosition,
	float2 InTexCoord,
	out float4 OutPosition,
	out float2 OutTexCoord)
{
	OutPosition = InPosition;
	OutPosition.xy = -1.0f + 2.0f * InPosition.xy;
	OutPosition.xy *= float2(1, -1);
	OutTexCoord.xy = InTexCoord.xy;
}

VS2PS VSMain(float4 Pos : POSITION, float2 uv : TEXCOORD0)
{
	VS2PS ret;
	float4 Outpos;
	float2 Outuv;
	DrawRectangle(
		Pos,
		uv,
		Outpos,
		Outuv);
	ret.Pos = Outpos;
	ret.UV = Outuv;
	return ret;
}

float4 PSMain(VS2PS input) : SV_TARGET
{
    float4 sceneColor = sceneTex.Sample(ce_Sampler_Clamp, input.UV);
    float4 fogColor = fogTex.Sample(ce_Sampler_Clamp, input.UV);
	
	//float3 ret = fogColor.xyz + sceneColor.xyz * fogColor.w;
	
    float3 ret = lerp(fogColor.xyz, sceneColor.xyz, fogColor.w);
	return float4(ret, 1.f);
}