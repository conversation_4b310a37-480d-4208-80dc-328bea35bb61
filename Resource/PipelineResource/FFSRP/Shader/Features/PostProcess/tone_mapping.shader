#pragma vertex VSMain
#pragma pixel PSMain

#include "ACES.h"
#include "FastNoiseLite.hlsl"

Texture2D<float4> src_texture : register(space0);
Texture2D<float4> pre_src_texture : register(space0);
Texture2D<float4> lut_texture: register(space0);
Texture2D<float4> exposure_texture: register(space0);
Texture3D<float3> ex_3d_lut_texture: register(space0);
Texture3D<float4> noise_texture : register(space0);
Texture2D<float> spreadTex : register(space0);
Texture2D<float> concentrationTex: register(space0);
SamplerState ce_Sampler_Clamp : register(space0);

#ifdef CROSS_NGI_D3D12 
    #define SHADER_CONST(Type, Name, Value) Type Name
#else
    #define SHADER_CONST(Type, Name, Value) [[vk::constant_id(__COUNTER__)]] const Type Name = Value
#endif

SHADER_CONST(bool, USE_LOCAL_EXPOSURE, false);
SHADER_CONST(bool, USE_VIGNETTE, false);

Texture3D LumBilateralGrid;
Texture2D BlurredLogLum;
// SamplerState LumBilateralGridSampler;
// SamplerState BlurredLogLumSampler;

cbuffer param : register(space0)
{
	bool EnableHSL;
	bool EnableSRGBAdjustment;
	bool EnableAutoExposure;
	float GlobalHueShift;
	float GlobalSaturation;
	float GlobalVibrance;
	float GlobalLuminance;
	float sigma;
	float multiFactor;

	bool EnableHue;
	float HueRed;
	float HueOrange;
	float HueYellow;
	float HueGreen;
	float HueAqua;
	float HueBlue;
	float HuePurple;
	float HueMagenta;

	bool EnableSaturation;
	float SaturationRed;
	float SaturationOrange;
	float SaturationYellow;
	float SaturationGreen;
	float SaturationAqua;
	float SaturationBlue;
	float SaturationPurple;
	float SaturationMagenta;

	bool EnableLuminance;
	float LuminanceRed;
	float LuminanceOrange;
	float LuminanceYellow;
	float LuminanceGreen;
	float LuminanceAqua;
	float LuminanceBlue;
	float LuminancePurple;
	float LuminanceMagenta;

	float ceTime;

	bool EnableLUT;
    bool _DisableWork;
    bool  UseSimpleGammaCorrection;
	float EyeAdaptation_HistogramScale;
	float EyeAdaptation_HistogramBias;
	float EyeAdaptation_LuminanceMin;
	float EyeAdaptation_LocalExposureBlurredLuminanceBlend;
	float EyeAdaptation_LocalExposureHighLightContrastReduction;
	float EyeAdaptation_LocalExposureShadowContrastReduction;
	float EyeAdaptation_LocalExposureDetailStrength;
	float EyeAdaptation_LocalExposureMiddleGreyExposureCompensation;
	float EyeAdaptation_LocalExposureShadowThreshold;
	float EyeAdaptation_LocalExposureHighlightThreshold;
	float VignetteIntensity;
	float ViewAspectRatio;
}

float Square(float x){
	return x*x;
}

float3 UnwrappedTexture3DSample(float3 UVW)
{
	const float LUTSize = 32.0f;

	float IntW = floor( UVW.z * LUTSize - 0.5 );
	float FracW = UVW.z * LUTSize - 0.5 - IntW;

	float U = ( UVW.x + IntW ) / LUTSize;
	float V = UVW.y;

	float4 RG0 = lut_texture.Sample(ce_Sampler_Clamp, float2(U, V));
	float4 RG1 = lut_texture.Sample(ce_Sampler_Clamp, float2(U + 1.0f / LUTSize, V));

	return lerp(RG0, RG1, FracW).xyz;
}

float4 ColorLookUpTable(float4 LinearColor)
{
	const float LUTSize = 32.0f;

	float3 LUTEncodedColor = LinToLog( LinearColor.rgb + LogToLin( 0 ) );

	float3 UVW = LUTEncodedColor * ((LUTSize - 1) / LUTSize) + (0.5f / LUTSize);

	float3 OutDeviceColor = UnwrappedTexture3DSample(UVW);

	OutDeviceColor *= 1.05f;

	return float4(OutDeviceColor, LinearColor.w);
}


float Adjust_Linear(float v, float adj){
	// v in range [0, 1], adj in range [-0.9999, 0.9999]
	float k = 1.0;
	if (adj < 0.f) 
		k = 1.f + adj;
	else 
		k = 1.f / (1.f - adj);
	v = v * k;
	v = min(1.f, max(0.f, v));
	return v;
}

float Adjust_Power(float v, float adj){
	// v in range [0, 1], adj in range [-0.9999, 0.9999]
	float k = 1.0;
	if (adj < 0.f) 
		k = 1.f / (1.f + adj);
	else 
		k = 1.f - adj;
	v = pow(v, k);
	v = min(1.f, max(0.f, v));
	return v;
}

float Adjust_Add(float v, float adj){
	// v in range [0, 1], adj in range [-0.9999, 0.9999]
	v += adj;
	v = min(1.f, max(0.f, v));
	return v;
}

float3 AdjustVibrance(float3 RGB){
	float avg = (RGB.r + RGB.g + RGB.g + RGB.b) * 0.25f;
	float inMax = max(RGB.r, max(RGB.g, RGB.b));
	float amt = (abs(inMax - avg) * 10.f) * (1.f - GlobalVibrance * 2.f);
	if (RGB.b != inMax) RGB.b += (inMax - RGB.b) * amt;
	if (RGB.g != inMax) RGB.g += (inMax - RGB.g) * amt;
	if (RGB.r != inMax) RGB.r += (inMax - RGB.r) * amt;
	return RGB;
}

float AdjustHue(float inH, float factor[8]){
	float adj = 
		factor[0] * (HueRed - 0.5f) +
		factor[1] * (HueOrange - 0.5f) +
		factor[2] * (HueYellow - 0.5f) +
		factor[3] * (HueGreen - 0.5f) +
		factor[4] * (HueAqua - 0.5f) +
		factor[5] * (HueBlue - 0.5f) +
		factor[6] * (HuePurple - 0.5f) +
		factor[7] * (HueMagenta - 0.5f);
	float outH = inH;
	outH += adj * multiFactor * multiFactor;
	while (outH >= 360.f) outH -= 360.f;
	while (outH < 0.f) outH +=360.f;
	return outH;
}

float AdjustSaturate(float inS, float factor[8]){
	float adj = 
		factor[0] * (SaturationRed - 0.5f) +
		factor[1] * (SaturationOrange - 0.5f) +
		factor[2] * (SaturationYellow - 0.5f) +
		factor[3] * (SaturationGreen - 0.5f) +
		factor[4] * (SaturationAqua - 0.5f) +
		factor[5] * (SaturationBlue - 0.5f) +
		factor[6] * (SaturationPurple - 0.5f) +
		factor[7] * (SaturationMagenta - 0.5f);
	adj /= 0.0148f;
	adj = min(0.99999f, max(-0.99999f, adj));
	float outS = Adjust_Linear(inS, adj);
	return outS;
}

float AdjustLuminance(float inL, float factor[8]){
	float adj = 
		factor[0] * (LuminanceRed - 0.5f) +
		factor[1] * (LuminanceOrange - 0.5f) +
		factor[2] * (LuminanceYellow - 0.5f) +
		factor[3] * (LuminanceGreen - 0.5f) +
		factor[4] * (LuminanceAqua - 0.5f) +
		factor[5] * (LuminanceBlue - 0.5f) +
		factor[6] * (LuminancePurple - 0.5f) +
		factor[7] * (LuminanceMagenta - 0.5f);
	adj /= 0.0148f;
	adj = min(0.99999f, max(-0.99999f, adj));
	float outL = Adjust_Power(inL, adj);
	return outL;
}

float sign(float x){
	if (x==0) return 0;
	else if (x>0) return 1;
	else return -1;
}

float3 RGB_2_HSL(float3 RGB){
	float H = 0.f;
	float S = 0.f;
	float L = 0.f;
	float inMax = max(RGB.r, max(RGB.g, RGB.b));
	float inMin = min(RGB.r, min(RGB.g, RGB.b));
	float invRange = 1.f / (inMax - inMin);
	if (abs(inMax - inMin)>1e-6f){
		if (RGB.r == inMax){
			if (RGB.g >= RGB.b){
				H = 60 * (RGB.g - RGB.b) * invRange;
			}
			else {
                // (chopperlin) be careful with this line
				H = 60 * (RGB.g - RGB.b) * invRange + 360;
			}
		}
		else if (RGB.g == inMax){
			H = 60 * (RGB.b - RGB.r) * invRange + 120;
		}
		else 
			H = 60 * (RGB.r - RGB.g) * invRange + 240;
	}
	L = 0.5f * (inMax + inMin);
	if (L == 0 || inMax == inMin){
		S = 0;
	}
	else if (L <= 0.5f){
		S = (inMax - inMin) / (2 * L);
	}
	else {
		S = (inMax - inMin) / (2 - 2 * L);
	}
	return float3(H, S, L);
}

float3 HSL_2_RGB(float3 HSL){
	float H = HSL.x;
	float S = HSL.y;
	float L = HSL.z;
	if (S == 0.f) return float3(L, L, L);

	float C = (1.f - abs(2.f * L - 1.f)) * S;
	float X = C * (1.f - abs((H / 60.f) % 2.f - 1.f));
	float m = L - 0.5f * C;
	float3 RGB;
	if (H >= 0 && H < 60)
		RGB = float3(C, X, 0);
	else if (H < 120)
		RGB = float3(X, C, 0);
	else if (H < 180)
		RGB = float3(0, C, X);
	else if (H < 240)
		RGB = float3(0, X, C);
	else if (H < 300)
		RGB = float3(X, 0, C);
	else 
		RGB = float3(C, 0, X);
	RGB += float3(m, m, m);
	return RGB;
}

float3 SRGBAdjustment(float3 RGB){
	float3 HSL = RGB_2_HSL(RGB);
	HSL.z = Adjust_Power(HSL.z, GlobalLuminance * 2.f - 1.f);
	HSL.x += (GlobalHueShift * 2.f - 1.f) * 180.f;
	while (HSL.x > 360.f) HSL.x -= 360.f;
	while (HSL.x < 0.f) HSL.x += 360.f;
	//HSL.x = Adjust_Add(HSL.x, GlobalHueShift * 2.f - 1.f);
	HSL.y = Adjust_Linear(HSL.y, GlobalSaturation * 2.f - 1.f);
	RGB = HSL_2_RGB(HSL);
	RGB = AdjustVibrance(RGB);
	
	if (!EnableHue && !EnableSaturation && !EnableLuminance) 
		return RGB;

	HSL = RGB_2_HSL(RGB);

	//float sigma = 15.f;
	float k = 0.3989422804f / sigma; // 1 / ( sqrt(2 * pi) * sigma)
	float inv_2_sigma_square = 1.f / (2 * Square(sigma));
	float lowerBound = k * exp2(-Square(3 * sigma) * inv_2_sigma_square); // phi(3*sigma)
	float factor[8];
	for (int i = 0; i < 8; i++){
		float minDist = min(abs(HSL.x - i * 45.f), min(abs(HSL.x +360.f - i*45.f), abs(HSL.x -360.f - i*45.f)));
		minDist = max(minDist, 0.5f);
		factor[i] = k * exp2(-Square(minDist) * inv_2_sigma_square);
		factor[i] = max(0.f, factor[i] - lowerBound);
	}

	float3 outHSL = HSL;
	if (EnableHue) outHSL.x = AdjustHue(HSL.x, factor);
	if (EnableSaturation) outHSL.y = AdjustSaturate(HSL.y, factor);
	if (EnableLuminance) outHSL.z = AdjustLuminance(HSL.z, factor);

	float3 outRGB = HSL_2_RGB(outHSL);
	return outRGB;
}

float3 Texture3DSampler(float3 UVW){
	return ex_3d_lut_texture.SampleLevel(ce_Sampler_Clamp, UVW, 0);
}

float3 Debug_Show3DLut(float2 UV){
	float LUTSize = 32.f;

	// 0.49999f instead of 0.5f to avoid getting into negative values
	UV -= float2(0.49999f / (LUTSize * LUTSize), 0.49999f / LUTSize);

	float Scale = LUTSize / (LUTSize - 1);

	float3 RGB;
		
	RGB.r = frac(UV.x * LUTSize);
	RGB.b = UV.x - RGB.r / LUTSize;
	RGB.g = UV.y;

	float3 c = Texture3DSampler(RGB);
	return float4(c, 1.f);
}

float2 random(float2 uv)
{
    uv = float2( dot(uv, float2(127.1f,311.7f) ),
               dot(uv, float2(269.5f,183.3f) ) );
    return -1.0f + 2.0f * frac(sin(uv) * 43758.5453123f);
}

float perlinNoise(float2 uv)
{
    float2 uv_index = floor(uv);
    float2 uv_fract = frac(uv);

    float2 blur = smoothstep(0.0, 1.0, uv_fract);

    return lerp( lerp( dot( random(uv_index + float2(0.0,0.0) ), uv_fract - float2(0.0,0.0) ),
                     dot( random(uv_index + float2(1.0,0.0) ), uv_fract - float2(1.0,0.0) ), blur.x),
                lerp( dot( random(uv_index + float2(0.0,1.0) ), uv_fract - float2(0.0,1.0) ),
                     dot( random(uv_index + float2(1.0,1.0) ), uv_fract - float2(1.0,1.0) ), blur.x), blur.y) + 0.5;
}

float Noise(float2 p)
{
	const float K1 = 0.366025404f; // (sqrt(3)-1)/2;
    const float K2 = 0.211324865f; // (3-sqrt(3))/6;
	float2 i = floor(p + (p.x+p.y)*K1);	
    float2 a = p - i + (i.x+i.y)*K2;
    float2 o = (a.x>a.y) ? float2(1.0,0.0) : float2(0.0,1.0); //vec2 of = 0.5 + 0.5*vec2(sign(a.x-a.y), sign(a.y-a.x));
    float2 b = a - o + K2;
	float2 c = a - 1.0 + 2.0*K2;
    float3 h = max(0.5-float3(dot(a,a), dot(b,b), dot(c,c) ), 0.0 );
	float3 n = h*h*h*h*float3( dot(a,random(i+0.0)), dot(b,random(i+o)), dot(c,random(i+1.0))); 
	return dot(n, float3(70.f,70.f,70.f));	
}

struct VS2PS
{
	float4 Pos : 		SV_POSITION;
	noperspective float2 UV : 		TEXCOORD0;
	noperspective float4 ExposureScaleVignette : TEXCOORD1;
	noperspective float2 FullViewUV 			: TEXCOORD4;
};

void DrawRectangle(
	float4 InPosition,
	float2 InTexCoord,
	out float4 OutPosition,
	out float2 OutTexCoord)
{
	OutPosition = InPosition;
	OutPosition.xy = -1.0f + 2.0f * InPosition.xy;
	OutPosition.xy *= float2(1, -1);
	OutTexCoord.xy = InTexCoord.xy;
}

// Scale {-1 to 1} of the viewport space to vignette circle space.
// Vignette space is scaled such that regardless of viewport aspect ratio, 
// corners are at the same brightness on a circle.
float2 VignetteSpace(float2 Pos, float AspectRatio)
{
	// could be optimized but this computation should be done in the vertex shader (3 or 4 vertices)
	float Scale = sqrt(2.0) / sqrt(1.0 + AspectRatio * AspectRatio);
	return Pos * float2(1.0, AspectRatio) * Scale;
}

float FastNoise(float3 uvw)
{
    fnl_state noise = fnlCreateState();
    noise.noise_type = FNL_NOISE_PERLIN;
    noise.frequency = 0.02;
    noise.fractal_type = FNL_FRACTAL_FBM;
    noise.seed = 5;
	noise.octaves = 5;

    return 0.5f + fnlGetNoise3D(noise, uvw.x, uvw.y, uvw.z);
}

float FastCellular(float3 uvw)
{
    fnl_state noise = fnlCreateState();
    noise.noise_type = FNL_NOISE_CELLULAR;
    noise.frequency = 0.02;
    noise.fractal_type = FNL_FRACTAL_FBM;
    noise.seed = 5;
	noise.octaves = 5;

    return -fnlGetNoise3D(noise, uvw.x, uvw.y, uvw.z);
}

float Remap(float v, float2 src, float2 dst){
    return dst.x + (v - src.x) * (dst.y - dst.x) / (src.y - src.x);
}

// inverse of ComputeLogLuminanceFromHistogramPosition
// @param LogLuminance
// @return HistogramPosition 0..1
float ComputeHistogramPositionFromLogLuminance(float LogLuminance)
{
	return LogLuminance * EyeAdaptation_HistogramScale + EyeAdaptation_HistogramBias;
}

float CalculateEyeAdaptationLuminance(float3 Color)
{
	return max(dot(Color, float3(1.0f, 1.0f, 1.0f) / 3.0f), EyeAdaptation_LuminanceMin);
}

float CalculateBaseLogLuminance(float BilateralLum, float BlurredLum, float BlurredLumBlend, float ExposureScale)
{
	return lerp(BilateralLum, BlurredLum, BlurredLumBlend) + log2(ExposureScale);
}

float CalculateBaseLogLuminance(float LogLum, float BlurredLumBlend, float ExposureScale, float2 UV, Texture3D LumBilateralGrid, Texture2D BlurredLogLum, SamplerState LumBilateralGridSampler, SamplerState BlurredLogLumSampler)
{
	const uint BILATERAL_GRID_DEPTH = 32;
	float3 BilateralGridUVW;
	BilateralGridUVW.xy = UV;// * LocalExposure_BilateralGridUVScale;
	BilateralGridUVW.z = (ComputeHistogramPositionFromLogLuminance(LogLum) * (BILATERAL_GRID_DEPTH - 1) + 0.5f) / BILATERAL_GRID_DEPTH;
	float2 BilateralGridLum = LumBilateralGrid.Sample(LumBilateralGridSampler, BilateralGridUVW).xy;
	float BilateralLum = BilateralGridLum.x / BilateralGridLum.y;
	float BlurredLum = BlurredLogLum.Sample(BlurredLogLumSampler, UV).r;
	if (BilateralGridLum.y < 0.001)
	{
		// fallback to blurred luminance if bilateral grid doesn't have data
		// this can happen since grid is populated using half resolution image
		BilateralLum = BlurredLum;
	}

	return CalculateBaseLogLuminance(BilateralLum, BlurredLum, BlurredLumBlend, ExposureScale);
}

float CalculateLogLocalExposure(float LogLum, float BaseLogLum, float LogMiddleGrey, float HighlightContrastScale, float ShadowContrastScale, float DetailStrength)
{
	float DetailLogLum = LogLum - BaseLogLum;
	float BaseCentered = (BaseLogLum - LogMiddleGrey);
	float ContrastScale = BaseCentered > 0 ? HighlightContrastScale : ShadowContrastScale;
	float ThresholdOffset;
	{
		if (BaseCentered > 0)
		{
			
			ThresholdOffset = BaseCentered - max(0.0f, BaseCentered - EyeAdaptation_LocalExposureHighlightThreshold);
		}
		else
		{
			ThresholdOffset = BaseCentered - min(0.0f, BaseCentered + EyeAdaptation_LocalExposureShadowThreshold);
		}
		BaseCentered -= ThresholdOffset;
	}
	float LogLocalLum = LogMiddleGrey + ThresholdOffset + BaseCentered * ContrastScale + DetailLogLum * DetailStrength;
	return LogLocalLum - LogLum;
}

float CalculateLocalExposure(float LogLum, float BaseLogLum, float LogMiddleGrey, float HighlightContrastScale, float ShadowContrastScale, float DetailStrength)
{
	return exp2(CalculateLogLocalExposure(LogLum, BaseLogLum, LogMiddleGrey, HighlightContrastScale, ShadowContrastScale, DetailStrength));
}

// Generate a mask to darken the screen borders.
// (Camera artifact and artistic effect)
// @param VignetteCircleSpacePos from VignetteSpace()
float ComputeVignetteMask(float2 VignetteCircleSpacePos, float Intensity)
{
	// Natural vignetting
	// cosine-fourth law
	VignetteCircleSpacePos *= Intensity;
	float Tan2Angle = dot( VignetteCircleSpacePos, VignetteCircleSpacePos );
	float Cos4Angle = Square( rcp( Tan2Angle + 1 ) );
	return Cos4Angle;
}

VS2PS VSMain(float4 Pos : POSITION, float2 uv : TEXCOORD0)
{
	VS2PS ret;
	float4 Outpos;
	float2 Outuv;
	DrawRectangle(
		Pos,
		uv,
		Outpos,
		Outuv);
	ret.Pos = Outpos;
	ret.UV = Outuv;
	ret.FullViewUV = Outpos.xy * float2(0.5,-0.5) + 0.5;
	
	ret.ExposureScaleVignette.xy = exposure_texture.Load(int3(0, 0, 0)).xw;
	
	// Middle grey lum value adjusted by exposure compensation
	ret.ExposureScaleVignette.y = log2(0.18 * ret.ExposureScaleVignette.y * EyeAdaptation_LocalExposureMiddleGreyExposureCompensation);

	// Scale vignette to always be a circle with consistent corner intensity.	
	if (USE_VIGNETTE) 
	{
		ret.ExposureScaleVignette.zw = VignetteSpace(Outpos.xy, ViewAspectRatio);
	}

	return ret;
}

float4 PSMain(VS2PS input) : SV_TARGET
{
	float4 ExposureScaleVignette = input.ExposureScaleVignette;
	float ExposureScale = ExposureScaleVignette.x;
	float ret = spreadTex.Sample(ce_Sampler_Clamp, input.UV).x;
	//float ret = concentrationTex.Sample(ce_Sampler_Clamp, input.UV);

	//ret+=0.1f;
	//return float4(ret, ret, ret, 1.f);

	float4 s = src_texture.Sample(ce_Sampler_Clamp, input.UV);
	if (isnan(s.x)) return float4(1.f, 0.f, 1.f, 1.f);
	
	float4 color = s;
    float _Gamma = 2.2f;
	if(UseSimpleGammaCorrection)
    {
        color = pow(color, 1.0 / _Gamma);
        return color;
    }
	else
    {
        if (!_DisableWork)
        {

            if (USE_LOCAL_EXPOSURE)
            {
                float4 pre_color = pre_src_texture.Sample(ce_Sampler_Clamp, input.UV);
                float3 combined_bloom = (color - pre_color).rgb;

                float2 FullViewUV = input.FullViewUV;

                float LuminanceVal = CalculateEyeAdaptationLuminance(pre_color.rgb);
                float LogLuminance = log2(LuminanceVal);
                float MiddleGreyLumValue = ExposureScaleVignette.y;
                float BaseLogLum = CalculateBaseLogLuminance(LogLuminance, EyeAdaptation_LocalExposureBlurredLuminanceBlend, ExposureScale, FullViewUV, LumBilateralGrid, BlurredLogLum, ce_Sampler_Clamp, ce_Sampler_Clamp);
                float LocalExposure = CalculateLocalExposure(LogLuminance + log2(ExposureScale), BaseLogLum, MiddleGreyLumValue, EyeAdaptation_LocalExposureHighLightContrastReduction, EyeAdaptation_LocalExposureShadowContrastReduction, EyeAdaptation_LocalExposureDetailStrength);

				// Apply before bloom
                pre_color.rgb *= LocalExposure;
                color.rgb = pre_color.rgb + combined_bloom;
            }

            color.rgb *= ExposureScale;
		
            if (USE_VIGNETTE)
            {
                color.rgb *= ComputeVignetteMask(ExposureScaleVignette.zw, VignetteIntensity);
			// color.rgb *= ComputeVignetteMask(ExposureScaleVignette.zw, VignetteIntensity) < 0.8 ? 0 : 1;
            }

            color = ColorLookUpTable(color);
            if (EnableLUT)
            {
            //float3 c = Debug_Show3DLut(input.UV);
            //return float4(c.xyz, 1.f);
                color.xyz = Texture3DSampler(color.xyz);
            }
            if (EnableSRGBAdjustment)
            {
                color.xyz = SRGBAdjustment(color.xyz);
            }
        }
    }
        return color;
 }
