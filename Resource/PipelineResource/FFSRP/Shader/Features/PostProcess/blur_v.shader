Texture2D<float4> toBlur_texture : register(space0);
SamplerState ce_Sampler_Clamp : register(space0);

cbuffer para : register(space0)
{
	float4 ce_ScreenParams;// w h 1/w 1/h
}


cbuffer cbMtl : register(space1)
{
    float _SoftShadowBlur_KernelSize;
}

struct VS2PS
{
	float2 UV : 		TEXCOORD0;
	float4 Pos : 		SV_POSITION;
};

void DrawRectangle(
	float4 InPosition,
	float2 InTexCoord,
	out float4 OutPosition,
	out float2 OutTexCoord)
{
	OutPosition = InPosition;
	OutPosition.xy = -1.0f + 2.0f * InPosition.xy;
	OutPosition.xy *= float2(1, -1);
	OutTexCoord.xy = InTexCoord.xy;
}

VS2PS VSMain(float4 Pos : POSITION, float2 uv : TEXCOORD0)
{
	VS2PS ret;
	float4 Outpos;
	float2 Outuv;
	DrawRectangle(
		Pos,
		uv,
		Outpos,
		Outuv);
	ret.Pos = Outpos;
	ret.UV = Outuv;
	return ret;
}

float4 PSMain(VS2PS input) : SV_TARGET
{
	int halfKernelSize = (int)(_SoftShadowBlur_KernelSize);
    float4 color = 0;

    for(int i=-halfKernelSize; i <= halfKernelSize; i++)
	{
		float2 sample_UV = input.UV + float2(0, i*ce_ScreenParams.w);
		color += toBlur_texture.Sample(ce_Sampler_Clamp, sample_UV);
	}
	
	return color/(float)(2*halfKernelSize + 1);
}