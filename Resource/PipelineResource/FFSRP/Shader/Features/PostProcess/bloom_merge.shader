Texture2D<float4> toBloom_texture : register(space0);
Texture2D<float4> bloomBlur_texture : register(space0);
SamplerState ce_Sampler_Clamp : register(space0);

cbuffer para : register(space0)
{
	float2 TexSizeLow;
	float BloomIntensity;
	float3 BloomTint;
	float SampleScale;
}

struct VS2PS
{
	float2 UV : 		TEXCOORD0;
	float4 Pos : 		SV_POSITION;
};

void DrawRectangle(
	float4 InPosition,
	float2 InTexCoord,
	out float4 OutPosition,
	out float2 OutTexCoord)
{
	OutPosition = InPosition;
	OutPosition.xy = -1.0f + 2.0f * InPosition.xy;
	OutPosition.xy *= float2(1, -1);
	OutTexCoord.xy = InTexCoord.xy;
}

VS2PS VSMain(float4 Pos : POSITION, float2 uv : TEXCOORD0)
{
	VS2PS ret;
	float4 Outpos;
	float2 Outuv;
	DrawRectangle(
		Pos,
		uv,
		Outpos,
		Outuv);
	ret.Pos = Outpos;
	ret.UV = Outuv;
	return ret;
}

float3 SampleWithScale(float2 uv, float2 offset)
{
	return bloomBlur_texture.Sample(ce_Sampler_Clamp, uv + TexSizeLow * offset * SampleScale);
}

float4 PSMain(VS2PS input) : SV_TARGET
{
    float4 color = toBloom_texture.Sample(ce_Sampler_Clamp, input.UV);

	float3 s1 = SampleWithScale(input.UV, float2(-0.5, -0.5));
	float3 s2 = SampleWithScale(input.UV, float2(-0.5, 0.5));
	float3 s3 = SampleWithScale(input.UV, float2(0.5, -0.5));
	float3 s4 = SampleWithScale(input.UV, float2(0.5, 0.5));

	float3 bloom = (s1 + s2 + s3 + s4) * 0.25f;

	return float4(color.xyz + bloom * BloomIntensity * BloomTint, color.w);
}