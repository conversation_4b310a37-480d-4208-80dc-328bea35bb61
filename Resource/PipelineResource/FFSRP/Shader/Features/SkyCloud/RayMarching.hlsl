#pragma once


#define float3_luma float3(0.299f, 0.587f, 0.114f)

float3 AddAPEffect(float4 cloudColor, float avgDepth, float2 screenUV)
{
    float depthInKM = avgDepth * 0.001;
    float4 AP = GetAtmosphereAerialPerspective(screenUV, depthInKM, cloudLocalAPScale);
    AP.xyz *= GetAtmosphereExposure();
    cloudColor.rgb = cloudColor.rgb * AP.w + AP.rgb * cloudColor.a;

    return cloudColor.rgb;
}

float GetCloudAnisotropy(float density, float min_value, float max_value) {
    // 原始三次曲线计算
    float t = -0.674067 * density * density * density + 
               2.105359 * density * density + 
              -2.260314 * density + 
               0.927877;
    
    // 归一化到[0,1]范围
    float normalized = (t - 0.098905) / (0.927877 - 0.098905);
    
    // 映射到目标范围[min_value, max_value]
    return min_value + normalized * (max_value - min_value);
}

// Saturate color
// saturation = 1 -> color
// saturation = 0 -> grey
float3 colorSaturation(float3 color, float saturation) { return lerp(1.xxx * dot(color, float3_luma), color, saturation); }
float rgbToLuma(float3 color) { return dot(color, float3(0.299, 0.587, 0.114)); }
float rgbToLuma(float4 color) { return rgbToLuma(color.xyz); }

// from unigine, a slightly different from UE's code
float getHenyeyGreensteinPhase(float cos_angle, float anisotropy)
{
    float anisotropy2 = anisotropy * anisotropy;
    return (1.0f - anisotropy2) / pow(1.0f + anisotropy2 - 2.0f * anisotropy * cos_angle, 1.5);
}

float getHenyeyGreensteinPhaseMultiscattering(float cos_angle, float anisotropy, float w0, float w1)
{
    return w0 * getHenyeyGreensteinPhase(cos_angle, anisotropy) + w1 * getHenyeyGreensteinPhase(cos_angle, 2.0 / 3.0 * anisotropy);
}

// TODO chopperlin we need a better multiscattering calculation

float getSunLighting(float lighting_occlusion, float3 sun_ray, float dotVL, float density, CloudData cloudData)
{
    // debug dotVL and it's right
#if 0    
    return saturate(dotVL);
#endif
    // artist handpicked parameters
    // if 0.5 means no anisotropy, and the lighting is reasonable
    // float density_to_anisotropy = TEXTURE_CLOUDS_ANISOTROPY_BY_DENSITY(0, saturate(density)).r;
    // TODO: Unigine use this anisotropy to get powder effect, but its contrast is too hight(the side to sun is too bright, and backside is too dark)
    // so I lower the largest value and it's still no good!
    float density_to_anisotropy = GetCloudAnisotropy(density, cloudData.cloud_anisotropy_min, cloudData.cloud_anisotropy_max);
    const float anisotropy = density_to_anisotropy * 2.0f - 1.0f;
    float remapDotVL = remap(dotVL, -1.0f, 1.0f, cloudData.dotVL_min, cloudData.dotVL_max);
    float lighting = getHenyeyGreensteinPhaseMultiscattering(remapDotVL, anisotropy, cloudData.phase_main_octave_weight, cloudData.phase_secondary_octave_weight);
    /*if (USE_OLD_HGPhase)
    {
        lighting = dualLobPhase(phaseG, phaseGbck, phaseBlend, dotVL);
    }*/
    // debug henyey greenstein phase lighting
#if 0
    return lighting;
#endif    

    float density_along_light_ray = cloudData.sun_attenuation * lighting_occlusion;
    lighting *= exp( - density_along_light_ray ) + (exp(-density_along_light_ray * 0.25) * cloudData.multiscattering_intensity * (1.0 - saturate(dotVL)));

    return lighting;
}

float GetMipLevel(float dist)
{
    return clamp(remap(dist, mipNearDist, mipFarDist, 0, mipLevelVal), 0, mipLevelVal);
}

//copy from lightning, maybe need refactor
float3 CalcShiningCloudLightScattering(Ray ray, float3 samplePosMeter, int lightIndex, CloudData cloudData)
{
    if (shineCloudLightCount==0)
        return 0.xxx;

    // Get light position in meter
    float3 lightPosMeter = GetLargeCoordinateAbsolutePosition(ce_Lights[lightIndex].LightDirPos.xyz, ce_Lights[lightIndex].LightTilePos.xyz) * 0.01;

    float distance = length(lightPosMeter - samplePosMeter);
    // out of range just return 0
    if (distance > ce_Lights[lightIndex].LightDirPos.w)
        return 0.xxx;


    // Exponential falloff + HG phase function
    float intensityFade = exp(-distance * LightningFade);
    float3 lightDir = normalize(lightPosMeter - samplePosMeter);
    float VoL = dot(ray.dir, lightDir);
    float lighting = getHenyeyGreensteinPhaseMultiscattering(VoL, 0, 1.f, 0.5f);

    float3 lightScattering = intensityFade * lighting * cloudData.attenuation_coefficient * ce_Lights[lightIndex].LightColor.xyz;

    // Added to scattering term
    return lightScattering;
}


CloudLayerAttribute GetCloudLayerAttributeImp(in CloudLayerAttribute layer, float heightY, float LayerBottomHeight, float LayerTopHeight, float coverage, float density)
{

    layer.SampleNormAltitudeInLayer = remap(heightY, LayerBottomHeight, LayerTopHeight, 0.f, 1.f);
    layer.Coverage = coverage;
    layer.CloudHeight = saturate(abs(LayerTopHeight - LayerBottomHeight));
    layer.SampleAltitudeInLayer = heightY - LayerBottomHeight;
    layer.BottomAltitude = LayerBottomHeight;
    layer.CloudDensity = density;

    return layer;
}



// get the attributes of layer at current sample
// this should be a more general case, instead of enum mid/bottom
// Yes, but before we refactor all the codes, we consider mainly one thunderstorm and one cloud layer now
CloudAttributeOut GetCloudLayerAttribute(float3 posMeter, out float normalizeHeight, in float mip = 0.f, out int cloud_layer_index = 0, in bool isShadow = false)
{
    CloudLayerAttribute attribute_out;

    float3 posOnEarth = GetWGS84Projection(posMeter);
    attribute_out.SampleAltitude = length(posMeter - posOnEarth); // height
    float heightY = (attribute_out.SampleAltitude - BottomHeight)/(TopHeight - BottomHeight);

    // you should compare all layer's density and choose the biggest one
    // this is ugly we will refactor this
    attribute_out.CloudType = -1;
    normalizeHeight = 0.f;
    CloudAttributeOut cloud_attrib_temp = (CloudAttributeOut)0;
    CloudAttributeOut cloud_attrib_out = (CloudAttributeOut)0;

    cloud_layer_index = 0;

    float density = 0.f;
    if (ThunderstormEnable && InsideStorm(posMeter))
    {
        // storm bottomHeight and topHeight remap to 0 - 20000
        // try strorm0 here
        float3 stormCenterProj = GetWGS84Projection(_StormData[0].PosEnable.xyz);
        float stormCenterAltitude = length(_StormData[0].PosEnable.xyz - stormCenterProj);
        float2 stormVRange = float2((stormCenterAltitude - _StormData[0].AxisY.w)/(TopHeight - BottomHeight), (stormCenterAltitude + _StormData[0].AxisY.w)/(TopHeight - BottomHeight));
        attribute_out = GetCloudLayerAttributeImp(attribute_out, heightY, stormVRange.x, stormVRange.y, 0,  _StormData[0].Params.y);
        cloud_attrib_temp = SampleCloudAttribute(posMeter, attribute_out, true, mip, 0, isShadow);
        if (cloud_attrib_temp.Density > density)
        {
            density = cloud_attrib_temp.Density;
            cloud_attrib_out = cloud_attrib_temp;
            normalizeHeight = attribute_out.SampleNormAltitudeInLayer;
        }
        // Don't forget this, it will cause thunderstorm very noisy when at far view
        cloud_attrib_out.hint = max(cloud_attrib_out.hint, cloud_attrib_temp.hint);
    }
    if (LowCloudEnable && heightY > LowCloudVRange.x && heightY < LowCloudVRange.y)
    {
        attribute_out.CloudType = 0;
        attribute_out = GetCloudLayerAttributeImp(attribute_out, heightY, LowCloudVRange.x, LowCloudVRange.y, LowCloudCover, LowCloudDensity);
        cloud_attrib_temp = SampleCloudAttribute(posMeter, attribute_out, false, mip, 0, isShadow);
        if (cloud_attrib_temp.Density > density)
        {
            density = cloud_attrib_temp.Density;
            cloud_attrib_out = cloud_attrib_temp;
            normalizeHeight = attribute_out.SampleNormAltitudeInLayer;
        }
         // Dont' forget this, it will cause thunderstorm very noisy when at farview
        cloud_attrib_out.hint = max(cloud_attrib_out.hint, cloud_attrib_temp.hint);
    }

    return cloud_attrib_out;
}



// // CloudMapNew only consider low & mid cloud, top cloud density is separate
// CloudAttributeOut cloudMapNew(float3 posMeter, float horizonDist, out float normalizeHeight, out float coverage4Shading, out float cloudHeight,
//                   bool isForShadow, out int cloudType)
// {
//     float mipLevel = GetMipLevel(horizonDist);
//     float kCoverage = 0.5f;
//     float3 posKm = posMeter * 0.001;

//     CloudAttributeOut layer_attrib = GetCloudLayerAttribute(posMeter, normalizeHeight);
//     return layer_attrib;
    
// }

float GetBlueNoiseSampleOffset(int x, int y){
    int JitterIdx = 1;//int(ceFrameNumber) & 15;
    int2 noisePixIdx = int2(x & 63, y & 63);
    float blueNoiseSample = BlueNoiseTex2D.Load(int3(noisePixIdx, 0));

    float offset = frac(float(JitterIdx) * 1.618034f + blueNoiseSample);
    return offset * 2 - 1.f;
}


float getCloudShadow(float3 posMeter, float3 sunDirection, float lighting_noise = 0.f)
{
    float lighting_occlusion = 0.0f;
    float normalizeHeight = 0.f;
    float3 old_lighting_samples = 0.xxx;
    
    // 假设有6个步骤（包括起始点和终点），符合给出的样本
    float steps = cloudShadowSteps;
    float stepSize = lighting_trace_length / steps;

    int cloud_layer_index = 0;
    
    for (int ls = 0; ls < cloudShadowSteps; ls++)
    {
        // 归一化位置 [0,1]
        float t = (float)(ls + 1) / steps;
        
        // 应用分布函数
        // 当 distribution = 1.0 时: [100, 200, 300, 400, 500] (均匀分布)
        // 当 distribution = 2.0 时: [16.666, 66.667, 150, 266.6, 416.667]
        // 当 distribution = 3.0 时: [2.777, 22.222, 75, 177.778, 347.22]
        
        // 使用幂函数实现这种分布关系
        float distT = pow(t, lighting_samples_distribution);
        
        // 计算沿光照方向的实际距离
        float currentDistance = distT * lighting_trace_length;
        
        // 生成当前样本位置
        float3 lighting_sample = sunDirection * currentDistance;
        
        // 保持原有的lighting_noise插值逻辑
        float3 lighting_sample_lerp = lerp(old_lighting_samples, lighting_sample, lighting_noise);
        old_lighting_samples = lighting_sample;
        
        // 在此位置采样
        float3 samplePos = posMeter + lighting_sample_lerp;
        CloudAttributeOut shadow_layer_attrib = GetCloudLayerAttribute(samplePos, normalizeHeight, 0.f, cloud_layer_index, true);
        
        // 累积密度
        lighting_occlusion += shadow_layer_attrib.Density;
    }
    
    return lighting_occlusion;
}

// TODO use Unigine cone-sampled lighting, for all the sample points, use common sample candidates
// TODO this is not good enough, need to refactor it
float getCloudShadowNew(float3 posMeter, float3 sunDirection, float3 s_lighting_samples[LIGHTING_NUM_SAMPLES], float lighting_noise = 0.f)
{   
    // Sample at points in the cone
    float lighting_occlusion = 0.0f;
    float normalizeHeight = 0.f;
    float sum_weights = 0.0f;
    float3 prev_position = 0.xxx;

    int cloud_layer_index = 0;
    
    for (int ls = 0; ls < LIGHTING_NUM_SAMPLES; ls++)
    {
        // 保留原有代码中的noise插值功能
        float3 sample_position = ls > 0 
            ? lerp(prev_position, s_lighting_samples[ls], lighting_noise)
            : s_lighting_samples[ls];
        prev_position = s_lighting_samples[ls];
        
        // 采样位置
        float3 samplePos = posMeter + sample_position;
        
        // 获取云层属性
        CloudAttributeOut shadow_layer_attrib = GetCloudLayerAttribute(samplePos, normalizeHeight, 0.f, cloud_layer_index);
        if (normalizeHeight < 0.0 || normalizeHeight > 1.0)
            continue;
        
        lighting_occlusion += shadow_layer_attrib.Density;
    }
    
    return lighting_occlusion;
}


// scatter cloud  : if distance too far then return a large step
float GetAdaptiveStepLength(int stepIdx, float distFromCamera, bool isOverCast = false){
    if (distFromCamera > mipNearDist && !isOverCast)
        return cloudStepLength;
    float a = cloudStepSmallScale;
    float b = cloudStepSmallExp;
    float expScale = a * exp(b * stepIdx);
    float scaleStepLength = cloudStepLength * expScale;
    float distScale = 1;//remap(distFromCamera, 10, 20, 1, farScale);
    return min(scaleStepLength * distScale, cloudStepLength);
}

// cloudDepth : R : trace_depth(unit m); G : Scene Depth(NDC depth)
float4 RayMarchingMainAdaptiveSteps_New(Ray ray, 
    float2 screenUV, out float2 cloudDepth, AtmosphereParametersUE atmosphere, float scene_depth, float scene_native_depth, 
    float3 s_lighting_samples[LIGHTING_NUM_SAMPLES], float3 sunDirection)
{
    int2 pixCoord = int2(int(screenUV.x * RayMarchingTexSize.x), int(screenUV.y * RayMarchingTexSize.y));
    float4 blue_noise = 0.xxxx; //BlueNoiseTex2D.Load(int3((pixCoord.xy) % 256, 0));   // unigine use a bluenoise texture array

    // 20250420: using bluenoise array can really reduce artifacts in horizon view, but when camera moves there is still flickering.
    // the bluenoise imported into engine using BC3(damn it's stupid), and the blocks are ruined.
    int bn_idx = int((ceFrameNumber / 4.0) % 4);
    if (enableTempBlueNoise)
    {
        switch(bn_idx) {
            case 0: blue_noise = bn_0.Load(int3(fmod(pixCoord.xy, 256), 0)); break;
            case 1: blue_noise = bn_1.Load(int3(fmod(pixCoord.xy, 256), 0)); break;
            case 2: blue_noise = bn_2.Load(int3(fmod(pixCoord.xy, 256), 0)); break;
            case 3: blue_noise = bn_3.Load(int3(fmod(pixCoord.xy, 256), 0)); break;
            // case 4: blue_noise = bn_4.Load(int3(fmod(pixCoord.xy, 256), 0)); break;
            // case 5: blue_noise = bn_5.Load(int3(fmod(pixCoord.xy, 256), 0)); break;
            // case 6: blue_noise = bn_6.Load(int3(fmod(pixCoord.xy, 256), 0)); break;
            // case 7: blue_noise = bn_7.Load(int3(fmod(pixCoord.xy, 256), 0)); break;
            // default: blue_noise = bn_0.Load(int3(fmod(pixCoord.xy, 256), 0)); break;
        }
    }

    float4 out_color = 0.xxxx;

    float VoL = clamp(dot(ray.dir, sunDirection), -1.f, 1.f);

    // ambient lighting
    float3 avgAmbientLighting = GetAtmosphereDistantSkyLight();

    float s_depth_test_threshold = 200.f;
    const float depth_test_factor = rcp(s_depth_test_threshold);
    float traced_space_min = INFINITY;
    float traced_space_max = -INFINITY;
    float traced_space_depth_alpha_aware = INFINITY;

    // TODO these will be opened as params
    float s_noise_lighting = 0.3f;
    float s_noise_step = 0.3f;
    float s_noise_iterations = 0.1f;
    float s_noise_step_skip = 0.3f;
    float s_step_accuracy = 0.75f;

    float lighting_noise = lerp(1.0f, blue_noise.r, s_noise_lighting);
    float max_step = 0.5f * lerp(1.0f, blue_noise.r, s_noise_step) / SAMPLES_COUNT_MODIFER;
    float min_step = 0.0f;

    float max_iterations = Cloud_RayMarching_Steps * lerp(1.0f, blue_noise.r, s_noise_iterations) * SAMPLES_COUNT_MODIFER;

    float alpha_trail = 1.0f;
    float cloud_traced_depth = INFINITY;

    float4 debug_color = 0.xxxx;
    float4 layers_bound = getLayersBound(ray.dir, ray.pos, scene_depth, debug_color);
    if (Debug_RayMarching_Option == Debug_Thunderstorm_Box)
    {
        return debug_color;

    }
    if (Debug_RayMarching_Option == Debug_Layer_Bounds)
    {
        return debug_color;
        /*if (layers_bound.y == CloudCutOffDistance)
            return float4(1.f, 0.f, 0.f, 1.f);
        else if (layers_bound.y < CloudCutOffDistance)
            return float4(0.f, 1.f, 0.f, 1.f);*/
    }
    float2 bounds = layers_bound.xy;   // meters
    float min_height = layers_bound.z;
    float max_height = layers_bound.w;

    // if screen is yellow it means the value is nan

    float2 noised_bounds = bounds;
    // TODO it should be useful but now it only add blue noise in horizontal view, will reopen it later
    if (bounds.x < bounds.y * 0.1f && EnableFarViewNoise)
    {
        // fight horizon line with noise
        float2 noised_uv = screenUV + (blue_noise.xy - 0.5.xx) * 0.05f;
        //return float4(blue_noise.xy, 0.f, 1.f);
        //return float4(noised_uv, 0.f, 1.f);

        float3 noised_cam_to_frag_direction_cp = -screenUVToViewDirection(noised_uv);

        if (Debug_RayMarching_Option == Debug_SceneDepth_Thres)
        {
            if (scene_depth < CloudCutOffDistance * 0.25f)
            {
                return float4(1, 0, 0, 1);
            } else
            {
                return float4(0, 1, 0, 1);
            }
        }

        float noised_scene_depth = scene_depth < CloudCutOffDistance * 0.25f   // only noise change depth for close geometry to avoid noise far away
                                ? GetLinearDepth(DownsampleDepth.Sample(ce_Sampler_Point, noised_uv).r, noised_uv) : scene_depth;
        Ray rayNoised = (Ray)0;
        rayNoised.pos = ray.pos;
        rayNoised.dir = noised_cam_to_frag_direction_cp;
        float4 debug_color_tmp;
        float4 noised_layers_bound = getLayersBound(rayNoised.dir, rayNoised.pos, noised_scene_depth, debug_color_tmp);

        if (abs(noised_layers_bound.y - bounds.y) > min(bounds.y, noised_layers_bound.y))   // apply noise only when there's big difference
        {
            noised_bounds.y = noised_layers_bound.y;
            // debug noised_bounds and it's strange
            if (Debug_RayMarching_Option == Debug_Far_View_Horizon)
                return float4(0.f, 1.f, 0.f, 0.f);
        }
    }

    noised_bounds.y = min(CloudCutOffDistance, noised_bounds.y);
    bounds.y = min(CloudCutOffDistance, bounds.y);

    // Process cloud layers
    float min_step_skip_transparent = 0.25f * lerp(1.0f, blue_noise.r, s_noise_step_skip) / SAMPLES_COUNT_MODIFER;
    float max_step_skip_transparent = 15.0f * (max_height - min_height) / 800.0f / SAMPLES_COUNT_MODIFER;


    float num_iterations = max_iterations;

    float traced_space = bounds.x;
    traced_space_min = min(traced_space, traced_space_min);
    traced_space_max = max(traced_space_max, bounds.y);

    float near_skip_transparent = min(sqrt(min(scene_depth, max(noised_bounds.y, bounds.x))) * min_step_skip_transparent, max_step_skip_transparent);
    float near_search_step = lerpFixed(8.0f, 1.0f, s_step_accuracy) * abs(noised_bounds.y - bounds.x) / num_iterations * (1.0 + 0.5 * lerp(1.0f, blue_noise.r, 0.55));
    float far_skip_transparent = min(sqrt(min(scene_depth, max(bounds.y, bounds.x))) * min_step_skip_transparent, max_step_skip_transparent);
    float far_search_step = -INFINITY;   // calculated later
    float max_dense_step = lerpFixed(150.0f, 50.0f, s_step_accuracy) * lerp(1.0f, 1.0 + blue_noise.r, 0.15);
    float min_dense_step = lerpFixed(2.5, 1.0, s_step_accuracy) * abs(bounds.y - bounds.x) / num_iterations;

    //if (min_dense_step < 0.f)
    //    return float4(0.f, 1.f, 0.f, 0.f);
    //return float4(scene_depth / (bounds.y - bounds.x), 0.xxx); // seems right

    float depth_test_alpha = 1.0f;

    float iteration = 0.0f;

    float cloud_depth = 0.0f;

    // Trace specific cloud layer 

    float step_hint = 1.0f;
    float prev_step_val = 0.0f;
    const int refine_disabled_steps = -5;
    int refine_steps = refine_disabled_steps;
    int refine_limit = 1;
    float lod = 0.0f; // mip?

    // TODO We should consider haze now
    float3 haze_color = 0.xxx;
    float haze_color_distance = -INFINITY;
    float visibility = 1.0f;

    float iterations_reserve_factor = lerp(1.0f, 1.5f, saturate(remap(s_step_accuracy, 0.7f, 1.0f, 0.0f, 1.0f)));

    float debug_density = 0.f;
    float debug_step = 0.f;

    for (float iteration = 1; iteration <= num_iterations * iterations_reserve_factor && (alpha_trail * depth_test_alpha) > EPSILON && traced_space < bounds.y && out_color.a < EARLY_ALPHA_THRESHOLD && visibility > 0.0001f; iteration++)
    {
        float far_factor = saturate(remap(iteration / num_iterations, 0.4 + 0.1 * blue_noise.r, 0.5, 0.0, 1.0));
        if (iteration / num_iterations > 0.4f && far_search_step <= 0)
        {
            far_search_step = lerpFixed(16.0f, 1.0f, s_step_accuracy) * abs(bounds.y - traced_space) / max(num_iterations - iteration - 16, 1) * (1.0 + 0.1 * lerp(1.0f, blue_noise.r, 0.55));
        }
        float skip_transparent = lerp(near_skip_transparent, far_skip_transparent, far_factor);
        float search_step = lerp(near_search_step, far_search_step, far_factor);
        search_step = max3(search_step, max_dense_step, min_dense_step);


        float dense_step = min3(min_dense_step, max_dense_step, search_step); // > 0.f verified

        float step_val = 0.0f;
        
        if (refine_steps > 0)
            step_val = dense_step;
        else
            step_val = min(lerp(search_step, dense_step, step_hint), iteration * iteration * max_step);


        refine_steps = max(refine_steps - 1, refine_disabled_steps);
        if (iteration > num_iterations)
            step_val = search_step;

        traced_space += step_val;
        // traced_space += clamp(trace_step, min_step, iteration * iteration * max_step);
        // traced_space += trace_step;

        // 5000.f ?
        lod = max(0.0f, traced_space - 5000.0f) / 5000.0f;

        float3 worldspace = ray.pos + ray.dir * traced_space;   // meters

        float normalizeHeight = 0.f;
        // TODO should 
        int cloud_layer_index = 0;
        CloudAttributeOut cloud_layer_attrib = GetCloudLayerAttribute(worldspace, normalizeHeight, lod, cloud_layer_index);

        CloudData cloudData = clouds_params[cloud_layer_index];

        step_hint = cloud_layer_attrib.hint;

        depth_test_alpha = saturate((scene_depth - traced_space) * depth_test_factor);
        //cloud_layer_attrib.Density *= depth_test_alpha;

        debug_density += cloud_layer_attrib.Density;

        if (cloud_layer_attrib.Density < EPSILON)
        {
            prev_step_val = step_val;
            if (step_hint <= 0.5f)
                traced_space += skip_transparent;
            continue;
        }
        if (cloud_layer_attrib.Density > lerp(0.15f, 0.5f, saturate(lod)) && refine_steps <= refine_disabled_steps && refine_limit > 0)
        {
            refine_steps = toInt(min(toInt(1.5 * prev_step_val / dense_step), (num_iterations - iteration - 8)));
            traced_space -= (refine_steps + 0.5) * dense_step;
            traced_space = max(bounds.x, traced_space);
            --refine_limit;
            continue;
        }

        // If refinement wasn't triggered, store the step value we used for this iteration.
        prev_step_val = step_val;

        // lighting
        float3 atmosphereTransmittance = GetAtmosphereTransmittance(worldspace * 0.001f, -sunDirection, true);
        int dirLightIdx = GetDirectLightIdx();
        //float3 sunColor = ce_LightOutSpaceColorsBuffer[dirLightIdx].xyz;
        float3 sunColor = ce_AtmosphereLightData[dirLightIdx].LightIlluminanceOuterSpace;
        float3 sunlightTerm = sunColor * atmosphereTransmittance * cloudData.sun_intensity;

        float3 layer_sun_color = colorSaturation(sunlightTerm, cloudData.sun_saturation);   // 1.f color, 0.f grey

        // TODO cloud's lighting also affected by haze(fog)
        float distance_shading_factor = 0.f;    //-getHazeDistanceFactor(traced_space, 1.5);
        float thundercloud_coef = 1.0f + cloud_layer_attrib.coverage.g * saturate(1.0 - normalizeHeight) * (1.0 - distance_shading_factor);   // storm
        float3 sun_term = layer_sun_color;

        float lighting_occlusion = 0.0f;

        float shadow = 1.0f;
        // TODO if there multiple layer clouds, receive other cloud's shadow
        // shadow = getShadowsFromClouds(layer_index, sample_point.xyz, sun_ray_cp, TEXTURE_OUT(TEX_STATIC_COVERAGE),
        // 	            TEXTURE_OUT(TEX_REGION_MASK), TEXTURE_OUT(TEX_DYNAMIC_COVERAGE));

        sun_term *= shadow;
     
        // Now the light_samples seems right!
        //lighting_occlusion = getCloudShadowNew(worldspace, sunDirection, s_lighting_samples, lighting_noise);
        lighting_occlusion = getCloudShadow(worldspace, sunDirection, lighting_noise);
        lighting_occlusion *= thundercloud_coef;
        sun_term *= getSunLighting(lighting_occlusion, sunDirection, VoL, cloud_layer_attrib.Density, cloudData);

        // remove lighting at far distance for fade with haze
        sun_term *= (1.0f - distance_shading_factor);

        float alpha = (1.0 - exp(-cloud_layer_attrib.Density * cloudData.attenuation_coefficient * step_val)) * alpha_trail + EPSILON;
        alpha_trail -= alpha;

        if (alpha > EPSILON)
            cloud_depth = traced_space;

        cloud_traced_depth = min(cloud_traced_depth, cloud_depth);

        out_color.a += alpha;

        // ambient lighting, we will improve this later
        float3 ambient_term = colorSaturation(avgAmbientLighting, 1.0);
        ambient_term *= lerp(cloudData.ambient_intensity_bottom, cloudData.ambient_intensity_top, pow(normalizeHeight, 0.45)); //ambient_intensity;
        //ambient_term *= s_environment_sky_intensity;
        ambient_term /= thundercloud_coef;


        // unigine apply haze color for every step here

        float3 local_lights_lighing = 0.xxx;
        for (int i = 0; i < shineCloudLightCount; i++)
        {
           local_lights_lighing += CalcShiningCloudLightScattering(ray, worldspace, _ShineCloudLightIndex[i], cloudData);
        }
        local_lights_lighing *= alpha;

        float3 sun_color = sun_term * alpha;

        if (out_color.a > 0.5)
            traced_space_depth_alpha_aware = min(traced_space_depth_alpha_aware, cloud_depth);
        float sun_luma = alpha;

        ambient_term *= sun_luma;
        out_color.rgb += ambient_term + sun_color  + local_lights_lighing;

        debug_step += cloudShadowSteps;
    }

    // Make clouds softer
	out_color.rgb /= (out_color.a + EPSILON);

    out_color.a = saturate(out_color.a / EARLY_ALPHA_THRESHOLD);
    out_color.a = pow(out_color.a, 1.5f);
    out_color.rgb *= out_color.a;

    const float half_transparent_guard_distance = 250.0f;
    float out_depth = min(cloud_traced_depth > half_transparent_guard_distance ? cloud_traced_depth : traced_space_depth_alpha_aware, traced_space_max);

    // TODO maybe should add AP each step like unigine
    // 20250428: use cloud_traced_depth for AP is fine since cloud_traced_depth is a more robust distance
    if (USE_AP)
    {
        out_color.rgb = AddAPEffect(out_color.rgba, cloud_traced_depth, screenUV);
    }

    // since our fog is different with unigine's haze, we add screen fog(haze) 
    if (EnableSFog)
    {
        float3 camPosW = ce_CameraPos.xyz;
        float3 PosWorld = ce_CameraPos.xyz + ray.dir * cloud_traced_depth * 100.f; // unit cm!
        float4 view = mul(ce_View, float4(PosWorld, 1.f));
        float4 sFogColor = GetScreenFogResult(false, PosWorld, camPosW, CloudUseWGS84, view.xyz);

        out_color.xyz = sFogColor.xyz * out_color.w + sFogColor.a * out_color.xyz;
    }


    cloudDepth.x = out_depth;
    cloudDepth.y = scene_native_depth;

    if(any(isnan(out_color)) || any(isinf(out_color)))
    {
        out_color = float4(0.0, 0.0, 0.0, 0.0);
    }

    return out_color;

}

//// retDistance:     x - avgDistance, y - minDistance, z - 0.5 transmittance distance, w - not used
//float4 RayMarchingMainAdaptivesSteps(Ray ray, float2 screenUV, RayMarchingResult marchingResult, out float4 retDistance, out float outStepCnt,
//                                     AtmosphereParametersUE atmosphere)
//{
//    // to km
//    float3 worldPos = ray.pos * 0.001f;
//
//    // tMin tMax to km
//    float tMin = marchingResult.whole.x * 0.001f;
//    float tMax = marchingResult.whole.y * 0.001f;
//
//
//    float sampleT = tMin;
//
//    float cosAngle = dot(normalize(worldPos), normalize(ray.dir));
//    float sinAngle = sqrt(1-cosAngle*cosAngle);
//    float firstHitHorizonDist = sampleT * sinAngle;
//
//    // Adaptive step length to prevent noise when near cloud
//    float stepT = GetAdaptiveStepLength(0, firstHitHorizonDist);
//    float firstHitMipLevel = GetMipLevel(firstHitHorizonDist);
//
//    // Reduce marching steps when far enough
//    uint stepCountUnit = max(1, cloudMarchingSteps);
//    stepCountUnit = remap(firstHitMipLevel, 0, mipLevelVal, cloudMarchingSteps, cloudMarchingStepsFar);
//
//    int nowSeg = 0;
//    
//    // Dithering starting position to prevent aliasing by zhiyu
//    float dither = 1;
//
//    if (SamplingJitter)
//    {
//        float dither1 = random(screenUV + float(ceFrameNumber % floor(ditherFreq))/ditherFreq).x; //GetBlueNoiseSampleOffset(screenUV.x, screenUV.y);
//        dither = lerp(dither, dither1, ditherRange);
//    }
//
//    
//    sampleT += (stepT * dither);
//
//    int dirLightIdx = GetDirectLightIdx();
//    float3 sunColor = ce_LightOutSpaceColorsBuffer[dirLightIdx].xyz;
//    //float3 sunColor = ce_Lights[dirLightIdx].LightColor.xyz;
//    float3 sunDirection = normalize(-ce_Lights[dirLightIdx].LightDirPos.xyz);
//
//    float VoL = dot(ray.dir, sunDirection);
//
//    float3 scatteredLight = 0.xxx;
//    float transmittance = 1.f;
//    float avgDistance = 0.f;
//    float minDistance = 200000000.f;
//    float halfTransmittanceDistance = 200000000.f;
//    float depthWeight = 0.f;
//
//    // GetSkySHDiffuse will be affected by cloud, but GetAtmosphereDistantSkyLight would not
//    float3 avgAmbientLighting = GetAtmosphereDistantSkyLight();
//
//    // Coverage and cloud thickness will affect cloud's rendering
//    float coverage4Shading = 0.f;
//    float cloudHeight = 0.f;
//
//    int cloudType = -1;
//    outStepCnt = 0;
//
//    int  firstSampleIdx = 0;
//    bool bFirstHit      = true;
//
//    float alpha_trail = 1.0f;
//    float out_alpha = 0.f;
//
//    for (int i = 0; i < stepCountUnit; i++)
//    {
//        outStepCnt++;
//
//        // Early out
//        if (sampleT > tMax){
//            break;
//        }
//
//        // World space sample pos, in km unit.
//        float3 samplePos = sampleT * ray.dir + worldPos;
//        
//        float3 atmosphereTransmittance = GetAtmosphereTransmittance(samplePos, -sunDirection, true);
//
//        float3 samplePosMeter = samplePos * 1000.0f;
//
//        float normalizeHeight = 0.f;
//        float stepCloudDensity;
//
//        float4 uvwFade =  CalculateUVW_EdgeFade(samplePosMeter);
//        float3 nowUVW  = uvwFade.xyz;
//
//        // horizon distance for mip level or steps
//        float horizonDist = sampleT * sinAngle;
//        float mipLevel = GetMipLevel(horizonDist);
//
//        CloudAttributeOut cloud_layer_attrib = GetCloudLayerAttribute(samplePosMeter, normalizeHeight);
//        stepCloudDensity = cloud_layer_attrib.Density;
//
//
//        if (stepCloudDensity > DENSITY_THRESHOULD)  
//        {
//            // TODO this is avg depth, neither cloud_traced_depth nor cloud_transparent_depth
//            avgDistance += sampleT * transmittance;
//            depthWeight += transmittance;
//
//            if (bFirstHit)
//            {
//                firstSampleIdx = i;
//                bFirstHit = false;
//            }
//            outStepCnt += cloudShadowSteps;
//            // unit meter
//            float opticalDepth = stepCloudDensity * stepT * 1000.f; // to meter unit.
//
//            // beer's lambert. Siggraph 2017's new step transmittance formula. 
//            // [original code] float stepTransmittance = max(exp(-opticalDepth), exp(-opticalDepth * 0.25) * 0.7);
//            float stepTransmittance = exp(-opticalDepth * extinctionFactor);
//
//           
//            // SunLighting
//            // Amount of sunlight that reaches the sample point through the cloud is the combination of ambient light and attenuated direct light.
//            // float atmosTransEnergy = dot(atmosphereTransmittance, float3(0.3f, 0.59f, 0.11f));
//            float3 sunlightTerm = sunColor * atmosphereTransmittance * sunLightEnergyScale;
//            
//            float3 layer_sun_color = colorSaturation(sunlightTerm, 1.f); // 1.f color, 0.f grey
//
//            // 
//            float distance_shading_factor = 0.01;//getHazeDistanceFactor(traced_space, layer.haze_gradient);
//            float thundercloud_coef = 1.0f + cloud_layer_attrib.coverage.g * saturate(0.5 - normalizeHeight) * (1.0 - distance_shading_factor);// storm 
//            float3 sun_term = layer_sun_color;
//
//            float lighting_occlusion = 0.0f;
//			
//			float shadow = 1.0f;
//            // TODO if there multiple layer clouds, receive other cloud's shadow
//            // shadow = getShadowsFromClouds(layer_index, sample_point.xyz, sun_ray_cp, TEXTURE_OUT(TEX_STATIC_COVERAGE),
//			// 	            TEXTURE_OUT(TEX_REGION_MASK), TEXTURE_OUT(TEX_DYNAMIC_COVERAGE));
//
//            sun_term *= shadow;
//            lighting_occlusion = getCloudShadow(samplePosMeter, sunDirection);
//            lighting_occlusion *= thundercloud_coef;
//         	sun_term *= getSunLighting(lighting_occlusion, sunDirection, -VoL, stepCloudDensity);
//			// sun_term *= TEXTURE_RAMP(CLOUDS_SUN_COLOR_BY_DEPTH, max3(sun_term), layer.layer_id).rgb; // in unigine is 1.xxx
//
//
//            float alpha = (1.0 - exp(- opticalDepth * extinctionFactor)) * alpha_trail + 1e-6;
//			alpha_trail -= alpha;
//			
//            //if (alpha > EPSILON)
//            // 	cloud_depth = traced_space;	
//            //
//            //cloud_traced_depth = min(cloud_traced_depth, cloud_depth);
//			
//			out_alpha += alpha;
//
//            // ambient lighting, we will improve this later
//            float3 ambient_term = colorSaturation(avgAmbientLighting, 0.5);
//            ambient_term *= ambientScale;
//
//            
//            float3 sun_color = sun_term * alpha;
//            float sun_luma = alpha;
//
//            ambient_term *= sun_luma;
//            scatteredLight += ambient_term + sun_color;
//
//            // debug shadow
//            // scatteredLight += lighting_occlusion / cloudShadowSteps;
//
//            transmittance *= stepTransmittance; // this transmittance is ok, using out_alpha is still a problem
//            minDistance = min(minDistance, sampleT);
//            halfTransmittanceDistance = (transmittance < 0.5f) ? min(halfTransmittanceDistance, 200000000.f) : 200000000.f;
//        }
//
//        float StepLenth = 0.0;
//        
//        sampleT -= (stepT * dither);
//        StepLenth = GetAdaptiveStepLength(i - firstSampleIdx, sampleT, false);
//        
//        
//        float factor = (stepCloudDensity > DENSITY_THRESHOULD) ? 1.f : 1.02f;
//        stepT = min(max(stepT * factor, StepLenth), 1.f);
//        sampleT += (stepT * dither);
//        
//        if (transmittance < cloudTransmittanceThres)
//            break;
//
//        sampleT += stepT;
//    }
//
//    // override transmittance using out_alpha and it should be the same
//    // transmittance = out_alpha;
//
//    avgDistance /= max(depthWeight, 1e-7);
//
//    transmittance = transmittance < cloudTransmittanceThres ? 0.0f : transmittance;
//
//    float4 result = float4(scatteredLight, transmittance);
//
//    if (USE_AP)
//    {
//        result = AddAPEffect(scatteredLight, transmittance, avgDistance * 1000.f, screenUV, coverage4Shading);
//    }
//
//
//    retDistance.x = avgDistance * 1000.f;
//    retDistance.y = minDistance * 1000.f;
//    retDistance.z = halfTransmittanceDistance * 1000.f;
//
//    if(any(isnan(result)) || any(isinf(result)))
//    {
//        result = float4(0.0, 0.0, 0.0, 1.0);
//    }
//    return result;
//}

