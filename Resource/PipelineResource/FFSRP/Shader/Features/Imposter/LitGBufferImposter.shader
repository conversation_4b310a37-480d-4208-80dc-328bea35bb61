#pragma vertex VSMain
#pragma pixel PSMain

// #pragma keyword QTANGENT
#pragma keyword CE_INSTANCING
#pragma keyword OUTPUT_DEPTH

// #define CE_INSTANCING
#define NUM_MATERIAL_TEXCOORDS 2
#define CE_USE_DOUBLE_TRANSFORM 1
#define ENABLE_VIEW_MODE_VISUALIZE
#define VERTEX_TYPE VertexType_Vegetation
#define TRANSFER_CUSTOM_DATA
#define USE_WORLDSPACE_NORMALMAP
// #define OUTPUT_DEPTH

#define ADD_VERTEX_OUTPUT \
float2 imposterUV : TEXCOORD8; \
float2 virtualPlaneUV : TEXCOORD9; \

#define ADD_PIXEL_INPUT \
float2 imposterUV : TEXCOORD8; \
float2 virtualPlaneUV : TEXCOORD9; \

#define ADD_SURFACE_INPUT \
float2 imposterUV; \
float2 virtualPlaneUV; \

// _PivotOffset (0, 0, 0, 0)
// _FullSphere false
// _FramesXY 16
// _DefaultMeshSize 350
// _InterpolatePosition 1
// _EnableParallax 1
// _MipForParallax 1
// _TanScaleFix 1
// _BaseColorScale 1
// _PDO 1
#define ADDITIONAL_MTL_PARAM \
float4 _PivotOffset; \
bool _FullSphere; \
float _FramesXY; \
float _DefaultMeshSize; \
float _InterpolatePosition; \
bool _EnableParallax; \
float _MipForParallax; \
float _TanScaleFix; \
float _BaseColorScale; \
float _PDO; \

#include "ShaderLibrary/GlobalModelVariables.hlsl"
#include "ShaderLibrary/VertexFactoryHeader.hlsl"
#include "Material/Lit/LitUEVariables.hlsl"
#include "Material/Lit/LitCommonStruct.hlsl"
#include "ImposterFunction.hlsl"

void TransferCustomDataVS(in VSInput vsInput, inout VSOutput vsOutput)
{
}

void TransferCustomDataPS(in VSOutput vsOutput, inout PSInput psInput)
{
	psInput.imposterUV = vsOutput.imposterUV;
	psInput.virtualPlaneUV = vsOutput.virtualPlaneUV;
}

void TransferCustomDataSurface(in PSInput psInput, inout SurfaceInput surfaceInput)
{
	surfaceInput.imposterUV = psInput.imposterUV;
	surfaceInput.virtualPlaneUV = psInput.virtualPlaneUV;
}

#ifdef CE_INSTANCING
#define WORLD_POSITION_OFFSET_PREVIOUS
float3 GetPreviousWorldPositionOffset(float3 Cur_or_Prev_worldPosition, inout VSOutput vOut, in VSInput vIn, in FoliageObjectSceneData instance)
{
    return GetWPOImpl(Cur_or_Prev_worldPosition, vOut, vIn, instance, true);
}

#define WORLD_POSITION_OFFSET
float3 GetWorldPositionOffset(float3 Cur_or_Prev_worldPosition, inout VSOutput vOut, in VSInput vIn, in FoliageObjectSceneData instance)
{
    return GetWPOImpl(Cur_or_Prev_worldPosition, vOut, vIn, instance, false);
}
#endif

#include "/Material/Lit/SurfaceShaderIncludes.hlsl"


#ifdef CE_INSTANCING
SurfaceData GetSurfaceData(ObjectSceneData objectData, SurfaceInput input)
#else
SurfaceData GetSurfaceData(SurfaceInput input)
#endif
{
	SurfaceData surfaceData = (SurfaceData)0;

#ifdef CE_INSTANCING
    float4x4 invTransposeWorld = objectData.ce_InvTransposeWorld;
    float4x4 worldMat = objectData.ce_World;
#else
    float4x4 invTransposeWorld = 0;
    float4x4 worldMat = 0;
#endif

    float2 uv = input.imposterUV;
    float2 framesUV = floor(uv) / _FramesXY + input.virtualPlaneUV;
    // float2 framesUV = floor(uv) / _FramesXY + input.uv * 10;
    // float2 framesUV = input.virtualPlaneUV;
    // todo: add parallax

    // todo: add blend weights

    // float4 baseColor = _BaseMap.SampleLevel(ce_Sampler_Clamp, framesUV, 0) * _BaseColor;
    float4 baseColor = _BaseMap.SampleBias(ce_Sampler_Clamp, framesUV, _TextureSampleBias) * _BaseColor;
    float4 normal = _NormalMap.SampleBias(ce_Sampler_Clamp, framesUV, _TextureSampleBias);
    float3 normalWS = (normal.rgb - 0.5) * 2;
    normalWS = normalize(mul(invTransposeWorld, float4(normalWS, 0)).xyz);

    surfaceData.baseColor = baseColor.xyz * _BaseColorScale;
    surfaceData.normalTS = normalWS.xyz;

    float clipValue = baseColor.a;
    surfaceData.opacity = surfaceData.opacityMask = clipValue;
    surfaceData.opacityMaskClip = _AlphaClip;
	surfaceData.materialType = MATERIAL_TYPE;
	surfaceData.roughness = 1 - _Smoothness;
    surfaceData.specular = _Specular;
    surfaceData.subsurfaceColor = _SubsurfaceColor.rgb;

    // pixel depth offset(PDO)
#ifdef OUTPUT_DEPTH
    float depth = normal.a;
    depth = 1 - depth;
	float3 cameraVector = normalize(ce_CameraPos.xyz - input.positionWS);
    float3 cameraDirVector = mul((float3x3)worldMat, float3(0, 0, 1));
    float VdotD = abs(dot(cameraVector, cameraDirVector));
    float3 objScaleXYZ = GetObjectScaleXYZ(worldMat);
    float pdoSize = _PDO * VdotD * objScaleXYZ.x * _DefaultMeshSize;
    float pdo = depth * pdoSize;
    float4 positionCS = mul(ce_Projection, mul(ce_View, float4(input.positionWS, 1.0f)));
 	float deviceDepth = positionCS.z / positionCS.w;
    deviceDepth = min(deviceDepth, positionCS.z / (positionCS.w + pdo));
    surfaceData.outDepth = deviceDepth;
#endif

    return surfaceData;
}

#include "Material/Lit/Lit.hlsl"
#include "RenderPass/ShaderPassGBuffer.hlsl"