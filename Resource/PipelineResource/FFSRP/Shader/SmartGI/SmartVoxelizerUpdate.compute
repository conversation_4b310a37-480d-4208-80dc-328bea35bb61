#pragma compute ClearVoxelizeSceneCS
#pragma compute ClearVoxelizeSceneCSWithTileMask
#pragma compute ClearVisBuffersCS
#pragma compute BuildUpdateGridTilesCS

//#pragma enable debug_symbol
#define USE_CLIPMAP_WRAP 1
#include "SmartVoxelizeCommon.hlsl"

#define CLEAR_WITH_TILEMASK 0

#ifndef THREADGROUP_SIZE
#define THREADGROUP_SIZE 8
#endif

RWTexture3D<uint> RWNormalTexture;
RWTexture3D<uint> RWAlbedoTexture;
RWTexture3D<uint> RWEmissiveTexture;
RWTexture3D<float> RWOpacityTexture;
RWTexture3D<uint> RWOpacityCompactTexture;

StructuredBuffer<uint> UpdateTileBuffer;

void ClearVoxelizeTexture(uint3 TexelCoord)
{
    float4 clearColor = float4(0.0, 0.0, 0.0, 0.0);
	RWAlbedoTexture[TexelCoord] = 0;
	RWNormalTexture[TexelCoord] = 0;
	RWEmissiveTexture[TexelCoord] = 0;
	#if CLEAR_WITH_TILEMASK
	//RWAlbedoTexture[TexelCoord] = float4(1.0,1.0,0.0,1.0);
	#endif
}

[numthreads(THREADGROUP_SIZE, THREADGROUP_SIZE, THREADGROUP_SIZE)]
void ClearVoxelizeSceneCS(uint GroupIndex:SV_GroupIndex,
	uint3 GroupId : SV_GroupID,
    uint3 GroupThreadId : SV_GroupThreadID,
	uint3 DispatchThreadId : SV_DispatchThreadID)
{
    if(all(DispatchThreadId < ClipmapResolution))
    {
        uint3 TexelCoord = DispatchThreadId;
    	TexelCoord.y += ClipmapIndex * ClipmapResolution.y;
		ClearVoxelizeTexture(TexelCoord);
		TexelCoord.z = TexelCoord.z / 6;
		RWOpacityTexture[TexelCoord] = 0.f;
		uint3 CompactTexelCoord = TexelCoord / 2;
		RWOpacityCompactTexture[CompactTexelCoord] = 0;
    }
}

[numthreads(THREADGROUP_SIZE, 1, 1)]
void ClearVoxelizeSceneCSWithTileMask(uint GroupIndex:SV_GroupIndex,
	uint3 GroupId : SV_GroupID,
    uint3 GroupThreadId : SV_GroupThreadID,
	uint3 DispatchThreadId : SV_DispatchThreadID)
{
	uint UpdateTileIndex = GroupId.x;
	uint PackedUpdateTile = UpdateTileBuffer[UpdateTileIndex];

	uint3 UpdateTileCoord;
	UpdateTileCoord.x = PackedUpdateTile & 0xFF;
	UpdateTileCoord.y = (PackedUpdateTile >> 8) & 0xFF;
	UpdateTileCoord.z = (PackedUpdateTile >> 16) & 0xFF;

	UpdateTileCoord.y += ClipmapIndex * ClipmapResolution.y;

	UNROLL
	for(uint Direction = 0;Direction < 6;++Direction)
	{
		uint3 TexelCoord = UpdateTileCoord;
		TexelCoord.z = UpdateTileCoord.z * 6 + Direction;
		ClearVoxelizeTexture(TexelCoord);
	}

	RWOpacityTexture[UpdateTileCoord] = 0.f;
	uint3 CompactTexelCoord = UpdateTileCoord / 2;
	RWOpacityCompactTexture[CompactTexelCoord] = 0;
}

RWStructuredBuffer<uint> RWClearVisBufferIndirectArgBuffer;
[numthreads(1, 1, 1)]
void ClearVisBuffersCS(
	uint3 GroupId : SV_GroupID,
	uint3 DispatchThreadId : SV_DispatchThreadID,
	uint3 GroupThreadId : SV_GroupThreadID)
{
	if (DispatchThreadId.x == 0)
	{
		RWClearVisBufferIndirectArgBuffer[0] = 0;
		RWClearVisBufferIndirectArgBuffer[1] = 1;
		RWClearVisBufferIndirectArgBuffer[2] = 1;
	}
}

RWStructuredBuffer<uint> RWGridTileBuffer;

StructuredBuffer<float4> UpdateBoundsBuffer;

cbuffer UpdateGridTiles
{
	uint NumUpdateBounds;
	float3 GridCenter;
	float3 GridExtent;
	float3 TileWorldExtent;
}

groupshared uint GroupInsideBounds;

Texture3D<uint> NormalTexture;
[numthreads(THREADGROUP_SIZE, 1, 1)]
void BuildUpdateGridTilesCS(
	uint3 GroupId : SV_GroupID,
	uint3 DispatchThreadId : SV_DispatchThreadID,
	uint3 GroupThreadId : SV_GroupThreadID)
{
	uint3 GridCoord = GroupId.xyz;
	uint ThreadId = GroupThreadId.x;

	float3 GridWorldCenter = GetClipmapToWorldPosition(ClipmapIndex,GridCoord,0,GridCenter,GridExtent,TileWorldExtent.x * 2);

	GridWorldCenter += TileWorldExtent;

	if (ThreadId == 0)
	{
		GroupInsideBounds = 0;
	}

	GroupMemoryBarrierWithGroupSync();

	LOOP
	for (uint UpdateBoundsIndex = ThreadId; UpdateBoundsIndex < NumUpdateBounds; UpdateBoundsIndex += THREADGROUP_SIZE)
	{
		float3 UpdateBoundsCenter = UpdateBoundsBuffer[UpdateBoundsIndex * 2 + 0].xyz;
		float3 UpdateBoundsExtent = UpdateBoundsBuffer[UpdateBoundsIndex * 2 + 1].xyz;

		float DistanceSq = ComputeSquaredDistanceBetweenAABBs(UpdateBoundsCenter, UpdateBoundsExtent, GridWorldCenter, TileWorldExtent);
		if (DistanceSq <= 0.0f)// && 	NormalTexture.Load(uint4(GridCoord.x, GridCoord.y, GridCoord.z * 6, 0)) > 0)
		{
			// Metal compiler issue: it requires `+=` instead of `=` to record the value as of UE 5.0
			GroupInsideBounds += 1;
			break;
		}
	}

	GroupMemoryBarrierWithGroupSync();

	if (all(GridCoord < ClipmapResolution) && ThreadId == 0)
	{
		if (GroupInsideBounds)
		{
			uint DestIndex;
			InterlockedAdd(RWClearVisBufferIndirectArgBuffer[0], 1, DestIndex);
			RWGridTileBuffer[DestIndex] = GridCoord.x | (GridCoord.y << 8) | (GridCoord.z << 16);
		}
	}
}