#pragma compute SmartDirectLightingCS
#pragma compute SmartEmissiveLightingCS

//#pragma enable debug_symbol

#define DISABLE_GLOBAL_DEFINES

#define USE_CLIPMAP_WRAP 1

#include "Common.hlsl"
#include "SmartVoxelizeCommon.hlsl"
#include "../ShaderLibrary/GlobalModelVariables.hlsl"
#include "SmartTracingCommon.hlsl"
#include "../Lighting/Shadow/Shadow.hlsl"

#define IS_MATERIAL_SHADER 0

#ifndef THREADGROUP_SIZE
#define THREADGROUP_SIZE 8
#endif

#define DEBUG_DIRECT_LIGHTING 0
#define DEBUG_SHOW_TRACE 0
#define DEBUG_SHOW_HIT 0

Texture3D<uint> EmissiveTexture;
StructuredBuffer<uint> VoxelVisBuffer;
RWTexture3D<float4> RWLightingTexture;

cbuffer cbTraceVoxels
{
    float MaxTraceDistance;
    float StepFactor;
    float MinTraceDistance;
    float SurfaceBias;
    uint lightCnt; // ce_lights count
}

float NormalWeightedAttenuation(float3 WorldNormal, float3 LightDirection)
{
    float3 weight = WorldNormal * WorldNormal;
    float rDotL = dot(float3(1.0, 0.0, 0.0), LightDirection);
    float uDotL = dot(float3(0.0, 1.0, 0.0), LightDirection);
    float fDotL = dot(float3(0.0, 0.0, 1.0), LightDirection);

    rDotL = WorldNormal.x > 0.0 ? max(rDotL, 0.0) : max(-rDotL, 0.0);
    uDotL = WorldNormal.y > 0.0 ? max(uDotL, 0.0) : max(-uDotL, 0.0);
    fDotL = WorldNormal.z > 0.0 ? max(fDotL, 0.0) : max(-fDotL, 0.0);

    return rDotL * weight.x + uDotL * weight.y + fDotL * weight.z;
}

bool LookupClipmapIndex(float3 SamplePos, inout uint SampleClipmapIndex)
{
    bool bOutsideValidRegion = any(SamplePos > ClipmapWorldCenter[SampleClipmapIndex] + ClipmapWorldExtent[SampleClipmapIndex]) || any(SamplePos < ClipmapWorldCenter[SampleClipmapIndex] - ClipmapWorldExtent[SampleClipmapIndex]);
    while (bOutsideValidRegion && SampleClipmapIndex + 1 < NumClipmapLevels)
    {
        SampleClipmapIndex++;
        bOutsideValidRegion = any(SamplePos > ClipmapWorldCenter[SampleClipmapIndex] + ClipmapWorldExtent[SampleClipmapIndex]) || any(SamplePos < ClipmapWorldCenter[SampleClipmapIndex] - ClipmapWorldExtent[SampleClipmapIndex]);
    }
    return !bOutsideValidRegion;
}

float GetClipmapVoxelSize(uint SampleClipmapIndex)
{
    return (2 * ClipmapWorldExtent[SampleClipmapIndex] / ClipmapResolution).x;
}

float VoxelTraceShadow(float3 WorldPosition, float3 TraceDir, float MinTraceDistance, float MaxTraceDistance)
{
    float3 SamplePos = WorldPosition;

    uint SampleClipmapIndex = 0;
    if (!LookupClipmapIndex(SamplePos, SampleClipmapIndex))
    {
        return 1.0f;
    }

    float ShadowTraceStep = GetClipmapVoxelSize(SampleClipmapIndex);
    float dst = ShadowTraceStep;
    float3 ShadowTraceDirection = TraceDir;

    WorldPosition = WorldPosition + ShadowTraceDirection * MinTraceDistance;
    WorldPosition = SkipSelfVoxel(WorldPosition, ShadowTraceDirection, ShadowTraceStep, SampleClipmapIndex);

    SamplePos = ShadowTraceDirection * dst + WorldPosition;

    bool bHitSomeThing = false;

    bool bFirstUnHit = !ENABLE_FIRST_VOXEL_UNHIT;
    LOOP
    for (uint StepIndex = 0; StepIndex < 256 && !bHitSomeThing; ++StepIndex)
    {
        if (!LookupClipmapIndex(SamplePos, SampleClipmapIndex))
        {
            break;
        }

        bool bHit = SampleVoxelCardValid(SamplePos, SampleClipmapIndex);
        if (!bFirstUnHit)
        {
            bFirstUnHit = !bHit;
        }
        else
        {
            bHitSomeThing = bHit;
        }

#if DEBUG_SHOW_TRACE
            for(uint i = 0; i < 6; i++)
            {
                int3 SamplePosVoxel = GetGlobalVoxelCoord(SampleClipmapIndex,SamplePos);
                uint3 AxisUV = GetClipmapCoordinate(SampleClipmapIndex,i,SamplePosVoxel);
                float4 DebugColor = bHitSomeThing ? float4(0.0, 1.0, 0.0, 1.0) : float4(0.1, 0.1, 0.1, 0.1);
                RWLightingTexture[AxisUV] = RWLightingTexture[AxisUV] + DebugColor;
            }
#endif

        if (bHitSomeThing || dst >= MaxTraceDistance)
        {
            break;
        }

        ShadowTraceStep = GetClipmapVoxelSize(SampleClipmapIndex);
        dst += ShadowTraceStep;
        SamplePos = ShadowTraceDirection * dst + WorldPosition;
    };

    return bHitSomeThing ? 0.0f : 1.0f;
}

float VoxelTraceShadowDistance(float3 WorldPosition, float3 TraceDir, float MinTraceDistance, float MaxDistance)
{
    float3 SamplePos = WorldPosition;

    uint SampleClipmapIndex = 0;
    if (!LookupClipmapIndex(SamplePos, SampleClipmapIndex))
    {
        return 1.0f;
    }

    float ShadowTraceStep = GetClipmapVoxelSize(SampleClipmapIndex);
    float dst = ShadowTraceStep;
    float3 ShadowTraceDirection = TraceDir;

    WorldPosition = WorldPosition + ShadowTraceDirection * MinTraceDistance;
    WorldPosition = SkipSelfVoxel(WorldPosition, ShadowTraceDirection, ShadowTraceStep, SampleClipmapIndex);

    SamplePos = ShadowTraceDirection * dst + WorldPosition;

    bool bHitSomeThing = false;

    bool bFirstUnHit = !ENABLE_FIRST_VOXEL_UNHIT;
    LOOP
    for (uint StepIndex = 0; StepIndex < 256 && !bHitSomeThing; ++StepIndex)
    {
        if (!LookupClipmapIndex(SamplePos, SampleClipmapIndex))
        {
            break;
        }
        if (dst >= MaxDistance)
        {
            break;
        }

        bool bHit = SampleVoxelCardValid(SamplePos, SampleClipmapIndex);
        if (!bFirstUnHit)
        {
            bFirstUnHit = !bHit;
        }
        else
        {
            bHitSomeThing = bHit;
        }

        if (bHitSomeThing)
        {
            break;
        }

        ShadowTraceStep = GetClipmapVoxelSize(SampleClipmapIndex);
        dst += ShadowTraceStep;
        SamplePos = ShadowTraceDirection * dst + WorldPosition;
    };

    return bHitSomeThing ? 0.0f : 1.0f;
}

float Get2DEllipseVerCoordinate(float2 hor_ver_axis, float hor_val)
{
    return sqrt(hor_ver_axis.y * hor_ver_axis.y * (1.0 - hor_val * hor_val / (hor_ver_axis.x * hor_ver_axis.x)));
}

float Get2DEllipseHorCoordinate(float2 hor_ver_axis, float ver_val)
{
    return sqrt(hor_ver_axis.x * hor_ver_axis.x * (1.0 - ver_val * ver_val / (hor_ver_axis.y * hor_ver_axis.y)));
}

// TODO(chopperlin) : need to align with local light cache later
//attenuationParam: ce_LightAttenuation[lightIndex]
//lightWorldPos:    lightDirPos.xyz
//range:            lightDirPos.w;
//overflowLength:   lightTilePos.w
//fadeIntensity:    ce_LightSpotDirections[lightIndex].w
//distExp:          1.f
float GetSpotLightAtten(
    float3 aimWorldPos, float3 lightWorldPos, float3 lightSpotDir,
    float4 attenuationParam, float range, float overflowLength, float fadeIntensity, float distExp)
{
    // Get matrix from world to light space
    matrix world_2_light_m = GetTransMatrixInverseFromEulerAndPos(lightSpotDir, lightWorldPos);

    // Get vertex xoz-plane & yoz-plane in light space
    float3 vertex_light_space = mul(world_2_light_m, float4(aimWorldPos, 1.0)).xyz;

    // Get cone range in light space forward
    float forward_zaxis = dot(float3(0.0, 0.0, 1.0), vertex_light_space);
    float forward_zaxis_theta = dot(float3(0.0, 0.0, 1.0), normalize(vertex_light_space));

    // LightAtten = Float4(spotInnerHorCosHalfAngle, spotInnerVerCosHalfAngle, spotOuterHorCosHalfAngle, spotOuterVerCosHalfAngle)
    float inner_hor_angle = acos(attenuationParam.x);
    float inner_ver_angle = acos(attenuationParam.y);
    float2 inner_hor_ver_axis = float2(min(forward_zaxis, range) * tan(inner_hor_angle), min(forward_zaxis, range) * tan(inner_ver_angle));

    float outer_hor_angle = acos(attenuationParam.z);
    float outer_ver_angle = acos(attenuationParam.w);
    float2 outer_hor_ver_axis = float2(min(forward_zaxis, range) * tan(outer_hor_angle), min(forward_zaxis, range) * tan(outer_ver_angle));

    if (forward_zaxis_theta < attenuationParam.z || forward_zaxis_theta < attenuationParam.w)
    {
        return 0.0f;
    }

    // Check & Refresh external spot overflow raidus
    if (overflowLength > 0.0)
    {
        if (forward_zaxis > range && forward_zaxis < (range + overflowLength))
        {
            float scale_ratio = outer_hor_ver_axis.x / inner_hor_ver_axis.x;

            // Grab x-axis coordinate in light space, which equals INNER & OUTER ellipse hor-major length
            float2 outer_exell_hor_axis = float2(outer_hor_ver_axis.x, overflowLength);
            outer_hor_ver_axis.x = Get2DEllipseHorCoordinate(outer_exell_hor_axis, forward_zaxis - range);

            float2 inner_exell_hor_axis = float2(inner_hor_ver_axis.x, overflowLength);
            inner_hor_ver_axis.x = Get2DEllipseHorCoordinate(inner_exell_hor_axis, forward_zaxis - range);

            // Grab y-axis coordinate in light space, which equals INNER & OUTER ellipse ver-major length
            float2 outer_exell_ver_axis = float2(overflowLength, outer_hor_ver_axis.y);
            outer_hor_ver_axis.y = Get2DEllipseVerCoordinate(outer_exell_ver_axis, forward_zaxis - range);

            float2 inner_exell_ver_axis = float2(overflowLength, inner_hor_ver_axis.y);
            inner_hor_ver_axis.y = Get2DEllipseVerCoordinate(inner_exell_ver_axis, forward_zaxis - range);
        }
    }

    // Establish INNER & OUTER ellipse forumla for later lerp intensity

    // Find INNER & OUTER eccentric angle in xoy-axis
    float inner_ecc_angle = atan2(vertex_light_space.y * inner_hor_ver_axis.x, vertex_light_space.x * inner_hor_ver_axis.y); //tan(vertex_ell_angle) * inner_hor_ver_axis.x / inner_hor_ver_axis.y;
    float outer_ecc_angle = atan2(vertex_light_space.y * outer_hor_ver_axis.x, vertex_light_space.x * outer_hor_ver_axis.y); //tan(vertex_ell_angle) * outer_hor_ver_axis.x / outer_hor_ver_axis.y;

    // Find INNER & OUTER ellipse intersect point
    float2 inner_touch_v = float2(cos(inner_ecc_angle) * inner_hor_ver_axis.x, sin(outer_ecc_angle) * inner_hor_ver_axis.y);
    float2 outer_touch_v = float2(cos(outer_ecc_angle) * outer_hor_ver_axis.x, sin(outer_ecc_angle) * outer_hor_ver_axis.y);

    //
    float2 vector_inner_outer = outer_touch_v - inner_touch_v;
    float2 vector_vertex_outer = vertex_light_space.xy - outer_touch_v;

    // outer range check
    float inside_cone_flag = step(vertex_light_space.x * vertex_light_space.x / (outer_hor_ver_axis.x * outer_hor_ver_axis.x) + vertex_light_space.y * vertex_light_space.y / (outer_hor_ver_axis.y * outer_hor_ver_axis.y), 1.0);
    // middle range check
    float stuck_cone_flag = inside_cone_flag * step(1.0,
                                                    vertex_light_space.x * vertex_light_space.x / (inner_hor_ver_axis.x * inner_hor_ver_axis.x) + vertex_light_space.y * vertex_light_space.y / (inner_hor_ver_axis.y * inner_hor_ver_axis.y));

    float len_inner_outer = length(vector_inner_outer);
    float len_vertex_outer = length(vector_vertex_outer);
    float len_vertex = length(vertex_light_space.xy);
    float len_inner = length(inner_touch_v);

    float inner_threshold_angle = atan2(len_inner, forward_zaxis);
    float outer_threshold_angle = atan2(len_inner_outer + len_inner, forward_zaxis);
    float vertex_cur_angle = atan2(len_vertex, forward_zaxis);

    // Get angle fade intensity with bezier smooth
    float2 point_origin = float2(0.0, 1.0);
    float2 point_middle = float2(inner_threshold_angle, fadeIntensity);
    float2 point_final = float2(outer_threshold_angle, 0.0);

    float bezier_curve_t = clamp(vertex_cur_angle / outer_threshold_angle, 0.0, 1.0);
    float2 angle_fade_intensity = point_origin * Pow2(1 - bezier_curve_t) + 2 * bezier_curve_t * (1 - bezier_curve_t) * point_middle + Pow2(bezier_curve_t) * point_final;

    angle_fade_intensity.y = step(vertex_cur_angle, outer_threshold_angle) * angle_fade_intensity.y * angle_fade_intensity.y;

    // Get normal in light space
    // float3 normalize_light_space_n = mul(world_2_light_m,float4(bsdfData.normalWS, 0.0)).xyz;
    // float l_dot_n       = dot(float3(0.0, 0.0, 1.0), normalize_light_space_n * -1.0);

    // Get Spot light color with intensity
    float distance_fade_intensity = clamp(pow(range + overflowLength - forward_zaxis, max(distExp, 1.0)) / pow(range + overflowLength, max(distExp, 1.0)), 0.0, 1.0);
    return angle_fade_intensity.y * distance_fade_intensity;
}

SHADER_CONST(bool, USE_LOCAL_LIGHT_SHADOW_CACHE, true);

float3 GetIrradianceFromAnyLight(in uint lightIdx, in float3 WorldPosition, in float3 TranslatedWorldPosition, in float4 WorldNormal)
{
    float lightVisibility = 0.f, CombinedAttenuation = 1.f, NoL = 0.f;
    float3 Irradiance = 0.f;

    LightInfo lightInfo = ce_Lights[lightIdx];
    float4 lightPos = lightInfo.LightDirPos;
    bool isDirectionalLight = lightPos.w == 0;
    bool isSpotLight = lightInfo.LightSpotDirection.w != 0;
    int shadowDataIndex = lightInfo.LightShadowDataIndice;
    float3 VoxelSize = ClipmapVoxelSizeAndRadius[ClipmapIndex].xyz;

    if (isDirectionalLight)
    {
        float3 L = lightInfo.LightDirPos.xyz;
        float3 ToLight = -L;

        if (USE_LOCAL_LIGHT_SHADOW_CACHE)
        {
            // float Stride = max(VoxelSize.x * 2.f * (1.f - max(dot(WorldNormal.xyz, ToLight), 0.f)), VoxelSize.x * .5f);
            // float3 WorldSpaceOffset = ToLight * Stride;
            // lightVisibility = GetDirectionalShadowAttenuation_Direct(WorldPosition + WorldSpaceOffset, shadowDataIndex, lightInfo.VirtualShadowMapId);

            lightVisibility = ConeTraceVoxelShadowNoPositionSS(WorldPosition, ToLight, MinTraceDistance, MaxTraceDistance);
        }
        else
        {
            lightVisibility = ConeTraceVoxelShadowNoPositionSS(WorldPosition, ToLight, MinTraceDistance, MaxTraceDistance);
        }
        NoL = NormalWeightedAttenuation(WorldNormal.xyz, ToLight);
    }
    else if (isSpotLight)
    {
        // trans ce_lights params to old params here
        float3 LightDataWorldTranslation = lightInfo.LightDirPos.xyz;
        float3 LightDataDirection = lightInfo.LightSpotDirection.xyz;
        float4 SpotLightAttenuation = lightInfo.LightAttenuation;
        float SpotLightOverflowLength = lightInfo.LightTilePos.w;
        float SpotLightFadeIntensity = lightInfo.LightSpotDirection.w;
        float SpotLightDistanceExp = lightInfo.LightColor.w;
        float LightDataRange = lightInfo.LightDirPos.w;

        float DistanceToLight = distance(TranslatedWorldPosition, LightDataWorldTranslation);
        if (DistanceToLight >= LightDataRange)
        {
            return Irradiance;
        }

        CombinedAttenuation = GetSpotLightAtten(WorldPosition, LightDataWorldTranslation, LightDataDirection, SpotLightAttenuation, LightDataRange, SpotLightOverflowLength, SpotLightFadeIntensity, SpotLightDistanceExp);

        float3 L = normalize(TranslatedWorldPosition - LightDataWorldTranslation);
        float3 ToLight = -L;
        if (CombinedAttenuation > 0)
        {
            if (USE_LOCAL_LIGHT_SHADOW_CACHE)
            {
                float Stride = clamp(VoxelSize.x * 2.f * (1.f - max(dot(WorldNormal.xyz, ToLight), 0.f)), VoxelSize.x * .5f, DistanceToLight * .95f);
                float3 WorldSpaceOffset = ToLight * Stride;

                lightVisibility = GetSpotShadowAttenuation_Direct(WorldPosition + WorldSpaceOffset, lightInfo.LightDirPos.xyz, lightInfo.LightTilePos.xyz, _ShadowDatas[shadowDataIndex]);
            }
            else
            {
                lightVisibility = ConeTraceVoxelShadowNoPositionSS(WorldPosition, ToLight, MinTraceDistance, min(MaxTraceDistance, DistanceToLight));
            }
        }
        NoL = NormalWeightedAttenuation(WorldNormal.xyz, ToLight);
    }
    else // point light
    {
        // trans ce_lights params to old params here
        float3 LightDataWorldTranslation = lightInfo.LightDirPos.xyz;
        float LightDataRange = sqrt(lightInfo.LightAttenuation.w);

        float3 L = normalize(TranslatedWorldPosition - LightDataWorldTranslation);
        float3 ToLight = -L;
        float DistanceToLight = distance(TranslatedWorldPosition, LightDataWorldTranslation);
        if (DistanceToLight >= LightDataRange)
        {
            return Irradiance;
        }

        CombinedAttenuation = 1 - Square(DistanceToLight) / Square(LightDataRange);

        if (USE_LOCAL_LIGHT_SHADOW_CACHE)
        {
            float Stride = clamp(VoxelSize.x * 2.f * (1.f - max(dot(WorldNormal.xyz, ToLight), 0.f)), VoxelSize.x * .5f, DistanceToLight * .95f);
            float3 WorldSpaceOffset = ToLight * Stride;

            lightVisibility = GetPointShadowAttenuation_Direct(WorldPosition + WorldSpaceOffset, lightInfo.LightDirPos.xyz, _ShadowDatas[shadowDataIndex]);
        }
        else
        {
            lightVisibility = ConeTraceVoxelShadowNoPositionSS(WorldPosition, ToLight, MinTraceDistance, min(MaxTraceDistance, DistanceToLight));
        }

        NoL = NormalWeightedAttenuation(WorldNormal.xyz, ToLight);
    }

    if (CombinedAttenuation > 0 && lightVisibility > 0.0f)
    {
        if (NoL > 0)
        {
            NoL = saturate(NoL);
            Irradiance.rgb = lightInfo.LightColor.rgb * (CombinedAttenuation * NoL * lightVisibility);
            //Irradiance = bShadowFactorValid ? float3(0, 1, 0) : float3(0.2f, 0.0f, 0.0f);
        }
    }

    return Irradiance;
}

// Direct Lighting Injection including directional/spot/point light
[numthreads(THREADGROUP_SIZE, 1, 1)]
void SmartDirectLightingCS(
    uint3 GroupId : SV_GroupID,
    uint3 DispatchThreadId : SV_DispatchThreadID,
    uint3 GroupThreadId : SV_GroupThreadID)
{
    uint3 ClipmapGridResolution = ClipmapResolution;
    ClipmapGridResolution.z = 6 * ClipmapResolution.z;

    uint3 DispatchCoord = UnpackVoxelInfo(VoxelVisBuffer[DispatchThreadId.x]);

    if (any(DispatchCoord >= ClipmapGridResolution))
        return;

    uint3 TexCoord = DispatchCoord + uint3(0, ClipmapIndex * ClipmapResolution.y, 0);

    uint3 VoxelCoord = uint3(DispatchCoord.x, DispatchCoord.y, DispatchCoord.z / 6);
    VoxelCoord = clamp(VoxelCoord, 0, GetClipmapResolution() - 1);

    uint Direction = DispatchCoord.z % 6;

    float voxelSize = ClipmapVoxelSizeAndRadius[ClipmapIndex].x;
    // TODO: Two WorldPosition
    // float3 WorldPosition = GetClipmapToWorldPosition(ClipmapIndex, VoxelCoord, Direction, ClipmapWorldCenter[ClipmapIndex], ClipmapWorldExtent[ClipmapIndex], voxelSize);
    float3 WorldPosition = GetClipmapToWorldPosition(ClipmapIndex, VoxelCoord, Direction);

    // RWLightingTexture[DispatchCoord].rgb = VoxelCoord / 64.f;
    // RWLightingTexture[DispatchCoord].rgb = WorldPosition / 1e3f;
    // RWLightingTexture[DispatchCoord].a = 1.f;
    // return;

    float4 WorldNormal = UnpackRGBA8Normal(NormalTexture.Load(uint4(TexCoord, 0)));
    WorldNormal.xyz = normalize(WorldNormal.xyz * 2.0f - 1.0);
    WorldPosition += .5f * voxelSize * WorldNormal;

    //float3 TranslatedWorldPosition = WorldPosition + LWCHackToFloat(PrimaryView.PreViewTranslation);
    float3 TranslatedWorldPosition = WorldPosition;

    bool bShadowFactorComplete = false;
    float ReceiverBias = 0.0f;

    float4 DiffuseColor = UnpackRGBA8Color(AlbedoTexture.Load(uint4(TexCoord, 0)));

    // Write alpha to 0 when voxel is invalid(actually it's a redundant statement, cause compact voxel pass will clear all invalid voxels, but we add here for preventing unknown behavior)
    if (DiffuseColor.a <= 0.f)
    {
        RWLightingTexture[TexCoord] = float4(0.f, 0.f, 0.f, 0.f);
        return;
    }

    float3 Irradiance = 0.f;

    //-----------------common code for directional/point/spot lights---------------------------
    for (uint lightIdx = 0; lightIdx < lightCnt; lightIdx++)
    {
        Irradiance += GetIrradianceFromAnyLight(lightIdx, WorldPosition, TranslatedWorldPosition, WorldNormal);
    }

#if DEBUG_VXGI_DIFFUSE_LAMBERT_TEST
    Irradiance *= Diffuse_Lambert(DiffuseColor.xyz);
#else
    Irradiance *= DiffuseColor.rgb;
#endif
    // Write alpha to 1 in any case
    RWLightingTexture[DispatchCoord] = float4(Irradiance, 1.f);
}

[numthreads(THREADGROUP_SIZE, 1, 1)]
void SmartEmissiveLightingCS(
    uint3 GroupId : SV_GroupID,
    uint3 DispatchThreadId : SV_DispatchThreadID,
    uint3 GroupThreadId : SV_GroupThreadID)
{
    uint3 ClipmapGridResolution = ClipmapResolution;
    ClipmapGridResolution.z = 6 * ClipmapResolution.z;

    uint3 DispatchCoord = UnpackVoxelInfo(VoxelVisBuffer[DispatchThreadId.x]);

    if (all(DispatchCoord < ClipmapGridResolution))
    {
        uint3 TexelCoord = DispatchCoord + uint3(0, ClipmapIndex * ClipmapResolution.y, 0);
        float4 EmissiveColor = UnpackRGBA8Color(EmissiveTexture.Load(uint4(TexelCoord, 0)));
        RWLightingTexture[DispatchCoord] = RWLightingTexture[DispatchCoord] + EmissiveColor;
    }
}
