#pragma compute UpdateVoxelizeTexture

//#pragma enable debug_symbol

#define IS_MATERIAL_SHADER 1

#ifndef THREADGROUP_SIZE
#define THREADGROUP_SIZE 8
#endif

#define USE_CLIPMAP_WRAP 1
#include "SmartVoxelizeCommon.hlsl"

RWTexture3D<uint> RWNormalTexture;
RWTexture3D<uint> RWAlbedoTexture;
RWTexture3D<uint> RWEmissiveTexture;

Texture3D<float4> SrcNormalTexture;
Texture3D<float4> SrcAlbedoTexture;
Texture3D<float4> SrcEmissiveTexture;

cbuffer cSrcTextureParameters
{
    uint3 _SrcClipmapGridResolution;
    float3 _SrcVoxelSize;
    float3 _SrcClipmapToWorldCenterScale;
    float3 _SrcClipmapToWorldCenterBias;
};

cbuffer ClearParam
{
    float4 _ClearValue;
    uint3 _TextureSize; 
}

float3 GetClipmapToWorldPositionInSrc(uint3 VoxelCoord)
{
    float3 WorldPosition = VoxelCoord * _SrcClipmapToWorldCenterScale + _SrcClipmapToWorldCenterBias;
    return WorldPosition;
}

uint GetAxisDirectionInSrc(float3 LocalNormal)
{
    int Axis = abs(LocalNormal.x) > abs(LocalNormal.y) ? 0 : 1;
    Axis = abs(LocalNormal[Axis]) > abs(LocalNormal.z) ? Axis : 2;

    Axis = Axis == 0 && LocalNormal.x > 0 ? 3 : Axis;
    Axis = Axis == 1 && LocalNormal.y > 0 ? 4 : Axis;
    Axis = Axis == 2 && LocalNormal.z > 0 ? 5 : Axis;

    return Axis;
}

uint3 GetClipmapCoordinateInSrc(int FaceDirection, uint3 VoxelCoord)
{
    return uint3(VoxelCoord.xy, VoxelCoord.z * 6 + FaceDirection);
}

float GetDepthOffsetInSrc(float3 LocalNormal, uint3 VoxelCoord)
{
    int Axis = GetAxisDirectionInSrc(LocalNormal);

    uint3 Texcoord = GetClipmapCoordinateInSrc(Axis, VoxelCoord);
    return UnpackRGBA8Normal(SrcNormalTexture.Load(uint4(Texcoord, 0))).w;
}

[numthreads(8, 8, 8)]
void UpdateVoxelizeTexture(uint GroupIndex:SV_GroupIndex,
	uint3 GroupId : SV_GroupID,
    uint3 GroupThreadId : SV_GroupThreadID,
	uint3 DispatchThreadId : SV_DispatchThreadID)
{
    const float3 CubeAttribute[6][2] = {
        { float3(-1, 0, 0), float3(-1, 0, 0) },
        { float3(0, -1, 0), float3(0, -1, 0) },
        { float3(0, 0, -1), float3(0, 0, -1) },
        { float3(1, 0, 0), float3(1, 0, 0) },
        { float3(0, 1, 0), float3(0, 1, 0) },
        { float3(0, 0, 1), float3(0, 1, 0) },
    };

    if(all(DispatchThreadId < _SrcClipmapGridResolution))
    {
        uint3 VoxelCoord = DispatchThreadId;
        float3 VoxelWorldCenter = GetClipmapToWorldPositionInSrc(VoxelCoord);
        UNROLL
        for(int faceDirection = 0; faceDirection < 6; faceDirection++)
        {
            uint3 srcTexelCoord = GetClipmapCoordinateInSrc(faceDirection, VoxelCoord);
            float4 srcBaseColor = SrcAlbedoTexture.Load(uint4(srcTexelCoord, 0));
            float4 srcEmissiveColor = SrcEmissiveTexture.Load(uint4(srcTexelCoord, 0));
            float4 srcNormal = SrcNormalTexture.Load(uint4(srcTexelCoord, 0));
            srcNormal.xyz = srcNormal.xyz * 2.0f - 1.0f;
            srcNormal.w = 1.0f - srcNormal.w;
            if (srcBaseColor.a > 0)
            {
                float3 LocalPosition = CubeAttribute[faceDirection][0];
                float3 LocalNormal = CubeAttribute[faceDirection][1];
                LocalPosition -= LocalNormal * srcNormal.w * 2.0f;
                LocalPosition *= _SrcVoxelSize * 0.5f;

                float3 VoxelWorldCenter = GetClipmapToWorldPositionInSrc(VoxelCoord);
                float3 PositionWS = LocalPosition + (_SrcVoxelSize * 0.5f) + VoxelWorldCenter;

                //write to dst voxel texture
            #if USE_CLIPMAP_WRAP
                float4 VoxelPosition = float4(PositionWS * ClipmapWorldToUVScale.xyz, 1.0f);
            #else
                float4 VoxelPosition = float4(PositionWS * ClipmapWorldToUVScale.xyz + ClipmapWorldToUVBias.xyz, 1.0f);
            #endif
                float3 DstOriginVoxelCoord = GetGlobalVoxelCoord(VoxelPosition);
                uint3 DstVoxelCoord = DstOriginVoxelCoord;
                if(all(DstVoxelCoord >= 0) && all(DstVoxelCoord < ClipmapGridResolution))
                {
                    bool isFrontFace = faceDirection < 3 ? true : false;

                    float3 Offset = frac(DstOriginVoxelCoord);
                    float Depth = Offset.x;

                    Depth = (faceDirection == 1 || faceDirection == 4) ? Offset.y : Depth;
                    Depth = (faceDirection == 2 || faceDirection == 5) ? Offset.z : Depth;

                    Depth = isFrontFace ? Depth : 1 - Depth;

                    uint3 DstTexcoord = GetClipmapCoordinate(ClipmapIndex, faceDirection, DstVoxelCoord);

                    Depth = max(1.0f - Depth, 1.0f/255.0f);
                    uint baseColorVal = PackRGBA8(float4(srcBaseColor.rgb, Depth));
                    uint emissiveColorVal = PackRGBA8(float4(srcEmissiveColor.rgb, Depth));
                    uint worldNormalVal = PackRGBA8(float4(srcNormal.rgb * 0.5f + 0.5f, Depth));
                    InterlockedMax(RWAlbedoTexture[DstTexcoord], baseColorVal);
                    InterlockedMax(RWEmissiveTexture[DstTexcoord], emissiveColorVal);
                    InterlockedMax(RWNormalTexture[DstTexcoord], worldNormalVal);
                }
            }
        }    
    }
}