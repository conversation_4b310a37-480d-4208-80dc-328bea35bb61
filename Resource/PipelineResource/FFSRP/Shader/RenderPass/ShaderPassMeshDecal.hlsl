#ifndef SHADER_PASS_MESH_DECAL
#define SHADER_PASS_MESH_DECAL

#include "../Material/MaterialUtilities.hlsl"
#include "../Material/Lit/Lit.hlsl"

#define DecalDepthBias 0.05f

VSOutput VSMain(VSInput input)
{
	VSOutput output = VSInputToVSOutput(input);
    output.positionNDC.z += DecalDepthBias;
    return output;
}

struct PSOutput
{
	float4 DBuffer0 : SV_Target0;
	float4 DBuffer1 : SV_Target1;
	float4 DBuffer2 : SV_Target2;
	float4 SceneColor : SV_Target3;
};

void DecalOutput(SurfaceData surfaceData, out float4 DBuffer0, out float4 DBuffer1, out float4 DBuffer2, out float4 Emissive)
{
	float surfaceAlpha = surfaceData.opacity;
	float Alpha0 = 0.0, Alpha1 = 0.0, Alpha2 = 0.0, Alpha3 = 0.0;

	// Alpha0 = surfaceAlpha;
	// Alpha1 = surfaceAlpha;
	// Alpha2 = surfaceAlpha;
	Alpha3= 1.0;

	DBuffer0 = float4(surfaceData.baseColor, surfaceAlpha);
	// surfaceData.normalTS is used as world space normal
	DBuffer1 = float4(surfaceData.normalWS * 0.5f + 128.0f/255.0f, surfaceAlpha);
	// default value is (0.5, 0, 0.5)
	DBuffer2 = float4(surfaceData.roughness, surfaceData.metallic, surfaceData.specular, surfaceAlpha);

	Emissive = float4(surfaceData.emissiveColor, Alpha3);
}

PSOutput PSMain(VSOutput vsoutput, bool isFrontFace : SV_IsFrontFace, uint coverage : SV_Coverage)
{
    PSInput input = VSOutputToPSInput(vsoutput);
	PositionInputs posInput = GetPositionInput(input.positionWS, input.screenUV);

	float3 V = GetWorldSpaceNormalizeViewDir(posInput.positionWS);

	SurfaceData surfaceData;
	BuiltinData builtinData;
#ifdef TERRAIN_USE_INSTANCING
	GetSurfaceAndBuiltinData(V, input, isFrontFace, input.slotIndex, surfaceData, builtinData);
#elif (defined(INSTANCING) && INSTANCING == 1 || defined(CE_INSTANCING))
	GetSurfaceAndBuiltinData(V, input, isFrontFace, input.instanceID, surfaceData, builtinData);
#else
	GetSurfaceAndBuiltinData(V, input, isFrontFace, 0, surfaceData, builtinData);
#endif

    PSOutput output = (PSOutput)0;
	DecalOutput(surfaceData, output.DBuffer0, output.DBuffer1, output.DBuffer2, output.SceneColor);
	return output;
}

#endif