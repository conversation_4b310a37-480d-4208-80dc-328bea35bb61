#ifndef TRANSPARENT_SHADING_UTIL_HLSL
#define TRANSPARENT_SHADING_UTIL_HLSL

#include "../ShaderLibrary/Common.hlsl"

SHADER_CONST(bool, CE_ENABLE_SEPERATE_TRANSLUCENCY, false)

struct TransparentPSOutput
{
    	float4 sceneColor : SV_Target0;
        float4 seperateTranslucency : SV_Target1; // should only be enable when seperateTranslucency is enabled, but right now, we didnot add pragma 
        float4 reactiveMask : SV_Target2;
};

TransparentPSOutput StoreTransparentPSOutput(float4 color, float2 reactive)
{
	TransparentPSOutput psOut = (TransparentPSOutput)0;
	if (CE_ENABLE_SEPERATE_TRANSLUCENCY)
	{
		psOut.seperateTranslucency = color;
		psOut.reactiveMask.zw = reactive;
	}
	else
	{
		psOut.sceneColor = color;
		psOut.reactiveMask.xy = reactive;
	}
    return psOut;
}

#endif