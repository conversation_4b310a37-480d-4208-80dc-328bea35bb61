#ifndef INDIRECT_LIGHTING_COMMON_HLSL
#define INDIRECT_LIGHTING_COMMON_HLSL
#include "../ShaderLibrary/Common.hlsl"

// Point lobe in off-specular peak direction
float3 GetOffSpecularPeakReflectionDir(float3 Normal, float3 ReflectionVector, float Roughness)
{
    float a = Square(Roughness);
    return lerp(Normal, ReflectionVector, (1 - a) * (sqrt(1 - a) + a));
}

#define REFLECTION_CAPTURE_ROUGHEST_MIP 1
#define REFLECTION_CAPTURE_ROUGHNESS_MIP_SCALE 1.2

/**
 * Compute absolute mip for a reflection capture cubemap given a roughness.
 */
half ComputeReflectionCaptureMipFromRoughness(half Roughness, half CubemapMaxMip)
{
    // Heuristic that maps roughness to mip level
    // This is done in a way such that a certain mip level will always have the same roughness, regardless of how many mips are in the texture
    // Using more mips in the cubemap just allows sharper reflections to be supported
    // need minus Roughness,so it capture the sharper mip
    half LevelFrom1x1 = REFLECTION_CAPTURE_ROUGHEST_MIP - REFLECTION_CAPTURE_ROUGHNESS_MIP_SCALE * log2(max(Roughness - M_RoughnessFloatMin, 0.001));
    return CubemapMaxMip - 1 - LevelFrom1x1;
}

float ComputeReflectionCaptureRoughnessFromMip(float Mip, half CubemapMaxMip)
{
    float LevelFrom1x1 = CubemapMaxMip - 1 - Mip;
    return exp2((REFLECTION_CAPTURE_ROUGHEST_MIP - LevelFrom1x1) / REFLECTION_CAPTURE_ROUGHNESS_MIP_SCALE);
}

float GetSpecularOcclusion(float NoV, float RoughnessSq, float AO)
{
    return saturate(pow(NoV + AO, RoughnessSq) - 1 + AO);
}

//SSRTracing
#include "../ShaderLibrary/Common.hlsl"
#include "../ShaderLibrary/Random.hlsl"
#include "../SmartGI/Common.hlsl"
#include "../SmartGI/SSRTRayCast.hlsl"
#include "../ShaderLibrary/GlobalModelVariables.hlsl"
float4 GetSSRTracing(Texture2D FurthestHZBTexture,
    SamplerState FurthestHZBTextureSampler,
    Texture2D PrevSceneColorTexture,
    SamplerState PrevSceneColorSampler,
    Texture2D DepthTexture,
    float3 WorldPosition,
    float SceneDepth,
    float TraceDistance,
    uint NumSteps,
    float RayRoughness,
    float3 RayDir,
    float2 ScreenUV,
    float4 ViewSizeAndInvSize,
    float4 HZBUvFactorAndInvFactor,
    matrix View_TranslatedWorldToView,
    matrix View_TranslatedWorldToClip,
    matrix View_ViewToClip,
    matrix View_InvViewProj,
    matrix View_PreView,
    matrix View_PreProj,
    matrix View_ClipToPrevClipMat,
    uint StateFrameIndexMod8,
    float StartMipLevel
    )
{
    float SlopeCompareToleranceScale = 4.0;
    bool bRayWasClipped;

    //float4 posH = mul(View_TranslatedWorldToClip, float4(WorldPosition, 1.0));
    FSSRTRay Ray = InitScreenSpaceRayFromWorldSpace(
        View_TranslatedWorldToView,
        View_TranslatedWorldToClip,
        View_ViewToClip,
        /* RayOriginTranslatedWorld = */WorldPosition,
        /* WorldRayDirection = */RayDir,
        /* WorldTMax = */ TraceDistance,
        /* SceneDepth = */ SceneDepth,
        /* SlopeCompareToleranceScale */ SlopeCompareToleranceScale,
        /* bExtendRayToScreenBorder = */ true,
        /* out */ bRayWasClipped, 3.0);

    float StepOffset = InterleavedGradientNoise(ViewSizeAndInvSize.xy * ScreenUV, StateFrameIndexMod8);
    StepOffset -= 0.5;
    float Level;
    float3 HitUVz;
    bool bHit = false;
    bool bUncertain;

    CastScreenSpaceRay(
        FurthestHZBTexture, FurthestHZBTextureSampler,
        StartMipLevel,
        Ray, RayRoughness, NumSteps, StepOffset,
        HZBUvFactorAndInvFactor,
        true,
        /* out */ HitUVz,
        /* out */ Level,
        /* out */ bHit,
        /* out */ bUncertain);

    if (bHit && !bUncertain)
    {
        /*float2 SampleUV = HitUVz.xy;
        float2 HitScreenPosition = HitUVz.xy * float2(2, -2) + float2(-1, 1);
        float Vignette = ComputeHitVignetteFromScreenPos(HitScreenPosition);*/

        //Reproject
        float depthDevice = DepthTexture.Sample(FurthestHZBTextureSampler, HitUVz.xy).r;
        float4 positionClip = float4(float2(HitUVz.x * 2 - 1, 1.0 - 2 * HitUVz.y), depthDevice, 1.0);
        float4 positionPreClip = mul(View_ClipToPrevClipMat, positionClip);
        positionPreClip.xyz /= positionPreClip.w;
        float2 SampleUV = float2((positionPreClip.x + 1) / 2, 1 - (positionPreClip.y + 1) / 2);
        //depthDevice = positionPreClip.z;
        float2 HitScreenPosition = SampleUV.xy * float2(2, -2) + float2(-1, 1);
        float Vignette = ComputeHitVignetteFromScreenPos(HitScreenPosition);
        
        //ReprojectHit(TranslucentBasePass_PrevScreenPositionScaleBias, HitUVz, SampleUV, Vignette);
        if (depthDevice < 0.000001)
            return 0;

        float4 SSR = SampleScreenColor(
            PrevSceneColorTexture,
            PrevSceneColorSampler,
            SampleUV);

        SSR *= Vignette * saturate(2 - 6.6 * RayRoughness);
        return SSR;
    }

    return 0;
}

float4 GetSSRTracingTransparent(Texture2D FurthestHZBTexture,
    SamplerState FurthestHZBTextureSampler,
    Texture2D SceneColorTexture,
    SamplerState SceneColorSampler,
    Texture2D DepthTexture,
    float3 WorldPosition,
    float SceneDepth,
    float TraceDistance,
    uint NumSteps,
    float RayRoughness,
    float3 RayDir,
    float2 ScreenUV,
    float4 ViewSizeAndInvSize,
    float4 HZBUvFactorAndInvFactor,
    matrix View_TranslatedWorldToView,
    matrix View_TranslatedWorldToClip,
    matrix View_ViewToClip,
    matrix View_InvViewProj,
    matrix View_PreView,
    matrix View_PreProj,
    matrix View_ClipToPrevClipMat,
    uint StateFrameIndexMod8,
    float StartMipLevel
)
{
    //float3 RayDirV = mul(View_TranslatedWorldToView, float4(RayDir, 0.0)).xyz;
    //if (RayDirV.z < 0)return 0;
    float SlopeCompareToleranceScale = 4.0;
    bool bRayWasClipped;

    //float4 posH = mul(View_TranslatedWorldToClip, float4(WorldPosition, 1.0));
    FSSRTRay Ray = InitScreenSpaceRayFromWorldSpace(
        View_TranslatedWorldToView,
        View_TranslatedWorldToClip,
        View_ViewToClip,
        /* RayOriginTranslatedWorld = */WorldPosition,
        /* WorldRayDirection = */RayDir,
        /* WorldTMax = */ TraceDistance,
        /* SceneDepth = */ SceneDepth,
        /* SlopeCompareToleranceScale */ SlopeCompareToleranceScale,
        /* bExtendRayToScreenBorder = */ true,
        /* out */ bRayWasClipped);

    float StepOffset = InterleavedGradientNoise(ViewSizeAndInvSize.xy * ScreenUV, StateFrameIndexMod8);
    StepOffset -= 0.5;
    float Level;
    float3 HitUVz;
    bool bHit = false;
    bool bUncertain;

    CastScreenSpaceRay(
        FurthestHZBTexture, FurthestHZBTextureSampler,
        StartMipLevel,
        Ray, RayRoughness, NumSteps, StepOffset,
        HZBUvFactorAndInvFactor,
        false,
        /* out */ HitUVz,
        /* out */ Level,
        /* out */ bHit,
        /* out */ bUncertain);

    if (bHit)
    {
        float depthDevice = DepthTexture.Sample(FurthestHZBTextureSampler, HitUVz.xy).r;
        if (depthDevice < 0.000001)
            return 0;
        float2 SampleUV = HitUVz.xy;
        //depthDevice = positionPreClip.z;
        float2 HitScreenPosition = SampleUV.xy * float2(2, -2) + float2(-1, 1);
        float Vignette = ComputeHitVignetteFromScreenPos(HitScreenPosition);

        float4 SSR = SampleScreenColor(
            SceneColorTexture,
            SceneColorSampler,
            SampleUV);

        SSR *= Vignette * saturate(2 - 6.6 * RayRoughness);
        return SSR;
    }

    return 0;
}

//REFLECTION_PROBE
float3 GetLookupVectorForSphereCapture(float3 ReflectionVector, float3 WorldPosition, float4 SphereCapturePositionAndRadius, float NormalizedDistanceToCapture, float3 LocalCaptureOffset, inout float DistanceAlpha)
{
    float3 ProjectedCaptureVector = ReflectionVector;
    float ProjectionSphereRadius = SphereCapturePositionAndRadius.w;
    float SphereRadiusSquared = ProjectionSphereRadius * ProjectionSphereRadius;

    float3 LocalPosition = WorldPosition - SphereCapturePositionAndRadius.xyz;
    float LocalPositionSqr = dot(LocalPosition, LocalPosition);

    float3 QuadraticCoef;
    QuadraticCoef.x = 1;
    QuadraticCoef.y = dot(ReflectionVector, LocalPosition);
    QuadraticCoef.z = LocalPositionSqr - SphereRadiusSquared;

    float Determinant = QuadraticCoef.y * QuadraticCoef.y - QuadraticCoef.z;

    if (Determinant >= 0)
    {
        float FarIntersection = sqrt(Determinant) - QuadraticCoef.y;

        float3 LocalIntersectionPosition = LocalPosition + FarIntersection * ReflectionVector;
        ProjectedCaptureVector = LocalIntersectionPosition - LocalCaptureOffset;

        float x = saturate(2.5 * NormalizedDistanceToCapture - 1.5);
        DistanceAlpha = 1 - x * x * (3 - 2 * x);
    }
    return ProjectedCaptureVector;
}

float3 GatherSkyLightReflectionCapture(TextureCube<float4> skyLightTexture, SamplerState textureSampler, float roughness, float3 RayDirection, out float outSkyAverageBrightness)
{
    float2 texDim;
    skyLightTexture.GetDimensions(texDim.x, texDim.y);
    half maxMip = (half)((int)(log(min(texDim.x, texDim.y)) / log(2)) + 1);
    float AbsoluteSpecularMip = ComputeReflectionCaptureMipFromRoughness(roughness, maxMip);
    float3 Reflection = skyLightTexture.SampleLevel(textureSampler, RayDirection, AbsoluteSpecularMip).rgb;

    return Reflection;
}

float3 GatherRadiance(RWBuffer<uint> CulledLightsDataGrid, TextureCubeArray CaptureReflectionCubemaps, SamplerState ReflectionCubeSampler, uint ReflectionCapturesMipNum,
    float4 RPExtentMipmapCount[MAX_RP_NUM], float4 ReflectionProbePosDistance[MAX_RP_NUM], float4 ReflectionProbeEulerRot[MAX_RP_NUM],
#ifdef CE_USE_DOUBLE_TRANSFORM
    float3 CameraTilePosition,
    float4 ReflectionProbeTilePos[MAX_RP_NUM],
#endif
    TextureCube DestSkyLightTexture,
    TextureCube SourceSkyLightTexture,  // extra TextureCube for realtime capture blend
    float SkyBlendFactor,
    SamplerState SkyLightTextureSampler,
    float3 SkyLightColor,
    float  SkyLightIntensity,
    float compositeAlpha, float3 worldPos, float3 reflectDir, float3 CE2UER, float roughness, float indirectIrradiance, uint shadingModelID, uint NumCulledReflectionCaptures, uint CaptureDataStartIndex, float specularOcclusion = 1.0)
{
    // Reflection
    float4 ImageBasedReflections = float4(0, 0, 0, compositeAlpha);
    for (uint TileCaptureIndex = 0; TileCaptureIndex < NumCulledReflectionCaptures; TileCaptureIndex++)
    {
        if (ImageBasedReflections.a < 0.001)
        {
            break;
        }

        uint CaptureIndex = 0;
        CaptureIndex = CulledLightsDataGrid[CaptureDataStartIndex + TileCaptureIndex];
        //RP Tile
        float3 CaptureWorldPosition = ReflectionProbePosDistance[CaptureIndex].xyz;
        float CaptureRadius = max(RPExtentMipmapCount[CaptureIndex].x, max(RPExtentMipmapCount[CaptureIndex].y, RPExtentMipmapCount[CaptureIndex].z));

#ifdef CE_USE_DOUBLE_TRANSFORM
        //Camera Tile
        CaptureWorldPosition = GetLargeCoordinateReltvPosition(CaptureWorldPosition, ReflectionProbeTilePos[CaptureIndex].xyz, CameraTilePosition);
#endif
        float3 CaptureVector = worldPos - CaptureWorldPosition;
        float CaptureVectorLength = sqrt(dot(CaptureVector, CaptureVector));
        float NormalizedDistanceToCapture = saturate(CaptureVectorLength / CaptureRadius);

        float Mip = ComputeReflectionCaptureMipFromRoughness(roughness, ReflectionCapturesMipNum);

        if (CaptureVectorLength < CaptureRadius)
        {
            float3 ProjectedCaptureVector = reflectDir;
            float4 CaptureOffsetAndAverageBrightness = 0;// ReflectionCapture_CaptureOffsetAndAverageBrightness[CaptureIndex];

            float DistanceAlpha = 0;
            {
                ProjectedCaptureVector = GetLookupVectorForSphereCapture(reflectDir, worldPos, float4(CaptureWorldPosition, CaptureRadius), NormalizedDistanceToCapture, CaptureOffsetAndAverageBrightness.xyz, DistanceAlpha);
                //ROTATE PROBE
                ProjectedCaptureVector = mul(GetRotMatrixFromEuler(ReflectionProbeEulerRot[CaptureIndex].xyz), ProjectedCaptureVector);
            }
            float CaptureArrayIndex = CaptureIndex;

            {
                //RPExtentMipmapCount[CaptureIndex].w : intensity
                float4 Sample = CaptureReflectionCubemaps.SampleLevel(ReflectionCubeSampler, float4(ProjectedCaptureVector, CaptureArrayIndex), Mip);
                Sample.rgb *= RPExtentMipmapCount[CaptureIndex].w;

                //Sample.rgb *= CaptureProperties.r;//Intensity
                Sample *= DistanceAlpha;

                ImageBasedReflections.rgb += Sample.rgb * ImageBasedReflections.a;// *IndirectSpecularOcclusion;
                ImageBasedReflections.a *= 1 - Sample.a;

                //float AverageBrightness = CaptureOffsetAndAverageBrightness.w;
                //CompositedAverageBrightness.x += AverageBrightness * DistanceAlpha * CompositedAverageBrightness.y;
                //CompositedAverageBrightness.y *= 1 - DistanceAlpha;
            }
        }
    }

    // SkyLight
    {
        float SkyAverageBrightness = 0;
        float3 SkyLighting = GatherSkyLightReflectionCapture(DestSkyLightTexture, SkyLightTextureSampler, roughness, CE2UER, SkyAverageBrightness) * SkyLightColor * SkyLightIntensity;// *_SkyLight_Intensity;;
        if (SkyBlendFactor > 0.f)
        {
            float3 SourceSkyLighting = GatherSkyLightReflectionCapture(SourceSkyLightTexture, SkyLightTextureSampler, roughness, CE2UER, SkyAverageBrightness) * SkyLightColor * SkyLightIntensity;
            SkyLighting = lerp(SourceSkyLighting, SkyLighting, SkyBlendFactor);
        }
        
        float3 ExtraIndirectSpecular = SkyLighting * specularOcclusion;// *IndirectSpecularOcclusion;
        ImageBasedReflections.rgb += ImageBasedReflections.a * ExtraIndirectSpecular;
    }

    //ImageBasedReflections.rgb *= ComputeMixingWeight(IndirectIrradiance, CompositedAverageBrightness.x, Roughness);
    return ImageBasedReflections.rgb;
}


#endif