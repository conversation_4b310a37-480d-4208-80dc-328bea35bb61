#pragma compute Cull<PERSON>er<PERSON>ageDrawUnits
#pragma compute AllocateCommandInstanceOutputSpace
#pragma compute OutputCommandInstanceLists

#include "VirtualShadowMapViewCommon.hlsl"
#include "SceneData.hlsl"

#define NUM_THREADS_PER_GROUP 64
#define PERSISTENT_CULLING_GROUP_SIZE_X 64
#define PERSISTENT_CULLING_GROUP_SIZE_Y 8
#define PERSISTENT_CULLING_GROUP_COUNT 64
#define PERSISTENT_CULLING_GROUP_SIZE PERSISTENT_CULLING_GROUP_SIZE_X * PERSISTENT_CULLING_GROUP_SIZE_Y
#define PERSISTENT_CULLING_INSTANCE_COUNT_PER_GROUP PERSISTENT_CULLING_GROUP_SIZE_X * 32
#define INSTANCE_COUNT_CACHE_SIZE PERSISTENT_CULLING_GROUP_SIZE
#define INDIRECT_ARGS_NUM_WORDS 5

cbuffer cbPass
{
    uint4 _UIntParams0;
    uint4 _UIntParams1;
    uint4 _UIntParams2;
}

#define _FirstPrimaryView                   _UIntParams0.x
#define _PrimaryViewCount                   _UIntParams0.y
#define _CurrSceneFrameCount                _UIntParams0.z
#define _PayloadCount                       _UIntParams1.x
#define _VisibleObjectCommandBufferMaxNum   _UIntParams1.y
#define _IndirectArgCount                   _UIntParams1.z
#define _RangePayloadOffset                 _UIntParams1.w
#define _RangePayloadCount                  _UIntParams2.x
#define _LargePayloadCount                  _UIntParams2.y

struct VisibleObjectCommand
{
    uint packedPageInfo;
    uint objectIndex;
    uint indirectArgIndex;
};

StructuredBuffer<uint> _PageFlags;
StructuredBuffer<uint4> _PageRectBounds;
StructuredBuffer<VirtualShadowMapViewData> _VirtualShadowMapViewDatas;

StructuredBuffer<PrimitiveCullingData> _GPUScenePrimitiveCullingDatas;
StructuredBuffer<ObjectCullingData> _GPUSceneObjectCullingDatas;

RWStructuredBuffer<VisibleObjectCommand> _OutVisibleObjectCommands;
RWStructuredBuffer<uint> _OutVisibleObjectCommandCount;
RWStructuredBuffer<uint> _OutDrawIndirectArgs;

struct GroupObjectPayloadData
{
    int objectCullingGUID;
    int objectIndex;
    uint indirectArgIndex;
};

StructuredBuffer<ObjectPayloadData2> _ObjectPayloadDatas;
RWStructuredBuffer<uint> _ObjectPayloadIndexAndObjectOffsetAndMutex;

#define _ObjectPayloadIndex _ObjectPayloadIndexAndObjectOffsetAndMutex[0]
#define _ObjectPayloadObjectOffset _ObjectPayloadIndexAndObjectOffsetAndMutex[1]
#define _ObjectPayloadMutex _ObjectPayloadIndexAndObjectOffsetAndMutex[2]

groupshared uint _GroupObjectPayloadIndex;
groupshared uint _GroupObjectPayloadIndexOffset;
groupshared uint _GroupObjectPayloadIndexEnd;
groupshared uint _GroupObjectPayloadIndexOffsetEnd;

SamplerState ce_Sampler_Point;

#include "VirtualShadowMapPageOverlap.hlsl"

void LockAcquire()
{
    uint value = 1u;
    while (value)
    {
        InterlockedCompareExchange(_ObjectPayloadMutex, 0u, 1u, value);
    }
}

void LockRelease()
{
    uint value;
    InterlockedExchange(_ObjectPayloadMutex, 0, value);
}


void WriteCommand(uint mipViewId, uint objectIndex, uint indirectArgIndex)
{
    PageInfo pageInfo;
    pageInfo.viewId = mipViewId;
    pageInfo.isStaticPage = false;

    VisibleObjectCommand visibleObjectCommand;
    visibleObjectCommand.packedPageInfo = PackPageInfo(pageInfo);
    visibleObjectCommand.objectIndex = objectIndex;
    visibleObjectCommand.indirectArgIndex = indirectArgIndex;

    uint visibleObjectCommandOutputOffset = 0u;
    InterlockedAdd(_OutVisibleObjectCommandCount[0], 1u, visibleObjectCommandOutputOffset);
    if (visibleObjectCommandOutputOffset < _VisibleObjectCommandBufferMaxNum)
    {
        _OutVisibleObjectCommands[visibleObjectCommandOutputOffset] = visibleObjectCommand;
    }
}

groupshared uint instanceCountCache[INSTANCE_COUNT_CACHE_SIZE + 1];

void Scan(uint groupIndex)
{
    // Blelloch Scan
    uint offset = 1;
    // build sum in place up the tree
    for (int d = INSTANCE_COUNT_CACHE_SIZE >> 1; d > 0; d >>= 1)
    {
        GroupMemoryBarrierWithGroupSync();

        if (groupIndex < d)
        {
            int ai = offset * (2 * groupIndex + 1) - 1;
            int bi = offset * (2 * groupIndex + 2) - 1;

            instanceCountCache[bi] += instanceCountCache[ai];
        }

        offset = offset << 1;
    }

    // Store the sum of the array to instanceCountCache[INSTANCE_COUNT_CACHE_SIZE]
    // and clear it
    if (groupIndex == 0) 
    {
        instanceCountCache[INSTANCE_COUNT_CACHE_SIZE] = instanceCountCache[INSTANCE_COUNT_CACHE_SIZE - 1];
        instanceCountCache[INSTANCE_COUNT_CACHE_SIZE - 1] = 0;
    }

    offset = INSTANCE_COUNT_CACHE_SIZE;
    for (int d = 1; d < INSTANCE_COUNT_CACHE_SIZE; d *= 2) // traverse down tree & build scan
    {
        offset >>= 1;
        GroupMemoryBarrierWithGroupSync();
        if (groupIndex < d)
        {
            int ai = offset * (2 * groupIndex + 1) - 1;
            int bi = offset * (2 * groupIndex + 2) - 1;
            uint t = instanceCountCache[bi];
            instanceCountCache[bi] += instanceCountCache[ai];
            instanceCountCache[ai] = t;
        }
        GroupMemoryBarrierWithGroupSync();
    }
}

struct ObjectMatrix
{
    matrix mat;
    float pad;
};

groupshared ObjectMatrix objectMatrix[PERSISTENT_CULLING_GROUP_SIZE_X];

void InstanceCulling(const GroupObjectPayloadData payload, const uint2 groupThreadID, bool predicate)
{
    int objectCullingGUID = payload.objectCullingGUID;
    int objectIndex = payload.objectIndex;

    predicate &= objectCullingGUID >= 0;
    PrimitiveCullingData primitiveCullingData;
    if (predicate)
    {
        ObjectCullingData objectCullingData = _GPUSceneObjectCullingDatas[objectCullingGUID];
        primitiveCullingData = _GPUScenePrimitiveCullingDatas[objectCullingData.primitiveCullingGUID];

        if (groupThreadID.y == 0)
        {
            objectMatrix[groupThreadID.x].mat = objectCullingData.worldMatrix;
        }
    }

    bool hasMoved = primitiveCullingData.lastUpdateSceneFrameCount == _CurrSceneFrameCount;
    GroupMemoryBarrierWithGroupSync();

    uint visibleViewCount = 0;

    for (uint primaryViewId = _FirstPrimaryView + groupThreadID.y; primaryViewId < _FirstPrimaryView + _PrimaryViewCount; primaryViewId += PERSISTENT_CULLING_GROUP_SIZE_Y)
    {
    #if 1
        VirtualShadowMapViewData viewData;

        if (predicate)
        {
        
            viewData = _VirtualShadowMapViewDatas[primaryViewId];
            uint virtualShadowMapId = viewData.virtualShadowMapId;
            matrix localToClip = CombineTranslationMatrix(objectMatrix[groupThreadID.x].mat, viewData.worldToShadowMatrix, primitiveCullingData.tilePosition, viewData.tilePosition);

            FrustumCullData cull = BoxCullFrustum(primitiveCullingData.localBoundsCenter, primitiveCullingData.localBoundsExtent, localToClip, false);
            if (cull.isVisible)
            {
                ScreenRect rect = GetScreenRect(viewData.viewRect, cull, 4);
                uint flagMask = VSM_ALLOCATED_FLAG;
                if (!hasMoved)
                {
                    flagMask |= VSM_STATIC_UNCACHED_FLAG | VSM_DYNAMIC_UNCACHED_FLAG;
                }

                if (OverlapsAnyValidPage(virtualShadowMapId, 0, rect, flagMask))
                {
                    uint4 rectPages = uint4(viewData.viewRect.xyxy + rect.pixels) >> VSM_LOG2_PAGE_SIZE;

                    uint4 allocatedBounds = _PageRectBounds[virtualShadowMapId * VSM_MAX_MIP_LEVELS];
                    rectPages.xy = max(rectPages.xy, allocatedBounds.xy);
                    rectPages.zw = min(rectPages.zw, allocatedBounds.zw);
                    if (all(rectPages.zw >= rectPages.xy))
                    {
                        ++visibleViewCount;
                        WriteCommand(primaryViewId, objectIndex, payload.indirectArgIndex);
                    }
                }            
            }
        }
    #else
        if (predicate)
        {
            ++visibleViewCount;
            WriteCommand(primaryViewId, objectIndex, payload.indirectArgIndex);
        }
    #endif


    }

    if (predicate && visibleViewCount > 0)
        InterlockedAdd(_OutDrawIndirectArgs[payload.indirectArgIndex * INDIRECT_ARGS_NUM_WORDS + 1], visibleViewCount);
}

[numthreads(PERSISTENT_CULLING_GROUP_SIZE_X, PERSISTENT_CULLING_GROUP_SIZE_Y, 1)]
void CullPerPageDrawUnits(uint dispatchThreadId : SV_DispatchThreadID, uint groupIndex : SV_GroupIndex, uint2 groupThreadID : SV_GroupThreadID, uint groupID: SV_GroupID)
{
    // Step0: Object count = 1
    const uint threadCount = PERSISTENT_CULLING_GROUP_COUNT * PERSISTENT_CULLING_GROUP_SIZE_X;
    uint payloadIndex = dispatchThreadId;
    while (payloadIndex - groupThreadID.x < _RangePayloadOffset)
    {
        bool predicate = payloadIndex < _RangePayloadOffset;
        ObjectPayloadData2 currentPayload;
        if (predicate)
            currentPayload = _ObjectPayloadDatas[payloadIndex];

        GroupObjectPayloadData groupPayload;
        groupPayload.objectCullingGUID = currentPayload.objectCullingGUID;
        groupPayload.objectIndex = currentPayload.objectIndex;
        groupPayload.indirectArgIndex = currentPayload.indirectArgIndex;
    
        InstanceCulling(groupPayload, groupThreadID, predicate);
    
        payloadIndex += threadCount;
    }

    // Step1: Object count is less than a threshold
    // Payloads are divided evenly across all these thread blocks
    const int payloadBatchSize = (_RangePayloadCount + PERSISTENT_CULLING_GROUP_COUNT - 1) / PERSISTENT_CULLING_GROUP_COUNT;
    int payloadOffset = payloadBatchSize * groupID + _RangePayloadOffset;
    const int payloadEnd = min(payloadOffset + payloadBatchSize, _RangePayloadOffset + _RangePayloadCount);
    int payloadCount = payloadEnd - payloadOffset;

    if (payloadCount > 0)
    {
        while (payloadOffset < payloadEnd)  // payloadOffset += INSTANCE_COUNT_CACHE_SIZE;
        {
            // Fetch a batch of payloads that fit into the size of the shared buffer
            payloadCount = min(INSTANCE_COUNT_CACHE_SIZE, payloadEnd - payloadOffset);
            payloadIndex = payloadOffset + groupIndex;
            const int currentPayloadEnd = payloadOffset + payloadCount;

            GroupMemoryBarrierWithGroupSync();

            // Load objectCount of each payload
            if (payloadIndex < currentPayloadEnd)
            {
                ObjectPayloadData2 currentPayload = _ObjectPayloadDatas[payloadIndex];
                instanceCountCache[payloadIndex - payloadOffset] = currentPayload.objectCount;
            }
            else // if (payloadIndex - payloadOffset < INSTANCE_COUNT_CACHE_SIZE)
                instanceCountCache[payloadIndex - payloadOffset] = 0;
        
            GroupMemoryBarrierWithGroupSync();

            // if (groupIndex == 0)
            // {
            //     uint cnt = 0;
            //     for (int i = 0; i < payloadCount; i++)
            //     {
            //         uint tmp = instanceCountCache[i];
            //         instanceCountCache[i] = cnt;
            //         cnt += tmp;
            //     }

            //     instanceCountCache[payloadCount] = cnt;
            // }

            // GroupMemoryBarrierWithGroupSync();

            // Exclusive prefix sum
            Scan(groupIndex);
            
            // The sum of the payloads is stored in instanceCountCache[INSTANCE_COUNT_CACHE_SIZE]
            // Instance culling tasks of all these payloads are divided evenly across the thread block
            const uint instanceTot = instanceCountCache[INSTANCE_COUNT_CACHE_SIZE];

            if (instanceTot == 0)
                continue;

            const uint instanceBatch = (instanceTot + PERSISTENT_CULLING_GROUP_SIZE_X - 1) / PERSISTENT_CULLING_GROUP_SIZE_X;
            const uint instanceOffset = instanceBatch * groupThreadID.x;
            bool predicate = instanceOffset < instanceTot;
            // if (instanceOffset < instanceTot)
            {
                const uint instanceCount = min(instanceBatch, instanceTot - instanceOffset);
                predicate &= instanceCount > 0;
                int instancePtr = 0;

                int left = 0;
                int right = payloadCount; 
                int found = payloadCount; 
                payloadIndex = -1;

                // Use binary search to find the first payload to handle
                if (predicate)
                {
                    while (left < right) {
                        int mid = left + (right - left) / 2;
                        if (instanceCountCache[mid + 1] > instanceOffset) 
                        {
                            found = mid;
                            right = mid;
                        } else {
                            left = mid + 1;
                        }
                    }

                    if (found < payloadCount) {
                        instancePtr = instanceOffset - instanceCountCache[found];
                        payloadIndex = found + payloadOffset;
                    }
                }
                
                predicate &= payloadIndex >= 0;

                // if (payloadIndex >= 0)
                {
                    uint cnt = 0;
                    ObjectPayloadData2 currentPayload;

                    if (predicate)
                        currentPayload = _ObjectPayloadDatas[payloadIndex];

                    for (int i = 0; i < instanceBatch; i++)
                    {
                        bool currentPredicate = predicate && cnt < instanceCount && instancePtr < currentPayload.objectCount;

                        GroupObjectPayloadData groupPayload;
                        groupPayload.indirectArgIndex = currentPayload.indirectArgIndex;
                        groupPayload.objectCullingGUID = currentPayload.objectCullingGUID + instancePtr;
                        groupPayload.objectIndex = currentPayload.objectIndex + instancePtr;
                        InstanceCulling(groupPayload, groupThreadID, currentPredicate);
                        
                        // Increment the counter when a culling task is dispatched
                        if (currentPredicate)
                        {
                            instancePtr++;
                            cnt++;
                        }

                        // Fetch the next payload
                        if (instancePtr >= currentPayload.objectCount)
                        {
                            instancePtr = 0;
                            payloadIndex++;
                            
                            if (predicate && cnt < instanceCount)
                            {
                                currentPayload = _ObjectPayloadDatas[payloadIndex];
                            }
                        }
                    }
                }
            }

            payloadOffset += INSTANCE_COUNT_CACHE_SIZE;
        }
    }

    if (_LargePayloadCount == 0)
        return;

    // Step2: Object count is greater than a threshold
    while(true)
    {
        GroupMemoryBarrierWithGroupSync();

        if (groupIndex == 0)
        {
            LockAcquire();

            uint objectPayloadIndexStart = _ObjectPayloadIndex;
            uint objectPayloadObjectOffsetStart = _ObjectPayloadObjectOffset;
            uint objectPayloadIndex = objectPayloadIndexStart;
            uint objectPayloadObjectOffset = objectPayloadObjectOffsetStart;

            uint groupPayloadDataIndex = 0;
            while (groupPayloadDataIndex < PERSISTENT_CULLING_INSTANCE_COUNT_PER_GROUP)
            {
                if (objectPayloadIndex >= _PayloadCount)
                    break;

                ObjectPayloadData2 currentPayload = _ObjectPayloadDatas[objectPayloadIndex];

                if (objectPayloadObjectOffset < currentPayload.objectCount)
                {
                    uint count = min(PERSISTENT_CULLING_INSTANCE_COUNT_PER_GROUP - groupPayloadDataIndex, currentPayload.objectCount - objectPayloadObjectOffset);
                    groupPayloadDataIndex += count;
                    objectPayloadObjectOffset += count;
                }

                if (objectPayloadObjectOffset >= currentPayload.objectCount)
                {
                    objectPayloadIndex++;
                    objectPayloadObjectOffset = 0;
                }
            }

            uint originValue;
            InterlockedExchange(_ObjectPayloadIndex, objectPayloadIndex, originValue);
            InterlockedExchange(_ObjectPayloadObjectOffset, objectPayloadObjectOffset, originValue);

            LockRelease();

            _GroupObjectPayloadIndex = objectPayloadIndexStart;
            _GroupObjectPayloadIndexOffset = objectPayloadObjectOffsetStart;
            _GroupObjectPayloadIndexEnd = objectPayloadIndex;
            _GroupObjectPayloadIndexOffsetEnd = objectPayloadObjectOffset;
        }

        GroupMemoryBarrierWithGroupSync();

        if (_GroupObjectPayloadIndex >= _PayloadCount)
            break;

        while (true)
        {            
            GroupMemoryBarrierWithGroupSync();

            uint payloadIndex = _GroupObjectPayloadIndex;
            if (payloadIndex > _GroupObjectPayloadIndexEnd || payloadIndex == _GroupObjectPayloadIndexEnd && _GroupObjectPayloadIndexOffset >= _GroupObjectPayloadIndexOffsetEnd)
                break;

            uint payloadObjectOffset = groupThreadID.x + _GroupObjectPayloadIndexOffset;

            GroupMemoryBarrierWithGroupSync();


            ObjectPayloadData2 currentPayload = _ObjectPayloadDatas[payloadIndex];
            {
                bool predicate = payloadObjectOffset < currentPayload.objectCount;
                GroupObjectPayloadData groupPayload;
                groupPayload.objectCullingGUID = currentPayload.objectCullingGUID + payloadObjectOffset;
                groupPayload.objectIndex = currentPayload.objectIndex + payloadObjectOffset;
                groupPayload.indirectArgIndex = currentPayload.indirectArgIndex;
            
                InstanceCulling(groupPayload, groupThreadID, predicate);
            }


            if (groupIndex == 0)
            {
                _GroupObjectPayloadIndexOffset += PERSISTENT_CULLING_GROUP_SIZE_X;

                if (_GroupObjectPayloadIndexOffset >= currentPayload.objectCount)
                {
                    _GroupObjectPayloadIndex++;
                    _GroupObjectPayloadIndexOffset = 0;
                }
            }
        }
    }
}

StructuredBuffer<uint> _DrawIndirectArgs;
RWStructuredBuffer<uint> _OutOffsetBufferCount;
RWStructuredBuffer<uint> _OutObjectIndexOffsetBuffer;
RWStructuredBuffer<uint> _OutTempObjectIndexOffsetBuffer;
 
[numthreads(NUM_THREADS_PER_GROUP, 1, 1)]
void AllocateCommandInstanceOutputSpace(uint indirectArgIndex : SV_DispatchThreadID)
{
    if (indirectArgIndex < _IndirectArgCount)
    {
        uint objectCommandCount = _DrawIndirectArgs[indirectArgIndex * INDIRECT_ARGS_NUM_WORDS + 1];
        uint objectCommandOffset = 0u;
        if (objectCommandCount > 0u)
        {
            InterlockedAdd(_OutOffsetBufferCount[0], objectCommandCount, objectCommandOffset);
        }
        _OutObjectIndexOffsetBuffer[indirectArgIndex] = objectCommandOffset;
        _OutTempObjectIndexOffsetBuffer[indirectArgIndex] = objectCommandOffset;
    }
}

StructuredBuffer<VisibleObjectCommand> _VisibleObjectCommands;
StructuredBuffer<uint> _VisibleObjectCommandCount;
RWStructuredBuffer<uint> _OutObjectIndexBuffer;
RWStructuredBuffer<uint> _OutPageInfoBuffer;

[numthreads(NUM_THREADS_PER_GROUP, 1, 1)]
void OutputCommandInstanceLists(uint visibleObjectCommandIndex : SV_DispatchThreadID)
{
    uint visibleObjectCommandCount = _VisibleObjectCommandCount[0];

    if (visibleObjectCommandIndex < visibleObjectCommandCount)
    {
        VisibleObjectCommand visibleObjectCommand = _VisibleObjectCommands[visibleObjectCommandIndex];

        uint objectIndexOutputOffset = 0u;
        InterlockedAdd(_OutTempObjectIndexOffsetBuffer[visibleObjectCommand.indirectArgIndex], 1u, objectIndexOutputOffset);

        _OutObjectIndexBuffer[objectIndexOutputOffset] = visibleObjectCommand.objectIndex;
        _OutPageInfoBuffer[objectIndexOutputOffset] = visibleObjectCommand.packedPageInfo;
    }
}