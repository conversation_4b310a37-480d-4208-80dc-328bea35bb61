#ifndef VIRTUAL_SHADOW_MAP_COMMON_HLSL
#define VIRTUAL_SHADOW_MAP_COMMON_HLSL

#define ENABLE_CONTACT_SHADOW
SHADER_CONST(bool, ENABLE_FOLIAGEONLY_CONTACTSHADOW, false);

cbuffer cbVirtualShadowMapCommon
{
    float4 _ScreenSizeAndInvSize;
    uint4 _UIntParams0;
    float4 _FloatParams0;
}
#define _FrameCount             _UIntParams0.x
#define _LightCount             _UIntParams0.y
#define _SMRTRayCount           _UIntParams0.z
#define _SMRTSamplesPerRay      _UIntParams0.w
#define _ContactShadowLength    _FloatParams0.x

struct LightData
{
    float4 lightDirPos;
    float4 lightTilePos;
    float4 lightAttenuation;
    float4 lightColor;
    float4 lightSpotDirection;
    int virtualShadowMapId;
    float sourceAngle;
    uint _pad[2];
};
StructuredBuffer<LightData> _LightDatas;
Texture2D<float> _DepthMap : register(space0);
Texture2D<float4> _GBuffer2 : register(space0);
#endif