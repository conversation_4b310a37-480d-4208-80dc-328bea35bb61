#ifndef VIRTUAL_SHADOW_MAP_PAGE_ACCESS_COMMON_HLSL
#define VIRTUAL_SHADOW_MAP_PAGE_ACCESS_COMMON_HLSL

// Used in the PageFlags
// NOTE: These flags are combined hierarchically using bitwise *OR*, so plan/negate them appropriately
// Marks pages that are allocated
#define VSM_ALLOCATED_FLAG			(1u)
// Marks pages whose dynamic pages are uncached
#define VSM_DYNAMIC_UNCACHED_FLAG	(2u)
// Marks pages whose static pages are uncached
#define VSM_STATIC_UNCACHED_FLAG	(4u)
// Marks pages that need non-nanite rendering
#define VSM_NON_NANITE_FLAG			(8u)
// NOTE: Bits for the hierarchical page flags are stored in the same uints as the regular mip tail,
// offset based on the Hmip level. For instance, at the 1x1 level the first 4 bits store the page
// flags for the coarsest mip, the next 4 bits store the hierarchical page flags for the second
// coarsest mip and so on.
// If the total bit count needs to change be sure it doesn't overlow the page flags for all Hmips
#define VSM_PAGE_FLAGS_BITS_PER_HMIP (4u)
#define VSM_PAGE_FLAGS_BITS_MASK     ((1u << VSM_PAGE_FLAGS_BITS_PER_HMIP) - 1u)

// same as C++
#define VSM_PAGE_SIZE                   (128u)
#define VSM_LEVEL0_DIM_PAGES_XY         (128u)
#define VSM_PAGE_SIZE_MASK              (VSM_PAGE_SIZE - 1u)
#define VSM_LOG2_PAGE_SIZE              (7u)
#define VSM_LOG2_LEVEL0_DIM_PAGES_XY    (7u)
#define VSM_MAX_MIP_LEVELS              (VSM_LOG2_LEVEL0_DIM_PAGES_XY + 1u)
#define VSM_PAGE_TABLE_SIZE             (21845u)
#define VSM_VIRTUAL_MAX_RESOLUTION_XY   (VSM_LEVEL0_DIM_PAGES_XY * VSM_PAGE_SIZE)
#define VSM_RASTER_WINDOW_PAGES         (4u)

struct PhysicalPageMetaData
{
    uint flags;	// VSM_*_FLAG. Not all relevant to physical pages
    uint age;
};

uint CalcLog2LevelDimsPages(uint level)
{
	return VSM_LOG2_LEVEL0_DIM_PAGES_XY - level;	// log2(VSM_LEVEL0_DIM_PAGES_XY >> level)
}

uint CalcLevelDimsPages(uint level)
{
	return 1u << CalcLog2LevelDimsPages(level);
}

uint CalcLevelOffsets(uint level)
{
	// VSM_LEVEL0_DIM_PAGES_XY is a power of two, so the footprint of each mip level MipSize_i=(VSM_LEVEL0_DIM_PAGES_XY>>i)^2 is also a power of two.
	// The binary representation of a mip size is just a single bit: 1 << log2(MipSize_i) = (1 << (2 * (VSM_LOG2_LEVEL0_DIM_PAGES_XY - i))).
	
	// To calculate the offset we need to calculate a sum of consecutive mip sizes, which is equivalent to producing a bit pattern with one bit per level starting out at 
	// bitposition 2*VSM_LOG2_LEVEL0_DIM_PAGES_XY and going down by 2 for every level.
	// E.g. VSM_LEVEL0_DIM_PAGES_XY=3
	//   Level 0: 0000000
	//   Level 1: 1000000
	//   Level 2: 1010000
	//   Level 3: 1010100
	//   Level 4: 1010101

	// To quickly produce a variable number of bits we just select a range of bits from the alternating bit sequence 0x55=0b01010101.
	uint numBits = level << 1;
	uint startBit = (2 * VSM_LOG2_LEVEL0_DIM_PAGES_XY + 2) - numBits;
#if COMPILER_SUPPORTS_BITFIELD_INTRINSICS
	uint mask = BitFieldMaskU32(numBits, startBit);
#else
	uint mask = ((1u << numBits) - 1u) << startBit;
#endif

	return 0x55555555u & mask;
}

/**
 * Compute the offset for a mip level page table given a shadow map ID and a level.
 */
uint CalcPageTableLevelOffset(uint shadowMapID, uint level)
{
	return shadowMapID * VSM_PAGE_TABLE_SIZE + CalcLevelOffsets(level);
}

/**
 * Compute the offset for page within a level page table given a level and PageAddress.
 */
uint CalcPageOffsetInLevel(uint level, uint2 pageAddress)
{
	// return pageAddress.x + pageAddress.y * levelDimsPages[level];
	return pageAddress.x + (pageAddress.y << CalcLog2LevelDimsPages(level));
}

uint CalcPageOffset(uint shadowMapID, uint level, uint2 pageAddress)
{
	return CalcPageTableLevelOffset(shadowMapID, level) + CalcPageOffsetInLevel(level, pageAddress);
}

// Linearlize a physical page address to a linear offset
uint VSMPhysicalPageAddressToIndex(uint2 physicalPageAddress, uint physicalPageRowShift)
{
	return (physicalPageAddress.y << physicalPageRowShift) + physicalPageAddress.x;
}

uint2 VSMPhysicalIndexToPageAddress(uint pageIndex, uint physicalPageRowShift, uint physicalPageRowMask)
{
	uint2 pageAddress;
	pageAddress.x = pageIndex & physicalPageRowMask;
	pageAddress.y = pageIndex >> physicalPageRowShift;
	return pageAddress;
}

// Current page table format:
// NOTE: Some redundancy in flags and encoding, but we have spare bits for now
//   [0:9] PageAddress.x
//   [10:19] PageAddress.y
//   [20:25] LODOffset
//   [26:30] (currently unused)
//   [31] bAnyLODValid
struct ShadowPhysicalPage
{
	uint2 physicalAddress;	// Physical page address X, Y
	uint lodOffset;			// 0 if page is mapped at this mip/clipmap level; 1 if mapped at next courser level, etc. [0..64)
	bool isAnyLODValid;		// Valid physical page mapped at some LOD level
	bool isThisLODValid;    // Valid page mapped at this specific level (equivalent to bAnyMipValid && LODOffset == 0)
	bool isCached;
};

#define VSM_PHYSICAL_PAGE_ANY_MIP_VALID_FLAG    0x80000000
#define VSM_PHYSICAL_PAGE_INVALID               0x00000000

uint ShadowEncodePageTable(uint2 physicalAddress)
{
	return VSM_PHYSICAL_PAGE_ANY_MIP_VALID_FLAG | (physicalAddress.y << 10) | (physicalAddress.x);
}

uint ShadowEncodePageTable(uint2 physicalAddress, uint lodOffset)
{
	return VSM_PHYSICAL_PAGE_ANY_MIP_VALID_FLAG | (lodOffset << 20) | (physicalAddress.y << 10) | (physicalAddress.x);
}

ShadowPhysicalPage ShadowDecodePageTable(uint value)
{
	ShadowPhysicalPage result;
	result.physicalAddress = uint2(value & 0x3FF, (value >> 10) & 0x3FF);
	result.lodOffset = (value >> 20) & 0x3F;
	result.isAnyLODValid = (value & VSM_PHYSICAL_PAGE_ANY_MIP_VALID_FLAG) != 0;
	result.isThisLODValid = result.isAnyLODValid && result.lodOffset == 0;
	return result;
}

ShadowPhysicalPage ShadowGetPhysicalPage(StructuredBuffer<uint> pageTable, uint pageOffset)
{
	return ShadowDecodePageTable(pageTable[pageOffset]);
}

ShadowPhysicalPage VirtualToPhysicalTexel(StructuredBuffer<uint> pageTable, uint virtualShadowMapId, uint level, uint2 virtualTexelAddress, inout uint2 physicalTexelAddress)
{
	uint vPageX = virtualTexelAddress.x >> VSM_LOG2_PAGE_SIZE;
	uint vPageY = virtualTexelAddress.y >> VSM_LOG2_PAGE_SIZE;

	ShadowPhysicalPage physicalPageEntry = ShadowGetPhysicalPage(pageTable, CalcPageOffset(virtualShadowMapId, level, uint2(vPageX, vPageY)));
	physicalTexelAddress = physicalPageEntry.physicalAddress * VSM_PAGE_SIZE + (virtualTexelAddress & VSM_PAGE_SIZE_MASK);
	return physicalPageEntry;
}

struct PageInfo
{
	uint viewId;
	bool isStaticPage;
};

uint PackPageInfo(PageInfo pageInfo)
{
	return pageInfo.viewId | (pageInfo.isStaticPage ? (1u << 16) : 0u);
}

PageInfo UnpackPageInfo(uint packedData)
{
	PageInfo pageInfo;
	pageInfo.viewId = packedData & 0xFFFF;
	pageInfo.isStaticPage = ((packedData >> 16) & 0x1) != 0;
	return pageInfo;
}

#endif