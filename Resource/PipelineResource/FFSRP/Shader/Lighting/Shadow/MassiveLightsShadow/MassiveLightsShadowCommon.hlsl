#ifndef MASSIVE_LIGHTS_SHADOW_COMMON
#define MASSIVE_LIGHTS_SHADOW_COMMON

// prerequisites:
//Texture2D<float4> _GBuffer1 : register(space0);
//Texture2D<float> _DepthMap : register(space0);

#include "../../../ShaderLibrary/Common.hlsl"
#include "../../../ShaderLibrary/Random.hlsl"
#include "../../../Lighting/LightDefinition.hlsl"

#define MAX_LIGHTS_PER_CLUSTER   15
#define THRESHOLD_MIN_LUMINANCE  0.01

struct MassiveLightCluster
{
	float4 DebugColor;
	uint LightCount;
	uint LightInstanceIDs[MAX_LIGHTS_PER_CLUSTER];
};

struct MassiveLightClusterScores
{
	int LightInstanceScores[MAX_LIGHTS_PER_CLUSTER];
};

cbuffer cbMassiveLightsShadow : register(space0)
{
    // massive lights
    uint _ClusterCount;
    float _ClusterOffsetX;
	float _ClusterOffsetZ;
	float _ClusterSize;
	float _InverseClusterSize;
	float _GlobalUnitScale;
	float _ThresholdScale;

	float _StepUV;
	uint _MinSteps;
	uint _MaxSteps;
	float _StepMultiplier;
	float _ExcludedDistance;
	float _FadeInStartDistance;
	float _FadeInEndDistance;

	uint _StateFrameIndexModed;
}

#define SUPPORT_SSRT_RAY_DEBUG
#define SUPPORT_MULTIPLE_TRACE
#include "../../../ShaderLibrary/ScreenSpaceRayTracing.hlsl"

bool ScreenSpaceRayTracing_Simple(PositionInputs posInput, float3 lightPositionWS, float3 normalWS, float2 positionSS, bool calculatesTransmission, inout int hitCount)
{
	float thresholdScale = _ThresholdScale;

	float depthBias = 0.1 * _GlobalUnitScale;
	float3 normalBias = 0.1 * _GlobalUnitScale * normalWS;

	float3 difference = lightPositionWS - posInput.positionWS;
	float rayLength = length(difference);
	float3 direction = difference / rayLength;
	rayLength = max(rayLength - _ExcludedDistance * _GlobalUnitScale, 0);

	float stepMultiplier = _StepMultiplier;
	bool multiLevelTrace = true;
	bool enableRayDebug = true;

	float3 hitPointWS;
	bool occluded = ScreenSpaceRayTracing(
		posInput.positionWS,
		direction,
		rayLength,
		positionSS, 
		_StateFrameIndexModed, 
		ce_ScreenParams,
		_StepUV,
		_MinSteps, 
		_MaxSteps, 
		thresholdScale,
		posInput.linearDepth,
		depthBias,
		normalBias,
		_GlobalUnitScale,
		calculatesTransmission,
		stepMultiplier,
		multiLevelTrace,
		enableRayDebug,
		hitCount,
		hitPointWS);

	return occluded;
}

SHADER_CONST(bool, MASSIVE_LIGHTS_SHADOW_ENABLE, false);
SHADER_CONST(bool, MASSIVE_LIGHTS_SHADOW_CLUSTER, false);
SHADER_CONST(bool, MASSIVE_LIGHTS_SHADOW_TRANSMISSION, false);
SHADER_CONST(bool, MASSIVE_LIGHTS_SHADOW_CULL_BACK, false);
SHADER_CONST(bool, MASSIVE_LIGHTS_SHADOW_DEBUG, false);

StructuredBuffer<InstanceLightType> _LightInstanceData : register(space0);
StructuredBuffer<MassiveLightCluster> _MassiveLightsClusters : register(space0);
StructuredBuffer<uint> _MassiveLightsShadow : register(space0);

void ToClusterPosition(int clusterIndexX, int clusterIndexZ, out float clusterPositionX, out float clusterPositionZ)
{
	clusterPositionX = (float)clusterIndexX * _ClusterSize + _ClusterOffsetX;
	clusterPositionZ = (float)clusterIndexZ * _ClusterSize + _ClusterOffsetZ;
}

void ToClusterIndex(float clusterPositionX, float clusterPositionZ, out int clusterIndexX, out int clusterIndexZ)
{
	clusterIndexX = floor((clusterPositionX - _ClusterOffsetX) * _InverseClusterSize);
	clusterIndexZ = floor((clusterPositionZ - _ClusterOffsetZ) * _InverseClusterSize);
}

float3 GetLightPosition(InstanceLightType light)
{
	float3 lightWorldPos = light.lightDirPos.xyz;
#ifdef CE_USE_DOUBLE_TRANSFORM
    lightWorldPos = GetLargeCoordinateReltvPosition(lightWorldPos, light.lightTilePos.xyz, ce_CameraTilePosition);
#endif
	return lightWorldPos;
}

float GetLightRange(InstanceLightType light)
{
	return light.lightDirPos.w;
}

float GetLightRangeSquared(InstanceLightType light)
{
	return light.lightDirPos.w * light.lightDirPos.w;
}

bool IsPointInLightRange(float3 positionWS, float3 lightPositionWS, InstanceLightType light)
{
	float radiusSquared = GetLightRangeSquared(light);
	float3 difference = lightPositionWS - positionWS;
	return dot(difference, difference) < radiusSquared;
}

bool IsPointFaceToLight(float3 positionWS, float3 lightPositionWS, float3 normalWS, InstanceLightType light)
{
	float3 lightDirection = lightPositionWS - positionWS;
	return dot(lightDirection, normalWS) > 0.0;
}

float EstimateLuminanceOfDiffuse(float3 diffuse)
{
	return dot(diffuse, float3(0.3, 0.3, 0.3));
}

bool IsLightPointLight(InstanceLightType light)
{
	return light.lightColor.w < 0.0;
}

bool MassiveLightsShadow_LoadShadowBit(float3 positionWS, uint2 positionSS, uint instanceID)
{
	bool shadowed = false;
	if (MASSIVE_LIGHTS_SHADOW_ENABLE && MASSIVE_LIGHTS_SHADOW_CLUSTER)
	{
		if (_ClusterCount > 0)
		{
			float clusterPositionX = positionWS.x;
			float clusterPositionZ = positionWS.z;
			
			int clusterIndexX = 0;
			int clusterIndexZ = 0;
			ToClusterIndex(clusterPositionX, clusterPositionZ, clusterIndexX, clusterIndexZ);

			if (clusterIndexX >= 0 && clusterIndexX < _ClusterCount && clusterIndexZ >= 0 && clusterIndexZ < _ClusterCount)
			{
				uint clusterIndex = clusterIndexX * _ClusterCount + clusterIndexZ;
				MassiveLightCluster cluster = _MassiveLightsClusters[clusterIndex];
				int clusterLightCount = cluster.LightCount;
				uint2 pixelCoord = positionSS;
				uint massiveLightsShadowIndex = pixelCoord.y * ce_ScreenParams.x + pixelCoord.x;
				for (int i = 0; i < clusterLightCount; i++)
				{
					uint lightInstanceID = cluster.LightInstanceIDs[i];
					if (lightInstanceID == instanceID)
					{
						uint shadowBits = _MassiveLightsShadow[massiveLightsShadowIndex];
						uint shadowBit = (1U << i);
						if (shadowBits & shadowBit)
						{
							shadowed = true;
						}
						break;
					}
				}
			}
		}
	}
	return shadowed;
}

float MassiveLightsShadow_CalculateShadowValue(PositionInputs posInput, uint2 positionSS, float3 normalWS, bool shadowed, float3 lightPositionWS, InstanceLightType instanceLight, out float shadowTransmission)
{
	float shadowValue = 1.0;
	shadowTransmission = 0.0;

	//if ((instanceLight.flags & LIGHT_INSTANCE_FLAG_CAST_SHADOW) == 0)
	//{
		//return shadowValue;
	//}

	float simpleTransmission = 1.0;
	float3 positionWS = posInput.positionWS;

	if (MASSIVE_LIGHTS_SHADOW_ENABLE)
	{
		float fadeInStartDistance = _FadeInStartDistance * _GlobalUnitScale;
		if (posInput.linearDepth < fadeInStartDistance)
		{
			shadowValue = 1.0;
			return shadowValue;		
		}

		//float fadeInEndDistance = _FadeInEndDistance * _GlobalUnitScale;
		//if (posInput.linearDepth > fadeInEndDistance)
		//{
			//shadowValue = 1.0;
			//return shadowValue;		
		//}


		if (MASSIVE_LIGHTS_SHADOW_CLUSTER)
		{
			shadowValue = shadowed ? 0.0 : 1.0;
			shadowTransmission = shadowValue * simpleTransmission;
		}
		else
		{
			if (MASSIVE_LIGHTS_SHADOW_TRANSMISSION)
			{
				if (IsPointInLightRange(positionWS, lightPositionWS, instanceLight))
				{
					bool calculatesHitCount = true;
					int hitCount = 0;
					shadowed = ScreenSpaceRayTracing_Simple(posInput, lightPositionWS, normalWS, (float2)positionSS, calculatesHitCount, hitCount);
					simpleTransmission = CalculateTransmission(hitCount);
				}
				else
				{
					simpleTransmission = 0.0;
				}
				shadowValue = shadowed ? 0.0 : 1.0;
				shadowTransmission = shadowValue * simpleTransmission;
			}
			else
			{
				if (IsPointInLightRange(positionWS, lightPositionWS, instanceLight))
				{
					if (MASSIVE_LIGHTS_SHADOW_CULL_BACK)
					{
						if (IsPointFaceToLight(positionWS, lightPositionWS, normalWS, instanceLight))
						{
							//if (EstimateLuminanceOfDiffuse(directLighting.diffuse.rgb) > THRESHOLD_MIN_LUMINANCE)
							{
								bool calculatesHitCount = false;
								int hitCount = 0;
								shadowed = ScreenSpaceRayTracing_Simple(posInput, lightPositionWS, normalWS, (float2)positionSS, calculatesHitCount, hitCount);
							}
							shadowValue = shadowed ? 0.0 : 1.0;
							shadowTransmission = shadowValue * simpleTransmission;
						}
						else
						{
							shadowed = true;
							shadowValue = shadowed ? 0.0 : 1.0;
							shadowTransmission = shadowValue * simpleTransmission;
						}
					}
					else
					{
						//if (EstimateLuminanceOfDiffuse(directLighting.diffuse.rgb) > THRESHOLD_MIN_LUMINANCE)
						{
							bool calculatesHitCount = false;
							int hitCount = 0;
							shadowed = ScreenSpaceRayTracing_Simple(posInput, lightPositionWS, normalWS, (float2)positionSS, calculatesHitCount, hitCount);
						}
						shadowValue = shadowed ? 0.0 : 1.0;
						shadowTransmission = shadowValue * simpleTransmission;
					}
				}
			}
		}

		float fadeInEndDistance = _FadeInEndDistance * _GlobalUnitScale;
		float fade = saturate((posInput.linearDepth - fadeInStartDistance) / (fadeInEndDistance - fadeInStartDistance));
		shadowValue = 1.0 - ((1.0 - shadowValue) * fade);
	}
	return shadowValue;
}

float4 MassiveLightsShadow_GetDebugColor(float3 positionWS)
{
	float4 debugColor = float4(1.0, 1.0, 1.0, 1.0);
	if (MASSIVE_LIGHTS_SHADOW_ENABLE && MASSIVE_LIGHTS_SHADOW_DEBUG)
	{
		if (_ClusterCount > 0)
		{
			float clusterPositionX = positionWS.x;
			float clusterPositionZ = positionWS.z;
			
			int clusterIndexX = 0;
			int clusterIndexZ = 0;
			ToClusterIndex(clusterPositionX, clusterPositionZ, clusterIndexX, clusterIndexZ);

			if (clusterIndexX >= 0 && clusterIndexX < _ClusterCount && clusterIndexZ >= 0 && clusterIndexZ < _ClusterCount)
			{
				uint clusterIndex = clusterIndexX * _ClusterCount + clusterIndexZ;
				MassiveLightCluster cluster = _MassiveLightsClusters[clusterIndex];
				debugColor = cluster.DebugColor;
				return debugColor;
			}
		}
	}
	return debugColor;
}

#endif