#ifndef FIBONACCI_HLSL
#define FIBONACCI_HLSL

#define GOLDEN_RATIO 1.618033988749895
#define GOLDEN_ANGLE 2.399963229728653

// Replaces the Fi<PERSON>acci sequence in Fibonacci2dSeq() with the Golden ratio.
float2 Golden2dSeq(uint i, float n)
{
	// GoldenAngle = 2 * Pi * (1 - 1 / GoldenRatio).
	// We can drop the "1 -" part since all it does is reverse the orientation.
	return float2(i / n + (0.5 / n), frac(i * rcp(GOLDEN_RATIO)));
}

float2 SampleDiskGolden(uint i, uint sampleCount)
{
	float2 f = Golden2dSeq(i, sampleCount);
	return float2(sqrt(f.x), M_2PI * f.y);
}

#endif