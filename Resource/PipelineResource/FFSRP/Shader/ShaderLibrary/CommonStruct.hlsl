#ifndef COMMON_STRUCT_HLSL
#define COMMON_STRUCT_HLSL

#include "Common.hlsl"

struct FoliageObjectSceneData
{
    float4x4 world;
    float4x4 preWorld;
    float4x4 invWorld;
    float4x4 invTransposeWorld;
    float4 nonUniformScale;
    float3 invNonUniformScale;
    float3 tilePosition;
    float3 preTilePosition;
};

struct FoliageCompactSceneData
{
    uint4 rotationScale;
    float3 translation;
    uint entityIndex;
};

struct FoliageEntityData
{
    float4x4 world;
    float4x4 preWorld;
    float4x4 invTransposeWorld;
    float3 tilePosition;
    float3 preTilePosition;
};

// [Frisvad 2012, "Building an Orthonormal Basis from a 3D Unit Vector Without Normalization"]
void _GetHemiOrthoBasis( inout float3 BasisX, inout float3 BasisY, float3 BasisZ )
{
    float A = 1.0f / ( 1.0f + BasisZ.z );
    float B = -BasisZ.x * BasisZ.y * A;
    BasisX = float3( 1.0f - BasisZ.x * BasisZ.x * A, B, -BasisZ.x );
    BasisY = float3( B, 1.0f - BasisZ.y * BasisZ.y * A, -BasisZ.y );
}

float3 _HemiOctahedronToUnitVector( float2 Oct )
{
    Oct = float2( Oct.x + Oct.y, Oct.x - Oct.y );
    float3 N = float3( Oct, 2.0 - dot( 1, abs(Oct) ) );
    return normalize(N);
}

float2 _UnitVectorToHemiOctahedron( float3 N )
{
    N.xy /= dot( 1, abs(N) );
    return float2( N.x + N.y, N.x - N.y );
}

float2 _UnitVectorToOctahedron( float3 N )
{
	N.xy /= dot( 1, abs(N) );
	if( N.z <= 0 )
	{
		N.xy = ( 1 - abs(N.yx) ) * select( N.xy >= 0, float2(1,1), float2(-1,-1) );
	}
	return N.xy;
}

float3 _OctahedronToUnitVector( float2 Oct )
{
	float3 N = float3( Oct, 1 - dot( 1, abs(Oct) ) );
	float t = max( -N.z, 0 );
    N.xy += select(N.xy >= 0, float2(-t, -t), float2(t, t));
	return normalize(N);
}

uint4 EncodeScaleAndRotation(float3 Scale, float3x3 Axis)
{
    const uint ExpBits			= 8;
    const uint ExpBias			= ( 1u << (ExpBits - 1) ) - 1;
    const uint SignMantissaBits	= 16;
    const uint SignMantissaMask	= (1u << SignMantissaBits) - 1;
    const uint MantissaBits		= SignMantissaBits - 1;
    const float Sqrt2 			= 1.41421356f;

    uint4 Output;

    // Rotation
    {
        if( Axis[2].z < 0.0f )
        {
            Axis[2] *= -1.0f;
            Scale.z *= -1.0f;
        }

        float2 OctZ = _UnitVectorToHemiOctahedron( Axis[2] );

        float3 BasisX, BasisY;
        _GetHemiOrthoBasis( BasisX, BasisY, Axis[2] );

        float X = dot(Axis[0], BasisX);
        float Y = dot(Axis[0], BasisY);

        float aX = abs( X );
        float aY = abs( Y );

        bool bSpinIsX = aX < aY;
        float Spin0 = bSpinIsX ? X : Y;
        float Spin1 = bSpinIsX ? Y : X;
        float Sign1 = Spin1 < 0.0f ? -1.0f : 1.0f;
    
        //Axis[0]	*= Sign1;
        Scale.x *= Sign1;
        Spin0	*= Sign1;

        float3 GeneratedY = cross(Axis[2], Axis[0]);
        Scale.y *= dot( Axis[1], GeneratedY ) < 0.0f ? -Sign1 : Sign1;

        // Avoid sign extension in shader by biasing
        Output.x  = (((int)round( OctZ.x * 32767.0f ) + 32768) & 0xFFFF) <<  0;
        Output.x |= (((int)round( OctZ.y * 32767.0f ) + 32768) & 0xFFFF) << 16;

        // NOTE: Masking the bits with `& 0x7FFF` below causes the whole int to be optimized to 0 on some shader platforms.
        // This is okay, as long as Spin0 is in [0, 1], which it should be.
        Output.y  = ((int)round( Spin0 * 16383.0f * Sqrt2 ) + 16384); // & 0x7FFF;
        Output.y |=	bSpinIsX ? (1u << 15) : 0;
    }

    // Scale
    {
        float MaxComponent = max(abs(Scale.x), max(abs(Scale.y), abs(Scale.z)));
        uint MaxComponentExponent = (asuint(MaxComponent) & 0x7f800000u) >> 23;

        // Need +1 because of losing the implicit leading bit of mantissa
        // TODO assumes ExpBits == 8
        // TODO clamp to expressable range
        uint SharedExp = MaxComponentExponent + 1;

        float ExpScale = asfloat(((127 + ExpBias + MantissaBits - SharedExp) & 0xFFu) << 23);

        if( (uint)round( MaxComponent * ExpScale ) == (1u << MantissaBits) )
        {
            // Mantissa rounded up
            SharedExp++;
            ExpScale *= 0.5f;
        }

        Output.z  = (((int)round( Scale.x * ExpScale ) + (1u << MantissaBits)) & 0xFFFFu) <<  0;
        Output.z |= (((int)round( Scale.y * ExpScale ) + (1u << MantissaBits)) & 0xFFFFu) << 16;
        Output.w  = (((int)round( Scale.z * ExpScale ) + (1u << MantissaBits)) & 0xFFFFu) <<  0;
        Output.w |= SharedExp << 16;		
    }

    return Output;
}

uint4 EncodeScaleAndRotation( float3x3 InTransform )
{
    float3 Scale = {
        length(InTransform[0]),
        length(InTransform[1]),
        length(InTransform[2])
    };
    float3x3 Axis = {
        InTransform[0] / Scale.x,
        InTransform[1] / Scale.y,
        InTransform[2] / Scale.z
    };
    return EncodeScaleAndRotation(Scale, Axis);
}

void EncodeTransform( float4x4 InTransform, inout uint4 OutRotationScale, inout float3 OutTranslation )
{
    OutRotationScale = EncodeScaleAndRotation((float3x3)InTransform);
    OutTranslation = InTransform[3].xyz;
}

float4x4 DecodeTransform( uint4 RotationScale, float3 Translation, inout float3 Scale )
{
    float4x4 M = 0.0;
    M[3].xyz = Translation;
    M[3].w = 1.0;

    // Rotation
    {
        float3 Rotation =
        {
            ( RotationScale[0] >>  0 ) & 0xffff,
            ( RotationScale[0] >> 16 ) & 0xffff,
            ( RotationScale[1] >>  0 ) & 0x7fff
        };

        float2 OctZ  = ( Rotation.xy - 32768 ) * (1.0f / 32767.0f);
        float Spin0  = ( Rotation.z  - 16384 ) * (0.70710678f / 16383.0f);	// rsqrt(2)
        bool bSpinIsX = RotationScale[1] & 0x8000;

        M[2].xyz = _HemiOctahedronToUnitVector( OctZ );

        float3 BasisX, BasisY;
        _GetHemiOrthoBasis( BasisX, BasisY, M[2].xyz );

        float Spin1 = sqrt( 1.0f - Spin0 * Spin0 );
        float X = bSpinIsX ? Spin0 : Spin1;
        float Y = bSpinIsX ? Spin1 : Spin0;

        M[0].xyz = BasisX * X + BasisY * Y;
        M[1].xyz = cross( M[2].xyz, M[0].xyz );
    }

    // Scale
    {
        const uint SignMantissaBits	= 16;
        const uint SignMantissaMask	= (1u << SignMantissaBits) - 1;
        const uint MantissaBits		= SignMantissaBits - 1;

#if 0
        uint SharedExp = RotationScale[3] >> 22;

        float ExpScale = asfloat( ( SharedExp - MantissaBits ) << 23 );

        int3 Mantissa =
        {
            ( RotationScale[2] >> 0 ),
            ( RotationScale[2] >> 18 ) | ( RotationScale[3] << 14 ),
            ( RotationScale[3] >> 4 )
        };
#else
        uint SharedExp = RotationScale[3] >> 16;

        float ExpScale = asfloat( ( SharedExp - MantissaBits ) << 23 );

        uint3 Mantissa =
        {
            RotationScale[2] >> 0,
            RotationScale[2] >> 16,
            RotationScale[3] >> 0
        };
        
#endif
        Mantissa &= SignMantissaMask;
        Scale = Mantissa;
        Scale -= 1u << MantissaBits;
        Scale *= ExpScale;

        M[0] *= Scale[0];
        M[1] *= Scale[1];
        M[2] *= Scale[2];
    }

    return transpose(M);
}

float4x4 MakeInverseMatrix(float4x4 LocalToRelativeWorld, float3 invNonUniformScale)
{
    float4x4 RelativeWorldToLocal               = LocalToRelativeWorld;
    RelativeWorldToLocal[0].xyz                 *= Pow2(invNonUniformScale.x);
    RelativeWorldToLocal[1].xyz                 *= Pow2(invNonUniformScale.y);
    RelativeWorldToLocal[2].xyz                 *= Pow2(invNonUniformScale.z);
    RelativeWorldToLocal[3].xyz                 = 0.0f;
    RelativeWorldToLocal                        = transpose(RelativeWorldToLocal);
    RelativeWorldToLocal[3].xyz                 = mul(float4(-LocalToRelativeWorld[3].xyz, 0.0f), RelativeWorldToLocal).xyz;

    return transpose(RelativeWorldToLocal);
}

void DecodeFoliageCompactSceneData(in FoliageCompactSceneData compactData, in FoliageEntityData entityData, inout FoliageObjectSceneData objectData)
{
    objectData = (FoliageObjectSceneData)0;
    float3 scale;
    float4x4 localMatrix = DecodeTransform(compactData.rotationScale, compactData.translation, scale);
    objectData.world = mul(entityData.world, localMatrix);
    objectData.preWorld = mul(entityData.preWorld, localMatrix);
    objectData.tilePosition = entityData.tilePosition;
    objectData.preTilePosition = entityData.preTilePosition;
    
    float4x4 world = transpose(objectData.world);
    float3 worldScale = {
        length(world[0].xyz),
        length(world[1].xyz),
        length(world[2].xyz)
    };
    objectData.nonUniformScale.xyz = abs(worldScale);
	objectData.nonUniformScale.w = max( objectData.nonUniformScale.x, max( objectData.nonUniformScale.y, objectData.nonUniformScale.z ) );
    objectData.invNonUniformScale = rcp(objectData.nonUniformScale.xyz);
    objectData.invWorld = MakeInverseMatrix(world, objectData.invNonUniformScale);
    objectData.invTransposeWorld = transpose(objectData.invWorld);
}

#endif