#ifndef HASH_FUNCTION_HLSL
#define HASH_FUNCTION_HLSL

// From https://www.shadertoy.com/view/XlGcRh

// commonly used constants
#define HASH_FUNCTION_CONSTANT0 0xcc9e2d51u
#define HASH_FUNCTION_CONSTANT1 0x1b873593u

// Helper Functions
uint RotL(uint x, uint r)
{
	return (x << r) | (x >> (32u - r));
}

uint RotR(uint x, uint r)
{
	return (x >> r) | (x << (32u - r));
}

uint FMix(uint h)
{
    h ^= h >> 16;
    h *= 0x85ebca6bu;
    h ^= h >> 13;
    h *= 0xc2b2ae35u;
    h ^= h >> 16;
    return h;
}

// Adapted from MurmurHash3_x86_32 from https://github.com/aappleby/smhasher
uint Murmur3(uint seed)
{
    uint h = 0u;
    uint k = seed;

    k *= HASH_FUNCTION_CONSTANT0;
    k = RotL(k, 15u);
    k *= HASH_FUNCTION_CONSTANT1;

    h ^= k;
    h = RotL(h, 13u);
    h = h * 5u + 0xe6546b64u;

    h ^= 4u;

    return FMix(h);
}

uint MurmurMix(uint Hash)
{
	Hash ^= Hash >> 16;
	Hash *= 0x85ebca6b;
	Hash ^= Hash >> 13;
	Hash *= 0xc2b2ae35;
	Hash ^= Hash >> 16;
	return Hash;
}

#endif