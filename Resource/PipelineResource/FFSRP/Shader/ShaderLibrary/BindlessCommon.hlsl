#ifndef BINDLESS_COMMON_HLSL
#define BINDLESS_COMMON_HLSL

#define BINDLESS_RESOURCE_GROUP_INDEX 3

// ------------------------buffers--------------------------
// [[vk::binding(0, 3)]]
// Buffer<float4> ce_BindlessFloatBuffers[] : register(space3);
[[vk::binding(0, 3)]]
Buffer<uint4> ce_BindlessUIntBuffers[]  : register(space3);
// [[vk::binding(0, 3)]]
// Buffer<int4> ce_BindlessIntBuffers[] : register(space3);


// MeshData meshData = MeshDataBuffer[mesh_index];
// float3 pos = ce_BindlessFloatBuffers[meshData.PosBufferIndex][tirangle_index].xyz;

// // ------------------------textures--------------------------
// [[vk::binding(0, 3)]]
// Texture2D<float4> BindlessTextures[];

// [[vk::binding(0, 3)]]
// Texture2DArray<float4> ;

// [[vk::binding(0, 3)]]
// Texture1D<float4> ;

// [[vk::binding(0, 3)]]
// Texture1DArray<float4> ;

// [[vk::binding(0, 3)]]
// Texture3D<float4> ;

// [[vk::binding(0, 3)]]
// TextureCube<float4> ;

// [[vk::binding(0, 3)]]
// TextureCubeArray<float4> ;

#endif
