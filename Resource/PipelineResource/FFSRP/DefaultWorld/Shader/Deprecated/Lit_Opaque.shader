
#pragma vertex VSMain
#pragma pixel PSMain

#pragma keyword QTANGENT
#pragma keyword TEXTURE_ARRAY_ENABLE
#pragma keyword VOXELIZE_PASS
#pragma keyword CE_USE_DOUBLE_TRANSFORM
#pragma keyword USED_WITH_SKELETAL_MESH
#pragma keyword FOLIAGE_RENDERING
#define FULL_CUSTOM 1
//#define VERTEX_TYPE VertexType_Vegetation
//#define INSTANCING 1
#ifdef FOLIAGE_RENDERING
    #define CE_INSTANCING
    #define VERTEX_TYPE VertexType_Vegetation
#endif

#define ADDITIONAL_MTL_PARAM \
float _Roughness; \
float _Metallic; \
float4 _SubsurfaceColor; \
float4 _EmissiveColor; \
float _Opacity; \
float _OpacityMask; \
float2 _ScaleUV; \
float2 _OffsetUV; \
float _AlphaClip; \
float _Specular; \
//float _VertOffsetIntensity; \

#ifndef USED_WITH_SKELETAL_MESH
    float2 _Reactivemask;
#endif
Texture2D<float4> _MROTex : register(space1);


#include "/ShaderLibrary/VertexFactoryHeader.hlsl"

#define VERTEX_MODIFICATION
// be care full, when change positionWS in shader, 
// note, change positionWS in this fucntion is useless, must use the GetWorldPositionOffset
VSOutput GetVertexOutput(in VSOutput vOut, in VSInput vIn)
{
	VSOutput ret = vOut;
	// note, change positionWS in this fucntion is useless, must use the 
	ret.uv *=  _ScaleUV;
    ret.uv +=  _OffsetUV;
	return ret;
}


#define WORLD_POSITION_OFFSET
// be carefull with potential mv, shadow, prez, all geometric relelated pass;
#if defined(FOLIAGE_RENDERING) && defined(CE_INSTANCING)
float3 GetWorldPositionOffset(float3 Cur_or_Prev_worldPosition, in VSOutput vOut, in VSInput vIn, in FoliageObjectSceneData instance)
#else
float3 GetWorldPositionOffset(float3 Cur_or_Prev_worldPosition, in VSOutput vOut, in VSInput vIn)
#endif
{
    //float vertOffsetMask = _HeightMap.SampleLevel(ce_Sampler_Repeat, vOut.uv, 0).r;
    //float3 offset = vOut.normalWS * _VertOffsetIntensity * vertOffsetMask;
	//return Cur_or_Prev_worldPosition + offset;
    return Cur_or_Prev_worldPosition;
}

#include "/Material/Lit/SurfaceShaderIncludes.hlsl"

SHADER_CONST(bool, ALPHA_CLIP, false);

#ifdef CE_INSTANCING
SurfaceData GetSurfaceData(ObjectSceneData objectData, SurfaceInput input)
#else
SurfaceData GetSurfaceData(SurfaceInput input)
#endif
{
	SurfaceData surfaceData = (SurfaceData)0;

    float2 uv = input.uv;
	float4 channels = _NormalMap.SampleBias(texture_sampler, uv, _TextureSampleBias);
    float4 color = _BaseMap.SampleBias(texture_sampler, uv, _TextureSampleBias);
    float3 normalTS = UnpackNormalmapRGorAG(channels, 1.0);
    //R:Metallic G:Roughness B:AO A:EmissiveMask
    float4 MRO_Tex = _MROTex.Sample(ce_Sampler_Repeat, uv);

	surfaceData.baseColor = color * _BaseColor.rgb;
	surfaceData.opacity = color.a * _Opacity;
    surfaceData.opacityMask = color.a * _OpacityMask;
    surfaceData.opacityMaskClip = _AlphaClip;
    surfaceData.normalTS = normalTS;
    surfaceData.metallic = _Metallic * MRO_Tex.r;
	surfaceData.roughness = _Roughness * MRO_Tex.g;
    surfaceData.specular = _Specular;
	surfaceData.emissiveColor = _EmissiveColor * surfaceData.baseColor * MRO_Tex.a;
    surfaceData.ambientOcclusion = MRO_Tex.b * _AO_Intensity;
    surfaceData.subsurfaceColor =  _SubsurfaceColor * _BaseColor.rgb;
	surfaceData.materialType = MATERIAL_TYPE;
    surfaceData.debugColor = float3(1, 0, 0);

    // Fsr reactive mask
    #ifdef USED_WITH_SKELETAL_MESH    
        float3 normalInView = mul(ce_View, float4(input.normalWS.xyz, 0.0));
        normalInView = normalize(normalInView);

        float reactiveFactor = min(0.0, dot(normalInView, float3(0, 0, 1))); // -1.0 -- 0.0
        reactiveFactor = step(_Reactivemask.x, 1.0 - abs(reactiveFactor)) * (1.0 - abs(reactiveFactor)) * _Reactivemask.y;
        surfaceData.temporalReactive = reactiveFactor;
    #else
        // temporalReactive.x 范围：0-1，值越大，历史影响的越少，拖尾越少，但随之抗锯齿效果越差，temporalReactive.y 是定位半透明像素,效果不明显
        surfaceData.temporalReactive = _Reactivemask;
    #endif

    //surfaceData.debugColor = channels.xyz;
	return surfaceData;
}


#include "/Material/Lit/Lit.hlsl"
#include "/RenderPass/ShaderPassGBuffer.hlsl"
